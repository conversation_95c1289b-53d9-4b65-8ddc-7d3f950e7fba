{
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Client - Native (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--log-level=ALL:INFO", "--local-native-module-path=${workspaceFolder}/build/private/cargo-target/debug/libamzn_av_living_room_rust_client.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-client-native-debug",
            "presentation": { "group": "client" }
        },
        // You need to run AVLivingRoomClientBundle for this
        {
            "type": "lldb",
            "request": "launch",
            "name": "Client - with JS - Native (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--device-proxy-url=http://127.0.0.1:3999", "--react-uri-prefix=/index.js", "--log-level=ALL:INFO", "--local-native-module-path=${workspaceFolder}/build/private/cargo-target/debug/libamzn_av_living_room_rust_client.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-client-native-debug",
            "presentation": { "group": "client" }
        },
        // You need to be in the right TOS/pilot ACM override to use the Rust pages
        // Optionally you can use alpha-mooshine JS app via ACM override
        {
            "type": "lldb",
            "request": "launch",
            "name": "Client - with JS prod - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--log-level=ALL:INFO", "--local-wasm-module-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/amzn-av-living-room-rust-client.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-client-wasm",
            "presentation": { "group": "client" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Ads Playback Examples",
            "program": "",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Surface X Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libsurface_x_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-surface_x_example-native-release",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Surface X Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libsurface_x_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-surface_x_example-native-debug",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Surface X Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/surface_x_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-surface_x_example-wasm-release",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Surface X Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/surface_x_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-surface_x_example-wasm-debug",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Iva Baseline Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libiva_baseline_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-iva_baseline_example-native-release",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Iva Baseline Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libiva_baseline_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-iva_baseline_example-native-debug",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Iva Baseline Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/iva_baseline_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-iva_baseline_example-wasm-release",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Iva Baseline Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/iva_baseline_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-ads-playback-iva_baseline_example-wasm-debug",
            "presentation": { "group": "ads-playback" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "App Drawer Examples",
            "program": "",
            "presentation": { "group": "app-drawer" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Drawer ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libdrawer.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-app-drawer-drawer-native-release",
            "presentation": { "group": "app-drawer" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Drawer (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libdrawer.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-app-drawer-drawer-native-debug",
            "presentation": { "group": "app-drawer" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Drawer - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/drawer.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-app-drawer-drawer-wasm-release",
            "presentation": { "group": "app-drawer" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Drawer - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/drawer.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-app-drawer-drawer-wasm-debug",
            "presentation": { "group": "app-drawer" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Buybox Examples",
            "program": "",
            "presentation": { "group": "buybox" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buybox ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libbuybox.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-buybox-buybox-native-release",
            "presentation": { "group": "buybox" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buybox (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libbuybox.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-buybox-buybox-native-debug",
            "presentation": { "group": "buybox" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buybox - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/buybox.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-buybox-buybox-wasm-release",
            "presentation": { "group": "buybox" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buybox - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/buybox.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-buybox-buybox-wasm-debug",
            "presentation": { "group": "buybox" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Checkout Examples",
            "program": "",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libbolt_confirmation_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page-native-release",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libbolt_confirmation_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page-native-debug",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/bolt_confirmation_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page-wasm-release",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/bolt_confirmation_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page-wasm-debug",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Single Monthly ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libbolt_confirmation_page_v2_single_monthly.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-native-release",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Single Monthly (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libbolt_confirmation_page_v2_single_monthly.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-native-debug",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Single Monthly - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/bolt_confirmation_page_v2_single_monthly.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-wasm-release",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Single Monthly - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/bolt_confirmation_page_v2_single_monthly.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-wasm-debug",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Monthly And Annual ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libbolt_confirmation_page_v2_monthly_and_annual.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-native-release",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Monthly And Annual (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libbolt_confirmation_page_v2_monthly_and_annual.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-native-debug",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Monthly And Annual - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/bolt_confirmation_page_v2_monthly_and_annual.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-wasm-release",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Bolt Confirmation Page V2 Monthly And Annual - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/bolt_confirmation_page_v2_monthly_and_annual.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-wasm-debug",
            "presentation": { "group": "checkout" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Collections Ui Examples",
            "program": "",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections All Containers ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcollections_all_containers.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_all_containers-native-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections All Containers (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcollections_all_containers.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_all_containers-native-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections All Containers - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/collections_all_containers.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_all_containers-wasm-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections All Containers - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/collections_all_containers.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_all_containers-wasm-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Page Skeleton ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcollections_page_skeleton.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_page_skeleton-native-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Page Skeleton (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcollections_page_skeleton.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_page_skeleton-native-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Page Skeleton - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/collections_page_skeleton.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_page_skeleton-wasm-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Page Skeleton - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/collections_page_skeleton.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_page_skeleton-wasm-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Stubbed ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcollections_stubbed.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_stubbed-native-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Stubbed (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcollections_stubbed.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_stubbed-native-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Stubbed - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/collections_stubbed.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_stubbed-wasm-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections Stubbed - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/collections_stubbed.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections_stubbed-wasm-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Grid ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libgrid.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-grid-native-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Grid (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libgrid.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-grid-native-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Grid - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/grid.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-grid-wasm-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Grid - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/grid.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-grid-wasm-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcollections.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections-native-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcollections.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections-native-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/collections.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections-wasm-release",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Collections - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/collections.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-collections-ui-collections-wasm-debug",
            "presentation": { "group": "collections-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Container Orchestrator Examples",
            "program": "",
            "presentation": { "group": "container-orchestrator" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Orchestrator ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/liborchestrator.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-container-orchestrator-orchestrator-native-release",
            "presentation": { "group": "container-orchestrator" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Orchestrator (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/liborchestrator.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-container-orchestrator-orchestrator-native-debug",
            "presentation": { "group": "container-orchestrator" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Orchestrator - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/orchestrator.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-container-orchestrator-orchestrator-wasm-release",
            "presentation": { "group": "container-orchestrator" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Orchestrator - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/orchestrator.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-container-orchestrator-orchestrator-wasm-debug",
            "presentation": { "group": "container-orchestrator" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Containers Examples",
            "program": "",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Special Collections Carousel ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libspecial_collections_carousel.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-special_collections_carousel-native-release",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Special Collections Carousel (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libspecial_collections_carousel.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-special_collections_carousel-native-debug",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Special Collections Carousel - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/special_collections_carousel.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-special_collections_carousel-wasm-release",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Special Collections Carousel - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/special_collections_carousel.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-special_collections_carousel-wasm-debug",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pills ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libpills.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-pills-native-release",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pills (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libpills.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-pills-native-debug",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pills - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/pills.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-pills-wasm-release",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pills - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/pills.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-pills-wasm-debug",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libtitle.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-title-native-release",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libtitle.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-title-native-debug",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/title.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-title-wasm-release",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/title.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-containers-title-wasm-debug",
            "presentation": { "group": "containers" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Contextual Menu Examples",
            "program": "",
            "presentation": { "group": "contextual-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Contextual Menu ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcontextual_menu.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-contextual-menu-contextual_menu-native-release",
            "presentation": { "group": "contextual-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Contextual Menu (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcontextual_menu.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-contextual-menu-contextual_menu-native-debug",
            "presentation": { "group": "contextual-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Contextual Menu - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/contextual_menu.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-contextual-menu-contextual_menu-wasm-release",
            "presentation": { "group": "contextual-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Contextual Menu - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/contextual_menu.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-contextual-menu-contextual_menu-wasm-debug",
            "presentation": { "group": "contextual-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Details Examples Examples",
            "program": "",
            "presentation": { "group": "details-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Details Page Stubbed ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libdetails_page_stubbed.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-details-examples-details_page_stubbed-native-release",
            "presentation": { "group": "details-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Details Page Stubbed (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libdetails_page_stubbed.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-details-examples-details_page_stubbed-native-debug",
            "presentation": { "group": "details-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Details Page Stubbed - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/details_page_stubbed.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-details-examples-details_page_stubbed-wasm-release",
            "presentation": { "group": "details-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Details Page Stubbed - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/details_page_stubbed.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-details-examples-details_page_stubbed-wasm-debug",
            "presentation": { "group": "details-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Diagnostics Examples",
            "program": "",
            "presentation": { "group": "diagnostics" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Catflap Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcatflap_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-diagnostics-catflap_example-native-release",
            "presentation": { "group": "diagnostics" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Catflap Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcatflap_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-diagnostics-catflap_example-native-debug",
            "presentation": { "group": "diagnostics" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Catflap Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/catflap_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-diagnostics-catflap_example-wasm-release",
            "presentation": { "group": "diagnostics" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Catflap Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/catflap_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-diagnostics-catflap_example-wasm-debug",
            "presentation": { "group": "diagnostics" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Fableous Examples",
            "program": "",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Primary Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprimary_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-primary_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Primary Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprimary_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-primary_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Primary Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/primary_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-primary_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Primary Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/primary_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-primary_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pagination Dots Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libpagination_dots_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pagination_dots_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pagination Dots Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libpagination_dots_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pagination_dots_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pagination Dots Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/pagination_dots_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pagination_dots_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pagination Dots Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/pagination_dots_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pagination_dots_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Gradients Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libgradients_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-gradients_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Gradients Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libgradients_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-gradients_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Gradients Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/gradients_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-gradients_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Gradients Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/gradients_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-gradients_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Secondary Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libsecondary_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-secondary_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Secondary Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libsecondary_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-secondary_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Secondary Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/secondary_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-secondary_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Secondary Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/secondary_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-secondary_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Star Rating Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libstar_rating_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-star_rating_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Star Rating Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libstar_rating_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-star_rating_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Star Rating Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/star_rating_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-star_rating_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Star Rating Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/star_rating_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-star_rating_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Typography Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libtypography_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-typography_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Typography Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libtypography_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-typography_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Typography Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/typography_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-typography_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Typography Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/typography_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-typography_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Spinner Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libspinner_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-spinner_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Spinner Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libspinner_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-spinner_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Spinner Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/spinner_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-spinner_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Spinner Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/spinner_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-spinner_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprogress_bar_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-progress_bar_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprogress_bar_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-progress_bar_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/progress_bar_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-progress_bar_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/progress_bar_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-progress_bar_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Button List Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libbutton_list_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-button_list_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Button List Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libbutton_list_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-button_list_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Button List Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/button_list_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-button_list_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Button List Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/button_list_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-button_list_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Checkbox Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcheckbox_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-checkbox_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Checkbox Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcheckbox_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-checkbox_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Checkbox Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/checkbox_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-checkbox_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Checkbox Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/checkbox_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-checkbox_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Badges Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libbadges_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-badges_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Badges Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libbadges_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-badges_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Badges Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/badges_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-badges_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Badges Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/badges_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-badges_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pill Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libpill_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pill_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pill Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libpill_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pill_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pill Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/pill_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pill_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Pill Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/pill_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-pill_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Cards Tiles Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcards_tiles_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-cards_tiles_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Cards Tiles Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcards_tiles_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-cards_tiles_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Cards Tiles Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/cards_tiles_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-cards_tiles_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Cards Tiles Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/cards_tiles_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-cards_tiles_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toggle Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libtoggle_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toggle_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toggle Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libtoggle_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toggle_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toggle Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/toggle_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toggle_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toggle Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/toggle_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toggle_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Icon Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libicon_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-icon_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Icon Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libicon_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-icon_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Icon Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/icon_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-icon_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Icon Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/icon_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-icon_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toast Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libtoast_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toast_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toast Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libtoast_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toast_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toast Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/toast_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toast_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Toast Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/toast_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-toast_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Modal Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libmodal_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-modal_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Modal Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libmodal_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-modal_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Modal Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/modal_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-modal_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Modal Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/modal_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-modal_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Radio Buttons Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libradio_buttons_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-radio_buttons_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Radio Buttons Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libradio_buttons_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-radio_buttons_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Radio Buttons Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/radio_buttons_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-radio_buttons_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Radio Buttons Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/radio_buttons_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-radio_buttons_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Play Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libplay_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-play_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Play Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libplay_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-play_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Play Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/play_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-play_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Play Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/play_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-play_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Tertiary Button Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libtertiary_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-tertiary_button_example-native-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Tertiary Button Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libtertiary_button_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-tertiary_button_example-native-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Tertiary Button Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/tertiary_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-tertiary_button_example-wasm-release",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Tertiary Button Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/tertiary_button_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-fableous-tertiary_button_example-wasm-debug",
            "presentation": { "group": "fableous" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Genai Recap Examples",
            "program": "",
            "presentation": { "group": "genai-recap" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Recap Experience ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/librecap_experience.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-genai-recap-recap_experience-native-release",
            "presentation": { "group": "genai-recap" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Recap Experience (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/librecap_experience.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-genai-recap-recap_experience-native-debug",
            "presentation": { "group": "genai-recap" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Recap Experience - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/recap_experience.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-genai-recap-recap_experience-wasm-release",
            "presentation": { "group": "genai-recap" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Recap Experience - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/recap_experience.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-genai-recap-recap_experience-wasm-debug",
            "presentation": { "group": "genai-recap" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Hello World Examples",
            "program": "",
            "presentation": { "group": "hello-world" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Hello World ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libhello_world.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-hello-world-hello_world-native-release",
            "presentation": { "group": "hello-world" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Hello World (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libhello_world.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-hello-world-hello_world-native-debug",
            "presentation": { "group": "hello-world" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Hello World - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/hello_world.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-hello-world-hello_world-wasm-release",
            "presentation": { "group": "hello-world" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Hello World - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/hello_world.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-hello-world-hello_world-wasm-debug",
            "presentation": { "group": "hello-world" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Linear Examples",
            "program": "",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Epg Row Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libstation_details_epg_row_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_epg_row_example-native-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Epg Row Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libstation_details_epg_row_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_epg_row_example-native-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Epg Row Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/station_details_epg_row_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_epg_row_example-wasm-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Epg Row Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/station_details_epg_row_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_epg_row_example-wasm-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Skeleton ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libskeleton.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-skeleton-native-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Skeleton (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libskeleton.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-skeleton-native-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Skeleton - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/skeleton.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-skeleton-wasm-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Skeleton - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/skeleton.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-skeleton-wasm-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Logo ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libstation_logo.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_logo-native-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Logo (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libstation_logo.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_logo-native-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Logo - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/station_logo.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_logo-wasm-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Logo - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/station_logo.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_logo-wasm-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Airing Card ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libairing_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-airing_card-native-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Airing Card (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libairing_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-airing_card-native-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Airing Card - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/airing_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-airing_card-wasm-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Airing Card - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/airing_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-airing_card-wasm-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Page Carousel List ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/liblive_page_carousel_list.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-live_page_carousel_list-native-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Page Carousel List (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/liblive_page_carousel_list.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-live_page_carousel_list-native-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Page Carousel List - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/live_page_carousel_list.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-live_page_carousel_list-wasm-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Page Carousel List - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/live_page_carousel_list.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-live_page_carousel_list-wasm-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Airing Card Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libstation_details_airing_card_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_airing_card_example-native-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Airing Card Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libstation_details_airing_card_example.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_airing_card_example-native-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Airing Card Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/station_details_airing_card_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_airing_card_example-wasm-release",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Station Details Airing Card Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/station_details_airing_card_example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-linear-station_details_airing_card_example-wasm-debug",
            "presentation": { "group": "linear" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Media Background Examples",
            "program": "",
            "presentation": { "group": "media-background" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Media Background ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libmedia_background.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-media-background-media_background-native-release",
            "presentation": { "group": "media-background" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Media Background (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libmedia_background.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-media-background-media_background-native-debug",
            "presentation": { "group": "media-background" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Media Background - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/media_background.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-media-background-media_background-wasm-release",
            "presentation": { "group": "media-background" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Media Background - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/media_background.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-media-background-media_background-wasm-debug",
            "presentation": { "group": "media-background" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Navigation Menu Examples",
            "program": "",
            "presentation": { "group": "navigation-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Navigation Menu ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libnavigation_menu.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-navigation-menu-navigation_menu-native-release",
            "presentation": { "group": "navigation-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Navigation Menu (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libnavigation_menu.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-navigation-menu-navigation_menu-native-debug",
            "presentation": { "group": "navigation-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Navigation Menu - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/navigation_menu.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-navigation-menu-navigation_menu-wasm-release",
            "presentation": { "group": "navigation-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Navigation Menu - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/navigation_menu.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-navigation-menu-navigation_menu-wasm-debug",
            "presentation": { "group": "navigation-menu" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Playback Carousel Examples",
            "program": "",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Generic ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libgeneric.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-generic-native-release",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Generic (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libgeneric.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-generic-native-debug",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Generic - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/generic.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-generic-wasm-release",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Generic - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/generic.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-generic-wasm-debug",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Vertical ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libvertical.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-vertical-native-release",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Vertical (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libvertical.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-vertical-native-debug",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Vertical - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/vertical.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-vertical-wasm-release",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Vertical - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/vertical.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-vertical-wasm-debug",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libtitle.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-title-native-release",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libtitle.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-title-native-debug",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/title.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-title-wasm-release",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Title - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/title.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-carousel-title-wasm-debug",
            "presentation": { "group": "playback-carousel" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Playback Examples Examples",
            "program": "",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Resizable Player ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libresizable_player.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-resizable_player-native-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Resizable Player (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libresizable_player.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-resizable_player-native-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Resizable Player - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/resizable_player.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-resizable_player-wasm-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Resizable Player - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/resizable_player.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-resizable_player-wasm-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Focus ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libfocus.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-focus-native-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Focus (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libfocus.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-focus-native-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Focus - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/focus.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-focus-wasm-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Focus - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/focus.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-focus-wasm-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Dwell ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libautoplay_dwell.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_dwell-native-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Dwell (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libautoplay_dwell.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_dwell-native-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Dwell - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/autoplay_dwell.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_dwell-wasm-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Dwell - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/autoplay_dwell.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_dwell-wasm-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Max Play Duration ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libautoplay_max_play_duration.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_max_play_duration-native-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Max Play Duration (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libautoplay_max_play_duration.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_max_play_duration-native-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Max Play Duration - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/autoplay_max_play_duration.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_max_play_duration-wasm-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Autoplay Max Play Duration - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/autoplay_max_play_duration.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-autoplay_max_play_duration-wasm-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ttff ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libttff.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-ttff-native-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ttff (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libttff.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-ttff-native-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ttff - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/ttff.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-ttff-wasm-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ttff - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/ttff.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-ttff-wasm-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libexample.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-example-native-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libexample.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-example-native-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-example-wasm-release",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/example.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-examples-example-wasm-debug",
            "presentation": { "group": "playback-examples" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Playback Live Event Betting Ux Examples",
            "program": "",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Odds Carousel ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/liblive_odds_carousel.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-live_odds_carousel-native-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Odds Carousel (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/liblive_odds_carousel.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-live_odds_carousel-native-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Odds Carousel - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/live_odds_carousel.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-live_odds_carousel-wasm-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Live Odds Carousel - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/live_odds_carousel.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-live_odds_carousel-wasm-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0My Bets Card ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libmy_bets_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-my_bets_card-native-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0My Bets Card (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libmy_bets_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-my_bets_card-native-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0My Bets Card - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/my_bets_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-my_bets_card-wasm-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0My Bets Card - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/my_bets_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-my_bets_card-wasm-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Account Sync Card ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libaccount_sync_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-account_sync_card-native-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Account Sync Card (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libaccount_sync_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-account_sync_card-native-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Account Sync Card - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/account_sync_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-account_sync_card-wasm-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Account Sync Card - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/account_sync_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-account_sync_card-wasm-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Go Live Card ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libgo_live_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-go_live_card-native-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Go Live Card (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libgo_live_card.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-go_live_card-native-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Go Live Card - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/go_live_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-go_live_card-wasm-release",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Go Live Card - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/go_live_card.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-betting-ux-go_live_card-wasm-debug",
            "presentation": { "group": "playback-live-event-betting-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Playback Live Event Explore Ux Examples",
            "program": "",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Head To Head ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libhead_to_head.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-head_to_head-native-release",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Head To Head (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libhead_to_head.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-head_to_head-native-debug",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Head To Head - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/head_to_head.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-head_to_head-wasm-release",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Head To Head - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/head_to_head.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-head_to_head-wasm-debug",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Common Action Button ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcommon_action_button.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-common_action_button-native-release",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Common Action Button (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcommon_action_button.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-common_action_button-native-debug",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Common Action Button - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/common_action_button.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-common_action_button-wasm-release",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Common Action Button - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/common_action_button.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-explore-ux-common_action_button-wasm-debug",
            "presentation": { "group": "playback-live-event-explore-ux" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Playback Live Event Key Moments Examples",
            "program": "",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Metadata ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libmetadata.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-metadata-native-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Metadata (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libmetadata.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-metadata-native-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Metadata - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/metadata.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-metadata-wasm-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Metadata - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/metadata.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-metadata-wasm-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Panel ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libpanel.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-panel-native-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Panel (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libpanel.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-panel-native-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Panel - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/panel.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-panel-wasm-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Panel - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/panel.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-panel-wasm-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buttons ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libbuttons.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-buttons-native-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buttons (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libbuttons.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-buttons-native-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buttons - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/buttons.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-buttons-wasm-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Buttons - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/buttons.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-buttons-wasm-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Replay Counter ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libreplay_counter.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-replay_counter-native-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Replay Counter (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libreplay_counter.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-replay_counter-native-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Replay Counter - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/replay_counter.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-replay_counter-wasm-release",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Replay Counter - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/replay_counter.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-live-event-key-moments-replay_counter-wasm-debug",
            "presentation": { "group": "playback-live-event-key-moments" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Playback Ui Examples",
            "program": "",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Trickplay ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libtrickplay.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-trickplay-native-release",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Trickplay (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libtrickplay.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-trickplay-native-debug",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Trickplay - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/trickplay.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-trickplay-wasm-release",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Trickplay - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/trickplay.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-trickplay-wasm-debug",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprogress_bar.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-progress_bar-native-release",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprogress_bar.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-progress_bar-native-debug",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/progress_bar.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-progress_bar-wasm-release",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Progress Bar - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/progress_bar.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-progress_bar-wasm-debug",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ad Timer ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libad_timer.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-ad_timer-native-release",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ad Timer (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libad_timer.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-ad_timer-native-debug",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ad Timer - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/ad_timer.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-ad_timer-wasm-release",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Ad Timer - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/ad_timer.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-playback-ui-ad_timer-wasm-debug",
            "presentation": { "group": "playback-ui" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Profile Selection Examples",
            "program": "",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Buttons ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprofile_buttons.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_buttons-native-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Buttons (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprofile_buttons.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_buttons-native-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Buttons - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/profile_buttons.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_buttons-wasm-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Buttons - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/profile_buttons.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_buttons-wasm-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Ui V2 ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprofile_selection_page_ui_v2.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_ui_v2-native-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Ui V2 (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprofile_selection_page_ui_v2.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_ui_v2-native-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Ui V2 - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/profile_selection_page_ui_v2.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_ui_v2-wasm-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Ui V2 - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/profile_selection_page_ui_v2.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_ui_v2-wasm-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Pin Dialog ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprofile_pin_dialog.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_pin_dialog-native-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Pin Dialog (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprofile_pin_dialog.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_pin_dialog-native-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Pin Dialog - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/profile_pin_dialog.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_pin_dialog-wasm-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Pin Dialog - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/profile_pin_dialog.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_pin_dialog-wasm-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profiles V2 ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprofiles_v2.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profiles_v2-native-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profiles V2 (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprofiles_v2.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profiles_v2-native-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profiles V2 - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/profiles_v2.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profiles_v2-wasm-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profiles V2 - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/profiles_v2.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profiles_v2-wasm-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Stubbed ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprofile_selection_page_stubbed.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_stubbed-native-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Stubbed (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprofile_selection_page_stubbed.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_stubbed-native-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Stubbed - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/profile_selection_page_stubbed.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_stubbed-wasm-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page Stubbed - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/profile_selection_page_stubbed.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page_stubbed-wasm-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprofile_selection_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page-native-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprofile_selection_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page-native-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/profile_selection_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page-wasm-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Selection Page - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/profile_selection_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_selection_page-wasm-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Error Modal ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libprofile_error_modal.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_error_modal-native-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Error Modal (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libprofile_error_modal.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_error_modal-native-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Error Modal - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/profile_error_modal.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_error_modal-wasm-release",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Profile Error Modal - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/profile_error_modal.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-profile-selection-profile_error_modal-wasm-debug",
            "presentation": { "group": "profile-selection" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Search Page Examples",
            "program": "",
            "presentation": { "group": "search-page" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Full Page ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libfull_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-search-page-full_page-native-release",
            "presentation": { "group": "search-page" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Full Page (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libfull_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-search-page-full_page-native-debug",
            "presentation": { "group": "search-page" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Full Page - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/full_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-search-page-full_page-wasm-release",
            "presentation": { "group": "search-page" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Full Page - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/full_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-search-page-full_page-wasm-debug",
            "presentation": { "group": "search-page" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Sports Schedule Examples",
            "program": "",
            "presentation": { "group": "sports-schedule" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example Schedule Page ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libexample_schedule_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-sports-schedule-example_schedule_page-native-release",
            "presentation": { "group": "sports-schedule" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example Schedule Page (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libexample_schedule_page.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-sports-schedule-example_schedule_page-native-debug",
            "presentation": { "group": "sports-schedule" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example Schedule Page - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/example_schedule_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-sports-schedule-example_schedule_page-wasm-release",
            "presentation": { "group": "sports-schedule" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Example Schedule Page - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/example_schedule_page.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-sports-schedule-example_schedule_page-wasm-debug",
            "presentation": { "group": "sports-schedule" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Title Details Examples",
            "program": "",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Entitlement ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcomponents_entitlement.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_entitlement-native-release",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Entitlement (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcomponents_entitlement.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_entitlement-native-debug",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Entitlement - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/components_entitlement.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_entitlement-wasm-release",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Entitlement - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/components_entitlement.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_entitlement-wasm-debug",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Title ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libcomponents_title.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_title-native-release",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Title (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libcomponents_title.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_title-native-debug",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Title - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/components_title.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_title-wasm-release",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Components Title - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/components_title.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-title-details-components_title-wasm-debug",
            "presentation": { "group": "title-details" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Xray Vod Examples",
            "program": "",
            "presentation": { "group": "xray-vod" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Xray Vod ",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/release/examples/libxray_vod.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-xray-vod-xray_vod-native-release",
            "presentation": { "group": "xray-vod" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Xray Vod (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-native-path=${workspaceFolder}/build/private/cargo-target/debug/examples/libxray_vod.dylib"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-xray-vod-xray_vod-native-debug",
            "presentation": { "group": "xray-vod" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Xray Vod - Wasm",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/xray_vod.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-xray-vod-xray_vod-wasm-release",
            "presentation": { "group": "xray-vod" }
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "\u00A0\u00A0Xray Vod - Wasm (Debug)",
            "program": "${env:IGNITION_X_PATH}/bin/ignition",
            "args": ["--local-wasm-path=${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/xray_vod.wasm"],
            "cwd": "${env:IGNITION_X_PATH}",
            "preLaunchTask": "build-example-xray-vod-xray_vod-wasm-debug",
            "presentation": { "group": "xray-vod" }
        }
    ]
}
