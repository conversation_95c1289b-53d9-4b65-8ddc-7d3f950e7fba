{"version": "2.0.0", "tasks": [{"label": "build-client-native-debug", "command": "cargo", "args": ["build", "-p", "amzn-av-living-room-rust-client", "--lib", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/app-start"}}, {"label": "build-client-wasm", "command": "cargo", "args": ["build", "--target", "wasm32-wasip1", "--release", "--features=amzn-ignx-compositron/use_ignx_local"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/app-start"}}, {"label": "build-example-ads-playback-surface_x_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "surface_x_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-ads-playback-surface_x_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "surface_x_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-ads-playback-surface_x_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "surface_x_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-ads-playback-surface_x_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "surface_x_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-ads-playback-iva_baseline_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "iva_baseline_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-ads-playback-iva_baseline_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "iva_baseline_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-ads-playback-iva_baseline_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "iva_baseline_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-ads-playback-iva_baseline_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "iva_baseline_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/ads-playback"}}, {"label": "build-example-app-drawer-drawer-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "drawer", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/app-drawer"}}, {"label": "build-example-app-drawer-drawer-native-debug", "command": "cargo", "args": ["rustc", "--example", "drawer", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/app-drawer"}}, {"label": "build-example-app-drawer-drawer-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "drawer", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/app-drawer"}}, {"label": "build-example-app-drawer-drawer-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "drawer", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/app-drawer"}}, {"label": "build-example-buybox-buybox-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "buybox", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/buybox"}}, {"label": "build-example-buybox-buybox-native-debug", "command": "cargo", "args": ["rustc", "--example", "buybox", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/buybox"}}, {"label": "build-example-buybox-buybox-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "buybox", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/buybox"}}, {"label": "build-example-buybox-buybox-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "buybox", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/buybox"}}, {"label": "build-example-checkout-bolt_confirmation_page-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "bolt_confirmation_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page-native-debug", "command": "cargo", "args": ["rustc", "--example", "bolt_confirmation_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "bolt_confirmation_page", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "bolt_confirmation_page", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "bolt_confirmation_page_v2_single_monthly", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-native-debug", "command": "cargo", "args": ["rustc", "--example", "bolt_confirmation_page_v2_single_monthly", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "bolt_confirmation_page_v2_single_monthly", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_single_monthly-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "bolt_confirmation_page_v2_single_monthly", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "bolt_confirmation_page_v2_monthly_and_annual", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-native-debug", "command": "cargo", "args": ["rustc", "--example", "bolt_confirmation_page_v2_monthly_and_annual", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "bolt_confirmation_page_v2_monthly_and_annual", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-checkout-bolt_confirmation_page_v2_monthly_and_annual-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "bolt_confirmation_page_v2_monthly_and_annual", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/checkout"}}, {"label": "build-example-collections-ui-collections_all_containers-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "collections_all_containers", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_all_containers-native-debug", "command": "cargo", "args": ["rustc", "--example", "collections_all_containers", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_all_containers-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "collections_all_containers", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_all_containers-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "collections_all_containers", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_page_skeleton-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "collections_page_skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_page_skeleton-native-debug", "command": "cargo", "args": ["rustc", "--example", "collections_page_skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_page_skeleton-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "collections_page_skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_page_skeleton-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "collections_page_skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_stubbed-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "collections_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_stubbed-native-debug", "command": "cargo", "args": ["rustc", "--example", "collections_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_stubbed-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "collections_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections_stubbed-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "collections_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-grid-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "grid", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-grid-native-debug", "command": "cargo", "args": ["rustc", "--example", "grid", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-grid-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "grid", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-grid-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "grid", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "collections", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections-native-debug", "command": "cargo", "args": ["rustc", "--example", "collections", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "collections", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-collections-ui-collections-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "collections", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/collections-ui"}}, {"label": "build-example-container-orchestrator-orchestrator-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "orchestrator", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/container-orchestrator"}}, {"label": "build-example-container-orchestrator-orchestrator-native-debug", "command": "cargo", "args": ["rustc", "--example", "orchestrator", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/container-orchestrator"}}, {"label": "build-example-container-orchestrator-orchestrator-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "orchestrator", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/container-orchestrator"}}, {"label": "build-example-container-orchestrator-orchestrator-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "orchestrator", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/container-orchestrator"}}, {"label": "build-example-containers-special_collections_carousel-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "special_collections_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-special_collections_carousel-native-debug", "command": "cargo", "args": ["rustc", "--example", "special_collections_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-special_collections_carousel-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "special_collections_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-special_collections_carousel-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "special_collections_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-pills-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "pills", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-pills-native-debug", "command": "cargo", "args": ["rustc", "--example", "pills", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-pills-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "pills", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-pills-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "pills", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-title-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-title-native-debug", "command": "cargo", "args": ["rustc", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-title-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-containers-title-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/containers"}}, {"label": "build-example-contextual-menu-contextual_menu-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "contextual_menu", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/contextual-menu"}}, {"label": "build-example-contextual-menu-contextual_menu-native-debug", "command": "cargo", "args": ["rustc", "--example", "contextual_menu", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/contextual-menu"}}, {"label": "build-example-contextual-menu-contextual_menu-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "contextual_menu", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/contextual-menu"}}, {"label": "build-example-contextual-menu-contextual_menu-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "contextual_menu", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/contextual-menu"}}, {"label": "build-example-details-examples-details_page_stubbed-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "details_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/details-examples"}}, {"label": "build-example-details-examples-details_page_stubbed-native-debug", "command": "cargo", "args": ["rustc", "--example", "details_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/details-examples"}}, {"label": "build-example-details-examples-details_page_stubbed-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "details_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/details-examples"}}, {"label": "build-example-details-examples-details_page_stubbed-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "details_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/details-examples"}}, {"label": "build-example-diagnostics-catflap_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "catflap_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/diagnostics"}}, {"label": "build-example-diagnostics-catflap_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "catflap_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/diagnostics"}}, {"label": "build-example-diagnostics-catflap_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "catflap_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/diagnostics"}}, {"label": "build-example-diagnostics-catflap_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "catflap_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/diagnostics"}}, {"label": "build-example-fableous-primary_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "primary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-primary_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "primary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-primary_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "primary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-primary_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "primary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pagination_dots_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "pagination_dots_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pagination_dots_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "pagination_dots_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pagination_dots_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "pagination_dots_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pagination_dots_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "pagination_dots_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-gradients_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "gradients_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-gradients_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "gradients_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-gradients_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "gradients_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-gradients_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "gradients_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-secondary_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "secondary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-secondary_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "secondary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-secondary_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "secondary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-secondary_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "secondary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-star_rating_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "star_rating_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-star_rating_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "star_rating_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-star_rating_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "star_rating_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-star_rating_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "star_rating_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-typography_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "typography_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-typography_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "typography_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-typography_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "typography_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-typography_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "typography_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-spinner_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "spinner_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-spinner_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "spinner_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-spinner_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "spinner_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-spinner_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "spinner_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-progress_bar_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "progress_bar_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-progress_bar_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "progress_bar_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-progress_bar_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "progress_bar_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-progress_bar_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "progress_bar_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-button_list_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "button_list_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-button_list_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "button_list_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-button_list_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "button_list_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-button_list_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "button_list_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-checkbox_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "checkbox_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-checkbox_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "checkbox_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-checkbox_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "checkbox_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-checkbox_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "checkbox_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-badges_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "badges_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-badges_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "badges_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-badges_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "badges_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-badges_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "badges_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pill_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "pill_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pill_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "pill_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pill_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "pill_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-pill_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "pill_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-cards_tiles_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "cards_tiles_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-cards_tiles_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "cards_tiles_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-cards_tiles_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "cards_tiles_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-cards_tiles_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "cards_tiles_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toggle_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "toggle_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toggle_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "toggle_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toggle_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "toggle_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toggle_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "toggle_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-icon_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "icon_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-icon_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "icon_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-icon_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "icon_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-icon_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "icon_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toast_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "toast_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toast_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "toast_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toast_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "toast_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-toast_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "toast_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-modal_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "modal_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-modal_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "modal_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-modal_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "modal_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-modal_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "modal_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-radio_buttons_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "radio_buttons_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-radio_buttons_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "radio_buttons_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-radio_buttons_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "radio_buttons_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-radio_buttons_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "radio_buttons_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-play_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "play_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-play_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "play_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-play_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "play_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-play_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "play_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-tertiary_button_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "tertiary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-tertiary_button_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "tertiary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-tertiary_button_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "tertiary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-fableous-tertiary_button_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "tertiary_button_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/fableous"}}, {"label": "build-example-genai-recap-recap_experience-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "recap_experience", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/genai-recap"}}, {"label": "build-example-genai-recap-recap_experience-native-debug", "command": "cargo", "args": ["rustc", "--example", "recap_experience", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/genai-recap"}}, {"label": "build-example-genai-recap-recap_experience-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "recap_experience", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/genai-recap"}}, {"label": "build-example-genai-recap-recap_experience-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "recap_experience", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/genai-recap"}}, {"label": "build-example-hello-world-hello_world-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "hello_world", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/hello-world"}}, {"label": "build-example-hello-world-hello_world-native-debug", "command": "cargo", "args": ["rustc", "--example", "hello_world", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/hello-world"}}, {"label": "build-example-hello-world-hello_world-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "hello_world", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/hello-world"}}, {"label": "build-example-hello-world-hello_world-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "hello_world", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/hello-world"}}, {"label": "build-example-linear-station_details_epg_row_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "station_details_epg_row_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_details_epg_row_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "station_details_epg_row_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_details_epg_row_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "station_details_epg_row_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_details_epg_row_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "station_details_epg_row_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-skeleton-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-skeleton-native-debug", "command": "cargo", "args": ["rustc", "--example", "skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-skeleton-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-skeleton-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "skeleton", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_logo-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "station_logo", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_logo-native-debug", "command": "cargo", "args": ["rustc", "--example", "station_logo", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_logo-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "station_logo", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_logo-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "station_logo", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-airing_card-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "airing_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-airing_card-native-debug", "command": "cargo", "args": ["rustc", "--example", "airing_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-airing_card-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "airing_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-airing_card-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "airing_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-live_page_carousel_list-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "live_page_carousel_list", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-live_page_carousel_list-native-debug", "command": "cargo", "args": ["rustc", "--example", "live_page_carousel_list", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-live_page_carousel_list-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "live_page_carousel_list", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-live_page_carousel_list-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "live_page_carousel_list", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_details_airing_card_example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "station_details_airing_card_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_details_airing_card_example-native-debug", "command": "cargo", "args": ["rustc", "--example", "station_details_airing_card_example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_details_airing_card_example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "station_details_airing_card_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-linear-station_details_airing_card_example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "station_details_airing_card_example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/linear"}}, {"label": "build-example-media-background-media_background-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "media_background", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/media-background"}}, {"label": "build-example-media-background-media_background-native-debug", "command": "cargo", "args": ["rustc", "--example", "media_background", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/media-background"}}, {"label": "build-example-media-background-media_background-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "media_background", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/media-background"}}, {"label": "build-example-media-background-media_background-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "media_background", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/media-background"}}, {"label": "build-example-navigation-menu-navigation_menu-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "navigation_menu", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/navigation-menu"}}, {"label": "build-example-navigation-menu-navigation_menu-native-debug", "command": "cargo", "args": ["rustc", "--example", "navigation_menu", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/navigation-menu"}}, {"label": "build-example-navigation-menu-navigation_menu-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "navigation_menu", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/navigation-menu"}}, {"label": "build-example-navigation-menu-navigation_menu-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "navigation_menu", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/navigation-menu"}}, {"label": "build-example-playback-carousel-generic-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "generic", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-generic-native-debug", "command": "cargo", "args": ["rustc", "--example", "generic", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-generic-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "generic", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-generic-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "generic", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-vertical-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "vertical", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-vertical-native-debug", "command": "cargo", "args": ["rustc", "--example", "vertical", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-vertical-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "vertical", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-vertical-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "vertical", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-title-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-title-native-debug", "command": "cargo", "args": ["rustc", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-title-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-carousel-title-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "title", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-carousel"}}, {"label": "build-example-playback-examples-resizable_player-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "resizable_player", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-resizable_player-native-debug", "command": "cargo", "args": ["rustc", "--example", "resizable_player", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-resizable_player-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "resizable_player", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-resizable_player-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "resizable_player", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-focus-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "focus", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-focus-native-debug", "command": "cargo", "args": ["rustc", "--example", "focus", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-focus-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "focus", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-focus-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "focus", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_dwell-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "autoplay_dwell", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_dwell-native-debug", "command": "cargo", "args": ["rustc", "--example", "autoplay_dwell", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_dwell-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "autoplay_dwell", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_dwell-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "autoplay_dwell", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_max_play_duration-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "autoplay_max_play_duration", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_max_play_duration-native-debug", "command": "cargo", "args": ["rustc", "--example", "autoplay_max_play_duration", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_max_play_duration-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "autoplay_max_play_duration", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-autoplay_max_play_duration-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "autoplay_max_play_duration", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-ttff-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "ttff", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-ttff-native-debug", "command": "cargo", "args": ["rustc", "--example", "ttff", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-ttff-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "ttff", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-ttff-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "ttff", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-example-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-example-native-debug", "command": "cargo", "args": ["rustc", "--example", "example", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-example-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-examples-example-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "example", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-examples"}}, {"label": "build-example-playback-live-event-betting-ux-live_odds_carousel-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "live_odds_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-live_odds_carousel-native-debug", "command": "cargo", "args": ["rustc", "--example", "live_odds_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-live_odds_carousel-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "live_odds_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-live_odds_carousel-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "live_odds_carousel", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-my_bets_card-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "my_bets_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-my_bets_card-native-debug", "command": "cargo", "args": ["rustc", "--example", "my_bets_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-my_bets_card-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "my_bets_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-my_bets_card-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "my_bets_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-account_sync_card-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "account_sync_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,amzn-ignx-compositron/skip_timer_mocks", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-account_sync_card-native-debug", "command": "cargo", "args": ["rustc", "--example", "account_sync_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-account_sync_card-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "account_sync_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-account_sync_card-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "account_sync_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-go_live_card-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "go_live_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-go_live_card-native-debug", "command": "cargo", "args": ["rustc", "--example", "go_live_card", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-go_live_card-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "go_live_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-betting-ux-go_live_card-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "go_live_card", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-betting-ux"}}, {"label": "build-example-playback-live-event-explore-ux-head_to_head-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "head_to_head", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,test_utils", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-explore-ux-head_to_head-native-debug", "command": "cargo", "args": ["rustc", "--example", "head_to_head", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,test_utils", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-explore-ux-head_to_head-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "head_to_head", "--features=amzn-ignx-compositron/allow_no_js_vm,test_utils", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-explore-ux-head_to_head-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "head_to_head", "--features=amzn-ignx-compositron/allow_no_js_vm,test_utils", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-explore-ux-common_action_button-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "common_action_button", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-explore-ux-common_action_button-native-debug", "command": "cargo", "args": ["rustc", "--example", "common_action_button", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-explore-ux-common_action_button-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "common_action_button", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-explore-ux-common_action_button-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "common_action_button", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-explore-ux"}}, {"label": "build-example-playback-live-event-key-moments-metadata-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "metadata", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-metadata-native-debug", "command": "cargo", "args": ["rustc", "--example", "metadata", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-metadata-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "metadata", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-metadata-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "metadata", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-panel-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "panel", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-panel-native-debug", "command": "cargo", "args": ["rustc", "--example", "panel", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-panel-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "panel", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-panel-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "panel", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-buttons-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "buttons", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-buttons-native-debug", "command": "cargo", "args": ["rustc", "--example", "buttons", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-buttons-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "buttons", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-buttons-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "buttons", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-replay_counter-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "replay_counter", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-replay_counter-native-debug", "command": "cargo", "args": ["rustc", "--example", "replay_counter", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-replay_counter-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "replay_counter", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-live-event-key-moments-replay_counter-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "replay_counter", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-live-event-key-moments"}}, {"label": "build-example-playback-ui-trickplay-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "trickplay", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-trickplay-native-debug", "command": "cargo", "args": ["rustc", "--example", "trickplay", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-trickplay-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "trickplay", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-trickplay-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "trickplay", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-progress_bar-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "progress_bar", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-progress_bar-native-debug", "command": "cargo", "args": ["rustc", "--example", "progress_bar", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-progress_bar-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "progress_bar", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-progress_bar-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "progress_bar", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-ad_timer-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "ad_timer", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-ad_timer-native-debug", "command": "cargo", "args": ["rustc", "--example", "ad_timer", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-ad_timer-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "ad_timer", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-playback-ui-ad_timer-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "ad_timer", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/playback-ui"}}, {"label": "build-example-profile-selection-profile_buttons-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "profile_buttons", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_buttons-native-debug", "command": "cargo", "args": ["rustc", "--example", "profile_buttons", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_buttons-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "profile_buttons", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_buttons-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "profile_buttons", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_ui_v2-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "profile_selection_page_ui_v2", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_ui_v2-native-debug", "command": "cargo", "args": ["rustc", "--example", "profile_selection_page_ui_v2", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_ui_v2-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "profile_selection_page_ui_v2", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_ui_v2-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "profile_selection_page_ui_v2", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_pin_dialog-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "profile_pin_dialog", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_pin_dialog-native-debug", "command": "cargo", "args": ["rustc", "--example", "profile_pin_dialog", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_pin_dialog-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "profile_pin_dialog", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_pin_dialog-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "profile_pin_dialog", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profiles_v2-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "profiles_v2", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profiles_v2-native-debug", "command": "cargo", "args": ["rustc", "--example", "profiles_v2", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profiles_v2-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "profiles_v2", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profiles_v2-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "profiles_v2", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_stubbed-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "profile_selection_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_stubbed-native-debug", "command": "cargo", "args": ["rustc", "--example", "profile_selection_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_stubbed-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "profile_selection_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page_stubbed-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "profile_selection_page_stubbed", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "profile_selection_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page-native-debug", "command": "cargo", "args": ["rustc", "--example", "profile_selection_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "profile_selection_page", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_selection_page-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "profile_selection_page", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_error_modal-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "profile_error_modal", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_error_modal-native-debug", "command": "cargo", "args": ["rustc", "--example", "profile_error_modal", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_error_modal-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "profile_error_modal", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-profile-selection-profile_error_modal-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "profile_error_modal", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/profile-selection"}}, {"label": "build-example-search-page-full_page-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "full_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/search-page"}}, {"label": "build-example-search-page-full_page-native-debug", "command": "cargo", "args": ["rustc", "--example", "full_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/search-page"}}, {"label": "build-example-search-page-full_page-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "full_page", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/search-page"}}, {"label": "build-example-search-page-full_page-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "full_page", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/search-page"}}, {"label": "build-example-sports-schedule-example_schedule_page-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "example_schedule_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/sports-schedule"}}, {"label": "build-example-sports-schedule-example_schedule_page-native-debug", "command": "cargo", "args": ["rustc", "--example", "example_schedule_page", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local,example_data", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/sports-schedule"}}, {"label": "build-example-sports-schedule-example_schedule_page-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "example_schedule_page", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/sports-schedule"}}, {"label": "build-example-sports-schedule-example_schedule_page-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "example_schedule_page", "--features=amzn-ignx-compositron/allow_no_js_vm,example_data", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/sports-schedule"}}, {"label": "build-example-title-details-components_entitlement-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "components_entitlement", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-title-details-components_entitlement-native-debug", "command": "cargo", "args": ["rustc", "--example", "components_entitlement", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-title-details-components_entitlement-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "components_entitlement", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-title-details-components_entitlement-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "components_entitlement", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-title-details-components_title-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "components_title", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-title-details-components_title-native-debug", "command": "cargo", "args": ["rustc", "--example", "components_title", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-title-details-components_title-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "components_title", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-title-details-components_title-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "components_title", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/title-details"}}, {"label": "build-example-xray-vod-xray_vod-native-release", "command": "cargo", "args": ["rustc", "--release", "--example", "xray_vod", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/xray-vod"}}, {"label": "build-example-xray-vod-xray_vod-native-debug", "command": "cargo", "args": ["rustc", "--example", "xray_vod", "--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local", "--crate-type=cdylib"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/xray-vod"}}, {"label": "build-example-xray-vod-xray_vod-wasm-release", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--release", "--example", "xray_vod", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/xray-vod"}}, {"label": "build-example-xray-vod-xray_vod-wasm-debug", "command": "cargo", "args": ["rustc", "--target", "wasm32-wasip1", "--example", "xray_vod", "--features=amzn-ignx-compositron/allow_no_js_vm", "--crate-type=bin"], "type": "shell", "options": {"cwd": "${workspaceFolder}/crates/xray-vod"}}]}