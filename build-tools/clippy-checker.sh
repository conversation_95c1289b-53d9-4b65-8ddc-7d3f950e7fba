#!/bin/bash

display_usage() {
    cat <<EOF
OVERVIEW:
    Used to get overall clippy results for the project helpful to monitor the warnings of different crates

USAGE:
    ./hybrid-run-native.sh

OPTIONS:
    --help              Display this message
    --no_color          Disables the ANSI codes used to display colors
    --include_message   Includes the warning/error messages from clippy
    --crate             Filters results only to a specific crate
EOF
}

help="0"
RED="\e[31m"
ORANGE="\e[38;5;214m"
END="\e[0m"
crate="0"
include_message="0"
client_path=$(dirname $(dirname "$0"))
while [[ $# -gt 0 ]]; do
  case $1 in
    --no_color)
	    RED=""
	    ORANGE=""
	    END=""
	    shift
	    ;;
	  --include_message)
	    include_message="1"
	    shift
	    ;;
	  --crate)
	    crate="$2"
      shift
      shift
      ;;
    --help)
      help="1"
      shift # past argument
      ;;
    *)
        echo "Error: option '$1' is invalid."
        exit 1
        ;;
  esac
done

if [ "$help" = "1" ]; then
  display_usage
  exit 0
fi

cmd="cargo clippy"
clippy_output=$($cmd 2>&1)
messages=()

# Output errors for any crate if existing
echo "$clippy_output" | while read -r line; do
  messages+=("$line")
  if [[ $line  =~ ^warning:\ \`([^\`]*)\`[^0-9]*([0-9]*) ]]; then
    messages=()
  fi

  if [[ $line  =~ ^error:\ could\ not\ compile\ \`([^\`]*)\`[^0-9]*([0-9]*) ]]; then
    crate_name="${BASH_REMATCH[1]}"
    errors="${BASH_REMATCH[2]}"

    if [ "$crate" != "0" ]; then
        if [ "$crate" != "$crate_name" ]; then
            messages=()
            continue
        fi
    fi

    printf "${RED}Crate: $crate_name, Errors: $errors ${END}\n\r"

    if [ "$include_message" = "1" ]; then
      for i in "${!messages[@]}"; do
        echo -e "${messages[$i]}"
      done
    fi

  fi
done

# Empty out the messages
messages=()

# Output the warnings for each crate
echo "$clippy_output" | while read -r line; do
  messages+=("$line")
  if [[ $line  =~ ^warning:\ \`([^\`]*)\`[^0-9]*([0-9]*) ]]; then
    crate_name="${BASH_REMATCH[1]}"
    warnings="${BASH_REMATCH[2]}"

    if [ "$crate" != "0" ]; then
        if [ "$crate" != "$crate_name" ]; then
            messages=()
            continue
        fi
    fi

    printf "${ORANGE}Crate: $crate_name, Warnings: $warnings ${END}\n\r"

    if [ "$include_message" = "1" ]; then
      for i in "${!messages[@]}"; do
        echo -e "${messages[$i]}"
      done
    fi

    messages=()
  fi
done