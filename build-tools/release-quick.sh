#!/bin/bash

set -euo pipefail


display_usage() {
    cat <<EOF
OVERVIEW:
    Runs a quick build usually used before deploying using put-this-on-that

USAGE:
    ./release-quick.sh

OPTIONS:
    --help              Display this message
EOF
}

# TODO: Add aot, threads to the options
help="0"
while [[ $# -gt 0 ]]; do
  case $1 in
    --help)
      help="1"
      shift # past argument
      ;;
    *)
        echo "Error: option '$1' is invalid."
        exit 1
        ;;
  esac
done

if [ "$help" = "1" ]; then
  display_usage
  exit 0
fi

# this must not be run in CI since this is not a Git repo on our build hosts
if [ -d "$(brazil-path package-src-root)/.git" ]; then
  echo "setting up Git repo to use included hooks..."
  git config core.hooksPath "$(brazil-path package-src-root)/git_hooks"
fi

cargo_release_dir="$(cargo brazil print target-dir)/wasm32-wasip1/release"
brazil_build_dir="$(brazil-path package-build-root)"

lib_dir="$brazil_build_dir/lib"
lib_debug_dir="$brazil_build_dir/lib_debug"

if [ ! -d "$lib_dir" ]; then
  echo "Creating lib directory as missing!"
  mkdir $lib_dir
fi

if [ ! -d "$lib_debug_dir" ]; then
  echo "Creating lib_debug directory as missing!"
  mkdir $lib_debug_dir
fi

finalise_build() {
   input_name="amzn-av-living-room-rust-client.wasm"
   output_name=$1

   wasm-opt -Oz "$cargo_release_dir/$input_name" --output "$cargo_release_dir/$input_name"
   wasm-opt "$cargo_release_dir/$input_name" --strip-debug --strip-dwarf --strip-producers --strip-target-features --output "$cargo_release_dir/$input_name"
   cp -f "$cargo_release_dir/$input_name" "$lib_dir/$output_name"
}

#Standard wasm build file
cargo build --release --target wasm32-wasip1
finalise_build "amzn_av_living_room_rust_client.wasm"
standard_wasm_file="amzn_av_living_room_rust_client.wasm"

bundles=(
  "amzn_av_living_room_rust_client_test_utils.wasm"
  "amzn_av_living_room_rust_client_test_utils_debug_renderer.wasm"
  "amzn_av_living_room_rust_client_aot.wasm"
  "amzn_av_living_room_rust_client_threads.wasm"
  "amzn_av_living_room_rust_client_threads_aot.wasm"
  "amzn_av_living_room_rust_client_aot_test_utils.wasm"
  "amzn_av_living_room_rust_client_threads_test_utils.wasm"
  "amzn_av_living_room_rust_client_threads_aot_test_utils.wasm"
)
# Ensure required bundles are all created to avoid issues when deploying (most users don't build threads/aot/test_utils)
for item in "${bundles[@]}"
do
  if [ ! -f "$lib_dir/$item" ]; then
    cp -f "$lib_dir/$standard_wasm_file" "$lib_dir/$item"
  fi
done

# Copy all files from lib to lib_debug
for item in "${bundles[@]}"
do
  if [ ! -f "$lib_debug_dir/$item" ]; then
    cp -f "$lib_dir/$item" "$lib_debug_dir/$item"
  fi
done