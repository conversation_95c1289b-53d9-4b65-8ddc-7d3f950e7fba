#!/bin/bash

set -euo pipefail

display_usage() {
    cat <<EOF
OVERVIEW:
    Builds a crate with the configuration required to run on IgnitionX.

USAGE:
    build <name-of-crate> [options]

    Example: 
     - build app-start
     - build media-background --example media_background

OPTIONS:
    --help                  Display this message
    --example               Name of the example target to run
    --wasm                  Use the 'wasm32-wasip1' build target
    --release               Build the target in release mode
    --additional-features   Appends additional features to the essential ones
EOF
}

script_dir=$(dirname $0)
original_args="$@"

# Check the required arguments were provided
if [[ $# -lt 1 ]]; then
    display_usage
    exit 0
fi

crate_name=$1
shift;

# Declare variables $arg1, $arg2, etc for each positional argument --arg1, --arg2, etc provided to $0
while [[ $# -gt 0 ]]; do
  case $1 in
    --example)
        example_name="$2"
        shift # past argument
        shift # past value
        ;;
    --wasm)
        wasm="wasm"
        shift # past argument
        ;;
    --release)
        release="release"
        shift # past argument
        ;;
    --lldb)
        # Just ignore
        shift # past argument
        ;;
    --help)
        help="help"
        shift # past argument
        ;;
    --additional-features)
        shift 2 # past argument + value
        ;;
    *)
        echo "Error: option '$1' is invalid."
        exit 1
        ;;
  esac
done


if [[ ${help+x} ]]; then
    display_usage
    exit 0
fi

# Relevant paths
cargo_build_dir="$(cargo brazil print target-dir)"
ignition_x_bin=$IGNITION_X_PATH/bin/ignition

# Build
source "${script_dir}/shared"
build_command_args=$(build_args ${original_args})
cargo ${build_command_args}

