#!/bin/bash
#
# native-rebuilder
#

set -euo pipefail

display_usage() {
    cat <<EOF
OVERVIEW:
    Builds and runs using IgnitionX emulator an example

USAGE:
    ./native-rebuilder.sh --crate details --example details_page_stubbed

OPTIONS:
    --help           Display this message
    --crate          The crate that the example is contained in
    --did               Set the device id to launch the emulator with
    --dtid              Set the device type id to launch the emulator with
    --example        The example to build and run
    --features 	     The features to enable for that example
    --kill_existing  If the script should kill previous instances created from running this script
    --proxyman       Runs the emulator with proxyman compatible settings
EOF
}

PID_FILE_PATH=~/.rust_native_rebuilder.pid
kill_existing="0"
proxyman="0"
device_id="uuid0d02b8fb0e724c609d75b9ee0cbd29e4"
device_type_id="AFOQV1TK6EU6O"
features=""
client_path=$(dirname $(dirname "$0"))
while [[ $# -gt 0 ]]; do
  case $1 in
    --example)
        example_name="$2"
        shift # past argument
        shift # past value
        ;;
    --rust_client_path)
        client_path="$2"
        shift
        shift
        ;;
    --kill_existing)
 	      kill_existing="1"
	      shift
	      ;;
	  --did)
    	  device_id="$2"
    	  shift
    	  shift
        ;;
    --dtid)
        device_type_id="$2"
        shift
        shift
        ;;
    --proxyman)
	      proxyman="1"
	      shift
	      ;;
    --crate)
	      crate="$2"
	      shift
	      shift
	      ;;
    --features)
	      features="--features $2"
	      shift
	      shift
	      ;;
    --help)
        help="help"
        shift # past argument
        ;;
    *)
        echo "Error: option '$1' is invalid."
        exit 1
        ;;
  esac
done

echo "Running native rebuilder with crate: $crate, example: $example_name"

# Navigate to crate folder
cd $client_path/crates/$crate

# Build native code
cmd="cargo build $features --example $example_name --lib --features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local"
echo -e "Executing build: $cmd"
eval $cmd

# Start IgnitionX Emulator

# Check if the PID file exists
if [ -f "$PID_FILE_PATH" ]; then
    # Read the content of the PID file into a variable
    RUST_CLIENT_NATIVE_REBUILDER_PID=$(<"$PID_FILE_PATH")
    echo "PID file exists. Process ID: $RUST_CLIENT_NATIVE_REBUILDER_PID"
else
    RUST_CLIENT_NATIVE_REBUILDER_PID=""
fi

EXAMPLE_LIB_PATH=$client_path/build/private/cargo-target/debug/examples/lib$example_name.dylib
emulator_cmd="$IGNITION_X_EMULATOR_PATH/bin/ignition --local-native-module-path=$EXAMPLE_LIB_PATH --width=1920 --height=1080 --device-id=$device_id --device-type-id=$device_type_id"
if [ "$proxyman" = "1" ]; then
	set -a && source "/Users/<USER>/.proxyman/proxyman_env_automatic_setup.sh" && set +a
	emulator_cmd="$emulator_cmd --disable-ssl-cert"
fi

if [ "$kill_existing" = "1" ]; then
	if [ -n "$RUST_CLIENT_NATIVE_REBUILDER_PID" ] && ps -p "$RUST_CLIENT_NATIVE_REBUILDER_PID" > /dev/null; then
	    echo "Killing existing IgnitionX emulator instance with PID: $RUST_CLIENT_NATIVE_REBUILDER_PID"
	    kill -9 $RUST_CLIENT_NATIVE_REBUILDER_PID
	fi
fi

eval "$emulator_cmd &"
#Write pid to a file
RUST_CLIENT_NATIVE_REBUILDER_PID=$!

echo "$RUST_CLIENT_NATIVE_REBUILDER_PID" > "$PID_FILE_PATH"

# Optionally, you can also check if the PID file was successfully created
if [ -f "$PID_FILE_PATH" ]; then
    echo "PID file created successfully at $PID_FILE_PATH"
else
