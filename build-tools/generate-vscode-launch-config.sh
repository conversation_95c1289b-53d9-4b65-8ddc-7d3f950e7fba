#!/bin/bash

# ---------------------------------------------------------
# This script will auto-generate the necessary tasks and
# launch configurations for debugging examples in VS Code.
# It searches all crates for files within the `examples` 
# folder (ie. `crates/fableous/examples`)
# ---------------------------------------------------------

# declare variables $arg1, $arg2, etc for each positional argument --arg1, --arg2, etc provided to $0
while getopts :-: _; do declare "$OPTARG"=1; done

if [[ ${help+x} ]]; then
    cat <<EOF
This script will auto-generate the necessary tasks and
launch configurations for debugging examples in VS Code.
It searches all crates for files within the 'examples' 
folder (ie. 'crates/fableous/examples')

    --help           Display this message
    --linux          Generate files compatible with Linux, OSX is default
EOF
    exit 0
fi

lib_extension="dylib"
debugger="lldb"
if [[ ${linux+x} ]]; then
  lib_extension="so"
  debugger="cppdbg"
fi

# Output files
vscode_tasks=".vscode/tasks.json"
vscode_launch=".vscode/launch.json"

build_types=("native-release" "native-debug" "wasm-release" "wasm-debug")

setup_tasks_json() {
  # Setup tasks.json
  echo "{" > "$vscode_tasks"
  echo "    \"version\": \"2.0.0\"," >> "$vscode_tasks"
  echo "    \"tasks\": [" >> "$vscode_tasks"

  # Add client build task
  echo "        {" >> "$vscode_tasks"
  echo "            \"label\": \"build-client-native-debug\"," >> "$vscode_tasks"
  echo "            \"command\": \"cargo\"," >> "$vscode_tasks"
  echo "            \"args\": [\"build\", \"-p\", \"amzn-av-living-room-rust-client\", \"--lib\", \"--features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local\"]," >> "$vscode_tasks"
  echo "            \"type\": \"shell\"," >> "$vscode_tasks"
  echo "            \"options\": { \"cwd\": \"\${workspaceFolder}/crates/app-start\" }" >> "$vscode_tasks"
  echo "        }," >> "$vscode_tasks"
  echo "        {" >> "$vscode_tasks"
  echo "            \"label\": \"build-client-wasm\"," >> "$vscode_tasks"
  echo "            \"command\": \"cargo\"," >> "$vscode_tasks"
  echo "            \"args\": [\"build\", \"--target\", \"wasm32-wasip1\", \"--release\", \"--features=amzn-ignx-compositron/use_ignx_local\"]," >> "$vscode_tasks"
  echo "            \"type\": \"shell\"," >> "$vscode_tasks"
  echo "            \"options\": { \"cwd\": \"\${workspaceFolder}/crates/app-start\" }" >> "$vscode_tasks"
  echo "        }," >> "$vscode_tasks"
}

setup_launch_json() {
  # Setup launch.json
  echo "{" > "$vscode_launch"
  echo "    \"configurations\": [" >> "$vscode_launch"

  # Add client build config
  echo "        {" >> "$vscode_launch"
  echo "            \"type\": \"$debugger\"," >> "$vscode_launch"
  echo "            \"request\": \"launch\"," >> "$vscode_launch"
  echo "            \"name\": \"Client - Native (Debug)\"," >> "$vscode_launch"
  echo "            \"program\": \"\${env:IGNITION_X_PATH}/bin/ignition\"," >> "$vscode_launch"
  echo "            \"args\": [\"--log-level=ALL:INFO\", \"--local-native-module-path=\${workspaceFolder}/build/private/cargo-target/debug/libamzn_av_living_room_rust_client.$lib_extension\"]," >> "$vscode_launch"
  echo "            \"cwd\": \"\${env:IGNITION_X_PATH}\"," >> "$vscode_launch"
  echo "            \"preLaunchTask\": \"build-client-native-debug\"," >> "$vscode_launch"
  echo "            \"presentation\": { \"group\": \"client\" }" >> "$vscode_launch"

  echo "        }," >> "$vscode_launch"
  echo "        // You need to run AVLivingRoomClientBundle for this" >> "$vscode_launch"
  echo "        {" >> "$vscode_launch"
  echo "            \"type\": \"$debugger\"," >> "$vscode_launch"
  echo "            \"request\": \"launch\"," >> "$vscode_launch"
  echo "            \"name\": \"Client - with JS - Native (Debug)\"," >> "$vscode_launch"
  echo "            \"program\": \"\${env:IGNITION_X_PATH}/bin/ignition\"," >> "$vscode_launch"
  echo "            \"args\": [\"--device-proxy-url=http://127.0.0.1:3999\", \"--react-uri-prefix=/index.js\", \"--log-level=ALL:INFO\", \"--local-native-module-path=\${workspaceFolder}/build/private/cargo-target/debug/libamzn_av_living_room_rust_client.$lib_extension\"]," >> "$vscode_launch"
  echo "            \"cwd\": \"\${env:IGNITION_X_PATH}\"," >> "$vscode_launch"
  echo "            \"preLaunchTask\": \"build-client-native-debug\"," >> "$vscode_launch"
  echo "            \"presentation\": { \"group\": \"client\" }" >> "$vscode_launch"
  echo "        }," >> "$vscode_launch"
  echo "        // You need to be in the right TOS/pilot ACM override to use the Rust pages" >> "$vscode_launch"
  echo "        // Optionally you can use alpha-mooshine JS app via ACM override" >> "$vscode_launch"
  echo "        {" >> "$vscode_launch"
  echo "            \"type\": \"$debugger\"," >> "$vscode_launch"
  echo "            \"request\": \"launch\"," >> "$vscode_launch"
  echo "            \"name\": \"Client - with JS prod - Wasm\"," >> "$vscode_launch"
  echo "            \"program\": \"\${env:IGNITION_X_PATH}/bin/ignition\"," >> "$vscode_launch"
  echo "            \"args\": [\"--log-level=ALL:INFO\", \"--local-wasm-module-path=\${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/amzn-av-living-room-rust-client.wasm\"]," >> "$vscode_launch"
  echo "            \"cwd\": \"\${env:IGNITION_X_PATH}\"," >> "$vscode_launch"
  echo "            \"preLaunchTask\": \"build-client-wasm\"," >> "$vscode_launch"
  echo "            \"presentation\": { \"group\": \"client\" }" >> "$vscode_launch"
  echo "        }," >> "$vscode_launch"
}

finalize_tasks_json() {
  # Strip last line and close out tasks.json
  sed -i '' '$ d' "$vscode_tasks"
  echo "        }" >> "$vscode_tasks"
  echo "    ]" >> "$vscode_tasks"
  echo "}" >> "$vscode_tasks"
}

finalize_launch_json() {
  # Strip last line and close out launch.json
  sed -i '' '$ d' "$vscode_launch"
  echo "        }" >> "$vscode_launch"
  echo "    ]" >> "$vscode_launch"
  echo "}" >> "$vscode_launch"
}

get_example_folder() {
  local file=$1
  dir=$(dirname "$1")
  folder=$(echo "$dir" | awk -F'/' '{print $2}')
  echo "$folder"
}

is_tracked_by_git() {
  local file="$1"
  git ls-files --error-unmatch "$file" >/dev/null 2>&1
}

# Given a string, replace hyphens and underscores
# with spaces and convert to Title Case
make_pretty() {
  local input="$1"
  local modified=$(echo "$input" | sed 's/[_-]/ /g')
  local modified=$(echo "$modified" | awk '{for(i=1;i<=NF;i++)sub(/./,toupper(substr($i,1,1)),$i)}1')
  echo "$modified"
}

generate_task_config_for_example() {
  local file=$1

  filename=$(basename "$1" .rs)
  folder=$(get_example_folder "$1")
  features="--features=amzn-ignx-compositron/allow_no_js_vm"
  native_features="amzn-ignx-compositron/use_ignx_local"
  stubbed_example_features="example_data"
  bin_crate="--crate-type=bin"
  lib_crate="--crate-type=cdylib"
  
  for type in "${build_types[@]}"; do
    label="build-example-$folder-$filename-$type"
    all_features="$features"
    if [ "$type" == "wasm-debug" ]; then
      if [[ "$filename" == *"stubbed" ]]; then
        all_features="$features,$stubbed_example_features"
      fi
      args="\"rustc\", \"--target\", \"wasm32-wasip1\", \"--example\", \"$filename\", \"$all_features\", \"$bin_crate\""
    elif [ "$type" == "wasm-release" ]; then
      if [[ "$filename" == *"stubbed" ]]; then
        all_features="$features,$stubbed_example_features"
      fi
      args="\"rustc\", \"--target\", \"wasm32-wasip1\", \"--release\", \"--example\", \"$filename\", \"$all_features\", \"$bin_crate\""
    elif [ "$type" == "native-debug" ]; then
      if [[ "$filename" == *"stubbed" ]]; then
        all_features="$features,$native_features,$stubbed_example_features"
      else
        all_features="$features,$native_features"
      fi
      args="\"rustc\", \"--example\", \"$filename\", \"$all_features\", \"$lib_crate\""
    elif [ "$type" == "native-release" ]; then
      if [[ "$filename" == *"stubbed" ]]; then
        all_features="$features,$native_features,$stubbed_example_features"
      else
        all_features="$features,$native_features"
      fi
      args="\"rustc\", \"--release\", \"--example\", \"$filename\", \"$all_features\", \"$lib_crate\""
    fi

    # Generate JSON block
    echo "        {" >> "$vscode_tasks"
    echo "            \"label\": \"$label\"," >> "$vscode_tasks"
    echo "            \"command\": \"cargo\"," >> "$vscode_tasks"
    echo "            \"args\": [$args]," >> "$vscode_tasks"
    echo "            \"type\": \"shell\"," >> "$vscode_tasks"
    echo "            \"options\": { \"cwd\": \"\${workspaceFolder}/crates/$folder\" }" >> "$vscode_tasks"
    echo "        }," >> "$vscode_tasks"
  done
}

# Creates a dummy config to use as a group header
generate_launch_config_for_group() {
  local dir=$1

  group=$(echo "$1" | cut -d'/' -f2)
  group_display_name=$(make_pretty "$group")

  # Generate JSON block
  echo "        {" >> "$vscode_launch"
  echo "            \"type\": \"$debugger\"," >> "$vscode_launch"
  echo "            \"request\": \"launch\"," >> "$vscode_launch"
  echo "            \"name\": \"$group_display_name Examples\"," >> "$vscode_launch"
  echo "            \"program\": \"\"," >> "$vscode_launch"
  echo "            \"presentation\": { \"group\": \"$group\" }" >> "$vscode_launch"
  echo "        }," >> "$vscode_launch"
}

generate_launch_config_for_example() {
  local file=$1

  filename=$(basename "$1" .rs)
  folder=$(get_example_folder "$1")
  display_filename=$(make_pretty "$filename")
  nbsp="\u00A0"

  for type in "${build_types[@]}"; do
    task="build-example-$folder-$filename-$type"
    if [ "$type" == "wasm-debug" ]; then
      typelabel="- Wasm (Debug)"
      args="\"--local-wasm-path=\${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/debug/examples/$filename.wasm\""
    elif [ "$type" == "wasm-release" ]; then
      typelabel="- Wasm"
      args="\"--local-wasm-path=\${workspaceFolder}/build/private/cargo-target/wasm32-wasip1/release/examples/$filename.wasm\""
    elif [ "$type" == "native-debug" ]; then
      typelabel="(Debug)"
      args="\"--local-native-path=\${workspaceFolder}/build/private/cargo-target/debug/examples/lib$filename.$lib_extension\""
    elif [ "$type" == "native-release" ]; then
      typelabel=""
      args="\"--local-native-path=\${workspaceFolder}/build/private/cargo-target/release/examples/lib$filename.$lib_extension\""
    fi

    # Generate JSON block
    echo "        {" >> "$vscode_launch"
    echo "            \"type\": \"$debugger\"," >> "$vscode_launch"
    echo "            \"request\": \"launch\"," >> "$vscode_launch"
    echo "            \"name\": \"$nbsp$nbsp$display_filename $typelabel\"," >> "$vscode_launch"
    echo "            \"program\": \"\${env:IGNITION_X_PATH}/bin/ignition\"," >> "$vscode_launch"
    echo "            \"args\": [$args]," >> "$vscode_launch"
    echo "            \"cwd\": \"\${env:IGNITION_X_PATH}\"," >> "$vscode_launch"
    echo "            \"preLaunchTask\": \"$task\"," >> "$vscode_launch"
    echo "            \"presentation\": { \"group\": \"$folder\" }" >> "$vscode_launch"
    echo "        }," >> "$vscode_launch"
  done
}

# Initialize JSON files
setup_tasks_json
setup_launch_json

# Find all example directories and sort them
examples_dirs=$(find "crates" type -d -name "examples" 2>/dev/null | sort)

if [ -z "$examples_dirs" ]; then
  echo "No 'examples' directories found."
  exit 1
fi

total_files=0

# Loop through each `examples` directory
for dir in $examples_dirs; do
  generate_launch_config_for_group "$dir"

  # Loop over each file found in current directory
  while IFS= read -r file; do

    # Ignore files that start with "mock"
    if [[ "$(basename "$file")" == mock* ]]; then
      continue
    fi

    #Ignore files not tracked by Git
    if is_tracked_by_git "$file"; then
      ((total_files++))
      generate_task_config_for_example "$file"
      generate_launch_config_for_example "$file"
    else
      echo "Skipping untracked file $file"
    fi
  done < <(find "$dir" -type f -name "*.rs")

done

# Add closing JSON syntax
finalize_tasks_json
finalize_launch_json

echo "Successfully generated VS Code tasks and launch configurations for $total_files examples."
