#!/usr/bin/env bash

set -euo pipefail

current_script_dir=$(dirname "$0")
test_phase_command="\"$current_script_dir/../test-helper.sh\" --reporters \"cobertura,lcov\" --workspace --client_path $(pwd)"

if [[ "$OSTYPE" == "darwin"* ]]; then
    if ! nextest_output=$(cargo nextest --version 2>&1); then
        echo "nextest check failed: $nextest_output"
        echo "Running test-phase with cargo test"
    else 
        echo "nextest is installed on MacOS"
        echo "Running test-phase with nextest"
        test_phase_command="$test_phase_command --nextest"
    fi
else
    # Defaulting to use nextest in package builder
    echo "Running test-phase with nextest"
    test_phase_command="$test_phase_command --nextest"
fi

echo -e "Running test phase command: $test_phase_command"
eval "$test_phase_command"
