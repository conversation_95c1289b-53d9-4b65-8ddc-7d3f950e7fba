#!/bin/bash

set -euo pipefail

if [[ $# -eq 2 ]]; then
    build_start_time=$1
    build_end_time=$2
else 
    echo "Error: Both build_start_time and build_end_time must be provided"
    echo "Usage: $0 <build_start_time> <build_end_time>"
    exit 1
fi

total_build_time=$(($build_end_time - $build_start_time))
versionset=$(grep -o -E "\"?versionSet\"?\s*=\s*\"?[A-Za-z0-9_/.-]+\"?;" ../../packageInfo | tr -d '"; ')
VERSIONSET=${versionset#*=}
package_name=$(basename $(pwd))

echo "Version Set: $VERSIONSET"
echo "Package Name: $package_name"
echo "Total Build Time: $total_build_time"

response=$(curl -X POST \
    https://prod.build-monitoring.pv.amazon.dev/rust-build \
    -H "Content-Type: application/json" \
    -d "{\"value\": $total_build_time, \"versionset\": \"$VERSIONSET\", \"packageName\": \"$package_name\", \"mode\": \"baseline\"}" \
    -w "\nStatus Code: %{http_code}\n")

echo "Reporting build time metrics response: $response"
