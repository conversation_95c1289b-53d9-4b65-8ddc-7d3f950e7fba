#!/usr/bin/env bash

set -euo pipefail

# This should be updated after more shards packages are created: 
# https://code.amazon.com/packages/AVLivingRoomRustClient-Build-Shard-20/trees/mainline
MAX_SHARDS_SCALED=20
TOTAL_BUILD_SHARD_COUNT="$(generate-bundles shard-count)"

if [[ $TOTAL_BUILD_SHARD_COUNT -gt $MAX_SHARDS_SCALED ]]; then
    echo "The number of available build shards has been exceeded. Please contact pv-lrc-idea to scale up the build shards." 
    exit 1
fi

if [[ $# -gt 0 ]] && [[ "$1" = write-metadata ]]; then
    # Initialising the build_start_time
    # This will be consumed in AVLivingRoomRustClient-Artifacts-Bundle to calculate
    # the total Rust build time and to emit this as a metric

    build_metadata_dir="$(brazil-path package-build-root)/build_metadata"
    mkdir -p "$build_metadata_dir"
    timestamp=$(date +%s)
    build_start_time_file="$build_metadata_dir/build_start_time.txt"
    echo $timestamp > $build_start_time_file

    # Writing out a flag to signal that this build was distributed
    touch "$build_metadata_dir/distributed_build_flag"

    # Storing the total shard count
    echo $TOTAL_BUILD_SHARD_COUNT > "$build_metadata_dir/total_shard_count"

    # Storing the full brazil version and change id
    echo $BRAZIL_PACKAGE_VERSION > "$build_metadata_dir/full_version"
    echo $BRAZIL_PACKAGE_CHANGE_ID > "$build_metadata_dir/change_id"
    exit 0;
fi

echo "Selecting build script for package $BRAZIL_PACKAGE_NAME"

cargo-brazil-build sync

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
mv $SCRIPT_DIR/cargo-brazil-build-shim $SCRIPT_DIR/cargo-brazil-build


case $BRAZIL_PACKAGE_NAME in
  AVLivingRoomRustClient-Build-Shard-*)
    shard_no=${BRAZIL_PACKAGE_NAME##*[!0-9]}
    if [[ $shard_no -le $TOTAL_BUILD_SHARD_COUNT ]]; then
      generate-bundles shard "$shard_no/$TOTAL_BUILD_SHARD_COUNT"
    else
      echo "This shard is unused, and will be used when a new bundle command is added to ./generate-bundles"
    fi
    ;;

  AVLivingRoomRustClient-Unit-Tests-*)
    cargo-brazil-on-distributed-test
    ;;

  AVLivingRoomRustClient-linting)
    cargo-brazil-on-lint
    ;;


  *)
    echo -n "Unknown package"
    exit 1
    ;;
esac