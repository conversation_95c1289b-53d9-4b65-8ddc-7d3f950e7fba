#!/usr/bin/env bash

set -euo pipefail

# Invoke default fetch phase
cargo-brazil-build run-default-phase fetch

# Set up cross-compilation for wasm32-wasip1 and wasm32-wasip1-threads
echo 'Set up wasm32-wasip1 target' >&2
rustup +stable target add wasm32-wasip1 wasm32-wasip1-threads
cargo brazil fetch --target wasm32-wasip1
cargo brazil fetch --target wasm32-wasip1-threads

echo 'Set up arm-unknown-linux-gnueabi target' >&2
rustup +stable target add arm-unknown-linux-gnueabi
cargo brazil fetch --target arm-unknown-linux-gnueabi

echo 'Set up armv7-linux-androideabi target' >&2
rustup +stable target add armv7-linux-androideabi
cargo brazil fetch --target armv7-linux-androideabi

# make sure no one has put test_utils feature flag in dependencies
if cargo tree -e features -e no-dev -q | grep -q "test_utils"; then
  echo -e "\033[0;31mError: test_utils feature flag was included in dependencies, it should only be in dev-dependencies\033[0m"
  exit 1
fi

# this must not be run in CI since this is not a Git repo on our build hosts
if [ -d "$(brazil-path package-src-root)/.git" ]; then
   echo "setting up Git repo to use included hooks..."
   git config core.hooksPath "$(brazil-path package-src-root)/git_hooks"
fi