#!/bin/bash

set -euo pipefail

build_start_time=""

handle_baseline_build() {
    build_start_time=$(date +%s)
    echo "Building AVLivingRoomRustClient in baseline mode (without faster builds enabled)"
}

handle_package_builder() {
    distributed-build write-metadata

    # Leave all processing to dependency packages
    exit 0
}

handle_setup() {
    cargo-brazil-build sync
    rustup +stable target add wasm32-wasip1
    cargo brazil fetch --target wasm32-wasip1
    setup_pre_cr
    exit 0
}

setup_pre_cr() {
    ./build-tools/setup-pre-cr.sh
}

main() {
    PATH="$(path-allowing-local-dev)"
    export PATH

    if [[ $# -gt 0 ]] && [[ "$1" = "baseline-build" ]]; then
        ENABLE_BASELINE_BUILD="true"
        handle_baseline_build
        shift # This is necessary if not brazil will complain baseline-build is not a good build-target
    fi

    # Handle package builder scenario
    if [[ -n "${BRAZIL_PACKAGE_VERSION:-}" ]] && [[ "${ENABLE_BASELINE_BUILD:-false}" != "true" ]]; then
        handle_package_builder
    fi

    if [[ $# -gt 0 ]] && [[ "$1" = "setup" ]]; then
        handle_setup
    fi

    if [[ $# -gt 0 ]] && [[ "$1" = "baseline-build" ]]; then
        (cargo-brazil-build "$@" || true)
    else 
        cargo-brazil-build "$@"
    fi

    build_end_time=$(date +%s)

    if [[ -n "${BRAZIL_PACKAGE_VERSION:-}" ]] && [[ "${ENABLE_BASELINE_BUILD:-false}" = "true" ]]; then
        report-build-time $build_start_time $build_end_time
        upload-build-artifacts
    fi
}

main "$@"
