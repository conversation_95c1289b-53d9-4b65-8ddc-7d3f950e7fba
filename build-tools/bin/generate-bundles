#!/bin/bash

set -euo pipefail

### General Setup

RED='\033[0;31m'
NC='\033[0m' # No Color

DEFAULT_MAX_JOBS=8

# Setup bundle defaults
BUNDLE_LIMIT_BYTES=14500000 # 14.5 MB
BUNDLE_LIMIT_BYTES_TEST_UTILS=$((BUNDLE_LIMIT_BYTES + 300000))

declare -a all_jobs=(
    # shellcheck disable=SC2016
    'bundle_prod "rust client" "cargo build --release --target wasm32-wasip1" "wasm32-wasip1/release" "amzn_av_living_room_rust_client.wasm"' 
    # shellcheck disable=SC2016
    'bundle_test_utils "rust client with test_utils" "cargo build --release --target wasm32-wasip1 --features=amzn-ignx-compositron/test_utils --target-dir $TEST_RELEASE_DIR" "wasm32-wasip1/release" "amzn_av_living_room_rust_client_test_utils.wasm"'
    # shellcheck disable=SC2016
    'bundle_test_utils "rust client with test_utils and debug renderer" "cargo build --release --target wasm32-wasip1 --features=amzn-ignx-compositron/test_utils,amzn-ignx-compositron/test_utils_debug_renderer,contextual-menu/test_utils_debug_renderer,hero/test_utils_debug_renderer --target-dir $TEST_RELEASE_DIR" "wasm32-wasip1/release" "amzn_av_living_room_rust_client_test_utils_debug_renderer.wasm"'
    # shellcheck disable=SC2016
    'bundle_prod "AOT compilation" "cargo build --profile release-aot --target wasm32-wasip1" "wasm32-wasip1/release-aot" "amzn_av_living_room_rust_client_aot.wasm"'
    # shellcheck disable=SC2016
    'bundle_prod "WASI threads" "cargo build --release --target wasm32-wasip1-threads --features=amzn-ignx-compositron/threads" "wasm32-wasip1-threads/release" "amzn_av_living_room_rust_client_threads.wasm"'
    # shellcheck disable=SC2016
    'bundle_prod "WASI threads for AOT compilation" "cargo build --profile release-aot --target wasm32-wasip1-threads --features=amzn-ignx-compositron/threads" "wasm32-wasip1-threads/release-aot" "amzn_av_living_room_rust_client_threads_aot.wasm"'
    # shellcheck disable=SC2016
    'bundle_test_utils "AOT compilation with test_utils" "cargo build --profile release-aot --target wasm32-wasip1 --features=amzn-ignx-compositron/test_utils --target-dir $TEST_RELEASE_DIR" "wasm32-wasip1/release-aot" "amzn_av_living_room_rust_client_aot_test_utils.wasm"'
    # shellcheck disable=SC2016
    'bundle_test_utils "WASI threads with test_utils" "cargo build --release --target wasm32-wasip1-threads --features=amzn-ignx-compositron/threads,amzn-ignx-compositron/test_utils --target-dir $TEST_RELEASE_DIR" "wasm32-wasip1-threads/release" "amzn_av_living_room_rust_client_threads_test_utils.wasm"'
    # shellcheck disable=SC2016
    'bundle_test_utils "WASI threads for AOT compilation with test_utils" "cargo build --profile release-aot --target wasm32-wasip1-threads --features=amzn-ignx-compositron/threads,amzn-ignx-compositron/test_utils --target-dir $TEST_RELEASE_DIR" "wasm32-wasip1-threads/release-aot" "amzn_av_living_room_rust_client_threads_aot_test_utils.wasm"'
    # shellcheck disable=SC2016
    'build_roku_native "$RELEASE_DIR"'
    # shellcheck disable=SC2016
    'build_roku_native "$RELEASE_DIR/test-utils" "amzn-ignx-compositron/test_utils" "test_utils"'
    # shellcheck disable=SC2016
    'build_roku_native "$RELEASE_DIR/test-utils-debug-renderer" "amzn-ignx-compositron/test_utils,amzn-ignx-compositron/test_utils_debug_renderer,contextual-menu/test_utils_debug_renderer" "test_utils_debug_renderer"'
    # shellcheck disable=SC2016
    'build_android_native "$RELEASE_DIR"'
    # shellcheck disable=SC2016
    'build_android_native "$RELEASE_DIR/test-utils" "amzn-ignx-compositron/test_utils" "test_utils"'
    # shellcheck disable=SC2016
    'build_android_native "$RELEASE_DIR/test-utils-debug-renderer" "amzn-ignx-compositron/test_utils,amzn-ignx-compositron/test_utils_debug_renderer,contextual-menu/test_utils_debug_renderer" "test_utils_debug_renderer"'
)

# As MacOS defaults to Bash 3, cannot use associative arrays -- adding human-friendly names
job_build_prod=${all_jobs[0]}
job_build_test_utils=${all_jobs[1]}
job_build_test_utils_debug=${all_jobs[2]}
job_build_aot=${all_jobs[3]}
job_build_threads=${all_jobs[4]}
job_build_aot_threads=${all_jobs[5]}
job_build_aot_test_utils=${all_jobs[6]}
job_build_threads_test_utils=${all_jobs[7]}
job_build_aot_threads_test_utils=${all_jobs[8]}
job_build_roku_native=${all_jobs[9]}
job_build_roku_native_test_utils=${all_jobs[10]}
job_build_roku_native_test_utils_debug=${all_jobs[11]}
job_build_android_native=${all_jobs[12]}
job_build_android_native_test_utils=${all_jobs[13]}
job_build_android_native_test_utils_debug=${all_jobs[14]}

run_flag=$1

if [[ "$run_flag" == "shard-count" ]]; then
    echo ${#all_jobs[@]}
    exit 0
fi

# HACK: Making sure this correctly adapts when we run this copied in a different package
if [ -n "${BRAZIL_BUILD_DIR_OVERRIDE-}" ]; then
    echo "Using BRAZIL_BUILD_DIR_OVERRIDE: $BRAZIL_BUILD_DIR_OVERRIDE"
    brazil_build_dir="$BRAZIL_BUILD_DIR_OVERRIDE"
else
    brazil_build_dir="$(brazil-path package-build-root)"
fi

LIB_DIR=$brazil_build_dir/lib
BUNDLE_DIR=$brazil_build_dir/lib/bundles
DEBUG_DIR=$brazil_build_dir/lib_debug
BIN_DIR=$brazil_build_dir/bin
RELEASE_DIR=$(cargo brazil print target-dir)
TEST_RELEASE_DIR="$RELEASE_DIR/test-utils"
BUNDLE_SIZE_DIR=$brazil_build_dir/bundle_size

# Make output sub-directories under brazil "build" directory
mkdir -p "$LIB_DIR" "$BUNDLE_DIR" "$DEBUG_DIR" "$BIN_DIR" "$BUNDLE_SIZE_DIR"

trap clean_up HUP INT TERM

log() {
    echo -e "${NC}$1"
}

log_error() {
    local status=$?
    echo -e "${RED}$1"
    return $status
}

clean_up() {
    job_pids=$(jobs -p)

    # deregister trap to avoid loops
    trap - HUP INT TERM
    log "Cleaning up background jobs: $job_pids"
    # `-$$` means kill the whole process group, not just the individual process
    # aka, kill this process and all of its descendants
    # async subshells (the ones spawned using `(...) &`) are created within the 
    # same process group of the parent by default, unless `set -m` is used.
    kill -- -$$ 
    wait
    log "Cleaned up background jobs: $job_pids"
    exit 1
}

### Helper functions

function spawn_clean_up() {
    log_error "Error in background job, sending kill to parent ($$)"
    # We could just as easily pass `-- -$$` here and get rid of the trap in the top-level
    # script, but this gives the top-level script a chance to log/do other cleanup before
    # killing the other child processes
    kill -TERM $$
    exit 1
}

function spawn() {
    if [[ $(jobs -p | wc -l) -ge $max_jobs ]]; then
        # $MAX_JOBS reached - wait for the oldest background job to complete.
        # note: we'd like to wait for _any_ to complete, not necessarily the
        # last one. Unfortunately `wait -n` is no supported in all shells.
        oldest_pid=$(jobs -p | awk 'NR==1{print $1}')
        log "Reached $max_jobs background jobs, waiting for $oldest_pid to finish..."
        wait "$oldest_pid"
    fi

    # if background task fails, send SIGTERM to the parent to exit immediately
    (
        trap 'code=$?; if [ $code -ne 0 ]; then echo "Exited with error code $code"; spawn_clean_up; fi' EXIT
        trap 'echo "Received signal, exiting"; spawn_clean_up' INT TERM HUP
        echo running "$@";
        # if the command passed into spawn returns non-zero exit code, then tell parent process
        # so that it can kill the other child processes
        if ! eval $@; then
            echo "Command $@ failed, exiting";
            spawn_clean_up
        fi
    ) &
    bg_pid=$!
    if [[ -z "$bg_pid" ]]; then
        log_error "Failed to start background job: $@"
        kill -TERM $$ # send sigterm instead of exit so we cleanup
    else
        log "Started background job with PID: $bg_pid (job count: $(jobs -p | wc -l) / $max_jobs)"
    fi
}

# Remove debug info from the .wasm file passed as $1 in-place using `wasm-opt`
function strip_bundle() {
    wasm-opt "$1" --strip-debug --strip-dwarf --strip-producers --strip-target-features --output "$1"
}

function write_bundle_size_metadata() {
    filename=$1
    accepted_size=$2
    actual_size=$3
    bundle_size_headroom=$(($accepted_size-$actual_size))

    bundle_size_file_name=$(basename "$filename")
    bundle_size_file_path="${BUNDLE_SIZE_DIR}/${bundle_size_file_name%.wasm}.json"

    echo "{\"filename\": \"$bundle_size_file_name\", \"accepted_size\": $accepted_size, \"actual_size\": $actual_size, \"bundle_size_headroom\": $bundle_size_headroom}" > $bundle_size_file_path
}

function validate_binary_size() {
    filename=$1
    accepted_size=$2
    actual_size=$(wc -c <"$filename")

    write_bundle_size_metadata "$filename" "$accepted_size" "$actual_size"

    if [ "$actual_size" -ge "$accepted_size" ]; then
        log_error "Size of file ($filename) is over $accepted_size bytes ($actual_size bytes)"
        exit 1
    else
        log "Size of file ($filename) is under $accepted_size bytes ($actual_size bytes)"
    fi
}

function bundle_finalize() {
    local cargo_release_dir=$1
    local out_wasm_file=$2
    local bundle_size=$3

    log "Running -Oz optimization"
    wasm-opt -Oz "$cargo_release_dir/amzn-av-living-room-rust-client.wasm" --debuginfo --output "$out_wasm_file"
    log "Archiving optimized bundle with debug info in $DEBUG_DIR"
    cp "$out_wasm_file" "$DEBUG_DIR" # required to symbolicate stacktraces
    log "Stripping $out_wasm_file"
    strip_bundle "$out_wasm_file"
    log "Validating size of stripped $out_wasm_file"
    validate_binary_size "$out_wasm_file" "$bundle_size"
}

function bundle_finalize_opt() {
    local cargo_release_dir=$1
    local out_wasm_file=$2
    local bundle_size=$3

    symbolmap_file=$(basename ${out_wasm_file%.wasm}.symbolmap)

    log "Running -Oz optimization and removing debug symbols"
    wasm-opt -Oz "$cargo_release_dir/amzn-av-living-room-rust-client.wasm" --output "$out_wasm_file" --symbolmap="$symbolmap_file"
    log "Copying symbol map in $DEBUG_DIR"
    cp "$symbolmap_file" "$DEBUG_DIR" # required to symbolicate stacktraces
    log "Stripping $out_wasm_file"
    strip_bundle "$out_wasm_file"
    log "Validating size of stripped $out_wasm_file"
    validate_binary_size "$out_wasm_file" "$bundle_size"
}

function bundle_prod() {
    local log_details=$1
    local cargo_command=$2
    local cargo_release_dir_suffix=$3
    local out_wasm_file_name=$4

    out_wasm_file=$LIB_DIR/$out_wasm_file_name
    cargo_release_dir=$RELEASE_DIR/$cargo_release_dir_suffix

    log "Building bundle for $log_details: $cargo_command"
    if ! $cargo_command; then
        log_error "Error: Command '$cargo_command' failed."
        exit 1
    fi

    bundle_finalize_opt "$cargo_release_dir" "$out_wasm_file" "$BUNDLE_LIMIT_BYTES"
}

function bundle_test_utils() {
    local log_details=$1
    local cargo_command=$2
    local cargo_release_dir_suffix=$3
    local out_wasm_file_name=$4

    out_wasm_file=$LIB_DIR/$out_wasm_file_name
    cargo_release_dir=$TEST_RELEASE_DIR/$cargo_release_dir_suffix

    log "Building bundle for $log_details: $cargo_command"
    if ! $cargo_command; then
        log_error "Error: Command '$cargo_command' failed."
        exit 1
    fi
    bundle_finalize_opt "$cargo_release_dir" "$out_wasm_file" "$BUNDLE_LIMIT_BYTES_TEST_UTILS"
}

function build_native_base() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        log "Skipping native build on macOS (requires Linux toolchains)"
        return 0
    fi

    local target_dir=${1:?}         # Fail if not given
    local target_triple=${2:?}      # Target triple (e.g., arm-unknown-linux-gnueabi)
    local target_abi_id=${3:?}      # ABI identifier
    local linker=${4:?}             # Path to linker
    local strip_cmd=${5:?}          # Command to strip binary
    local rustflags=${6:-}          # Optional rust flags
    local features=${7:-}           # Optional features
    local output_suffix=${8:-}      # Optional output suffix

    local u_target_triple=${target_triple//-/_} # convert dashes to underscores
    u_target_triple=$(echo "$u_target_triple" | tr '[:lower:]' '[:upper:]')  # convert to uppercase

    local release_binary=$LIB_DIR/libamzn_av_living_room_rust_client${output_suffix:+_$output_suffix}-$target_abi_id.so
    local debug_binary=$DEBUG_DIR/libamzn_av_living_room_rust_client${output_suffix:+_$output_suffix}-$target_abi_id-debug.so
    local release_bundle=$BUNDLE_DIR/libamzn_av_living_room_rust_client${output_suffix:+_$output_suffix}-$target_abi_id.so.br

    eval "export CARGO_TARGET_${u_target_triple}_LINKER='$linker'"
    if [ -n "$rustflags" ]; then
        eval "export CARGO_TARGET_${u_target_triple}_RUSTFLAGS='$rustflags'"
    fi

    cd crates/app-start

    run_or_log() {
        "$@" || {
            last_exit_code=$?
            log_error "build_native_base error: exit code $last_exit_code from '$*'"
            exit $last_exit_code
        }
    }

    run_or_log cargo build \
        -p amzn-av-living-room-rust-client \
        --lib \
        --features="${features:+$features,}"amzn-ignx-compositron/use_ignx_local \
        --target-dir "$target_dir" \
        --target="$target_triple" \
        --release

    run_or_log cp -f \
        "$target_dir/$target_triple/release/libamzn_av_living_room_rust_client.so" \
        "$debug_binary"

    run_or_log cp -f \
        "$target_dir/$target_triple/release/libamzn_av_living_room_rust_client.so" \
        "$release_binary"

    run_or_log $strip_cmd "$release_binary"

    rm -f "$release_bundle"
    run_or_log brotli --output="$release_bundle" "$release_binary"
}

function build_roku_native() {
    local target_dir=${1:?}         # Fail if not given
    local features=${2:-}           # Optional features
    local output_suffix=${3:-}      # Optional output suffix

    local target_triple=arm-unknown-linux-gnueabi
    # Generated for roku with
    # https://bitbucket.amazondcl.com/projects/IGR/repos/ignite/browse/portingplatforms/roku/cmake/generate-abi-metadata.cmake?at=676ba76b8e381e8f3782cfb5ce99ec3a7090fdcd
    local target_abi_id=26c88907beffb4e16f80e4c2c8b523bf83a6078e25762a55bbc59f473182d5d1

    # Roku toolchain setup
    local TOOLCHAIN_DIR=$(brazil-path '[RokuNDK]run.artifact.ndk')/platforms/Roku2/toolchain
    local linker="$TOOLCHAIN_DIR/bin/arm-roku-linux-gnueabi-gcc"
    local strip_cmd="$TOOLCHAIN_DIR/bin/arm-roku-linux-gnueabi-strip --remove-section=.comment --remove-section=.note --strip-unneeded"
    local rustflags="-C link-arg=--sysroot=$TOOLCHAIN_DIR/arm-roku-linux-gnueabi/sysroot/"

    build_native_base "$target_dir" "$target_triple" "$target_abi_id" "$linker" "$strip_cmd" "$rustflags" "$features" "$output_suffix"
}

function build_android_native() {
    local target_dir=${1:?}         # Fail if not given
    local features=${2:-}           # Optional features
    local output_suffix=${3:-}      # Optional output suffix

    local target_triple=armv7-linux-androideabi
    local target_abi_id=1472453a32544f64da062fe7d0f25df7f71f518e587c28df22b49ba82eba1d67

    local ANDROID_NDK_DIR=$(brazil-path "[AndroidNDK]run.artifact.android-ndk")/android-ndk-r27c/
    if [ ! -d "$ANDROID_NDK_DIR" ]; then
        log_error "Android NDK directory not found: $ANDROID_NDK_DIR"
        return 1
    fi

    local ANDROID_TOOLCHAIN=$ANDROID_NDK_DIR/toolchains/llvm/prebuilt/linux-x86_64/bin
    if [ ! -d "$ANDROID_TOOLCHAIN" ]; then
        log_error "Android toolchain not found: $ANDROID_TOOLCHAIN"
        return 1
    fi

    local linker="$ANDROID_TOOLCHAIN/armv7a-linux-androideabi21-clang"
    local strip_cmd="$ANDROID_TOOLCHAIN/llvm-strip --remove-section=.comment --remove-section=.note --strip-unneeded"
    local rustflags="-C link-arg=--sysroot=$ANDROID_NDK_DIR/toolchains/llvm/prebuilt/linux-x86_64/sysroot/"

    build_native_base "$target_dir" "$target_triple" "$target_abi_id" "$linker" "$strip_cmd" "$rustflags" "$features" "$output_suffix"
}

function build_prod_rust_client() {
    spawn $job_build_prod
}

function build_test_utils_rust_client() {
    spawn $job_build_test_utils
    spawn $job_build_test_utils_debug
}

function build_aot_threads_bundles() {
    ## Prod bundles
    spawn $job_build_aot
    spawn $job_build_threads
    spawn $job_build_aot_threads

    ## Test Utils bundles
    spawn $job_build_aot_test_utils
    spawn $job_build_threads_test_utils
    spawn $job_build_aot_threads_test_utils
}

function build_prod_roku_native() {
    spawn $job_build_roku_native
}

function build_test_utils_roku_native() {
    spawn $job_build_roku_native_test_utils
    spawn $job_build_roku_native_test_utils_debug
}

function build_prod_android_native() {
    spawn $job_build_android_native
}

function build_test_utils_android_native() {
    spawn $job_build_android_native_test_utils
    spawn $job_build_android_native_test_utils_debug
}

max_jobs=${GAOT_MAX_JOBS:=$DEFAULT_MAX_JOBS}

# we need this because brazil-path is not safe to parallelise
# it is used in build.rs of custom allocator
# https://issues.amazon.com/BrazilBX-7359
brazil-path build.libfarm

if [[ "$run_flag" == "all" ]]; then
    log "Building all bundles"
    for job in "${all_jobs[@]}"
    do
        spawn $job
    done
    # Do not add more commands here -- please instead expand the all_jobs array
elif [[ "$run_flag" == "base" ]]; then
    log "Building rust client bundle for prod and test_utils in parent process $$"
    build_prod_rust_client
    build_test_utils_rust_client
elif [[ "$run_flag" == "native" ]]; then
    log "Building Roku native bundle for prod and test_utils in parent process $$"
    build_prod_roku_native
    build_prod_android_native
    build_test_utils_roku_native
    build_test_utils_android_native
elif [[ "$run_flag" == "roku" ]]; then
    log "Building Roku native bundle for prod and test_utils in parent process $$"
    build_prod_roku_native
    build_test_utils_roku_native
elif [[ "$run_flag" == "android" ]]; then
    log "Building Android native bundle for prod and test_utils in parent process $$"
    build_prod_android_native
    build_test_utils_android_native
elif [[ "$run_flag" == "all-native" ]]; then
    log "Building all native bundles (Roku and Android) in parent process $$"
    build_prod_roku_native
    build_test_utils_roku_native
    build_prod_android_native
    build_test_utils_android_native
elif [[ "$run_flag" == "shard" ]]; then
    IFS='/' read shard_no total_shards <<< $2 # Expects a `shard 1/10` parameter (1-indexed to match convention)
    if [[ ${#all_jobs[@]} != $total_shards ]]; then
        # Check the total number of shards to ensure anyone adding a build command doesn't forget to update sharded packages
        log_error "The total number of shards doesn't match the number of build commands"
        exit 1
    fi
    build_command_index=$((shard_no - 1))
    if (( build_command_index >= 0 && build_command_index < ${#all_jobs[@]} )); then
        log "Running command: ${all_jobs[$build_command_index]}" 
        spawn "${all_jobs[$build_command_index]}"
    else
        log_error "Invalid shard number"
        exit 1
    fi
else
    log_error "Unrecognized parameter: $1"
    exit 2
fi

wait
