#!/bin/bash

set -euo pipefail

artifacts_directory="$(brazil-path package-build-root)"
change_id=$BRAZIL_PACKAGE_CHANGE_ID

echo "Reading tod.json file"

if [ -f tod.json ]; then
    deployment_id=$(grep -o '"event-id":"[^"]*"' tod.json | cut -d'"' -f4)
    if [ -z "$deployment_id" ]; then
        echo "Error: Could not find event-id pattern in tod.json"
        exit 1
    fi
else
    echo "Error: Could not find tod.json file"
    exit 1
fi

s3_bucket_prefix="${deployment_id}_${change_id}"
s3_bucket_name="avlrc-rust-faster-build-artifact-prod"
s3_bucket_url="s3://${s3_bucket_name}/${s3_bucket_prefix}/baseline-build-artifacts"

echo "Uploading baseline-build build-artifacts to S3 path ${s3_bucket_url}"

# --no-follow-symlinks is necessary becuase the artifacts_directory contains symlinks to non-existent files
if aws s3 sync "${artifacts_directory}" "${s3_bucket_url}" \
    --no-follow-symlinks \
    --exclude "private/*" \
    --exclude "*.gz" \
    --exclude "brazil-documentation/*" \
    --exclude ".DS_Store"; then
    echo "Uploading baseline-build build-artifacts successful"
else
    echo "Uploading baseline-build build-artifacts failed"
fi