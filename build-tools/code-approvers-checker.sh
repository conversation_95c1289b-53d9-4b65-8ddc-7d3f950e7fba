#!/bin/bash

display_usage() {
    cat <<EOF
OVERVIEW:
    Checks for coverage gaps in CODE_APPROVERS.yaml by verifying that all crates
    are covered by at least one pattern in the file.

USAGE:
    ./code-approvers-checker.sh [OPTIONS]

OPTIONS:
    --help              Display this message
    --no_color          Disables the ANSI codes used to display colors
    --verbose           Shows detailed information about pattern matching
EOF
}

help="0"
RED="\033[31m"
GREEN="\033[32m"
YELLOW="\033[33m"
BLUE="\033[34m"
END="\033[0m"
verbose="0"
client_path=$(dirname $(dirname "$0"))
code_approvers_file="${client_path}/CODE_APPROVERS.yaml"

while [[ $# -gt 0 ]]; do
  case $1 in
    --no_color)
      RED=""
      GREEN=""
      YELLOW=""
      BLUE=""
      END=""
      shift
      ;;
    --verbose)
      verbose="1"
      shift
      ;;
    --help)
      help="1"
      shift
      ;;
    *)
      echo "Error: option '$1' is invalid."
      exit 1
      ;;
  esac
done

if [ "$help" = "1" ]; then
  display_usage
  exit 0
fi

if [ ! -f "$code_approvers_file" ]; then
  echo -e "${RED}Error: CODE_APPROVERS.yaml not found at ${code_approvers_file}${END}"
  exit 1
fi

echo -e "${BLUE}Checking for coverage gaps in CODE_APPROVERS.yaml...${END}"

# Extract patterns from CODE_APPROVERS.yaml
echo -e "${BLUE}Extracting patterns from CODE_APPROVERS.yaml...${END}"
patterns=()
while IFS= read -r line; do
  if [[ $line =~ ^[[:space:]]*matching:[[:space:]]*\'([^\']*)\' ]]; then
    pattern="${BASH_REMATCH[1]}"
    # Skip placeholder patterns
    if [[ "$pattern" == "THIS IS A PLACEHOLDER"* ]]; then
      continue
    fi
    # Skip the catch-all pattern
    if [[ "$pattern" == "*" ]]; then
      continue
    fi
    patterns+=("$pattern")
    if [ "$verbose" = "1" ]; then
      echo -e "  Found pattern: ${YELLOW}${pattern}${END}"
    fi
  fi
done < "$code_approvers_file"

echo -e "${BLUE}Found ${#patterns[@]} patterns in CODE_APPROVERS.yaml${END}"

# Get list of all crates
echo -e "${BLUE}Checking crates directory...${END}"
crates_dir="${client_path}/crates"
crates=()
for dir in "$crates_dir"/*; do
  if [ -d "$dir" ]; then
    crate_name=$(basename "$dir")
    crates+=("$crate_name")
  fi
done

echo -e "${BLUE}Found ${#crates[@]} crates to check${END}"

# Check each crate against the patterns
uncovered_crates=()
covered_crates=()

for crate in "${crates[@]}"; do
  crate_path="crates/${crate}"
  covered=0
  matching_pattern=""
  
  for pattern in "${patterns[@]}"; do
    # Convert glob pattern to regex for bash matching
    regex_pattern=$(echo "$pattern" | sed 's/\*/[^\/]*/g')
    
    if [[ "$crate_path" =~ ^$regex_pattern ]]; then
      covered=1
      matching_pattern="$pattern"
      break
    fi
    
    # Also check with wildcard at the end for directory patterns
    if [[ "$pattern" == *"/" ]]; then
      if [[ "$crate_path/" =~ ^$regex_pattern ]]; then
        covered=1
        matching_pattern="$pattern"
        break
      fi
    fi
  done
  
  if [ "$covered" = "1" ]; then
    covered_crates+=("$crate")
    if [ "$verbose" = "1" ]; then
      echo -e "  ${GREEN}✓${END} Crate ${GREEN}${crate}${END} is covered by pattern: ${YELLOW}${matching_pattern}${END}"
    fi
  else
    uncovered_crates+=("$crate")
    echo -e "  ${RED}✗${END} Crate ${RED}${crate}${END} is not covered by any pattern"
  fi
done

# Report results
echo -e "\n${BLUE}Coverage Report:${END}"
echo -e "  ${GREEN}${#covered_crates[@]}${END} crates are covered"
echo -e "  ${RED}${#uncovered_crates[@]}${END} crates are not covered"

if [ ${#uncovered_crates[@]} -gt 0 ]; then
  echo -e "\n${YELLOW}Suggestion: Add appropriate patterns to CODE_APPROVERS.yaml for these crates.${END}"
  echo -e "${YELLOW}Example:${END}"
  echo -e "  - pattern:"
  echo -e "      matching: 'crates/${uncovered_crates[0]}*'"
  echo -e "    approvers:"
  echo -e "      - *appropriate_team"
  exit 1
else
  echo -e "\n${GREEN}All crates are covered by patterns in CODE_APPROVERS.yaml!${END}"
  exit 0
fi