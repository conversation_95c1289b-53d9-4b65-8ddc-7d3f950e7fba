#!/bin/bash
#
# hybrid-run-native
# 1. Builds a native build of the rust application
# 2. Checks if the JS Bundle is serving js (AVLivingRoomClientBundle), if its not will start a new instance
# 3. Starts igniton with the required params for a hybrid native app
#
# Use this script if you'd like to test the combined JS and Rust application
#
# To run this make sure you do the following first:
# - Your blastIgniteConfig.json in bundle has "bootstrapRustClient": true under the weblabs section
# - Set the following environment variables:
# RUST_CLIENT_PATH (eg: /Volumes/workplace/avlrc-rust-and-js/src/AVLivingRoomRustClient)
# AV_LIVING_ROOM_CLIENT_BUNDLE_PATH (eg: /Volumes/workplace/avlrc-rust-bundle/src/AVLivingRoomClientBundle)
# IGNITION_X_EMULATOR_PATH (eg: /Users/<USER>/IgnitionXEmulator)
#
# The bundle above will need to be using the AVLRC/rel versionset and be linked to the RustClient through its packageInfo
# That either means they are within the same workspace or are just linked via the packageInfo in separate workspaces
#
# If you encounter any issues or want this script to support something please feel free to add onto it!
set -e  
cleanup() {
	trap - SIGTERM && kill -- -$$
}
trap cleanup EXIT SIGTERM SIGINT

display_usage() {
    cat <<EOF
OVERVIEW:
     Runs a combined JS+Rust client in either of the following ways:
     1. Builds native build and uses prod JS
     2. (--local) Builds a native build of the rust application
        - Checks if the JS Bundle is serving js (AVLivingRoomClientBundle), if its not will start a new instance
        - Starts igniton with the required params for a hybrid native app

    Use this script if you'd like to test the combined JS and Rust application

    To run this make sure you do the following first:
     - Set the following environment variables:
       -  AV_LIVING_ROOM_CLIENT_BUNDLE_PATH (eg: /Volumes/workplace/avlrc-rust-bundle/src/AVLivingRoomClientBundle) (--local only)
       -  AV_LIVING_ROOM_CLIENT_PATH (eg: /Volumes/workplace/avlrc-rust-and-js/src/AVLivingRoomClient) (--local only)
       -  IGNITION_X_EMULATOR_PATH (eg: /Users/<USER>/IgnitionXEmulator)

    When using prod (the default) make sure your account is within the LRC_RUST_PAGES TOS group.

    The bundle above will need to be using the AVLRC/rel versionset and be linked to the RustClient through its packageInfo
    That either means they are within the same workspace or are just linked via the packageInfo in separate workspaces
    If you encounter any issues or want this script to support something please feel free to add onto it!

USAGE:
    ./hybrid-run-native.sh

OPTIONS:
    --help              Display this message
    --local             Run against local avlrc and run bundle for you
    --did               Set the device id to launch the emulator with
    --dtid              Set the device type id to launch the emulator with
    --wasm              Run wasm build instead of native
    --release           Use release build instead of debug build
    --features          Features to set when building the application
    --rust_client_path  Set the rust client path if executing outside of the build-tools folder
    --kill_existing     If to sigkill the previous instance launched this this script when its run again
    --proxyman          Sets up the emulator to be proxyman compatible
    --mitmproxy         Sets up the emulator to be mitmproxy compatible
    --launch_config     Allows specifying launch config for passing in deep link (e.g: --launch_config contentId=amzn1.dv.gti.fcafba5d-1d8f-2901-b602-2747381e5857&mediaType=movie)
    --tts               Enables text-to-speech
    --pointer_control   Enables pointer control
    --log_level
    --debug_overlay     Enables debug overlay
    --resolution        Set emulator resolution (720p, 1080p, or custom WIDTHxHEIGHT like 1280x720)
    --width             Set custom emulator width (use with --height, or use --resolution instead)
    --height            Set custom emulator height (use with --width, or use --resolution instead)
EOF
}

PID_FILE_PATH=~/.rust_hybrid_runner.pid
help="0"

# If the below aren't valid authentication won't work
device_id="uuid0d02b8fb0e724c609d75b9ee0cbd29e4"
device_type_id="AFOQV1TK6EU6O"
enable_pointer_control="0"
enable_debug_overlay="0"
kill_existing="0"
proxyman="0"
mitmproxy="0"
launch_config="0"
release="0"
tts="0"
wasm="0"
features=""
log_level="ALL:INFO"
local="0"
local_no_build="0"
client_path=$(dirname $(dirname "$0"))
resolution=""
custom_width=""
custom_height=""
while [[ $# -gt 0 ]]; do
  case $1 in
    --release)
      release="1"
      shift
      ;;
    --local)
	    local="1"
	    shift
	    ;;
	  --local_no_build)
	    local_no_build="1"
	    shift
	    ;;
	  --wasm)
	    wasm="1"
	    shift
	    ;;
	  --launch_config)
	    launch_config="$2"
	    shift
	    shift
	    ;;
	  --log_level)
	    log_level="$2"
	    shift
	    shift
	    ;;
	  --did)
    	device_id="$2"
    	shift
    	shift
      ;;
    --features)
      features=",$2"
      shift
      shift
      ;;
    --rust_client_path)
      client_path="$2"
      shift
      shift
      ;;
    --dtid)
      device_type_id="$2"
      shift
      shift
      ;;
    --pointer_control)
      enable_pointer_control="1"
      shift
      ;;
    --debug_overlay)
      enable_debug_overlay="1"
      shift
      ;;
    --kill_existing)
      kill_existing="1"
      shift
      ;;
    --proxyman)
      proxyman="1"
      shift
      ;;
    --mitmproxy)
      mitmproxy="1"
      shift
      ;;
    --tts)
      tts="1"
      shift
      ;;
    --resolution)
      resolution="$2"
      shift
      shift
      ;;
    --width)
      custom_width="$2"
      shift
      shift
      ;;
    --height)
      custom_height="$2"
      shift
      shift
      ;;
    --help)
      help="1"
      shift # past argument
      ;;
    *)
        echo "Error: option '$1' is invalid."
        exit 1
        ;;
  esac
done

if [ "$help" = "1" ]; then
  display_usage
  exit 0
fi

if [ "$kill_existing" = "1" ]; then
  if [ -f "$PID_FILE_PATH" ]; then
      # Read the content of the PID file into a variable
      RUST_CLIENT_HYBRID_PID=$(<"$PID_FILE_PATH")
      echo "PID file exists. Process ID: $RUST_CLIENT_HYBRID_PID"
  else
      RUST_CLIENT_HYBRID_PID=""
  fi

	if [ -n "$RUST_CLIENT_HYBRID_PID" ] && ps -p "$RUST_CLIENT_HYBRID_PID" > /dev/null; then
	    echo "Killing existing IgnitionX emulator instance with PID: $RUST_CLIENT_HYBRID_PID"
	    kill -9 "$RUST_CLIENT_HYBRID_PID"
	fi
fi

env_vars=""
if [ "$tts" = "1" ]; then
  # Enables Text-to-speech
  env_vars="$env_vars export TTS_ENABLED=true &&"
fi

built_features=""
if [ "$enable_pointer_control" = "1" ]; then
  built_features="$built_features,pointer_control"
fi
if [ "$enable_debug_overlay" = "1" ]; then
  built_features="$built_features,amzn-ignx-compositron/debug_overlay"
fi

# Build Native
cd "$client_path"

path_segment="debug"
build_env_arg=""
if [ "$release" = "1" ]; then
  path_segment="release"
  build_env_arg="--release"
fi

build_cmd="cargo build -p amzn-av-living-room-rust-client $build_env_arg --lib --features=amzn-ignx-compositron/use_ignx_local$built_features$features"
file_path_emulator="--local-native-module-path=$client_path/build/private/cargo-target/$path_segment/libamzn_av_living_room_rust_client.dylib"
if [ "$wasm" = "1" ]; then
  build_cmd="cargo build --target wasm32-wasip1 $build_env_arg --features=amzn-ignx-compositron/use_ignx_local$built_features$features"
  file_path_emulator="--local-wasm-module-path=$client_path/build/private/cargo-target/wasm32-wasip1/$path_segment/amzn-av-living-room-rust-client.wasm"
fi

eval "$build_cmd"

emulator_args="$file_path_emulator --set-default-js-engine=QuickJs --log-level=$log_level"

if [ "$proxyman" = "1" ]; then
  # Invoke proxyman setup
  set -a && source "/Users/<USER>/.proxyman/proxyman_env_automatic_setup.sh" && set +a

  # Due to proxyman using a custom cert verification won't work so disable it
  emulator_args="$emulator_args --disable-ssl-cert"
fi

if [ $mitmproxy = "1" ]; then
  # Append mitmproxy launch args
  emulator_args="$emulator_args --http-proxy-server=http://127.0.0.1:8080 --disable-ssl-cert"
fi

if [ "$launch_config" != "0" ]; then
  emulator_args="$emulator_args --launch-config=$launch_config"
fi

# Handle resolution settings
resolution_args=""
if [ -n "$resolution" ]; then
  case "$resolution" in
    720p)
      resolution_args="--width=1280 --height=720"
      ;;
    1080p)
      resolution_args="--width=1920 --height=1080"
      ;;
    *x*)
      # Custom resolution in WIDTHxHEIGHT format
      width=$(echo "$resolution" | cut -d'x' -f1)
      height=$(echo "$resolution" | cut -d'x' -f2)
      if [[ "$width" =~ ^[0-9]+$ ]] && [[ "$height" =~ ^[0-9]+$ ]]; then
        resolution_args="--width=$width --height=$height"
      else
        echo "Error: Invalid resolution format '$resolution'. Use format like 1280x720 or predefined resolutions (720p, 1080p)."
        exit 1
      fi
      ;;
    *)
      echo "Error: Unsupported resolution '$resolution'. Supported: 720p, 1080p, or custom WIDTHxHEIGHT format."
      exit 1
      ;;
  esac
elif [ -n "$custom_width" ] && [ -n "$custom_height" ]; then
  # Custom width and height specified
  if [[ "$custom_width" =~ ^[0-9]+$ ]] && [[ "$custom_height" =~ ^[0-9]+$ ]]; then
    resolution_args="--width=$custom_width --height=$custom_height"
  else
    echo "Error: Width and height must be numeric values."
    exit 1
  fi
elif [ -n "$custom_width" ] || [ -n "$custom_height" ]; then
  echo "Error: Both --width and --height must be specified together, or use --resolution instead."
  exit 1
fi

if [ -n "$resolution_args" ]; then
  emulator_args="$emulator_args $resolution_args"
fi

device_proxy_args=""
# Block is used when running against local AVLRC
if [ "$local" = "1" ]; then
  # Build JS Changes unless specified not to
  if [ $local_no_build = "0" ]; then
    echo -e "Building JS Client changes to serve via bundle"
      (cd "$AV_LIVING_ROOM_CLIENT_PATH" && brazil-build run build-prod -- --env deviceType=Rust-Ignite-XP)&
      wait $!
      BUILT_CLIENT_CHANGES_EXIT_CODE=$?
      if [ $BUILT_CLIENT_CHANGES_EXIT_CODE -ne 0 ]; then
        echo -e "Failed to build JS Client changes, exiting."
        exit 1
      fi
  fi

  # Check if the Bundle server is running
  set +e
  (curl --output /dev/null --silent --head --fail http://127.0.0.1:3999/index.js)&
  wait $!
  BUNDLE_SERVER_PING_EXIT_CODE=$?
  set -e

  # If the bundle server isn't running start and wait for it to start serving JS
  if [ $BUNDLE_SERVER_PING_EXIT_CODE -ne 0 ]; then
    echo -e "Starting bundle server in background"
    (cd "$AV_LIVING_ROOM_CLIENT_BUNDLE_PATH" && brazil-build run build -- --watch --server -p 3999 --rust-client -f 'Ignition-X-XP' --nobytecode --noaot --nothreads --notestutils --nonativebundle)&
    export BUNDLE_SERVER_PID=$!

    echo -e "Waiting for bundle server to finish startup"
    # Wait till bundle server starts running
    until curl --output /dev/null --silent --head --fail http://127.0.0.1:3999/index.js; do
          printf '.'
          sleep 5
    done
  fi

  # Start ignition telling it to use the native lib rather than the one supplied by bundle
  device_proxy_args="--device-proxy-url=http://127.0.0.1:3999 --react-uri-prefix=/index.js"
fi

emulator_args="$emulator_args $device_proxy_args --device-id=$device_id --device-type-id=$device_type_id"
cmd="$env_vars $IGNITION_X_EMULATOR_PATH/bin/ignition $emulator_args &"
echo -e "Starting emulator with args: $cmd"
eval $cmd
RUST_CLIENT_HYBRID_PID=$!
echo "$RUST_CLIENT_HYBRID_PID" > "$PID_FILE_PATH"
wait $RUST_CLIENT_HYBRID_PID
