#!/bin/bash

function build_args() {
    crate_name=$1
    additional_features=""
    shift;

    # Declare variables $arg1, $arg2, etc for each positional argument --arg1, --arg2, etc provided to $0
    while [[ $# -gt 0 ]]; do
    case $1 in
        --example)
            example_name="$2"
            shift # past argument
            shift # past value
            ;;
        --wasm)
            wasm="wasm"
            shift # past argument
            ;;
        --release)
            release="release"
            shift # past argument
            ;;
        --lldb)
            # Just ignore
            shift # past argument
            ;;
        --additional-features)
            additional_features=",$2"
            shift 2
            ;;
        *)
            echo "Error: option '$1' is invalid."
            exit 1
            ;;
    esac
    done

    # Relevant paths
    cargo_build_dir="$(cargo brazil print target-dir)"
    ignition_x_bin=$IGNITION_X_PATH/bin/ignition

    # Build
    build_command_args="rustc --package ${crate_name}"

    if [[ ${example_name+x} ]]; then
        build_command_args="${build_command_args} --example ${example_name}"
    fi

    if [[ ${release+x} ]]; then
        build_command_args="${build_command_args} --release"
    fi

    if [[ ${wasm+x} ]]; then
        build_command_args="${build_command_args} --target wasm32-wasip1 --features=amzn-ignx-compositron/allow_no_js_vm${additional_features} --crate-type=bin"
    else
        build_command_args="${build_command_args} --features=amzn-ignx-compositron/allow_no_js_vm,amzn-ignx-compositron/use_ignx_local${additional_features} --crate-type=cdylib"
    fi

    echo "${build_command_args}"
}
