#!/bin/bash

set -euo pipefail

display_usage() {
    cat <<EOF
OVERVIEW:
    Builds and runs an example on an instance of IgnitionX.

USAGE:
    run-example <name-of-crate> [options]

    Example: run-example media-background --example media_background

OPTIONS:
    --help                  Display this message
    --example
    --wasm                  Use the 'wasm32-wasip1' build target
    --native                Use the host (native) build target
    --release               Build the example in release mode
    --lldb                  Attach an instance of "lldb" to the application process
    --additional-features   Appends additional features to the essential ones during the build
EOF
}

script_dir=$(dirname $0)
original_args="$@"

# Check the required arguments were provided
if [[ $# -lt 1 ]]; then
    display_usage
    exit 0
fi

crate_name=$1
shift;

# Declare variables $arg1, $arg2, etc for each positional argument --arg1, --arg2, etc provided to $0
while [[ $# -gt 0 ]]; do
  case $1 in
    --example)
        example_name="$2"
        shift # past argument
        shift # past value
        ;;
    --wasm)
        wasm="wasm"
        shift # past argument
        ;;
    --native)
        native="native"
        shift # past argument
        ;;
    --release)
        release="release"
        shift # past argument
        ;;
    --lldb)
        lldb="lldb"
        shift # past argument
        ;;
    --help)
        help="help"
        shift # past argument
        ;;
    --additional-features) # Ignored here, but used as a build arg
        shift 2 # past argument + value
        ;;
    *)
        echo "Error: option '$1' is invalid."
        exit 1
        ;;
  esac
done


if [[ ${help+x} ]]; then
    display_usage
    exit 0
fi

# Relevant paths
cargo_build_dir="$(cargo brazil print target-dir)"
ignition_x_bin=$IGNITION_X_PATH/bin/ignition

# Build before running
source "${script_dir}/shared"
build_command_args=$(build_args ${original_args})

# Run
run_command_args="--local-wasm-path=${cargo_build_dir}"

if [[ ${wasm+x} ]]; then
    run_command_args="${run_command_args}/wasm32-wasip1"
fi

if [[ ${release+x} ]]; then
    run_command_args="${run_command_args}/release"
else
    run_command_args="${run_command_args}/debug"
fi

if [[ ${example_name+x} ]]; then
    if [[ ${wasm+x} ]]; then
        run_command_args="${run_command_args}/examples/${example_name}.wasm"
    else
        run_command_args="${run_command_args}/examples/lib${example_name}.dylib"
    fi
else
    bin_name=$(echo ${crate_name} | sed 's/-/_/g')
    if [[ ${wasm+x} ]]; then
        run_command_args="${run_command_args}/${bin_name}.wasm"
    else
        run_command_args="${run_command_args}/lib${bin_name}.dylib"
    fi
fi

cargo ${build_command_args}

if [[ ${lldb+x} ]]; then
    if [[ ${wasm+x} ]]; then
        echo "Error: '--lldb' option is not compatible with '--wasm' option."
        exit 1
    fi

    ${ignition_x_bin} ${run_command_args}&
    lldb -p $!
else 
    ${ignition_x_bin} ${run_command_args}
fi
