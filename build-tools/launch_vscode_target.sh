#!/bin/bash

# Get the script's directory (workspaceFolder)
WORKSPACE_FOLDER="$( cd "$( dirname "${BASH_SOURCE[0]}" )/.." && pwd )"

echo "WORKSPACE_FOLDER = ${WORKSPACE_FOLDER}"

DECLARE_CLEANUP_FILES=()

cleanup() {
    for file in "${DECLARE_CLEANUP_FILES[@]}"; do
        rm -f "$file" 2>/dev/null
    done
}

trap cleanup EXIT

# Function to remove C-style comments from JSON and clean it
prepare_json() {
    local json_file="$1"
    # Remove // comments and empty lines
    sed 's/^ *\/\/.*//' "$json_file" | \
        sed '/^[[:space:]]*$/d'
}

usage() {
    echo "Usage: $0 [-c config_name]"
    echo "  -c config_name    Directly run the specified configuration"
    echo "  If no arguments provided, runs in interactive mode"
    exit 1
}

# Function to replace variables in a string
replace_variables() {
    local input="$1"

    # First replace ${workspaceFolder}
    input="${input//\${workspaceFolder\}/${WORKSPACE_FOLDER}}"

    # Then replace ${env:IGNITION_X_PATH}
    input="${input/\${env:IGNITION_X_PATH\}/$IGNITION_X_PATH}"

    echo "$input"
}

# Function to run a task from tasks.json
run_task() {
    local task_label="$1"

    # Check if task exists in tasks.json
    if [ ! -f "$WORKSPACE_FOLDER/.vscode/tasks.json" ]; then
        echo "Error: tasks.json not found"
        exit 1
    fi

    # Prepare JSON and store in temporary file
    local temp_tasks=$(mktemp)
    DECLARE_CLEANUP_FILES+=("$temp_tasks")
    prepare_json "$WORKSPACE_FOLDER/.vscode/tasks.json" > "$temp_tasks"

    # Extract task details using jq
    local task_command=$(jq -r ".tasks[] | select(.label == \"$task_label\") | .command" "$temp_tasks")
    local task_cwd=$(jq -r ".tasks[] | select(.label == \"$task_label\") | .options.cwd // \"\"" "$temp_tasks")
    
    # Extract args as JSON array
    local args_json=$(jq -r ".tasks[] | select(.label == \"$task_label\") | .args" "$temp_tasks")
    
    # Create an empty args array
    declare -a task_args_array
    
    # Process each argument if args is an array
    if [ "$args_json" != "null" ] && [ "$args_json" != "" ]; then
        # Get number of arguments
        local num_args=$(jq '. | length' <<< "$args_json")
        
        # Process each argument
        for ((i=0; i<num_args; i++)); do
            # Extract single argument
            local arg=$(jq -r ".[$i]" <<< "$args_json")
            
            # Replace variables in the argument
            arg=$(replace_variables "$arg")
            
            # Add to array
            task_args_array+=("$arg")
        done
    fi

    # Clean up temporary file
    rm "$temp_tasks"

    # Replace variables in paths
    task_command=$(replace_variables "$task_command")
    task_cwd=$(replace_variables "$task_cwd")

    if [ -n "$task_cwd" ]; then
        cd "$task_cwd"
    fi

    if [ -n "$task_command" ]; then
        echo "Running task: $task_label"
        "$task_command" "${task_args_array[@]}"
        echo "task done: $task_label"
    fi
}

# Function to run a launch configuration
run_launch_config() {
    local config_name="$1"

    # Prepare JSON and store in temporary file
    local temp_launch=$(mktemp)
    DECLARE_CLEANUP_FILES+=("$temp_launch")
    prepare_json "$WORKSPACE_FOLDER/.vscode/launch.json" > "$temp_launch"

    # Verify configuration exists
    local config_exists=$(jq -r ".configurations[] | select(.name == \"$config_name\") | .name" "$temp_launch")
    if [ -z "$config_exists" ]; then
        echo "Error: Configuration '$config_name' not found"
        echo "Available configurations:"
        jq -r '.configurations[] | .name' "$temp_launch"
        rm "$temp_launch"
        exit 1
    fi

    # Extract configuration details
    local program=$(jq -r ".configurations[] | select(.name == \"$config_name\") | .program" "$temp_launch")
    local cwd=$(jq -r ".configurations[] | select(.name == \"$config_name\") | .cwd // \"\"" "$temp_launch")
    local pre_task=$(jq -r ".configurations[] | select(.name == \"$config_name\") | .preLaunchTask // \"\"" "$temp_launch")
    
    # Extract args as JSON array
    local args_json=$(jq -r ".configurations[] | select(.name == \"$config_name\") | .args" "$temp_launch")
    
    # Create an empty args array
    declare -a args_array
    
    # Process each argument if args is an array
    if [ "$args_json" != "null" ] && [ "$args_json" != "" ]; then
        # Get number of arguments
        local num_args=$(jq '. | length' <<< "$args_json")
        
        # Process each argument
        for ((i=0; i<num_args; i++)); do
            # Extract single argument
            local arg=$(jq -r ".[$i]" <<< "$args_json")
            
            # Replace variables in the argument
            arg=$(replace_variables "$arg")
            
            # Add to array
            args_array+=("$arg")
        done
    fi

    # Clean up temporary file
    rm "$temp_launch"

    # Replace variables in paths
    program=$(replace_variables "$program")
    cwd=$(replace_variables "$cwd")

    # Run preLaunchTask if specified
    if [ -n "$pre_task" ] && [ "$pre_task" != "null" ]; then
        run_task "$pre_task"
    fi

    # Change to working directory if specified
    if [ -n "$cwd" ] && [ "$cwd" != "null" ]; then
        cd "$cwd"
    fi

    # Run the program with arguments
    if [ -n "$program" ] && [ "$program" != "null" ]; then
        echo "Running configuration: $config_name"
        echo "Program: $program"
        echo "Arguments: ${args_array[*]}"
        "$program" "${args_array[@]}"
    fi
}

# Check if IGNITION_X_PATH is set
if [ -z "$IGNITION_X_PATH" ]; then
    echo "Error: IGNITION_X_PATH environment variable is not set"
    exit 1
fi

# Check if launch.json exists
if [ ! -f "$WORKSPACE_FOLDER/.vscode/launch.json" ]; then
    echo "Error: launch.json not found"
    exit 1
fi

# Parse command line arguments
while getopts "c:h" opt; do
    case $opt in
        c)
            config_name="$OPTARG"
            run_launch_config "$config_name"
            exit 0
            ;;
        h)
            usage
            ;;
        \?)
            usage
            ;;
    esac
done

# If no arguments provided, run in interactive mode
if [ $OPTIND -eq 1 ]; then
    # Create temporary file with cleaned JSON
    temp_launch=$(mktemp)
    DECLARE_CLEANUP_FILES+=("$temp_launch")
    prepare_json "$WORKSPACE_FOLDER/.vscode/launch.json" > "$temp_launch"

    # List all available configurations
    echo "Available launch configurations:"
    jq -r '.configurations[] | .name' "$temp_launch" | nl -w2 -s') '

    # Get user selection
    echo -n "Enter the number of the configuration to run: "
    read selection

    # Get the selected configuration name
    config_name=$(jq -r ".configurations[$(($selection-1))] | .name" "$temp_launch")

    # Clean up temporary file
    rm "$temp_launch"

    if [ -n "$config_name" ] && [ "$config_name" != "null" ]; then
        run_launch_config "$config_name"
    else
        echo "Invalid selection"
        exit 1
    fi
fi