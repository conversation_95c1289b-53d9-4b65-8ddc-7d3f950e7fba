#!/bin/bash

# Version of the pre-cr setup
# If you update the version, update the .gitignore as well
CURRENT_VERSION="v0"
PRE_CR_FLAG_PREFIX=".PRE_CR_SETUP_"
PRE_CR_FLAG_FILE="${PRE_CR_FLAG_PREFIX}${CURRENT_VERSION}"
PRE_CR_GLOBAL_HOOK="$HOME/.config/cr/hooks/pre-cr"

for flag in ${PRE_CR_FLAG_PREFIX}*; do
    INITIALIZED_VERSION=${flag#$PRE_CR_FLAG_PREFIX}

    if [[ "$INITIALIZED_VERSION" != "$CURRENT_VERSION" ]]; then
        echo "Updating pre-cr hook from version $INITIALIZED_VERSION to $CURRENT_VERSION"
        # Clean up old version
        rm -f "$flag"
        rm -f "$PRE_CR_GLOBAL_HOOK"
    else
        echo "Pre-cr hook is already at version $CURRENT_VERSION"
        exit 0
    fi
    break # We only need to check the first flag file
done

# Get the absolute path to your package's pre-cr file
PACKAGE_PRE_CR="$(pwd)/pre-cr"

# Create the hooks directory if it doesn't exist
mkdir -p ~/.config/cr/hooks

# Create a wrapper script that checks for changes against origin/mainline
cat >"$PRE_CR_GLOBAL_HOOK" <<EOF
#!/bin/bash
# Auto-generated hook for AVLivingRoomRustClient

# Early return if we're already in AVLivingRoomRustClient directory
if [[ "\$(pwd)" == */AVLivingRoomRustClient* ]]; then
    # Running from AVLivingRoomRustClient directory, skip global hook
    exit 0
fi

# Check if brazil ws command is available
if ! command -v brazil &> /dev/null; then
    echo "Warning: brazil command not found, skipping AVLivingRoomRustClient pre-cr validation"
    exit 0
fi

# Check if AVLivingRoomRustClient is part of the current workspace
RUST_CLIENT_LINE=\$(brazil ws show 2>/dev/null | grep "^\s*AVLivingRoomRustClient-.*->")
if [[ -z "\$RUST_CLIENT_LINE" ]]; then
    # We don't have to continue as AVLivingRoomRustClient is not in the workspace
    exit 0
fi

# As this global pre-cr will be executed for any cr call, we only want to run rust pre-cr when
# AVLivingRoomRustClient is included in the CR either specifically or with --all
if [[ "\$*" == *"--all"* ]]; then
    # --all flag is present and rust client is in workspace, continue
    :
elif [[ "\$*" == *"AVLivingRoomRustClient"* ]]; then
    # AVLivingRoomRustClient is explicitly included, continue
    :
else
    # Neither --all nor explicit include, skip RustClient pre-cr
    exit 0
fi


# Check if we're in a git repository
if ! git rev-parse --is-inside-work-tree &> /dev/null; then
    echo "Warning: not in a git repository, skipping AVLivingRoomRustClient pre-cr validation"
    exit 0
fi

echo "AVLivingRoomRustClient found in workspace"

# Get repository root
REPO_ROOT=\$(git rev-parse --show-toplevel)

# Get workspace root using brazil-path
WORKSPACE_ROOT=\$(brazil-path workspace-root)

RUST_CLIENT_PATH="\$WORKSPACE_ROOT/src/AVLivingRoomRustClient"

echo "Checking for changes in: \$RUST_CLIENT_PATH"

# Check if there are any changes to the rust client directory compared to origin/mainline
cd "\$RUST_CLIENT_PATH"
CHANGES=\$(git diff --name-only origin/mainline... -- "\$RUST_CLIENT_PATH" 2>/dev/null)

if [[ -n "\$CHANGES" ]]; then
    echo "Rust client changes found that will be included in the CR"
    echo "Running pre-cr validation..."
    
    # Check if the pre-cr script exists and is executable
    if [[ -x "$PACKAGE_PRE_CR" ]]; then
        cd "\$RUST_CLIENT_PATH" 
        exec "$PACKAGE_PRE_CR" "\$@"
    else
        echo "Error: $PACKAGE_PRE_CR not found or not executable" >&2
        exit 1
    fi
else
    echo "No rust client changes will be included in the CR, skipping validation"
    exit 0
fi
EOF

# Make the wrapper script executable
chmod +x "$PRE_CR_GLOBAL_HOOK"

# Create the version flag file
touch "$PRE_CR_FLAG_FILE"

echo "Global pre-cr hook version $CURRENT_VERSION created successfully"
