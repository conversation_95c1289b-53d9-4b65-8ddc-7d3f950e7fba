#!/bin/bash
#
# test-helper
#

set -euo pipefail

display_usage() {
    cat <<EOF
OVERVIEW:
    Calculates coverage into a HTML report

USAGE:
    ./test-helper.sh --crate details

OPTIONS:
    --help           Display this message
    --workspace      Run all tests in the whole project not just in the crate
    --open-html      Opens the html report automatically when the reports are generated
    --reporters      Override the default reporters (currently HTML is only run by default)
    --no-report      Used if you want to just use this script as a test runner rather than outputting coverage info
    --full-clean     Cleans cb-coverage-target before running reporters (Ensures the outputted data is correct locally)
    --nextest        Runs tests using nextest instead of the default test runner (See https://nexte.st/docs/installation/pre-built-binaries/ for installation)
    --client_path    Specify the path to the AVLivingRoomRustClient
    --crate          The crate to run tests for
    --verbose        Display print output - use with nextest
    --name           Prefix of specific tests to run (e.g. ui::page_ui_sig::test::standard_hero::hero_should_be_expanded_by_default_on_initial_load_when_default_focus_experiment_is_active) - use with nextest
    --partition      Partition to run tests for (e.g. 1/4) - use with nextest
EOF
}

PID_FILE_PATH=~/.rust_native_rebuilder.pid
crate="0"
workspace="0"
open_html="0"
no_cd="0"
nextest="0"
no_report="0"
full_clean="0"
verbose="0"
name="0"
reporters="html"
partition="0"
client_path=$(dirname $(dirname "$0"))
while [[ $# -gt 0 ]]; do
  case $1 in
    --crate)
	    crate="$2"
	    shift
	    shift
	    ;;
	  --nextest)
	    nextest="1"
	    shift
	    ;;
	  --no-report)
	    no_report="1"
	    shift
	    ;;
	  --reporters)
	    reporters="$2"
	    shift
	    shift
	    ;;
	  --open-html)
	    open_html="1"
	    shift
	    ;;
	  --full-clean)
	    full_clean="1"
	    shift
	    ;;
    --workspace)
	    workspace="1"
	    shift
	    ;;
    --verbose)
	    verbose="1"
	    shift
	    ;;
	  --client_path)
	    client_path="$2"
	    shift
	    shift
	    ;;
    --name)
      name="$2"
      shift
      shift
      ;;
    --partition)
	    partition="$2"
	    shift
	    shift
	    ;;
    --help)
        help="help"
        shift # past argument
        ;;
    *)
        echo "Error: option '$1' is invalid."
        exit 1
        ;;
  esac
done

# If a crate is not specified force the --workspace command to on
if [ "$crate" = "0" ]; then
	workspace="1"
fi

target_path=$client_path

# Go to crate we want to test
if [ "$crate" != "0" ]; then
  target_path=$client_path/crates/$crate
fi

cd $target_path

# Clear previous coverage data
cargo brazil with-coverage clean

# For some reason in specific cases if cb-coverage-target is present coverage numbers are wrong even if profraw data is cleared
if [ "$full_clean" = "1" ]; then
  cb_target_path="$client_path/build/private/cb-coverage-target"
  if [ -d "$cb_target_path" ]; then
    echo -e "Cleaning cb-coverage-target"
    rm -rf $cb_target_path
  fi
fi

test_runner="test"
if [ "$nextest" = "1" ]; then
  test_runner="nextest run"
fi

test_command="RUSTFLAGS=\"-Cinstrument-coverage\" cargo brazil with-coverage $test_runner"

if [ "$no_report" = "1" ]; then
  test_command="cargo $test_runner"
fi

if [ "$workspace" = "1" ]; then
  test_command="$test_command --workspace"
fi

# Used to make sure details examples are run and kept in sync with source code
if [ "$workspace" = "1" ] || [ "$crate" = "details" ]; then
    test_command="$test_command --features details_example"
fi

if [ "$crate" = "linear" ]; then
    test_command="$test_command --features example_data"
fi

# Run only tests for a specific crate with nextest
if [ "$crate" != "0" ] && [ "$nextest" = "1" ]; then
    test_command="$test_command -E 'package($crate)'"
fi

# Run only specific tests with cargo test
if [ "$crate" != "0" ] && [ "$nextest" = "0" ]; then
    test_command="$test_command -p $crate"
fi

if [ "$verbose" = "1" ]; then
  test_command="$test_command --nocapture"
fi

if [ "$name" != "0" ]; then
  test_command="$test_command $name"
fi

if [ "$partition" != "0" ] && [ "$nextest" = "1" ]; then
  test_command="$test_command --partition count:$partition"
fi

echo -e "Running test command: $test_command"
eval "$test_command"

if [ "$no_report" = "0" ]; then
  echo -e "Finished running tests, generating report..."

  cargo brazil with-coverage report -- \
    --output-types $reporters \
    --branch \
    --ignore-not-existing \
    --excl-start '// GRCOV_STOP_COVERAGE|#\[cfg\(test\)\]' \
    --excl-stop  '// GRCOV_BEGIN_COVERAGE' \
    --excl-line  '// GRCOV_IGNORE_LINE|#\[derive\(|^\s*$|^\s*\/\/.*' \
    --ignore '/*' \
    --ignore '**/examples/**'

  # Open the HTML Coverage file
  if [ "$open_html" != "0" ]; then
    COVERAGE_PATH=$client_path/build/brazil-documentation/coverage/html/index.html
    echo -e "Opening coverage file at path: $COVERAGE_PATH"
    open $COVERAGE_PATH
  fi
fi
