[target.wasm32-wasip1]
linker = "rust-lld"
rustflags = [
    # Enable bulk memory feature to use memory opcodes in our Wasm binary
    "-Ctarget-feature=+bulk-memory",
    # Initialise the linear memory to expected amount to avoid many grow and
    # realloc operations during initialisation
    "-Clink-args=--initial-memory=6291456 --export=malloc --export=free --build-id=fast",
]

[target.wasm32-wasip1-threads]
linker = "rust-lld"
rustflags = ["-Clink-args=--max-memory=104857600 --initial-memory=6291456 --export=malloc --export=free --build-id=fast"]

[target.x86_64-apple-darwin]
linker = "rust-lld"
rustflags = ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"]

[target.aarch64-apple-darwin]
linker = "rust-lld"
rustflags = ["-C", "link-arg=-undefined", "-C", "link-arg=dynamic_lookup"]
