use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::app::rpc::RPCManager;
use serde::Deserialize;
use serde_json::Value;
use std::{
    cell::{OnceCell, RefCell},
    collections::HashMap,
    rc::Rc,
};

pub trait AcmConfigLoader {}

pub struct AcmConfigLoaderImpl {
    acm_config: OnceCell<AcmConfigImpl>,
}

#[derive_test_only(PartialEq, Debug)]
#[derive(Deserialize, Clone)]
enum AcmConfigUpdateType {
    Full,
    Partial,
}

#[derive(Deserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
#[allow(nonstandard_style, reason = "JS compability")]
struct AcmConfigUpdateArgs {
    updateType: AcmConfigUpdateType,
    data: Value,
    partialName: Option<String>,
}

impl AcmConfigLoaderImpl {
    pub fn new(rpc_manager: RPCManager) -> Rc<Self> {
        let loader = Rc::new(Self {
            acm_config: OnceCell::new(),
        });

        let self_clone = Rc::clone(&loader);
        rpc_manager.register_cross_app_function(
            "acm_store_update".into(),
            move |args: AcmConfigUpdateArgs| self_clone.receive_config_update(args),
        );

        loader
    }

    #[cfg(test)]
    pub fn new_without_rpc_registration() -> Rc<Self> {
        let loader = Rc::new(Self {
            acm_config: OnceCell::new(),
        });

        loader
    }

    fn receive_config_update(
        &self,
        update: AcmConfigUpdateArgs,
    ) -> Result<(), Box<dyn std::error::Error>> {
        match self.acm_config.get() {
            Some(acm_config) => {
                match update.updateType {
                    AcmConfigUpdateType::Full => {
                        log::info!("Full update received, replacing config");
                        acm_config.replace_config(update.data);
                    }
                    AcmConfigUpdateType::Partial => {
                        if let Some(partial_name) = update.partialName {
                            log::info!("Partial update received for {partial_name}");
                            acm_config.update_partial(partial_name, update.data);
                        } else {
                            return Err("Partial update without partial name".into());
                        }
                    }
                };
                Ok(())
            }
            None => match update.updateType {
                AcmConfigUpdateType::Full => {
                    let acm_config = match update.data {
                        Value::Object(dict) => {
                            log::info!("Initialising Rust ACM store");
                            AcmConfigImpl::new(dict.into_iter().collect())
                        }
                        _ => {
                            return Err("Full update received but data is not an object".into());
                        }
                    };

                    match self.acm_config.set(acm_config) {
                        Ok(_) => Ok(()),
                        Err(_) => Err(
                            "Once cell empty but acm config already set, this should not happen"
                                .into(),
                        ),
                    }
                }
                AcmConfigUpdateType::Partial => {
                    Err("Partial update received when ACM store hasn't been constructed yet".into())
                }
            },
        }
    }
}

impl AcmConfigLoader for AcmConfigLoaderImpl {}

pub trait AcmConfig {
    fn get_partial(&self, partial_name: String) -> Option<Value>;
}

pub struct AcmConfigImpl {
    data: RefCell<HashMap<String, Value>>,
}

impl AcmConfigImpl {
    pub fn new(initial_data: HashMap<String, Value>) -> Self {
        Self {
            data: RefCell::new(initial_data),
        }
    }

    pub(crate) fn replace_config(&self, new_value: Value) {
        if let Value::Object(obj) = new_value {
            {
                let mut data = self.data.borrow_mut();
                data.clear();
            }
            for (key, value) in obj {
                self.update_partial(key, value);
            }
        } else {
            log::error!("New config data is not an object, discarding update");
        }
    }

    pub(crate) fn update_partial(&self, partial_name: String, new_value: Value) {
        let mut data = self.data.borrow_mut();
        data.insert(partial_name, new_value);
    }
}

impl AcmConfig for AcmConfigImpl {
    fn get_partial(&self, partial_name: String) -> Option<Value> {
        let data = self.data.borrow();
        data.get(&partial_name).cloned()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_initial_full_config_update() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();
        let test_data = json!({
            "test_key": "test_value",
            "another_key": 42
        });

        // Act
        let update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: test_data.clone(),
            partialName: None,
        };
        let result = loader.receive_config_update(update);

        // Assert
        assert!(result.is_ok());
        let config = loader.acm_config.get().unwrap();
        assert_eq!(
            config.get_partial("test_key".to_string()),
            Some(json!("test_value"))
        );
        assert_eq!(
            config.get_partial("another_key".to_string()),
            Some(json!(42))
        );
    }

    #[test]
    fn test_partial_config_update() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();

        // First set initial full config
        let initial_data = json!({
            "section1": {"key1": "value1"},
            "section2": {"key2": "value2"}
        });

        let full_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: initial_data,
            partialName: None,
        };
        loader.receive_config_update(full_update).unwrap();

        // Act - perform partial update
        let partial_data = json!({"key1": "updated_value"});
        let partial_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Partial,
            data: partial_data.clone(),
            partialName: Some("section1".to_string()),
        };
        let result = loader.receive_config_update(partial_update);

        // Assert
        assert!(result.is_ok());
        let config = loader.acm_config.get().unwrap();
        assert_eq!(
            config.get_partial("section1".to_string()),
            Some(partial_data)
        );
        assert_eq!(
            config.get_partial("section2".to_string()),
            Some(json!({"key2": "value2"}))
        );
    }

    #[test]
    fn test_full_config_update_replaces_existing() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();

        // Set initial config
        let initial_data = json!({
            "section1": {"key1": "value1"},
            "section2": {"key2": "value2"}
        });

        let initial_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: initial_data,
            partialName: None,
        };
        loader.receive_config_update(initial_update).unwrap();

        // Act - perform full update with new data
        let new_data = json!({
            "section3": {"key3": "value3"},
            "section4": {"key4": "value4"}
        });

        let full_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: new_data.clone(),
            partialName: None,
        };
        let result = loader.receive_config_update(full_update);

        // Assert
        assert!(result.is_ok());
        let config = loader.acm_config.get().unwrap();

        // Check that old sections are gone
        assert_eq!(config.get_partial("section1".to_string()), None);
        assert_eq!(config.get_partial("section2".to_string()), None);

        // Check that new sections are present
        assert_eq!(
            config.get_partial("section3".to_string()),
            Some(json!({"key3": "value3"}))
        );
        assert_eq!(
            config.get_partial("section4".to_string()),
            Some(json!({"key4": "value4"}))
        );
    }

    #[test]
    fn test_partial_update_before_full_returns_error() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();

        // Act
        let partial_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Partial,
            data: json!({"key": "value"}),
            partialName: Some("section1".to_string()),
        };
        let result = loader.receive_config_update(partial_update);

        // Assert
        assert!(result.is_err());
        assert!(loader.acm_config.get().is_none());
    }

    #[test]
    fn test_partial_update_without_name() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();

        // First set initial full config
        let initial_data = json!({
            "section1": {"key1": "value1"}
        });

        let full_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: initial_data,
            partialName: None,
        };
        loader.receive_config_update(full_update).unwrap();

        // Act - attempt partial update without name
        let partial_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Partial,
            data: json!({"key": "value"}),
            partialName: None,
        };
        let result = loader.receive_config_update(partial_update);

        // Assert
        assert!(result.is_err()); // Partial update without name should return an error

        // Verify original config remains unchanged
        let config = loader.acm_config.get().unwrap();
        assert_eq!(
            config.get_partial("section1".to_string()),
            Some(json!({"key1": "value1"}))
        );
    }

    #[test]
    fn test_non_object_full_config() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();

        // Act - try to set a non-object JSON value
        let invalid_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: json!("just a string"), // Non-object JSON value
            partialName: None,
        };
        let result = loader.receive_config_update(invalid_update);

        // Assert
        assert!(result.is_err()); // Implementation should reject non-object data
        assert!(loader.acm_config.get().is_none()); // Config should not be initialized
    }

    #[test]
    fn test_multiple_partial_updates_to_same_section() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();

        // Set initial config
        let initial_data = json!({
            "section1": {"key1": "value1", "key2": "value2"}
        });

        let full_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: initial_data,
            partialName: None,
        };
        loader.receive_config_update(full_update).unwrap();

        // Act - perform multiple partial updates to the same section
        let first_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Partial,
            data: json!({"key1": "updated_value1"}),
            partialName: Some("section1".to_string()),
        };
        loader.receive_config_update(first_update).unwrap();

        let second_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Partial,
            data: json!({"key2": "updated_value2"}),
            partialName: Some("section1".to_string()),
        };
        let result = loader.receive_config_update(second_update);

        // Assert
        assert!(result.is_ok());
        let config = loader.acm_config.get().unwrap();

        // Verify that the last update completely replaced the previous content
        assert_eq!(
            config.get_partial("section1".to_string()),
            Some(json!({"key2": "updated_value2"}))
        );
    }

    #[test]
    fn test_get_nonexistent_partial() {
        // Arrange
        let loader = AcmConfigLoaderImpl::new_without_rpc_registration();

        // Set initial config with some data
        let initial_data = json!({
            "existing_section": {"key": "value"}
        });

        let full_update = AcmConfigUpdateArgs {
            updateType: AcmConfigUpdateType::Full,
            data: initial_data,
            partialName: None,
        };
        loader.receive_config_update(full_update).unwrap();

        // Act & Assert
        let config = loader.acm_config.get().unwrap();

        // Test getting non-existent section
        assert_eq!(config.get_partial("nonexistent_section".to_string()), None);

        // Verify existing section still accessible
        assert_eq!(
            config.get_partial("existing_section".to_string()),
            Some(json!({"key": "value"}))
        );
    }
}
