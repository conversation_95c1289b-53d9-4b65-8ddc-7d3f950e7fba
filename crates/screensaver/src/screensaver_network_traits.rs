use std::rc::Rc;

use ignx_compositron::{network::http::HttpMethod, prelude::metric};
use network::{common::lrc_edge_constants::LRCEdgeTransforms, NetworkClient, RequestError};
use network_parser::prelude::*;

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct ScreensaverResponse {
    pub container_list: Vec<Container>,
}

pub trait ScreensaverRequests {
    fn promotions_data<S, F>(&self, success_callback: S, failure_callback: F)
    where
        S: FnOnce(ScreensaverResponse) + 'static,
        F: FnOnce(RequestError) + 'static;
}

impl ScreensaverRequests for NetworkClient {
    fn promotions_data<S, F>(&self, success_callback: S, failure_callback: F)
    where
        S: FnOnce(ScreensaverResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
        let taps_roles = if let Some(taps_params_store) =
            use_context::<Rc<dyn TapsParameterStore>>(self.ctx.scope())
        {
            taps_params_store.get_collections_roles()
        } else {
            vec![]
        };

        let url = match promotions_screensaver_url_constructor(
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
            taps_roles,
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!("ScreensaverRequests#promotions_data: Cannot create the request url. Error: {:?}", e);
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let success_cb = move |v: ScreensaverResponse, _| {
            success_callback(v);
        };

        let failure_cb = move |e: RequestError, _| {
            failure_callback(e);
        };

        self.builder(url, HttpMethod::Get, "promotionsForProfile")
            .with_lrc_edge_headers(&LRCEdgeTransforms::PromotionsForScreensaver, None::<&str>)
            .with_parser(promotions_for_screensaver_parser_callback)
            .with_curl_latency(Rc::new(move |latency| {
                metric!(
                    "PageAction.NetworkLatency", latency.as_millis() as f64,
                    "pageType" => "Screensaver",
                    "actionName" => "GetPromotions",
                );
            }))
            .with_processing_latency(Rc::new(move |_, latency| {
                metric!(
                    "PageAction.ProcessingLatency", latency.as_millis() as f64,
                    "pageType" => "Screensaver",
                    "actionName" => "GetPromotions",
                );
            }))
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb))
            .execute();
    }
}

use crate::screensaver_network::{
    promotions_for_screensaver_parser_callback, promotions_screensaver_url_constructor,
};
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::containers::Container;
#[cfg(test)]
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::use_context;
#[cfg(test)]
use mockall::mock;
use network_parser_derive::NetworkParsed;
use taps_parameters::TapsParameterStore;

#[cfg(test)]
mock! {
    pub NetworkClient {
        pub fn new(ctx: &AppContext) -> Self;
    }
    impl ScreensaverRequests for NetworkClient {
        fn promotions_data<S, F>(&self, success_callback: S, failure_callback: F)
        where
            S: FnOnce(ScreensaverResponse) + 'static,
            F: FnOnce(RequestError) + 'static;
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use app_config::{AppConfigContext, MockAppConfig};
    use auth::{AuthContext, MockAuth};
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::network::http::MockHttpRequestContext;
    use ignx_compositron::prelude::provide_context;
    use router::{MockRouting, RoutingContext};

    fn mock_context(ctx: &AppContext) {
        let mock_routing_context = MockRouting::new();
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
        let mut mock_app_config = MockAppConfig::default();
        mock_app_config
            .expect_get_ux_locale()
            .returning(|| "en_GB".into());
        mock_app_config
            .expect_get_geo_location()
            .returning(|| Some("IE".into()));
        mock_app_config
            .expect_get_supported_locales()
            .returning(|| vec!["de_DE".into(), "en_US".into(), "fr_FR".into()]);
        mock_app_config
            .expect_get_base_url()
            .return_once(|| Some("https://base_url.com".to_string()));
        provide_context::<AppConfigContext>(ctx.scope(), Rc::new(mock_app_config));

        let auth = Rc::new(MockAuth::new_without_params(ctx.scope()));
        provide_context::<AuthContext>(ctx.scope, auth);
    }

    #[test]
    fn should_call_network_client_with_right_params() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);

            let network_client = NetworkClient::new(&ctx);
            network_client.promotions_data(|_| {}, |_| {});

            let expected_url = "https://base_url.com/lrcedge/getDataByJavaTransform/v1/lr/collections/screensaver?pageId=screensaver&pageType=home&widgetScheme=lrc%2Dscreensaver%2Dv1%2E1&featureScheme=react%2Dv5&decorationScheme=lr%2Ddecoration%2Dgen4%2Dv4&dynamicFeatures=AppleTVODEnabled%2CCLIENT%5FDECORATION%5FENABLE%5FDAAPI%2CDigitalBundlePvcPvc%2CLinearTitles%2CLiveTab%2CRustLRSupported%2CSupportsImageTextLinkTextInStandardHero&clientFeatures=DynamicBadging%2CRemaster%2CUseDynamicDatumParameters%2CUseV12TitleActionsView&isBrandingEnabled=true&regulatoryLabel=true&clientId=pv%2Dlrc%2Drust&transformStore=local&transformStage=prod&javaTransformTimeout=5000&timeZoneId=MockTimeZoneId&gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB";

            let props = MockHttpRequestContext::get();
            assert_eq!(props.method, HttpMethod::Get);
            assert_eq!(props.url, expected_url);
            let mut headers = props.headers.clone();
            headers.sort();
            let mut expected_headers = vec![
                "x-client-app: avlrc",
                "accept: application/json",
                "x-client-version: unknown-version",
                "content-type: application/json",
                "x-atv-page-type: ATVHome",
                "x-request-priority: CRITICAL",
            ];
            expected_headers.sort();
            assert_eq!(headers, expected_headers);
            assert_eq!(props.timeout, None);
        })
    }
}
