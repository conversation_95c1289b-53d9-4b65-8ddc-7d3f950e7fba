use proc_macro2::{Ident, Punct};
use quote::quote;
use syn::parse::{Parse, ParseStream};
use syn::{parse_macro_input, Expr};

struct IdentifierAndFn {
    ident: Ident,
    fn_pointer: Ident,
}

impl Parse for IdentifierAndFn {
    fn parse(input: ParseStream<'_>) -> syn::Result<Self> {
        let ident: Ident = input.parse()?;
        let _: Punct = input.parse()?;
        let fn_pointer: Ident = input.parse()?;
        Ok(IdentifierAndFn { ident, fn_pointer })
    }
}

struct PairOfExpr {
    expr: Expr,
    second_expr: Expr,
}

impl Parse for PairOfExpr {
    fn parse(input: ParseStream<'_>) -> syn::Result<Self> {
        let expr: Expr = input.parse()?;
        let _: Punct = input.parse()?;
        let second_expr: Expr = input.parse()?;
        Ok(PairOfExpr { expr, second_expr })
    }
}

struct ExprAndMessage {
    expr: Expr,
    message: Expr,
}

impl Parse for ExprAndMessage {
    fn parse(input: ParseStream<'_>) -> syn::Result<Self> {
        let expr: Expr = input.parse()?;
        let _: Punct = input.parse()?;
        let message: Expr = input.parse()?;
        Ok(ExprAndMessage { expr, message })
    }
}

struct ExprValAndMessage {
    expr: Expr,
    message: Expr,
    val: Expr,
}

impl Parse for ExprValAndMessage {
    fn parse(input: ParseStream<'_>) -> syn::Result<Self> {
        let expr: Expr = input.parse()?;
        let _: Punct = input.parse()?;
        let message: Expr = input.parse()?;
        let _: Punct = input.parse()?;
        let val: Expr = input.parse()?;
        Ok(ExprValAndMessage { expr, message, val })
    }
}

fn impl_use_details_context_or_fallback(input: IdentifierAndFn) -> proc_macro2::TokenStream {
    let ctx = input.ident;
    let pointer = input.fn_pointer;
    quote! {
        {
            let details_ctx_result = use_details_page_context(#ctx .scope());
            match details_ctx_result {
                None => {
                    return #pointer(#ctx)
                },
                Some(details_ctx) => details_ctx
            }
        }
    }
}

#[proc_macro]
pub fn use_details_context_or_fallback(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let ident = parse_macro_input!(input as IdentifierAndFn);
    proc_macro::TokenStream::from(impl_use_details_context_or_fallback(ident))
}

fn impl_get_some_or_return(input: Expr) -> proc_macro2::TokenStream {
    quote! {
        match #input {
            None => {
                return;
            },
            Some(val) => val
        }
    }
}

#[proc_macro]
pub fn get_some_or_return(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let expr = parse_macro_input!(input as Expr);
    proc_macro::TokenStream::from(impl_get_some_or_return(expr))
}

fn impl_get_some_or_return_value(value: PairOfExpr) -> proc_macro2::TokenStream {
    let optional_expr = value.expr;
    let second_expr = value.second_expr;
    quote! {
        match #optional_expr {
            None => {
                return #second_expr;
            },
            Some(val) => val
        }
    }
}

#[proc_macro]
pub fn get_some_or_return_value(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let pair_of_expr = parse_macro_input!(input as PairOfExpr);
    proc_macro::TokenStream::from(impl_get_some_or_return_value(pair_of_expr))
}

fn impl_get_some_or_return_with_error_message(value: ExprAndMessage) -> proc_macro2::TokenStream {
    let optional_expr = value.expr;
    let message = value.message;
    quote! {
        match #optional_expr {
            None => {
                DETAILS_LOGGER.error(#message);
                return;
            },
            Some(val) => val
        }
    }
}

/// Requires a `DETAILS_LOGGER` to be available in the calling scope in order to work and report the error
#[proc_macro]
pub fn get_some_or_return_with_error_message(
    input: proc_macro::TokenStream,
) -> proc_macro::TokenStream {
    let expr_and_message = parse_macro_input!(input as ExprAndMessage);
    proc_macro::TokenStream::from(impl_get_some_or_return_with_error_message(expr_and_message))
}

fn impl_get_some_or_return_with_val_and_error_message(
    value: ExprValAndMessage,
) -> proc_macro2::TokenStream {
    let optional_expr = value.expr;
    let message = value.message;
    let value = value.val;
    quote! {
        match #optional_expr {
            None => {
                DETAILS_LOGGER.error(#message);
                return #value;
            },
            Some(val) => val
        }
    }
}

/// Requires a `DETAILS_LOGGER` to be available in the calling scope in order to work and report the error
#[proc_macro]
pub fn get_some_or_return_with_val_and_error_message(
    input: proc_macro::TokenStream,
) -> proc_macro::TokenStream {
    let expr_val_and_message = parse_macro_input!(input as ExprValAndMessage);
    proc_macro::TokenStream::from(impl_get_some_or_return_with_val_and_error_message(
        expr_val_and_message,
    ))
}

fn impl_safe_rw_signal_get_untracked(value: PairOfExpr) -> proc_macro2::TokenStream {
    let sig = value.expr;
    let return_val = value.second_expr;

    let expr_string = quote!(#sig).to_string();
    quote!(
        {
            #[allow(clippy::disallowed_methods, reason = "ReadSignal::try_get_untracked() is safe")]
            {
                match #sig.read_only().try_get_untracked() {
                    None => {
                        info!("[safe_rw_signal_get_untracked] failed to retrieve signal {:?}", #expr_string);
                        #return_val
                    }
                    Some(val) => val
                }
            }
        }

    )
}

#[proc_macro]
pub fn safe_rw_signal_get_untracked(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let expr = parse_macro_input!(input as PairOfExpr);
    proc_macro::TokenStream::from(impl_safe_rw_signal_get_untracked(expr))
}

fn impl_safe_rw_signal_get_untracked_ret(value: PairOfExpr) -> proc_macro2::TokenStream {
    let sig_expr = value.expr;
    let return_expr = value.second_expr;

    let expr_string = quote!(#sig_expr).to_string();
    quote!(
        {
            #[allow(clippy::disallowed_methods, reason = "ReadSignal::try_get_untracked() is safe")]
            {
                match #sig_expr.read_only().try_get_untracked() {
                    None => {
                        info!("[safe_rw_signal_get_untracked_ret] failed to retrieve signal {:?}", #expr_string);
                        return #return_expr;
                    }
                    Some(val) => val
                }
            }
        }
    )
}

#[proc_macro]
pub fn safe_rw_signal_get_untracked_ret(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let expr = parse_macro_input!(input as PairOfExpr);
    proc_macro::TokenStream::from(impl_safe_rw_signal_get_untracked_ret(expr))
}

fn impl_try_set_untracked(value: PairOfExpr) -> proc_macro2::TokenStream {
    let sig_expr = value.expr;
    let value_to_set = value.second_expr;

    quote!(
        #sig_expr.try_update_untracked(|val| {
            *val = #value_to_set
        })
    )
}

#[proc_macro]
pub fn safe_try_set_untracked(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let expr = parse_macro_input!(input as PairOfExpr);
    proc_macro::TokenStream::from(impl_try_set_untracked(expr))
}

#[cfg(test)]
mod test {
    use crate::{
        impl_get_some_or_return, impl_get_some_or_return_value,
        impl_get_some_or_return_with_error_message,
        impl_get_some_or_return_with_val_and_error_message, impl_use_details_context_or_fallback,
    };
    use quote::quote;
    use syn::parse_quote;

    #[test]
    fn should_create_get_some_return_with_val_and_error_message() {
        let tokens = parse_quote! {
            item, "[error] failed", None
        };

        let expected_output = quote! {
            match item {
                None => {
                    DETAILS_LOGGER.error("[error] failed");
                    return None;
                },
                Some(val) => val
            }
        };

        let output_code = impl_get_some_or_return_with_val_and_error_message(tokens);

        assert_eq!(output_code.to_string(), expected_output.to_string());
    }

    #[test]
    fn should_create_correct_use_details_fallback_hook() {
        let tokens = parse_quote! {
            item, fallback_fn
        };

        let expected_output = quote! {
            {
                let details_ctx_result = use_details_page_context(item.scope());
                match details_ctx_result {
                    None => {
                        return fallback_fn(item)
                    },
                    Some(details_ctx) => details_ctx
                }
            }
        };

        let output_code = impl_use_details_context_or_fallback(tokens);

        assert_eq!(output_code.to_string(), expected_output.to_string());
    }

    #[test]
    fn should_get_some_or_return() {
        let tokens = parse_quote! {
            item
        };

        let expected_output = quote! {
            match item {
                None => { return; },
                Some(val) => val
            }
        };

        let output_code = impl_get_some_or_return(tokens);

        assert_eq!(output_code.to_string(), expected_output.to_string());
    }

    #[test]
    fn should_get_some_or_return_val() {
        let tokens = parse_quote! {
            item, "value"
        };

        let expected_output = quote! {
            match item {
                None => { return "value"; },
                Some(val) => val
            }
        };

        let output_code = impl_get_some_or_return_value(tokens);

        assert_eq!(output_code.to_string(), expected_output.to_string());
    }

    #[test]
    fn should_get_some_or_return_with_msg() {
        let tokens = parse_quote! {
            item, "error msg"
        };

        let expected_output = quote! {
            match item {
                None => {
                    DETAILS_LOGGER.error("error msg");
                    return;
                },
                Some(val) => val
            }
        };

        let output_code = impl_get_some_or_return_with_error_message(tokens);

        assert_eq!(output_code.to_string(), expected_output.to_string());
    }
}
