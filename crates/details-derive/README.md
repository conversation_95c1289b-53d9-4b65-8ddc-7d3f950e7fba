# Details Derive Helpers
### A package containing various helpers for the details page

## Context hook helpers
- These functions take in a AppContext along with a function to invoke and return the value of. This allows for you to return the ErrorOverlay at the start of a composer or even an empty composable if an error occurs when constructing it.

## Return value helpers
- Similar to above these write the code for a match statement on a optional value and then return in the current function if its None. (there is also a variant to return a static value)