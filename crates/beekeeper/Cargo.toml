[package]
name = "beekeeper"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[features]
debug_impl = []
mocks = []

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
synchronized-state-store.workspace = true
serde.workspace = true
serde_json.workspace = true
log.workspace = true
mockall.workspace = true
mockall_double.workspace = true
network.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
cfg-test-attr-derive.workspace = true
strum.workspace = true
strum_macros.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }
synchronized-state-store = { workspace = true, features = ["test-utils"] }
network = { workspace = true, features = ["test_utils", "mock_network"] }
rstest.workspace = true
router.workspace = true
app-config.workspace = true
auth.workspace = true

[lints]
workspace = true
