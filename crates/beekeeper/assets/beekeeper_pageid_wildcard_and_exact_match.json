{"timeToRefreshInSeconds": 600, "resiliencyMetadata": [{"pageType": "inputType", "pageId": {"primaryPageId": "*"}, "resiliencyStatus": "enabled", "action": {"type": "fallback", "targetPageType": "targetType3", "targetPageId": "targetId3", "cdnBaseUrl": "http://example3.com/"}}, {"pageType": "inputType", "pageId": {"primaryPageId": "inputId"}, "resiliencyStatus": "enabled", "action": {"type": "fallback", "targetPageType": "targetType4", "targetPageId": "targetId4", "cdnBaseUrl": "http://example4.com/"}}], "isTentpole": false}