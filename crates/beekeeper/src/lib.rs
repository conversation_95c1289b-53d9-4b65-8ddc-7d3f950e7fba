mod beekeeper;
mod config;
mod request_wrapper;
pub mod types;

use std::rc::Rc;

#[cfg(any(test, feature = "mocks"))]
pub use request_wrapper::mock_with_beekeeper_fallback;
pub use request_wrapper::with_beekeeper_fallback;

pub use beekeeper::Beekeeper;
pub type BeekeeperContext = Rc<Beekeeper>;

#[cfg(any(test, feature = "mocks"))]
pub use beekeeper::mocks::MockBeekeeper;
#[cfg(any(test, feature = "mocks"))]
pub type MockBeekeeperContext = Rc<MockBeekeeper>;
