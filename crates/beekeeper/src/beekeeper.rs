use ignx_compositron::{app::rpc::<PERSON><PERSON><PERSON><PERSON><PERSON>, reactive::Scope};
use mockall_double::double;

use crate::{
    config::model::ACMBeekeeperConfig,
    types::{BeekeeperFallbackPageParams, BeekeeperFallbackPageResponse, BeekeeperOverrides},
};

#[double]
use crate::config::BeekeeperConfig;
#[double]
use ignx_compositron::app::rpc::RPCManager;
#[double]
use synchronized_state_store::StateDispatcher;

pub struct Beekeeper {
    config: BeekeeperConfig,
    rpc_manager: RPCManager,
}

impl Beekeeper {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher, rpc_manager: RPCManager) -> Self {
        let config = BeekeeperConfig::new(scope, state_dispatcher);

        Self {
            config,
            rpc_manager,
        }
    }

    #[cfg(test)]
    pub(crate) fn new_injected(config: BeekeeperConfig, rpc_manager: RPCManager) -> Self {
        Self {
            config,
            rpc_manager,
        }
    }
    // GRCOV_BEGIN_COVERAGE

    pub fn get_beekeeper_overrides<'a>(
        &self,
        query_params: &'a [(String, String)],
    ) -> BeekeeperOverrides<'a> {
        let mut overrides = BeekeeperOverrides::default();

        for (key, value) in query_params {
            match key.as_str() {
                "dynamicFeatures" => overrides.dynamic_features = Some(value.as_str()),
                "decorationScheme" => overrides.decoration_scheme = Some(value.as_str()),
                "featureScheme" => overrides.feature_scheme = Some(value.as_str()),
                "widgetScheme" => overrides.widget_scheme = Some(value.as_str()),
                "presentationScheme" => overrides.presentation_scheme = Some(value.as_str()),
                _ => {}
            }
        }

        overrides
    }

    pub fn get_beekeeper_fallback_page(
        &self,
        params: BeekeeperFallbackPageParams,
        query_params: &[(String, String)],
        success_callback: Box<dyn FnOnce(Option<BeekeeperFallbackPageResponse>)>,
        failure_callback: Box<dyn FnOnce(RPCError)>,
    ) {
        let mut rpc_call = self
            .rpc_manager
            .call::<Option<BeekeeperFallbackPageResponse>>("getStorefrontFallbackPage");

        let overrides = self.get_beekeeper_overrides(query_params);

        match params {
            BeekeeperFallbackPageParams::Sport(sport_params) => {
                rpc_call = rpc_call
                    .arg("pageType", sport_params.pageType.into())
                    .arg("pageId", sport_params.pageId.into())
                    .arg("queryHash", sport_params.query_hash.into())
                    .arg("operationName", sport_params.operation_name.into());
            }
            BeekeeperFallbackPageParams::LrcEdge(lrc_params) => {
                rpc_call = rpc_call
                    .arg("pageType", lrc_params.pageType.into())
                    .arg("pageId", lrc_params.pageId.into());

                if let Some(page_section) = lrc_params.pageSection {
                    rpc_call = rpc_call.arg("pageSection", page_section.into());
                }

                if let Some(dynamic_features) = overrides.dynamic_features {
                    rpc_call = rpc_call.arg("dynamicFeatures", dynamic_features.into())
                }

                if let Some(decoration_scheme) = overrides.decoration_scheme {
                    rpc_call = rpc_call.arg("decorationScheme", decoration_scheme.into())
                }

                if let Some(feature_scheme) = overrides.feature_scheme {
                    rpc_call = rpc_call.arg("featureScheme", feature_scheme.into())
                }

                if let Some(presentation_scheme) = overrides.presentation_scheme {
                    rpc_call = rpc_call.arg("presentationScheme", presentation_scheme.into())
                }

                if let Some(widget_scheme) = overrides.widget_scheme {
                    rpc_call = rpc_call.arg("widgetScheme", widget_scheme.into())
                }
            }
        }

        rpc_call
            .success_callback(success_callback)
            .error_callback(failure_callback)
            .send();
    }

    pub fn is_tentpole(&self) -> bool {
        self.config.is_tentpole()
    }

    pub fn get_config(&self) -> Option<ACMBeekeeperConfig> {
        self.config.get_config()
    }

    pub fn is_circuit_breaker_enabled(&self, page_type: &str, page_id: &str) -> bool {
        self.config.is_circuit_breaker_enabled(page_type, page_id)
    }

    pub fn is_page_fallback_enabled(&self, page_type: &str, page_id: &str) -> bool {
        self.config.is_fallback_enabled(page_type, page_id)
    }
}

#[cfg(any(test, feature = "mocks"))]
pub mod mocks {
    use crate::types::{BeekeeperFallbackPageParams, BeekeeperFallbackPageResponse};

    use super::{ACMBeekeeperConfig, RPCManager};
    use ignx_compositron::{app::rpc::RPCError, reactive::Scope};
    use mockall::mock;
    use synchronized_state_store::MockStateDispatcher;

    mock! {
        pub Beekeeper {
            pub fn new(scope: Scope, state_dispatcher: &MockStateDispatcher, rpc_manager: RPCManager) -> Self;
            pub fn is_tentpole(&self) -> bool;
            pub fn get_config(&self) -> Option<ACMBeekeeperConfig>;
            pub fn get_beekeeper_fallback_page(
                &self,
                params: BeekeeperFallbackPageParams,
                query_params: &[(String, String)],
                success_callback: Box<dyn FnOnce(Option<BeekeeperFallbackPageResponse>)>,
                failure_callback: Box<dyn FnOnce(RPCError)>,
            );
            pub fn is_circuit_breaker_enabled(&self, page_type: &str, page_id: &str) -> bool;
            pub fn is_page_fallback_enabled(&self, page_type: &str, page_id: &str) -> bool;
        }
    }
}

#[cfg(test)]
mod tests {
    use std::{cell::RefCell, sync::Arc};

    use crate::types::{
        BeekeeperPageSection, LrcEdgeBeekeeperPageParams, SportEdgeBeekeeperPageParams,
    };

    use super::*;
    use ignx_compositron::app::{launch_only_scope, rpc::MockRPCCall};
    use mockall::predicate::eq;
    use rstest::rstest;
    use serde_json::Value;

    #[test]
    fn creates_beekeeper_struct() {
        launch_only_scope(|scope: Scope| {
            let mock_state_dispatcher = StateDispatcher::default();
            let config_context = BeekeeperConfig::new_context();
            config_context
                .expect()
                .once()
                .return_once_st(|_, _| BeekeeperConfig::default());
            let mock_rpc_manager = RPCManager::default();

            Beekeeper::new(scope, &mock_state_dispatcher, mock_rpc_manager);
        });
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn should_get_tentpole_info(#[case] is_tentpole: bool) {
        let mut mock_config = BeekeeperConfig::default();
        mock_config.expect_is_tentpole().return_const(is_tentpole);
        let mock_rpc_manager = RPCManager::default();

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);

        assert_eq!(beekeeper.is_tentpole(), is_tentpole);
    }

    #[rstest]
    #[case(Some(ACMBeekeeperConfig{ timeToRefreshInSeconds: 42, resiliencyMetadata: vec![], isTentpole: true}))]
    #[case(None)]
    fn should_get_config(#[case] config: Option<ACMBeekeeperConfig>) {
        let mut mock_config = BeekeeperConfig::default();
        let mock_rpc_manager = RPCManager::default();
        let config_clone = config.clone();
        mock_config
            .expect_get_config()
            .return_once_st(|| config_clone);

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);

        assert_eq!(beekeeper.get_config(), config);
    }

    #[test]
    fn should_get_beekeeper_fallback_page_from_js_via_rpc() {
        let mock_config = BeekeeperConfig::default();
        let mock_rpc_manager = mock_rpc_manager("home-test", "gti", None, None, None, false, false);

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);
        let success_callback = Box::new(|_: Option<BeekeeperFallbackPageResponse>| {});
        let error_callback = Box::new(|_: RPCError| {});

        beekeeper.get_beekeeper_fallback_page(
            BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                pageType: "home-test".into(),
                pageId: "gti".into(),
                pageSection: None,
            }),
            &[],
            success_callback,
            error_callback,
        )
    }

    #[test]
    fn should_get_beekeeper_fallback_page_from_js_via_rpc_with_page_section() {
        let mock_config = BeekeeperConfig::default();
        let mock_rpc_manager = mock_rpc_manager(
            "home-test",
            "gti",
            Some(BeekeeperPageSection::ATF),
            None,
            None,
            false,
            false,
        );

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);
        let success_callback = Box::new(move |_: Option<BeekeeperFallbackPageResponse>| {});
        let error_callback = Box::new(|_: RPCError| {});

        beekeeper.get_beekeeper_fallback_page(
            BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                pageType: "home-test".into(),
                pageId: "gti".into(),
                pageSection: Some(BeekeeperPageSection::ATF),
            }),
            &[],
            success_callback,
            error_callback,
        );
    }

    #[test]
    fn should_get_beekeeper_fallback_page_from_js_via_rpc_with_sport_params() {
        let mock_config = BeekeeperConfig::default();
        let mock_rpc_manager = mock_rpc_manager(
            "home-test",
            "gti",
            None,
            Option::from("test-hash"),
            Option::from("test-operation"),
            false,
            false,
        );

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);
        let success_callback = Box::new(move |_: Option<BeekeeperFallbackPageResponse>| {});
        let error_callback = Box::new(|_: RPCError| {});

        beekeeper.get_beekeeper_fallback_page(
            BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                pageType: "home-test".into(),
                pageId: "gti".into(),
                query_hash: "test-hash".into(),
                operation_name: "test-operation".into(),
            }),
            &[],
            success_callback,
            error_callback,
        );
    }

    #[test]
    fn should_get_beekeeper_fallback_page_from_js_with_overrides_from_query_params() {
        let mock_config = BeekeeperConfig::default();
        let mut mock_send_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        mock_send_call.expect_send().return_once(|| {});

        let mut mock_error_callback_call =
            MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        mock_error_callback_call
            .expect_error_callback()
            .return_once(move |cb| {
                cb(RPCError::TimeoutExceeded);
                mock_send_call
            });

        let mut mock_success_callback_call =
            MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        mock_success_callback_call
            .expect_success_callback()
            .return_once(move |cb| {
                cb(None);
                mock_error_callback_call
            });

        let mut widget_scheme_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        widget_scheme_call
            .expect_arg()
            .with(
                eq("widgetScheme"),
                eq(Value::String("TestWidgetScheme".into())),
            )
            .return_once(move |_, _| mock_success_callback_call);

        let mut presentation_scheme_call =
            MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        presentation_scheme_call
            .expect_arg()
            .with(
                eq("presentationScheme"),
                eq(Value::String("TestPresentationScheme".into())),
            )
            .return_once(move |_, _| widget_scheme_call);

        let mut feature_scheme_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        feature_scheme_call
            .expect_arg()
            .with(
                eq("featureScheme"),
                eq(Value::String("TestFeatureScheme".into())),
            )
            .return_once(move |_, _| presentation_scheme_call);

        let mut decoration_scheme_call =
            MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        decoration_scheme_call
            .expect_arg()
            .with(
                eq("decorationScheme"),
                eq(Value::String("TestDecorationScheme".into())),
            )
            .return_once(move |_, _| feature_scheme_call);

        let mut dynamic_features_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        dynamic_features_call
            .expect_arg()
            .with(
                eq("dynamicFeatures"),
                eq(Value::String("TestDynamicFeatures".into())),
            )
            .return_once(move |_, _| decoration_scheme_call);

        let mut page_id_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        page_id_call
            .expect_arg()
            .with(eq("pageId"), eq(Value::String("gti".into())))
            .return_once(move |_, _| dynamic_features_call);

        let mut page_type_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        page_type_call
            .expect_arg()
            .with(eq("pageType"), eq(Value::String("home-test".into())))
            .return_once(move |_, _| page_id_call);

        let mut mock_rpc_manager = RPCManager::default();
        mock_rpc_manager
            .expect_call()
            .with(eq("getStorefrontFallbackPage"))
            .once()
            .return_once(move |_| page_type_call);

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);
        let success_callback = Box::new(|_: Option<BeekeeperFallbackPageResponse>| {});
        let error_callback = Box::new(|_: RPCError| {});

        beekeeper.get_beekeeper_fallback_page(
            BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                pageType: "home-test".into(),
                pageId: "gti".into(),
                pageSection: None,
            }),
            &[
                ("dynamicFeatures".into(), "TestDynamicFeatures".into()),
                ("decorationScheme".into(), "TestDecorationScheme".into()),
                ("featureScheme".into(), "TestFeatureScheme".into()),
                ("presentationScheme".into(), "TestPresentationScheme".into()),
                ("widgetScheme".into(), "TestWidgetScheme".into()),
                ("this".into(), "should_not_do_anything".into()),
            ],
            success_callback,
            error_callback,
        )
    }

    #[test]
    fn should_get_beekeeper_fallback_page_from_js_and_call_passed_cb_on_success() {
        let mock_config = BeekeeperConfig::default();
        let mock_rpc_manager = mock_rpc_manager(
            "home-test",
            "gti",
            Some(BeekeeperPageSection::ATF),
            None,
            None,
            true,
            false,
        );

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);
        let success_flag = Arc::new(RefCell::new(false));
        let success_flag_clone = Arc::clone(&success_flag);
        let success_callback = Box::new(move |_: Option<BeekeeperFallbackPageResponse>| {
            *success_flag_clone.borrow_mut() = true;
        });
        let error_callback = Box::new(|_: RPCError| {});

        beekeeper.get_beekeeper_fallback_page(
            BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                pageType: "home-test".into(),
                pageId: "gti".into(),
                pageSection: Some(BeekeeperPageSection::ATF),
            }),
            &[],
            success_callback,
            error_callback,
        );

        assert!(*success_flag.borrow());
    }

    #[test]
    fn should_get_beekeeper_fallback_page_from_js_and_call_passed_cb_on_error() {
        let mock_config = BeekeeperConfig::default();
        let mock_rpc_manager = mock_rpc_manager(
            "home-test",
            "gti",
            Some(BeekeeperPageSection::ATF),
            None,
            None,
            false,
            true,
        );

        let beekeeper = Beekeeper::new_injected(mock_config, mock_rpc_manager);
        let success_callback = Box::new(|_: Option<BeekeeperFallbackPageResponse>| {});
        let error_flag = Arc::new(RefCell::new(false));
        let error_flag_clone = Arc::clone(&error_flag);
        let error_callback = Box::new(move |_: RPCError| {
            *error_flag_clone.borrow_mut() = true;
        });

        beekeeper.get_beekeeper_fallback_page(
            BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                pageType: "home-test".into(),
                pageId: "gti".into(),
                pageSection: Some(BeekeeperPageSection::ATF),
            }),
            &[],
            success_callback,
            error_callback,
        );

        assert!(*error_flag.borrow());
    }

    fn mock_rpc_manager(
        page_type: &str,
        page_id: &str,
        page_section: Option<BeekeeperPageSection>,
        query_hash: Option<&str>,
        operation_name: Option<&str>,
        should_call_success: bool,
        should_call_error: bool,
    ) -> RPCManager {
        let mut mock_send_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        mock_send_call.expect_send().return_once(|| {});

        let mut mock_error_callback_call =
            MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        mock_error_callback_call
            .expect_error_callback()
            .return_once(move |cb| {
                if should_call_error {
                    cb(RPCError::TimeoutExceeded);
                }
                mock_send_call
            });

        let mut mock_success_callback_call =
            MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        mock_success_callback_call
            .expect_success_callback()
            .return_once(move |cb| {
                if should_call_success {
                    cb(None)
                }
                mock_error_callback_call
            });

        let mut mock_page_id_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();

        if let (Some(hash), Some(op_name)) = (query_hash, operation_name) {
            let mut mock_operation_name_call =
                MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
            mock_operation_name_call
                .expect_arg()
                .with(eq("operationName"), eq(Value::String(op_name.into())))
                .return_once(|_, _| mock_success_callback_call);

            let mut mock_query_hash_call =
                MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
            mock_query_hash_call
                .expect_arg()
                .with(eq("queryHash"), eq(Value::String(hash.into())))
                .return_once(|_, _| mock_operation_name_call);

            mock_page_id_call
                .expect_arg()
                .with(eq("pageId"), eq(Value::String(page_id.into())))
                .return_once(|_, _| mock_query_hash_call);
        } else if let Some(section) = page_section {
            let mut mock_page_section_call =
                MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
            mock_page_section_call
                .expect_arg()
                .with(eq("pageSection"), eq::<Value>(section.into()))
                .return_once(|_, _| mock_success_callback_call);

            mock_page_id_call
                .expect_arg()
                .with(eq("pageId"), eq(Value::String(page_id.into())))
                .return_once(|_, _| mock_page_section_call);
        } else {
            mock_page_id_call
                .expect_arg()
                .with(eq("pageId"), eq(Value::String(page_id.into())))
                .return_once(|_, _| mock_success_callback_call);
        }

        let mut mock_page_type_call = MockRPCCall::<Option<BeekeeperFallbackPageResponse>>::new();
        mock_page_type_call
            .expect_arg()
            .with(eq("pageType"), eq(Value::String(page_type.into())))
            .return_once(|_, _| mock_page_id_call);

        let mut mock_rpc_manager = RPCManager::default();
        mock_rpc_manager
            .expect_call()
            .with(eq("getStorefrontFallbackPage"))
            .once()
            .return_once(|_| mock_page_type_call);

        mock_rpc_manager
    }
}
