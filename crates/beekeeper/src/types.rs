#![allow(nonstandard_style, reason = "Follows JSON pattern")]

use cfg_test_attr_derive::derive_test_only;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use strum::Display;

#[derive(Serialize, Deserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub enum BeekeeperFallbackPageParams {
    Sport(SportEdgeBeekeeperPageParams),
    LrcEdge(LrcEdgeBeekeeperPageParams),
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Clone)]
pub struct SportEdgeBeekeeperPageParams {
    pub pageType: String,
    pub pageId: String,
    pub query_hash: String,
    pub operation_name: String,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Clone)]
pub struct LrcEdgeBeekeeperPageParams {
    pub pageType: String,
    pub pageId: String,
    pub pageSection: Option<BeekeeperPageSection>,
}

#[derive(Display, Serialize, Deserialize, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Debug)]
pub enum BeekeeperPageSection {
    ATF,
    BTF,
}

impl From<BeekeeperPageSection> for Value {
    fn from(value: BeekeeperPageSection) -> Self {
        Value::String(value.to_string())
    }
}

#[derive(Default)]
pub struct BeekeeperOverrides<'a> {
    pub dynamic_features: Option<&'a str>,
    pub decoration_scheme: Option<&'a str>,
    pub feature_scheme: Option<&'a str>,
    pub widget_scheme: Option<&'a str>,
    pub presentation_scheme: Option<&'a str>,
}

#[derive(Serialize, Deserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct BeekeeperFallbackPageResponse {
    pub targetPage: ResiliencyPage,
    pub data: Value,
}

#[derive(Serialize, Deserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct ResiliencyPage {
    pub pageType: String,
    pub pageId: String,
}

#[derive(Serialize, Deserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct ResiliencyState {
    pub isResiliencyEnabled: bool,
    pub targetPage: Option<ResiliencyPage>,
}

#[derive(Serialize, Deserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct StorefrontFallbackPageFulfilledAction<T> {
    pub resiliencyState: Option<ResiliencyState>,
    pub response: Option<T>,
}
