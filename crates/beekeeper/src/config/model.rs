#![allow(nonstandard_style, reason = "Follows JSON pattern")]
use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;

#[derive(Serialize, NetworkParsed, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct ACMBeekeeperConfig {
    pub(crate) timeToRefreshInSeconds: u64,
    pub(crate) resiliencyMetadata: Vec<BeekeeperResiliencyMetadata>,
    pub(crate) isTentpole: bool,
}

#[derive(Serialize, NetworkParsed, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct BeekeeperResiliencyMetadata {
    pub(crate) pageType: String,
    pub(crate) pageId: PageId,
    pub(crate) resiliencyStatus: ResiliencyStatus,
    pub(crate) action: ResiliencyAction,
}

#[derive(Serialize, NetworkParsed, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct ResiliencyAction {
    pub(crate) cdnBaseUrl: String,
    pub(crate) targetPageId: String,
    pub(crate) targetPageType: String,
    #[network(rename = "type")]
    pub(crate) actionType: ResiliencyActionType,
}

#[derive(Serialize, NetworkParsed, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct PageId {
    pub(crate) primaryPageId: String,
    alias: Option<Vec<String>>,
}

#[allow(clippy::upper_case_acronyms)]
#[derive(Serialize, NetworkParsed, Clone, Copy, PartialEq)]
#[derive_test_only(Debug)]
pub enum ResiliencyStatus {
    #[network(rename = "enabled")]
    ENABLED,
    #[network(rename = "disabled")]
    DISABLED,
}

#[allow(clippy::upper_case_acronyms)]
#[derive(Serialize, NetworkParsed, Clone, Copy, PartialEq)]
#[derive_test_only(Debug)]
pub enum ResiliencyActionType {
    #[network(rename = "fallback")]
    FALLBACK,
    #[network(rename = "circuitbreaker")]
    CIRCUIT_BREAKER,
}
