#![allow(dead_code)]
// TODO: remove this once function are in use
// bug in clippy https://github.com/rust-lang/rust-clippy/issues/12824
#![allow(clippy::indexing_slicing)]

pub(crate) mod model;

use self::model::{
    ACMBeekeeperConfig, BeekeeperResiliencyMetadata, ResiliencyAction, ResiliencyActionType,
    ResiliencyStatus,
};
use ignx_compositron::reactive::Scope;
use mockall::automock;
use mockall_double::double;
use network_parser::core::NetworkOptional;

#[double]
use synchronized_state_store::{StateDispatcher, SynchronizedStateStore};

const BEEEKEEPER_STORE_ID: &str = "beekeeper";
const PAGE_ID_WILDCARD: &str = "*";

pub struct BeekeeperConfig {
    synchronized_state_store: SynchronizedStateStore<NetworkOptional<ACMBeekeeperConfig>>,
}

#[automock]
impl BeekeeperConfig {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher) -> Self {
        let synchronized_state_store = SynchronizedStateStore::new(
            scope,
            state_dispatcher,
            BEEEKEEPER_STORE_ID.into(),
            NetworkOptional::None,
        );

        Self {
            synchronized_state_store,
        }
    }

    /// returns the [`ACMBeekeeperConfig`] from `SynchronizedStateStore` by cloning.
    ///
    /// # Warning
    /// This is an expensive operation. Be carefull while using it. If you need to access a value
    /// deep within the config, use `BeekeeperConfig::derive` function
    pub fn get_config(&self) -> Option<ACMBeekeeperConfig> {
        self.synchronized_state_store
            .derive(|store| store.to_owned().into())
    }

    /// Derives a value from internal [`SynchronizedStateStore`] that contains [`ACMBeekeeperConfig`].
    pub fn derive<T: 'static, F>(&self, func: F) -> T
    where
        F: FnOnce(&NetworkOptional<ACMBeekeeperConfig>) -> T + 'static,
    {
        self.synchronized_state_store.derive(func)
    }

    /// Return a bool to tell if the received ACM config is tentpole or not
    pub fn is_tentpole(&self) -> bool {
        self.synchronized_state_store.derive(|store| {
            store
                .as_option_ref()
                .is_some_and(|config| config.isTentpole)
        })
    }

    /// Parses internal [`ACMBeekeeperConfig`] for given `page_type` and `page_id` to check if the
    /// resiliency action is `CIRCUIT_BREAKER`
    pub fn is_circuit_breaker_enabled(&self, page_type: &str, page_id: &str) -> bool {
        self.is_page_enabled(page_type, page_id, ResiliencyActionType::CIRCUIT_BREAKER)
    }

    /// Parses internal [`ACMBeekeeperConfig`] for given `page_type` and `page_id` to check if the
    /// resiliency action is `FALLBACK`
    pub fn is_fallback_enabled(&self, page_type: &str, page_id: &str) -> bool {
        self.is_page_enabled(page_type, page_id, ResiliencyActionType::FALLBACK)
    }

    fn is_page_enabled(
        &self,
        page_type: &str,
        page_id: &str,
        action_type: ResiliencyActionType,
    ) -> bool {
        let Some(action) = self.get_page_action(page_type, page_id) else {
            return false;
        };

        action.actionType == action_type
    }

    fn get_page_action(&self, page_type: &str, page_id: &str) -> Option<ResiliencyAction> {
        let metadata =
            self.find_resiliency_metadata_match(page_type.to_string(), page_id.to_string())?;

        if metadata.resiliencyStatus != ResiliencyStatus::ENABLED {
            return None;
        }

        if metadata.action.targetPageId == PAGE_ID_WILDCARD {
            Some(ResiliencyAction {
                targetPageId: page_id.to_string(),
                ..metadata.action
            })
        } else {
            Some(metadata.action)
        }
    }

    fn find_resiliency_metadata_match(
        &self,
        page_type: String,
        page_id: String,
    ) -> Option<BeekeeperResiliencyMetadata> {
        // NOTE: derive is scoped as 'static so I have to use String rather than &str
        self.synchronized_state_store.derive(move |store| {
            store.as_option_ref().map(|config| {
                let mut result: Option<&BeekeeperResiliencyMetadata> = None;
                for metadata in config.resiliencyMetadata.iter() {
                    if metadata.pageType != page_type {
                        continue;
                    }

                    if metadata.pageId.primaryPageId == page_id {
                        result = Some(metadata);
                        break;
                    }

                    if metadata.pageId.primaryPageId == PAGE_ID_WILDCARD {
                        result = Some(metadata);
                    }
                }
                result.map(|metadata| metadata.to_owned())
            })?
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_only_scope;
    use network_parser::core::network_parse_from_str;
    use rstest::*;

    use synchronized_state_store::{
        test_utils::mock_synchronized_state_store_new, MockStateDispatcher,
        MockSynchronizedStateStore,
    };

    #[test]
    fn should_create_beekeeper_config() {
        launch_only_scope(|scope: Scope| {
            let mock_state_dispatcher = MockStateDispatcher::default();
            let (_guard, _context, store_info) =
                mock_synchronized_state_store_new::<NetworkOptional<ACMBeekeeperConfig>>();
            BeekeeperConfig::new(scope, &mock_state_dispatcher);
            let store_data = store_info.read().expect("Able to read store info");
            assert_eq!(store_data.id, BEEEKEEPER_STORE_ID);
            // mock_synchronized_state_store_new wraps the initial data with `Some` so I have to do
            // this.
            assert_eq!(store_data.state, Some(NetworkOptional::None))
        });
    }

    #[rstest]
    #[case(include_str!("../../assets/beekeeper_config.json"))]
    #[case(include_str!("../../assets/beekeeper_pageid_wildcard.json"))]
    #[case(include_str!("../../assets/beekeeper_pageid_wildcard_and_exact_match.json"))]
    #[case(include_str!("../../assets/beekeeper_targetpageid_wildcard.json"))]
    fn should_parse_acm_beekeeper_config(#[case] config_str: &str) {
        if let Err(e) = network_parse_from_str::<ACMBeekeeperConfig>(config_str) {
            core::panic!("parse failed with error: {:?}", e)
        }
    }

    #[test]
    fn should_get_config() {
        let Ok(config) = network_parse_from_str::<ACMBeekeeperConfig>(include_str!(
            "../../assets/beekeeper_config.json"
        )) else {
            core::panic!("parse failed");
        };

        let config_clone = config.clone();
        let mut mock_sync_state_store = MockSynchronizedStateStore::default();
        mock_sync_state_store
            .expect_derive::<Option<ACMBeekeeperConfig>>()
            .once()
            .return_once(move |f| f(&NetworkOptional::Some(config)));

        let beekeeper_config = BeekeeperConfig {
            synchronized_state_store: mock_sync_state_store,
        };

        let retrieved_config = beekeeper_config.get_config();
        assert_eq!(retrieved_config, Some(config_clone));
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn should_tell_if_config_is_tentpole(#[case] tentpole: bool) {
        let mut mock_sync_state_store = MockSynchronizedStateStore::default();

        let state = NetworkOptional::Some(ACMBeekeeperConfig {
            timeToRefreshInSeconds: 42_000,
            resiliencyMetadata: vec![],
            isTentpole: tentpole,
        });

        mock_sync_state_store
            .expect_derive::<bool>()
            .once()
            .return_once(move |f| f(&state));

        let config = BeekeeperConfig {
            synchronized_state_store: mock_sync_state_store,
        };

        assert_eq!(config.is_tentpole(), tentpole)
    }

    #[rstest]
    #[case(include_str!("../../assets/beekeeper_config.json"), "inputType", "inputId", true)]
    #[case(include_str!("../../assets/beekeeper_config.json"), "inputTypeNotThere", "inputId", false)]
    #[case(include_str!("../../assets/beekeeper_config.json"), "inputType", "inputIdNotWildcard", false)]
    #[case(include_str!("../../assets/beekeeper_pageid_wildcard.json"), "inputType", "hasWildcard", true)]
    #[case(include_str!("../../assets/beekeeper_pageid_wildcard.json"), "inputTypeNotThere", "hasWildcard", false)]
    #[case(include_str!("../../assets/beekeeper_config_no_action.json"), "inputType", "inputId", false)]
    fn should_find_correct_metadata(
        #[case] config_str: &str,
        #[case] page_type: &'static str,
        #[case] page_id: &'static str,
        #[case] is_some: bool,
    ) {
        let Ok(config) = network_parse_from_str::<ACMBeekeeperConfig>(config_str) else {
            core::panic!("parse failed");
        };

        let mut mock_sync_state_store = MockSynchronizedStateStore::default();
        mock_sync_state_store
            .expect_derive::<Option<BeekeeperResiliencyMetadata>>()
            .once()
            .return_once(move |f| f(&NetworkOptional::Some(config)));

        let config = BeekeeperConfig {
            synchronized_state_store: mock_sync_state_store,
        };

        let metadata =
            config.find_resiliency_metadata_match(page_type.to_string(), page_id.to_string());
        assert_eq!(metadata.is_some(), is_some);
    }

    #[test]
    fn should_find_correct_metadata_when_multiple_matches() {
        let config_str =
            include_str!("../../assets/beekeeper_pageid_wildcard_and_exact_match.json");
        let Ok(config) = network_parse_from_str::<ACMBeekeeperConfig>(config_str) else {
            core::panic!("parse failed");
        };

        let mut mock_sync_state_store = MockSynchronizedStateStore::default();
        mock_sync_state_store
            .expect_derive::<Option<BeekeeperResiliencyMetadata>>()
            .once()
            .return_once(move |f| f(&NetworkOptional::Some(config)));

        let config = BeekeeperConfig {
            synchronized_state_store: mock_sync_state_store,
        };

        let metadata = config
            .find_resiliency_metadata_match("inputType".to_string(), "inputId".to_string())
            .unwrap();
        assert_eq!(metadata.action.targetPageId, "targetId4");
        assert_eq!(metadata.action.targetPageType, "targetType4");
    }

    #[test]
    fn should_derive_from_acm_config() {
        let config_str =
            include_str!("../../assets/beekeeper_pageid_wildcard_and_exact_match.json");
        let Ok(config) = network_parse_from_str::<ACMBeekeeperConfig>(config_str) else {
            core::panic!("parse failed");
        };

        let mut mock_sync_state_store = MockSynchronizedStateStore::default();
        mock_sync_state_store
            .expect_derive::<Option<Option<String>>>()
            .once()
            .return_once(move |f| f(&NetworkOptional::Some(config)));

        let config = BeekeeperConfig {
            synchronized_state_store: mock_sync_state_store,
        };

        let first_metadata_page_type =
            config.derive(|config: &NetworkOptional<ACMBeekeeperConfig>| {
                config.as_option_ref().map(|store| {
                    store
                        .resiliencyMetadata
                        .first()
                        .map(|metadata| metadata.pageType.clone())
                })
            });

        assert_eq!(first_metadata_page_type.unwrap().unwrap(), "inputType");
    }

    #[rstest]
    #[case(include_str!("../../assets/beekeeper_config_circuit_breaker.json"), true)]
    #[case(include_str!("../../assets/beekeeper_config_fallback.json"), false)]
    fn should_calculate_circuit_breaker_correctly(
        #[case] config_str: &str,
        #[case] is_circuit_breaker_enabled: bool,
    ) {
        let Ok(config) = network_parse_from_str::<ACMBeekeeperConfig>(config_str) else {
            core::panic!("parse failed");
        };

        let mut mock_sync_state_store = MockSynchronizedStateStore::default();
        mock_sync_state_store
            .expect_derive::<Option<BeekeeperResiliencyMetadata>>()
            .once()
            .return_once(move |f| f(&NetworkOptional::Some(config)));

        let config = BeekeeperConfig {
            synchronized_state_store: mock_sync_state_store,
        };

        let enabled = config.is_circuit_breaker_enabled("inputType", "inputId");

        assert_eq!(enabled, is_circuit_breaker_enabled);
    }

    #[rstest]
    #[case(include_str!("../../assets/beekeeper_config_circuit_breaker.json"), false)]
    #[case(include_str!("../../assets/beekeeper_config_fallback.json"), true)]
    fn should_calculate_fallback_enabled_correctly(
        #[case] config_str: &str,
        #[case] is_circuit_breaker_enabled: bool,
    ) {
        let Ok(config) = network_parse_from_str::<ACMBeekeeperConfig>(config_str) else {
            core::panic!("parse failed");
        };

        let mut mock_sync_state_store = MockSynchronizedStateStore::default();
        mock_sync_state_store
            .expect_derive::<Option<BeekeeperResiliencyMetadata>>()
            .once()
            .return_once(move |f| f(&NetworkOptional::Some(config)));

        let config = BeekeeperConfig {
            synchronized_state_store: mock_sync_state_store,
        };

        let enabled = config.is_fallback_enabled("inputType", "inputId");

        assert_eq!(enabled, is_circuit_breaker_enabled);
    }
}
