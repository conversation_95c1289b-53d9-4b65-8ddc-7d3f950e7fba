use std::{cell::Cell, rc::Rc};

use ignx_compositron::app::rpc::RPCError;
use network::request::builder::RequestBuilder;
use network_parser::{core::ParserError, wrappers::parse_with_try_from};
use serde_json::Value;

use crate::{
    config::model::ResiliencyActionType,
    request_wrapper::metrics::{report_page_invalid, report_page_unavailable},
    types::{
        BeekeeperFallbackPageParams, BeekeeperFallbackPageResponse, BeekeeperPageSection,
        ResiliencyPage, ResiliencyState, StorefrontFallbackPageFulfilledAction,
    },
};
use mockall_double::double;

use super::{
    metrics::{
        report_page_displayed, report_redirection_failure, report_redirection_success,
        report_redirection_triggered,
    },
    FallbackPageValidatorFn,
};
use crate::types::LrcEdgeBeekeeperPageParams;
#[double]
use crate::BeekeeperContext;

pub(crate) type RequestCell<T> = Rc<Cell<Option<RequestBuilder<T>>>>;

pub(crate) fn process_redirect_fallback_page<T, R>(
    beekeeper: BeekeeperContext,
    page_params: BeekeeperFallbackPageParams,
    query_params: &[(String, String)],
    redirect: ResiliencyPage,
    fallback_page_validator: Option<FallbackPageValidatorFn>,
    request_cell: Option<RequestCell<T>>,
    resiliency_action_cell: Rc<Cell<Option<R>>>,
) where
    T: Clone + for<'a> TryFrom<&'a Value, Error = ParserError> + 'static,
    R: FnOnce(StorefrontFallbackPageFulfilledAction<T>) + 'static,
{
    let (page_type, page_id) = match &page_params {
        BeekeeperFallbackPageParams::Sport(params) => {
            (params.pageType.clone(), params.pageId.clone())
        }
        BeekeeperFallbackPageParams::LrcEdge(params) => {
            (params.pageType.clone(), params.pageId.clone())
        }
    };

    let action_type =
        match beekeeper.is_circuit_breaker_enabled(&redirect.pageType, &redirect.pageId) {
            true => ResiliencyActionType::CIRCUIT_BREAKER,
            false => ResiliencyActionType::FALLBACK,
        };
    let is_tentpole = beekeeper.is_tentpole();

    let success_callback = {
        let redirect_clone = redirect.clone();
        let page_type_clone = page_type.clone();
        let page_id_clone = page_id.clone();
        let request_cell_clone = request_cell.clone();
        let request_recovery_failure_callback_cell = Rc::new(Cell::new(None::<Box<dyn FnOnce()>>));

        Box::new(move |r: Option<BeekeeperFallbackPageResponse>| {
            // If there is no response from beekeeper, we continue to request prod.
            let Some(response) = r else {
                log::warn!("[beekeeper#process_redirect_fallback_page] Beekeeper response is None for redirected pageType: {:?}, pageId: {:?}", redirect_clone.pageType, redirect_clone.pageId);
                report_redirection_failure(
                    &redirect_clone.pageType,
                    &redirect_clone.pageId,
                    &page_type_clone,
                    &page_id_clone,
                    action_type,
                    is_tentpole,
                );

                return execute_prod_request_or_fail(
                    request_cell_clone,
                    request_recovery_failure_callback_cell,
                    &page_type_clone,
                    &page_id_clone,
                    "process_redirect_fallback_page",
                );
            };

            if fallback_page_validator.is_some_and(|validator| !validator(&response.data)) {
                report_redirection_failure(
                    &redirect_clone.pageType,
                    &redirect_clone.pageId,
                    &page_type_clone,
                    &page_id_clone,
                    action_type,
                    is_tentpole,
                );
                log::error!("[beekeeper#process_redirect_fallback_page] page validator failed to validate received page for pageType:{}, pageId:{}.", redirect_clone.pageType, redirect_clone.pageId);

                return execute_prod_request_or_fail(
                    request_cell_clone,
                    request_recovery_failure_callback_cell,
                    &page_type_clone,
                    &page_id_clone,
                    "process_redirect_fallback_page__invalid",
                );
            }

            let Some(callback) = resiliency_action_cell.take() else {
                log::error!("(FATAL): [beekeeper#process_redirect_fallback_page] cannot take resiliency_action while returning fulfilled fallback page. RPC call to get CDN fallback page succeeded but resiliency action callback weren't available to call.");
                return execute_prod_request_or_fail(
                    request_cell_clone,
                    request_recovery_failure_callback_cell,
                    &page_type_clone,
                    &page_id_clone,
                    "process_redirect_fallback_page__no_resiliency_action",
                );
            };

            let response = match parse_with_try_from(&response.data) {
                Ok(r) => r,
                Err(e) => {
                    log::error!("(FATAL): [beekeeper#process_redirect_fallback_page] Cannot parse received Value to requested type T. Error: {:?}", e);
                    return execute_prod_request_or_fail(
                        request_cell_clone,
                        request_recovery_failure_callback_cell,
                        &page_type_clone,
                        &page_id_clone,
                        "process_redirect_fallback_page__parser_error",
                    );
                }
            };
            report_redirection_success(
                &redirect_clone.pageType,
                &redirect_clone.pageId,
                &page_type_clone,
                &page_id_clone,
                action_type,
                is_tentpole,
            );
            log::warn!("[beekeeper#process_redirect_fallback_page] Redirect to pageType:{} pageId:{} successful.", redirect_clone.pageType, redirect_clone.pageId);

            callback(StorefrontFallbackPageFulfilledAction {
                resiliencyState: Some(ResiliencyState {
                    isResiliencyEnabled: true,
                    targetPage: None,
                }),
                response: Some(response),
            });
        })
    };

    let failure_callback = {
        let request_recovery_failure_callback_cell = Rc::new(Cell::new(None::<Box<dyn FnOnce()>>));
        let redirect_clone = redirect.clone();
        Box::new(move |err: RPCError| {
            log::error!("[beekeeper#process_redirect_fallback_page] RPC call failed while requesting redirect fallback page. targetPageType: {:?}, targetPageId: {:?}. RPCError: {:?}.", redirect_clone.pageType, redirect_clone.pageId, err);
            report_redirection_failure(
                &redirect_clone.pageType,
                &redirect_clone.pageId,
                &page_type,
                &page_id,
                action_type,
                is_tentpole,
            );

            execute_prod_request_or_fail(
                request_cell,
                request_recovery_failure_callback_cell,
                &page_type,
                &page_id,
                "process_redirect_fallback_page__rpc_failed",
            )
        })
    };

    // redirect fallback page(homepage/details) only happened in LRCEdge
    beekeeper.get_beekeeper_fallback_page(
        BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
            pageType: redirect.pageType,
            pageId: redirect.pageId,
            pageSection: None,
        }),
        query_params,
        success_callback,
        failure_callback,
    );
}

pub(crate) fn retrieve_and_validate_target_page<T, F, R>(
    beekeeper: BeekeeperContext,
    page_params: BeekeeperFallbackPageParams,
    query_params: &[(String, String)],
    fallback_page_validator: Option<FallbackPageValidatorFn>,
    request_cell: Option<RequestCell<T>>,
    resiliency_action_cell: Rc<Cell<Option<R>>>,
    request_recovery_failure_callback: Option<Box<F>>,
    error_string: Option<String>,
) where
    T: Clone + for<'a> TryFrom<&'a Value, Error = ParserError> + 'static,
    F: FnOnce() + 'static + ?Sized,
    R: FnOnce(StorefrontFallbackPageFulfilledAction<T>) + 'static,
{
    let (page_type, page_id, page_section) = match &page_params {
        BeekeeperFallbackPageParams::Sport(sport_params) => (
            sport_params.pageType.clone(),
            sport_params.pageId.clone(),
            None,
        ),
        BeekeeperFallbackPageParams::LrcEdge(lrc_params) => (
            lrc_params.pageType.clone(),
            lrc_params.pageId.clone(),
            lrc_params.pageSection,
        ),
    };

    let action_type = match beekeeper.is_circuit_breaker_enabled(&page_type, &page_id) {
        true => ResiliencyActionType::CIRCUIT_BREAKER,
        false => ResiliencyActionType::FALLBACK,
    };
    let is_tentpole = beekeeper.is_tentpole();
    let request_recovery_failure_callback_cell =
        Rc::new(Cell::new(request_recovery_failure_callback));

    let success_callback = {
        let request_cell_clone = request_cell.clone();
        let resiliency_action_cell_clone = Rc::clone(&resiliency_action_cell);
        let request_recovery_failure_callback_cell_clone =
            Rc::clone(&request_recovery_failure_callback_cell);
        let page_type_clone = page_type.clone();
        let page_id_clone = page_id.clone();
        Box::new(move |response: Option<BeekeeperFallbackPageResponse>| {
            let Some(BeekeeperFallbackPageResponse { targetPage, data }) = response else {
                log::error!("[beekeeper#retrieve_and_validate_target_page] fallback page is unavailable for pageType: {:?}, pageId: {:?}.", page_type_clone, page_id_clone);
                report_page_unavailable(
                    &page_type_clone,
                    &page_id_clone,
                    page_section,
                    action_type,
                    is_tentpole,
                    error_string,
                );

                return execute_prod_request_or_fail(
                    request_cell_clone,
                    request_recovery_failure_callback_cell_clone,
                    &page_type_clone,
                    &page_id_clone,
                    "retrieve_and_validate_target_page__unavailable",
                );
            };

            // If target page is not the same as current page, return the target page to trigger a redirection
            if targetPage.pageType != page_type_clone || targetPage.pageId != page_id_clone {
                // Return empty resiliencyState for BTF so that a redirection is not triggered twice for a single Details Page
                if page_section.is_some_and(|section| section == BeekeeperPageSection::BTF) {
                    let Some(callback) = resiliency_action_cell_clone.take() else {
                        log::error!("(FATAL): [beekeeper#retrieve_and_validate_target_page] Cannot take resiliency_action to resolve BTF request. Callback has already been called by another function.");
                        return;
                    };
                    callback(StorefrontFallbackPageFulfilledAction {
                        response: None,
                        resiliencyState: None,
                    });
                    return;
                }
                // Return ResiliencyAction to trigger redirection
                let Some(callback) = resiliency_action_cell_clone.take() else {
                    log::error!("(FATAL): [beekeeper#retrieve_and_validate_target_page] Cannot take success_callback to resolve ATF request. Callback has already been called by another function.");
                    return;
                };
                report_redirection_triggered(
                    &page_type_clone,
                    &page_id_clone,
                    page_section,
                    &targetPage.pageType,
                    &targetPage.pageId,
                    action_type,
                    is_tentpole,
                    error_string,
                );
                callback(StorefrontFallbackPageFulfilledAction {
                    response: None,
                    resiliencyState: Some(ResiliencyState {
                        targetPage: Some(targetPage),
                        // NOTE: JS leaves this empty which will default to undefined so I'll
                        // make it false to not diverge from js logic for the initial impl
                        isResiliencyEnabled: false,
                    }),
                });
                return;
            }

            if fallback_page_validator.is_some_and(|validator| !validator(&data)) {
                report_page_invalid(
                    &page_type_clone,
                    &page_id_clone,
                    page_section,
                    is_tentpole,
                    error_string,
                );
                log::warn!("[beekeeper#retrieve_and_validate_target_page] fallback_page_validator returned false. Fallback page is invalid.");
                return execute_prod_request_or_fail(
                    request_cell_clone,
                    request_recovery_failure_callback_cell_clone,
                    &page_type_clone,
                    &page_id_clone,
                    "retrieve_and_validate_target_page__invalid",
                );
            }

            let Some(callback) = resiliency_action_cell_clone.take() else {
                log::error!("(FATAL): [beekeeper#retrieve_and_validate_target_page] Cannot take resiliency_action to resolve fallback request. Callback has already been called by another function.");
                return;
            };

            let response = match parse_with_try_from(&data) {
                Ok(r) => r,
                Err(e) => {
                    log::error!("(FATAL): [beekeeper#retrieve_and_validate_target_page] Cannot parse received Value to requested type T. Error: {:?}", e);
                    return;
                }
            };
            // Valid BK4C data retrieved from CDN, returning to requesting page along with resiliencyState
            report_page_displayed(
                &page_type_clone,
                &page_id_clone,
                page_section,
                action_type,
                is_tentpole,
                error_string,
            );
            callback(StorefrontFallbackPageFulfilledAction {
                resiliencyState: Some(ResiliencyState {
                    isResiliencyEnabled: true,
                    targetPage: None,
                }),
                response: Some(response),
            })
        })
    };

    let failure_callback = {
        Box::new(move |err: RPCError| {
            log::error!("[beekeeper#retrieve_and_validate_target_page] RPC call failed while requesting fallback page. pageType: {:?}, pageId: {:?}, pageSection: {:?}. RPCError: {:?}.", page_type, page_id, page_section, err);
            report_page_unavailable(
                &page_type,
                &page_id,
                page_section,
                action_type,
                is_tentpole,
                Some(err.to_string()),
            );

            //TODO: MISSING_METRIC: new metric to report rpc errors
            execute_prod_request_or_fail(
                request_cell,
                request_recovery_failure_callback_cell,
                &page_type,
                &page_id,
                "retrieve_and_validate_target_page__rpc_failed",
            )
        })
    };

    beekeeper.get_beekeeper_fallback_page(
        page_params,
        query_params,
        success_callback,
        failure_callback,
    );
}

pub(crate) fn execute_prod_request_or_fail<T, F>(
    request_cell: Option<RequestCell<T>>,
    request_recovery_failure_callback_cell: Rc<Cell<Option<Box<F>>>>,
    page_type: &str,
    page_id: &str,
    log_prefix: &'static str,
) where
    T: Clone + for<'a> TryFrom<&'a Value, Error = ParserError> + 'static,
    F: FnOnce() + 'static + ?Sized,
{
    // If there is a request available, it means we still haven't requested the prod endpoint so we'll do it here
    if let Some(request_cell) = request_cell {
        log::warn!(
            "[beekeeper#{log_prefix}] Falling back to prod endpoint for pageType: {:?}, pageId: {:?}.",
            page_type,
            page_id
        );
        let Some(rq) = request_cell.take() else {
            // As request_cell is None, We assume it called either success or failure callbacks.
            log::error!("[beekeeper#{log_prefix}] Beekeeper protected request has already been sent. Cannot take the request cell.");
            return;
        };

        rq.execute()
    }

    // If no request is available this means we got a failure in prod request and falled back to beekeeper but it failed as well.
    // So we need to fail the whole thing to inform user.
    let Some(cb) = request_recovery_failure_callback_cell.take() else {
        log::error!("(FATAL): [beekeeper#{log_prefix}] Request weren't available and cannot take failure callback cell. Cannot call the http failure callback.");
        return;
    };

    cb();
}

#[cfg(test)]
mod tests {
    use super::*;
    use app_config::{AppConfigContext, MockAppConfig};
    use auth::{AuthContext, MockAuth};
    use ignx_compositron::{
        app::launch_only_app_context,
        network::http::{HttpMethod, MockHttpRequestContext},
        prelude::{provide_context, AppContext},
    };
    use network_parser_derive::NetworkParsed;
    use router::{MockRouting, RoutingContext};

    #[derive(Clone, NetworkParsed)]
    struct MockStruct {}

    fn mock_context(ctx: &AppContext) {
        let mock_routing_context = MockRouting::new();
        let auth = Rc::new(MockAuth::new_without_params(ctx.scope()));
        let app_config = Rc::new(MockAppConfig::default());
        provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing_context));
        provide_context::<AuthContext>(ctx.scope(), auth);
        provide_context::<AppConfigContext>(ctx.scope(), app_config);
    }

    mod execute_prod_request_or_fail {
        use network::{
            common::{DeviceProxyResponse, LRCEdgeResponse},
            NetworkClient, URL,
        };
        use std::cell::RefCell;

        use super::*;

        #[test]
        fn should_execute_given_request_if_its_available() {
            launch_only_app_context(move |ctx: AppContext| {
                mock_context(&ctx);
                let success_called = Rc::new(RefCell::new(false));
                let success_called_cl = Rc::clone(&success_called);
                let client = NetworkClient::new(&ctx);
                let request = client
                    .builder(URL::test_url(), HttpMethod::Get, "log_prefix")
                    .with_parser(|_, _, _, cb| {
                        cb(Ok(DeviceProxyResponse::LRCEdgeResponse(
                            LRCEdgeResponse::with_resource(MockStruct {}),
                        )))
                    })
                    .on_success(move |_, _| {
                        *success_called_cl.borrow_mut() = true;
                    });

                execute_prod_request_or_fail(
                    Some(Rc::new(Cell::new(Some(request)))),
                    Rc::new(Cell::new(Some(Box::new(|| {
                        log::error!("fail called");
                    })))),
                    "inputPage",
                    "inputId",
                    "log_prefix",
                );
                let props = MockHttpRequestContext::get();

                // this is the closes thing I found in the current network mocks to `execute`
                props.invoke_callback(Some("some-data".into()), 200);

                assert!(*success_called.borrow());
            });
        }

        #[test]
        fn should_execute_failure_callback_when_request_is_not_available() {
            let fail_called = Rc::new(RefCell::new(false));
            let fail_called_cl = Rc::clone(&fail_called);

            execute_prod_request_or_fail(
                None::<RequestCell<MockStruct>>,
                Rc::new(Cell::new(Some(Box::new(move || {
                    *fail_called_cl.borrow_mut() = true;
                })))),
                "inputPage",
                "inputId",
                "log_prefix",
            );

            assert!(*fail_called.borrow());
        }
    }

    mod retrieve_and_validate_target_page {
        use mockall::predicate::{always, eq};

        use super::*;
        use crate::types::SportEdgeBeekeeperPageParams;
        use crate::MockBeekeeper;

        #[test]
        fn should_make_rpc_call_to_react_with_correct_params() {
            let mut beekeeper_mock = MockBeekeeper::default();
            beekeeper_mock
                .expect_get_beekeeper_fallback_page()
                .with(
                    eq(BeekeeperFallbackPageParams::LrcEdge(
                        LrcEdgeBeekeeperPageParams {
                            pageType: "inputPage".into(),
                            pageId: "inputId".into(),
                            pageSection: None,
                        },
                    )),
                    always(),
                    always(),
                    always(),
                )
                .return_const(());
            beekeeper_mock
                .expect_is_circuit_breaker_enabled()
                .with(eq("inputPage"), eq("inputId"))
                .return_const(true);
            beekeeper_mock.expect_is_tentpole().return_const(false);
            retrieve_and_validate_target_page(
                Rc::new(beekeeper_mock),
                BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                    pageType: "inputPage".into(),
                    pageId: "inputId".into(),
                    pageSection: None,
                }),
                &[],
                Some(Rc::new(|_| true)),
                None::<RequestCell<MockStruct>>,
                Rc::new(Cell::new(Some(
                    move |_: StorefrontFallbackPageFulfilledAction<MockStruct>| {},
                ))),
                None::<Box<dyn FnOnce()>>,
                None,
            )
        }

        //TODO: extend test cases to cover rest of the logic

        #[test]
        fn should_make_rpc_call_to_react_with_correct_params_for_sports() {
            let mut beekeeper_mock = MockBeekeeper::default();
            beekeeper_mock
                .expect_get_beekeeper_fallback_page()
                .with(
                    eq(BeekeeperFallbackPageParams::Sport(
                        SportEdgeBeekeeperPageParams {
                            pageType: "inputPage".into(),
                            pageId: "inputId".into(),
                            query_hash: "testHash".into(),
                            operation_name: "testOperation".into(),
                        },
                    )),
                    always(),
                    always(),
                    always(),
                )
                .return_const(());
            beekeeper_mock
                .expect_is_circuit_breaker_enabled()
                .with(eq("inputPage"), eq("inputId"))
                .return_const(true);
            beekeeper_mock.expect_is_tentpole().return_const(false);

            retrieve_and_validate_target_page(
                Rc::new(beekeeper_mock),
                BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                    pageType: "inputPage".into(),
                    pageId: "inputId".into(),
                    query_hash: "testHash".into(),
                    operation_name: "testOperation".into(),
                }),
                &[],
                Some(Rc::new(|_| true)),
                None::<RequestCell<MockStruct>>,
                Rc::new(Cell::new(Some(
                    move |_: StorefrontFallbackPageFulfilledAction<MockStruct>| {},
                ))),
                None::<Box<dyn FnOnce()>>,
                None,
            )
        }
    }

    mod process_redirect_fallback_page {
        use mockall::predicate::{always, eq};

        use crate::MockBeekeeper;

        use super::*;

        #[test]
        fn should_make_rpc_call_to_react_with_correct_params() {
            let mut beekeeper_mock = MockBeekeeper::default();
            beekeeper_mock
                .expect_get_beekeeper_fallback_page()
                .with(
                    eq(BeekeeperFallbackPageParams::LrcEdge(
                        LrcEdgeBeekeeperPageParams {
                            pageType: "someInputPage".into(),
                            pageId: "somePageId".into(),
                            pageSection: None,
                        },
                    )),
                    always(),
                    always(),
                    always(),
                )
                .return_const(());
            beekeeper_mock
                .expect_is_circuit_breaker_enabled()
                .with(eq("someInputPage"), eq("somePageId"))
                .return_const(true);
            beekeeper_mock.expect_is_tentpole().return_const(false);
            process_redirect_fallback_page(
                Rc::new(beekeeper_mock),
                BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                    pageType: "inputPage".into(),
                    pageId: "inputId".into(),
                    pageSection: None,
                }),
                &[],
                ResiliencyPage {
                    pageType: "someInputPage".into(),
                    pageId: "somePageId".into(),
                },
                Some(Rc::new(|_| true)),
                None::<RequestCell<MockStruct>>,
                Rc::new(Cell::new(Some(
                    move |_: StorefrontFallbackPageFulfilledAction<MockStruct>| {},
                ))),
            )
        }

        //TODO: extend test cases to cover rest of the logic
    }
}
