use ignx_compositron::{log::values, metrics::metric};
use serde_json::json;

use crate::{config::model::ResiliencyActionType, types::BeekeeperPageSection};

const REPORT_ID: &'static str = "StorefrontFallbackEvent";
const TAGS: [&str; 1] = ["StorefrontFallback"];

fn get_mode(action: ResiliencyActionType) -> &'static str {
    match action {
        ResiliencyActionType::CIRCUIT_BREAKER => "CircuitBreaker",
        ResiliencyActionType::FALLBACK => "Fallback",
    }
}

fn get_experience_type(is_tentpole: bool, page_type: Option<&str>) -> &'static str {
    let is_detail_page = match page_type {
        Some(page) => page == "detail",
        None => false,
    };

    if is_tentpole && !is_detail_page {
        "Tentpole"
    } else {
        "AlwaysOn"
    }
}

fn get_page_type_for_page(page_type: &str) -> &'static str {
    match page_type {
        "detail" => "DetailsPage",
        _ => "CollectionsPage",
    }
}

pub(crate) fn report_redirection_failure(
    from_page_type: &str,
    from_page_id: &str,
    to_page_type: &str,
    to_page_id: &str,
    action_type: ResiliencyActionType,
    is_tentpole: bool,
) {
    let event = json!({
        "eventType": "BK4C.RedirectionFailure",
        "fromPageType": from_page_type,
        "fromPageId": from_page_id,
        "toPageType": to_page_type,
        "toPageId": to_page_id,
        "mode": get_mode(action_type),
        "experienceType": get_experience_type(is_tentpole, None)
    });

    log::error!(reportId = REPORT_ID, tags = values![TAGS]; "{event}");

    // TODO: integreate critical events store
    metric!("StorefrontFallback.PageRedirection", 0,
        "mode" => get_mode(action_type),
        "experienceType" => get_experience_type(is_tentpole, None),
        "fromPage" => get_page_type_for_page(from_page_type),
        "isRemaster" => "true"
    )
}

pub(crate) fn report_redirection_success(
    from_page_type: &str,
    from_page_id: &str,
    to_page_type: &str,
    to_page_id: &str,
    action_type: ResiliencyActionType,
    is_tentpole: bool,
) {
    let event = json!({
        "eventType": "BK4C.RedirectionSuccess",
        "fromPageType": from_page_type,
        "fromPageId": from_page_id,
        "toPageType": to_page_type,
        "toPageId": to_page_id,
        "mode": get_mode(action_type),
        "experienceType": get_experience_type(is_tentpole, None)
    });

    // TODO: check out how we can report ReportContext and this is info log in js
    log::info!(reportId = REPORT_ID, tags = values![TAGS]; "{event}");

    // TODO: integreate critical events store
    metric!("StorefrontFallback.PageRedirection", 1,
        "mode" => get_mode(action_type),
        "experienceType" => get_experience_type(is_tentpole, None),
        "fromPage" => get_page_type_for_page(from_page_type),
        "isRemaster" => "true",
    )
}

pub(crate) fn report_page_unavailable(
    page_type: &str,
    page_id: &str,
    page_section: Option<BeekeeperPageSection>,
    action_type: ResiliencyActionType,
    is_tentpole: bool,
    fallback_error: Option<String>,
) {
    let event = json!({
        "eventType": "BK4C.PageUnavailable",
        "pageType": page_type,
        "pageId": page_id,
        "pageSection": page_section,
        "mode": get_mode(action_type),
        "experienceType": get_experience_type(is_tentpole, Some(page_type)),
        "fallbackError": fallback_error
    });

    log::warn!(reportId = REPORT_ID, tags = values![TAGS]; "{event}");

    if let Some(section) = page_section {
        metric!("StorefrontFallback.Displayed", 0,
            "mode" => get_mode(action_type),
            "experienceType" => get_experience_type(is_tentpole, Some(page_type)),
            "page" => get_page_type_for_page(page_type),
            "pageSection" => section,
            "isRemaster" => "true",
        )
    } else {
        metric!("StorefrontFallback.Displayed", 0,
            "mode" => get_mode(action_type),
            "experienceType" => get_experience_type(is_tentpole, Some(page_type)),
            "page" => get_page_type_for_page(page_type),
            "isRemaster" => "true",
        )
    }
}

pub(crate) fn report_redirection_triggered(
    from_page_type: &str,
    from_page_id: &str,
    from_page_section: Option<BeekeeperPageSection>,
    target_page_type: &str,
    target_page_id: &str,
    action_type: ResiliencyActionType,
    is_tentpole: bool,
    fallback_error: Option<String>,
) {
    let event = json!({
        "eventType": "BK4C.RedirectionTriggered",
        "fromPageType": from_page_type,
        "fromPageId": from_page_id,
        "fromPageSection": from_page_section,
        "toPageType": target_page_type,
        "toPageId": target_page_id,
        "mode": get_mode(action_type),
        "experienceType": get_experience_type(is_tentpole, None),
        "fallbackError": fallback_error
    });

    log::info!(reportId = REPORT_ID, tags = values![TAGS]; "{event}");
}

pub(crate) fn report_page_invalid(
    page_type: &str,
    page_id: &str,
    page_section: Option<BeekeeperPageSection>,
    is_tentpole: bool,
    fallback_error: Option<String>,
) {
    let event = json!({
        "eventType": "BK4C.PageInvalid",
        "pageType": page_type,
        "pageId": page_id,
        "pageSection": page_section,
        "experienceType": get_experience_type(is_tentpole, Some(page_type)),
        "fallbackError": fallback_error
    });

    log::error!(reportId = REPORT_ID, tags = values![TAGS]; "{event}");

    if let Some(section) = page_section {
        metric!("StorefrontFallback.PageInvalid", 1,
            "experienceType" => get_experience_type(is_tentpole, Some(page_type)),
            "page" => get_page_type_for_page(page_type),
            "pageSection" => section,
            "isRemaster" => "true",
        )
    } else {
        metric!("StorefrontFallback.PageInvalid", 1,
            "experienceType" => get_experience_type(is_tentpole, Some(page_type)),
            "page" => get_page_type_for_page(page_type),
            "isRemaster" => "true",
        )
    }
}

pub(crate) fn report_page_displayed(
    page_type: &str,
    page_id: &str,
    page_section: Option<BeekeeperPageSection>,
    action_type: ResiliencyActionType,
    is_tentpole: bool,
    fallback_error: Option<String>,
) {
    let event = json!({
        "eventType": "BK4C.PageDisplayed",
        "pageType": page_type,
        "pageId": page_id,
        "pageSection": page_section,
        "mode": get_mode(action_type),
        "experienceType": get_experience_type(is_tentpole, Some(page_type)),
        "fallbackError": fallback_error
    });

    log::info!(reportId = REPORT_ID, tags = values![TAGS]; "{event}");

    if let Some(section) = page_section {
        metric!("StorefrontFallback.Displayed", 1,
            "mode" =>  get_mode(action_type),
            "experienceType" => get_experience_type(is_tentpole, Some(page_type)),
            "page" => get_page_type_for_page(page_type),
            "pageSection" => section,
            "isRemaster" => "true",
        )
    } else {
        metric!("StorefrontFallback.Displayed", 1,
            "mode" => get_mode(action_type),
            "experienceType" => get_experience_type(is_tentpole, Some(page_type)),
            "page" => get_page_type_for_page(page_type),
            "isRemaster" => "true",
        )
    }
}

#[cfg(test)]
mod tests {
    use rstest::rstest;

    use super::*;

    #[rstest]
    #[case(ResiliencyActionType::CIRCUIT_BREAKER, "CircuitBreaker")]
    #[case(ResiliencyActionType::FALLBACK, "Fallback")]
    fn get_mode_should_return_correctly(
        #[case] action: ResiliencyActionType,
        #[case] result: &'static str,
    ) {
        assert_eq!(get_mode(action), result);
    }

    #[rstest]
    #[case(true, Some("collection"), "Tentpole")]
    #[case(true, Some("detail"), "AlwaysOn")]
    #[case(true, None, "Tentpole")]
    #[case(false, Some("collection"), "AlwaysOn")]
    #[case(false, Some("detail"), "AlwaysOn")]
    #[case(false, None, "AlwaysOn")]
    fn get_experience_type_should_return_correctly(
        #[case] is_tentpole: bool,
        #[case] page_type: Option<&'static str>,
        #[case] result: &'static str,
    ) {
        assert_eq!(get_experience_type(is_tentpole, page_type), result)
    }

    #[rstest]
    #[case("detail", "DetailsPage")]
    #[case("collection", "CollectionsPage")]
    #[case("something_else", "CollectionsPage")]
    fn get_page_type_for_page_should_return_correctly(
        #[case] page_type: &'static str,
        #[case] result: &'static str,
    ) {
        assert_eq!(get_page_type_for_page(page_type), result);
    }
}
