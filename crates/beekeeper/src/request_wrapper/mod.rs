mod metrics;
mod processes;
mod with_beekeeper_fallback;

use serde_json::Value;
use std::rc::Rc;

/// Function to validate the received value from CDN. Should return `true` if the data is valid.
pub type FallbackPageValidatorFn = Rc<dyn Fn(&Value) -> bool>;

#[cfg(any(test, feature = "mocks"))]
pub use with_beekeeper_fallback::mocks::mock_with_beekeeper_fallback;
pub use with_beekeeper_fallback::with_beekeeper_fallback;
