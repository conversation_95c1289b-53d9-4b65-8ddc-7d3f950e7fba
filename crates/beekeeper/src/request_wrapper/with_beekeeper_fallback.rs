use std::{cell::Cell, rc::Rc};

use ignx_compositron::time::Instant;
use network::{request::builder::RequestBuilder, RequestError};
use network_parser::core::ParserError;
use serde_json::Value;

use super::{processes::process_redirect_fallback_page, FallbackPageValidatorFn};
use crate::{
    request_wrapper::processes::retrieve_and_validate_target_page,
    types::{BeekeeperFallbackPageParams, ResiliencyPage, StorefrontFallbackPageFulfilledAction},
};

use mockall_double::double;

#[double]
use crate::BeekeeperContext;

/// This function wraps the [`RequestBuilder`] and uses <PERSON><PERSON> to either circuit break and get
/// data from CDN directly or if the prod request fails, uses CDN fallback data to perfrom
/// recovery.
///
/// # Warnings
/// this function takes the `on_success` and `on_failure` callbacks to extend them. This means if
/// you provide [`RequestBuilder`] with a different callback, it will be overridden.
///
/// # How it works
/// In high level, this function uses [`Cell`] data type to distribute user provided callbacks to
/// multiple I/O operations. The main I/O operations are `network` and `rpc` requests. We perform
/// RPC request to JavaScript which gives us the CDN data based on our input. We validate and call
/// related functions based on the business logic. Since everything is a callback I suggest start
/// reading from bottom to grasp what would the callbacks do.
///
/// Quick Overview:
/// - We [`Cell`] the provided callbacks to pass down to multiple callbacks.
/// - We wrap [`RequestBuilder`]'s `on_success` method to use wrapped callbacks.
/// - We add fallback logic to [`RequestBuilder`]'s `on_failure` method.
/// - Pass the created callbacks to [`RequestBuilder`] and cell the `request` to share.
/// - Check and perform redirection business logic
/// - Check `CircuitBreaker` mode, if its active we should request CDN without trying prod.
/// - Execute prod request and perform fallback if it fails via 3rd step's extension
pub fn with_beekeeper_fallback<T, S, F, R>(
    beekeeper: BeekeeperContext,
    page_params: BeekeeperFallbackPageParams,
    redirect: Option<ResiliencyPage>,
    fallback_page_validator: Option<FallbackPageValidatorFn>,
    mut request: RequestBuilder<T>,
    success_callback: S,
    failure_callback: F,
    on_resiliency_action: R,
) where
    T: Clone + for<'a> TryFrom<&'a Value, Error = ParserError> + 'static,
    S: FnOnce(T, Instant) + 'static,
    F: FnOnce(RequestError, Instant) + 'static,
    R: FnOnce(StorefrontFallbackPageFulfilledAction<T>) + 'static,
{
    // I need to cell these initial callbacks to pass down later
    let success_callback_cell = Rc::new(Cell::new(Some(success_callback)));
    let failure_callback_cell = Rc::new(Cell::new(Some(failure_callback)));
    let resiliency_action_cell = Rc::new(Cell::new(Some(on_resiliency_action)));

    let (page_type, page_id) = match &page_params {
        BeekeeperFallbackPageParams::Sport(params) => {
            (params.pageType.clone(), params.pageId.clone())
        }
        BeekeeperFallbackPageParams::LrcEdge(params) => {
            (params.pageType.clone(), params.pageId.clone())
        }
    };

    let query_params = request.request_data().url().query_params.clone();

    let request_success_callback = {
        let success_callback_cell_clone = Rc::clone(&success_callback_cell);
        move |data: T, data_fetch_time| {
            let Some(success_callback) = success_callback_cell_clone.take() else {
                log::error!("(FATAL): [beekeeper#http_success] Success callback were already called when prod network request succeeded.");
                return;
            };
            success_callback(data, data_fetch_time);
        }
    };

    let page_type_clone = page_type.clone();
    let page_id_clone = page_id.clone();
    let request_failure_callback = {
        let beekeeper_clone = Rc::clone(&beekeeper);
        let page_params_clone = page_params.clone();
        let query_params_clone = query_params.clone();
        let failure_callback_cell_clone = Rc::clone(&failure_callback_cell);
        let resiliency_action_cell_clone = Rc::clone(&resiliency_action_cell);
        let fallback_page_validator_clone = fallback_page_validator.clone();

        move |err: RequestError, data_fetch_time| {
            // If fallback not enabled, resolve the request with failure
            if !beekeeper_clone.is_page_fallback_enabled(&page_type_clone, &page_id_clone) {
                let Some(failure_callback) = failure_callback_cell_clone.take() else {
                    log::error!("(FATAL): [beekeeper#network] Cannot take failure_callback in http request failure.");
                    return;
                };
                return failure_callback(err, data_fetch_time);
            }

            log::error!("[beekeeper#with_beekeeper_fallback] Trying to recover after a failed HttpRequest. RequestError: {:?}", err);

            let error_string = err.to_string();
            let http_request_failure_callback = Some(Box::new(move || {
                log::error!("[beekeeper#network] Beekeeper fallback request failed. ");
                let Some(failure_callback) = failure_callback_cell.take() else {
                    log::error!("(FATAL): [beekeeper#network] Cannot take failure_callback in http request failure.");
                    return;
                };

                failure_callback(err, Instant::now());
            }));

            retrieve_and_validate_target_page(
                beekeeper_clone,
                page_params_clone,
                &query_params_clone,
                fallback_page_validator_clone,
                None,
                resiliency_action_cell_clone,
                http_request_failure_callback,
                Some(error_string),
            )
        }
    };

    request = request
        .on_success(request_success_callback)
        .on_failure(request_failure_callback);

    let request_cell = Rc::new(Cell::new(Some(request)));

    if let Some(redirect) = redirect {
        log::warn!("[beekeeper#with_beekeeper_fallback] Redirect detected. redirectPageType:{}, redirectPageId:{}", redirect.pageType, redirect.pageId);
        return process_redirect_fallback_page(
            Rc::clone(&beekeeper),
            page_params,
            &query_params,
            redirect,
            fallback_page_validator.clone(),
            Some(Rc::clone(&request_cell)),
            Rc::clone(&resiliency_action_cell),
        );
    }

    if beekeeper.is_circuit_breaker_enabled(&page_type, &page_id) {
        log::warn!(
            "[beekeeper#with_beekeeper_fallback] CircuitBreaker is enabled. Using fallback page for pageType:{} pageId:{}",
            page_type,
            page_id
        );
        return retrieve_and_validate_target_page(
            Rc::clone(&beekeeper),
            page_params,
            &query_params,
            fallback_page_validator.clone(),
            Some(Rc::clone(&request_cell)),
            Rc::clone(&resiliency_action_cell),
            None::<Box<dyn FnOnce()>>,
            None,
        );
    }

    let Some(request) = request_cell.take() else {
        log::error!(
            "[beekeeper_wrapper] Request protected by beekeeper already sent. Cannot send the same request."
        );
        return;
    };

    request.execute();
}

#[cfg(any(test, feature = "mocks"))]
pub mod mocks {
    use super::*;
    use crate::MockBeekeeperContext;

    // TODO: extend beekeeper wrapper function mock similar to HttpRequestBuilder to create assertions around
    pub fn mock_with_beekeeper_fallback<T, S, F, R>(
        _beekeeper: MockBeekeeperContext,
        _page_params: BeekeeperFallbackPageParams,
        _redirect: Option<ResiliencyPage>,
        _fallback_page_validator: Option<FallbackPageValidatorFn>,
        mut _request: RequestBuilder<T>,
        _success_callback: S,
        _failure_callback: F,
        _on_resiliency_action: R,
    ) where
        T: Clone + for<'a> TryFrom<&'a Value, Error = ParserError> + 'static,
        S: FnOnce(T, Instant) + 'static,
        F: FnOnce(RequestError, Instant) + 'static,
        R: FnOnce(StorefrontFallbackPageFulfilledAction<T>) + 'static,
    {
    }
}

#[cfg(test)]
mod tests {
    use app_config::{AppConfigContext, MockAppConfig};
    use auth::{AuthContext, MockAuth};
    use ignx_compositron::{
        app::launch_only_app_context,
        network::http::HttpMethod,
        prelude::{provide_context, AppContext},
    };
    use mockall::predicate::{always, eq};
    use network::{
        common::{DeviceProxyResponse, LRCEdgeResponse},
        NetworkClient, URL,
    };
    use network_parser_derive::NetworkParsed;
    use router::{MockRouting, RoutingContext};

    use super::*;
    use crate::types::LrcEdgeBeekeeperPageParams;
    use crate::MockBeekeeper;

    #[derive(Clone, NetworkParsed)]
    struct MockStruct {}

    fn mock_context(ctx: &AppContext) {
        let mock_routing_context = MockRouting::new();
        let auth = Rc::new(MockAuth::new_without_params(ctx.scope()));
        let app_config = Rc::new(MockAppConfig::default());
        provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing_context));
        provide_context::<AuthContext>(ctx.scope(), auth);
        provide_context::<AppConfigContext>(ctx.scope(), app_config);
    }

    #[test]
    fn should_call_prod_request_with_correct_params() {
        launch_only_app_context(|ctx: AppContext| {
            mock_context(&ctx);
            let client = NetworkClient::new(&ctx);
            let request = client
                .builder(URL::test_url(), HttpMethod::Get, "log_prefix")
                .with_parser(|_, _, _, cb| {
                    cb(Ok(DeviceProxyResponse::LRCEdgeResponse(
                        LRCEdgeResponse::with_resource(MockStruct {}),
                    )))
                });
            let mut beekeeper = MockBeekeeper::default();
            beekeeper
                .expect_is_circuit_breaker_enabled()
                .with(eq("inputType"), eq("inputId"))
                .return_const(false);

            with_beekeeper_fallback(
                Rc::new(beekeeper),
                BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                    pageType: "inputType".into(),
                    pageId: "inputId".into(),
                    pageSection: None,
                }),
                None,
                Some(Rc::new(|_| true)),
                request,
                |_, _| {},
                |_, _| {},
                |_| {},
            );
        });
    }

    #[test]
    fn should_circuit_break_and_call_cdn() {
        launch_only_app_context(|ctx: AppContext| {
            mock_context(&ctx);
            let client = NetworkClient::new(&ctx);
            let request = client
                .builder(URL::test_url(), HttpMethod::Get, "log_prefix")
                .with_parser(|_, _, _, cb| {
                    cb(Ok(DeviceProxyResponse::LRCEdgeResponse(
                        LRCEdgeResponse::with_resource(MockStruct {}),
                    )))
                });
            let mut beekeeper = MockBeekeeper::default();
            beekeeper
                .expect_is_circuit_breaker_enabled()
                .with(eq("inputType"), eq("inputId"))
                .times(2)
                .return_const(true);
            beekeeper.expect_is_tentpole().return_const(false);
            beekeeper
                .expect_get_beekeeper_fallback_page()
                .with(
                    eq(BeekeeperFallbackPageParams::LrcEdge(
                        LrcEdgeBeekeeperPageParams {
                            pageType: "inputType".into(),
                            pageId: "inputId".into(),
                            pageSection: None,
                        },
                    )),
                    always(),
                    always(),
                    always(),
                )
                .return_const(());

            with_beekeeper_fallback(
                Rc::new(beekeeper),
                BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                    pageType: "inputType".into(),
                    pageId: "inputId".into(),
                    pageSection: None,
                }),
                None,
                Some(Rc::new(|_| true)),
                request,
                |_, _| {},
                |_, _| {},
                |_| {},
            );
        });
    }

    #[test]
    fn should_redirect() {
        launch_only_app_context(|ctx: AppContext| {
            mock_context(&ctx);
            let client = NetworkClient::new(&ctx);
            let request = client
                .builder(URL::test_url(), HttpMethod::Get, "log_prefix")
                .with_parser(|_, _, _, cb| {
                    cb(Ok(DeviceProxyResponse::LRCEdgeResponse(
                        LRCEdgeResponse::with_resource(MockStruct {}),
                    )))
                });
            let mut beekeeper = MockBeekeeper::default();
            beekeeper
                .expect_is_circuit_breaker_enabled()
                .with(eq("somePageType"), eq("somePageId"))
                .times(1)
                .return_const(true);
            beekeeper.expect_is_tentpole().return_const(false);
            beekeeper
                .expect_get_beekeeper_fallback_page()
                .with(
                    eq(BeekeeperFallbackPageParams::LrcEdge(
                        LrcEdgeBeekeeperPageParams {
                            pageType: "somePageType".into(),
                            pageId: "somePageId".into(),
                            pageSection: None,
                        },
                    )),
                    always(),
                    always(),
                    always(),
                )
                .return_const(());
            with_beekeeper_fallback(
                Rc::new(beekeeper),
                BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                    pageType: "inputType".into(),
                    pageId: "inputId".into(),
                    pageSection: None,
                }),
                Some(ResiliencyPage {
                    pageType: "somePageType".into(),
                    pageId: "somePageId".into(),
                }),
                Some(Rc::new(|_| true)),
                request,
                |_, _| {},
                |_, _| {},
                |_| {},
            );
        });
    }
}
