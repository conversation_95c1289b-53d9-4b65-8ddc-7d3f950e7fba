[package]
name = "details-integration"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
serde.workspace = true
serde_json.workspace = true
location.workspace = true
router.workspace = true
mockall.workspace = true

[dev-dependencies]
mockall.workspace = true
rstest.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }

[lints]
workspace = true

