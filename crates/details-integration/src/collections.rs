#![allow(clippy::indexing_slicing)] // bug in clippy https://github.com/rust-lang/rust-clippy/issues/12824

use ignx_compositron::prelude::*;
use location::PageType;
use location::RustPage::RUST_DETAILS;
use mockall::automock;
use router::hooks::{use_location, use_router};
use std::rc::Rc;

pub const DETAILS_COLLECTIONS_SEAMLESS_TRANSITION_PARAM: &str =
    "detailsCollectionsSeamlessTransition";

#[derive(Clone, Copy)]
pub struct DetailsCollectionsIntegration {
    scope: Scope,
}

/// Creates a new `DetailsCollectionsIntegration` or uses an existing one for context (helpful for testing)
pub fn use_details_collections_integration(scope: Scope) -> DetailsCollectionIntegrationContext {
    if let Some(existing) = use_context::<DetailsCollectionIntegrationContext>(scope) {
        existing
    } else {
        Rc::new(DetailsCollectionsIntegration::new(scope))
    }
}

pub type DetailsCollectionIntegrationContext = Rc<dyn DetailsCollectionIntegrations>;

#[automock]
pub trait DetailsCollectionIntegrations {
    fn is_seamless_return_from_details_enabled(&self) -> bool;
}

impl DetailsCollectionsIntegration {
    fn new(scope: Scope) -> Self {
        Self { scope }
    }
}

impl DetailsCollectionIntegrations for DetailsCollectionsIntegration {
    fn is_seamless_return_from_details_enabled(&self) -> bool {
        let router = use_router(self.scope);
        let last_nav_action = router.get_last_navigation_action();
        if last_nav_action.from == PageType::Rust(RUST_DETAILS) {
            let location = use_location(self.scope);
            if let Some(param) = location
                .get_untracked()
                .pageParams
                .get(DETAILS_COLLECTIONS_SEAMLESS_TRANSITION_PARAM)
            {
                return param.as_bool().is_some_and(|v| v);
            }
        }

        false
    }
}

#[cfg(test)]
mod test {
    use crate::collections::{
        DetailsCollectionIntegrations, DetailsCollectionsIntegration,
        DETAILS_COLLECTIONS_SEAMLESS_TRANSITION_PARAM,
    };
    use ignx_compositron::compose;
    use ignx_compositron::prelude::*;
    use location::RustPage::{RUST_COLLECTIONS, RUST_DETAILS};
    use location::{Location, NavigationAction, NavigationDirection, PageType};
    use router::{MockRouting, RoutingContext};
    use rstest::rstest;
    use serde_json::{Map, Value};
    use std::rc::Rc;

    #[rstest]
    #[case(true, PageType::Rust(RUST_DETAILS), true)]
    #[case(false, PageType::Rust(RUST_DETAILS), false)]
    #[case(true, PageType::Rust(RUST_COLLECTIONS), false)]
    #[case(false, PageType::Rust(RUST_COLLECTIONS), false)]
    fn should_return_seamless_value_for_details(
        #[case] seamless_enabled: bool,
        #[case] from_page: PageType,
        #[case] expected_is_seamless_enabled: bool,
    ) {
        ignx_compositron::app::launch_test(
            move |ctx| {
                let details_collections_integration =
                    DetailsCollectionsIntegration::new(ctx.scope());
                let mut mock_router = MockRouting::default();

                let mut page_params: Map<String, Value> = Map::new();
                page_params.insert(
                    DETAILS_COLLECTIONS_SEAMLESS_TRANSITION_PARAM.to_string(),
                    Value::Bool(seamless_enabled),
                );

                let location_signal = create_rw_signal(
                    ctx.scope(),
                    Location {
                        pageType: PageType::Rust(RUST_COLLECTIONS),
                        pageParams: page_params,
                    },
                );
                mock_router
                    .expect_location()
                    .returning(move || location_signal.into());
                mock_router
                    .expect_get_last_navigation_action()
                    .returning(move || NavigationAction {
                        direction: NavigationDirection::Backwards,
                        from: from_page,
                        to: PageType::Rust(RUST_COLLECTIONS),
                    });

                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

                assert_eq!(
                    details_collections_integration.is_seamless_return_from_details_enabled(),
                    expected_is_seamless_enabled
                );

                compose! {
                    Label(text: "Empty")
                }
            },
            |_scope, mut test_game_loop| {
                let _node_tree = test_game_loop.tick_until_done();
            },
        )
    }
}
