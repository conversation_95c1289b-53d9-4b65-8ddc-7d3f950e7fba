#![allow(nonstandard_style, reason = "JS compatibility")]
use crate::api_link_action::ApiLinkAction;
use crate::container_items::EntitlementMessaging;
use crate::playback_metadata::{
    PlaybackExperienceMetadata, PlaybackMetadata, TitleActionMetadataType,
};
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::log;
use ignx_compositron::prelude::metric;
use ignx_compositron::serde::de::{AsyncDeserialize, Error};
use network_parser::custom_network_parser;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::*;
use serde_json::Value;
use std::collections::HashMap;
use std::mem::discriminant;
use strum_macros::AsRefStr;

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct OfferDetails {
    pub isFreeTrial: Option<bool>,
    pub duration: Option<u32>,
    pub unit: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct PlaybackGroupMetadata {
    pub refMarker: Option<String>,
    pub metadataActionType: Option<TitleActionMetadataType>,
}

fn async_resiliency_callback(page_source: &str, err: Error) {
    metric!("PageAction.Count", 1, "pageType" => "CommonActions", "actionName" => "DeserializationFailedResilient");
    log::error!(
        "[AsyncDeserialize][resiliency-fallback] page_source: {}, error: {}",
        page_source,
        err
    );
}

fn common_action_resiliency(err: Error) {
    async_resiliency_callback("CommonActions", err);
}

#[derive(Default, NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[network(resiliency = "CommonActions")]
#[ignx_serde(resiliency = "common_action_resiliency")]
#[derive_test_only(Debug, Serialize)]
pub enum TitleActionPresentation {
    Simple,
    Modal,
    ModalSection,
    ModalSectionHeader,
    ModalSectionBody,
    ModalSectionButton,

    #[network(skip)]
    #[ignx_serde(skip)]
    #[default]
    Unknown,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct BaseTitleActionMetadata {
    pub refMarker: Option<String>,
    pub metadataActionType: Option<TitleActionMetadataType>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
#[network(tag = "metadataActionType")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
#[ignx_serde(tag = "metadataActionType")]
pub enum TitleActionMetadata {
    Playback(PlaybackMetadata),
    PlaybackGroup(PlaybackGroupMetadata),
    AcquisitionPrime(BaseTitleActionMetadata),
    AcquisitionTVOD(AcquisitionTVODMetadata),
    AcquisitionSVOD(AcquisitionSVODMetadata),
    PreorderSuppression(BaseTitleActionMetadata),
    PreorderSuppressionSection(BaseTitleActionMetadata),
    Suppression(SuppressionMetadata),
    SuppressionSection(BaseTitleActionMetadata),
    MoreWays(BaseTitleActionMetadata),
    MoreWaysSection(MoreWaysSectionMetadata),
    WatchParty(WatchPartyMetadata),
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[network(camelCaseAll)]
#[ignx_serde(camelCaseAll)]
#[derive_test_only(Debug, Serialize)]
pub struct EntitlementMetadata {
    pub channel_tier_id: String,
    pub consent_type: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[network(camelCaseAll)]
#[ignx_serde(camelCaseAll)]
#[derive_test_only(Debug, Serialize)]
pub struct TitleAction {
    pub label: String,
    pub metadata: TitleActionMetadata,
    pub presentation: TitleActionPresentation,
    pub entitlement_metadata: Option<EntitlementMetadata>,
}

/// Used for details page where MPO actions can have multiple nested actions
#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct TitleActionNestedChild {
    pub childActions: Vec<TitleActionNestedChild>,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleAction: TitleAction,
}

/// Note that the `child_actions` will not contain further `child_actions`, at least for the
/// collections page. Therefore we can separate the model.
#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitleActionWithChild {
    pub childActions: Vec<TitleAction>,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub title_action: TitleAction,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, AsRefStr)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum LinkActionTarget {
    Landing,
    Registration,
    Settings,
    Search,
    ClientSearch,
    WatchList,
    Yvl,
    LegacyDetail,
    PrimeSignUp,
    Detail,
    Tournament,
    Category,
    Store,
    Live,
    FreeWithAds,
    MyStuff,
    Collection,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ClientAction {
    pub text: Option<String>,
    pub refMarker: String,
    pub target: String,
}

#[cfg(any(test, feature = "test_utils"))]
impl Default for ClientAction {
    fn default() -> Self {
        ClientAction {
            text: Some("Client Action Text".to_string()),
            refMarker: "ref marker".to_string(),
            target: "somewhere".to_string(),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct RetentionAction {
    pub text: Option<String>,
    pub refMarker: String,
    pub target: String,
    pub workflowName: String,
    pub retentionType: String,
}

#[cfg(any(test, feature = "test_utils"))]
impl Default for RetentionAction {
    fn default() -> Self {
        RetentionAction {
            text: Some("Retention Action Text".to_string()),
            refMarker: "ref marker".to_string(),
            target: "somewhere".to_string(),
            workflowName: "workflowName".to_string(),
            retentionType: "retentionType".to_string(),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct SportsScheduleAction {
    pub refMarker: String,
    pub excludedEventIds: Vec<String>,
    pub scheduleStart: String,
    pub scheduleEnd: String,
    pub sportId: Option<String>,
    pub competitionGroupIds: Vec<String>,
    pub scheduleFilterGroupBy: String,
    pub scheduleGroupBy: String,
    pub organizationIds: Vec<String>,
    pub competitorIds: Vec<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ConsentAction {
    pub refMarker: String,
    pub label: String,
    pub channelTierId: String,
    pub consentType: String,
}

#[derive(Serialize, NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct SwiftAction {
    pub text: Option<String>,
    pub analytics: HashMap<String, String>,
    pub refMarker: String,
    pub pageId: String,
    pub pageType: String,
    pub serviceToken: Option<String>,
    pub journeyIngressContext: Option<String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl SwiftAction {
    pub fn create_home_home() -> Self {
        SwiftAction {
            text: Some("landing action".to_string()),
            analytics: Default::default(),
            refMarker: "ref_marker".to_string(),
            pageId: "home".to_string(),
            pageType: "home".to_string(),
            serviceToken: None,
            journeyIngressContext: None,
        }
    }

    pub fn with_service_token(self, service_token: Option<String>) -> Self {
        Self {
            serviceToken: service_token,
            ..self
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct CategoryPageSwiftAction {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub swiftAction: SwiftAction,
    pub journeyIngressContext: Option<String>,
    pub serviceToken: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct SignUpAction {
    pub text: Option<String>,
    pub analytics: HashMap<String, String>,
    pub benefitId: String,
    pub bundleId: Option<String>,
    pub nonSupportedText: String,
    pub uri: String,
    pub benefitName: Option<String>,
    pub offerDetails: Option<OfferDetails>,
    pub refMarker: String,
}

#[cfg(any(test, feature = "test_utils"))]
impl SignUpAction {
    pub fn populated_default() -> Self {
        SignUpAction {
            text: None,
            analytics: Default::default(),
            benefitId: "benefit_id".to_string(),
            bundleId: None,
            nonSupportedText: "not_supported".to_string(),
            uri: "uri".to_string(),
            benefitName: None,
            offerDetails: None,
            refMarker: "ref_marker".to_string(),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ManageSubscriptionAction {
    pub refMarker: String,
    pub text: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ProfileSettingsAction {
    pub refMarker: String,
    pub text: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct PlaybackAction {
    pub uri: String,
    pub label: Option<String>,
    pub playableUriType: String,
    pub playbackExperienceMetadata: Option<PlaybackExperienceMetadata>,
    /// For linear streams, streamingType is not provided by the downstreams so this will be None.
    /// Be mindful when comparing this value to change execution path.
    pub streamingType: Option<String>,
    pub refMarker: String,
    // Linear content can provide analytics in the playback action
    pub analytics: Option<HashMap<String, String>>,
}

pub const LIVE_EVENT_STREAMING_TYPE: &str = "LiveEvent";

impl PlaybackAction {
    pub fn is_linear(&self) -> bool {
        self.streamingType
            .as_ref()
            // all player actions are linear unless they have LiveEvent type
            .map_or(true, |v| v != LIVE_EVENT_STREAMING_TYPE)
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl PlaybackAction {
    pub fn create_populated_action(uri: &str) -> Self {
        PlaybackAction {
            uri: uri.to_string(),
            playableUriType: "SomeType".to_string(),
            playbackExperienceMetadata: None,
            streamingType: None,
            refMarker: "SomeRefMarker".to_string(),
            analytics: None,
            label: None,
        }
    }

    pub fn with_ref_marker(mut self, ref_marker: String) -> Self {
        self.refMarker = ref_marker;
        self
    }

    pub fn with_label(mut self, label: Option<String>) -> Self {
        self.label = label;
        self
    }

    pub fn with_streaming_type(mut self, streaming_type: Option<String>) -> Self {
        self.streamingType = streaming_type;
        self
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitlePlaybackAction {
    pub playbackMetadata: PlaybackMetadata,
    pub label: Option<String>,
    pub refMarker: String,
}

impl TitlePlaybackAction {
    /// The model for [`TitlePlaybackAction`]s allows for other metadata types, this would not be a
    /// valid playback action.
    pub fn is_valid_title_playback_action(&self) -> bool {
        matches!(
            self.playbackMetadata.metadataActionType,
            TitleActionMetadataType::Playback
        )
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl TitlePlaybackAction {
    pub fn populated_default() -> Self {
        use crate::playback_metadata::{
            PlaybackMetadata, PlaybackPosition, TitleActionMetadataType, UserEntitlementMetadata,
            UserPlaybackMetadata,
        };
        TitlePlaybackAction {
            playbackMetadata: PlaybackMetadata {
                refMarker: "ref_marker".to_string(),
                contentDescriptors: None,
                playbackExperienceMetadata: None,
                position: PlaybackPosition::FeatureFinished,
                startPositionEpochUtc: None,
                userPlaybackMetadata: UserPlaybackMetadata {
                    runtimeSeconds: None,
                    timecodeSeconds: None,
                    hasStreamed: None,
                    isLinear: None,
                    linearStartTime: None,
                    linearEndTime: None,
                },
                userEntitlementMetadata: UserEntitlementMetadata {
                    entitlementType: "type".to_string(),
                    benefitType: vec![],
                },
                videoMaterialType: "type".to_string(),
                channelId: None,
                playbackTitle: None,
                metadataActionType: TitleActionMetadataType::Playback,
                catalogMetadata: None,
                isTrailer: None,
                isUnopenedRental: false,
            },
            label: None,
            refMarker: "ref_marker".to_string(),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitlePlaybackGroupAction {
    pub playbackGroupMetadata: PlaybackGroupMetadata,
    pub groupLabel: String,
    pub childActions: Vec<TitleActionWithChild>,
    pub label: Option<String>,
    pub refMarker: String,
    pub target: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitleOpenModalAction {
    pub refMarker: String,
    pub label: String,
    pub modalHeader: String,
    pub actionSegments: Vec<TitleActionSegment>,
}

impl TitleOpenModalAction {
    pub fn is_valid_ssm_action(&self) -> bool {
        let Some(segment) = self.actionSegments.first() else {
            return false;
        };

        let Some(transition_action) = segment.childActions.first() else {
            return false;
        };

        let TransitionAction::playback(title_action) = transition_action else {
            return false;
        };

        matches!(
            title_action.playbackMetadata.metadataActionType,
            TitleActionMetadataType::Playback
        )
    }

    pub fn extract_playable_title_id(&self) -> Option<String> {
        match self.actionSegments.first()?.childActions.first()? {
            TransitionAction::playback(TitlePlaybackAction {
                playbackMetadata:
                    PlaybackMetadata {
                        metadataActionType: TitleActionMetadataType::Playback,
                        playbackTitle,
                        ..
                    },
                ..
            }) => playbackTitle.clone(),
            _ => None,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitleActionSegment {
    pub childActions: Vec<TransitionAction>,
    pub entitlementMessaging: Option<EntitlementMessaging>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct AcquisitionSVODMetadata {
    pub refMarker: String,
    pub benefitId: String,
    pub metadataActionType: Option<TitleActionMetadataType>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct WatchPartyMetadata {
    pub refMarker: String,
    #[network(default)]
    #[ignx_serde(default)]
    pub hasPlayback: bool,
    pub metadataActionType: Option<TitleActionMetadataType>,
}

#[derive(Serialize, NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct AcquisitionTVODMetadata {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub refMarker: Option<String>,
    pub offerToken: String,
    pub contentType: String,
    pub offerType: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub videoQuality: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadataActionType: Option<TitleActionMetadataType>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct SuppressionMetadata {
    pub refMarker: Option<String>,
    pub severity: Option<String>,
    pub metadataActionType: Option<TitleActionMetadataType>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct MoreWaysSectionMetadata {
    pub refMarker: Option<String>,
    pub disclaimerText: Option<String>,
    pub metadataActionType: Option<TitleActionMetadataType>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitleAcquisitionAction {
    pub label: String,
    pub metadata: Option<TitleActionMetadata>,
    pub refMarker: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct UriAction {
    pub uri: String,
    pub text: Option<String>,
    pub refMarker: String,
    pub target: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinkAction {
    pub refMarker: Option<String>,
    pub target: LinkActionTarget,
    pub pageId: String,
    pub pageType: String,
    pub analytics: HashMap<String, String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl LinkAction {
    pub fn populated_default() -> Self {
        LinkAction {
            refMarker: Some("ref marker".to_string()),
            target: LinkActionTarget::Landing,
            pageId: "page id".to_string(),
            pageType: "page type".to_string(),
            analytics: HashMap::new(),
        }
    }
}

impl From<LinkAction> for Option<SwiftAction> {
    fn from(value: LinkAction) -> Self {
        value.refMarker.map(|rm| SwiftAction {
            text: None,
            analytics: value.analytics,
            refMarker: rm,
            pageId: value.pageId,
            pageType: value.pageType,
            serviceToken: None,
            journeyIngressContext: None,
        })
    }
}

impl From<LinkAction> for Option<TransitionAction> {
    fn from(value: LinkAction) -> Self {
        let target = value.target.clone();
        let swift_action: Option<SwiftAction> = value.into();
        swift_action.map(|sa| match target {
            LinkActionTarget::Landing => TransitionAction::landing(sa),
            LinkActionTarget::Registration => TransitionAction::registration(sa),
            LinkActionTarget::Settings => TransitionAction::settings(sa),
            LinkActionTarget::Search => TransitionAction::search(sa),
            LinkActionTarget::ClientSearch => TransitionAction::clientSearch(sa),
            LinkActionTarget::WatchList => TransitionAction::watchList(sa),
            LinkActionTarget::Yvl => TransitionAction::yvl(sa),
            LinkActionTarget::LegacyDetail => TransitionAction::legacyDetail(sa),
            LinkActionTarget::PrimeSignUp => TransitionAction::primeSignUp(sa),
            LinkActionTarget::Detail => TransitionAction::detail(sa),
            LinkActionTarget::Tournament => TransitionAction::tournament(sa),
            LinkActionTarget::Category => TransitionAction::category(sa),
            LinkActionTarget::Store => TransitionAction::store(sa),
            LinkActionTarget::Live => TransitionAction::live(sa),
            LinkActionTarget::FreeWithAds => TransitionAction::freeWithAds(sa),
            LinkActionTarget::MyStuff => TransitionAction::myStuff(sa),
            LinkActionTarget::Collection => TransitionAction::collection(sa),
        })
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, AsRefStr)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "target")]
#[ignx_serde(tag = "target")]
pub enum TransitionAction {
    landing(SwiftAction),
    registration(SwiftAction),
    settings(SwiftAction),
    search(SwiftAction),
    clientSearch(SwiftAction),
    watchList(SwiftAction),
    yvl(SwiftAction),
    legacyDetail(SwiftAction),
    primeSignUp(SwiftAction),
    detail(SwiftAction),
    tournament(SwiftAction),
    category(SwiftAction),
    store(SwiftAction),
    live(SwiftAction),
    freeWithAds(SwiftAction),
    myStuff(SwiftAction),
    collection(SwiftAction),
    testPlayground(SwiftAction),
    sportsFavorites(ClientAction),
    removeFromFavorites(ClientAction),
    linearStationDetail(SwiftAction),
    manageSubscription(ManageSubscriptionAction),
    consent(ConsentAction),
    signUp(SignUpAction),
    player(PlaybackAction),
    playback(TitlePlaybackAction),
    playbackGroup(TitlePlaybackGroupAction),
    acquisition(TitleAcquisitionAction),
    browse(CategoryPageSwiftAction),
    profileSettings(ProfileSettingsAction),
    openModal(TitleOpenModalAction),
    fuseOfferActivation(ClientAction),
    changeHomeRegion(ClientAction),
    primeRetention(RetentionAction),
    sportsSchedule(SportsScheduleAction),
    liveSportsNoOp,
    apiLink(ApiLinkAction),
}

impl TransitionAction {
    pub fn is_swift_action(&self) -> bool {
        matches!(
            self,
            TransitionAction::landing(_)
                | TransitionAction::registration(_)
                | TransitionAction::settings(_)
                | TransitionAction::search(_)
                | TransitionAction::clientSearch(_)
                | TransitionAction::watchList(_)
                | TransitionAction::yvl(_)
                | TransitionAction::legacyDetail(_)
                | TransitionAction::primeSignUp(_)
                | TransitionAction::detail(_)
                | TransitionAction::tournament(_)
                | TransitionAction::category(_)
                | TransitionAction::store(_)
                | TransitionAction::live(_)
                | TransitionAction::freeWithAds(_)
                | TransitionAction::myStuff(_)
                | TransitionAction::collection(_)
                | TransitionAction::testPlayground(_)
                | TransitionAction::linearStationDetail(_)
        )
    }

    pub fn is_swift_based_action(&self) -> bool {
        self.is_swift_action()
            || matches!(
                self,
                TransitionAction::player(_)
                    | TransitionAction::signUp(_)
                    | TransitionAction::browse(_)
            )
    }

    pub fn is_swift_action_and<T>(&self, cb: impl Fn(&SwiftAction) -> T) -> Option<T> {
        match self {
            TransitionAction::landing(swift_action)
            | TransitionAction::registration(swift_action)
            | TransitionAction::settings(swift_action)
            | TransitionAction::search(swift_action)
            | TransitionAction::clientSearch(swift_action)
            | TransitionAction::watchList(swift_action)
            | TransitionAction::yvl(swift_action)
            | TransitionAction::legacyDetail(swift_action)
            | TransitionAction::primeSignUp(swift_action)
            | TransitionAction::detail(swift_action)
            | TransitionAction::tournament(swift_action)
            | TransitionAction::category(swift_action)
            | TransitionAction::store(swift_action)
            | TransitionAction::live(swift_action)
            | TransitionAction::freeWithAds(swift_action)
            | TransitionAction::myStuff(swift_action)
            | TransitionAction::collection(swift_action)
            | TransitionAction::testPlayground(swift_action) => Some(cb(swift_action)),
            _ => None,
        }
    }

    pub fn get_ref_marker(&self) -> String {
        match self {
            TransitionAction::landing(action)
            | TransitionAction::registration(action)
            | TransitionAction::settings(action)
            | TransitionAction::search(action)
            | TransitionAction::clientSearch(action)
            | TransitionAction::watchList(action)
            | TransitionAction::yvl(action)
            | TransitionAction::legacyDetail(action)
            | TransitionAction::primeSignUp(action)
            | TransitionAction::linearStationDetail(action)
            | TransitionAction::detail(action)
            | TransitionAction::tournament(action)
            | TransitionAction::category(action)
            | TransitionAction::store(action)
            | TransitionAction::live(action)
            | TransitionAction::freeWithAds(action)
            | TransitionAction::myStuff(action)
            | TransitionAction::collection(action)
            | TransitionAction::testPlayground(action) => action.refMarker.clone(),
            TransitionAction::consent(action) => action.refMarker.clone(),
            TransitionAction::manageSubscription(action) => action.refMarker.clone(),
            TransitionAction::signUp(action) => action.refMarker.clone(),
            TransitionAction::player(action) => action.refMarker.clone(),
            TransitionAction::playback(action) => action.refMarker.clone(),
            TransitionAction::playbackGroup(action) => action.refMarker.clone(),
            TransitionAction::acquisition(action) => action.refMarker.clone(),
            TransitionAction::browse(action) => action.swiftAction.refMarker.clone(),
            TransitionAction::profileSettings(action) => action.refMarker.clone(),
            TransitionAction::openModal(action) => action.refMarker.clone(),
            TransitionAction::fuseOfferActivation(action)
            | TransitionAction::sportsFavorites(action)
            | TransitionAction::removeFromFavorites(action) => action.refMarker.clone(),
            TransitionAction::changeHomeRegion(action) => action.refMarker.clone(),
            TransitionAction::primeRetention(action) => action.refMarker.clone(),
            TransitionAction::sportsSchedule(action) => action.refMarker.clone(),
            TransitionAction::liveSportsNoOp => "".to_string(),
            TransitionAction::apiLink(action) => action.refMarker.clone(),
        }
    }

    /// For CSM, we should report with the primary action only if it is a swift-based action.
    /// [React implementation](https://code.amazon.com/packages/AVLivingRoomClient/blobs/19bda937e075790996b5be4c70c090b5499ea581/--/packages/avlrc-containers/common/HeroComponent/HeroComponent.tsx#L508-L512)
    /// [Ticket discussion](https://i.amazon.com/issues/P145951059?selectedConversation=da0f3834-cf31-45e9-9836-13dee4459162)
    pub fn get_csm_action(
        primary_action: Option<&TransitionAction>,
        secondary_action: Option<&TransitionAction>,
    ) -> Option<TransitionAction> {
        if primary_action.is_some_and(|a| a.is_swift_based_action()) {
            primary_action.cloned()
        } else {
            secondary_action.cloned()
        }
    }

    pub fn get_journey_ingress_context(&self) -> Option<String> {
        match self {
            TransitionAction::landing(action)
            | TransitionAction::registration(action)
            | TransitionAction::settings(action)
            | TransitionAction::search(action)
            | TransitionAction::clientSearch(action)
            | TransitionAction::watchList(action)
            | TransitionAction::yvl(action)
            | TransitionAction::legacyDetail(action)
            | TransitionAction::primeSignUp(action)
            | TransitionAction::detail(action)
            | TransitionAction::tournament(action)
            | TransitionAction::category(action)
            | TransitionAction::store(action)
            | TransitionAction::live(action)
            | TransitionAction::freeWithAds(action)
            | TransitionAction::myStuff(action)
            | TransitionAction::collection(action)
            | TransitionAction::linearStationDetail(action)
            | TransitionAction::testPlayground(action) => action.journeyIngressContext.clone(),
            TransitionAction::browse(action) => action.journeyIngressContext.clone(),
            TransitionAction::consent(_)
            | TransitionAction::manageSubscription(_)
            | TransitionAction::signUp(_)
            | TransitionAction::player(_)
            | TransitionAction::playback(_)
            | TransitionAction::acquisition(_)
            | TransitionAction::profileSettings(_)
            | TransitionAction::openModal(_)
            | TransitionAction::primeRetention(_)
            | TransitionAction::fuseOfferActivation(_)
            | TransitionAction::changeHomeRegion(_)
            | TransitionAction::playbackGroup(_)
            | TransitionAction::sportsFavorites(_)
            | TransitionAction::removeFromFavorites(_)
            | TransitionAction::liveSportsNoOp
            | TransitionAction::sportsSchedule(_)
            | TransitionAction::apiLink(_) => None,
        }
    }

    /// Compares two `TransitionAction`s to see if they are equivalent looking only at the
    /// relevant transition parameters (e.g. page type, page id, service token, target)
    pub fn eq_transition_params(&self, other: &Self) -> bool {
        if discriminant(self) != discriminant(other) {
            // different targets
            return false;
        }
        let same_swift_action = self
            .is_swift_action_and(|swift_action_self| {
                let self_params = (
                    &swift_action_self.pageId,
                    &swift_action_self.pageType,
                    &swift_action_self.serviceToken,
                    &swift_action_self.journeyIngressContext,
                );
                other.is_swift_action_and(|s| {
                    let other_params = (
                        &s.pageId,
                        &s.pageType,
                        &s.serviceToken,
                        &s.journeyIngressContext,
                    );
                    self_params == other_params
                })
            })
            .flatten()
            .unwrap_or_default();
        if same_swift_action {
            return true;
        }
        match (self, other) {
            (TransitionAction::manageSubscription(_), TransitionAction::manageSubscription(_)) => {
                true
            }
            (TransitionAction::consent(first), TransitionAction::consent(second)) => {
                first.channelTierId == second.channelTierId
            }
            (TransitionAction::signUp(first), TransitionAction::signUp(second)) => {
                let first_params = (
                    &first.benefitId,
                    &first.bundleId,
                    &first.benefitName,
                    &first.uri,
                );
                let second_params = (
                    &second.benefitId,
                    &second.bundleId,
                    &second.benefitName,
                    &second.uri,
                );
                first_params == second_params
            }
            (TransitionAction::player(first), TransitionAction::player(second)) => {
                first.uri == second.uri
            }
            (TransitionAction::playback(first), TransitionAction::playback(second)) => {
                first.playbackMetadata == second.playbackMetadata
            }
            (TransitionAction::playbackGroup(first), TransitionAction::playbackGroup(second)) => {
                first.target == second.target
                    && first.playbackGroupMetadata == second.playbackGroupMetadata
            }
            (TransitionAction::acquisition(first), TransitionAction::acquisition(second)) => {
                first.metadata == second.metadata
            }
            (TransitionAction::browse(first), TransitionAction::browse(second)) => first == second,
            (TransitionAction::profileSettings(_), TransitionAction::profileSettings(_)) => true,
            (TransitionAction::openModal(_), TransitionAction::openModal(_)) => true,
            (
                TransitionAction::fuseOfferActivation(first),
                TransitionAction::fuseOfferActivation(second),
            ) => first.target == second.target,
            (
                TransitionAction::changeHomeRegion(first),
                TransitionAction::changeHomeRegion(second),
            ) => first.target == second.target,
            (TransitionAction::primeRetention(first), TransitionAction::primeRetention(second)) => {
                let first_params = (&first.target, &first.retentionType, &first.workflowName);
                let second_params = (&second.target, &second.retentionType, &second.workflowName);
                first_params == second_params
            }
            (TransitionAction::sportsSchedule(first), TransitionAction::sportsSchedule(second)) => {
                first == second
            }
            (TransitionAction::apiLink(first), TransitionAction::apiLink(second)) => {
                first == second
            }
            _ => false,
        }
    }

    fn is_hero_primary_non_swift_based_action(&self) -> bool {
        match self {
            TransitionAction::playback(action) => action.is_valid_title_playback_action(),
            TransitionAction::consent(_) => true,
            TransitionAction::acquisition(_) => true,
            TransitionAction::openModal(_) => true,
            TransitionAction::sportsFavorites(_) => true,
            _ => false,
        }
    }

    fn find_action_matching_condition(
        actions: &[&TransitionAction],
        condition: impl Fn(&TransitionAction) -> bool,
    ) -> Option<TransitionAction> {
        actions.iter().find_map(|&action| {
            if condition(action) {
                Some(action.clone())
            } else {
                None
            }
        })
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl TransitionAction {
    pub fn create_landing_with_service_token(token: Option<String>) -> Self {
        TransitionAction::landing(SwiftAction::create_home_home().with_service_token(token))
    }

    pub fn create_player() -> Self {
        TransitionAction::player(PlaybackAction::create_populated_action("SomeUri"))
    }

    pub fn create_playback() -> Self {
        TransitionAction::playback(TitlePlaybackAction::populated_default())
    }

    pub fn create_sign_up() -> Self {
        TransitionAction::signUp(SignUpAction::populated_default())
    }

    pub fn create_consent() -> Self {
        TransitionAction::consent(ConsentAction {
            refMarker: "fake refMarker".to_string(),
            label: "fake label".to_string(),
            channelTierId: "fake channelTierId".to_string(),
            consentType: "fake consentType".to_string(),
        })
    }

    pub fn create_detail() -> Self {
        TransitionAction::detail(SwiftAction {
            pageType: "detail".to_string(),
            pageId: "a fake gti".to_string(),
            ..SwiftAction::create_home_home()
        })
    }

    pub fn create_linear_station_detail() -> Self {
        TransitionAction::linearStationDetail(SwiftAction {
            pageType: "linearStationDetail".to_string(),
            pageId: "amzn1.fake.gti.adae5e4f-e886-4b0f-bf55-c66fd3fc032b".to_string(),
            analytics: Default::default(),
            refMarker: "ref_marker".to_string(),
            journeyIngressContext: None,
            serviceToken: None,
            text: None,
        })
    }

    pub fn with_ref_marker(mut self, ref_marker: String) -> Self {
        match self {
            TransitionAction::landing(ref mut action)
            | TransitionAction::registration(ref mut action)
            | TransitionAction::settings(ref mut action)
            | TransitionAction::search(ref mut action)
            | TransitionAction::clientSearch(ref mut action)
            | TransitionAction::watchList(ref mut action)
            | TransitionAction::yvl(ref mut action)
            | TransitionAction::legacyDetail(ref mut action)
            | TransitionAction::primeSignUp(ref mut action)
            | TransitionAction::linearStationDetail(ref mut action)
            | TransitionAction::detail(ref mut action)
            | TransitionAction::tournament(ref mut action)
            | TransitionAction::category(ref mut action)
            | TransitionAction::store(ref mut action)
            | TransitionAction::live(ref mut action)
            | TransitionAction::freeWithAds(ref mut action)
            | TransitionAction::myStuff(ref mut action)
            | TransitionAction::collection(ref mut action)
            | TransitionAction::testPlayground(ref mut action) => {
                action.refMarker = ref_marker;
            }
            TransitionAction::consent(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::manageSubscription(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::signUp(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::player(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::playback(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::playbackGroup(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::acquisition(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::browse(ref mut action) => action.swiftAction.refMarker = ref_marker,
            TransitionAction::profileSettings(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::openModal(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::fuseOfferActivation(ref mut action)
            | TransitionAction::changeHomeRegion(ref mut action)
            | TransitionAction::sportsFavorites(ref mut action)
            | TransitionAction::removeFromFavorites(ref mut action) => {
                action.refMarker = ref_marker
            }
            TransitionAction::primeRetention(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::sportsSchedule(ref mut action) => action.refMarker = ref_marker,
            TransitionAction::liveSportsNoOp => {}
            TransitionAction::apiLink(ref mut action) => action.refMarker = ref_marker,
        };
        self
    }
}

/// Order is important here. Serde will try and deserialise the JSON for an action to these variants
/// in order, choosing the first one that matches. As some of the models are supersets of others,
/// we should ensure the most complex ones are first and the least complex are last.
#[derive(Clone, AsyncDeserialize)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
#[ignx_serde(untagged)]
pub enum Action {
    TransitionAction(TransitionAction),
    TitlePlaybackGroupAction(TitlePlaybackGroupAction),
    LinkAction(LinkAction),
    UriAction(UriAction),
    ClientAction(ClientAction),
    RetentionAction(RetentionAction),
}

impl Action {
    /// The hero has (up to) two buttons, a "primary" button and a "secondary" button.
    /// The primary button should be a "buy box" action, i.e. a:
    ///     - `TitlePlayback`
    ///     - `TitleAcquisition`
    ///     - `OpenModal`
    /// action, or if none of these are present then a Swift-based action.
    /// The secondary button should be a Swift-based action if the "buy box" action is present, or none
    /// at all.
    pub fn get_hero_buttons_from_actions(
        actions: Vec<&Action>,
    ) -> (Option<TransitionAction>, Option<TransitionAction>) {
        let actions = actions
            .iter()
            .filter_map(|action| {
                if let Action::TransitionAction(action) = action {
                    match action {
                        TransitionAction::openModal(open_modal_action) => {
                            if open_modal_action.is_valid_ssm_action() {
                                Some(action)
                            } else {
                                None
                            }
                        }
                        _ => Some(action),
                    }
                } else {
                    None
                }
            })
            .collect::<Vec<&TransitionAction>>();

        let primary_non_swift_based_action =
            TransitionAction::find_action_matching_condition(&actions, |a: &TransitionAction| {
                a.is_hero_primary_non_swift_based_action()
            });

        let secondary_action = primary_non_swift_based_action.as_ref().and_then(|_| {
            TransitionAction::find_action_matching_condition(&actions, |a: &TransitionAction| {
                a.is_swift_based_action()
            })
        });
        let primary_action = primary_non_swift_based_action.or_else(|| {
            TransitionAction::find_action_matching_condition(&actions, |a: &TransitionAction| {
                a.is_swift_based_action()
            })
        });

        (primary_action, secondary_action)
    }

    /// Check if the given action is valid.
    /// Performs sanity checks for API link action, defaults to true otherwise.
    /// We can add validation logic for other action types if needed.
    pub fn is_valid(&self) -> bool {
        match self {
            Action::TransitionAction(TransitionAction::apiLink(api_link_action)) => {
                api_link_action.is_valid()
            }
            _ => true,
        }
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl Action {
    pub fn create_transition_landing(service_token: Option<String>) -> Self {
        Action::TransitionAction(TransitionAction::create_landing_with_service_token(
            service_token,
        ))
    }

    pub fn create_client_default() -> Self {
        Action::ClientAction(ClientAction::default())
    }

    pub fn create_retention_default() -> Self {
        Action::RetentionAction(RetentionAction::default())
    }

    pub fn create_link_default() -> Self {
        Action::LinkAction(LinkAction::populated_default())
    }
}

custom_network_parser!(Action, |value| {
    let transition_action: ParserReturnType<TransitionAction> = parse_with_try_from(value);
    if let Ok(action) = transition_action {
        return Ok(Action::TransitionAction(action));
    }

    let title_playback_group_action: ParserReturnType<TitlePlaybackGroupAction> =
        parse_with_try_from(value);
    if let Ok(action) = title_playback_group_action {
        return Ok(Action::TitlePlaybackGroupAction(action));
    }

    let link_action: ParserReturnType<LinkAction> = parse_with_try_from(value);
    if let Ok(action) = link_action {
        return Ok(Action::LinkAction(action));
    }

    let uri_action: ParserReturnType<UriAction> = parse_with_try_from(value);
    if let Ok(action) = uri_action {
        return Ok(Action::UriAction(action));
    }

    let client_action: ParserReturnType<ClientAction> = parse_with_try_from(value);
    if let Ok(action) = client_action {
        return Ok(Action::ClientAction(action));
    }

    let retention_action: ParserReturnType<RetentionAction> = parse_with_try_from(value);
    if let Ok(action) = retention_action {
        return Ok(Action::RetentionAction(action));
    }

    Err("No matching type for 'Action'".to_string())
});

#[cfg(test)]
mod test {
    use super::*;
    use crate::actions::{TitleActionSegment, TitleOpenModalAction, TransitionAction};
    use rstest::*;

    mod action {
        use super::*;

        #[rstest]
        #[case(Action::create_client_default(), true)]
        #[case(Action::create_link_default(), true)]
        #[case(
            Action::TransitionAction(TransitionAction::apiLink(
                ApiLinkAction::create_with_all_params()
            )),
            true
        )]
        #[case(
            Action::TransitionAction(TransitionAction::apiLink(
                ApiLinkAction::create_with_undefined_target_criteria()
            )),
            false
        )]
        #[case(Action::TransitionAction(TransitionAction::create_detail()), true)]
        #[case(Action::TransitionAction(TransitionAction::create_player()), true)]
        fn is_valid_should_return_expected_value(#[case] input: Action, #[case] expected: bool) {
            assert_eq!(input.is_valid(), expected);
        }
    }

    mod ssm_action {
        use super::*;
        use crate::playback_metadata::{
            PlaybackPosition, UserEntitlementMetadata, UserPlaybackMetadata,
        };

        fn get_mock_playback_transition_action() -> TransitionAction {
            get_mock_playback_transition_action_with_title(None)
        }

        fn get_mock_playback_transition_action_with_title(
            title: Option<String>,
        ) -> TransitionAction {
            TransitionAction::playback(TitlePlaybackAction {
                playbackMetadata: PlaybackMetadata {
                    refMarker: "ref_marker".to_string(),
                    contentDescriptors: None,
                    playbackExperienceMetadata: None,
                    position: PlaybackPosition::FeatureFinished,
                    startPositionEpochUtc: None,
                    userPlaybackMetadata: UserPlaybackMetadata {
                        runtimeSeconds: Some(0),
                        timecodeSeconds: Some(0),
                        hasStreamed: Some(false),
                        isLinear: None,
                        linearStartTime: None,
                        linearEndTime: None,
                    },
                    userEntitlementMetadata: UserEntitlementMetadata {
                        entitlementType: "type".to_string(),
                        benefitType: vec![],
                    },
                    videoMaterialType: "type".to_string(),
                    channelId: None,
                    playbackTitle: title,
                    metadataActionType: TitleActionMetadataType::Playback,
                    catalogMetadata: None,
                    isTrailer: None,
                    isUnopenedRental: false,
                },
                label: None,
                refMarker: "".to_string(),
            })
        }

        fn get_mock_acquisition_transition_action() -> TransitionAction {
            TransitionAction::acquisition(TitleAcquisitionAction {
                label: "Test Label".to_string(),
                metadata: Some(TitleActionMetadata::AcquisitionPrime(
                    BaseTitleActionMetadata {
                        refMarker: None,
                        metadataActionType: Some(TitleActionMetadataType::AcquisitionPrime),
                    },
                )),
                refMarker: "Test RefMarker".to_string(),
            })
        }

        #[rstest]
        #[case(get_mock_playback_transition_action(), true)]
        #[case(get_mock_acquisition_transition_action(), false)]
        fn is_valid_ssm_action_should_return_expected_value(
            #[case] input: TransitionAction,
            #[case] expected: bool,
        ) {
            let action = TitleOpenModalAction {
                refMarker: "Test Refmarker".to_string(),
                label: "Open Modal Label".to_string(),
                modalHeader: "Test Header".to_string(),
                actionSegments: vec![TitleActionSegment {
                    childActions: vec![input],
                    entitlementMessaging: None,
                }],
            };

            assert_eq!(action.is_valid_ssm_action(), expected)
        }

        #[rstest]
        #[case(get_mock_playback_transition_action_with_title(Some("title-id".to_string())), Some("title-id".to_string()))]
        #[case(get_mock_playback_transition_action_with_title(None), None)]
        #[case(get_mock_acquisition_transition_action(), None)]
        fn extract_playable_title_id(
            #[case] input: TransitionAction,
            #[case] expected: Option<String>,
        ) {
            let action = TitleOpenModalAction {
                refMarker: "Test Refmarker".to_string(),
                label: "Open Modal Label".to_string(),
                modalHeader: "Test Header".to_string(),
                actionSegments: vec![TitleActionSegment {
                    childActions: vec![input],
                    entitlementMessaging: None,
                }],
            };

            assert_eq!(action.extract_playable_title_id(), expected)
        }
    }

    mod journey_ingress_context {
        use super::*;
        use crate::playback_metadata::{
            PlaybackPosition, UserEntitlementMetadata, UserPlaybackMetadata,
        };

        fn get_dummy_swift_action() -> SwiftAction {
            SwiftAction {
                text: None,
                analytics: Default::default(),
                refMarker: "ref_marker".to_string(),
                pageId: "".to_string(),
                pageType: "".to_string(),
                serviceToken: None,
                journeyIngressContext: Some("TestContext".to_string()),
            }
        }

        fn get_dummy_browse_action() -> CategoryPageSwiftAction {
            CategoryPageSwiftAction {
                swiftAction: get_dummy_swift_action(),
                journeyIngressContext: Some("TestContext".to_string()),
                serviceToken: "SomeToken".to_string(),
            }
        }

        fn get_dummy_consent_action() -> ConsentAction {
            ConsentAction {
                refMarker: "SomeRefMarker".to_string(),
                label: "SomeLabel".to_string(),
                channelTierId: "SomeId".to_string(),
                consentType: "SomeConsentType".to_string(),
            }
        }

        fn get_dummy_manage_subscription_action() -> ManageSubscriptionAction {
            ManageSubscriptionAction {
                refMarker: "SomeRefMarker".to_string(),
                text: "SomeLabel".to_string(),
            }
        }

        fn get_dummy_signup_action() -> SignUpAction {
            SignUpAction {
                text: None,
                analytics: Default::default(),
                benefitId: "SomeId".to_string(),
                bundleId: None,
                nonSupportedText: "SomeText".to_string(),
                uri: "SomeUri".to_string(),
                benefitName: None,
                offerDetails: None,
                refMarker: "SomeRefMarker".to_string(),
            }
        }

        fn get_dummy_playback_action() -> TitlePlaybackAction {
            TitlePlaybackAction {
                playbackMetadata: PlaybackMetadata {
                    refMarker: "SomeRefMarker".to_string(),
                    metadataActionType: TitleActionMetadataType::AcquisitionSVOD,
                    userPlaybackMetadata: UserPlaybackMetadata {
                        runtimeSeconds: None,
                        timecodeSeconds: None,
                        hasStreamed: None,
                        isLinear: None,
                        linearStartTime: None,
                        linearEndTime: None,
                    },
                    userEntitlementMetadata: UserEntitlementMetadata {
                        entitlementType: "".to_string(),
                        benefitType: vec![],
                    },
                    playbackExperienceMetadata: None,
                    videoMaterialType: "SomeType".to_string(),
                    position: PlaybackPosition::FeatureFinished,
                    contentDescriptors: None,
                    startPositionEpochUtc: None,
                    channelId: None,
                    playbackTitle: None,
                    catalogMetadata: None,
                    isTrailer: None,
                    isUnopenedRental: false,
                },
                label: None,
                refMarker: "SomeRefMarker".to_string(),
            }
        }

        fn get_dummy_acquisition_action() -> TitleAcquisitionAction {
            TitleAcquisitionAction {
                label: "SomeLabel".to_string(),
                metadata: None,
                refMarker: "SomeRefMarker".to_string(),
            }
        }

        fn get_dummy_profile_settings_action() -> ProfileSettingsAction {
            ProfileSettingsAction {
                refMarker: "SomeRefMarker".to_string(),
                text: None,
            }
        }

        fn get_dummy_open_modal_action() -> TitleOpenModalAction {
            TitleOpenModalAction {
                refMarker: "SomeRefMarker".to_string(),
                label: "SomeLabel".to_string(),
                modalHeader: "SomeHeader".to_string(),
                actionSegments: vec![TitleActionSegment {
                    childActions: vec![],
                    entitlementMessaging: None,
                }],
            }
        }

        fn get_dummy_client_action() -> ClientAction {
            ClientAction {
                text: Some("SomeText".to_string()),
                refMarker: "SomeRefMarker".to_string(),
                target: "SomeTarget".to_string(),
            }
        }

        fn get_dummy_retention_action() -> RetentionAction {
            RetentionAction {
                text: Some("SomeText".to_string()),
                refMarker: "SomeRefMarker".to_string(),
                target: "SomeTarget".to_string(),
                workflowName: "workflowName".to_string(),
                retentionType: "retentionType".to_string(),
            }
        }

        #[rstest]
        #[case(
            TransitionAction::landing(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::registration(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::settings(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::search(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::clientSearch(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::watchList(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::yvl(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::legacyDetail(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::primeSignUp(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::detail(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::tournament(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::category(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::store(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::live(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::freeWithAds(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::myStuff(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::collection(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::testPlayground(get_dummy_swift_action()),
            Some("TestContext".to_string()
        ))]
        #[case(
            TransitionAction::linearStationDetail(get_dummy_swift_action()),
            Some("TestContext".to_string())
        )]
        #[case(
            TransitionAction::browse(get_dummy_browse_action()),
            Some("TestContext".to_string())
        )]
        #[case(TransitionAction::consent(get_dummy_consent_action()), None)]
        #[case(
            TransitionAction::manageSubscription(get_dummy_manage_subscription_action()),
            None
        )]
        #[case(TransitionAction::signUp(get_dummy_signup_action()), None)]
        #[case(
            TransitionAction::player(PlaybackAction::create_populated_action("SomeUri")),
            None
        )]
        #[case(TransitionAction::playback(get_dummy_playback_action()), None)]
        #[case(TransitionAction::acquisition(get_dummy_acquisition_action()), None)]
        #[case(
            TransitionAction::profileSettings(get_dummy_profile_settings_action()),
            None
        )]
        #[case(TransitionAction::openModal(get_dummy_open_modal_action()), None)]
        #[case(TransitionAction::fuseOfferActivation(get_dummy_client_action()), None)]
        #[case(TransitionAction::changeHomeRegion(get_dummy_client_action()), None)]
        #[case(TransitionAction::primeRetention(get_dummy_retention_action()), None)]
        #[case(
            TransitionAction::apiLink(ApiLinkAction::create_with_all_params()),
            None
        )]
        fn get_journey_ingress_context_should_return_expected_value(
            #[case] input: TransitionAction,
            #[case] expected: Option<String>,
        ) {
            assert_eq!(input.get_journey_ingress_context(), expected)
        }
    }
}
