use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;

use crate::{
    actions::TransitionAction,
    offer_card::{IconType, OfferCard, OfferCardDecoration},
};

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(tag = "type")]
pub enum TitleActionV2 {
    Play(PlayAction),
    Subscribe(SubscribeAction),
    Transact(TransactAction),
    Message(MessageAction),
    OpenModal(ModalAction),
    ShowCardOptions(ShowCardOptionsAction),
    Redirect(RedirectAction),
    ExpandCard(ExpandCardAction),
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct PlayAction {
    pub presentation: ActionPresentation,
    pub navigation_action: TransitionAction,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct SubscribeAction {
    pub presentation: ActionPresentation,
    pub subscription_type: SubscriptionType,
    pub associated_offer_cards: Vec<OfferCard>,
    pub navigation_action: TransitionAction,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct TransactAction {
    pub presentation: ActionPresentation,
    pub transaction_type: Option<String>,
    pub navigation_action: TransitionAction,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct MessageAction {
    pub presentation: ActionPresentation,
    pub reason: MessageReason,
    pub context: HashMap<String, String>,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct ModalAction {
    pub presentation: ActionPresentation,
    pub modal_type: ModalType,
    pub modal_header: String,
    pub sections: Vec<ModalSection>,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct ShowCardOptionsAction {
    pub presentation: ActionPresentation,
    pub offer_cards: Vec<OfferCard>,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct RedirectAction {
    pub presentation: ActionPresentation,
    pub redirect_type: RedirectType,
    pub navigation_action: TransitionAction,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct ExpandCardAction {
    pub presentation: ActionPresentation,
    pub navigation_action: TransitionAction,
    pub offer_decoration: OfferCardDecoration,
    pub ref_marker: String,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct ActionPresentation {
    pub label: String,
    pub secondary_label: Option<String>,
    pub icon: Option<IconType>,
    pub state_presentation: HashMap<String, ActionPresentation>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct SlotItem {
    pub message: Option<String>,
    pub image: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct ModalSection {
    pub segment_header: Option<String>,
    pub segment_text: String,
    pub actions: Vec<TitleActionV2>,
    pub slot_items: HashMap<String, Vec<SlotItem>>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum SubscriptionType {
    Prime,
    Channel,
    Svod,
    ThirdParty,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum VideoQuality {
    Sd,
    Hd,
    Uhd,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum MessageReason {
    Default,
    PlaybackSuppression,
    GeographicRestriction,
    AgeRestriction,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum ModalType {
    PreorderConfirmation,
    SubscribeAndWatch,
    StreamSelector,
    HowDoIWatchThis,
    MorePurchaseOptions,
    Default,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum RedirectType {
    Consent,
}
