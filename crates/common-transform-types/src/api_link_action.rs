#![allow(nonstandard_style)]

use cfg_test_attr_derive::{cfg_test_only, derive_test_only};
use ignx_compositron::{
    lazy_static::lazy_static,
    log,
    network::http::HttpMethod,
    regex_lite::{<PERSON>rror, Regex},
    serde::de::AsyncDeserialize,
};
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
#[cfg_test_only]
use serde::Serialize;
use serde_json::Value;
use std::collections::HashMap;
use strum_macros::EnumString;

#[derive(AsyncDeserialize, Clone, NetworkParsed, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct ApiLinkAction {
    pub metadata: ApiLinkMetadata,
    pub refMarker: String,
    pub requestBody: Option<String>,
    pub requestHeaders: HashMap<String, String>,
    pub requestMethod: ApiLinkRequestMethod,
    pub text: Option<String>,
    pub target: String,
    pub uri: String,
}

#[derive(AsyncDeserialize, Clone, NetworkParsed, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct ApiLinkMetadata {
    pub clientTimeoutInMs: u32,
    pub isAsync: bool,
    pub responseStatusMessages: Vec<ApiLinkResponseStatusMessage>,
}

#[derive(AsyncDeserialize, Clone, NetworkParsed, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct ApiLinkResponseStatusMessage {
    pub message: String,
    #[network(rename = "type")]
    pub messageType: ApiLinkResponseStatusMessageType,
    pub priority: i32,
    pub targetCriteria: ApiLinkResponseStatusMessageTargetCriteria,
}

#[derive(AsyncDeserialize, Clone, NetworkParsed, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub struct ApiLinkResponseStatusMessageTargetCriteria {
    pub targetResponseBodyRegEx: Option<String>,
    pub targetStatusCodes: Vec<String>,
}

#[derive(AsyncDeserialize, Clone, EnumString, NetworkParsed, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub enum ApiLinkResponseStatusMessageType {
    nonRetryableError,
    retryableError,
    success,
}

#[derive(AsyncDeserialize, Clone, EnumString, NetworkParsed, PartialEq)]
#[derive_test_only(Debug, Serialize)]
pub enum ApiLinkRequestMethod {
    DELETE,
    GET,
    POST,
    PUT,
}

lazy_static! {
    static ref VALID_HTTP_STATUS_CODE_REGEX: Result<Regex, Error> = Regex::new(r"^[Xx0-9]{3}$");
}

impl ApiLinkAction {
    const REQUEST_HEADER_CONTENT_TYPE: &str = "content-type";

    pub fn is_valid(&self) -> bool {
        let valid_http_status_code_regex = match VALID_HTTP_STATUS_CODE_REGEX.as_ref() {
            Ok(regex) => regex,
            Err(err) => {
                log::error!(
                    "Failed to compile API link action valid HTTP status code regex pattern: {}",
                    err
                );
                return false;
            }
        };

        // clientTimeoutInMs should be a positive integer.
        if self.metadata.clientTimeoutInMs == 0 {
            return false;
        }

        let mut has_success_msg = false;
        let mut has_error_msg = false;

        for response_status_msg in self.metadata.responseStatusMessages.iter() {
            has_success_msg |=
                response_status_msg.messageType == ApiLinkResponseStatusMessageType::success;
            has_error_msg |= response_status_msg.messageType
                == ApiLinkResponseStatusMessageType::nonRetryableError
                || response_status_msg.messageType
                    == ApiLinkResponseStatusMessageType::retryableError;

            // targetCriteria should not be undefined.
            if response_status_msg
                .targetCriteria
                .targetResponseBodyRegEx
                .clone()
                .is_none_or(|regex_pattern| regex_pattern.is_empty())
                && response_status_msg
                    .targetCriteria
                    .targetStatusCodes
                    .is_empty()
            {
                return false;
            }

            // targetResponseBodyRegEx should be a valid regex if present.
            if let Some(regex_pattern) = response_status_msg
                .targetCriteria
                .targetResponseBodyRegEx
                .clone()
            {
                if Regex::new(regex_pattern.as_str()).is_err() {
                    return false;
                }
            }

            // targetStatusCodes must be 3 character long, and contain only 0-9 digits or X placeholder token.
            if !response_status_msg
                .targetCriteria
                .targetStatusCodes
                .iter()
                .all(|status_code_pattern| {
                    valid_http_status_code_regex.is_match(status_code_pattern.as_str())
                })
            {
                return false;
            }
        }

        // responseStatusMessages should have at least one success and one error message.
        if !has_success_msg || !has_error_msg {
            return false;
        }

        // requestHeaders should contain Content-Type header if requestBody is not blank.
        if let Some(requestBody) = &self.requestBody {
            if !requestBody.is_empty()
                && !self
                    .requestHeaders
                    .keys()
                    .any(|key| key.eq_ignore_ascii_case(ApiLinkAction::REQUEST_HEADER_CONTENT_TYPE))
            {
                return false;
            }
        }

        // URI should use either HTTP, HTTPS, or PV edge protocol.
        if !Self::is_http_uri(self.uri.clone()) && !Self::is_pv_edge_uri(self.uri.clone()) {
            return false;
        }

        true
    }

    pub fn is_http_uri(api_link_uri: String) -> bool {
        api_link_uri.to_lowercase().starts_with("http://")
            || api_link_uri.to_lowercase().starts_with("https://")
    }

    pub fn is_pv_edge_uri(api_link_uri: String) -> bool {
        Self::extract_pv_edge_uri_path(api_link_uri).is_some()
    }

    pub fn extract_pv_edge_uri_path(api_link_uri: String) -> Option<String> {
        // e.g.: pvedge:/endpoint/path, pvedge:///endpoint/path
        for scheme_prefix in ["pvedge:", "pvedge://"] {
            if let Some(uri_path) = api_link_uri.to_lowercase().strip_prefix(scheme_prefix) {
                let num_domain_slashes = uri_path.chars().take_while(|ch| *ch == '/').count();
                if num_domain_slashes == 1 {
                    return Some(uri_path.to_string());
                }
            }
        }
        None
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl ApiLinkAction {
    pub fn create_with_all_params() -> Self {
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                clientTimeoutInMs: 200,
                isAsync: false,
                responseStatusMessages: vec![
                    ApiLinkResponseStatusMessage {
                        message: String::from("nonRetryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::nonRetryableError,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from(
                                "targetResponseBodyRegEx",
                            )),
                            targetStatusCodes: vec![String::from("4XX")],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("retryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::retryableError,
                        priority: 10,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from(
                                "targetResponseBodyRegEx",
                            )),
                            targetStatusCodes: vec![String::from("429"), String::from("5XX")],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("successMessage"),
                        messageType: ApiLinkResponseStatusMessageType::success,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from(
                                "targetResponseBodyRegEx",
                            )),
                            targetStatusCodes: vec![String::from("2XX")],
                        },
                    },
                ],
            },
            refMarker: String::from("refMarker"),
            requestBody: Option::from(String::from("{\"clientId\":\"Blast\",\"data\":{\"retentionType\":\"AR_OFF\"},\"workflowType\":\"Retention\"}")),
            requestHeaders: HashMap::from([(
                ApiLinkAction::REQUEST_HEADER_CONTENT_TYPE.to_string(),
                String::from("application/json"),
            )]),
            requestMethod: ApiLinkRequestMethod::POST,
            text: Option::from(String::from("linkText")),
            target: String::from("apiLink"),
            uri: String::from("pvedge:///cdp/acquisition/v2/workflow"),
        }
    }

    pub fn create_with_all_params_except_target_status_codes() -> Self {
        let api_link_action = ApiLinkAction::create_with_all_params();
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                responseStatusMessages: vec![
                    ApiLinkResponseStatusMessage {
                        message: String::from("nonRetryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::nonRetryableError,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from(
                                "targetResponseBodyRegEx",
                            )),
                            targetStatusCodes: vec![],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("retryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::retryableError,
                        priority: 10,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from(
                                "targetResponseBodyRegEx",
                            )),
                            targetStatusCodes: vec![],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("successMessage"),
                        messageType: ApiLinkResponseStatusMessageType::success,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from(
                                "targetResponseBodyRegEx",
                            )),
                            targetStatusCodes: vec![],
                        },
                    },
                ],
                ..api_link_action.metadata
            },
            ..api_link_action
        }
    }

    pub fn create_with_required_only_params() -> Self {
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                clientTimeoutInMs: 200,
                isAsync: false,
                responseStatusMessages: vec![
                    ApiLinkResponseStatusMessage {
                        message: String::from("nonRetryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::nonRetryableError,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![String::from("4XX")],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("retryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::retryableError,
                        priority: 10,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![String::from("429"), String::from("5XX")],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("successMessage"),
                        messageType: ApiLinkResponseStatusMessageType::success,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![String::from("2XX")],
                        },
                    },
                ],
            },
            refMarker: String::from("refMarker"),
            requestBody: None,
            requestHeaders: HashMap::new(),
            requestMethod: ApiLinkRequestMethod::POST,
            text: None,
            target: String::from("apiLink"),
            uri: String::from("pvedge:///cdp/acquisition/v2/workflow"),
        }
    }

    pub fn create_with_invalid_client_timeout() -> Self {
        let api_link_action = ApiLinkAction::create_with_required_only_params();
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                clientTimeoutInMs: 0,
                ..api_link_action.metadata
            },
            ..api_link_action
        }
    }

    pub fn create_with_no_success_message() -> Self {
        let api_link_action = ApiLinkAction::create_with_required_only_params();
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                responseStatusMessages: vec![
                    ApiLinkResponseStatusMessage {
                        message: String::from("nonRetryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::nonRetryableError,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![String::from("4XX")],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("retryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::retryableError,
                        priority: 10,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![String::from("429"), String::from("5XX")],
                        },
                    },
                ],
                ..api_link_action.metadata
            },
            ..api_link_action
        }
    }

    pub fn create_with_no_error_message() -> Self {
        let api_link_action = ApiLinkAction::create_with_required_only_params();
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                responseStatusMessages: vec![ApiLinkResponseStatusMessage {
                    message: String::from("successMessage"),
                    messageType: ApiLinkResponseStatusMessageType::success,
                    priority: 1,
                    targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                        targetResponseBodyRegEx: None,
                        targetStatusCodes: vec![String::from("2XX")],
                    },
                }],
                ..api_link_action.metadata
            },
            ..api_link_action
        }
    }

    pub fn create_with_undefined_target_criteria() -> Self {
        let api_link_action = ApiLinkAction::create_with_required_only_params();
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                responseStatusMessages: vec![
                    ApiLinkResponseStatusMessage {
                        message: String::from("nonRetryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::nonRetryableError,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("retryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::retryableError,
                        priority: 10,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("successMessage"),
                        messageType: ApiLinkResponseStatusMessageType::success,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: vec![],
                        },
                    },
                ],
                ..api_link_action.metadata
            },
            ..api_link_action
        }
    }

    pub fn create_with_invalid_target_response_body_regex() -> Self {
        let api_link_action = ApiLinkAction::create_with_all_params();
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                responseStatusMessages: vec![
                    ApiLinkResponseStatusMessage {
                        message: String::from("nonRetryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::nonRetryableError,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from("[")),
                            targetStatusCodes: vec![String::from("4XX")],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("retryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::retryableError,
                        priority: 10,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from("[")),
                            targetStatusCodes: vec![String::from("429"), String::from("5XX")],
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("successMessage"),
                        messageType: ApiLinkResponseStatusMessageType::success,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: Option::from(String::from("[")),
                            targetStatusCodes: vec![String::from("2XX")],
                        },
                    },
                ],
                ..api_link_action.metadata
            },
            ..api_link_action
        }
    }

    pub fn create_with_target_status_codes(status_codes: Vec<String>) -> Self {
        let api_link_action = ApiLinkAction::create_with_required_only_params();
        ApiLinkAction {
            metadata: ApiLinkMetadata {
                responseStatusMessages: vec![
                    ApiLinkResponseStatusMessage {
                        message: String::from("nonRetryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::nonRetryableError,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: status_codes.clone(),
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("retryableErrorMessage"),
                        messageType: ApiLinkResponseStatusMessageType::retryableError,
                        priority: 10,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: status_codes.clone(),
                        },
                    },
                    ApiLinkResponseStatusMessage {
                        message: String::from("successMessage"),
                        messageType: ApiLinkResponseStatusMessageType::success,
                        priority: 1,
                        targetCriteria: ApiLinkResponseStatusMessageTargetCriteria {
                            targetResponseBodyRegEx: None,
                            targetStatusCodes: status_codes.clone(),
                        },
                    },
                ],
                ..api_link_action.metadata
            },
            ..api_link_action
        }
    }

    pub fn create_with_missing_request_headers() -> Self {
        let api_link_action = ApiLinkAction::create_with_all_params();
        ApiLinkAction {
            requestHeaders: HashMap::new(),
            ..api_link_action
        }
    }

    pub fn create_with_uri(uri: String) -> Self {
        let api_link_action = ApiLinkAction::create_with_all_params();
        ApiLinkAction {
            uri: uri,
            ..api_link_action
        }
    }
}

impl ApiLinkResponseStatusMessageTargetCriteria {
    const HTTP_STATUS_CODE_PATTERN_MASK: char = 'x';

    pub fn matches(&self, response_body: Option<String>, response_status_code: i32) -> bool {
        let matches_status_code = self.targetStatusCodes.is_empty()
            || self
                .targetStatusCodes
                .iter()
                .map(|status_code_pattern| status_code_pattern.to_lowercase().replace(Self::HTTP_STATUS_CODE_PATTERN_MASK, "[0-9]"))
                .any(|status_code_regex| {
                    let regex = match Regex::new(status_code_regex.as_str()) {
                        Ok(regex) => regex,
                        Err(err) => {
                            log::error!(
                                "Failed to compile API link action target HTTP status code regex pattern: {}",
                                err
                            );
                            return false;
                        }
                    };
                    regex.is_match(response_status_code.to_string().as_str())
                });

        let mut matches_body = true;
        if let Some(target_body_regex) = &self.targetResponseBodyRegEx {
            let regex = match Regex::new(target_body_regex.as_str()) {
                Ok(regex) => regex,
                Err(err) => {
                    log::error!(
                        "Failed to compile API link action target response body regex pattern: {}",
                        err
                    );
                    return false;
                }
            };
            if let Some(response_body_str) = response_body {
                matches_body = regex.is_match(response_body_str.as_str());
            } else {
                matches_body = false;
            };
        }

        matches_body && matches_status_code
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl ApiLinkResponseStatusMessageTargetCriteria {
    fn create(
        target_response_body_regex: Option<String>,
        target_status_codes: Vec<String>,
    ) -> Self {
        ApiLinkResponseStatusMessageTargetCriteria {
            targetResponseBodyRegEx: target_response_body_regex,
            targetStatusCodes: target_status_codes,
        }
    }
}

impl ApiLinkRequestMethod {
    pub fn to_http_method(&self) -> HttpMethod {
        match *self {
            Self::DELETE => HttpMethod::Delete,
            Self::GET => HttpMethod::Get,
            Self::POST => HttpMethod::Post,
            Self::PUT => HttpMethod::Put,
        }
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(ApiLinkAction::create_with_all_params(), true)]
    #[case(
        ApiLinkAction::create_with_all_params_except_target_status_codes(),
        true
    )]
    #[case(ApiLinkAction::create_with_required_only_params(), true)]
    #[case(ApiLinkAction::create_with_invalid_client_timeout(), false)]
    #[case(ApiLinkAction::create_with_no_success_message(), false)]
    #[case(ApiLinkAction::create_with_no_error_message(), false)]
    #[case(ApiLinkAction::create_with_undefined_target_criteria(), false)]
    #[case(ApiLinkAction::create_with_invalid_target_response_body_regex(), false)]
    #[case(ApiLinkAction::create_with_target_status_codes(vec!["99".to_string()]), false)]
    #[case(ApiLinkAction::create_with_target_status_codes(vec!["9X".to_string()]), false)]
    #[case(ApiLinkAction::create_with_target_status_codes(vec!["20Y".to_string()]), false)]
    #[case(ApiLinkAction::create_with_target_status_codes(vec!["2XY".to_string()]), false)]
    #[case(ApiLinkAction::create_with_target_status_codes(vec!["1000".to_string()]), false)]
    #[case(ApiLinkAction::create_with_target_status_codes(vec!["1XXX".to_string()]), false)]
    #[case(ApiLinkAction::create_with_missing_request_headers(), false)]
    #[case(ApiLinkAction::create_with_uri("ftp://endpoint/path".to_string()), false)]
    #[case(ApiLinkAction::create_with_uri("https://endpoint/path".to_string()), true)]
    #[case(ApiLinkAction::create_with_uri("http://endpoint/path".to_string()), true)]
    #[case(ApiLinkAction::create_with_uri("pvedge:///endpoint/path".to_string()), true)]
    #[case(ApiLinkAction::create_with_uri("pvedge://endpoint/path".to_string()), false)]
    #[case(ApiLinkAction::create_with_uri("pvedge:/endpoint/path".to_string()), true)]
    #[case(ApiLinkAction::create_with_uri("pvedge:endpoint/path".to_string()), false)]
    fn should_determine_if_action_is_valid(
        #[case] api_link_action: ApiLinkAction,
        #[case] is_valid: bool,
    ) {
        assert_eq!(api_link_action.is_valid(), is_valid);
    }

    #[rstest]
    #[case("ftp://endpoint/path".to_string(), None)]
    #[case("https://endpoint/path".to_string(), None)]
    #[case("http://endpoint/path".to_string(), None)]
    #[case("pvedge:///endpoint/path".to_string(), Some("/endpoint/path".to_string()))]
    #[case("pvedge://endpoint/path".to_string(), None)]
    #[case("pvedge:/endpoint/path".to_string(), Some("/endpoint/path".to_string()))]
    #[case("pvedge:endpoint/path".to_string(), None)]
    fn should_extract_pv_edge_url(#[case] input: String, #[case] expected: Option<String>) {
        assert_eq!(ApiLinkAction::extract_pv_edge_uri_path(input), expected);
    }

    #[rstest]
    // With Target Response Status Codes Only
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), None, 201, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), None, 299, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2xx".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2xx".to_string()]), None, 201, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2xx".to_string()]), None, 299, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2x0".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2x0".to_string()]), None, 210, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["20x".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["20x".to_string()]), None, 201, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["x00".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["x00".to_string()]), None, 400, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["200".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["201".to_string()]), None, 201, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["200".to_string(), "1xx".to_string()]), None, 199, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["200".to_string(), "201".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["200".to_string(), "201".to_string()]), None, 201, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), None, 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), None, 401, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), None, 499, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2xx".to_string()]), None, 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2xx".to_string()]), None, 401, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2xx".to_string()]), None, 499, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2x0".to_string()]), None, 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2x0".to_string()]), None, 410, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["20x".to_string()]), None, 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["20x".to_string()]), None, 401, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["x00".to_string()]), None, 201, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["x00".to_string()]), None, 401, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["200".to_string()]), None, 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["201".to_string()]), None, 401, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["200".to_string(), "1xx".to_string()]), None, 201, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["200".to_string(), "201".to_string()]), None, 299, false)]
    // With Target Response Body RegEx
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec!["2XX".to_string()]), Some("123targetPattern123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec!["2XX".to_string()]), Some("123targetPattern123".to_string()), 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec!["2XX".to_string()]), Some("".to_string()), 200, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec!["2XX".to_string()]), None, 200, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec![]), Some("123targetPattern123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec![]), Some("123123".to_string()), 200, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec![]), Some("".to_string()), 200, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("targetPattern".to_string()), vec![]), None, 200, false)]
    // With Empty Target Response Body RegEx
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec!["2XX".to_string()]), Some("123targetPattern123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec!["2XX".to_string()]), Some("123targetPattern123".to_string()), 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec!["2XX".to_string()]), Some("".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec!["2XX".to_string()]), None, 200, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec![]), Some("123targetPattern123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec![]), Some("123123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec![]), Some("".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(Some("".to_string()), vec![]), None, 200, false)]
    // With No Target Response Body RegEx
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), Some("123targetPattern123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), Some("123targetPattern123".to_string()), 400, false)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), Some("".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec!["2XX".to_string()]), None, 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec![]), Some("123targetPattern123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec![]), Some("123123".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec![]), Some("".to_string()), 200, true)]
    #[case(ApiLinkResponseStatusMessageTargetCriteria::create(None, vec![]), None, 200, true)]
    fn should_match_target_criteria(
        #[case] target_criteria: ApiLinkResponseStatusMessageTargetCriteria,
        #[case] response_body: Option<String>,
        #[case] response_status_code: i32,
        #[case] should_match: bool,
    ) {
        assert_eq!(
            target_criteria.matches(response_body, response_status_code),
            should_match
        );
    }
}
