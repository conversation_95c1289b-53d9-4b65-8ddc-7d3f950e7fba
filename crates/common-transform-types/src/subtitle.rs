use std::fmt::{self, Display};

use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(Default, Clone, NetworkParsed, PartialEq)]
#[derive_test_only(Debug)]
#[network(default)]
pub enum SubTitleType {
    #[network(rename = "SEARCH_MESSAGE")]
    SearchMessage,
    #[network(rename = "NO_RESULTS_SEARCH_MESSAGE")]
    NoResultsMessage,
    #[default]
    Unknown,
}

#[derive(Clone, NetworkParsed, PartialEq)]
#[derive_test_only(Debug)]
pub struct SubTitle {
    #[network(rename = "type")]
    pub title_type: SubTitleType,
    pub text: String,
}

impl Display for SubTitle {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.text)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(SubTitleType::SearchMessage, "SEARCH_MESSAGE", "TestString")]
    #[case(
        SubTitleType::NoResultsMessage,
        "NO_RESULTS_SEARCH_MESSAGE",
        "TestString"
    )]
    #[case(SubTitleType::Unknown, "Doesnt exist", "TestString")]
    fn it_deserializes_for_different_types_correctly(
        #[case] title_type: SubTitleType,
        #[case] raw_string: &str,
        #[case] text: &str,
    ) {
        let input_string = format!(r#"{{"type":"{}","text":"{}"}}"#, raw_string, text);

        let result: SubTitle = network_parse_from_str(&input_string).expect("Parsed successfully");
        assert_eq!(
            result,
            SubTitle {
                title_type,
                text: text.into(),
            }
        );
    }

    fn it_impls_to_string() {
        let subtitle = SubTitle {
            title_type: SubTitleType::SearchMessage,
            text: "Test string".into(),
        };

        assert_eq!(subtitle.to_string(), "Test string");
    }
}
