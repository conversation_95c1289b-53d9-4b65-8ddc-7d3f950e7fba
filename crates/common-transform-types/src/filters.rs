use std::fmt::{self, Display};

use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::id::Id;
use network_parser::prelude::*;
use network_parser_derive::*;

use crate::actions::Action;

#[derive(<PERSON><PERSON>ult, <PERSON><PERSON>, NetworkParsed, Hash)]
#[derive_test_only(Debug, PartialEq)]
#[network(default)]
pub enum FilterKeys {
    #[network(rename = "OFFER_FILTER")]
    FreeToMe,
    #[network(rename = "p_n_ways_to_watch")]
    WaysToWatch,
    #[network(rename = "p_n_subscription_id")]
    SubscriptionType,
    #[network(rename = "p_n_entity_type")]
    ContentType,
    #[network(rename = "p_n_feature_browse-bin")]
    Genre,
    #[network(rename = "p_n_video_quality")]
    VideoDefinition,
    #[network(rename = "p_n_feature_twelve_browse-bin")]
    SubtitlesAndClosedCaptioning,
    #[network(rename = "p_n_feature_four_browse-bin")]
    Language,
    #[default]
    Unknown,
}

#[derive(<PERSON><PERSON>, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct FilterItem {
    id: String,
    text: String,
    is_selected: bool,
    action: Action,
}

impl Display for FilterItem {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.text)
    }
}

impl Id for FilterItem {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}
#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct Filter {
    pub filter_key: FilterKeys,
    pub text: String,
    pub items: Vec<FilterItem>,
}

impl Display for Filter {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.text)
    }
}

impl Id for Filter {
    type Id = FilterKeys;

    fn id(&self) -> &Self::Id {
        &self.filter_key
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    use rstest::rstest;

    #[rstest]
    #[case(FilterKeys::FreeToMe, "OFFER_FILTER")]
    #[case(FilterKeys::WaysToWatch, "p_n_ways_to_watch")]
    #[case(FilterKeys::SubscriptionType, "p_n_subscription_id")]
    #[case(FilterKeys::ContentType, "p_n_entity_type")]
    #[case(FilterKeys::Genre, "p_n_feature_browse-bin")]
    #[case(FilterKeys::VideoDefinition, "p_n_video_quality")]
    #[case(
        FilterKeys::SubtitlesAndClosedCaptioning,
        "p_n_feature_twelve_browse-bin"
    )]
    #[case(FilterKeys::Language, "p_n_feature_four_browse-bin")]
    #[case(FilterKeys::Unknown, "some_unknown-feature")]
    fn it_deserializes_filter_keys_correctly(#[case] filter_key: FilterKeys, #[case] text: &str) {
        let input_string = format!(
            r#"{{"filterKey":"{}","text":"{}", "items": []}}"#,
            text, text
        );
        let result: Filter = network_parse_from_str(&input_string).expect("Parsed successfully");
        assert_eq!(
            result,
            Filter {
                filter_key,
                text: text.into(),
                items: vec![],
            }
        );
    }

    #[test]
    fn filters_impl_id() {
        let filter = Filter {
            filter_key: FilterKeys::FreeToMe,
            text: "Free to Me".into(),
            items: vec![],
        };
        assert_eq!(filter.id(), &FilterKeys::FreeToMe);
    }

    #[test]
    fn filters_can_display() {
        let filter = Filter {
            filter_key: FilterKeys::FreeToMe,
            text: "Free to Me".into(),
            items: vec![],
        };
        assert_eq!(filter.to_string(), "Free to Me");
    }

    #[test]
    fn should_parse_filter_items() {
        let input_string = r#"{
            "filterKey": "p_n_video_quality",
            "text": "Video quality",
            "items": [
                {
                    "id": "1",
                    "text": "UHD",
                    "isSelected": false,
                    "action": {
                        "serviceToken": "TOKEN_1",
                        "refMarker": "sr_fle_2_slct",
                        "text": null,
                        "analytics": {
                            "refMarker": "sr_fle_2_slct"
                        },
                        "pageType": "search",
                        "pageId": "flex",
                        "target": "search"
                    }
                },
                {
                    "id": "2",
                    "text": "HD",
                    "isSelected": true,
                    "action": {
                        "serviceToken": "TOKEN_2",
                        "refMarker": "sr_fle_2_slct",
                        "text": null,
                        "analytics": {
                            "refMarker": "sr_fle_2_slct"
                        },
                        "pageType": "search",
                        "pageId": "flex",
                        "target": "search"
                    }
                }
            ]
        }"#;

        let result: Filter = network_parse_from_str(input_string).expect("Parsed successfully");
        assert_eq!(result.items.len(), 2);

        let item_one = &result.items[0];
        assert_eq!(item_one.text, "UHD");
        assert_eq!(item_one.is_selected, false);

        let item_two = &result.items[1];
        assert_eq!(item_two.text, "HD");
        assert_eq!(item_two.is_selected, true);
    }

    #[test]
    fn items_impl_id() {
        let filter = FilterItem {
            id: "1".into(),
            text: "UHD".into(),
            is_selected: false,
            action: Action::create_client_default(),
        };
        assert_eq!(filter.id(), "1");
    }

    #[test]
    fn items_can_display() {
        let filter = FilterItem {
            id: "1".into(),
            text: "UHD".into(),
            is_selected: false,
            action: Action::create_client_default(),
        };
        assert_eq!(filter.to_string(), "UHD");
    }
}
