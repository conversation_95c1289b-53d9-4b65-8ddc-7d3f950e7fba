use crate::{containers::ContainerWithResiliency, page_metadata::PageMetadata};
use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::*;

#[derive(<PERSON>lone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct CollectionResponse {
    pub container_list: Vec<ContainerWithResiliency>,
    pub page_metadata: PageMetadata,
}
