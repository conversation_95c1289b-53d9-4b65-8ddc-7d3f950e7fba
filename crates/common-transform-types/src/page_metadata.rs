#![allow(nonstandard_style)]

use cfg_test_attr_derive::{cfg_test_only, derive_test_only};
use ignx_compositron::serde::de::AsyncDeserialize;
use network_parser::prelude::*;
use network_parser_derive::*;
#[cfg_test_only]
use serde::Serialize;

use crate::{container_items::IconBadges, image::ImageV2, resiliency::WithResiliency};

#[derive(NetworkParsed, AsyncDeserialize, Clone, Default)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum PageMetadataType {
    #[network(rename = "PageMetadata")]
    #[ignx_serde(rename = "PageMetadata")]
    #[default]
    Default,
    SelectedPillPageMetadata,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize, Default)]
pub struct PageMetadata {
    pub title: Option<String>,
    pub logoImage: Option<ImageV2>,
    pub entitlementIntent: Option<String>,
    pub navNode: Option<String>,
    #[network(default)]
    #[ignx_serde(default)]
    pub locationDependentPage: bool,
    #[network(default)]
    #[ignx_serde(default)]
    pub showVoiceFilters: bool,
    #[network(default)]
    #[ignx_serde(default)]
    pub persistentTitleOrLogo: bool,
    #[network(rename = "type")]
    #[ignx_serde(rename = "type")]
    pub pageMetadataType: Option<PageMetadataType>,
    pub badge: Option<WithResiliency<IconBadges>>,
    pub titleImage: Option<String>,
    pub selectedPill: Option<String>,
    pub displayMiniDetails: Option<bool>,
}
