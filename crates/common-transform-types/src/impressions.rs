use std::collections::HashMap;

use crate::actions::TransitionAction;
use crate::container_items::{
    BonusScheduleCard, ChannelCard, GenericTitleCard, HasCarouselItemData,
    HasTitleCardBaseMetadata, HeroCard, LinearAiringCard, LinearStationCard, LinkCard,
    LiveLinearCard, MovieCard, SeriesCard, ShowCard, SportsCard, VodExtraContentCard,
};
use crate::containers::{CommonCarouselTitleCardItem, GetTransitionAction, SeeMoreLink};

const TITLE_CARD: &str = "titleCard";
const LINK_CARD: &str = "imageTextLink";
// hero title and channel card fallbacks determined from CSM data reported from react
// see worklog of ticket https://t.corp.amazon.com/D154838694/communication
const HERO_TITLE_CARD: &str = "heroTitle";
const CHANNEL_CARD: &str = "channel";
const SPORTS_CARD: &str = "sportsCard";
const LINEAR_AIRING_CARD: &str = "linearAiringCard";

pub trait GetCreativeId {
    fn get_creative_id(&self) -> Option<String>;
}

impl GetCreativeId for VodExtraContentCard {
    fn get_creative_id(&self) -> Option<String> {
        self.get_title_card_base_metadata().coverImage.clone()
    }
}

impl GetCreativeId for BonusScheduleCard {
    fn get_creative_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.coverImage.clone()
    }
}

impl GetCreativeId for CommonCarouselTitleCardItem {
    fn get_creative_id(&self) -> Option<String> {
        self.get_base_title_card_metadata().coverImage.clone()
    }
}

impl GetCreativeId for LinearStationCard {
    fn get_creative_id(&self) -> Option<String> {
        self.image.clone()
    }
}

impl GetCreativeId for LinearAiringCard {
    fn get_creative_id(&self) -> Option<String> {
        self.linear_airing_item_data.image.clone()
    }
}

impl GetCreativeId for LinkCard {
    fn get_creative_id(&self) -> Option<String> {
        self.imageUrl.clone()
    }
}

impl GetCreativeId for LiveLinearCard {
    fn get_creative_id(&self) -> Option<String> {
        self.image.clone()
    }
}

impl GetCreativeId for SeriesCard {
    fn get_creative_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.coverImage.clone()
    }
}

impl GetCreativeId for ShowCard {
    fn get_creative_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.coverImage.clone()
    }
}

impl GetCreativeId for GenericTitleCard {
    fn get_creative_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.coverImage.clone()
    }
}

impl GetCreativeId for MovieCard {
    fn get_creative_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.coverImage.clone()
    }
}

impl GetCreativeId for SeeMoreLink {
    fn get_creative_id(&self) -> Option<String> {
        self.linkImage.clone()
    }
}

impl GetCreativeId for HeroCard {
    fn get_creative_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.heroImage.clone()
    }
}

impl GetCreativeId for ChannelCard {
    fn get_creative_id(&self) -> Option<String> {
        self.heroImage.clone()
    }
}

impl GetCreativeId for SportsCard {
    fn get_creative_id(&self) -> Option<String> {
        self.backgroundImage.clone()
    }
}

pub trait GetContentId {
    fn get_content_id(&self) -> Option<String>;
}

impl GetContentId for VodExtraContentCard {
    fn get_content_id(&self) -> Option<String> {
        self.get_title_card_base_metadata().gti.clone()
    }
}

impl GetContentId for BonusScheduleCard {
    fn get_content_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.gti.clone()
    }
}

impl GetContentId for CommonCarouselTitleCardItem {
    fn get_content_id(&self) -> Option<String> {
        self.get_base_title_card_metadata().gti.clone()
    }
}

impl GetContentId for LinearStationCard {
    fn get_content_id(&self) -> Option<String> {
        self.gti.clone()
    }
}

impl GetContentId for LinearAiringCard {
    fn get_content_id(&self) -> Option<String> {
        self.linear_airing_attributes.gti.clone()
    }
}

impl GetContentId for LinkCard {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

impl GetContentId for LiveLinearCard {
    fn get_content_id(&self) -> Option<String> {
        self.gti.clone()
    }
}

impl GetContentId for SeriesCard {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

impl GetContentId for ShowCard {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

impl GetContentId for GenericTitleCard {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

impl GetContentId for MovieCard {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

impl GetContentId for SeeMoreLink {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

impl GetContentId for HeroCard {
    fn get_content_id(&self) -> Option<String> {
        self.titleCardBaseMetadata.gti.clone()
    }
}

impl GetContentId for ChannelCard {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

impl GetContentId for SportsCard {
    fn get_content_id(&self) -> Option<String> {
        let action = self.get_transition_action()?;
        get_content_id(action)
    }
}

fn get_content_id(action: &TransitionAction) -> Option<String> {
    match action {
        TransitionAction::detail(action) | TransitionAction::legacyDetail(action) => {
            Some(action.pageId.clone())
        }
        TransitionAction::landing(action)
        | TransitionAction::registration(action)
        | TransitionAction::settings(action)
        | TransitionAction::search(action)
        | TransitionAction::clientSearch(action)
        | TransitionAction::watchList(action)
        | TransitionAction::yvl(action)
        | TransitionAction::primeSignUp(action)
        | TransitionAction::tournament(action)
        | TransitionAction::category(action)
        | TransitionAction::store(action)
        | TransitionAction::live(action)
        | TransitionAction::freeWithAds(action)
        | TransitionAction::myStuff(action)
        | TransitionAction::collection(action)
        | TransitionAction::testPlayground(action) => {
            Some(format!("{}:{}", action.pageType, action.pageId))
        }
        TransitionAction::player(action) => Some(action.uri.clone()),
        _ => None,
    }
}

pub trait GetWidgetType {
    fn get_widget_type(&self) -> String;
}

impl GetWidgetType for VodExtraContentCard {
    fn get_widget_type(&self) -> String {
        self.carousel_item_data()
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for BonusScheduleCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for CommonCarouselTitleCardItem {
    fn get_widget_type(&self) -> String {
        self.get_carousel_card_metadata()
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for LinearStationCard {
    fn get_widget_type(&self) -> String {
        self.widgetType.clone()
    }
}

impl GetWidgetType for LinearAiringCard {
    fn get_widget_type(&self) -> String {
        self.carousel_item_data()
            .widgetType
            .clone()
            .unwrap_or_else(|| LINEAR_AIRING_CARD.to_owned())
    }
}

impl GetWidgetType for LinkCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| LINK_CARD.to_owned())
    }
}

impl GetWidgetType for LiveLinearCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for SeriesCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for ShowCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for GenericTitleCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for MovieCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for SeeMoreLink {
    fn get_widget_type(&self) -> String {
        LINK_CARD.to_owned()
    }
}

impl GetWidgetType for HeroCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| HERO_TITLE_CARD.to_owned())
    }
}

impl GetWidgetType for ChannelCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| CHANNEL_CARD.to_owned())
    }
}

impl GetWidgetType for SportsCard {
    fn get_widget_type(&self) -> String {
        self.carouselCardMetadata
            .widgetType
            .clone()
            .unwrap_or_else(|| SPORTS_CARD.to_owned())
    }
}

pub fn get_benefit_id(action: &TransitionAction) -> Option<String> {
    match action {
        TransitionAction::landing(action) => Some(action.pageId.clone()),
        TransitionAction::browse(action) => Some(action.swiftAction.pageId.clone()),
        TransitionAction::signUp(action) => Some(action.benefitId.clone()),
        TransitionAction::primeSignUp(_) => Some("Prime".to_string()),
        _ => None,
    }
}

pub fn get_ref_marker(action: &TransitionAction) -> String {
    match action {
        TransitionAction::landing(action)
        | TransitionAction::registration(action)
        | TransitionAction::settings(action)
        | TransitionAction::search(action)
        | TransitionAction::clientSearch(action)
        | TransitionAction::watchList(action)
        | TransitionAction::yvl(action)
        | TransitionAction::legacyDetail(action)
        | TransitionAction::primeSignUp(action)
        | TransitionAction::detail(action)
        | TransitionAction::tournament(action)
        | TransitionAction::category(action)
        | TransitionAction::store(action)
        | TransitionAction::live(action)
        | TransitionAction::freeWithAds(action)
        | TransitionAction::myStuff(action)
        | TransitionAction::collection(action)
        | TransitionAction::linearStationDetail(action)
        | TransitionAction::testPlayground(action) => action.refMarker.clone(),
        TransitionAction::playbackGroup(action) => action.refMarker.clone(),
        TransitionAction::consent(action) => action.refMarker.clone(),
        TransitionAction::signUp(action) => action.refMarker.clone(),
        TransitionAction::player(action) => action.refMarker.clone(),
        TransitionAction::playback(action) => action.refMarker.clone(),
        TransitionAction::acquisition(action) => action.refMarker.clone(),
        TransitionAction::browse(action) => action.swiftAction.refMarker.clone(),
        TransitionAction::manageSubscription(action) => action.refMarker.clone(),
        TransitionAction::profileSettings(action) => action.refMarker.clone(),
        TransitionAction::openModal(action) => action.refMarker.clone(),
        TransitionAction::fuseOfferActivation(action)
        | TransitionAction::sportsFavorites(action)
        | TransitionAction::removeFromFavorites(action)
        | TransitionAction::changeHomeRegion(action) => action.refMarker.clone(),
        TransitionAction::primeRetention(action) => action.refMarker.clone(),
        TransitionAction::sportsSchedule(action) => action.refMarker.clone(),
        TransitionAction::liveSportsNoOp => "".to_string(),
        TransitionAction::apiLink(action) => action.refMarker.clone(),
    }
}

pub trait GetImpressionsContentType {
    fn get_csm_content_type(&self) -> Option<String>;
}

impl GetImpressionsContentType for VodExtraContentCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for BonusScheduleCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for CommonCarouselTitleCardItem {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for LinearStationCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for LinearAiringCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for LinkCard {
    fn get_csm_content_type(&self) -> Option<String> {
        Some("LINK".to_owned())
    }
}

impl GetImpressionsContentType for SeeMoreLink {
    fn get_csm_content_type(&self) -> Option<String> {
        Some("LINK".to_owned())
    }
}

impl GetImpressionsContentType for LiveLinearCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for SeriesCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for ShowCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for GenericTitleCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for MovieCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for HeroCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for ChannelCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

impl GetImpressionsContentType for SportsCard {
    fn get_csm_content_type(&self) -> Option<String> {
        None
    }
}

pub fn get_carousel_analytics(analytics: &HashMap<String, String>) -> Option<String> {
    analytics.get("ClientSideMetrics").cloned()
}

/// Extracts the analytics field (a map) from the action, for actions that have one.
/// Actions with target: playback use the [`crate::actions::TitlePlaybackAction`] type, which does
///     not have an analytics field.
/// Actions with target: acquisition use the [`crate::actions::TitleAcquisitionAction`] type, which
///     does not have an analytics field.
/// Actions with target: manageSubscription use the [`crate::actions::ManageSubscriptionAction`]
///     type, which does not have an analytics field.
/// Actions with target: profileSettings use the [`crate::actions::ProfileSettingsAction`] type,
///     which does not have an analytics field.
/// Actions with target: openModal use the [`crate::actions::TitleOpenModalAction`] type, which
///     does not have an analytics field.
pub fn get_analytics(action: &TransitionAction) -> Option<HashMap<String, String>> {
    match action {
        TransitionAction::landing(action)
        | TransitionAction::registration(action)
        | TransitionAction::settings(action)
        | TransitionAction::search(action)
        | TransitionAction::clientSearch(action)
        | TransitionAction::watchList(action)
        | TransitionAction::yvl(action)
        | TransitionAction::legacyDetail(action)
        | TransitionAction::primeSignUp(action)
        | TransitionAction::detail(action)
        | TransitionAction::tournament(action)
        | TransitionAction::category(action)
        | TransitionAction::store(action)
        | TransitionAction::live(action)
        | TransitionAction::freeWithAds(action)
        | TransitionAction::myStuff(action)
        | TransitionAction::collection(action)
        | TransitionAction::linearStationDetail(action)
        | TransitionAction::testPlayground(action) => Some(action.analytics.clone()),
        TransitionAction::signUp(action) => Some(action.analytics.clone()),
        TransitionAction::player(action) => action.analytics.clone(),
        TransitionAction::browse(action) => Some(action.swiftAction.analytics.clone()),
        TransitionAction::consent(_)
        | TransitionAction::playback(_)
        | TransitionAction::playbackGroup(_)
        | TransitionAction::acquisition(_)
        | TransitionAction::manageSubscription(_)
        | TransitionAction::profileSettings(_)
        | TransitionAction::openModal(_)
        | TransitionAction::fuseOfferActivation(_)
        | TransitionAction::changeHomeRegion(_)
        | TransitionAction::sportsFavorites(_)
        | TransitionAction::removeFromFavorites(_)
        | TransitionAction::sportsSchedule(_) => None,
        TransitionAction::primeRetention(_) => None,
        TransitionAction::liveSportsNoOp => None,
        TransitionAction::apiLink(_) => None,
    }
}
