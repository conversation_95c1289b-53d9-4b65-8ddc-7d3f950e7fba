use ignx_compositron::id::Id;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(Clone, NetworkParsed, Debug, Eq, PartialEq, Hash)]
pub struct Suggestion {
    pub id: String,
    pub text: String,
    #[network(rename = "refMarker")]
    pub ref_marker: String,
}

impl Id for Suggestion {
    type Id = String;

    fn id(&self) -> &String {
        &self.id
    }
}

#[derive(Clone, NetworkParsed, Hash, PartialEq, Eq, Debug)]
#[network(camelCaseAll)]
pub struct SuggestionsResponse {
    pub suggestions: Vec<Suggestion>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn suggestions_should_provide_an_id() {
        let suggestion = Suggestion {
            id: "TestId".into(),
            text: "Some text".into(),
            ref_marker: "REF_MARKER".into(),
        };

        assert_eq!(suggestion.id(), "TestId");
    }
}
