#![allow(nonstandard_style)]
use crate::actions::{Action, ClientAction};
use cfg_test_attr_derive::{cfg_test_only, derive_test_only};
use ignx_compositron::{id::Id, serde::de::AsyncDeserialize};
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
#[cfg_test_only]
use serde::Serialize;
use serde_json::Value;

pub use crate::item_metadata::*;

/// Trait for types that have [`TitleCardBaseMetadata`]
pub trait HasTitleCardBaseMetadata {
    /// Get the [`TitleCardBaseMetadata`] for this card
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata;
}

/// Trait for types that have [`LinearAttributes`]
pub trait HasLinearAttributes {
    /// Get the [`LinearAttributes`] for this card if available
    fn linear_attributes(&self) -> Option<&LinearAttributes>;
}

/// Trait for types that have [`CarouselItemData`]
pub trait HasCarouselItemData {
    /// Get the [`CarouselItemData`] for this card
    fn carousel_item_data(&self) -> &CarouselItemData;
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct GenericTitleCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleCardBaseMetadata: TitleCardBaseMetadata,
}

impl Id for GenericTitleCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasTitleCardBaseMetadata for GenericTitleCard {
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata {
        &self.titleCardBaseMetadata
    }
}

impl HasLinearAttributes for GenericTitleCard {
    fn linear_attributes(&self) -> Option<&LinearAttributes> {
        None
    }
}

impl HasCarouselItemData for GenericTitleCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct SeriesCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleCardBaseMetadata: TitleCardBaseMetadata,
    pub seasonNumber: Option<u32>,
    pub numberOfSeasons: Option<u32>,
    pub showName: Option<String>,
    pub episodeNumber: Option<u32>,
    pub episodicSynopsis: Option<String>,
    pub linearAttributes: Option<LinearSeriesAttributes>,
}

impl Id for SeriesCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasTitleCardBaseMetadata for SeriesCard {
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata {
        &self.titleCardBaseMetadata
    }
}

impl HasLinearAttributes for SeriesCard {
    fn linear_attributes(&self) -> Option<&LinearAttributes> {
        self.linearAttributes.as_ref().map(|i| &i.linearAttributes)
    }
}

impl HasCarouselItemData for SeriesCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ShowCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleCardBaseMetadata: TitleCardBaseMetadata,
    pub numberOfSeasons: Option<u32>,
    pub showName: Option<String>,
}

impl Id for ShowCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasTitleCardBaseMetadata for ShowCard {
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata {
        &self.titleCardBaseMetadata
    }
}

impl HasLinearAttributes for ShowCard {
    fn linear_attributes(&self) -> Option<&LinearAttributes> {
        None
    }
}

impl HasCarouselItemData for ShowCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct MovieCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleCardBaseMetadata: TitleCardBaseMetadata,
    pub linearAttributes: Option<LinearAttributes>,
}

impl Id for MovieCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasTitleCardBaseMetadata for MovieCard {
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata {
        &self.titleCardBaseMetadata
    }
}

impl HasLinearAttributes for MovieCard {
    fn linear_attributes(&self) -> Option<&LinearAttributes> {
        self.linearAttributes.as_ref()
    }
}

impl HasCarouselItemData for MovieCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct EventCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleCardBaseMetadata: TitleCardBaseMetadata,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub eventMetadata: EventMetadata,
}

impl Id for EventCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasTitleCardBaseMetadata for EventCard {
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata {
        &self.titleCardBaseMetadata
    }
}

impl HasLinearAttributes for EventCard {
    fn linear_attributes(&self) -> Option<&LinearAttributes> {
        None
    }
}

impl HasCarouselItemData for EventCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveLinearCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    pub gti: Option<String>,
    pub rating: Option<String>,
    pub image: Option<String>,
    pub swiftId: Option<String>,
    pub schedule: Vec<ScheduledShow>,
    pub contextualActions: Option<Vec<ClientAction>>,
    pub entitlementMessaging: Option<EntitlementMessaging>,
}

impl Id for LiveLinearCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasCarouselItemData for LiveLinearCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, Default)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinearStationReactions {
    pub isFavorite: bool,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinearStationCard {
    pub action: Option<Action>,
    #[network(default)]
    #[ignx_serde(default)]
    pub actions: Vec<Action>,
    pub contextualActions: Option<Vec<ClientAction>>,
    pub deferredAction: Option<Action>,
    pub entitlementMessaging: Option<EntitlementMessaging>,
    pub gti: Option<String>,
    pub image: Option<String>,
    pub name: Option<String>,
    pub rating: Option<String>,
    pub schedule: Vec<LinearAiringItem>,
    pub stationLogo: Option<String>,
    pub widgetType: String,
    pub reactions: Option<LinearStationReactions>,
}

impl Id for LinearStationCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        &self.gti
    }
}

// corresponds to https://code.amazon.com/packages/AVLivingRoomBorgJvmTransform/blobs/ab52e1ef54be649c53deefad822563a5316aa53c/--/src/com/amazon/avlrc/transform/parser/swift/containerItem/model/ContainerItemModels.kt#L379
#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinearAiringCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub linear_airing_item_data: LinearAiringItem,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub linear_airing_attributes: LinearAiringAttributes,
    #[network(default)]
    #[ignx_serde(default)]
    pub contextualActions: Vec<ClientAction>,
    pub deferredAction: Option<Action>,
}

impl HasCarouselItemData for LinearAiringCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

impl Id for LinearAiringCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carousel_item_data().id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinkCard {
    // TODO: Refactor this so that devs do not inadvertently access LinkCard#carouselCardMetadata#synopsis, which does not exist in the transform.
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    pub imageAlternateText: Option<String>,
    pub headerText: Option<String>,
    pub imageUrl: Option<String>,
    pub backgroundImageUrl: Option<String>,
    pub isEntitled: Option<bool>,
    pub offerText: Option<String>,
    pub overlayTextPosition: Option<OverlayTextPosition>,
    pub logoImageUrl: Option<String>,
    pub entitlementMessaging: Option<EntitlementMessaging>,
    pub regulatoryLabel: Option<String>,
    pub description: Option<String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl LinkCard {
    pub fn populated_default() -> LinkCard {
        LinkCard {
            carouselCardMetadata: CarouselItemData::populated_default(),
            imageAlternateText: Some("alternate text".to_string()),
            headerText: Some("header text".to_string()),
            imageUrl: Some("https://www.image.com/image-url.jpg".to_string()),
            backgroundImageUrl: Some("https://www.image.com/background-image-url.jpg".to_string()),
            isEntitled: Some(false),
            offerText: Some("Offer".to_string()),
            overlayTextPosition: Some(OverlayTextPosition::Bottom_Left),
            logoImageUrl: Some("https://www.image.com/logo-image-url.jpg".to_string()),
            entitlementMessaging: Some(EntitlementMessaging::populated_default()),
            regulatoryLabel: Some("15".to_string()),
            description: Some("description".to_string()),
        }
    }

    pub fn with_carousel_card_metadata(self, carouselCardMetadata: CarouselItemData) -> Self {
        Self {
            carouselCardMetadata,
            ..self
        }
    }

    pub fn with_image_url(self, url: Option<String>) -> Self {
        Self {
            imageUrl: url,
            ..self
        }
    }

    pub fn with_text_overlay_position(self, pos: Option<OverlayTextPosition>) -> Self {
        Self {
            overlayTextPosition: pos,
            ..self
        }
    }

    pub fn with_entitlement_messaging(
        self,
        entitlementMessaging: Option<EntitlementMessaging>,
    ) -> Self {
        Self {
            entitlementMessaging,
            ..self
        }
    }
}

impl Id for LinkCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasCarouselItemData for LinkCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

pub type BonusScheduleCard = EventCard;

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct VodExtraContentCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleCardBaseMetadata: TitleCardBaseMetadata,
}

impl Id for VodExtraContentCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasTitleCardBaseMetadata for VodExtraContentCard {
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata {
        &self.titleCardBaseMetadata
    }
}

impl HasLinearAttributes for VodExtraContentCard {
    fn linear_attributes(&self) -> Option<&LinearAttributes> {
        None
    }
}

impl HasCarouselItemData for VodExtraContentCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct HeroCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub titleCardBaseMetadata: TitleCardBaseMetadata,
    pub providerLogoImageMetadata: Option<IndividualImageMetadata>,
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub eventMetadata: EventMetadata,
    pub moreDetailsAction: Option<Action>,
    pub callToAction: Option<Action>,
    pub tnfProperty: Option<TNFProperties>,
    pub tournamentIcid: Option<String>,
    pub regulatoryLabel: Option<String>,
}

impl Id for HeroCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasTitleCardBaseMetadata for HeroCard {
    fn get_title_card_base_metadata(&self) -> &TitleCardBaseMetadata {
        &self.titleCardBaseMetadata
    }
}

impl HasCarouselItemData for HeroCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ChannelCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    pub headerText: Option<String>,
    pub heroImage: Option<String>,
    pub titleLogoImage: Option<String>,
    pub entitlementMessaging: Option<EntitlementMessaging>,
    pub regulatoryLabel: Option<String>,
    pub image: Option<String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl ChannelCard {
    pub fn populated_default() -> Self {
        ChannelCard {
            carouselCardMetadata: CarouselItemData::populated_default(),
            headerText: Some("header text".into()),
            titleLogoImage: Some("title logo image".into()),
            heroImage: Some("hero image".into()),
            image: Some("image".into()),
            regulatoryLabel: None,
            entitlementMessaging: Some(EntitlementMessaging::populated_default()),
        }
    }

    pub fn with_carousel_card_metadata(self, carouselCardMetadata: CarouselItemData) -> Self {
        Self {
            carouselCardMetadata,
            ..self
        }
    }

    pub fn with_image_url(self, url: Option<String>) -> Self {
        Self { image: url, ..self }
    }
}

impl Id for ChannelCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

impl HasCarouselItemData for ChannelCard {
    fn carousel_item_data(&self) -> &CarouselItemData {
        &self.carouselCardMetadata
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct SportsCard {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub carouselCardMetadata: CarouselItemData,
    pub backgroundImage: Option<String>,
    pub backgroundColor: Option<RGBColor>,
    pub isFavorited: Option<bool>,
    pub entityType: Option<String>,
}

impl Id for SportsCard {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        self.carouselCardMetadata.id()
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl SportsCard {
    pub fn populated_default() -> SportsCard {
        SportsCard {
            carouselCardMetadata: CarouselItemData::populated_default(),
            backgroundImage: Some("https://www.image.com/sport-image.jpg".to_string()),
            backgroundColor: Some(RGBColor {
                red: 255,
                green: 0,
                blue: 0,
            }),
            isFavorited: Some(false),
            entityType: Some("TEAM".to_string()),
        }
    }

    pub fn with_carousel_card_metadata(self, carouselCardMetadata: CarouselItemData) -> Self {
        Self {
            carouselCardMetadata,
            ..self
        }
    }

    pub fn with_background_image(self, url: Option<String>) -> Self {
        Self {
            backgroundImage: url,
            ..self
        }
    }

    pub fn with_background_color(self, color: Option<RGBColor>) -> Self {
        Self {
            backgroundColor: color,
            ..self
        }
    }

    pub fn with_is_favorited(self, is_favorited: Option<bool>) -> Self {
        Self {
            isFavorited: is_favorited,
            ..self
        }
    }

    pub fn with_entity_type(self, entity_type: Option<String>) -> Self {
        Self {
            entityType: entity_type,
            ..self
        }
    }
}
