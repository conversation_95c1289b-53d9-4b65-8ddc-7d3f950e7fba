#![allow(nonstandard_style)]
use ignx_compositron::text::{LocalizedText, SubstitutionParameters, TextContent};
use network_parser::custom_network_parser;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde_json::Value;
use std::collections::HashMap;

#[derive(Clone)]
#[cfg_attr(
    any(
        debug_assertions,
        test,
        feature = "debug_impl",
        not(target_arch = "wasm32")
    ),
    derive(Debug, PartialEq)
)]
pub struct InlineMessage {
    pub message: TextContent,
    pub messageType: InlineMessageType,
}

custom_network_parser!(InlineMessage, |value: &Value| {
    let react_inline_message: ReactInlineMessage = value.try_into()?;
    let inline_message: InlineMessage = react_inline_message.into();
    Ok(inline_message)
});

impl From<ReactInlineMessage> for InlineMessage {
    fn from(value: ReactInlineMessage) -> Self {
        InlineMessage {
            message: match value.message {
                ReactTextContent::ReactLocalizedText(ReactLocalizedText {
                    stringId,
                    substitutionParams,
                }) => TextContent::LocalizedText(LocalizedText {
                    string_id: stringId.into(),
                    substitution_parameters: substitutionParams.map(|params| {
                        SubstitutionParameters(
                            params
                                .iter()
                                .map(|(key, value)| {
                                    (key.clone(), TextContent::String(value.clone()))
                                })
                                .collect(),
                        )
                    }),
                }),
                ReactTextContent::ReactPlainText(ReactPlainText { value }) => {
                    TextContent::String(value)
                }
            },
            messageType: value.messageType,
        }
    }
}

#[derive(NetworkParsed, Clone, Copy)]
#[cfg_attr(
    any(
        debug_assertions,
        test,
        feature = "debug_impl",
        not(target_arch = "wasm32")
    ),
    derive(Debug, PartialEq)
)]
pub enum InlineMessageType {
    PROFILES_SELECTION_PAGE_ERROR,
    PROFILES_SELECTION_PAGE_SUCCESS,
    // TODO: enable as needed
    // PROFILES_MANAGEMENT_PAGE_ERROR,
    // PROFILES_MANAGEMENT_PAGE_SUCCES,
    // NO_BROWSE_CONTENT,
    // ACQUISITION_INPUT_ERROR,
    // ACQUISITION_INPUT_SUCCESS,
    // ACQUISITION_ERROR,
    // ACQUISITION_SUCCESS,
    // RECOMMENDATIONS_SETTINGS_PAGE_ERROR,
    // FALLBACK_HOMEPAGE_NOTIFICATION,
    // PIN_DIALOG_ERROR
}

#[derive(NetworkParsed)]
struct ReactInlineMessage {
    pub message: ReactTextContent,
    pub messageType: InlineMessageType,
}

enum ReactTextContent {
    ReactLocalizedText(ReactLocalizedText),
    ReactPlainText(ReactPlainText),
}

custom_network_parser!(ReactTextContent, |value: &Value| {
    let localized_text: ParserReturnType<ReactLocalizedText> = value.try_into();
    if let Ok(local_text) = localized_text {
        return Ok(ReactTextContent::ReactLocalizedText(local_text));
    }

    let plain_text: ParserReturnType<ReactPlainText> = value.try_into();
    if let Ok(plain_text) = plain_text {
        return Ok(ReactTextContent::ReactPlainText(plain_text));
    }

    Err("Unable to find corresponding match for ReactTextContent".to_string())
});

#[derive(NetworkParsed)]
struct ReactLocalizedText {
    stringId: String,
    substitutionParams: Option<HashMap<String, String>>,
}

#[derive(NetworkParsed)]
struct ReactPlainText {
    value: String,
}

#[cfg(test)]
mod test {
    use ignx_compositron::text::LocalizedText;

    use super::*;

    #[test]
    fn it_deserializes_localized_text() {
        let _eg = r#"{"messageType":"PROFILES_SELECTION_PAGE_SUCCESS","message":{"type":"localized","stringId":"AV_LRC_PROFILES_CREATE_SUCCESS","substitutionParams":{"a":"b"}}}"#;

        let expected = InlineMessage {
            message: TextContent::LocalizedText(LocalizedText {
                string_id: "AV_LRC_PROFILES_CREATE_SUCCESS".into(),
                substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                    "a".to_owned(),
                    TextContent::String("b".to_owned()),
                )]))),
            }),
            messageType: InlineMessageType::PROFILES_SELECTION_PAGE_SUCCESS,
        };

        let inline_message: InlineMessage =
            network_parse_from_str(_eg).expect("Parsed successfully");
        assert_eq!(inline_message, expected);
    }

    #[test]
    fn it_deserializes_plain_text() {
        let _eg = r#"{"messageType":"PROFILES_SELECTION_PAGE_SUCCESS","message":{"type":"plain","value":"Profile has been created."}}"#;

        let expected = InlineMessage {
            message: TextContent::String("Profile has been created.".into()),
            messageType: InlineMessageType::PROFILES_SELECTION_PAGE_SUCCESS,
        };

        let inline_message: InlineMessage =
            network_parse_from_str(_eg).expect("Parsed successfully");
        assert_eq!(inline_message, expected);
    }
}
