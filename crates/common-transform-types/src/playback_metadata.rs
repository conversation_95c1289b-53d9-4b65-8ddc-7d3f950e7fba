#![allow(nonstandard_style)]
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::serde::de::AsyncDeserialize;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::*;
use serde_json::Value;
use strum::EnumString;

#[derive(Default, Serialize, Clone, PartialEq, NetworkParsed, AsyncDeserialize, EnumString)]
#[network(default)]
#[derive_test_only(Debug)]
pub enum BenefitType {
    #[network(rename = "FREE_WITH_ADS")]
    #[ignx_serde(rename = "FREE_WITH_ADS")]
    #[strum(serialize = "FREE_WITH_ADS")]
    FreeWithAds,

    #[default]
    Unknown,
}

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, Serialize, Debug)]
pub struct PlaybackExperienceMetadata {
    pub playbackEnvelope: String,
    pub expiryTime: u64,
    pub correlationId: String,
}

#[derive(Serialize, <PERSON>lone, PartialEq, NetworkParsed, AsyncDeserialize)]
#[derive_test_only(Debug)]
pub struct UserPlaybackMetadata {
    pub runtimeSeconds: Option<u64>,
    pub timecodeSeconds: Option<u64>,
    pub hasStreamed: Option<bool>,
    pub isLinear: Option<bool>,
    pub linearStartTime: Option<u64>,
    pub linearEndTime: Option<u64>,
}

#[derive(Serialize, Clone, PartialEq, NetworkParsed, AsyncDeserialize)]
#[derive_test_only(Debug)]
pub struct UserEntitlementMetadata {
    pub entitlementType: String,
    pub benefitType: Vec<BenefitType>,
}

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, EnumString)]
#[derive_test_only(Debug)]
#[network(SNAKE_CASE_ALL)]
#[ignx_serde(SCREAMING_SNAKE_CASE_ALL)]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum PlaybackPosition {
    FeatureFinished,
    FeatureInProgress,
    FeatureNotStarted,
    LiveStreamFromBeginning,
    LiveStreamFromBookmark,
    LiveStreamFromEarliest,
    LiveStreamWatchNow,
    RapidRecap,
    Feature,
    MultiView,
    None,
}

impl Serialize for PlaybackPosition {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match self {
            PlaybackPosition::FeatureFinished => {
                serializer.serialize_unit_variant("PlaybackPosition", 0, "FEATURE_FINISHED")
            }
            PlaybackPosition::FeatureInProgress => {
                serializer.serialize_unit_variant("PlaybackPosition", 1, "FEATURE_IN_PROGRESS")
            }
            PlaybackPosition::FeatureNotStarted => {
                serializer.serialize_unit_variant("PlaybackPosition", 2, "FEATURE_NOT_STARTED")
            }
            PlaybackPosition::LiveStreamFromBeginning => serializer.serialize_unit_variant(
                "PlaybackPosition",
                3,
                "LIVE_STREAM_FROM_BEGINNING",
            ),
            PlaybackPosition::LiveStreamFromBookmark => serializer.serialize_unit_variant(
                "PlaybackPosition",
                4,
                "LIVE_STREAM_FROM_BOOKMARK",
            ),
            PlaybackPosition::LiveStreamFromEarliest => serializer.serialize_unit_variant(
                "PlaybackPosition",
                5,
                "LIVE_STREAM_FROM_EARLIEST",
            ),
            PlaybackPosition::LiveStreamWatchNow => {
                serializer.serialize_unit_variant("PlaybackPosition", 6, "LIVE_STREAM_WATCH_NOW")
            }
            PlaybackPosition::RapidRecap => {
                serializer.serialize_unit_variant("PlaybackPosition", 7, "RAPID_RECAP")
            }
            PlaybackPosition::Feature => {
                serializer.serialize_unit_variant("PlaybackPosition", 8, "FEATURE")
            }
            PlaybackPosition::MultiView => {
                serializer.serialize_unit_variant("PlaybackPosition", 9, "MULTI_VIEW")
            }
            PlaybackPosition::None => {
                serializer.serialize_unit_variant("PlaybackPosition", 10, "FEATURE")
            }
        }
    }
}

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, Serialize, Debug)]
pub enum VideoMaterialType {
    Feature,
    Trailer,
    LiveStreaming,
    Promo,
}

impl VideoMaterialType {
    pub fn as_string(&self) -> String {
        match self {
            VideoMaterialType::Feature => "Feature".into(),
            VideoMaterialType::Trailer => "Trailer".into(),
            VideoMaterialType::LiveStreaming => "LiveStreaming".into(),
            VideoMaterialType::Promo => "Promo".into(),
        }
    }

    pub fn as_value(&self) -> Value {
        Value::String(self.as_string())
    }
}

#[derive(Serialize, Clone, PartialEq, NetworkParsed, AsyncDeserialize)]
#[derive_test_only(Debug)]
pub enum TitleActionMetadataType {
    AcquisitionSVOD,
    AcquisitionTVOD,
    AcquisitionPrime,
    MoreWays,
    MoreWaysSection,
    Playback,
    PlaybackGroup,
    Suppression,
    PreorderSuppression,
    PreorderSuppressionSection,
    SuppressionSection,
    WatchParty,
}

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, Serialize)]
#[derive_test_only(Debug)]
pub struct CatalogMetadata {
    title: String,
    seasonNumber: Option<u32>,
    episodeNumber: Option<u32>,
    episodeName: Option<String>,
    coverImage: Option<String>,
}

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, Serialize)]
#[derive_test_only(Debug)]
pub struct PlaybackMetadata {
    pub refMarker: String,
    pub metadataActionType: TitleActionMetadataType,
    pub userPlaybackMetadata: UserPlaybackMetadata,
    pub userEntitlementMetadata: UserEntitlementMetadata,
    pub playbackExperienceMetadata: Option<PlaybackExperienceMetadata>,
    pub videoMaterialType: String,
    pub position: PlaybackPosition,
    pub contentDescriptors: Option<Vec<String>>,
    pub startPositionEpochUtc: Option<u64>,
    pub channelId: Option<String>,
    pub playbackTitle: Option<String>,
    pub catalogMetadata: Option<CatalogMetadata>,
    pub isTrailer: Option<bool>,
    #[network(default)]
    pub isUnopenedRental: bool,
}

#[cfg(test)]
mod test {
    use crate::playback_metadata::{
        BenefitType, PlaybackExperienceMetadata, PlaybackMetadata, PlaybackPosition,
        TitleActionMetadataType, UserEntitlementMetadata, UserPlaybackMetadata, VideoMaterialType,
    };
    use rstest::rstest;
    use serde_json::{Map, Number, Value};

    fn create_serde_map<const N: usize>(map_iterable: [(&str, Value); N]) -> Map<String, Value> {
        let mut serde_map = Map::new();
        for (key, val) in map_iterable {
            serde_map.insert(key.to_string(), val);
        }
        serde_map
    }

    #[test]
    fn parse_playback_experience_metadata() {
        let playback_experience_meta = create_serde_map([
            (
                "playbackEnvelope",
                Value::String("playbackEnvelope".to_string()),
            ),
            ("expiryTime", Value::Number(Number::from(32))),
            ("correlationId", Value::String("correlationId".to_string())),
        ]);
        let playback_obj = Value::Object(playback_experience_meta);
        let parsed = (&playback_obj).try_into();
        assert_eq!(
            parsed,
            Ok(PlaybackExperienceMetadata {
                playbackEnvelope: "playbackEnvelope".to_string(),
                expiryTime: 32,
                correlationId: "correlationId".to_string(),
            })
        )
    }

    #[test]
    fn parse_user_playback_metadata() {
        let user_playback_metadata = create_serde_map([
            ("runtimeSeconds", Value::Number(Number::from(90))),
            ("timecodeSeconds", Value::Number(Number::from(72))),
            ("hasStreamed", Value::Bool(true)),
            ("isLinear", Value::Bool(false)),
            ("linearStartTime", Value::Number(Number::from(61234))),
            ("linearEndTime", Value::Null),
        ]);
        let parsed = (&Value::Object(user_playback_metadata)).try_into();
        assert_eq!(
            parsed,
            Ok(UserPlaybackMetadata {
                runtimeSeconds: Some(90),
                timecodeSeconds: Some(72),
                hasStreamed: Some(true),
                isLinear: Some(false),
                linearStartTime: Some(61234),
                linearEndTime: None,
            })
        )
    }

    #[test]
    fn parse_benefit_type_valid() {
        let benefit_type = Value::String("FREE_WITH_ADS".to_string());
        let parsed: Result<BenefitType, String> = (&benefit_type).try_into();
        assert_eq!(parsed, Ok(BenefitType::FreeWithAds))
    }

    #[test]
    fn parse_user_entitlement_metadata() {
        let user_entitlement_metadata = create_serde_map([
            ("entitlementType", Value::String("type".to_string())),
            (
                "benefitType",
                Value::Array(vec![Value::String("FREE_WITH_ADS".to_string())]),
            ),
        ]);
        let parsed = (&Value::Object(user_entitlement_metadata)).try_into();
        assert_eq!(
            parsed,
            Ok(UserEntitlementMetadata {
                entitlementType: "type".to_string(),
                benefitType: vec![BenefitType::FreeWithAds],
            })
        );
    }

    #[rstest]
    #[case("FEATURE_FINISHED", Ok(PlaybackPosition::FeatureFinished))]
    #[case("FEATURE_IN_PROGRESS", Ok(PlaybackPosition::FeatureInProgress))]
    #[case("FEATURE_NOT_STARTED", Ok(PlaybackPosition::FeatureNotStarted))]
    #[case(
        "LIVE_STREAM_FROM_BEGINNING",
        Ok(PlaybackPosition::LiveStreamFromBeginning)
    )]
    #[case(
        "LIVE_STREAM_FROM_BOOKMARK",
        Ok(PlaybackPosition::LiveStreamFromBookmark)
    )]
    #[case(
        "LIVE_STREAM_FROM_EARLIEST",
        Ok(PlaybackPosition::LiveStreamFromEarliest)
    )]
    #[case("LIVE_STREAM_WATCH_NOW", Ok(PlaybackPosition::LiveStreamWatchNow))]
    #[case("RAPID_RECAP", Ok(PlaybackPosition::RapidRecap))]
    #[case("MULTI_VIEW", Ok(PlaybackPosition::MultiView))]
    #[case("not_valid", Err("Unknown type for PlaybackPosition 'not_valid'".to_string()))]
    fn parse_playback_position(
        #[case] input: &'_ str,
        #[case] expected: Result<PlaybackPosition, String>,
    ) {
        let playback_position = (&Value::String(input.to_string())).try_into();
        assert_eq!(playback_position, expected)
    }

    #[rstest]
    #[case("Playback", Ok(TitleActionMetadataType::Playback))]
    #[case("PlaybackGroup", Ok(TitleActionMetadataType::PlaybackGroup))]
    #[case("AcquisitionPrime", Ok(TitleActionMetadataType::AcquisitionPrime))]
    #[case("AcquisitionTVOD", Ok(TitleActionMetadataType::AcquisitionTVOD))]
    #[case("AcquisitionSVOD", Ok(TitleActionMetadataType::AcquisitionSVOD))]
    #[case(
        "PreorderSuppression",
        Ok(TitleActionMetadataType::PreorderSuppression)
    )]
    #[case(
        "PreorderSuppressionSection",
        Ok(TitleActionMetadataType::PreorderSuppressionSection)
    )]
    #[case("SuppressionSection", Ok(TitleActionMetadataType::SuppressionSection))]
    #[case("WatchParty", Ok(TitleActionMetadataType::WatchParty))]
    #[case("not_valid", Err("Unknown type for TitleActionMetadataType 'not_valid'".to_string()))]
    fn parse_title_metadata_type(
        #[case] input: &'_ str,
        #[case] expected: Result<TitleActionMetadataType, String>,
    ) {
        let title_action_metadata_type = (&Value::String(input.to_string())).try_into();
        assert_eq!(title_action_metadata_type, expected)
    }

    #[test]
    fn parse_playback_metadata_valid() {
        let value: Value =
            serde_json::from_str(include_str!("assets/playback_metadata_valid.json"))
                .expect("Expected mock response to parse");
        let parsed = (&value).try_into();
        assert_eq!(
            parsed,
            Ok(PlaybackMetadata {
                refMarker: "atv_dp_atf_prime_hd_mv_resume_t1ACAAAAAA0lr0".to_string(),
                contentDescriptors: Some(vec!["foul language".to_string()]),
                playbackExperienceMetadata: None,
                position: PlaybackPosition::FeatureInProgress,
                startPositionEpochUtc: None,
                userPlaybackMetadata: UserPlaybackMetadata {
                    runtimeSeconds: Some(3947),
                    timecodeSeconds: Some(1255),
                    hasStreamed: Some(true),
                    isLinear: Some(false),
                    linearStartTime: None,
                    linearEndTime: None,
                },
                userEntitlementMetadata: UserEntitlementMetadata {
                    entitlementType: "PRIME_SUBSCRIPTION".to_string(),
                    benefitType: vec![],
                },
                videoMaterialType: VideoMaterialType::Feature.as_string(),
                channelId: None,
                playbackTitle: None,
                metadataActionType: TitleActionMetadataType::Playback,
                catalogMetadata: None,
                isTrailer: None,
                isUnopenedRental: false
            })
        )
    }

    #[test]
    fn parse_playback_metadata_valid_full() {
        let value: Value =
            serde_json::from_str(include_str!("assets/playback_metadata_full_valid.json"))
                .expect("Expected mock response to parse");
        let parsed = (&value).try_into();
        assert_eq!(
            parsed,
            Ok(PlaybackMetadata {
                refMarker: "atv_dp_atf_prime_hd_mv_resume_t1ACAAAAAA0lr0".to_string(),
                contentDescriptors: Some(vec!["foul language".to_string()]),
                playbackExperienceMetadata: Some(PlaybackExperienceMetadata {
                    playbackEnvelope: "testEnv".to_string(),
                    expiryTime: 10000,
                    correlationId: "testCorrelationId".to_string(),
                }),
                position: PlaybackPosition::FeatureInProgress,
                startPositionEpochUtc: Some(10000),
                userPlaybackMetadata: UserPlaybackMetadata {
                    runtimeSeconds: Some(3947),
                    timecodeSeconds: Some(1255),
                    hasStreamed: Some(true),
                    isLinear: Some(false),
                    linearStartTime: Some(555),
                    linearEndTime: Some(777),
                },
                userEntitlementMetadata: UserEntitlementMetadata {
                    entitlementType: "PRIME_SUBSCRIPTION".to_string(),
                    benefitType: vec![],
                },
                videoMaterialType: VideoMaterialType::Feature.as_string(),
                channelId: Some("channelId".to_string()),
                playbackTitle: Some("playbackTitle".to_string()),
                metadataActionType: TitleActionMetadataType::Playback,
                catalogMetadata: None,
                isTrailer: None,
                isUnopenedRental: false
            })
        )
    }
}
