#![allow(nonstandard_style)]

use crate::containers::Container;
use cfg_test_attr_derive::derive_test_only;
use network_parser::custom_network_parser;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;
use serde_json::Value;
use std::hash::Hash;
use std::hash::Hasher;

/// Enum containing the different permissions a particular profile may have to perform some action
#[derive(Serialize, NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub enum ProfilePermissionType {
    ALLOWED,
    DENIED,
    /// This profile must complete a PIN challenge to be allowed to do the relevant action
    CHALLENGE,
}

#[derive(Serialize, NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub enum ProfilePermissionReason {
    NO_RESTRICTIONS,
    TEEN_ACCOUNT,
    IMPLICIT_PROFILE,
    REACHED_LIMIT,
    HOUSEHOLD_PROFILE,
    CUSTOMER_PREFERENCE,
}

#[derive(<PERSON>ial<PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
#[derive_test_only(Debug)]
#[serde(tag = "type")]
pub enum ProfileChallenge {
    PIN_SETUP_REQUIRED(PinSetupRequiredChallenge),
    PIN_REQUIRED(PinRequiredChallenge),
}

custom_network_parser!(ProfileChallenge, |value| {
    let accessor_key = parse_within_object("type", value, parse_string)?;

    match accessor_key.as_str() {
        "PIN_SETUP_REQUIRED" => {
            let val: ParserReturnType<PinSetupRequiredChallenge> = parse_with_try_from(value);
            Ok(ProfileChallenge::PIN_SETUP_REQUIRED(val?))
        }
        "PIN_REQUIRED" => {
            let val: ParserReturnType<PinRequiredChallenge> = parse_with_try_from(value);
            Ok(ProfileChallenge::PIN_REQUIRED(val?))
        }
        _ => Err("Unsupported ProfileChallenge attempted to be parsed!".to_string()),
    }
});

#[derive(Serialize, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct PinSetupRequiredChallenge {
    pub profileId: String,
    pub alternateChallenge: Option<Box<ProfileChallenge>>,
}

custom_network_parser!(PinSetupRequiredChallenge, |value| {
    Ok(PinSetupRequiredChallenge {
        profileId: parse_within_object("profileId", value, parse_string)?,
        alternateChallenge: parse_with_opt(value, |value| {
            let parsed: ParserReturnType<ProfileChallenge> =
                parse_within_object("alternateChallenge", value, parse_with_try_from);
            Ok(Box::new(parsed?))
        }),
    })
});

#[derive(Serialize, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct PinRequiredChallenge {
    pub profileId: String,
    pub msg: String,
    pub pinLength: i32,
    pub alternateChallenge: Option<Box<ProfileChallenge>>,
}

custom_network_parser!(PinRequiredChallenge, |value| {
    Ok(PinRequiredChallenge {
        profileId: parse_within_object("profileId", value, parse_string)?,
        msg: parse_within_object("msg", value, parse_string)?,
        pinLength: parse_within_object("pinLength", value, parse_number_i32)?,
        alternateChallenge: parse_with_opt(value, |value| {
            let parsed: ParserReturnType<ProfileChallenge> =
                parse_within_object("alternateChallenge", value, parse_with_try_from);
            Ok(Box::new(parsed?))
        }),
    })
});

#[derive(Serialize, NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct ProfilePermission {
    #[network(rename = "type")]
    #[serde(rename = "type")]
    pub permissionType: ProfilePermissionType,
    pub challenge: Option<ProfileChallenge>,
    pub reason: Option<ProfilePermissionReason>,
}

#[derive(Serialize, NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct ProfilePermissions {
    pub edit: ProfilePermission,
    pub entry: ProfilePermission,
}

/// Represents particular strings that should be taken localised from the client string bundle rather than using values from the service
#[derive(Serialize, NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
#[network(tag = "translationKey")]
#[serde(tag = "translationKey")]
pub enum TranslationDetails {
    /// An indication to use the string from the string bundle with the default name for Kids profiles.
    DEFAULT_KIDS_PROFILE,
}

/// The avatar for a profile, including a unique id, image url, and TTS description
#[derive(Serialize, NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct ProfileAvatar {
    /// an identifier for the avatar. Used when creating/editing profiles.
    pub avatarId: String,
    /// the url of the avatar image.
    pub avatarUrl: String,
    /// a string describing the avatar image for accessibility purposes.
    pub avatarDescription: Option<String>,
}

fn profile_age_group_to_adult(age_group: &str) -> Option<bool> {
    match age_group {
        "ADULT" => Some(true),
        "CHILD" => Some(false),
        _ => None,
    }
}

/// This model contains both fields from the profile network response and the react representation
/// both are then converted to the Profile model below using the `TryFrom` trait, this is needed
/// for interoperability between the two data representations as the state holder is on the React side
#[derive(NetworkParsed)]
struct ProfileDeserializationModel {
    #[network(alias = "profileId")]
    pub id: String,
    pub avatar: Option<ProfileAvatar>,
    pub avatarId: Option<String>,
    pub avatarUrl: Option<String>,
    pub avatarDescription: Option<String>,
    pub name: String,
    pub isActive: bool,
    pub profileAgeGroup: Option<String>,
    pub isAdult: Option<bool>,
    #[network(alias = "isDefaultProfile")]
    #[network(default)]
    pub profileIsImplicit: bool,
    pub translationDetails: Option<TranslationDetails>,
    pub permissions: Option<ProfilePermissions>,
}

impl TryFrom<ProfileDeserializationModel> for Profile {
    type Error = &'static str;

    fn try_from(model: ProfileDeserializationModel) -> Result<Self, Self::Error> {
        if model.avatar.is_none() && model.avatarId.is_none() {
            return Err("missing avatarId");
        }

        if model.avatar.is_none() && model.avatarUrl.is_none() {
            return Err("missing avatarUrl");
        }

        if model.isAdult.is_none() && model.profileAgeGroup.is_none() {
            return Err("missing isAdult or profileAgeGroup");
        }

        Ok(Profile {
            id: model.id,
            avatar: model.avatar.unwrap_or_else(|| ProfileAvatar {
                #[allow(clippy::unwrap_used)]
                avatarId: model.avatarId.unwrap(),
                #[allow(clippy::unwrap_used)]
                avatarUrl: model.avatarUrl.unwrap(),
                avatarDescription: model.avatarDescription,
            }),
            name: model.name,
            isActive: model.isActive,
            #[allow(clippy::unwrap_used)]
            isAdult: model
                .profileAgeGroup
                .as_deref()
                .and_then(profile_age_group_to_adult)
                .or(model.isAdult)
                .unwrap(),
            profileIsImplicit: model.profileIsImplicit,
            translationDetails: model.translationDetails,
            permissions: model.permissions,
        })
    }
}

#[derive(Serialize, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct Profile {
    pub id: String,
    #[serde(flatten)]
    pub avatar: ProfileAvatar,
    pub name: String,
    pub isActive: bool,
    pub isAdult: bool,
    pub profileIsImplicit: bool,
    pub translationDetails: Option<TranslationDetails>,
    pub permissions: Option<ProfilePermissions>,
    // TODO when required
    // pub continue_watching_cards: Option<Card[]>,
    // pub recommended_for_you_cards: Option<Card[]>,
}

impl Hash for Profile {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.id.hash(state);
    }
}

custom_network_parser!(Profile, |value: &Value| {
    let parsed_val: ProfileDeserializationModel = value.try_into()?;
    let profile: Result<Profile, &'static str> = parsed_val.try_into();
    match profile {
        Ok(val) => Ok(val),
        Err(err) => Err(err.to_string()),
    }
});

impl ignx_compositron::id::Id for Profile {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Represents the page data for the profile selection page ("Who's Watching?").
/// Derived from [the transform response](https://code.amazon.com/packages/AVLivingRoomBorgJvmTransform/blobs/mainline/--/src/com/amazon/avlrc/transform/model/response/ProfileSelectionResponse.kt)
#[derive(NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct ProfileSelectionPage {
    /// Indicates if the account can create a new profile (and therefore if the create button should be displayed)
    #[network(alias = "canCreateProfileHint")]
    pub showCreateButton: bool,
    /// The permissions required to create a new profile (or re-associate a hidden profile)
    pub createOrAssociatePermission: Option<ProfilePermission>,
    /// The list of profiles to display
    pub profiles: Vec<Profile>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct PromotionsForProfileResponse {
    pub containerList: Vec<Container>,
}

#[cfg(test)]
mod test {
    use serde_json::json;

    use super::*;

    #[test]
    fn profile_age_group_deserializer_adult_to_true() {
        let age_group = "ADULT";
        let result: Option<bool> = profile_age_group_to_adult(age_group);
        assert!(result.is_some_and(|x| x));
    }

    #[test]
    fn profile_age_group_deserializer_child_to_false() {
        let age_group = "CHILD";
        let result: Option<bool> = profile_age_group_to_adult(age_group);
        assert!(result.is_some_and(|x| !x));
    }

    #[test]
    fn profile_age_group_deserializer_other_string_to_none() {
        let age_group = "other";
        let result: Option<bool> = profile_age_group_to_adult(age_group);
        assert!(result.is_none());
    }

    #[test]
    fn it_deserializes_from_react_repr() {
        let _eg = r#"
        {
            "id": "someProfileId",
            "name": "someName",
            "avatarUrl": "someAvatarUrl",
            "avatarId": "someAvatarId",
            "avatarDescription": "someDescription",
            "isActive": true,
            "isAdult": true,
            "isDefaultProfile": true,
            "translationDetails": { "translationKey": "DEFAULT_KIDS_PROFILE" },
            "permissions": {
                "edit": { "reason": "NO_RESTRICTIONS", "type": "ALLOWED", "challenge": {
                    "type": "PIN_REQUIRED",
                    "profileId": "someProfileId",
                    "msg": "someMsg",
                    "pinLength": 4,
                    "alternateChallenge": {
                        "type": "PIN_SETUP_REQUIRED",
                        "profileId": "someProfileId",
                        "alternateChallenge": null
                    }
                }},
                "entry": { "reason": "NO_RESTRICTIONS", "type": "ALLOWED" }
              }
          }
        "#;

        let expected_profile: Profile = Profile {
            id: "someProfileId".to_owned(),
            avatar: ProfileAvatar {
                avatarId: "someAvatarId".to_owned(),
                avatarUrl: "someAvatarUrl".to_owned(),
                avatarDescription: Some("someDescription".to_owned()),
            },
            name: "someName".to_owned(),
            isActive: true,
            isAdult: true,
            profileIsImplicit: true,
            translationDetails: Some(TranslationDetails::DEFAULT_KIDS_PROFILE),
            permissions: Some(ProfilePermissions {
                edit: ProfilePermission {
                    permissionType: ProfilePermissionType::ALLOWED,
                    reason: Some(ProfilePermissionReason::NO_RESTRICTIONS),
                    challenge: Some(ProfileChallenge::PIN_REQUIRED(PinRequiredChallenge {
                        profileId: "someProfileId".to_string(),
                        msg: "someMsg".to_owned(),
                        pinLength: 4,
                        alternateChallenge: Some(Box::new(ProfileChallenge::PIN_SETUP_REQUIRED(
                            PinSetupRequiredChallenge {
                                profileId: "someProfileId".to_owned(),
                                alternateChallenge: None,
                            },
                        ))),
                    })),
                },
                entry: ProfilePermission {
                    permissionType: ProfilePermissionType::ALLOWED,
                    reason: Some(ProfilePermissionReason::NO_RESTRICTIONS),
                    challenge: None,
                },
            }),
        };

        let profile: Profile =
            network_parse_from_str(_eg).expect("Expected parse from Value -> Profile");
        assert_eq!(profile, expected_profile);
    }

    #[test]
    fn it_deserializes_from_profile_network_response_repr() {
        let _eg = r#"
        {
            "id": "someProfileId",
            "name": "someName",
            "avatar": {
                "avatarId": "someAvatarId",
                "avatarUrl": "someAvatarUrl",
                "avatarDescription": "someDescription"
            },
            "isActive": true,
            "profileAgeGroup": "ADULT",
            "isDefaultProfile": true,
            "translationDetails": { "translationKey": "DEFAULT_KIDS_PROFILE" },
            "permissions": {
                "edit": { "reason": "NO_RESTRICTIONS", "type": "ALLOWED", "challenge": {
                    "type": "PIN_REQUIRED",
                    "profileId": "someProfileId",
                    "msg": "someMsg",
                    "pinLength": 4,
                    "alternateChallenge": {
                        "type": "PIN_SETUP_REQUIRED",
                        "profileId": "someProfileId",
                        "alternateChallenge": null
                    }
                }},
                "entry": { "reason": "NO_RESTRICTIONS", "type": "ALLOWED" }
              }
          }
        "#;

        let expected_profile: Profile = Profile {
            id: "someProfileId".to_owned(),
            avatar: ProfileAvatar {
                avatarId: "someAvatarId".to_owned(),
                avatarUrl: "someAvatarUrl".to_owned(),
                avatarDescription: Some("someDescription".to_owned()),
            },
            name: "someName".to_owned(),
            isActive: true,
            isAdult: true,
            profileIsImplicit: true,
            translationDetails: Some(TranslationDetails::DEFAULT_KIDS_PROFILE),
            permissions: Some(ProfilePermissions {
                edit: ProfilePermission {
                    permissionType: ProfilePermissionType::ALLOWED,
                    reason: Some(ProfilePermissionReason::NO_RESTRICTIONS),
                    challenge: Some(ProfileChallenge::PIN_REQUIRED(PinRequiredChallenge {
                        profileId: "someProfileId".to_string(),
                        msg: "someMsg".to_owned(),
                        pinLength: 4,
                        alternateChallenge: Some(Box::new(ProfileChallenge::PIN_SETUP_REQUIRED(
                            PinSetupRequiredChallenge {
                                profileId: "someProfileId".to_owned(),
                                alternateChallenge: None,
                            },
                        ))),
                    })),
                },
                entry: ProfilePermission {
                    permissionType: ProfilePermissionType::ALLOWED,
                    reason: Some(ProfilePermissionReason::NO_RESTRICTIONS),
                    challenge: None,
                },
            }),
        };

        let profile: Profile =
            network_parse_from_str(_eg).expect("Expected parse from Value -> Profile");
        assert_eq!(profile, expected_profile);
    }

    #[test]
    fn it_passes_with_missing_optional_fields() {
        let _eg = r#"
        {
            "id": "someProfileId",
            "name": "someName",
            "avatarUrl": "someAvatarUrl",
            "avatarId": "someAvatarId",
            "avatarDescription": "someDescription",
            "isActive": true,
            "isAdult": true,
            "isDefaultProfile": true
        }
        "#;

        let profile: Result<Profile, _> = network_parse_from_str(_eg);
        assert!(profile.is_ok());
    }

    #[test]
    fn it_returns_error_if_no_avatar_id() {
        let _eg = r#"
        {
            "id": "someProfileId",
            "name": "someName",
            "avatarUrl": "someAvatarUrl",
            "avatarDescription": "someDescription",
            "isActive": true,
            "isAdult": true,
            "isDefaultProfile": true
          }
        "#;

        let profile: Result<Profile, _> = network_parse_from_str(_eg);
        assert!(profile.is_err());
    }

    #[test]
    fn it_returns_error_if_no_avatar_url() {
        let _eg = r#"
        {
            "id": "someProfileId",
            "name": "someName",
            "avatarId": "someAvatarId",
            "avatarDescription": "someDescription",
            "isActive": true,
            "isAdult": true,
            "isDefaultProfile": true
          }
        "#;

        let profile: Result<Profile, _> = network_parse_from_str(_eg);
        assert!(profile.is_err());
    }

    #[test]
    fn it_returns_error_if_missing_age() {
        let _eg = r#"
        {
            "id": "someProfileId",
            "name": "someName",
            "avatarId": "someAvatarId",
            "avatarUrl": "someAvatarUrl",
            "avatarDescription": "someDescription",
            "isActive": true,
            "isDefaultProfile": true
        }"#;

        let profile: Result<Profile, _> = network_parse_from_str(_eg);
        assert!(profile.is_err());
    }

    #[test]
    fn it_serializes_to_react_repr() {
        let profile = Profile {
            id: "someProfileId".to_owned(),
            avatar: ProfileAvatar {
                avatarId: "someAvatarId".to_owned(),
                avatarUrl: "someAvatarUrl".to_owned(),
                avatarDescription: Some("someDescription".to_owned()),
            },
            name: "someName".to_owned(),
            isActive: true,
            isAdult: true,
            profileIsImplicit: true,
            translationDetails: Some(TranslationDetails::DEFAULT_KIDS_PROFILE),
            permissions: Some(ProfilePermissions {
                edit: ProfilePermission {
                    permissionType: ProfilePermissionType::ALLOWED,
                    reason: Some(ProfilePermissionReason::NO_RESTRICTIONS),
                    challenge: Some(ProfileChallenge::PIN_REQUIRED(PinRequiredChallenge {
                        profileId: "someProfileId".to_string(),
                        msg: "someMsg".to_owned(),
                        pinLength: 4,
                        alternateChallenge: Some(Box::new(ProfileChallenge::PIN_SETUP_REQUIRED(
                            PinSetupRequiredChallenge {
                                profileId: "someProfileId".to_owned(),
                                alternateChallenge: None,
                            },
                        ))),
                    })),
                },
                entry: ProfilePermission {
                    permissionType: ProfilePermissionType::ALLOWED,
                    reason: Some(ProfilePermissionReason::NO_RESTRICTIONS),
                    challenge: None,
                },
            }),
        };

        let expected_json = json!({
            "id": "someProfileId",
            "name": "someName",
            "avatarUrl": "someAvatarUrl",
            "avatarId": "someAvatarId",
            "avatarDescription": "someDescription",
            "isActive": true,
            "isAdult": true,
            "profileIsImplicit": true,
            "translationDetails": { "translationKey": "DEFAULT_KIDS_PROFILE" },
            "permissions": {
                "edit": { "reason": "NO_RESTRICTIONS", "type": "ALLOWED", "challenge": {
                    "type": "PIN_REQUIRED",
                    "profileId": "someProfileId",
                    "msg": "someMsg",
                    "pinLength": 4,
                    "alternateChallenge": {
                        "type": "PIN_SETUP_REQUIRED",
                        "profileId": "someProfileId",
                        "alternateChallenge": null
                    }
                }},
                "entry": { "reason": "NO_RESTRICTIONS", "type": "ALLOWED", "challenge": null }
              }
        });

        assert_eq!(json!(profile), expected_json);
    }
}
