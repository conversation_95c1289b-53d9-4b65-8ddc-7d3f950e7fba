use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(NetworkParsed, Clone, PartialEq)]
#[cfg_attr(
    any(
        debug_assertions,
        test,
        feature = "debug_impl",
        not(target_arch = "wasm32")
    ),
    derive(Debug)
)]
pub struct PaymentRiskMessageResponse {
    #[network(rename = "paymentRiskMessage")]
    pub payment_risk_message: Option<PaymentRiskMessage>, // If customer doesn't have any payment related issues, this will be None
}

#[derive(NetworkParsed, Clone, PartialEq)]
#[cfg_attr(
    any(
        debug_assertions,
        test,
        feature = "debug_impl",
        not(target_arch = "wasm32")
    ),
    derive(Debug)
)]
pub struct PaymentRiskMessage {
    pub action: String,
    pub body: String,
    pub dismiss: String,
    pub header: String,
    #[network(rename = "offerType")] // Changed to match the network rename style
    pub offer_type: String,
}
