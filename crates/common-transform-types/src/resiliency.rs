use cfg_test_attr_derive::{cfg_test_only, derive_test_only};
use ignx_compositron::log;
use ignx_compositron::prelude::metric;
use ignx_compositron::serde::de::AsyncDeserialize;
use network_parser::core::{NetworkOptional, NetworkVec, ParserError};
use network_parser::custom_network_parser;
use network_parser::wrappers::parse_with_try_from;
use network_parser_derive::NetworkParsed;
#[cfg_test_only]
use serde::Serialize;
use serde_json::Value;

#[derive(Clone, AsyncDeserialize)]
#[ignx_serde(untagged)]
#[derive_test_only(PartialEq, Debug, Serialize)]
pub enum UnexpectedValue {
    Value(String),
}

#[cfg(any(
    debug_assertions,
    test,
    feature = "debug_impl",
    not(target_arch = "wasm32")
))]
impl Default for UnexpectedValue {
    fn default() -> Self {
        UnexpectedValue::Value("".to_string())
    }
}

custom_network_parser!(UnexpectedValue, |value| {
    let unexpected_string = format!("{}", value);
    Ok(UnexpectedValue::Value(unexpected_string))
});

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug, Serialize)]
#[network(untagged)]
#[ignx_serde(untagged)]
pub enum WithResiliency<T> {
    Ok(T),
    Unexpected(UnexpectedValue),
}

#[cfg(any(
    debug_assertions,
    test,
    feature = "debug_impl",
    not(target_arch = "wasm32")
))]
impl<T> Default for WithResiliency<T> {
    fn default() -> Self {
        WithResiliency::Unexpected(UnexpectedValue::default())
    }
}

impl<T> WithResiliency<NetworkOptional<T>> {
    pub fn parse_resiliency(
        self,
        page_source: impl AsRef<str>,
        context: impl AsRef<str>,
    ) -> Option<T> {
        match self {
            WithResiliency::Ok(v) => v.into(),
            WithResiliency::Unexpected(v) => {
                let UnexpectedValue::Value(unexpected_value) = v;
                log_and_report_metric_on_unexpected(page_source, context, &unexpected_value);
                None
            }
        }
    }
}

impl<T: for<'a> TryFrom<&'a Value>> WithResiliency<NetworkVec<T>> {
    pub fn parse_resiliency(
        self,
        page_source: impl AsRef<str>,
        context: impl AsRef<str>,
    ) -> NetworkVec<T> {
        match self {
            WithResiliency::Ok(v) => v,
            WithResiliency::Unexpected(v) => {
                let UnexpectedValue::Value(unexpected_value) = v;
                log_and_report_metric_on_unexpected(page_source, context, &unexpected_value);
                NetworkVec::Items(vec![])
            }
        }
    }
}

impl<T> WithResiliency<T> {
    pub fn parse_resiliency_into_opt(
        &self,
        page_source: impl AsRef<str>,
        context: impl AsRef<str>,
    ) -> Option<&T> {
        match self {
            WithResiliency::Ok(v) => Some(v),
            WithResiliency::Unexpected(v) => {
                let UnexpectedValue::Value(unexpected_value) = v;
                log_and_report_metric_on_unexpected(page_source, context, unexpected_value);
                None
            }
        }
    }
}

fn log_and_report_metric_on_unexpected(
    page_source: impl AsRef<str>,
    context: impl AsRef<str>,
    unexpected_value: &String,
) {
    log::error!(
        "[{}] [{}] unexpected value, unable to deserialise this section. Will be dropped. Value received: {}",
        page_source.as_ref(),
        context.as_ref(),
        unexpected_value
    );
    metric!("PageAction.Count", 1, "pageType" => page_source.as_ref(), "actionName" => "DeserializationFailedResilient");
}

#[cfg(test)]
mod tests {
    use super::*;
    use network_parser::core::NetworkBool;

    #[test]
    fn parse_resiliency_fns_when_ok() {
        let vec = vec![
            NetworkBool::Item(true),
            NetworkBool::Item(false),
            NetworkBool::Item(true),
        ];
        let a = WithResiliency::Ok(NetworkOptional::<NetworkBool>::None);
        let b = WithResiliency::Ok(NetworkVec::Items(vec.clone()));
        let c = WithResiliency::Ok(NetworkBool::Item(true));

        assert_eq!(a.parse_resiliency("", ""), None);
        assert_eq!(b.parse_resiliency("", ""), NetworkVec::Items(vec));
        assert_eq!(
            c.parse_resiliency_into_opt("", ""),
            Some(&NetworkBool::Item(true))
        );
    }

    #[test]
    fn parse_resiliency_fns_when_unexpected() {
        let a: WithResiliency<NetworkOptional<NetworkBool>> =
            WithResiliency::Unexpected(UnexpectedValue::Value("this is a string".to_string()));
        let b: WithResiliency<NetworkVec<NetworkBool>> =
            WithResiliency::Unexpected(UnexpectedValue::Value("this is a string".to_string()));
        let c: WithResiliency<NetworkBool> =
            WithResiliency::Unexpected(UnexpectedValue::Value("this is a string".to_string()));
        assert_eq!(a.parse_resiliency("", ""), None);
        assert_eq!(b.parse_resiliency("", ""), NetworkVec::Items(vec![]));
        assert_eq!(c.parse_resiliency_into_opt("", ""), None);
    }
}
