use crate::container_items::ShowContext;

// function to build the prefix string that contains episode, season and episode title
pub fn build_prefix_string(context: &Option<ShowContext>) -> Option<String> {
    // TODO: Localization
    context.as_ref().and_then(|context| {
        let episode_title = context.episodeTitle.as_ref();
        let season = context.season;
        let episode = context.episode;

        match (episode_title, season, episode) {
            (Some(episode_title), Some(season), Some(episode)) => {
                Some(format!("S{} E{} {}", season, episode, episode_title))
            }
            (Some(episode_title), Some(season), None) => {
                Some(format!("S{} {}", season, episode_title))
            }
            (Some(episode_title), None, Some(episode)) => {
                Some(format!("E{} {}", episode, episode_title))
            }
            (Some(episode_title), None, None) => Some(episode_title.clone()),
            (None, Some(season), Some(episode)) => Some(format!("S{} E{}", season, episode)),
            (None, Some(season), None) => Some(format!("S{}", season)),
            (None, None, Some(episode)) => Some(format!("E{}", episode)),
            (None, None, None) => None,
        }
    })
}

// function to build the description string that contains prefix string and synopsis
pub fn build_description_string(
    prefix: &Option<String>,
    synopsis: &Option<String>,
) -> Option<String> {
    match (prefix, synopsis) {
        (Some(prefix), Some(synopsis)) => Some(format!("{} - {}", prefix, synopsis)),
        (Some(prefix), None) => Some(prefix.to_owned()),
        (None, Some(synopsis)) => Some(synopsis.to_owned()),
        (None, None) => None,
    }
}

/// Unwraps the episodic info string and appends a space if it is Some
/// The space separates the episodic info from the synopsis
pub fn format_episodic_info(episodic_info: Option<String>) -> String {
    match episodic_info {
        Some(episodic_info) => format!("{episodic_info} "),
        None => "".to_string(),
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use rstest::rstest;

    fn mock_show_context(
        episode: Option<u32>,
        season: Option<u32>,
        episode_title: Option<String>,
    ) -> ShowContext {
        ShowContext {
            episode,
            season,
            episodeTitle: episode_title,
        }
    }

    #[rstest]
    //Test case 1 when episode, episode_title and synopsis are present
    #[case(
        None,
        Some(6),
        Some(String::from("test")),
        Some(String::from("Synopsis text")),
        Some(String::from("E6 test - Synopsis text"))
    )]
    //Test case 2 when season, episode_title and synopsis are present
    #[case(
        Some(5),
        None,
        Some(String::from("test")),
        None,
        Some(String::from("S5 test"))
    )]
    //Test case 3 when episode and synopsis are present
    #[case(
        None,
        None,
        Some(String::from("test")),
        Some(String::from("Synopsis text")),
        Some(String::from("test - Synopsis text"))
    )]
    //Test case 4 when only synopsis is present
    #[case(
        None,
        None,
        None,
        Some(String::from("Synopsis text")),
        Some(String::from("Synopsis text"))
    )]
    //Test case 5 when all are none
    #[case(None, None, None, None, None)]
    //Test case 6 when season, episode and synopsis are present
    #[case(
        Some(5),
        Some(6),
        None,
        Some(String::from("Synopsis text")),
        Some(String::from("S5 E6 - Synopsis text"))
    )]
    //Test case 7 when season and synopsis are present
    #[case(
        Some(5),
        None,
        None,
        Some(String::from("Synopsis text")),
        Some(String::from("S5 - Synopsis text"))
    )]
    //Test case 8 when episode and synopsis are present
    #[case(
        None,
        Some(6),
        None,
        Some(String::from("Synopsis text")),
        Some(String::from("E6 - Synopsis text"))
    )]
    //Test case 9 when season, episode, episode_title and synopsis present
    #[case(
        Some(5),
        Some(6),
        Some(String::from("test")),
        Some(String::from("Synopsis text")),
        Some(String::from("S5 E6 test - Synopsis text"))
    )]
    fn it_constructs_display_string(
        #[case] season: Option<u32>,
        #[case] episode: Option<u32>,
        #[case] episode_title: Option<String>,
        #[case] synopsis: Option<String>,
        #[case] expected_text: Option<String>,
    ) {
        let context = mock_show_context(episode, season, episode_title);
        let prefix = build_prefix_string(&Some(context));
        let display_string = build_description_string(&prefix, &synopsis);
        assert_eq!(display_string, expected_text);
    }
}
