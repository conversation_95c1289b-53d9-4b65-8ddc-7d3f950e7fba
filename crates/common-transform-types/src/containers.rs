#![allow(nonstandard_style, reason = "Follows JSON style for compability")]

use crate::actions::{Action, LinkAction, SwiftAction, TransitionAction};
use crate::container_items::{
    BonusScheduleCard, CarouselItemData, ChannelCard, EntitlementMessaging, EventCard,
    EventMetadata, GenericTitleCard, HasCarouselItemData, HeroCard, IconBadges, LinearAiringCard,
    LinearStationCard, LinkCard, LiveLinearCard, MovieCard, SeriesCard, ShowCard, SportsCard,
    TitleCardBaseMetadata, VodExtraContentCard,
};
use crate::image::ImageV2;
use crate::impressions::GetImpressionsContentType;
use crate::resiliency::{UnexpectedValue, WithResiliency};
use cacheable_derive::Cacheable;
use cfg_test_attr_derive::{cfg_test_only, derive_test_only};
use derive_more::Display;
use ignx_compositron::id::Id;
use ignx_compositron::prelude::*;
use ignx_compositron::serde::de::AsyncDeserialize;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
#[cfg_test_only]
use serde::Serialize;
use std::collections::HashMap;
use strum::AsRefStr;
#[cfg(any(
    debug_assertions,
    test,
    feature = "debug_impl",
    not(target_arch = "wasm32")
))]
use strum_macros::EnumIter;

/// `PaginationLink` also contains a `pageContext` field that repeats the page id and page type. We
/// are ignoring it as it is duplicated data (you can see this in the [transform](https://code.amazon.com/packages/AVLivingRoomBorgJvmTransform/blobs/26df548aa5fc0edaaeafabea5eeed134acb933e9/--/src/com/amazon/avlrc/transform/atom/swift/SwiftParams.kt))
/// but if that changes we'll need to update.
#[derive(NetworkParsed, AsyncDeserialize, Clone, Eq, PartialEq, Hash, Cacheable)]
#[derive_test_only(Debug, Serialize)]
pub struct PaginationLink {
    pub serviceToken: String,
    pub startIndex: u32,
    pub swiftId: String,
    pub pageId: String,
    pub pageType: String,
    pub decorationScheme: Option<String>,
    pub pageSize: Option<u32>,
}

impl Default for PaginationLink {
    fn default() -> Self {
        Self {
            serviceToken: String::new(),
            startIndex: 0,
            swiftId: String::new(),
            pageId: String::new(),
            pageType: String::new(),
            decorationScheme: None,
            pageSize: None,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct HintAttribute {
    pub attributeType: String,
    pub refMarker: Option<String>,
    pub text: Option<String>,
    pub focusState: Option<bool>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct HintImage {
    pub imageType: String,
    pub imageUrl: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "contentType")]
#[ignx_serde(tag = "contentType")]
pub enum CommonCarouselTitleCardItem {
    SEASON(SeriesCard),
    SHOW(ShowCard),
    MOVIE(MovieCard),
    EVENT(EventCard),
    GENERIC_TITLE_CARD(GenericTitleCard),
    EPISODE(GenericTitleCard),
    SERIES(GenericTitleCard),
    VOD_EVENT_ITEM(GenericTitleCard),
    LIVE_EVENT_ITEM(EventCard),
    SPORT(GenericTitleCard),
    LEAGUE(GenericTitleCard),
    TOURNAMENT(GenericTitleCard),
    TEAM(GenericTitleCard),
    PLAYER(GenericTitleCard),
    VOD_EXTRA_CONTENT(GenericTitleCard),
}

impl Id for CommonCarouselTitleCardItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        match self {
            CommonCarouselTitleCardItem::SEASON(card) => card.id(),
            CommonCarouselTitleCardItem::SHOW(card) => card.id(),
            CommonCarouselTitleCardItem::MOVIE(card) => card.id(),
            CommonCarouselTitleCardItem::EVENT(card) => card.id(),
            CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card) => card.id(),
            CommonCarouselTitleCardItem::EPISODE(card) => card.id(),
            CommonCarouselTitleCardItem::SERIES(card) => card.id(),
            CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card) => card.id(),
            CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => card.id(),
            CommonCarouselTitleCardItem::SPORT(card) => card.id(),
            CommonCarouselTitleCardItem::LEAGUE(card) => card.id(),
            CommonCarouselTitleCardItem::TOURNAMENT(card) => card.id(),
            CommonCarouselTitleCardItem::TEAM(card) => card.id(),
            CommonCarouselTitleCardItem::PLAYER(card) => card.id(),
            CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(card) => card.id(),
        }
    }
}

impl CommonCarouselTitleCardItem {
    pub fn get_base_title_card_metadata(&self) -> &TitleCardBaseMetadata {
        match self {
            CommonCarouselTitleCardItem::SEASON(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::SHOW(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::MOVIE(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::EVENT(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::EPISODE(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::SERIES(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::SPORT(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::LEAGUE(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::TOURNAMENT(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::TEAM(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::PLAYER(card) => &card.titleCardBaseMetadata,
            CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(card) => &card.titleCardBaseMetadata,
        }
    }

    pub fn get_carousel_card_metadata(&self) -> &CarouselItemData {
        match self {
            CommonCarouselTitleCardItem::SEASON(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::SHOW(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::MOVIE(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::EVENT(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::EPISODE(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::SERIES(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::SPORT(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::LEAGUE(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::TOURNAMENT(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::TEAM(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::PLAYER(card) => &card.carouselCardMetadata,
            CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(card) => &card.carouselCardMetadata,
        }
    }

    pub fn get_event_metadata(&self) -> Option<EventMetadata> {
        match self {
            CommonCarouselTitleCardItem::EVENT(card) => Some(card.eventMetadata.clone()),
            CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => Some(card.eventMetadata.clone()),
            _ => None,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
pub enum StandardCarouselItem {
    TITLE_CARD(CommonCarouselTitleCardItem),
    LIVE_LINEAR_CARD(LiveLinearCard),
    LINK_CARD(LinkCard),
    CHANNEL_CARD(ChannelCard),
    BONUS_SCHEDULE_CARD(BonusScheduleCard),
    VOD_EXTRA_CONTENT(CommonCarouselTitleCardItem),
    LINEAR_STATION(LinearStationCard),
    LINEAR_AIRING(LinearAiringCard),
    HERO_CARD(UnexpectedValue), // unexpected so we don't need to care about the content, we'll just drop it
}

impl Id for StandardCarouselItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        match self {
            StandardCarouselItem::TITLE_CARD(card) => card.id(),
            StandardCarouselItem::LIVE_LINEAR_CARD(card) => card.id(),
            StandardCarouselItem::LINK_CARD(card) => card.id(),
            StandardCarouselItem::CHANNEL_CARD(card) => card.id(),
            StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => card.id(),
            StandardCarouselItem::VOD_EXTRA_CONTENT(card) => card.id(),
            StandardCarouselItem::LINEAR_STATION(card) => card.id(),
            StandardCarouselItem::LINEAR_AIRING(card) => card.id(),
            StandardCarouselItem::HERO_CARD(_) => &None, // we drop this without further use
        }
    }
}

impl GetImpressionsContentType for StandardCarouselItem {
    fn get_csm_content_type(&self) -> Option<String> {
        match self {
            StandardCarouselItem::TITLE_CARD(c) => c.get_csm_content_type(),
            StandardCarouselItem::HERO_CARD(_) => None,
            StandardCarouselItem::LINEAR_STATION(c) => c.get_csm_content_type(),
            StandardCarouselItem::LINK_CARD(c) => c.get_csm_content_type(),
            StandardCarouselItem::CHANNEL_CARD(c) => c.get_csm_content_type(),
            StandardCarouselItem::VOD_EXTRA_CONTENT(c) => c.get_csm_content_type(),
            StandardCarouselItem::BONUS_SCHEDULE_CARD(c) => c.get_csm_content_type(),
            StandardCarouselItem::LIVE_LINEAR_CARD(c) => c.get_csm_content_type(),
            StandardCarouselItem::LINEAR_AIRING(c) => c.get_csm_content_type(),
        }
    }
}

impl<'a> From<&'a StandardCarouselItem> for Option<&'a EntitlementMessaging> {
    fn from(value: &'a StandardCarouselItem) -> Self {
        match value {
            StandardCarouselItem::TITLE_CARD(i) => i
                .get_base_title_card_metadata()
                .entitlementMessaging
                .as_ref(),
            StandardCarouselItem::LIVE_LINEAR_CARD(i) => i.entitlementMessaging.as_ref(),
            StandardCarouselItem::LINK_CARD(i) => i.entitlementMessaging.as_ref(),
            StandardCarouselItem::CHANNEL_CARD(i) => i.entitlementMessaging.as_ref(),
            StandardCarouselItem::BONUS_SCHEDULE_CARD(i) => {
                i.titleCardBaseMetadata.entitlementMessaging.as_ref()
            }
            StandardCarouselItem::VOD_EXTRA_CONTENT(i) => i
                .get_base_title_card_metadata()
                .entitlementMessaging
                .as_ref(),
            StandardCarouselItem::LINEAR_STATION(i) => i.entitlementMessaging.as_ref(),
            StandardCarouselItem::LINEAR_AIRING(i) => {
                i.linear_airing_attributes.entitlementMessaging.as_ref()
            }
            StandardCarouselItem::HERO_CARD(_) => None,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum StandardHeroItem {
    LINK_CARD(LinkCard),
    HERO_CARD(HeroCard),
    CHANNEL_CARD(ChannelCard),
    LINEAR_STATION(LinearStationCard),
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum FullscreenHeroItem {
    LINK_CARD(LinkCard),
    HERO_CARD(HeroCard),
    CHANNEL_CARD(ChannelCard),
}
pub trait CardItemData {
    fn get_carousel_item_data(&self) -> &'_ CarouselItemData;
}

impl CardItemData for FullscreenHeroItem {
    fn get_carousel_item_data(&self) -> &'_ CarouselItemData {
        match self {
            FullscreenHeroItem::LINK_CARD(link) => &link.carouselCardMetadata,
            FullscreenHeroItem::HERO_CARD(hero) => &hero.carouselCardMetadata,
            FullscreenHeroItem::CHANNEL_CARD(channel) => &channel.carouselCardMetadata,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
pub enum ChartsCarouselItem {
    TITLE_CARD(CommonCarouselTitleCardItem),
}

impl Id for ChartsCarouselItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        match self {
            ChartsCarouselItem::TITLE_CARD(card) => card.id(),
        }
    }
}

impl<'a> From<&'a ChartsCarouselItem> for Option<&'a EntitlementMessaging> {
    fn from(value: &'a ChartsCarouselItem) -> Self {
        match value {
            ChartsCarouselItem::TITLE_CARD(i) => i
                .get_base_title_card_metadata()
                .entitlementMessaging
                .as_ref(),
        }
    }
}

impl<'a> From<&'a ChartsCarouselItem> for Option<&'a TitleCardBaseMetadata> {
    fn from(value: &'a ChartsCarouselItem) -> Self {
        match value {
            ChartsCarouselItem::TITLE_CARD(i) => Some(i.get_base_title_card_metadata()),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum CoverCarouselItem {
    TITLE_CARD(CommonCarouselTitleCardItem),
    LINK_CARD(LinkCard),
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum SuperCarouselItem {
    TITLE_CARD(CommonCarouselTitleCardItem),
    LINK_CARD(LinkCard),
}

impl Id for SuperCarouselItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        match self {
            SuperCarouselItem::TITLE_CARD(card) => card.id(),
            SuperCarouselItem::LINK_CARD(card) => card.id(),
        }
    }
}

impl<'a> From<&'a SuperCarouselItem> for Option<&'a EntitlementMessaging> {
    fn from(value: &'a SuperCarouselItem) -> Self {
        match value {
            SuperCarouselItem::TITLE_CARD(i) => i
                .get_base_title_card_metadata()
                .entitlementMessaging
                .as_ref(),
            SuperCarouselItem::LINK_CARD(i) => i.entitlementMessaging.as_ref(),
        }
    }
}

impl<'a> From<&'a SuperCarouselItem> for Option<&'a TitleCardBaseMetadata> {
    fn from(value: &'a SuperCarouselItem) -> Self {
        match value {
            SuperCarouselItem::TITLE_CARD(i) => Some(i.get_base_title_card_metadata()),
            SuperCarouselItem::LINK_CARD(_) => None,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
pub enum NodesItem {
    LINK_CARD(LinkCard),
    CHANNEL_CARD(ChannelCard),
}

impl Id for NodesItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        match self {
            NodesItem::LINK_CARD(card) => card.id(),
            NodesItem::CHANNEL_CARD(card) => card.id(),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
pub enum TentpoleHeroItem {
    HERO_CARD(HeroCard),
}

#[cfg(any(
    debug_assertions,
    test,
    feature = "debug_impl",
    not(target_arch = "wasm32")
))]
impl Default for TentpoleHeroItem {
    fn default() -> Self {
        TentpoleHeroItem::HERO_CARD(HeroCard {
            carouselCardMetadata: CarouselItemData {
                transformItemId: None,
                title: None,
                synopsis: None,
                action: None,
                deferredAction: None,
                actions: vec![],
                widgetType: None,
            },
            titleCardBaseMetadata: TitleCardBaseMetadata::default(),
            providerLogoImageMetadata: None,
            eventMetadata: EventMetadata {
                liveliness: None,
                liveEventDateHeader: None,
                liveEventDateBadge: None,
                venue: None,
                scoreBug: None,
            },
            moreDetailsAction: None,
            callToAction: None,
            tnfProperty: None,
            tournamentIcid: None,
            regulatoryLabel: None,
        })
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
#[allow(
    clippy::large_enum_variant,
    reason = "Same as Hero, https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum PromoBannerItem {
    HERO_CARD(HeroCard),
    LINK_CARD(LinkCard),
    CHANNEL_CARD(ChannelCard),
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum GridItem {
    TITLE_CARD(CommonCarouselTitleCardItem),
    LINK_CARD(LinkCard),
    CHANNEL_CARD(ChannelCard),
    LINEAR_AIRING(LinearAiringCard),
}

impl Id for GridItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        match self {
            GridItem::TITLE_CARD(c) => c.id(),
            GridItem::LINK_CARD(c) => c.id(),
            GridItem::CHANNEL_CARD(c) => c.id(),
            GridItem::LINEAR_AIRING(c) => c.id(),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
pub enum ShortCarouselItem {
    LINK_CARD(LinkCard),
}

impl Id for ShortCarouselItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        let ShortCarouselItem::LINK_CARD(item) = self;
        item.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
pub enum DiscoveryAssistantContainerItem {
    LINK_CARD(LinkCard),
}

impl Id for DiscoveryAssistantContainerItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        let DiscoveryAssistantContainerItem::LINK_CARD(item) = self;
        item.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
pub enum EntityCarouselItem {
    SPORTS_CARD(SportsCard),
}

impl Id for EntityCarouselItem {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        let EntityCarouselItem::SPORTS_CARD(card) = self;
        card.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "cardType")]
#[ignx_serde(tag = "cardType")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum SpecialCarouselItem {
    CHANNEL_CARD(ChannelCard),
    TITLE_CARD(CommonCarouselTitleCardItem),
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ScheduleGroup {
    pub date_image_url: String,
    pub items: Vec<ScheduleCarouselItem>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ScheduleCarouselItem {
    pub eventCard: EventCard,
    pub isOffPlatformTitle: Option<bool>,
    pub isCustomerGeoRestrictedToLiveEvent: Option<bool>,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum ComponentItems {
    ShortCarousel(Vec<ShortCarouselItem>),
    Grid(Vec<GridItem>),
    SuperCarousel(Vec<SuperCarouselItem>),
    CoverCarousel(Vec<CoverCarouselItem>),
    ChartsCarousel(Vec<ChartsCarouselItem>),
    StandardCarousel(Vec<StandardCarouselItem>),
    StandardHero(Vec<StandardHeroItem>),
    Nodes(Vec<NodesItem>),
    TentpoleHero(Vec<TentpoleHeroItem>),
    PromoBanner(Vec<PromoBannerItem>),
    ContainerList(Vec<Container>),
    EntityCarousel(Vec<EntityCarouselItem>),
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Default, Serialize)]
pub struct ContainerMetadata {
    pub id: String,
    pub offerType: Option<String>,   // offerType is used for title
    pub entitlement: Option<String>, // entitlement is used for title colour
    #[network(default)]
    #[ignx_serde(default)]
    pub analytics: HashMap<String, String>,
    #[network(default)]
    #[ignx_serde(default)]
    pub tags: Vec<String>,
    pub badges: Option<Vec<IconBadges>>,
}

impl Id for ContainerMetadata {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct Facet {
    pub text: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Cacheable, Clone, Display, Debug)]
#[derive_test_only(PartialEq, Serialize)]
pub enum DisplayPlacement {
    Start,
    End,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct SeeMoreLink {
    pub displayPlacement: Option<DisplayPlacement>,
    pub title: Option<String>,
    pub linkText: Option<String>,
    pub accessibilityText: Option<String>,
    pub action: Option<LinkAction>,
    pub linkAction: Option<Action>,
    pub backgroundImage: Option<String>,
    pub linkImage: Option<String>,
    pub logoImage: Option<String>,
    pub description: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct CtaButton {
    pub title: Option<String>,
    pub linkAction: Option<Action>,
}

#[cfg(any(test, feature = "test_utils"))]
impl SeeMoreLink {
    pub fn populated_default() -> Self {
        SeeMoreLink {
            displayPlacement: Some(DisplayPlacement::Start),
            title: Some("See More Link Title".to_string()),
            linkText: Some("See More Link Link Text".to_string()),
            accessibilityText: Some("See More Link Accessibility Text".to_string()),
            action: None, // leaving out as linkAction is expected to be preferred
            linkAction: Some(Action::create_transition_landing(None)),
            backgroundImage: Some("https://www.image.com/background-image-url.jpg".to_string()),
            linkImage: Some("https://www.image.com/link-image-url.jpg".to_string()),
            logoImage: Some("https://www.image.com/logo-image-url.jpg".to_string()),
            description: Some("See More Link Description".to_string()),
        }
    }

    pub fn with_action(self, action: Option<Action>) -> Self {
        Self {
            linkAction: action,
            ..self
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct StandardCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub facet: Option<Facet>,
    pub title: Option<String>,
    pub titleImageUrl: Option<String>,
    pub journeyIngressContext: Option<String>,
    pub seeMore: Option<WithResiliency<SeeMoreLink>>,
    pub items: Vec<WithResiliency<StandardCarouselItem>>,
    pub paginationLink: Option<PaginationLink>,
}

impl Id for StandardCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct StandardHeroDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<StandardHeroItem>>,
}

impl Id for StandardHeroDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct FullscreenHeroDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<FullscreenHeroItem>,
}

impl Id for FullscreenHeroDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct ChartsCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub facet: Option<Facet>,
    pub title: Option<String>,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<ChartsCarouselItem>>,
}

impl Id for ChartsCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct CoverCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub facet: Option<Facet>,
    pub title: Option<String>,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<CoverCarouselItem>>,
    pub description: Option<String>,
    pub backgroundImageUrl: Option<String>,
    pub action: Option<Action>,
    pub paginationLink: Option<PaginationLink>,
}

impl Id for CoverCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct SuperCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub actions: Option<Vec<Action>>,
    pub facet: Option<Facet>,
    pub paginationLink: Option<PaginationLink>,
    pub items: Vec<WithResiliency<SuperCarouselItem>>,
    pub journeyIngressContext: Option<String>,
    pub notExpandable: bool,
}

impl Id for SuperCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct NodesDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub facet: Option<Facet>,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<NodesItem>>,
    pub unentitledText: Option<String>,
    pub unentitledItems: Vec<WithResiliency<NodesItem>>,
    pub seeMore: Option<SeeMoreLink>,
}

impl Id for NodesDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

// Skipping EPG Items for now since it won't be supported yet
#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct EPGDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub paginationLink: Option<PaginationLink>,
    pub journeyIngressContext: Option<String>,
}

impl Id for EPGDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct TentpoleHeroDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub journeyIngressContext: Option<String>,
    pub item: WithResiliency<TentpoleHeroItem>,
}

impl Id for TentpoleHeroDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct GridDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub facet: Option<Facet>,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<GridItem>>,
    pub paginationLink: Option<PaginationLink>,
}

impl Id for GridDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

/// This is not yet finalised and may need to be updated.
#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Default, Serialize)]
pub struct ShortCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub facet: Option<Facet>,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<ShortCarouselItem>>,
}

impl Id for ShortCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct DetailsActions {
    pub id: String,
}

impl Id for DetailsActions {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct BeardSupportedCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub facet: Option<Facet>,
    pub title: Option<String>,
    pub titleImageUrl: Option<String>,
    pub journeyIngressContext: Option<String>,
    pub seeMore: Option<SeeMoreLink>,
    pub items: Vec<WithResiliency<StandardCarouselItem>>,
    pub paginationLink: Option<PaginationLink>,
}

impl Id for BeardSupportedCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, Default, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum DiscoveryAssistantItemDisplaySize {
    #[default]
    Small,
    Large,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct DiscoveryAssistantContainerDeserialization {
    #[network(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub facet: Option<Facet>,
    pub title: Option<String>,
    pub image: Option<ImageV2>,
    pub subtitle: Option<String>,
    pub journeyIngressContext: Option<String>,
    pub seeMore: Option<SeeMoreLink>,
    pub items: Vec<WithResiliency<DiscoveryAssistantContainerItem>>,
    pub paginationLink: Option<PaginationLink>,
    pub itemDisplaySize: Option<WithResiliency<DiscoveryAssistantItemDisplaySize>>,
    pub badges: Option<Vec<WithResiliency<IconBadges>>>,
}

impl Id for DiscoveryAssistantContainerDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct PromoBannerDeserialization {
    #[network(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<PromoBannerItem>>,
}

impl Id for PromoBannerDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct OnboardingDeserialization {
    #[network(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<StandardCarouselItem>>,
}

impl Id for OnboardingDeserialization {
    type Id = String;
    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct TextWidgetDeserialization {
    #[network(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub subtitle: Option<String>,
}

impl Id for TextWidgetDeserialization {
    type Id = String;
    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct ActionButtonDeserialization {
    #[network(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub items: Vec<WithResiliency<StandardCarouselItem>>,
    pub journeyIngressContext: Option<String>,
    pub button: Option<SwiftAction>,
}

impl Id for ActionButtonDeserialization {
    type Id = String;
    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

// This is used to determine the shape of SportsCard in EntityCarousel
#[derive(NetworkParsed, AsyncDeserialize, Cacheable, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum PresentationCue {
    Circle,
    Portrait,
}

impl PresentationCue {
    pub fn get_spacing(&self) -> f32 {
        match self {
            PresentationCue::Portrait => 36.0,
            PresentationCue::Circle => 48.0,
        }
    }

    pub fn get_image_size(&self) -> f32 {
        match self {
            PresentationCue::Portrait => 128.0,
            PresentationCue::Circle => 132.0,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct EntityCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub presentationCue: PresentationCue,
    pub facet: Option<Facet>,
    pub seeMore: Option<WithResiliency<SeeMoreLink>>,
    pub items: Vec<WithResiliency<EntityCarouselItem>>,
    pub journeyIngressContext: Option<String>,
    pub paginationLink: Option<PaginationLink>,
}

impl Id for EntityCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

impl Default for EntityCarouselDeserialization {
    fn default() -> Self {
        EntityCarouselDeserialization {
            containerMetadata: ContainerMetadata {
                id: "".to_string(),
                offerType: None,
                entitlement: None,
                analytics: HashMap::new(),
                tags: vec![],
                badges: None,
            },
            title: None,
            presentationCue: PresentationCue::Circle,
            facet: None,
            seeMore: None,
            items: vec![],
            journeyIngressContext: None,
            paginationLink: None,
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct ScheduleCarouselDeserialization {
    #[network(flatten)]
    #[ignx_serde(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub title: Option<String>,
    pub journeyIngressContext: Option<String>,
    pub items: Vec<WithResiliency<ScheduleGroup>>,
    pub fullScheduleAction: Option<Action>,
}
impl Id for ScheduleCarouselDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct SpecialCarouselContainerDeserialization {
    #[network(flatten)]
    pub containerMetadata: ContainerMetadata,
    pub journeyIngressContext: Option<String>,
    pub paginationLink: Option<PaginationLink>,
    pub items: Vec<WithResiliency<SpecialCarouselItem>>,
    pub facet: Option<Facet>,
    pub title: Option<String>,
    pub actions: Option<Vec<Action>>,
    pub heading: Option<String>,
    pub description: Option<String>,
    pub entitlementMessage: Option<String>,
    pub titleImageUrl: Option<String>,
    pub backgroundImageUrl: Option<String>,
    pub ctaButton: Option<CtaButton>,
}

impl Id for SpecialCarouselContainerDeserialization {
    type Id = String;

    fn id(&self) -> &Self::Id {
        self.containerMetadata.id()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, AsRefStr)]
#[derive_test_only(Debug, PartialEq, Serialize, EnumIter)]
#[network(tag = "type")]
#[ignx_serde(tag = "type")]
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
pub enum Container {
    STANDARD_CAROUSEL(StandardCarouselDeserialization),
    STANDARD_HERO(StandardHeroDeserialization),
    PROMOTIONAL_BANNER(PromoBannerDeserialization),
    CHARTS(ChartsCarouselDeserialization),
    COVER(CoverCarouselDeserialization),
    SUPER_CAROUSEL(SuperCarouselDeserialization),
    NODES(NodesDeserialization),
    EPG(EPGDeserialization),
    TENTPOLE_HERO(TentpoleHeroDeserialization),
    GRID(GridDeserialization),
    NEXT_BEST_ACTION_HINT(UnexpectedValue), // unexpected so we don't need to care about the content, we'll just drop it
    ONBOARDING(OnboardingDeserialization),
    ACTION_BUTTON_COLLECTION(ActionButtonDeserialization),
    TEXT_WIDGET(TextWidgetDeserialization),
    SHORT_CAROUSEL(ShortCarouselDeserialization),
    CHANNEL_NAVIGATION(UnexpectedValue), // unexpected so we don't need to care about the content, we'll just drop it
    DETAILS_ACTIONS(DetailsActions),
    VOD_EXTRA_CONTENT(StandardCarouselDeserialization),
    FULL_SCREEN_HERO(FullscreenHeroDeserialization),
    BEARD_SUPPORTED_CAROUSEL(BeardSupportedCarouselDeserialization),
    DISCOVERY_ASSISTANT(DiscoveryAssistantContainerDeserialization),
    ENTITY_CAROUSEL(EntityCarouselDeserialization),
    SCHEDULE_CAROUSEL(ScheduleCarouselDeserialization),
    SPECIAL_CAROUSEL(SpecialCarouselContainerDeserialization),
}

pub type ContainerWithResiliency = WithResiliency<Container>;

impl Id for Container {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            Container::STANDARD_CAROUSEL(item) => item.id(),
            Container::STANDARD_HERO(item) => item.id(),
            Container::FULL_SCREEN_HERO(item) => item.id(),
            Container::CHARTS(item) => item.id(),
            Container::COVER(item) => item.id(),
            Container::SUPER_CAROUSEL(item) => item.id(),
            Container::NODES(item) => item.id(),
            Container::EPG(item) => item.id(),
            Container::TENTPOLE_HERO(item) => item.id(),
            Container::GRID(item) => item.id(),
            Container::SHORT_CAROUSEL(item) => item.id(),
            Container::DETAILS_ACTIONS(item) => item.id(),
            Container::VOD_EXTRA_CONTENT(item) => item.id(),
            Container::BEARD_SUPPORTED_CAROUSEL(item) => item.id(),
            Container::DISCOVERY_ASSISTANT(item) => item.id(),
            Container::PROMOTIONAL_BANNER(item) => item.id(),
            Container::TEXT_WIDGET(item) => item.id(),
            Container::ACTION_BUTTON_COLLECTION(item) => item.id(),
            Container::NEXT_BEST_ACTION_HINT(item) => {
                let UnexpectedValue::Value(s) = item;
                s
            } // we drop this container without further use
            Container::ONBOARDING(item) => item.id(),
            Container::ENTITY_CAROUSEL(item) => item.id(),
            Container::CHANNEL_NAVIGATION(item) => {
                let UnexpectedValue::Value(s) = item;
                s
            } // we drop this container without further use
            Container::SCHEDULE_CAROUSEL(item) => item.id(),
            Container::SPECIAL_CAROUSEL(item) => item.id(),
        }
    }
}

impl Container {
    pub fn type_id(&self) -> String {
        match self {
            Container::STANDARD_CAROUSEL(_) => "STANDARD_CAROUSEL".to_owned(),
            Container::STANDARD_HERO(_) => "STANDARD_HERO".to_owned(),
            Container::FULL_SCREEN_HERO(_) => "FULL_SCREEN_HERO".to_owned(),
            Container::CHARTS(_) => "CHARTS".to_owned(),
            Container::COVER(_) => "COVER".to_owned(),
            Container::SUPER_CAROUSEL(_) => "SUPER_CAROUSEL".to_owned(),
            Container::NODES(_) => "NODES".to_owned(),
            Container::EPG(_) => "EPG".to_owned(),
            Container::TENTPOLE_HERO(_) => "TENTPOLE_HERO".to_owned(),
            Container::GRID(_) => "GRID".to_owned(),
            Container::SHORT_CAROUSEL(_) => "SHORT_CAROUSEL".to_owned(),
            Container::DETAILS_ACTIONS(_) => "DETAILS_ACTIONS".to_owned(),
            Container::VOD_EXTRA_CONTENT(_) => "VOD_EXTRA_CONTENT".to_owned(),
            Container::BEARD_SUPPORTED_CAROUSEL(_) => "BEARD_SUPPORTED_CAROUSEL".to_owned(),
            Container::DISCOVERY_ASSISTANT(_) => "DISCOVERY_ASSISTANT".to_owned(),
            Container::PROMOTIONAL_BANNER(_) => "PROMOTIONAL_BANNER".to_owned(),
            Container::TEXT_WIDGET(_) => "TEXT_WIDGET".to_owned(),
            Container::ACTION_BUTTON_COLLECTION(_) => "ACTION_BUTTON_COLLECTION".to_owned(),
            Container::NEXT_BEST_ACTION_HINT(_) => "NEXT_BEST_ACTION_HINT".to_owned(),
            Container::ONBOARDING(_) => "ONBOARDING".to_owned(),
            Container::ENTITY_CAROUSEL(_) => "ENTITY_CAROUSEL".to_owned(),
            Container::CHANNEL_NAVIGATION(_) => "CHANNEL_NAVIGATION".to_owned(),
            Container::SCHEDULE_CAROUSEL(_) => "SCHEDULE_CAROUSEL".to_owned(),
            Container::SPECIAL_CAROUSEL(_) => "SPECIAL_CAROUSEL".to_owned(),
        }
    }
}

#[derive(Clone, Eq, PartialEq, Hash)]
#[derive_test_only(Debug)]
pub enum PaginatableComponent {
    StandardCarousel(Option<PaginationLink>, Option<String>),
    CoverCarousel(Option<PaginationLink>, Option<String>),
    SuperCarousel(Option<PaginationLink>, Option<String>),
    Grid(Option<PaginationLink>, Option<String>),
    ContainerList(Option<PaginationLink>, Option<String>),
    BeardSupportedCarousel(Option<PaginationLink>, Option<String>),
    EntityCarousel(Option<PaginationLink>, Option<String>),
}

impl PaginatableComponent {
    pub fn log_name(&self) -> String {
        match self {
            PaginatableComponent::StandardCarousel(_, _) => "StandardCarousel".to_owned(),
            PaginatableComponent::CoverCarousel(_, _) => "CoverCarousel".to_owned(),
            PaginatableComponent::SuperCarousel(_, _) => "SuperCarousel".to_owned(),
            PaginatableComponent::Grid(_, _) => "Grid".to_owned(),
            PaginatableComponent::ContainerList(_, _) => "ContainerList".to_owned(),
            PaginatableComponent::BeardSupportedCarousel(_, _) => {
                "BeardSupportedCarousel".to_owned()
            }
            PaginatableComponent::EntityCarousel(_, _) => "EntityCarousel".to_owned(),
        }
    }

    pub fn metric_action_name(&self) -> String {
        match self {
            PaginatableComponent::StandardCarousel(_, _) => {
                "paginateContainer.STANDARDCAROUSEL".to_owned()
            }
            PaginatableComponent::CoverCarousel(_, _) => "paginateContainer.COVER".to_owned(),
            PaginatableComponent::SuperCarousel(_, _) => {
                "paginateContainer.SUPERCAROUSEL".to_owned()
            }
            PaginatableComponent::Grid(_, _) => "paginateContainer.GRID".to_owned(),
            PaginatableComponent::ContainerList(_, _) => "paginateContainerList".to_owned(),
            PaginatableComponent::BeardSupportedCarousel(_, _) => {
                "paginateContainer.BEARDSUPPORTEDCAROUSEL".to_owned()
            }
            PaginatableComponent::EntityCarousel(_, _) => {
                "paginateContainer.ENTITYCAROUSEL".to_owned()
            }
        }
    }
}

impl From<&Container> for Option<PaginatableComponent> {
    fn from(container: &Container) -> Self {
        match container {
            Container::STANDARD_CAROUSEL(c) => Some(PaginatableComponent::StandardCarousel(
                c.paginationLink.to_owned(),
                c.journeyIngressContext.to_owned(),
            )),
            Container::COVER(c) => Some(PaginatableComponent::CoverCarousel(
                c.paginationLink.to_owned(),
                c.journeyIngressContext.to_owned(),
            )),
            Container::SUPER_CAROUSEL(c) => Some(PaginatableComponent::SuperCarousel(
                c.paginationLink.to_owned(),
                c.journeyIngressContext.to_owned(),
            )),
            Container::GRID(c) => Some(PaginatableComponent::Grid(
                c.paginationLink.to_owned(),
                c.journeyIngressContext.to_owned(),
            )),
            Container::BEARD_SUPPORTED_CAROUSEL(c) => {
                Some(PaginatableComponent::BeardSupportedCarousel(
                    c.paginationLink.to_owned(),
                    c.journeyIngressContext.to_owned(),
                ))
            }
            Container::ENTITY_CAROUSEL(c) => Some(PaginatableComponent::EntityCarousel(
                c.paginationLink.to_owned(),
                c.journeyIngressContext.to_owned(),
            )),
            Container::PROMOTIONAL_BANNER(_) => None,
            Container::EPG(_) => None,
            Container::STANDARD_HERO(_) => None,
            Container::FULL_SCREEN_HERO(_) => None,
            Container::CHARTS(_) => None,
            Container::NODES(_) => None,
            Container::TENTPOLE_HERO(_) => None,
            Container::SHORT_CAROUSEL(_) => None,
            Container::DETAILS_ACTIONS(_) => None,
            Container::VOD_EXTRA_CONTENT(_) => None,
            Container::TEXT_WIDGET(_) => None,
            Container::ACTION_BUTTON_COLLECTION(_) => None,
            Container::NEXT_BEST_ACTION_HINT(_) => None,
            Container::ONBOARDING(_) => None,
            Container::CHANNEL_NAVIGATION(_) => None,
            Container::DISCOVERY_ASSISTANT(_) => None,
            Container::SCHEDULE_CAROUSEL(_) => None,
            Container::SPECIAL_CAROUSEL(_) => None,
        }
    }
}

pub trait Paginatable {
    fn is_paginatable(&self) -> bool;

    fn get_pagination_data(&self) -> (Option<PaginationLink>, Option<String>);
}

impl Paginatable for PaginatableComponent {
    fn is_paginatable(&self) -> bool {
        true
    }

    fn get_pagination_data(&self) -> (Option<PaginationLink>, Option<String>) {
        match self {
            PaginatableComponent::StandardCarousel(link, jic) => (link.to_owned(), jic.to_owned()),
            PaginatableComponent::CoverCarousel(link, jic) => (link.to_owned(), jic.to_owned()),
            PaginatableComponent::SuperCarousel(link, jic) => (link.to_owned(), jic.to_owned()),
            PaginatableComponent::Grid(link, jic) => (link.to_owned(), jic.to_owned()),
            PaginatableComponent::ContainerList(link, jic) => (link.to_owned(), jic.to_owned()),
            PaginatableComponent::BeardSupportedCarousel(link, jic) => {
                (link.to_owned(), jic.to_owned())
            }
            PaginatableComponent::EntityCarousel(link, jic) => (link.to_owned(), jic.to_owned()),
        }
    }
}

impl Paginatable for Container {
    fn is_paginatable(&self) -> bool {
        let paginatable_container: Option<PaginatableComponent> = self.into();
        paginatable_container.is_some_and(|p| p.is_paginatable())
    }

    fn get_pagination_data(&self) -> (Option<PaginationLink>, Option<String>) {
        let paginatable_container: Option<PaginatableComponent> = self.into();
        if let Some(p) = paginatable_container {
            let (p, jic) = p.get_pagination_data();
            (p, jic)
        } else {
            (None, None)
        }
    }
}

pub trait GetTransitionAction {
    fn get_transition_action(&self) -> Option<&TransitionAction>;
}

impl GetTransitionAction for Action {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            crate::actions::Action::TransitionAction(action) => Some(action),
            _ => None,
        }
    }
}

impl GetTransitionAction for LinkCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}
impl GetTransitionAction for LiveLinearCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}
impl GetTransitionAction for EventCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}

impl GetTransitionAction for MovieCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}
impl GetTransitionAction for ShowCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}
impl GetTransitionAction for SeriesCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}
impl GetTransitionAction for GenericTitleCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}

impl GetTransitionAction for HeroCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}

impl GetTransitionAction for ChannelCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        let action = self
            .carouselCardMetadata
            .action
            .as_ref()
            .or_else(|| self.carouselCardMetadata.actions.first());
        action.and_then(|action| action.get_transition_action())
    }
}

impl GetTransitionAction for CommonCarouselTitleCardItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.get_carousel_card_metadata()
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}

impl GetTransitionAction for SuperCarouselItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            SuperCarouselItem::TITLE_CARD(card) => card.get_transition_action(),
            SuperCarouselItem::LINK_CARD(card) => card.get_transition_action(),
        }
    }
}

impl GetTransitionAction for LinearStationCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}

impl GetTransitionAction for SeeMoreLink {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.linkAction
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}

impl GetTransitionAction for LinearAiringCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carousel_item_data()
            .action
            .as_ref()
            .and_then(|a| a.get_transition_action())
    }
}

impl GetTransitionAction for GridItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            GridItem::LINK_CARD(card) => card.get_transition_action(),
            GridItem::TITLE_CARD(card) => card.get_transition_action(),
            GridItem::CHANNEL_CARD(card) => card.get_transition_action(),
            GridItem::LINEAR_AIRING(card) => card.get_transition_action(),
        }
    }
}

impl GetTransitionAction for SportsCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}

pub trait TitleCard {
    fn get_metadata(&self) -> (&TitleCardBaseMetadata, &CarouselItemData);
}

macro_rules! impl_title_card_for {
    ($($card_type:ty),*) => {
        $(
            impl TitleCard for $card_type {
                fn get_metadata(&self) -> (&TitleCardBaseMetadata, &CarouselItemData) {
                    (&self.titleCardBaseMetadata, &self.carouselCardMetadata)
                }
            }
        )*
    }
}

impl_title_card_for!(
    GenericTitleCard,
    SeriesCard,
    ShowCard,
    MovieCard,
    EventCard,
    VodExtraContentCard,
    HeroCard
);

impl TitleCard for CommonCarouselTitleCardItem {
    fn get_metadata(&self) -> (&TitleCardBaseMetadata, &CarouselItemData) {
        match self {
            CommonCarouselTitleCardItem::SEASON(card) => card.get_metadata(),
            CommonCarouselTitleCardItem::MOVIE(card) => card.get_metadata(),
            CommonCarouselTitleCardItem::SHOW(card) => card.get_metadata(),
            CommonCarouselTitleCardItem::EVENT(card)
            | CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => card.get_metadata(),
            CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(card)
            | CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card)
            | CommonCarouselTitleCardItem::LEAGUE(card)
            | CommonCarouselTitleCardItem::PLAYER(card)
            | CommonCarouselTitleCardItem::EPISODE(card)
            | CommonCarouselTitleCardItem::SERIES(card)
            | CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card)
            | CommonCarouselTitleCardItem::SPORT(card)
            | CommonCarouselTitleCardItem::TOURNAMENT(card)
            | CommonCarouselTitleCardItem::TEAM(card) => card.get_metadata(),
        }
    }
}

impl GetTransitionAction for StandardCarouselItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            StandardCarouselItem::TITLE_CARD(card) => card.get_transition_action(),
            StandardCarouselItem::LINK_CARD(card) => card.get_transition_action(),
            StandardCarouselItem::CHANNEL_CARD(card) => card.get_transition_action(),
            StandardCarouselItem::LIVE_LINEAR_CARD(card) => card.get_transition_action(),
            StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => card.get_transition_action(),
            StandardCarouselItem::VOD_EXTRA_CONTENT(card) => card.get_transition_action(),
            StandardCarouselItem::LINEAR_STATION(card) => card.get_transition_action(),
            StandardCarouselItem::LINEAR_AIRING(card) => card.get_transition_action(),
            StandardCarouselItem::HERO_CARD(_) => None,
        }
    }
}

impl GetTransitionAction for ShortCarouselItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            ShortCarouselItem::LINK_CARD(card) => card.get_transition_action(),
        }
    }
}
impl GetTransitionAction for ChartsCarouselItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            ChartsCarouselItem::TITLE_CARD(card) => card.get_transition_action(),
        }
    }
}

impl GetTransitionAction for NodesItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            NodesItem::LINK_CARD(card) => card.get_transition_action(),
            NodesItem::CHANNEL_CARD(card) => card.get_transition_action(),
        }
    }
}

impl GetTransitionAction for StandardHeroItem {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        match self {
            StandardHeroItem::LINK_CARD(card) => card.get_transition_action(),
            StandardHeroItem::HERO_CARD(card) => card.get_transition_action(),
            StandardHeroItem::CHANNEL_CARD(card) => card.get_transition_action(),
            StandardHeroItem::LINEAR_STATION(card) => card.get_transition_action(),
        }
    }
}

impl GetTransitionAction for VodExtraContentCard {
    fn get_transition_action(&self) -> Option<&TransitionAction> {
        self.carouselCardMetadata
            .action
            .as_ref()
            .and_then(|action| action.get_transition_action())
    }
}
