use crate::{
    actions::TitleActionNestedChild,
    details::{EntitlementIcon, SlotEntitlementMessage, TitleDecorationInformationCore},
};
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::id::Id;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, Default, PartialEq)]
#[network(camelCaseAll)]
pub struct EpisodeListV2 {
    pub selected_episode_gti: Option<String>,
    pub selected_series_gti: Option<String>, // Not used in Rust
    pub next_page_refresh_token: Option<String>,
    pub prev_page_refresh_token: Option<String>, // Not used in Rust
    pub episodes: Vec<EpisodeListV2Episode>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, Default, PartialEq)]
#[network(camelCaseAll)]
pub struct EpisodeListV2Episode {
    pub gti: String,
    pub sequence_number: u32,
    pub season_sequence_number: u32,
    pub actions: Vec<TitleActionNestedChild>,
    pub title_details: TitleDecorationInformationCore,
    pub traits: Vec<EpisodeTrait>,
    pub entitlement_messaging: HashMap<SlotIdentifier, SlotEntitlementMessage>,
}

#[derive(Eq, PartialEq, Hash, Clone, NetworkParsed)]
#[derive_test_only(Debug)]
#[network(SNAKE_CASE_ALL)]
pub enum EpisodeTrait {
    Unreleased,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, Default, PartialEq)]
#[network(camelCaseAll)]
pub struct SeasonEpisodes {
    pub episodes_by_season_index: HashMap<i32, Vec<Episode>>,
    pub seasons: Vec<Season>,
    pub selected_season_index: u32,
    pub selected_episode_index: u32,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct Season {
    pub gti: String,
    pub season_number: u32,
    pub offer_icon: Option<EntitlementIcon>,
}

impl Id for Season {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.gti
    }
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct Episode {
    pub actions: Vec<TitleActionNestedChild>,
    pub title_details: TitleDecorationInformationCore,
    pub is_first_in_season: bool,
    pub is_last_in_season: bool,
    pub entitlement_messaging: HashMap<SlotIdentifier, SlotEntitlementMessage>,
}

#[derive(Eq, PartialEq, Hash, Clone, NetworkParsed)]
#[derive_test_only(Debug)]
#[network(SNAKE_CASE_ALL)]
pub enum SlotIdentifier {
    #[network(rename = "BUYBOX_MESSAGE_SLOT")]
    BuyBoxMessageSlot,
    EntitlementMessageSlot,
    GlanceMessageSlot,
    HighValueMessageSlot,
    HighValueMessageSlotLite,
    SeasonSelectorSlot,
    InformationalMessageSlot,
    TitleMetadataBadgeSlot,
    ProviderLogoSlot,
}
