#![allow(nonstandard_style)]
use crate::{
    actions::{Action, ClientAction},
    string_builder::{build_description_string, build_prefix_string},
};
use cacheable_derive::Cacheable;
use cfg_test_attr_derive::derive_test_only;
use core::fmt;
use ignx_compositron::{
    id::Id,
    prelude::*,
    serde::de::AsyncDeserialize,
    text::{LocalizedText, SubstitutionParameters, TextContent},
};
use linear_common::{
    types::{DatePair, Schedule, TimeRange},
    util::datetime_util::parse_date_pair,
};
use network_parser::{custom_network_parser, prelude::*};
use network_parser_derive::NetworkParsed;
use serde::Serialize;
use serde_json::Value;
use std::collections::HashMap;
use strum_macros::EnumString;

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, Default)]
#[derive_test_only(Debug, Serialize)]
#[network(camelCaseAll)]
#[network(default)]
#[ignx_serde(camelCaseAll)]
#[ignx_serde(default)]
pub struct Badges {
    pub applyAudioDescription: bool,
    #[ignx_serde(rename = "applyCC")]
    pub applyCC: bool,
    pub applyDolby: bool,
    pub applyDolbyVision: bool,
    pub applyDolbyAtmos: bool,
    #[network(alias = "applyHdr")]
    #[ignx_serde(alias = "applyHdr")]
    pub applyHdr10: bool,
    pub applyPrime: bool,
    pub applyUhd: bool,
    pub regulatoryRating: Option<String>,
    pub showPSE: bool,
}

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, Default)]
#[derive_test_only(Debug, Serialize)]
#[network(default)]
#[ignx_serde(default)]
pub struct LinearBadges {
    pub applyLive: bool,
    pub applyNew: bool,
    pub applyCC: bool,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, PartialEq, Hash)]
#[derive_test_only(Debug, Serialize)]
pub enum IconBadges {
    GenAI,
}

impl Id for IconBadges {
    type Id = IconBadges;

    fn id(&self) -> &Self::Id {
        self
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, EnumString)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum EntitlementGlanceIcons {
    ENTITLED_ICON,
    OFFER_ICON,
    ADS_ICON,
    ERROR_ICON,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, EnumString)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum EntitlementMessageIcons {
    ENTITLED_ICON,
    OFFER_ICON,
    ADS_ICON,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, EnumString)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum BuyboxMessageIcons {
    OFFER_ICON,
    ENTITLED_ICON,
    ADS_ICON,
    ERROR_ICON,
    TRENDING_ICON,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, EnumString)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum TitleMetadataBadgeLevel {
    INFO,
    INFO_ACTIVE,
    INFO_INACTIVE,
    INFO_HIGHLIGHT,
}

impl From<TitleMetadataBadgeLevel> for String {
    fn from(value: TitleMetadataBadgeLevel) -> Self {
        match value {
            TitleMetadataBadgeLevel::INFO => "INFO".to_string(),
            TitleMetadataBadgeLevel::INFO_ACTIVE => "INFO_ACTIVE".to_string(),
            TitleMetadataBadgeLevel::INFO_INACTIVE => "INFO_INACTIVE".to_string(),
            TitleMetadataBadgeLevel::INFO_HIGHLIGHT => "INFO_HIGHLIGHT".to_string(),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct EntitlementGlance {
    pub message: Option<String>,
    pub icon: Option<EntitlementGlanceIcons>,
    pub level: Option<String>,
    #[network(rename = "type")]
    #[ignx_serde(rename = "type")]
    pub glance_type: Option<String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl EntitlementGlance {
    pub fn populated_default() -> EntitlementGlance {
        EntitlementGlance {
            message: Some("entitlement glance message".to_string()),
            icon: Some(EntitlementGlanceIcons::ENTITLED_ICON),
            level: Some("INFO".to_string()),
            glance_type: Some("glance type".to_string()),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct EntitlementMessage {
    pub message: Option<String>,
    pub icon: Option<EntitlementMessageIcons>,
}

#[cfg(any(test, feature = "test_utils"))]
impl EntitlementMessage {
    pub fn populated_default() -> EntitlementMessage {
        EntitlementMessage {
            message: Some("entitlement message".to_string()),
            icon: Some(EntitlementMessageIcons::ENTITLED_ICON),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct HighValueMessage {
    pub message: Option<String>,
    pub icon: Option<String>,
    pub level: Option<String>,
    #[network(rename = "type")]
    #[ignx_serde(rename = "type")]
    pub hvm_type: Option<String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl HighValueMessage {
    pub fn populated_default() -> HighValueMessage {
        HighValueMessage {
            message: Some("high value message".to_string()),
            icon: Some("icon".to_string()),
            level: Some("INFO".to_string()),
            hvm_type: Some("hvm type".to_string()),
        }
    }
}

pub type HighValueMessageLite = HighValueMessage;

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitleMetadataBadge {
    pub message: Option<String>,
    pub level: Option<TitleMetadataBadgeLevel>,
}

#[cfg(any(test, feature = "test_utils"))]
impl TitleMetadataBadge {
    pub fn populated_default() -> TitleMetadataBadge {
        TitleMetadataBadge {
            message: Some("title metadata badge message".to_string()),
            level: Some(TitleMetadataBadgeLevel::INFO),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct InformationalMessage {
    pub messages: Option<Vec<String>>,
}

#[cfg(any(test, feature = "test_utils"))]
impl InformationalMessage {
    pub fn populated_default() -> InformationalMessage {
        InformationalMessage {
            messages: Some(vec!["informational message".to_string()]),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct BuyboxMessage {
    pub message: Option<String>,
    pub icon: Option<BuyboxMessageIcons>,
}

#[cfg(any(test, feature = "test_utils"))]
impl BuyboxMessage {
    pub fn populated_default() -> BuyboxMessage {
        BuyboxMessage {
            message: Some("buybox message".to_string()),
            icon: Some(BuyboxMessageIcons::OFFER_ICON),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ProductSummary {
    pub message: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ProductPromotion {
    pub message: Option<String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl ProductSummary {
    pub fn populated_default() -> ProductSummary {
        ProductSummary {
            message: Some("product summary message".to_string()),
        }
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl ProductPromotion {
    pub fn populated_default() -> ProductPromotion {
        ProductPromotion {
            message: Some("product promotion message".to_string()),
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct EntitlementMessaging {
    pub GLANCE_MESSAGE_SLOT: Option<EntitlementGlance>,
    pub ENTITLEMENT_MESSAGE_SLOT: Option<EntitlementMessage>,
    pub HIGH_VALUE_MESSAGE_SLOT: Option<HighValueMessage>,
    pub HIGH_VALUE_MESSAGE_SLOT_LITE: Option<HighValueMessageLite>,
    pub TITLE_METADATA_BADGE_SLOT: Option<TitleMetadataBadge>,
    pub INFORMATIONAL_MESSAGE_SLOT: Option<InformationalMessage>,
    pub BUYBOX_MESSAGE_SLOT: Option<BuyboxMessage>,
    pub PRODUCT_SUMMARY_SLOT: Option<ProductSummary>,
    pub PRODUCT_PROMOTION_SLOT: Option<ProductPromotion>,
}

impl EntitlementMessaging {
    fn to_hvm(&self) -> Option<&HighValueMessage> {
        self.HIGH_VALUE_MESSAGE_SLOT.as_ref()
    }

    pub fn to_hvm_message(&self) -> Option<&String> {
        let hvm: Option<&HighValueMessage> = self.to_hvm();
        hvm?.message.as_ref()
    }

    pub fn to_hvm_lite(&self) -> Option<&HighValueMessageLite> {
        self.HIGH_VALUE_MESSAGE_SLOT_LITE.as_ref()
    }

    pub fn to_informational_messages(&self) -> Option<&Vec<String>> {
        let informational_message: Option<&InformationalMessage> = self.into();
        informational_message?.messages.as_ref()
    }

    pub fn to_title_metadata_badge_message(&self) -> Option<&String> {
        let title_metadata_badge: Option<&TitleMetadataBadge> = self.into();
        title_metadata_badge?.message.as_ref()
    }

    pub fn to_title_metadata_badge_level(&self) -> Option<&TitleMetadataBadgeLevel> {
        let title_metadata_badge: Option<&TitleMetadataBadge> = self.into();
        title_metadata_badge?.level.as_ref()
    }

    pub fn to_entitlement_message_message(&self) -> Option<&String> {
        let entitlement_message: Option<&EntitlementMessage> = self.into();
        entitlement_message?.message.as_ref()
    }

    pub fn to_product_summary_message(&self) -> Option<&String> {
        let product_summary: Option<&ProductSummary> = self.into();
        product_summary?.message.as_ref()
    }

    pub fn to_product_promotion_message(&self) -> Option<&String> {
        let product_promotion: Option<&ProductPromotion> = self.into();
        product_promotion?.message.as_ref()
    }

    #[cfg(any(test, feature = "test_utils"))]
    pub fn populated_default() -> EntitlementMessaging {
        EntitlementMessaging {
            GLANCE_MESSAGE_SLOT: Some(EntitlementGlance::populated_default()),
            ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage::populated_default()),
            HIGH_VALUE_MESSAGE_SLOT: Some(HighValueMessage::populated_default()),
            HIGH_VALUE_MESSAGE_SLOT_LITE: Some(HighValueMessageLite::populated_default()),
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge::populated_default()),
            INFORMATIONAL_MESSAGE_SLOT: Some(InformationalMessage::populated_default()),
            BUYBOX_MESSAGE_SLOT: Some(BuyboxMessage::populated_default()),
            PRODUCT_SUMMARY_SLOT: Some(ProductSummary::populated_default()),
            PRODUCT_PROMOTION_SLOT: Some(ProductPromotion::populated_default()),
        }
    }
}

macro_rules! impl_from_entitlement_for_child {
    ($(($type:ty, $field:ident)),+) => {
        $(
            impl<'a> From<&'a EntitlementMessaging> for Option<&'a $type> {
                fn from(value: &'a EntitlementMessaging) -> Self {
                    value.$field.as_ref()
                }
            }
        )+
    };
}

impl_from_entitlement_for_child!(
    (EntitlementGlance, GLANCE_MESSAGE_SLOT),
    (EntitlementMessage, ENTITLEMENT_MESSAGE_SLOT),
    (TitleMetadataBadge, TITLE_METADATA_BADGE_SLOT),
    (InformationalMessage, INFORMATIONAL_MESSAGE_SLOT),
    (BuyboxMessage, BUYBOX_MESSAGE_SLOT),
    (ProductSummary, PRODUCT_SUMMARY_SLOT),
    (ProductPromotion, PRODUCT_PROMOTION_SLOT)
);

macro_rules! impl_from_entitlement_for_grandchild {
    ($(($child_type:ty, $final_type:ty, $field:ident)),+) => {
        $(
            impl<'a> From<&'a EntitlementMessaging> for Option<&'a $final_type> {
                fn from(value: &'a EntitlementMessaging) -> Self {
                    let c: Option<&$child_type> = value.into();
                    c?.$field.as_ref()
                }
            }
        )+
    };
}

impl_from_entitlement_for_grandchild!(
    (EntitlementGlance, EntitlementGlanceIcons, icon),
    (EntitlementMessage, EntitlementMessageIcons, icon),
    (BuyboxMessage, BuyboxMessageIcons, icon),
    (TitleMetadataBadge, TitleMetadataBadgeLevel, level)
);

impl<'a> From<&'a TitleMetadataBadge> for Option<&'a String> {
    fn from(value: &'a TitleMetadataBadge) -> Self {
        value.message.as_ref()
    }
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum TitleCardContentType {
    MOVIE,
    SEASON,
    EVENT,
    SHOW,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum AmPm {
    AM,
    PM,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum ShortMonth {
    JAN,
    FEB,
    MAR,
    APR,
    MAY,
    JUN,
    JUL,
    AUG,
    SEP,
    OCT,
    NOV,
    DEC,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventTime {
    pub hoursTwoDigitTwelveHour: String,
    pub hoursTwoDigitTwentyFourHour: String,
    pub minutesTwoDigit: String,
    pub amOrPm: AmPm,
    pub shouldShowConfidenceIndicator: bool,
    pub timeZoneAbreviation: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct DayNumber {
    pub day: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct DayMonth {
    pub day: String,
    pub month: ShortMonth,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct DayMonthYear {
    pub day: String,
    pub month: ShortMonth,
    pub year: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LocalizedLiveEventBadge {
    pub text: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LocalizedLiveEventHeader {
    pub date: Option<String>,
    pub time: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventDateHeaderMultiDay {
    pub startDate: DayMonthYear,
    pub endDate: DayMonthYear,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventDateHeaderSingleDay {
    pub date: DayMonthYear,
    pub time: LiveEventTime,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventDateBadgeMultiDaySameMonth {
    pub startDate: DayNumber,
    pub endDate: DayMonth,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventDateBadgeMultiDayDifferentMonth {
    pub startDate: DayMonth,
    pub endDate: DayMonth,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventDateBadgeSingleDayDate {
    pub date: DayMonth,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventDateBadgeSingleDayTime {
    pub time: LiveEventTime,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LiveEventDateBadgeSingleDayDateTime {
    pub date: DayMonth,
    pub time: LiveEventTime,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(tag = "type")]
#[ignx_serde(tag = "type")]
pub enum LiveEventDateTime {
    UNLOCALIZED,
    LOCALIZED_BADGE(LocalizedLiveEventBadge),
    LOCALIZED_HEADER(LocalizedLiveEventHeader),
    HEADER_MULTI_DAY(LiveEventDateHeaderMultiDay),
    HEADER_SINGLE_DAY(LiveEventDateHeaderSingleDay),
    BADGE_MULTI_DAY_SAME_MONTH(LiveEventDateBadgeMultiDaySameMonth),
    BADGE_MULTI_DAY_DIFFERENT_MONTH(LiveEventDateBadgeMultiDayDifferentMonth),
    BADGE_SINGLE_DAY_DATE(LiveEventDateBadgeSingleDayDate),
    BADGE_SINGLE_DAY_TIME(LiveEventDateBadgeSingleDayTime),
    BADGE_SINGLE_DAY_DATE_TIME(LiveEventDateBadgeSingleDayDateTime),
}

#[derive(Clone, PartialEq, NetworkParsed, AsyncDeserialize, Serialize)]
#[derive_test_only(Debug)]
pub struct Dimension {
    pub width: u32,
    pub height: u32,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct MaturityRatingImage {
    pub url: String,
    pub dimension: Dimension,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, EnumString)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[network(lowercase_all)]
#[ignx_serde(lowercase_all)]
#[strum(serialize_all = "lowercase")]
#[derive(Default)]
pub enum LogoSize {
    Subtle,
    Emphasis,
    #[default]
    Default,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct IndividualImageMetadata {
    pub height: Option<u32>,
    pub width: Option<u32>,
    pub scalarHorizontal: Option<LogoSize>,
    pub scalarStacked: Option<LogoSize>,
    pub safeToOverlay: Option<bool>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct IndividualImageMetadataMapping {
    pub providerLogoImage: Option<IndividualImageMetadata>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ImageAttributes {
    pub isAdult: Option<bool>,
    pub isRestricted: Option<bool>,
    pub individualImageMetadata: Option<IndividualImageMetadataMapping>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]

pub struct LinearSchedule {
    pub startTime: u64,
    pub endTime: u64,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinearAttributes {
    pub stationName: Option<String>,
    pub schedule: Option<LinearSchedule>,
    pub upNextShowTitle: Option<String>,
    pub isOnNow: Option<bool>,
    pub linearVodAction: Option<Action>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinearSeriesAttributes {
    #[network(flatten)]
    pub linearAttributes: LinearAttributes,
    pub episodeNumber: Option<u32>,
    pub episodeTitle: Option<String>,
    pub episodeSynopsis: Option<String>,
    pub episodeGti: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, Cacheable)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ShowContext {
    pub season: Option<u32>,
    pub episode: Option<u32>,
    pub episodeTitle: Option<String>,
}

impl ShowContext {
    pub fn to_tts_messages(&self) -> Vec<TextContent> {
        let mut v = vec![];
        if let Some(season) = self.season {
            v.push(
                LocalizedText {
                    string_id: "AV_LRC_TITLE_DETAILS_SYNOPSIS_SEASON".into(),
                    substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                        "number".into(),
                        TextContent::String(season.to_string()),
                    )]))),
                }
                .into(),
            );
        }
        if let Some(episode) = self.episode {
            v.push(
                LocalizedText {
                    string_id: "AV_LRC_EPISODE_PREFIX".into(),
                    substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                        "number".into(),
                        TextContent::String(episode.to_string()),
                    )]))),
                }
                .into(),
            );
        }
        if let Some(episode_title) = &self.episodeTitle {
            v.push(TextContent::String(episode_title.clone()));
        }

        v
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct RestrictionData {
    #[network(rename = "type")]
    #[ignx_serde(rename = "type")]
    pub restriction_type: String,
    pub reason: String,
    pub action: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct AccessControls {
    pub pinLength: u32,
    pub restrictionData: Option<RestrictionData>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ScheduledShow {
    pub title: Option<String>,
    pub synopsis: Option<String>,
    pub image: Option<String>,
    pub heroImage: Option<String>,
    #[network(alias = "startTime")]
    #[ignx_serde(alias = "startTime")]
    pub startTimeDate: Option<String>,
    #[network(alias = "endTime")]
    #[ignx_serde(alias = "endTime")]
    pub endTimeDate: Option<String>,
    pub rating: Option<String>,
    pub badges: Option<Badges>,
    pub showContext: Option<ShowContext>,
    pub accessControls: Option<AccessControls>,
}

impl Schedule for ScheduledShow {
    fn get_time_range(&self) -> TimeRange {
        parse_date_pair(self.startTimeDate.as_deref(), self.endTimeDate.as_deref())
            .map(
                |DatePair {
                     start_date,
                     end_date,
                 }| (start_date.timestamp_millis(), end_date.timestamp_millis()),
            )
            // we drop items that are missing valid start/end dates, so unwrap is safe here
            .unwrap_or_default()
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct LinearAiringItem {
    pub airingId: Option<String>,
    pub title: Option<String>,
    pub startTime: String,
    pub endTime: String,
    pub rating: Option<String>,
    pub synopsis: Option<String>,
    pub contentDescriptors: Vec<String>,
    pub context: Option<ShowContext>,
    pub heroImage: Option<String>,
    pub image: Option<String>,
    pub badges: Option<LinearBadges>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct LinearAiringAttributes {
    pub gti: Option<String>,
    pub stationLogo: Option<String>,
    pub stationName: Option<String>,
    pub entitlementMessaging: Option<EntitlementMessaging>,
    pub liveEventDateHeader: Option<LiveEventDateTime>,
    pub liveEventDateBadge: Option<LiveEventDateTime>,
}

impl Schedule for LinearAiringItem {
    fn get_time_range(&self) -> TimeRange {
        parse_date_pair(Some(&self.startTime), Some(&self.endTime))
            .map(
                |DatePair {
                     start_date,
                     end_date,
                 }| (start_date.timestamp_millis(), end_date.timestamp_millis()),
            )
            // we drop items that are missing valid start/end dates, so unwrap is safe here
            .unwrap_or_default()
    }
}

impl LinearAiringItem {
    pub fn get_apply_live(&self) -> bool {
        self.badges.as_ref().is_some_and(|b| b.applyLive)
    }
    pub fn synopsis_with_prefix(&self) -> Option<String> {
        let prefix = build_prefix_string(&self.context);
        build_description_string(&prefix, &self.synopsis)
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, EnumString)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum OverlayTextPosition {
    Center_Left,
    Center_Right,
    Center_Center,
    Top_Left,
    Top_Right,
    Top_Center,
    Bottom_Left,
    Bottom_Right,
    Bottom_Center,
}

#[derive(Clone, AsyncDeserialize)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub enum CustomerIntent {
    UNSET,
    MODAL_DISMISSED,
    SSM_DISMISSED,
    OPT_IN,
    NONE,
}

custom_network_parser!(CustomerIntent, |value: &Value| {
    let value = value.as_str().unwrap_or("NONE");
    Ok(match value {
        "UNSET" => CustomerIntent::UNSET,
        "MODAL_DISMISSED" => CustomerIntent::MODAL_DISMISSED,
        "SSM_DISMISSED" => CustomerIntent::SSM_DISMISSED,
        "OPT_IN" => CustomerIntent::OPT_IN,
        "NONE" => CustomerIntent::NONE,
        _ => CustomerIntent::NONE,
    })
});

impl fmt::Display for CustomerIntent {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CustomerIntent::UNSET => write!(f, "UNSET"),
            CustomerIntent::MODAL_DISMISSED => write!(f, "MODAL_DISMISSED"),
            CustomerIntent::SSM_DISMISSED => write!(f, "SSM_DISMISSED"),
            CustomerIntent::OPT_IN => write!(f, "OPT_IN"),
            CustomerIntent::NONE => write!(f, "NONE"),
        }
    }
}
#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TNFLocalizedStringContents {
    pub helpHubString: Option<String>,
    pub helpHubUrl: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize, Default)]
pub struct TNFProperties {
    pub icid: Option<String>,
    pub customerIntent: Option<CustomerIntent>,
    pub customerOptInSuccessful: Option<bool>,
    pub localizedStringContents: Option<TNFLocalizedStringContents>,
}

#[derive(NetworkParsed, AsyncDeserialize, Cacheable, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct RGBColor {
    pub red: u32,
    pub green: u32,
    pub blue: u32,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
#[cfg_attr(feature = "test_utils", derive(Default))]
pub struct CarouselItemData {
    pub transformItemId: Option<String>,
    pub title: Option<String>,
    pub synopsis: Option<String>,
    pub action: Option<Action>,
    pub deferredAction: Option<Action>,
    #[network(default)]
    #[ignx_serde(default)]
    pub actions: Vec<Action>,
    pub widgetType: Option<String>,
}

#[cfg(any(test, feature = "test_utils"))]
impl CarouselItemData {
    pub fn populated_default() -> Self {
        CarouselItemData {
            transformItemId: Some("item id".to_string()),
            title: Some("card title".to_string()),
            synopsis: Some("card synopsis".to_string()),
            action: Some(Action::create_transition_landing(None)),
            deferredAction: None,
            actions: vec![Action::create_transition_landing(None)],
            widgetType: Some("widget type".to_string()),
        }
    }

    pub fn with_id(self, id: Option<String>) -> Self {
        Self {
            transformItemId: id,
            ..self
        }
    }

    pub fn with_title(self, title: Option<String>) -> Self {
        Self { title, ..self }
    }

    pub fn with_synopsis(self, synopsis: Option<String>) -> Self {
        Self { synopsis, ..self }
    }

    pub fn with_action(self, action: Option<Action>) -> Self {
        let actions = if let Some(a) = action.clone() {
            vec![a]
        } else {
            vec![]
        };
        Self {
            action,
            actions,
            ..self
        }
    }

    pub fn with_actions(self, actions: Vec<Action>) -> Self {
        Self { actions, ..self }
    }

    pub fn with_widget_type(self, widget_type: Option<String>) -> Self {
        Self {
            widgetType: widget_type,
            ..self
        }
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct CurrentPeriod {
    pub periodClockText: Option<String>,
    pub periodClockAccessibilityText: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct OrderedParticipant {
    pub name: Option<String>,
    pub formattedScore: Option<String>,
    pub competitionFinalResult: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct ScoreSummary {
    pub orderedParticipants: Option<Vec<OrderedParticipant>>,
    pub scoreAccessibilityText: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct ScoreBug {
    pub currentPeriod: Option<CurrentPeriod>,
    pub scoreSummary: Option<ScoreSummary>,
    pub scoreBugLastUpdateTimestamp: Option<i64>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, Default, PartialEq, Serialize)]
pub struct EventMetadata {
    pub liveliness: Option<String>,
    pub liveEventDateHeader: Option<LiveEventDateTime>,
    pub liveEventDateBadge: Option<LiveEventDateTime>,
    pub venue: Option<String>,
    pub scoreBug: Option<ScoreBug>,
}

impl Id for CarouselItemData {
    type Id = Option<String>;

    fn id(&self) -> &Self::Id {
        // TODO: generate an unique ID for these items.
        // Apparently, `transformItemId` is optional: https://code.amazon.com/packages/AVLivingRoomBorgJvmTransform/blobs/3b95579668f36f9734e7e1d23de8c8f9d20b02b4/--/src/com/amazon/avlrc/transform/parser/swift/containerItem/model/ContainerItemModels.kt#L57-L59
        &self.transformItemId
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone, Default)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct TitleCardBaseMetadata {
    pub coverImage: Option<String>,
    pub boxartImage: Option<String>,
    pub titleLogoImage: Option<String>,
    pub providerLogoImage: Option<String>,
    pub poster2x3Image: Option<String>,
    pub heroImage: Option<String>,
    pub totalReviewCount: Option<u32>,
    pub overallRating: Option<f32>,
    pub gti: Option<String>,
    pub badges: Option<Badges>,
    pub publicReleaseDate: Option<i64>,
    pub runtimeSeconds: Option<u64>,
    pub entitlementStatus: Option<String>,
    pub entitlementMessaging: Option<EntitlementMessaging>,
    pub contentType: Option<String>,
    #[network(default)]
    #[ignx_serde(default)]
    pub genres: Vec<String>,
    pub watchProgress: Option<f32>,
    pub imageAlternateText: Option<String>,
    pub isEntitled: Option<bool>,
    pub offerText: Option<String>,
    pub isInWatchlist: Option<bool>,
    pub maturityRatingString: Option<String>,
    pub maturityRatingImage: Option<MaturityRatingImage>,
    pub regulatoryLabel: Option<String>,
    pub imageAttributes: Option<ImageAttributes>,
    pub contextualActions: Option<Vec<ClientAction>>,
}

impl<'a> From<&'a TitleCardBaseMetadata> for Option<&'a EntitlementMessaging> {
    fn from(value: &'a TitleCardBaseMetadata) -> Self {
        value.entitlementMessaging.as_ref()
    }
}

macro_rules! impl_from_title_card_base_metadata_for_entitlement {
    ($($type:ty),+) => {
        $(
            impl<'a> From<&'a TitleCardBaseMetadata> for Option<&'a $type> {
                fn from(value: &'a TitleCardBaseMetadata) -> Self {
                    let m: Option<&EntitlementMessaging> = value.into();
                    <&EntitlementMessaging as Into<Option<&$type>>>::into(m?)
                }
            }
        )+
    };
}

impl_from_title_card_base_metadata_for_entitlement!(
    EntitlementGlance,
    EntitlementMessage,
    TitleMetadataBadge,
    InformationalMessage,
    BuyboxMessage,
    EntitlementGlanceIcons,
    EntitlementMessageIcons,
    TitleMetadataBadgeLevel,
    BuyboxMessageIcons
);

#[cfg(test)]
mod test {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case::no_badges(None, false)]
    #[case::dont_apply_live(Some(LinearBadges {
        applyLive: false,
        ..Default::default()
    }), false)]
    #[case::apply_live(Some(LinearBadges {
        applyLive: true,
        ..Default::default()
    }), true)]
    fn test_apply_live_for_linear_airing_item(
        #[case] badges: Option<LinearBadges>,
        #[case] expected: bool,
    ) {
        let item = LinearAiringItem {
            badges,
            ..Default::default()
        };

        assert_eq!(item.get_apply_live(), expected);
    }

    #[test]
    fn test_synopsis_with_prefix_for_linear_airing_item() {
        let item = LinearAiringItem {
            context: Some(ShowContext {
                season: Some(1),
                episode: Some(2),
                episodeTitle: Some("episode title".to_string()),
            }),
            synopsis: Some("synopsis".to_string()),
            ..Default::default()
        };

        assert_eq!(
            item.synopsis_with_prefix(),
            Some("S1 E2 episode title - synopsis".to_string())
        );
    }

    #[rstest]
    #[case::all_context(
        ShowContext {
            season: Some(1),
            episode: Some(2),
            episodeTitle: Some("episode title".to_string()),
        }, vec![
        LocalizedText {
            string_id: "AV_LRC_TITLE_DETAILS_SYNOPSIS_SEASON".into(),
            substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                "number".into(),
                TextContent::String("1".to_string()),
            )]))),
        }.into(),
        LocalizedText {
            string_id: "AV_LRC_EPISODE_PREFIX".into(),
            substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                "number".into(),
                TextContent::String("2".to_string()),
            )])))
        }.into(),
        TextContent::String("episode title".to_string())]
    )]
    #[case::no_context(
        ShowContext {
            season: None,
            episode: None,
            episodeTitle: None,
        }, vec![]
    )]
    fn test_to_tts_messages_for_show_context(
        #[case] context: ShowContext,
        #[case] expected: Vec<TextContent>,
    ) {
        assert_eq!(context.to_tts_messages(), expected)
    }
}
