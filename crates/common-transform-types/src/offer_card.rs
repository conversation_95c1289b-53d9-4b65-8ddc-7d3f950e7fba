use crate::title_actions::TitleActionV2;
use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct OfferCard {
    pub card_id: String,
    pub actions: Vec<TitleActionV2>,
    pub offer_card_decoration: OfferCardDecoration,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct OfferCardDecoration {
    pub header: Option<CardHeader>,
    pub transaction_detail: Vec<CardText>,
    pub high_value_message: Option<CardBadge>,
    pub motivator_messages: Vec<CardText>,
    pub related_benefits: Option<CardRelatedBenefits>,
    pub background_color: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct CardImage {
    pub url: String,
    pub image_type: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct CardText {
    pub text: String,
    pub icon: Option<IconType>,
    pub text_type: TextType,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct CardBadge {
    pub text: String,
    pub level: BadgeLevel,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(tag = "type")]
pub enum CardHeader {
    Logo(CardLogo),
    TextWithBadge(CardHeaderTextWithBadge),
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct CardHeaderTextWithBadge {
    pub text: CardText,
    pub badge: Option<CardBadge>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct CardRelatedBenefits {
    pub text: CardText,
    pub images: Vec<CardImage>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct CardLogo {
    pub url: String,
    pub tags: HashMap<String, String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum IconType {
    Tick,
    Cross,
    Play,
    OfferIcon,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum TextType {
    Default,
    Heading,
    Emphasis,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum BadgeLevel {
    Info,
    InfoPill,
    Highlight,
    Success,
}
