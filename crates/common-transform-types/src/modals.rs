#![allow(nonstandard_style)]

use crate::actions::Action;
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::serde::de::AsyncDeserialize;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct BenefitImage {
    pub url: Option<String>,
    pub height: Option<u32>,
    pub width: Option<u32>,
    pub alternateText: Option<String>,
    pub gradientRequired: Option<bool>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct MarketingModalItem {
    pub swiftId: Option<String>,
    pub itemId: Option<String>,
    pub action: Option<Action>,
    pub benefitImage: Option<BenefitImage>,
    pub title: Option<String>,
    pub description: Option<String>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct MarketingModalButton {
    pub title: String,
    #[network(rename = "type")]
    #[ignx_serde(rename = "type")]
    pub button_type: String,
    pub action: Option<Action>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct ButtonAttribute {
    pub refMarker: Option<String>,
    pub focusState: Option<bool>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct MarketingModalAttributedButton {
    pub title: String,
    pub attributes: HashMap<String, ButtonAttribute>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct MarketingModalData {
    pub title: Option<String>,
    pub subtitle: Option<String>,
    pub marketingModalItems: Option<Vec<MarketingModalItem>>,
    pub clientSideMetrics: Option<String>,
    pub refMarker: Option<String>,
    pub containerMetadataRefMarker: Option<String>,
    pub theme: Option<String>,
    pub buttons: Option<Vec<MarketingModalButton>>,
    pub attributedButtons: Option<Vec<MarketingModalAttributedButton>>,
    pub notificationId: Option<String>,
}
