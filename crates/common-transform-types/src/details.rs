use crate::{container_items::Dimension, item_metadata::Badges};
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::color::Color;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, Default, PartialEq)]
#[network(resiliency = "DetailsPage")]
#[network(camelCaseAll)]
pub struct TitleDecorationInformationCore {
    pub audios: Vec<LanguageOption>,
    pub entity_type: String,
    pub gti: String,

    pub images: TitleDecorationImages,

    pub is_in_watchlist: bool,
    pub ratings_metadata: Rating,

    pub subtitles: Vec<LanguageOption>,
    pub synopsis: String,
    pub title: String,
    pub title_type: TitleType,

    #[network(flatten)]
    pub badges: Badges,

    pub current_episode_number: Option<i32>,
    pub maturity_rating: Option<MaturityRating>,
    pub release_date: Option<i64>,
    pub runtime_seconds: Option<i64>,

    pub upcoming_message: Option<String>,
    pub season_id: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct LanguageOption {
    pub locale: String,
    pub name: String,
}

#[derive(NetworkParsed, Clone, Default)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct TitleDecorationImages {
    #[network(alias = "heroImage")]
    pub hero: Option<String>,
    pub provider_logo: Option<String>,
    pub title: Option<String>,
    #[network(alias = "image16x9")]
    pub title16x9: Option<String>,
    pub title_logo: Option<String>,
    pub cover: Option<String>,
}

#[derive(NetworkParsed, Default, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct Rating {
    pub count: i64,
    pub rating: f64,
}

#[derive(NetworkParsed, Default, Clone, PartialEq)]
#[derive_test_only(Debug)]
#[network(SNAKE_CASE_ALL)]
#[network(default)]
pub enum TitleType {
    Live,
    Movie,
    Episode,
    Season,

    #[network(skip)]
    #[default]
    Unknown,
}

#[derive(NetworkParsed, Serialize, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
#[serde(rename_all = "camelCase")]
pub struct MaturityRating {
    pub regulatory_rating_image: Option<RegulatoryRatingImage>,
    pub regulatory_rating_string: String,
}

#[derive(NetworkParsed, Serialize, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct RegulatoryRatingImage {
    pub url: String,
    pub dimension: Dimension,
}

#[derive(NetworkParsed, Serialize, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct SlotEntitlementMessageColor {
    pub r: u32,
    pub g: u32,
    pub b: u32,
    pub a: u32,
}

impl Into<Color> for SlotEntitlementMessageColor {
    fn into(self) -> Color {
        Color {
            r: self.r.min(255) as u8,
            g: self.g.min(255) as u8,
            b: self.b.min(255) as u8,
            a: self.a.min(255) as u8,
        }
    }
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct SlotEntitlementMessage {
    pub message: Option<String>,
    pub messages: Option<Vec<String>>,
    pub icon: Option<EntitlementIcon>,
    pub level: Option<TagsLevel>,
    #[network(rename = "type")]
    pub entitlement_type: Option<TagsType>,
    pub color: Option<SlotEntitlementMessageColor>,
}

#[derive(NetworkParsed, Clone, PartialEq)]
#[derive_test_only(Debug)]
#[network(SNAKE_CASE_ALL)]
pub enum EntitlementIcon {
    OfferIcon,
    EntitledIcon,
    AdsIcon,
    ErrorIcon,
    TrendingIcon,
    WarningIcon,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(SNAKE_CASE_ALL)]
pub enum TagsLevel {
    Error,
    Info,
    InfoInactive,
    InfoHighlight,
}

impl std::fmt::Display for TagsLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TagsLevel::Error => write!(f, "ERROR"),
            TagsLevel::Info => write!(f, "INFO"),
            TagsLevel::InfoInactive => write!(f, "INFO_INACTIVE"),
            TagsLevel::InfoHighlight => write!(f, "INFO_HIGHLIGHT"),
        }
    }
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(SNAKE_CASE_ALL)]
pub enum TagsType {
    Badge,
    Message,
}
