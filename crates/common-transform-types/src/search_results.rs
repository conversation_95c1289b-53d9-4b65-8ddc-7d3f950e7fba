use crate::containers::ContainerWithResiliency;
use crate::filters::Filter;
use crate::subtitle::SubTitle;
use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::*;

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct SearchResultsResponse {
    pub containers: Vec<ContainerWithResiliency>,
    pub subtitle: SubTitle,
    pub filters: Vec<Filter>,
}
