#![allow(nonstandard_style)]

use cfg_test_attr_derive::{cfg_test_only, derive_test_only};
use ignx_compositron::serde::de::AsyncDeserialize;
use network_parser::prelude::*;
use network_parser_derive::*;
#[cfg_test_only]
use serde::Serialize;

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(Debug, PartialEq, Serialize)]
pub struct ImageV2 {
    pub url: Option<String>,
    pub aspectRatio: Option<String>,
}
