[package]
name = "common-transform-types"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
default = []
debug_impl = []
test_utils = []

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
network-parser.workspace = true
network-parser-derive.workspace = true
serde.workspace = true
serde_json.workspace = true
derive_more.workspace = true
cacheable-derive.workspace = true
cfg-test-attr-derive.workspace = true
strum.workspace = true
strum_macros.workspace = true
linear-common.workspace = true

[lints]
workspace = true

[dev-dependencies]
rstest.workspace = true
