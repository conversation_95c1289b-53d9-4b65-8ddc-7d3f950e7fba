use amzn_fable_tokens::*;
use fableous::typography::typography::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextVerticalAlignment;
use ignx_compositron::{compose, Composer};
use std::rc::*;

use fableous::typography::type_ramp::TypeRamp;

const TEST_ID: &str = "legal-messages";

#[Composer]
pub fn LegalMessages(
    ctx: &AppContext,
    #[into] messages: MaybeSignal<Vec<String>>,
) -> ColumnListComposable {
    compose! {
        ColumnList(items: messages, id: |item| item, item_builder: Rc::new(move |ctx: &AppContext, item: &String, idx| {
            compose! {
                Typography(content: item.to_owned(), type_ramp: TypeRamp::from(FableText::TYPE_LABEL100))
                    .vertical_alignment(TextVerticalAlignment::End)
                    .test_id(format!("legal-message-{}", idx))
            }
        }))
        .cross_axis_alignment(CrossAxisAlignment::End)
        .test_id(TEST_ID)
    }
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_exists, node_properties::NodeTypeProperties},
    };
    use rstest::*;

    #[rstest]
    #[case(vec![])]
    #[case(vec!["Line 1".to_string()])]
    #[case(vec!["Line 1".to_string(), "Line 2".to_string(), "Line 3".to_string()])]
    pub fn renders(#[case] messages: Vec<String>) {
        launch_test(
            {
                let messages = messages.clone();
                move |ctx| {
                    compose! {
                        LegalMessages(messages)
                    }
                }
            },
            move |_scope, mut test_renderer_game_loop| {
                let tree = test_renderer_game_loop.tick_until_done();
                let legal_messages = tree.find_by_test_id(TEST_ID);

                assert_node_exists!(&legal_messages);

                if messages.is_empty() {
                    return;
                }

                let all_typographies = legal_messages
                    .find_any_child_with()
                    .composable_type(ignx_compositron::test_utils::ComposableType::Label)
                    .find_all();

                for (i, typography) in all_typographies.iter().enumerate() {
                    let props = typography.borrow_props();

                    assert_eq!(
                        props.text,
                        Some(messages[i].clone()),
                        "Legal message content not rendered properly (maybe due to wrong ordering?)");

                    if let NodeTypeProperties::Text(text_props) = &props.node_type_props {
                        assert_eq!(
                            text_props.text_alignment,
                            Some(ClientTextAlign::End),
                            "Legal text must be aligned to the end (important for RTL)"
                        );
                    } else {
                        panic!("Legal text node not a text");
                    }

                    let text_layout = props.text_layout.as_ref().unwrap();
                    assert!(
                        !text_layout.is_text_truncated,
                        "Legal messages can not be truncated"
                    );
                }
            },
        );
    }
}
