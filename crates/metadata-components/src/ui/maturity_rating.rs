use common_transform_types::container_items::MaturityRatingImage;
use fableous::badges::label_badge::*;
use fableous::badges::maturity_rating_badge::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, compose_option, Composer};

const IMAGE_TEST_ID: &str = "maturity-rating-image";
const TEXT_TEST_ID: &str = "maturity-rating-text";
pub const MATURITY_RATING_TEST_ID: &str = "maturity-rating";

#[Composer]
pub fn MaturityRating(
    ctx: &AppContext,
    #[into] image: MaybeSignal<Option<MaturityRatingImage>>,
    #[into] text: MaybeSignal<Option<String>>,
) -> MemoComposable {
    let item_builder = Box::new(move |ctx: &AppContext| {
        let image = image.get();

        if let Some(image) = image {
            return compose_option! {
                Stack() {
                    MaturityRatingBadge(src: image.url, dimensions: Some(MaturityRatingBadgeDimensions {
                        height: (image.dimension.height as f32).into(),
                        width: (image.dimension.width as f32).into()
                    }))
                    .test_id(IMAGE_TEST_ID)
                }
                .test_id(MATURITY_RATING_TEST_ID)
            };
        }

        let text = text.get();

        text.map(|text| {
            compose! {
                Stack() {
                    LabelBadge(text: MaybeSignal::from(TextContent::from(text)), size: LabelBadgeSize::SIZE400, color_scheme: LabelBadgeColorScheme::SECONDARY)
                        .test_id(TEXT_TEST_ID)
                }
                .test_id(MATURITY_RATING_TEST_ID)
            }
        })
    });

    compose! {
        Memo(item_builder)
    }
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use common_transform_types::container_items::Dimension;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{
            assert_node_does_not_exist, assert_node_exists, node_properties::NodeTypeProperties,
            ComposableType, TestRendererGameLoop,
        },
    };

    fn assert_image(
        test_renderer_game_loop: &mut TestRendererGameLoop,
        expected_url: &str,
        expected_height: f32,
        expected_width: f32,
    ) {
        let tree = test_renderer_game_loop.tick_until_done();
        let image = tree.find_by_test_id(IMAGE_TEST_ID);
        let text = tree.find_by_test_id(TEXT_TEST_ID);

        assert_node_exists!(&image);
        assert_node_does_not_exist!(&text);

        assert_eq!(image.borrow_props().layout.size.width, expected_width);
        assert_eq!(image.borrow_props().layout.size.height, expected_height);

        if let NodeTypeProperties::Image(image_props) = &image.borrow_props().node_type_props {
            assert!(image_props.uri.as_ref().unwrap().contains(expected_url));
        } else {
            panic!("node is not an image node");
        }
    }

    fn assert_text(test_renderer_game_loop: &mut TestRendererGameLoop, expected_text: &str) {
        let tree = test_renderer_game_loop.tick_until_done();
        let image = tree.find_by_test_id(IMAGE_TEST_ID);
        let text = tree.find_by_test_id(TEXT_TEST_ID);

        assert_node_exists!(&text);
        assert_node_does_not_exist!(&image);

        let label = text
            .find_any_child_with()
            .composable_type(ComposableType::Label)
            .find_first();

        assert_node_exists!(&label);

        assert_eq!(
            label.borrow_props().text,
            Some(expected_text.to_string()),
            "Incorrect text rendered"
        );
    }

    fn assert_nothing(test_renderer_game_loop: &mut TestRendererGameLoop) {
        let tree = test_renderer_game_loop.tick_until_done();
        let image = tree.find_by_test_id(IMAGE_TEST_ID);
        let text = tree.find_by_test_id(TEXT_TEST_ID);

        assert_node_does_not_exist!(&text);
        assert_node_does_not_exist!(&image);
    }

    #[test]
    pub fn renders_with_image() {
        launch_test(
            move |ctx| {
                compose! {
                    MaturityRating(image: Some(MaturityRatingImage { url: "image-url".to_string(), dimension: Dimension { width: 50, height: 10 }}), text: Some("text".to_string()))
                }
            },
            move |_scope, mut test_renderer_game_loop| {
                assert_image(&mut test_renderer_game_loop, "image-url", 36.0, 180.0);
            },
        );
    }

    #[test]
    pub fn renders_with_text() {
        launch_test(
            move |ctx| {
                compose! {
                    MaturityRating(image: None, text: Some("text".to_string()))
                }
            },
            move |_scope, mut test_renderer_game_loop| {
                assert_text(&mut test_renderer_game_loop, "text");
            },
        );
    }

    #[test]
    pub fn renders_with_neither_image_or_text() {
        launch_test(
            move |ctx| {
                compose! {
                    MaturityRating(image: None, text: None)
                }
            },
            move |_scope, mut test_renderer_game_loop| {
                assert_nothing(&mut test_renderer_game_loop);
            },
        );
    }

    #[test]
    pub fn updates_when_signals_change() {
        launch_test(
            move |ctx| {
                let (image, set_image) = create_signal(
                    ctx.scope(),
                    Some(MaturityRatingImage {
                        url: "image-url".to_string(),
                        dimension: Dimension {
                            width: 50,
                            height: 10,
                        },
                    }),
                );
                let (text, set_text) = create_signal(ctx.scope(), Some("text".to_string()));

                provide_context(ctx.scope(), set_image);
                provide_context(ctx.scope(), set_text);

                compose! {
                    Stack() {
                        MaturityRating(image, text)
                    }
                }
            },
            move |scope, mut test_renderer_game_loop| {
                let set_image =
                    use_context::<WriteSignal<Option<MaturityRatingImage>>>(scope).unwrap();
                let set_text = use_context::<WriteSignal<Option<String>>>(scope).unwrap();

                assert_image(&mut test_renderer_game_loop, "image-url", 36.0, 180.0);

                set_image.set(Some(MaturityRatingImage {
                    url: "new-image-url".to_string(),
                    dimension: Dimension {
                        width: 75,
                        height: 20,
                    },
                }));

                assert_image(&mut test_renderer_game_loop, "new-image-url", 36.0, 135.0);

                set_image.set(None);

                assert_text(&mut test_renderer_game_loop, "text");

                set_text.set(Some("new-text".to_string()));

                assert_text(&mut test_renderer_game_loop, "new-text");

                set_text.set(None);

                assert_nothing(&mut test_renderer_game_loop);
            },
        );
    }
}
