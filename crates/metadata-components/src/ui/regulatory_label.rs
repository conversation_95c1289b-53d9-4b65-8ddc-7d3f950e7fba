use amzn_fable_tokens::*;
use fableous::badges::label_badge::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};

const TEXT_TEST_ID: &str = "regulatory-label-text";
pub const REGULATORY_LABEL_TEST_ID: &str = "regulatory-label";

#[Composer]
pub fn RegulatoryLabel(
    ctx: &AppContext,
    #[into] text: MaybeSignal<Option<String>>,
) -> MemoComposable {
    let item_builder = Box::new(move |ctx: &AppContext| {
        let text = text.get();

        text.map(|text| {
            compose! {
                Stack() {
                    LabelBadge(text: MaybeSignal::from(TextContent::from(text)), size: LabelBadgeSize::SIZE400, color_scheme: LabelBadgeColorScheme::SECONDARY)
                        .test_id(TEXT_TEST_ID)
                }
                .test_id(REGULATORY_LABEL_TEST_ID)
                .padding(Padding::new(FableSpacing::SPACING054, 0.0, 0.0, 0.0))
            }
        })
    });

    compose! {
        Memo(item_builder)
    }
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{
            assert_node_does_not_exist, assert_node_exists, ComposableType, TestRendererGameLoop,
        },
    };

    fn assert_text(test_renderer_game_loop: &mut TestRendererGameLoop, expected_text: &str) {
        let tree = test_renderer_game_loop.tick_until_done();
        let text = tree.find_by_test_id(TEXT_TEST_ID);

        assert_node_exists!(&text);

        let label = text
            .find_any_child_with()
            .composable_type(ComposableType::Label)
            .find_first();

        assert_node_exists!(&label);

        assert_eq!(
            label.borrow_props().text,
            Some(expected_text.to_string()),
            "Incorrect text rendered"
        );
    }

    fn assert_nothing(test_renderer_game_loop: &mut TestRendererGameLoop) {
        let tree = test_renderer_game_loop.tick_until_done();
        let text = tree.find_by_test_id(TEXT_TEST_ID);

        assert_node_does_not_exist!(&text);
    }

    #[test]
    pub fn renders_with_text() {
        launch_test(
            move |ctx| {
                compose! {
                    RegulatoryLabel(text: Some("text".to_string()))
                }
            },
            move |_scope, mut test_renderer_game_loop| {
                assert_text(&mut test_renderer_game_loop, "text");
            },
        );
    }

    #[test]
    pub fn renders_without_text() {
        launch_test(
            move |ctx| {
                compose! {
                    RegulatoryLabel(text: None)
                }
            },
            move |_scope, mut test_renderer_game_loop| {
                assert_nothing(&mut test_renderer_game_loop);
            },
        );
    }

    #[test]
    pub fn updates_when_signals_change() {
        launch_test(
            move |ctx| {
                let (text, set_text) = create_signal(ctx.scope(), Some("text".to_string()));

                provide_context(ctx.scope(), set_text);

                compose! {
                    Stack() {
                        RegulatoryLabel(text)
                    }
                }
            },
            move |scope, mut test_renderer_game_loop| {
                let set_text = use_context::<WriteSignal<Option<String>>>(scope).unwrap();

                assert_text(&mut test_renderer_game_loop, "text");

                set_text.set(Some("new-text".to_string()));

                assert_text(&mut test_renderer_game_loop, "new-text");

                set_text.set(None);

                assert_nothing(&mut test_renderer_game_loop);
            },
        );
    }
}
