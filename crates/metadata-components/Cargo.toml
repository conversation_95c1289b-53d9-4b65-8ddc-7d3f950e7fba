[package]
name = "metadata-components"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
fableous.workspace = true
lrc-image.workspace = true
chrono.workspace = true
container-types.workspace = true
common-transform-types.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
rstest.workspace = true

[lints]
workspace = true
