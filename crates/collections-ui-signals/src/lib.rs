use ignx_compositron::reactive::*;

/// `CollectionsPageStateOverrides` provides configuration options page loading behavior
///
/// # Fields
/// * `should_reload` - Signal controlling whether page should perform a full reload.
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct CollectionsPageStateOverrides {
    pub should_reload: RwSignal<bool>,
}

impl CollectionsPageStateOverrides {
    pub fn new(scope: Scope) -> CollectionsPageStateOverrides {
        Self {
            should_reload: create_rw_signal(scope, false),
        }
    }
}
