[package]
name = "details"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
acm-config.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
network-parser.workspace = true
network-parser-derive.workspace = true
serde.workspace = true
serde_json.workspace = true
collections-ui.workspace = true
details-integration.workspace = true
checkout-navigation.workspace = true
common-transform-types.workspace = true
container-list.workspace = true
container-types.workspace = true
containers.workspace = true
fableous.workspace = true
amzn-fable-tokens.workspace = true
auth.workspace = true
location.workspace = true
lrc-image.workspace = true
network.workspace = true
app-config.workspace = true
router.workspace = true
navigation-menu.workspace = true
title-details.workspace = true
media-background.workspace = true
transition-executor.workspace = true
cfg-test-attr-derive.workspace = true
log.workspace = true
watchlist-service.workspace = true
personalization-feedback-service.workspace = true
details-derive.workspace = true
profile-manager.workspace = true
mockall_double.workspace = true
strum.workspace = true
strum_macros.workspace = true
cross-app-events.workspace = true
app-reporting.workspace = true
playback-navigation.workspace = true
taps-parameters.workspace = true
rust-features.workspace = true
mockall.workspace = true
beekeeper.workspace = true
popups.workspace = true
app-events.workspace = true
genai-recap.workspace = true
cache.workspace = true
title-reaction-buttons.workspace = true
clickstream.workspace = true
contextual-menu.workspace = true
contextual-menu-types.workspace = true
watch-modal.workspace = true
liveliness-types.workspace = true
container-orchestrator.workspace = true
transform-clients.workspace = true
container-item-types.workspace = true
uuid.workspace = true
playback.workspace = true
modal-manager.workspace = true
educational-cx.workspace = true
buybox.workspace = true

[dev-dependencies]
# Just used to help construct types
contextual-menu.workspace = true
contextual-menu-types.workspace = true
mockall.workspace = true
rstest.workspace = true
insta.workspace = true
cache = { workspace = true, features = ["test-utils"] }
common-transform-types = { workspace = true, features = ["test_utils"] }
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
container-types = { workspace = true, features = ["example_data"] }
synchronized-state-store = { workspace = true, features = ["test-utils"] }
rust-features.workspace = true
beekeeper = { workspace = true, features = ["mocks"] }
clickstream = { workspace = true, features = ["test_utils"] }
router = { workspace = true, features = ["test_utils"] }
network = { workspace = true, features = ["mock_network"] }
testing_logger = { workspace = true }
watch-modal = { workspace = true, features = ["test_utils"] }
container-orchestrator = { workspace = true, features = ["test_utils"] }
sports-favorites-utils = { workspace = true, features = ["test_utils"] }
collections-ui = { workspace = true, features = ["test_utils"] }
firetv = { workspace = true, features = ["test_utils"] }
playback = { workspace = true, features = ["test_utils"] }
educational-cx = { workspace = true, features = ["test_utils"] }
serial_test.workspace = true
test-utils = { workspace = true, features = ["test_utils"] }

[lints]
workspace = true


[features]
test_utils = ["container-orchestrator/test_utils", "sports-favorites-utils/test_utils", "collections-ui/test_utils"]
details_example = []
default = []
collections_mock_network = ["collections-ui/example_data"]
debug_impl = []
