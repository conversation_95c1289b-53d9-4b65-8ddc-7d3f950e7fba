use crate::context::hooks::{empty_stack_fallback, use_details_page_context};
use crate::ui::test_ids::DETAILS_ENTITLEMENT_ROW_TEST_ID;
use common_transform_types::details::EntitlementIcon;
use details_derive::use_details_context_or_fallback;
use fableous::eoc::*;
use ignx_compositron::prelude::*;
use ignx_compositron::show::*;
use ignx_compositron::{compose, Composer};

#[Composer]
pub fn EntitlementRow(ctx: &AppContext) -> StackComposable {
    let details_ctx = use_details_context_or_fallback!(ctx, empty_stack_fallback);

    let atf_response = details_ctx.borrow().state.page_resources.atf_response;

    let entitlement_info = Signal::derive(ctx.scope(), move || {
        atf_response.with(|response| {
            let details_response = match response {
                None => return None,
                Some(val) => val,
            };

            details_response
                .entitlement_message
                .clone()
                .and_then(|info| {
                    let msg = match info.message {
                        None => return None,
                        Some(msg) => msg,
                    };

                    let entitlement_type = match info.icon {
                        None => return None,
                        Some(icon) => match icon {
                            EntitlementIcon::OfferIcon => EntitlementType::Unentitled,
                            EntitlementIcon::EntitledIcon => EntitlementType::Entitled,
                            EntitlementIcon::AdsIcon => EntitlementType::Entitled,
                            EntitlementIcon::ErrorIcon => EntitlementType::Unavailable,
                            EntitlementIcon::TrendingIcon => EntitlementType::None,
                            EntitlementIcon::WarningIcon => EntitlementType::Unavailable,
                        },
                    };

                    Some((msg, entitlement_type))
                })
        })
    });

    let entitlement_type = Signal::derive(ctx.scope(), move || {
        entitlement_info.with(|val| match val {
            None => EntitlementType::None,
            Some(val) => val.1,
        })
    });
    let entitlement_string = Signal::derive(ctx.scope(), move || {
        entitlement_info.with(|val| match val {
            None => String::default(),
            Some(val) => val.0.clone(),
        })
    });

    let show_builder: Box<ConditionalComposableBuilder> = Box::new(move |ctx| {
        let composable = compose! {
            Stack() {
                EOC(entitlement_type, content: entitlement_string)
                    .test_id(DETAILS_ENTITLEMENT_ROW_TEST_ID)
            }.padding(Padding::new(0.0, 0.0, 21.0, 0.0))
        };
        composable.into_widget()
    });

    let should_display = Signal::derive(ctx.scope(), move || {
        entitlement_info.with(|val| val.is_some())
    });

    compose! {
        Stack() {
            Show(if_builder: show_builder, condition: should_display.into(), else_builder: None)
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::hooks::create_or_use_details_page_context;
    use crate::context::test_utils::{create_or_use_details_context_test, mock_out_atf_response};
    use crate::network::mock_responses::MOCK_RESPONSE_VOD_MPO_ATF;
    use amzn_fable_tokens::FableIcon;
    use ignx_compositron::test_utils::assert_node_exists;
    use mockall::TimesRange;
    #[test]
    fn should_render_entitlement_message_and_icon_prime() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let _details_ctx = create_or_use_details_page_context(&ctx);

                compose! {
                    EntitlementRow()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id(DETAILS_ENTITLEMENT_ROW_TEST_ID);
                assert_node_exists!(node.clone());

                let label_text = node
                    .find_any_child_with()
                    .test_id(EOC_LABEL_TEST_ID)
                    .find_first();

                let label_props = label_text.borrow_props();
                assert_eq!(
                    label_props.text.clone().unwrap(),
                    "Watch with a 7 day free Prime trial, auto renews at €6.99/month"
                );

                let eoc_icon = node
                    .find_any_child_with()
                    .test_id(EOC_ICON_TEST_ID)
                    .find_first();

                let icon_props = eoc_icon.borrow_props();
                assert_eq!(icon_props.text.clone().unwrap(), FableIcon::ENTITLED);
            },
        )
    }

    #[test]
    fn should_render_entitlement_message_and_icon_offer() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_VOD_MPO_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                    EntitlementRow()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id(DETAILS_ENTITLEMENT_ROW_TEST_ID);
                assert_node_exists!(node.clone());

                let label_text = node
                    .find_any_child_with()
                    .test_id(EOC_LABEL_TEST_ID)
                    .find_first();

                let label_props = label_text.borrow_props();
                assert_eq!(label_props.text.clone().unwrap(), "Available to buy");

                let eoc_icon = node
                    .find_any_child_with()
                    .test_id(EOC_ICON_TEST_ID)
                    .find_first();

                let icon_props = eoc_icon.borrow_props();
                assert_eq!(icon_props.text.clone().unwrap(), FableIcon::STORE_FILLED);
            },
        )
    }
}
