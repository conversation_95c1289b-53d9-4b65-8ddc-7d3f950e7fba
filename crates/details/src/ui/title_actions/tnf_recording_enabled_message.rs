use crate::context::hooks::{empty_stack_fallback, use_details_page_context};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::localized_text::DetailsString::{
    PV_LE_IP_RECORDINGS_SCHEDULED, PV_LE_IP_RECORD_ERROR,
};
use amzn_fable_tokens::FableColor;
use common_transform_types::container_items::CustomerIntent;
use details_derive::use_details_context_or_fallback;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, compose_option, Composer};

pub const RECORDING_ENABLED_LABEL_TEST_ID: &str = "recording-enabled-test-id";

const DETAILS_LOGGER: DetailsDeferredLogger =
    build_deferred_logger("tnf_recording_enabled_message");

#[Composer]
pub fn TnfRecordingEnabledMessage(ctx: &AppContext) -> StackComposable {
    let details_ctx = use_details_context_or_fallback!(ctx, empty_stack_fallback);
    let tnf_signal = details_ctx.borrow().state.tnf_property;

    compose! {
        Stack() {
            Memo(item_builder: Box::new(move |ctx| {
                tnf_signal
                    .get()
                    .and_then(|tnf_property| {
                        match (tnf_property.customerIntent, tnf_property.customerOptInSuccessful) {
                            (Some(CustomerIntent::OPT_IN), _) |
                            (_, Some(true)) => Some(PV_LE_IP_RECORDINGS_SCHEDULED.get_localised_text()),
                            (_, Some(false)) => Some(PV_LE_IP_RECORD_ERROR.get_localised_text()),
                            _ => None
                        }
                    })
                    .and_then(|content| {
                        compose_option! {
                            TypographyLabel100(
                                content: content,
                                color: get_ignx_color(FableColor::SECONDARY)
                            )
                            .test_id(RECORDING_ENABLED_LABEL_TEST_ID)
                        }
                    })
            }))
        }
        .padding(Padding::new(0.0, 0.0, 21.0, 0.0))
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::create_or_use_details_context_test;
    use common_transform_types::container_items::TNFProperties;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use rstest::rstest;

    #[rstest]
    #[case(Some(TNFProperties { customerIntent: Some(CustomerIntent::OPT_IN), ..Default::default()}), true)]
    #[case(Some(TNFProperties { customerIntent: Some(CustomerIntent::SSM_DISMISSED), ..Default::default()}), false)]
    #[case(Some(TNFProperties { customerIntent: None, ..Default::default()}), false)]
    #[case(None, false)]
    fn should_render_when(
        #[case] tnf_property: Option<TNFProperties>,
        #[case] should_render: bool,
    ) {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);

                details_ctx
                    .borrow_mut()
                    .state
                    .tnf_property
                    .set(tnf_property);

                compose! {
                    TnfRecordingEnabledMessage()
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let tnf_message = node_tree.find_by_test_id(RECORDING_ENABLED_LABEL_TEST_ID);

                if should_render {
                    assert_node_exists!(&tnf_message);
                } else {
                    assert_node_does_not_exist!(&tnf_message);
                }
            },
        )
    }
}
