use crate::ui::test_ids::DETAILS_PAGE_INFORMATIONAL_MESSAGE_TEST_ID;
use amzn_fable_tokens::{FableColor, FableText};
use fableous::typography::{multiline_typography::*, type_ramp::*};
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::*;
use ignx_compositron::show::ShowComposable;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};

#[Composer]
pub fn InformationalMessages(
    ctx: &AppContext,
    informational_messages: RwSignal<Option<Vec<String>>>,
) -> ShowComposable {
    let should_show = Signal::derive(
        ctx.scope(),
        move || matches!(informational_messages.get(), Some(vec) if !vec.is_empty()),
    );
    let content = Signal::derive(ctx.scope(), move || {
        if let Some(vec) = informational_messages.get() {
            vec.iter().map(|s| TextContent::String(s.clone())).collect()
        } else {
            vec![TextContent::String("".into())]
        }
    });

    compose! {
        if should_show.get() {
            Column() {
                MultilineTypography(
                    content,
                    color: get_ignx_color(FableColor::COOL400),
                    type_ramp: <FableText as Into<TypeRamp>>::into(FableText::TYPE_LABEL200),
                )
            }
            .padding(Padding::new(0.0, 0.0, 21.0, 0.0))
            .test_id(DETAILS_PAGE_INFORMATIONAL_MESSAGE_TEST_ID)
            .width(850.0)
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, ComposableType};
    #[test]
    fn should_show_information_message_if_signal_is_not_none() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let msgs_sig = create_rw_signal(
                    ctx.scope(),
                    Some(vec!["Hello".to_string(), "world".to_string()]),
                );
                provide_context(ctx.scope(), msgs_sig);

                compose! {
                    InformationalMessages(informational_messages: msgs_sig)
                }
            },
            |scope, mut test_game_loop| {
                let msgs_sig =
                    use_context::<RwSignal<Option<Vec<String>>>>(scope).expect("Msg sig to exist!");

                let node_tree = test_game_loop.tick_until_done();
                let info_msg =
                    node_tree.find_by_test_id(DETAILS_PAGE_INFORMATIONAL_MESSAGE_TEST_ID);
                let text = info_msg
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_all();
                assert_eq!(text[0].borrow_props().text, Some("Hello".to_string()));
                assert_eq!(text[1].borrow_props().text, Some("world".to_string()));

                msgs_sig.set(Some(vec!["Goodbye".to_string(), "moon".to_string()]));
                let node_tree = test_game_loop.tick_until_done();

                let text = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .find_all();
                assert_eq!(text[0].borrow_props().text, Some("Goodbye".to_string()));
                assert_eq!(text[1].borrow_props().text, Some("moon".to_string()));
            },
        )
    }

    #[test]
    fn should_not_show_information_message_if_signal_is_none() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let msg_sig = create_rw_signal(ctx.scope(), None);

                compose! {
                    InformationalMessages(informational_messages: msg_sig)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let info_msg =
                    node_tree.find_by_test_id(DETAILS_PAGE_INFORMATIONAL_MESSAGE_TEST_ID);
                assert_node_does_not_exist!(info_msg);
            },
        )
    }
}
