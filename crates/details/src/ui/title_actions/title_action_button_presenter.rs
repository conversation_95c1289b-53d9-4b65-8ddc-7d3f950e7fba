use crate::context::state::state_creators::title_actions::TitleActionsState;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::localized_text::DetailsString;
use crate::modules::title_actions::types::{TitleActionHeight, TitleActionItem, TitleActionType};
use crate::modules::title_actions_helpers::get_playback_progress;
use crate::ui::title_actions::title_action_button_presenter::TitleActionButtonPresentation::{
    IconButton, PlayButton, PrimaryButton, ReactionButton, ToolTipButton,
};
use amzn_fable_tokens::FableIcon;
use cfg_test_attr_derive::derive_test_only;
use details_derive::{get_some_or_return_value, get_some_or_return_with_val_and_error_message};
use fableous::buttons::play_button::PlayButtonHeight;
use fableous::buttons::primary_button::PrimaryButtonVariant;
use fableous::progress_bar::ProgressBarVariant;
use ignx_compositron::prelude::{create_memo, Signal};
use ignx_compositron::reactive::{MaybeSignal, Scope, SignalGet};
use ignx_compositron::text::TextContent;
use location::RustPage;
use title_reaction_buttons::{state::title_reaction_state::ReactionState, ReactionButtonVariant};
use watch_modal::metrics::ssm_reporters::report_on_ssm_watch_button_rendered_metric;

const DETAILS_LOGGER: DetailsDeferredLogger =
    build_deferred_logger("title_action_button_presenter");

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum TitleActionButtonPresentation {
    IconButton {
        label: MaybeSignal<TextContent>,
        icon: MaybeSignal<String>,
    },
    PrimaryButton {
        variant: MaybeSignal<PrimaryButtonVariant>,
        disabled: bool,
    },
    PlayButton {
        content: TextContent,
        variant: ProgressBarVariant,
        progress: f32,
        rounded: bool,
        height: PlayButtonHeight,
    },
    ReactionButton {
        reaction_sig: MaybeSignal<ReactionState>,
        variant: ReactionButtonVariant,
    },
    ToolTipButton {
        height: TitleActionHeight,
        label: TextContent,
        tool_tip: TextContent,
    },
}

fn static_str(value: &'_ str) -> TextContent {
    TextContent::String(value.to_string())
}

fn static_maybe_sig(value: &'_ str) -> MaybeSignal<String> {
    MaybeSignal::Static(value.to_string())
}

fn static_str_maybe_signal(value: &'_ str) -> MaybeSignal<TextContent> {
    MaybeSignal::Static(static_str(value))
}

pub fn get_presentation_for_resume(
    item: &'_ TitleActionItem,
) -> Option<TitleActionButtonPresentation> {
    let unwrapped_action = get_some_or_return_with_val_and_error_message!(
        &item.data,
        "[get_presentation_for_resume] Unable get data for resume action",
        None
    );
    let progress = get_some_or_return_with_val_and_error_message!(
        get_playback_progress(unwrapped_action),
        "[get_presentation_for_resume] Unable to get progress for playback action",
        None
    );

    let height = match item.height {
        TitleActionHeight::OneLine => PlayButtonHeight::Size400,
        TitleActionHeight::TwoLines => PlayButtonHeight::Size800,
    };

    Some(PlayButton {
        content: DetailsString::AV_LRC_RESUME.get_localised_text(),
        variant: ProgressBarVariant::VOD,
        progress,
        rounded: true,
        height,
    })
}

pub fn get_presentation_for_suppression(
    item: &'_ TitleActionItem,
) -> Option<TitleActionButtonPresentation> {
    let unwrapped_action = get_some_or_return_value!(&item.data, None);
    let is_disabled_suppression = unwrapped_action.childActions.is_empty();
    let label = unwrapped_action.titleAction.label.clone();

    Some(PrimaryButton {
        variant: MaybeSignal::Static(PrimaryButtonVariant::TextSize800(label.into())),
        disabled: is_disabled_suppression,
    })
}

fn get_primary_variant_based_on_height(
    height: TitleActionHeight,
    content: TextContent,
    icon: Option<String>,
) -> PrimaryButtonVariant {
    match height {
        TitleActionHeight::OneLine => match icon {
            Some(icon) => PrimaryButtonVariant::IconAndTextSize400(icon, content),
            None => PrimaryButtonVariant::TextSize400(content),
        },
        TitleActionHeight::TwoLines => match icon {
            Some(icon) => PrimaryButtonVariant::IconAndTextSize800(icon, content),
            None => PrimaryButtonVariant::TextSize800(content),
        },
    }
}

fn get_watchlist_variant(
    title_action_type: &TitleActionType,
    reactive_scope: Scope,
    is_primary: bool,
    item_details: &'_ TitleActionItem,
    title_actions_state: &'_ TitleActionsState,
) -> Option<TitleActionButtonPresentation> {
    let watchlist_icon = Signal::derive(reactive_scope, {
        let watchlist_sig = title_actions_state.is_in_watchlist;
        move || {
            let in_watchlist = watchlist_sig.get();
            if in_watchlist {
                FableIcon::CHECK.to_string()
            } else {
                FableIcon::ADD.to_string()
            }
        }
    });

    let label = match title_action_type {
        TitleActionType::WatchListAndRecordButton => DetailsString::PV_LE_IP_WATCHLIST_AND_RECORD,
        _ => DetailsString::AV_LRC_watchlist_tooltip_text,
    }
    .get_localised_text();

    if is_primary {
        let variant = create_memo(reactive_scope, {
            let height = item_details.height.clone();
            move |_| {
                let icon = watchlist_icon.get();
                get_primary_variant_based_on_height(height.clone(), label.clone(), Some(icon))
            }
        });

        Some(PrimaryButton {
            variant: variant.into(),
            disabled: false,
        })
    } else {
        Some(IconButton {
            label: label.into(),
            icon: watchlist_icon.into(),
        })
    }
}

pub fn get_presentation_information_for_button(
    reactive_scope: Scope,
    item_details: &'_ TitleActionItem,
    is_primary: bool,
    title_actions_state: &'_ TitleActionsState,
) -> Option<TitleActionButtonPresentation> {
    match item_details.name {
        TitleActionType::PlayFromBeginningButton => {
            if is_primary {
                return Some(PrimaryButton {
                    variant: MaybeSignal::Static(PrimaryButtonVariant::TextSize800(
                        DetailsString::AV_LRC_START_OVER_TEXT.get_localised_text(),
                    )),
                    disabled: false,
                });
            }
            Some(IconButton {
                label: DetailsString::AV_LRC_START_OVER_TEXT.get_localised_text_maybe_signal(),
                icon: static_maybe_sig(FableIcon::START_OVER),
            })
        }
        TitleActionType::ResumeButton => get_presentation_for_resume(item_details),
        TitleActionType::ForcePlaybackButton => Some(PrimaryButton {
            variant: MaybeSignal::Static(PrimaryButtonVariant::TextSize800(
                DetailsString::AV_LRC_START_PLAYING_BUTTON_TEXT.get_localised_text(),
            )),
            disabled: false,
        }),
        TitleActionType::WatchListButton | TitleActionType::WatchListAndRecordButton => {
            get_watchlist_variant(
                &item_details.name,
                reactive_scope,
                is_primary,
                item_details,
                title_actions_state,
            )
        }
        TitleActionType::WatchTrailerButton => {
            if is_primary {
                let item_height = item_details.height.clone();
                let title_action_label = item_details.data.as_ref().map_or(
                    DetailsString::AV_LRC_trailer_tooltip_text.get_localised_text(),
                    |data| TextContent::String(data.titleAction.label.clone()),
                );

                return Some(PrimaryButton {
                    variant: get_primary_variant_based_on_height(
                        item_height,
                        title_action_label,
                        Some(FableIcon::TRAILER.to_string()),
                    )
                    .into(),
                    disabled: false,
                });
            }
            Some(IconButton {
                label: DetailsString::AV_LRC_trailer_tooltip_text.get_localised_text_maybe_signal(),
                icon: FableIcon::TRAILER.to_string().into(),
            })
        }
        TitleActionType::RecapButton => {
            let label: TextContent = "Recap".to_string().into();
            Some(IconButton {
                label: label.into(),
                icon: FableIcon::RECAP.into(),
            })
        }
        TitleActionType::ReactionsThumbsUpButton => Some(ReactionButton {
            reaction_sig: title_actions_state.reaction_state.into(),
            variant: ReactionButtonVariant::Like,
        }),
        TitleActionType::ReactionsThumbsDownButton => Some(ReactionButton {
            reaction_sig: title_actions_state.reaction_state.into(),
            variant: ReactionButtonVariant::Dislike,
        }),
        TitleActionType::MoreTitleDetailsButton => Some(IconButton {
            label: DetailsString::AV_LRC_DETAILS_PAGE_MORE_TITLE_DETAILS_BUTTON
                .get_localised_text_maybe_signal(),
            icon: FableIcon::INFO.to_string().into(),
        }),
        TitleActionType::ExploreButton => Some(IconButton {
            label: DetailsString::Starlight_Explore.get_localised_text_maybe_signal(),
            icon: FableIcon::EXPLORE.to_string().into(),
        }),
        TitleActionType::CustomerServiceWidgetsButton => Some(PrimaryButton {
            variant: get_primary_variant_based_on_height(
                item_details.height.clone(),
                DetailsString::AV_LRC_DETAILS_PAGE_LIVE_EVENT_FAQ_BUTTON.get_localised_text(),
                None,
            )
            .into(),
            disabled: false,
        }),
        TitleActionType::SuppressionButton => get_presentation_for_suppression(item_details),
        TitleActionType::PlayRegularButton | TitleActionType::VPPAPlayRegularButton => {
            let unwrapped_action = get_some_or_return_value!(&item_details.data, None);
            let item_height = item_details.height.clone();
            let title_action_label = unwrapped_action.titleAction.label.clone();

            Some(PrimaryButton {
                variant: get_primary_variant_based_on_height(
                    item_height,
                    TextContent::String(title_action_label),
                    Some(FableIcon::PLAY.to_string()),
                )
                .into(),
                disabled: false,
            })
        }
        TitleActionType::SVODAcquisitionButton
        | TitleActionType::TVODAcquisitionButton
        | TitleActionType::MoreWaysButton
        | TitleActionType::PreorderButton
        | TitleActionType::PrimeSignUpButton => {
            let unwrapped_action = get_some_or_return_value!(&item_details.data, None);
            let item_height = item_details.height.clone();
            let title_action_label = unwrapped_action.titleAction.label.clone();

            Some(PrimaryButton {
                variant: get_primary_variant_based_on_height(
                    item_height,
                    TextContent::String(title_action_label),
                    None,
                )
                .into(),
                disabled: false,
            })
        }
        TitleActionType::PlaybackGroupButton => {
            let unwrapped_action = get_some_or_return_value!(&item_details.data, None);
            let item_height = item_details.height.clone();
            let title_action_label = unwrapped_action.titleAction.label.clone();

            report_on_ssm_watch_button_rendered_metric(RustPage::RUST_DETAILS);

            Some(PrimaryButton {
                variant: get_primary_variant_based_on_height(
                    item_height,
                    TextContent::String(title_action_label),
                    Some(FableIcon::PLAY.to_string()),
                )
                .into(),
                disabled: false,
            })
        }
        TitleActionType::RecordTNFButton => Some(ToolTipButton {
            height: item_details.height.clone(),
            label: DetailsString::PV_LE_IP_RECORD_TNF_LIVE.get_localised_text(),
            tool_tip: DetailsString::PV_LE_IP_OPT_IN_TO_WATCH.get_localised_text(),
        }),
        // FIXME: TVSchedule needs to be added
        TitleActionType::TVScheduleButton => {
            // The below is just added to help debugging till TVSchedule is added
            Some(IconButton {
                label: static_str_maybe_signal("Missing"),
                icon: FableIcon::ERROR.to_string().into(),
            })
        }
    }
}

#[cfg(test)]
mod test {
    use crate::context::state::state_creators::title_actions::TitleActionsState;
    use crate::modules::localized_text::DetailsString;
    use crate::modules::title_actions::types::{
        TitleActionHeight, TitleActionItem, TitleActionType,
    };
    use crate::modules::title_actions_helpers::get_playback_data;
    use crate::ui::title_actions::title_action_button_presenter::TitleActionButtonPresentation::{
        IconButton, PlayButton, PrimaryButton, ReactionButton,
    };
    use crate::ui::title_actions::title_action_button_presenter::{
        get_presentation_information_for_button, static_maybe_sig, static_str,
        static_str_maybe_signal, TitleActionButtonPresentation,
    };
    use amzn_fable_tokens::FableIcon;
    use common_transform_types::actions::{
        SuppressionMetadata, TitleAction, TitleActionMetadata, TitleActionNestedChild,
        TitleActionPresentation,
    };
    use common_transform_types::playback_metadata::{
        PlaybackMetadata, PlaybackPosition, TitleActionMetadataType, UserEntitlementMetadata,
        UserPlaybackMetadata,
    };
    use fableous::buttons::play_button::PlayButtonHeight;
    use fableous::buttons::primary_button::PrimaryButtonVariant;
    use fableous::progress_bar::ProgressBarVariant;
    use ignx_compositron::prelude::{create_focus_signal, create_rw_signal, SignalGetUntracked};
    use ignx_compositron::reactive::Scope;
    use ignx_compositron::text::TextContent;
    use rstest::rstest;
    use title_reaction_buttons::{
        state::title_reaction_state::ReactionState, ReactionButtonVariant,
    };

    fn get_presentation(
        scope: Scope,
        button_type: TitleActionType,
        is_primary: bool,
        data: Option<TitleActionNestedChild>,
        is_in_watchlist: bool,
        reaction_state: ReactionState,
        is_two_line_button: bool,
    ) -> TitleActionButtonPresentation {
        let mut action_item = TitleActionItem::new(button_type, data, 0);

        let state = TitleActionsState {
            is_ssm_enabled: create_rw_signal(scope, false),
            informational_messages: create_rw_signal(scope, None),
            taps_fallback_enabled: create_rw_signal(scope, false),
            is_in_watchlist: create_rw_signal(scope, is_in_watchlist),
            focus_signal: create_focus_signal(scope),
            reaction_state: create_rw_signal(scope, reaction_state),
            has_empty_taps_buttons: create_rw_signal(scope, false),
        };

        if is_two_line_button {
            action_item.height = TitleActionHeight::TwoLines;
        }

        get_presentation_information_for_button(scope, &action_item, is_primary, &state).unwrap()
    }

    fn create_action_with_label(label: String) -> TitleActionNestedChild {
        TitleActionNestedChild {
            childActions: vec![],
            titleAction: TitleAction {
                label,
                metadata: TitleActionMetadata::Suppression(SuppressionMetadata {
                    refMarker: None,
                    severity: None,
                    metadataActionType: None,
                }),
                presentation: TitleActionPresentation::Simple,
                entitlement_metadata: None,
            },
        }
    }

    fn create_fake_playback_action(
        label: String,
        runtime_seconds: Option<u64>,
        timecode_seconds: Option<u64>,
    ) -> TitleActionNestedChild {
        TitleActionNestedChild {
            childActions: vec![],
            titleAction: TitleAction {
                label,
                metadata: TitleActionMetadata::Playback(PlaybackMetadata {
                    refMarker: "".to_string(),
                    contentDescriptors: None,
                    playbackExperienceMetadata: None,
                    position: PlaybackPosition::FeatureInProgress,
                    startPositionEpochUtc: None,
                    userPlaybackMetadata: UserPlaybackMetadata {
                        runtimeSeconds: runtime_seconds,
                        timecodeSeconds: timecode_seconds,
                        hasStreamed: Some(false),
                        isLinear: None,
                        linearStartTime: None,
                        linearEndTime: None,
                    },
                    userEntitlementMetadata: UserEntitlementMetadata {
                        entitlementType: "".to_string(),
                        benefitType: vec![],
                    },
                    videoMaterialType: "TV Show".to_string(),
                    channelId: None,
                    playbackTitle: None,
                    metadataActionType: TitleActionMetadataType::Playback,
                    catalogMetadata: None,
                    isTrailer: None,
                    isUnopenedRental: false,
                }),
                presentation: TitleActionPresentation::Simple,
                entitlement_metadata: None,
            },
        }
    }

    #[test]
    fn get_playback_data_should_return_none_for_non_playback_items() {
        let action_data = create_action_with_label("non_playback".to_string());
        let playback_data = get_playback_data(&action_data);
        assert_eq!(playback_data, None);
    }

    #[rstest]
    #[case(TitleActionType::PlayRegularButton, "Play Regular")]
    #[case(TitleActionType::SVODAcquisitionButton, "Acquire")]
    #[case(TitleActionType::PrimeSignUpButton, "Sign-up")]
    #[case(TitleActionType::WatchTrailerButton, "Watch Trailer")]
    #[case(TitleActionType::PlaybackGroupButton, "Watch")]
    fn should_get_primary_button(#[case] button_type: TitleActionType, #[case] label: String) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let btn_presentation = get_presentation(
                ctx,
                button_type.clone(),
                true,
                Some(create_action_with_label(label.clone())),
                false,
                ReactionState::None,
                false,
            );

            let presentation = match button_type {
                TitleActionType::PlayRegularButton | TitleActionType::PlaybackGroupButton => {
                    PrimaryButtonVariant::IconAndTextSize400(
                        FableIcon::PLAY.to_string(),
                        TextContent::String(label),
                    )
                }
                TitleActionType::WatchTrailerButton => PrimaryButtonVariant::IconAndTextSize400(
                    FableIcon::TRAILER.to_string(),
                    TextContent::String(label),
                ),
                _ => PrimaryButtonVariant::TextSize400(TextContent::String(label)),
            };

            if let PrimaryButton { variant, disabled } = btn_presentation {
                assert_eq!(variant.get_untracked(), presentation);
                assert!(!disabled);
            } else {
                panic!("Unexpected button type");
            };
        })
    }

    #[test]
    fn should_get_watch_trailer_primary_button_without_data() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::WatchTrailerButton,
                true,
                None,
                false,
                ReactionState::None,
                false,
            );

            if let PrimaryButton { variant, disabled } = btn_presentation {
                assert_eq!(
                    variant.get_untracked(),
                    PrimaryButtonVariant::IconAndTextSize400(
                        FableIcon::TRAILER.to_string(),
                        DetailsString::AV_LRC_trailer_tooltip_text.get_localised_text()
                    )
                );
                assert!(!disabled);
            } else {
                panic!("Unexpected button presentation");
            }
        })
    }

    #[test]
    fn should_get_regular_button_from_start_over_action_primary() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::PlayFromBeginningButton,
                true,
                Some(create_fake_playback_action(
                    "Start Over".to_string(),
                    None,
                    None,
                )),
                false,
                ReactionState::None,
                false,
            );

            if let PrimaryButton { variant, disabled } = btn_presentation {
                assert_eq!(
                    variant.get_untracked(),
                    PrimaryButtonVariant::TextSize800(TextContent::String(
                        "Start Over".to_string()
                    ))
                );

                assert!(!disabled);
            } else {
                panic!("Unexpected button type");
            };
        })
    }

    #[test]
    fn should_get_regular_button_from_start_over_action_non_primary() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::PlayFromBeginningButton,
                false,
                Some(create_fake_playback_action(
                    "Start Over".to_string(),
                    None,
                    None,
                )),
                false,
                ReactionState::None,
                false,
            );

            assert_eq!(
                btn_presentation,
                IconButton {
                    label: static_str_maybe_signal("Start Over"),
                    icon: static_maybe_sig(FableIcon::START_OVER)
                }
            )
        })
    }

    #[rstest]
    #[case(false, PlayButtonHeight::Size400)]
    #[case(true, PlayButtonHeight::Size800)]
    fn should_get_resume_button(
        #[case] is_two_line: bool,
        #[case] expected_height: PlayButtonHeight,
    ) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::ResumeButton,
                true,
                Some(create_fake_playback_action(
                    "Resume".to_string(),
                    Some(1000),
                    Some(100),
                )),
                false,
                ReactionState::None,
                is_two_line,
            );

            assert_eq!(
                btn_presentation,
                PlayButton {
                    content: "Resume".to_string().into(),
                    variant: ProgressBarVariant::VOD,
                    progress: 0.1,
                    rounded: true,
                    height: expected_height
                }
            )
        })
    }

    #[test]
    fn should_get_force_playback_button() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::ForcePlaybackButton,
                true,
                None,
                false,
                ReactionState::None,
                false,
            );

            if let PrimaryButton { variant, disabled } = btn_presentation {
                assert_eq!(
                    variant.get_untracked(),
                    PrimaryButtonVariant::TextSize800(TextContent::String(
                        "Start playing".to_string()
                    ))
                );

                assert!(!disabled);
            } else {
                panic!("Unexpected button type");
            };
        })
    }

    #[rstest]
    #[case::secondary_checked(false, true, FableIcon::CHECK.to_string())]
    #[case::secondary_unchecked(false, false, FableIcon::ADD.to_string())]
    #[case::primary_unchecked(true, false, FableIcon::ADD.to_string())]
    #[case::primary_checked(true, true, FableIcon::CHECK.to_string())]
    fn should_get_watchlist_button(
        #[case] is_primary: bool,
        #[case] is_checked: bool,
        #[case] icon_expected: String,
    ) {
        ignx_compositron::app::launch_only_scope(move |scope| {
            let btn_presentation = get_presentation(
                scope,
                TitleActionType::WatchListButton,
                is_primary,
                None,
                is_checked,
                ReactionState::None,
                false,
            );

            match btn_presentation {
                IconButton { label, icon } => {
                    if is_primary {
                        panic!("Should not be IconButton when is_primary is true");
                    }

                    assert_eq!(label, static_str_maybe_signal("Watchlist"));
                    assert_eq!(icon.get_untracked(), icon_expected);
                }
                PrimaryButton { variant, .. } => {
                    if !is_primary {
                        panic!("Should not be PrimaryButton when is_primary is false");
                    }

                    match variant.get_untracked() {
                        PrimaryButtonVariant::IconAndTextSize400(icon, label) => {
                            assert_eq!(icon, icon_expected);
                            assert_eq!(label, static_str("Watchlist"))
                        }
                        _ => panic!("Unexpected variant for PrimaryButton"),
                    }
                }
                _ => panic!("Unexpected button type"),
            }
        })
    }

    #[test]
    fn should_get_secondary_watch_trailer_button() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::WatchTrailerButton,
                false,
                None,
                false,
                ReactionState::None,
                false,
            );

            assert_eq!(
                btn_presentation,
                IconButton {
                    label: static_str_maybe_signal("Trailer"),
                    icon: static_maybe_sig(FableIcon::TRAILER)
                }
            )
        })
    }

    #[test]
    fn should_get_recap_button() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::RecapButton,
                false,
                None,
                false,
                ReactionState::None,
                false,
            );

            assert_eq!(
                btn_presentation,
                IconButton {
                    label: TextContent::String("Recap".to_string()).into(),
                    icon: static_maybe_sig(FableIcon::RECAP)
                }
            )
        })
    }

    #[test]
    fn should_get_customer_service_button() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::CustomerServiceWidgetsButton,
                true,
                None,
                false,
                ReactionState::None,
                false,
            );
            match btn_presentation {
                PrimaryButton { variant, disabled } => {
                    assert_eq!(
                        variant.get_untracked(),
                        PrimaryButtonVariant::TextSize400(TextContent::String(
                            "Need help?".to_string()
                        ))
                    );
                    assert_eq!(disabled, false);
                }
                _ => panic!("Unexpected button type"),
            }
        })
    }

    #[rstest]
    #[case(ReactionState::Liked)]
    #[case(ReactionState::None)]
    #[case(ReactionState::Disliked)]
    fn should_get_reactions_thumbs_up_button(#[case] reaction_state: ReactionState) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::ReactionsThumbsUpButton,
                false,
                None,
                false,
                reaction_state.clone(),
                false,
            );

            match btn_presentation {
                ReactionButton {
                    reaction_sig,
                    variant,
                } => {
                    assert_eq!(reaction_sig.get_untracked(), reaction_state);
                    assert_eq!(variant, ReactionButtonVariant::Like);
                }
                _ => panic!("Unexpected variant"),
            };
        })
    }

    #[rstest]
    #[case(ReactionState::Liked)]
    #[case(ReactionState::None)]
    #[case(ReactionState::Disliked)]
    fn should_get_reactions_thumbs_down_button(#[case] reaction_state: ReactionState) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::ReactionsThumbsDownButton,
                false,
                None,
                false,
                reaction_state.clone(),
                false,
            );

            match btn_presentation {
                ReactionButton {
                    reaction_sig,
                    variant,
                } => {
                    assert_eq!(reaction_sig.get_untracked(), reaction_state);
                    assert_eq!(variant, ReactionButtonVariant::Dislike);
                }
                _ => panic!("Unexpected variant"),
            };
        })
    }

    #[test]
    fn should_get_languages_and_more_button() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::MoreTitleDetailsButton,
                false,
                None,
                false,
                ReactionState::None,
                false,
            );

            assert_eq!(
                btn_presentation,
                IconButton {
                    label: static_str_maybe_signal("Languages & More"),
                    icon: static_maybe_sig(FableIcon::INFO)
                }
            )
        })
    }

    #[test]
    fn should_get_explore_button() {
        ignx_compositron::app::launch_only_scope(|ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::ExploreButton,
                false,
                None,
                false,
                ReactionState::None,
                false,
            );

            assert_eq!(
                btn_presentation,
                IconButton {
                    label: static_str_maybe_signal("Explore"),
                    icon: static_maybe_sig(FableIcon::EXPLORE)
                }
            )
        })
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn should_get_presentation_scheme_for_suppression(#[case] is_disabled_action: bool) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let example_title_action = TitleAction {
                label: "Suppression".to_string(),
                metadata: TitleActionMetadata::Suppression(SuppressionMetadata {
                    refMarker: None,
                    severity: None,
                    metadataActionType: None,
                }),
                presentation: TitleActionPresentation::Simple,
                entitlement_metadata: None,
            };

            let mock_action = if is_disabled_action {
                Some(TitleActionNestedChild {
                    childActions: vec![],
                    titleAction: example_title_action,
                })
            } else {
                Some(TitleActionNestedChild {
                    childActions: vec![TitleActionNestedChild {
                        childActions: vec![],
                        titleAction: example_title_action.clone(),
                    }],
                    titleAction: example_title_action,
                })
            };

            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::SuppressionButton,
                true,
                mock_action,
                false,
                ReactionState::None,
                false,
            );

            if let PrimaryButton { variant, disabled } = btn_presentation {
                assert_eq!(
                    variant.get_untracked(),
                    PrimaryButtonVariant::TextSize800(TextContent::String(
                        "Suppression".to_string()
                    ))
                );

                assert_eq!(disabled, is_disabled_action);
            } else {
                panic!("Unexpected button type");
            };
        })
    }

    #[rstest]
    #[case(TitleActionType::PlayRegularButton, "Watch Now")]
    fn should_return_one_line_primary_button(
        #[case] button_type: TitleActionType,
        #[case] label: String,
    ) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let btn_presentation = get_presentation(
                ctx,
                button_type,
                true,
                Some(create_action_with_label(label.clone())),
                false,
                ReactionState::None,
                false,
            );

            match btn_presentation {
                PrimaryButton { variant, disabled } => {
                    assert_eq!(
                        variant.get_untracked(),
                        PrimaryButtonVariant::IconAndTextSize400(
                            FableIcon::PLAY.to_string(),
                            TextContent::String(label)
                        )
                    );

                    assert!(!disabled);
                }
                _ => panic!("Unexpected button type"),
            }
        })
    }

    #[rstest]
    #[case(TitleActionType::PlayRegularButton, "Watch Now")]
    fn should_return_two_line_primary_button(
        #[case] button_type: TitleActionType,
        #[case] label: String,
    ) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let btn_presentation = get_presentation(
                ctx,
                button_type,
                true,
                Some(create_action_with_label(label.clone())),
                false,
                ReactionState::None,
                true,
            );

            match btn_presentation {
                PrimaryButton { variant, disabled } => {
                    assert_eq!(
                        variant.get_untracked(),
                        PrimaryButtonVariant::IconAndTextSize800(
                            FableIcon::PLAY.to_string(),
                            TextContent::String(label)
                        )
                    );

                    assert!(!disabled);
                }
                _ => panic!("Unexpected button type"),
            }
        })
    }

    #[rstest]
    #[case(false, TitleActionHeight::OneLine)]
    #[case(true, TitleActionHeight::TwoLines)]
    fn should_get_expected_record_tnf_button_variant(
        #[case] is_two_line_button: bool,
        #[case] expected_height: TitleActionHeight,
    ) {
        ignx_compositron::app::launch_only_scope(move |ctx| {
            let btn_presentation = get_presentation(
                ctx,
                TitleActionType::RecordTNFButton,
                false,
                None,
                false,
                ReactionState::None,
                is_two_line_button,
            );

            match btn_presentation {
                TitleActionButtonPresentation::ToolTipButton {
                    height,
                    label,
                    tool_tip,
                } => {
                    assert_eq!(height, expected_height);
                    assert_eq!(
                        label,
                        DetailsString::PV_LE_IP_RECORD_TNF_LIVE.get_localised_text()
                    );
                    assert_eq!(
                        tool_tip,
                        DetailsString::PV_LE_IP_OPT_IN_TO_WATCH.get_localised_text()
                    );
                }
                _ => panic!("Unexpected variant"),
            };
        })
    }
}
