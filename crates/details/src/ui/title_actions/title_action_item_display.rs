use crate::context::hooks::empty_column_fallback;
use crate::context::state::state_creators::title_actions::TitleActionsState;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::title_actions::types::TitleActionItem;
use crate::ui::record_season::tool_tip_button::*;
use crate::ui::test_ids::DETAILS_TITLE_ACTION_TEST_ID;
use crate::ui::title_actions::title_action_button_presenter::{
    get_presentation_information_for_button, TitleActionButtonPresentation,
};
use fableous::buttons::icon_button::*;
use fableous::buttons::play_button::*;
use fableous::buttons::primary_button::*;
use ignx_compositron::column::ColumnComposable;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use title_reaction_buttons::{
    ui::{dislike_button::*, like_button::*},
    ReactionButtonVariant,
};

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("title_action_item_display");

#[Composer]
fn TitleActionItemPresentation(
    ctx: &AppContext,
    presentation: TitleActionButtonPresentation,
) -> ColumnComposable {
    // This is the largest a button should be in terms of height, we can force PrimaryButton to abide by this
    // In certain locales it can exceed the size without this.
    let item_max_height = get_play_button_height_without_progress_bar(PlayButtonHeight::Size800);

    match presentation {
        TitleActionButtonPresentation::IconButton { label, icon } => {
            compose! {
                Column(){
                    IconButton(label, icon)
                }
                .test_id(DETAILS_TITLE_ACTION_TEST_ID)
            }
        }
        TitleActionButtonPresentation::PrimaryButton { variant, disabled } => {
            compose! {
                Column(){
                    PrimaryButton(variant, disabled)
                    .max_height(item_max_height)
                }
                .test_id(DETAILS_TITLE_ACTION_TEST_ID)
            }
        }
        TitleActionButtonPresentation::PlayButton {
            content,
            variant,
            progress,
            rounded,
            height,
        } => {
            let without_progress_height =
                get_play_button_height_without_progress_bar(height.clone());
            compose! {
                Column(){
                    PlayButton(content, variant, progress, rounded, width: None, height)
                }
                .height(without_progress_height)
                .test_id(DETAILS_TITLE_ACTION_TEST_ID)
            }
        }
        TitleActionButtonPresentation::ReactionButton {
            reaction_sig,
            variant,
        } => match variant {
            ReactionButtonVariant::Like => {
                compose! {
                    Column(){
                        LikeButton(reaction_sig)
                    }
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                }
            }
            ReactionButtonVariant::Dislike => {
                compose! {
                    Column(){
                        DislikeButton(reaction_sig)
                    }
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                }
            }
        },
        TitleActionButtonPresentation::ToolTipButton {
            height,
            label,
            tool_tip,
        } => {
            compose! {
                Column(){
                    ToolTipButton(height, label, tool_tip)
                }
                .test_id(DETAILS_TITLE_ACTION_TEST_ID)
            }
        }
    }
}

#[Composer]
pub fn TitleActionItemDisplay(
    ctx: &AppContext,
    item: &'_ TitleActionItem,
    state: &'_ TitleActionsState,
    is_primary: bool,
) -> ColumnComposable {
    let presentation_info =
        get_presentation_information_for_button(ctx.scope(), item, is_primary, state);

    let presentation_inner = match presentation_info {
        None => {
            DETAILS_LOGGER.error(
                format!(
                    "Unexpected none presentation, falling back to empty column {:?}",
                    item.name
                )
                .as_str(),
            );

            return empty_column_fallback(ctx);
        }
        Some(inner_val) => inner_val,
    };

    let select_handler = {
        let ctx_clone = ctx.clone();
        let item_ref = item.clone();
        move || match &item_ref.on_select {
            None => {}
            Some(handler_fn) => handler_fn(&ctx_clone, &item_ref),
        }
    };

    compose! {
        TitleActionItemPresentation(presentation: presentation_inner)
            .on_select(select_handler)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::modules::title_actions::handlers::record_tnf::on_record_tnf_button_pressed;
    use crate::modules::title_actions::handlers::watchlist::on_watchlist_select;
    use crate::modules::title_actions::types::{TitleActionHeight, TitleActionType};
    use common_transform_types::actions::{
        TitleAction, TitleActionMetadata, TitleActionNestedChild, TitleActionPresentation,
    };
    use common_transform_types::playback_metadata::{
        PlaybackMetadata, PlaybackPosition, TitleActionMetadataType, UserEntitlementMetadata,
        UserPlaybackMetadata,
    };
    use ignx_compositron::test_utils::assert_node_exists;

    // FIXME: These below convert from presentation tests could be improved by asserting on the props
    // However this isn't really possible without knowing the implementation details of the fable components
    // So until the SDK updates its good enough.
    #[test]
    fn should_construct_icon_button_from_presentation() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let title_action_item = TitleActionItem {
                    name: TitleActionType::WatchListButton,
                    data: None,
                    id: "fakeId".to_string(),
                    on_select: Some(on_watchlist_select),
                    height: TitleActionHeight::OneLine,
                };

                let state = TitleActionsState::new(ctx.scope());

                compose! {
                   TitleActionItemDisplay(item: &title_action_item, is_primary: false, state: &state)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let play_button = node_tree.find_by_test_id(ICON_BUTTON_TEST_ID);
                assert_node_exists!(play_button);
            },
        )
    }

    #[test]
    fn should_construct_primary_button_from_presentation() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let title_action_item = TitleActionItem {
                    name: TitleActionType::ForcePlaybackButton,
                    data: None,
                    id: "forcePlayback".to_string(),
                    on_select: None,
                    height: TitleActionHeight::OneLine,
                };

                let state = TitleActionsState::new(ctx.scope());

                compose! {
                   TitleActionItemDisplay(item: &title_action_item, is_primary: true, state: &state)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let primary_button = node_tree.find_by_test_id(PRIMARY_BUTTON_TEST_ID);
                assert_node_exists!(primary_button);
            },
        )
    }

    #[test]
    fn should_construct_play_button_from_presentation() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let title_action_item = TitleActionItem {
                    name: TitleActionType::ResumeButton,
                    data: Some(TitleActionNestedChild {
                        childActions: vec![],
                        titleAction: TitleAction {
                            label: "".to_string(),
                            metadata: TitleActionMetadata::Playback(PlaybackMetadata {
                                refMarker: "".to_string(),
                                contentDescriptors: None,
                                playbackExperienceMetadata: None,
                                position: PlaybackPosition::FeatureFinished,
                                startPositionEpochUtc: None,
                                userPlaybackMetadata: UserPlaybackMetadata {
                                    runtimeSeconds: Some(200),
                                    timecodeSeconds: Some(100),
                                    hasStreamed: Some(false),
                                    isLinear: None,
                                    linearStartTime: None,
                                    linearEndTime: None,
                                },
                                userEntitlementMetadata: UserEntitlementMetadata {
                                    entitlementType: "".to_string(),
                                    benefitType: vec![],
                                },
                                videoMaterialType: "".to_string(),
                                channelId: None,
                                playbackTitle: None,
                                metadataActionType: TitleActionMetadataType::Playback,
                                catalogMetadata: None,
                                isTrailer: None,
                                isUnopenedRental: false,
                            }),
                            presentation: TitleActionPresentation::Simple,
                            entitlement_metadata: None,
                        },
                    }),
                    id: "playback".to_string(),
                    on_select: None,
                    height: TitleActionHeight::OneLine,
                };

                let state = TitleActionsState::new(ctx.scope());

                compose! {
                   TitleActionItemDisplay(item: &title_action_item, is_primary: true, state: &state)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let play_button = node_tree.find_by_test_id(PLAY_BUTTON_TEST_ID);
                assert_node_exists!(play_button);
            },
        )
    }

    #[test]
    fn should_construct_record_tnf_button() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let title_action_item = TitleActionItem {
                    name: TitleActionType::RecordTNFButton,
                    data: None,
                    id: "fakeId".to_string(),
                    on_select: Some(on_record_tnf_button_pressed),
                    height: TitleActionHeight::OneLine,
                };

                let state = TitleActionsState::new(ctx.scope());

                compose! {
                   TitleActionItemDisplay(item: &title_action_item, is_primary: false, state: &state)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let button = node_tree.find_by_test_id(TOOL_TIP_BUTTON_PRIMARY_BUTTON_TEST_ID);
                assert_node_exists!(button);
            },
        )
    }
}
