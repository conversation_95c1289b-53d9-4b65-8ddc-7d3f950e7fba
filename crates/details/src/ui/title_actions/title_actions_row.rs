use crate::context::hooks::{empty_column_fallback, use_details_page_context};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::title_actions::get_atf_button_visibility::ATFButtonVisibilityParams;
use crate::modules::title_actions::get_title_actions_buttons_list::{
    get_title_actions_button_list, GetActionsButtonsListParams, TitleActionButtons,
};
use crate::modules::title_actions::types::{TitleActionItem, TitleActionType};
use crate::ui::test_ids::DETAILS_PAGE_TITLE_ACTIONS;
use crate::ui::title_actions::entitlement_row::*;
use crate::ui::title_actions::title_action_item_display::*;
use crate::ui::title_actions::tnf_recording_enabled_message::*;

use crate::modules::live_gating::is_live_dp_enabled_without_trigger;
use auth::AuthContext;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use mockall_double::*;
use playback::cache::use_playback_cache;
#[double]
use profile_manager::try_use_profile_manager;
#[cfg(test)]
use rust_features::use_mock_rust_features as use_rust_features;
#[cfg(not(test))]
use rust_features::use_rust_features;
use std::rc::Rc;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("title_actions_row");

#[Composer]
pub fn TitleActionsRow(ctx: &AppContext) -> ColumnComposable {
    let scope = ctx.scope();
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            DETAILS_LOGGER.error("Failed to obtain details page context, rendering empty column");
            return empty_column_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let playback_cache = use_playback_cache(ctx);

    let rust_features = use_rust_features(ctx.scope());
    let title_actions_state = details_ctx.borrow().state.title_actions_state.clone();

    let gti = {
        let details_ctx = details_ctx.clone();
        move || details_ctx.borrow().get_current_gti()
    };

    let actions = create_memo(ctx.scope(), move |_| {
        let details_response = match details_ctx.borrow().state.page_resources.atf_response.get() {
            None => {
                return TitleActionButtons {
                    primary_buttons: vec![],
                    secondary_buttons: vec![],
                }
            }
            Some(response) => response,
        };
        let title_information = match details_ctx.borrow().state.title_information.get() {
            None => {
                return TitleActionButtons {
                    primary_buttons: vec![],
                    secondary_buttons: vec![],
                }
            }
            Some(title_info) => title_info,
        };

        let is_user_signed_in = match use_context::<AuthContext>(scope) {
            None => false,
            Some(context) => context.is_last_known_state_signed_in(),
        };

        let is_kids_profile = match try_use_profile_manager(scope) {
            None => false,
            Some(profile_manager) => match profile_manager
                .get_active_profile()
                .try_get_untracked()
                .flatten()
            {
                None => false,
                Some(profile) => !profile.isAdult,
            },
        };

        // The SSM is also controlled by the resiliency switch onboarded to taps-parameters. So, we
        // can gate this just by the LEDP weblab.
        let is_ssm_enabled = is_live_dp_enabled_without_trigger(&details_ctx.borrow().app_context);

        get_title_actions_button_list(
            &details_ctx,
            GetActionsButtonsListParams {
                is_ssm_enabled,
                actions: details_response.actions,
                adaptive_actions: details_response.adaptive_actions,
                enable_taps_fallback: title_actions_state.taps_fallback_enabled.get(),
                atf_button_visibility_params: ATFButtonVisibilityParams {
                    title_information,
                    is_user_signed_in,
                    is_kids_profile,
                    is_reactions_enabled: true,
                    // This will in future also check there is recap data in the ATF response.
                    is_recap_available: rust_features.is_gen_ai_recap_enabled()
                        && !details_response.recaps.is_empty(),
                    tnf_property: details_ctx.borrow().state.tnf_property.get(),
                },
                title_type: details_response.title_decoration.title_type,
                title_gti: details_response.title_decoration.gti,
            },
        )
    });

    create_effect(ctx.scope(), move |_| {
        let actions = actions.get();

        let Some(gti) = gti() else {
            return;
        };

        let Ok(cache_event) = actions.try_to_playback_cache_event(gti) else {
            return;
        };

        playback_cache.event(cache_event);
    });

    let all_buttons = create_memo(ctx.scope(), move |_| {
        let actions_buttons = actions.get();
        let mut primary_buttons = actions_buttons.primary_buttons;
        let secondary_buttons = actions_buttons.secondary_buttons;
        let num_of_primary_buttons = primary_buttons.len();

        // FIXME: A bit of a hack to ensure that RowList updates the last PrimaryItem when it changes
        if num_of_primary_buttons > 0 {
            if let Some(last_item) = primary_buttons.get_mut(num_of_primary_buttons - 1) {
                last_item.id = format!("{}_LastPrimaryItem", last_item.id)
            }
        }

        let merged_vec: Vec<_> = primary_buttons
            .iter()
            .chain(secondary_buttons.iter())
            .cloned()
            .collect();
        merged_vec
    });

    let preferred_focus = create_rw_signal(ctx.scope(), "".to_string());

    let focused_index = create_rw_signal(
        ctx.scope(),
        ScrollTo::Index(0, Pivot::Start, Animation::default()),
    );
    create_effect(ctx.scope(), move |_| {
        let buttons = all_buttons.get();
        for item in buttons {
            let is_suppressed = matches!(item.name, TitleActionType::SuppressionButton);
            let is_playback_group = matches!(item.name, TitleActionType::PlaybackGroupButton);
            let has_no_child_actions = if let Some(data) = &item.data {
                data.childActions.is_empty()
            } else {
                true
            };

            let selectable = is_playback_group || (!is_suppressed && has_no_child_actions);
            if selectable {
                preferred_focus.set(item.id);
                break;
            }
        }
    });

    let builder = {
        let state = title_actions_state.clone();
        Rc::new(
            move |ctx: &AppContext, item: &TitleActionItem, item_idx: usize| {
                let last_primary_idx = actions
                    .try_with_untracked(|actions| {
                        if !actions.primary_buttons.is_empty() {
                            actions.primary_buttons.len() - 1
                        } else {
                            0
                        }
                    })
                    .unwrap_or(0);

                let is_primary_item = item_idx <= last_primary_idx;
                let last_primary_item = item_idx == last_primary_idx;

                let padding = if last_primary_item {
                    Padding::new(0.0, 32.0, 0.0, 0.0)
                } else {
                    Padding::default()
                };

                let is_preferred = Signal::derive(ctx.scope(), {
                    let item_id = item.id.clone();
                    move || preferred_focus.get() == item_id
                });

                let item_id = item.id.clone();
                // FIXME: The below should be modified when we have a way to know how far we are
                // from the edges of the screen
                compose! {
                    TitleActionItemDisplay(item, state: &state, is_primary: is_primary_item)
                        .preferred_focus(is_preferred)
                        .padding(padding)
                        .on_focus(move || {
                            preferred_focus.set(item_id.clone());
                            if last_primary_idx >= 3 {
                                let buffer_amount = last_primary_idx - 1;

                                if item_idx > last_primary_idx - buffer_amount {
                                    focused_index.set(ScrollTo::Index(
                                        (item_idx - 2) as u32,
                                        Pivot::Start,
                                        Animation::default(),
                                    ));
                                } else if item_idx <= last_primary_idx - buffer_amount {
                                    focused_index.set(ScrollTo::Offset(
                                        0.0,
                                        Pivot::Start,
                                        Animation::default(),
                                    ));
                                }
                            }
                        })
                }
            },
        )
    };

    compose! {
        Column() {
            RowList(items: all_buttons, item_builder: builder),
                .overflow_behavior(OverflowBehavior::Visible)
                .main_axis_alignment(MainAxisAlignment::SpacedBy(16.0))
                .cross_axis_alignment(CrossAxisAlignment::Center)
                .scroll_to(focused_index)
                .test_id(DETAILS_PAGE_TITLE_ACTIONS)
                .focused(title_actions_state.focus_signal)
            EntitlementRow()
            TnfRecordingEnabledMessage()
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::hooks::create_or_use_details_page_context;
    use crate::context::test_utils::{
        create_or_use_details_context_test, mock_out_atf_response, mock_out_live_atf_response,
    };
    use crate::network::mock_responses::{
        MOCK_RESPONSE_ATF_WITH_EPISODE_RECAPS, MOCK_RESPONSE_LIVE_PLAYBACK_GROUP_WATCH,
        MOCK_RESPONSE_VOD_LONG_ATF, MOCK_RESPONSE_VOD_MPO_ATF, MOCK_RESPONSE_VOD_WATCH_NOW_ATF,
    };
    use crate::network::parser::details_page_atf_parser;
    use crate::ui::test_ids::DETAILS_TITLE_ACTION_TEST_ID;

    use amzn_fable_tokens::*;
    use common_transform_types::profile::{Profile, ProfileAvatar};
    use fableous::buttons::icon_button::ICON_BUTTON_ICON_SECTION_TEST_ID;
    use fableous::font_icon::FONT_ICON_TEST_ID;
    use ignx_compositron::test_utils::node_properties::NodeQuery;
    use ignx_compositron::test_utils::ComposableType;
    use mockall::TimesRange;
    use playback::{
        cache::{PlaybackCache, PlaybackCacheEvent},
        src::{PlaybackSrc, VodSrc, VodStreamingConfig},
    };
    use profile_manager::MockProfileManager;
    use rstest::*;
    use rust_features::MockRustFeaturesBuilder;
    use std::cell::RefCell;
    use title_reaction_buttons::ui::{
        dislike_button::{DISLIKE_FILLED_ICON_TEST_ID, DISLIKE_OUTLINE_ICON_TEST_ID},
        like_button::{LIKE_FILLED_ICON_TEST_ID, LIKE_OUTLINE_ICON_TEST_ID},
    };

    fn adult_profile() -> Profile {
        mock_profile(
            "adult".to_string(),
            "Adult Name".to_string(),
            "https://m.media-amazon.com/images/adult.png".to_string(),
            true,
        )
    }

    fn kid_profile() -> Profile {
        mock_profile(
            "kid".to_string(),
            "Kid Name".to_string(),
            "https://m.media-amazon.com/images/kid.png".to_string(),
            false,
        )
    }

    fn mock_profile(id: String, name: String, avatar_url: String, is_adult: bool) -> Profile {
        Profile {
            id,
            avatar: ProfileAvatar {
                avatarId: "1".to_string(),
                avatarUrl: avatar_url,
                avatarDescription: None,
            },
            name,
            isActive: true,
            isAdult: is_adult,
            profileIsImplicit: true,
            translationDetails: None,
            permissions: None,
        }
    }

    fn set_active_profile(ctx: &AppContext, is_adult: bool) {
        let scope = ctx.scope();

        let mut mock_profile_manager = MockProfileManager::default();
        let mut mock_profile_manager_clone = MockProfileManager::default();
        let profile = if is_adult {
            adult_profile()
        } else {
            kid_profile()
        };
        mock_profile_manager_clone
            .expect_get_active_profile()
            .once()
            .return_once(move || create_rw_signal(scope, Some(profile)).into());

        mock_profile_manager
            .expect_clone()
            .once()
            .return_once(|| mock_profile_manager_clone);

        provide_context(ctx.scope(), mock_profile_manager);
    }

    fn assert_icon_button_label(items: Rc<RefCell<Vec<NodeQuery<'_>>>>, label: String) {
        let item = items.borrow_mut().remove(0);
        let icon_section = item
            .find_any_child_with()
            .test_id(ICON_BUTTON_ICON_SECTION_TEST_ID)
            .find_first();
        let icon_label = icon_section
            .find_any_child_with()
            .composable_type(ComposableType::Label)
            .find_first();
        assert_eq!(icon_label.borrow_props().text, Some(label));
    }

    fn assert_reaction_button_label(items: Rc<RefCell<Vec<NodeQuery<'_>>>>, label: String) {
        let test_id = match label.as_str() {
            FableIcon::THUMB_UP => LIKE_OUTLINE_ICON_TEST_ID,
            FableIcon::THUMB_UP_FILLED => LIKE_FILLED_ICON_TEST_ID,
            FableIcon::THUMB_DOWN => DISLIKE_OUTLINE_ICON_TEST_ID,
            FableIcon::THUMB_DOWN_FILLED => DISLIKE_FILLED_ICON_TEST_ID,
            _ => panic!("Unexpected reaction button label"),
        };
        let item = items.borrow_mut().remove(0);
        let icon_label = item.find_any_child_with().test_id(test_id).find_first();
        assert_eq!(icon_label.borrow_props().text, Some(label));
    }

    fn assert_button_text(items: Rc<RefCell<Vec<NodeQuery<'_>>>>, label: String) {
        let item = items.borrow_mut().remove(0);
        let item_label = item
            .find_any_child_with()
            .composable_type(ComposableType::RichTextLabel)
            .find_first();

        assert_eq!(item_label.borrow_props().text, Some(label));
    }

    fn set_gen_ai_enabled(scope: Scope, enabled: bool) {
        let features = MockRustFeaturesBuilder::new().set_is_gen_ai_recap_enabled(enabled);
        provide_context(scope, features.build());
    }

    #[test]
    fn should_build_actions_from_context_response() {
        ignx_compositron::app::launch_test(
            move |ctx| {
                create_or_use_details_context_test(&ctx);
                set_gen_ai_enabled(ctx.scope(), false);
                set_active_profile(&ctx, true);

                compose! {
                    TitleActionsRow()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let title_actions_root = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let title_actions_items = Rc::new(RefCell::new(
                    title_actions_root
                        .find_any_child_with()
                        .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                        .find_all(),
                ));

                // Should be 6 items in the default VOD response
                assert_eq!(title_actions_items.borrow().len(), 6);
                assert_button_text(title_actions_items.clone(), "Watch with Prime".to_string());
                assert_icon_button_label(
                    title_actions_items.clone(),
                    FableIcon::TRAILER.to_string(),
                );
                assert_icon_button_label(title_actions_items.clone(), FableIcon::ADD.to_string());
                assert_reaction_button_label(
                    title_actions_items.clone(),
                    FableIcon::THUMB_UP_FILLED.to_string(),
                );
                assert_reaction_button_label(
                    title_actions_items.clone(),
                    FableIcon::THUMB_DOWN.to_string(),
                );
                assert_icon_button_label(title_actions_items.clone(), FableIcon::INFO.to_string());
            },
        )
    }

    #[rstest]
    #[case(false, false)]
    #[case(false, true)]
    #[case(true, false)]
    fn should_not_render_recap_due_to_feature_flag_or_missing_data(
        #[case] gen_ai_enabled: bool,
        #[case] has_recaps: bool,
    ) {
        ignx_compositron::app::launch_test(
            move |ctx| {
                set_gen_ai_enabled(ctx.scope(), gen_ai_enabled);
                let context = create_or_use_details_context_test(&ctx);
                let context = context.borrow();
                set_active_profile(&ctx, true);

                if has_recaps {
                    context.state.page_resources.atf_response.update(|data| {
                        let data = data.as_mut().expect("ATF response was None");
                        let recaps = details_page_atf_parser(
                            MOCK_RESPONSE_ATF_WITH_EPISODE_RECAPS.to_string(),
                        )
                        .map(|result| match result {
                            network::common::DeviceProxyResponse::LRCEdgeResponse(r) => {
                                r.resource.recaps
                            }
                            network::common::DeviceProxyResponse::ErrorResponse(_) => {
                                panic!("unexpected error response")
                            }
                        })
                        .expect("Failed to parse response");

                        data.recaps = recaps;
                    });
                }

                compose! {
                    TitleActionsRow()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let title_actions_root = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let title_actions_items = Rc::new(RefCell::new(
                    title_actions_root
                        .find_any_child_with()
                        .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                        .find_all(),
                ));

                // Should be 6 items in the default VOD response
                assert_eq!(title_actions_items.borrow().len(), 6);
            },
        )
    }

    #[test]
    fn should_build_actions_with_recap_when_available() {
        ignx_compositron::app::launch_test(
            |ctx| {
                set_gen_ai_enabled(ctx.scope(), true);
                set_active_profile(&ctx, true);

                let context = create_or_use_details_context_test(&ctx);
                let context = context.borrow();

                context.state.page_resources.atf_response.update(|data| {
                    let data = data.as_mut().expect("ATF response was None");
                    let recaps =
                        details_page_atf_parser(MOCK_RESPONSE_ATF_WITH_EPISODE_RECAPS.to_string())
                            .map(|result| match result {
                                network::common::DeviceProxyResponse::LRCEdgeResponse(r) => {
                                    r.resource.recaps
                                }
                                network::common::DeviceProxyResponse::ErrorResponse(_) => {
                                    panic!("unexpected error response")
                                }
                            })
                            .expect("Failed to parse response");

                    data.recaps = recaps;
                });

                compose! {
                    TitleActionsRow()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let title_actions_root = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let title_actions_items = Rc::new(RefCell::new(
                    title_actions_root
                        .find_any_child_with()
                        .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                        .find_all(),
                ));

                // Should be 7 items
                assert_eq!(title_actions_items.borrow().len(), 7);
                assert_button_text(title_actions_items.clone(), "Watch with Prime".to_string());
                assert_icon_button_label(title_actions_items.clone(), FableIcon::RECAP.to_string());
            },
        )
    }

    #[test]
    fn should_render_playback_group_button_when_enabled() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let rust_features = MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build();
                provide_context(ctx.scope(), rust_features);

                let details_ctx = create_or_use_details_page_context(&ctx)
                    .expect("details context to be created");
                mock_out_live_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_LIVE_PLAYBACK_GROUP_WATCH.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_live_atf();

                compose! {
                    TitleActionsRow()
                }
            },
            |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let row = tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let items = row
                    .find_any_child_with()
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                    .find_all();

                assert_eq!(items.len(), 4);
                let first = items.first().expect("No first playback item found");
                let item_label = first
                    .find_any_child_with()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();
                let icon = first
                    .find_any_child_with()
                    .test_id(FONT_ICON_TEST_ID)
                    .find_first();

                assert_eq!(item_label.borrow_props().text, Some("Watch".to_string()));
                assert_eq!(icon.borrow_props().text, Some(FableIcon::PLAY.to_string()));
            },
        )
    }

    #[test]
    fn should_build_kids_only_actions_for_kids_account() {
        ignx_compositron::app::launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);
                set_gen_ai_enabled(ctx.scope(), false);
                set_active_profile(&ctx, false);

                compose! {
                    TitleActionsRow()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let title_actions_root = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let title_actions_items = Rc::new(RefCell::new(
                    title_actions_root
                        .find_any_child_with()
                        .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                        .find_all(),
                ));

                // Where adults normally see 6 items, kids would only see 4
                // as the reaction buttons are not shown
                assert_eq!(title_actions_items.borrow().len(), 4);
                assert_button_text(title_actions_items.clone(), "Watch with Prime".to_string());
                assert_icon_button_label(
                    title_actions_items.clone(),
                    FableIcon::TRAILER.to_string(),
                );
                assert_icon_button_label(title_actions_items.clone(), FableIcon::ADD.to_string());
                assert_icon_button_label(title_actions_items.clone(), FableIcon::INFO.to_string());
            },
        )
    }

    #[test]
    fn should_scroll_to_items_if_list_is_too_long() {
        ignx_compositron::app::launch_test(
            |ctx| {
                set_gen_ai_enabled(ctx.scope(), false);
                let details_context = create_or_use_details_page_context(&ctx)
                    .expect("details context to be created");
                let details_context = details_context.borrow();
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_VOD_LONG_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_context.get_atf();

                compose! {
                    TitleActionsRow()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let title_actions_root = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let title_actions_items = title_actions_root
                    .find_any_child_with()
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                    .find_all();

                // Select the third item
                let third_item = title_actions_items.get(3).expect("third item to exist!");

                // Last item will be off-screen partially or completely
                let last_item = title_actions_items.last().expect("Last item to exist!");
                assert!(last_item.borrow_props().on_screen_amount < 1.0);

                test_game_loop.send_on_focus_event(third_item.borrow_props().node_id);
                let node_tree = test_game_loop.tick_until_done();

                // After scroll should be on screen
                let title_actions_root = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let title_actions_items = title_actions_root
                    .find_any_child_with()
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                    .find_all();
                assert_eq!(title_actions_items.len(), 11);

                let first_item = title_actions_items.first().expect("first item");
                assert!(first_item.borrow_props().on_screen_amount < 1.0);

                let second_item = title_actions_items.get(1).expect("Second item to exist!");
                test_game_loop.send_on_focus_event(second_item.borrow_props().node_id);
                let node_tree = test_game_loop.tick_until_done();

                // After going back to the second item should scroll backwards
                let title_actions_root = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_ACTIONS);
                let title_actions_items = title_actions_root
                    .find_any_child_with()
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                    .find_all();
                let first_item = title_actions_items.first().expect("First item to exist");
                assert_eq!(first_item.borrow_props().on_screen_amount, 1.0);
            },
        )
    }

    #[test]
    fn should_emit_playback_cache_event_when_watch_now() {
        ignx_compositron::app::launch_test(
            move |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_VOD_WATCH_NOW_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                    TitleActionsRow()
                }
            },
            |scope, mut test_game_loop| {
                let playback_cache = PlaybackCache::expect(scope);
                let _ = test_game_loop.tick_until_done();

                playback_cache.validate_events(|events| {
                    assert_eq!(events.len(), 1);
                    assert_eq!(
                        events.first().unwrap(),
                        &PlaybackCacheEvent::DetailsPageEntered {
                            primary: PlaybackSrc::Vod(VodSrc {
                                title_id: "amzn1.dv.gti.648e578d-b007-42c7-a544-14c3ed5dd82a"
                                    .to_string(),
                                playback_envelope: None,
                                streaming_config: VodStreamingConfig::Vod { position: 0 }
                            })
                        }
                    );
                });
            },
        )
    }

    #[test]
    fn should_not_emit_playback_cache_event_if_no_primary_playback_action() {
        ignx_compositron::app::launch_test(
            move |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_VOD_MPO_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                    TitleActionsRow()
                }
            },
            |scope, mut test_game_loop| {
                let playback_cache = PlaybackCache::expect(scope);
                let _ = test_game_loop.tick_until_done();

                playback_cache.validate_events(|events| {
                    assert_eq!(events.len(), 0);
                });
            },
        )
    }
}
