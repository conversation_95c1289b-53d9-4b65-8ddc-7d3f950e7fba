use crate::metrics::clickstream_reporter::{
    report_error_modal_button_pressed, report_error_modal_rendered,
};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::localized_text::DetailsString;
use crate::ui::test_ids::{
    DETAILS_ERROR_OVERLAY_HOME_BUTTON_TEST_ID, DETAILS_ERROR_OVERLAY_SEARCH_BUTTON_TEST_ID,
    DETAILS_ERROR_OVERLAY_TEST_ID,
};
use amzn_fable_tokens::FableColor;
use app_reporting::kpi_reporting::report_cx_fatal;
use cfg_test_attr_derive::derive_test_only;
use fableous::buttons::primary_button::*;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::memo::ComposableBuilder;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};
use ignx_compositron::{compose_option, prelude::*};
use location::RustPage::{self, RUST_COLLECTIONS};
use location::{JSPage, Location, PageType};
use popups::popup::popup_wrapper;
use router::hooks::use_router;
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("error_overlay");

fn navigate_to_home(scope: Scope) {
    let router = use_router(scope);
    router.navigate(
        Location {
            pageType: PageType::Rust(RUST_COLLECTIONS),
            pageParams: Default::default(),
        },
        "DETAILS_PAGE_ERROR",
    )
}

fn navigate_to_search(scope: Scope) {
    let router = use_router(scope);
    router.navigate(
        Location {
            pageType: PageType::Js(JSPage::FIND_PAGE),
            pageParams: Default::default(),
        },
        "DETAILS_PAGE_ERROR",
    )
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct ErrorState {
    pub state: Option<ErrorType>,
}

impl ErrorState {
    pub fn no_error() -> ErrorState {
        ErrorState { state: None }
    }

    pub fn standard_error() -> ErrorState {
        ErrorState {
            state: Some(ErrorType::Standard),
        }
    }

    pub fn traveling_customer_error() -> ErrorState {
        ErrorState {
            state: Some(ErrorType::TravelingCustomer),
        }
    }

    pub fn hidden_event_error() -> ErrorState {
        ErrorState {
            state: Some(ErrorType::HiddenEvent),
        }
    }

    pub fn is_error(&self) -> bool {
        self.state.is_some()
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum ErrorType {
    Standard,
    TravelingCustomer,
    HiddenEvent,
}

impl ErrorType {
    pub fn get_title(&self) -> TextContent {
        match self {
            ErrorType::Standard => DetailsString::AV_LRC_GENERIC_ERROR_TITLE.get_localised_text(),
            ErrorType::TravelingCustomer => {
                DetailsString::AV_LRC_ERROR_TRAVELING_CUSTOMER_TITLE.get_localised_text()
            }
            ErrorType::HiddenEvent => {
                DetailsString::AV_LRC_GENERIC_ERROR_TITLE.get_localised_text()
            }
        }
    }
    pub fn get_message(&self) -> TextContent {
        match self {
            ErrorType::Standard => {
                DetailsString::AV_LRC_RENDERING_ERROR_MESSAGE.get_localised_text()
            }
            ErrorType::TravelingCustomer => {
                DetailsString::AV_LRC_GEO_RESTRICTION_BODY.get_localised_text()
            }
            ErrorType::HiddenEvent => {
                DetailsString::AV_LRC_GENERIC_ERROR_MESSAGE.get_localised_text()
            }
        }
    }
    pub fn get_error_code(&self) -> Option<String> {
        match self {
            ErrorType::Standard => Some("9345".to_string()),
            ErrorType::TravelingCustomer => None,
            ErrorType::HiddenEvent => Some("5004".to_string()),
        }
    }

    pub fn get_modal_name(&self) -> String {
        match self {
            ErrorType::Standard => "detailsPageGenericError".to_string(),
            ErrorType::TravelingCustomer => "detailsPageTravelingCustomerError".to_string(),
            ErrorType::HiddenEvent => "detailsPageHiddenEventError".to_string(),
        }
    }
}

pub fn set_details_error_state(scope: Scope, error_type: Option<ErrorType>) {
    let error_state = use_context::<RwSignal<ErrorState>>(scope);
    match error_state {
        None => {
            DETAILS_LOGGER.error("Unable to find the details page error state (no-op)");
        }
        Some(state) => {
            match error_type {
                None => {
                    DETAILS_LOGGER
                        .error("Details error state has been unset, error overlay is now hidden");
                }
                Some(_) => {
                    DETAILS_LOGGER
                        .error("Details error state has been set, error overlay is now visible");
                }
            }
            state.set(ErrorState { state: error_type })
        }
    }
}

pub fn set_details_standard_error_state(scope: Scope) {
    set_details_error_state(scope, Some(ErrorType::Standard));
}

pub fn set_details_traveling_customer_error_state(scope: Scope) {
    set_details_error_state(scope, Some(ErrorType::TravelingCustomer));
}

pub fn set_hidden_event_error_state(scope: Scope) {
    set_details_error_state(scope, Some(ErrorType::HiddenEvent));
}

#[Composer]
fn ErrorOverlayContent(ctx: &AppContext, error_type: ErrorType) -> ColumnComposable {
    let wrapped_navigate_to_search = {
        let scope = ctx.scope();
        let error_type = error_type.clone();
        move || {
            report_error_modal_button_pressed(scope, &error_type);
            navigate_to_search(scope)
        }
    };

    let wrapped_navigate_to_home = {
        let scope = ctx.scope();
        let error_type = error_type.clone();
        move || {
            report_error_modal_button_pressed(scope, &error_type);
            navigate_to_home(scope)
        }
    };

    report_error_modal_rendered(ctx.scope(), &error_type);
    report_cx_fatal(RustPage::RUST_DETAILS);

    let title = error_type.get_title();
    let message = error_type.get_message();
    let mut context_messages = vec![title.clone(), message.clone()];
    let error_code = error_type.get_error_code().map(|code| {
        let text_content = TextContent::String(code);
        context_messages.push(text_content.clone());
        text_content
    });

    let error_code_row = Box::new(move |ctx: &AppContext| {
        if let Some(error_code) = error_code.clone() {
            compose_option! {
                Row() {
                    TypographyBody400(content: DetailsString::AV_LRC_ERROR_CODE.get_localised_text())
                        .font_weight(FontWeight::Bold)
                    TypographyBody400(content: error_code)
                }
                .main_axis_alignment(MainAxisAlignment::SpacedBy(5.0))
            }
        } else {
            None
        }
    });
    compose! {
        Column() {
            Column() {
                TypographyHeading600(content: title.clone())
                TypographyBody400(content: message.clone())
                Memo(item_builder: error_code_row)
                Row() {
                    PrimaryButton(variant: PrimaryButtonVariant::TextSize400(DetailsString::AV_LRC_GO_TO_HOME.get_localised_text()))
                        .on_select(wrapped_navigate_to_home)
                        .test_id(DETAILS_ERROR_OVERLAY_HOME_BUTTON_TEST_ID)
                        .preferred_focus(true)
                    PrimaryButton(variant: PrimaryButtonVariant::TextSize400(DetailsString::AV_LRC_SEARCH.get_localised_text()))
                        .on_select(wrapped_navigate_to_search)
                        .test_id(DETAILS_ERROR_OVERLAY_SEARCH_BUTTON_TEST_ID)
                }
                .focus_hierarchical_container(NavigationStrategy::Horizontal)
                .main_axis_alignment(MainAxisAlignment::SpacedBy(10.0))
            }
            .width(1000.0)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(25.0))
        }
        .main_axis_alignment(MainAxisAlignment::Center)
        .padding(Padding::new(TITLE_DETAILS_LEFT_OFFSET, 0.0, 0.0, 0.0))
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
        .test_id(DETAILS_ERROR_OVERLAY_TEST_ID)
        .background_color(get_ignx_color(FableColor::BACKGROUND))
        .accessibility_context_messages(context_messages)
    }
}

// Overlays the page when the context detects an issue with the page, Independent of details context
// in case something causes issue with it.
#[Composer]
pub fn ErrorOverlay(ctx: &AppContext) -> MemoComposable {
    let error_state = use_context::<RwSignal<ErrorState>>(ctx.scope())
        .unwrap_or_else(|| create_rw_signal(ctx.scope(), ErrorState::standard_error()));

    let item_builder: Box<ComposableBuilder<ColumnComposable>> = Box::new(move |ctx| {
        let Some(error_state) = error_state.try_get() else {
            DETAILS_LOGGER.error("Unable to get error state aborting");
            return None;
        };

        if let Some(state) = error_state.state {
            return Some(popup_wrapper(
                ctx,
                compose! { ErrorOverlayContent(error_type: state) },
            ));
        }

        None
    });

    compose! {
        Memo(item_builder)
    }
}

pub fn use_details_error_state(scope: Scope) -> Option<RwSignal<ErrorState>> {
    use_context::<RwSignal<ErrorState>>(scope)
}

pub fn setup_details_error_state(scope: Scope, start_value: Option<ErrorType>) {
    let state = ErrorState { state: start_value };
    provide_context::<RwSignal<ErrorState>>(scope, create_rw_signal(scope, state));
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use router::{MockRouting, RoutingContext};
    use rust_features::provide_context_test_rust_features;
    use std::rc::Rc;

    #[test]
    fn should_not_display_the_error_overlay_when_error_state_is_none() {
        ignx_compositron::app::launch_test(
            |ctx| {
                setup_details_error_state(ctx.scope(), None);

                compose! {
                    ErrorOverlay()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id(DETAILS_ERROR_OVERLAY_TEST_ID);
                assert_node_does_not_exist!(node);
            },
        )
    }

    #[test]
    fn should_display_the_error_overlay_when_error_state_is_some() {
        ignx_compositron::app::launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                setup_details_error_state(ctx.scope(), Some(ErrorType::Standard));

                compose! {
                    ErrorOverlay()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id(DETAILS_ERROR_OVERLAY_TEST_ID);
                assert_node_exists!(node);
            },
        )
    }

    #[test]
    fn should_navigate_to_home_on_home_button_pressed() {
        ignx_compositron::app::launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                setup_details_error_state(ctx.scope(), Some(ErrorType::Standard));
                let mut mock_router = MockRouting::default();
                mock_router
                    .expect_navigate()
                    .times(1)
                    .returning(|loc, source| {
                        assert_eq!(
                            Location {
                                pageType: PageType::Rust(RUST_COLLECTIONS),
                                pageParams: Default::default(),
                            },
                            loc
                        );
                        assert_eq!(source, "DETAILS_PAGE_ERROR");
                    });
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

                compose! {
                    ErrorOverlay()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id(DETAILS_ERROR_OVERLAY_HOME_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(node.borrow_props().node_id);
                test_game_loop.tick_until_done();
            },
        )
    }

    #[test]
    fn should_navigate_to_search_on_search_button_pressed() {
        ignx_compositron::app::launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                setup_details_error_state(ctx.scope(), Some(ErrorType::Standard));
                let mut mock_router = MockRouting::default();
                mock_router
                    .expect_navigate()
                    .times(1)
                    .returning(|loc, source| {
                        assert_eq!(
                            Location {
                                pageType: PageType::Js(JSPage::FIND_PAGE),
                                pageParams: Default::default(),
                            },
                            loc
                        );
                        assert_eq!(source, "DETAILS_PAGE_ERROR");
                    });
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

                compose! {
                    ErrorOverlay()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id(DETAILS_ERROR_OVERLAY_SEARCH_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(node.borrow_props().node_id);
                test_game_loop.tick_until_done();
            },
        )
    }

    #[test]
    fn test_hidden_event_error_type_properties() {
        let error_type = ErrorType::HiddenEvent;

        assert_eq!(
            error_type.get_title(),
            DetailsString::AV_LRC_GENERIC_ERROR_TITLE.get_localised_text()
        );
        assert_eq!(
            error_type.get_message(),
            DetailsString::AV_LRC_GENERIC_ERROR_MESSAGE.get_localised_text()
        );
        assert_eq!(error_type.get_error_code(), Some("5004".to_string()));
        assert_eq!(
            error_type.get_modal_name(),
            "detailsPageHiddenEventError".to_string()
        );
    }

    #[test]
    fn test_hidden_event_error_factory_method() {
        let error_state = ErrorState::hidden_event_error();

        assert_eq!(error_state.state, Some(ErrorType::HiddenEvent));
        assert!(error_state.is_error());
    }
}
