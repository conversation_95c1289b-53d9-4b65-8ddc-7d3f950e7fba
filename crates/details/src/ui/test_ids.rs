pub const DETAILS_ROOT_TEST_ID: &str = "DetailsPageRoot";
pub const DETAILS_PAGE_TITLE_ACTIONS: &str = "DetailsPageTitleActions";
pub const DETAILS_PAGE_TITLE_ACTIONS_CONTAINER: &str = "DetailsPageTitleActionsContainer";
pub const DETAILS_TITLE_ACTION_TEST_ID: &str = "DetailsPageTitleActionItem";
pub const DETAILS_COMPONENT_LIST_TEST_ID: &str = "DetailsPageBTFComponentList";
pub const DETAILS_BTF_STANDARD_CAROUSEL_TEST_ID: &str = "DetailsPageBTFStandardCarousel";
pub const DETAILS_BTF_BEARD_SUPPORTED_CAROUSEL_TEST_ID: &str =
    "DetailsPageBTFBeardSupportedCarousel";
pub const DETAILS_BTF_DISCOVERY_ASSISTANT_PILL_CONTAINER_TEST_ID: &str =
    "DetailsPageBTFDiscoveryAssistantPillContainer";
// Pointer control
pub const DETAILS_UP_CARET_POINTER_TEST_ID: &str = "DetailsUpCaretPointer";
pub const DETAILS_DOWN_CARET_POINTER_TEST_ID: &str = "DetailsDownCaretPointer";

// Entitlement row
pub const DETAILS_ENTITLEMENT_ROW_TEST_ID: &str = "DetailsEntitlementRow";

// Modal manager
pub const DETAILS_MODAL_MANAGER_TEST_ID: &str = "DetailsModalManager";
pub const DETAILS_MODAL_MANAGER_ITEM_TEST_ID: &str = "DetailsModalManagerItem";

// Contextual Menu
pub const DETAILS_CONTEXTUAL_MENU_TEST_ID: &str = "DetailsContextualMenu";

// MPO
pub const DETAILS_MPO_TEST_ID: &str = "DetailsPageMpo";
pub const DETAILS_MPO_UP_CARET_TEST_ID: &str = "DetailsPageMpoUpCaret";
pub const DETAILS_MPO_SECTION_CONTAINER_TEST_ID: &str = "DetailsPageMpoSectionContainer";
pub const DETAILS_MPO_SECTION_TEST_ID: &str = "DetailsPageMpoSection";
pub const DETAILS_MPO_SECTION_HEADER_TEST_ID: &str = "DetailsPageMpoSectionHeader";
pub const DETAILS_MPO_SECTION_DISCLAIMER_TEST_ID: &str = "DetailsPageMpoSectionDisclaimer";

//LANGUAGE & More Details
pub const L_M_DETAILS_OK_BUTTON: &str = "ok-button";
pub const L_M_DETAILS_METADATA_ITEM_CONTENT: &str = "metadata-item-content";
pub const L_M_DETAILS_METADATA_ITEM: &str = "metadata-item";
pub const L_M_DETAILS_METADATA_LIST: &str = "metadata-list";
pub const L_M_DETAILS_MODAL_CONTAINER: &str = "modal-container";

// Customer Service Modal
pub const CUSTOMER_SERVICE_OK_BUTTON: &str = "cs-ok-button";
pub const CUSTOMER_SERVICE_MODAL_CONTAINER: &str = "cs-modal-container";
pub const CUSTOMER_SERVICE_WIDGET_LIST: &str = "cs-widget-list";
pub const CUSTOMER_SERVICE_WIDGET_ITEM: &str = "cs-widget-item";
pub const CUSTOMER_SERVICE_WIDGET_ITEM_SECTION: &str = "cs-widget-item-section";
pub const CUSTOMER_SERVICE_WIDGET_ITEM_TITLE: &str = "cs-widget-item-title";
pub const CUSTOMER_SERVICE_WIDGET_ITEM_DESCRIPTION: &str = "cs-widget-item-description";
pub const CUSTOMER_SERVICE_WIDGET_ITEM_IMAGE: &str = "cs-widget-item-image";

// Require Sign in modal
pub const REQUIRE_SIGN_IN_TEST_ID: &str = "DetailsRequireSignInModal";
pub const REQUIRE_SIGN_IN_CANCEL_BUTTON_TEST_ID: &str = "DetailsRequireSignInCancelButton";
pub const REQUIRE_SIGN_IN_SIGN_IN_BUTTON_TEST_ID: &str = "DetailsRequireSignSignInButton";

// Pre Order Details Modal
pub const PRE_ORDER_DETAILS_TEST_ID: &str = "DetailsPreOrderDetailsModal";
pub const PRE_ORDER_DETAILS_OK_BUTTON_TEST_ID: &str = "DetailsPreOrderOkButton";
pub const PRE_ORDER_DETAILS_PARAGRAPH_TEST_ID: &str = "DetailsPreOrderParagraph";

// Suppression Modal
pub const SUPPRESSION_DETAILS_TEST_ID: &str = "DetailsSuppressionModal";
pub const SUPPRESSION_DETAILS_OK_BUTTON_TEST_ID: &str = "DetailsSuppressionOkButton";

// Content Unavailable Modal
pub const CONTENT_UNAVAILABLE_DETAILS_TEST_ID: &str = "DetailsContentUnavailableModal";
pub const CONTENT_UNAVAILABLE_DETAILS_OK_BUTTON_TEST_ID: &str = "DetailsContentUnavailableOkButton";

// Error overlay test id
pub const DETAILS_ERROR_OVERLAY_TEST_ID: &str = "DetailsErrorOverlay";
pub const DETAILS_ERROR_OVERLAY_HOME_BUTTON_TEST_ID: &str = "DetailsErrorOverlayHomeButton";
pub const DETAILS_ERROR_OVERLAY_SEARCH_BUTTON_TEST_ID: &str = "DetailsErrorOverlaySearchButton";

// Season & Episodes
pub const DETAILS_SEASON_AND_EPISODES_CARD_CAROUSEL_TEST_ID: &str =
    "DetailsSeasonAndEpisodesCarousel";
pub const DETAILS_SEASON_AND_EPISODES_EPISODE_CARD_TEST_ID: &str =
    "DetailsSeasonAndEpisodesCarouselEpisodeCard";

// Skeleton
pub const DETAILS_PAGE_SKELETON_ATF_TEST_ID: &str = "DetailsPageSkeletonAtf";
pub const DETAILS_PAGE_SKELETON_BTF_TEST_ID: &str = "DetailsPageSkeletonBtf";
pub const DETAILS_PAGE_SKELETON_SEASON_AND_EPISODES_TEST_ID: &str =
    "DetailsPageSkeletonSeasonAndEpisodes";

pub const DETAILS_PAGE_BTF_RETRY_CONTAINER_TEST_ID: &str = "DetailsPageBtfRetryContainer";
pub const DETAILS_PAGE_INFORMATIONAL_MESSAGE_TEST_ID: &str = "DetailsPageAtfInformationalMessage";

pub const DETAILS_PAGE_TITLE_DETAILS_TEST_ID: &str = "DetailsPageTitleDetails";
pub const DETAILS_PAGE_BTF_TEST_ID: &str = "DetailsPageBtf";
pub const DETAILS_PAGE_TAB_BUTTONS: &str = "DetailsPageTabButtons";
