use crate::modules::title_actions::types::TitleActionHeight;
use amzn_fable_tokens::{FableColor, FableText};
use fableous::buttons::primary_button::*;
use fableous::typography::type_ramp::TypeRamp;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::column::ColumnComposable;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};
use ignx_compositron::{context::AppContext, prelude::*};

pub const TOOL_TIP_BUTTON_PRIMARY_BUTTON_TEST_ID: &str = "tool-tip-button--primary-button";
const TOOL_TIP_BUTTON_TOOL_TIP_TEST_ID: &str = "tool-tip-button--tool-tip";

#[Composer]
pub fn ToolTipButton(
    ctx: &AppContext,
    height: TitleActionHeight,
    label: TextContent,
    tool_tip: TextContent,
) -> ColumnComposable {
    let focused = create_focus_signal(ctx.scope());
    let type_ramp: TypeRamp = FableText::TYPE_LABEL200.into();
    let line_height = type_ramp.line_height;
    let tool_tip_color = Signal::derive(ctx.scope(), move || {
        if focused.get() {
            get_ignx_color(FableColor::PRIMARY)
        } else {
            get_ignx_color(FableColor::PRIMARY000)
        }
    });
    let variant = match height {
        TitleActionHeight::OneLine => PrimaryButtonVariant::TextSize400(label.clone()),
        TitleActionHeight::TwoLines => PrimaryButtonVariant::TextSize800(label.clone()),
    };

    compose! {
        Column() {
            Column() {
                TypographyLabel200(content: tool_tip.clone(), color: tool_tip_color)
                .test_id(TOOL_TIP_BUTTON_TOOL_TIP_TEST_ID)
            }
            .translate_y(-(line_height + 9.0))
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .height(0.0)

            PrimaryButton(variant)
            .focused(focused)
            .accessibility_description(label)
            .accessibility_closing_context_message(tool_tip)
            .test_id(TOOL_TIP_BUTTON_PRIMARY_BUTTON_TEST_ID)
        }
        .main_axis_alignment(MainAxisAlignment::Start)
        .cross_axis_alignment(CrossAxisAlignment::Center)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::modules::localized_text::DetailsString;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::assert_node_exists;
    use ignx_compositron::text::TextContent;
    use rstest::rstest;

    #[rstest]
    #[case(TitleActionHeight::OneLine)]
    #[case(TitleActionHeight::TwoLines)]
    fn test_record_season_button_renders_correctly(#[case] height: TitleActionHeight) {
        launch_test(
            |ctx| {
                let label = DetailsString::PV_LE_IP_RECORD_TNF_LIVE.get_localised_text();
                let tool_tip = DetailsString::PV_LE_IP_OPT_IN_TO_WATCH.get_localised_text();

                compose! {
                    ToolTipButton(height, label, tool_tip)
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let tooltip = tree.find_by_test_id(TOOL_TIP_BUTTON_TOOL_TIP_TEST_ID);
                assert_node_exists!(&tooltip);

                assert_eq!(
                    TextContent::String(tooltip.borrow_props().text.clone().unwrap_or_default()),
                    DetailsString::PV_LE_IP_OPT_IN_TO_WATCH.get_localised_text()
                );
            },
        );
    }
}
