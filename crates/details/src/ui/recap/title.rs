use crate::{
    context::state::state_creators::season_episodes::SeasonEpisodesState,
    network::types::season_episodes::EpisodeData,
};
use genai_recap::{
    network::RecapType,
    types::{
        RecapExperienceTitle, RecapExperienceTitlePreviousSeason, RecapExperienceTitleThisEpisode,
        RecapExperienceTitleThisSeason,
    },
};
use ignx_compositron::{reactive::SignalGetUntracked, reactive::SignalWithUntracked};

pub fn generate_recap_experience_title(
    recap_type: &RecapType,
    season_episode_state: &SeasonEpisodesState,
) -> RecapExperienceTitle {
    let current_season_number = season_episode_state.current_season_number.get_untracked();
    let current_episode_idx = season_episode_state.current_episode_idx.get_untracked();

    match recap_type {
        RecapType::Episode => {
            let episode_title =
                season_episode_state
                    .current_season
                    .with_untracked(|current_season| {
                        current_season
                            .get(current_episode_idx as usize)
                            .map(|episode_info| {
                                episode_info.episode.with_untracked(|episode_data| {
                                    match episode_data {
                                        EpisodeData::Episode(episode) => {
                                            episode.title_details.title.clone()
                                        }
                                        EpisodeData::EpisodeV2(episode_v2) => {
                                            episode_v2.title_details.title.clone()
                                        }
                                    }
                                })
                            })
                    });

            RecapExperienceTitle::ThisEpisode(RecapExperienceTitleThisEpisode {
                season: current_season_number,
                episode: (current_episode_idx + 1) as u32,
                episode_title,
            })
        }
        RecapType::Season => RecapExperienceTitle::ThisSeason(RecapExperienceTitleThisSeason {
            selected_season: current_season_number,
            selected_episode: (current_episode_idx + 1) as u32,
        }),
        RecapType::PreviousSeason => {
            RecapExperienceTitle::PreviousSeason(RecapExperienceTitlePreviousSeason {
                previous_season: current_season_number - 1,
            })
        }
        RecapType::Movie => RecapExperienceTitle::Movie,
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::network::network_type::season_episodes::EpisodeInfo;
    use common_transform_types::details::TitleDecorationInformationCore;
    use common_transform_types::season_episodes::{Episode, EpisodeListV2Episode};
    use ignx_compositron::{
        app::launch_only_scope, prelude::create_rw_signal, prelude::Scope, prelude::SignalSet,
    };
    use rstest::*;
    use std::collections::HashMap;

    fn generate_episode_info(scope: Scope, title: &str, episodic_release: bool) -> EpisodeInfo {
        let title_details = TitleDecorationInformationCore {
            audios: vec![],
            entity_type: "".to_string(),
            gti: "some_gti".to_string(),
            images: Default::default(),
            is_in_watchlist: false,
            ratings_metadata: Default::default(),
            subtitles: vec![],
            synopsis: "".to_string(),
            title: title.to_string(),
            title_type: Default::default(),
            badges: Default::default(),
            current_episode_number: None,
            maturity_rating: None,
            release_date: None,
            runtime_seconds: None,
            upcoming_message: None,
            season_id: None,
        };

        let episode_data = if episodic_release {
            EpisodeData::EpisodeV2(EpisodeListV2Episode {
                actions: vec![],
                entitlement_messaging: HashMap::new(),
                title_details,
                gti: "some_gti".to_string(),
                sequence_number: 1,
                season_sequence_number: 1,
                traits: vec![],
            })
        } else {
            EpisodeData::Episode(Episode {
                actions: vec![],
                entitlement_messaging: HashMap::new(),
                title_details,
                is_first_in_season: false,
                is_last_in_season: false,
            })
        };

        let episode = create_rw_signal(scope, episode_data);

        EpisodeInfo {
            episode,
            id: "id".to_string(),
            season_idx: 0,
        }
    }

    #[rstest]
    #[case(RecapType::Episode, 1, 1, vec!["Episode 1".to_string(), "Episode 2".to_string()], RecapExperienceTitle::ThisEpisode(RecapExperienceTitleThisEpisode { season: 1, episode: 2, episode_title: Some("Episode 2".to_string()) }), true)]
    #[case(RecapType::Episode, 1, 2, vec!["Episode 1".to_string(), "Episode 2".to_string()], RecapExperienceTitle::ThisEpisode(RecapExperienceTitleThisEpisode { season: 1, episode: 3, episode_title: None }), true)]
    #[case(RecapType::Season, 2, 0, vec![], RecapExperienceTitle::ThisSeason(RecapExperienceTitleThisSeason { selected_season: 2, selected_episode: 1 }), true)]
    #[case(RecapType::PreviousSeason, 3, 0, vec![], RecapExperienceTitle::PreviousSeason(RecapExperienceTitlePreviousSeason { previous_season: 2 }), true)]
    #[case(RecapType::Movie, 0, 0, vec![], RecapExperienceTitle::Movie, true)]
    #[case(RecapType::Episode, 1, 1, vec!["Episode 1".to_string(), "Episode 2".to_string()], RecapExperienceTitle::ThisEpisode(RecapExperienceTitleThisEpisode { season: 1, episode: 2, episode_title: Some("Episode 2".to_string()) }), false)]
    #[case(RecapType::Episode, 1, 2, vec!["Episode 1".to_string(), "Episode 2".to_string()], RecapExperienceTitle::ThisEpisode(RecapExperienceTitleThisEpisode { season: 1, episode: 3, episode_title: None }), false)]
    #[case(RecapType::Season, 2, 0, vec![], RecapExperienceTitle::ThisSeason(RecapExperienceTitleThisSeason { selected_season: 2, selected_episode: 1 }), false)]
    #[case(RecapType::PreviousSeason, 3, 0, vec![], RecapExperienceTitle::PreviousSeason(RecapExperienceTitlePreviousSeason { previous_season: 2 }), false)]
    #[case(RecapType::Movie, 0, 0, vec![], RecapExperienceTitle::Movie, false)]
    fn test_generate_recap_experience_title(
        #[case] recap_type: RecapType,
        #[case] current_season_number: u32,
        #[case] current_episode_idx: i32,
        #[case] episode_titles: Vec<String>,
        #[case] expected: RecapExperienceTitle,
        #[case] episodic_release: bool,
    ) {
        launch_only_scope(move |scope| {
            let season_episode_state = SeasonEpisodesState::new(scope);

            let current_season = episode_titles
                .iter()
                .map(|title| generate_episode_info(scope, title, episodic_release))
                .collect();

            season_episode_state
                .current_season_number
                .set(current_season_number);
            season_episode_state
                .current_episode_idx
                .set(current_episode_idx);
            season_episode_state.current_season.set(current_season);

            let result = generate_recap_experience_title(&recap_type, &season_episode_state);
            assert_eq!(result, expected);
        });
    }
}
