use crate::context::DetailsPageContextStore;
use crate::modules::title_actions::get_title_actions_buttons_list::get_primary_title_action;
use crate::modules::title_actions::handlers::recap::play_video_recap;
use crate::ui::recap::play_button::generate_recap_play_button;
use crate::ui::recap::title::generate_recap_experience_title;
use crate::ui::title_actions::title_action_button_presenter::get_presentation_information_for_button;
use details_derive::get_some_or_return;
use fableous::animations::FableMotionDuration;
use genai_recap::drawer::recap_drawer::*;
use genai_recap::experience::recap_experience::*;
use genai_recap::network::Recap;
use genai_recap::types::metrics::{
    report_experience_opened, DRAWER_CLOSED_METRIC, DRAWER_OPENED_METRIC, EXPERIENCE_CLOSED_METRIC,
};
use genai_recap::types::RecapExperienceModel;
use ignx_compositron::animation::with_animation;
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{context::AppContext, prelude::*, reactive::Signal};
use media_background::types::MediaBackgroundType;
use std::cell::*;
use std::rc::*;

const RECAP_CONTAINER_DRAWER_TEST_ID: &str = "recap-container-drawer";
const RECAP_CONTAINER_EXPERIENCE_TEST_ID: &str = "recap-container-experience";
const RECAP_CONTAINER_EXPERIENCE_CONTAINER_TEST_ID: &str = "recap-container-experience-container";

#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
#[derive(Clone)]
enum State {
    Drawer,
    Experience(RecapExperienceModel),
}

#[Composer]
pub fn RecapContainer(
    ctx: &AppContext,
    #[into] details_context: Rc<RefCell<DetailsPageContextStore>>,
    on_closed: Rc<dyn Fn()>,
) -> StackComposable {
    let title_information: RwSignal<
        Option<crate::network::types::title_information::TitleInformation>,
    > = details_context.borrow().state.title_information;
    let atf_response = details_context.borrow().state.page_resources.atf_response;
    let media_background_signal = details_context.borrow().state.media_background;
    let show_utility_nav = details_context
        .borrow()
        .dependent_data
        .nav_control
        .show_utility_nav;
    let current_state = create_rw_signal(ctx.scope(), State::Drawer);

    let drawer_pointer_control = Signal::derive(ctx.scope(), move || {
        if matches!(current_state.get(), State::Drawer) {
            FocusPointerControl::Inherit
        } else {
            FocusPointerControl::Disabled
        }
    });

    let experience_pointer_control = Signal::derive(ctx.scope(), move || {
        if matches!(current_state.get(), State::Experience(_)) {
            FocusPointerControl::Inherit
        } else {
            FocusPointerControl::Disabled
        }
    });

    let experience_opacity = create_rw_signal(ctx.scope(), 0.0);
    let drawer_opacity = create_rw_signal(ctx.scope(), 1.0);

    let show_title = title_information.with(|title_information| {
        let title_info = match title_information.as_ref() {
            None => return "".to_string(),
            Some(info) => info,
        };

        if let Some(season_details) = title_info.get_season_details() {
            if let Some(series_title) = season_details.series_title.as_ref() {
                return series_title.clone();
            }
        }

        title_info
            .get_basic_details()
            .title_decoration_information
            .title
            .clone()
    });

    let season_number: Option<i32> = title_information.with(|title_information| {
        let title_info = title_information.as_ref()?;
        let season_details = title_info.get_season_details()?;
        Some(season_details.season_number)
    });

    let play_action = atf_response.with_untracked(|atf_response| {
        atf_response.as_ref().and_then(|response| {
            get_primary_title_action(response.actions.clone()).and_then(|inner_action| {
                let select_handler = {
                    let ctx_clone = ctx.clone();
                    let item_ref = inner_action.clone();
                    Rc::new(move || match &item_ref.on_select {
                        None => {}
                        Some(handler_fn) => handler_fn(&ctx_clone, &item_ref),
                    })
                };

                let first_action_presentation = get_presentation_information_for_button(
                    ctx.scope(),
                    &inner_action,
                    true,
                    &details_context.borrow().state.title_actions_state,
                );

                generate_recap_play_button(first_action_presentation, select_handler)
            })
        })
    });

    let ctx_clone = ctx.clone();
    let open_recap = Rc::new(move |recap, first_load| {
        // We skip attempting to open a video recap on the first load as that means this has failed from the title action button handler
        if first_load
            || play_video_recap(&ctx_clone, &recap, "VideoRecapSelected.Drawer".to_string())
                .is_err()
        {
            let recap_experience_data = RecapExperienceModel {
                segments: recap.recap_segments.clone(),
                default_background_image: recap.background_image.clone(),
                title: generate_recap_experience_title(
                    &recap.recap_type,
                    &details_context.borrow().state.season_episodes_state,
                ),
                play_action: play_action.clone(),
                recap_type: recap.recap_type.clone(),
            };

            report_experience_opened(&recap.recap_type);
            current_state.set(State::Experience(recap_experience_data));
        }
    });

    let on_select = Rc::new({
        move |idx: usize| {
            atf_response.with_untracked(|atf_response| {
                atf_response.as_ref().and_then(|atf_response| {
                    atf_response.recaps.get(idx).map(|selected_recap| {
                        open_recap(selected_recap.clone(), false);
                    })
                })
            });
        }
    });

    let on_closed = Rc::new(move || {
        match current_state.get_untracked() {
            State::Drawer => metric!(DRAWER_CLOSED_METRIC, 1),
            State::Experience(_) => metric!(EXPERIENCE_CLOSED_METRIC, 1),
        }
        on_closed();
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();

        move |_| {
            let state = get_some_or_return!(current_state.try_get());

            match state {
                State::Drawer => {
                    metric!(DRAWER_OPENED_METRIC, 1);
                }
                State::Experience(_) => {
                    // To stop any trailer playback and free up texture memory
                    media_background_signal.try_set(MediaBackgroundType::None);
                    show_utility_nav.try_set(false);

                    with_animation(
                        ctx.scope(),
                        &Animation::default().with_duration(FableMotionDuration::Standard.into()),
                        move || {
                            experience_opacity.try_set(1.0);
                            drawer_opacity.try_set(0.0);
                        },
                    );
                }
            }
        }
    });

    let render_component = {
        let on_closed = on_closed.clone();
        move |recaps: &Vec<Recap>| {
            compose! {
                Stack() {
                    RecapDrawer(title: show_title.clone(), season_number: season_number, on_closed: on_closed.clone(), recaps, on_recap_selected: on_select)
                        .focus_pointer_control(drawer_pointer_control)
                        .opacity(drawer_opacity)
                        .test_id(RECAP_CONTAINER_DRAWER_TEST_ID)

                    Stack() {
                        Memo(item_builder: Box::new(move |ctx| {
                            let current_state = current_state.get();

                            if let State::Experience(data) = current_state {
                                compose_option! {
                                    RecapExperience(data, show_title: &show_title, on_closed: on_closed.clone())
                                        .focus_pointer_control(experience_pointer_control)
                                        .focus_window()
                                        .test_id(RECAP_CONTAINER_EXPERIENCE_TEST_ID)
                                }
                            } else {
                                None
                            }
                        }))
                    }
                    .test_id(RECAP_CONTAINER_EXPERIENCE_CONTAINER_TEST_ID)
                    .opacity(experience_opacity)
                }
            }
        }
    };

    atf_response
        .with_untracked(|atf_response| {
            atf_response.as_ref().and_then(|atf_response| {
                let recaps = &atf_response.recaps;
                if !recaps.is_empty() {
                    Some(render_component(recaps))
                } else {
                    None
                }
            })
        })
        .unwrap_or_else(|| {
            // protect against missing or empty data.
            on_closed();
            compose! { Stack() { Rectangle() } }
        })
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, use_details_page_context_test,
    };
    use crate::network::mock_responses::{
        MOCK_RESPONSE_ATF_WITH_EPISODE_RECAPS, MOCK_RESPONSE_VOD_ATF,
    };
    use crate::network::network_type::title_information::{
        BasicTitleInformation, SeasonDetails, VODTitleInformation,
    };
    use crate::network::parser::details_page_atf_parser;
    use crate::network::types::title_information::TitleInformation;
    use common_transform_types::details::TitleDecorationInformationCore;
    use ignx_compositron::test_utils::node_properties::NodeQuery;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, ComposableType};
    use ignx_compositron::{app::launch_test, test_utils::assert_node_exists};
    use router::hooks::setup_mock_routing_ctx;
    use rstest::*;

    fn assert_text_of_node(button: &'_ NodeQuery<'_>, expected_line: &'_ str) {
        let button_text_lines = button
            .find_any_child_with()
            .composable_type(ComposableType::RichTextLabel)
            .find_all();
        let line_of_text_button = button_text_lines.first().unwrap();
        let line_of_text_props = line_of_text_button.borrow_props();
        assert_eq!(
            line_of_text_props.text.clone().unwrap(),
            expected_line.to_string()
        );
    }

    struct TestConfig {
        atf_data: Option<String>,
        drawer_shown: bool,
        title_information: Option<TitleInformation>,
        expect_navigate: bool,
    }

    impl TestConfig {
        fn new() -> Self {
            TestConfig {
                atf_data: Some(MOCK_RESPONSE_ATF_WITH_EPISODE_RECAPS.to_string()),
                drawer_shown: false,
                title_information: None,
                expect_navigate: false,
            }
        }
        fn with_atf_data(mut self, atf_data: Option<String>) -> Self {
            self.atf_data = atf_data;
            self
        }

        fn with_drawer_shown(mut self) -> Self {
            self.drawer_shown = true;
            self
        }

        fn with_title_information(mut self, title_information: Option<TitleInformation>) -> Self {
            self.title_information = title_information;
            self
        }

        fn expect_navigate_to_playback(mut self) -> Self {
            self.expect_navigate = true;
            self
        }
    }

    fn setup_test(ctx: &AppContext, config: TestConfig) -> impl Composable<'static> {
        let details_context = create_or_use_details_context_test(ctx);

        if let Some(atf_response) = config.atf_data {
            let atf_data = details_page_atf_parser(atf_response)
                .map(|result| match result {
                    network::common::DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                    network::common::DeviceProxyResponse::ErrorResponse(_) => {
                        panic!("unexpected error response")
                    }
                })
                .expect("Expected vod response to be parsed correctly");
            details_context
                .borrow()
                .state
                .page_resources
                .atf_response
                .set(Some(atf_data));
        }

        if config.drawer_shown {
            details_context
                .borrow()
                .state
                .recap_state
                .drawer_shown
                .set(true);
        }

        details_context
            .borrow()
            .state
            .title_information
            .set(config.title_information);

        setup_mock_routing_ctx(ctx.scope(), |mock_routing| {
            if config.expect_navigate {
                mock_routing
                    .expect_navigate()
                    .times(1)
                    .returning(move |_, _| {});
            }
        });

        compose! {
            Stack() {
                RecapContainer(
                    details_context,
                    on_closed: Rc::new(move || {})
                )
            }
        }
    }

    #[test]
    pub fn should_transition_to_recap_experience_correctly_on_select() {
        launch_test(
            move |ctx| setup_test(&ctx, TestConfig::new()),
            move |_scope, mut test_game_loop| {
                let details_ctx = use_details_page_context_test(_scope);

                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id(RECAP_CONTAINER_DRAWER_TEST_ID);
                let experience_container =
                    tree.find_by_test_id(RECAP_CONTAINER_EXPERIENCE_CONTAINER_TEST_ID);
                let experience = tree.find_by_test_id(RECAP_CONTAINER_EXPERIENCE_TEST_ID);

                assert_eq!(drawer.borrow_props().base_styles.opacity, Some(1.0));
                assert_eq!(
                    experience_container.borrow_props().base_styles.opacity,
                    Some(0.0)
                );
                assert_node_does_not_exist!(&experience);

                let button = tree.find_by_test_id("recap-button-0");
                assert_node_exists!(&button);

                test_game_loop.send_on_select_event(button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id(RECAP_CONTAINER_DRAWER_TEST_ID);
                let experience_container =
                    tree.find_by_test_id(RECAP_CONTAINER_EXPERIENCE_CONTAINER_TEST_ID);
                let experience = tree.find_by_test_id(RECAP_CONTAINER_EXPERIENCE_TEST_ID);

                assert_eq!(drawer.borrow_props().base_styles.opacity, Some(0.0));
                assert_eq!(
                    experience_container.borrow_props().base_styles.opacity,
                    Some(1.0)
                );
                assert_node_exists!(&experience);

                // Assert that recap button is rendered with correct text as a proxy for the correct data being passed
                let recap_experience_button = tree.find_by_test_id(RECAP_EXPERIENCE_BUTTON_TEST_ID);
                assert_text_of_node(&recap_experience_button, "Resume");

                assert_eq!(
                    details_ctx.borrow().state.media_background.get_untracked(),
                    MediaBackgroundType::None,
                );
                assert!(
                    !details_ctx
                        .borrow()
                        .dependent_data
                        .nav_control
                        .show_utility_nav
                        .get_untracked(),
                    "Utility nav should be hidden after entering experience"
                );
            },
        )
    }

    #[test]
    pub fn should_transition_to_video_recap_correctly_on_select() {
        launch_test(
            move |ctx| setup_test(&ctx, TestConfig::new().expect_navigate_to_playback()),
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id(RECAP_CONTAINER_DRAWER_TEST_ID);
                let experience_container =
                    tree.find_by_test_id(RECAP_CONTAINER_EXPERIENCE_CONTAINER_TEST_ID);
                let experience = tree.find_by_test_id(RECAP_CONTAINER_EXPERIENCE_TEST_ID);

                assert_eq!(drawer.borrow_props().base_styles.opacity, Some(1.0));
                assert_eq!(
                    experience_container.borrow_props().base_styles.opacity,
                    Some(0.0)
                );
                assert_node_does_not_exist!(&experience);

                let button = tree.find_by_test_id("recap-button-2");
                assert_node_exists!(&button);

                test_game_loop.send_on_select_event(button.borrow_props().node_id);
                test_game_loop.tick_until_done();
            },
        )
    }

    #[test]
    pub fn should_render_drawer_buttons_titles_correctly() {
        launch_test(
            move |ctx| {
                setup_test(
                    &ctx,
                    TestConfig::new()
                        .with_drawer_shown()
                        .with_title_information(Some(create_title_information_for_series())),
                )
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id(RECAP_CONTAINER_DRAWER_TEST_ID);

                assert_node_exists!(&drawer);

                let title = drawer
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_first();
                assert_node_exists!(&title);
                assert_eq!(title.borrow_props().text, Some("The Boys".to_string()));

                assert_text_of_node(&tree.find_by_test_id("recap-button-0"), "Recap episode");
                assert_text_of_node(&tree.find_by_test_id("recap-button-1"), "Recap season 3");
                assert_text_of_node(
                    &tree.find_by_test_id("recap-button-2"),
                    "Recap previous season",
                );
            },
        );
    }

    fn create_basic_title_information(title: &str) -> BasicTitleInformation {
        BasicTitleInformation {
            title_decoration_information: TitleDecorationInformationCore {
                audios: vec![],
                entity_type: "".to_string(),
                gti: "".to_string(),
                images: Default::default(),
                is_in_watchlist: false,
                ratings_metadata: Default::default(),
                subtitles: vec![],
                synopsis: "".to_string(),
                title: title.to_string(),
                title_type: Default::default(),
                badges: Default::default(),
                current_episode_number: None,
                maturity_rating: None,
                release_date: None,
                runtime_seconds: None,
                upcoming_message: None,
                season_id: None,
            },
            is_audio_tracks_present: false,
            is_subtitle_present: false,
            genres: vec![],
            moods: None,
            studios: vec![],
            imdb_rating: 0.0,
            has_trailer: false,
            content_descriptors: vec![],
            directors: vec![],
            starring_cast: vec![],
            supporting_cast: vec![],
            high_value_message: None,
            entitlement_message: None,
            informational_message: None,
            metadata_badge_message: None,
            widget_sections: None,
            trailer_gti: None,
            impression_ref_marker: None,
        }
    }

    pub fn create_title_information_for_movie() -> TitleInformation {
        TitleInformation::Vod(VODTitleInformation {
            common: create_basic_title_information("Oppenheimer"),
            season_details: None,
            user_reaction: None,
            starlight: None,
        })
    }

    pub fn create_title_information_for_series() -> TitleInformation {
        TitleInformation::Vod(VODTitleInformation {
            common: create_basic_title_information(
                "Episode Name for The Boys which should not be used in Recaps",
            ),
            season_details: Some(SeasonDetails {
                series_title: Some("The Boys".to_string()),
                current_episode_number: 0,
                season_gti: "".to_string(),
                season_number: 3,
                season_title: "".to_string(),
                series_gti: None,
            }),
            user_reaction: None,
            starlight: None,
        })
    }

    #[rstest]
    #[case::series_should_prefer_series_title_over_episode_title(
        Some(create_title_information_for_series()),
        "The Boys"
    )]
    #[case::movie(Some(create_title_information_for_movie()), "Oppenheimer")]
    #[case::fallback_to_empty_if_none(None, "")]
    pub fn should_render_drawer_title_correctly(
        #[case] title_information: Option<TitleInformation>,
        #[case] expected_title: &'static str,
    ) {
        launch_test(
            move |ctx| {
                setup_test(
                    &ctx,
                    TestConfig::new()
                        .with_drawer_shown()
                        .with_title_information(title_information),
                )
            },
            move |_, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id(RECAP_CONTAINER_DRAWER_TEST_ID);

                assert_node_exists!(&drawer);

                let title = drawer
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_first();

                assert_node_exists!(&title);
                assert_eq!(title.borrow_props().text, Some(expected_title.to_string()));
            },
        );
    }

    #[rstest]
    #[case::no_atf_data(None)]
    #[case::no_recaps(Some(MOCK_RESPONSE_VOD_ATF.to_string()))]
    pub fn should_close_immediately_when_data_is_invalid(#[case] atf_data: Option<String>) {
        launch_test(
            move |ctx: AppContext| setup_test(&ctx, TestConfig::new().with_atf_data(atf_data)),
            move |_, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id(RECAP_CONTAINER_DRAWER_TEST_ID);

                assert_node_does_not_exist!(&drawer);
            },
        );
    }
}
