use std::rc::Rc;

use crate::ui::title_actions::title_action_button_presenter::TitleActionButtonPresentation;
use genai_recap::types::{PlayVariant, PrimaryVariant, RecapAction};

pub fn generate_recap_play_button(
    item_presentation: Option<TitleActionButtonPresentation>,
    on_select: Rc<dyn Fn()>,
) -> Option<RecapAction> {
    if let Some(item_presentation_inner) = item_presentation {
        match item_presentation_inner {
            TitleActionButtonPresentation::PlayButton {
                content,
                variant,
                progress,
                rounded: _,
                height,
            } => Some(RecapAction::Play(PlayVariant {
                title: content,
                on_select,
                height,
                variant,
                progress,
            })),
            TitleActionButtonPresentation::PrimaryButton {
                variant,
                disabled: _,
            } => Some(RecapAction::Primary(PrimaryVariant { variant, on_select })),
            _ => None,
        }
    } else {
        None
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use amzn_fable_tokens::FableIcon;
    use fableous::buttons::play_button::PlayButtonHeight;
    use fableous::buttons::primary_button::PrimaryButtonVariant;
    use fableous::progress_bar::ProgressBarVariant;
    use ignx_compositron::reactive::MaybeSignal;
    use ignx_compositron::text::TextContent;

    #[test]
    pub fn should_generate_play_button_data_correctly() {
        let play_button_presentation = Some(TitleActionButtonPresentation::PlayButton {
            content: TextContent::String("Resume".to_string()),
            variant: ProgressBarVariant::VOD,
            progress: 0.5,
            rounded: false,
            height: PlayButtonHeight::Size800,
        });
        let on_select = Rc::new(|| {});
        let result = generate_recap_play_button(play_button_presentation, on_select);

        let matches_title = match result.clone() {
            Some(RecapAction::Play(variant)) => {
                variant.title == TextContent::String("Resume".to_string())
            }
            _ => false,
        };

        let matches_variant = match result.clone() {
            Some(RecapAction::Play(fff)) => fff.variant == ProgressBarVariant::VOD,
            _ => false,
        };

        let matches_height = match result.clone() {
            Some(RecapAction::Play(variant)) => variant.height == PlayButtonHeight::Size800,
            _ => false,
        };

        let matches_progress = match result.clone() {
            Some(RecapAction::Play(variant)) => variant.progress == 0.5,
            _ => false,
        };

        assert!(matches_title);
        assert!(matches_variant);
        assert!(matches_height);
        assert!(matches_progress);
    }

    #[test]
    pub fn should_generate_primary_button_data_correctly() {
        let primary_button_presentation = Some(TitleActionButtonPresentation::PrimaryButton {
            variant: MaybeSignal::Static(PrimaryButtonVariant::IconAndTextSize800(
                FableIcon::PLAY.to_string(),
                TextContent::String("Watch Episode 1".to_string()),
            )),
            disabled: false,
        });
        let result = generate_recap_play_button(primary_button_presentation, Rc::new(|| {}));

        let matches_variant = match result.clone() {
            Some(RecapAction::Primary(variant)) => {
                variant.variant
                    == MaybeSignal::Static(PrimaryButtonVariant::IconAndTextSize800(
                        FableIcon::PLAY.to_string(),
                        TextContent::String("Watch Episode 1".to_string()),
                    ))
            }
            _ => false,
        };

        assert!(matches_variant);
    }

    #[test]
    pub fn should_generate_none_for_non_play_action() {
        let non_primary_or_play_button_presentation =
            Some(TitleActionButtonPresentation::IconButton {
                label: MaybeSignal::Static(TextContent::String("label".to_string())),
                icon: MaybeSignal::Static("icon".to_string()),
            });
        let on_select = Rc::new(|| {});
        let result = generate_recap_play_button(non_primary_or_play_button_presentation, on_select);

        let equals_none = match result {
            None => true,
            _ => false,
        };

        assert!(equals_none);
    }
}
