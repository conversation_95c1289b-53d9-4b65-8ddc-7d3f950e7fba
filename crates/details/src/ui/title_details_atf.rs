use crate::context::hooks::{empty_column_fallback, use_details_page_context};
use crate::context::state::base_creator::DetailsFocusedSection;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::ui::test_ids::DETAILS_PAGE_TITLE_DETAILS_TEST_ID;
use ignx_compositron::prelude::*;
use ignx_compositron::time::Instant;
use ignx_compositron::{compose, Composer};
use title_details::components::title::ImageLoadStatus;
use title_details::core::*;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("title_details_atf");

// This function fades in the details title details only when the image is finished loading,
// without this you will see a 'flash' of the title details especially on slower devices
fn fade_in_title_details_when_image_is_ready(
    ctx: &AppContext,
    image_ready: RwSignal<ImageLoadStatus>,
    global_opacity: RwSignal<f32>,
    details_title_opacity: RwSignal<f32>,
) {
    create_effect(ctx.scope(), {
        let load = {
            let ctx = ctx.clone();
            move || {
                ctx.schedule_task(Instant::now(), move || {
                    global_opacity.set(0.0);
                    details_title_opacity.set(1.0);
                });
            }
        };

        move |_| match image_ready.try_get() {
            Some(ImageLoadStatus::SUCCESS) => load(),
            Some(ImageLoadStatus::EMPTY) => load(),
            _ => {}
        }
    });
}

#[Composer]
pub fn TitleDetailsAtf(ctx: &AppContext) -> ColumnComposable {
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            DETAILS_LOGGER.error("Unable to obtain details context!");
            return empty_column_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let title_details_atf_signal = details_ctx.borrow().state.title_details.atf;

    let global_opacity = details_ctx.borrow().state.title_details.global_opacity;

    let title_details_opacity = details_ctx.borrow().state.title_details.atf_opacity;
    let image_ready = details_ctx.borrow().state.title_details.atf_image_load;

    fade_in_title_details_when_image_is_ready(
        ctx,
        image_ready,
        global_opacity,
        title_details_opacity,
    );

    let focused_section = details_ctx.borrow().state.focused_section;

    let focus_enabled = Signal::derive(ctx.scope(), move || match focused_section.try_get() {
        Some(DetailsFocusedSection::Atf) => true,
        Some(DetailsFocusedSection::Btf | DetailsFocusedSection::TabButtons) => false,
        _ => false,
    });
    let pointer_control_enabled =
        Signal::derive(ctx.scope(), move || match focused_section.try_get() {
            Some(DetailsFocusedSection::Atf) => FocusPointerControl::Inherit,
            Some(DetailsFocusedSection::Btf | DetailsFocusedSection::TabButtons) => {
                FocusPointerControl::Disabled
            }
            _ => FocusPointerControl::Disabled,
        });

    compose! {
        Column() {
            TitleDetailsSingleBuffer(data: title_details_atf_signal, image_load_status: Some(image_ready))
        }
        .focus_pointer_control(pointer_control_enabled)
        .focus_enabled(focus_enabled)
        .test_id(DETAILS_PAGE_TITLE_DETAILS_TEST_ID)
        .opacity(title_details_opacity)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, mock_out_successful_atf_response,
        use_details_page_context_test,
    };
    use mockall::TimesRange;
    #[test]
    fn should_initialise_as_transparent() {
        ignx_compositron::app::launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);

                compose! {
                    TitleDetailsAtf()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_once().node_tree;
                let title_details = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_DETAILS_TEST_ID);
                assert_eq!(title_details.borrow_props().base_styles.opacity, Some(0.0))
            },
        )
    }

    // Mock struct just to create difference between an already provided struct
    #[derive(Clone)]
    struct ExistingTitleSignal {
        pub signal: RwSignal<TitleDetailsChangeRequest>,
    }

    #[test]
    fn on_atf_load_after_image_is_ready() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let existing_signal = create_title_details_signals(ctx.scope());
                provide_context(
                    ctx.scope(),
                    ExistingTitleSignal {
                        signal: existing_signal.data,
                    },
                );
                let details_ctx = create_or_use_details_context_test(&ctx);
                details_ctx.borrow_mut().state.title_details.global = existing_signal.data;
                details_ctx.borrow_mut().state.title_details.global_opacity =
                    existing_signal.opacity;
                details_ctx.borrow_mut().state.title_details.global_y_offset =
                    existing_signal.y_offset;

                compose! {
                    TitleDetailsAtf()
                }
            },
            |scope, mut test_game_loop| {
                let existing_signal = use_context::<ExistingTitleSignal>(scope)
                    .expect("Existing title details sig to exist");
                let details_ctx = use_details_page_context_test(scope);

                mock_out_successful_atf_response(scope, TimesRange::from(1));
                details_ctx.borrow().get_atf();
                let node_tree = test_game_loop.tick_until_done();
                // Details is loaded here

                // Due to the scheduled task it should still be non-visible at this point
                let title_details = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_DETAILS_TEST_ID);
                assert_eq!(title_details.borrow_props().base_styles.opacity, Some(0.0));

                details_ctx
                    .borrow()
                    .state
                    .title_details
                    .atf_image_load
                    .set(ImageLoadStatus::SUCCESS);
                let node_tree = test_game_loop.tick_until_done();
                // Now it should be visible

                let title_details = node_tree.find_by_test_id(DETAILS_PAGE_TITLE_DETAILS_TEST_ID);
                assert_eq!(title_details.borrow_props().base_styles.opacity, Some(1.0));

                // Main title details should of been sent an empty request
                assert_eq!(
                    existing_signal.signal.get_untracked(),
                    TitleDetailsChangeRequest {
                        data: TitleDetailsData::Empty,
                        navigation_direction: Default::default(),
                    }
                )
            },
        )
    }
}
