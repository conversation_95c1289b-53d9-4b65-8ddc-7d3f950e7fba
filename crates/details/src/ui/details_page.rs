use crate::context::hooks::*;
use crate::context::DetailsPageContext;
use crate::context::ResourceState;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::back_press_handler::on_details_back_press;
use crate::ui::content::details_content::*;
use crate::ui::content::details_content_v2::*;
use crate::ui::error_overlay::*;
use crate::ui::modals::modal_manager::*;
use crate::ui::test_ids::{DETAILS_CONTEXTUAL_MENU_TEST_ID, DETAILS_ROOT_TEST_ID};
use contextual_menu::contextual_menu::*;
use fableous::animations::{FableMotionDuration, MotionDuration};
use fableous::pointer_control::use_focus_pointer_control;
use fableous::toasts::toast::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::accessbility::CustomAccessibilityBorderStyle;
use ignx_compositron::input::KeyCode;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::on_cleanup;
use ignx_compositron::{compose, Composer};
use media_background::types::MediaBackgroundType;
use modal_manager::modal_manager::AppModal;
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use std::rc::Rc;
use title_details::core::signals::TitleDetailsSignals;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("details_page");

struct DetailsPreconditionResult {
    details_ctx: DetailsPageContext,
    signals: DetailsPageSignals,
    back_handler: Rc<dyn Fn()>,
}

// Signals required to render details page
struct DetailsPageSignals {
    details_opacity: RwSignal<f32>,
    no_active_modal: Signal<bool>,
    loading: RwSignal<bool>,
}

fn get_required_details_signals(
    ctx: &AppContext,
    details_context: &'_ DetailsPageContext,
) -> DetailsPageSignals {
    // Modal related signals
    let active_modal = details_context.borrow().state.modal_state.active_modal;
    let details_opacity = details_context
        .borrow()
        .state
        .transition_state
        .transition_opacity;
    let no_active_modal = Signal::derive(ctx.scope(), move || {
        active_modal
            .try_get()
            .is_some_and(|val| val == DetailsModal::None)
    });

    let loading = details_context.borrow().state.page_resources.atf_is_loading;

    DetailsPageSignals {
        loading,
        details_opacity,
        no_active_modal,
    }
}

fn precondition(
    ctx: &AppContext,
    update_media_background_rw: RwSignal<MediaBackgroundType>,
    title_details_signals: TitleDetailsSignals,
    app_modal_data: WriteSignal<Vec<AppModal>>,
) -> Result<DetailsPreconditionResult, &'_ str> {
    let error_state = create_rw_signal(ctx.scope(), ErrorState::no_error());
    provide_context(ctx.scope(), error_state);

    let initial_state = DetailsInitialState {
        title_details_signals,
        media_background_signal: update_media_background_rw,
        app_modal_data,
    };

    let details_ctx_result =
        create_or_use_details_page_context_with_initial_state(ctx, initial_state);
    let details_ctx = match details_ctx_result {
        None => {
            error_state.set(ErrorState::standard_error());
            return Err("Failed to construct details page context");
        }
        Some(details_ctx) => details_ctx,
    };

    let required_signals = get_required_details_signals(ctx, &details_ctx);

    Ok(DetailsPreconditionResult {
        details_ctx: details_ctx.clone(),
        back_handler: Rc::new({
            let ctx = ctx.clone();
            move || {
                on_details_back_press(&ctx, &details_ctx);
            }
        }),
        signals: required_signals,
    })
}

/// Scaffolding composable for the details page responsible for:
/// - Loading overlay
/// - Error overlay
/// - Modal overlay
/// - Handling back navigation
/// - Mounting details content
///
/// For logic concerning the rendering of ATF/BTF/Tabs see DetailsContent
#[Composer]
pub fn DetailsPage(
    ctx: &AppContext,
    update_media_background_rw: RwSignal<MediaBackgroundType>,
    title_details_signals: &'_ TitleDetailsSignals,
    app_modal_data: WriteSignal<Vec<AppModal>>,
) -> StackComposable {
    let data = match precondition(
        ctx,
        update_media_background_rw,
        *title_details_signals,
        app_modal_data,
    ) {
        Ok(data) => data,
        Err(msg) => {
            DETAILS_LOGGER.error(msg);
            return compose! { Stack() { ErrorOverlay() } };
        }
    };

    let tab_controller_details_ctx = data.details_ctx.clone();
    let details_content_ctx = data.details_ctx.clone();
    let modal_details_ctx = data.details_ctx.clone();
    let toast_details_ctx = data.details_ctx.clone();

    let details_opacity = data.signals.details_opacity;
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            ctx.with_animation(
                Animation::default().with_delay(FableMotionDuration::Slow.to_duration()),
                move || {
                    details_opacity.set(1.0);
                },
            );
        }
    });

    let contextual_menu_state = data.details_ctx.borrow().state.contextual_menu_state;

    // Needed to make sure utility nav is always reset when leaving the details page
    let nav_control = data.details_ctx.borrow().dependent_data.nav_control.clone();
    let details_ctx = data.details_ctx;
    on_cleanup(ctx.scope(), move || {
        details_ctx
            .borrow()
            .state
            .title_details
            .global_y_offset
            .set(0.0);
        details_ctx
            .borrow()
            .state
            .title_details
            .global_opacity
            .set(1.0);
        nav_control.show_utility_nav.set(true);

        if let Some(ResourceState::Loading) = details_ctx
            .borrow()
            .state
            .page_resources
            .atf_state
            .try_get_untracked()
        {
            details_ctx
                .borrow()
                .metric_reporter
                .report_atf_page_load_abandoned(
                    details_ctx
                        .borrow()
                        .state
                        .is_live_details_page
                        .get_untracked(),
                );
        }
        if let Some(ResourceState::Loading) = details_ctx
            .borrow()
            .state
            .page_resources
            .btf_state
            .try_get_untracked()
        {
            details_ctx
                .borrow()
                .metric_reporter
                .report_btf_page_load_abandoned(
                    details_ctx
                        .borrow()
                        .state
                        .is_live_details_page
                        .get_untracked(),
                );
        }
    });

    let is_atf_redesign_enabled =
        try_use_rust_features(ctx.scope()).is_some_and(|f| f.is_rust_atf_redesign_enabled());

    compose! {
        Stack() {
            Stack() {
                Column() {
                    if is_atf_redesign_enabled {
                        DetailsContentV2(
                            details_context: details_content_ctx.clone()
                        )
                        .opacity(data.signals.details_opacity)
                        .focus_enabled(data.signals.no_active_modal)
                        .focus_hierarchical_container(NavigationStrategy::Vertical)
                     } else {
                        DetailsContent(
                            details_context: tab_controller_details_ctx.clone()
                        )
                        .opacity(data.signals.details_opacity)
                        .focus_enabled(data.signals.no_active_modal)
                        .cross_axis_alignment(CrossAxisAlignment::Start)
                        .main_axis_alignment(MainAxisAlignment::Start)
                        .focus_hierarchical_container(NavigationStrategy::Vertical)
                    }
                }.focus_hierarchical_container(NavigationStrategy::Vertical)

                DetailsModalManager(details_context: modal_details_ctx)
                Column() {
                    Toast(message: toast_details_ctx.borrow().state.toast_state.message, visible: toast_details_ctx.borrow().state.toast_state.visible)
                }
                .opacity(data.signals.details_opacity)
                .main_axis_size(MainAxisSize::Max)
                .main_axis_alignment(MainAxisAlignment::End)

                // Ghost focus element to prevent utility nav grabbing focus when loading
                Rectangle()
                    .opacity(0.0)
                    .focusable()
                    .focus_enabled(data.signals.loading)
                    .accessibility_highlight_disabled(true)
                    .accessibility_highlight_custom(CustomAccessibilityBorderStyle {
                        width: 0.0,
                        color: Default::default(),
                        padding: 0.0
                    })
            }

            ErrorOverlay()

            ContextualMenu(
                contextual_menu_data: contextual_menu_state.contextual_menu_data,
                show_contextual_menu_hint: contextual_menu_state.show_contextual_menu_hint,
                contextual_menu_events: contextual_menu_state.contextual_menu_events.write_only(),
                notify_navigation: None
            ).test_id(DETAILS_CONTEXTUAL_MENU_TEST_ID)
        }
        .test_id(DETAILS_ROOT_TEST_ID)
        .size(Vec2::new(SCREEN_WIDTH, SCREEN_HEIGHT))
        .focus_pointer_control(use_focus_pointer_control(ctx.scope()))
        .on_key_down(KeyCode::Backspace, {
            let back_handler = data.back_handler.clone();
            move || back_handler()
        })
        .on_key_down(KeyCode::Escape, move || (data.back_handler)())
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::create_or_use_details_context_test;
    use crate::metrics::metric_reporter::traits::MockDetailsReporter;
    use crate::{
        context::test_utils::setup_mock_reporter, test_utils::details::construct_details_page,
    };
    use firetv::MockFireTV;
    use ignx_compositron::test_utils::assert_node_exists;
    use rstest::rstest;
    use rust_features::MockRustFeaturesBuilder;

    fn provide_firetv_context(scope: Scope) {
        let mut firetv = MockFireTV::default();
        firetv.expect_is_firetv().return_const(false);
        firetv.provide_mock(scope);
    }

    #[test]
    fn should_render_details_page() {
        ignx_compositron::app::launch_test(
            |ctx| {
                provide_firetv_context(ctx.scope());
                construct_details_page(&ctx)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let root_details_page = node_tree.find_by_test_id(DETAILS_ROOT_TEST_ID);
                assert_node_exists!(root_details_page);
            },
        )
    }

    #[test]
    fn should_render_contextual_menu() {
        ignx_compositron::app::launch_test(
            |ctx| {
                provide_firetv_context(ctx.scope());
                construct_details_page(&ctx)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                assert_node_exists!(node_tree.find_by_test_id(DETAILS_CONTEXTUAL_MENU_TEST_ID));
            },
        )
    }

    #[test]
    fn should_emit_abandoned_metrics_on_dispose() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let mock_context: DetailsPageContext = create_or_use_details_context_test(&ctx);
                let mut metric_reporter = MockDetailsReporter::default();

                metric_reporter
                    .expect_report_atf_page_load_abandoned()
                    .times(1)
                    .returning(|_| {});
                metric_reporter
                    .expect_report_btf_page_load_abandoned()
                    .times(1)
                    .returning(|_| {});
                setup_mock_reporter(&mut metric_reporter);
                mock_context.borrow_mut().metric_reporter = Rc::new(metric_reporter);

                mock_context
                    .borrow_mut()
                    .state
                    .page_resources
                    .atf_state
                    .set(ResourceState::Loading);
                mock_context
                    .borrow_mut()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Loading);

                provide_firetv_context(ctx.scope());
                construct_details_page(&ctx)
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                scope.dispose();
            },
        )
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn should_render_correct_details_content_depending_on_atf_redesign_feature(
        #[case] atf_redesign_enabled: bool,
    ) {
        ignx_compositron::app::launch_test(
            move |ctx| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_atf_redesign_enabled(atf_redesign_enabled)
                    .build_into_context(ctx.scope());

                provide_firetv_context(ctx.scope());
                construct_details_page(&ctx)
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let expected_test_id = if atf_redesign_enabled {
                    DETAILS_CONTENT_V2_TEST_ID
                } else {
                    DETAILS_CONTENT_TEST_ID
                };
                let details_content = node_tree.find_by_test_id(expected_test_id);
                assert_node_exists!(details_content);
            },
        )
    }
}
