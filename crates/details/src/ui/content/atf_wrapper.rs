use crate::context::hooks::{empty_column_fallback, use_details_page_context};
use crate::context::state::base_creator::DetailsFocusedSection;
use crate::context::DetailsPageContext;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::ui::content::atf_carousel::*;
use crate::ui::content::title_actions::*;
use crate::ui::details_skeleton::*;
use details_derive::get_some_or_return_with_val_and_error_message;
use fableous::animations::{FableMotionDuration, MotionDuration};
use ignx_compositron::app::wasm_app::EventData;
use ignx_compositron::input::KeyCode;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use std::rc::Rc;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("atf_wrapper");

fn on_focus_move(details_ctx: &DetailsPageContext) {
    let nav_control = details_ctx.borrow().dependent_data.nav_control.clone();
    nav_control.show_utility_nav.set(true);

    let atf_opacity = details_ctx.borrow().state.title_details.atf_opacity;
    let global_opacity = details_ctx.borrow().state.title_details.global_opacity;
    let btf_scroll_to = details_ctx.borrow().state.btf_scroll_to;
    let btf_offset = details_ctx.borrow().state.btf_offset;
    let content_scroll_to = details_ctx.borrow().state.content_scroll_to;
    let focused_section = details_ctx.borrow().state.focused_section;

    atf_opacity.set(1.0);
    details_ctx.borrow().app_context.with_animation(
        Animation::default().with_duration(FableMotionDuration::Standard.to_duration()),
        move || {
            btf_offset.set(0.0);
        },
    );
    focused_section.set(DetailsFocusedSection::Atf);
    btf_scroll_to.set(ScrollTo::Offset(0.0, Pivot::Start, Animation::default()));
    content_scroll_to.set(ScrollTo::Offset(0.0, Pivot::Start, Animation::default()));
    global_opacity.set(0.0)
}

#[derive(Clone, Hash)]
enum AtfFocusedSection {
    TitleActions,
    AtfCarousel,
}

const TITLE_ACTIONS_TEST_ID: &str = "title-actions";
const ATF_CAROUSEL_TEST_ID: &str = "atf-carousel";

#[Composer]
pub fn AtfWrapper(ctx: &AppContext) -> ColumnComposable {
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            DETAILS_LOGGER.error("failed to obtain details content");
            return empty_column_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    // FIXME: Move to a focused section binding
    let title_actions_opacity = create_memo(ctx.scope(), {
        let opacity_atf = details_ctx.borrow().state.transition_state.atf_opacity;
        let focused_section = details_ctx.borrow().state.focused_section;
        move |_| {
            let overall_opacity = get_some_or_return_with_val_and_error_message!(
                opacity_atf.try_get(),
                "Failed to get opacity",
                0.0
            );
            let focused_section = get_some_or_return_with_val_and_error_message!(
                focused_section.try_get(),
                "Failed to get focused section",
                0.0
            );

            if overall_opacity == 1.0 {
                match focused_section {
                    DetailsFocusedSection::Atf => 1.0,
                    DetailsFocusedSection::Btf | DetailsFocusedSection::TabButtons => 0.0,
                }
            } else {
                overall_opacity
            }
        }
    });

    let is_loading = details_ctx.borrow().state.page_resources.atf_is_loading;
    let skeleton_details_ctx = details_ctx.clone();

    let focus_handler = Rc::new(move || on_focus_move(&details_ctx));

    let focused_atf_section = create_focus_value_signal(ctx.scope());
    let on_atf_carousel_back_press = Rc::new(move |event: &mut EventData| {
        focused_atf_section.set(AtfFocusedSection::TitleActions);
        event.stop_propagation();
    });

    compose! {
        Column() {
            if is_loading.try_get().unwrap_or(false) {
                DetailsPageAtfSkeleton(details_ctx: skeleton_details_ctx.clone())
            } else {
                Column() {
                    TitleActions()
                        .test_id(TITLE_ACTIONS_TEST_ID)
                        .focused_value(focused_atf_section, AtfFocusedSection::TitleActions)
                        .opacity(title_actions_opacity)
                        .on_focus_move({
                            let focus_handler = focus_handler.clone();
                            move |_| focus_handler()
                        })

                    AtfCarousel()
                        .test_id(ATF_CAROUSEL_TEST_ID)
                        .focused_value(focused_atf_section, AtfFocusedSection::AtfCarousel)
                        .on_key_down_event(KeyCode::Backspace, {
                            let on_atf_carousel_back_press = on_atf_carousel_back_press.clone();
                            move |event| on_atf_carousel_back_press(event)
                        })
                        .on_key_down_event(KeyCode::Escape, {
                            let on_atf_carousel_back_press = on_atf_carousel_back_press.clone();
                            move |event| on_atf_carousel_back_press(event)
                        })
                }
                .focus_hierarchical_container(NavigationStrategy::Vertical)
            }
        }
    }
}

#[cfg(test)]
pub mod test {
    use std::cell::RefCell;

    use super::*;
    use crate::{
        context::test_utils::create_or_use_details_context_test,
        ui::content::atf_carousel::test::{get_mock_standard_carousel, set_atf_carousel},
    };
    use container_orchestrator::test_assets::mocks::MockNetworkClient;
    use firetv::MockFireTV;
    use ignx_compositron::{
        app::launch_test, reactive::store_value, test_utils::assert_node_exists,
    };
    use rstest::*;
    use rust_features::MockRustFeaturesBuilder;

    fn build(ctx: &AppContext) -> ColumnComposable {
        MockRustFeaturesBuilder::new().build_as_mock_and_real_into_context(false, ctx.scope());
        provide_firetv_context(ctx);

        let mock_context = MockNetworkClient::new_context();
        mock_context
            .expect()
            .returning(|_| MockNetworkClient::default());

        store_value(ctx.scope(), mock_context);

        compose! {
            AtfWrapper()
        }
    }

    fn provide_firetv_context(ctx: &AppContext) {
        let mut firetv = MockFireTV::default();
        // is_firetv to return false
        firetv.expect_is_firetv().return_const(false);
        firetv.provide_mock(ctx.scope());
    }

    #[rstest]
    #[case(KeyCode::Escape)]
    #[case(KeyCode::Backspace)]
    pub fn title_actions_back_press(#[case] back_key: KeyCode) {
        let on_back_called_times = Rc::new(RefCell::new(0));
        let on_back_called_times_dupe = on_back_called_times.clone();
        let back_key_clone = back_key.clone();

        launch_test(
            move |ctx| {
                create_or_use_details_context_test(&ctx);
                set_atf_carousel(ctx.scope(), Some(get_mock_standard_carousel()));
                build(&ctx).on_key_down(back_key_clone, move || {
                    *on_back_called_times.borrow_mut() += 1
                })
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let title_actions = tree.find_by_test_id(TITLE_ACTIONS_TEST_ID);

                assert_node_exists!(&title_actions);
                assert!(title_actions.borrow_props().is_focused);
                assert_eq!(*on_back_called_times_dupe.borrow(), 0);

                test_game_loop.send_key_down_up_event(back_key);
                let tree = test_game_loop.tick_until_done();
                let title_actions = tree.find_by_test_id(TITLE_ACTIONS_TEST_ID);

                assert!(title_actions.borrow_props().is_focused);
                assert_eq!(*on_back_called_times_dupe.borrow(), 1);
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Escape)]
    #[case(KeyCode::Backspace)]
    pub fn atf_carousel_back_press(#[case] back_key: KeyCode) {
        let on_back_called_times = Rc::new(RefCell::new(0));
        let on_back_called_times_dupe = on_back_called_times.clone();
        let back_key_clone = back_key.clone();

        launch_test(
            move |ctx| {
                create_or_use_details_context_test(&ctx);
                set_atf_carousel(ctx.scope(), Some(get_mock_standard_carousel()));
                build(&ctx).on_key_down(back_key_clone, move || {
                    *on_back_called_times.borrow_mut() += 1
                })
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let title_actions = tree.find_by_test_id(TITLE_ACTIONS_TEST_ID);

                assert_node_exists!(&title_actions);
                assert!(title_actions.borrow_props().is_focused);
                assert_eq!(*on_back_called_times_dupe.borrow(), 0);

                test_game_loop.send_key_down_up_event(KeyCode::Down);

                let tree = test_game_loop.tick_until_done();
                let carousel = tree.find_by_test_id("basic-carousel");

                assert_node_exists!(&carousel);
                assert!(carousel.borrow_props().is_focused);

                test_game_loop.send_key_down_up_event(back_key.clone());
                let tree = test_game_loop.tick_until_done();
                let title_actions = tree.find_by_test_id(TITLE_ACTIONS_TEST_ID);

                assert!(title_actions.borrow_props().is_focused);
                assert_eq!(*on_back_called_times_dupe.borrow(), 0);

                test_game_loop.send_key_down_up_event(back_key);
                let tree = test_game_loop.tick_until_done();
                let title_actions = tree.find_by_test_id(TITLE_ACTIONS_TEST_ID);

                assert!(title_actions.borrow_props().is_focused);
                assert_eq!(*on_back_called_times_dupe.borrow(), 1);
            },
        )
    }
}
