use crate::context::state::base_creator::DetailsFocusedSection;
use crate::context::DetailsPageContext;
use crate::modules::localized_text::DetailsString;
use amzn_fable_tokens::FableColor;
use fableous::animations::{FableMotionDuration, MotionDuration};
use fableous::buttons::primary_button::PrimaryButtonVariant;
use ignx_compositron::focus::FocusDirection;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
//use std::rc::Rc;
use crate::modules::background_image::restore_atf_media_background_if_required;
use fableous::buttons::primary_button::*;
use fableous::utils::get_ignx_color;
use fableous::SCREEN_WIDTH;
use ignx_compositron::reactive::MaybeSignal;

fn on_focus_move(details_ctx: &DetailsPageContext, focus_direction: Option<FocusDirection>) {
    let nav_control = details_ctx.borrow().dependent_data.nav_control.clone();
    nav_control.show_utility_nav.set(true);

    let global_opacity = details_ctx.borrow().state.title_details.global_opacity;
    let btf_offset = details_ctx.borrow().state.btf_offset;
    let focused_section = details_ctx.borrow().state.focused_section;

    details_ctx.borrow().app_context.with_animation(
        Animation::default().with_duration(FableMotionDuration::Standard.to_duration()),
        move || {
            btf_offset.set(0.0);
        },
    );
    focused_section.set(DetailsFocusedSection::Atf);
    global_opacity.set(0.0);

    let focus_direction_is_up = focus_direction == Some(FocusDirection::Up);
    let focus_direction_is_empty = focus_direction.is_none();

    if focus_direction_is_up || focus_direction_is_empty {
        restore_atf_media_background_if_required(details_ctx);
    }
}

const BUY_BOX_MAX_WIDTH: f32 = 430.0;
const BUY_BOX_MIN_WIDTH: f32 = 350.0;
pub const ATF_HEIGHT: f32 = 980.0;
const ATF_ROW_SPACING: f32 = 60.0;
pub const VERTICAL_TITLE_ACTIONS_TEST_ID: &str = "vertical-title-actions-test-id";
pub const ATF_TITLE_DETAILS_TEST_ID: &str = "atf-title-details-test-id";
pub const ATF_WRAPPER_V2_TEST_ID: &str = "atf-wrapper-v2-test-id";

#[Composer]
pub fn AtfWrapperV2(ctx: &AppContext, details_ctx: DetailsPageContext) -> ColumnComposable {
    let title_actions_state = details_ctx.borrow().state.title_actions_state.clone();

    compose! {
        Column() {
            // Todo: Render Panorama specific skeleton when loading
            Row() {
                // Todo: Replace place holder primary button with Vertical Buy Box component
                PrimaryButton(
                    variant: MaybeSignal::Static(PrimaryButtonVariant::TextSize800(
                        DetailsString::AV_LRC_START_OVER_TEXT.get_localised_text(),
                    )),
                    disabled: false
                )
                .max_width(BUY_BOX_MAX_WIDTH)
                .min_width(BUY_BOX_MIN_WIDTH)
                .on_focus_move({
                    move |direction| {
                        on_focus_move(&details_ctx, direction);
                    }
                })
                .test_id(VERTICAL_TITLE_ACTIONS_TEST_ID)
                // Todo: Replace place holder title details component with new ATF layout
                Rectangle()
                .width(830.0)
                .height(200.0)
                .background_color(get_ignx_color(FableColor::DEBUG200))
                .test_id(ATF_TITLE_DETAILS_TEST_ID)
            }
            .height(ATF_HEIGHT)
            .width(SCREEN_WIDTH)
            .cross_axis_alignment(CrossAxisAlignment::End)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(ATF_ROW_SPACING))
            .focused(title_actions_state.focus_signal)
            .focus_hierarchical_container(NavigationStrategy::Vertical)
            .test_id(ATF_WRAPPER_V2_TEST_ID)
        }
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, use_details_page_context_test,
    };
    use container_orchestrator::test_assets::mocks::MockNetworkClient;
    use firetv::MockFireTV;
    use ignx_compositron::{
        app::launch_test, reactive::store_value, test_utils::assert_node_exists,
    };
    use media_background::types::{MediaBackgroundType, MediaStrategy, StandardBackgroundData};
    use rstest::rstest;
    use rust_features::MockRustFeaturesBuilder;
    use test_utils::assert_test_id_is_focused;

    fn build(
        ctx: &AppContext,
        details_ctx: DetailsPageContext,
        with_dummy_focusable: bool,
    ) -> ColumnComposable {
        MockRustFeaturesBuilder::new().build_as_mock_and_real_into_context(false, ctx.scope());
        provide_firetv_context(ctx);
        let mock_context = MockNetworkClient::new_context();
        mock_context
            .expect()
            .returning(|_| MockNetworkClient::default());

        store_value(ctx.scope(), mock_context);

        compose! {
            Column() {
                AtfWrapperV2(details_ctx)
                if with_dummy_focusable {
                    Rectangle().focusable().test_id("dummy-focusable")
                }
            }
        }
    }

    fn provide_firetv_context(ctx: &AppContext) {
        let mut firetv = MockFireTV::default();
        // is_firetv to return false
        firetv.expect_is_firetv().return_const(false);
        firetv.provide_mock(ctx.scope());
    }

    #[test]
    pub fn should_render_title_actions_and_title_details() {
        launch_test(
            move |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                build(&ctx, details_context, false)
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let title_actions = tree.find_by_test_id(VERTICAL_TITLE_ACTIONS_TEST_ID);
                assert_node_exists!(&title_actions);

                let atf_title_details = tree.find_by_test_id(ATF_TITLE_DETAILS_TEST_ID);
                assert_node_exists!(&atf_title_details);
            },
        )
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    pub fn should_handle_signals_correctly_when_recieving_focus(#[case] is_up_direction: bool) {
        launch_test(
            move |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                build(&ctx, details_context, true)
            },
            move |scope, mut test_game_loop| {
                let details_context = use_details_page_context_test(scope);
                let tree = test_game_loop.tick_until_done();

                // Test set up
                let dummy_focusable_id =
                    tree.find_by_test_id("dummy-focusable").get_props().node_id;
                test_game_loop.send_on_focus_event(dummy_focusable_id);
                assert_test_id_is_focused(&mut test_game_loop, "dummy-focusable");

                details_context
                    .borrow()
                    .dependent_data
                    .nav_control
                    .show_utility_nav
                    .set(false);
                details_context
                    .borrow()
                    .state
                    .focused_section
                    .set(DetailsFocusedSection::Btf);
                details_context
                    .borrow()
                    .state
                    .title_details
                    .global_opacity
                    .set(1.0);
                details_context
                    .borrow()
                    .state
                    .media_background
                    .set(MediaBackgroundType::None);

                let title_actions_id = tree
                    .find_by_test_id(VERTICAL_TITLE_ACTIONS_TEST_ID)
                    .get_props()
                    .node_id;

                if is_up_direction {
                    test_game_loop
                        .send_on_focus_event_with_direction(title_actions_id, FocusDirection::Up);
                } else {
                    test_game_loop.send_on_focus_event(title_actions_id);
                }

                test_game_loop.tick_until_done();

                // Assert all signals updated correctly after the focus event if occurs
                let show_utility_nav = details_context
                    .borrow()
                    .dependent_data
                    .nav_control
                    .show_utility_nav
                    .get_untracked();
                let focused_section = details_context
                    .borrow()
                    .state
                    .focused_section
                    .get_untracked();
                let global_opacity = details_context
                    .borrow()
                    .state
                    .title_details
                    .global_opacity
                    .get_untracked();
                let media_background = details_context
                    .borrow()
                    .state
                    .media_background
                    .get_untracked();

                assert_eq!(show_utility_nav, true);
                assert_eq!(focused_section, DetailsFocusedSection::Atf);
                assert_eq!(global_opacity, 0.0);
                assert_eq!(media_background, MediaBackgroundType::Standard(StandardBackgroundData {
                        id: "fakeGti".to_string(),
                        image_url: Some("https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/079983f10fc6866c8675e4e8e0538a9e045eb5ffb057f978704154ea98ac32af._RI_TTW_.jpg".to_string()),
                        csm_data: None,
                        video_id: Some("fakeGti".to_string()),
                        enter_immediately: false,
                        placement: "PVBrowse".to_string(),
                        media_strategy: MediaStrategy::Promo,
                        interaction_source_override: None
                    }))
            },
        )
    }
}
