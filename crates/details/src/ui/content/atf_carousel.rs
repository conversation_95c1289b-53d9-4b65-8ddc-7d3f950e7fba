use container_orchestrator::config::container_options::{
    BackPressStrategy, CSMStrategy, CommonContainerOptions, ContainerVerticalPivot, TTIStrategy,
    TitleDisplayOption,
};
use container_orchestrator::config::orchestrator_options::ContainerOrchestratorLayout;
use container_orchestrator::core::containers::standard_carousel::{
    StandardCarouselOptions, StandardCarouselSpecificOptions,
};
use container_orchestrator::core::containers::transformations::containers_to_orchestrator;
use container_orchestrator::core::containers::ContainerData;
use container_orchestrator::core::orchestrator::container_orchestrator::*;
use ignx_compositron::column::*;
use ignx_compositron::{compose, prelude::*, Composer};
use location::{PageType, RustPage};

use crate::context::hooks::{empty_column_fallback, use_details_page_context};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("atf_carousel");

#[Composer]
pub fn AtfCarousel(ctx: &AppContext) -> ColumnComposable {
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            DETAILS_LOGGER.error("failed to obtain details content");
            return empty_column_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let atf_response = details_ctx.borrow().state.page_resources.atf_response;

    let data: Signal<Vec<ContainerData>> = Signal::derive(ctx.scope(), {
        let ctx = ctx.clone();
        move || {
            atf_response
                .with(|atf_response| {
                    atf_response.as_ref().and_then(|atf_response| {
                        atf_response.atf_carousel.as_ref().map(|atf_carousel| {
                            containers_to_orchestrator(&ctx, &[atf_carousel.clone()], None, "")
                        })
                    })
                })
                .unwrap_or_else(|| vec![])
        }
    });

    // We use this so that the carousel only rerenders to mount or unmount. The number of items changing length or the content changing won't rerender.
    let should_render = create_memo(ctx.scope(), move |_| data.with(|data| !data.is_empty()));

    let orchestrator_builder = move |ctx: &AppContext| {
        let orchestrator = ContainerOrchestrator::new()
            .sizing(ContainerOrchestratorLayout::Full)
            .tti(TTIStrategy::Disabled)
            .csm(CSMStrategy::Disabled)
            .standard_carousel(Some(standard_carousel_configuration()));

        orchestrator
            .into_ui(
                ctx,
                data,
                PageType::Rust(RustPage::RUST_DETAILS),
                "DETAILS_ATF".into(),
            )
            .height(216.0 + 32.0)
            .padding(Padding::new(0.0, 0.0, 32.0, 0.0))
    };

    let item_builder = Box::new(move |ctx: &AppContext| {
        if should_render.get() {
            Some(orchestrator_builder(ctx))
        } else {
            None
        }
    });

    compose! {
        Column() {
            Memo(item_builder)
        }
    }
}

fn standard_carousel_configuration() -> StandardCarouselOptions {
    StandardCarouselOptions {
        common: CommonContainerOptions {
            display_title: TitleDisplayOption::Hidden,
            container_vertical_pivot: ContainerVerticalPivot::Top,
            back_press_strategy: BackPressStrategy::ResetAndInherit,
            ..CommonContainerOptions::default()
        },
        specific: StandardCarouselSpecificOptions::default(),
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, use_details_page_context_test,
    };
    use common_transform_types::containers::ContainerWithResiliency;
    use container_orchestrator::test_assets::mocks::MockNetworkClient;
    use firetv::MockFireTV;
    use ignx_compositron::{
        app::launch_test,
        prelude::Scope,
        reactive::store_value,
        test_utils::{assert_node_does_not_exist, assert_node_exists},
    };
    use network_parser::core::network_parse_from_str;
    use rust_features::MockRustFeaturesBuilder;

    const RESPONSE: &str = include_str!("./mocks/standard_carousel.json");

    pub fn get_mock_standard_carousel() -> ContainerWithResiliency {
        network_parse_from_str(RESPONSE).unwrap()
    }

    fn build(ctx: &AppContext) -> ColumnComposable {
        MockRustFeaturesBuilder::new().build_as_mock_and_real_into_context(false, ctx.scope());
        provide_firetv_context(ctx);

        let mock_context = MockNetworkClient::new_context();
        mock_context
            .expect()
            .returning(|_| MockNetworkClient::default());

        store_value(ctx.scope(), mock_context);

        compose! {
            AtfCarousel()
        }
    }

    pub fn set_atf_carousel(scope: Scope, atf_carousel: Option<ContainerWithResiliency>) {
        let details_context = use_details_page_context_test(scope);
        details_context
            .borrow()
            .state
            .page_resources
            .atf_response
            .update(move |atf_response| {
                if let Some(atf_response) = atf_response.as_mut() {
                    atf_response.atf_carousel = atf_carousel;
                }
            });
    }

    fn provide_firetv_context(ctx: &AppContext) {
        let mut firetv = MockFireTV::default();
        // is_firetv to return false
        firetv.expect_is_firetv().return_const(false);
        firetv.provide_mock(ctx.scope());
    }

    #[test]
    pub fn render_with_data() {
        launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);
                set_atf_carousel(ctx.scope(), Some(network_parse_from_str(RESPONSE).unwrap()));
                build(&ctx)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let container_orchestrator = node_tree.find_by_test_id("container-orchestrator");

                assert_node_exists!(container_orchestrator);
            },
        )
    }

    #[test]
    pub fn render_with_no_data() {
        launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);
                set_atf_carousel(ctx.scope(), None);
                build(&ctx)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let container_orchestrator = node_tree.find_by_test_id("container-orchestrator");

                assert_node_does_not_exist!(container_orchestrator);
            },
        )
    }

    #[test]
    pub fn mounts_and_unmounts_as_data_changes() {
        launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);
                set_atf_carousel(ctx.scope(), None);
                build(&ctx)
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let container_orchestrator = node_tree.find_by_test_id("container-orchestrator");

                assert_node_does_not_exist!(container_orchestrator);

                set_atf_carousel(scope, Some(network_parse_from_str(RESPONSE).unwrap()));

                let node_tree = test_game_loop.tick_until_done();
                let container_orchestrator = node_tree.find_by_test_id("container-orchestrator");

                assert_node_exists!(container_orchestrator);

                set_atf_carousel(scope, None);

                let node_tree = test_game_loop.tick_until_done();
                let container_orchestrator = node_tree.find_by_test_id("container-orchestrator");

                assert_node_does_not_exist!(container_orchestrator);
            },
        )
    }
}
