use crate::context::state::base_creator::DetailsFocusedSection;
use crate::context::DetailsPageContext;
use crate::modules::tab_calculator::{setup_tab, DetailsTabType};
use crate::ui::container_list::types::DetailsPageContainerType;
use crate::ui::content::btf_wrapper::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use media_background::types::MediaBackgroundType;
use std::rc::Rc;
use std::time::Duration;
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;

pub const TAB_PAGE_CONTROLLER_TEST_ID: &str = "tab-page-controller-test-id";

#[derive(PartialEq)]
enum TransitionDirection {
    Left,
    Right,
}

fn perform_tab_transition(
    ctx: &AppContext,
    details_ctx: &DetailsPageContext,
    direction: TransitionDirection,
    transition: TabTransitionSignals,
) {
    let (exit_pos, enter_pos) = if direction == TransitionDirection::Left {
        (
            TITLE_DETAILS_LEFT_OFFSET - 50.0,
            TITLE_DETAILS_LEFT_OFFSET + 50.0,
        )
    } else {
        (
            TITLE_DETAILS_LEFT_OFFSET + 50.0,
            TITLE_DETAILS_LEFT_OFFSET - 50.0,
        )
    };

    // (2) Fade in content and title details after updating tab content
    let update_containers = Rc::new({
        let ctx = ctx.clone();
        let details_ctx = details_ctx.clone();
        move || {
            transition.content_opacity.try_set(0.0);
            transition.title_details_opacity.try_set(0.0);
            transition.content_offset_x.try_set(exit_pos);
            transition
                .title_details_offset_x
                .try_set(exit_pos - TITLE_DETAILS_LEFT_OFFSET);

            if let Some(new_containers) = transition.current_page_containers.try_get_untracked() {
                setup_tab(
                    &ctx,
                    transition.selected_tab.get_untracked(),
                    &new_containers,
                    &details_ctx,
                );
                transition.shown_containers.try_set(new_containers);
            };

            transition.active.try_set(false);
            ctx.with_animation(
                Animation::default()
                    .with_interpolation(Interpolation::EaseOutExpo)
                    .with_duration(Duration::from_millis(200)),
                move || {
                    transition
                        .content_offset_x
                        .try_set(TITLE_DETAILS_LEFT_OFFSET);
                    transition.title_details_offset_x.try_set(0.0);
                    transition.title_details_opacity.try_set(1.0);
                    transition.content_opacity.try_set(1.0);
                },
            );
        }
    });

    // As soon as the tab changes remove the media background
    transition
        .media_background
        .try_set(MediaBackgroundType::None);

    // (1) Slide out content
    ctx.with_animation_completion(
        Animation::default().with_duration(Duration::from_millis(200)),
        move || {
            transition.title_details_opacity.try_set(0.0);
            transition.content_opacity.try_set(0.0);
            transition.content_offset_x.try_set(enter_pos);
            transition
                .title_details_offset_x
                .try_set(enter_pos - TITLE_DETAILS_LEFT_OFFSET);
        },
        move || update_containers(),
    );
}

#[derive(Copy, Clone)]
struct TabTransitionSignals {
    pub active: RwSignal<bool>,

    pub selected_tab_index: RwSignal<Option<usize>>,

    pub media_background: RwSignal<MediaBackgroundType>,
    pub selected_tab: RwSignal<DetailsTabType>,

    pub current_btf_offset: RwSignal<f32>,

    pub current_page_containers: RwSignal<Vec<DetailsPageContainerType>>,
    pub shown_containers: RwSignal<Vec<DetailsPageContainerType>>,

    pub title_details_offset_x: RwSignal<f32>,
    pub title_details_opacity: RwSignal<f32>,

    pub content_opacity: RwSignal<f32>,
    pub content_offset_x: RwSignal<f32>,
}

impl TabTransitionSignals {
    pub fn new(
        scope: Scope,
        containers: RwSignal<Vec<DetailsPageContainerType>>,
        details_ctx: &DetailsPageContext,
    ) -> Self {
        let btf_offset = details_ctx.borrow().state.btf_offset;
        let global_title_details_offset_x =
            details_ctx.borrow().state.title_details.global_x_offset;
        let global_title_details_opacity = details_ctx.borrow().state.title_details.global_opacity;
        let selected_tab = details_ctx.borrow().state.tab_state.selected_tab;
        let media_background = details_ctx.borrow().state.media_background;
        let selected_tab_index = details_ctx.borrow().state.tab_state.selected_tab_index;

        Self {
            active: create_rw_signal(scope, false),
            selected_tab_index,
            selected_tab,
            media_background,
            current_btf_offset: btf_offset,
            current_page_containers: containers,
            shown_containers: create_rw_signal(scope, containers.get_untracked()),
            title_details_offset_x: global_title_details_offset_x,
            title_details_opacity: global_title_details_opacity,
            content_opacity: create_rw_signal(scope, 1.0f32),
            content_offset_x: create_rw_signal(scope, TITLE_DETAILS_LEFT_OFFSET),
        }
    }
}

#[Composer]
pub fn TabPageController(
    ctx: &AppContext,
    details_context: &DetailsPageContext,
    containers: RwSignal<Vec<DetailsPageContainerType>>,
) -> StackComposable {
    let transition = TabTransitionSignals::new(ctx.scope(), containers, details_context);

    // While focus is on the ATF allow the tab content to update without animations
    create_effect(ctx.scope(), {
        let details_ctx = details_context.clone();
        move |_| {
            let current_containers = transition.current_page_containers.get();
            if details_ctx
                .borrow()
                .state
                .focused_section
                .try_get_untracked()
                .is_some_and(|val| val == DetailsFocusedSection::Atf)
            {
                transition.shown_containers.try_set(current_containers);
            }
        }
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let details_ctx = details_context.clone();
        move |prev_index| {
            let index = transition.selected_tab_index.get()?;

            let prev_index = prev_index.flatten().unwrap_or(0);

            // While a transition is active don't attempt to perform another one
            if transition.active.try_get_untracked().is_some_and(|val| val) {
                return Some(index);
            }

            // If both prev_index and current_index available first figure out what direction we are
            // transitioning from
            let transition_dir = if index < prev_index {
                TransitionDirection::Left
            } else {
                TransitionDirection::Right
            };

            transition.active.set(true);
            perform_tab_transition(
                &ctx.clone(),
                &details_ctx.clone(),
                transition_dir,
                transition,
            );

            Some(index)
        }
    });

    compose! {
        Stack() {
            BtfWrapper(containers: transition.shown_containers)
                .opacity(transition.content_opacity)
                .translate_x(transition.content_offset_x)
                .translate_y(transition.current_btf_offset)
        }.test_id(TAB_PAGE_CONTROLLER_TEST_ID)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_no_bindings_test, use_details_page_context_test,
    };
    use crate::context::ResourceState;
    use crate::ui::container_list::types::DetailsPageContainer;
    use crate::ui::test_ids::DETAILS_SEASON_AND_EPISODES_CARD_CAROUSEL_TEST_ID;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use rstest::rstest;
    #[test]
    fn should_update_containers_while_on_atf() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_no_bindings_test(&ctx);
                let containers = create_rw_signal(ctx.scope(), vec![]);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .atf_state
                    .set(ResourceState::Loaded);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Loaded);
                details_context
                    .borrow()
                    .state
                    .season_episodes_state
                    .loading_state
                    .set(ResourceState::Loaded);

                provide_context(ctx.scope(), containers);

                compose! {
                    TabPageController(details_context: &details_context, containers)
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let containers_sig = use_context::<RwSignal<Vec<DetailsPageContainerType>>>(scope)
                    .expect("To exist");
                containers_sig.set(vec![DetailsPageContainerType {
                    container: DetailsPageContainer::SeasonEpisodes,
                }]);
                let update = test_game_loop.tick_once();

                // Should find season episodes container
                let season_episodes_carousel = update
                    .node_tree
                    .find_by_test_id(DETAILS_SEASON_AND_EPISODES_CARD_CAROUSEL_TEST_ID);
                assert_node_exists!(season_episodes_carousel);
            },
        )
    }

    #[rstest]
    #[case(DetailsFocusedSection::Btf)]
    #[case(DetailsFocusedSection::TabButtons)]
    fn should_not_update_containers_if_on_tab_or_btf(#[case] section: DetailsFocusedSection) {
        ignx_compositron::app::launch_test(
            move |ctx| {
                let details_context = create_or_use_details_context_no_bindings_test(&ctx);
                let containers = create_rw_signal(ctx.scope(), vec![]);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .atf_state
                    .set(ResourceState::Loaded);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Loaded);
                details_context
                    .borrow()
                    .state
                    .season_episodes_state
                    .loading_state
                    .set(ResourceState::Loaded);
                details_context.borrow().state.focused_section.set(section);

                provide_context(ctx.scope(), containers);

                compose! {
                    TabPageController(details_context: &details_context, containers)
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let containers_sig = use_context::<RwSignal<Vec<DetailsPageContainerType>>>(scope)
                    .expect("To exist");
                containers_sig.set(vec![DetailsPageContainerType {
                    container: DetailsPageContainer::SeasonEpisodes,
                }]);
                let update = test_game_loop.tick_once();

                // Should NOT find season episodes container, an animation should be required to run before the container appears
                let season_episodes_carousel = update
                    .node_tree
                    .find_by_test_id(DETAILS_SEASON_AND_EPISODES_CARD_CAROUSEL_TEST_ID);
                assert_node_does_not_exist!(season_episodes_carousel);
            },
        )
    }

    #[test]
    fn should_run_animation_on_index_change() {
        ignx_compositron::app::launch_test(
            move |ctx| {
                let details_context = create_or_use_details_context_no_bindings_test(&ctx);
                let containers = create_rw_signal(ctx.scope(), vec![]);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .atf_state
                    .set(ResourceState::Loaded);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Loaded);
                details_context
                    .borrow()
                    .state
                    .season_episodes_state
                    .loading_state
                    .set(ResourceState::Loaded);
                details_context
                    .borrow()
                    .state
                    .focused_section
                    .set(DetailsFocusedSection::Btf);

                provide_context(ctx.scope(), containers);

                compose! {
                    TabPageController(details_context: &details_context, containers)
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let containers_sig = use_context::<RwSignal<Vec<DetailsPageContainerType>>>(scope)
                    .expect("To exist");
                containers_sig.set(vec![DetailsPageContainerType {
                    container: DetailsPageContainer::SeasonEpisodes,
                }]);
                let update = test_game_loop.tick_once();

                // Should NOT find season episodes container, an animation should be required to run before the container appears
                let season_episodes_carousel = update
                    .node_tree
                    .find_by_test_id(DETAILS_SEASON_AND_EPISODES_CARD_CAROUSEL_TEST_ID);
                assert_node_does_not_exist!(season_episodes_carousel);

                // Updating tab index will trigger the update to run the animation
                let details_context = use_details_page_context_test(scope);
                details_context
                    .borrow()
                    .state
                    .tab_state
                    .selected_tab_index
                    .set(Some(0));

                // One tick should not result in the animation being finished
                let update = test_game_loop.tick_once();
                let season_episodes_carousel = update
                    .node_tree
                    .find_by_test_id(DETAILS_SEASON_AND_EPISODES_CARD_CAROUSEL_TEST_ID);
                assert_node_does_not_exist!(season_episodes_carousel);

                let finished_animation = test_game_loop.tick_until_done();
                let season_episodes_carousel = finished_animation
                    .find_by_test_id(DETAILS_SEASON_AND_EPISODES_CARD_CAROUSEL_TEST_ID);
                assert_node_exists!(season_episodes_carousel);
            },
        )
    }
}
