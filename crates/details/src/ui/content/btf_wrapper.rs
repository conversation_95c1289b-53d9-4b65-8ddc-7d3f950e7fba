use crate::context::hooks::{empty_column_fallback, use_details_page_context};
use crate::context::state::base_creator::DetailsFocusedSection;
use crate::context::{DetailsPageContext, ResourceState};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::localized_text::DetailsString;
use crate::ui::container_list::details_page_container_list::*;
use crate::ui::container_list::types::DetailsPageContainerType;
use crate::ui::details_skeleton::*;
use crate::ui::test_ids::{DETAILS_PAGE_BTF_RETRY_CONTAINER_TEST_ID, DETAILS_PAGE_BTF_TEST_ID};
use container_list::container_list_sig::{ContainerListPointerOptions, PointerCaretOptions};
use fableous::buttons::primary_button::*;
use fableous::cards::sizing::{CardDimensions, CardSize};
use fableous::typography::typography::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("btf_wrapper");

#[Composer]
fn BtfRetryButton(ctx: &AppContext, details_ctx: DetailsPageContext) -> ColumnComposable {
    let on_select = move || {
        DETAILS_LOGGER.warn("BTF Retry button pressed");
        details_ctx.borrow().get_btf()
    };

    compose! {
        Column() {
            TypographyLabel500(content: DetailsString::AV_LRC_CHECKOUT_GENERIC_ERROR_PAGE_HEADER.get_localised_text())
                .color(Color::white())
            PrimaryButton(variant: PrimaryButtonVariant::TextSize200(DetailsString::AV_LRC_TRY_AGAIN.get_localised_text()))
                .on_select(on_select)
        }
        .test_id(DETAILS_PAGE_BTF_RETRY_CONTAINER_TEST_ID)
        .main_axis_alignment(MainAxisAlignment::SpacedBy(15.0))
    }
}

#[Composer]
fn BtfRetryContainer(
    ctx: &AppContext,
    details_ctx: DetailsPageContext,
    containers: RwSignal<Vec<DetailsPageContainerType>>,
) -> ColumnComposable {
    let btf_focused = details_ctx.borrow().state.btf_focused;
    let btf_pointer_control = details_ctx.borrow().state.pointer_state.btf_pointer_control;
    let enable_btf_pointer_focus_trap = details_ctx
        .borrow()
        .state
        .pointer_state
        .enable_btf_pointer_focus_trap;
    let enable_btf_free_focus = details_ctx
        .borrow()
        .state
        .pointer_state
        .enable_btf_free_focus;

    let btf_resource_state = details_ctx.borrow().state.page_resources.btf_state;
    let btf_scroll_to = details_ctx.borrow().state.btf_scroll_to;
    let opacity_btf = details_ctx.borrow().state.transition_state.btf_opacity;
    let has_failed = Signal::derive(ctx.scope(), move || {
        btf_resource_state.try_get() == Some(ResourceState::Failed)
    });

    let focused_section = details_ctx.borrow().state.focused_section;
    let focus_handler = move |_| {
        focused_section.set(DetailsFocusedSection::Btf);
    };

    let card_size: CardDimensions = CardSize::Standard.into();
    let pointer_options = ContainerListPointerOptions {
        enable_focus_trap: enable_btf_pointer_focus_trap.into(),
        free_focus_pointer_control: enable_btf_free_focus.into(),
        focus_trap_offset: create_rw_signal(ctx.scope(), Vec2::new(0.0, 10.0)).into(),
        pointer_caret_options: PointerCaretOptions {
            x_offset: create_rw_signal(ctx.scope(), -TITLE_DETAILS_LEFT_OFFSET).into(),
            y_offset: create_rw_signal(ctx.scope(), -40.0).into(),
            height: create_rw_signal(ctx.scope(), card_size.height + 250.0).into(),
            ..PointerCaretOptions::new(ctx.scope())
        },
    };

    compose! {
        Column() {
            if has_failed.try_get().unwrap_or(false) {
               BtfRetryButton(details_ctx: details_ctx.clone())
                        .opacity(opacity_btf)
            } else {
                Stack() {
                    DetailsPageContainers(containers: containers, scroll_to: Some(btf_scroll_to), pointer_options: Some(pointer_options))
                        .focused(btf_focused)
                        .focus_hierarchical_container(NavigationStrategy::Vertical)
                        .on_focus_move(focus_handler)
                        .opacity(opacity_btf)
                }
            }
        }
        .focus_pointer_control(btf_pointer_control)
    }
}

/// A Wrapper around the BTF that handles
/// - Loading state
/// - Failure state (Retry button)
/// - Success state (Passing to container list)
#[Composer]
pub fn BtfWrapper(
    ctx: &AppContext,
    containers: RwSignal<Vec<DetailsPageContainerType>>,
) -> ColumnComposable {
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            DETAILS_LOGGER.error("Failed to obtain content");
            return empty_column_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let atf_resource_state = details_ctx.borrow().state.page_resources.atf_state;
    let btf_resource_state = details_ctx.borrow().state.page_resources.btf_state;
    let is_loading = Signal::derive(ctx.scope(), move || {
        btf_resource_state.try_get() == Some(ResourceState::Loading)
            || atf_resource_state.try_get() == Some(ResourceState::Loading)
    });

    let skeleton_details_ctx = details_ctx.clone();
    let failure_details_ctx = details_ctx;
    compose! {
        Column() {
            if is_loading.try_get().unwrap_or(false) {
               DetailsPageBtfSkeleton(details_ctx: skeleton_details_ctx.clone(), x_offset: 0.0, y_offset: 0.0)
            } else {
               BtfRetryContainer(details_ctx: failure_details_ctx.clone(), containers)
            }
        }.test_id(DETAILS_PAGE_BTF_TEST_ID)
    }
}
