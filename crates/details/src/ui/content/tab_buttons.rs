use crate::context::hooks::{empty_column_fallback, use_details_page_context};
use crate::context::state::base_creator::DetailsFocusedSection;
use crate::context::DetailsPageContext;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::tab_calculator::{setup_tab, DetailsTabType};
use crate::ui::test_ids::DETAILS_PAGE_TAB_BUTTONS;
use details_derive::get_some_or_return_with_error_message;
use fableous::sliding_focus_list::*;
use ignx_compositron::focus::FocusDirection;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use media_background::types::MediaBackgroundType;
use std::rc::Rc;
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("tab_buttons");

fn get_tab_index(details_context: &DetailsPageContext, tab: DetailsTabType) -> Option<usize> {
    details_context
        .borrow()
        .state
        .tab_state
        .tab_items
        .try_with_untracked(|items| {
            for (index, item) in items.iter().enumerate() {
                if item.id == tab {
                    return Some(index);
                }
            }
            None
        })
        .flatten()
}

/// Tab selection is determined by focus not by an actual select event
fn tab_selected(details_context: &DetailsPageContext, tab: &DetailsTabType) {
    let Some(current_tab) = details_context
        .borrow()
        .state
        .tab_state
        .selected_tab
        .try_get_untracked()
    else {
        DETAILS_LOGGER.error("Failed to get current tab, scope likely disposed.");
        return;
    };

    if current_tab != *tab {
        details_context
            .borrow()
            .state
            .tab_state
            .selected_tab
            .try_set(*tab);

        details_context
            .borrow()
            .state
            .tab_state
            .selected_tab_index
            .try_set(get_tab_index(details_context, *tab));
    } else {
        // If we are coming back from the tab content we just want to remove any active video (if applicable)
        if let Some(MediaBackgroundType::Standard(mut current_background)) = details_context
            .borrow()
            .state
            .media_background
            .try_get_untracked()
        {
            current_background.video_id = None;
            details_context
                .borrow()
                .state
                .media_background
                .try_set(MediaBackgroundType::Standard(current_background));
        };
    }
}

pub const TABS_BUTTON_HEIGHT: f32 = 60.0;
pub const TABS_BUTTON_SPACING: f32 = 15.0;

#[Composer]
pub fn TabButtons(ctx: &AppContext, content_idx: usize) -> ColumnComposable {
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            DETAILS_LOGGER.error("Unable to retrieve details page context");
            return empty_column_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let tab_pointer_control = details_ctx.borrow().state.pointer_state.tab_pointer_control;
    let tabs_focused = details_ctx.borrow().state.tab_state.tab_buttons_focused;
    let tab_items = details_ctx.borrow().state.tab_state.tab_items;
    let selected_tab = details_ctx.borrow().state.tab_state.selected_tab;
    let tab_containers = details_ctx.borrow().state.tab_state.tab_containers;

    let nav_control = details_ctx.borrow().dependent_data.nav_control.clone();
    let atf_details_opacity = details_ctx.borrow().state.title_details.atf_opacity;
    let global_details_opacity = details_ctx.borrow().state.title_details.global_opacity;
    let focused_section = details_ctx.borrow().state.focused_section;
    let scroll_to_sig = details_ctx.borrow().state.content_scroll_to;

    // Move tabs buttons to top of the screen
    let initial_focus = {
        let ctx = ctx.clone();
        let details_ctx = details_ctx.clone();
        move |focus_dir| {
            // Only if focus came from above setup the current active tab
            if focus_dir == Some(FocusDirection::Down) {
                tab_containers.read_only().try_with_untracked(|items| {
                    let selected_tab = get_some_or_return_with_error_message!(
                        selected_tab.try_get_untracked(),
                        "Failed to get selected tab aborting focus tab update"
                    );
                    setup_tab(&ctx, selected_tab, items, &details_ctx);
                });

                nav_control.show_utility_nav.set(false);
                atf_details_opacity.set(0.0);
                global_details_opacity.set(1.0);
                focused_section.set(DetailsFocusedSection::TabButtons);
                scroll_to_sig.set(ScrollTo::Index(
                    content_idx as u32,
                    Pivot::Custom(64.0),
                    Animation::default(),
                ));
            }
        }
    };

    let on_tab_focus = Rc::new(move |item: &SlidingFocusItem<DetailsTabType>| {
        tab_selected(&details_ctx, &item.id)
    });

    let default_focus_item = Some(
        selected_tab
            .try_get_untracked()
            .unwrap_or(DetailsTabType::None),
    );
    compose! {
        Column() {
            SlidingFocusList(items: tab_items, on_item_select: on_tab_focus, default_focus_item)
                .padding(Padding::new(TITLE_DETAILS_LEFT_OFFSET, 0.0, 0.0, 16.0))
        }
        .focus_pointer_control(tab_pointer_control)
        .focused(tabs_focused)
        .on_focus_move(initial_focus)
        .main_axis_alignment(MainAxisAlignment::Center)
        .test_id(DETAILS_PAGE_TAB_BUTTONS)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, mock_out_btf_response,
        mock_out_successful_season_episode_next_response, use_details_page_context_test,
    };
    use crate::network::mock_responses::MOCK_RESPONSE_VOD_TROP_BTF;
    use crate::network::types::season_episodes::{EpisodeData, EpisodeInfo};
    use common_transform_types::details::TitleDecorationInformationCore;
    use common_transform_types::season_episodes::Episode;
    use media_background::types::{MediaStrategy, StandardBackgroundData};
    use mockall::TimesRange;
    use std::collections::HashMap;

    fn setup_tab_buttons(scope: Scope) -> DetailsPageContext {
        let details_ctx = use_details_page_context_test(scope);
        // Picked because TROP will have more than a single tab button
        mock_out_btf_response(
            scope,
            MOCK_RESPONSE_VOD_TROP_BTF.to_string(),
            TimesRange::from(1),
        );
        mock_out_successful_season_episode_next_response(scope, TimesRange::from(1));

        details_ctx.borrow().get_btf();
        details_ctx
    }

    #[test]
    fn should_remove_active_video_when_returning_from_tab() {
        ignx_compositron::app::launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);

                compose! {
                    Stack() {
                        TabButtons(content_idx: 0)
                        Label(text: "Dummy Element").test_id("dummy").focusable()
                    }
                }
            },
            |scope, mut test_game_loop| {
                let details_ctx = setup_tab_buttons(scope);
                let node_tree = test_game_loop.tick_until_done();

                details_ctx
                    .borrow()
                    .state
                    .media_background
                    .set(MediaBackgroundType::Standard(StandardBackgroundData {
                        id: "".to_string(),
                        image_url: None,
                        csm_data: None,
                        video_id: Some("VideoId".to_string()),
                        enter_immediately: false,
                        placement: "".to_string(),
                        media_strategy: MediaStrategy::Live,
                        interaction_source_override: None,
                    }));

                // Set focus to related button twice the second should cause the MB to be reset
                let bonus_button = node_tree.find_by_test_id("tab-related");
                test_game_loop.send_on_focus_event(bonus_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let dummy_button = node_tree.find_by_test_id("dummy");
                test_game_loop.send_on_focus_event(dummy_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                test_game_loop.send_on_focus_event(bonus_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                if let MediaBackgroundType::Standard(standard_data) =
                    details_ctx.borrow().state.media_background.get_untracked()
                {
                    assert_eq!(standard_data.video_id, None);
                } else {
                    panic!("Unexpected media background type!");
                }

                assert_eq!(
                    details_ctx
                        .borrow()
                        .state
                        .tab_state
                        .selected_tab
                        .get_untracked(),
                    DetailsTabType::Related
                );
            },
        )
    }

    #[test]
    fn should_update_current_tab_and_setup_tab_when_focused() {
        ignx_compositron::app::launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);

                compose! {
                    TabButtons(content_idx: 0)
                }
            },
            |scope, mut test_game_loop| {
                let details_ctx = setup_tab_buttons(scope);
                let node_tree = test_game_loop.tick_until_done();

                // Set focus to bonus button
                let bonus_button = node_tree.find_by_test_id("tab-extras");
                test_game_loop.send_on_focus_event(bonus_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                assert_eq!(
                    details_ctx
                        .borrow()
                        .state
                        .tab_state
                        .selected_tab
                        .get_untracked(),
                    DetailsTabType::Extras
                );
            },
        )
    }

    fn generate_episode_info() -> EpisodeData {
        let title_details = TitleDecorationInformationCore {
            audios: vec![],
            entity_type: "".to_string(),
            gti: "some_gti".to_string(),
            images: Default::default(),
            is_in_watchlist: false,
            ratings_metadata: Default::default(),
            subtitles: vec![],
            synopsis: "".to_string(),
            title: "title".to_string(),
            title_type: Default::default(),
            badges: Default::default(),
            current_episode_number: None,
            maturity_rating: None,
            release_date: None,
            runtime_seconds: None,
            upcoming_message: None,
            season_id: None,
        };

        EpisodeData::Episode(Episode {
            actions: vec![],
            entitlement_messaging: HashMap::new(),
            title_details,
            is_first_in_season: false,
            is_last_in_season: false,
        })
    }

    #[test]
    fn should_update_various_dependent_signals_when_a_tab_is_focused() {
        ignx_compositron::app::launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);

                compose! {
                    Column() {
                        Rectangle().focusable().preferred_focus(true)
                        TabButtons(content_idx: 1)
                    }
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let details_ctx = use_details_page_context_test(scope);

                // Change signals we want to detect changes in
                details_ctx
                    .borrow()
                    .dependent_data
                    .nav_control
                    .show_utility_nav
                    .set(true);
                details_ctx
                    .borrow()
                    .state
                    .title_details
                    .atf_opacity
                    .set(1.0);
                details_ctx
                    .borrow()
                    .state
                    .title_details
                    .global_opacity
                    .set(0.0);
                details_ctx
                    .borrow()
                    .state
                    .focused_section
                    .set(DetailsFocusedSection::Atf);
                details_ctx
                    .borrow()
                    .state
                    .content_scroll_to
                    .set(ScrollTo::Index(0, Pivot::Start, Animation::default()));
                details_ctx
                    .borrow()
                    .state
                    .season_episodes_state
                    .current_season
                    .set(vec![EpisodeInfo {
                        episode: create_rw_signal(scope, generate_episode_info()),
                        season_idx: 0,
                        id: "str".to_string(),
                    }]);

                // Move focus to tab buttons to the first time
                let tab_buttons = node_tree.find_by_test_id(DETAILS_PAGE_TAB_BUTTONS);
                test_game_loop.send_on_focus_event_with_direction(
                    tab_buttons.borrow_props().node_id,
                    FocusDirection::Down,
                );
                test_game_loop.tick_until_done();

                assert!(!details_ctx
                    .borrow()
                    .dependent_data
                    .nav_control
                    .show_utility_nav
                    .get_untracked());
                assert_eq!(
                    details_ctx
                        .borrow()
                        .state
                        .title_details
                        .atf_opacity
                        .get_untracked(),
                    0.0
                );
                assert_eq!(
                    details_ctx
                        .borrow()
                        .state
                        .title_details
                        .global_opacity
                        .get_untracked(),
                    1.0
                );
                assert_eq!(
                    details_ctx.borrow().state.focused_section.get_untracked(),
                    DetailsFocusedSection::TabButtons
                );
                match details_ctx.borrow().state.content_scroll_to.get_untracked() {
                    ScrollTo::Offset(_, _, _) => panic!("Unexpected scroll to value!"),
                    ScrollTo::Index(idx, pivot, _) => {
                        assert_eq!(idx, 1);
                        assert_eq!(pivot, Pivot::Custom(64.0));
                    }
                };
            },
        )
    }
}
