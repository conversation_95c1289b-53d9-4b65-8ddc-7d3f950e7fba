use crate::context::hooks::{empty_column_fallback, use_details_page_context};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use fableous::pointer_control::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use std::rc::Rc;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("pointer_overlay");

#[Composer]
pub fn PointerOverlay(ctx: &AppContext) -> ColumnComposable {
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            DETAILS_LOGGER.error("Failed to obtain details context");
            return empty_column_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let display_down_caret = details_ctx.borrow().state.pointer_state.display_down_caret;
    let display_up_caret = details_ctx.borrow().state.pointer_state.display_up_caret;
    let title_actions_focus = details_ctx.borrow().state.title_actions_state.focus_signal;

    let on_up_press = Rc::new(move || {
        title_actions_focus.set(true);
    });

    compose! {
        Column() {
            Stack() {
                if display_down_caret.try_get().unwrap_or(false) {
                    PointerControlCaret(direction: Direction::Down)
                        .translate_y(600.0)
                }

                if display_up_caret.try_get().unwrap_or(false) {
                    PointerControlCaret(direction: Direction::Up, on_select: on_up_press.clone())
                        .translate_y(50.0)
                }
            }
        }
        .cross_axis_alignment(CrossAxisAlignment::Center)
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
    }
}
