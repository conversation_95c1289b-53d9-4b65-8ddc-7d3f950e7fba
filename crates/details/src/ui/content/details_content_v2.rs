use crate::context::DetailsPageContext;
use crate::ui::content::atf_wrapper_v2::*;
use crate::ui::content::pointer_overlay::*;
use crate::ui::content::tab_buttons::*;
use crate::ui::content::tab_page_controller::*;
use fableous::animations::fable_motion_linear_medium;
use fableous::animations::FableMotionDuration;
use fableous::animations::MotionDuration;
use ignx_compositron::focus::FocusEdges;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;

pub const TAB_CONTENT_INACTIVE_PADDING: f32 = 75.0;
pub const BTF_TRANSLATE_Y_OFFSET: f32 = -ATF_HEIGHT;
pub const ATF_BTF_PADDING: f32 = 64.0;
pub const DETAILS_CONTENT_V2_TEST_ID: &str = "details-content-v2-test-id";
pub const BTF_SECTION_TEST_ID: &str = "btf-section-test-id";

/// Core Details page component that renders a redesigned version of the entire page responsible for
///  - Rendering an updated version of the ATF that contains a vertical buy box component and embedded title details
///  - Rendering Tabs (for now) in a future experiment this will be removed
///  - Rendering BTF containers (Caw/VAM/Franchise/Variant)
///  - Treating ATF as a distinct section to other elements of the page, handling custom animation transitions between focusing transitions
#[Composer]
pub fn DetailsContentV2(ctx: &AppContext, details_context: DetailsPageContext) -> StackComposable {
    let details_ctx = details_context.clone();
    let btf_containers = details_context.borrow().state.tab_state.tab_containers;
    let opacity_atf = details_context.borrow().state.transition_state.atf_opacity;

    let (translate_btf_y, set_translate_btf_y) = create_signal(ctx.scope(), 0.0);
    let btf_focused = create_focus_signal(ctx.scope());

    let app_context = details_ctx.borrow().app_context.clone();
    create_effect(ctx.scope(), move |prev_value| {
        // Todo: Update animations with fable motion tokens
        let current_value = btf_focused.get();

        if prev_value.is_some() {
            let (next_atf_opacty, next_btf_offest) = if current_value {
                (0.0, BTF_TRANSLATE_Y_OFFSET)
            } else {
                (1.0, 0.0)
            };

            app_context.with_animation(
                Animation::default().with_duration(FableMotionDuration::Standard.to_duration()),
                move || opacity_atf.set(next_atf_opacty),
            );

            app_context.with_animation(fable_motion_linear_medium(), move || {
                set_translate_btf_y.set(next_btf_offest)
            });
        }

        current_value
    });

    // Todo: Move AI Topics filtering logic to the parsing level: https://issues.amazon.com/issues/71f1c638-6c63-4dad-8e5e-372709478bf8
    let btf_is_loaded = details_context.borrow().state.page_resources.btf_is_loaded;
    let opacity_btf = details_context.borrow().state.transition_state.btf_opacity;

    compose! {
        Stack() {
            PointerOverlay()
            Column() {
                AtfWrapperV2(details_ctx)
                    .padding(Padding::new(TITLE_DETAILS_LEFT_OFFSET, 0.0 ,0.0, 0.0))
                    .opacity(opacity_atf)
                Column() {
                    if btf_is_loaded.try_get().unwrap_or(false) {
                        TabButtons(content_idx: 0)
                            .opacity(opacity_btf)
                    }
                    TabPageController(details_context: &details_context, containers: btf_containers)
                }
                .translate_y(translate_btf_y)
                .focused(btf_focused)
                .focus_hierarchical_container(NavigationStrategy::Vertical)
                .blocked_focus_edges(FocusEdges::BOTTOM)
                .test_id(BTF_SECTION_TEST_ID)
            }
            .focus_hierarchical_container(NavigationStrategy::Vertical)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(ATF_BTF_PADDING))
        }.test_id(DETAILS_CONTENT_V2_TEST_ID)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, setup_mock_reporter, use_details_page_context_test,
    };
    use crate::context::ResourceState;
    use crate::metrics::metric_reporter::traits::MockDetailsReporter;
    use crate::ui::test_ids::{
        DETAILS_PAGE_BTF_RETRY_CONTAINER_TEST_ID, DETAILS_PAGE_SKELETON_BTF_TEST_ID,
        DETAILS_PAGE_TAB_BUTTONS,
    };
    use firetv::MockFireTV;
    use ignx_compositron::test_utils::assert_node_exists;
    use std::rc::Rc;

    fn setup_details_content(
        ctx: &AppContext,
        details_context: DetailsPageContext,
    ) -> StackComposable {
        provide_firetv_context(ctx.scope());
        compose! {
            DetailsContentV2(details_context)
        }
    }

    fn provide_firetv_context(scope: Scope) {
        let mut firetv = MockFireTV::default();
        // is_firetv to return false
        firetv.expect_is_firetv().return_const(false);
        firetv.provide_mock(scope);
    }

    #[test]
    fn should_render_atf_tabs_btf_after_load_correctly() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_is_loaded
                    .set(true);

                setup_details_content(&ctx, details_context)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let atf_wrapper = node_tree.find_by_test_id(ATF_WRAPPER_V2_TEST_ID);
                assert_node_exists!(atf_wrapper);
                let tab_buttons = node_tree.find_by_test_id(DETAILS_PAGE_TAB_BUTTONS);
                assert_node_exists!(tab_buttons);
                let tab_page_controller = node_tree.find_by_test_id(TAB_PAGE_CONTROLLER_TEST_ID);
                assert_node_exists!(tab_page_controller);
            },
        )
    }

    #[test]
    fn should_insert_retry_container_if_failed() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_response
                    .set(None);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Failed);

                setup_details_content(&ctx, details_context)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let btf_skeleton =
                    node_tree.find_by_test_id(DETAILS_PAGE_BTF_RETRY_CONTAINER_TEST_ID);
                assert_node_exists!(btf_skeleton);
            },
        )
    }

    #[test]
    fn should_insert_btf_skeleton_if_loading() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_response
                    .set(None);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Loading);

                setup_details_content(&ctx, details_context)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let btf_skeleton = node_tree.find_by_test_id(DETAILS_PAGE_SKELETON_BTF_TEST_ID);
                assert_node_exists!(btf_skeleton);
            },
        )
    }

    #[test]
    fn should_report_atf_is_interactive() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);

                setup_details_content(&ctx, details_context)
            },
            |scope, mut test_game_loop| {
                let details_context = use_details_page_context_test(scope);

                let mut metric_reporter = MockDetailsReporter::default();
                setup_mock_reporter(&mut metric_reporter);
                details_context.borrow_mut().metric_reporter = Rc::new(metric_reporter);

                test_game_loop.tick_until_done();
            },
        );
    }

    #[test]
    fn should_handle_btf_focus_animations_correctly() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_is_loaded
                    .set(true);

                details_context
                    .borrow()
                    .state
                    .transition_state
                    .atf_opacity
                    .set(1.0);

                setup_details_content(&ctx, details_context)
            },
            |scope, mut test_game_loop| {
                let details_context = use_details_page_context_test(scope);

                let opacity_atf = details_context.borrow().state.transition_state.atf_opacity;
                // Assert ATF is currently visible
                assert_eq!(opacity_atf.get(), 1.0);

                let node_tree = test_game_loop.tick_until_done();
                // Assert BTF section position is at the bottom of the screen
                let btf_section_position = node_tree
                    .find_by_test_id(BTF_SECTION_TEST_ID)
                    .borrow_props()
                    .layout
                    .position
                    .clone();
                assert_eq!(btf_section_position.y, 1044.0);
                let tab_buttons_id = node_tree
                    .find_by_test_id(DETAILS_PAGE_TAB_BUTTONS)
                    .get_props()
                    .node_id;

                // Simulate moving focus from ATF to Tabs
                test_game_loop.send_on_focus_event(tab_buttons_id);

                let node_tree = test_game_loop.tick_until_done();
                // Assert ATF opacity is updated correctly (should be 0.0 when BTF is focused)
                assert_eq!(
                    opacity_atf.get(),
                    0.0,
                    "ATF should have zero opacity when BTF is focused"
                );
                // Assert BTF section position is now at the top of the screen
                let btf_section_position = node_tree
                    .find_by_test_id(BTF_SECTION_TEST_ID)
                    .borrow_props()
                    .layout
                    .position
                    .clone();
                assert_eq!(btf_section_position.y, 64.0);

                let atf_id = node_tree
                    .find_by_test_id(ATF_WRAPPER_V2_TEST_ID)
                    .get_props()
                    .node_id;
                // Simulate moving focus back up to ATF
                test_game_loop.send_on_focus_event(atf_id);
                let node_tree = test_game_loop.tick_until_done();

                // // Verify ATF opacity returns to 1.0 when BTF is unfocused
                assert_eq!(
                    opacity_atf.get(),
                    1.0,
                    "ATF should return to full opacity when BTF is unfocused"
                );
                // Assert BTF section position is back at the bottom of the screen
                let btf_section_position = node_tree
                    .find_by_test_id(BTF_SECTION_TEST_ID)
                    .borrow_props()
                    .layout
                    .position
                    .clone();
                assert_eq!(btf_section_position.y, 1044.0);
            },
        )
    }
}
