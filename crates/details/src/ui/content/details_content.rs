use crate::context::DetailsPageContext;
use crate::ui::container_list::types::DetailsPageContainer;
use crate::ui::content::atf_wrapper::*;
use crate::ui::content::pointer_overlay::*;
use crate::ui::content::tab_buttons::*;
use crate::ui::content::tab_page_controller::*;
use crate::ui::content::types::DetailsPageElement;
use crate::ui::title_details_atf::*;
use container_types::ui_signals::ContainerModel;
use ignx_compositron::list::ItemBuilderFn;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use std::rc::Rc;
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;

pub const TAB_CONTENT_INACTIVE_PADDING: f32 = 75.0;
pub const DETAILS_CONTENT_TEST_ID: &str = "details-content-test-id";

/// Core Details page component that renders the entire page responsible for
///  - Rendering all containers and page: ATF/BTF/Tabs
///  - Updating scroll states and title details state so everything is positioned correctly
///  - Mounting container lists / Tabs / Title actions
#[Composer]
pub fn DetailsContent(ctx: &AppContext, details_context: DetailsPageContext) -> ColumnComposable {
    let btf_containers = details_context.borrow().state.tab_state.tab_containers;

    let scope = ctx.scope();
    create_effect(scope, move |_| {
        let is_ai_topics_enabled =
            try_use_rust_features(scope).is_some_and(|f| f.is_rust_details_ai_topics_enabled());

        let filtered = btf_containers
            .try_get()
            .unwrap_or_default()
            .into_iter()
            .filter(|c| {
                is_ai_topics_enabled
                    || match &c.container {
                        DetailsPageContainer::Common(common_container) => !matches!(
                            common_container.model.try_get_untracked(),
                            Some(ContainerModel::DiscoveryAssistant(_))
                        ),
                        _ => true,
                    }
            })
            .collect::<Vec<_>>();
        btf_containers.try_set(filtered);
    });

    let btf_is_loaded = details_context.borrow().state.page_resources.btf_is_loaded;
    let opacity_btf = details_context.borrow().state.transition_state.btf_opacity;

    let scroll_to_sig = details_context.borrow().state.content_scroll_to;

    let builder: ItemBuilderFn<DetailsPageElement<'_>, ColumnComposable> =
        Rc::new(move |ctx, item, idx| {
            let item = item.clone();
            let details_ctx = details_context.clone();
            match item {
                DetailsPageElement::AtfTitleDetails => {
                    compose! {
                        TitleDetailsAtf()
                    }
                }
                DetailsPageElement::Atf => {
                    compose! {
                        AtfWrapper()
                            .padding(Padding::new(TITLE_DETAILS_LEFT_OFFSET, 0.0 ,0.0, 0.0))
                    }
                }
                DetailsPageElement::Btf(containers) => {
                    compose! {
                        Column() {
                            TabPageController(details_context: &details_ctx, containers)
                        }
                    }
                }
                DetailsPageElement::TabButtons => {
                    compose! {
                        Column() {
                            if btf_is_loaded.try_get().unwrap_or(false) {
                                TabButtons(content_idx: idx)
                                    .opacity(opacity_btf)
                            }
                        }
                    }
                }
                DetailsPageElement::MarginBox { amount, .. } => {
                    compose! {
                        Column() {
                            Rectangle().height(amount).opacity(0.0)
                        }
                    }
                }
            }
        });

    let items = vec![
        DetailsPageElement::AtfTitleDetails,
        DetailsPageElement::MarginBox {
            amount: 64.0,
            id: "TitleActionsOffsetTop",
        },
        DetailsPageElement::Atf,
        DetailsPageElement::MarginBox {
            amount: 32.0,
            id: "TitleActionsOffsetBottom",
        },
        DetailsPageElement::TabButtons,
        DetailsPageElement::Btf(btf_containers),
    ];

    compose! {
        Column() {
            Stack() {
                PointerOverlay()
                ColumnList(items: items.clone(), item_builder: builder.clone())
                    .scroll_to(scroll_to_sig)
                    .focus_hierarchical_container(NavigationStrategy::Vertical)
            }
        }.test_id(DETAILS_CONTENT_TEST_ID)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::state::state_bindings::root::setup_context_bindings;
    use crate::context::test_utils::{
        create_or_use_details_context_no_bindings_test, create_or_use_details_context_test,
        mock_out_successful_atf_response, mock_out_successful_btf_ai_topics_response,
        prepare_context_for_binding, setup_mock_reporter, use_details_page_context_test,
    };
    use crate::context::ResourceState;
    use crate::metrics::metric_reporter::traits::MockDetailsReporter;
    use crate::ui::test_ids::{
        DETAILS_PAGE_BTF_RETRY_CONTAINER_TEST_ID, DETAILS_PAGE_SKELETON_BTF_TEST_ID,
    };
    use firetv::MockFireTV;
    use ignx_compositron::test_utils::assert_node_exists;
    use mockall::TimesRange;
    use rstest::*;
    use rust_features::MockRustFeaturesBuilder;

    fn setup_details_content(
        ctx: &AppContext,
        details_context: DetailsPageContext,
    ) -> ColumnComposable {
        provide_firetv_context(ctx.scope());
        compose! {
            DetailsContent(details_context)
        }
    }

    fn provide_firetv_context(scope: Scope) {
        let mut firetv = MockFireTV::default();
        // is_firetv to return false
        firetv.expect_is_firetv().return_const(false);
        firetv.provide_mock(scope);
    }

    #[test]
    fn should_insert_retry_container_if_failed() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_response
                    .set(None);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Failed);

                setup_details_content(&ctx, details_context)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let btf_skeleton =
                    node_tree.find_by_test_id(DETAILS_PAGE_BTF_RETRY_CONTAINER_TEST_ID);
                assert_node_exists!(btf_skeleton);
            },
        )
    }

    #[test]
    fn should_insert_btf_skeleton_if_loading() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_response
                    .set(None);
                details_context
                    .borrow()
                    .state
                    .page_resources
                    .btf_state
                    .set(ResourceState::Loading);

                setup_details_content(&ctx, details_context)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let btf_skeleton = node_tree.find_by_test_id(DETAILS_PAGE_SKELETON_BTF_TEST_ID);
                assert_node_exists!(btf_skeleton);
            },
        )
    }

    #[test]
    fn should_report_atf_is_interactive() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);

                setup_details_content(&ctx, details_context)
            },
            |scope, mut test_game_loop| {
                let details_context = use_details_page_context_test(scope);

                let mut metric_reporter = MockDetailsReporter::default();
                setup_mock_reporter(&mut metric_reporter);
                details_context.borrow_mut().metric_reporter = Rc::new(metric_reporter);

                test_game_loop.tick_until_done();
            },
        );
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn should_keep_ai_topics_if_feature_enabled(#[case] ai_topics_enabled: bool) {
        ignx_compositron::app::launch_test(
            move |ctx| {
                let details_context = create_or_use_details_context_no_bindings_test(&ctx);
                prepare_context_for_binding(&ctx, details_context.clone());
                mock_out_successful_atf_response(ctx.scope(), TimesRange::from(1));
                mock_out_successful_btf_ai_topics_response(ctx.scope(), TimesRange::from(1));
                setup_context_bindings(&ctx, details_context.clone());

                MockRustFeaturesBuilder::new()
                    .set_is_rust_details_ai_topics_enabled(ai_topics_enabled)
                    .build_into_context(ctx.scope());

                setup_details_content(&ctx, details_context)
            },
            move |scope, mut test_game_loop| {
                let details_context = use_details_page_context_test(scope);

                test_game_loop.tick_until_done();
                let containers = details_context
                    .borrow()
                    .state
                    .tab_state
                    .tab_containers
                    .get();

                let expected_containers = if ai_topics_enabled { 2 } else { 1 };
                assert_eq!(containers.len(), expected_containers);
            },
        )
    }
}
