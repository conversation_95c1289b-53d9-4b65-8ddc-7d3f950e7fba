use crate::context::DetailsPageContext;
use crate::ui::content::tab_buttons::{TABS_BUTTON_HEIGHT, TABS_BUTTON_SPACING};
use crate::ui::test_ids::{
    DETAILS_PAGE_SKELETON_ATF_TEST_ID, DETAILS_PAGE_SKELETON_BTF_TEST_ID,
    DETAILS_PAGE_SKELETON_SEASON_AND_EPISODES_TEST_ID,
};
use amzn_fable_tokens::{FableButton, FableColor, FableIcon, FableSize};
use containers::standard_carousel_sig::STANDARD_CAROUSEL_SPACING;
use fableous::cards::sizing::CardSize::Standard;
use fableous::cards::sizing::{CardBorderRadius, CardDimensions};
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use title_details::core::TitleDetailsData;
use title_details::layouts::collection_and_details::*;

pub const RECTANGLE_BORDER_RADIUS: f32 = 15.0;
pub const RECTANGLE_WIDTH: f32 = 250.0;
pub const RECTANGLE_HEIGHT: f32 = 150.0;

#[Composer]
pub fn CardSkeleton(ctx: &AppContext, #[into] opacity: MaybeSignal<f32>) -> RectangleComposable {
    let standard_card_dims: CardDimensions = Standard.into();
    let border_radius: CardBorderRadius = Standard.into();
    let border_radius_f32: f32 = border_radius.into();

    compose! {
        Rectangle()
            .opacity(opacity)
            .background_color(get_ignx_color(FableColor::PRIMARY))
            .border_radius(border_radius_f32)
            .size(Vec2::new(standard_card_dims.width, standard_card_dims.height))
    }
}

#[Composer]
pub fn BoxSkeleton(
    ctx: &AppContext,
    width: f32,
    height: f32,
    #[into] opacity: MaybeSignal<f32>,
) -> RectangleComposable {
    compose! {
        Rectangle()
            .opacity(opacity)
            .background_color(get_ignx_color(FableColor::PRIMARY))
            .border_radius(RECTANGLE_BORDER_RADIUS)
            .size(Vec2::new(width, height))
    }
}

#[Composer]
pub fn PrimaryButtonSkeleton(
    ctx: &AppContext,
    #[into] opacity: MaybeSignal<f32>,
) -> RectangleComposable {
    compose! {
        Rectangle()
            .opacity(opacity)
            .border_radius(FableButton::PRIMARY_BORDER_RADIUS_SIZE400)
            .background_color(get_ignx_color(FableColor::PRIMARY))
            .size(Vec2::new(200.0, FableButton::PRIMARY_HEIGHT_MIN_SIZE400))
    }
}

#[Composer]
pub fn IconButtonSkeleton(
    ctx: &AppContext,
    #[into] opacity: MaybeSignal<f32>,
) -> RectangleComposable {
    compose! {
        Rectangle()
            .opacity(opacity)
            .border_radius(FableButton::ICON_BORDER_RADIUS)
            .background_color(get_ignx_color(FableColor::PRIMARY))
            .size(Vec2::new(FableIcon::SIZE300, FableIcon::SIZE300))
    }
}

#[Composer]
fn DetailsPageSkeletonAtfRaw(
    ctx: &AppContext,
    #[into] opacity: MaybeSignal<f32>,
) -> ColumnComposable {
    compose! {
        Column() {
            Row() {
                Column() {
                    PrimaryButtonSkeleton(opacity)
                }
                .height(FableSize::SIZE300)
                .main_axis_alignment(MainAxisAlignment::Center)

                IconButtonSkeleton(opacity)
                IconButtonSkeleton(opacity)
                IconButtonSkeleton(opacity)
                IconButtonSkeleton(opacity)
                IconButtonSkeleton(opacity)
            }
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(STANDARD_CAROUSEL_SPACING))
            .padding(Padding::new(0.0, 0.0, 0.0, 64.0))
        }
        .test_id(DETAILS_PAGE_SKELETON_ATF_TEST_ID)
        .cross_axis_alignment(CrossAxisAlignment::Center)
    }
}

#[Composer]
fn DetailsPageSkeletonBtfRaw(
    ctx: &AppContext,
    #[into] opacity: MaybeSignal<f32>,
    x_offset: f32,
    y_offset: f32,
) -> ColumnComposable {
    compose! {
        Column() {
            // Tab buttons
            Row() {
                BoxSkeleton(opacity, width: 150.0, height: TABS_BUTTON_HEIGHT)
                BoxSkeleton(opacity, width: 150.0, height: TABS_BUTTON_HEIGHT)
                BoxSkeleton(opacity, width: 150.0, height: TABS_BUTTON_HEIGHT)
            }.main_axis_alignment(MainAxisAlignment::SpacedBy(TABS_BUTTON_SPACING))

            // Tab content
            Row() {
                CardSkeleton(opacity)
                CardSkeleton(opacity)
                CardSkeleton(opacity)
            }
            .main_axis_alignment(MainAxisAlignment::SpacedBy(STANDARD_CAROUSEL_SPACING))
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(32.0))
        .test_id(DETAILS_PAGE_SKELETON_BTF_TEST_ID)
        .translate_x(x_offset)
        .translate_y(y_offset)
    }
}

#[Composer]
pub fn DetailsPageSeasonEpisodesSkeleton(
    ctx: &AppContext,
    details_ctx: &'_ DetailsPageContext,
) -> ColumnComposable {
    let opacity = details_ctx.borrow().state.skeleton_state.skeleton_opacity;
    compose! {
        Column() {
            Row() {
                CardSkeleton(opacity)
                CardSkeleton(opacity)
                CardSkeleton(opacity)
                CardSkeleton(opacity)
                CardSkeleton(opacity)
                CardSkeleton(opacity)
            }.main_axis_alignment(MainAxisAlignment::SpacedBy(STANDARD_CAROUSEL_SPACING))
        }.test_id(DETAILS_PAGE_SKELETON_SEASON_AND_EPISODES_TEST_ID)
    }
}

#[Composer]
pub fn DetailsPageBtfSkeleton(
    ctx: &AppContext,
    details_ctx: DetailsPageContext,
    x_offset: f32,
    y_offset: f32,
) -> ColumnComposable {
    let opacity = details_ctx.borrow().state.skeleton_state.skeleton_opacity;
    compose! {
        DetailsPageSkeletonBtfRaw(opacity, x_offset, y_offset)
    }
}

#[Composer]
pub fn DetailsPageContainersRow(ctx: &AppContext, opacity: MaybeSignal<f32>) -> RowComposable {
    compose! {
        // Tab content
        Row() {
            CardSkeleton(opacity)
            CardSkeleton(opacity)
            CardSkeleton(opacity)
            CardSkeleton(opacity)
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(STANDARD_CAROUSEL_SPACING))
    }
}

#[Composer]
pub fn DetailsPageAtfSkeleton(
    ctx: &AppContext,
    details_ctx: DetailsPageContext,
) -> ColumnComposable {
    let opacity = details_ctx.borrow().state.skeleton_state.skeleton_opacity;

    let title_details = details_ctx.borrow().state.title_details.global;
    let should_display_title_details_skeleton = Signal::derive(ctx.scope(), move || {
        title_details.with(|val| val.data == TitleDetailsData::Empty)
    });

    compose! {
        Column() {
            if should_display_title_details_skeleton.try_get().unwrap_or(false) {
                CollectionAndDetailsPageTitleDetailsSkeleton(input_opacity: Some(opacity))
                    .translate_x(-TITLE_DETAILS_LEFT_OFFSET)
                    .translate_y(-125.0)
                    .height(400.0)
            }
            DetailsPageSkeletonAtfRaw(opacity)
        }.main_axis_alignment(MainAxisAlignment::SpacedBy(64.0))
    }
}
