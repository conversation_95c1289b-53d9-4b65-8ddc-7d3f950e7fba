use crate::modules::localized_text::DetailsString;
use crate::ui::test_ids::{SUPPRESSION_DETAILS_OK_BUTTON_TEST_ID, SUPPRESSION_DETAILS_TEST_ID};
use amzn_fable_tokens::{FableColor, FableText};
use common_transform_types::actions::{TitleActionNestedChild, TitleActionPresentation};
use fableous::buttons::primary_button::*;
use fableous::typography::style_hints_typography::*;
use fableous::typography::type_ramp::TypeRamp;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};

#[Composer]
pub fn SuppressionModal<T>(
    ctx: &AppContext,
    action: TitleActionNestedChild,
    okay_cb: T,
) -> StackComposable
where
    T: Fn() + 'static,
{
    let body_action = action
        .childActions
        .iter()
        .find(|x| x.titleAction.presentation == TitleActionPresentation::ModalSection)
        .map(|x| x.titleAction.label.clone());

    let body_text = body_action.unwrap_or_default();
    let ramp = TypeRamp::from(FableText::TYPE_BODY200_EMPHASIS);

    compose! {
        Stack() {
            Column() {
                TypographyHeading600(content: TextContent::String(action.titleAction.label))
                Stack() {
                    StyleHintsTypography(type_ramp: ramp, content: TextContent::String(body_text))
                }
                    .width(800.0)
                    .padding(Padding::new(0.0, 0.0, 0.0, 30.0))
                PrimaryButton(variant: PrimaryButtonVariant::TextSize400(DetailsString::AV_LRC_OK_BUTTON_TEXT.get_localised_text()))
                    .on_select(okay_cb)
                    .test_id(SUPPRESSION_DETAILS_OK_BUTTON_TEST_ID)
            }
            .padding(Padding::new(144.0, 0.0, 0.0, 0.0))
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .main_axis_alignment(MainAxisAlignment::Center)
            .test_id(SUPPRESSION_DETAILS_TEST_ID)
        }
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
        .background_color(get_ignx_color(FableColor::BACKGROUND))
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use common_transform_types::actions::{SuppressionMetadata, TitleAction, TitleActionMetadata};

    fn test_title_action() -> TitleActionNestedChild {
        TitleActionNestedChild {
            childActions: vec![
                TitleActionNestedChild {
                    childActions: vec![],
                    titleAction: TitleAction {
                        label: "To watch this title, buy or rent it from the Amazon website on your mobile device or computer.".to_string(),
                        metadata: TitleActionMetadata::Suppression(SuppressionMetadata {
                            refMarker: None,
                            severity: None,
                            metadataActionType: None,
                        }),
                        presentation: TitleActionPresentation::ModalSection,
                        entitlement_metadata: None,
                    },
                }
            ],
            titleAction: TitleAction {
                label: "How do I watch this?".to_string(),
                metadata: TitleActionMetadata::Suppression(SuppressionMetadata {
                    refMarker: None,
                    severity: None,
                    metadataActionType: None,
                }),
                presentation: TitleActionPresentation::Modal,
                entitlement_metadata: None,
            }
        }
    }

    #[test]
    fn should_invoke_okay_callback_when_pressed() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let action = test_title_action();

                let pressed_sig = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), pressed_sig);
                let okay_cb = move || {
                    pressed_sig.set(true);
                };

                compose! {
                    SuppressionModal(action, okay_cb)
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let okay_button = node_tree.find_by_test_id(SUPPRESSION_DETAILS_OK_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(okay_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let pressed_sig =
                    use_context::<RwSignal<bool>>(scope).expect("pressed_sig to exist");
                assert!(pressed_sig.get_untracked());
            },
        )
    }
}
