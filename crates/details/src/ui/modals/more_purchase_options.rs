use crate::context::hooks::{create_or_use_details_page_context, empty_stack_fallback};
use crate::context::state::state_creators::title_actions::TitleActionsState;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::background_image::get_details_background_image;
use crate::modules::title_actions::get_title_actions_buttons_list::get_title_actions_button_list_mpo;
use crate::modules::title_actions::types::TitleActionItem;
use crate::ui::error_overlay::set_details_standard_error_state;
use crate::ui::test_ids::{
    DETAILS_DOWN_CARET_POINTER_TEST_ID, DETAILS_MPO_SECTION_CONTAINER_TEST_ID,
    DETAILS_MPO_SECTION_DISCLAIMER_TEST_ID, DETAILS_MPO_SECTION_HEADER_TEST_ID,
    DETAILS_MPO_SECTION_TEST_ID, DETAILS_MPO_TEST_ID, DETAILS_MPO_UP_CARET_TEST_ID,
    DETAILS_UP_CARET_POINTER_TEST_ID,
};
use crate::ui::title_actions::title_action_item_display::*;
use amzn_fable_tokens::FableIcon;
use common_transform_types::actions::{TitleActionMetadata, TitleActionNestedChild};
use common_transform_types::details::{EntitlementIcon, SlotEntitlementMessage, TitleType};
use containers::utils::tts_utils::tts_details_data_to_details_more_purchase_options_metadata_descriptions;
use details_derive::get_some_or_return_with_error_message;
use fableous::eoc::*;
use fableous::font_icon::*;
use fableous::pointer_control::*;
use fableous::typography::typography::*;
use fableous::SCREEN_HEIGHT;
use ignx_compositron::context::AppContext;
use ignx_compositron::id::Id;
use ignx_compositron::layout::MainAxisAlignment::SpacedBy;
use ignx_compositron::list::ItemBuilderFn;
use ignx_compositron::prelude::*;
use ignx_compositron::stack::StackComposable;
use ignx_compositron::{compose, Composer};
use media_background::types::{FullscreenBackgroundData, MediaBackgroundType};
use std::rc::Rc;
use std::time::Duration;
use title_details::core::{
    title_details_data_to_tts_details_data, TitleDetailsChangeRequest, TitleDetailsData,
};
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;
use title_details::types::common::StandardTitleDetailsData;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("more_purchase_options");

#[derive(Clone)]
struct EntitlementMessageMPO {
    content: String,
    icon: EntitlementType,
}

fn get_entitlement_info(
    action: &TitleActionNestedChild,
    entitlement_message: &Option<SlotEntitlementMessage>,
    title_type: &Option<TitleType>,
) -> Option<EntitlementMessageMPO> {
    let is_prime_or_svod_action = !action.childActions.is_empty()
        && match action.childActions.first() {
            None => false,
            Some(action) => matches!(
                action.titleAction.metadata,
                TitleActionMetadata::AcquisitionPrime(_) | TitleActionMetadata::AcquisitionSVOD(_)
            ),
        };

    if !is_prime_or_svod_action {
        return None;
    }

    if let Some(TitleType::Live) = title_type {
        return Some(EntitlementMessageMPO {
            content: action.titleAction.label.clone(),
            icon: EntitlementType::Unentitled,
        });
    }

    if let Some(entitlement_message) = entitlement_message {
        let entitlement_msg = match &entitlement_message.message {
            None => return None,
            Some(msg) => msg,
        };

        let entitlement_icon = match &entitlement_message.icon {
            None => return None,
            Some(icon) => match icon {
                EntitlementIcon::OfferIcon => EntitlementType::Unentitled,
                EntitlementIcon::EntitledIcon => EntitlementType::Entitled,
                EntitlementIcon::ErrorIcon => EntitlementType::Unavailable,
                _ => EntitlementType::None,
            },
        };

        Some(EntitlementMessageMPO {
            content: entitlement_msg.clone(),
            icon: entitlement_icon,
        })
    } else {
        None
    }
}

#[Composer]
pub fn MorePurchaseOptionsSectionHeader(
    ctx: &AppContext,
    section_action: &MpoItem,
) -> RowComposable {
    match &section_action.entitlement_info {
        Some(decorate_info) => {
            compose! {
                Row() {
                    EOC(content: decorate_info.content.clone(), entitlement_type: decorate_info.icon)
                }.test_id(DETAILS_MPO_SECTION_HEADER_TEST_ID)
            }
        }
        None => {
            let content = section_action.action.titleAction.label.clone();
            compose! {
                Row() {
                    TypographyHeading200(content)
                }.test_id(DETAILS_MPO_SECTION_HEADER_TEST_ID)
            }
        }
    }
}

#[Composer]
pub fn MorePurchaseOptionSection(ctx: &AppContext, section_action: &MpoItem) -> ColumnComposable {
    let action = &section_action.action;
    let disclaimer_text = match &action.titleAction.metadata {
        TitleActionMetadata::MoreWaysSection(section) => section.disclaimerText.clone(),
        _ => None,
    }
    .unwrap_or_default();

    let mut result = get_title_actions_button_list_mpo(action.childActions.clone());

    result.primary_buttons.extend(result.secondary_buttons);
    let items = result.primary_buttons;

    let mpo_button_builder: ItemBuilderFn<TitleActionItem, ColumnComposable> =
        Rc::new(move |ctx, item, _idx| {
            let state = TitleActionsState::new(ctx.scope());
            compose! {
                TitleActionItemDisplay(item, state: &state, is_primary: true)
            }
        });

    let tts_metadata_descriptions = match &section_action.entitlement_info {
        Some(decorate_info) => decorate_info.content.clone(),
        None => section_action.action.titleAction.label.clone(),
    };

    compose! {
        Column() {
            MorePurchaseOptionsSectionHeader(section_action)
            RowList(item_builder: mpo_button_builder, items)
                .main_axis_alignment(SpacedBy(30.0))
                .accessibility_description(tts_metadata_descriptions)
            Column() {
                Label(text: disclaimer_text)
                    .font_weight(FontWeight::SemiBold)
                    .font_size(FontSize(26))
                    .color(Color::new(190, 190, 190, 255))
                    .width(1632.0)
                .test_id(DETAILS_MPO_SECTION_DISCLAIMER_TEST_ID)
            }.width(0.0)
        }
        .focus_hierarchical_container(NavigationStrategy::Vertical)
        .test_id(DETAILS_MPO_SECTION_TEST_ID)
        .main_axis_alignment(SpacedBy(20.0))
    }
}

#[derive(Clone)]
struct MpoItem {
    entitlement_info: Option<EntitlementMessageMPO>,
    action: TitleActionNestedChild,
    id: String,
}

impl Id for MpoItem {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

const MAX_VISIBLE_ITEMS_ON_MODAL: usize = 3;
const START_SCROLL_IDX: usize = 2;

fn find_more_ways_action(items: &Vec<TitleActionNestedChild>) -> Option<TitleActionNestedChild> {
    for item in items {
        if let TitleActionMetadata::MoreWays(_) = item.titleAction.metadata {
            return Some(item.clone());
        }
    }
    None
}

#[Composer]
pub fn MorePurchaseOptions(ctx: &AppContext) -> StackComposable {
    let details_ctx = match create_or_use_details_page_context(ctx) {
        None => {
            set_details_standard_error_state(ctx.scope());
            return empty_stack_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let atf_response = details_ctx.borrow().state.page_resources.atf_response;
    let media_background = details_ctx.borrow().state.media_background;
    let title_details_global = details_ctx.borrow().state.title_details.global;
    let title_details_global_opacity = details_ctx.borrow().state.title_details.global_opacity;

    let title_details_atf = details_ctx.borrow().state.title_details.atf;
    let atf_state_binding_on_change = details_ctx.borrow().state.state_binding_atf_on_change;
    let title_information = details_ctx.borrow().state.title_information;

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            atf_state_binding_on_change.track();
            let response = get_some_or_return_with_error_message!(
                atf_response.try_get().flatten(),
                "Unable to get atf response"
            );

            let fullscreenBackgroundData = FullscreenBackgroundData {
                id: response.title_decoration.gti.clone(),
                image_url: get_details_background_image(&response),
                enter_immediately: true,
                image_opacity_percentage: 45,
            };
            media_background.set(MediaBackgroundType::FullscreenBackground(
                fullscreenBackgroundData,
            ));

            let data = match title_details_atf.try_get_untracked() {
                Some(TitleDetailsData::DetailsLayout(data)) => {
                    TitleDetailsData::DetailsMorePurchaseOptionsLayout(StandardTitleDetailsData {
                        title_data: data.title_data,
                        metadata: data.metadata,
                        synopsis: data.synopsis,
                        entitlement_data: data.entitlement_data,
                    })
                }
                _ => TitleDetailsData::Empty,
            };

            title_details_global.set(TitleDetailsChangeRequest {
                data,
                navigation_direction: Default::default(),
            });

            // FIXME: Add option to title details to disable animation (probably as a navigation_direction or other option)
            //        Delay to hide the animation change that happens when the title details gets updated
            ctx.with_animation(
                Animation::default().with_delay(Duration::from_millis(200)),
                move || {
                    title_details_global_opacity.set(1.0);
                },
            );
        }
    });

    let tts_details_data =
        title_details_data_to_tts_details_data(&title_details_global.clone().get_untracked().data);
    let tts_metadata_descriptions = tts_details_data.map_or_else(Vec::new, |data| {
        tts_details_data_to_details_more_purchase_options_metadata_descriptions(data)
    });

    let entitlement_message = Signal::derive(ctx.scope(), move || {
        atf_response.with(|val| match val {
            None => None,
            Some(response) => response.entitlement_message.clone(),
        })
    });

    let scroll_to = create_rw_signal(
        ctx.scope(),
        ScrollTo::Index(0, Pivot::Start, Animation::default()),
    );
    let sections = Signal::derive(ctx.scope(), move || {
        let section_entitle_message = entitlement_message.get();

        let Some(nav_gti) = atf_response
            .try_with(|response| {
                let Some(response) = response else {
                    return None;
                };

                Some(response.title_decoration.gti.clone())
            })
            .flatten()
        else {
            return vec![];
        };

        let Some(more_ways_action) = atf_response
            .try_with(|response| {
                let Some(response) = response else {
                    return None;
                };

                find_more_ways_action(&response.actions)
            })
            .flatten()
        else {
            return vec![];
        };

        let title_type = title_information
            .try_with(|info| {
                if let Some(info) = info {
                    Some(info.get_title_type().clone())
                } else {
                    None
                }
            })
            .flatten();

        more_ways_action
            .childActions
            .iter()
            .map(|item| MpoItem {
                entitlement_info: get_entitlement_info(item, &section_entitle_message, &title_type),
                id: format!("{}-{}", nav_gti, item.titleAction.label),
                action: item.clone(),
            })
            .collect()
    });

    let total_items = Signal::derive(ctx.scope(), move || sections.with(|items| items.len()));

    let focused_idx = create_rw_signal(ctx.scope(), 0);
    let pointer_control_active = is_pointer_control_active(ctx.scope());
    let section_builder: ItemBuilderFn<MpoItem, ColumnComposable> = Rc::new(
        move |ctx, section_action, idx| {
            let focused = create_focus_signal(ctx.scope());
            let focus_handler = move |_| {
                focused_idx.set(idx);
                let Some(focused_index_val) = scroll_to.try_get_untracked() else {
                    DETAILS_LOGGER.error("Unable to get current focused value");
                    return;
                };

                let focused_index = match focused_index_val {
                    ScrollTo::Offset(_, _, _) => 0,
                    ScrollTo::Index(idx, _, _) => idx,
                };

                // If there are more than three sections the fourth will now be off-screen
                if idx >= START_SCROLL_IDX && total_items.get() > MAX_VISIBLE_ITEMS_ON_MODAL {
                    scroll_to.set(ScrollTo::Index(
                        idx as u32,
                        Pivot::Start,
                        Animation::default(),
                    ))
                } else if focused_index > 0 {
                    scroll_to.set(ScrollTo::Index(0, Pivot::Start, Animation::default()))
                }
            };

            let item_opacity = Signal::derive(ctx.scope(), move || {
                if idx < focused_idx.get()
                    && focused_idx.get() >= START_SCROLL_IDX
                    && total_items.get() > MAX_VISIBLE_ITEMS_ON_MODAL
                {
                    0.0f32
                } else {
                    1.0f32
                }
            });

            let up_pointer_visible = Signal::derive(ctx.scope(), move || {
                focused_idx.get() > 0
                    && pointer_control_active.get()
                    && focused.get()
                    && total_items.get() > 3
            });

            let down_pointer_visible = Signal::derive(ctx.scope(), move || {
                let total_items = total_items.get();
                focused_idx.get() != total_items - 1
                    && pointer_control_active.get()
                    && focused.get()
                    && total_items > 3
            });

            let pointer_control_behaviour = Signal::derive(ctx.scope(), move || {
                if focused_idx.get() == idx || idx <= START_SCROLL_IDX {
                    FocusPointerControl::Inherit
                } else {
                    FocusPointerControl::Disabled
                }
            });

            compose! {
                Column() {
                    Column() {
                        PointerControlCaret(direction: Direction::Up, visible: up_pointer_visible)
                    }
                    .height(0.0)
                    .z_index(1)
                    .translate_y(-100.0)
                    .test_id(DETAILS_UP_CARET_POINTER_TEST_ID)

                    MorePurchaseOptionSection(section_action)
                        .on_focus_move(focus_handler)
                        .focus_pointer_control(pointer_control_behaviour)
                        .focused(focused)

                    Column() {
                        PointerControlCaret(direction: Direction::Down, visible: down_pointer_visible)
                    }
                    .height(0.0)
                    .z_index(1)
                    .translate_y(-40.0)
                    .test_id(DETAILS_DOWN_CARET_POINTER_TEST_ID)
                }
                .test_id(DETAILS_MPO_SECTION_CONTAINER_TEST_ID)
                .opacity(item_opacity)
                .cross_axis_alignment(CrossAxisAlignment::Center)
            }
        },
    );

    let pointer_control_active = is_pointer_control_active(ctx.scope());
    let show_up_caret = Signal::derive(ctx.scope(), move || {
        let has_scrolled = match scroll_to.get() {
            ScrollTo::Offset(_, _, _) => false,
            ScrollTo::Index(idx, _, _) => idx > 0,
        };
        if has_scrolled && !pointer_control_active.get() {
            1.0f32
        } else {
            0.0f32
        }
    });

    compose! {
        Stack() {
            Column() {
                Column() {
                    FontIcon(icon: FableIcon::CARET_UP, size: FontSize(35), color: Color::white())
                }
                .test_id(DETAILS_MPO_UP_CARET_TEST_ID)
                .opacity(show_up_caret)
                .width(600.0)
                .cross_axis_alignment(CrossAxisAlignment::Center)

                ColumnList(item_builder: section_builder, items: sections)
                    .scroll_to(scroll_to)
                    .focus_hierarchical_container(NavigationStrategy::Vertical)
                    .main_axis_alignment(SpacedBy(50.0))
            }
            .test_id(DETAILS_MPO_TEST_ID)
            .translate_x(TITLE_DETAILS_LEFT_OFFSET)
            .translate_y(400.0)
            .height(SCREEN_HEIGHT - 400.0)
            .accessibility_context_messages(tts_metadata_descriptions)
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, mock_out_atf_response, mock_out_live_atf_response,
        setup_mock_router_dp_to_dp, use_details_page_context_test,
    };
    use crate::network::mock_responses::{
        MOCK_RESPONSE_LIVE_ATF_MPO, MOCK_RESPONSE_VOD_FOUR_LINE_MPO_ATF,
        MOCK_RESPONSE_VOD_LONG_MPO_ATF, MOCK_RESPONSE_VOD_MPO_99_ATF,
    };
    use crate::ui::test_ids::{
        DETAILS_MPO_SECTION_DISCLAIMER_TEST_ID, DETAILS_MPO_UP_CARET_TEST_ID,
        DETAILS_TITLE_ACTION_TEST_ID,
    };
    use amzn_fable_tokens::{FableColor, FableIcon};
    use fableous::utils::get_ignx_color;
    use ignx_compositron::pointer_control::PointerControlActiveContext;
    use ignx_compositron::test_utils::node_properties::{NodeQuery, SceneNodeTree};
    use ignx_compositron::test_utils::{ComposableType, TestRendererGameLoop};
    use mockall::TimesRange;
    use router::{MockRouting, RoutingContext};
    use rust_features::MockRustFeaturesBuilder;
    use std::time::Duration;
    use title_details::core::NavigationDirection;
    use title_details::types::common::BadgeTypes::{Icon, TextContent};
    use title_details::types::common::MaturityRatingData::TextBadge;
    use title_details::types::common::{
        HighValueMessageData, StandardTitleDetailsData, StarRatingData, TitleData,
        TitleDetailsMetadata, VodMetadata,
    };

    fn assert_text_of_node<const N: usize>(
        button: &'_ NodeQuery<'_>,
        expected_lines: [&'_ str; N],
    ) {
        let button_text_lines = button
            .find_any_child_with()
            .composable_type(ComposableType::RichTextLabel)
            .find_all();
        let mut current_idx = 0;
        for line in expected_lines {
            let line_of_text_button = button_text_lines.get(current_idx).unwrap();
            let line_of_text_props = line_of_text_button.borrow_props();
            assert_eq!(line_of_text_props.text.clone().unwrap(), line.to_string());
            current_idx += 1;
        }
    }

    fn enable_and_activate_pointer_control(ctx: &AppContext) {
        MockRustFeaturesBuilder::new().build_as_mock_and_real_into_context(true, ctx.scope());
        let pointer_control_active_context =
            PointerControlActiveContext(create_rw_signal(ctx.scope(), true).read_only());
        provide_context(ctx.scope(), pointer_control_active_context);
    }

    fn focus_on_mpo_section(
        node: &NodeQuery<'_>,
        test_renderer_game_loop: &mut TestRendererGameLoop,
    ) -> SceneNodeTree {
        test_renderer_game_loop.send_on_focus_event(node.borrow_props().node_id);
        test_renderer_game_loop.tick_until_done()
    }

    struct MpoTest {
        scope: Scope,
        scene_tree: SceneNodeTree,
        test_game_loop: TestRendererGameLoop,
    }

    impl MpoTest {
        pub fn new(scope: Scope, mut test_renderer_game_loop: TestRendererGameLoop) -> MpoTest {
            MpoTest {
                scope,
                scene_tree: test_renderer_game_loop.tick_until_done(),
                test_game_loop: test_renderer_game_loop,
            }
        }

        fn get_root(&self) -> NodeQuery<'_> {
            self.scene_tree.find_by_test_id(DETAILS_MPO_TEST_ID)
        }

        pub fn focus_section(&mut self, num: usize) {
            let root = self.get_root();
            let sections = root
                .find_any_child_with()
                .test_id(DETAILS_MPO_SECTION_CONTAINER_TEST_ID)
                .find_all();
            self.test_game_loop
                .send_on_focus_event(sections[num].borrow_props().node_id);
            self.scene_tree = self.test_game_loop.tick_until_done();
        }

        pub fn assert_pointer_carets_for_section(
            &self,
            section_num: usize,
            up_visible: bool,
            down_visible: bool,
        ) {
            let root = self.get_root();
            let sections = root
                .find_any_child_with()
                .test_id(DETAILS_MPO_SECTION_CONTAINER_TEST_ID)
                .find_all();
            let section = &sections[section_num];

            let up_arrow = section
                .find_any_child_with()
                .test_id(DETAILS_UP_CARET_POINTER_TEST_ID)
                .find_first();
            let inner_up_arrow = up_arrow
                .find_any_child_with()
                .test_id(POINTER_CONTROL_ICON_TEST_ID)
                .find_first();
            let up_expected_opacity = if up_visible { 1.0 } else { 0.0 };
            assert_eq!(
                inner_up_arrow.borrow_props().base_styles.opacity,
                Some(up_expected_opacity)
            );

            let down_arrow = section
                .find_any_child_with()
                .test_id(DETAILS_DOWN_CARET_POINTER_TEST_ID)
                .find_first();
            let inner_down_arrow = down_arrow
                .find_any_child_with()
                .test_id(POINTER_CONTROL_ICON_TEST_ID)
                .find_first();
            let down_expected_opacity = if down_visible { 1.0 } else { 0.0 };
            assert_eq!(
                inner_down_arrow.borrow_props().base_styles.opacity,
                Some(down_expected_opacity)
            );
        }

        pub fn assert_opacity(&self, test_id: &'_ str, expected_opacity: Option<f32>) {
            let root = self.get_root();
            let child_node = root.find_any_child_with().test_id(test_id).find_first();
            assert_eq!(
                child_node.borrow_props().base_styles.opacity,
                expected_opacity
            );
        }

        pub fn tick_until_done(&mut self) {
            self.scene_tree = self.test_game_loop.tick_until_done()
        }
    }

    #[test]
    fn should_show_up_caret_if_scrolled_and_pointer_control_not_enabled() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope,
                    MOCK_RESPONSE_VOD_FOUR_LINE_MPO_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                   MorePurchaseOptions()
                }
            },
            |scope, test_game_loop| {
                let mut mpo_test = MpoTest::new(scope, test_game_loop);
                mpo_test.assert_opacity(DETAILS_MPO_UP_CARET_TEST_ID, Some(0.0));

                mpo_test.focus_section(3);
                mpo_test.assert_opacity(DETAILS_MPO_UP_CARET_TEST_ID, Some(1.0));
            },
        )
    }

    #[test]
    fn should_show_correct_pointer_carets_when_pointer_control_is_enabled() {
        ignx_compositron::app::launch_test(
            |ctx| {
                enable_and_activate_pointer_control(&ctx);

                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope,
                    MOCK_RESPONSE_VOD_FOUR_LINE_MPO_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                   MorePurchaseOptions()
                }
            },
            |scope, test_game_loop| {
                let mut mpo_test = MpoTest::new(scope, test_game_loop);
                mpo_test.assert_pointer_carets_for_section(0, false, true);
                mpo_test.assert_pointer_carets_for_section(1, false, false);
                mpo_test.assert_pointer_carets_for_section(2, false, false);
                mpo_test.assert_pointer_carets_for_section(3, false, false);

                mpo_test.focus_section(1);
                mpo_test.assert_pointer_carets_for_section(1, true, true);

                mpo_test.focus_section(2);
                mpo_test.assert_pointer_carets_for_section(1, false, false);
                mpo_test.assert_pointer_carets_for_section(2, true, true);

                mpo_test.focus_section(3);

                // Also make sure the normal caret for non-pointer control is not displayed
                mpo_test.assert_opacity(DETAILS_MPO_UP_CARET_TEST_ID, Some(0.0));
                mpo_test.assert_pointer_carets_for_section(2, false, false);
                mpo_test.assert_pointer_carets_for_section(3, true, false);
            },
        )
    }

    #[test]
    fn should_scroll_if_more_than_three_sections_exist_after_hitting_third() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope,
                    MOCK_RESPONSE_VOD_FOUR_LINE_MPO_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                   MorePurchaseOptions()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let mpo = node_tree.find_by_test_id(DETAILS_MPO_TEST_ID);
                let sections = mpo
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_SECTION_CONTAINER_TEST_ID)
                    .find_all();

                assert_eq!(sections[0].borrow_props().base_styles.opacity, Some(1.0));
                assert_eq!(sections[1].borrow_props().base_styles.opacity, Some(1.0));

                let node_tree = focus_on_mpo_section(&sections[2usize], &mut test_game_loop);
                assert_eq!(sections.len(), 4);

                let mpo = node_tree.find_by_test_id(DETAILS_MPO_TEST_ID);
                let sections = mpo
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_SECTION_CONTAINER_TEST_ID)
                    .find_all();
                // First two items should be scrolled off screen now
                assert_eq!(sections[0].borrow_props().base_styles.opacity, Some(0.0));
                assert_eq!(sections[1].borrow_props().base_styles.opacity, Some(0.0));
            },
        )
    }

    #[test]
    fn should_update_title_details_and_media_background_on_load() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let mut router = MockRouting::default();
                setup_mock_router_dp_to_dp(ctx.scope(), &mut router);
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(router));

                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope,
                    MOCK_RESPONSE_VOD_MPO_99_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                    MorePurchaseOptions()
                }
            },
            |scope, mut test_game_loop| {
                let _node_tree = test_game_loop.tick_until_done();
                let details_ctx = use_details_page_context_test(scope);

                let title_details = details_ctx.borrow().state.title_details.global;

                assert_eq!(title_details.get_untracked(), TitleDetailsChangeRequest {
                    data: TitleDetailsData::DetailsMorePurchaseOptionsLayout(StandardTitleDetailsData {
                        title_data: TitleData {
                            title_art_url: Some("https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f0b6be03229609830c7561d0da3670866afb9630f05824ae47d3106d5fe1cda5._RI_TTW_.png".to_string()),
                            provider_logo_url: None,
                            title_text: "99".to_string(),
                        },
                        metadata: TitleDetailsMetadata::Vod(VodMetadata {
                            high_value_message: Some(HighValueMessageData {
                                message: "#2 in Ireland".to_string(),
                                color: get_ignx_color(FableColor::EXPLORER300)
                            }),
                            metadata_badge_message: None,
                            star_rating: Some(StarRatingData {
                                rating: 5.0,
                                votes: Some(3),
                            }),
                            duration: Some(Duration::from_secs(3400)),
                            number_of_seasons: None,
                            release_year: Some(2024),
                            maturity_rating: Some(TextBadge("16+".to_string())),
                            badges: vec![
                                TextContent("5.1".to_string()),
                                Icon(FableIcon::SUBTITLES_CC.to_string())
                            ],
                            genres: vec![
                                "Documentary".to_string(), "Sports".to_string()
                            ],
                            is_prerelease: None,
                            card_badge: None,
                            upcoming_message: None,
                            condensed_page: false,
                        }),
                        synopsis: Some("S1 E1 1999 was a year of groundbreaking success for England’s biggest and most powerful football club, Manchester United. Despite dominating domestic football during the 90s, manager Alex Ferguson was yet to restore Manchester United’s European glory when in 1999, glory arrived in an unprecedented fashion.".to_string()),
                        entitlement_data: Default::default(),
                    }),
                    navigation_direction: NavigationDirection::NONE
                });

                assert_eq!(details_ctx.borrow().state.media_background.get_untracked(), MediaBackgroundType::FullscreenBackground(FullscreenBackgroundData {
                    id: "amzn1.dv.gti.b85a3f54-0746-4ec0-879a-94378ec2e873".to_string(),
                    image_url: "https://m.media-amazon.com/images/S/pv-target-images/b81e4ca029f74d6b53b0f106c30e5faf24c9f26b0d10737a1d43ea3f72242b76._RI_V0_TTW_.jpg".to_string(),
                    enter_immediately: true,
                    image_opacity_percentage: 45,
                }));
            },
        )
    }

    #[test]
    fn should_render_rent_and_buy_sections() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope,
                    MOCK_RESPONSE_VOD_MPO_99_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                    MorePurchaseOptions()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let mpo = node_tree.find_by_test_id(DETAILS_MPO_TEST_ID);
                let sections = mpo
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_SECTION_CONTAINER_TEST_ID)
                    .find_all();

                assert_eq!(sections.len(), 2);

                // First MPO Section Rent
                let first_section_header_text = sections
                    .first()
                    .unwrap()
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_first();
                let first_section_header_text_props = first_section_header_text.borrow_props();
                assert_eq!(
                    first_section_header_text_props.text.clone().unwrap(),
                    "Rent"
                );

                let first_section_disclaimer = sections
                    .first()
                    .unwrap()
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_SECTION_DISCLAIMER_TEST_ID)
                    .find_first();
                let first_section_disclaimer_props = first_section_disclaimer.borrow_props();
                assert_eq!(
                    first_section_disclaimer_props.text.clone().unwrap(),
                    "The price before discount is the median price for the last 90 days. Rentals include 30 days to start watching this video and 48 hours to finish once started."
                );

                let title_actions_items = sections
                    .first()
                    .unwrap()
                    .find_any_child_with()
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                    .find_all();
                assert_eq!(title_actions_items.len(), 3);

                // First Rent button
                assert_text_of_node(
                    title_actions_items.first().unwrap(),
                    ["Rent movie ", "UHD ", "£15.99", " £5.49"],
                );

                // Second Rent Button
                assert_text_of_node(
                    title_actions_items.get(1).unwrap(),
                    ["Rent movie ", "HD ", "£15.99", " £4.99"],
                );

                // Third Rent Button
                assert_text_of_node(
                    title_actions_items.get(2).unwrap(),
                    ["Rent movie ", "SD ", "£15.99", " £3.49"],
                );

                // Second MPO Section Buy
                let second_section_header_text = sections
                    .get(1)
                    .unwrap()
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_first();
                let second_section_header_text_props = second_section_header_text.borrow_props();
                assert_eq!(
                    second_section_header_text_props.text.clone().unwrap(),
                    "Buy"
                );

                let second_section_disclaimer = sections
                    .get(1)
                    .unwrap()
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_SECTION_DISCLAIMER_TEST_ID)
                    .find_first();
                let second_section_disclaimer_props = second_section_disclaimer.borrow_props();
                assert_eq!(
                    second_section_disclaimer_props.text.clone().unwrap(),
                    "The price before discount is the median price for the last 90 days."
                );

                let title_actions_items = sections
                    .get(1)
                    .unwrap()
                    .find_any_child_with()
                    .test_id(DETAILS_TITLE_ACTION_TEST_ID)
                    .find_all();
                assert_eq!(title_actions_items.len(), 3);

                // First Rent button
                assert_text_of_node(
                    title_actions_items.first().unwrap(),
                    ["Buy movie ", "UHD ", "£19.99", " £13.99"],
                );

                // Second Rent Button
                assert_text_of_node(
                    title_actions_items.get(1).unwrap(),
                    ["Buy movie ", "HD ", "£19.99", " £13.99"],
                );

                // Third Rent Button
                assert_text_of_node(
                    title_actions_items.get(2).unwrap(),
                    ["Buy movie ", "SD ", "£19.99", " £9.99"],
                );
            },
        )
    }

    #[test]
    fn should_render_entitlement_mpo_section() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope,
                    MOCK_RESPONSE_VOD_LONG_MPO_ATF.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_atf();

                compose! {
                    MorePurchaseOptions()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let eoc_component = node_tree.find_by_test_id(EOC_TEST_ID);
                let eoc_label = eoc_component
                    .find_any_child_with()
                    .test_id(EOC_LABEL_TEST_ID)
                    .composable_type(ComposableType::Label)
                    .find_first();
                let eoc_label_props = eoc_label.borrow_props();

                let eoc_icon = eoc_component
                    .find_any_child_with()
                    .test_id(EOC_ICON_TEST_ID)
                    .composable_type(ComposableType::Label)
                    .find_first();
                let eoc_icon_props = eoc_icon.borrow_props();

                assert_eq!(
                    eoc_label_props.text.clone().unwrap(),
                    "Free 7 day trial of THE ICON FILM CHANNEL, auto renews at £5.99/month, rent or buy"
                );

                assert_eq!(
                    eoc_icon_props.text.clone().unwrap(),
                    FableIcon::STORE_FILLED
                );
            },
        )
    }

    #[test]
    fn should_render_entitlement_mpo_section_for_live() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let rust_features = MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build();
                provide_context(ctx.scope(), rust_features);

                let details_ctx = create_or_use_details_context_test(&ctx);
                mock_out_live_atf_response(
                    ctx.scope,
                    MOCK_RESPONSE_LIVE_ATF_MPO.to_string(),
                    TimesRange::from(1),
                );
                details_ctx.borrow().get_live_atf();

                compose! {
                    MorePurchaseOptions()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let eoc_component = node_tree.find_by_test_id(EOC_TEST_ID);
                let eoc_label = eoc_component
                    .find_any_child_with()
                    .test_id(EOC_LABEL_TEST_ID)
                    .composable_type(ComposableType::Label)
                    .find_first();
                let eoc_label_props = eoc_label.borrow_props();

                let eoc_icon = eoc_component
                    .find_any_child_with()
                    .test_id(EOC_ICON_TEST_ID)
                    .composable_type(ComposableType::Label)
                    .find_first();
                let eoc_icon_props = eoc_icon.borrow_props();

                assert_eq!(
                    eoc_label_props.text.clone().unwrap(),
                    "Subscribe for $8.99/month"
                );

                assert_eq!(
                    eoc_icon_props.text.clone().unwrap(),
                    FableIcon::STORE_FILLED
                );
            },
        )
    }
}
