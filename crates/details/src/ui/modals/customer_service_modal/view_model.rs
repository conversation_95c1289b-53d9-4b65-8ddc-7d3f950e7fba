use amzn_fable_tokens::FableText;
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::container_items::TNFProperties;
use fableous::typography::type_ramp::TypeRamp;
use fableous::typography::typography::*;
use ignx_compositron::id::Id;
use ignx_compositron::text::TextContent;
use ignx_compositron::Composer;
use ignx_compositron::{compose, prelude::*};
use lrc_image::lrc_image::*;
use lrc_image::types::{ImageData, ImageTag, ScalingStrategy};

use crate::network::types::widgets::customer_service::{
    ContactOption, CustomerServiceWidget, QuestionAndAnswer,
};
use crate::ui::test_ids::{
    CUSTOMER_SERVICE_WIDGET_ITEM, CUSTOMER_SERVICE_WIDGET_ITEM_DESCRIPTION,
    CUSTOMER_SERVICE_WIDGET_ITEM_IMAGE, CUSTOMER_SERVICE_WIDGET_ITEM_SECTION,
    CUSTOMER_SERVICE_WIDGET_ITEM_TITLE,
};

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub(super) enum WidgetItemViewModel {
    Section {
        id: String,
        text_content: TextContent,
    },
    Title {
        id: String,
        text_content: TextContent,
    },
    Description {
        id: String,
        text_content: TextContent,
    },
    Image {
        id: String,
        image_data: ImageData,
        accessibility_label: TextContent,
    },
}

impl Id for WidgetItemViewModel {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            WidgetItemViewModel::Section { id, .. } => id,
            WidgetItemViewModel::Title { id, .. } => id,
            WidgetItemViewModel::Description { id, .. } => id,
            WidgetItemViewModel::Image { id, .. } => id,
        }
    }
}

pub(super) struct WidgetViewModelContext {
    pub(super) tnf_property: Option<TNFProperties>,
}

pub(super) trait IntoViewModel {
    fn into_view_model(
        self,
        id_prefix: &str,
        context: &WidgetViewModelContext,
    ) -> Vec<WidgetItemViewModel>;
}

impl IntoViewModel for Vec<CustomerServiceWidget> {
    fn into_view_model(
        mut self,
        id_prefix: &str,
        context: &WidgetViewModelContext,
    ) -> Vec<WidgetItemViewModel> {
        self.sort_by(|a, b| {
            let a_is_contact_options =
                matches!(a, CustomerServiceWidget::ContactOptionsWidgetV1(_));
            let b_is_contact_options =
                matches!(b, CustomerServiceWidget::ContactOptionsWidgetV1(_));
            if a_is_contact_options && !b_is_contact_options {
                std::cmp::Ordering::Less
            } else if !a_is_contact_options && b_is_contact_options {
                std::cmp::Ordering::Greater
            } else {
                std::cmp::Ordering::Equal
            }
        });

        self.into_iter()
            .enumerate()
            .flat_map(|(idx, item)| {
                let id_prefix = format!("{}-{}", id_prefix, idx);
                item.into_view_model(&id_prefix, context)
            })
            .collect()
    }
}

impl IntoViewModel for QuestionAndAnswer {
    fn into_view_model(
        self,
        id_prefix: &str,
        _context: &WidgetViewModelContext,
    ) -> Vec<WidgetItemViewModel> {
        vec![
            WidgetItemViewModel::Title {
                id: format!("{}-question", id_prefix),
                text_content: TextContent::String(self.question.to_plain_text()),
            },
            WidgetItemViewModel::Description {
                id: format!("{}-answer", id_prefix),
                text_content: TextContent::String(self.answer.to_plain_text()),
            },
        ]
    }
}

impl IntoViewModel for ContactOption {
    fn into_view_model(
        self,
        id_prefix: &str,
        context: &WidgetViewModelContext,
    ) -> Vec<WidgetItemViewModel> {
        let mut items = Vec::new();
        items.push(WidgetItemViewModel::Title {
            id: format!("{}-title", id_prefix),
            text_content: TextContent::String(self.title.to_plain_text()),
        });
        items.push(WidgetItemViewModel::Description {
            id: format!("{}-description", id_prefix),
            text_content: TextContent::String(self.description.to_plain_text()),
        });
        if let Some(qr_code) = self.qr_code {
            items.push(WidgetItemViewModel::Image {
                id: format!("{}-qr-code", id_prefix),
                image_data: ImageData {
                    url: qr_code.uri,
                    width: 342.0,
                    height: 342.0,
                    tags: vec![ImageTag::Scaling(ScalingStrategy::UpscaleToCover)],
                },
                accessibility_label: TextContent::String(qr_code.accessibility_label),
            });
        }
        if let Some(tnf_strings) = context
            .tnf_property
            .as_ref()
            .and_then(|t| t.localizedStringContents.as_ref())
        {
            if let Some(tnf_help_string) = &tnf_strings.helpHubString {
                items.push(WidgetItemViewModel::Title {
                    id: format!("{}-tnf-help-info", id_prefix),
                    text_content: TextContent::String(tnf_help_string.to_owned()),
                });
            }
            if let Some(tnf_help_url) = &tnf_strings.helpHubUrl {
                items.push(WidgetItemViewModel::Description {
                    id: format!("{}-tnf-help-url", id_prefix),
                    text_content: TextContent::String(tnf_help_url.to_owned()),
                });
            }
        }
        items
    }
}

impl IntoViewModel for CustomerServiceWidget {
    fn into_view_model(
        self,
        id_prefix: &str,
        context: &WidgetViewModelContext,
    ) -> Vec<WidgetItemViewModel> {
        match self {
            CustomerServiceWidget::QuestionsAndAnswersWidgetV1(q_and_a_widget) => {
                let mut items = Vec::new();
                items.push(WidgetItemViewModel::Section {
                    id: format!("{}-section", id_prefix),
                    text_content: TextContent::String(
                        q_and_a_widget.widget_payload.data.title.to_plain_text(),
                    ),
                });
                let questions_and_answers = q_and_a_widget
                    .widget_payload
                    .data
                    .questions_and_answers
                    .into_iter()
                    .enumerate();
                for (qidx, question_and_answer) in questions_and_answers {
                    let id_prefix = format!("{}-q&a-{}", id_prefix, qidx);
                    let q_and_a_items = question_and_answer.into_view_model(&id_prefix, context);
                    items.extend(q_and_a_items);
                }
                items
            }
            CustomerServiceWidget::ContactOptionsWidgetV1(contact_options_widget) => {
                let mut items = Vec::new();
                items.push(WidgetItemViewModel::Section {
                    id: format!("{}-section", id_prefix),
                    text_content: TextContent::String(
                        contact_options_widget
                            .widget_payload
                            .data
                            .title
                            .to_plain_text(),
                    ),
                });
                let contact_options = contact_options_widget
                    .widget_payload
                    .data
                    .contact_options
                    .into_iter()
                    .enumerate();
                for (cidx, contact_option) in contact_options {
                    let id_prefix = format!("{}-contact-{}", id_prefix, cidx);
                    items.extend(contact_option.into_view_model(&id_prefix, context));
                }
                items
            }
        }
    }
}

#[Composer]
pub fn WidgetItem(ctx: &AppContext, item: WidgetItemViewModel) -> ColumnComposable {
    match item {
        WidgetItemViewModel::Section { text_content, .. } => {
            let title_type_ramp = TypeRamp::from(FableText::TYPE_HEADING400);
            compose! {
                Column() {
                    Typography(content: text_content, type_ramp: title_type_ramp)
                        .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_SECTION)
                }
                .test_id(CUSTOMER_SERVICE_WIDGET_ITEM)
                .padding(Padding::new(0.0, 0.0, 24.0, 24.0))
            }
        }
        WidgetItemViewModel::Title { text_content, .. } => {
            let title_type_ramp = TypeRamp::from(FableText::TYPE_HEADING100);
            compose! {
                Column() {
                    Typography(content: text_content, type_ramp: title_type_ramp)
                        .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_TITLE)
                }
                .test_id(CUSTOMER_SERVICE_WIDGET_ITEM)
                .padding(Padding::new(0.0, 0.0, 0.0, 12.0))
            }
        }
        WidgetItemViewModel::Description { text_content, .. } => {
            let description_type_ramp = TypeRamp::from(FableText::TYPE_LABEL500);
            compose! {
                Column() {
                    Typography(content: text_content, type_ramp: description_type_ramp)
                        .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_DESCRIPTION)
                }
                .test_id(CUSTOMER_SERVICE_WIDGET_ITEM)
                .padding(Padding::new(0.0, 0.0, 0.0, 12.0))
            }
        }
        WidgetItemViewModel::Image {
            image_data,
            accessibility_label,
            ..
        } => {
            compose! {
                Column() {
                    Row() {
                        LRCImage(data: image_data)
                            .accessibility_hint(accessibility_label)
                            .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_IMAGE)
                    }
                    .main_axis_alignment(MainAxisAlignment::Center)
                    .main_axis_size(MainAxisSize::Max)
                }
                .test_id(CUSTOMER_SERVICE_WIDGET_ITEM)
                .padding(Padding::new(0.0, 0.0, 12.0, 12.0))
            }
        }
    }
}

#[cfg(test)]
mod test {
    use std::collections::HashMap;

    use common_transform_types::container_items::TNFLocalizedStringContents;
    use ignx_compositron::test_utils::assert_node_exists;
    use ignx_compositron::test_utils::node_properties::NodeTypeProperties;

    use super::*;
    use crate::network::types::widgets::customer_service::{
        ContactOptionsWidgetV1, ContactOptionsWidgetV1Data, ContactOptionsWidgetV1Payload,
        ContactType, CustomerServiceWidget, QuestionsAndAnswersWidgetV1,
        QuestionsAndAnswersWidgetV1Data, QuestionsAndAnswersWidgetV1Payload,
    };
    use crate::network::types::widgets::{Image, Text};

    fn create_test_q_and_a_widget() -> (CustomerServiceWidget, Vec<WidgetItemViewModel>) {
        let input =
            CustomerServiceWidget::QuestionsAndAnswersWidgetV1(QuestionsAndAnswersWidgetV1 {
                widget_payload: QuestionsAndAnswersWidgetV1Payload {
                    data: QuestionsAndAnswersWidgetV1Data {
                        title: Text {
                            string_literal: "Q&A Title".to_string(),
                            emphasis: Vec::new(),
                            arguments: HashMap::new(),
                        },
                        questions_and_answers: vec![QuestionAndAnswer {
                            id: "1".to_string(),
                            question: Text {
                                string_literal: "Test Question".to_string(),
                                emphasis: Vec::new(),
                                arguments: HashMap::new(),
                            },
                            answer: Text {
                                string_literal: "Test Answer".to_string(),
                                emphasis: Vec::new(),
                                arguments: HashMap::new(),
                            },
                        }],
                        see_more: None,
                    },
                },
            });
        let expected = vec![
            WidgetItemViewModel::Section {
                id: "test-prefix-1-section".to_string(),
                text_content: TextContent::String("Q&A Title".to_string()),
            },
            WidgetItemViewModel::Title {
                id: "test-prefix-1-q&a-0-question".to_string(),
                text_content: TextContent::String("Test Question".to_string()),
            },
            WidgetItemViewModel::Description {
                id: "test-prefix-1-q&a-0-answer".to_string(),
                text_content: TextContent::String("Test Answer".to_string()),
            },
        ];

        (input, expected)
    }

    fn create_test_contact_options_widget() -> (CustomerServiceWidget, Vec<WidgetItemViewModel>) {
        let input = CustomerServiceWidget::ContactOptionsWidgetV1(ContactOptionsWidgetV1 {
            widget_payload: ContactOptionsWidgetV1Payload {
                data: ContactOptionsWidgetV1Data {
                    title: Text {
                        string_literal: "Contact Options Title".to_string(),
                        emphasis: Vec::new(),
                        arguments: HashMap::new(),
                    },
                    contact_options: vec![ContactOption {
                        id: "1".to_string(),
                        title: Text {
                            string_literal: "Test Title".to_string(),
                            emphasis: Vec::new(),
                            arguments: HashMap::new(),
                        },
                        description: Text {
                            string_literal: "Test Description".to_string(),
                            emphasis: Vec::new(),
                            arguments: HashMap::new(),
                        },
                        button: None,
                        contact_type: ContactType::Chat,
                        qr_code: Some(Image {
                            uri: "https://test-qr-code".to_string(),
                            accessibility_label: "test label".to_string(),
                        }),
                        footer: None,
                    }],
                },
            },
        });
        let expected = vec![
            WidgetItemViewModel::Section {
                id: "test-prefix-0-section".to_string(),
                text_content: TextContent::String("Contact Options Title".to_string()),
            },
            WidgetItemViewModel::Title {
                id: "test-prefix-0-contact-0-title".to_string(),
                text_content: TextContent::String("Test Title".to_string()),
            },
            WidgetItemViewModel::Description {
                id: "test-prefix-0-contact-0-description".to_string(),
                text_content: TextContent::String("Test Description".to_string()),
            },
            WidgetItemViewModel::Image {
                id: "test-prefix-0-contact-0-qr-code".to_string(),
                image_data: ImageData {
                    url: "https://test-qr-code".to_string(),
                    width: 342.0,
                    height: 342.0,
                    tags: vec![ImageTag::Scaling(ScalingStrategy::UpscaleToCover)],
                },
                accessibility_label: TextContent::String("test label".to_string()),
            },
        ];

        (input, expected)
    }

    fn create_test_contact_options_widget_with_tnf_help_info() -> (
        CustomerServiceWidget,
        WidgetViewModelContext,
        Vec<WidgetItemViewModel>,
    ) {
        let (input, mut expected) = create_test_contact_options_widget();
        let context = WidgetViewModelContext {
            tnf_property: Some(TNFProperties {
                icid: None,
                customerIntent: None,
                customerOptInSuccessful: None,
                localizedStringContents: Some(TNFLocalizedStringContents {
                    helpHubString: Some("Test Help String".to_string()),
                    helpHubUrl: Some("https://test.help.url".to_string()),
                }),
            }),
        };
        expected.extend([
            WidgetItemViewModel::Title {
                id: "test-prefix-0-contact-0-tnf-help-info".to_string(),
                text_content: TextContent::String("Test Help String".to_string()),
            },
            WidgetItemViewModel::Description {
                id: "test-prefix-0-contact-0-tnf-help-url".to_string(),
                text_content: TextContent::String("https://test.help.url".to_string()),
            },
        ]);
        (input, context, expected)
    }

    #[test]
    fn should_convert_widgets() {
        let (q_and_a_widget, q_and_a_expected) = create_test_q_and_a_widget();
        let (contact_options_widget, contact_options_expected) =
            create_test_contact_options_widget();

        let widgets = vec![contact_options_widget, q_and_a_widget];
        let view_models = widgets
            .into_iter()
            .enumerate()
            .flat_map(|(idx, widget)| {
                widget.into_view_model(
                    &format!("test-prefix-{}", idx),
                    &WidgetViewModelContext { tnf_property: None },
                )
            })
            .collect::<Vec<_>>();
        assert_eq!(
            view_models,
            [contact_options_expected, q_and_a_expected].concat()
        );
    }

    #[test]
    fn should_sort_contact_options_first() {
        let (q_and_a_widget, q_and_a_expected) = create_test_q_and_a_widget();
        let (contact_options_widget, contact_options_expected) =
            create_test_contact_options_widget();
        let widgets = vec![q_and_a_widget, contact_options_widget];

        let view_models = widgets.into_view_model(
            "test-prefix",
            &WidgetViewModelContext { tnf_property: None },
        );

        assert_eq!(
            view_models,
            [contact_options_expected, q_and_a_expected].concat()
        );
    }

    #[test]
    fn should_emit_tnf_help_info() {
        let (contact_options_widget, context, contact_options_expected) =
            create_test_contact_options_widget_with_tnf_help_info();

        let view_models = contact_options_widget.into_view_model("test-prefix-0", &context);

        assert_eq!(view_models, contact_options_expected);
    }

    #[test]
    fn should_render_section() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let section_item = WidgetItemViewModel::Section {
                    id: "test-id".to_string(),
                    text_content: TextContent::String("Test Section".to_string()),
                };
                compose! {
                    WidgetItem(item: section_item)
                }
            },
            |_, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let item = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_ITEM);
                assert_node_exists!(&item);
                let mut section = item
                    .find_child_with()
                    .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_SECTION)
                    .find_all();
                let section = section.remove(0);
                assert_node_exists!(&section);
                let section_text = section.get_props().text;
                assert_eq!(section_text, Some("Test Section".to_string()));
            },
        );
    }

    #[test]
    fn should_render_title() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let title_item = WidgetItemViewModel::Title {
                    id: "test-id".to_string(),
                    text_content: TextContent::String("Test Title".to_string()),
                };
                compose! {
                    WidgetItem(item: title_item)
                }
            },
            |_, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let item = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_ITEM);
                assert_node_exists!(&item);
                let mut title = item
                    .find_child_with()
                    .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_TITLE)
                    .find_all();
                let title = title.remove(0);
                assert_node_exists!(&title);
                let title_text = title.get_props().text;
                assert_eq!(title_text, Some("Test Title".to_string()));
            },
        );
    }

    #[test]
    fn should_render_description() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let description_item = WidgetItemViewModel::Description {
                    id: "test-id".to_string(),
                    text_content: TextContent::String("Test Description".to_string()),
                };
                compose! {
                    WidgetItem(item: description_item)
                }
            },
            |_, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let item = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_ITEM);
                assert_node_exists!(&item);
                let mut description = item
                    .find_child_with()
                    .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_DESCRIPTION)
                    .find_all();
                let description = description.remove(0);
                assert_node_exists!(&description);
                let description_text = description.get_props().text;
                assert_eq!(description_text, Some("Test Description".to_string()));
            },
        );
    }

    #[test]
    fn should_render_image() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let image_item = WidgetItemViewModel::Image {
                    id: "test-id".to_string(),
                    image_data: ImageData {
                        url: "https://test-qr-code/image.png".to_string(),
                        width: 342.0,
                        height: 342.0,
                        tags: vec![ImageTag::Scaling(ScalingStrategy::UpscaleToCover)],
                    },
                    accessibility_label: TextContent::String("test label".to_string()),
                };
                compose! {
                    WidgetItem(item: image_item)
                }
            },
            |_, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let item = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_ITEM);
                assert_node_exists!(&item);
                let mut image = item
                    .find_any_child_with()
                    .test_id(CUSTOMER_SERVICE_WIDGET_ITEM_IMAGE)
                    .find_all();
                let image = image.remove(0);
                assert_node_exists!(&image);
                let general_props = image.borrow_props();
                let image_props = match &general_props.node_type_props {
                    NodeTypeProperties::Image(image) => image,
                    _ => panic!("Expected image"),
                };

                assert_eq!(
                    image_props.uri,
                    Some("https://test-qr-code/image._UC342,342_FMjpg_DB,0_.png".to_string())
                );

                assert_eq!(
                    general_props
                        .accessibility
                        .as_ref()
                        .map(|accessibility_component| accessibility_component.get_hints()),
                    Some(vec!["test label".to_string()])
                );
            },
        );
    }
}
