use crate::modules::localized_text::DetailsString;
use crate::ui::test_ids::{
    CONTENT_UNAVAILABLE_DETAILS_OK_BUTTON_TEST_ID, CONTENT_UNAVAILABLE_DETAILS_TEST_ID,
};
use amzn_fable_tokens::FableColor;
use fableous::buttons::primary_button::*;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

const BUTTON_WIDTH: f32 = 196.0;

#[Composer]
pub fn ContentUnavailableModal<T>(ctx: &AppContext, okay_cb: T) -> StackComposable
where
    T: Fn() + 'static,
{
    let localized_text =
        DetailsString::AV_LRC_DETAILS_PAGE_BROADCAST_NOT_AVAILABLE.get_localised_text();

    compose! {

        Stack() {
            Column() {
                TypographyHeading600(content: localized_text.clone())
                    .padding(Padding::new(0.0, 0.0, 0.0, 48.0))
                PrimaryButton(variant: PrimaryButtonVariant::TextSize400(DetailsString::AV_LRC_OK_BUTTON_TEXT.get_localised_text()))
                    .on_select(okay_cb)
                    .test_id(CONTENT_UNAVAILABLE_DETAILS_OK_BUTTON_TEST_ID)
                    .width(BUTTON_WIDTH)
                    .main_axis_alignment(MainAxisAlignment::Center)
            }
            .padding(Padding::new(144.0, 0.0, 0.0, 0.0))
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .main_axis_alignment(MainAxisAlignment::Center)
            .test_id(CONTENT_UNAVAILABLE_DETAILS_TEST_ID)
        }
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
        .background_color(get_ignx_color(FableColor::BACKGROUND))
        .accessibility_context_message(localized_text)
    }
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn should_invoke_okay_callback_when_pressed() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let pressed_sig = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), pressed_sig);
                let okay_cb = move || {
                    pressed_sig.set(true);
                };

                compose! {
                    ContentUnavailableModal(okay_cb)
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let okay_button =
                    node_tree.find_by_test_id(CONTENT_UNAVAILABLE_DETAILS_OK_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(okay_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let pressed_sig =
                    use_context::<RwSignal<bool>>(scope).expect("pressed_sig to exist");
                assert!(pressed_sig.get_untracked());
            },
        )
    }
}
