use core::f32;
use std::rc::Rc;

use crate::context::hooks::{create_or_use_details_page_context, empty_stack_fallback};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::metrics::metric_reporter::types::DimensionValues;
use crate::modules::localized_text::DetailsString;
use crate::modules::title_details::convert_details_layout_to_languages_and_more;
use crate::network::types::atf::DetailsPageATFResponse;
use crate::network::types::widgets::customer_service::CustomerServiceWidget;
use crate::ui::error_overlay::set_details_standard_error_state;
use crate::ui::test_ids::{
    CUSTOMER_SERVICE_MODAL_CONTAINER, CUSTOMER_SERVICE_OK_BUTTON, CUSTOMER_SERVICE_WIDGET_LIST,
};
use clickstream::client::{use_clickstream_client, CrossAppEventKeys};
use containers::utils::tts_utils::tts_details_data_to_details_language_and_more_metadata_description;
use details_derive::{
    get_some_or_return_with_error_message, get_some_or_return_with_val_and_error_message,
};
use fableous::buttons::primary_button::*;
use fableous::pointer_control::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::input::KeyCode;
use ignx_compositron::prelude::*;
use ignx_compositron::show::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, Composer};
use media_background::types::MediaBackgroundType;
use serde::Serialize;
use title_details::core::{
    title_details_data_to_tts_details_data, TitleDetailsChangeRequest, TitleDetailsData,
};
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;
mod view_model;
use view_model::*;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("customer_service_modal");
const BUTTON_BOTTOM: f32 = 136.0;
const RIGHT_CONTAINER_TOP: f32 = 154.0;
const CONTAINERS_SPACING: f32 = 96.0;
const LEFT_CONTAINER_WIDTH: f32 = 798.0;
const RIGHT_CONTAINER_WIDTH: f32 = 810.0;

fn get_customer_service_widget_items(
    details_response: Option<DetailsPageATFResponse>,
) -> Vec<CustomerServiceWidget> {
    let response = get_some_or_return_with_val_and_error_message!(
        details_response,
        "Unable to get atf response",
        vec![]
    );

    let customer_service_section = response
        .widget_sections
        .as_ref()
        .and_then(|sections| sections.customer_service.as_ref());

    let customer_service_section = get_some_or_return_with_val_and_error_message!(
        customer_service_section,
        "Unable to get customer service section",
        vec![]
    );

    customer_service_section.widgets.clone()
}

fn build_tts_from_title_details(title_details_data: TitleDetailsData) -> Vec<TextContent> {
    let Some(tts_details_data) = title_details_data_to_tts_details_data(&title_details_data) else {
        return Vec::new();
    };
    let mut tts_descriptions = vec![TextContent::String(tts_details_data.title.clone())];
    tts_descriptions.extend(
        tts_details_data_to_details_language_and_more_metadata_description(
            tts_details_data,
            match title_details_data {
                TitleDetailsData::DetailsLanguagesAndMoreLayout(data) if data.imdb_rating > 0.0 => {
                    data.imdb_rating.to_string()
                }
                _ => String::new(),
            },
        ),
    );
    tts_descriptions
}

fn build_tts_from_widget_items(widget_items: Vec<WidgetItemViewModel>) -> Vec<TextContent> {
    let mut tts_descriptions = Vec::new();

    tts_descriptions.extend(widget_items.iter().map(|item| match item {
        WidgetItemViewModel::Section { text_content, .. } => text_content.clone(),
        WidgetItemViewModel::Title { text_content, .. } => text_content.clone(),
        WidgetItemViewModel::Description { text_content, .. } => text_content.clone(),
        WidgetItemViewModel::Image {
            accessibility_label,
            ..
        } => accessibility_label.clone(),
    }));

    tts_descriptions
}

const POINTER_CONTROL_UP_CARET: &str = "POINTER_CONTROL_UP_CARET";
const POINTER_CONTROL_DOWN_CARET: &str = "POINTER_CONTROL_DOWN_CARET";

#[Composer]
fn CustomerServicePointerOverlay(
    ctx: &AppContext,
    items_length: u32,
    focused_index: RwSignal<ScrollTo>,
    on_navigate: Rc<dyn Fn(i32)>,
) -> ShowComposable {
    let pointer_control_active = is_pointer_control_active(ctx.scope());

    let show_builder: Box<ConditionalComposableBuilder> = Box::new({
        move |ctx| {
            let should_show_up_pointer = Signal::derive(ctx.scope(), move || {
                let focused_index = get_some_or_return_with_val_and_error_message!(
                    focused_index.try_get(),
                    "unable to get focused index",
                    false
                );
                match focused_index {
                    ScrollTo::Index(idx, _, _) => idx > 0,
                    ScrollTo::Offset(_, _, _) => false,
                }
            });

            let should_show_down_pointer = Signal::derive(ctx.scope(), move || {
                let focused_index = get_some_or_return_with_val_and_error_message!(
                    focused_index.try_get(),
                    "unable to get focused index",
                    false
                );
                match focused_index {
                    ScrollTo::Index(idx, _, _) => idx < items_length.saturating_add_signed(-1),
                    ScrollTo::Offset(_, _, _) => false,
                }
            });

            let pointer_down_handler = Rc::new({
                let on_navigate = on_navigate.clone();
                move || {
                    on_navigate(1);
                }
            });
            let pointer_up_handler = Rc::new({
                let on_navigate = on_navigate.clone();
                move || {
                    on_navigate(-1);
                }
            });

            let component = compose! {
                Column() {
                    PointerControlCaret(direction: Direction::Up, on_select: pointer_up_handler.clone(), visible: should_show_up_pointer)
                    .test_id(POINTER_CONTROL_UP_CARET)
                    PointerControlCaret(direction: Direction::Down, on_select: pointer_down_handler.clone(), visible: should_show_down_pointer)
                    .test_id(POINTER_CONTROL_DOWN_CARET)

                }
                .width(SCREEN_WIDTH)
                .height(SCREEN_HEIGHT)
                .cross_axis_alignment(CrossAxisAlignment::Center)
                .main_axis_alignment(MainAxisAlignment::SpaceBetween)
            };

            component.into_widget()
        }
    });

    compose! {
        Show(if_builder: show_builder, condition: pointer_control_active, else_builder: None)
    }
}

const CLICKSTREAM_SOURCE: &str = "DETAILS_PAGE";
const CUSTOMER_SERVICE_WIDGET_MODAL_CLOSE: &str = "CUSTOMER_SERVICE_WIDGET_MODAL_CLOSE";

#[derive(Serialize)]
struct CustomerServiceWidgetCloseEvent;

impl CrossAppEventKeys for CustomerServiceWidgetCloseEvent {
    fn source(&self) -> &'static str {
        CLICKSTREAM_SOURCE
    }

    fn name(&self) -> &'static str {
        CUSTOMER_SERVICE_WIDGET_MODAL_CLOSE
    }
}

#[Composer]
pub fn CustomerServiceModal<T>(ctx: &AppContext, okay_cb: T) -> StackComposable
where
    T: Fn() + 'static,
{
    let focused_index = create_rw_signal(
        ctx.scope(),
        ScrollTo::Index(0, Pivot::Start, Animation::default()),
    );

    let details_ctx = match create_or_use_details_page_context(ctx) {
        None => {
            set_details_standard_error_state(ctx.scope());
            return empty_stack_fallback(ctx);
        }
        Some(ctx) => ctx,
    };
    let response = details_ctx.borrow().state.page_resources.atf_response;
    let atf_state_binding_on_change = details_ctx.borrow().state.state_binding_atf_on_change;
    let media_background = details_ctx.borrow().state.media_background;
    let title_details_atf = details_ctx.borrow().state.title_details.atf;
    let title_details_global = details_ctx.borrow().state.title_details.global;
    let title_details_global_opacity = details_ctx.borrow().state.title_details.global_opacity;

    let items = Signal::derive(ctx.scope(), move || {
        get_customer_service_widget_items(response.get())
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            atf_state_binding_on_change.track();
            let response = get_some_or_return_with_error_message!(
                response.try_get().flatten(),
                "Failed to get atf response"
            );
            let title_details_atf = get_some_or_return_with_error_message!(
                title_details_atf.try_get_untracked(),
                "Failed to get title details atf"
            );

            media_background.set(MediaBackgroundType::None);

            if let Some(new_title_details) =
                convert_details_layout_to_languages_and_more(&response, title_details_atf)
            {
                title_details_global.set(TitleDetailsChangeRequest {
                    data: new_title_details,
                    navigation_direction: Default::default(),
                });
            }

            ctx.with_animation(
                Animation::default().with_delay(Duration::from_millis(200)),
                move || title_details_global_opacity.set(1.0),
            );
        }
    });

    let widget_items = Signal::derive(ctx.scope(), {
        let details_ctx = details_ctx.clone();
        move || {
            let context = WidgetViewModelContext {
                tnf_property: details_ctx.borrow().state.tnf_property.get(),
            };

            items.get().into_view_model("item", &context)
        }
    });

    let mut tts_descriptions = build_tts_from_title_details(title_details_atf.get_untracked());
    tts_descriptions.append(&mut build_tts_from_widget_items(
        widget_items.get_untracked(),
    ));

    let on_navigate = Rc::new(move |offset: i32| {
        let focused_index_value = get_some_or_return_with_error_message!(
            focused_index.try_get_untracked(),
            "Failed to get focused index"
        );
        let current_index = match focused_index_value {
            ScrollTo::Offset(_, _, _) => 0,
            ScrollTo::Index(idx, _, _) => idx as i32,
        };

        let new_index = current_index + offset;
        let items_len = widget_items.get_untracked().len() as i32;

        if new_index >= items_len || new_index < 0 {
            return;
        }

        focused_index.set(ScrollTo::Index(
            (new_index) as u32,
            Pivot::Start,
            Animation::default(),
        ));
    });

    let widget_item_builder = Rc::new(|ctx: &AppContext, item: &WidgetItemViewModel, _| {
        compose! {
            WidgetItem(item: item.clone())
        }
    });

    let on_close = {
        let details_ctx = details_ctx.clone();
        let ctx = ctx.clone();
        move || {
            let cs_client = use_clickstream_client(ctx.scope());
            cs_client.track(CustomerServiceWidgetCloseEvent);

            details_ctx
                .borrow()
                .metric_reporter
                .button_pressed(DimensionValues::CustomerServiceWidgetModalClose);
            okay_cb();
        }
    };

    compose! {
        Stack() {
            CustomerServicePointerOverlay(items_length: widget_items.with(|items| items.len() as u32), focused_index, on_navigate: on_navigate.clone())
            PrimaryButton(variant: PrimaryButtonVariant::TextSize400(DetailsString::AV_LRC_OK_BUTTON_TEXT.get_localised_text()))
                .on_select(on_close)
                .test_id(CUSTOMER_SERVICE_OK_BUTTON)
                .accessibility_context_messages(tts_descriptions)
                .accessibility_closing_context_message(DetailsString::AV_LRC_PRESS_SELECT_TO_EXIT.get_localised_text())
                .translate_x(TITLE_DETAILS_LEFT_OFFSET)
                .translate_y(SCREEN_HEIGHT - BUTTON_BOTTOM)
                .pointer_control_target(None)
                .z_index(2)
            ColumnList(items: widget_items, item_builder: widget_item_builder)
                .overflow_behavior(OverflowBehavior::Visible)
                .scroll_to(focused_index)
                .width(RIGHT_CONTAINER_WIDTH)
                .translate_x(LEFT_CONTAINER_WIDTH + CONTAINERS_SPACING + TITLE_DETAILS_LEFT_OFFSET)
                .translate_y(RIGHT_CONTAINER_TOP)
                .test_id(CUSTOMER_SERVICE_WIDGET_LIST)
                .z_index(1)
        }
        .on_key_down(KeyCode::Down,  {
            let on_navigate = on_navigate.clone();
            move || on_navigate(1)
        })
        .on_key_down(KeyCode::Up, move || {
            on_navigate(-1)
        })
        .size(Vec2::new(SCREEN_WIDTH, SCREEN_HEIGHT))
        .test_id(CUSTOMER_SERVICE_MODAL_CONTAINER)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_live_details_page_context_test, use_details_page_context_test,
    };
    use crate::metrics::metric_reporter::traits::MockDetailsReporter;
    use crate::ui::test_ids::CUSTOMER_SERVICE_WIDGET_ITEM;
    use clickstream::client::CrossAppClickstreamEventPayload;
    use clickstream::test_utils::{provide_clickstream_client_spy, use_clickstream_emitter_spy};
    use common_transform_types::container_items::{TNFLocalizedStringContents, TNFProperties};
    use fableous::animations::{FableMotionDuration, MotionDuration};
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::pointer_control::PointerControlActiveContext;
    use ignx_compositron::test_utils::assert_node_exists;
    use lrc_image::types::ImageData;
    use mockall::predicate::eq;
    use rust_features::MockRustFeaturesBuilder;
    use title_details::core::TitleDetailsData;

    #[test]
    fn should_update_media_background_to_fullscreen_background_image() {
        launch_test(
            |ctx| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build_as_mock_and_real_into_context(false, ctx.scope());
                create_or_use_live_details_page_context_test(&ctx);

                compose! {
                    CustomerServiceModal(okay_cb: || {})
                }
            },
            |scope, mut test_game_loop| {
                let details_ctx = use_details_page_context_test(scope);
                let _ = test_game_loop.tick_until_done();

                assert_eq!(
                    details_ctx.borrow().state.media_background.get_untracked(),
                    MediaBackgroundType::None
                );

                let title_details = details_ctx
                    .borrow()
                    .state
                    .title_details
                    .global
                    .get_untracked();
                match title_details.data {
                    TitleDetailsData::DetailsLanguagesAndMoreLayout(_) => {}
                    _ => panic!("Unexpected title details type"),
                }
            },
        )
    }

    #[test]
    fn should_render_correctly() {
        launch_test(
            |ctx| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build_as_mock_and_real_into_context(false, ctx.scope());
                let _details_context = create_or_use_live_details_page_context_test(&ctx);

                compose! {
                    CustomerServiceModal(okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let ok_button = node_tree.find_by_test_id(CUSTOMER_SERVICE_OK_BUTTON);
                assert_node_exists!(ok_button);

                let widget_list = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_LIST);
                assert_node_exists!(&widget_list);
                let widget_items = widget_list
                    .find_any_child_with()
                    .test_id(CUSTOMER_SERVICE_WIDGET_ITEM)
                    .find_all();

                // ColumnList doesn't render off-screen nodes, so we don't know exactly how many will be rendered.
                // but reasonably there should be more than one. We also have tests in the view module covering the
                // conversion of widgets from the response to WidgetItemViewModels, as well as the rendering
                // of individual WidgetItems, so this should be sufficient.
                assert!(widget_items.len() > 1, "Expected more than 1 widget item");
            },
        );
    }

    #[test]
    fn should_render_tnf_help_info_correctly() {
        launch_test(
            |ctx| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build_as_mock_and_real_into_context(false, ctx.scope());
                let details_context = create_or_use_live_details_page_context_test(&ctx);

                details_context
                    .borrow_mut()
                    .state
                    .tnf_property
                    .set(Some(TNFProperties {
                        icid: None,
                        customerIntent: None,
                        customerOptInSuccessful: None,
                        localizedStringContents: Some(TNFLocalizedStringContents {
                            helpHubString: Some("Test Help String".to_string()),
                            helpHubUrl: Some("https://test.help.url".to_string()),
                        }),
                    }));

                compose! {
                    CustomerServiceModal(okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let ok_button = node_tree.find_by_test_id(CUSTOMER_SERVICE_OK_BUTTON);
                assert_node_exists!(ok_button);

                let widget_list = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_LIST);
                assert_node_exists!(&widget_list);

                // this should be safe, because the TNF help info is rendered with the contact options
                // and they are rendered first, so they should be above the scroll
                let tnf_help_string_nodes = widget_list
                    .find_any_child_with()
                    .text("Test Help String")
                    .find_all();

                assert!(
                    tnf_help_string_nodes.len() == 1,
                    "Expected 1 Tnf Help String to be rendered"
                );

                let tnf_help_url_nodes = widget_list
                    .find_any_child_with()
                    .text("https://test.help.url")
                    .find_all();

                assert!(
                    tnf_help_url_nodes.len() == 1,
                    "Expected 1 Tnf Help Url to be rendered"
                );
            },
        );
    }

    #[test]
    fn should_scroll_correctly() {
        const EXPECTED_WIDGET_ITEMS: i32 = 20;

        launch_test(
            |ctx| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build_as_mock_and_real_into_context(false, ctx.scope());
                let _details_context = create_or_use_live_details_page_context_test(&ctx);

                compose! {
                    CustomerServiceModal(okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let widget_list = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_LIST);
                assert_node_exists!(&widget_list);
                // Scroll all the way down to make sure that
                // a) scrolling works
                // b) over-scrolling beyond the end of the list doesn't blow up
                // c) the last widget item is fully visible when we get to the bottom
                for _ in 0..EXPECTED_WIDGET_ITEMS {
                    test_game_loop.send_key_down_up_event(KeyCode::Down);
                    let _ = test_game_loop.tick_until_done();
                }

                let node_tree = test_game_loop.tick_until_done();
                let widget_list = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_LIST);
                assert_node_exists!(&widget_list);
                let widget_items = widget_list
                    .find_any_child_with()
                    .test_id(CUSTOMER_SERVICE_WIDGET_ITEM)
                    .find_all();
                let Some(last_item) = widget_items.last() else {
                    panic!("Expected last item to exist");
                };
                assert_node_exists!(last_item);
                assert_eq!(last_item.borrow_props().on_screen_amount, 1.0);

                for _ in 0..EXPECTED_WIDGET_ITEMS {
                    test_game_loop.send_key_down_up_event(KeyCode::Up);
                }
                let node_tree = test_game_loop.tick_until_done();
                let widget_list = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_LIST);
                assert_node_exists!(&widget_list);
                let first_node = widget_list
                    .find_any_child_with()
                    .test_id(CUSTOMER_SERVICE_WIDGET_ITEM)
                    .find_first();

                assert_node_exists!(&first_node);
                assert_eq!(first_node.borrow_props().on_screen_amount, 1.0);
            },
        );
    }

    #[test]
    fn should_build_tts_descriptions_from_widgets() {
        let widget_items = vec![
            WidgetItemViewModel::Section {
                id: Default::default(),
                text_content: TextContent::String("Test Section".to_string()),
            },
            WidgetItemViewModel::Title {
                id: Default::default(),
                text_content: TextContent::String("Test Title".to_string()),
            },
            WidgetItemViewModel::Description {
                id: Default::default(),
                text_content: TextContent::String("Test Description".to_string()),
            },
            WidgetItemViewModel::Image {
                id: Default::default(),
                image_data: ImageData {
                    url: "https://test.image.url".to_string(),
                    width: 100.0,
                    height: 100.0,
                    tags: vec![],
                },
                accessibility_label: TextContent::String("Test Image Label".to_string()),
            },
        ];

        let expected_tts_descriptions = vec![
            TextContent::String("Test Section".to_string()),
            TextContent::String("Test Title".to_string()),
            TextContent::String("Test Description".to_string()),
            TextContent::String("Test Image Label".to_string()),
        ];

        let tts_descriptions = build_tts_from_widget_items(widget_items);
        assert_eq!(tts_descriptions, expected_tts_descriptions);
    }

    #[test]
    fn pointer_control_overlay_should_render_correctly() {
        let expected_length = 5 as u32;
        launch_test(
            move |ctx| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build_as_mock_and_real_into_context(true, ctx.scope());
                let pointer_control_active_context =
                    PointerControlActiveContext(create_rw_signal(ctx.scope(), true).read_only());
                provide_context(ctx.scope(), pointer_control_active_context);

                let focused_index = create_rw_signal(
                    ctx.scope(),
                    ScrollTo::Index(0, Pivot::Start, Animation::default()),
                );

                provide_context(ctx.scope(), focused_index);

                let on_navigate = Rc::new(|_: i32| {});
                compose! {
                    Stack() {
                        CustomerServicePointerOverlay(items_length: expected_length, focused_index: focused_index, on_navigate: on_navigate)
                    }
                }
            },
            move |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let focused_index = use_context::<RwSignal<ScrollTo>>(scope)
                    .expect("Expected focused index to be provided");

                let up_caret = node_tree.find_by_test_id(POINTER_CONTROL_UP_CARET);
                let up_caret_icon = up_caret
                    .find_any_child_with()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_first();
                assert_node_exists!(&up_caret_icon);
                assert!(
                    up_caret_icon.borrow_props().base_styles.opacity == Some(0.0),
                    "Up caret should not be visible"
                );
                let down_caret = node_tree.find_by_test_id(POINTER_CONTROL_DOWN_CARET);
                let down_caret_icon = down_caret
                    .find_any_child_with()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_first();
                assert_node_exists!(&down_caret_icon);
                assert!(
                    down_caret_icon.borrow_props().base_styles.opacity == Some(1.0),
                    "Down caret should be visible"
                );

                // after moving down once, both should be visible.

                focused_index.set(ScrollTo::Index(1, Pivot::Start, Animation::default()));
                let _ = test_game_loop.tick_until_done();
                test_game_loop.advance_time(FableMotionDuration::Medium.to_duration());
                let node_tree = test_game_loop.tick_until_done();

                let up_caret = node_tree.find_by_test_id(POINTER_CONTROL_UP_CARET);
                let up_caret_icon = up_caret
                    .find_any_child_with()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_first();
                assert_node_exists!(&up_caret_icon);
                assert!(
                    up_caret_icon.borrow_props().base_styles.opacity == Some(1.0),
                    "Up caret should be visible"
                );

                let down_caret = node_tree.find_by_test_id(POINTER_CONTROL_DOWN_CARET);
                let down_caret_icon = down_caret
                    .find_any_child_with()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_first();
                assert_node_exists!(&down_caret_icon);
                assert!(
                    down_caret_icon.borrow_props().base_styles.opacity == Some(1.0),
                    "Down caret should be visible"
                );

                // after moving to the bottom, only up should be visible
                focused_index.set(ScrollTo::Index(
                    expected_length.saturating_add_signed(-1),
                    Pivot::Start,
                    Animation::default(),
                ));
                let _ = test_game_loop.tick_until_done();
                test_game_loop.advance_time(FableMotionDuration::Medium.to_duration());
                let node_tree = test_game_loop.tick_until_done();

                let up_caret = node_tree.find_by_test_id(POINTER_CONTROL_UP_CARET);
                let up_caret_icon = up_caret
                    .find_any_child_with()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_first();
                assert_node_exists!(&up_caret_icon);
                assert!(
                    up_caret_icon.borrow_props().base_styles.opacity == Some(1.0),
                    "Up caret should be visible"
                );
                let down_caret = node_tree.find_by_test_id(POINTER_CONTROL_DOWN_CARET);
                let down_caret_icon = down_caret
                    .find_any_child_with()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_first();
                assert_node_exists!(&down_caret_icon);
                assert!(
                    down_caret_icon.borrow_props().base_styles.opacity == Some(0.0),
                    "Down caret should not be visible"
                );
            },
        );
    }

    #[test]
    fn should_emit_metrics_on_close() {
        launch_test(
            |ctx| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_live_details_page_enabled(true)
                    .build_as_mock_and_real_into_context(false, ctx.scope());
                let details_ctx = create_or_use_live_details_page_context_test(&ctx);
                provide_clickstream_client_spy(ctx.scope());

                let mut metric_reporter = MockDetailsReporter::default();
                metric_reporter
                    .expect_button_pressed()
                    .times(1)
                    .with(eq(DimensionValues::CustomerServiceWidgetModalClose))
                    .return_const(());
                details_ctx.borrow_mut().metric_reporter = Rc::new(metric_reporter);

                compose! {
                    CustomerServiceModal(okay_cb: || {})
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let widget_list = node_tree.find_by_test_id(CUSTOMER_SERVICE_WIDGET_LIST);
                assert_node_exists!(&widget_list);

                let spy = use_clickstream_emitter_spy(scope);

                let ok_button = node_tree.find_by_test_id(CUSTOMER_SERVICE_OK_BUTTON);
                assert_node_exists!(&ok_button);
                test_game_loop.send_on_select_event(ok_button.borrow_props().node_id);
                let _ = test_game_loop.tick_until_done();

                let events = spy.recorded_events();

                assert_eq!(
                    events,
                    vec![CrossAppClickstreamEventPayload {
                        source: CLICKSTREAM_SOURCE,
                        name: CUSTOMER_SERVICE_WIDGET_MODAL_CLOSE,
                        params: Default::default()
                    }]
                )
            },
        );
    }
}
