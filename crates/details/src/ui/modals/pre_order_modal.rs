use crate::modules::localized_text::DetailsString;
use crate::ui::test_ids::{
    PRE_ORDER_DETAILS_OK_BUTTON_TEST_ID, PRE_ORDER_DETAILS_PARAGRAPH_TEST_ID,
    PRE_ORDER_DETAILS_TEST_ID,
};
use amzn_fable_tokens::{FableColor, FableText};
use common_transform_types::actions::{TitleActionNestedChild, TitleActionPresentation};
use fableous::buttons::primary_button::*;
use fableous::typography::style_hints_typography::*;
use fableous::typography::type_ramp::TypeRamp;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::id::Id;
use ignx_compositron::layout::MainAxisAlignment::SpacedBy;
use ignx_compositron::list::ItemBuilderFn;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};
use std::rc::Rc;

//FIXME: Clickstream actions for opening?
#[derive(Clone)]
struct TextBlock {
    pub content: String,
}

fn split_data_by_double_line_breaks(actions: Vec<String>) -> Vec<TextBlock> {
    actions
        .iter()
        .flat_map(|item| {
            let split_str: Vec<&str> = item.split("{lineBreak}{lineBreak}").collect();
            split_str
        })
        .map(|item| TextBlock {
            content: item.to_string(),
        })
        .collect()
}

impl Id for TextBlock {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.content
    }
}

#[Composer]
pub fn PreOrderDetailsModal<T>(
    ctx: &AppContext,
    action: TitleActionNestedChild,
    okay_cb: T,
) -> StackComposable
where
    T: Fn() + 'static,
{
    let header_action = action
        .childActions
        .iter()
        .find(|x| x.titleAction.presentation == TitleActionPresentation::ModalSectionHeader);
    let body_actions = action
        .childActions
        .iter()
        .filter(|x| x.titleAction.presentation == TitleActionPresentation::ModalSectionBody)
        .map(|x| x.titleAction.label.clone())
        .collect::<Vec<String>>();

    let header_text = match header_action {
        None => "Thanks for pre-ordering".to_string(),
        Some(action) => action.titleAction.label.clone(),
    };

    let text_blocks = split_data_by_double_line_breaks(body_actions);

    let text_block_builder: ItemBuilderFn<TextBlock, StackComposable> = Rc::new(
        |ctx, item, _idx| {
            let ramp = TypeRamp::from(FableText::TYPE_BODY200_EMPHASIS);
            compose! {
                Stack() {
                    StyleHintsTypography(type_ramp: ramp, content: TextContent::String(item.content.clone()))
                }.test_id(PRE_ORDER_DETAILS_PARAGRAPH_TEST_ID)
            }
        },
    );

    compose! {
        Stack() {
            Column() {
                TypographyHeading600(content: TextContent::String(header_text))
                ColumnList(items: text_blocks, item_builder: text_block_builder)
                    .main_axis_alignment(SpacedBy(20.0))
                    .padding(Padding::new(0.0, 0.0, 0.0, 30.0))
                PrimaryButton(variant: PrimaryButtonVariant::TextSize400(DetailsString::AV_LRC_OK_BUTTON_TEXT.get_localised_text()))
                    .on_select(okay_cb)
                    .test_id(PRE_ORDER_DETAILS_OK_BUTTON_TEST_ID)
            }
            .padding(Padding::new(144.0, 0.0, 0.0, 0.0))
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .main_axis_alignment(MainAxisAlignment::Center)
            .test_id(PRE_ORDER_DETAILS_TEST_ID)
        }
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
        .background_color(get_ignx_color(FableColor::BACKGROUND))
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use common_transform_types::actions::{
        BaseTitleActionMetadata, TitleAction, TitleActionMetadata,
    };

    fn test_title_action() -> TitleActionNestedChild {
        TitleActionNestedChild {
            childActions: vec![
                TitleActionNestedChild {
                    childActions: vec![],
                    titleAction: TitleAction {
                        label: "Thanks for pre-ordering".to_string(),
                        metadata: TitleActionMetadata::PreorderSuppressionSection(
                            BaseTitleActionMetadata {
                                refMarker: None,
                                metadataActionType: None,
                            },
                        ),
                        presentation: TitleActionPresentation::ModalSectionHeader,
                        entitlement_metadata: None,
                    },
                },
                TitleActionNestedChild {
                    childActions: vec![],
                    titleAction: TitleAction {
                        label: "Body1{lineBreak}{lineBreak}Body2".to_string(),
                        metadata: TitleActionMetadata::PreorderSuppressionSection(
                            BaseTitleActionMetadata {
                                refMarker: None,
                                metadataActionType: None,
                            },
                        ),
                        presentation: TitleActionPresentation::ModalSectionBody,
                        entitlement_metadata: None,
                    },
                },
                TitleActionNestedChild {
                    childActions: vec![],
                    titleAction: TitleAction {
                        label: "Body3".to_string(),
                        metadata: TitleActionMetadata::PreorderSuppressionSection(
                            BaseTitleActionMetadata {
                                refMarker: None,
                                metadataActionType: None,
                            },
                        ),
                        presentation: TitleActionPresentation::ModalSectionBody,
                        entitlement_metadata: None,
                    },
                },
                TitleActionNestedChild {
                    childActions: vec![],
                    titleAction: TitleAction {
                        label: "OK".to_string(),
                        metadata: TitleActionMetadata::PreorderSuppressionSection(
                            BaseTitleActionMetadata {
                                refMarker: None,
                                metadataActionType: None,
                            },
                        ),
                        presentation: TitleActionPresentation::ModalSectionButton,
                        entitlement_metadata: None,
                    },
                },
            ],
            titleAction: TitleAction {
                label: "Order Details".to_string(),
                metadata: TitleActionMetadata::PreorderSuppression(BaseTitleActionMetadata {
                    refMarker: None,
                    metadataActionType: None,
                }),
                presentation: TitleActionPresentation::Simple,
                entitlement_metadata: None,
            },
        }
    }

    #[test]
    fn should_invoke_cancel_callback_when_pressed() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let action = test_title_action();

                let pressed_sig = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), pressed_sig);
                let okay_cb = move || {
                    pressed_sig.set(true);
                };

                compose! {
                    PreOrderDetailsModal(action, okay_cb)
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let okay_button = node_tree.find_by_test_id(PRE_ORDER_DETAILS_OK_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(okay_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let pressed_sig =
                    use_context::<RwSignal<bool>>(scope).expect("pressed_sig to exist");
                assert!(pressed_sig.get_untracked());
            },
        )
    }

    #[test]
    fn should_render_with_correct_number_of_paragraphs() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let action = test_title_action();

                compose! {
                    PreOrderDetailsModal(action, okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let modal = node_tree.find_by_test_id(PRE_ORDER_DETAILS_TEST_ID);
                let paragraphs = modal
                    .find_any_child_with()
                    .test_id(PRE_ORDER_DETAILS_PARAGRAPH_TEST_ID)
                    .find_all();

                assert_eq!(paragraphs.len(), 3);
            },
        )
    }
}
