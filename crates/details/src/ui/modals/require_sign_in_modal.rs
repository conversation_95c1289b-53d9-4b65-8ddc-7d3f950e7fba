use crate::context::hooks::use_details_page_context;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::localized_text::DetailsString;
use crate::ui::test_ids::{
    REQUIRE_SIGN_IN_CANCEL_BUTTON_TEST_ID, REQUIRE_SIGN_IN_SIGN_IN_BUTTON_TEST_ID,
    REQUIRE_SIGN_IN_TEST_ID,
};
use amzn_fable_tokens::FableColor;
use details_derive::get_some_or_return_with_error_message;
use fableous::buttons::primary_button::*;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use location::{JSPage, Location, PageType};

const BUTTON_WIDTH: f32 = 600.0;
const CONTAINER_WIDTH: f32 = 750.0;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("require_sign_in_modal");

pub fn go_to_sign_in_page(ctx: &AppContext) {
    let details_context = get_some_or_return_with_error_message!(
        use_details_page_context(ctx.scope),
        "[go_to_sign_in_page] Failed to get details context"
    );

    details_context.borrow().navigate(
        Location {
            pageType: PageType::Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE),
            pageParams: Default::default(),
        },
        "DETAILS_PAGE_SIGN_IN_MODAL",
    );
}

#[Composer]
pub fn RequireSignInModal<T>(ctx: &AppContext, cancel_cb: T) -> StackComposable
where
    T: Fn() + 'static,
{
    let wrapped_sign_in_cb = {
        let ctx = ctx.clone();
        move || {
            go_to_sign_in_page(&ctx);
        }
    };

    compose! {
        Stack() {
            Column() {
                Row() {
                    Column() {
                        TypographyLabel600(content: DetailsString::AV_LRC_DETAILS_PAGE_SIGN_IN_TO_PURCHASE_POPUP_MESSAGE.get_localised_text())
                            .padding(Padding::new(0.0, 0.0, 30.0, 0.0))

                        Column() {
                            PrimaryButton(variant: PrimaryButtonVariant::TextSize200(DetailsString::AV_LRC_DETAILS_PAGE_SIGN_IN_TO_PURCHASE_POPUP_HEADING.get_localised_text()))
                                .main_axis_alignment(MainAxisAlignment::Center)
                                .width(BUTTON_WIDTH)
                                .on_select(wrapped_sign_in_cb)
                                .test_id(REQUIRE_SIGN_IN_SIGN_IN_BUTTON_TEST_ID)
                            PrimaryButton(variant: PrimaryButtonVariant::TextSize200(DetailsString::AV_LRC_CANCEL_BUTTON_TEXT.get_localised_text()))
                                .main_axis_alignment(MainAxisAlignment::Center)
                                .width(BUTTON_WIDTH)
                                .on_select(cancel_cb)
                                .test_id(REQUIRE_SIGN_IN_CANCEL_BUTTON_TEST_ID)
                        }
                        .focus_hierarchical_container(NavigationStrategy::Vertical)
                        .main_axis_alignment(MainAxisAlignment::SpacedBy(15.0))
                    }
                    .cross_axis_alignment(CrossAxisAlignment::Center)
                }
                .padding(Padding::all(25.0))
                .main_axis_alignment(MainAxisAlignment::Center)
                .background_color(get_ignx_color(FableColor::COOL800))
                .border_radius(15.0)
                .width(CONTAINER_WIDTH)
            }
            .test_id(REQUIRE_SIGN_IN_TEST_ID)
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .main_axis_alignment(MainAxisAlignment::Center)
            .cross_axis_alignment(CrossAxisAlignment::Center)
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_no_bindings_test, setup_mock_router,
    };
    use router::{MockRouting, RoutingContext};
    use std::rc::Rc;

    #[test]
    fn should_invoke_cancel_cb_on_cancel_button_press() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let cancel_invoked = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), cancel_invoked);

                let on_cancel_cb = move || {
                    cancel_invoked.set(true);
                };

                compose! {
                    RequireSignInModal(cancel_cb: on_cancel_cb)
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let cancel_button =
                    node_tree.find_by_test_id(REQUIRE_SIGN_IN_CANCEL_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(cancel_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let cancel_invoked =
                    use_context::<RwSignal<bool>>(scope).expect("Cancel invoked signal to exist");
                assert!(cancel_invoked.get_untracked());
            },
        )
    }

    #[test]
    fn should_navigate_to_js_sign_in_page_on_sign_in_press() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let mut router = MockRouting::default();
                router.expect_navigate().times(1).returning(|loc, source| {
                    assert_eq!(
                        loc.pageType,
                        PageType::Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE)
                    );
                    assert_eq!(source, "DETAILS_PAGE_SIGN_IN_MODAL");
                });
                setup_mock_router(ctx.scope(), &mut router);
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(router));
                create_or_use_details_context_no_bindings_test(&ctx);

                compose! {
                    RequireSignInModal(cancel_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let cancel_button =
                    node_tree.find_by_test_id(REQUIRE_SIGN_IN_SIGN_IN_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(cancel_button.borrow_props().node_id);
                test_game_loop.tick_until_done();
            },
        )
    }
}
