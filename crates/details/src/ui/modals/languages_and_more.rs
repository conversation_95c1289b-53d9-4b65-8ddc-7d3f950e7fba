use crate::context::hooks::{create_or_use_details_page_context, empty_stack_fallback};
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::modules::background_image::get_details_background_image;
use crate::modules::localized_text::DetailsString;
use crate::modules::title_details::convert_details_layout_to_languages_and_more;
use crate::network::network_type::atf::DetailsPageATFResponse;
use crate::ui::error_overlay::set_details_standard_error_state;
use crate::ui::test_ids::{
    L_M_DETAILS_METADATA_ITEM, L_M_DETAILS_METADATA_ITEM_CONTENT, L_M_DETAILS_METADATA_LIST,
    L_M_DETAILS_MODAL_CONTAINER, L_M_DETAILS_OK_BUTTON,
};
use containers::utils::tts_utils::tts_details_data_to_details_language_and_more_metadata_description;
use details_derive::{
    get_some_or_return_with_error_message, get_some_or_return_with_val_and_error_message,
};
use fableous::buttons::primary_button::*;
use fableous::pointer_control::*;
use fableous::typography::typography::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::context::AppContext;
use ignx_compositron::id::Id;
use ignx_compositron::input::KeyCode;
use ignx_compositron::list::ItemBuilderFn;
use ignx_compositron::prelude::*;
use ignx_compositron::show::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};
use media_background::types::{FullscreenBackgroundData, MediaBackgroundType};
use rust_features::try_use_rust_features;
use std::rc::Rc;
use std::time::Duration;
use title_details::core::{
    title_details_data_to_tts_details_data, TitleDetailsChangeRequest, TitleDetailsData,
};
use title_details::layouts::collection_and_details::TITLE_DETAILS_LEFT_OFFSET;
use title_details::types::common::TTSTitleDetailsData;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("languages_and_more");

const BUTTON_BOTTOM: f32 = 136.0;
const RIGHT_CONTAINER_TOP: f32 = 154.0;
const CONTAINERS_SPACING: f32 = 96.0;
const LEFT_CONTAINER_WIDTH: f32 = 885.0;
const RIGHT_CONTAINER_WIDTH: f32 = 696.0;

#[derive(Clone)]
enum MetadataItem {
    Simple {
        title: TextContent,
        content: TextContent,
    },
    AutonymItem {
        title: TextContent,
        content: TextContent,
    },
}

impl Id for MetadataItem {
    type Id = TextContent;

    fn id(&self) -> &Self::Id {
        match self {
            MetadataItem::Simple { title, .. } => title,
            MetadataItem::AutonymItem { title, .. } => title,
        }
    }
}

#[Composer]
fn MetadataItem(ctx: &AppContext, item: MetadataItem) -> ColumnComposable {
    match item {
        MetadataItem::Simple { title, content } => {
            let default_line_height = 42.0f32;

            compose! {
                Column() {
                    TypographyLabel800(content: title)
                    .padding(Padding{ start:0.0, end: 0.0, top: 0.0, bottom: 18.0 })

                    TypographyBody400(content: content)
                    .line_height(default_line_height)
                    .test_id(L_M_DETAILS_METADATA_ITEM_CONTENT)
                 }.test_id(L_M_DETAILS_METADATA_ITEM)
            }
        }
        MetadataItem::AutonymItem { title, content } => {
            let autonym_line_height = (42f32 * 1.25).ceil();
            let autonym_font_families = FontFamilies::from(vec!["autonym_v7_20231025".to_string()]);

            compose! {
                Column() {
                    TypographyLabel800(content: title)
                    .padding(Padding{ start:0.0, end: 0.0, top: 0.0, bottom: 18.0 })

                    TypographyBody400(content: content)
                    .font_families(autonym_font_families)
                    .line_height(autonym_line_height)
                    .test_id(L_M_DETAILS_METADATA_ITEM_CONTENT)
                 }.test_id(L_M_DETAILS_METADATA_ITEM)
            }
        }
    }
}

#[Composer]
fn LanguagesAndMorePointerOverlay(
    ctx: &AppContext,
    items_length: i32,
    focused_index: RwSignal<ScrollTo>,
    on_navigate: Rc<dyn Fn(i32)>,
) -> ShowComposable {
    let pointer_control_active = is_pointer_control_active(ctx.scope());

    let show_builder: Box<ConditionalComposableBuilder> = Box::new({
        move |ctx| {
            let should_show_up_pointer = Signal::derive(ctx.scope(), move || {
                let focused_index = get_some_or_return_with_val_and_error_message!(
                    focused_index.try_get(),
                    "unable to get focused index",
                    false
                );
                match focused_index {
                    ScrollTo::Index(idx, _, _) => idx > 0,
                    ScrollTo::Offset(_, _, _) => false,
                }
            });

            let should_show_down_pointer = Signal::derive(ctx.scope(), move || {
                let focused_index = get_some_or_return_with_val_and_error_message!(
                    focused_index.try_get(),
                    "unable to get focused index",
                    false
                );
                match focused_index {
                    ScrollTo::Index(idx, _, _) => idx < (items_length - 1) as u32,
                    ScrollTo::Offset(_, _, _) => false,
                }
            });

            let pointer_down_handler = Rc::new({
                let on_navigate = on_navigate.clone();
                move || {
                    on_navigate(1);
                }
            });
            let pointer_up_handler = Rc::new({
                let on_navigate = on_navigate.clone();
                move || {
                    on_navigate(-1);
                }
            });

            let component = compose! {
                Column() {
                    PointerControlCaret(direction: Direction::Up, on_select: pointer_up_handler.clone(), visible: should_show_up_pointer)
                    PointerControlCaret(direction: Direction::Down, on_select: pointer_down_handler.clone(), visible: should_show_down_pointer)
                }
                .width(SCREEN_WIDTH)
                .height(SCREEN_HEIGHT)
                .cross_axis_alignment(CrossAxisAlignment::Center)
                .main_axis_alignment(MainAxisAlignment::SpaceBetween)
            };

            component.into_widget()
        }
    });

    compose! {
        Show(if_builder: show_builder, condition: pointer_control_active, else_builder: None)
    }
}

/// Generates TTS (Text-to-Speech) messages for the Languages and More details page.
///
/// This function compiles a list of `TextContent` items that represent the information
/// to be spoken for the Languages and More details page. It includes all relevant
/// metadata about the title, such as the title itself, subtitle, ratings, duration,
/// year, content advisory, etc.
///
/// It does not include the "OK" button TTS or its secondary closing message ("Press Select to exit").
///
/// # Returns
///
/// A Vec<TextContent> containing all the TTS messages in the order they should be spoken
fn build_tts_metadata_messages(
    tts_details_data: Option<TTSTitleDetailsData>,
    tts_imdb_rating_string: String,
    tts_right_column_descriptions: Vec<TextContent>,
) -> Vec<TextContent> {
    let mut tts_metadata_descriptions = vec![TextContent::String(
        tts_details_data
            .clone()
            .map(|data| data.title)
            .unwrap_or_default(),
    )];

    if let Some(data) = tts_details_data {
        tts_metadata_descriptions.extend(
            tts_details_data_to_details_language_and_more_metadata_description(
                data,
                tts_imdb_rating_string,
            ),
        );
    }
    tts_metadata_descriptions.extend(tts_right_column_descriptions);
    tts_metadata_descriptions
}

#[Composer]
pub fn LanguagesAndMore<T>(ctx: &AppContext, okay_cb: T) -> StackComposable
where
    T: Fn() + 'static,
{
    let focused_index = create_rw_signal(
        ctx.scope(),
        ScrollTo::Index(0, Pivot::Start, Animation::default()),
    );

    let details_ctx = match create_or_use_details_page_context(ctx) {
        None => {
            set_details_standard_error_state(ctx.scope());
            return empty_stack_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    // Check if PSE badge is enabled from the parent scope
    let is_pse_badge_enabled = try_use_rust_features(details_ctx.borrow().app_context.scope())
        .map_or(false, |features| features.is_pse_badge_enabled());

    let atf_state_binding_on_change = details_ctx.borrow().state.state_binding_atf_on_change;
    let media_background = details_ctx.borrow().state.media_background;
    let atf_response = details_ctx.borrow().state.page_resources.atf_response;

    let title_details_global = details_ctx.borrow().state.title_details.global;
    let title_details_global_opacity = details_ctx.borrow().state.title_details.global_opacity;
    let title_details_atf = details_ctx.borrow().state.title_details.atf;
    let response = details_ctx.borrow().state.page_resources.atf_response;

    let items = generate_and_set_metadata_list(response.into(), is_pse_badge_enabled);
    let tts_right_column_descriptions: Vec<TextContent> = items
        .clone()
        .into_iter()
        .flat_map(|item| match item {
            MetadataItem::Simple { title, content } => vec![title, content],
            MetadataItem::AutonymItem { title, content } => vec![title, content],
        })
        .collect();

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            atf_state_binding_on_change.track();
            let response = get_some_or_return_with_error_message!(
                atf_response.try_get().flatten(),
                "Failed to get atf response"
            );
            let title_details_atf = get_some_or_return_with_error_message!(
                title_details_atf.try_get_untracked(),
                "Failed to get title details atf"
            );

            let fullscreenBackgroundData = FullscreenBackgroundData {
                id: response.title_decoration.gti.clone(),
                image_url: get_details_background_image(&response),
                enter_immediately: true,
                image_opacity_percentage: 45,
            };
            media_background.set(MediaBackgroundType::FullscreenBackground(
                fullscreenBackgroundData,
            ));

            if let Some(new_title_details) =
                convert_details_layout_to_languages_and_more(&response, title_details_atf)
            {
                title_details_global.set(TitleDetailsChangeRequest {
                    data: new_title_details,
                    navigation_direction: Default::default(),
                });
            }

            // FIXME: Add option to title details to disable animation (probably as a navigation_direction or other option)
            //        Delay to hide the animation change that happens when the title details gets updated
            ctx.with_animation(
                Animation::default().with_delay(Duration::from_millis(200)),
                move || title_details_global_opacity.set(1.0),
            );
        }
    });
    let tts_details_data =
        title_details_data_to_tts_details_data(&title_details_global.clone().get_untracked().data);
    let tts_imdb_rating_string: String = match title_details_global.clone().get_untracked().data {
        TitleDetailsData::DetailsLanguagesAndMoreLayout(data) => data.imdb_rating.to_string(),
        _ => String::new(),
    };
    let tts_metadata_descriptions = build_tts_metadata_messages(
        tts_details_data,
        tts_imdb_rating_string,
        tts_right_column_descriptions,
    );

    let on_close = {
        let details_ctx = details_ctx.clone();

        move || {
            details_ctx
                .borrow()
                .clickstream_reporter
                .more_details_close_button_pressed();

            okay_cb()
        }
    };

    let items_length = items.len() as i32;
    let scroll_reported = create_rw_signal(ctx.scope(), false);

    let on_navigate = Rc::new(move |offset: i32| {
        let focused_index_value = get_some_or_return_with_error_message!(
            focused_index.try_get_untracked(),
            "Failed to get focused index"
        );
        let current_index = match focused_index_value {
            ScrollTo::Offset(_, _, _) => 0,
            ScrollTo::Index(idx, _, _) => idx as i32,
        };

        let new_index = current_index + offset;

        if new_index > items_length || new_index < 0 {
            return;
        }

        if scroll_reported.try_get_untracked().is_none_or(|val| !val) {
            details_ctx
                .borrow()
                .clickstream_reporter
                .more_details_modal_scroll();

            scroll_reported.set_untracked(true);
        }

        focused_index.set(ScrollTo::Index(
            (new_index) as u32,
            Pivot::Start,
            Animation::default(),
        ));
    });

    let metadata_item_builder: ItemBuilderFn<MetadataItem, ColumnComposable> =
        Rc::new(|ctx, item, _| {
            compose! {
                MetadataItem(item: item.clone())
            }
        });

    let overlay_on_navigate = on_navigate.clone();
    compose! {
        Stack() {
            LanguagesAndMorePointerOverlay(items_length, on_navigate: overlay_on_navigate, focused_index)
            PrimaryButton(variant: PrimaryButtonVariant::TextSize400(DetailsString::AV_LRC_OK_BUTTON_TEXT.get_localised_text()))
                .on_select(on_close)
                .test_id(L_M_DETAILS_OK_BUTTON)
                .accessibility_context_messages(tts_metadata_descriptions)
                .accessibility_closing_context_message(DetailsString::AV_LRC_PRESS_SELECT_TO_EXIT.get_localised_text())
                .translate_x(TITLE_DETAILS_LEFT_OFFSET)
                .translate_y(SCREEN_HEIGHT - BUTTON_BOTTOM)
                .pointer_control_target(None)
                .z_index(2)

            ColumnList(items: items, item_builder: metadata_item_builder)
                .overflow_behavior(OverflowBehavior::Visible)
                .main_axis_alignment(MainAxisAlignment::SpacedBy(48.0))
                .scroll_to(focused_index)
                .width(RIGHT_CONTAINER_WIDTH)
                .translate_x(LEFT_CONTAINER_WIDTH + CONTAINERS_SPACING + TITLE_DETAILS_LEFT_OFFSET)
                .translate_y(RIGHT_CONTAINER_TOP)
                .test_id(L_M_DETAILS_METADATA_LIST)
                .z_index(1)
        }
        .size(Vec2::new(SCREEN_WIDTH, SCREEN_HEIGHT))
        .on_key_down(KeyCode::Down,  {
            let on_navigate = on_navigate.clone();
            move || on_navigate(1)
        })
        .on_key_down(KeyCode::Up, move || {
            on_navigate(-1)
        })
        .test_id(L_M_DETAILS_MODAL_CONTAINER)
    }
}

fn generate_and_set_metadata_list(
    details_response: Signal<Option<DetailsPageATFResponse>>,
    is_pse_badge_enabled: bool,
) -> Vec<MetadataItem> {
    let mut all_items = vec![];

    let response = get_some_or_return_with_val_and_error_message!(
        details_response.try_get_untracked().flatten(),
        "Unable to get atf response",
        all_items
    );

    if !response.content_descriptors.is_empty() {
        let metadata_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_CONTENT_ADVISORY.get_localised_text(),
            content: TextContent::String(response.content_descriptors.join(", ")),
        };
        all_items.push(metadata_item)
    }

    if !response.content_warnings.is_empty() && is_pse_badge_enabled {
        let metadata_item = MetadataItem::Simple {
            title: TextContent::String("".to_string()),
            content: TextContent::String(
                response
                    .content_warnings
                    .values()
                    .cloned()
                    .collect::<Vec<String>>()
                    .join(", "),
            ),
        };
        all_items.push(metadata_item)
    }

    if !response.title_decoration.audios.is_empty() {
        let audio_item = MetadataItem::AutonymItem {
            title: DetailsString::AV_LRC_AUDIO.get_localised_text(),
            content: TextContent::String(
                response
                    .title_decoration
                    .audios
                    .iter()
                    .map(|x| x.clone().name)
                    .collect::<Vec<String>>()
                    .join(", "),
            ),
        };
        all_items.push(audio_item)
    } else {
        let audio_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_AUDIO.get_localised_text(),
            content: DetailsString::AV_LRC_NONE_AVAILABLE.get_localised_text(),
        };
        all_items.push(audio_item);
    }

    if !response.title_decoration.subtitles.is_empty() {
        let audio_item = MetadataItem::AutonymItem {
            title: DetailsString::AV_LRC_SUBTITLES.get_localised_text(),
            content: TextContent::String(
                response
                    .title_decoration
                    .subtitles
                    .iter()
                    .map(|x| x.clone().name)
                    .collect::<Vec<String>>()
                    .join(", "),
            ),
        };

        all_items.push(audio_item)
    } else {
        let audio_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_SUBTITLES.get_localised_text(),
            content: DetailsString::AV_LRC_NONE_AVAILABLE.get_localised_text(),
        };
        all_items.push(audio_item)
    }

    if !response.title_decoration.audios.is_empty()
        || !response.title_decoration.subtitles.is_empty()
    {
        let metadata_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_CHANGE_LANGUAGES_QUESTION.get_localised_text(),
            content: DetailsString::AV_LRC_CHANGE_LANGUAGES_ANSWER.get_localised_text(),
        };
        all_items.push(metadata_item)
    }

    if !response.starring_cast.is_empty() {
        let metadata_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_ACTORS.get_localised_text(),
            content: TextContent::String(response.starring_cast.join(", ")),
        };
        all_items.push(metadata_item)
    }

    if !response.directors.is_empty() {
        let metadata_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_DIRECTOR.get_localised_text(),
            content: TextContent::String(response.directors.join(", ")),
        };
        all_items.push(metadata_item)
    }

    if !response.supporting_cast.is_empty() {
        let metadata_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_SUPPORTING_ACTORS.get_localised_text(),
            content: TextContent::String(response.supporting_cast.join(", ")),
        };
        all_items.push(metadata_item)
    }

    if !response.studios.is_empty() {
        let metadata_item = MetadataItem::Simple {
            title: DetailsString::AV_LRC_STUDIO.get_localised_text(),
            content: TextContent::String(response.studios.join(", ")),
        };
        all_items.push(metadata_item)
    }

    all_items
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, use_details_page_context_test,
    };
    use crate::metrics::clickstream_reporter::MockClickStreamReporter;
    use crate::network::mock_responses::MOCK_RESPONSE_VOD_ATF;
    use crate::network::parser::details_page_atf_parser;
    use ignx_compositron::node::NodeID;
    use ignx_compositron::test_utils::node_properties::NodeQuery;
    use ignx_compositron::test_utils::{assert_node_exists, ComposableType, TestRendererGameLoop};
    use ignx_compositron::text::LocalizedText;
    use std::cell::RefCell;
    use title_details::core::TitleDetailsData;

    fn assert_item(
        items: Rc<RefCell<Vec<NodeQuery<'_>>>>,
        label: String,
        expected_content: String,
    ) {
        let item = items.borrow_mut().remove(0);
        let item_label = item
            .find_any_child_with()
            .composable_type(ComposableType::Label)
            .find_first();

        let item_content = item
            .find_any_child_with()
            .composable_type(ComposableType::Label)
            .test_id(L_M_DETAILS_METADATA_ITEM_CONTENT)
            .find_first();

        assert_eq!(item_label.borrow_props().text, Some(label));
        assert_eq!(item_content.borrow_props().text, Some(expected_content));
    }

    fn assert_item_visibility(
        test_game_loop: &mut TestRendererGameLoop,
        expected_first_item_on_screen_amount: f32,
        expected_last_item_on_screen_amount: f32,
    ) -> NodeID {
        let node_tree = test_game_loop.tick_until_done();

        let meta_data_list = node_tree.find_by_test_id(L_M_DETAILS_METADATA_LIST);
        let title_actions_items = meta_data_list
            .find_any_child_with()
            .test_id(L_M_DETAILS_METADATA_ITEM)
            .find_all();
        let first_item = title_actions_items.first().expect("First item to exist!");

        let last_item = title_actions_items.last().expect("Last item to exist!");

        let modal_container_id = node_tree
            .find_by_test_id(L_M_DETAILS_MODAL_CONTAINER)
            .borrow_props()
            .node_id;

        assert_eq!(
            first_item.borrow_props().on_screen_amount,
            expected_first_item_on_screen_amount
        );
        assert_eq!(
            last_item.borrow_props().on_screen_amount,
            expected_last_item_on_screen_amount
        );

        modal_container_id
    }

    #[test]
    fn should_update_media_background_to_fullscreen_background_image() {
        ignx_compositron::app::launch_test(
            |ctx| {
                create_or_use_details_context_test(&ctx);

                compose! {
                    LanguagesAndMore(okay_cb: || {})
                }
            },
            |scope, mut test_game_loop| {
                let details_ctx = use_details_page_context_test(scope);
                test_game_loop.tick_until_done();

                assert_eq!(
                    details_ctx.borrow().state.media_background.get_untracked(),
                    MediaBackgroundType::FullscreenBackground(FullscreenBackgroundData {
                        id: "amzn1.dv.gti.162cbf12-a59d-47ab-bdd5-acd2995bdd26".to_string(),
                        image_url: "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/079983f10fc6866c8675e4e8e0538a9e045eb5ffb057f978704154ea98ac32af._RI_TTW_.jpg".to_string(),
                        enter_immediately: true,
                        image_opacity_percentage: 45,
                    })
                );

                let title_details = details_ctx
                    .borrow()
                    .state
                    .title_details
                    .global
                    .get_untracked();
                match title_details.data {
                    TitleDetailsData::DetailsLanguagesAndMoreLayout(_) => {}
                    _ => panic!("Unexpected title details type"),
                }
            },
        )
    }

    #[test]
    fn should_render_correctly() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let _details_context = create_or_use_details_context_test(&ctx);

                compose! {
                    LanguagesAndMore(okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let ok_button = node_tree.find_by_test_id(L_M_DETAILS_OK_BUTTON);
                assert_node_exists!(ok_button);

                let meta_data_list = node_tree.find_by_test_id(L_M_DETAILS_METADATA_LIST);
                let title_actions_items = Rc::new(RefCell::new(
                    meta_data_list
                        .find_any_child_with()
                        .test_id(L_M_DETAILS_METADATA_ITEM)
                        .find_all(),
                ));

                assert_eq!(title_actions_items.borrow().len(), 8);

                assert_item(
                    title_actions_items.clone(),
                    "Content advisory".to_string(),
                    "violence, smoking, foul language".to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "Audio Languages".to_string(),
                    "ಕನ\u{ccd}ನಡ, Čeština, English [Audio Description], English, 日本語"
                        .to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "Subtitles".to_string(),
                    "日本語, Español (Latinoamérica), Português (Brasil)".to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "How do I change language?".to_string(),
                    "Choose subtitle and audio options when watching the video.".to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "Starring".to_string(),
                    "Eiza Gonzalez Reyna, Alexander Skarsgard, Tamara Torres".to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "Directors".to_string(),
                    "Hiro Murai".to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "Supporting actors".to_string(),
                    "Caleb Hearon".to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "Studio".to_string(),
                    "Amazon Studios".to_string(),
                );
            },
        )
    }

    #[test]
    fn should_render_correctly_with_empty_metadata() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);
                let mut details_response =
                    details_page_atf_parser(MOCK_RESPONSE_VOD_ATF.to_string())
                        .map(|result| match result {
                            network::common::DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                            network::common::DeviceProxyResponse::ErrorResponse(_) => {
                                panic!("unexpected error response")
                            }
                        })
                        .expect("VOD response to be parsed successfully");

                let response_signal = details_context.borrow().state.page_resources.atf_response;
                details_response.directors = vec![];
                details_response.starring_cast = vec![];
                details_response.supporting_cast = vec![];
                details_response.title_decoration.audios = vec![];
                details_response.studios = vec![];
                details_response.content_descriptors = vec![];
                details_response.title_decoration.subtitles = vec![];
                details_response.supporting_cast = vec![];

                response_signal.set(Some(details_response));

                compose! {
                    LanguagesAndMore(okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let meta_data_list = node_tree.find_by_test_id(L_M_DETAILS_METADATA_LIST);
                let title_actions_items = Rc::new(RefCell::new(
                    meta_data_list
                        .find_any_child_with()
                        .test_id(L_M_DETAILS_METADATA_ITEM)
                        .find_all(),
                ));

                assert_eq!(title_actions_items.borrow().len(), 2);

                assert_item(
                    title_actions_items.clone(),
                    "Audio Languages".to_string(),
                    "None available".to_string(),
                );

                assert_item(
                    title_actions_items.clone(),
                    "Subtitles".to_string(),
                    "None available".to_string(),
                );
            },
        )
    }

    #[test]
    fn should_scroll_correctly() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);

                let mut clickstream_mock_reporter = MockClickStreamReporter::default();
                clickstream_mock_reporter
                    .expect_more_details_modal_scroll()
                    .times(1)
                    .returning(|| {});
                details_context.borrow_mut().clickstream_reporter =
                    Rc::new(clickstream_mock_reporter);

                compose! {
                    LanguagesAndMore(okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let _modal_container_id = assert_item_visibility(&mut test_game_loop, 1.0, 0.0);

                for _ in 0..3 {
                    test_game_loop.send_key_down_up_event(KeyCode::Down);
                }

                assert_item_visibility(&mut test_game_loop, 0.0, 1.0);

                for _ in 0..3 {
                    test_game_loop.send_key_down_up_event(KeyCode::Up);
                }

                assert_item_visibility(&mut test_game_loop, 1.0, 0.0);

                //Handle out of bound index while moving up
                for _ in 0..3 {
                    test_game_loop.send_key_down_up_event(KeyCode::Up);
                }
                assert_item_visibility(&mut test_game_loop, 1.0, 0.0);

                //Handle out of bound index while moving down
                for _ in 0..3 {
                    test_game_loop.send_key_down_up_event(KeyCode::Down);
                }
                assert_item_visibility(&mut test_game_loop, 0.0, 1.0);

                for _ in 0..9 {
                    test_game_loop.send_key_down_up_event(KeyCode::Down);
                }

                assert_item_visibility(&mut test_game_loop, 0.0, 1.0);
            },
        )
    }

    #[test]
    fn should_report_clickstream_on_close() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let details_context = create_or_use_details_context_test(&ctx);

                let mut clickstream_mock_reporter = MockClickStreamReporter::default();
                clickstream_mock_reporter
                    .expect_more_details_close_button_pressed()
                    .times(1)
                    .returning(|| {});
                details_context.borrow_mut().clickstream_reporter =
                    Rc::new(clickstream_mock_reporter);

                compose! {
                    LanguagesAndMore(okay_cb: || {})
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let ok_button = node_tree.find_by_test_id(L_M_DETAILS_OK_BUTTON);

                test_game_loop.send_on_select_event(ok_button.borrow_props().node_id);
                test_game_loop.tick_until_done();
            },
        )
    }

    fn get_tts_details_data() -> TTSTitleDetailsData {
        TTSTitleDetailsData {
            title: "The Lord of the Rings: The Fellowship of the Ring".to_string(),
            high_value_message: Some("Exclusive Extended Edition".to_string()),
            metadata_badge_message: Some("Sample Badge".to_string()),
            entitlement_text: Some("Available with Prime".to_string()),
            synopsis: Some("An ancient Ring thought lost for centuries has been found, and through a strange twist of fate has been given to a small Hobbit named Frodo. When Gandalf discovers the Ring is, in fact, the One Ring of the Dark Lord Sauron, Frodo must make an epic quest to the Cracks of Doom in order to destroy it.".to_string()),
            maturity_rating: Some("PG-13".to_string()),
            audio_descriptions: true,
            star_rating: Some("4.8".to_string()),
            duration: Some(TextContent::String("180 min".to_string())),
            release_year: Some("2001".to_string()),
            genres: "Adventure, Drama, Fantasy".to_string(),
            ..Default::default()
        }
    }

    #[test]
    fn test_build_tts_metadata_messages_with_tts_details_data_and_tts_right_column_descriptions() {
        let tts_details_data = Some(get_tts_details_data());
        let imdb_rating = "8.5".to_string();
        let tts_right_column_descriptions = vec![
            TextContent::String("Content advisory".to_string()),
            TextContent::String("voilence, substance use, foul language".to_string()),
        ];

        let tts_metadata_messages = build_tts_metadata_messages(
            tts_details_data,
            imdb_rating,
            tts_right_column_descriptions.clone(),
        );

        let expected_output = vec![
            TextContent::String("The Lord of the Rings: The Fellowship of the Ring".to_string()),
            TextContent::String("Exclusive Extended Edition".to_string()),
            TextContent::String("Available with Prime".to_string()),
            TextContent::String("4.8".to_string()),
            TextContent::String("IMDb".to_string()),
            TextContent::String("8.5".to_string()),
            TextContent::String("180 min".to_string()),
            TextContent::String("2001".to_string()),
            LocalizedText::new("AV_LRC_AGE_RATING").into(),
            TextContent::String("PG-13".to_string()),
            TextContent::LocalizedText(LocalizedText::new("AV_LRC_AUDIO_DESCRIPTION_AVAILABLE")),
            TextContent::String("An ancient Ring thought lost for centuries has been found, and through a strange twist of fate has been given to a small Hobbit named Frodo. When Gandalf discovers the Ring is, in fact, the One Ring of the Dark Lord Sauron, Frodo must make an epic quest to the Cracks of Doom in order to destroy it.".to_string()),
            TextContent::String("Adventure, Drama, Fantasy".to_string()),
            TextContent::String("Content advisory".to_string()),
            TextContent::String("voilence, substance use, foul language".to_string()),
        ];

        assert_eq!(tts_metadata_messages, expected_output);
    }

    #[test]
    fn test_build_tts_metadata_messages_with_tts_details_data_for_empty_tts_right_column_descriptions(
    ) {
        let tts_details_data = Some(get_tts_details_data());
        let imdb_rating = "8.5".to_string();
        let tts_right_column_descriptions = vec![];

        let tts_metadata_messages = build_tts_metadata_messages(
            tts_details_data,
            imdb_rating,
            tts_right_column_descriptions,
        );

        let expected_output = vec![
            TextContent::String("The Lord of the Rings: The Fellowship of the Ring".to_string()),
            TextContent::String("Exclusive Extended Edition".to_string()),
            TextContent::String("Available with Prime".to_string()),
            TextContent::String("4.8".to_string()),
            TextContent::String("IMDb".to_string()),
            TextContent::String("8.5".to_string()),
            TextContent::String("180 min".to_string()),
            TextContent::String("2001".to_string()),
            LocalizedText::new("AV_LRC_AGE_RATING").into(),
            TextContent::String("PG-13".to_string()),
            TextContent::LocalizedText(LocalizedText::new("AV_LRC_AUDIO_DESCRIPTION_AVAILABLE")),
            TextContent::String("An ancient Ring thought lost for centuries has been found, and through a strange twist of fate has been given to a small Hobbit named Frodo. When Gandalf discovers the Ring is, in fact, the One Ring of the Dark Lord Sauron, Frodo must make an epic quest to the Cracks of Doom in order to destroy it.".to_string()),
            TextContent::String("Adventure, Drama, Fantasy".to_string()),
        ];

        assert_eq!(tts_metadata_messages, expected_output);
    }
}
