use crate::context::DetailsPageContext;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::ui::modals::modal_manager::DetailsModal;
use details_derive::get_some_or_return_with_error_message;
use fableous::animations::{FableMotionDuration, MotionDuration};
use ignx_compositron::prelude::*;
use media_background::types::MediaBackgroundType;
use std::ops::Deref;
use std::rc::Rc;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("transition_controller");

#[derive(Default)]
struct ModalTransitionSettings {
    details_page_visible: bool,
    clear_media_background_on_load: bool,
    mutates_global_title_details: bool,
    mutates_media_background: bool,
    is_triggered_from_btf: bool,
}

fn get_modal_settings(details_modal: &DetailsModal) -> ModalTransitionSettings {
    match details_modal {
        DetailsModal::None => ModalTransitionSettings::default(),
        DetailsModal::RequireSignIn => ModalTransitionSettings {
            details_page_visible: true,
            clear_media_background_on_load: false,
            mutates_global_title_details: false,
            mutates_media_background: false,
            is_triggered_from_btf: false,
        },
        DetailsModal::MorePurchaseOptions => ModalTransitionSettings {
            details_page_visible: false,
            clear_media_background_on_load: true,
            mutates_global_title_details: true,
            mutates_media_background: true,
            is_triggered_from_btf: false,
        },
        DetailsModal::LanguagesAndMore => ModalTransitionSettings {
            details_page_visible: false,
            clear_media_background_on_load: true,
            mutates_global_title_details: true,
            mutates_media_background: false,
            is_triggered_from_btf: false,
        },
        DetailsModal::PreOrderDetails(_) => ModalTransitionSettings {
            details_page_visible: true,
            clear_media_background_on_load: true,
            mutates_global_title_details: false,
            mutates_media_background: false,
            is_triggered_from_btf: false,
        },
        DetailsModal::Suppression(_) => ModalTransitionSettings {
            details_page_visible: true,
            clear_media_background_on_load: true,
            mutates_global_title_details: false,
            mutates_media_background: false,
            is_triggered_from_btf: false,
        },
        DetailsModal::Recap => ModalTransitionSettings {
            details_page_visible: true,
            clear_media_background_on_load: false,
            mutates_global_title_details: false,
            mutates_media_background: false,
            is_triggered_from_btf: false,
        },
        DetailsModal::CustomerService => ModalTransitionSettings {
            details_page_visible: false,
            clear_media_background_on_load: true,
            mutates_global_title_details: true,
            mutates_media_background: false,
            is_triggered_from_btf: false,
        },
        DetailsModal::ContentUnavailable => ModalTransitionSettings {
            details_page_visible: true,
            clear_media_background_on_load: false,
            mutates_global_title_details: false,
            mutates_media_background: false,
            is_triggered_from_btf: true,
        },
    }
}

fn restore_media_background_and_title_details_on_exit(
    details_ctx: &'_ DetailsPageContext,
    transition_from: &'_ DetailsModal,
) {
    let atf_media_background_cache = details_ctx.borrow().state.atf_media_background_cache;
    let media_background_signal = details_ctx.borrow().state.media_background;
    let global_title_details_opacity = details_ctx.borrow().state.title_details.global_opacity;

    let prev_modal_settings = get_modal_settings(transition_from);

    // Restore media background
    if prev_modal_settings.mutates_media_background
        || prev_modal_settings.clear_media_background_on_load
    {
        let media_background_data = atf_media_background_cache.get();
        media_background_signal.set(media_background_data);
    }

    // Hide global title details
    if prev_modal_settings.mutates_global_title_details {
        global_title_details_opacity.set(0.0);
    }
}

fn get_modal_animation(_is_enter: bool) -> Animation {
    Animation::default().with_duration(FableMotionDuration::Standard.to_duration())
}

fn modal_open(details_ctx: &DetailsPageContext, ctx: &AppContext, next_modal: DetailsModal) {
    let transition_settings = get_modal_settings(&next_modal);
    let modal_opacity = details_ctx.borrow().state.modal_state.modal_opacity;
    let active_modal = details_ctx.borrow().state.modal_state.active_modal;
    let nav_control = details_ctx.borrow().dependent_data.nav_control.clone();
    let atf_title_details_opacity = details_ctx.borrow().state.title_details.atf_opacity;
    let global_title_details_y_offset = details_ctx.borrow().state.title_details.global_y_offset;
    let details_opacity = details_ctx
        .borrow()
        .state
        .transition_state
        .transition_opacity;

    // Done before as we don't want to animate this
    if transition_settings.mutates_global_title_details {
        global_title_details_y_offset.set(0.0);
    }

    // If triggered from BTF, set  the active modal before setting up the animation so that
    // the fade occurs smoothly.
    if transition_settings.is_triggered_from_btf {
        active_modal.set(next_modal.clone());
    }

    ctx.with_animation_completion(
        get_modal_animation(true),
        {
            let details_ctx = details_ctx.clone();
            move || {
                modal_opacity.set(1.0);

                if !transition_settings.details_page_visible {
                    nav_control.show_utility_nav.set(false);
                    details_opacity.set(0.0);
                    atf_title_details_opacity.set(0.0);
                }

                if transition_settings.clear_media_background_on_load {
                    details_ctx
                        .borrow()
                        .state
                        .media_background
                        .set(MediaBackgroundType::None);
                }
            }
        },
        move || {
            // This was already set before doing the animation, setting it again would result
            // in a brief flicker that should be avoided.
            if !transition_settings.is_triggered_from_btf {
                active_modal.set(next_modal.clone());
            }
        },
    )
}

fn modal_close(
    details_ctx: &DetailsPageContext,
    ctx: &AppContext,
    prev_modal: DetailsModal,
    on_finish: Option<Rc<dyn Fn()>>,
) {
    let transition_settings = get_modal_settings(&prev_modal);
    let atf_focus_signal = details_ctx.borrow().state.title_actions_state.focus_signal;
    let global_title_details_opacity = details_ctx.borrow().state.title_details.global_opacity;
    let modal_opacity = details_ctx.borrow().state.modal_state.modal_opacity;
    let active_modal = details_ctx.borrow().state.modal_state.active_modal;
    let nav_control = details_ctx.borrow().dependent_data.nav_control.clone();
    let atf_title_details_opacity = details_ctx.borrow().state.title_details.atf_opacity;
    let details_opacity = details_ctx
        .borrow()
        .state
        .transition_state
        .transition_opacity;

    ctx.with_animation_completion(
        get_modal_animation(false),
        move || {
            modal_opacity.set(0.0);

            // Prevent the global title details from disappearing if modal was triggered from BTF
            // (e.g. live sports bonus content card)
            if !transition_settings.is_triggered_from_btf {
                global_title_details_opacity.set(0.0);
            }
        },
        {
            let effect_context = ctx.clone();
            let details_ctx = details_ctx.clone();
            move || {
                // Check if the scope was disposed between the modal starting to close
                if details_ctx.borrow().is_disposed() {
                    return;
                }

                let Some(transition_from) = active_modal.try_get_untracked() else {
                    DETAILS_LOGGER.error("Failed to get active modal aborting transition");
                    return;
                };

                active_modal.try_set(DetailsModal::None);
                effect_context.with_animation(get_modal_animation(false), {
                    let details_ctx = details_ctx.clone();
                    let on_finish = on_finish.clone();
                    move || {
                        nav_control.show_utility_nav.set(true);
                        if !transition_settings.details_page_visible {
                            details_opacity.set(1.0);
                            atf_title_details_opacity.set(1.0);
                        }
                        restore_media_background_and_title_details_on_exit(
                            &details_ctx,
                            &transition_from,
                        );

                        // FIXME: The below causes TTS to stop uttering without moving focus
                        //        Once SDK has a dedicated function to stop TTS we should insert that here instead.
                        atf_focus_signal.set(false);
                        atf_focus_signal.set(true);

                        if let Some(finish_cb) = on_finish {
                            finish_cb.deref()();
                        }
                    }
                });
            }
        },
    )
}

pub fn setup_transition_controller(ctx: &AppContext, details_context: &'_ DetailsPageContext) {
    let active_modal = details_context.borrow().state.modal_state.active_modal;
    let transition_to_modal = details_context
        .borrow()
        .state
        .modal_state
        .transition_to_modal;

    create_effect(ctx.scope(), {
        let details_ctx = details_context.clone();
        let ctx = ctx.clone();
        move |_| {
            let next_modal = get_some_or_return_with_error_message!(
                transition_to_modal.try_get(),
                "Failed to get next modal aborting transition"
            );
            let prev_modal = get_some_or_return_with_error_message!(
                active_modal.try_get_untracked(),
                "Failed getting previous modal aborting transition"
            );

            if matches!(next_modal, DetailsModal::None) && !matches!(prev_modal, DetailsModal::None)
            {
                // Modal closing
                modal_close(&details_ctx, &ctx, prev_modal, None);
            } else if !matches!(next_modal, DetailsModal::None) {
                if prev_modal != DetailsModal::None {
                    modal_close(
                        &details_ctx,
                        &ctx,
                        prev_modal,
                        Some(Rc::new({
                            let ctx = ctx.clone();
                            let details_ctx = details_ctx.clone();
                            move || {
                                modal_open(&details_ctx, &ctx, next_modal.clone());
                            }
                        })),
                    );
                } else {
                    // Modal opening
                    modal_open(&details_ctx, &ctx, next_modal);
                }
            }
        }
    });
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, use_details_page_context_test,
    };
    use ignx_compositron::compose;
    use media_background::types::{MediaStrategy, StandardBackgroundData};
    use rstest::rstest;

    fn setup_fake_modal(ctx: &AppContext) -> ColumnComposable {
        let details_ctx = create_or_use_details_context_test(ctx);
        setup_transition_controller(ctx, &details_ctx);

        let details_opacity = details_ctx
            .borrow()
            .state
            .transition_state
            .transition_opacity;
        let modal_opacity = details_ctx.borrow().state.modal_state.modal_opacity;

        compose! {
            Column() {
                Label(text: "Details")
                    .opacity(details_opacity)
                Label(text: "Modal")
                    .opacity(modal_opacity)
            }
        }
    }

    #[rstest]
    #[case(DetailsModal::LanguagesAndMore, true, true, true, true, true)]
    #[case(DetailsModal::RequireSignIn, false, false, false, false, false)]
    #[case(DetailsModal::MorePurchaseOptions, true, true, true, true, true)]
    fn should_correctly_transition_signals_on_modal_enter(
        #[case] modal: DetailsModal,
        #[case] should_clear_mb: bool,
        #[case] should_fade_out_title_details: bool,
        #[case] should_hide_utility_nav: bool,
        #[case] should_fade_out_details: bool,
        #[case] should_set_global_offset: bool,
    ) {
        ignx_compositron::app::launch_test(
            |ctx| setup_fake_modal(&ctx),
            move |scope, mut test_game_loop| {
                let details_ctx = use_details_page_context_test(scope);
                let _node_tree = test_game_loop.tick_until_done();

                let media_background = details_ctx.borrow().state.media_background;
                media_background.set_untracked(MediaBackgroundType::Standard(
                    StandardBackgroundData {
                        id: "".to_string(),
                        image_url: None,
                        csm_data: None,
                        video_id: None,
                        enter_immediately: false,
                        placement: "".to_string(),
                        media_strategy: MediaStrategy::Live,
                        interaction_source_override: None,
                    },
                ));

                let modal_opacity = details_ctx.borrow().state.modal_state.modal_opacity;
                let details_opacity = details_ctx
                    .borrow()
                    .state
                    .transition_state
                    .transition_opacity;
                let title_details_opacity = details_ctx.borrow().state.title_details.atf_opacity;
                let active_modal = details_ctx.borrow().state.modal_state.active_modal;
                let transition_to_modal =
                    details_ctx.borrow().state.modal_state.transition_to_modal;
                let nav_control = details_ctx.borrow().dependent_data.nav_control.clone();

                details_ctx
                    .borrow()
                    .state
                    .title_details
                    .global_y_offset
                    .set(15.0);

                transition_to_modal.set(modal.clone());
                test_game_loop.tick_once();

                if should_hide_utility_nav {
                    assert!(!nav_control.show_utility_nav.get_untracked());
                }
                if should_fade_out_title_details {
                    assert_eq!(title_details_opacity.get_untracked(), 0.0);
                }
                if should_fade_out_details {
                    assert_eq!(details_opacity.get_untracked(), 0.0);
                }
                if should_set_global_offset {
                    assert_eq!(
                        details_ctx
                            .borrow()
                            .state
                            .title_details
                            .global_y_offset
                            .get_untracked(),
                        0.0
                    );
                }

                assert_eq!(modal_opacity.get_untracked(), 1.0);

                if should_clear_mb {
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);
                } else {
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None)
                }

                // FIXME: This should be a mock advance by the animation timing but seems to be a bug with MockClock atm
                test_game_loop.tick_until_done();

                assert_eq!(active_modal.get_untracked(), modal);
            },
        )
    }

    #[test]
    fn should_close_previous_modal_if_transitioning_from_modal_to_modal() {
        ignx_compositron::app::launch_test(
            |ctx| setup_fake_modal(&ctx),
            |scope, mut test_game_loop| {
                let _node_tree = test_game_loop.tick_until_done();
                let details_ctx = use_details_page_context_test(scope);
                let details_opacity = details_ctx
                    .borrow()
                    .state
                    .transition_state
                    .transition_opacity;
                let modal_opacity = details_ctx.borrow().state.modal_state.modal_opacity;

                details_ctx
                    .borrow()
                    .state
                    .modal_state
                    .transition_to_modal
                    .set(DetailsModal::MorePurchaseOptions);
                test_game_loop.tick_until_done();

                assert_eq!(details_opacity.get_untracked(), 0.0);
                assert_eq!(modal_opacity.get_untracked(), 1.0);

                // Transition to another modal (that doesn't need details faded out)
                details_ctx
                    .borrow()
                    .state
                    .modal_state
                    .transition_to_modal
                    .set(DetailsModal::RequireSignIn);
                test_game_loop.tick_until_done();

                assert_eq!(details_opacity.get_untracked(), 1.0);
                assert_eq!(modal_opacity.get_untracked(), 1.0);
            },
        )
    }

    #[rstest]
    #[case(DetailsModal::LanguagesAndMore, true, true, true)]
    #[case(DetailsModal::RequireSignIn, false, false, false)]
    #[case(DetailsModal::MorePurchaseOptions, true, true, true)]
    #[case(DetailsModal::ContentUnavailable, false, true, false)]
    fn should_correctly_transition_signals_on_modal_exit(
        #[case] modal: DetailsModal,
        #[case] should_fade_in_details: bool,
        #[case] should_restore_media_background: bool,
        #[case] should_hide_global_title_details: bool,
    ) {
        ignx_compositron::app::launch_test(
            |ctx| setup_fake_modal(&ctx),
            move |scope, mut test_game_loop| {
                let details_ctx = use_details_page_context_test(scope);
                let _node_tree = test_game_loop.tick_until_done();

                let media_background = details_ctx.borrow().state.media_background;
                media_background.set(MediaBackgroundType::Standard(StandardBackgroundData {
                    id: "".to_string(),
                    image_url: None,
                    csm_data: None,
                    video_id: None,
                    enter_immediately: false,
                    placement: "".to_string(),
                    media_strategy: MediaStrategy::Live,
                    interaction_source_override: None,
                }));

                let nav_control = details_ctx.borrow().dependent_data.nav_control.clone();
                let modal_opacity = details_ctx.borrow().state.modal_state.modal_opacity;
                let title_details_global_opacity =
                    details_ctx.borrow().state.title_details.global_opacity;
                let details_opacity = details_ctx
                    .borrow()
                    .state
                    .transition_state
                    .transition_opacity;
                let global_title_details_opacity =
                    details_ctx.borrow().state.title_details.global_opacity;

                let active_modal = details_ctx.borrow().state.modal_state.active_modal;
                let transition_to_modal =
                    details_ctx.borrow().state.modal_state.transition_to_modal;

                transition_to_modal.set(modal.clone());
                test_game_loop.tick_until_done();
                transition_to_modal.set(DetailsModal::None);
                test_game_loop.tick_once();

                assert_eq!(modal_opacity.get_untracked(), 0.0);
                assert_eq!(global_title_details_opacity.get_untracked(), 0.0);

                // Wait for that animation to finish
                test_game_loop.tick_until_done();

                assert!(nav_control.show_utility_nav.get_untracked());
                if should_fade_in_details {
                    assert_eq!(details_opacity.get_untracked(), 1.0);
                }

                if should_restore_media_background {
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);
                }

                if should_hide_global_title_details {
                    assert_eq!(title_details_global_opacity.get_untracked(), 0.0);
                }

                assert_eq!(active_modal.get_untracked(), DetailsModal::None);
            },
        )
    }
}
