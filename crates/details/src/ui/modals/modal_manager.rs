use crate::context::DetailsPageContext;
use crate::metrics::logging_reporter::{build_deferred_logger, DetailsDeferredLogger};
use crate::ui::modals::content_unavailable_modal::*;
use crate::ui::modals::customer_service_modal::*;
use crate::ui::modals::languages_and_more::*;
use crate::ui::modals::more_purchase_options::*;
use crate::ui::modals::pre_order_modal::*;
use crate::ui::modals::require_sign_in_modal::*;
use crate::ui::modals::suppression_modal::*;
use crate::ui::modals::transition_controller::setup_transition_controller;
use crate::ui::recap::recap_drawer_container::*;
use crate::ui::test_ids::{DETAILS_MODAL_MANAGER_ITEM_TEST_ID, DETAILS_MODAL_MANAGER_TEST_ID};
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::TitleActionNestedChild;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::composable::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::input::KeyCode;
use ignx_compositron::memo::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, compose_option, Composer};
use std::cell::RefCell;
use std::rc::Rc;

const DETAILS_LOGGER: DetailsDeferredLogger = build_deferred_logger("modal_manager");

#[derive(Clone, PartialEq)]
#[derive_test_only(Debug)]
pub enum DetailsModal {
    None,
    RequireSignIn,
    MorePurchaseOptions,
    LanguagesAndMore,
    Suppression(TitleActionNestedChild),
    PreOrderDetails(TitleActionNestedChild),
    Recap,
    CustomerService,
    ContentUnavailable,
}

pub fn clear_modal_and_return_to_details(details_context: DetailsPageContext) {
    let borrowed_context = details_context.borrow();
    borrowed_context
        .state
        .modal_state
        .transition_to_modal
        .set(DetailsModal::None)
}

#[Composer]
fn DetailsModalWrapper(
    ctx: &AppContext,
    details_context: DetailsPageContext,
    #[into] modal_type: Signal<DetailsModal>,
) -> StackComposable {
    let modal_pointer_control = details_context
        .borrow()
        .state
        .pointer_state
        .modal_pointer_control;
    let return_to_details = Rc::new({
        let details_context: Rc<RefCell<crate::context::DetailsPageContextStore>> =
            details_context.clone();
        move || clear_modal_and_return_to_details(details_context.clone())
    });

    let item_builder_inner_type: Box<ComposableBuilder<StackComposable>> = Box::new({
        let return_to_details = return_to_details.clone();
        move |ctx| {
            let Some(modal_type) = modal_type.try_get() else {
                DETAILS_LOGGER.error("modal type unavailable aborting with no modal");
                return None;
            };

            match modal_type {
                DetailsModal::None => None,
                DetailsModal::MorePurchaseOptions => {
                    compose_option! { MorePurchaseOptions() }
                }
                DetailsModal::PreOrderDetails(title_action) => {
                    let cancel_cb = return_to_details.clone();
                    compose_option! { PreOrderDetailsModal(action: title_action, okay_cb: move || cancel_cb()) }
                }
                DetailsModal::Suppression(title_action) => {
                    let cancel_cb = return_to_details.clone();
                    compose_option! { SuppressionModal(action: title_action, okay_cb: move || cancel_cb()) }
                }
                DetailsModal::RequireSignIn => {
                    let cancel_cb = return_to_details.clone();
                    compose_option! { RequireSignInModal(cancel_cb: move || cancel_cb()) }
                }
                DetailsModal::LanguagesAndMore => {
                    let cancel_cb = return_to_details.clone();
                    compose_option! { LanguagesAndMore(okay_cb: move || cancel_cb())}
                }
                DetailsModal::Recap => {
                    let details_context = details_context.clone();
                    compose_option! { RecapContainer(details_context, on_closed: return_to_details.clone()) }
                }
                DetailsModal::CustomerService => {
                    let cancel_cb = return_to_details.clone();
                    compose_option! { CustomerServiceModal(okay_cb: move || cancel_cb()) }
                }
                DetailsModal::ContentUnavailable => {
                    let cancel_cb = return_to_details.clone();
                    compose_option! { ContentUnavailableModal(
                        okay_cb: move || cancel_cb()
                    ) }
                }
            }
        }
    });

    let on_back: Rc<dyn Fn()> = Rc::new(move || match modal_type.get_untracked() {
        DetailsModal::Recap => {}
        _ => return_to_details(),
    });

    compose! {
        Stack() {
            Memo(item_builder: item_builder_inner_type)
        }
        .focus_pointer_control(modal_pointer_control)
        .focus_window()
        .on_key_down(KeyCode::Backspace, {
            let on_back = on_back.clone();
            move || on_back()
        })
        .on_key_down(KeyCode::Escape, move || on_back())
        .test_id(DETAILS_MODAL_MANAGER_ITEM_TEST_ID)
    }
}

#[Composer]
pub fn DetailsModalManager(
    ctx: &AppContext,
    details_context: DetailsPageContext,
) -> StackComposable {
    let modal_opacity = details_context.borrow().state.modal_state.modal_opacity;
    let modal_type = details_context.borrow().state.modal_state.active_modal;
    setup_transition_controller(ctx, &details_context);

    let should_render = Signal::derive(ctx.scope(), move || {
        !matches!(modal_type.try_get(), Some(DetailsModal::None))
    });

    compose! {
        Stack() {
            if should_render.try_get().unwrap_or(false) {
                DetailsModalWrapper(details_context: details_context.clone(), modal_type)
            }
        }
        .opacity(modal_opacity)
        .height(SCREEN_HEIGHT)
        .width(SCREEN_WIDTH)
        .test_id(DETAILS_MODAL_MANAGER_TEST_ID)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::context::test_utils::{
        create_or_use_details_context_test, mock_out_atf_response, use_details_page_context_test,
    };
    use crate::network::mock_responses::{
        MOCK_RESPONSE_ATF_WITH_EPISODE_RECAPS, MOCK_RESPONSE_VOD_MPO_99_ATF,
    };
    use crate::ui::test_ids::DETAILS_MPO_TEST_ID;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use mockall::TimesRange;
    use rstest::rstest;

    #[test]
    fn does_not_show_full_screen_modal_when_given_none() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let context = create_or_use_details_context_test(&ctx);

                compose! {
                    DetailsModalManager(details_context: context)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let modal_node = node_tree.find_by_test_id(DETAILS_MODAL_MANAGER_ITEM_TEST_ID);
                assert_node_does_not_exist!(modal_node);
            },
        )
    }

    #[test]
    fn should_render_mpo_when_selected_as_modal() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let context = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_VOD_MPO_99_ATF.to_string(),
                    TimesRange::from(1),
                );
                context.borrow().get_atf();

                compose! {
                    DetailsModalManager(details_context: context)
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let root_node = node_tree.find_by_test_id(DETAILS_MODAL_MANAGER_TEST_ID);
                let modal_node = root_node
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_TEST_ID)
                    .find_first();
                assert_node_does_not_exist!(modal_node);

                let context = use_details_page_context_test(scope);
                context
                    .borrow()
                    .state
                    .modal_state
                    .active_modal
                    .set(DetailsModal::MorePurchaseOptions);

                let node_tree = test_game_loop.tick_until_done();
                let root_node = node_tree.find_by_test_id(DETAILS_MODAL_MANAGER_TEST_ID);
                let modal_node = root_node
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_TEST_ID)
                    .find_first();
                assert_node_exists!(modal_node);
            },
        )
    }

    #[test]
    fn should_render_content_unavailable_when_details_type_is_content_unavailable() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let context = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_VOD_MPO_99_ATF.to_string(),
                    TimesRange::from(1),
                );
                context.borrow().get_atf();

                compose! {
                    DetailsModalManager(details_context: context)
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let root_node = node_tree.find_by_test_id(DETAILS_MODAL_MANAGER_TEST_ID);
                let modal_node = root_node
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_TEST_ID)
                    .find_first();
                assert_node_does_not_exist!(modal_node);

                let context = use_details_page_context_test(scope);
                context
                    .borrow()
                    .state
                    .modal_state
                    .active_modal
                    .set(DetailsModal::ContentUnavailable);

                let node_tree = test_game_loop.tick_until_done();
                let root_node = node_tree.find_by_test_id(DETAILS_MODAL_MANAGER_TEST_ID);
                let modal_node = root_node
                    .find_any_child_with()
                    .test_id(DETAILS_MPO_TEST_ID)
                    .find_first();
                assert_node_does_not_exist!(modal_node);
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Backspace)]
    #[case(KeyCode::Escape)]
    fn should_reset_modal_state_on_back_press(#[case] key: KeyCode) {
        ignx_compositron::app::launch_test(
            |ctx| {
                let context = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_VOD_MPO_99_ATF.to_string(),
                    TimesRange::from(1),
                );
                context.borrow().get_atf();

                compose! {
                    DetailsModalManager(details_context: context)
                }
            },
            |scope, mut test_game_loop| {
                let details_ctx = use_details_page_context_test(scope);
                let active_modal = details_ctx.borrow().state.modal_state.active_modal;
                active_modal.set(DetailsModal::MorePurchaseOptions);

                let node_tree = test_game_loop.tick_until_done();

                let root_node = node_tree.find_by_test_id(DETAILS_MODAL_MANAGER_ITEM_TEST_ID);
                assert_eq!(
                    active_modal.get_untracked(),
                    DetailsModal::MorePurchaseOptions
                );

                test_game_loop
                    .send_key_down_up_event_to_node(root_node.borrow_props().node_id, key);
                test_game_loop.tick_until_done();

                assert_eq!(active_modal.get_untracked(), DetailsModal::None);
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Backspace)]
    #[case(KeyCode::Escape)]
    fn should_reset_modal_state_on_back_press_for_recap(#[case] key: KeyCode) {
        // Seperate test for recap because although both should close, the recap modal does this manually following the drawer closing animation.
        ignx_compositron::app::launch_test(
            |ctx| {
                let context = create_or_use_details_context_test(&ctx);
                mock_out_atf_response(
                    ctx.scope(),
                    MOCK_RESPONSE_ATF_WITH_EPISODE_RECAPS.to_string(),
                    TimesRange::from(1),
                );
                context.borrow().get_atf();

                compose! {
                    DetailsModalManager(details_context: context)
                }
            },
            |scope, mut test_game_loop| {
                let details_ctx = use_details_page_context_test(scope);
                let active_modal = details_ctx.borrow().state.modal_state.active_modal;
                active_modal.set(DetailsModal::Recap);

                let node_tree = test_game_loop.tick_until_done();

                let root_node = node_tree.find_by_test_id(DETAILS_MODAL_MANAGER_ITEM_TEST_ID);
                assert_eq!(active_modal.get_untracked(), DetailsModal::Recap);

                test_game_loop
                    .send_key_down_up_event_to_node(root_node.borrow_props().node_id, key);
                test_game_loop.tick_until_done();

                assert_eq!(active_modal.get_untracked(), DetailsModal::None);
            },
        )
    }
}
