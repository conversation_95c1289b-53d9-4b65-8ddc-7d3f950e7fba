{"resource": {"actions": [], "adaptiveActions": [{"childActions": [], "label": "Watch trailer", "metadata": {"refMarker": "atv_dp_watch_trailer_tv", "playbackExperienceMetadata": {"playbackEnvelope": "eyJ0eXAiOiJwbGVuditqd2UiLCJjdHkiOiJwbGVuditqd3MiLCJhbGciOiJBMjU2S1ciLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiYTJ6K3BwX2VuYytzX3AwU0NUZGcifQ.UIJbeaU3C15idoc60TXaIBdywDFGae5nfEH1kRaxvBLtNKlMYdPEB-7CdodKisbNEeJhiAvsFAfHHS5iGtmk_jMMApK2aS1u.jSYna90MKqOmjlKGj3phdw._10AygurkccmFvTWi-gbWs90Qb1UBQOfJ6UIz2eIiQLGirU6fUimQsmx0dK51Yb9qV4Vq7Q_IOj0CbmtKxTVp1kr1T_mQ2nuQkmIHP62hCZ2fvU9AJ40_w0oYE3nn-2uZY1PSkz9X1foc1ns4X5g_4AN_8wB6pgeS7-h440IqH4LpqhozXNT9QzDtYDlFsAvzDpqcNyiOMQ_e6S3XK_lv6sZ97aNyCy8KI6A-BW8O8nHDFtwWXOD60jR5S2Yau8Ts1g3R3CErtCdW3KFhXtsIHumQv_oA_pXxWH1UCzxz9FiUa7jwg0ylB_r0jAnbsUoNeMHkIhJUJhufOv4b2IwrRhnztnojOFSktQbHGa1YKnCbLM8Jcdwpm6htD3kvnTxMQNaTkGVDR3zg0amceSeYBXjbW8bay-xKWkOKm3uKHm5RGRHrQxInLbosj1d32_cvusW5VErjciVFRHg7wloHawoM_w461N0zWjYwjf2B_o-W6NJ-JZjLaxNs5m5uh2Nvi3nJOTrPzivDXN6Mra8atXfO-qevfSESgVY-VkZq-vfh0lNbkEBNnYM1u15gFSkjrcFa3e6JkUdvXRb8ndxlWIdW5qaMZyle1EDEksmdsXgv4Dut-ekCOT2iyWG_aNpc5HWdRV-_YYZU4BbOLg_46ja91-hIPao8hFLNyKdWlM1aZwxaeNnFpa9uiB3zy0yJVVV60kAqsNMD3NHfIUqau6XhJ3DOr-_sS7yR-EQWuNnQZgP9TODxRhW3QMbqSQHzVHgFe3SXKG1Z12sdaQMoc3Lk6JMx4TmRKj9hysJ1A2xqXqtiRAai9kGpHodyO9fA_9fvz8DvSl0dnLD0wxmicfrsm0FYM0VV_jc0SGbNW0qWOaM2lzinrA3SeEAiDbO2qu-Bm-MqCYvadjxVas1oC7hSlqq3b8sEh6udE9Jh_nXijnEAczcXOGvVAAtDnwX74lanrgNRx4-ILaCP4C5v067jfsb5C0Y7uqGKgkqRYBtkoGbqwKM27GtTJBzTrT_CNHtXgtyMYIuwE9HeS2fDh6Jhpbl37mozuWuvCdglAhKmrMw8S5Zu8NCYoAHsJdk-Oc7Nrjt8DJ7ssv_dwrCbmi6e0ZKYN4rHkoGZjU8Cm3e1WqRJVRI7lMkUA58VvwknHF6vnMeffUHGxCAUH4T37PXxIKicOoptCfeOg6ODqURV-TDEsWEUYKou0SiAzepaKwdAQ3DK63eD_NNaYKLetXghu6I-KMXcCYlXQ9TxqemUAMjZRcSMt2jntpypGnw1jvlVRjKKAO4hEeWPf-uf8AGE5LBrXmWWSfd1xYrUL34mT3K78HysRQiwjPPGEOB8rbxmxZun55CT4phN-vvw0UN-Xt_oslWlWzkWPWUCs05p9CX2x6vNv4eLw9661rYRsqetT2XgyS9GBdXnbAMhMP8KWJOjQmLq7-aCv51MqBcJx6rbyoH4EOGYXc0cj7Wu1re2p6HnGyqqGQ-L8fhPEtl79Nug2oma4ostJW1vNzOszW4ggqapi9lEjr44TP4MilS9VTo8_qMkMcNeOMsypMazN4LAdHAt3tlmkycLgbJovUCqXqgucj-R8oFSbhwYZqcQa4qv5MwfZf_Z7nFKIT8Yf4oJrpgZq84jUcm1Fko8tVugGo6GPcyEQUdDY9dQ4HybkMsKyea3KBerU3Q4P5zABQmYTfwB00ksV85pxT_Z138i2YHnElaoBmuvYuSOLyqgB8A8FzVMOWaYvRsivqIlGldaZAPLwLUVFkQAaijjV5RcPaYmbE9BSX7JPdi5_dBkTiMC3P__KpucqP3mkZFphbeW0M6Zks6jaMD6CyZf59mbNX8KTHoGbGIfrzuFYevnfInGSjzivWCLeO05o6grhZGhoNDx2DLvphb8xaiXQkt59GO2OPnP4h_WT5N8fvDm2Vxl3Jo1AvXu1xAa88xN6lJyA-Q0E8gObISXeJ3Ih5Shs023Qmn3ft6Zpca1cJv7-YWmrhTRs8Gs9-b1QujK3PKMZukxrLFCu2p-ghZqZbasZxESfcRLjoQrRf92NUn7rV4e4esyPteyMmq39lEsQr34bMu0RLJ25iK3qbsJHyBkgYt2uk8vm140uuaZCcEY-KbleAm0P-Fu7oKnenDj9_h5CcBw_9RKL3AvBRMKuNxgHHYNirNVrP5WD4XZ_d92VB3cxyCUdXmaSOb-u1XDq2tiSPxcIvoatp8Se5FT46BEgQs9fKqJvCGDMp5H5KTiNL8KGQOc2s-o0_bysghSnm5W85hUV7xbT3D55jJfAafjzEZY0xE-9F_9xtiq51EKPQDSyHX_hytfgQg93dqntqGrLkTC13n95768JVFGwNu8-Wo7dgpuY3cNf-Q1sCwff7xHA7p-SchcoYCySdTuWhX4Mza165B36u_mKO7pY0j4A3J8CDwpOQ7OKYDhnz8DBkPUuWZizIk4d6Q9sfc2e3CALzO0ZLkrxfs0XByXLTl3E28mfWeIgNuCrDXIVqs70VTv9U8g0gRbEOjoDfEEuCdy-6SiyjPMAkRSrC_QktYyfgWJ0N_SpMesIiDzLZhJWjF1ky77wq5s410f6-jw5FSSxhFJcjaSj8r63jNHnJywmt4-_7oNEwET8ktGXY_OrdzyyRMQb_EW65FAr6a09j9IkZdSRf5aQUBQScCR0INovHpAc9UP_cXuXG9PsvfFmie3snHRMhPI7sBk3-DZXB6pFNl7_Wodc7tAButU8A-6ZPi820sHItvUXH4PmM5M6s7vVnscRivi015oz0aLRPaMGU_DNlu3ZAMFpp79jgQOuPpyLCAda5RNpyRk37M2O7dlcDAD5Qu-0yaeon5e1pWyXFMOEl3XuxxpCBiwajL0oHsGtwzxOJ6KUBd5LpetPGQEHXV7-wwMc42U2JkQnoH9vJGevdJ1d8_fqMpkKJS2bdtatVz5kasDcIqLmzPqkFl7x64qISck5DxLBG1azuXlySzB2hj61XSUNF0CbUhW0zFOIk64AnEvZD6Nn3iUr5NGnovkbSeFFNpKeKL2YeDNER1sLlDBR5rqcI10mwOUkFY9U0d99AZGfcHcbbXu8a7E3oq2glETA2Vt5Hsyp1YV9oDz4DzarbVl39VrIODwED32Y0esrkvLx2e._3N6iFAjuSuNcYhv-mgzvsvE-fgEYLUdMk7QH67lbB8", "expiryTime": 1740492952809, "correlationId": "YW16bjEuZHYuZ3RpLmRlNWE4ZTk3LWYwMmYtNDhhNS05Zjg1LWMxOTg3NDhhY2RkNTpTb3VyY2UodHlwZT1Vbml2ZXJzYWxCZW5lZml0LCBpZD1GVk9ELCBvcmRlcj1PcmRlcihpZD1udWxsKSwgc2hhcmVkQnk9bnVsbCk6YW16bjEuZHYucHZpZC4yYTUwZDUxYi05ZDI5LTQzN2MtYWNjOS04MmQxMDUyZGUxNzU6SEQ="}, "position": "NONE", "userPlaybackMetadata": {"runtimeSeconds": 122, "timecodeSeconds": 1, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "FREE", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.de5a8e97-f02f-48a5-9f85-c198748acdd5", "isTrailer": true, "metadataActionType": "Playback"}, "presentation": "Simple"}], "audios": [], "contentDescriptors": ["violence, foul language"], "directors": ["<PERSON>"], "entitlementMessage": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "entityType": "TV Show", "genres": ["Action", "Suspense", "Drama"], "gti": "amzn1.dv.gti.c3730dd1-fc7c-4ae6-aa69-299c5e78cfb0", "hasTrailer": false, "highValueMessage": {"message": "New episode Thursday"}, "metadataBadgeMessage": {"message": "NEW SEASON", "level": "INFO", "type": "BADGE"}, "informationalMessage": {"message": "", "messages": [""]}, "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/14558b57e8e089511a80a89e02414b5f6b9865a677feacad55f608c39f4cb3bc._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/a37b8f4d7e7f3e1f1f1c7431f9ee2ecdb30540fe009c2602443e9bf855208c7a._UY500_UX667_RI_TTW_.jpg", "title16x9": "https://m.media-amazon.com/images/S/pv-target-images/4c31cd58f13628f534fbe50f52a569bd03030b06c721f0a38b1ad022f43cd543._UY500_UX667_RI_V0_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/b56ac909263fea19f9f6aa7ee86df8e25aeb089363f6a37f0458ffeca9e25610._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/4c31cd58f13628f534fbe50f52a569bd03030b06c721f0a38b1ad022f43cd543._UY500_UX667_RI_V0_TTW_.jpg"}, "imdbRating": 8.0, "imdbTconst": "tt9288030", "impressionRefMarker": "atv_dp_atf_tv_t1AAAAAAAA0lr0", "isInWatchlist": false, "maturityRating": {"regulatoryRatingString": "16+"}, "ratingsMetadata": {"count": 0, "rating": 0.0}, "regulatoryRating": "16+", "releaseDate": 1740614400000, "runtimeSeconds": 3159, "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "studios": ["Amazon Studios"], "subtitles": [], "supportingCast": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "synopsis": "<PERSON> reunites with <PERSON> and <PERSON><PERSON><PERSON> and finally shares the painful details of his history with <PERSON>.", "title": "<PERSON>", "titleType": "EPISODE", "applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyDolbyVision": false, "applyDolbyAtmos": false, "applyHdr": false, "applyPrime": false, "applyUhd": false, "currentEpisodeNumber": 4, "seasonGti": "amzn1.dv.gti.1fff81b4-011f-4ba2-a685-486cb4a25f3b", "seasonNumber": 3, "seasonTitle": "Reacher - Season 3", "seriesTitle": "<PERSON>", "seriesGti": "amzn1.dv.gti.47db6971-feec-45b6-bf64-601bee7f0576", "moods": ["Serious"], "starlight": {"explorePanelURL": "https://www.amazon.co.uk/gp/video/detail/amzn1.dv.gti.1fff81b4-011f-4ba2-a685-486cb4a25f3b?ref_=atv_dp_exp_rchr_s3_qrscan#tab-content-explore", "refMarker": "atv_dp_exp_rchr_s3_expbutton", "isRecommendedDefaultTab": true, "headlineText": "Dive deeper into the world", "recommendedTabName": "Explore & Shop"}, "trailerGti": "amzn1.dv.gti.de5a8e97-f02f-48a5-9f85-c198748acdd5", "widgetSections": {"aboveTheFold": {"widgets": [{"widgetName": "WatchTrailer", "preferredTrailerGTI": "amzn1.dv.gti.de5a8e97-f02f-48a5-9f85-c198748acdd5", "videoMaterialType": "FEATURE"}]}}, "userReaction": {"shouldShowReactions": true, "reaction": "DISLIKE"}, "isEventHidden": false, "isTravelingCustomer": false, "recaps": []}, "metadata": {"requestId": "t46ol2ICbaksFlaYh0Ti4gfv06yRIq7X3PtUupsDq5MLvOWEuVvvCA==", "requestedTransformId": "lr/detailsPage/detailsPageATF", "domain": "prod", "realm": "eu-west-1", "timestamp": "2025-02-25T14:00:52.830626Z"}}