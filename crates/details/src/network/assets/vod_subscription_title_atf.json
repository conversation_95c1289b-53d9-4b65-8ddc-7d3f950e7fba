{"resource": {"actions": [{"childActions": [], "label": "Watch with Paramount+{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_dp_atf_mv_signup_3p_bb_t1P4CAAAAA0lr0", "benefitId": "paramountplusgb", "metadataActionType": "AcquisitionSVOD"}, "presentation": "Simple"}, {"childActions": [], "label": "Rent movie {lineBreak}HD £3.49", "metadata": {"refMarker": "atv_dp_atf_rent_hd_mv_bb_t1P4AAAAAA0lr1", "contentType": "MOVIE", "offerToken": "amzn.dv.offertoken.v1.eyJ2ZXJzaW9uIiA6IDEsICJkYXRhIiA6IHsiQVNJTiIgOiAiQjAwSDM3SlVaNCIsICJwcmljZSIgOiB7ImN1cnJlbmN5IiA6ICJHQlAiLCAidmFsdWUiIDogIjMuNDkifX19", "offerType": "TVOD_RENTAL", "videoQuality": "HD", "message": "Rentals include 30 days to start watching this video and 48 hours to finish once started.", "metadataActionType": "AcquisitionTVOD"}, "presentation": "Simple"}, {"childActions": [], "label": "Buy movie {lineBreak}HD {strike}£5.99{end} £3.99", "metadata": {"refMarker": "atv_dp_atf_buy_hd_mv_bb_t1P4AAAAAA0lr2", "contentType": "MOVIE", "offerToken": "amzn.dv.offertoken.v1.eyJ2ZXJzaW9uIiA6IDEsICJkYXRhIiA6IHsiQVNJTiIgOiAiQjAwSDM3SlRZUSIsICJwcmljZSIgOiB7ImN1cnJlbmN5IiA6ICJHQlAiLCAidmFsdWUiIDogIjMuOTkifX19", "offerType": "TVOD_PURCHASE", "videoQuality": "HD", "message": "The price before discount is the median price for the last 90 days.", "metadataActionType": "AcquisitionTVOD"}, "presentation": "Simple"}, {"childActions": [{"childActions": [{"childActions": [], "label": "Watch with Paramount+{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_dp_mpo_mv_signup_3p_bb_t1P4CAAAAA0lr0", "benefitId": "paramountplusgb", "metadataActionType": "AcquisitionSVOD"}, "presentation": "Simple"}], "label": "Included with Paramount+ on Amazon for £6.99/month after trial", "metadata": {"metadataActionType": "MoreWaysSection"}, "presentation": "ModalSection"}, {"childActions": [{"childActions": [], "label": "Rent movie {lineBreak}HD £3.49", "metadata": {"refMarker": "atv_dp_mpo_rent_hd_mv_bb_t1P4AAAAAA0lr1", "contentType": "MOVIE", "offerToken": "amzn.dv.offertoken.v1.eyJ2ZXJzaW9uIiA6IDEsICJkYXRhIiA6IHsiQVNJTiIgOiAiQjAwSDM3SlVaNCIsICJwcmljZSIgOiB7ImN1cnJlbmN5IiA6ICJHQlAiLCAidmFsdWUiIDogIjMuNDkifX19", "offerType": "TVOD_RENTAL", "videoQuality": "HD", "metadataActionType": "AcquisitionTVOD"}, "presentation": "Simple"}, {"childActions": [], "label": "Rent movie {lineBreak}SD £2.49", "metadata": {"refMarker": "atv_dp_mpo_rent_sd_mv_bb_t1P4AAAAAA0lr2", "contentType": "MOVIE", "offerToken": "amzn.dv.offertoken.v1.eyJ2ZXJzaW9uIiA6IDEsICJkYXRhIiA6IHsiQVNJTiIgOiAiQjAwRllOTUVWUSIsICJwcmljZSIgOiB7ImN1cnJlbmN5IiA6ICJHQlAiLCAidmFsdWUiIDogIjIuNDkifX19", "offerType": "TVOD_RENTAL", "videoQuality": "SD", "metadataActionType": "AcquisitionTVOD"}, "presentation": "Simple"}], "label": "Rent", "metadata": {"disclaimerText": "Rentals include 30 days to start watching this video and 48 hours to finish once started.", "metadataActionType": "MoreWaysSection"}, "presentation": "ModalSection"}, {"childActions": [{"childActions": [], "label": "Buy movie {lineBreak}HD {strike}£5.99{end} £3.99", "metadata": {"refMarker": "atv_dp_mpo_buy_hd_mv_bb_t1P4AAAAAA0lr3", "contentType": "MOVIE", "offerToken": "amzn.dv.offertoken.v1.eyJ2ZXJzaW9uIiA6IDEsICJkYXRhIiA6IHsiQVNJTiIgOiAiQjAwSDM3SlRZUSIsICJwcmljZSIgOiB7ImN1cnJlbmN5IiA6ICJHQlAiLCAidmFsdWUiIDogIjMuOTkifX19", "offerType": "TVOD_PURCHASE", "videoQuality": "HD", "metadataActionType": "AcquisitionTVOD"}, "presentation": "Simple"}, {"childActions": [], "label": "Buy movie {lineBreak}SD {strike}£5.99{end} £3.99", "metadata": {"refMarker": "atv_dp_mpo_buy_sd_mv_bb_t1P4AAAAAA0lr4", "contentType": "MOVIE", "offerToken": "amzn.dv.offertoken.v1.eyJ2ZXJzaW9uIiA6IDEsICJkYXRhIiA6IHsiQVNJTiIgOiAiQjAwRllOTURQSSIsICJwcmljZSIgOiB7ImN1cnJlbmN5IiA6ICJHQlAiLCAidmFsdWUiIDogIjMuOTkifX19", "offerType": "TVOD_PURCHASE", "videoQuality": "SD", "metadataActionType": "AcquisitionTVOD"}, "presentation": "Simple"}], "label": "Buy", "metadata": {"disclaimerText": "The price before discount is the median price for the last 90 days.", "metadataActionType": "MoreWaysSection"}, "presentation": "ModalSection"}], "label": "More Purchase{lineBreak}Options", "metadata": {"refMarker": "atv_dp_atf_mv_mpo_t1P4AAAAAA0lr0", "metadataActionType": "MoreWays"}, "presentation": "Modal"}, {"childActions": [], "label": "Watch Party", "metadata": {"refMarker": "atv_dp_atf_mv_watchparty_t1P4AAAAAA0lr0", "hasPlayback": false, "metadataActionType": "WatchParty"}, "presentation": "Simple"}], "audios": [{"locale": "en_US", "name": "English"}], "contentDescriptors": ["violence", "substance use", "alcohol use", "foul language", "sexual content"], "directors": ["<PERSON>"], "entitlementMessage": {"message": "Free 7 day trial of Paramount+, auto renews at £6.99/month, rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "entitlementMessageMetadata": {"entitlementMessage": [{"text": "Watch with Paramount+{lineBreak}Start your 7-day free trial"}], "entitlementImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/m-r/paramountplusgb/logos/3p-logo._CB635951060_CB_.png", "currencyCode": "GBP"}, "entityType": "Movie", "genres": ["Comedy", "Romance", "Young Adult Audience"], "gti": "amzn1.dv.gti.56a9f693-3e3e-ae2e-48f2-536201b3db90", "hasTrailer": true, "highValueMessage": {"message": "", "icon": null, "level": null, "type": null}, "informationalMessage": {"message": "The price before discount is the median price for the last 90 days. Rentals include 30 days to start watching this video and 48 hours to finish once started.", "icon": null, "level": null, "type": null}, "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/19a8e9a0014206ad7e06624574c0b085030b24a9d8a0958a012b4a1d1e390289._RI_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/58970b75ece693d1a11f120b72d34a5c92c6e50966eb4e4c7e1ce9358fe35e63._UY500_UX375_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/0bab722cb14ed52b4c7e9456b3d7183f50ebf97d9d140fd17a28d1b9f5a37e77._UY500_UX667_RI_TTW_.jpg", "cover": "https://ecx.images-amazon.com/images/S/pv-target-images/0bab722cb14ed52b4c7e9456b3d7183f50ebf97d9d140fd17a28d1b9f5a37e77._UY500_UX667_RI_TTW_.jpg"}, "imdbRating": 6.9, "imdbTconst": "tt0112697", "impressionRefMarker": "atv_dp_atf_mv_t1P4AAAAAA0lr0", "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "ratingsMetadata": {"count": 4304, "rating": 4.5}, "regulatoryRating": "12", "releaseDate": 814147200000, "runtimeSeconds": 5832, "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "studios": ["Viacom"], "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "supportingCast": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "synopsis": "<PERSON> might never have imagined that her 1816 novel <PERSON> could be turned into a fresh and satirical look at ultra-rich teenagers in a Beverly Hills high school. <PERSON> stars.", "title": "CLUELESS", "titleType": "MOVIE", "applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyDolbyVision": false, "applyDolbyAtmos": false, "applyHdr": false, "applyPrime": false, "applyUhd": false, "moods": ["Passionate", "Fun", "Understated"], "starlight": {"explorePanelURL": null, "refMarker": null, "isRecommendedDefaultTab": true, "treatmentPreviewImageUrl": null, "headlineText": "Dive deeper into the world"}, "widgetSections": {"linearAirings": {"widgets": []}, "standardContent": {"widgets": []}, "aboveTheFold": {"widgets": [{"widgetName": "WatchTrailer", "preferredTrailerGTI": "amzn1.dv.gti.56a9f693-3e3e-ae2e-48f2-536201b3db90", "videoMaterialType": "TRAILER"}]}}, "userReaction": {"shouldShowReactions": true, "reaction": null}, "isEventHidden": false, "isTravelingCustomer": false}, "metadata": {"requestId": "7015d6e302c41cf1e676639f3cc0072b", "requestedTransformId": "lr/detailsPage/detailsPageATF", "domain": "prod", "realm": "eu-west-1", "timestamp": "2024-02-16T13:52:43.224613Z"}}