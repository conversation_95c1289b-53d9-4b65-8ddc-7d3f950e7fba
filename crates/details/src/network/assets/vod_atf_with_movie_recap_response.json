{"resource": {"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_atf_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 4162, "timecodeSeconds": 959, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_atf_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 4162, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "adaptiveActions": [], "audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "directors": ["<PERSON>"], "entitlementMessage": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "entitlementMessageMetadata": {"entitlementMessage": [{"text": "Resume"}], "entitlementImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-color._CB554929912_.png"}, "entityType": "TV Show", "genres": ["Action", "Adventure", "Drama", "Fantasy"], "gti": "amzn1.dv.gti.fbb73ae3-eeb4-4d24-982a-b7a9f5f60fdf", "hasTrailer": false, "highValueMessage": {"message": "PRIMETIME EMMYS® 6X nominee"}, "informationalMessage": {"message": "", "messages": [""]}, "images": {"hero": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b34d5f560672f999102620ad9638f978fbba17e8e1e9f677f8f12776d9f8c4bd._RI_TTW_.jpg", "providerLogo": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/0393aff2ef3474c5a32f418bcbe6d67a04975157242451aeb0622b94be07f243._UY500_UX667_RI_TTW_.jpg", "title16x9": "https://m.media-amazon.com/images/S/pv-target-images/0705e32f6d314bad7eba9d35e74daafbca16857d5b4b937d16be1726d574ca5f._UY500_UX667_RI_V0_TTW_.jpg", "titleLogo": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/0705e32f6d314bad7eba9d35e74daafbca16857d5b4b937d16be1726d574ca5f._UY500_UX667_RI_V0_TTW_.jpg"}, "imdbRating": 6.9, "imdbTconst": "tt7631058", "impressionRefMarker": "atv_dp_atf_tv_t1ACAAAAAA0lr0", "isInWatchlist": true, "maturityRating": {"regulatoryRatingString": "TV-14"}, "ratingsMetadata": {"count": 30115, "rating": 3.5}, "regulatoryRating": "TV-14", "releaseDate": 1662681600000, "runtimeSeconds": 4162, "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "studios": ["Amazon Studios"], "subtitles": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "ko_KR", "name": "한국어"}], "supportingCast": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "synopsis": "<PERSON><PERSON><PERSON> finds himself a captive; <PERSON><PERSON><PERSON> and <PERSON><PERSON> explore a legendary kingdom; <PERSON><PERSON><PERSON> is given a new assignment; <PERSON><PERSON> faces the consequences.", "title": "<PERSON><PERSON>", "titleType": "EPISODE", "applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyDolbyVision": false, "applyDolbyAtmos": false, "applyHdr": true, "applyPrime": true, "applyUhd": true, "currentEpisodeNumber": 3, "seasonGti": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935", "seasonNumber": 1, "seasonTitle": "The Lord of the Rings: The Rings of Power - Season 1", "seriesTitle": "The Lord of the Rings: The Rings of Power", "seriesGti": "amzn1.dv.gti.1a0d302f-2fc3-4db4-8219-d6b277fbceb8", "moods": ["Exciting", "<PERSON>"], "starlight": {"explorePanelURL": "https://a.co/d/c9wiLAJ?ref_=atv_dp_exp_rop_s1_qrscan#tab-content-explore", "refMarker": "atv_dp_exp_rop_s1_expbutton", "isRecommendedDefaultTab": true, "treatmentPreviewImageUrl": "https://m.media-amazon.com/images/G/01/pv_starlight/trop-c9184079-2c78-432f-9f38-49d5ab3c4158/3PLR_HeroHall_8e11e680-9e43-49b3-8660-802e8c3f7c57.jpg", "headlineText": "Dive deeper into the world"}, "trailerGti": "amzn1.dv.gti.33a31012-a188-41ac-9014-89990765c6e0", "widgetSections": {"aboveTheFold": {"widgets": [{"widgetName": "WatchTrailer", "preferredTrailerGTI": "amzn1.dv.gti.33a31012-a188-41ac-9014-89990765c6e0", "videoMaterialType": "FEATURE"}]}}, "userReaction": {"shouldShowReactions": true, "reaction": "LIKE"}, "isEventHidden": false, "isTravelingCustomer": false, "recaps": [{"recapType": "MOVIE", "backgroundImage": {"url": "Movie default background url", "height": 1080, "width": 1920}, "recapSegments": [{"backgroundImage": {"url": "Movie recap segment background url - 0", "height": 1080, "width": 1920}, "text": "Movie recap segment text - 0"}, {"backgroundImage": {"url": "Movie recap segment background url - 1", "height": 1080, "width": 1920}, "text": "Movie recap segment text - 1"}, {"backgroundImage": {"url": "Movie recap segment background url - 2", "height": 1080, "width": 1920}, "text": "Movie recap segment text - 2"}]}]}, "metadata": {"requestId": "EGAa6p9zQ-cT8YoRZaTdh8NduCcPQLgHHPqFoWGhvzyrZRQJp1o85w==", "requestedTransformId": "lr/detailsPage/detailsPageATF", "domain": "prod", "realm": "us-east-1", "timestamp": "2024-10-22T11:02:21.346116Z"}}