{"resource": {"seasonsEpisodes": {"selectedSeasonIndex": 0, "selectedEpisodeIndex": 0, "seasons": [{"gti": "amzn1.dv.gti.ed351061-599a-4544-bab9-943d8623504d", "seasonNumber": 1, "offerIcon": null}, {"gti": "amzn1.dv.gti.ed351061-599a-4544-bab9-943d8623504c", "seasonNumber": 2, "offerIcon": null}, {"gti": "amzn1.dv.gti.ed351061-599a-4544-bab9-943d8623504e", "seasonNumber": 3, "offerIcon": null}], "episodesBySeasonIndex": {"0": [{"actions": [{"childActions": [], "label": "Episode 1{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.da7482da-df08-4bfc-969f-cf1346a11976", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/4b4b22eb4d273f52f21e353e39136bf230c107662fcdb0514c335256c99c9def._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6139aa8146173cdb11cd6189459cebcf24ddcf069869f383c093e0988c854a84._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1710806400000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "<PERSON>, <PERSON> and <PERSON> have all traded in their wild nights for a quiet suburban life in the Valley. <PERSON> and <PERSON><PERSON> try to juggle three kids. <PERSON> and <PERSON> navigate life as parents and business partners.", "title": "Welcome to the Valley", "titleType": "EPISODE", "currentEpisodeNumber": 1, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": true, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 2{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.cb35e8d4-6a22-4e23-9814-adbcf51a7374", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/53c8afb807993e2b083d3b93a1eea3445956f8590f8a40860a99e57c6485d291._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e8a8299a70eaaab02dce11e0866614014692b092142e1c7cbdfd6ca26e167215._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1711411200000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "In the aftermath of <PERSON>'s birthday party, <PERSON><PERSON> is angry about <PERSON> pantsing her husband, <PERSON>, in front of the group.", "title": "Tit for Tat", "titleType": "EPISODE", "currentEpisodeNumber": 2, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 3{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.b4cbc24d-9d73-4de6-abe9-b3c32679a664", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f869af98ae0b72394cb07c8966a4aceff253f4306f1972a9fed5bec4e9d16530._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/1cdfbc635a2640b16a6e3651ffb856cdfae6c4387e02f2f8f81efbe7e7076755._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1712016000000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "While everyone turns against <PERSON> for the accusations she hurled at <PERSON> during <PERSON>'s Night, <PERSON> tries to help her understand the impact of her actions.", "title": "Doubting <PERSON>", "titleType": "EPISODE", "currentEpisodeNumber": 3, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 4{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.cf9c22e6-18f3-4385-8140-55b7bf8b72e2", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/2740e7378285e028861b45c4b222ffa45d0b2652b586e181aafc2afdff37f6d3._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e2bc0c9c01c66430c0dcd8f04737efadc5153a379e1d175c206db31451ca2a9d._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1712620800000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "<PERSON> and <PERSON> deal with the aftermath of their Capri-themed dinner party, leaving the group on shaky ground with <PERSON>.", "title": "<PERSON><PERSON>", "titleType": "EPISODE", "currentEpisodeNumber": 4, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 5{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.3221e257-716a-40ac-a0c3-99e2e0bb9936", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/2d9a72aadf177b400231ef5a10147a7b59796634c107ef3cb34fd08b5a6e7ba3._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://m.media-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1713225600000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "The ladies retreat to Malibu for a girls' night while leaving the boys to daddy daycare.", "title": "The 'D' Word", "titleType": "EPISODE", "currentEpisodeNumber": 5, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 6{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.66b3ba3a-4aa3-4606-8785-6d9a65bcf218", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/21cae55c7728f0ab01294825fd0206c5759f546917a3f0f67615aa8b7961746e._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1713830400000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "<PERSON> tries to get <PERSON> to do his dirty work and find out if rumors about <PERSON> are true.", "title": "Congrats On Your Hair Loss", "titleType": "EPISODE", "currentEpisodeNumber": 6, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 7{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.8db58b1b-242e-49e2-9a07-e08633a6338a", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/b7b175b48b910b1768bffb5c6a0630743a840a072cdb5e8c053cb7369312ec4d._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/0c801ec830f301989cc0f3f70bdbb4d5bdd9b10399ee7e5befbf01f809bc8cda._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1714435200000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "<PERSON> and <PERSON> confront <PERSON> about cheating rumors.", "title": "The #1 Gossip Of The Group", "titleType": "EPISODE", "currentEpisodeNumber": 7, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 8{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.29aa8eea-4ecf-4db1-ac5a-9d65618e9073", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/01efe00d57cde718df84f6208bc220b1bc1f09670f6d265472b27bbd6ce1f488._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/b525b9563858dc8ef6c04e99b9768e82528fef32e75971dca649b4e7f2f6de36._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1715040000000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "After a disastrous date night, <PERSON> and <PERSON>'s struggles continue.", "title": "The Invite Fight", "titleType": "EPISODE", "currentEpisodeNumber": 8, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 9{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.96148a61-455c-4995-b5d2-387760fa233b", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/01c976283a7367a89daf417b8fb007d4e31ee17f6a5012d5b1a64b3b5bc31b47._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/212bfa188d38c9e8eca25ab218f36adc2c6ebf5b67989723f5647c29a82935c8._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1715644800000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "<PERSON> returns from his spiritual retreat claiming to be a changed man.", "title": "The Big Bear Bombshell", "titleType": "EPISODE", "currentEpisodeNumber": 9, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 10{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.80fd5510-2675-440c-9a83-feb60127edcd", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6bef598ebc0bc3440857529d5b8402d9ae5a198038b75a764d03b04ddc766e18._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/2055639d1c0a787ccd96ed37232bf5cf8d92b2c7742bb227ac3eedaf0c1b7092._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1716249600000, "runtimeSeconds": 2555, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "<PERSON> finally learns about the rumors swirling about his wife, <PERSON>.", "title": "Babymoon Mayhem", "titleType": "EPISODE", "currentEpisodeNumber": 10, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Episode 11{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 2555, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "en_US", "name": "English"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.5e287694-d1bd-4055-a67f-fab03f696979", "images": {"hero": "https://m.media-amazon.com/images/S/pv-target-images/486bacb893f8f363b0057f6b5d3f06142becc1393d1ea5964eff74d28a6838cb._RI_V0_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e18d0a24212c8f0efc93b11ba18515bc3c26f5e235c317c80adc1c46568f6dc0._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/925ddd9a1e5e0873e5f2e7ad0b5a6855913f7e1fdef0880e48851685fc27d0f6._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/178354114b84afe7eb4d8a8f125075159f163ceb2ed28d303e9bb0a8a87c504b._UY500_UX667_PGLock-Icon-Landscape_RI_V0_TTW_.jpg"}, "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1716854400000, "runtimeSeconds": 2281, "ratingsMetadata": {"count": 1, "rating": 5}, "subtitles": [{"locale": "en_US", "name": "English [CC]"}], "synopsis": "The women throw a surprise baby shower for <PERSON>. The whole group indulges in a dinner for the final night in Big Bear.", "title": "Darkside Danny", "titleType": "EPISODE", "currentEpisodeNumber": 11, "applyCC": true, "applyUhd": false, "applyDolby": false, "applyPrime": false, "applyHdr": false, "applyDolbyVision": false, "applyAudioDescription": false}, "isFirstInSeason": false, "isLastInSeason": true, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}]}}, "castAndCrew": [{"id": "/name/nm1731694/", "name": "<PERSON>", "image": {"id": "/name/nm1731694/images/rm1734394880", "url": "http://ecx.images-amazon.com/images/M/MV5BMTYwMTgwOTcyNV5BMl5BanBnXkFtZTgwNDg1NzY3MjE@._V1_.jpg", "width": 1500, "height": 1800}, "miniBio": "<PERSON> was born on October 17, 1983 in Three Rivers, Michigan, USA. He is an actor, known for 21 & Over (2013), iCarly (2007) and Avengers: Infinity War (2018). He has been married to <PERSON><PERSON> since October 17, 2015. They have three children."}, {"id": "/name/nm3691901/", "name": "<PERSON>", "image": {"id": "/name/nm3691901/images/rm2291376128", "url": "http://ecx.images-amazon.com/images/M/MV5BNzA1NzMxNjE3Ml5BMl5BanBnXkFtZTcwOTA0ODMyOQ@@._V1_.jpg", "width": 449, "height": 600}, "miniBio": "<PERSON> was born on February 17, 1983 in Dearborn, Michigan, USA. She is an actress, known for Blood on Canvas (2013), Behind Your Eyes (2011) and 23 Minutes to Sunrise (2012)."}, {"id": "/name/nm8408736/", "name": "<PERSON>", "image": {"id": "/name/nm8408736/images/rm884367104", "url": "http://ecx.images-amazon.com/images/M/MV5BNWJlYzBjNGItOWQ1Yi00NzU4LThjZTYtNTUxZTIzN2QxMGUzXkEyXkFqcGdeQXVyNjc5Mjg0NjU@._V1_.jpg", "width": 667, "height": 1000}, "miniBio": "<PERSON> is known for God Bless the Broken Road (2018), <PERSON><PERSON> feat. <PERSON><PERSON>: Drinking Side of Country (2012) and The Valley (2024). She has been married to <PERSON> since June 29, 2019. They have one child."}, {"id": "/name/nm5470001/", "name": "<PERSON>", "image": {"id": "/name/nm5470001/images/rm2459148288", "url": "http://ecx.images-amazon.com/images/M/MV5BMjYwNTE0Mjc2MV5BMl5BanBnXkFtZTcwNTE0ODMyOQ@@._V1_.jpg", "width": 449, "height": 600}, "miniBio": "<PERSON> was born on July 11, 1979 in Shelby Township, Michigan, EUA. He is a producer and actor, known for Savage Salvation (2022), Sharknado 4: The 4th Awakens (2016) and Vanderpump Rules (2013). He has been married to <PERSON> since June 29, 2019. They have one child."}, {"id": "/name/nm6560516/", "name": "<PERSON><PERSON>", "image": {"id": "/name/nm6560516/images/rm2567200256", "url": "http://ecx.images-amazon.com/images/M/MV5BMjAzNjAzNjM4OV5BMl5BanBnXkFtZTgwNTM2NjYyNDM@._V1_.jpg", "width": 1365, "height": 2048}, "miniBio": "<PERSON><PERSON> was born on February 16, 1990 in Sacramento, California, USA. She has been married to <PERSON> since October 17, 2015. They have three children."}, {"id": "/name/nm3946655/", "name": "<PERSON>", "image": {"id": "/name/nm3946655/images/rm3540076033", "url": "http://ecx.images-amazon.com/images/M/MV5BNzI0M2MxZTEtZTA2ZC00ZmY3LWFmNjItMDM3N2U4MjAzN2IyXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_.jpg", "width": 920, "height": 1140}, "miniBio": null}, {"id": "/name/nm15738114/", "name": "<PERSON>", "image": {"id": "/name/nm15738114/images/rm3640739329", "url": "http://ecx.images-amazon.com/images/M/MV5BM2U5YjJmM2QtZjkwNS00MTkxLWI1OWItNmEyZTMxOGZjOTNlXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_.jpg", "width": 862, "height": 1094}, "miniBio": null}, {"id": "/name/nm15738112/", "name": "<PERSON>", "image": {"id": "/name/nm15738112/images/rm3573630465", "url": "http://ecx.images-amazon.com/images/M/MV5BZmFkNGVhNzAtN2Q1Yy00NGY5LWExZjQtNDljYmIwOGYyMmIzXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_.jpg", "width": 908, "height": 1168}, "miniBio": null}, {"id": "/name/nm15738113/", "name": "<PERSON>", "image": {"id": "/name/nm15738113/images/rm3355526657", "url": "http://ecx.images-amazon.com/images/M/MV5BZjE0NmMxN2EtNzhiZi00MmUwLTgyODAtNzNhOWI4YTA4OGMzXkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_.jpg", "width": 958, "height": 1160}, "miniBio": null}, {"id": "/name/nm14717107/", "name": "<PERSON>", "image": {"id": "/name/nm14717107/images/rm3271640577", "url": "http://ecx.images-amazon.com/images/M/MV5BM2Y3MTAwNDgtOWM4Ny00N2RlLTljMzktMWIxNTBmMTE1NWU2XkEyXkFqcGdeQXVyMTQxNzMzNDI@._V1_.jpg", "width": 1010, "height": 1234}, "miniBio": null}], "containerList": [{"facet": {"text": null}, "title": "More from <PERSON><PERSON>", "titleImageUrl": null, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiMjk0Mzk5NDMtYmU4OC00NzkzLWI1NzEtY2YxNmU5NDg0ZjJiIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6ImVjYzkwOGFmM2I4ZThjMDA1YTQxMmJlMWRkZDdhOTRlOjE3MTczNTQ3ODcwMDAiLCJhcE1heCI6NzUsInN0cmlkIjoiMjpUU0YzODlFQTE3Njc0MyMjTU5RWEUzM1ZPTlNXWSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUDQ3YTI3MDY1ZTg1MzJjZWQ2N2U3NGY5NzJjZDRkMjM3OGNhZGY4MDVkMDVkNjY4N2FkNTkxM2QwYjU2NjczNzVcIn0iLCJvcmVxayI6IkUzRk5RVU9OVXZWVUZ3Rzg1UlFwemUvd3RjaWx3MWdYSDFQZDd0SC9VMFk9Iiwib3JlcWt2IjoxfQ==", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmVkMzUxMDYxLTU5OWEtNDU0NC1iYWI5LTk0M2Q4NjIzNTA0ZIuGZGV0YWlsjI6fMjpUU0YzODlFQTE3Njc0MyMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "pageId": "amzn1.dv.gti.ed351061-599a-4544-bab9-943d8623504d", "pageType": "detail", "pageContext": {"pageType": "detail", "pageId": "amzn1.dv.gti.ed351061-599a-4544-bab9-943d8623504d"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmVkMzUxMDYxLTU5OWEtNDU0NC1iYWI5LTk0M2Q4NjIzNTA0ZIuGZGV0YWlsjI6fMjpUU0YzODlFQTE3Njc0MyMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "Vanderpump Rules Season 1", "gti": "amzn1.dv.gti.ccba11c7-bc39-7731-71b0-c00fe69d5b2f", "transformItemId": "amzn1.dv.gti.ccba11c7-bc39-7731-71b0-c00fe69d5b2f", "synopsis": "Saucy British beauty <PERSON> welcomes us behind-the-scenes of her Hollywood restaurant, SUR, and its mischievous staff.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary", "Unscripted"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/10fa50a346e43da6456098e4bf3af81494e0728bceb37fbb8dbfd2800555edfd.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/10b87789add4af542173fd796a0ca42b7f30011a1278295ab28df27f346aec75._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1357516800000, "runtimeSeconds": null, "runtime": null, "overallRating": 4.2, "totalReviewCount": 41, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 11, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.ccba11c7-bc39-7731-71b0-c00fe69d5b2f", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_1"}, "refMarker": "dp_amz_c_TS176743_1_1", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 2X nominee", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.ccba11c7-bc39-7731-71b0-c00fe69d5b2f", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_1"}, "refMarker": "dp_amz_c_TS176743_1_1", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "Vanderpump Rules", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of New Jersey Season 1", "gti": "amzn1.dv.gti.00ba07c0-2e24-e61a-d558-408f272a3bce", "transformItemId": "amzn1.dv.gti.00ba07c0-2e24-e61a-d558-408f272a3bce", "synopsis": "TBig homes. Big hair. Big money. Big drama. The first ladies of New Jersey like to do things in style. But there's always trouble right around the corner.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted", "Documentary"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6c73271163cf4a2bcc0d44a2cff5fa6f19fdc158e208773d491bd16ef80958bc.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6ef3f35646e4f476ae634d124a1dc97f546e9ce1d8ef7c2e1e1f791a9214f3c4._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1247122800000, "runtimeSeconds": null, "runtime": null, "overallRating": 4.5, "totalReviewCount": 14, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 14, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.00ba07c0-2e24-e61a-d558-408f272a3bce", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_2"}, "refMarker": "dp_amz_c_TS176743_1_2", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.00ba07c0-2e24-e61a-d558-408f272a3bce", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_2"}, "refMarker": "dp_amz_c_TS176743_1_2", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "The Real Housewives of New Jersey", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Watch What Happens Live Season 18", "gti": "amzn1.dv.gti.6cbb682f-c162-2c71-c041-9a78d7412dd0", "transformItemId": "amzn1.dv.gti.6cbb682f-c162-2c71-c041-9a78d7412dd0", "synopsis": "<PERSON> chats to guests from the world of entertainment and pop culture.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Talk Show and Variety"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e9420cc36beeaa429214c6e1cea691461f0b5afd75137e76e51ff5ef32708a31.png", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c1206ad5998ac5ac267f2ace46de15b694d7066f6360c19cbd9699985dc728d4._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1641340800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 18, "watchProgress": null, "numberOfSeasons": 6, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.6cbb682f-c162-2c71-c041-9a78d7412dd0", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_3"}, "refMarker": "dp_amz_c_TS176743_1_3", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® nominee", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.6cbb682f-c162-2c71-c041-9a78d7412dd0", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_3"}, "refMarker": "dp_amz_c_TS176743_1_3", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "Watch What Happens Live", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of Beverly Hills Season 1", "gti": "amzn1.dv.gti.34ba07d7-e6f0-bb22-f0e9-9c45659530bb", "transformItemId": "amzn1.dv.gti.34ba07d7-e6f0-bb22-f0e9-9c45659530bb", "synopsis": "Luxury, wealth and pampered privilege - The Real Housewives of Beverly Hills have it all. Literally. But with a lavish lifestyle comes A LOT of drama...", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted", "Documentary"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/18250926e332c9c2bb7f1fbb6ae6c72fdc074f9e23cf91eea586e30935a929fe.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/1d228151df434b488a1e3e2b919058b8686a31f95e61d02176427c1a4e9b8853._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1297756800000, "runtimeSeconds": null, "runtime": null, "overallRating": 4.3, "totalReviewCount": 82, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 13, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.34ba07d7-e6f0-bb22-f0e9-9c45659530bb", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_4"}, "refMarker": "dp_amz_c_TS176743_1_4", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.34ba07d7-e6f0-bb22-f0e9-9c45659530bb", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_4"}, "refMarker": "dp_amz_c_TS176743_1_4", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "The Real Housewives of Beverly Hills", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of New York City Season 1", "gti": "amzn1.dv.gti.54a9f675-467d-9080-b704-a90fa787cc57", "transformItemId": "amzn1.dv.gti.54a9f675-467d-9080-b704-a90fa787cc57", "synopsis": "For a certain group of people in New York, status is everything... Get a glimpse at the glamorous lives of New York's rich and powerful elite.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Unscripted"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/da9fb643d17fe14f482a4d4dde4fbe3fff93847debee700a555497aa076d6250.png", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/61bb57b4fc6d34bb09c203a582533e479ab831d21add14540d12824b798c9c48._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1211871600000, "runtimeSeconds": null, "runtime": null, "overallRating": 4, "totalReviewCount": 15, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 14, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.54a9f675-467d-9080-b704-a90fa787cc57", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_5"}, "refMarker": "dp_amz_c_TS176743_1_5", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.54a9f675-467d-9080-b704-a90fa787cc57", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_5"}, "refMarker": "dp_amz_c_TS176743_1_5", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "The Real Housewives of New York City", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Summer House Season 1", "gti": "amzn1.dv.gti.94ba029c-56f1-f1b8-02c1-0d038f588612", "transformItemId": "amzn1.dv.gti.94ba029c-56f1-f1b8-02c1-0d038f588612", "synopsis": "Montauk, N.Y., a beach town on the easternmost point of Long Island, is where affluent New Yorkers head during the summer to have a good time and get away from the stresses of city life. \"Summer House\" follows a group of nine friends who, after toiling away at their jobs during the week, share a house together on weekends between Memorial Day and Labor Day -- the unofficial summer season.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary", "Unscripted"], "maturityRatingString": "18+", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e654663ba7f95e03ca1ea97db0343d4b586e6b8adca987cf7230d30948cc950f.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/aeb3f249eba9f6da9a74acfc238181c38a6d51d14dc3d97bcc2dcdba8c24d500._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1483920000000, "runtimeSeconds": null, "runtime": null, "overallRating": 3.5, "totalReviewCount": 2, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 8, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.94ba029c-56f1-f1b8-02c1-0d038f588612", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_6"}, "refMarker": "dp_amz_c_TS176743_1_6", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.94ba029c-56f1-f1b8-02c1-0d038f588612", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_6"}, "refMarker": "dp_amz_c_TS176743_1_6", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "Summer House", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of Cheshire Season 1", "gti": "amzn1.dv.gti.bcacbb8f-1765-7aa1-0c08-7789637d90cf", "transformItemId": "amzn1.dv.gti.bcacbb8f-1765-7aa1-0c08-7789637d90cf", "synopsis": "Reality series following a group of women residing in one of the UK's most affluent areas, the glamorous Golden Triangle of Cheshire.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary", "Unscripted"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/466b7e3018da053cab7d825495988c642b8b87680426d3f18dff0169df2e516c.png", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/ef4209618ef0113df91154f9fb85cab0687b30f3198618cbb022a381eb5e6a18._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1421020800000, "runtimeSeconds": null, "runtime": null, "overallRating": 5, "totalReviewCount": 2, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 21, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.bcacbb8f-1765-7aa1-0c08-7789637d90cf", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_7"}, "refMarker": "dp_amz_c_TS176743_1_7", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.bcacbb8f-1765-7aa1-0c08-7789637d90cf", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_7"}, "refMarker": "dp_amz_c_TS176743_1_7", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "The Real Housewives of Cheshire", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of Atlanta Season 2", "gti": "amzn1.dv.gti.02a9f67a-76c5-05a2-6880-7ef823ec0315", "transformItemId": "amzn1.dv.gti.02a9f67a-76c5-05a2-6880-7ef823ec0315", "synopsis": "Hold on to your weaves, ladies! Get an up-close look at the sassy Southern Belles as they navigate the trials and tribulations of high society in Hotlanta.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/09a14b0964f4c5f738fd7584f5a72cf3288a528be292425f4d982a0495fbbb71.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/b030f4c29968007ace82d862f9bdee8d7fd62286de027ff80d7dd0f8eab8f648._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1260432000000, "runtimeSeconds": null, "runtime": null, "overallRating": 4.3, "totalReviewCount": 16, "seasonNumber": 2, "watchProgress": null, "numberOfSeasons": 15, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.02a9f67a-76c5-05a2-6880-7ef823ec0315", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_8"}, "refMarker": "dp_amz_c_TS176743_1_8", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.02a9f67a-76c5-05a2-6880-7ef823ec0315", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_8"}, "refMarker": "dp_amz_c_TS176743_1_8", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "The Real Housewives of Atlanta", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of Potomac Season 1", "gti": "amzn1.dv.gti.5cb9ccfe-d79b-cc14-249d-9d5b7181bef8", "transformItemId": "amzn1.dv.gti.5cb9ccfe-d79b-cc14-249d-9d5b7181bef8", "synopsis": "Peel back the curtains of the exclusive suburb of Potomac, Maryland and follow the upscale lives of six affluent African-American women, who call Potomac home.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f189d9b8c36d9396d78e582d58462c97da259b811658340b27461298bcbd9ef3.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/11a167ac43fea9cf9d99e174b1b5a49c570511c1f3c6fa5b88092f23aa31312a._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1460856600000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 9, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5cb9ccfe-d79b-cc14-249d-9d5b7181bef8", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_9"}, "refMarker": "dp_amz_c_TS176743_1_9", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5cb9ccfe-d79b-cc14-249d-9d5b7181bef8", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_9"}, "refMarker": "dp_amz_c_TS176743_1_9", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "The Real Housewives of Potomac", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of Miami Season 1", "gti": "amzn1.dv.gti.9eba00c1-897d-c8f3-0bb9-94ba539163d5", "transformItemId": "amzn1.dv.gti.9eba00c1-897d-c8f3-0bb9-94ba539163d5", "synopsis": "Bienvenido a Miami! Real Housewives heads to the Sunshine State to meet <PERSON><PERSON>'s most influential women balancing careers, marriage, motherhood.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/910dfb0abfd759c6e4bdc1a5ce79f8539200c07755c19bce757b2eb1a38ff379.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/7cf11da5598acc9de1f8a8bed832fd3747a9273ca013e08ff3a815e194283845._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1302850800000, "runtimeSeconds": null, "runtime": null, "overallRating": 5, "totalReviewCount": 1, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 6, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.9eba00c1-897d-c8f3-0bb9-94ba539163d5", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_10"}, "refMarker": "dp_amz_c_TS176743_1_10", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "hayu", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.9eba00c1-897d-c8f3-0bb9-94ba539163d5", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS176743_1_10"}, "refMarker": "dp_amz_c_TS176743_1_10", "text": null, "journeyIngressContext": "16|CgRoYXl1EgNhbGw="}], "playableGti": null, "showName": "The Real Housewives of Miami", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "dp_amz_c_TS176743_1", "ClientSideMetrics": "460|CkMKI0NBV1JlbW90ZVN0cmF0ZWd5TGl2ZURlZmF1bHREZWZhdWx0EhAyOlRTRjM4OUVBMTc2NzQzGgAiCFRTMTc2NzQzEmsKBmRldGFpbBIxYW16bjEuZHYuZ3RpLmVkMzUxMDYxLTU5OWEtNDU0NC1iYWI5LTk0M2Q4NjIzNTA0ZCIGY2VudGVyKgAyJDI5NDM5OTQzLWJlODgtNDc5My1iNTcxLWNmMTZlOTQ4NGYyYhoDYWxsIgNhbGwqBGhheXUyCGNhcm91c2VsOh5BVFZDdXN0b21lcnNBbHNvV2F0Y2hlZFNlcnZpY2VCDGNoYW5uZWxzLWNhd0oKVk9EQ29udGVudFILbm90RW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgBcgB6IGVjYzkwOGFmM2I4ZThjMDA1YTQxMmJlMWRkZDdhOTRlggEDYWxsigEAkgEA"}, "tags": [], "journeyIngressContext": "16|CgRoYXl1EgNhbGw=", "seeMore": null, "type": "STANDARD_CAROUSEL"}, {"facet": {"text": null}, "title": "Customers also watched", "titleImageUrl": null, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiMjk0Mzk5NDMtYmU4OC00NzkzLWI1NzEtY2YxNmU5NDg0ZjJiIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6ImVjYzkwOGFmM2I4ZThjMDA1YTQxMmJlMWRkZDdhOTRlOjE3MTczNTQ3ODcwMDAiLCJhcE1heCI6MTcsInN0cmlkIjoiMjpUUzNGMkQ4RTgwRTFGMyMjTU5RWEUzM1ZPTlNXWSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUDQ3YTI3MDY1ZTg1MzJjZWQ2N2U3NGY5NzJjZDRkMjM3OGNhZGY4MDVkMDVkNjY4N2FkNTkxM2QwYjU2NjczNzVcIn0iLCJvcmVxayI6IkUzRk5RVU9OVXZWVUZ3Rzg1UlFwemUvd3RjaWx3MWdYSDFQZDd0SC9VMFk9Iiwib3JlcWt2IjoxfQ==", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmVkMzUxMDYxLTU5OWEtNDU0NC1iYWI5LTk0M2Q4NjIzNTA0ZIuGZGV0YWlsjI6fMjpUUzNGMkQ4RTgwRTFGMyMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "pageId": "amzn1.dv.gti.ed351061-599a-4544-bab9-943d8623504d", "pageType": "detail", "pageContext": {"pageType": "detail", "pageId": "amzn1.dv.gti.ed351061-599a-4544-bab9-943d8623504d"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmVkMzUxMDYxLTU5OWEtNDU0NC1iYWI5LTk0M2Q4NjIzNTA0ZIuGZGV0YWlsjI6fMjpUUzNGMkQ4RTgwRTFGMyMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "Quiet on Set: The Dark Side of Kids TV - Season 1", "gti": "amzn1.dv.gti.04a2cd4c-88d2-42e8-9d96-580115527b5f", "transformItemId": "amzn1.dv.gti.04a2cd4c-88d2-42e8-9d96-580115527b5f", "synopsis": "Discover the untold story of the toxic and abusive environment inside '90s kids' TV. Hear harrowing accounts from former child stars and crew who probe the balance of power in the industry and reveal an era that inflicted lasting wounds still felt today.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6a4c36836ebecdac7b5fcf10b9125c9284220925fc7c7fb50c39ce0837e00839.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/a453941cb57acdb9f8ab9466d916dd740eebff3fdf05c36d29912f6436033dbb._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1712534400000, "runtimeSeconds": null, "runtime": null, "overallRating": 5, "totalReviewCount": 2, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.04a2cd4c-88d2-42e8-9d96-580115527b5f", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_1"}, "refMarker": "dp_amz_c_TS80e1f3_2_1", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to discovery+", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.04a2cd4c-88d2-42e8-9d96-580115527b5f", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_1"}, "refMarker": "dp_amz_c_TS80e1f3_2_1", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "Quiet on Set: The Dark Side of Kids TV", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Christina On The Coast - Season 1", "gti": "amzn1.dv.gti.06bc6089-90f5-a9d5-5ec7-5baebcc3126e", "transformItemId": "amzn1.dv.gti.06bc6089-90f5-a9d5-5ec7-5baebcc3126e", "synopsis": "<PERSON> transforms properties into high-value homes", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Special Interest"], "maturityRatingString": "All", "maturityRatingImage": null, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6a126a44f2eccadc2590f3507de09a01b2a7f57b465288e29bbb22f866467e3a.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/77777be5cac5c96a21cf4a5407eb443368df1e7da2fc8b6d5689a1f9948c1ef7._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1614556800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 4, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.06bc6089-90f5-a9d5-5ec7-5baebcc3126e", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_2"}, "refMarker": "dp_amz_c_TS80e1f3_2_2", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to discovery+", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.06bc6089-90f5-a9d5-5ec7-5baebcc3126e", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_2"}, "refMarker": "dp_amz_c_TS80e1f3_2_2", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "Christina On The Coast", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "90 Day Fiance Pillow Talk: The Other Way - Season 2", "gti": "amzn1.dv.gti.babc635a-df9c-1d60-d820-78d0d0ccfc78", "transformItemId": "amzn1.dv.gti.babc635a-df9c-1d60-d820-78d0d0ccfc78", "synopsis": "Show favourites give their own juicy opinions on the latest episodes of 90 Day Fiancé: The Other Way. This outspoken cast don’t hold back as they watch from the comfort of their own homes.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "13+", "maturityRatingImage": null, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/9930d0f536c0cfd9416b45b82cbf4c439527ac5b0e8eed07f3b9f44f228b9b4b.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c5dba0fcc44465ed3372384821053b215e68f476a021cc1a020fce353d60b344._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1606867200000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 2, "watchProgress": null, "numberOfSeasons": 4, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.babc635a-df9c-1d60-d820-78d0d0ccfc78", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_3"}, "refMarker": "dp_amz_c_TS80e1f3_2_3", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to discovery+", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.babc635a-df9c-1d60-d820-78d0d0ccfc78", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_3"}, "refMarker": "dp_amz_c_TS80e1f3_2_3", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "90 Day Fiancé <PERSON> Talk: The Other Way", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Death By Fame - Season 1", "gti": "amzn1.dv.gti.d243a4a3-2075-4612-8541-393337eaf334", "transformItemId": "amzn1.dv.gti.d243a4a3-2075-4612-8541-393337eaf334", "synopsis": "A behind-the-scenes look at what happens when Hollywood dreams turn deadly.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/10bc3d9a5ac133572ced718fe42e8855ce07e1933d9a41c46a98758b89f4a163.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/484a2a8cb2941658cbe31cf42093843bc125c5ec4ef6079fe441afdfa67e0cfc._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1685750400000, "runtimeSeconds": null, "runtime": null, "overallRating": 3, "totalReviewCount": 1, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 4, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d243a4a3-2075-4612-8541-393337eaf334", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_4"}, "refMarker": "dp_amz_c_TS80e1f3_2_4", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to discovery+", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d243a4a3-2075-4612-8541-393337eaf334", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_4"}, "refMarker": "dp_amz_c_TS80e1f3_2_4", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "Death By Fame", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Real Housewives of Miami ('21), Season 2", "gti": "amzn1.dv.gti.e47afbe5-1445-4689-a5ca-8840d3aa9d6d", "transformItemId": "amzn1.dv.gti.e47afbe5-1445-4689-a5ca-8840d3aa9d6d", "synopsis": "\"The Real Housewives of Miami\" heats up for a wild new season of fun, friendship and sizzling drama in the \"Sunshine State.\"", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "12", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f36e9a7ec0e1b3d80cd7fb146b04fa4ce722952c782937a2cffe1b6ae568d6b3.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/203a531f653d0de462153d3078c35eb8d69d8271c3f6d37aa8b95cdab622bfbc._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1679529600000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 2, "watchProgress": null, "numberOfSeasons": 2, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e47afbe5-1445-4689-a5ca-8840d3aa9d6d", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_5"}, "refMarker": "dp_amz_c_TS80e1f3_2_5", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e47afbe5-1445-4689-a5ca-8840d3aa9d6d", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_5"}, "refMarker": "dp_amz_c_TS80e1f3_2_5", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "The Real Housewives of Miami ('21)", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Murder Under The Friday Night Lights - Season 1", "gti": "amzn1.dv.gti.fe85b16f-8389-4f70-89fa-e78dae55eaf6", "transformItemId": "amzn1.dv.gti.fe85b16f-8389-4f70-89fa-e78dae55eaf6", "synopsis": "Across the country, high school football unites small-town communities. But when a heinous murder shatters that Friday night dream world, the crime ripples beyond those immediately impacted, and the community will never be the same again.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/70bd79984b239e898e557d79f2266d04b08208641d01bc00fd1c46b6d0ca0b61.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/2d5d54bf27cf59a06d475561a973b9144c367fe3b4a268b12ff64d730b361d8f._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1645228800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 3, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fe85b16f-8389-4f70-89fa-e78dae55eaf6", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_6"}, "refMarker": "dp_amz_c_TS80e1f3_2_6", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to discovery+", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.fe85b16f-8389-4f70-89fa-e78dae55eaf6", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_6"}, "refMarker": "dp_amz_c_TS80e1f3_2_6", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "Murder Under The Friday Night Lights", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Big Tips Texas Season 1", "gti": "amzn1.dv.gti.86a9f676-2068-27ff-04d8-5949e8a09591", "transformItemId": "amzn1.dv.gti.86a9f676-2068-27ff-04d8-5949e8a09591", "synopsis": "Big Tips Texas documents the lives of a group of fun-loving girlfriends serving up southern hospitality at a popular Texas watering hole while hoping to make their dreams a reality.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/bbadd995c9fca0f960df952f81343651814a55792bb9047d08f24523264ffcd6._UR1920,1080_BL99_CLs%7C1920,1080%7Cbbadd995c9fca0f960df952f81343651814a55792bb9047d08f24523264ffcd6.jpg%7C0,0,1920,1080+0,0,1920,1080_UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1387324800000, "runtimeSeconds": null, "runtime": null, "overallRating": 5, "totalReviewCount": 1, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.86a9f676-2068-27ff-04d8-5949e8a09591", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_7"}, "refMarker": "dp_amz_c_TS80e1f3_2_7", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.86a9f676-2068-27ff-04d8-5949e8a09591", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_7"}, "refMarker": "dp_amz_c_TS80e1f3_2_7", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "Big Tips Texas", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "People Magazine Investigates - Season 1", "gti": "amzn1.dv.gti.76bc608c-0409-405a-67b3-335ec2c777e2", "transformItemId": "amzn1.dv.gti.76bc608c-0409-405a-67b3-335ec2c777e2", "synopsis": "People Magazine uncovers the heart-wrenching true stories behind crimes that transcended headlines and became part of popular culture. Exclusive firsthand interviews reveal shocking twists, new evidence, and unexpected resolutions.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "18+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c2c6970a67d216d7fea34afe511c374255bbdc5eae629ef7125783e159a50d85._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1510358400000, "runtimeSeconds": null, "runtime": null, "overallRating": 4, "totalReviewCount": 2, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 7, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.76bc608c-0409-405a-67b3-335ec2c777e2", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_8"}, "refMarker": "dp_amz_c_TS80e1f3_2_8", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to discovery+", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.76bc608c-0409-405a-67b3-335ec2c777e2", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_8"}, "refMarker": "dp_amz_c_TS80e1f3_2_8", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "People Magazine Investigates", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Bingo & Julia", "gti": "amzn1.dv.gti.479c5978-4fa1-4380-a116-61c025ad13b6", "transformItemId": "amzn1.dv.gti.479c5978-4fa1-4380-a116-61c025ad13b6", "synopsis": "A reality series that follows influencer couple <PERSON><PERSON> and <PERSON>’s action-packed world. A life of extravagance, but above all, we get a glimpse into their warm and, to say the least, special family life. The couple long to have children and want to consult none other than <PERSON>’s doctor to make sure they have twins. <PERSON><PERSON> also has an elaborate plan for a spectacular proposal.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted", "International"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/32a4e3acbfe8fcb424aaf4d1f3c543c0f5042f619f3e85d81898c4470b50512c.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/5ca903cbae0f459b78ebabd1d174495abb2b908228c8fc8f42908f4502d414f6._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1692921600000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 2, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.479c5978-4fa1-4380-a116-61c025ad13b6", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_9"}, "refMarker": "dp_amz_c_TS80e1f3_2_9", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.479c5978-4fa1-4380-a116-61c025ad13b6", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_9"}, "refMarker": "dp_amz_c_TS80e1f3_2_9", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "Bingo & Julia", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "My Big Fat Fabulous Life - Season 1", "gti": "amzn1.dv.gti.02bc3cc4-7568-e0cc-55fa-5a5ec594720a", "transformItemId": "amzn1.dv.gti.02bc3cc4-7568-e0cc-55fa-5a5ec594720a", "synopsis": "<PERSON> weighs an incredible 380 pounds and has polycystic ovary syndrome, but when her self-choreographed A Fat Girl Dancing becomes an overnight sensation, she's plucked from obscurity and her life is turned upside down.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "maturityRatingString": "13+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/1c429e57dba3c9f2f19a184b803a8f45da40110d8df6dcac2be51e152a782978._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1618272000000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 10, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.02bc3cc4-7568-e0cc-55fa-5a5ec594720a", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_10"}, "refMarker": "dp_amz_c_TS80e1f3_2_10", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to discovery+", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.02bc3cc4-7568-e0cc-55fa-5a5ec594720a", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2_10"}, "refMarker": "dp_amz_c_TS80e1f3_2_10", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "My Big Fat Fabulous Life", "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "dp_amz_c_TS80e1f3_2", "ClientSideMetrics": "472|CkMKI0NBV1JlbW90ZVN0cmF0ZWd5TGl2ZURlZmF1bHREZWZhdWx0EhAyOlRTM0YyRDhFODBFMUYzGgAiCFRTODBlMWYzEmsKBmRldGFpbBIxYW16bjEuZHYuZ3RpLmVkMzUxMDYxLTU5OWEtNDU0NC1iYWI5LTk0M2Q4NjIzNTA0ZCIGY2VudGVyKgAyJDI5NDM5OTQzLWJlODgtNDc5My1iNTcxLWNmMTZlOTQ4NGYyYhoDYWxsIgNhbGwqA2FsbDIIY2Fyb3VzZWw6HkFUVkN1c3RvbWVyc0Fsc29XYXRjaGVkU2VydmljZUIUY2hhbm5lbHMtZGVmYXVsdC1jYXdKClZPRENvbnRlbnRSC25vdEVudGl0bGVkWgBiEFN0YW5kYXJkQ2Fyb3VzZWxoAnIAeiBlY2M5MDhhZjNiOGU4YzAwNWE0MTJiZTFkZGQ3YTk0ZYIBA2FsbIoBAJIBAA=="}, "tags": [], "journeyIngressContext": "16|CgNhbGwSA2FsbA==", "seeMore": null, "type": "STANDARD_CAROUSEL"}]}, "metadata": {"requestId": "ecc908af3b8e8c005a412be1ddd7a94e", "requestedTransformId": "lr/detailsPage/detailsPageBTF", "domain": "prod", "realm": "eu-west-1", "timestamp": "2024-06-02T18:59:47.921868Z"}}