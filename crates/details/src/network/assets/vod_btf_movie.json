{"resource": {"castAndCrew": [{"id": "/name/nm0386472/", "name": "<PERSON><PERSON>", "image": {"id": "/name/nm0386472/images/rm1987676673", "url": "http://ecx.images-amazon.com/images/M/MV5BZjM4OTg0NTMtN2NmNS00ZGVjLWFmNWEtZDZiY2Y1ZDFjYWYzXkEyXkFqcGdeQXVyNjk1MjYyNTA@._V1_.jpg", "width": 1041, "height": 1344}, "miniBio": "<PERSON><PERSON> was born in Palms, California, to <PERSON> (Davenport), a teacher and visual artist, and <PERSON>, an entrepreneur and producer. He grew up in Los Angeles and Santa Fe, New Mexico, and having been introduced to acting while still at school, he got his first acting job on TV at the age of 11 in an episode of Kindred: The Embraced (1996).\n\nMore TV work followed until he made his Hollywood debut in The Dangerous Lives of Altar Boys (2002) and doesn't seem to have been out of work since."}, {"id": "/name/nm0922342/", "name": "<PERSON>", "image": {"id": "/name/nm0922342/images/rm464410113", "url": "http://ecx.images-amazon.com/images/M/MV5BNjZjYmYwYzUtM2JiZS00YWI2LTgxYTgtMDM3MDkyZTBkOGUzXkEyXkFqcGdeQXVyMjQwMDg0Ng@@._V1_CR154,140,527,790_.jpg", "width": 527, "height": 790}, "miniBio": "<PERSON> was born <PERSON> in 1978 in Baton Rouge, Louisiana, the son of <PERSON> (<PERSON><PERSON>), a lawyer, and <PERSON>, a drugstore owner. <PERSON> began to pursue an acting career at the age of fifteen, gaining bit parts on various television shows, but it was not until 1999 when <PERSON> appeared in ABC's Once and Again (1999) that he got his major breakthrough. Aside from catapulting to teen heart-throb status with the success of the show, <PERSON> has made his mark on the screen within such films as Liberty Heights (1999) and Whatever It Takes (2000). He starred opposite <PERSON> in the hit teen romance A Walk to Remember (2002), played <PERSON> in the action film The League of Extraordinary Gentlemen (2003), and then depicted the short life of musician <PERSON><PERSON> in the biopic What We Do Is Secret (2007). From 2010 to 2013, he starred as <PERSON> on the show \"<PERSON><PERSON>\" (2010)_, and since 2014, has played <PERSON> on the show \"<PERSON>\" (2014)_.\n\nAside from acting, <PERSON> continues the musical traditions of his family (his parents were both were musicians when he was young) with his band, Average Jo, for which he writes and plays guitar.\n\n<PERSON>'s father was born in Jamaica, of mostly English and Sephardi Jewish descent. <PERSON>'s mother is from a Cajun (French) family from Louisiana."}, {"id": "/name/nm0307726/", "name": "<PERSON><PERSON>", "image": {"id": "/name/nm0307726/images/rm916593153", "url": "http://ecx.images-amazon.com/images/M/MV5BMGNmYTkzYTMtM2Q2My00ZTQxLThlYzEtNDU2OTRlNjBlZjhiXkEyXkFqcGdeQXVyODY0MDIxOTE@._V1_.jpg", "width": 596, "height": 1054}, "miniBio": "A California native, <PERSON><PERSON> made her film debut at age sixteen in director <PERSON>' short film Architecture of Reassurance. Her performance captured the attention of director <PERSON> who cast her in her first feature film, as the drug-addicted teen, <PERSON>, in the controversial docudrama Bully, establishing <PERSON><PERSON> as an edgy young talent. After honing in on her unique chameleon like skills in more independent film, the fiery young actor earned a part as the 1940's Hollywood ingénue, <PERSON>, in <PERSON>'s The Aviator, opposite <PERSON>. Soon after, she reunited with <PERSON> for his 2005 Sundance hit Thumbsucker, and further proved her ability to shine in comedy, with her first leading roles, starring opposite <PERSON> in Man of the House as well as the quirky and off beat comedy <PERSON> and the Real Girl, opposite <PERSON>.\n\nOn the television side, <PERSON> most recently starred as <PERSON> on NBC's The Enemy Within opposite <PERSON> and <PERSON> and gave a knockout performance per Variety as <PERSON> /<PERSON> in LIfetime's event drama, The Secret Life of <PERSON>, also starring <PERSON>. <PERSON> co-starred in ABC's 1960's airline drama, Pan Am, opposite <PERSON> , cementing her throwback, vintage appeal, along the way.\n\nIn both film and television <PERSON><PERSON> has continued to disappear into role after role, leaving us with raw, honest and heartbreaking performances.\n\nSome other notable roles include, <PERSON>'s <PERSON> and <PERSON>'s <PERSON>'s with <PERSON> Theatre credits include:\n\n<PERSON> Seagull by <PERSON>, opposite <PERSON> Stage Company\n\n1+1 by <PERSON>ian, <PERSON> Stage and <PERSON>\n\nDog <PERSON>'s <PERSON>: Confessions of a Teenage Blockhead by Bert <PERSON> Century Center for Performing Arts"}, {"id": "/name/nm0713389/", "name": "<PERSON><PERSON>", "image": {"id": "/name/nm0713389/images/rm1607942657", "url": "http://ecx.images-amazon.com/images/M/MV5BY2Q0ODRmZTYtZDBmZi00YWQxLTlkMjctMGFlYWI5Yjk3NTE3XkEyXkFqcGdeQXVyNjI1MTE1NA@@._V1_.jpg", "width": 1080, "height": 1440}, "miniBio": "Born and raised in Los Angeles, <PERSON><PERSON> began her career on <PERSON> the Middle (2000), and is known for her portrayal of <PERSON> in the J.J<PERSON> Abrams series Lost. She played the title role of notorious murderer <PERSON><PERSON> in the Lifetime original feature Jo<PERSON>s: Dirty Little Secret (2013), and starred alongside <PERSON> and <PERSON> in <PERSON>'s legal drama Goliath for Amazon. Other TV credits include recurring roles on The Last Ship, The Big Bang Theory (2007), and <PERSON> (2008), opposite <PERSON>."}, {"id": "/name/nm0001418/", "name": "<PERSON>", "image": {"id": "/name/nm0001418/images/rm3576076032", "url": "http://ecx.images-amazon.com/images/M/MV5BMTQ3ODAyNjQyN15BMl5BanBnXkFtZTcwNTM5NTk3MQ@@._V1_.jpg", "width": 319, "height": 400}, "miniBio": "<PERSON> was born on May 8, 1954 in Knoxville, Tennessee, the son of <PERSON><PERSON><PERSON> and <PERSON>. He graduated from the University of Tennessee with a Bachelor of Arts in Speech and Theater. <PERSON> had a supporting role in <PERSON> Rose (1979) starring <PERSON><PERSON>, had a supporting role in <PERSON><PERSON><PERSON> (1980), and co-starred with <PERSON> in An Officer and a Gentleman (1982). He played a local thug in The Great Santini (1979), starred in The Lords of Discipline (1983) and White of the Eye (1987), and held a prominent supporting role opposite <PERSON> in U-571 (2000). He played opposite <PERSON> in the science fiction horror film Firestarter (1984), and opposite <PERSON> and <PERSON> in Running Wild (1995).\n\n<PERSON> played <PERSON> in <PERSON>' Heartbreak Hotel (1988), the cowboy \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" <PERSON> in <PERSON>'s The Indian in the Cupboard (1995), and the leading role of <PERSON> in the horror film Hangman's Curse (2003). He also co-starred in the sitcom The Class (2006) as <PERSON><PERSON>, a retired professional football player. He has appeared in the horror remake <PERSON> (2002), Dare<PERSON><PERSON> (2003), Rai<PERSON> (2004) starring <PERSON>, and <PERSON><PERSON> (2006). He has also appeared on the television series Law & Order: Special Victims Unit (1999), Law & Order: Criminal Intent (2001), CSI: Miami (2002), NCIS (2003), and Hawaii Five-0 (2010). He also co-starred as <PERSON>'s father <PERSON> on the short-lived Fox drama series <PERSON> Star (2010).\n\n<PERSON> married realtor <PERSON> <PERSON> in 2000 and the couple reside in Knoxville, Tennessee."}, {"id": "/name/nm0183548/", "name": "<PERSON>", "image": {"id": "/name/nm0183548/images/rm119604224", "url": "http://ecx.images-amazon.com/images/M/MV5BMmNhMTg0YWYtNjJjYi00ZGVhLWExNjQtZmUxYWQzMWVkMTc3XkEyXkFqcGc@._V1_.jpg", "width": 2709, "height": 3075}, "miniBio": "Born in Canada, <PERSON> grew up in South America and Cleveland Heights, Ohio. An actor and writer, his film & television appearances include recurring roles on She-Hulk Attorney at Law, Yellowstone, P-Valley, The Walking Dead, Brockmire, and House of Cards. He also appeared in two HBO films directed by <PERSON>: Wizard of Lies with <PERSON> and <PERSON><PERSON><PERSON> with <PERSON>. Film appearances include <PERSON>'s First Man, The Conjuring 1-3, The Hunger Games, Birth of a Nation, Insidious 2 & 3, The Front Runner, and Charlie Day's El Tonto. He was the head writer for both of <PERSON>'s television series, House of Payne and Meet the Browns, where he supervised over 100 episodes and won two consecutive NAACP IMAGE Awards for Best Comedy Series. He is a graduate of the North Carolina School of the Arts."}, {"id": "/name/nm0584492/", "name": "<PERSON>", "image": {"id": "/name/nm0584492/images/rm2600590337", "url": "http://ecx.images-amazon.com/images/M/MV5BZTg0MjlmNTAtZjJjZC00M2JiLWI1NDMtZjE5YmQzMjE1YTY3XkEyXkFqcGdeQXVyMzkxMzcxNQ@@._V1_.jpg", "width": 1284, "height": 928}, "miniBio": "After a busy 2021, 2022 has a lot more in store for <PERSON> and his production company Benacus Entertainment. As expected, Red Notice was a big hit, becoming the most watched film in the history of Netflix. The first project of 2022 is \"Southern Gothic\" a film written and directed by Academy Award winner <PERSON> (Dead Poets Society) His second feature was \"The Stenographer\", starring <PERSON><PERSON>. With a slate of 7 projects from 4 features, 2 doc series and a TV Pilot, nothing seems to be slowing down anytime soon. Setting up a mini-studio out of Atlanta with original content has always been the dream, that dream has now become a reality.\n\n<PERSON> has studied with the best, from World Renown acting teacher and Author <PERSON> to <PERSON>. He has also Studied with Character Actor <PERSON> and top Casting Director <PERSON><PERSON>. Also <PERSON>, daughter of Legendary <PERSON> Teachers <PERSON> and <PERSON>.\n\nHis Television Credits include Doom Patrol, American Soul, a recurring role on <PERSON> and co-starring on Nip/Tuck. <PERSON> does Shakespeare's Romeo and Juliet at Casa di Giulietta every summer in Verona, Italy. He recently won Best Actor the California Women's Film Festival for his starring role as <PERSON> in the film \"Free Dead or Alive\" and he was also nominated as Best Actor for \"The Martyr\" at the Method Film Festival that focuses only on acting, The Martyr went on to win the CINE Award of Excellence that year. He only spoke Hebrew and Arabic in the film. He also played a Mexican Federale on AFI's top film shot on 35mm called \"Lucha\" all in Spanish, leaving audiences at festivals completely silent for the heavy content and realism of the missing Women of Juarez. He has played everything from Israeli to Mexican, Brazilian, American, Argentinean, Italian, Middle Eastern, French, Egyptian due to his Ethnically Ambiguous appearance. <PERSON> has over 100 Commercials under his belt as well, he has shot in different markets, Spanish, English, European, Asian and South American for Top Brand names like Nike, Adidas, AT&T, Verizon, Honda, Chevrolet, Budweiser, Modelo, IH<PERSON>, <PERSON>'s, Etc...\n\n<PERSON> was born in Orange County, California. He excelled at soccer, he went onto College on full scholarships, first to San Francisco State University and then San Diego State University. After his sophomore year he left college to play Professional soccer for Pumas UNAM in Mexico City. One of Seth's passions is traveling and meeting new people, he has traveled to 37 countries and counting. He is fluent in 4 languages, his native tongue English but also Italian, Spanish and Portuguese. He has been a Vegan for 20 years which leads him to donate part of his earnings to Charities like Hope For Paws and the ASPCA. He also donates to Harvest Home in Venice Beach, a place for single mothers who have nowhere to go, Seth Believes that a strong foundation to a child's development is a strong bond with their mothers, teaching children Kindness and Compassion, Harvest Homes provides shelter and food to mothers that have nowhere else to go. He has also volunteered for P.A.L. (Police Athletic League) in Hollywood, CA helping youth at risk.\n\nI am looking forward to keep making a difference through my work not only as a Filmmaker but as a human being, to give and help those less fortunate and to teach, invigorate, uplift, inspire the next generation of film makers. To collaborate with pioneers like Christopher Nolan, Alejandro Iñarritu and Steven Spielberg and also actors like Leonardo DiCaprio, Tom Hardy, Daniel Day Lewis and many more."}, {"id": "/name/nm1007554/", "name": "<PERSON>", "image": {"id": "/name/nm1007554/images/rm681054209", "url": "http://ecx.images-amazon.com/images/M/MV5BNTZjZWZkYzctYTQ0OS00MjY1LTg1ZmEtYzlkNzdkOWE3ZWJkXkEyXkFqcGdeQXVyNTczMTQxMg@@._V1_.jpg", "width": 973, "height": 1352}, "miniBio": "<PERSON> is an actress, writer, and comedian who hails from Rainbow City, Alabama. After traveling the world on the modeling circuit for a year following high school, <PERSON> went on to graduate with a B.A. in Theatre from University of Mobile, a small liberal arts school where she discovered her passion for acting and entertainment. She relocated to New York upon graduation. Soon after, a Sony film brought <PERSON> to the West Coast. Since then, she has gone on to achieve recurring roles on ABC's Once Upon a Time (2011), CBS's MacGyver (2021), Lifetime's The Client List (2011), AMC's Mad Men (2007), TNT's Memphis Beat (2010), and CMT's Still the King (2016). She has starred in many studio and independent films, playing a young <PERSON> in Hillbilly Elegy (2020), co-starring opposite <PERSON> in the feature film, xXx: State of the Union (2005) and again in the cult comedy-thriller, Snakes on a Plane (2006). Her independent film One Last Thing... (2005) premiered at the Toronto Film Festival. Taking the world of Vine, Instagram, Twitter, TikTok, and Facebook by storm with her quirky brand of comedy, she quickly amassed a following of over 2 million. By relating to her audience through many different mediums, <PERSON> uses these platforms to release her creativity, express her ideas, promote and share news on film and television projects, help charitable organizations, and display branded content. In addition to acting, <PERSON> writes, directs, and produces films and music videos. She also spends much of her time creating music and fronting a band she formed with her husband and fellow actor, <PERSON> and musician, <PERSON>del. <PERSON> splits her time between Los Angeles and Atlanta."}, {"id": "/name/nm13358879/", "name": "<PERSON>", "image": {"id": "/name/nm13358879/images/rm745147137", "url": "http://ecx.images-amazon.com/images/M/MV5BMGQwMDE5ZjgtYjczMC00ZjRhLTgxMmMtNGI5YTVmYzEzMmM3XkEyXkFqcGdeQXVyMTUzMjY4NTc4._V1_.jpg", "width": 585, "height": 576}, "miniBio": "<PERSON> is known for <PERSON><PERSON> (2022), <PERSON>'ll <PERSON> Watching (2023) and <PERSON> (2023)."}, {"id": "/name/nm2952021/", "name": "<PERSON>", "image": {"id": "/name/nm2952021/images/rm1734341633", "url": "http://ecx.images-amazon.com/images/M/MV5BOWNmZWJkOTItNzFmNi00YzgyLTgwODMtMzM2NTg4OTBmYjEwXkEyXkFqcGdeQXVyMTg5MDA3MTU@._V1_.jpg", "width": 2256, "height": 3554}, "miniBio": "As a child, Italy's largest lake provided scenic inspiration of grand antiquity that helped young <PERSON> act out characters from her favorite fairy tales and animated movies. In her youth, <PERSON> acted for fun, but her talents wouldn't lay latent. When she turned eighteen she took a chance and began taking classes to develop her acting talents. Under the tutelage of <PERSON> (mother of <PERSON><PERSON><PERSON>, Italy's great stage actress), <PERSON> not only learned how to fine-tune her acting talent, but also learned the not-so-glamorous realities of the industry. The rewards of being able to do stage productions such as Romeo and Juliet, Hamlet, <PERSON><PERSON><PERSON> were the product of arduous hours of classes and rehearsals, many of which stretched across 7 consecutive days. The hard work paid great dividends, though. <PERSON> was cast in more and more production, and very quickly it was clear that her career path had been born.\n\nWith the teachings of <PERSON><PERSON> as her foundation, <PERSON> decided to test her talents in Rome's acting circles; however, her ambition was far greater than the spoils that Rome could offer. After much consideration, her decision to move to Rome gave way to a decision to move to Hollywood, California instead. <PERSON> quickly acclimated herself to her new surroundings and has gained a foot-hold in the film industry. Buoyed by her passion for her art, the support and the love of her family, <PERSON> reflects on those youthful days when she would act just for fun and realizes just how far she's come."}], "containerList": [{"facet": {"text": null}, "title": "Customers also watched", "titleImageUrl": null, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiZmExZTU1YzUtM2YwYS00NjA2LWEyYmUtOTcyYmM1MTEzNjJlIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6IjBlNWJkMmY2NGY1Yjg3ZDY2ZDhiZGIxMWJiMGY4MzJjOjE3MTg3NjE3NjMwMDAiLCJhcE1heCI6NzQsInN0cmlkIjoiMjpUUzY3NkE3QzUxMjRDNSMjTU5RWEUzM1ZPTlNXWSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUGVhODY1MDg2NzZhOTk0OTY2OWUyYjczZjQ4MmM4YjljMGMxMDQwYzNiZjBmNGRjZTc2ZDM3ZjAwMDFiOWYzMjJcIn0iLCJvcmVxayI6IkUzRk5RVU9OVXZWVUZ3Rzg1UlFwemUvd3RjaWx3MWdYSDFQZDd0SC9VMFk9Iiwib3JlcWt2IjoxfQ==", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmY4MmQzMjMxLTBhYTUtNDA3ZS1iMDYwLTJkY2FiMjAwYjAxMouGZGV0YWlsjI6fMjpUUzY3NkE3QzUxMjRDNSMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "pageId": "amzn1.dv.gti.f82d3231-0aa5-407e-b060-2dcab200b012", "pageType": "detail", "pageContext": {"pageType": "detail", "pageId": "amzn1.dv.gti.f82d3231-0aa5-407e-b060-2dcab200b012"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmY4MmQzMjMxLTBhYTUtNDA3ZS1iMDYwLTJkY2FiMjAwYjAxMouGZGV0YWlsjI6fMjpUUzY3NkE3QzUxMjRDNSMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "<PERSON><PERSON>", "gti": "amzn1.dv.gti.78957cf0-7d0d-4430-9fc8-7fddba014e56", "transformItemId": "amzn1.dv.gti.78957cf0-7d0d-4430-9fc8-7fddba014e56", "synopsis": "When <PERSON>, a single mother struggling to raise two children in New York City, receives a job offer to work as a private nurse in the mid-West, it's an opportunity she can't refuse. So she uproots her family, moving them to the remote farm owned by <PERSON>, a semi-retired antiquities appraiser who has multiple sclerosis.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Horror", "Suspense", "Fantasy"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f35f5468ad3e1ea41f517f4d2812cb495bdbb0e658736c94b6244a3d495d0c9e.png", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/7fc3eb75ffb5076843ea4199aff29b6f100479c95f60cf3d2aebdcabf0b9e0a5.png", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/9e52bb370bb185d0d6a818f0140caba1e626d72468ceaee9044e1676d73dd796.png", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/0-9/101filmsuk/logos/channels-logo-white._CB558465892_.png", "poster2x3Image": null, "publicReleaseDate": 1577664000000, "runtimeSeconds": 5684, "runtime": "94 min", "overallRating": 2, "totalReviewCount": 1, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.78957cf0-7d0d-4430-9fc8-7fddba014e56", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_1"}, "refMarker": "dp_amz_c_TS5124c5_1_1", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free 7 day trial of Channel 101, auto renews at £4.99/month, rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.78957cf0-7d0d-4430-9fc8-7fddba014e56", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_1"}, "refMarker": "dp_amz_c_TS5124c5_1_1", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 584, "width": 2000, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Replicas", "gti": "amzn1.dv.gti.5eb4c94d-60c8-ad60-e60b-72cd1fcf9dde", "transformItemId": "amzn1.dv.gti.5eb4c94d-60c8-ad60-e60b-72cd1fcf9dde", "synopsis": "<PERSON><PERSON> leads this suspenseful sci-fi thriller about a neuroscientist who secretly tries to clone and create replicas of his family after a tragic car crash.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Science Fiction"], "maturityRatingString": "12", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c0b399e608174bab329c8f8662170209f9beeb13748bf0442660652d3a39d0b6.png", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/d08989b801c8c929667cf581ec8d6268a8f3be0934cc3729f07beb6dad31e8f2.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/30dd4e7cc4383db49e7985e066e9e6e37b06d3b1b60a13c1851f1970f8407d2a.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052927_.png", "poster2x3Image": null, "publicReleaseDate": 1547164800000, "runtimeSeconds": 6458, "runtime": "107 min", "overallRating": 4, "totalReviewCount": 3398, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5eb4c94d-60c8-ad60-e60b-72cd1fcf9dde", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_2"}, "refMarker": "dp_amz_c_TS5124c5_1_2", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 11 days", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5eb4c94d-60c8-ad60-e60b-72cd1fcf9dde", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_2"}, "refMarker": "dp_amz_c_TS5124c5_1_2", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis", "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "The Vanishing On 7th Street", "gti": "amzn1.dv.gti.d22a40b0-8a59-4ac8-8fcb-7ea39af86a45", "transformItemId": "amzn1.dv.gti.d22a40b0-8a59-4ac8-8fcb-7ea39af86a45", "synopsis": "The population of Detroit has almost completely disappeared, but a few remain. As daylight disappears they realize that the Dark is coming for them.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Horror", "Suspense"], "maturityRatingString": "18+", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/06e54dad0c3e82473873b3ecd9152518b6a3a161a6282de735931d3e0f86749e.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bdb4ff2955a09afc3c2c99f786239cb1837f13e2b2072ce780165320416c4879.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/5f49ca3ac5be625ab7e223037a6b155cf436c36fa1ac57e9591dfb75cf5976c7.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1296882000000, "runtimeSeconds": 5488, "runtime": "91 min", "overallRating": 3.4, "totalReviewCount": 274, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d22a40b0-8a59-4ac8-8fcb-7ea39af86a45", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_3"}, "refMarker": "dp_amz_c_TS5124c5_1_3", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d22a40b0-8a59-4ac8-8fcb-7ea39af86a45", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_3"}, "refMarker": "dp_amz_c_TS5124c5_1_3", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>ir", "gti": "amzn1.dv.gti.77fbbf97-be15-4e77-a03e-076b4d4c850f", "transformItemId": "amzn1.dv.gti.77fbbf97-be15-4e77-a03e-076b4d4c850f", "synopsis": "One of the most controversial cases in recent years in Argentina, this movie is based on the true story of <PERSON><PERSON> and the murder of her boyfriend that rocked the country. The youngest woman to be convicted and sentenced to life in prison in Argentina, but questions still remain- was this a crime of passion, or is the truth still yet to be fully discovered?", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "15", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Suspense"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/97941f28f90ac280799b55a08aac6c4feb2095d52b53bc33ed34ae0fb990639b.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e372e87feaededc03ad07cf85558754339058631156b0ac6d3bb81e066d9fd87.png", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/2529ff6827e5a5b00448b7049b449afc135d20406af000a470454b9b0ed0e3d7.png", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052927_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/daf99a892341beb1559ea5e36d1949e9955c153407cb23cee76717f071643da0.png", "publicReleaseDate": 1716336000000, "runtimeSeconds": 6414, "runtime": "106 min", "overallRating": null, "totalReviewCount": null, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.77fbbf97-be15-4e77-a03e-076b4d4c850f", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_4"}, "refMarker": "dp_amz_c_TS5124c5_1_4", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.77fbbf97-be15-4e77-a03e-076b4d4c850f", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_4"}, "refMarker": "dp_amz_c_TS5124c5_1_4", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis", "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON>", "gti": "amzn1.dv.gti.d78d71ab-4d4e-4eea-8aa9-0e12dce45756", "transformItemId": "amzn1.dv.gti.d78d71ab-4d4e-4eea-8aa9-0e12dce45756", "synopsis": "A married couple and their young daughter live in terror because of the neighbors' dog, which ends up attacking the little girl. The dog is put down to the desolation of its owners who loved it as their child. They will seek revenge, terrorizing the family and bringing justice in a terrible way.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Horror", "International", "Suspense"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a3551cf261477640e849eed5c5aaac4f8f1d9096386cdf65aa9c404b5653b6a9.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/5a5ba61e6a815734d1bc3600ee662fd41523aa8cc525f5b474bebd9380db6b76.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/5cb8237169fe6d90a3a3c9662fcf181ed465603cfde3126777453bd760ca822c.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/fba7a6b1e4aaa39ab418ca2847c5e4b92745d30f4d7b98e88835d77091c7991f.png", "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052927_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/2b2380eac8193f7ad4814ec39c63e1cc5b5c56b9a0266d3ca0095481a9a01f60.png", "publicReleaseDate": 1607731200000, "runtimeSeconds": 5317, "runtime": "88 min", "overallRating": 3.1, "totalReviewCount": 8, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d78d71ab-4d4e-4eea-8aa9-0e12dce45756", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_5"}, "refMarker": "dp_amz_c_TS5124c5_1_5", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d78d71ab-4d4e-4eea-8aa9-0e12dce45756", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_5"}, "refMarker": "dp_amz_c_TS5124c5_1_5", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "titleLogoImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis", "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Seeking Justice", "gti": "amzn1.dv.gti.f139d235-f60d-4a10-a126-c2d6518a38f5", "transformItemId": "amzn1.dv.gti.f139d235-f60d-4a10-a126-c2d6518a38f5", "synopsis": "<PERSON> is an high school teacher who is happily married to <PERSON>, an accomplished musician. One fateful evening <PERSON> is brutally assaulted and while awaiting an update on her condition, <PERSON> is approached by a well-dressed man who offers to dispense immediate justice. He accepts the proposal and is pulled into an underground vigilante organization that leads to dangerous consequences.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Suspense"], "maturityRatingString": "18+", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f8115d9a5eb7e09ee3fde998f9688339596185411cb97004f3e2acf287d02ce0.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/aaf5362f69fc70c1f16e09f7ab47135ae0a22d3a7118b024aaee6269a8575a8d.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/084d55e3d66439a618553daa6b54b12deebe99c9f819788de34c5403b67ecb64.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1331870400000, "runtimeSeconds": 6305, "runtime": "105 min", "overallRating": 4, "totalReviewCount": 350, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f139d235-f60d-4a10-a126-c2d6518a38f5", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_6"}, "refMarker": "dp_amz_c_TS5124c5_1_6", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f139d235-f60d-4a10-a126-c2d6518a38f5", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_6"}, "refMarker": "dp_amz_c_TS5124c5_1_6", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Lost Angel", "gti": "amzn1.dv.gti.b9e5f0d9-c999-4374-bf2f-e576455e9123", "transformItemId": "amzn1.dv.gti.b9e5f0d9-c999-4374-bf2f-e576455e9123", "synopsis": "<PERSON>'s quest for the truth about her sister's death leads her to uncover shady criminal activity that puts her own life in danger. Her only ally, a mysterious man called <PERSON>, is hiding a dark secret of his own.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/000bc5c95450979f7d4a56f306e57b73e27b19185ad336ec71bb339af6610245.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e766b7c7e98b8bbf64a85cc208af5f60a216140485ec7676733392af7cf248b7.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/f477268b098ed26a064528e1d847e0051ceb1f116a23556a0098e6a3f9ab2c54.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1647327600000, "runtimeSeconds": 5393, "runtime": "89 min", "overallRating": null, "totalReviewCount": null, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b9e5f0d9-c999-4374-bf2f-e576455e9123", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_7"}, "refMarker": "dp_amz_c_TS5124c5_1_7", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b9e5f0d9-c999-4374-bf2f-e576455e9123", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_7"}, "refMarker": "dp_amz_c_TS5124c5_1_7", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Stir of Echoes", "gti": "amzn1.dv.gti.eea9f66d-f219-6611-0bd0-5ed109ceb925", "transformItemId": "amzn1.dv.gti.eea9f66d-f219-6611-0bd0-5ed109ceb925", "synopsis": "After being hypnotized by his sister in law, a man begins seeing haunting visions of a girl's ghost and a dark mystery begins to unfold around him.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense", "Horror"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/40f430a03b4a3282b750d5aa18361641127cb539ccd248ff453642dc25fcfba5.png", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/dc8a2cb836708d6fec4abc2760c1b7782e9e1babd9184926f87c8ba0b41c33a3.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/93137dae234d1f96f112908d0332a65187606a5a24f219bfe8f8fe4858a41c16.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 959299200000, "runtimeSeconds": 5946, "runtime": "99 min", "overallRating": 4.6, "totalReviewCount": 1362, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.eea9f66d-f219-6611-0bd0-5ed109ceb925", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_8"}, "refMarker": "dp_amz_c_TS5124c5_1_8", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.eea9f66d-f219-6611-0bd0-5ed109ceb925", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_8"}, "refMarker": "dp_amz_c_TS5124c5_1_8", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "House on Rockingham", "gti": "amzn1.dv.gti.362a0d44-e08e-400e-8f3c-5b1220210f01", "transformItemId": "amzn1.dv.gti.362a0d44-e08e-400e-8f3c-5b1220210f01", "synopsis": "A young woman takes up a job as a live in housekeeper and slowly learns the owner may be connected to her sisters disappearance.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense", "Drama", "Horror"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/96ce6262eabe52968a34896679988e9972ef6bdb0b9f967a926319534165de96.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/629bc7979b65aa36ae3091a84440b813eb823e8a4c09ee344b53de7c893fda75.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1714694400000, "runtimeSeconds": 4703, "runtime": "78 min", "overallRating": null, "totalReviewCount": null, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.362a0d44-e08e-400e-8f3c-5b1220210f01", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_9"}, "refMarker": "dp_amz_c_TS5124c5_1_9", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.362a0d44-e08e-400e-8f3c-5b1220210f01", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_9"}, "refMarker": "dp_amz_c_TS5124c5_1_9", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Invisible Child", "gti": "amzn1.dv.gti.eeb01402-a99c-79f7-7ad3-b51180fbaf29", "transformItemId": "amzn1.dv.gti.eeb01402-a99c-79f7-7ad3-b51180fbaf29", "synopsis": "<PERSON> (<PERSON>) and <PERSON> (<PERSON>) hire <PERSON> as a nanny for their daughter <PERSON>...who only exists in the mind of her mother to cope with emotional trauma. <PERSON> plays along until the authorities find out and threaten to put <PERSON> away.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama"], "maturityRatingString": "13+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2426397e8f09ea6c3137e15aa18a3ad88f9c62d58cad23de4045310907e6fbe5.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/9382609444818e84a6fea04fe76829ba69488d580528f58bad4f24593485e625.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 920851200000, "runtimeSeconds": 5451, "runtime": "90 min", "overallRating": 3.9, "totalReviewCount": 47, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.eeb01402-a99c-79f7-7ad3-b51180fbaf29", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_10"}, "refMarker": "dp_amz_c_TS5124c5_1_10", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.eeb01402-a99c-79f7-7ad3-b51180fbaf29", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_10"}, "refMarker": "dp_amz_c_TS5124c5_1_10", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "dp_amz_c_TS5124c5_1", "ClientSideMetrics": "484|CkMKI0NBV1JlbW90ZVN0cmF0ZWd5TGl2ZURlZmF1bHREZWZhdWx0EhAyOlRTNjc2QTdDNTEyNEM1GgAiCFRTNTEyNGM1EmsKBmRldGFpbBIxYW16bjEuZHYuZ3RpLmY4MmQzMjMxLTBhYTUtNDA3ZS1iMDYwLTJkY2FiMjAwYjAxMiIGY2VudGVyKgAyJGZhMWU1NWM1LTNmMGEtNDYwNi1hMmJlLTk3MmJjNTExMzYyZRoDYWxsIgNhbGwqA2FsbDIIY2Fyb3VzZWw6HkFUVkN1c3RvbWVyc0Fsc29XYXRjaGVkU2VydmljZUIdYWl2LWNvbnN1bXB0aW9uLXB1cmNoYXNlLXNpbXNKClZPRENvbnRlbnRSC25vdEVudGl0bGVkWgBiEFN0YW5kYXJkQ2Fyb3VzZWxoAXIAeiAwZTViZDJmNjRmNWI4N2Q2NmQ4YmRiMTFiYjBmODMyY4IBA2FsbIoBAJIBAA=="}, "tags": [], "journeyIngressContext": "16|CgNhbGwSA2FsbA==", "seeMore": null, "type": "STANDARD_CAROUSEL"}]}, "metadata": {"requestId": "0e5bd2f64f5b87d66d8bdb11bb0f832c", "requestedTransformId": "lr/detailsPage/detailsPageBTF", "domain": "prod", "realm": "eu-west-1", "timestamp": "2024-06-19T01:49:23.86526Z"}}