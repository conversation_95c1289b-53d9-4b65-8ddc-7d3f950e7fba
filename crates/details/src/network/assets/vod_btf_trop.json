{"resource": {"seasonsEpisodes": {"selectedSeasonIndex": 0, "selectedEpisodeIndex": 7, "seasons": [{"gti": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935", "seasonNumber": 1, "offerIcon": null}], "episodesBySeasonIndex": {"0": [{"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 3947, "timecodeSeconds": 3591, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 3947, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.eedf35c9-fa32-4c9f-8516-bfa78ab759de", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/8142b0f00113ee5855c8831da6a9b1e0a96a6d5f68a373b8d620acdc309dc8f2._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/2520061e6dad539f1723a71fc4c18491201eeff38f0de8a4823f5d4dcfc2dae0._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "regulatoryRating": "12", "releaseDate": 1662076800000, "runtimeSeconds": 3947, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "Series Premiere. <PERSON><PERSON><PERSON> is disturbed by signs of an ancient evil’s return; <PERSON><PERSON><PERSON> makes an unsettling discovery; <PERSON><PERSON><PERSON> is presented with an intriguing new venture; <PERSON><PERSON> breaks the Harfoot community’s most deeply-held rule.", "title": "A Shadow of the Past", "titleType": "EPISODE", "currentEpisodeNumber": 1, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": true, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use", "foul language"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 4022, "timecodeSeconds": 3680, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use", "foul language"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 4022, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.ad2d34d7-a239-4105-896e-de360fdb0eba", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/295daf333d1c4187089d3f73ec8488151108199663638a8060d3fc8036916485._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/659a0a13d1add429575fd1af7e355deece6226f5c878b0410ed1c3e18155a75d._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "regulatoryRating": "12", "releaseDate": 1662076800000, "runtimeSeconds": 4022, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "<PERSON><PERSON><PERSON> finds a new ally; <PERSON><PERSON><PERSON> faces a cold reception from an old friend; <PERSON><PERSON> endeavors to help a Stranger; <PERSON><PERSON><PERSON> searches for answers while <PERSON><PERSON><PERSON> warns her people of a threat.", "title": "Adrift", "titleType": "EPISODE", "currentEpisodeNumber": 2, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 4162, "timecodeSeconds": 3873, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 4162, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.fbb73ae3-eeb4-4d24-982a-b7a9f5f60fdf", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/80363d7d48deb12118a3addb519449387293a5debab177b292306f6a4214e85a._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/99d20d6da10f023acd170de112e3254e42745d917838d254ec98128ba3838f34._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1662681600000, "runtimeSeconds": 4162, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "<PERSON><PERSON><PERSON> finds himself a captive; <PERSON><PERSON><PERSON> and <PERSON><PERSON> explore a legendary kingdom; <PERSON><PERSON><PERSON> is given a new assignment; <PERSON><PERSON> faces the consequences.", "title": "<PERSON><PERSON>", "titleType": "EPISODE", "currentEpisodeNumber": 3, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 4303, "timecodeSeconds": 4025, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 4303, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.7400400d-e675-4c9f-9730-6bbe8219bb80", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/817e0f1581e53679c49aea75ede092a277a2c43c0610b0ff5ba525953e2afadf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/9e56f5a95bdce87f0d87d92855f035000af59e5d25d593f35e91dd792cb0ca3a._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "regulatoryRating": "12", "releaseDate": 1663286400000, "runtimeSeconds": 4303, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "Queen Regent <PERSON><PERSON><PERSON>’s faith is tested; <PERSON><PERSON><PERSON><PERSON> finds himself at a crossroads; <PERSON><PERSON><PERSON> uncovers a secret; <PERSON><PERSON><PERSON> is given an ultimatum; <PERSON> disobeys <PERSON><PERSON><PERSON>.", "title": "The Great Wave", "titleType": "EPISODE", "currentEpisodeNumber": 4, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": [], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_FINISHED", "userPlaybackMetadata": {"runtimeSeconds": 4330, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.d77f9fa8-afaf-4365-be83-9387e48d9264", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ebbe2a11ffd9370e2eb17621733f614bb23556aeb68572596d1873e1ea0fa5e8._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/a54eea43cb97a75e3f1d2c57bd6fe7a16559fbbd00509c5b183bf918f7d97b43._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "regulatoryRating": "12", "releaseDate": 1663891200000, "runtimeSeconds": 4330, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "<PERSON><PERSON> questions her instincts; <PERSON><PERSON><PERSON> struggles to stay true to his oath; <PERSON><PERSON> weighs his destiny; The Southlanders brace for attack.", "title": "Partings", "titleType": "EPISODE", "currentEpisodeNumber": 5, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 4169, "timecodeSeconds": 37, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "15", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 4169, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.740c4e8e-f8ff-475b-96aa-88be0b49fcbc", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/98bd2d4a4cf90db7a2d6c39f324af367cd1644f621c2078a94b88d5ab6a18cd9._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c93ab97d0530dcc82c07ad1c17277c0d5ffe57f163a90cc10c990c3c38042de6._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "15"}, "regulatoryRating": "15", "releaseDate": 1664496000000, "runtimeSeconds": 4169, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "<PERSON><PERSON> and his army march on Ostirith.", "title": "Udûn", "titleType": "EPISODE", "currentEpisodeNumber": 6, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 4333, "timecodeSeconds": 4043, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 4333, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.df6f335d-8da2-4b9a-a71c-2ed0b19f04ba", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/875feacc1337cc9730fb7d44cf9e5439cb520bca2478cdfd84d05691c9bb4399._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/45c0dbd21425875325e3da87bbdcfbcab2f7e5839a38fd91f1f88428a440bc48._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "regulatoryRating": "12", "releaseDate": 1665100800000, "runtimeSeconds": 4333, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "Survivors of a cataclysm try to find safety; the Harfoots confront evil; <PERSON><PERSON> is torn between friendship and duty; <PERSON><PERSON> considers a new name.", "title": "The Eye", "titleType": "EPISODE", "currentEpisodeNumber": 7, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": false, "isLastInSeason": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}, {"actions": [{"childActions": [], "label": "Resume", "metadata": {"contentDescriptors": [], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 4346, "timecodeSeconds": 4019, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}, {"childActions": [], "label": "Start over", "metadata": {"contentDescriptors": [], "regulatoryRating": "12", "refMarker": "atv_dp_btf_el_prime_hd_tv_wfb_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 4346, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "th_TH", "name": "ไทย"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "en_US", "name": "English"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}], "entityType": "TV Show", "gti": "amzn1.dv.gti.13144a5d-792f-4593-922c-e9ee8f7ddb24", "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ea0be2a561cd5f81e3945287d1e2918c5b73eec1952aa8cd07961f4687c2c1ae._RI_TTW_.jpg", "providerLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e._RI_TTW_.png", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d1f7cd5c9d12b41f3afcae8470c9f39c9ddfa51c96c9704508b859cd0e72e1c0._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/44bd0cc5a87ed65763c48d1c1c396ea5a6eee602ff3bf82679be055298830332._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e230fa1e2b85d368e90687fa567ee9633bfa90d1754ad8ad4be975dc7a1c5d5b._RI_TTW_.png", "cover": "http://ecx.images-amazon.com/images/S/pv-target-images/d192766d1e83bf478887dffe0d48cf9fa80f21eca11099ec42508800dbe011bf._UY500_UX667_PGLock-Icon-Landscape_RI_TTW_.jpg"}, "isInWatchlist": true, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "regulatoryRating": "12", "releaseDate": 1665705600000, "runtimeSeconds": 4346, "ratingsMetadata": {"count": 8910, "rating": 3.2}, "subtitles": [{"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "zh_HANT", "name": "中文（繁體）"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "ro_RO", "name": "Română"}], "synopsis": "Season One Finale. New alliances are forged.", "title": "Alloyed", "titleType": "EPISODE", "currentEpisodeNumber": 8, "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": true, "applyAudioDescription": true}, "isFirstInSeason": false, "isLastInSeason": true, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}}]}}, "castAndCrew": [{"id": "/name/nm6077056/", "name": "<PERSON><PERSON><PERSON><PERSON>", "image": {"id": "/name/nm6077056/images/rm2815097600", "url": "http://ecx.images-amazon.com/images/M/MV5BMTc4OTMyNzMxNF5BMl5BanBnXkFtZTgwODUzMDExMzE@._V1_.jpg", "width": 461, "height": 576}, "miniBio": "<PERSON><PERSON><PERSON><PERSON> is a Swedish-born Welsh actress. She is best known for <PERSON> (2019) and The Lord of the Rings TV Series.\n\nFor her work in Saint Maud she was nominated for Best Actress of the British Independent Film Awards, and the BAFTA Rising Star Award.\n\nShe also appears in The Falling (2014), Pride and Prejudice and Zombies (2016), Love & Friendship (2016), The Man Who Invented Christmas (2017), and The Personal History of David <PERSON> (2019).\n\nHer feature film debut was in Madame Bovary (2014), but she also starred in the short film Two Missing (2014), and had two works on TV."}, {"id": "/name/nm3033454/", "name": "<PERSON><PERSON><PERSON>", "image": {"id": "/name/nm3033454/images/rm825060864", "url": "http://ecx.images-amazon.com/images/M/MV5BNTcxNzkxNzc5MV5BMl5BanBnXkFtZTgwODc2MjA0NzM@._V1_.jpg", "width": 1582, "height": 2048}, "miniBio": "<PERSON><PERSON><PERSON> is an actor, entrepreneur currently based in New York. Born and raised in Aguas Buenas, Puerto Rico. At age fifteen, <PERSON><PERSON><PERSON> began working locally in commercials, TV and film shortly after joining his High School Drama Club. In 2006 he decided to further pursue his dream and moved to New York City where he studied at NYU's Tisch Shcool of the Arts. in NYC <PERSON><PERSON><PERSON> has been recognized for his acting work with several awards and critical acclaim named one of the 25 Leaders of the future by Latino Leaders Magazine, and awarded a VOCES de Ford Award by Ford Motors, which is given to trailblazing exemplary Individuals in the media and entertainment Industry."}, {"id": "/name/nm8190914/", "name": "<PERSON>", "image": {"id": "/name/nm8190914/images/rm1009912833", "url": "http://ecx.images-amazon.com/images/M/MV5BYTZiZmI1YTItMmQ4ZC00Y2U0LWJjMDktZDI0ZjgxNjhiMDY3XkEyXkFqcGdeQXVyMTUzMTg2ODkz._V1_.jpg", "width": 2467, "height": 3500}, "miniBio": "<PERSON> is known for The Lord of the Rings: The Rings of Power (2022), <PERSON> (2016) and <PERSON> Help."}, {"id": "/name/nm9220161/", "name": "<PERSON><PERSON>", "image": {"id": "/name/nm9220161/images/rm2274652928", "url": "http://ecx.images-amazon.com/images/M/MV5BNDhhNjkwYjUtYzc2OC00Nzc5LTkxMzEtYzcwNzU5MmJjNzY2XkEyXkFqcGdeQXVyOTQ2MDc1NTg@._V1_.jpg", "width": 4128, "height": 2752}, "miniBio": "<PERSON><PERSON> was born in 2000 in Australia. She is an actress, known for My First Summer (2020), The Lord of the Rings: The Rings of Power (2022) and True History of the Kelly Gang (2019)."}, {"id": "/name/nm10090380/", "name": "<PERSON>", "image": {"id": "/name/nm10090380/images/rm2630025985", "url": "http://ecx.images-amazon.com/images/M/MV5BNDIxNTlkNTItZmE3ZC00MWQ5LWIwNDYtMDk4ZGM1ZGVhOTRhXkEyXkFqcGdeQXVyMzQ3Nzk5MTU@._V1_.jpg", "width": 780, "height": 438}, "miniBio": "<PERSON> is known for The Lord of the Rings: The Rings of Power (2022), <PERSON><PERSON><PERSON>t (2018) and Doctors (2000)."}, {"id": "/name/nm1070709/", "name": "<PERSON>", "image": {"id": "/name/nm1070709/images/rm3468154112", "url": "http://ecx.images-amazon.com/images/M/MV5BMjE2MTIxMTI4MV5BMl5BanBnXkFtZTgwNDk5OTMyMTE@._V1_.jpg", "width": 1000, "height": 800}, "miniBio": "<PERSON> is known for The Starter Wife (2007), The Lord of the Rings: The Rings of Power (2022) and Nightmares & Dreamscapes: From the Stories of <PERSON> (2006)."}, {"id": "/name/nm1621849/", "name": "<PERSON>", "image": {"id": "/name/nm1621849/images/rm2739459328", "url": "http://ecx.images-amazon.com/images/M/MV5BYmIwNTFjYzMtZWE0MC00ZTFjLWE2NTctY2Y4NjdmYzEwMTMwXkEyXkFqcGdeQXVyMjQwMDg0Ng@@._V1_.jpg", "width": 800, "height": 1000}, "miniBio": "<PERSON> was born in 1977 in Newcastle, Tyne and Wear, England, UK. He is an actor, known for The Lord of the Rings: The Rings of Power (2022), <PERSON> (2019) and Silent Witness (1996)."}, {"id": "/name/nm1870963/", "name": "<PERSON>", "image": {"id": "/name/nm1870963/images/rm2853046528", "url": "http://ecx.images-amazon.com/images/M/MV5BYjRkMjIwNWItMjA4NS00YWUzLWE1YjQtOWMzZWFmOTZmODlhXkEyXkFqcGdeQXVyMjQwMDg0Ng@@._V1_.jpg", "width": 6000, "height": 8007}, "miniBio": "<PERSON> is an English-born actress. She was born in London; her mother is from Ghana and her father is an United States citizen. She moved to US when she was four, and was raised by her mother in a suburb of Washington, DC. She is a graduate of Montgomery Blair High School in Silver Spring, MD, and studied at the Tisch School of the Arts with a Bachelor of Fine Arts in Theater. In addition, she trained at Lee Strasberg Theater Institute and in dance forms ballet, jazz, tap. She received Navy Seal and New Zealand Special Forces training during the filming of Spartacus: War of the Damned."}, {"id": "/name/nm0377901/", "name": "<PERSON>", "image": {"id": "/name/nm0377901/images/rm2317585920", "url": "http://ecx.images-amazon.com/images/M/MV5BMTI4MjQ4NjQwMF5BMl5BanBnXkFtZTcwNTA4NDgwMw@@._V1_.jpg", "width": 1581, "height": 2048}, "miniBio": "<PERSON><PERSON> was born on August 29, 1958, in Dudley, West Midlands in England to a family of Jamaican immigrants. He made his TV debut on a talent show called \"New Faces\" in 1975 at the age of 16. He won and went on to things such as The Fosters (1976) and <PERSON><PERSON><PERSON> (1974), which was when his career as a comedian took off. In 1989, <PERSON> made the stand-up comedy movie <PERSON> Henry: Lenny Live and Unleashed (1989), which caught the eyes of the Walt Disney Company, which gave him the lead role in the American movie True Identity (1991) and a contract to do two other Disney films for about US$1 million. The movie flopped, bringing in less than US$5 million. The contract was canceled, and <PERSON> got half of what he would have if he had done the three films. <PERSON> is now becoming well-known in the United States for the role of <PERSON> in the hit BBC show <PERSON>! (1993)."}, {"id": "/name/nm0654197/", "name": "<PERSON>", "image": {"id": "/name/nm0654197/images/rm403855617", "url": "http://ecx.images-amazon.com/images/M/MV5BNWU5MzE5YzgtNWJkZi00Yjc5LWJkMDMtZjljOGQ5NWQwMzk4XkEyXkFqcGdeQXVyMTAxNzU5MDEw._V1_.jpg", "width": 630, "height": 847}, "miniBio": "<PERSON> was born in Westminster, London, England, UK. He is an actor and director, known for The Lord of the Rings: The Rings of Power (2022), <PERSON> (2006) and <PERSON> 18 (2011). He is married to <PERSON>. They have two children."}], "containerList": [{"facet": {"text": null}, "title": "Trailers", "titleImageUrl": null, "paginationLink": null, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmY4NTY0NjJlLWYzZjAtNDdkNi05OWE3LThlNzkwMGZmYjkzNYuGZGV0YWlsjI6fMToxMUNZM1FEQjJDNklOUCMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "Comic-Con Trailer", "gti": "amzn1.dv.gti.61d263a9-3403-49dc-b82e-a7c1860fd5f3", "transformItemId": null, "synopsis": "Discover the legend that forged the rings.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/0bc1b6b0e1b2214c25a31baef4a9b4bed3dc73d13a64640f1448445fe7b7d8d8._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1658448000000, "runtimeSeconds": 182, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 182, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.61d263a9-3403-49dc-b82e-a7c1860fd5f3", "metadataActionType": "Playback"}, "label": "Play Trailer"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Main Teaser", "gti": "amzn1.dv.gti.35db8272-dace-49ae-9e4e-c75450db7cee", "transformItemId": null, "synopsis": "Set thousands of years before the events of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s 'The Hobbit' and 'The Lord of the Rings', The Rings of Power follows an ensemble cast of characters, both familiar and new, as they confront the long-feared re-emergence of evil to Middle-earth.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_drama", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/9a8981b4acfe35a0ba32b811662e486018d955835f9ba6d1749b82ebb9ab54a9._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1657756800000, "runtimeSeconds": 151, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 151, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.35db8272-dace-49ae-9e4e-c75450db7cee", "metadataActionType": "Playback"}, "label": "Play Trailer"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Teaser Trailer", "gti": "amzn1.dv.gti.34db2160-c87a-43cf-9e37-9a9be1a8981b", "transformItemId": null, "synopsis": "Before the King, Before the Fellowship, Before the Ring… A NEW LEGEND BEGINS.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/eb24ca146fe40f1b5780e9c0e1defd0dd144e09410ba6688a708b1bdd4213079._UR1920,1080_RI_.jpg", "publicReleaseDate": 1644796800000, "runtimeSeconds": 60, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 60, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.34db2160-c87a-43cf-9e37-9a9be1a8981b", "metadataActionType": "Playback"}, "label": "Play Trailer"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}], "analytics": {"refMarker": "dp_amz_e_bxN4fE_1", "ClientSideMetrics": "484|ClUKJ0NhcmF2YW5FeHRyYXNUcmFpbGVyc0xpdmVEZWZhdWx0RGVmYXVsdBIQMToxMUNZM1FEQjJDNklOUBoQMjpEWUM3QjJCMzYyQTVBMCIGYnhONGZFEmsKBmRldGFpbBIxYW16bjEuZHYuZ3RpLmY4NTY0NjJlLWYzZjAtNDdkNi05OWE3LThlNzkwMGZmYjkzNSIGZXh0cmFzKgAyJGViMjAyYjgyLTlkMGItNDg0Ni04YWZlLTg1NWVlYmE3ZGRkZBoAIgAqADIIY2Fyb3VzZWw6HEFUVlZhbUNvbnRlbnRQcm92aWRlclNlcnZpY2VCHlRSQUlMRVJfQ0FST1VTRUxfU1RSQVRFR1lfVFlQRUoKVk9EQ29udGVudFILbm90RW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgBcgB6IGQxNjEwMmEwNTdkNGVhODkxYjIyODFmZmQ5NjNhZjI4ggEDYWxs"}, "tags": [], "seeMore": null, "type": "STANDARD_CAROUSEL"}, {"facet": {"text": null}, "title": "Bonus", "titleImageUrl": null, "paginationLink": null, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7jio6xYW16bjEuZHYuZ3RpLmY4NTY0NjJlLWYzZjAtNDdkNi05OWE3LThlNzkwMGZmYjkzNYuGZGV0YWlsjI6eMToxUU9JUjVWVDZTNFpEIyNNTlFYRTMzVk9OU1dZjQ-OglYy", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "The Making of The Rings of Power - Episode 7", "gti": "amzn1.dv.gti.035784fa-bcf8-4736-99e1-f73e2207e7ec", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/d143eea6732eea5be81d73bb1464e7afe49f2d8b73ea156b41e166c06be5c612._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 342, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "The Making of The Rings of Power - Episode 2", "gti": "amzn1.dv.gti.078733cd-e790-4259-979e-ccb3743bd92c", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/726097bb2dea22a04be64f5669edec11f6d20f108d9359e5994f841ee193ff60._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 360, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "The Making of The Rings of Power - Episode 8", "gti": "amzn1.dv.gti.1b6889a1-98db-4758-b73b-fd0d9f0de78f", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/49a10c274847519caec359a2ac4e245408c8d9e8926e791956b5e7b63b060ac0._UR1920,1080_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 357, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "The Making of The Rings of Power - Episode 4", "gti": "amzn1.dv.gti.2ebe97ee-8378-4e7f-88aa-43056ea4c5ad", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/097d2fd664dbc4d3cdda47f6ed93b883ef369c2ce387a390804e849bca701479._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 345, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "The Making of The Rings of Power - Episode 6", "gti": "amzn1.dv.gti.4bb3240a-5b4b-43a2-a48d-5b45d094c522", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/790f762d2fa1b5966e88648e27593e63d3edc594228592459c79cd0197c70537._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 359, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "The Making of The Rings of Power - Episode 5", "gti": "amzn1.dv.gti.c6e3ad23-0739-420d-9fdc-d4123dc7b781", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/69cf8ae6811b587a162ba6553e52c715a3060975936b10b9ab1d5e0d94bff5c8._UR1920,1080_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 359, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "The Making of The Rings of Power - Episode 1", "gti": "amzn1.dv.gti.d6e0197e-fb65-4b01-9507-a9d6524c38a6", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/4e576e40856054fe11adb57f6dea08a6b812145129bf7321fd0dedee0ecf1b42._UR1920,1080_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 359, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "The Making of The Rings of Power - Episode 3", "gti": "amzn1.dv.gti.fd461157-d22f-4395-bc68-270716d63f90", "transformItemId": null, "synopsis": "Go behind-the-scenes of The Lord of the Rings: The Rings of Power, season 1 and see how this epic world was brought to life.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/f1481dc8df9bed91bdcfa79575c43163822975a991907c69e6b5d300c0fd5434._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1668988800000, "runtimeSeconds": 336, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": null, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Preview of Ep8 – The Rings of Power", "gti": "amzn1.dv.gti.cd897657-68c3-46a1-bf4a-8e9ea5a69564", "transformItemId": null, "synopsis": "Watch a preview for the season 1 finale of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/3c2e1ff0d6518f3d648a0ff0cb24ce8f5dd0dacbeaba13e1b4cc13d44ba4f4cc._UR1920,1080_RI_.jpg", "publicReleaseDate": 1665100800000, "runtimeSeconds": 30, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 30, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.cd897657-68c3-46a1-bf4a-8e9ea5a69564", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Preview of Ep7 – The Rings of Power", "gti": "amzn1.dv.gti.fd417ea7-4f51-4d72-84f3-db9817c30709", "transformItemId": null, "synopsis": "Watch a preview for Episode 7 of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/12a5f0bf9b0e1bfab1381c3af0f19568e810db0eb7a3d4104066dbeaf7fbb585._UR1920,1080_RI_.jpg", "publicReleaseDate": 1664496000000, "runtimeSeconds": 30, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 30, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.fd417ea7-4f51-4d72-84f3-db9817c30709", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Preview of Ep6 – The Rings of Power", "gti": "amzn1.dv.gti.446bf5d7-438c-418d-bb2d-d38eecd3a34b", "transformItemId": null, "synopsis": "Watch a preview for Episode 6 of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c5886895aa5f6f24ede6ea5d1628e8fae02882afd120766aadee5a6fb9bb5ef3._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1663891200000, "runtimeSeconds": 30, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 30, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.446bf5d7-438c-418d-bb2d-d38eecd3a34b", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Preview of Ep5 – The Rings of Power", "gti": "amzn1.dv.gti.1ae64eec-1a74-4a71-9278-1b2722c7430c", "transformItemId": null, "synopsis": "Watch a preview for Episode 5 of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/fa923ff0dee002a27a79aa37de46480cea79398a4f6f2b5d04c547ce8ccf3064._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1663286400000, "runtimeSeconds": 30, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 30, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.1ae64eec-1a74-4a71-9278-1b2722c7430c", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Preview of Ep4 – The Rings of Power", "gti": "amzn1.dv.gti.6676baa5-cc61-4485-8a31-af7e833f0b53", "transformItemId": null, "synopsis": "Watch a preview for Episode 4 of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/6b8c116ab2f2ca1749880e32ae7d0e2b620317c442a86f6464ee338e65e27e8a._UR1920,1080_RI_.jpg", "publicReleaseDate": 1662681600000, "runtimeSeconds": 30, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 30, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.6676baa5-cc61-4485-8a31-af7e833f0b53", "metadataActionType": "Playback"}, "label": "Resume"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Stories of the Second Age – Rhovanion", "gti": "amzn1.dv.gti.1a83ecf6-6900-4ce0-939c-68dff4582c51", "transformItemId": null, "synopsis": "Cast provide a behind-the-scenes glimpse at the Harfoots, a mysterious band of early hobbits hidden from sight until now.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/ef32463e28ef105d8963a57bbb2db18d335377f72c703e2f1b7c39b77f195a27._UR1920,1080_RI_.jpg", "publicReleaseDate": 1662076800000, "runtimeSeconds": 76, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 76, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.1a83ecf6-6900-4ce0-939c-68dff4582c51", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Stories of the Second Age – Lindon", "gti": "amzn1.dv.gti.99b52118-eefc-47b4-ba14-162410277ff4", "transformItemId": null, "synopsis": "Enter the capital of the High Elves and learn from cast how these immortal beings deal with the rising evil in Middle-earth.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/b1b549bf2354accc844046fbd7c9ca9bd0dec7f412d7f81ae98c27f57de3c045._UR1920,1080_RI_.jpg", "publicReleaseDate": 1662508800000, "runtimeSeconds": 73, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 73, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.99b52118-eefc-47b4-ba14-162410277ff4", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Stories of the Second Age – Khazad-dûm", "gti": "amzn1.dv.gti.7b4b83e8-cd17-4cf4-87f3-f17825c746b8", "transformItemId": null, "synopsis": "Learn about the secretive realm of the dwarves as cast discuss what mysteries might lie beneath the Misty Mountains.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/e2e4f702338f9e1eac28ba3f8b070ae4dbfb98f7366d861eca0e9d4a38268456._UR1920,1080_RI_.jpg", "publicReleaseDate": 1662076800000, "runtimeSeconds": 72, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 72, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.7b4b83e8-cd17-4cf4-87f3-f17825c746b8", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Stories of the Second Age – Númenor", "gti": "amzn1.dv.gti.921e5dfe-241d-48f6-95f6-3ee524e4f364", "transformItemId": null, "synopsis": "Cast explore the breathtaking island kingdom of Númenor and share background on a society at the height of its power.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/920174033b15238e266fe26da0eb9c2b113cd656ef44dba1c40f4d316c6f716a._UR1920,1080_RI_.jpg", "publicReleaseDate": 1662076800000, "runtimeSeconds": 76, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 76, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.921e5dfe-241d-48f6-95f6-3ee524e4f364", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Stories of the Second Age – The Southlands", "gti": "amzn1.dv.gti.e05c4ca0-c473-4f20-9225-50e9fe68200e", "transformItemId": null, "synopsis": "Paying the price for decisions made generations ago, The Southlanders must now face a new evil might on lurking on their horizon.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8045f964d621135b299de8e7d9e049b27353b5847df21481bd8e48f0a6434cc6._UR1920,1080_RI_.jpg", "publicReleaseDate": 1662076800000, "runtimeSeconds": 76, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 76, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.e05c4ca0-c473-4f20-9225-50e9fe68200e", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Preview of Ep3 – The Rings of Power", "gti": "amzn1.dv.gti.c85883fa-61a3-4b82-a52b-0a05d834576a", "transformItemId": null, "synopsis": "Watch a preview for Episode 3 of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/84e03030774236bbeb874497b0e7ced13258be4265ec48bf44fd658ef1d61f3d._UR1920,1080_RI_.jpg", "publicReleaseDate": 1662076800000, "runtimeSeconds": 30, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 30, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.c85883fa-61a3-4b82-a52b-0a05d834576a", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Preview of Ep2 – The Rings of Power", "gti": "amzn1.dv.gti.532156e8-cf48-4414-acc2-76394599cf18", "transformItemId": null, "synopsis": "Watch a preview for Episode 2 of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/ef1d07b382e17cf7c2d94bc614483378e9f439a377747b8e33250b7e589b4e20._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1662076800000, "runtimeSeconds": 30, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 30, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.532156e8-cf48-4414-acc2-76394599cf18", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Season Preview – The Rings of Power", "gti": "amzn1.dv.gti.5820a577-c7c6-4fbf-827d-7da5af068042", "transformItemId": null, "synopsis": "Watch a preview for this season of The Lord of the Rings: The Rings of Power.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_fantasy", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/1ff67043af2e2fe830bbf2c2d6ea7da7627190c48bb421606701014ebd879423._UR1920,1080_PGLock-Icon-Landscape_RI_.jpg", "publicReleaseDate": 1662076800000, "runtimeSeconds": 109, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"runtimeSeconds": 109, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.5820a577-c7c6-4fbf-827d-7da5af068042", "metadataActionType": "Playback"}, "label": "Bonus{lineBreak}Watch Now"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}, {"title": "Title Reveal", "gti": "amzn1.dv.gti.7e0e5af9-1179-4125-a78a-4748b4e1ca5c", "transformItemId": null, "synopsis": "Sparks fly. Molten metal flows. A title reveal of the ages that will prepare you for a new legend coming to Middle-earth.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EPISODE", "isInWatchlist": true, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": null, "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/aebfb5353e37207e2535af21d58d264e3069053607a1da630206ccdd65059def._UR1920,1080_RI_.jpg", "publicReleaseDate": 1642550400000, "runtimeSeconds": 60, "overallRating": null, "totalReviewCount": null, "watchProgress": null, "action": {"refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_btf_el_prime_hd_tv_resume_t1ACAAAAAA0lr0", "position": "FEATURE_IN_PROGRESS", "userPlaybackMetadata": {"runtimeSeconds": 60, "timecodeSeconds": 0, "hasStreamed": true, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "Feature", "playbackTitle": "amzn1.dv.gti.7e0e5af9-1179-4125-a78a-4748b4e1ca5c", "metadataActionType": "Playback"}, "label": "Continue watching"}, "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 6X nominee", "icon": null, "level": null, "type": null}}, "widgetType": "vodExtraContent", "cardType": "VOD_EXTRA_CONTENT"}], "analytics": {"refMarker": "dp_amz_e_kVjxNB_2", "ClientSideMetrics": "476|ClEKJENhcmF2YW5FeHRyYXNCb251c0xpdmVEZWZhdWx0RGVmYXVsdBIPMToxUU9JUjVWVDZTNFpEGhAyOkRZQTE0RENGQjJGM0VGIgZrVmp4TkISawoGZGV0YWlsEjFhbXpuMS5kdi5ndGkuZjg1NjQ2MmUtZjNmMC00N2Q2LTk5YTctOGU3OTAwZmZiOTM1IgZleHRyYXMqADIkZWIyMDJiODItOWQwYi00ODQ2LThhZmUtODU1ZWViYTdkZGRkGgAiACoAMghjYXJvdXNlbDocQVRWVmFtQ29udGVudFByb3ZpZGVyU2VydmljZUIcQk9OVVNfQ0FST1VTRUxfU1RSQVRFR1lfVFlQRUoKVk9EQ29udGVudFILbm90RW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgCcgB6IGQxNjEwMmEwNTdkNGVhODkxYjIyODFmZmQ5NjNhZjI4ggEDYWxs"}, "tags": [], "seeMore": null, "type": "STANDARD_CAROUSEL"}, {"facet": {"text": null}, "title": "You might also like", "titleImageUrl": null, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiZWIyMDJiODItOWQwYi00ODQ2LThhZmUtODU1ZWViYTdkZGRkIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6ImQxNjEwMmEwNTdkNGVhODkxYjIyODFmZmQ5NjNhZjI4OjE3MTQwNjQ2NjIwMDAiLCJhcE1heCI6MTYsInN0cmlkIjoiMjpUUzY3NkE3QzUxMjRDNSMjTU5RWEUzM1ZPTlNXWSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUGY2Zjc1YmRlNmNhZTRkMWZkYzM3ZGU0ZWFmMjFjZGIwMGMxMDQwYzNiZjBmNGRjZTc2ZDM3ZjAwMDFiOWYzMjJcIn0iLCJvcmVxayI6Ik5YVkhJWU5xc1FxSi9aUDAxSEVXLzMvNTlrWm54Zkc4Nit2VkwzTHZiam89Iiwib3JlcWt2IjoxfQ==", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmY4NTY0NjJlLWYzZjAtNDdkNi05OWE3LThlNzkwMGZmYjkzNYuGZGV0YWlsjI6fMjpUUzY3NkE3QzUxMjRDNSMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "pageId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935", "pageType": "detail", "pageContext": {"pageType": "detail", "pageId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7kio6xYW16bjEuZHYuZ3RpLmY4NTY0NjJlLWYzZjAtNDdkNi05OWE3LThlNzkwMGZmYjkzNYuGZGV0YWlsjI6fMjpUUzY3NkE3QzUxMjRDNSMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "The Lord of the Rings: The Fellowship of the Ring", "gti": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "transformItemId": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "synopsis": "In the first part of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s masterpiece, The Lord of the Rings, a young hobbit, <PERSON><PERSON><PERSON>, inherits a gold ring which holds the secret to the survival of the entire world.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Fantasy", "Drama"], "maturityRatingString": "13+", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d2b9a8cf993aebfd61ff19167476475bc5de0da31022d2f3e5c810e39cb3718a.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/cf28bbd7fed8541b67dadc3b29b82440829e72a6c2213788d649078bfa104b4d.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/0eb3ef03ffc66ae2d19d66d64a406aaa226dc7aefe157c06e7e966c81102f736.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1008720000000, "runtimeSeconds": 10282, "runtime": "171 min", "overallRating": 4.8, "totalReviewCount": 19723, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_1"}, "refMarker": "dp_amz_c_TS5124c5_1_1", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "BAFTA FILM AWARDS® 3X winner", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_1"}, "refMarker": "dp_amz_c_TS5124c5_1_1", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Samaritan", "gti": "amzn1.dv.gti.10f53276-dbe0-4d41-b04e-853043f0ad5c", "transformItemId": "amzn1.dv.gti.10f53276-dbe0-4d41-b04e-853043f0ad5c", "synopsis": "Thirteen year old <PERSON> (<PERSON><PERSON><PERSON> <PERSON>) suspects that his mysteriously reclusive neighbor Mr<PERSON> <PERSON> (<PERSON>) is actually the legendary vigilante <PERSON><PERSON><PERSON>, who was reported dead 25 years ago. With crime on the rise and the city on the brink of chaos, <PERSON> makes it his mission to coax his neighbor out of hiding to save the city from ruin.", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Fantasy", "Science Fiction"], "maturityRatingString": "12", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/adbd36f6789fdf3a0d189f614cde56acd0d04bf320e122a0ba2b9fff33c36f50.jpg", "unbrandedCoverImage": "https://m.media-amazon.com/images/S/pv-target-images/325def5506e36ab80fd53ed3454533c9a039feac0bda51bf198d5298c70b9347.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/0012476751f238875c717d21c0c62c6e7aa8dd81201151e82df416e9d8bb5488.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/b2e73ccb67c492370dda8f3f928cd7692b1585fa45467709f1116ba806417b92.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/3495fe9d7bd332dc478c598a3768077db0d1001c668ae08a3af261511cb120ec.png", "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/647fa58332029248dc816dbd2cfde45720ad000a01bd070b7a4a3ae08e45b35e.jpg", "publicReleaseDate": 1661472000000, "runtimeSeconds": 6146, "runtime": "102 min", "overallRating": 3.6, "totalReviewCount": 496, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.10f53276-dbe0-4d41-b04e-853043f0ad5c", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_2"}, "refMarker": "dp_amz_c_TS5124c5_1_2", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.10f53276-dbe0-4d41-b04e-853043f0ad5c", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_2"}, "refMarker": "dp_amz_c_TS5124c5_1_2", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "titleLogoImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Hanna - Season 1", "gti": "amzn1.dv.gti.14b3d425-d8b5-4d1b-89a0-76316b9be5c4", "transformItemId": "amzn1.dv.gti.14b3d425-d8b5-4d1b-89a0-76316b9be5c4", "synopsis": "In equal parts high-concept thriller and coming-of-age drama, HAN<PERSON> follows the journey of an extraordinary young girl raised in the forest, as she evades the relentless pursuit of an off-book CIA agent and tries to unearth the truth behind who she is.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "15", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Drama", "Young Adult Audience"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/648edca28accc0eb7d1458b4eed8c6e981bb3023c78fd524591ce32c1ad4d1c2.jpg", "unbrandedCoverImage": "https://m.media-amazon.com/images/S/pv-target-images/955458eca635b41c75e60690e2de13d3dd3bf5d45e3816d07deeeba89f470fbb.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4762e5126660b374d21a31be529e5f547eb7576344d6c95f5edd4ebd4cff4c66.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/45693cc2b89c5b831e3aed4636cacae15f74a1c34ed8af31ee8d6a88da9e4b7b.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/1c0ffc445d57201739240fe8e48686579dd7cee86d37f8550280a6105c310b1f.png", "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/ea7b934a35de2aa399a2bd6116f20e3281815cba09f2e9e9385a4622206fe342.png", "publicReleaseDate": 1549238400000, "runtimeSeconds": null, "runtime": null, "overallRating": 4.4, "totalReviewCount": 694, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 3, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.14b3d425-d8b5-4d1b-89a0-76316b9be5c4", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_3"}, "refMarker": "dp_amz_c_TS5124c5_1_3", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® nominee", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.14b3d425-d8b5-4d1b-89a0-76316b9be5c4", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_3"}, "refMarker": "dp_amz_c_TS5124c5_1_3", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "<PERSON>", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "titleLogoImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "The Tomorrow War", "gti": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "transformItemId": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "synopsis": "Time travelers arrive from 2051 to deliver an urgent message: 30 years in the future mankind is losing a war against a deadly alien species. The only hope for survival is for soldiers and civilians to be transported to the future and join the fight. Determined to save the world for his daughter, <PERSON> teams up with a brilliant scientist and his estranged father to rewrite the planet’s fate.", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Suspense", "Drama", "Science Fiction"], "maturityRatingString": "12", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/06e43e32087be701c6200cfc7ef1d49958b285a0deba4ed53ab9d2e16acd88c7.jpg", "unbrandedCoverImage": "https://m.media-amazon.com/images/S/pv-target-images/38454db023729d8a20a41ac8481ee6831bc23ee97e13bcd92c6d62c202450c71.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/db3229797988f87ccafa445019d4e74b26053e7e22ded38853ce8d79e009e62c.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/d6c7528b517fd8f2b0a7359ffa3c3283695cb34546276425e648f23ab5df57c6.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/1e21cf6617a5dfe03e00712c93611d08feb9c9e6d6ef41541a550bf6a79201f0.png", "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/5290bc58bba182a8ffbc6b25f3169021fd5d5c4d37962165e8fb2d6da0de2e2d.png", "publicReleaseDate": 1625184000000, "runtimeSeconds": 8286, "runtime": "138 min", "overallRating": 3.8, "totalReviewCount": 4227, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_4"}, "refMarker": "dp_amz_c_TS5124c5_1_4", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_4"}, "refMarker": "dp_amz_c_TS5124c5_1_4", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "titleLogoImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Lord of the Rings: The Two Towers", "gti": "amzn1.dv.gti.a4b62a25-51e9-1095-7aa8-6541579aeeb4", "transformItemId": "amzn1.dv.gti.a4b62a25-51e9-1095-7aa8-6541579aeeb4", "synopsis": "In the part second of the Tolkien trilogy, <PERSON><PERSON><PERSON> and the other members of the Fellowship continue on their sacred quest to destroy the One Ring--but on separate paths.", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Fantasy", "Adventure", "Action"], "maturityRatingString": "12", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/642c79b07eff7403e096f51f40e82ce8faffa445198c1419c886cb94cfd162ec.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ae5ecf15b5ea19f28205658a775ca20b931979fe67ba69e974f0b0c049fdbd38.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/9b0539ebcd639601b897eb3695003a6b96c60b07d33d0420021482f6110469f2.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/26eea1cf1e2a98bcbb0bbd4842a90154ba050ae3f3fe30c468e0c2fe8a37b425.jpg", "publicReleaseDate": 1040169600000, "runtimeSeconds": 10760, "runtime": "179 min", "overallRating": 4.8, "totalReviewCount": 14394, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a4b62a25-51e9-1095-7aa8-6541579aeeb4", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_5"}, "refMarker": "dp_amz_c_TS5124c5_1_5", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "BAFTA FILM AWARDS® 2X winner", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a4b62a25-51e9-1095-7aa8-6541579aeeb4", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_5"}, "refMarker": "dp_amz_c_TS5124c5_1_5", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "The Wheel of Time - Season 1", "gti": "amzn1.dv.gti.fc256dec-bd7d-40af-8ca5-77e5169c2fc7", "transformItemId": "amzn1.dv.gti.fc256dec-bd7d-40af-8ca5-77e5169c2fc7", "synopsis": "The lives of five young villagers change forever when a strange and powerful woman arrives, claiming one of them is the child of an ancient prophecy with the power to tip the balance between Light and Dark forever. They must choose whether to trust this stranger – and each other – with the fate of the world before the Dark One breaks out of His prison, and the Last Battle begins.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "15", "applyHdr10": true, "applyDolbyVision": true, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Adventure", "Drama", "Fantasy"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4b0182f25032adfd88472562ce0b1dd54cdf2e28bdf4ee78ecc04b1cc78da8f4.png", "unbrandedCoverImage": "https://m.media-amazon.com/images/S/pv-target-images/dcc84a8c23e866a5bbffe7e49ce19ae86bcc724dfa46fac75de1dfeb35d78927.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/91076e2f38b6844a472af35c0fb12475787152a0d625188d5808c9965a8935f6.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/819df9a91078d18a96a8638d89859c60ba384feae9a1bcf3e324c5db9a214ce8.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/d818604622d7e30eab5093cf56c549ea02b1abf4635f198105016813bbc6c24d.png", "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/eea9eab9ec9725a17ed774cc457e7edd27514581415a2c16e8242143588a61d2.jpg", "publicReleaseDate": 1637280000000, "runtimeSeconds": null, "runtime": null, "overallRating": 3.8, "totalReviewCount": 3235, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 2, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fc256dec-bd7d-40af-8ca5-77e5169c2fc7", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_6"}, "refMarker": "dp_amz_c_TS5124c5_1_6", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.fc256dec-bd7d-40af-8ca5-77e5169c2fc7", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_6"}, "refMarker": "dp_amz_c_TS5124c5_1_6", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": "The Wheel of Time", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "titleLogoImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "The Lord of the Rings: The Return of the King", "gti": "amzn1.dv.gti.91d91081-a2e1-4004-af7d-4efb31e99e27", "transformItemId": "amzn1.dv.gti.91d91081-a2e1-4004-af7d-4efb31e99e27", "synopsis": "In the conclusion of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'s epic masterpiece, The Lord of the Rings, as armies mass for a final battle that will decide the fate of the world -- and powerful.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Fantasy", "Action", "Drama"], "maturityRatingString": "13+", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4853deaa17ff9fe73b07adb7b569852300e687ab7532fa3f4faeb39c5a358c53.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/765fff993131fb831888a7794fdabf794cd6fea260e98c31027652ec0d40eb7a.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8b7884e548efa47815118ad7b583d099ea06c5c53b7a6a74fc7e853ecf7ab692.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/9d23ed08848c0e5a2ae984108bfcaa1cc94679e54cee9fac884c5d2be24d308a.jpg", "publicReleaseDate": 1071619200000, "runtimeSeconds": 12056, "runtime": "3h 20m", "overallRating": 4.8, "totalReviewCount": 13128, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.91d91081-a2e1-4004-af7d-4efb31e99e27", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_7"}, "refMarker": "dp_amz_c_TS5124c5_1_7", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "BAFTA FILM AWARDS® 4X winner", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.91d91081-a2e1-4004-af7d-4efb31e99e27", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_7"}, "refMarker": "dp_amz_c_TS5124c5_1_7", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "No Time to Die", "gti": "amzn1.dv.gti.e1ae889a-3a6e-4835-a121-953be771a755", "transformItemId": "amzn1.dv.gti.e1ae889a-3a6e-4835-a121-953be771a755", "synopsis": "A mission to rescue a kidnapped scientist turns out to be far more treacherous than expected, leading <PERSON> (<PERSON>) onto the trail of a mysterious villain armed with dangerous new technology.", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense", "Action", "Adventure"], "maturityRatingString": "12", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7e28a9fe45236a8d09aea268b7bb753d706377f615c4127f2de80f939bc424e5.jpg", "unbrandedCoverImage": "https://m.media-amazon.com/images/S/pv-target-images/ce939fcc6802ae4e39d4f95f47f49b253913297437fe326c51a25dedd992aee2.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/a0e93cdfdd7f4ee994147bd17916cefe0b8cc0f0a697fe174cf77d94f66ac251.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/16dfe0601b4a76865378756491c122a40c86243796aa29ad767202b8f102d844.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/02ed8ff0e8fea042996f3e2951fc26c76b53b223074a9914e349d4456692bd20.png", "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/731e62217889c8df29f8a7e7cc5cb49234940326e8bf4970bf3b15ec4b8feae2.jpg", "publicReleaseDate": 1632960000000, "runtimeSeconds": 9803, "runtime": "163 min", "overallRating": 4.5, "totalReviewCount": 47703, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e1ae889a-3a6e-4835-a121-953be771a755", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_8"}, "refMarker": "dp_amz_c_TS5124c5_1_8", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "BAFTA FILM AWARD® winner", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e1ae889a-3a6e-4835-a121-953be771a755", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_8"}, "refMarker": "dp_amz_c_TS5124c5_1_8", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "titleLogoImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "The Hobbit: The Desolation of Smaug", "gti": "amzn1.dv.gti.b221beb2-14c7-4861-afd4-ba6c8396d3e0", "transformItemId": "amzn1.dv.gti.b221beb2-14c7-4861-afd4-ba6c8396d3e0", "synopsis": "In the company of thirteen dwarves and the wizard <PERSON><PERSON><PERSON> the <PERSON> (<PERSON>), <PERSON><PERSON><PERSON> enters the Lonely Mountain in possession of <PERSON><PERSON>'s \"precious\" ring and his keen blade, <PERSON>.", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Fantasy", "Adventure", "Action", "Drama"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8951229072ab7f87af290d44c4e3291e5737ab0e7df2b8e1008cad482685ff1f.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e944be968608c635bc95df9482fe5b81b252f4b61d9fe5e0555d6f043c0b4f45.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/56d17b991830851caded24d4246335fb7c5d755aa7418904625bb7d4a3d14366.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/36279f6cbd92267dfc8d23bdbebaa9892d976add236c0b7b8845056f6242e911.jpg", "publicReleaseDate": 1386892800000, "runtimeSeconds": 9672, "runtime": "161 min", "overallRating": 4.7, "totalReviewCount": 18186, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b221beb2-14c7-4861-afd4-ba6c8396d3e0", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_9"}, "refMarker": "dp_amz_c_TS5124c5_1_9", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "BAFTA FILM AWARDS® 2X nominee", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b221beb2-14c7-4861-afd4-ba6c8396d3e0", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_9"}, "refMarker": "dp_amz_c_TS5124c5_1_9", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "The Hobbit: The Battle of The Five Armies", "gti": "amzn1.dv.gti.84b57641-ee32-8a74-6daf-40d25ab16ff6", "transformItemId": "amzn1.dv.gti.84b57641-ee32-8a74-6daf-40d25ab16ff6", "synopsis": "The epic conclusion to the adventure of <PERSON><PERSON><PERSON>, who joins the <PERSON>, <PERSON><PERSON> and his company of Dwarves, in the clash of the Battle of the Five Armies.", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Fantasy", "Action"], "maturityRatingString": "12", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/db6d8c395531c2013296a99b18f41686f33d73aaa9852e5aced80f2fc42a0bd9.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/b5eb1d181e815bc25fbde0c3a1d9bba78b92408dacfc9ceb23693cc3fa0465d3.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/4b5f22ca1e61ac3554c651f904f913daf81c9387f400a423f5d732b1e76c3812.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/ea02442669cc29fa1361767ba0bdeb1858465c9182d275868783f9bcf19d0fd1.jpg", "publicReleaseDate": 1418342400000, "runtimeSeconds": 8483, "runtime": "141 min", "overallRating": 4.7, "totalReviewCount": 9560, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.84b57641-ee32-8a74-6daf-40d25ab16ff6", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_10"}, "refMarker": "dp_amz_c_TS5124c5_1_10", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Prime", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "BAFTA FILM AWARD® nominee", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.84b57641-ee32-8a74-6daf-40d25ab16ff6", "pageType": "detail", "analytics": {"refMarker": "dp_amz_c_TS5124c5_1_10"}, "refMarker": "dp_amz_c_TS5124c5_1_10", "text": null, "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": true, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "poster2x3Image": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "dp_amz_c_TS5124c5_1", "ClientSideMetrics": "476|CkMKI0NBV1JlbW90ZVN0cmF0ZWd5TGl2ZURlZmF1bHREZWZhdWx0EhAyOlRTNjc2QTdDNTEyNEM1GgAiCFRTNTEyNGM1EmsKBmRldGFpbBIxYW16bjEuZHYuZ3RpLmY4NTY0NjJlLWYzZjAtNDdkNi05OWE3LThlNzkwMGZmYjkzNSIGY2VudGVyKgAyJGViMjAyYjgyLTlkMGItNDg0Ni04YWZlLTg1NWVlYmE3ZGRkZBoDYWxsIgNhbGwqA2FsbDIIY2Fyb3VzZWw6HkFUVkN1c3RvbWVyc0Fsc29XYXRjaGVkU2VydmljZUIdYWl2LWNvbnN1bXB0aW9uLXB1cmNoYXNlLXNpbXNKClZPRENvbnRlbnRSC25vdEVudGl0bGVkWgBiEFN0YW5kYXJkQ2Fyb3VzZWxoAXIAeiBkMTYxMDJhMDU3ZDRlYTg5MWIyMjgxZmZkOTYzYWYyOIIBA2FsbA=="}, "tags": [], "journeyIngressContext": "16|CgNhbGwSA2FsbA==", "seeMore": null, "type": "STANDARD_CAROUSEL"}], "episodeListV2": {"widgetName": "episode-list-v2", "selectedEpisodeGti": "amzn1.dv.gti.eedf35c9-fa32-4c9f-8516-bfa78ab759de", "selectedSeasonGti": "amzn1.dv.gti.1a0d302f-2fc3-4db4-8219-d6b277fbceb8", "episodes": [{"gti": "amzn1.dv.gti.eedf35c9-fa32-4c9f-8516-bfa78ab759de", "sequenceNumber": 1, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 1{lineBreak}Watch now", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/2520061e6dad539f1723a71fc4c18491201eeff38f0de8a4823f5d4dcfc2dae0._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/8142b0f00113ee5855c8831da6a9b1e0a96a6d5f68a373b8d620acdc309dc8f2.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1662076800000, "runtimeSeconds": 3947, "ratingsMetadata": {"count": 30097, "rating": 3.5}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Series Premiere. <PERSON><PERSON><PERSON> is disturbed by signs of an ancient evil’s return; <PERSON><PERSON><PERSON> makes an unsettling discovery; <PERSON><PERSON><PERSON> is presented with an intriguing new venture; <PERSON><PERSON> breaks the Harfoot community’s most deeply-held rule.", "title": "A Shadow of the Past", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.ad2d34d7-a239-4105-896e-de360fdb0eba", "sequenceNumber": 2, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 2{lineBreak}Watch now", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "foul language"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/659a0a13d1add429575fd1af7e355deece6226f5c878b0410ed1c3e18155a75d._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/295daf333d1c4187089d3f73ec8488151108199663638a8060d3fc8036916485.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1662076800000, "runtimeSeconds": 4022, "ratingsMetadata": {"count": 30097, "rating": 3.5}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "<PERSON><PERSON><PERSON> finds a new ally; <PERSON><PERSON><PERSON> faces a cold reception from an old friend; <PERSON><PERSON> endeavors to help a Stranger; <PERSON><PERSON><PERSON> searches for answers while <PERSON><PERSON><PERSON> warns her people of a threat.", "title": "Adrift", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.fbb73ae3-eeb4-4d24-982a-b7a9f5f60fdf", "sequenceNumber": 3, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 3{lineBreak}Watch now", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes", "alcohol use"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_hd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/99d20d6da10f023acd170de112e3254e42745d917838d254ec98128ba3838f34._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/80363d7d48deb12118a3addb519449387293a5debab177b292306f6a4214e85a.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1662681600000, "runtimeSeconds": 4162, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "<PERSON><PERSON><PERSON> finds himself a captive; <PERSON><PERSON><PERSON> and <PERSON><PERSON> explore a legendary kingdom; <PERSON><PERSON><PERSON> is given a new assignment; <PERSON><PERSON> faces the consequences.", "title": "<PERSON><PERSON>", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.7400400d-e675-4c9f-9730-6bbe8219bb80", "sequenceNumber": 4, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 4{lineBreak}Watch now", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_uhd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/9e56f5a95bdce87f0d87d92855f035000af59e5d25d593f35e91dd792cb0ca3a._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/817e0f1581e53679c49aea75ede092a277a2c43c0610b0ff5ba525953e2afadf.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1663286400000, "runtimeSeconds": 4303, "ratingsMetadata": {"count": 30097, "rating": 3.5}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Queen Regent <PERSON><PERSON><PERSON>’s faith is tested; <PERSON><PERSON><PERSON><PERSON> finds himself at a crossroads; <PERSON><PERSON><PERSON> uncovers a secret; <PERSON><PERSON><PERSON> is given an ultimatum; <PERSON> disobeys <PERSON><PERSON><PERSON>.", "title": "The Great Wave", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.d77f9fa8-afaf-4365-be83-9387e48d9264", "sequenceNumber": 5, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 5{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_uhd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/a54eea43cb97a75e3f1d2c57bd6fe7a16559fbbd00509c5b183bf918f7d97b43._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/ebbe2a11ffd9370e2eb17621733f614bb23556aeb68572596d1873e1ea0fa5e8.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1663891200000, "runtimeSeconds": 4330, "ratingsMetadata": {"count": 2426, "rating": 3.3}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "<PERSON><PERSON> questions her instincts; <PERSON><PERSON><PERSON> struggles to stay true to his oath; <PERSON><PERSON> weighs his destiny; The Southlanders brace for attack.", "title": "Partings", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.740c4e8e-f8ff-475b-96aa-88be0b49fcbc", "sequenceNumber": 6, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 6{lineBreak}Watch now", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_uhd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/c93ab97d0530dcc82c07ad1c17277c0d5ffe57f163a90cc10c990c3c38042de6._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/98bd2d4a4cf90db7a2d6c39f324af367cd1644f621c2078a94b88d5ab6a18cd9.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1664496000000, "runtimeSeconds": 4169, "ratingsMetadata": {"count": 2426, "rating": 3.3}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "<PERSON><PERSON> and his army march on Ostirith.", "title": "Udûn", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.df6f335d-8da2-4b9a-a71c-2ed0b19f04ba", "sequenceNumber": 7, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 7{lineBreak}Watch now", "metadata": {"contentDescriptors": ["flashing lights", "violence", "frightening scenes"], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_uhd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/45c0dbd21425875325e3da87bbdcfbcab2f7e5839a38fd91f1f88428a440bc48._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/875feacc1337cc9730fb7d44cf9e5439cb520bca2478cdfd84d05691c9bb4399.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1665100800000, "runtimeSeconds": 4333, "ratingsMetadata": {"count": 2426, "rating": 3.3}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Survivors of a cataclysm try to find safety; the Harfoots confront evil; <PERSON><PERSON> is torn between friendship and duty; <PERSON><PERSON> considers a new name.", "title": "The Eye", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.13144a5d-792f-4593-922c-e9ee8f7ddb24", "sequenceNumber": 8, "seasonSequenceNumber": 1, "actions": [{"childActions": [], "label": "Episode 8{lineBreak}Watch now", "metadata": {"contentDescriptors": [], "regulatoryRating": "TV-14", "refMarker": "atv_dp_btf_el_3p_uhd_tv_play_t1ACAAAAAA0lr0", "position": "FEATURE_NOT_STARTED", "userPlaybackMetadata": {"timecodeSeconds": 0, "hasStreamed": false, "isLinear": false}, "userEntitlementMetadata": {"entitlementType": "THIRD_PARTY_SUBSCRIPTION", "benefitType": ["FREE_WITH_ADS"]}, "videoMaterialType": "Feature", "isTrailer": false, "metadataActionType": "Playback"}, "presentation": "Simple"}], "titleDetails": {"audios": [{"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/44bd0cc5a87ed65763c48d1c1c396ea5a6eee602ff3bf82679be055298830332._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/d1f7cd5c9d12b41f3afcae8470c9f39c9ddfa51c96c9704508b859cd0e72e1c0.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1665705600000, "runtimeSeconds": 4346, "ratingsMetadata": {"count": 30097, "rating": 3.5}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fil_PH", "name": "Filipino"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "ru_RU", "name": "Русский"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "uk_UA", "name": "Українська"}, {"locale": "vi_VN", "name": "Tiếng <PERSON>"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Season One Finale. New alliances are forged.", "title": "Alloyed", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}}}, {"gti": "amzn1.dv.gti.14cce958-3188-4746-8cc8-23800e2e9480", "sequenceNumber": 1, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/94d2496f86b1402f9d56a376b496049ffe78d73e18aa3d80c0929a0815a5e8f6._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/3ed5bab07aa460261352a8a4a3f445c65570774785842349a9da411070abb8fb.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1724889600000, "runtimeSeconds": 4566, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Season Premiere. Sauron bargains with <PERSON><PERSON>. The Stranger and <PERSON><PERSON> venture into new lands. The Three Elven Rings face judgment.", "title": "Elven Kings Under the Sky", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}, {"gti": "amzn1.dv.gti.40d0e196-7806-4657-bd23-293db80d96b1", "sequenceNumber": 2, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/9246c9bb40f17ad609bcf615f5adf286190c1b1cea828850c0d48331c369ca84._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/bda0566ba77d11ffa68547bf48a5bdf3325d929d9fb87659db2465ea30809e48.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1724889600000, "runtimeSeconds": 3781, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Darkness falls over Khazad-dûm. <PERSON><PERSON> and <PERSON><PERSON><PERSON> each seek new allies. The <PERSON> and <PERSON><PERSON><PERSON><PERSON> encounter a growing threat.", "title": "Where the Stars are Strange", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}, {"gti": "amzn1.dv.gti.9485455f-4637-42a1-9e7e-af4f7f68bacf", "sequenceNumber": 3, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/cdd7ee3f6339ccb10f0239674d23e76614d2f64aa73b4bf3060571eb7efd51a2._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/1532b51a12ef930d649faf4af3a25d375f9f5542d0b924479e38b71ab1d1ffc2.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1724889600000, "runtimeSeconds": 4005, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "<PERSON><PERSON><PERSON><PERSON> and an old friend reunite. <PERSON><PERSON><PERSON> grapples with change. <PERSON><PERSON><PERSON> faces rising opposition. Annatar counsels Ce<PERSON>brimbor.", "title": "The Eagle and the Sceptre", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}, {"gti": "amzn1.dv.gti.e7cc90d7-0cd9-4bee-80d7-a52448ff6c5f", "sequenceNumber": 4, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/ee107212f765b57fa43e09de5144c03b652e033af016debe757f3816f4bccd3c._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/70190368794e862009d2656f7dd7648b45ec0f97b387f1c25f9b10c6f1836f67.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1725494400000, "runtimeSeconds": 3914, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "The Stranger finds what he’s been searching for. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> search for <PERSON><PERSON> <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> walk into a trap.", "title": "Eldest", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}, {"gti": "amzn1.dv.gti.211d8ed2-0945-4ead-b8b4-fd6af526821b", "sequenceNumber": 5, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/fd9828193c8406d56d033a2d488e373e4a4df1508563fd8a4cd443f17c97547b._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/f42cbe790ebb27a799ae064e51a1102c571025b20f5bc029883e0735e11692b0.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1726099200000, "runtimeSeconds": 3666, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "When <PERSON><PERSON> grows suspicious of the Dwarven Rings, <PERSON><PERSON><PERSON><PERSON><PERSON> must reassess his priorities. <PERSON><PERSON><PERSON>’s shifting currents, <PERSON><PERSON><PERSON> searches for hope.", "title": "Halls of Stone", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}, {"gti": "amzn1.dv.gti.1d681469-ff06-4994-90e5-fd70fa8a486f", "sequenceNumber": 6, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/30c65a7c38fef8c444cbc438e2987a477fa693fc4d42fba97b44b2d127f5ab88._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/76155b13227fc492c0e97dd55b3a4b28fa35cdefdaf446bf075aa138b070abbb.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1726704000000, "runtimeSeconds": 3786, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "<PERSON><PERSON><PERSON> considers a proposition. <PERSON><PERSON><PERSON> faces judgment. The Stranger finds himself at a crossroads. <PERSON><PERSON>’s plans bear fruit.", "title": "Where Is He?", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}, {"gti": "amzn1.dv.gti.dabdd66a-a109-40b6-8d64-20a37f145e79", "sequenceNumber": 7, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/d5eb86071d924c324fc14d32b6be68bb6b22951e8dcd415bf4ada63d3d8de696._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/31bc5a8716e456f9c9a4d0bf419b141bd6a19687bb1ff454ea0fef9c777c06a3.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1727308800000, "runtimeSeconds": 4336, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Eregion’s fate is decided.", "title": "Doomed to Die", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}, {"gti": "amzn1.dv.gti.ef9328f7-3d33-42bb-b51a-16ffe18059c7", "sequenceNumber": 8, "seasonSequenceNumber": 2, "actions": [], "titleDetails": {"audios": [{"locale": "ca_ES", "name": "Català"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "ar_001", "name": "العربية"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "ja_<PERSON>", "name": "日本語"}], "entityType": "TV Show", "images": {"cover": "https://m.media-amazon.com/images/S/pv-target-images/620771e7a11f9a1a3a5781115d471e13d188d53841e53930c793f6efec5b330f._UR1920,1080_RI_.jpg", "boxart": "https://m.media-amazon.com/images/S/pv-target-images/7482fdc8d7ab295fa7f969c9a3b0343c4829203399d78b46c9cf7ac315159266.jpg"}, "isInWatchlist": false, "regulatoryRating": "TV-14", "releaseDate": 1727913600000, "runtimeSeconds": 4431, "ratingsMetadata": {"count": 1615, "rating": 3.8}, "subtitles": [{"locale": "ar_001", "name": "العربية"}, {"locale": "ca_ES", "name": "Català"}, {"locale": "cs_CZ", "name": "Čeština"}, {"locale": "da_DK", "name": "Dansk"}, {"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "el_GR", "name": "Ελληνικά"}, {"locale": "en_US", "name": "English [CC]"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "eu_ES", "name": "Euskara"}, {"locale": "fi_FI", "name": "<PERSON><PERSON>"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "gl_ES", "name": "Galego"}, {"locale": "he_IL", "name": "עברית"}, {"locale": "hi_IN", "name": "हिन्दी"}, {"locale": "hu_HU", "name": "<PERSON><PERSON><PERSON>"}, {"locale": "id_ID", "name": "Indonesia"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "kn_IN", "name": "ಕನ್ನಡ"}, {"locale": "ko_KR", "name": "한국어"}, {"locale": "ml_IN", "name": "മലയാളം"}, {"locale": "ms_MY", "name": "Bahasa Melayu"}, {"locale": "nb_NO", "name": "Norsk Bokmål"}, {"locale": "nl_NL", "name": "Nederlands"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)"}, {"locale": "pt_PT", "name": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)"}, {"locale": "ro_RO", "name": "Română"}, {"locale": "sv_SE", "name": "Svenska"}, {"locale": "ta_IN", "name": "தமிழ்"}, {"locale": "te_IN", "name": "తెలుగు"}, {"locale": "th_TH", "name": "ไทย"}, {"locale": "tr_TR", "name": "Türkçe"}, {"locale": "zh_HANS", "name": "中文（简体）"}, {"locale": "zh_HANT", "name": "中文（繁體）"}], "synopsis": "Season Finale. The free peoples of Middle-earth struggle against the forces of darkness.", "title": "Shadow and Flame", "titleType": "EPISODE", "applyCC": true, "applyUhd": true, "applyDolby": false, "applyPrime": true, "applyHdr": true, "applyDolbyVision": false, "applyAudioDescription": true, "seasonId": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae"}, "traits": [], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Join <PERSON>", "icon": "OFFER_ICON"}, "GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}}], "hasPrerelease": false}, "seasonSelectorV2": {"widgetName": "season-selector-v2", "seasons": [{"gti": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935", "seasonNumber": 1, "asin": "B09QH97PTF", "title": "Season 1"}, {"gti": "amzn1.dv.gti.ffefa84e-6092-433b-9f8a-79c6dd7551ae", "seasonNumber": 2, "asin": "B0CXGPBXBN", "title": "Season 2"}], "selectedSeason": "amzn1.dv.gti.f856462e-f3f0-47d6-99a7-8e7900ffb935", "seriesId": "amzn1.dv.gti.1a0d302f-2fc3-4db4-8219-d6b277fbceb8", "defaultDisplayString": "Season 1"}}, "metadata": {"requestId": "d16102a057d4ea891b2281ffd963af28", "requestedTransformId": "lr/detailsPage/detailsPageBTF", "domain": "prod", "realm": "eu-west-1", "timestamp": "2024-04-25T17:04:22.884795Z"}}