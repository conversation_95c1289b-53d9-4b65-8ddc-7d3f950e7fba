{"resource": {"actions": [{"childActions": [{"childActions": [], "label": "Thanks for pre-ordering", "metadata": {"metadataActionType": "PreorderSuppressionSection"}, "presentation": "ModalSectionHeader"}, {"childActions": [], "label": "Release Date: Coming soon{lineBreak}Payment: Scheduled on the release date.{lineBreak}{lineBreak}Pre-order price guarantee ensures you pay the lowest prime video price between placing the order and the release date.{lineBreak}{lineBreak}We'll send you an email as soon as it's available.{lineBreak}Cancel anytime on the website before its release date.", "metadata": {"metadataActionType": "PreorderSuppressionSection"}, "presentation": "ModalSectionBody"}, {"childActions": [], "label": "This should be a separate paragraph! (NOT REAL DATA)", "metadata": {"metadataActionType": "PreorderSuppressionSection"}, "presentation": "ModalSectionBody"}, {"childActions": [], "label": "OK", "metadata": {"metadataActionType": "PreorderSuppressionSection"}, "presentation": "ModalSectionButton"}], "label": "Order Details", "metadata": {"metadataActionType": "PreorderSuppression"}, "presentation": "Modal"}], "audios": [{"locale": "de_DE", "name": "De<PERSON>ch"}, {"locale": "it_IT", "name": "Italiano"}, {"locale": "en_US", "name": "English Dialogue Boost: Medium"}, {"locale": "es_419", "name": "Español (Latinoamérica)"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France)"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada)"}, {"locale": "de_DE", "name": "Deutsch [Audiobeschreibung]"}, {"locale": "pl_PL", "name": "<PERSON><PERSON>"}, {"locale": "pt_BR", "name": "Português"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España)"}, {"locale": "en_US", "name": "English [Audio Description]"}, {"locale": "en_US", "name": "English"}, {"locale": "en_US", "name": "English Dialogue Boost: High"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "ca_ES", "name": "Català"}], "contentDescriptors": ["violence", "foul language", "sexual content"], "directors": ["<PERSON><PERSON>"], "entitlementMessage": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "entityType": "Movie", "genres": ["Comedy", "Fantasy", "Adventure"], "gti": "amzn1.dv.gti.17f2bd91-afd8-4ae8-8dbc-9c49066e771c", "hasTrailer": true, "highValueMessage": {"message": "GOLDEN GLOBES® 2X winner", "icon": null, "level": null, "type": null}, "informationalMessage": {"message": "Rentals include 30 days to start watching this video and 48 hours to finish once started.", "icon": null, "level": null, "type": null}, "images": {"hero": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/9385606578990c4b41c20c44dc904676d2bb763411fd88cf761371dee5f78ee9._RI_TTW_.jpg", "title": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/0f4ae6ff2569e9a1a70cc1d35de5cf6f78946b245f412d05d9682d6223fba902._UY500_UX375_RI_TTW_.jpg", "title16x9": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/33e33fd772cd7f1ff56d0e03772d5abb9c46649748286cd3d333814cd97aea1e._UY500_UX667_RI_TTW_.jpg", "titleLogo": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/5a3ed57321c275187389be833b120c81aee9e7ba69f0659eeb0632977de4e40d._RI_TTW_.png", "cover": "https://ecx.images-amazon.com/images/S/pv-target-images/33e33fd772cd7f1ff56d0e03772d5abb9c46649748286cd3d333814cd97aea1e._UY500_UX667_RI_TTW_.jpg"}, "imdbRating": 6.9, "imdbTconst": "tt1517268", "impressionRefMarker": "atv_dp_atf_mv_t1AAAAAAAA0lr0", "isInWatchlist": false, "maturityRating": {"regulatoryRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png", "dimension": {"width": 80, "height": 80}}, "regulatoryRatingString": "12"}, "ratingsMetadata": {"count": 6173, "rating": 3.8}, "regulatoryRating": "12", "releaseDate": 1689897600000, "runtimeSeconds": 6841, "starringCast": ["<PERSON><PERSON>", "<PERSON>", "America Ferrera"], "studios": ["Warner Bros. Entertainment Inc."], "subtitles": [{"locale": "sv_SE", "name": "<PERSON><PERSON> [CC]"}, {"locale": "es_ES", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (España) [CC]"}, {"locale": "ja_<PERSON>", "name": "日本語"}, {"locale": "pt_BR", "name": "Português [SDH]"}, {"locale": "pl_PL", "name": "<PERSON><PERSON> [CC]"}, {"locale": "es_419", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Latinoamérica) [CC]"}, {"locale": "fr_FR", "name": "<PERSON><PERSON><PERSON> (France) [CC]"}, {"locale": "de_DE", "name": "Deutsch [UT]"}, {"locale": "nl_NL", "name": "Nederlands [CC]"}, {"locale": "fr_CA", "name": "<PERSON><PERSON><PERSON> (Canada) [CC]"}, {"locale": "it_IT", "name": "<PERSON>o [CC]"}, {"locale": "en_US", "name": "English [CC]"}], "supportingCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "synopsis": "To live in Barbie Land is to be a perfect being in a perfect place. Unless you have a full‐on existential crisis. Or you're a Ken.", "title": "Barbie", "titleType": "MOVIE", "applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyDolbyVision": false, "applyDolbyAtmos": false, "applyHdr": true, "applyPrime": false, "applyUhd": true, "moods": [], "starlight": {"explorePanelURL": null, "refMarker": "atv_dp_exp_unk771c_expbutton", "isRecommendedDefaultTab": true, "treatmentPreviewImageUrl": null, "headlineText": "Dive deeper into the world"}, "widgetSections": {"linearAirings": {"widgets": []}, "standardContent": {"widgets": []}, "aboveTheFold": {"widgets": [{"widgetName": "WatchTrailer", "preferredTrailerGTI": "amzn1.dv.gti.17f2bd91-afd8-4ae8-8dbc-9c49066e771c", "videoMaterialType": "TRAILER"}]}}, "userReaction": {"shouldShowReactions": true, "reaction": null}, "isEventHidden": false, "isTravelingCustomer": false}, "metadata": {"requestId": "811d7ac2ed3b1e3f74f2aff411a0fce8", "requestedTransformId": "lr/detailsPage/detailsPageATF", "domain": "prod", "realm": "eu-west-1", "timestamp": "2024-02-16T15:27:46.315032Z"}}