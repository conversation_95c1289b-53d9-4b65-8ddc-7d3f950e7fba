use proc_macro2::TokenStream;
use quote::quote;
use syn::parse_macro_input;

#[proc_macro_derive(Playable)]
pub fn playable_derive(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let ast = parse_macro_input!(input);
    proc_macro::TokenStream::from(impl_playable_macro(&ast))
}

#[proc_macro_derive(Transitionable)]
pub fn transitionable_background_derive(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let ast = parse_macro_input!(input);
    proc_macro::TokenStream::from(impl_transitionable_macro(&ast))
}

#[proc_macro_derive(Resizable)]
pub fn resizable_background_derive(input: proc_macro::TokenStream) -> proc_macro::TokenStream {
    let ast = parse_macro_input!(input);
    proc_macro::TokenStream::from(impl_resizable_macro(&ast))
}

fn impl_playable_macro(ast: &syn::DeriveInput) -> TokenStream {
    let name = &ast.ident;
    let gen = quote! {
        impl Playable for #name {
            fn get_video_id(&self) -> &Option<String> {
                &self.video_id
            }

            fn get_media_strategy(&self) -> &MediaStrategy {
                &self.media_strategy
            }

            fn get_csm_data(&self) -> &Option<String> {
                &self.csm_data
            }

            fn get_placement(&self) -> &String {
                &self.placement
            }
        }
    };
    gen
}

fn impl_transitionable_macro(ast: &syn::ItemStruct) -> TokenStream {
    let name = &ast.ident;
    let gen = quote! {
        impl Transitionable<#name> for #name {
            fn should_transition(&self, other: &#name) -> bool {
                 &self.id != &other.id
             }

             fn should_enter_immediately(&self) -> bool {
                 self.enter_immediately
             }
        }
    };
    gen
}

fn impl_resizable_macro(ast: &syn::ItemStruct) -> TokenStream {
    let name = &ast.ident;
    let gen = quote! {
        impl Resizable for #name {
             fn is_resizable(&self) -> bool {
                 false
             }
             fn is_resized(&self) -> bool {
                 false
             }
         }
    };
    gen
}

#[cfg(test)]
mod test {
    use crate::{impl_playable_macro, impl_resizable_macro, impl_transitionable_macro};
    use quote::quote;
    use syn::parse_quote;

    #[test]
    fn derive_transitionable() {
        let derived_struct = parse_quote! {
            struct MockStruct {
                id: String
            }
        };

        let final_code = impl_transitionable_macro(&derived_struct);

        let expected_fn = quote! {
            impl Transitionable<MockStruct> for MockStruct {
                fn should_transition(&self, other: &MockStruct) -> bool {
                    &self.id != &other.id
                }

                fn should_enter_immediately(&self) -> bool {
                    self.enter_immediately
                }
            }
        };

        assert_eq!(final_code.to_string(), expected_fn.to_string());
    }

    #[test]
    fn derive_playable() {
        let derived_struct = parse_quote! {
            struct MockStruct {
                video_id: String
            }
        };

        let final_code = impl_playable_macro(&derived_struct);

        let expected_fn = quote! {
            impl Playable for MockStruct {
                fn get_video_id(&self) -> &Option<String> {
                    &self.video_id
                }

                fn get_media_strategy(&self) -> &MediaStrategy {
                    &self.media_strategy
                }

                fn get_csm_data(&self) -> &Option<String> {
                    &self.csm_data
                }

                fn get_placement(&self) -> &String {
                    &self.placement
                }
            }
        };

        assert_eq!(final_code.to_string(), expected_fn.to_string());
    }

    #[test]
    fn derive_resizable() {
        let derived_struct = parse_quote! {
            struct MockStruct {
                video_id: String
            }
        };

        let final_code = impl_resizable_macro(&derived_struct);

        let expected_fn = quote! {
            impl Resizable for MockStruct {
                 fn is_resizable(&self) -> bool {
                     false
                 }
                 fn is_resized(&self) -> bool {
                     false
                 }
            }
        };

        assert_eq!(final_code.to_string(), expected_fn.to_string());
    }
}
