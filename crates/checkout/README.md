### Description

Example taken from https://rust.the-docs.prime-video.amazon.dev/compositron/chapter-01/03-reactivity-example.html

Run it by calling `./build-tools/run hello-world --example hello_world`

### Tests
Package presents two approaches to tests:
 - defining tests in the same file as component
 - defining tests in separate file

The latter approach may be helpful in cases where number of tests 
leads to component file being bloated and hard to navigate.

It's recommended to take single approach for single component, eg. if we prefer local tests, let's not create separate file with tests etc.
If we choose first approach, tests module should be called `tests`. Here we had to call it `local_tests` because there can't be two with the same name.

Running tests:
  - all tests within package: `cargo test --package hello-world` or `cd crates/hello-world && cargo test`
  - single test: `cargo test updates_label_on_button_press --package hello-world`