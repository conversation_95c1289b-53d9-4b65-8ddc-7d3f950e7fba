[package]
name = "checkout"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-fable-tokens.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
app-config.workspace = true
auth.workspace = true
cfg-test-attr-derive.workspace = true
checkout-navigation.workspace = true
cross-app-events.workspace = true
fableous.workspace = true
location.workspace = true
log.workspace = true
lrc-image.workspace = true
media-background.workspace = true
mockall.workspace = true
mockall_double.workspace = true
navigation-menu.workspace = true
network-parser-derive.workspace = true
network-parser.workspace = true
network.workspace = true
router.workspace = true
rust-features.workspace = true
serde.workspace = true
serde_json.workspace = true
serial_test.workspace = true
thiserror.workspace = true
uuid.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
synchronized-state-store = { workspace = true, features = ["test-utils"] }
mockall.workspace = true
rstest.workspace = true

log.workspace = true
[[example]]
name = "bolt_confirmation_page"
crate-type = ["cdylib"]
path = "examples/bolt_confirmation_page.rs"

[[example]]
name = "bolt_confirmation_page_v2_single_monthly"
crate-type = ["cdylib"]
path = "examples/bolt_confirmation_page_v2_single_monthly.rs"

[[example]]
name = "bolt_confirmation_page_v2_monthly_and_annual"
crate-type = ["cdylib"]
path = "examples/bolt_confirmation_page_v2_monthly_and_annual.rs"
