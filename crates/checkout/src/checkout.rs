use core::result::Result::Err;
use std::rc::Rc;

use crate::error::handle_checkout_error;
use crate::metrics::*;
#[cfg(test)]
use crate::network::checkout_network::MockNetworkClient as NetworkClient;
use crate::utils::cleanup_utils::cleanup_checkout;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::compose_option;
use ignx_compositron::input::KeyCode;
use media_background::types::TrailerPlaybackContext;
use navigation_menu::context::nav_context::NavControl;
#[cfg(not(test))]
use network::NetworkClient;

use ignx_compositron::{compose, prelude::*, Composer};
use router::hooks::use_back_navigation;

use crate::checkout_controller::create_checkout_controller;

use crate::ui::ui_renderer::*;

// GRCOV_BEGIN_COVERAGE

#[Composer]
#[allow(clippy::unwrap_used)]
pub fn Checkout(ctx: &AppContext, network_client: NetworkClient) -> impl VisualComposable<'static> {
    provide_checkout_metrics_context(ctx.scope());
    put_metric_dimension(ctx.scope(), "clientId", "Blast");
    put_metric_dimension(ctx.scope(), "activeLayer", "Wasm");

    let checkout_controller = create_checkout_controller(ctx, network_client);

    put_metric(ctx.scope(), "Checkout.Start", 1.0, vec![]);

    compose! {
        Stack() {
            Memo(item_builder: Box::new(move |ctx| {
                match &checkout_controller {
                    Ok(checkout_controller) => {
                        let scope = ctx.scope();
                        let back_navigation = use_back_navigation(scope, "RUST_CHECKOUT_BACK");

                        let on_back_press = Rc::new(move || {
                            cleanup_checkout(scope);
                            back_navigation();
                        });

                        if let Some(playback_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                            playback_context.suppress_trailer_playback.set(true);
                        }

                        compose_option! {
                            Stack() {
                                Column() {
                                    CheckoutUI(checkout_controller: checkout_controller.clone())
                                }
                                .main_axis_alignment(MainAxisAlignment::Center)
                                .cross_axis_alignment(CrossAxisAlignment::Center)
                                .width(SCREEN_WIDTH)
                                .height(SCREEN_HEIGHT)
                            }
                            .on_focus_move({
                                // when entering checkout, hide the top and utility navs
                                let scope = ctx.scope();
                                move |_| {
                                    if let Some(nav_control) = use_context::<NavControl>(scope) {
                                        nav_control.show_utility_nav.set(false);
                                        nav_control.show_top_nav_intent.set(false);
                                    }
                                }
                            })
                            .on_key_down(KeyCode::Backspace, {
                                let on_back_press = on_back_press.clone();
                                move || {
                                    on_back_press()
                                }
                            })
                            .on_key_down(KeyCode::Escape, move || {
                                on_back_press()
                            })

                        }

                    }
                    Err(e) => {
                        handle_checkout_error(ctx.scope(), e);
                        None
                    }
                }
            }))
        }
    }
}

#[cfg(test)]
mod tests {
    use std::time::Duration;

    use cross_app_events::create_serde_map;
    use ignx_compositron::{
        app::launch_test,
        compose,
        test_utils::{assert_node_exists, node_properties::SceneNodeTree, TestRendererGameLoop},
        time::MockClock,
    };
    use location::{Location, PageType};
    use mockall::predicate::eq;
    use navigation_menu::context::nav_context::TopNavMode;
    use router::{rust_location, MockRouting, RoutingContext};
    use rstest::rstest;
    use serial_test::serial;

    use crate::{
        metrics::MockMetricEmitter,
        network::{checkout_network::MockNetworkClient, test_utils::create_dummy_cos_v2_response},
        ui::widgets::bolt_confirmation_page::BOLT_CONFIRMATION_PAGE_TEST_ID,
    };

    #[derive(Clone)]
    struct ShowSignal {
        signal: RwSignal<bool>,
    }

    use super::*;

    fn run_animation(test_game_loop: &mut TestRendererGameLoop) -> SceneNodeTree {
        MockClock::advance(Duration::from_millis(2000));
        let _ = test_game_loop.tick_until_done();
        MockClock::advance(Duration::from_millis(2000));
        test_game_loop.tick_until_done()
    }

    #[test]
    #[serial]
    fn it_instantiates_controller_then_calls_cos_then_displays_a_widget() {
        let mut mock_network_client = MockNetworkClient::default();
        let mut mock_routing = MockRouting::new();

        launch_test(
            |ctx| {
                mock_network_client
                    .expect_workflow_v2()
                    .times(1)
                    .return_once(move |_request, sb, _| {
                        sb(create_dummy_cos_v2_response());
                    });

                let (location_signal, _) = create_signal(
                    ctx.scope(),
                    rust_location!(RUST_CHECKOUT, {"benefitId" => "test benefit id", "ingressPoint" => "ingressPoint", "checkout_request_type" => "Channels" }),
                );

                mock_routing
                    .expect_location()
                    .returning(move || location_signal.into());

                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));

                let emit_context = MockMetricEmitter::emit_context();
                expect_happy_path_metrics(&emit_context);

                compose! {
                    Checkout(network_client: mock_network_client);
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let bolt_confirmation_page = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID);

                assert_node_exists!(&bolt_confirmation_page);
            },
        )
    }

    fn expect_happy_path_metrics(emit_context: &__mock_MockMetricEmitter::__emit::Context) {
        expect_metric(
            &emit_context,
            "Checkout.Start",
            1.0,
            vec![
                ("clientId", "Blast"),
                ("activeLayer", "Wasm"),
                ("workflowType", "3ps"),
            ],
        );
        expect_metric(
            &emit_context,
            "Checkout.APILatency",
            0.0,
            vec![
                ("clientId", "Blast"),
                ("activeLayer", "Wasm"),
                ("workflowType", "3ps"),
                ("api", "COS::WorkflowV2"),
            ],
        );
    }

    #[test]
    #[serial]
    fn it_navigates_to_js_error_page_when_benefit_id_is_missing() {
        let mut mock_network_client = MockNetworkClient::default();
        let mut mock_routing = MockRouting::new();

        launch_test(
            |ctx| {
                mock_network_client.expect_workflow_v2().times(0);

                let (location_signal, _) =
                    create_signal(ctx.scope(), rust_location!(RUST_CHECKOUT, {}));

                mock_routing
                    .expect_location()
                    .returning(move || location_signal.into());

                mock_routing
                    .expect_navigate()
                    .once()
                    .with(
                        eq(Location {
                            pageType: PageType::Js(location::JSPage::ERROR_PAGE),
                            pageParams: create_serde_map([(
                                "redirectButtonsEnabled",
                                serde_json::Value::Bool(true),
                            )]),
                        }),
                        eq("RUST_CHECKOUT"),
                    )
                    .return_const(());

                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));

                let emit_context = MockMetricEmitter::emit_context();
                expect_metric(
                    &emit_context,
                    "Checkout.Start",
                    1.0,
                    vec![("clientId", "Blast"), ("activeLayer", "Wasm")],
                );
                expect_metric(
                    &emit_context,
                    "Checkout.Error",
                    1.0,
                    vec![
                        ("clientId", "Blast"),
                        ("activeLayer", "Wasm"),
                        ("errorType", "DeserializationError"),
                    ],
                );

                compose! {
                    Checkout(network_client: mock_network_client);
                }
            },
            move |_scope, mut test_loop| {
                let _ = test_loop.tick_until_idle();
            },
        )
    }

    #[test]
    #[serial]
    fn it_suppresses_trailer_playback_when_loaded() {
        let mut mock_network_client = MockNetworkClient::default();
        let mut mock_routing = MockRouting::new();

        launch_test(
            |ctx| {
                mock_network_client
                    .expect_workflow_v2()
                    .times(1)
                    .return_once(move |_request, sb, _| {
                        sb(create_dummy_cos_v2_response());
                    });

                let (location_signal, _) = create_signal(
                    ctx.scope(),
                    rust_location!(RUST_CHECKOUT, {"benefitId" => "test benefit id", "ingressPoint" => "ingressPoint", "checkout_request_type" => "Channels" }),
                );

                mock_routing
                    .expect_location()
                    .returning(move || location_signal.into());

                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));

                provide_trailer_playback_context(ctx.scope());

                let emit_context = MockMetricEmitter::emit_context();
                expect_happy_path_metrics(&emit_context);

                compose! {
                    Checkout(network_client: mock_network_client);
                }
            },
            move |scope, mut test_loop| {
                let _ = test_loop.tick_until_idle();

                let trailer_playback_context = use_context::<TrailerPlaybackContext>(scope)
                    .expect("should have TrailerPlaybackContext");
                assert_eq!(
                    trailer_playback_context.suppress_trailer_playback.get(),
                    true
                );
            },
        )
    }

    fn provide_trailer_playback_context(scope: Scope) {
        let trailer_playback_context = TrailerPlaybackContext {
            on_playback_finished: create_rw_signal(scope, false),
            on_trailer_playback_start: create_rw_signal(scope, false),
            should_play_hero: create_rw_signal(scope, false),
            suppress_trailer_playback: create_rw_signal(scope, false),
        };
        provide_context(scope, trailer_playback_context);
    }

    #[rstest]
    #[serial]
    fn exits_and_resets_nav_state_and_trailer_playback_on_back_button_press(
        #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
    ) {
        use ignx_compositron::input::KeyEventType;
        use mockall::predicate::eq;
        let mut mock_network_client = MockNetworkClient::default();
        let mut mock_routing = MockRouting::new();

        launch_test(
            |ctx| {
                mock_network_client
                    .expect_workflow_v2()
                    .times(1)
                    .return_once(move |_request, sb, _| {
                        sb(create_dummy_cos_v2_response());
                    });

                let (location_signal, _) = create_signal(
                    ctx.scope(),
                    rust_location!(RUST_CHECKOUT, {"benefitId" => "test benefit id", "ingressPoint" => "ingressPoint", "checkout_request_type" => "Channels" }),
                );

                mock_routing
                    .expect_location()
                    .returning(move || location_signal.into());

                mock_routing
                    .expect_back()
                    .with(eq("RUST_CHECKOUT_BACK"))
                    .return_const(())
                    .once();

                let emit_context = MockMetricEmitter::emit_context();
                expect_happy_path_metrics(&emit_context);

                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));

                let show_signal = ShowSignal {
                    signal: create_rw_signal(ctx.scope(), true),
                };
                provide_context(ctx.scope(), show_signal.clone());

                let nav_control = NavControl {
                    disable_top_nav_focus_trap: create_rw_signal(ctx.scope(), false),
                    disable_utility_nav_focus_trap: create_rw_signal(ctx.scope(), false),
                    show_utility_nav: create_rw_signal(ctx.scope(), true),
                    show_top_nav_intent: create_rw_signal(ctx.scope(), true),
                    set_report_top_nav_visibility: create_signal(ctx.scope(), None).1,
                    focused_nav: create_focus_value_signal(ctx.scope()),
                    top_nav_mode: create_signal(ctx.scope(), TopNavMode::TopNav).0,
                    enable_focus: create_rw_signal(ctx.scope(), true),
                };
                provide_context(ctx.scope(), nav_control);
                provide_trailer_playback_context(ctx.scope());

                compose! {
                    Checkout(network_client: mock_network_client);
                }
            },
            move |scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let nav_control = use_context::<NavControl>(scope).expect("should have NavControl");
                assert_eq!(nav_control.show_utility_nav.get(), false);

                let show_signal = use_context::<ShowSignal>(scope).expect("shold have ShowSignal");
                show_signal.signal.set(false);

                let bolt_confirmation_page = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID);

                assert_node_exists!(&bolt_confirmation_page);

                run_animation(&mut test_loop);

                let _ = test_loop.tick_until_idle();

                test_loop.send_key_press_event(KeyEventType::ButtonDown, button);

                let _ = test_loop.tick_until_idle();

                assert_eq!(nav_control.show_utility_nav.get(), true);
                let trailer_playback_context = use_context::<TrailerPlaybackContext>(scope)
                    .expect("should have TrailerPlaybackContext");
                assert_eq!(
                    trailer_playback_context.suppress_trailer_playback.get(),
                    false
                );
            },
        )
    }
}
