#[cfg(test)]
use crate::network::checkout_network::MockNetworkClient as NetworkClient;
use crate::{
    app_context::{restore_checkout_context, stash_current_checkout_context},
    metrics::*,
    network::redirect::STASHED_CHECKOUT_CONTEXT_IDENTIFIER,
    utils::view_data_utils::{gate_view_data, validate_view_data},
};
use cfg_test_attr_derive::derive_test_only;
use checkout_navigation::{
    start_checkout::StartCheckoutRequest,
    workflow_type_identifier::{get_checkout_workflow_type, AcquisitionWorkflowType},
};
use ignx_compositron::{prelude::*, reactive::*};
#[cfg(not(test))]
use network::NetworkClient;
use network::RequestError;
use network_parser::core::network_parse_from_str;
use router::hooks::use_location;
use serde::Serialize;
use serde_json::Error;
use std::rc::Rc;
use uuid::Uuid;

use crate::{
    error::{handle_checkout_error, CheckoutError},
    network::{
        checkout_network::{COSWorkflowV2Request, CheckoutRequests},
        redirect::redirect_to_js,
        types::{
            checkout_client_data::{
                cos_client_data::{
                    CosClientData, FlowAgnosticClientData, FlowClientData, FlowSpecificClientData,
                    CHECKOUT_RUST_BOLT_CAPABILITY,
                },
                svod_client_data::channel_client_data::ChannelClientData,
            },
            checkout_view_data::view_data::ViewData,
            workflow_v2_raw_response::{
                StepOption, TaskDataType, WorkflowV2RawResponse, WorkflowV2ServiceData,
            },
            workflow_v2_response::{
                convert_workflow_v2_raw_response_to_workflow_v2_response, WorkflowV2Response,
            },
        },
    },
    ui::ui_renderer::DisplayableWidget,
    utils::{
        step_option_utils::get_step_option_from_stage,
        workflow_stages_utils::get_optional_workflow_service_data_from_stages,
    },
};

// GRCOV_BEGIN_COVERAGE

const API_DIMENSION: &str = "api";
const COS_WORKFLOW_V2_API_NAME: &str = "COS::WorkflowV2";

#[derive(Clone, Serialize, Debug)]
#[derive_test_only(PartialEq)]
pub struct WorkflowStage {
    pub workflow_data: WorkflowV2ServiceData,
    pub options: Vec<StepOption>,
}

#[allow(dead_code)]
pub struct WorkflowV2ResponseTaskData {
    pub view_data: ViewData,
    pub template_data: TemplateData,
    pub template_id: String,
}

pub struct TemplateData {}

pub struct CheckoutController {
    scope: Scope,
    pub(crate) client_data: StoredValue<CosClientData>,
    pub(crate) workflow_stages: StoredValue<Vec<WorkflowStage>>,
    network_client: NetworkClient,
    pub(crate) widget_signal: RwSignal<Option<DisplayableWidget>>,
    pub(crate) last_response: StoredValue<Option<String>>,
}

pub fn create_checkout_controller(
    ctx: &AppContext,
    network_client: NetworkClient,
) -> Result<Rc<CheckoutController>, CheckoutError> {
    let start_checkout_request: StartCheckoutRequest = use_location(ctx.scope())
        .with_untracked(|loc| {
            let page_params: &serde_json::Map<String, serde_json::Value> = &loc.pageParams;
            let request_value = serde_json::Value::Object(page_params.clone());
            serde_json::from_value(request_value)
        })
        .map_err(CheckoutError::DeserializationError)?;

    let workflow_type = match &start_checkout_request {
        StartCheckoutRequest::Channels { benefit_id, .. } => get_checkout_workflow_type(benefit_id),
        StartCheckoutRequest::PrimeAddOn { .. } => AcquisitionWorkflowType::Other,
        StartCheckoutRequest::Boomerang { .. } => AcquisitionWorkflowType::Other,
    };

    put_metric_dimension(
        ctx.scope(),
        "workflowType",
        match workflow_type {
            AcquisitionWorkflowType::Channels => "3ps",
            AcquisitionWorkflowType::Other => "other", // TODO identify other cases and fill this out
        },
    );

    if workflow_type == AcquisitionWorkflowType::Other {
        return Err(CheckoutError::UnsupportedFlow);
    }

    let workflow_stages = store_value(ctx.scope(), vec![]);

    let context_to_restore: Option<Uuid> = use_location(ctx.scope()).with_untracked(|loc| {
        let uuid_value = loc.pageParams.get(STASHED_CHECKOUT_CONTEXT_IDENTIFIER)?;
        log::info!("found stashed context identifier {}", uuid_value);
        let uuid_str = uuid_value.as_str()?;
        Uuid::parse_str(uuid_str).ok()
    });

    if let Some(context_to_restore) = context_to_restore {
        restore_checkout_context(ctx.scope(), context_to_restore, workflow_stages);
    }

    let widget_signal = create_rw_signal(ctx.scope, None::<DisplayableWidget>);

    let client_data = create_client_data(start_checkout_request)?;

    // Instantiate the CheckoutController
    let checkout_controller = Rc::new(CheckoutController::new(
        ctx.scope(),
        store_value(ctx.scope(), client_data),
        workflow_stages,
        network_client,
        widget_signal,
        store_value(ctx.scope(), None),
    ));

    let react_cos_payload: Option<serde_json::Value> = use_location(ctx.scope())
        .with_untracked(|loc| loc.pageParams.get("reactCosPayload").cloned());

    if let Some(react_cos_payload) = react_cos_payload {
        log::info!("initialize from React COS payload");
        let workflow_v2_response = parse_react_cos_payload(react_cos_payload)?;
        checkout_controller.inject(workflow_v2_response)?;
    } else {
        log::info!("submitting initial request to COS");
        checkout_controller.initial_request();
    }

    Ok(checkout_controller)
}

fn parse_react_cos_payload(
    react_cos_payload: serde_json::Value,
) -> Result<WorkflowV2RawResponse, CheckoutError> {
    react_cos_payload
        .as_str()
        .ok_or_else(|| {
            CheckoutError::InvalidInputError("react_cos_payload should contain a string")
        })
        .and_then(|react_cos_payload_str| {
            network_parse_from_str(react_cos_payload_str)
                .map_err(CheckoutError::DeserializationError)
        })
}

pub trait CheckoutControl {
    fn take_step(&self, option_id: &str);
    fn initial_request(&self);
}

#[derive(Clone)]
struct RedirectContext {
    scope: Scope,
    last_response: StoredValue<Option<String>>,
    workflow_stages: StoredValue<Vec<WorkflowStage>>,
}

impl RedirectContext {
    fn new(controller: &CheckoutController) -> Self {
        Self {
            scope: controller.scope,
            last_response: controller.last_response,
            workflow_stages: controller.workflow_stages,
        }
    }

    fn redirect(&self) {
        let cos_payload = self.last_response.get_value();
        let stash_identifer = stash_current_checkout_context(self.scope, self.workflow_stages);
        redirect_to_js(self.scope, cos_payload, stash_identifer);
    }
}

pub(crate) fn create_client_data(
    start_checkout_request: StartCheckoutRequest,
) -> Result<CosClientData, CheckoutError> {
    match start_checkout_request {
        StartCheckoutRequest::Channels {
            benefit_id,
            ref_marker,
            bundle_id,
            ..
        } => Ok(CosClientData::FlowClientData(FlowClientData {
            flow_specific_client_data: {
                FlowSpecificClientData::Channel(ChannelClientData::new(benefit_id, bundle_id))
            },
            flow_agnostic_client_data: FlowAgnosticClientData::new(
                "false".into(),
                ref_marker.unwrap_or_default(),
            )
            .with_additional_checkout_capabilities(CHECKOUT_RUST_BOLT_CAPABILITY.to_string()),
        })),
        StartCheckoutRequest::PrimeAddOn { .. } => Err(CheckoutError::UnsupportedFlow),
        StartCheckoutRequest::Boomerang { .. } => Err(CheckoutError::UnsupportedFlow),
    }
}

fn metric_name_for_request_error(request_error: &RequestError) -> String {
    match request_error {
        RequestError::Http { code, .. } => format!("HttpError{}", code),
        _ => "HttpClientError".to_string(),
    }
}

impl CheckoutController {
    pub fn new(
        scope: Scope,
        client_data: StoredValue<CosClientData>,
        workflow_stages: StoredValue<Vec<WorkflowStage>>,
        network_client: NetworkClient,
        widget_signal: RwSignal<Option<DisplayableWidget>>,
        last_response: StoredValue<Option<String>>,
    ) -> Self {
        Self {
            scope,
            client_data,
            workflow_stages,
            network_client,
            widget_signal,
            last_response,
        }
    }

    fn inject(&self, response: WorkflowV2RawResponse) -> Result<(), CheckoutError> {
        Self::update_state_with_workflow_response(
            self.workflow_stages,
            self.widget_signal,
            self.last_response,
            self.scope,
            response,
        )
    }

    fn handle_successfully_parsed_view_data(
        view_data: ViewData,
        widget_signal: RwSignal<Option<DisplayableWidget>>,
    ) {
        log::info!("[Checkout Controller] view_data parse success");
        widget_signal.set(Some(DisplayableWidget { view_data }))
    }

    fn get_success_cb(
        &self,
        workflow_stages: StoredValue<Vec<WorkflowStage>>,
        widget_signal: RwSignal<Option<DisplayableWidget>>,
        last_response: StoredValue<Option<String>>,
        timer: Timer,
    ) -> impl FnMut(WorkflowV2RawResponse) {
        let redirect_context = RedirectContext::new(self);

        let scope = self.scope;

        move |response: WorkflowV2RawResponse| {
            timer.stop();
            let result = Self::update_state_with_workflow_response(
                workflow_stages,
                widget_signal,
                last_response,
                scope,
                response,
            );
            if let Err(err) = result {
                log::info!(
                    "update_state_with_workflow_response returned error {}, redirecting to react",
                    err
                );
                redirect_context.redirect();
            }
        }
    }

    // GRCOV_STOP_COVERAGE
    fn get_failure_cb(self: &CheckoutController, timer: Timer) -> impl FnMut(RequestError) {
        let scope = self.scope;
        move |error| {
            timer.stop();
            put_metric(
                scope,
                "Checkout.APIError",
                1.0,
                vec![
                    (
                        API_DIMENSION.to_string(),
                        COS_WORKFLOW_V2_API_NAME.to_string(),
                    ),
                    (
                        ERROR_TYPE_DIMENSION.to_string(),
                        metric_name_for_request_error(&error),
                    ),
                ],
            );
            handle_checkout_error(scope, &CheckoutError::COSRequestError(error))
        }
    }
    // GRCOV_BEGIN_COVERAGE

    fn call_workflow(&self, request: COSWorkflowV2Request) {
        let timer = Timer::start(
            self.scope,
            "Checkout.APILatency",
            vec![(
                API_DIMENSION.to_string(),
                COS_WORKFLOW_V2_API_NAME.to_string(),
            )],
        );
        self.network_client.workflow_v2(
            request,
            self.get_success_cb(
                self.workflow_stages,
                self.widget_signal,
                self.last_response,
                timer.clone(),
            ),
            self.get_failure_cb(timer),
        );
    }

    fn update_state_with_workflow_response(
        workflow_stages: StoredValue<Vec<WorkflowStage>>,
        widget_signal: RwSignal<Option<DisplayableWidget>>,
        last_response: StoredValue<Option<String>>,
        scope: Scope,
        response: WorkflowV2RawResponse,
    ) -> Result<(), CheckoutError> {
        if let Some(ref response_string) = response.original_response {
            last_response.set_value(Some(response_string.clone()));
        }
        let workflow_v2_response: WorkflowV2Response =
            convert_workflow_v2_raw_response_to_workflow_v2_response(response);

        workflow_stages.update_value(|stages| {
            stages.push(WorkflowStage {
                workflow_data: workflow_v2_response.service_data,
                options: workflow_v2_response.options,
            });
        });

        match workflow_v2_response.task_data {
            TaskDataType::Widget(widget) => {
                let common_fields = widget.get_common_fields();

                log::info!(
                    "received widget response templateId={}, process_viewer_url={}",
                    common_fields.template_id,
                    common_fields.process_viewer_url
                );

                log::info!("view data {}", common_fields.view_data);

                let view_data: Result<ViewData, Error> =
                    network_parse_from_str(&common_fields.view_data);

                match view_data {
                    Ok(view_data) => {
                        validate_view_data(&view_data)?;
                        gate_view_data(&view_data)?;
                        Self::handle_successfully_parsed_view_data(view_data, widget_signal);
                        Ok(())
                    }
                    Err(error) => {
                        log::info!(
                            "[Checkout Controller] view data parse failure {} for template id {}",
                            error,
                            common_fields.template_id
                        );
                        widget_signal.set(None);
                        Err(CheckoutError::DeserializationError(error))
                    }
                }
            }

            TaskDataType::Callback(callback) => {
                let common_fields = callback.get_common_fields();
                log::error!("workflow {} attempted to invoke a callback {} which is not supported - attempt to recover by redirecting to React", common_fields.process_viewer_url, common_fields.callback_id);
                put_metric(
                    scope,
                    "Checkout.Error",
                    1.0,
                    vec![("errorType".to_string(), "UnknownCallbackError".to_string())],
                );
                Err(CheckoutError::UnsupportedCallback(
                    common_fields.callback_id,
                ))
            }
        }
    }
}

impl CheckoutControl for CheckoutController {
    fn take_step(&self, option_id: &str) {
        let step_option: Result<StepOption, CheckoutError> = self
            .workflow_stages
            .with_value(|stages| get_step_option_from_stage(option_id, stages.last()));

        match step_option {
            Ok(step_option) => {
                let client_data = self.client_data.get_value().with_step_option(&step_option);

                log::info!("[Checkout Controller] take step");

                let request = COSWorkflowV2Request {
                    client_id: "Blast".into(),
                    client_data,
                    service_data: self.workflow_stages.with_value(|stages| {
                        get_optional_workflow_service_data_from_stages(stages)
                    }),
                    workflow_type: "Acquisition".into(),
                };

                self.call_workflow(request);
            }
            Err(e) => {
                handle_checkout_error(self.scope, &e);
            }
        }
    }

    fn initial_request(&self) {
        let request = COSWorkflowV2Request {
            client_id: "Blast".into(),
            client_data: self.client_data.get_value(),
            service_data: None,
            workflow_type: "Acquisition".into(),
        };
        self.call_workflow(request);
    }
}

#[cfg(test)]
mod checkout_controller_tests {
    use core::cell::RefCell;
    use core::convert::Into;
    use core::{assert, assert_eq, include_str};
    use std::rc::Rc;

    use super::*;
    use crate::app_context::{CheckoutAppContext, MockCheckoutAppContext};
    use crate::metrics::MockMetricEmitter;
    use crate::network::types::callback_types::{
        CallbackVariation, CommonCallbackFields, StandardCallback,
    };
    use crate::network::{
        checkout_network::MockNetworkClient,
        test_utils::{
            create_dummy_cos_v2_request, create_dummy_cos_v2_response,
            create_test_checkout_controller_with_stages,
        },
        types::{
            checkout_client_data::{
                cos_client_data::CosClientData,
                svod_client_data::svod_common_client_data::SvodCommonClientData,
            },
            checkout_view_data::common::Layout,
            widget_types::{CommonWidgetFields, WidgetVariation},
            workflow_v2_raw_response::{StepOption, WorkflowResponseClientContract},
        },
    };
    use cross_app_events::create_serde_map;
    use ignx_compositron::{app::launch_only_app_context, reactive::create_rw_signal};
    use location::{JSPage, Location, PageType, RustPage};
    use mockall::predicate::{always, eq};
    use router::{MockRouting, RoutingContext};
    use rstest::rstest;
    use serde_json::Value;
    use serial_test::serial;

    #[test]
    fn test_create_client_data() {
        launch_only_app_context(|_| {
            let start_checkout_request = StartCheckoutRequest::Channels {
                benefit_id: "new_test_benefit_id".to_string(),
                ref_marker: Some("ref_marker".to_string()),
                bundle_id: None,
                ingress_point: "ingress_point".to_string(),
                rust_source_name: None,
                title_id: None,
                deferred_navigation_location: None,
            };

            let client_data = create_client_data(start_checkout_request).unwrap();

            assert_eq!(
                client_data,
                CosClientData::FlowClientData(FlowClientData {
                    flow_specific_client_data: FlowSpecificClientData::Channel(ChannelClientData {
                        subscription_product_id: "new_test_benefit_id".to_string(),
                        bundle_id: None,
                        svod_common_client_data: SvodCommonClientData {
                            client_prime_signup_name: "rust".to_string(),
                        },
                    }),
                    flow_agnostic_client_data: FlowAgnosticClientData {
                        client_version: "2.7.0".to_string(),
                        consumption_mode_enabled: "false".to_string(),
                        ref_tag: "ref_marker".to_string(),
                        checkout_client_capabilities:
                            "LRD_MULTI_BUTTON_PAGE_VERSION=1&RUST_SINGLE_OFFER_BOLT_VERSION=1"
                                .to_string(),
                        option_id: None,
                        action: None,
                        next_option: None,
                    },
                })
            );
        });
    }

    #[rstest]
    #[serial]
    fn test_create_checkout_controller_with_checkout_context_identifier_triggers_restore() {
        launch_only_app_context(|ctx| {
            let mut mock_network_client = MockNetworkClient::default();
            let expected_response = create_dummy_cos_v2_response();

            mock_network_client
                .expect_workflow_v2()
                .times(1)
                .returning(move |_, success_cb, _| {
                    success_cb(expected_response.clone());
                });

            let emit_context = MockMetricEmitter::emit_context();

            expect_metric(
                &emit_context,
                "Checkout.APILatency",
                0.0,
                vec![("api", "COS::WorkflowV2")],
            );

            let mock_router = create_mock_router_with_location(
                &ctx,
                create_serde_map([
                    (
                        "checkout_request_type",
                        Value::String("Channels".to_string()),
                    ),
                    ("benefitId", Value::String("test_benefit_id".to_string())),
                    ("ingressPoint", Value::String("ingressPoint".to_string())),
                    (
                        "rustStashedCheckoutContextIdentifier",
                        Value::String("550e8400-e29b-41d4-a716-************".to_string()),
                    ),
                ]),
            );

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            let mock_checkout_app_context = Rc::new(RefCell::new(MockCheckoutAppContext::new()));
            provide_context::<Rc<RefCell<dyn CheckoutAppContext>>>(
                ctx.scope(),
                mock_checkout_app_context.clone(),
            );
            mock_checkout_app_context
                .borrow_mut()
                .expect_restore_checkout_context()
                .with(
                    eq(Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap()),
                    always(),
                )
                .return_once(|_, _| ())
                .once();

            create_checkout_controller(&ctx, mock_network_client).unwrap();
        });
    }

    fn create_mock_router_with_location(
        ctx: &AppContext,
        page_params: serde_json::Map<String, serde_json::Value>,
    ) -> MockRouting {
        let mut mock_router = MockRouting::default();
        mock_router.expect_location().returning({
            let scope = ctx.scope();
            move || {
                create_rw_signal(
                    scope,
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_CHECKOUT),
                        pageParams: page_params.clone(),
                    },
                )
                .into()
            }
        });

        mock_router
    }

    #[test]
    fn test_create_checkout_controller_with_react_cos_payload_injects_payload() {
        launch_only_app_context(|ctx| {
            let mock_network_client = MockNetworkClient::default();

            let mock_router = create_mock_router_with_location(
                &ctx,
                create_serde_map([
                    (
                        "checkout_request_type",
                        Value::String("Channels".to_string()),
                    ),
                    ("benefitId", Value::String("test_benefit_id".to_string())),
                    ("ingressPoint", Value::String("ingressPoint".to_string())),
                    (
                        "reactCosPayload",
                        Value::String(
                            include_str!("network/test_data/cos_response_rearchitecture.json")
                                .to_string(),
                        ),
                    ),
                ]),
            );

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            let checkout_controller =
                create_checkout_controller(&ctx, mock_network_client).unwrap();

            assert_eq!(checkout_controller.workflow_stages.get_value().len(), 1);
            assert!(matches!(
                checkout_controller.widget_signal.get(),
                Some(DisplayableWidget { .. })
            ));
        });
    }

    #[test]
    fn test_create_checkout_controller_with_errors_when_react_cos_payload_is_not_string() {
        launch_only_app_context(|ctx| {
            let mock_network_client = MockNetworkClient::default();

            let mock_router = create_mock_router_with_location(
                &ctx,
                create_serde_map([
                    (
                        "checkout_request_type",
                        Value::String("Channels".to_string()),
                    ),
                    ("benefitId", Value::String("test_benefit_id".to_string())),
                    ("ingressPoint", Value::String("ingressPoint".to_string())),
                    ("reactCosPayload", Value::Number(1.into())),
                ]),
            );

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            let maybe_controller = create_checkout_controller(&ctx, mock_network_client);

            match maybe_controller {
                Err(CheckoutError::InvalidInputError(_)) => {} // expected case
                Err(err) => {
                    panic!(
                        "create_checkout_controller errored but didn't return InvalidInputError: {}",
                        err
                    )
                }
                _ => panic!("create_checkout_controller should have errored"),
            }
        });
    }

    #[test]
    fn test_create_checkout_controller_with_react_cos_payload_injects_payload_should_error_response_parse_error(
    ) {
        launch_only_app_context(|ctx| {
            let mock_network_client = MockNetworkClient::default();

            let mock_router = create_mock_router_with_location(
                &ctx,
                create_serde_map([
                    (
                        "checkout_request_type",
                        Value::String("Channels".to_string()),
                    ),
                    ("benefitId", Value::String("test_benefit_id".to_string())),
                    ("ingressPoint", Value::String("ingressPoint".to_string())),
                    ("reactCosPayload", Value::String("not_json".to_string())),
                ]),
            );

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            let maybe_controller = create_checkout_controller(&ctx, mock_network_client);

            match maybe_controller {
                Err(CheckoutError::DeserializationError(_)) => {} // expected case
                Err(err) => {
                    panic!(
                        "create_checkout_controller errored but didn't return DeserializationError: {}",
                        err
                    )
                }
                _ => panic!("create_checkout_controller should have errored"),
            }
        });
    }

    #[test]
    fn test_create_checkout_controller_with_react_cos_payload_injects_payload_should_error_on_viewdata_parse_error(
    ) {
        launch_only_app_context(|ctx| {
            let mock_network_client = MockNetworkClient::default();

            let mock_router = create_mock_router_with_location(
                &ctx,
                create_serde_map([
                    (
                        "checkout_request_type",
                        Value::String("Channels".to_string()),
                    ),
                    ("benefitId", Value::String("test_benefit_id".to_string())),
                    ("ingressPoint", Value::String("ingressPoint".to_string())),
                    (
                        "reactCosPayload",
                        Value::String(
                            include_str!("./network/test_data/cos_response_multi_button_page.json")
                                .to_string(),
                        ),
                    ),
                ]),
            );

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            let maybe_controller = create_checkout_controller(&ctx, mock_network_client);

            match maybe_controller {
                Err(CheckoutError::DeserializationError(_)) => {} // expected case
                Err(err) => {
                    panic!(
                        "create_checkout_controller errored but didn't return DeserializationError: {}",
                        err
                    )
                }
                _ => panic!("create_checkout_controller should have errored"),
            }
        });
    }

    #[test]
    fn test_create_client_data_invalid_benefit_id() {
        launch_only_app_context(|ctx| {
            let mock_network_client = MockNetworkClient::default();

            let mock_router = create_mock_router_with_location(
                &ctx,
                create_serde_map([
                    (
                        "checkout_request_type",
                        Value::String("Channels".to_string()),
                    ),
                    ("benefitId", Value::String("Prime".to_string())),
                    ("ingressPoint", Value::String("ingressPoint".to_string())),
                ]),
            );

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            let maybe_controller = create_checkout_controller(&ctx, mock_network_client);

            match maybe_controller {
                Err(CheckoutError::UnsupportedFlow) => {} // expected case
                Err(err) => {
                    panic!(
                        "create_checkout_controller errored but didn't return UnsupportedFlow: {}",
                        err
                    )
                }
                _ => panic!("checkout_checkout_controller should have errored"),
            }
        });
    }

    #[test]
    fn test_when_checkout_params_includes_bundle_id_then_it_is_populated_in_client_data() {
        launch_only_app_context(|_| {
            let start_checkout_request = StartCheckoutRequest::Channels {
                benefit_id: "new_test_benefit_id".to_string(),
                ref_marker: Some("ref_marker".to_string()),
                bundle_id: Some("bundle_id".to_string()),
                ingress_point: "ingress_point".to_string(),
                rust_source_name: None,
                title_id: None,
                deferred_navigation_location: None,
            };

            let client_data = create_client_data(start_checkout_request).unwrap();

            assert_eq!(
                client_data,
                CosClientData::FlowClientData(FlowClientData {
                    flow_specific_client_data: FlowSpecificClientData::Channel(ChannelClientData {
                        subscription_product_id: "new_test_benefit_id".to_string(),
                        bundle_id: Some("bundle_id".to_string()),
                        svod_common_client_data: SvodCommonClientData {
                            client_prime_signup_name: "rust".to_string(),
                        },
                    }),
                    flow_agnostic_client_data: FlowAgnosticClientData {
                        client_version: "2.7.0".to_string(),
                        consumption_mode_enabled: "false".to_string(),
                        ref_tag: "ref_marker".to_string(),
                        checkout_client_capabilities:
                            "LRD_MULTI_BUTTON_PAGE_VERSION=1&RUST_SINGLE_OFFER_BOLT_VERSION=1"
                                .to_string(),
                        option_id: None,
                        action: None,
                        next_option: None,
                    },
                })
            );
        });
    }

    #[test]
    #[serial]
    fn test_take_step_and_call_workflow() {
        launch_only_app_context(|ctx| {
            let mut mock_network_client = MockNetworkClient::default();

            let expected_request = create_dummy_cos_v2_request();
            let expected_response = create_dummy_cos_v2_response();

            mock_network_client
                .expect_workflow_v2()
                .withf(move |request: &COSWorkflowV2Request, _, _| request == &expected_request)
                .times(1)
                .returning(move |_, success_cb, _| {
                    success_cb(expected_response.clone());
                });

            let emit_context = MockMetricEmitter::emit_context();

            expect_metric(
                &emit_context,
                "Checkout.APILatency",
                0.0,
                vec![("api", "COS::WorkflowV2")],
            );

            let step_option = StepOption {
                action: "test_action".to_string(),
                option_id: "test_option".to_string(),
                next_option: "next_test_option".to_string(),
            };

            let controller = create_test_checkout_controller_with_stages(
                &ctx,
                mock_network_client,
                vec![WorkflowStage {
                    workflow_data: WorkflowV2ServiceData {
                        data: "data".to_string(),
                        version: "version".to_string(),
                    },
                    options: vec![step_option],
                }],
            );

            assert_eq!(controller.workflow_stages.get_value().len(), 1);

            controller.take_step("test_option");

            let stages = controller.workflow_stages.get_value();
            assert_eq!(stages.len(), 2);

            let latest_stage = &stages[1];
            assert_eq!(latest_stage.workflow_data.data, "service_data");
            assert_eq!(latest_stage.workflow_data.version, "2");
            assert_eq!(latest_stage.options.len(), 1);
            assert_eq!(latest_stage.options[0].option_id, "option_id");
            assert_eq!(latest_stage.options[0].action, "action");
            assert_eq!(latest_stage.options[0].next_option, "next_option");
        });
    }

    #[test]
    #[serial]
    fn test_get_success_cb_for_a_displayable_widget() {
        launch_only_app_context(|ctx| {
            let mock_network_client = MockNetworkClient::default();

            let controller =
                create_test_checkout_controller_with_stages(&ctx, mock_network_client, vec![]);

            let emit_context = MockMetricEmitter::emit_context();
            expect_metric(&emit_context, "latency_metric", 0.0, vec![]);

            let success_cb = CheckoutController::get_success_cb(
                &controller,
                controller.workflow_stages,
                controller.widget_signal,
                controller.last_response,
                Timer::start(ctx.scope(), "latency_metric", vec![]),
            );

            let widget_variation = WidgetVariation::StandardWidget(CommonWidgetFields {
                template_data: "test_template_data".to_string(),
                template_id: "lrdBoltConfirmationOneOffer".to_string(),
                view_data: r#"{"backgroundImage": "https://example.com/image.jpg", "rightSectionButtons": {"layout": "horizontal", "buttons": []}}"#.to_string(),
                process_viewer_url: "process_viewer_url".to_string(),
            });

            let raw_response = WorkflowV2RawResponse {
                client_contracts: vec![WorkflowResponseClientContract {
                    task_type: "task_type".to_string(),
                    task_data: TaskDataType::Widget(widget_variation),
                }],
                options: vec![],
                service_data: WorkflowV2ServiceData {
                    data: "test_data".to_string(),
                    version: "1.0".to_string(),
                },
                original_response: Some("updated response string".to_string()),
            };

            let mut success_cb = success_cb;
            success_cb(raw_response);

            let stages = controller.workflow_stages.get_value();
            assert_eq!(stages.len(), 1);
            assert_eq!(stages[0].workflow_data.data, "test_data");
            assert_eq!(stages[0].workflow_data.version, "1.0");
            assert_eq!(
                controller.last_response.get_value(),
                Some("updated response string".to_string())
            );

            let widget = controller.widget_signal.get();
            assert!(widget.is_some());
            let displayable_widget = widget.unwrap();

            match displayable_widget {
                DisplayableWidget { view_data } => match view_data {
                    ViewData::ConfirmationBoltSingleTierViewData(lrd_data) => {
                        assert_eq!(
                            lrd_data.background_image,
                            Some("https://example.com/image.jpg".to_string())
                        );
                        assert_eq!(
                            lrd_data.right_section_buttons.clone().layout,
                            Layout::Horizontal
                        );
                        assert_eq!(lrd_data.right_section_buttons.buttons.len(), 0);
                    }
                    _ => panic!("Expected ConfirmationBoltSingleTierViewData"),
                },
            }
        });
    }

    #[test]
    #[serial]
    fn test_get_success_cb_for_a_callback() {
        launch_only_app_context(|ctx| {
            let mock_network_client = MockNetworkClient::default();

            let controller =
                create_test_checkout_controller_with_stages(&ctx, mock_network_client, vec![]);

            let emit_context = MockMetricEmitter::emit_context();
            expect_metric(&emit_context, "latency_metric", 0.0, vec![]);
            expect_metric(
                &emit_context,
                "Checkout.Error",
                1.0,
                vec![("errorType", "UnknownCallbackError")],
            );
            expect_metric(
                &emit_context,
                "Checkout.TransitionFromRustToReact",
                1.0,
                vec![],
            );

            let stash_uuid = Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap();
            expect_navigate_to_react(&ctx, Some(stash_uuid.clone()));

            expect_stash_context(&ctx, stash_uuid);

            let success_cb = CheckoutController::get_success_cb(
                &controller,
                controller.workflow_stages,
                controller.widget_signal,
                controller.last_response,
                Timer::start(ctx.scope(), "latency_metric", vec![]),
            );

            let callback_variation = CallbackVariation::StandardCallback(StandardCallback {
                common_callback_fields: CommonCallbackFields {
                    callback_id: "callback_id".to_string(),
                    process_viewer_url: "process_viewer_url".to_string(),
                },
            });

            let raw_response = WorkflowV2RawResponse {
                client_contracts: vec![WorkflowResponseClientContract {
                    task_type: "task_type".to_string(),
                    task_data: TaskDataType::Callback(callback_variation),
                }],
                options: vec![],
                service_data: WorkflowV2ServiceData {
                    data: "test_data".to_string(),
                    version: "1.0".to_string(),
                },
                original_response: Some("originalresponse".to_string()),
            };

            let mut success_cb = success_cb;
            success_cb(raw_response);

            let stages = controller.workflow_stages.get_value();
            assert_eq!(stages.len(), 1);
            assert_eq!(stages[0].workflow_data.data, "test_data");
            assert_eq!(stages[0].workflow_data.version, "1.0");
            assert_eq!(
                controller.last_response.get_value(),
                Some("originalresponse".to_string())
            );

            let widget = controller.widget_signal.get();
            assert!(widget.is_none());
        });
    }

    fn expect_stash_context(ctx: &AppContext, stash_uuid: Uuid) {
        let mock_checkout_app_context = Rc::new(RefCell::new(MockCheckoutAppContext::new()));
        provide_context::<Rc<RefCell<dyn CheckoutAppContext>>>(
            ctx.scope(),
            mock_checkout_app_context.clone(),
        );
        mock_checkout_app_context
            .borrow_mut()
            .expect_stash_current_checkout_context()
            .withf(|workflow_stages| {
                workflow_stages.get_value()
                    == vec![WorkflowStage {
                        workflow_data: WorkflowV2ServiceData {
                            data: "test_data".to_string(),
                            version: "1.0".to_string(),
                        },
                        options: vec![],
                    }]
            })
            .return_once(move |_| stash_uuid)
            .once();
    }

    #[rstest]
    #[case("invalidTemplateId", r#"{"invalid_view_data:": "test_value" }"#)]
    #[case(
        "lrdBoltConfirmationOneOffer",
        r#"{"invalid_view_data:": "test_value" }"#
    )]
    #[case("invalidTemplateId", r#"{"backgroundImage": "https://example.com/image.jpg", "horizontalAlign": "center", "buttons": {"layout": "horizontal", "buttons": []}}"#)]
    #[serial]
    fn test_get_success_cb_for_a_non_displayable_widget(
        #[case] template_id_param: String,
        #[case] view_data_param: String,
    ) {
        launch_only_app_context(move |ctx| {
            let mock_network_client = MockNetworkClient::default();

            let stash_uuid = Uuid::parse_str("550e8400-e29b-41d4-a716-************").unwrap();
            expect_navigate_to_react(&ctx, Some(stash_uuid.clone()));

            expect_stash_context(&ctx, stash_uuid);

            let emit_context = MockMetricEmitter::emit_context();
            expect_metric(&emit_context, "latency_metric", 0.0, vec![]);
            expect_metric(
                &emit_context,
                "Checkout.TransitionFromRustToReact",
                1.0,
                vec![],
            );

            let controller =
                create_test_checkout_controller_with_stages(&ctx, mock_network_client, vec![]);
            let success_cb = CheckoutController::get_success_cb(
                &controller,
                controller.workflow_stages,
                controller.widget_signal,
                controller.last_response,
                Timer::start(ctx.scope(), "latency_metric", vec![]),
            );

            let widget_variation = WidgetVariation::StandardWidget(CommonWidgetFields {
                template_data: "test_template_data".to_string(),
                template_id: template_id_param.clone(),
                view_data: view_data_param.clone(),
                process_viewer_url: "process_viewer_url".to_string(),
            });

            let raw_response = WorkflowV2RawResponse {
                client_contracts: vec![WorkflowResponseClientContract {
                    task_type: "task_type".to_string(),
                    task_data: TaskDataType::Widget(widget_variation),
                }],
                options: vec![],
                service_data: WorkflowV2ServiceData {
                    data: "test_data".to_string(),
                    version: "1.0".to_string(),
                },
                original_response: Some("originalresponse".to_string()),
            };

            let mut success_cb = success_cb;
            success_cb(raw_response);

            let stages = controller.workflow_stages.get_value();
            assert_eq!(stages.len(), 1);
            assert_eq!(stages[0].workflow_data.data, "test_data");
            assert_eq!(stages[0].workflow_data.version, "1.0");
            assert_eq!(
                controller.last_response.get_value(),
                Some("originalresponse".to_string())
            );

            let widget = controller.widget_signal.get();
            assert!(widget.is_none());
        });
    }

    #[test]
    #[serial]
    fn test_take_step_navigates_to_error_page_when_step_option_is_invalid() {
        launch_only_app_context(move |ctx| {
            provide_checkout_metrics_context(ctx.scope());
            put_metric_dimension(ctx.scope(), "clientId", "Blast");
            put_metric_dimension(ctx.scope(), "activeLayer", "Wasm");

            let mock_network_client = MockNetworkClient::default();

            let mut mock_router = MockRouting::default();

            mock_router
                .expect_navigate()
                .once()
                .with(
                    eq(Location {
                        pageType: PageType::Js(location::JSPage::ERROR_PAGE),
                        pageParams: create_serde_map([(
                            "redirectButtonsEnabled",
                            serde_json::Value::Bool(true),
                        )]),
                    }),
                    eq("RUST_CHECKOUT"),
                )
                .return_const(());

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            let emit_context = MockMetricEmitter::emit_context();
            expect_metric(
                &emit_context,
                "Checkout.Error",
                1.0,
                vec![
                    ("clientId", "Blast"),
                    ("activeLayer", "Wasm"),
                    ("errorType", "InvalidStepOptionError"),
                ],
            );

            let controller =
                create_test_checkout_controller_with_stages(&ctx, mock_network_client, vec![]);

            controller.take_step("invalid_option");
        });
    }

    fn expect_navigate_to_react(ctx: &AppContext, stash_uuid: Option<Uuid>) {
        let mut mock_router = MockRouting::default();
        mock_router.expect_location().returning({
            let scope = ctx.scope();

            move || {
                create_rw_signal(
                    scope,
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_DETAILS),
                        pageParams: create_serde_map([(
                            "benefitId",
                            Value::String("test_benefit".to_string()),
                        )]),
                    },
                )
                .into()
            }
        });

        mock_router
            .expect_navigate()
            .times(1)
            .returning(move |loc: Location, source| {
                assert_eq!(loc.pageType, PageType::Js(JSPage::NEW_CONFIRMATION_PAGE));

                let mut expected_params = create_serde_map([
                    ("benefitId", Value::String("test_benefit".to_string())),
                    (
                        "rustTransitionStartTimestamp",
                        Value::String("0".to_string()),
                    ),
                    (
                        "rustCosPayload",
                        Value::String("originalresponse".to_string()),
                    ),
                ]);

                if let Some(uuid) = stash_uuid {
                    expected_params.insert(
                        "rustStashedCheckoutContextIdentifier".to_string(),
                        Value::String(uuid.to_string()),
                    );
                }

                assert_eq!(loc.pageParams, expected_params);
                assert_eq!(source, "RUST_CHECKOUT");
            });
        provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));
    }
    #[test]
    fn test_create_client_data_prime_addon() {
        launch_only_app_context(|_| {
            let start_checkout_request = StartCheckoutRequest::PrimeAddOn {
                ref_marker: Some("ref_marker".to_string()),
                ingress_point: "ingress_point".to_string(),
                rust_source_name: Some("rust_source_name".to_string()),
                title_id: Some("title_id".to_string()),
            };

            let result = create_client_data(start_checkout_request);

            assert!(matches!(result, Err(CheckoutError::UnsupportedFlow)));
        });
    }
    #[test]
    fn test_create_client_data_boomerang() {
        launch_only_app_context(|_| {
            let start_checkout_request = StartCheckoutRequest::Boomerang {
                ref_marker: Some("ref_marker".to_string()),
                ingress_point: "ingress_point".to_string(),
                rust_source_name: Some("rust_source_name".to_string()),
                title_id: Some("title_id".to_string()),
            };

            let result = create_client_data(start_checkout_request);

            assert!(matches!(result, Err(CheckoutError::UnsupportedFlow)));
        });
    }
}
