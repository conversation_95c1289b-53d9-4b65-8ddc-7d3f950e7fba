use core::matches;

use cross_app_events::create_serde_map;
use ignx_compositron::prelude::*;
use location::{Location, PageType};
use network::RequestError;
use router::hooks::use_navigation;
use thiserror::Error;

use crate::{
    metrics::*, network::redirect::redirect_to_js, utils::cleanup_utils::cleanup_checkout,
};

// this enum should categorise the types of error we hit in checkout
// as we implement them we can add cases for failures in widget rendering,
// invalid callbacks etc
#[derive(Debug, Error)]
pub enum CheckoutError {
    #[error("failed to call COS::WorkflowV2")]
    COSRequestError(#[source] RequestError),

    #[error("invalid input: {0}")]
    InvalidInputError(&'static str),

    #[error("invalid step option: {0}")]
    InvalidStepOptionError(String),

    #[error("failed to deserialize StartCheckoutRequest")]
    DeserializationError(#[source] serde_json::Error),

    #[error("failed to serialize order action")]
    SerializationError(#[source] serde_json::Error),

    #[error("invalid view data: {0}")]
    InvalidViewData(String),

    #[error("gated functionality: {0}")]
    GatingError(String),

    #[error("unsupported flow - redirect to React")]
    UnsupportedFlow,

    #[error("unsupported callback {0}")]
    UnsupportedCallback(String),
}

fn log_error(error_type: &CheckoutError) {
    log::error!(
        "redirect to error page due to checkout error: {}",
        error_type
    );

    let mut error: &dyn std::error::Error = &error_type;
    while let Some(cause) = error.source() {
        log::error!("caused by: {}", cause);
        error = cause;
    }
}

fn navigate_to_js(scope: Scope, error_type: &CheckoutError) {
    if matches!(error_type, CheckoutError::UnsupportedFlow) {
        redirect_to_js(scope, None, None);
    } else {
        navigate_to_js_error_page(scope);
    }
}

fn navigate_to_js_error_page(scope: Scope) {
    cleanup_checkout(scope);
    let navigate = use_navigation(scope);
    navigate(
        Location {
            pageType: PageType::Js(location::JSPage::ERROR_PAGE),
            pageParams: create_serde_map([(
                "redirectButtonsEnabled",
                serde_json::Value::Bool(true),
            )]),
        },
        "RUST_CHECKOUT",
    );
}

fn emit_metrics(scope: Scope, error_type: &CheckoutError) {
    let error_type = match error_type {
        CheckoutError::COSRequestError(_) => "WorkflowRequestError",
        CheckoutError::InvalidInputError(_) => "InvalidInputError",
        CheckoutError::InvalidStepOptionError(_) => "InvalidStepOptionError",
        CheckoutError::UnsupportedFlow => "UnsupportedFlowError",
        CheckoutError::DeserializationError(_) => "DeserializationError",
        CheckoutError::SerializationError(_) => "SerializationError",
        CheckoutError::InvalidViewData(_) => "InvalidViewData",
        CheckoutError::GatingError(_) => "GatingError",
        CheckoutError::UnsupportedCallback(_) => "UnsupportedCallback",
    };
    put_metric(
        scope,
        "Checkout.Error",
        1.0,
        vec![(ERROR_TYPE_DIMENSION.to_string(), error_type.to_string())],
    );
}

pub fn handle_checkout_error(scope: Scope, error_type: &CheckoutError) {
    log_error(error_type);
    emit_metrics(scope, error_type);
    navigate_to_js(scope, error_type);
}

#[cfg(test)]
mod tests {
    use ignx_compositron::{
        app::launch_only_app_context,
        prelude::{create_rw_signal, provide_context},
    };
    use location::RustPage;
    use mockall::predicate::eq;
    use router::{MockRouting, RoutingContext};
    use serial_test::serial;

    use crate::metrics::{expect_metric, MockMetricEmitter};

    use super::*;

    #[test]
    #[serial]
    fn should_navigate_to_js_error_page() {
        launch_only_app_context(|ctx| {
            let error = CheckoutError::COSRequestError(network::RequestError::Authorization(
                "test".to_string(),
            ));

            let mut mock_routing = MockRouting::new();
            mock_routing
                .expect_navigate()
                .once()
                .with(
                    eq(Location {
                        pageType: PageType::Js(location::JSPage::ERROR_PAGE),
                        pageParams: create_serde_map([(
                            "redirectButtonsEnabled",
                            serde_json::Value::Bool(true),
                        )]),
                    }),
                    eq("RUST_CHECKOUT"),
                )
                .return_const(());
            provide_context::<RoutingContext>(ctx.scope(), std::rc::Rc::new(mock_routing));

            let emit_context = MockMetricEmitter::emit_context();
            expect_metric(
                &emit_context,
                "Checkout.Error",
                1.0,
                vec![("errorType", "WorkflowRequestError")],
            );

            handle_checkout_error(ctx.scope(), &error);
        });
    }

    #[test]
    #[serial]
    fn when_error_is_unsupported_flow_should_navigate_to_js_checkout() {
        launch_only_app_context(|ctx| {
            let error = CheckoutError::UnsupportedFlow;

            let mut mock_router = MockRouting::default();
            mock_router.expect_location().returning({
                let scope = ctx.scope();
                move || {
                    create_rw_signal(
                        scope,
                        Location {
                            pageType: PageType::Rust(RustPage::RUST_CHECKOUT),
                            pageParams: create_serde_map([(
                                "benefitId",
                                serde_json::Value::String("Prime".to_string()),
                            )]),
                        },
                    )
                    .into()
                }
            });

            mock_router
                .expect_navigate()
                .once()
                .with(
                    eq(Location {
                        pageType: PageType::Js(location::JSPage::NEW_CONFIRMATION_PAGE),
                        pageParams: create_serde_map([
                            ("benefitId", serde_json::Value::String("Prime".to_string())),
                            (
                                "rustTransitionStartTimestamp",
                                serde_json::Value::String("0".to_string()),
                            ),
                        ]),
                    }),
                    eq("RUST_CHECKOUT"),
                )
                .return_const(());
            provide_context::<RoutingContext>(ctx.scope(), std::rc::Rc::new(mock_router));

            let emit_context = MockMetricEmitter::emit_context();
            expect_metric(
                &emit_context,
                "Checkout.Error",
                1.0,
                vec![("errorType", "UnsupportedFlowError")],
            );
            expect_metric(
                &emit_context,
                "Checkout.TransitionFromRustToReact",
                1.0,
                vec![],
            );

            handle_checkout_error(ctx.scope(), &error);
        });
    }
}
