use core::cell::RefCell;
use mockall::automock;
use std::rc::Rc;
use uuid::Uuid;

use ignx_compositron::{prelude::*, reactive::*};

use crate::{checkout_controller::WorkflowStage, metrics::put_metric};

pub struct StashedCheckoutContext {
    pub identifier: Uuid,
    pub workflow_stages: Vec<WorkflowStage>,
}

pub struct CheckoutAppContextImpl {
    pub stashed_checkout_context: Option<StashedCheckoutContext>,
}

#[automock]
pub trait CheckoutAppContext {
    fn stash_current_checkout_context(
        &mut self,
        workflow_stages: StoredValue<Vec<WorkflowStage>>,
    ) -> Uuid;
    fn restore_checkout_context(
        &mut self,
        context_to_restore: Uuid,
        workflow_stages: StoredValue<Vec<WorkflowStage>>,
    );
}

impl CheckoutAppContext for CheckoutAppContextImpl {
    fn stash_current_checkout_context(
        &mut self,
        workflow_stages: StoredValue<Vec<WorkflowStage>>,
    ) -> Uuid {
        let identifier = Uuid::new_v4();

        self.stashed_checkout_context = Some(StashedCheckoutContext {
            identifier,
            workflow_stages: workflow_stages.get_value(),
        });

        identifier
    }

    fn restore_checkout_context(
        &mut self,
        context_to_restore: Uuid,
        workflow_stages: StoredValue<Vec<WorkflowStage>>,
    ) {
        if let Some(stashed_checkout_context) = self.stashed_checkout_context.take() {
            if stashed_checkout_context.identifier != context_to_restore {
                log::warn!("attempt to restore checkout context failed - context in stash {} does not match request {}", stashed_checkout_context.identifier, context_to_restore);
                return;
            }

            workflow_stages.set_value(stashed_checkout_context.workflow_stages);

            log::info!("attempt to restore checkout context succeeded");
        };
    }
}

pub fn stash_current_checkout_context(
    scope: Scope,
    workflow_stages: StoredValue<Vec<WorkflowStage>>,
) -> Option<Uuid> {
    let Some(checkout_app_context) = use_context::<Rc<RefCell<dyn CheckoutAppContext>>>(scope)
    else {
        log::warn!("attempt to stash checkout context failed - CheckoutAppContext not found");
        put_metric(
            scope,
            "Checkout.Error",
            1.0,
            vec![(
                "errorType".to_string(),
                "CheckoutAppContextNotFound".to_string(),
            )],
        );
        return None;
    };

    let identifier = checkout_app_context
        .borrow_mut()
        .stash_current_checkout_context(workflow_stages);
    Some(identifier)
}

pub fn restore_checkout_context(
    scope: Scope,
    context_to_restore: Uuid,
    workflow_stages: StoredValue<Vec<WorkflowStage>>,
) {
    log::info!("attempt to restore checkout context {}", context_to_restore);

    let Some(checkout_app_context) = use_context::<Rc<RefCell<dyn CheckoutAppContext>>>(scope)
    else {
        log::warn!("attempt to restore checkout context failed - CheckoutAppContext not found");
        put_metric(
            scope,
            "Checkout.Error",
            1.0,
            vec![(
                "errorType".to_string(),
                "CheckoutAppContextNotFound".to_string(),
            )],
        );
        return;
    };

    checkout_app_context
        .borrow_mut()
        .restore_checkout_context(context_to_restore, workflow_stages);
}

pub fn provide_checkout_app_context(scope: Scope) {
    provide_context::<Rc<RefCell<dyn CheckoutAppContext>>>(
        scope,
        Rc::new(RefCell::new(CheckoutAppContextImpl {
            stashed_checkout_context: None,
        })),
    );
}

#[cfg(test)]
mod tests {
    use core::assert_eq;

    use ignx_compositron::{app::launch_only_app_context, reactive::store_value};
    use rstest::rstest;
    use serial_test::serial;

    use crate::{
        metrics::{expect_metric, MockMetricEmitter as MetricEmitter},
        network::types::workflow_v2_raw_response::WorkflowV2ServiceData,
    };

    use super::*;

    #[rstest]
    #[serial]
    fn stash_current_checkout_context_should_pass_if_checkout_context_is_missing() {
        launch_only_app_context(|ctx| {
            let emit_context = MetricEmitter::emit_context();
            expect_metric(
                &emit_context,
                "Checkout.Error",
                1.0,
                vec![("errorType", "CheckoutAppContextNotFound")],
            );
            let workflow_stages = store_value(ctx.scope(), vec![]);
            stash_current_checkout_context(ctx.scope(), workflow_stages);
            assert_eq!(workflow_stages.get_value().len(), 0);
        })
    }

    #[rstest]
    #[serial]
    fn restore_checkout_context_should_pass_if_checkout_context_is_missing() {
        launch_only_app_context(|ctx| {
            let emit_context = MetricEmitter::emit_context();
            expect_metric(
                &emit_context,
                "Checkout.Error",
                1.0,
                vec![("errorType", "CheckoutAppContextNotFound")],
            );
            let workflow_stages = store_value(ctx.scope(), vec![]);
            restore_checkout_context(ctx.scope(), Uuid::new_v4(), workflow_stages);
            assert_eq!(workflow_stages.get_value().len(), 0);
        })
    }

    #[test]
    fn should_not_restore_context_with_different_identifier() {
        launch_only_app_context(|ctx| {
            let workflow_stage = WorkflowStage {
                workflow_data: WorkflowV2ServiceData {
                    data: "data".to_string(),
                    version: "version".to_string(),
                },
                options: vec![],
            };

            provide_checkout_app_context(ctx.scope());

            let workflow_stages = store_value(ctx.scope(), vec![]);
            workflow_stages.set_value(vec![workflow_stage.clone()]);

            stash_current_checkout_context(ctx.scope(), workflow_stages);

            let new_workflow_stages = store_value(ctx.scope(), vec![]);

            let different_identifier = Uuid::new_v4();
            restore_checkout_context(ctx.scope(), different_identifier, new_workflow_stages);

            assert_eq!(new_workflow_stages.get_value(), vec![]);
        })
    }

    #[test]
    fn should_stash_and_restore_context_with_same_idenfifier() {
        launch_only_app_context(|ctx| {
            let workflow_stage = WorkflowStage {
                workflow_data: WorkflowV2ServiceData {
                    data: "data".to_string(),
                    version: "version".to_string(),
                },
                options: vec![],
            };

            provide_checkout_app_context(ctx.scope());

            let workflow_stages = store_value(ctx.scope(), vec![]);
            workflow_stages.set_value(vec![workflow_stage.clone()]);

            let uuid = stash_current_checkout_context(ctx.scope(), workflow_stages)
                .expect("should generate context uuid");

            let new_workflow_stages = store_value(ctx.scope(), vec![]);

            restore_checkout_context(ctx.scope(), uuid, new_workflow_stages);

            assert_eq!(new_workflow_stages.get_value(), vec![workflow_stage]);
        })
    }
}
