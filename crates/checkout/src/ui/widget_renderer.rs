use std::rc::Rc;

use ignx_compositron::prelude::*;
use ignx_compositron::{compose, prelude::AppContex<PERSON>, Composer};
use safe::StackComposable;

use crate::checkout_controller::CheckoutController;
use crate::network::types::checkout_view_data::view_data::ViewData;

use crate::ui::widgets::bolt_confirmation_page::*;
use crate::ui::widgets::bolt_confirmation_page_v2::*;

#[Composer]
pub fn RenderWidget(
    ctx: &AppContext,
    view_data: ViewData,
    checkout_controller: Rc<CheckoutController>,
) -> StackComposable<'static> {
    match view_data {
        ViewData::ConfirmationBoltSingleTierViewData(view_data) => {
            compose! {
                BoltConfirmationPage(view_data, checkout_controller)
            }
        }
        ViewData::ConfirmationBoltSingleTierViewDataV2(view_data) => {
            compose! {
                BoltConfirmationPageV2(view_data, checkout_controller)
            }
        }
    }
}
