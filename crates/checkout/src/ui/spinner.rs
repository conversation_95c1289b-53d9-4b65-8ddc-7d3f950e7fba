use fableous::{spinner::*, SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::accessbility::CustomAccessibilityBorderStyle;
use ignx_compositron::time::{Duration, Instant};
use ignx_compositron::{compose, compose_option, prelude::*};
use ignx_compositron::{prelude::App<PERSON>ontex<PERSON>, Composer};
use safe::StackComposable;

#[Composer]
pub fn RenderSpinner(ctx: &AppContext) -> StackComposable<'static> {
    let spinner_signal = create_rw_signal(ctx.scope(), false);

    ctx.schedule_task(Instant::now() + Duration::from_millis(2000), move || {
        spinner_signal.set(true);
    });

    compose! {
        Stack() {
            Column() {
                // Ghost focus element to prevent utility
                // nav grabbing focus when loading
                Rectangle()
                    .opacity(0.0)
                    .focusable()
                    .accessibility_highlight_disabled(true)
                    .accessibility_highlight_custom(CustomAccessibilityBorderStyle {
                        width: 0.0,
                        color: Default::default(),
                        padding: 0.0
                    })

                    Memo(item_builder: Box::new(move |ctx| {
                        spinner_signal.with(|should_show_spinner| {
                            if *should_show_spinner {
                                compose_option! { Spinner().test_id("spinner") }
                            } else {
                                None
                            }
                        })
                    }))
            }
            .main_axis_alignment(MainAxisAlignment::Center)
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .test_id("spinner_container")
        }
    }
}

#[cfg(test)]
mod tests {

    use ignx_compositron::{
        app::launch_test,
        compose,
        test_utils::{assert_node_does_not_exist, assert_node_exists},
    };

    use ignx_compositron::time::MockClock;

    use super::*;

    #[test]
    fn renders_no_spinner_before_two_seconds() {
        launch_test(
            move |ctx| {
                compose! {
                    RenderSpinner()
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_once().node_tree;

                let spinner_container = tree.find_by_test_id("spinner_container");
                assert_node_exists!(&spinner_container);

                let spinner = tree.find_by_test_id("spinner");
                assert_node_does_not_exist!(&spinner);
            },
        );
    }

    #[test]
    fn renders_spinner_after_two_seconds() {
        launch_test(
            move |ctx| {
                compose! {
                    RenderSpinner()
                }
            },
            move |_scope, mut test_loop| {
                MockClock::advance(Duration::from_millis(2100));

                let tree = test_loop.tick_until_idle();
                let spinner_container = tree.find_by_test_id("spinner_container");
                assert_node_exists!(&spinner_container);

                let spinner = tree.find_by_test_id("spinner");
                assert_node_exists!(&spinner);
            },
        );
    }
}
