use amzn_fable_tokens::FableSpacing;
use ignx_compositron::prelude::safe::MaybeSignal;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, compose_option, Composer};
use safe::{LocalizedText, TextContent};

use std::rc::Rc;

use fableous::buttons::primary_button::*;
use fableous::typography::typography::*;

use lrc_image::lrc_image::*;
use lrc_image::types::ImageAlignment;
use lrc_image::types::ImageData;
use lrc_image::types::*;

use crate::ui::widgets::utils::background_image::*;
use crate::checkout_controller::CheckoutControl;
use crate::network::types::checkout_view_data::common::{Buttons, ButtonsParsing, CheckoutButtonParsing};
use crate::network::types::checkout_view_data::common::CheckoutButton;
use crate::network::types::checkout_view_data::confirmation_page_view_data::bolt_single_tier_confirmation::ConfirmationBoltSingleTierViewData;
use crate::network::types::checkout_view_data::lrd_common::{ImageProperties, ImagePropertiesContainer, LrdContent, LrdView, TextProperties, TextPropertiesContainer, TextRowPropertiesContainer};
use crate::utils::tts::TTSExtractable;

const TOP_PADDING: f32 = 144.0;
const SIDE_PADDING: f32 = 144.0;
const BOTTOM_PADDING: f32 = 54.0;

const LEFT_COL_WIDTH: f32 = 687.0;
const RIGHT_COL_WIDTH: f32 = 945.0;

const VERTICAL_GUTTER: f32 = 45.0;

const PARAGRAPH_SPACING: f32 = FableSpacing::SPACING050;
const BUTTON_SPACING: f32 = FableSpacing::SPACING040;

pub const BOLT_CONFIRMATION_PAGE_TEST_ID: &str = "bolt-confirmation-page";

#[Composer]
fn RenderTextContent(
    ctx: &AppContext,
    properties: &TextProperties,
    item_path: &str,
) -> ColumnComposable {
    let type_ramp = properties.get_fable_type_ramp();
    let paragraphs = properties.get_paragraphs();
    let color = properties.get_color();
    let top_margin = properties.get_top_margin();
    let start_margin = properties.get_start_margin();
    let test_id = properties.get_test_id(item_path, "text-content");
    let font_weight = properties
        .get_font_weight()
        .unwrap_or(type_ramp.font_weight);

    if paragraphs.iter().all(String::is_empty) {
        return compose! {
            Column() {}
                .test_id(test_id)
        };
    }

    let list_builder = Rc::new(move |ctx: &AppContext, item: &String, _idx: usize| {
        let type_ramp = MaybeSignal::Static(type_ramp.clone());

        compose! {
            Row() {
                Typography(content: item.clone(), color, type_ramp)
                    .font_weight(MaybeSignal::Static(font_weight))
            }
            .padding(Padding{top: PARAGRAPH_SPACING, start: 0.0, end: 0.0, bottom: 0.0})
        }
    });

    compose! {
        Column() {
            ColumnList(
                item_builder: list_builder.clone(),
                items: paragraphs,
                id: |item| item,
            )
        }.padding(Padding {
            top: top_margin,
            start: start_margin,
            end: 0.0,
            bottom: 0.0,
        }).test_id(test_id)
    }
}

#[Composer]
fn ImageContent(ctx: &AppContext, properties: &ImageProperties, item_path: &str) -> MemoComposable {
    let url = properties.get_image_url();
    let width = properties.get_width();
    let height = properties.get_height();
    let test_id = properties.get_test_id(item_path, "image-content");

    compose! {
        Memo(item_builder: Box::new(move |ctx| {
            compose_option! {
                LRCImage(data: ImageData {
                    url: url.clone(),
                    width,
                    height,
                    tags: vec![
                        ImageTag::Scaling(
                            ScalingStrategy::ScaleToRectangle(
                                ScaleToRectangleOptions {
                                    alignment: ImageAlignment::Centered,
                                    hide_canvas: true
                                }
                            )
                        ),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .test_id(test_id.clone())
            }
        }))
    }
}

#[Composer]
fn RenderTextRow(
    ctx: &AppContext,
    text_properties: &Vec<TextProperties>,
    item_path: String,
) -> RowComposable {
    let item_builder = Rc::new(
        move |ctx: &AppContext, (_, item_properties): &(usize, TextProperties), idx: usize| {
            compose! {
                RenderTextContent(properties: item_properties, item_path: &format!("{}-{}", item_path, idx))
            }
        },
    );

    let text_properties_with_index: Vec<(usize, TextProperties)> =
        text_properties.iter().cloned().enumerate().collect();

    compose! {
        Row() {
            RowList(
                item_builder,
                items: text_properties_with_index,
                id: |(idx, _)| idx,
            )
            .cross_axis_alignment(CrossAxisAlignment::End)
        }
    }
}

#[Composer]
fn RenderColumnView(
    ctx: &AppContext,
    view: &Option<LrdView>,
    section_name: String,
) -> ColumnComposable {
    let list_builder = Rc::new(
        move |ctx: &AppContext, (_, item): &(usize, LrdContent), idx: usize| {
            let item_path = format!("{}-{}", section_name, idx);
            match item {
                LrdContent::TextRow(TextRowPropertiesContainer { properties }) => compose! {
                    Row() {
                        RenderTextRow(text_properties: properties, item_path)
                    }
                },
                LrdContent::Text(TextPropertiesContainer { properties }) => compose! {
                    Row() {
                        RenderTextContent(properties, item_path: &item_path)
                    }
                },
                LrdContent::Image(ImagePropertiesContainer { properties }) => compose! {
                    Row() {
                        ImageContent(properties, item_path: &item_path)
                    }
                },
            }
        },
    );

    let items_with_index: Vec<(usize, LrdContent)> = view.as_ref().map_or_else(Vec::new, |view| {
        view.content.iter().cloned().enumerate().collect()
    });

    compose! {
        Column() {
            ColumnList(
                item_builder: list_builder.clone(),
                items: items_with_index,
                id: |(idx, _)| idx,
            )
        }
    }
}

#[Composer]
fn RenderButtonView(
    ctx: &AppContext,
    view: &Buttons,
    section_name: String,
    checkout_controller: Rc<dyn CheckoutControl>,
    accessibility_context_messages: Vec<TextContent>,
) -> ColumnComposable {
    let items = view.buttons.clone();
    let is_horizontal = view.is_horizontal_layout();

    let list_builder = Rc::new(move |ctx: &AppContext, item: &CheckoutButton, idx: usize| {
        let test_id = item.get_test_id(&section_name, idx);
        let button_variant = item.to_button_variant();
        let checkout_controller = checkout_controller.clone();
        let option_id = item.workflow_action.option_id.clone();
        let button_tts = item.extract_specific_tts();
        let accessibility_context_messages = accessibility_context_messages.clone();

        compose! {
            Memo(item_builder: Box::new(move |ctx| {
                let option_id = option_id.clone();
                let test_id = test_id.clone();
                let button_tts = button_tts.clone();
                let accessibility_context_messages = accessibility_context_messages.clone();

                button_variant.as_ref().and_then(|variant| {
                    compose_option! {
                        Row() {
                            PrimaryButton(variant: variant.clone()).focusable().on_select({
                                let checkout_controller = checkout_controller.clone();
                                move || {
                                    checkout_controller.take_step(&option_id)
                                }
                            })
                                .accessibility_role(LocalizedText::new("AIV_BLAST_TTS_ROLE_BUTTON"))
                                .accessibility_description(button_tts.unwrap_or(TextContent::String("".to_string())))
                                .accessibility_context_messages(accessibility_context_messages)
                                .test_id(test_id.clone())
                        }.padding(Padding{end: BUTTON_SPACING, bottom: BUTTON_SPACING, start: 0.0, top: 0.0 })
                    }
                })
            }))
        }
    });

    if is_horizontal {
        compose! {
            Column() {
                RowList(
                    item_builder: list_builder,
                    items: items.clone(),
                    id: |item| item,
                )
            }
        }
    } else {
        compose! {
            Column() {
                ColumnList(
                    item_builder: list_builder,
                    items: items.clone(),
                    id: |item| item,
                )
            }
        }
    }
}

#[Composer]
pub fn BoltConfirmationPage(
    ctx: &AppContext,
    view_data: ConfirmationBoltSingleTierViewData,
    checkout_controller: Rc<dyn CheckoutControl>,
) -> StackComposable {
    let view_data = Rc::new(view_data);

    let view_data_for_memo = Rc::clone(&view_data);
    let accessibility_context_messages = create_memo(ctx.scope(), move |_| {
        view_data_for_memo.extract_context_tts()
    });

    let background_image_url = view_data.background_image.clone();

    compose! {
        Stack() {

            BackgroundImage(background_image_url, section_name: BOLT_CONFIRMATION_PAGE_TEST_ID)

            Column() {
                Row() {
                    RenderColumnView(view: &view_data.top_view, section_name: format!("{}-top-section", BOLT_CONFIRMATION_PAGE_TEST_ID))
                }
                .main_axis_size(MainAxisSize::Max)
                .main_axis_alignment(MainAxisAlignment::Start)
                .width(LEFT_COL_WIDTH)

                Row() {}
                    .height(VERTICAL_GUTTER)
                    .main_axis_size(MainAxisSize::Max)
                Row() {
                    Column() {
                        RenderColumnView(view: &view_data.left_section, section_name: format!("{}-left-section", BOLT_CONFIRMATION_PAGE_TEST_ID))
                    }
                    .main_axis_size(MainAxisSize::Max)
                    .main_axis_alignment(MainAxisAlignment::Start)
                    .padding(Padding{end: FableSpacing::SPACING200, start: 0.0, bottom: 0.0, top: 0.0})
                    .width(LEFT_COL_WIDTH)

                    Column() {
                        RenderColumnView(view: &view_data.right_section_top_view, section_name: format!("{}-right-section-top", BOLT_CONFIRMATION_PAGE_TEST_ID))

                        RenderButtonView(
                            view: &view_data.right_section_buttons,
                            section_name: format!("{}-right-section-buttons", BOLT_CONFIRMATION_PAGE_TEST_ID),
                            checkout_controller,
                            accessibility_context_messages: accessibility_context_messages.get() //FIXME either use get_untracked() or make this reactive
                        )
                        .padding(Padding { start: 0.0, end: 0.0, top: FableSpacing::SPACING200, bottom: 0.0 })

                        RenderColumnView(view: &view_data.right_section_bottom_view, section_name: format!("{}-right-section-bottom", BOLT_CONFIRMATION_PAGE_TEST_ID))
                    }
                    .main_axis_size(MainAxisSize::Max)
                    .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
                    .padding(Padding{start: FableSpacing::SPACING100, bottom: 0.0, end: 0.0, top: 0.0})
                    .width(RIGHT_COL_WIDTH)
                }
                .flex(1.0)
                .main_axis_size(MainAxisSize::Max)
                .main_axis_alignment(MainAxisAlignment::SpaceBetween)
                .cross_axis_alignment(CrossAxisAlignment::Start)

                Row() {}
                    .height(VERTICAL_GUTTER)
                    .main_axis_size(MainAxisSize::Max)
                Row() {
                    RenderColumnView(view: &view_data.bottom_view, section_name: format!("{}-bottom-section", BOLT_CONFIRMATION_PAGE_TEST_ID))
                }
                .main_axis_size(MainAxisSize::Max)

            }
            .main_axis_size(MainAxisSize::Max)
            .main_axis_alignment(MainAxisAlignment::SpaceBetween)
            .padding(Padding{start: SIDE_PADDING, end: SIDE_PADDING, top: TOP_PADDING, bottom: BOTTOM_PADDING })
        }
        .test_id(BOLT_CONFIRMATION_PAGE_TEST_ID)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::network::types::checkout_view_data::{
        view_data::ViewData, view_data_parser::view_data_parser,
    };
    use crate::network::{
        checkout_network::MockNetworkClient, test_utils::create_empty_test_checkout_controller,
    };
    use ignx_compositron::test_utils::{assert_node_does_not_exist, ComposableType};
    use ignx_compositron::{
        app::launch_test, compose, lazy_static::lazy_static, test_utils::assert_node_exists,
    };
    use media_background::types::{FullscreenBackgroundData, MediaBackgroundType};
    use network::RequestError;

    lazy_static! {
        pub static ref VIEW_DATA: Result<ViewData, RequestError> = view_data_parser(include_str!("../../network/test_data/bolt_confirmation_page_view_data/single_tier_bolt_confirmation_page_1883.json").to_owned());
        pub static ref BOLT_VIEW_DATA: ConfirmationBoltSingleTierViewData = match &*VIEW_DATA {
            Ok(ViewData::ConfirmationBoltSingleTierViewData(data)) => data.clone(),
            _ => panic!("Expected ConfirmationBoltSingleTierViewData"),
        };
        pub static ref VIEW_DATA_NO_TEST_IDS: Result<ViewData, RequestError> = view_data_parser(include_str!("../../network/test_data/bolt_confirmation_page_view_data/single_tier_bolt_confirmation_page_1883_no_test_ids.json").to_owned());
        pub static ref BOLT_VIEW_DATA_NO_TEST_IDS: ConfirmationBoltSingleTierViewData = match &*VIEW_DATA_NO_TEST_IDS {
            Ok(ViewData::ConfirmationBoltSingleTierViewData(data)) => data.clone(),
            _ => panic!("Expected ConfirmationBoltSingleTierViewData"),
        };
    }

    #[test]
    fn it_renders_bolt_confirmation_page() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let root = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID);
                assert_node_exists!(&root);
            },
        );
    }

    #[test]
    fn it_renders_does_not_render_empty_text() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let empty_text =
                    tree.find_by_test_id("CONFIRMATION_PAGE_WIDGET_CHANNEL+EMPTY_TEXT");
                assert_node_exists!(&empty_text);
                let child = empty_text
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_first();
                assert_node_does_not_exist!(&child);
            },
        );
    }

    #[test]
    fn it_renders_logo_in_top_view() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let logo = tree.find_by_test_id("CONFIRMATION_PAGE_WIDGET_CHANNEL+LOGO_IMAGE");
                assert_node_exists!(&logo);
            },
        );
    }

    #[test]
    fn it_renders_text_row_in_right_view() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let due_today = tree.find_by_test_id("CONFIRMATION_PAGE_WIDGET_CHANNEL+DUE_TODAY");
                assert_node_exists!(&due_today);
                let due_today_amount =
                    tree.find_by_test_id("CONFIRMATION_PAGE_WIDGET_CHANNEL+DUE_TODAY_AMOUNT");
                assert_node_exists!(&due_today_amount);

                // both due today and due today amount should be on the same row so the bottom of each node should be the same y coordinate
                assert_eq!(
                    due_today.get_props().layout.border_box.bottom(),
                    due_today_amount.get_props().layout.border_box.bottom()
                );
            },
        );
    }

    #[test]
    fn it_renders_body_content() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let body = tree.find_by_test_id("CONFIRMATION_PAGE_WIDGET_CHANNEL+BODY");
                assert_node_exists!(&body);
            },
        );
    }

    #[test]
    fn it_renders_button() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let button = tree.find_by_test_id("CONFIRMATION_PAGE_WIDGET_CHANNEL+BUTTON");
                assert_node_exists!(&button);
            },
        );
    }

    #[test]
    fn it_renders_terms_text() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let terms_text =
                    tree.find_by_test_id("CONFIRMATION_PAGE_WIDGET_CHANNEL+TERMS_TEXT");
                assert_node_exists!(&terms_text);
            },
        );
    }

    #[test]
    fn it_renders_background_image_when_mediabackground_is_empty() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let media_background_type_sig =
                    create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                provide_context::<RwSignal<MediaBackgroundType>>(
                    ctx.scope(),
                    media_background_type_sig,
                );

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let background_image =
                    tree.find_by_test_id("bolt-confirmation-page-background-image");
                assert_node_exists!(&background_image);
            },
        );
    }

    #[test]
    fn it_does_not_render_background_image_when_mediabackground_is_not_empty() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let media_background_type_sig = create_rw_signal(
                    ctx.scope(),
                    MediaBackgroundType::FullscreenBackground(FullscreenBackgroundData {
                        id: "test".to_string(),
                        image_url: "url".to_string(),
                        enter_immediately: true,
                        image_opacity_percentage: 255,
                    }),
                );
                provide_context::<RwSignal<MediaBackgroundType>>(
                    ctx.scope(),
                    media_background_type_sig,
                );

                compose! {
                    BoltConfirmationPage(view_data: BOLT_VIEW_DATA.clone(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let background_image =
                    tree.find_by_test_id("bolt-confirmation-page-background-image");
                assert_node_does_not_exist!(&background_image);
            },
        );
    }

    mod autogenerated_test_ids {
        use super::*;
        lazy_static! {
            pub static ref VIEW_DATA_NO_TEST_IDS: Result<ViewData, RequestError> = view_data_parser(include_str!("../../network/test_data/bolt_confirmation_page_view_data/single_tier_bolt_confirmation_page_1883_no_test_ids.json").to_owned());
            pub static ref BOLT_VIEW_DATA_NO_TEST_IDS: ConfirmationBoltSingleTierViewData = match &*VIEW_DATA_NO_TEST_IDS {
                Ok(ViewData::ConfirmationBoltSingleTierViewData(data)) => data.clone(),
                _ => panic!("Expected ConfirmationBoltSingleTierViewData"),
            };
        }

        #[test]
        fn it_generates_correct_default_test_id_for_logo_in_top_view() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPage(view_data: BOLT_VIEW_DATA_NO_TEST_IDS.clone(), checkout_controller)
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_idle();

                    let logo =
                        tree.find_by_test_id("bolt-confirmation-page-top-section-0-image-content");
                    assert_node_exists!(&logo);
                },
            );
        }

        #[test]
        fn it_generates_correct_default_test_ids_for_left_section() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPage(view_data: BOLT_VIEW_DATA_NO_TEST_IDS.clone(), checkout_controller)
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_idle();

                    let mut node =
                        tree.find_by_test_id("bolt-confirmation-page-left-section-0-text-content");
                    assert_node_exists!(&node);
                    node =
                        tree.find_by_test_id("bolt-confirmation-page-left-section-1-text-content");
                    assert_node_exists!(&node);
                },
            );
        }

        #[test]
        fn it_generates_correct_default_test_ids_for_right_section_top() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPage(view_data: BOLT_VIEW_DATA_NO_TEST_IDS.clone(), checkout_controller)
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_idle();

                    let mut node = tree
                        .find_by_test_id("bolt-confirmation-page-right-section-top-0-text-content");
                    assert_node_exists!(&node);
                    node = tree
                        .find_by_test_id("bolt-confirmation-page-right-section-top-1-text-content");
                    assert_node_exists!(&node);
                    node = tree
                        .find_by_test_id("bolt-confirmation-page-right-section-top-2-text-content");
                    assert_node_exists!(&node);
                },
            );
        }

        #[test]
        fn it_generates_correct_default_test_ids_for_right_section_buttons() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPage(view_data: BOLT_VIEW_DATA_NO_TEST_IDS.clone(), checkout_controller)
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_idle();

                    let node = tree
                        .find_by_test_id("bolt-confirmation-page-right-section-buttons-button-0");
                    assert_node_exists!(&node);
                },
            );
        }

        #[test]
        fn it_generates_correct_default_test_ids_for_bottom_section() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPage(view_data: BOLT_VIEW_DATA_NO_TEST_IDS.clone(), checkout_controller)
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_idle();

                    let node = tree
                        .find_by_test_id("bolt-confirmation-page-bottom-section-0-text-content");
                    assert_node_exists!(&node);
                },
            );
        }
    }

    mod tts {
        use super::*;
        use crate::network::types::checkout_view_data::{common::*, lrd_common::TextProperties};

        use safe::TextContent;
        use std::rc::Rc;

        #[test]
        fn test_bolt_confirmation_page_tts_extraction() {
            let view_data = ConfirmationBoltSingleTierViewData {
                top_view: Some(create_sample_lrd_view("Top section text")),
                left_section: Some(create_sample_lrd_view(
                    "Left section text\n\nLeft section second line",
                )),
                right_section_top_view: Some(create_sample_lrd_view("Right top section text")),
                right_section_buttons: Buttons {
                    layout: Layout::Horizontal,
                    buttons: vec![
                        create_sample_button("Button 1"),
                        create_sample_button("Button 2"),
                    ],
                },
                right_section_bottom_view: Some(create_sample_lrd_view(
                    "Right bottom section text",
                )),
                bottom_view: Some(create_sample_lrd_view("Bottom section text")),
                background_image: None,
            };

            let extracted_tts = view_data.extract_context_tts();

            assert!(extracted_tts.iter().any(
                |content| matches!(content, TextContent::String(text) if text == "Top section text")
            ));
            assert!(extracted_tts.iter().any(|content| matches!(content, TextContent::String(text) if text == "Left section text")));
            assert!(extracted_tts.iter().any(|content| matches!(content, TextContent::String(text) if text == "Left section second line")));
            assert!(extracted_tts.iter().any(|content| matches!(content, TextContent::String(text) if text == "Right top section text")));
            assert!(extracted_tts.iter().any(|content| matches!(content, TextContent::String(text) if text == "Right bottom section text")));
            assert!(extracted_tts.iter().any(|content| matches!(content, TextContent::String(text) if text == "Bottom section text")));
        }

        #[test]
        fn test_button_tts_extraction() {
            let button = create_sample_button("Click me");
            let extracted_tts = button.extract_specific_tts();

            assert!(extracted_tts.is_some());
            match extracted_tts {
                Some(TextContent::String(text)) => assert_eq!(text, "Click me"),
                _ => panic!("Unexpected TTS content extracted"),
            }
        }

        #[test]
        fn test_bolt_confirmation_page_accessibility_props() {
            launch_test(
                |ctx| {
                    let view_data = create_sample_confirmation_data();
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPage(
                            view_data: view_data,
                            checkout_controller: checkout_controller,
                        )
                    }
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    let button_one = node_tree.find_by_test_id("test-button-Watch Now");
                    assert_node_exists!(&button_one);
                    let button_two = node_tree.find_by_test_id("test-button-Return to Home");
                    assert_node_exists!(&button_two);

                    for button in vec![&button_one, &button_two] {
                        let props = button.clone().get_props();

                        let accessibility_data = props.accessibility;
                        assert!(accessibility_data.is_some());

                        if let Some(data) = accessibility_data {
                            assert_eq!(
                                data.role,
                                (
                                    "".to_string(),
                                    Some(LocalizedText::new("AIV_BLAST_TTS_ROLE_BUTTON"))
                                )
                            );
                            assert!(data.descriptions.len() > 0);
                            assert!(data.context_messages.len() > 0);
                        }
                    }
                },
            );
        }

        fn create_sample_lrd_view(text: &str) -> LrdView {
            LrdView {
                width: None,
                content: vec![LrdContent::Text(TextPropertiesContainer {
                    properties: TextProperties {
                        typography: Some("body-200".to_string()),
                        content: text.to_string(),
                        test_id: None,
                        styling: None,
                        color: None,
                        font_weight: None,
                    },
                })],
            }
        }

        fn create_sample_button(text: &str) -> CheckoutButton {
            CheckoutButton {
                text_lines: vec![text.to_string()],
                size: "size-400".to_string(),
                workflow_action: WorkflowAction {
                    action_type: String::from("select_option"),
                    option_id: String::from("test-option"),
                    confirmation_modal: ConfirmationModal {
                        message_body: None,
                        cancel_button_text: None,
                        accept_button_text: None,
                        title: None,
                    },
                },
                test_id: Some(format!("test-button-{}", text)),
            }
        }

        fn create_sample_confirmation_data() -> ConfirmationBoltSingleTierViewData {
            ConfirmationBoltSingleTierViewData {
                top_view: Some(create_sample_lrd_view("Thank you for your purchase")),
                left_section: Some(create_sample_lrd_view("Order details")),
                right_section_top_view: Some(create_sample_lrd_view("Subscription information")),
                right_section_buttons: Buttons {
                    layout: Layout::Horizontal,
                    buttons: vec![
                        create_sample_button("Watch Now"),
                        create_sample_button("Return to Home"),
                    ],
                },
                right_section_bottom_view: Some(create_sample_lrd_view("Additional information")),
                bottom_view: Some(create_sample_lrd_view("Copyright information")),
                background_image: None,
            }
        }
    }
}
