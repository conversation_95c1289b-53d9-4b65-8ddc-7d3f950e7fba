use amzn_fable_tokens::{FableColor, FableIcon, FableSize, FableSpacing};
use fableous::buttons::{checkbox_button::*, primary_button::*};
use fableous::font_icon::*;
use fableous::inputs::radio::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::*;
use ignx_compositron::show::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};

use lrc_image::lrc_image::*;
use lrc_image::types::ImageAlignment;
use lrc_image::types::*;
use std::rc::Rc;

use crate::ui::widgets::utils::background_image::*;
use fableous::typography::typography::*;

use crate::network::types::checkout_view_data::view_data_v2_common::action::ActionPointer;
use crate::checkout_controller::CheckoutControl;
use crate::network::types::checkout_view_data::confirmation_page_view_data::bolt_single_tier_confirmation_v2::ConfirmationBoltSingleTierViewDataV2;
use crate::network::types::checkout_view_data::view_data_v2_common::legal::{DisclosureKey, Disclosures, Legal};
use crate::network::types::checkout_view_data::view_data_v2_common::product::{Product, OfferDetails, TransactableOffer};
use crate::network::types::checkout_view_data::view_data_v2_common::payment::PresentablePayment;

const TOP_PADDING: f32 = 144.0;
const SIDE_PADDING: f32 = 144.0;
const BOTTOM_PADDING: f32 = 54.0;

const LEFT_COL_WIDTH: f32 = 650.0;
const RIGHT_COL_WIDTH: f32 = 946.0;

const HEADER_HEIGHT: f32 = 66.0;
const VERTICAL_GUTTER: f32 = 55.0;

const PARAGRAPH_SPACING: f32 = FableSpacing::SPACING050;
const BUTTON_SPACING: f32 = FableSpacing::SPACING113;

const DISCLOSURE_MAX_WIDTH: f32 = 901.;

const RIGHT_RAIL_BUTTONS_MIN_HEIGHT: f32 = 98.;
const CONFIRMATION_BUTTON_MAX_WIDTH: f32 = 406.;

const RADIO_MAX_WIDTH: f32 = 600.0;

const HEADER_LOGO_MAX_HEIGHT: f32 = 66.0;
const HEADER_LOGO_MAX_WIDTH: f32 = 650.0;

pub const BOLT_CONFIRMATION_PAGE_TEST_ID_V2: &str = "bolt-confirmation-page-v2";

pub const BOLT_CONFIRMATION_AUTO_RENEWAL_BUTTON_TEST_ID: &str = "auto-renewal-button";
pub const BOLT_CONFIRMATION_CONFIRM_BUTTON_TEST_ID: &str = "confirm-order-button";

#[Composer]
pub fn RenderHeader(
    ctx: &AppContext,
    view_data: &ConfirmationBoltSingleTierViewDataV2,
    section_name: &str,
) -> ImageComposable {
    let url = view_data.main_product.images.logo.url.clone();

    compose! {
        LRCImage(
            data: ImageData {
                url,
                height: HEADER_LOGO_MAX_HEIGHT,
                width: HEADER_LOGO_MAX_WIDTH,
                tags: vec![
                    ImageTag::Scaling(
                        ScalingStrategy::ScaleToRectangle(
                            ScaleToRectangleOptions {
                                alignment: ImageAlignment::Left,
                                hide_canvas: true
                            }
                        )
                    ),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            },
            dimensions_type: DimensionsType::MAX
        )
        .test_id(format!("{}-{}", section_name, "logo-image"))
    }
}

#[Composer]
fn RenderLeftRailCopyVariant(
    ctx: &AppContext,
    product: &Product,
    section_name: &str,
) -> ColumnComposable {
    let content = product.description.as_deref().unwrap_or_default();

    compose! {
        Column() {
            TypographyBody400(content).font_weight(FontWeight::SemiBold).max_width(650.0).test_id(format!("{}-{}", section_name, "copy-variant"))
        }
    }
}

fn create_radio_button_data(offer: &OfferDetails, test_id: String) -> RadioButtonData {
    RadioButtonData {
        heading: TextContent::from(offer.plan_name.clone()).into(),
        subtext: TextContent::from(
            offer
                .pricing
                .full_price_description
                .clone()
                .unwrap_or_default(),
        )
        .into(),
        test_id,
    }
}

#[Composer]
fn RenderLeftRailRadioVariant(
    ctx: &AppContext,
    selected_offer: RwSignal<TransactableOffer>,
    product: &Product,
    section_name: &str,
) -> ColumnComposable {
    let mut buttons: Vec<RadioButtonData> = Vec::new();
    let mut offers: Vec<TransactableOffer> = Vec::new();

    let primary_offer = product.primary_offer.clone();
    buttons.push(create_radio_button_data(
        &primary_offer,
        format!("{}-{}", section_name, "primary-offer-radio-button"),
    ));
    offers.push(selected_offer.get_untracked());

    if let Some(secondary_offer) = &product.secondary_offer {
        buttons.push(create_radio_button_data(
            &secondary_offer.offer,
            format!("{}-{}", section_name, "secondary-offer-radio-button"),
        ));
        offers.push(secondary_offer.clone());
    }

    let on_item_selected = Rc::new(move |selected: usize| {
        if let Some(transactable_offer) = offers.get(selected) {
            selected_offer.set(transactable_offer.clone())
        }
    });

    compose! {
        RadioButtonGroup(
            items: buttons,
            on_item_selected,
            preselect_index: Some(0)
        )
        .test_id(format!("{}-{}", section_name, "radio-button-group"))
        .focus_area(FocusAreaEntryMode::Memo)
        .max_width(RADIO_MAX_WIDTH)
    }
}

pub enum LeftRailVariants {
    Copy,
    RadioButtons,
}

fn get_left_rail_variant(product: &Product) -> LeftRailVariants {
    if product.secondary_offer.is_some() {
        return LeftRailVariants::RadioButtons;
    }
    LeftRailVariants::Copy
}

#[Composer]
fn RenderLeftRail(
    ctx: &AppContext,
    product: &Product,
    selected_offer: RwSignal<TransactableOffer>,
    section_name: &str,
) -> ColumnComposable {
    match get_left_rail_variant(product) {
        LeftRailVariants::Copy => compose! {
            RenderLeftRailCopyVariant(product, section_name),
        },
        LeftRailVariants::RadioButtons => compose! {
            RenderLeftRailRadioVariant(product, selected_offer, section_name),
        },
    }
}

#[Composer]
fn RenderAutoRenewalDisclosureError(
    ctx: &AppContext,
    legal: &Legal,
    section_name: String,
) -> ShowComposable {
    let content = legal.disclosures.as_ref().and_then(|disclosures_map| {
        disclosures_map
            .get(DisclosureKey::AUTO_RENEWAL)
            .and_then(|disclosure| disclosure.error_message.clone())
    });

    let condition = {
        let error_signal = use_renewal_consent_error_signal(ctx.scope());
        let content_is_some = content.is_some();
        Signal::derive(ctx.scope(), move || {
            error_signal.with(|signal| *signal && content_is_some)
        })
    };

    let content = content.unwrap_or_default();

    compose! {
        if condition.get() {
            Row() {
                FontIcon(icon: FableIcon::ERROR, color: get_ignx_color(FableColor::ERROR), size: FontSize(FableSize::SIZE100 as u32))
                TypographyLabel600(content: content.clone(), color: get_ignx_color(FableColor::ERROR)).font_weight(FontWeight::SemiBold)
            }
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::FOUNDATION_SPACING065))
            .padding(Padding::new(FableSpacing::SPACING133, 0.0, FableSpacing::SPACING125, 0.0))
            .test_id(format!("{}-{}", section_name, "auto-renewal-disclosure-error"))
        }
    }
}

#[Composer]
fn RenderRightRailButtons(
    ctx: &AppContext,
    selected_offer: RwSignal<TransactableOffer>,
    legal: &Legal,
    section_name: &str,
    checkout_controller: Rc<dyn CheckoutControl>,
) -> RowComposable {
    let auto_renewal_button_test_id = format!(
        "{}-{}",
        section_name, BOLT_CONFIRMATION_AUTO_RENEWAL_BUTTON_TEST_ID
    );

    let auto_renewal_disclosure = legal
        .disclosures
        .as_ref()
        .and_then(|d| d.get(DisclosureKey::AUTO_RENEWAL));

    let should_show_auto_renewal = auto_renewal_disclosure
        .as_ref()
        .is_some_and(|d| d.explicit_consent_required);

    let confirm_button_is_enabled = create_rw_signal(ctx.scope(), !should_show_auto_renewal);

    let auto_renewal_button_text: TextContent = auto_renewal_disclosure
        .as_ref()
        .map(|d| d.text.as_str())
        .unwrap_or_default()
        .into();

    let confirm_button_variant = Signal::derive(ctx.scope(), move || {
        selected_offer.with(|offer| {
            PrimaryButtonVariant::TextSize400(
                offer.confirm_order_action.button_text.as_str().into(),
            )
        })
    });

    let error_signal = use_renewal_consent_error_signal(ctx.scope());

    compose! {
        Row() {
            if should_show_auto_renewal {
                CheckboxButton(
                    variant: CheckboxButtonVariant::TextSize400,
                    content: auto_renewal_button_text.clone(),
                    selected: confirm_button_is_enabled
                )
                .on_select(move || error_signal.set(false))
                .test_id(&auto_renewal_button_test_id)
                .preferred_focus(Signal::derive(ctx.scope(), move || {!confirm_button_is_enabled.get()}))
                .cross_axis_alignment(CrossAxisAlignment::Center)
            }

            PrimaryButton(
                variant: confirm_button_variant,
                center_content: true
            ).on_select({
                Box::new(move || {
                    if confirm_button_is_enabled.get_untracked() {
                        let encoded_action_data = selected_offer.with_untracked(|offer| offer.confirm_order_action.action.as_ref().map(|action| action.encoded_action_data.clone()));

                        if let Some(encoded_action_data) = encoded_action_data {
                            checkout_controller.take_step(&encoded_action_data);
                        }
                    } else {
                        error_signal.set(true);
                    }
                })
            })
            .test_id(format!("{}-{}", section_name, BOLT_CONFIRMATION_CONFIRM_BUTTON_TEST_ID))
            .preferred_focus(confirm_button_is_enabled)
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .max_width(CONFIRMATION_BUTTON_MAX_WIDTH)
        }
        .min_height(RIGHT_RAIL_BUTTONS_MIN_HEIGHT)
        .main_axis_size(MainAxisSize::Max)
        .cross_axis_stretch(true)
        .main_axis_alignment(MainAxisAlignment::SpacedBy(BUTTON_SPACING))
    }
}

#[Composer]
fn RenderChangePaymentButton(
    ctx: &AppContext,
    change_payment_action: Option<ActionPointer>,
    section_name: String,
) -> ShowComposable {
    let text_content = change_payment_action.map(|action| action.button_text);

    let condition = text_content.is_some();
    let variant =
        PrimaryButtonVariant::TextSize200(text_content.as_deref().unwrap_or_default().into());
    compose! {
        if condition {
            PrimaryButton(variant: variant.clone()).on_select(Box::new(move || {
                todo!()
            }))
            .test_id(section_name.clone())
        }
    }
}

#[Composer]
fn RenderPriceInfo(
    ctx: &AppContext,
    selected_offer: RwSignal<TransactableOffer>,
    due_today_text: Option<String>,
    item_path: &str,
) -> RowComposable {
    let due_today_with_prerequisites = Signal::derive(ctx.scope(), move || {
        selected_offer.with(|transactable_offer| {
            transactable_offer
                .offer
                .pricing
                .due_today_with_prerequisites
                .clone()
                .unwrap_or_default()
        })
    });

    let due_today_text = due_today_text.unwrap_or_default();

    compose! {
        Row() {
            TypographyHeading400(content: due_today_with_prerequisites)
                .test_id(format!("{}-{}", item_path, "due-today-with-prerequisites"))
            TypographyHeading400(content: due_today_text)
                .test_id(format!("{}-{}", item_path, "due-today-text"))
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING088))
        .cross_axis_alignment(CrossAxisAlignment::End)
    }
}

#[Composer]
fn RenderFullPriceDescriptionSummary(
    ctx: &AppContext,
    renewal_information: Signal<String>,
    item_path: String,
) -> LabelComposable {
    compose! {
        TypographyBody400(
            content: Signal::derive(ctx.scope(), move || { renewal_information.get() }),
            color: get_ignx_color(FableColor::WHITE)
        )
        .font_weight(FontWeight::UltraBold)
        .test_id(format!("{}-{}", item_path, "full-price-description-summary"))
    }
}

#[Composer]
fn RenderPaymentInfo(
    ctx: &AppContext,
    selected_offer: RwSignal<TransactableOffer>,
    payment_method: &PresentablePayment,
    item_path: &str,
) -> ColumnComposable {
    let renewal_information = Signal::derive(ctx.scope(), move || {
        selected_offer.with(|transactable_offer| transactable_offer.offer.renewal_info.clone())
    });

    let full_price_description_next_charge = Signal::derive(ctx.scope(), move || {
        selected_offer.with(|transactable_offer| {
            transactable_offer
                .offer
                .pricing
                .full_price_description_next_charge
                .clone()
        })
    });

    compose! {
        Column() {
            RenderFullPriceDescriptionSummary(renewal_information, item_path: item_path.to_owned())

            Row() {
                TypographyBody400(content: full_price_description_next_charge)
                    .font_weight(FontWeight::SemiBold)
                    .test_id(format!("{}-{}", item_path, "full-price-description-charge"))
                TypographyBody400(content: payment_method.formatted_payment_details_list.first().map(|pd| pd.as_str()).unwrap_or_default())
                    .font_weight(FontWeight::SemiBold)
                    .test_id(format!("{}-{}", item_path, "formatted-payment-details"))
                RenderChangePaymentButton(change_payment_action: payment_method.change_payment_action.clone(), section_name: format!("{}-{}", item_path, "change-payment-button"))
            }
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING075))
            .cross_axis_alignment(CrossAxisAlignment::Center)
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING075))
    }
}

#[Composer]
fn RenderDisclosures(
    ctx: &AppContext,
    disclosures: &Option<Disclosures>,
    section_name: &str,
) -> ColumnComposable {
    let section_name = section_name.to_owned();

    // collect disclosures into Vec<(disclosure_text: String, test_id: String)>
    let items = {
        if let Some(disclosures) = &disclosures {
            disclosures
                .iter()
                .filter(|(_, v)| !v.explicit_consent_required)
                .map(|(k, v)| (v.text.clone(), k.to_lowercase().replace('_', "-")))
                .collect::<Vec<_>>()
        } else {
            vec![]
        }
    };

    let item_builder = Rc::new(
        move |ctx: &AppContext, item: &(String, String), _idx: usize| {
            compose! {
                TypographyLabel100(content: item.0.as_str()).font_weight(FontWeight::Medium).test_id(format!("{}-{}", section_name, item.1))
            }
        },
    );

    compose! {
        ColumnForEach(items, id: |item| &item.1, item_builder)
            .max_width(DISCLOSURE_MAX_WIDTH)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING133))
    }
}

#[Composer]
fn RenderRightRail(
    ctx: &AppContext,
    selected_offer: RwSignal<TransactableOffer>,
    legal: &Legal,
    payment_method: &PresentablePayment,
    section_name: &str,
    checkout_controller: Rc<dyn CheckoutControl>,
) -> ColumnComposable {
    compose! {
        Column() {
            Column() {
                RenderPriceInfo(selected_offer, due_today_text: payment_method.due_today_text.clone(), item_path: &format!("{}-{}", section_name, "price-info"))
                RenderPaymentInfo(selected_offer, payment_method, item_path: &format!("{}-{}", section_name, "payment-info"))
                    .focus_area(FocusAreaEntryMode::Default)
            }.main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING088))

            Column() {
                TypographyLabel100(content: legal.terms_and_conditions.as_str()).font_weight(FontWeight::Medium).test_id(format!("{}-{}", section_name, "terms-and-conditions"))
                RenderDisclosures(disclosures: &legal.disclosures, section_name: &format!("{}-{}", section_name, "disclosures"))
            }.main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING133))

            RenderRightRailButtons(selected_offer, legal, section_name: &format!("{}-{}", section_name, "buttons"), checkout_controller)
            .main_axis_size(MainAxisSize::Max)
            .focus_area(FocusAreaEntryMode::Default)
        }.main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING267))
    }
}

fn create_accessibility_context_messages(
    product: &Product,
    selected_offer: &OfferDetails,
    legal: &Legal,
    payment: &PresentablePayment,
) -> Vec<TextContent> {
    let variant = get_left_rail_variant(product);

    let mut accessibility_context_messages = vec![
        match variant {
            LeftRailVariants::Copy => product.description.as_deref(),
            LeftRailVariants::RadioButtons => None,
        },
        selected_offer
            .pricing
            .due_today_with_prerequisites
            .as_deref(),
        payment.due_today_text.as_deref(),
        selected_offer
            .pricing
            .full_price_description_summary
            .as_deref(),
        Some(
            selected_offer
                .pricing
                .full_price_description_next_charge
                .as_str(),
        ),
        payment
            .formatted_payment_details_list
            .first()
            .map(|pd| pd.as_str()),
        Some(legal.terms_and_conditions.as_str()),
    ]
    .into_iter()
    .flatten()
    .map(TextContent::from)
    .collect::<Vec<_>>();

    let mut disclosures_accessibility_context_messages = {
        if let Some(disclosures) = &legal.disclosures {
            disclosures
                .iter()
                .filter(|(_, v)| !v.explicit_consent_required)
                .map(|(_, v)| TextContent::from(v.text.clone()))
                .collect::<Vec<_>>()
        } else {
            vec![]
        }
    };

    accessibility_context_messages.append(&mut disclosures_accessibility_context_messages);

    accessibility_context_messages
}

#[derive(Clone)]
struct RenewalConsentErrorSignal {
    pub signal: RwSignal<bool>,
}

fn use_renewal_consent_error_signal(scope: Scope) -> RwSignal<bool> {
    match use_context::<RenewalConsentErrorSignal>(scope) {
        Some(error_signal_wrapper) => error_signal_wrapper.signal,
        None => create_rw_signal(scope, false),
    }
}

#[Composer]
pub fn BoltConfirmationPageV2(
    ctx: &AppContext,
    view_data: ConfirmationBoltSingleTierViewDataV2,
    checkout_controller: Rc<dyn CheckoutControl>,
) -> StackComposable {
    let view_data = Rc::new(view_data);
    let checkout_controller = Rc::clone(&checkout_controller);

    let background_image = view_data.main_product.images.hero_image.clone();
    let background_image_url = background_image.map(|bi| bi.url);

    let selected_offer: RwSignal<TransactableOffer> = create_rw_signal(
        ctx.scope(),
        TransactableOffer {
            offer: view_data.main_product.primary_offer.clone(),
            confirm_order_action: view_data.confirm_order_action.clone(),
        },
    );

    let error_signal_wrapper = RenewalConsentErrorSignal {
        signal: create_rw_signal(ctx.scope(), false),
    };
    provide_context(ctx.scope(), error_signal_wrapper);

    let accessibility_context_messages = {
        let legal = view_data.legal.clone();
        let payment = view_data.payment.clone();
        let offer = view_data.main_product.clone();

        create_memo(ctx.scope(), move |_| {
            selected_offer.with(|so| {
                create_accessibility_context_messages(&offer, &so.offer, &legal, &payment)
            })
        })
    };

    compose! {
        Stack() {

            BackgroundImage(background_image_url, section_name: BOLT_CONFIRMATION_PAGE_TEST_ID_V2)

            Column() {
                Row() {
                    RenderHeader(view_data: &view_data, section_name: &format!("{}-header", BOLT_CONFIRMATION_PAGE_TEST_ID_V2))
                }
                .main_axis_size(MainAxisSize::Max)
                .main_axis_alignment(MainAxisAlignment::Start)
                .width(LEFT_COL_WIDTH)
                .height(HEADER_HEIGHT)

                Row() {}
                    .height(VERTICAL_GUTTER)
                    .main_axis_size(MainAxisSize::Max)
                Row() {
                    Column() {
                        RenderLeftRail(product: &view_data.main_product, selected_offer, section_name: &format!("{}-left-rail", BOLT_CONFIRMATION_PAGE_TEST_ID_V2))
                    }
                    .main_axis_size(MainAxisSize::Max)
                    .padding(Padding::new(0.0, 0.0, 0.0, FableSpacing::SPACING200))
                    .width(LEFT_COL_WIDTH)

                    Column() {
                        RenderRightRail(
                            selected_offer,
                            legal: &view_data.legal,
                            payment_method: &view_data.payment,
                            section_name: &format!("{}-right-rail", BOLT_CONFIRMATION_PAGE_TEST_ID_V2),
                            checkout_controller)

                        RenderAutoRenewalDisclosureError(legal: &view_data.legal, section_name: format!("{}-right-rail", BOLT_CONFIRMATION_PAGE_TEST_ID_V2))
                    }
                    .main_axis_size(MainAxisSize::Max)
                    .width(RIGHT_COL_WIDTH)
                }
                .flex(1.0)
                .main_axis_size(MainAxisSize::Max)
                .main_axis_alignment(MainAxisAlignment::SpaceBetween)
                .cross_axis_alignment(CrossAxisAlignment::Start)
                .focus_hierarchical_container(NavigationStrategy::Horizontal)
            }
            .main_axis_size(MainAxisSize::Max)
            .main_axis_alignment(MainAxisAlignment::SpaceBetween)
            .padding(Padding::new(SIDE_PADDING, SIDE_PADDING, TOP_PADDING, BOTTOM_PADDING))
        }
        .test_id(BOLT_CONFIRMATION_PAGE_TEST_ID_V2)
        .accessibility_context_messages(accessibility_context_messages)
    }
}

#[cfg(test)]
mod tests {
    use crate::network::types::checkout_view_data::{
        view_data::ViewData, view_data_parser::view_data_parser,
    };
    use crate::network::{
        checkout_network::MockNetworkClient, test_utils::create_empty_test_checkout_controller,
    };
    use ignx_compositron::{
        app::launch_test,
        input::KeyCode,
        lazy_static::lazy_static,
        test_utils::{
            assert_node_does_not_exist, assert_node_exists, node_properties::SceneNodeTree,
            TestRendererGameLoop,
        },
    };

    use super::*;

    fn extract_view_data(view_data: &str) -> ConfirmationBoltSingleTierViewDataV2 {
        match view_data_parser(view_data.to_string()).unwrap() {
            ViewData::ConfirmationBoltSingleTierViewDataV2(data) => data,
            _ => panic!(""),
        }
    }

    lazy_static! {
        pub static ref BOLT_V2_SINGLE_MONTHLY_VIEW_DATA_NOR: ConfirmationBoltSingleTierViewDataV2 = extract_view_data(include_str!("../../network/test_data/bolt_confirmation_page_view_data_v2/channels_single_monthly_offer_NOR.json"));
        pub static ref BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR: ConfirmationBoltSingleTierViewDataV2 = extract_view_data(include_str!("../../network/test_data/bolt_confirmation_page_view_data_v2/channels_monthly_and_annual_offers_NOR.json"));
        pub static ref BOLT_V2_SINGLE_MONTHLY_VIEW_DATA: ConfirmationBoltSingleTierViewDataV2 = extract_view_data(include_str!("../../network/test_data/bolt_confirmation_page_view_data_v2/channels_single_monthly_offer.json"));
    }

    #[test]
    fn it_renders_single_monthly_offer_nor_page() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_SINGLE_MONTHLY_VIEW_DATA_NOR.clone())
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let root = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID_V2);
                assert_node_exists!(root);
            },
        );
    }

    #[test]
    fn it_renders_without_change_payment_button() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let mut view_data = BOLT_V2_SINGLE_MONTHLY_VIEW_DATA_NOR.clone();
                view_data.payment.change_payment_action = None;

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let root = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID_V2);
                assert_node_exists!(root.clone());
                let change_payment_button = root
                    .find_any_child_with()
                    .test_id(
                        "bolt-confirmation-page-v2-right-rail-payment-info-change-payment-button",
                    )
                    .find_first();
                assert_node_does_not_exist!(change_payment_button);
            },
        );
    }

    #[test]
    fn it_renders_without_offer_description() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let mut view_data = BOLT_V2_SINGLE_MONTHLY_VIEW_DATA_NOR.clone();
                view_data.main_product.description = None;

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let root = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID_V2);
                assert_node_exists!(root.clone());
                let offer_description = root
                    .find_any_child_with()
                    .test_id("bolt-confirmation-page-v2-left-rail-copy-variant")
                    .is_visible()
                    .find_first();
                assert_node_does_not_exist!(offer_description);
            },
        );
    }

    #[test]
    fn displays_error_when_confirm_button_pressed_with_no_auto_renewal_consent_given() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let view_data = BOLT_V2_SINGLE_MONTHLY_VIEW_DATA_NOR.clone();

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data)
                }
            },
            move |_scope, mut test_loop| {
                let confirm_button_test_id =
                    "bolt-confirmation-page-v2-right-rail-buttons-confirm-order-button";
                let auto_renewal_button_test_id =
                    "bolt-confirmation-page-v2-right-rail-buttons-auto-renewal-button";
                let auto_renewal_error_test_id =
                    "bolt-confirmation-page-v2-right-rail-auto-renewal-disclosure-error";

                let tree = test_loop.tick_until_done();
                // check auto_renewal_error is not displayed by default
                let auto_renewal_error = tree.find_by_test_id(auto_renewal_error_test_id);
                assert_node_does_not_exist!(auto_renewal_error);

                // check error is displayed if confirm_button is pressed with no auto renewal consent
                let confirm_button = tree.find_by_test_id(confirm_button_test_id);
                test_loop.send_on_select_event(confirm_button.borrow_props().node_id);

                let tree = test_loop.tick_until_done();
                let auto_renewal_error = tree.find_by_test_id(auto_renewal_error_test_id);
                assert_node_exists!(auto_renewal_error);

                // check error is removed from view when consent given
                let auto_renewal_button = tree.find_by_test_id(auto_renewal_button_test_id);
                test_loop.send_on_select_event(auto_renewal_button.borrow_props().node_id);

                let tree = test_loop.tick_until_done();
                let auto_renewal_error = tree.find_by_test_id(auto_renewal_error_test_id);
                assert_node_does_not_exist!(auto_renewal_error);
            },
        );
    }

    #[test]
    fn it_renders_monthly_and_annual_offers_nor_page() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let root = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID_V2);
                assert_node_exists!(root);
            },
        );
    }

    #[test]
    fn it_renders_header_logo_image() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let logo_image =
                    tree.find_by_test_id("bolt-confirmation-page-v2-header-logo-image");
                assert_node_exists!(logo_image);
            },
        );
    }

    #[test]
    fn it_renders_disclosures() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let viewership_data_sharing_disclosure = tree.find_by_test_id(
                    "bolt-confirmation-page-v2-right-rail-disclosures-viewership-data-sharing",
                );
                assert_node_exists!(viewership_data_sharing_disclosure);

                let auto_renewal_disclosure = tree.find_by_test_id(
                    "bolt-confirmation-page-v2-right-rail-disclosures-auto-renewal",
                );
                assert_node_does_not_exist!(auto_renewal_disclosure);
            },
        );
    }

    #[ignore = "Fix confirmation_button .flex horizontally"]
    #[test]
    fn confirmation_button_fills_available_space() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_SINGLE_MONTHLY_VIEW_DATA.clone())
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let confirmation_button = tree.find_by_test_id(
                    "bolt-confirmation-page-v2-right-rail-buttons-confirm-order-button",
                );

                let button_width = confirmation_button.get_props().layout.size.width;

                assert_eq!(button_width, CONFIRMATION_BUTTON_MAX_WIDTH);
            },
        )
    }

    mod actions {
        use super::*;
        use mockall::*;

        use mockall::predicate;
        use rstest::rstest;

        mock! {
            pub CheckoutController {
                pub fn create() -> Self;
            }

            impl CheckoutControl for CheckoutController {
                fn take_step(&self, option_id: &str);

                fn initial_request(&self);
            }
        }

        #[rstest]
        #[case(&*BOLT_V2_SINGLE_MONTHLY_VIEW_DATA_NOR, nor_setup_fn)]
        #[case(&*BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR, nor_setup_fn)]
        #[case(&*BOLT_V2_SINGLE_MONTHLY_VIEW_DATA, empty_setup)]
        #[test]
        fn test_action_for_confirmation_button_selected(
            #[case] view_data: &ConfirmationBoltSingleTierViewDataV2,
            #[case] setup_test: fn(&mut TestRendererGameLoop) -> (),
        ) {
            let view_data = view_data.clone();

            launch_test(
                move |ctx| {
                    let mut checkout_controller = MockCheckoutController::new();

                    checkout_controller
                        .expect_take_step()
                        .with(predicate::function(|option_id| {
                            option_id == "eyJhY3Rpb25EYXRhIjp7ImNvbmZpcm1PcmRlckFjdGlvbiI6e319fQ=="
                        }))
                        .times(1)
                        .return_const(());

                    let checkout_controller = Rc::new(checkout_controller);

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: view_data)
                    }
                },
                move |_scope, mut test_loop| {
                    setup_test(&mut test_loop);

                    let tree = test_loop.tick_until_done();
                    let confirm_button = tree.find_by_test_id(
                        "bolt-confirmation-page-v2-right-rail-buttons-confirm-order-button",
                    );
                    test_loop.send_on_select_event(confirm_button.borrow_props().node_id);
                    test_loop.tick_until_done();
                },
            );
        }

        #[test]
        fn test_action_when_secondary_offer_selected_for_confirmation_button_selected() {
            launch_test(
                move |ctx| {
                    let mut checkout_controller = MockCheckoutController::new();

                    let expected_option_id = concat!(
                        "eyJhY3Rpb25EYXRhIjp7ImNhbmNlbE9yZGVyQWN0aW9uIjpudWxsLCJjaGFuZ2VCaWxsaW5nQWN0aW9uIjpu",
                        "dWxsLCJjaGFuZ2VQYXltZW50QWN0aW9uIjpudWxsLCJjb25maXJtT3JkZXJBY3Rpb24iOnsib2ZmZXJUb2tl",
                        "bnMiOm51bGwsInVzZVJld2FyZHNBY2NvdW50IjpudWxsfSwiZW50ZXJQaW5BY3Rpb24iOm51bGwsInBhZ2VS",
                        "ZWZyZXNoQWN0aW9uIjpudWxsLCJyZXF1ZXN0U2VsZWN0T2ZmZXJBY3Rpb24iOm51bGwsInNlbGVjdE9mZmVy",
                        "QWN0aW9uIjpudWxsLCJzdGFydENoZWNrb3V0QWN0aW9uIjpudWxsfX0="
                    );

                    checkout_controller
                        .expect_take_step()
                        .with(predicate::function(move |option_id| {
                            option_id == expected_option_id
                        }))
                        .times(1)
                        .return_const(());

                    let checkout_controller = Rc::new(checkout_controller);

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    nor_setup_fn(&mut test_loop);
                    press_secondary_offer_radio_button(&mut test_loop);

                    let tree = test_loop.tick_until_done();
                    let confirm_button = tree.find_by_test_id(
                        "bolt-confirmation-page-v2-right-rail-buttons-confirm-order-button",
                    );
                    test_loop.send_on_select_event(confirm_button.borrow_props().node_id);
                    test_loop.tick_until_done();
                },
            );
        }

        static AUTO_RENEWAL_BUTTON_TEST_ID: &str =
            "bolt-confirmation-page-v2-right-rail-buttons-auto-renewal-button";

        fn nor_setup_fn(test_loop: &mut TestRendererGameLoop) -> () {
            let tree = test_loop.tick_until_done();

            let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
            test_loop.send_on_select_event(auto_renewal_button.borrow_props().node_id);
            test_loop.tick_until_done();
        }

        fn empty_setup(_: &mut TestRendererGameLoop) -> () {}
    }
    mod tts {
        use super::*;

        #[test]
        fn test_bolt_confirmation_page_v2_tts_extraction() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_SINGLE_MONTHLY_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    let expected_context_messages = vec![
                        "STARZ is entertainment by grown ups, for grown ups. Experience a world of boundary-breaking stories.",
                        "$0.00",
                        "Due today",
                        "$5.99/month after 7-day free trial until canceled.",
                        "Payment due on 10 October.",
                        "Visa ending in 1111",
                        "By signing up, you acknowledge that you agree to the **Amazon Prime Terms**",
                        "I agree to share my information with Max",
                    ];
                    let tree = test_loop.tick_until_done();

                    validate_context_messages(
                        &tree,
                        BOLT_CONFIRMATION_PAGE_TEST_ID_V2,
                        &expected_context_messages,
                    );
                },
            )
        }

        #[test]
        fn context_message_changes_with_selected_offer() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    let expected_context_messages = vec![
                        "$0.00",
                        "Due today",
                        "$99/year after 7-day free trial until canceled.",
                        "Payment due on 10 October.",
                        "Visa ending in 1111",
                        "By signing up, you acknowledge that you agree to the **Amazon Prime Terms**",
                        "I agree to share my information with Max",
                    ];
                    let tree = press_secondary_offer_radio_button(&mut test_loop);

                    validate_context_messages(
                        &tree,
                        BOLT_CONFIRMATION_PAGE_TEST_ID_V2,
                        &expected_context_messages,
                    );
                },
            )
        }

        fn validate_context_messages(
            tree: &SceneNodeTree,
            test_id: &str,
            expected_context_messages: &Vec<&str>,
        ) {
            let root = tree.find_by_test_id(test_id);
            if let Some(accessibility_component) = root.get_props().accessibility {
                let actual_context_messages: Vec<&str> = accessibility_component
                    .context_messages
                    .iter()
                    .map(|tc| tc.0.as_str())
                    .collect();

                check_context_messages_equality(
                    &actual_context_messages,
                    expected_context_messages,
                );
            } else {
                panic!("Missing accessibility component")
            }
        }

        fn check_context_messages_equality(actual: &Vec<&str>, expected: &Vec<&str>) {
            assert_eq!(actual.len(), expected.len());

            for (actual, expected) in actual.iter().zip(expected) {
                assert_eq!(actual, expected);
            }
        }
    }

    mod navigation {
        use super::*;

        pub static AUTO_RENEWAL_BUTTON_TEST_ID: &str =
            "bolt-confirmation-page-v2-right-rail-buttons-auto-renewal-button";
        pub static CONFIRM_BUTTON_TEST_ID: &str =
            "bolt-confirmation-page-v2-right-rail-buttons-confirm-order-button";
        pub static CHANGE_PAYMENT_BUTTON_TEST_ID: &str =
            "bolt-confirmation-page-v2-right-rail-payment-info-change-payment-button";
        pub static RADIO_BUTTON_GROUP_TEST_ID: &str =
            "bolt-confirmation-page-v2-left-rail-radio-button-group";

        #[test]
        fn auto_renewal_consent_button_is_default_focused() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();

                    let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                    assert!(auto_renewal_button.borrow_props().is_focused);
                },
            );
        }

        #[test]
        fn confirm_button_is_default_focused_when_auto_renewal_consent_not_required() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    let mut view_data = BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone();
                    view_data.legal.disclosures = None;

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data)
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();

                    let confirm_button = tree.find_by_test_id(CONFIRM_BUTTON_TEST_ID);
                    assert!(confirm_button.borrow_props().is_focused);
                },
            );
        }

        #[test]
        fn can_navigate_from_change_payment_button_to_right_rail_buttons() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    // focus change_payment_button and press down
                    {
                        let tree = test_loop.tick_until_done();
                        let change_payment_button =
                            tree.find_by_test_id(CHANGE_PAYMENT_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(change_payment_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Down);

                        let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                        assert!(auto_renewal_button.borrow_props().is_focused);
                    }

                    // test with auto_renewal consent checkbox selected
                    {
                        let tree = test_loop.tick_until_done();
                        let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                        test_loop.send_on_select_event(auto_renewal_button.borrow_props().node_id);
                        let tree = test_loop.tick_until_done();

                        let change_payment_button =
                            tree.find_by_test_id(CHANGE_PAYMENT_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(change_payment_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Down);

                        let confirm_button = tree.find_by_test_id(CONFIRM_BUTTON_TEST_ID);
                        assert!(confirm_button.borrow_props().is_focused);
                    }
                },
            );
        }

        #[test]
        fn can_navigate_to_change_payment_button_from_right_rail_buttons() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    // focus auto_renewal_button and press up
                    {
                        let tree = test_loop.tick_until_done();
                        let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(auto_renewal_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Up);
                        let change_payment_button =
                            tree.find_by_test_id(CHANGE_PAYMENT_BUTTON_TEST_ID);
                        assert!(change_payment_button.borrow_props().is_focused);
                    }

                    // test from confirm_button
                    {
                        let tree = test_loop.tick_until_done();
                        let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                        test_loop.send_on_select_event(auto_renewal_button.borrow_props().node_id);
                        let tree = test_loop.tick_until_done();

                        let confirm_button = tree.find_by_test_id(CONFIRM_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(confirm_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Up);

                        let change_payment_button =
                            tree.find_by_test_id(CHANGE_PAYMENT_BUTTON_TEST_ID);
                        assert!(change_payment_button.borrow_props().is_focused);
                    }
                },
            );
        }

        #[test]
        fn can_navigate_between_change_payment_and_confirm_button_when_no_consent_required() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    let mut view_data = BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone();
                    view_data.legal.disclosures = None;

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data)
                    }
                },
                move |_scope, mut test_loop| {
                    // test from confirm_button to change_payment_button
                    {
                        let tree = test_loop.tick_until_done();
                        let confirm_button = tree.find_by_test_id(CONFIRM_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(confirm_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Up);

                        let change_payment_button =
                            tree.find_by_test_id(CHANGE_PAYMENT_BUTTON_TEST_ID);
                        assert!(change_payment_button.borrow_props().is_focused);
                    }

                    // test from change_payment_button to confirm_button
                    {
                        let tree = test_loop.tick_until_done();
                        let change_payment_button =
                            tree.find_by_test_id(CHANGE_PAYMENT_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(change_payment_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Down);

                        let confirm_button = tree.find_by_test_id(CONFIRM_BUTTON_TEST_ID);
                        assert!(confirm_button.borrow_props().is_focused);
                    }
                },
            );
        }

        #[test]
        fn can_navigate_to_radio_button_group() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    // focus auto_renewal_button and press left
                    {
                        let tree = test_loop.tick_until_done();
                        let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(auto_renewal_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Left);
                        let radio_button_group = tree.find_by_test_id(RADIO_BUTTON_GROUP_TEST_ID);
                        assert!(radio_button_group.borrow_props().is_focused);
                    }

                    // focus change_payment_button and press left
                    {
                        let tree = test_loop.tick_until_done();
                        let change_payment_button =
                            tree.find_by_test_id(CHANGE_PAYMENT_BUTTON_TEST_ID);
                        test_loop.send_on_focus_event(change_payment_button.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Left);
                        let radio_button_group = tree.find_by_test_id(RADIO_BUTTON_GROUP_TEST_ID);
                        assert!(radio_button_group.borrow_props().is_focused);
                    }
                },
            );
        }

        #[test]
        fn can_navigate_from_radio_button_group() {
            launch_test(
                move |ctx| {
                    let mock_network_client = MockNetworkClient::default();

                    let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                        &ctx,
                        mock_network_client,
                    ));

                    compose! {
                        BoltConfirmationPageV2(checkout_controller, view_data: BOLT_V2_MONTHLY_AND_ANNUAL_VIEW_DATA_NOR.clone())
                    }
                },
                move |_scope, mut test_loop| {
                    // should move to auto_renewal_button when no consent given
                    {
                        let tree = test_loop.tick_until_done();
                        let radio_button_group = tree.find_by_test_id(RADIO_BUTTON_GROUP_TEST_ID);
                        test_loop.send_on_focus_event(radio_button_group.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Right);
                        let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                        assert!(auto_renewal_button.borrow_props().is_focused);
                    }

                    // should move to confirmation_button when consent given
                    {
                        // enable confirmation_button
                        let tree = test_loop.tick_until_done();
                        let auto_renewal_button = tree.find_by_test_id(AUTO_RENEWAL_BUTTON_TEST_ID);
                        test_loop.send_on_select_event(auto_renewal_button.borrow_props().node_id);

                        let radio_button_group = tree.find_by_test_id(RADIO_BUTTON_GROUP_TEST_ID);
                        test_loop.send_on_focus_event(radio_button_group.borrow_props().node_id);
                        test_loop.tick_until_done();
                        let tree = press_key(&mut test_loop, KeyCode::Right);
                        let confirmation_button = tree.find_by_test_id(CONFIRM_BUTTON_TEST_ID);
                        assert!(confirmation_button.borrow_props().is_focused);
                    }
                },
            );
        }
    }

    fn press_secondary_offer_radio_button(test_loop: &mut TestRendererGameLoop) -> SceneNodeTree {
        let tree = test_loop.tick_until_done();

        let secondary_offer_radio_button = tree
            .find_by_test_id("bolt-confirmation-page-v2-left-rail-secondary-offer-radio-button")
            .get_props();

        test_loop.send_on_focus_event(secondary_offer_radio_button.node_id);
        let _ = test_loop.tick_until_done();
        press_key(test_loop, KeyCode::Select)
    }

    fn press_key(test_loop: &mut TestRendererGameLoop, key_code: KeyCode) -> SceneNodeTree {
        test_loop.send_key_down_up_event(key_code);
        test_loop.tick_until_done()
    }
}
