use amzn_fable_tokens::{FableColor, FableOpacity};
use fableous::gradients::gradient::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use media_background::types::MediaBackgroundType;

use lrc_image::lrc_image::*;
use lrc_image::types::{ImageData, ImageFormat, ImageTag, ScalingStrategy};
const GRADIENT_OPACITY: f32 = FableOpacity::OPACITY080;

#[Composer]
pub fn BackgroundImage(
    ctx: &AppContext,
    background_image_url: Option<String>,
    section_name: &str,
) -> StackComposable {
    // Check if media_background_type is None or MediaBackgroundType::None
    let media_background_type = use_context::<RwSignal<MediaBackgroundType>>(ctx.scope());
    let is_media_background_empty: MaybeSignal<bool> = Signal::derive(ctx.scope(), move || {
        if let Some(mbt) = media_background_type {
            mbt.with(|mbt| *mbt == MediaBackgroundType::None)
        } else {
            true
        }
    })
    .into();

    let url_is_some = background_image_url.is_some();

    let url = background_image_url.unwrap_or_default();
    let background_image_test_id = format!("{}-background-image", section_name);

    compose! {
        Stack() {
            if is_media_background_empty.get() && url_is_some {
                LRCImage(data: ImageData {
                    url: url.clone(),
                    width: SCREEN_WIDTH,
                    height: SCREEN_HEIGHT,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::UpscaleToCover),
                        ImageTag::Format(ImageFormat::JPG),
                    ],
                })
                .test_id(background_image_test_id.clone())
            }

            Rectangle()
                .background_color(get_ignx_color(FableColor::BACKGROUND))
                .width(SCREEN_WIDTH)
                .height(SCREEN_HEIGHT)
                .opacity(GRADIENT_OPACITY)
                .test_id(format!("{}-background-color", section_name))

            Gradient(gradient: GradientName::RadialScrimCombined720, width: SCREEN_WIDTH, height: SCREEN_HEIGHT)
                .test_id(format!("{}-background-gradient", section_name))

        }.test_id(format!("{}-background", section_name))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};

    pub static BACKGROUND_IMAGE_URL: &str = "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/hayu/heroes/bigscreen_3p-signup-background_hd_large._CB652074987_SX1920_.png";

    #[test]
    fn it_renders_background_image_when_no_media_background_type() {
        launch_test(
            move |ctx| {
                compose! {
                    BackgroundImage(background_image_url: Some(BACKGROUND_IMAGE_URL.to_owned()), section_name: "test")
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                check_background_with_image(&tree);
            },
        )
    }

    #[test]
    fn it_renders_background_image_when_media_background_type_none() {
        launch_test(
            move |ctx| {
                let media_background_type =
                    create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                provide_context(ctx.scope(), media_background_type);

                compose! {
                    BackgroundImage(background_image_url: Some(BACKGROUND_IMAGE_URL.to_owned()), section_name: "test")
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                check_background_with_image(&tree);
            },
        )
    }

    #[test]
    fn it_renders_background_without_image_when_media_background_type_not_none() {
        launch_test(
            move |ctx| {
                let media_background_type =
                    create_rw_signal(ctx.scope(), MediaBackgroundType::Playback);
                provide_context(ctx.scope(), media_background_type);

                compose! {
                    BackgroundImage(background_image_url: Some(BACKGROUND_IMAGE_URL.to_owned()), section_name: "test")
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                check_background_without_image(&tree);
            },
        )
    }

    #[test]
    fn it_renders_without_background_image_when_url_is_none() {
        launch_test(
            move |ctx| {
                compose! {
                    BackgroundImage(background_image_url: None, section_name: "test")
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                check_background_without_image(&tree);
            },
        )
    }

    #[test]
    fn is_reactive_to_media_background_type() {
        launch_test(
            move |ctx| {
                let media_background_type =
                    create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                provide_context(ctx.scope(), media_background_type);

                compose! {
                    BackgroundImage(background_image_url: Some(BACKGROUND_IMAGE_URL.to_owned()), section_name: "test")
                }
            },
            move |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                check_background_with_image(&tree);

                let media_background_type = use_context::<RwSignal<MediaBackgroundType>>(scope);
                media_background_type.inspect(|mbt| mbt.set(MediaBackgroundType::Playback));
                let tree = test_loop.tick_until_done();
                check_background_without_image(&tree);
            },
        )
    }

    fn check_background_with_image(tree: &SceneNodeTree) {
        assert_node_exists!(tree.find_by_test_id("test-background"));
        assert_node_exists!(tree.find_by_test_id("test-background-image"));
        assert_node_exists!(tree.find_by_test_id("test-background-color"));
        assert_node_exists!(tree.find_by_test_id("test-background-gradient"));
    }

    fn check_background_without_image(tree: &SceneNodeTree) {
        assert_node_exists!(tree.find_by_test_id("test-background"));
        assert_node_does_not_exist!(tree.find_by_test_id("test-background-image"));
        assert_node_exists!(tree.find_by_test_id("test-background-color"));
        assert_node_exists!(tree.find_by_test_id("test-background-gradient"));
    }
}
