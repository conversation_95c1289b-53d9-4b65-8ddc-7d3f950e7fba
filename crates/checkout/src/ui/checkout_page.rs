use ignx_compositron::{compose, prelude::*, Composer};

#[cfg(test)]
use crate::network::checkout_network::MockNetworkClient as NetworkClient;
#[cfg(not(test))]
use network::NetworkClient;

use crate::checkout::*;

fn get_network_client(_ctx: &AppContext) -> NetworkClient {
    #[cfg(not(test))]
    {
        NetworkClient::new(_ctx)
    }
    #[cfg(test)]
    {
        NetworkClient::default()
    }
}

#[Composer]
pub fn CheckoutPage(ctx: &AppContext) -> impl VisualComposable<'static> {
    let network_client = get_network_client(ctx);

    compose! {
        Checkout(network_client)
    }
}
