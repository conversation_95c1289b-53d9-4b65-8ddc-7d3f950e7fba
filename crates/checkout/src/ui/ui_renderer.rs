use crate::ui::spinner::*;
use crate::ui::widget_renderer::*;
use crate::{
    checkout_controller::CheckoutController,
    network::types::checkout_view_data::view_data::ViewData,
};
use cfg_test_attr_derive::derive_test_only;
use fableous::animations::fable_motion_linear_medium;
use ignx_compositron::composable::VisualComposable;
use ignx_compositron::time::Instant;
use ignx_compositron::{compose, prelude::AppContext, Composer};
use ignx_compositron::{compose_option, prelude::*};
use std::rc::Rc;

const RENDER_WIDGET_TEST_ID: &str = "render_widget";

#[derive_test_only(Debug, PartialEq)]
#[derive(Clone)]
pub struct DisplayableWidget {
    pub view_data: ViewData,
}

fn trigger_content_fade_in(ctx: &AppContext, opacity: RwSignal<f32>) {
    let anim_ctx = ctx.clone();
    ctx.schedule_task(Instant::now(), move || {
        anim_ctx.with_animation(fable_motion_linear_medium(), move || {
            opacity.set(1.0);
        });
    });
}

#[Composer]
pub fn CheckoutUI(
    ctx: &AppContext,
    checkout_controller: Rc<CheckoutController>,
) -> impl VisualComposable<'static> {
    let signal = checkout_controller.widget_signal;

    compose! {
        Stack() {
            Memo(item_builder: Box::new(move |ctx| {
                signal.with(|displayable_widget| {
                    match displayable_widget {
                        Some(DisplayableWidget { view_data }) => {
                            let opacity = create_rw_signal(ctx.scope(), 0.0);
                            trigger_content_fade_in(ctx, opacity);

                            compose_option! {
                                Stack() {
                                    RenderWidget(
                                        view_data: view_data.clone(),
                                        checkout_controller: checkout_controller.clone()
                                    )
                                }
                                .test_id(RENDER_WIDGET_TEST_ID)
                                .opacity(opacity)
                            }
                        }
                        None => {
                            compose_option! {
                                RenderSpinner()
                            }
                        }
                    }
                })
            }))
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::network::{
        checkout_network::MockNetworkClient, test_utils::create_empty_test_checkout_controller,
        types::checkout_view_data::view_data_parser::view_data_parser,
    };
    use crate::ui::widgets::bolt_confirmation_page::BOLT_CONFIRMATION_PAGE_TEST_ID;
    use crate::ui::widgets::bolt_confirmation_page_v2::BOLT_CONFIRMATION_PAGE_TEST_ID_V2;
    use ignx_compositron::test_utils::assert_node_does_not_exist;
    use ignx_compositron::time::{Duration, MockClock};
    use ignx_compositron::{app::launch_test, compose, test_utils::assert_node_exists};
    use std::rc::Rc;

    use super::*;

    #[test]
    fn it_renders_a_spinner_when_widget_signal_is_null_for_more_than_two_seconds() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    CheckoutUI(checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                MockClock::advance(Duration::from_millis(2100));
                let tree = test_loop.tick_until_idle();

                let spinner_container = tree.find_by_test_id("spinner_container");
                assert_node_exists!(&spinner_container);

                let spinner = tree.find_by_test_id("spinner");
                assert_node_exists!(&spinner);
            },
        );
    }

    #[test]
    fn it_does_not_render_a_spinner_when_widget_signal_is_null_for_less_than_two_seconds() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                compose! {
                    CheckoutUI(checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let spinner_container = tree.find_by_test_id("spinner_container");
                assert_node_exists!(&spinner_container);

                let spinner = tree.find_by_test_id("spinner");
                assert_node_does_not_exist!(&spinner);
            },
        );
    }

    #[test]
    fn it_renders_a_bolt_confirmation_page_when_widget_signal_is_bolt() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let bolt_view_data = view_data_parser(include_str!("../network/test_data/bolt_confirmation_page_view_data/single_tier_bolt_confirmation_page_1883.json").to_owned());

                compose! {
                    RenderWidget(view_data: bolt_view_data.unwrap(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let bolt_confirmation_page = tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID);

                assert_node_exists!(&bolt_confirmation_page);
            },
        );
    }

    #[test]
    fn it_renders_a_bolt_confirmation_page_v2_when_widget_signal_is_bolt_v2() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let bolt_view_data = view_data_parser(include_str!("../network/test_data/bolt_confirmation_page_view_data_v2/channels_monthly_and_annual_offers_NOR.json").to_owned());

                compose! {
                    RenderWidget(view_data: bolt_view_data.unwrap(), checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_idle();

                let bolt_confirmation_page =
                    tree.find_by_test_id(BOLT_CONFIRMATION_PAGE_TEST_ID_V2);

                assert_node_exists!(&bolt_confirmation_page);
            },
        );
    }

    #[test]
    fn it_renders_with_full_opacity_after_animation() {
        launch_test(
            move |ctx| {
                let mock_network_client = MockNetworkClient::default();

                let checkout_controller = Rc::new(create_empty_test_checkout_controller(
                    &ctx,
                    mock_network_client,
                ));

                let bolt_view_data = view_data_parser(include_str!("../network/test_data/bolt_confirmation_page_view_data/single_tier_bolt_confirmation_page_1883.json").to_owned());

                checkout_controller
                    .widget_signal
                    .set(Some(DisplayableWidget {
                        view_data: bolt_view_data.unwrap(),
                    }));

                compose! {
                    CheckoutUI(checkout_controller)
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_once().node_tree;
                let render_widget = tree.find_by_test_id(RENDER_WIDGET_TEST_ID);
                assert_eq!(render_widget.get_props().base_styles.opacity, Some(0.0));

                let tree = test_loop.tick_until_idle();
                let render_widget = tree.find_by_test_id(RENDER_WIDGET_TEST_ID);
                assert_eq!(render_widget.get_props().base_styles.opacity, Some(1.0));
            },
        );
    }
}
