// Needed due to a bug in clippy
#![allow(clippy::indexing_slicing)]

use checkout_navigation::start_checkout::StartCheckoutRequest;
use ignx_compositron::{
    device_information::DeviceInformation,
    prelude::{create_rw_signal, AppContext},
    reactive::store_value,
};

use app_config::MockAppConfig;

use crate::{
    checkout_controller::{create_client_data, CheckoutController, WorkflowStage},
    network::checkout_network::MockNetworkClient,
};

use super::{
    checkout_network::COSWorkflowV2Request,
    types::{
        checkout_client_data::{
            cos_client_data::{
                CosClientData, FlowAgnosticClientData, FlowClientData, FlowSpecificClientData,
            },
            svod_client_data::{
                channel_client_data::ChannelClientData,
                svod_common_client_data::SvodCommonClientData,
            },
        },
        widget_types::{CommonWidgetFields, WidgetVariation},
        workflow_v2_raw_response::{
            StepO<PERSON>, TaskDataType, WorkflowResponseClientContract, WorkflowV2RawResponse,
            WorkflowV2ServiceData,
        },
    },
};

pub fn setup_device_info_and_app_config(
    url_base: Option<String>,
) -> (DeviceInformation, MockAppConfig) {
    let device_info = DeviceInformation::new();
    let mut app_config = MockAppConfig::default();
    app_config
        .expect_get_ux_locale()
        .returning(|| String::from("en_US"));
    app_config
        .expect_get_geo_location()
        .returning(|| Some("US".into()));
    app_config
        .expect_get_supported_locales()
        .returning(|| vec!["de_DE".into(), "en_GB".into(), "fr_FR".into()]);
    app_config.expect_get_base_url().return_once(|| url_base);

    (device_info, app_config)
}

pub fn create_empty_test_checkout_controller(
    ctx: &AppContext,
    network_client: MockNetworkClient,
) -> CheckoutController {
    let start_checkout_request = StartCheckoutRequest::Channels {
        benefit_id: "test_benefit_id".to_string(),
        ref_marker: Some("ref_marker".to_string()),
        bundle_id: None,
        ingress_point: "ingress_point".to_string(),
        rust_source_name: None,
        title_id: None,
        deferred_navigation_location: None,
    };

    let workflow_stages = store_value(ctx.scope(), vec![]);
    let widget_signal = create_rw_signal(ctx.scope(), None);
    let client_data = create_client_data(start_checkout_request).unwrap();

    CheckoutController::new(
        ctx.scope(),
        store_value(ctx.scope(), client_data),
        workflow_stages,
        network_client,
        widget_signal,
        store_value(ctx.scope(), None),
    )
}

pub fn create_test_checkout_controller_with_stages(
    ctx: &AppContext,
    network_client: MockNetworkClient,
    workflow_stages: Vec<WorkflowStage>,
) -> CheckoutController {
    let start_checkout_request = StartCheckoutRequest::Channels {
        benefit_id: "test_benefit_id".to_string(),
        ref_marker: Some("ref_marker".to_string()),
        bundle_id: None,
        ingress_point: "ingress_point".to_string(),
        rust_source_name: None,
        title_id: None,
        deferred_navigation_location: None,
    };

    let workflow_stages = store_value(ctx.scope(), workflow_stages);
    let widget_signal = create_rw_signal(ctx.scope(), None);
    let client_data = create_client_data(start_checkout_request).unwrap();

    CheckoutController::new(
        ctx.scope(),
        store_value(ctx.scope(), client_data),
        workflow_stages,
        network_client,
        widget_signal,
        store_value(ctx.scope(), None),
    )
}

pub fn create_dummy_cos_v2_request() -> COSWorkflowV2Request {
    COSWorkflowV2Request::new(
        "Blast".into(),
        CosClientData::FlowClientData(FlowClientData {
            flow_specific_client_data: {
                FlowSpecificClientData::Channel(ChannelClientData {
                    subscription_product_id: "test_benefit_id".into(),
                    bundle_id: None,
                    svod_common_client_data: SvodCommonClientData {
                        client_prime_signup_name: "rust".into(),
                    },
                })
            },
            flow_agnostic_client_data: FlowAgnosticClientData {
                client_version: "2.7.0".into(),
                consumption_mode_enabled: "false".into(),
                ref_tag: "ref_marker".into(),
                checkout_client_capabilities:
                    "LRD_MULTI_BUTTON_PAGE_VERSION=1&RUST_SINGLE_OFFER_BOLT_VERSION=1".to_string(),
                option_id: Some("test_option".to_string()),
                action: Some("test_action".to_string()),
                next_option: Some("next_test_option".to_string()),
            },
        }),
        Some(WorkflowV2ServiceData {
            data: "data".into(),
            version: "version".into(),
        }),
        "Acquisition".into(),
    )
}

pub fn create_dummy_cos_v2_response() -> WorkflowV2RawResponse {
    WorkflowV2RawResponse {
        client_contracts: vec![WorkflowResponseClientContract {
            task_type: "task_type".to_string(),
            task_data: TaskDataType::Widget(WidgetVariation::StandardWidget(CommonWidgetFields {
                template_data: "template_data".to_string(),
                template_id: "lrdBoltConfirmationOneOffer".to_string(),
                view_data: include_str!("../network/test_data/bolt_confirmation_page_view_data/single_tier_bolt_confirmation_page_1883.json").to_owned(),
                process_viewer_url: "process_viewer_url".to_string(),
            }))
        }],
        options: vec![StepOption {
            next_option: "next_option".to_string(),
            option_id: "option_id".to_string(),
            action: "action".to_string(),
        }],
        service_data: WorkflowV2ServiceData {
            data: "service_data".to_string(),
            version: "2".to_string(),
        },
        original_response: None
    }
}
