use mockall::mock;
// GRCOV_STOP_COVERAGE
use ignx_compositron::network::http::HttpMethod;
use network::NetworkClient;
use network::RequestError;

use serde::Serialize;

use crate::network::parser::cos_response_parser_callback;
use crate::network::url_constructor::generate_workflow_v2_service_request_url;
use ignx_compositron::context::AppContext;

use super::types::{
    checkout_client_data::cos_client_data::CosClientData,
    workflow_v2_raw_response::{WorkflowV2RawResponse, WorkflowV2ServiceData},
};

#[allow(dead_code)]
pub trait CheckoutRequests {
    fn workflow_v2<S, F>(
        &self,
        request: COSWorkflowV2Request,
        success_callback: S,
        failure_callback: F,
    ) where
        S: FnOnce(WorkflowV2RawResponse) + 'static,
        F: FnOnce(RequestError) + 'static;
}

#[derive(Serialize, <PERSON><PERSON>, Debug, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct COSWorkflowV2Request {
    #[serde(rename = "clientId")]
    pub(crate) client_id: String,
    #[serde(rename = "data")]
    pub(crate) client_data: CosClientData,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub(crate) service_data: Option<WorkflowV2ServiceData>,
    pub(crate) workflow_type: String,
}

impl COSWorkflowV2Request {
    pub fn new(
        client_id: String,
        client_data: CosClientData,
        service_data: Option<WorkflowV2ServiceData>,
        workflow_type: String,
    ) -> Self {
        Self {
            client_id,
            client_data,
            service_data,
            workflow_type,
        }
    }
}

impl CheckoutRequests for NetworkClient {
    fn workflow_v2<S, F>(
        &self,
        request: COSWorkflowV2Request,
        success_callback: S,
        failure_callback: F,
    ) where
        S: FnOnce(WorkflowV2RawResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
        let url = match generate_workflow_v2_service_request_url(
            self.device_info.clone(),
            self.app_config.clone(),
        ) {
            Ok(url) => url,
            Err(e) => return failure_callback(RequestError::Builder(e.to_string())),
        };

        let body_str = match serde_json::to_string(&request) {
            Ok(body) => body,
            Err(e) => {
                log::error!("Checkout: Cannot request. Error: {:?}", e);
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let success_cb = move |v: WorkflowV2RawResponse, _| {
            success_callback(v);
        };

        let failure_cb = move |e: RequestError, _| {
            failure_callback(e);
        };

        self.builder(url, HttpMethod::Post, "CHECKOUT")
            .data(body_str)
            .with_parser(cos_response_parser_callback)
            .with_device_proxy_headers()
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb))
            .execute();
    }
}

mock! {
    pub NetworkClient {
        pub fn new(ctx: &AppContext) -> Self;
    }
    impl CheckoutRequests for NetworkClient {
        fn workflow_v2<S, F>(&self, request: COSWorkflowV2Request, success: S, failure: F)
        where
            S: FnOnce(WorkflowV2RawResponse) + 'static,
            F: FnOnce(RequestError) + 'static;
    }
}
