use app_config::AppConfigContext;
use ignx_compositron::device_information::DeviceInformation;
use network::{URL<PERSON><PERSON>er, URLBuilderResult};
use std::rc::Rc;

const WORKFLOW_V2_URL: &str = "/cdp/acquisition/v2/workflow";

pub(crate) fn generate_workflow_v2_service_request_url(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
) -> URLBuilderResult {
    match URLBuilder::for_device_proxy(
        WORKFLOW_V2_URL,
        vec![],
        false,
        Rc::clone(&device_info),
        Rc::clone(&app_config),
    ) {
        Ok(url) => Ok(url),
        Err(e) => {
            log::error!("Checkout: Cannot create the request url. Error: {:?}", e);
            Err(e)
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::network::test_utils::setup_device_info_and_app_config;

    use super::*;

    #[test]
    fn generate_workflow_v2_service_request_url_successfully_generates_url() {
        let (device_info, app_config) =
            setup_device_info_and_app_config(Some("https://base_url.com".to_string()));
        let res =
            generate_workflow_v2_service_request_url(Rc::new(device_info), Rc::new(app_config));
        let expected_transform = "/cdp/acquisition/v2/workflow";
        match res {
            Ok(url) => {
                assert!(url.to_url_string().contains(expected_transform))
            }
            Err(e) => panic!("shouldn't have errored, but did with {:?}", e),
        }
    }

    #[test]
    fn generate_workflow_v2_service_request_url_handles_error() {
        let (device_info, app_config) = setup_device_info_and_app_config(None);
        let res =
            generate_workflow_v2_service_request_url(Rc::new(device_info), Rc::new(app_config));
        match res {
            Ok(_) => panic!("Expected an error, but got Ok"),
            Err(e) => {
                assert!(true, "Received expected error: {:?}", e);
            }
        }
    }
}
