use crate::network::types::workflow_v2_raw_response::StepOption;

use super::svod_client_data::channel_client_data::ChannelClientData;
use super::svod_client_data::retention_client_data::RetentionClientData;

use serde::Serialize;

#[derive(Serial<PERSON>, Clone, Debug, PartialEq)]
#[serde(rename_all = "camelCase", untagged)]
pub enum CosClientData {
    FlowClientData(FlowClientData),
    RetentionClientData(RetentionClientData),
}

#[derive(Serialize, Clone, Debug, PartialEq)]
pub struct FlowClientData {
    #[serde(flatten)]
    pub flow_specific_client_data: FlowSpecificClientData,
    #[serde(flatten)]
    pub flow_agnostic_client_data: FlowAgnosticClientData,
}

#[derive(Serialize, Clone, Debug, PartialEq)]
#[serde(rename_all = "camelCase", untagged)]
pub enum FlowSpecificClientData {
    Channel(ChannelClientData),
    // Add new client data variants her...
    // Overall design here is that we have an enum variant for each
    // type of flow so that we avoid issues seen with react where
    // an anonymous JSON blob is passed to the
}

#[derive(Serialize, Clone, Debug, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct FlowAgnosticClientData {
    pub(crate) client_version: String,
    pub(crate) checkout_client_capabilities: String,
    pub(crate) consumption_mode_enabled: String,
    pub(crate) ref_tag: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub(crate) option_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub(crate) action: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub(crate) next_option: Option<String>,
}

impl CosClientData {
    pub fn with_step_option(mut self, step_option: &StepOption) -> Self {
        if let CosClientData::FlowClientData(ref mut flow_data) = self {
            flow_data.flow_agnostic_client_data.option_id = Some(step_option.option_id.clone());
            flow_data.flow_agnostic_client_data.next_option = Some(step_option.next_option.clone());
            flow_data.flow_agnostic_client_data.action = Some(step_option.action.clone());
        }
        self
    }
}

const CLIENT_VERSION: &str = "2.7.0";
const CHECKOUT_LRD_MULTI_BUTTON_PAGE_CAPABILITIES: &str = "LRD_MULTI_BUTTON_PAGE_VERSION=1";
pub const CHECKOUT_RUST_BOLT_CAPABILITY: &str = "RUST_SINGLE_OFFER_BOLT_VERSION=1";

impl FlowAgnosticClientData {
    pub fn new(consumption_mode_enabled: String, ref_tag: String) -> Self {
        Self {
            client_version: CLIENT_VERSION.to_string(),
            checkout_client_capabilities: CHECKOUT_LRD_MULTI_BUTTON_PAGE_CAPABILITIES.to_string(),
            consumption_mode_enabled,
            ref_tag,
            option_id: None,
            action: None,
            next_option: None,
        }
    }

    // GRCOV_STOP_COVERAGE
    #[allow(dead_code)]
    pub fn with_additional_checkout_capabilities(
        self,
        additional_checkout_client_capabilities: String,
    ) -> Self {
        Self {
            checkout_client_capabilities: format!(
                "{}&{}",
                self.checkout_client_capabilities, additional_checkout_client_capabilities
            ),
            ..self
        }
    }
}
