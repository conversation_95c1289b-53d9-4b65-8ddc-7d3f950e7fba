use serde::Serialize;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>Eq)]
#[serde(rename_all = "camelCase")]
pub struct RetentionClientData {
    pub(crate) retention_type: String,
}

impl RetentionClientData {
    pub fn new(retention_type: String) -> Self {
        Self { retention_type }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_retention_client_data() {
        let retention_type = "some retentionType".to_string();
        let data = RetentionClientData::new(retention_type.clone());

        assert_eq!(data.retention_type, retention_type);
    }

    #[test]
    fn test_retention_client_data_serialization() {
        let data = RetentionClientData::new("some retentionType".to_string());
        let json = serde_json::to_string(&data).unwrap();
        assert_eq!(json, "{\"retentionType\":\"some retentionType\"}");
    }
}
