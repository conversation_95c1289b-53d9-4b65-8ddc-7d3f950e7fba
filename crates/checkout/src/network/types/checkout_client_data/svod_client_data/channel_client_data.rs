use serde::Serialize;

use super::svod_common_client_data::SvodCommonClientData;

#[derive(Ser<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct ChannelClientData {
    pub(crate) subscription_product_id: String,
    pub(crate) bundle_id: Option<String>,
    #[serde(flatten)]
    pub(crate) svod_common_client_data: SvodCommonClientData,
}

impl ChannelClientData {
    pub fn new(subscription_product_id: String, bundle_id: Option<String>) -> Self {
        Self {
            subscription_product_id,
            bundle_id,
            svod_common_client_data: SvodCommonClientData::new(),
        }
    }
}
