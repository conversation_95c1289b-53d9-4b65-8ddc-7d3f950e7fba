use serde::Serialize;

const RUST_CLIENT_PRIME_SIGNUP_NAME: &str = "rust";

#[derive(Serial<PERSON>, <PERSON><PERSON>, Debug, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct SvodCommonClientData {
    pub(crate) client_prime_signup_name: String,
}

impl SvodCommonClientData {
    pub fn new() -> Self {
        Self {
            client_prime_signup_name: RUST_CLIENT_PRIME_SIGNUP_NAME.to_string(),
        }
    }
}
