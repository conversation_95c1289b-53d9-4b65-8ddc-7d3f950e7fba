use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;

use super::{callback_types::CallbackVariation, widget_types::WidgetVariation};

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct WorkflowV2RawResponse {
    pub client_contracts: Vec<WorkflowResponseClientContract>,
    pub options: Vec<StepOption>,
    pub service_data: WorkflowV2ServiceData,
    pub original_response: Option<String>,
}

#[derive(Serialize, NetworkParsed, Clone, Debug, PartialEq)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct WorkflowV2ServiceData {
    pub data: String,
    pub version: String,
}

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct WorkflowResponseClientContract {
    #[network(default)]
    pub task_type: String,
    pub task_data: TaskDataType,
}

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll, untagged)]
#[allow(dead_code)]
pub enum TaskDataType {
    Widget(WidgetVariation),
    Callback(CallbackVariation),
}

#[derive(NetworkParsed, Clone, Serialize, Debug, PartialEq)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct StepOption {
    pub next_option: String,
    pub option_id: String,
    pub action: String,
}
