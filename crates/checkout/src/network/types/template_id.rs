use cfg_test_attr_derive::derive_test_only;

#[derive_test_only(Debug, PartialEq)]
#[derive(Clone)]
pub enum TemplateId {
    LrdBoltSinglePlanConfirmationPage,
    LrdBoltSinglePlanConfirmationPageT1,
    LrdBoltSinglePlanConfirmationPageT2,
}

pub fn convert_template_id_to_enum(template_id: &str) -> Option<TemplateId> {
    match template_id {
        LRD_BOLT_CONFIRMATION_ONE_OFFER => Some(TemplateId::LrdBoltSinglePlanConfirmationPage),
        LRD_BOLT_CONFIRMATION_ONE_OFFER_T1 => Some(TemplateId::LrdBoltSinglePlanConfirmationPageT1),
        LRD_BOLT_CONFIRMATION_ONE_OFFER_T2 => Some(TemplateId::LrdBoltSinglePlanConfirmationPageT2),
        _ => None,
    }
}

const LRD_BOLT_CONFIRMATION_ONE_OFFER: &str = "lrdBoltConfirmationOneOffer";
const LRD_BOLT_CONFIRMATION_ONE_OFFER_T1: &str = "lrdBoltConfirmationOneOfferT1Treatment";
const LRD_BOLT_CONFIRMATION_ONE_OFFER_T2: &str = "lrdBoltConfirmationOneOfferT2Treatment";
