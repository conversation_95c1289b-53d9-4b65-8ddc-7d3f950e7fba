use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll, untagged)]
pub enum WidgetVariation {
    StandardWidget(CommonWidgetFields),
}

impl WidgetVariation {
    #[allow(dead_code)]
    pub fn get_common_fields(&self) -> CommonWidgetFields {
        match &self {
            WidgetVariation::StandardWidget(common_widget_fields) => common_widget_fields.clone(),
        }
    }
}

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct StandardWidget {
    #[network(flatten)]
    pub common_widget_fields: CommonWidgetFields,
}

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct CommonWidgetFields {
    pub template_data: String,
    #[network(default)]
    pub template_id: String,
    pub view_data: String,
    pub process_viewer_url: String,
}

#[cfg(test)]
mod tests {
    use crate::network::types::widget_types::{CommonWidgetFields, WidgetVariation};

    #[test]
    fn test_get_common_fields() {
        let common_fields = CommonWidgetFields {
            template_data: "test_template_data".to_string(),
            template_id: "test_template_id".to_string(),
            view_data: "test_view_data".to_string(),
            process_viewer_url: "process_viewer_url".to_string(),
        };

        let widget = WidgetVariation::StandardWidget(common_fields.clone());

        let retrieved_fields = widget.get_common_fields();

        assert_eq!(retrieved_fields.template_data, common_fields.template_data);
        assert_eq!(retrieved_fields.template_id, common_fields.template_id);
        assert_eq!(retrieved_fields.view_data, common_fields.view_data);
    }
}
