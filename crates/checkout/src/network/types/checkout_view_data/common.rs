use fableous::buttons::primary_button::*;
use ignx_compositron::prelude::safe::TextContent;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::{Deserialize, Serialize};

use crate::utils::tts::TTSExtractable;

#[derive(NetworkParsed, Clone, Deserialize, PartialEq, Serialize, Hash, Debug)]
pub enum Layout {
    #[network(rename = "horizontal")]
    Horizontal,
    #[network(rename = "vertical")]
    Vertical,
}

#[derive(NetworkParsed, Clone, Deserialize, PartialEq, Serialize, Debug, Hash)]
pub struct Buttons {
    pub layout: Layout,
    pub buttons: Vec<CheckoutButton>,
}

#[derive(NetworkParsed, Clone, Deserialize, PartialEq, Serialize, Debug, Hash)]
pub struct CheckoutButton {
    pub size: String,
    #[network(rename = "textLines")]
    pub text_lines: Vec<String>,
    #[network(rename = "workflowAction")]
    pub workflow_action: WorkflowAction,
    #[network(rename = "testID")]
    pub test_id: Option<String>,
}

#[derive(NetworkParsed, Clone, Deserialize, PartialEq, Serialize, Debug, Hash)]
pub struct WorkflowAction {
    #[network(rename = "type")]
    pub action_type: String,
    #[network(rename = "optionId")]
    pub option_id: String,
    #[network(rename = "confirmationModal")]
    pub confirmation_modal: ConfirmationModal,
}

#[derive(NetworkParsed, Clone, Deserialize, PartialEq, Serialize, Debug, Hash)]
pub struct ConfirmationModal {
    #[network(rename = "messageBody")]
    pub message_body: Option<String>,
    #[network(rename = "cancelButtonText")]
    pub cancel_button_text: Option<String>,
    #[network(rename = "acceptButtonText")]
    pub accept_button_text: Option<String>,
    pub title: Option<String>,
}

pub trait CheckoutButtonParsing {
    fn get_test_id(&self, section_name: &str, idx: usize) -> String;
    fn to_button_variant(&self) -> Option<PrimaryButtonVariant>;
}

impl CheckoutButtonParsing for CheckoutButton {
    fn get_test_id(&self, section_name: &str, idx: usize) -> String {
        self.test_id
            .clone()
            .unwrap_or_else(|| format!("{}-button-{}", section_name, idx))
    }

    fn to_button_variant(&self) -> Option<PrimaryButtonVariant> {
        if self.text_lines.is_empty() {
            return None;
        }

        match self.size.as_str() {
            #[allow(clippy::indexing_slicing, reason = "bound check above")]
            "size-200" => Some(PrimaryButtonVariant::TextSize200(TextContent::from(
                self.text_lines[0].clone(),
            ))),
            #[allow(clippy::indexing_slicing, reason = "bound check above")]
            "size-400" => Some(PrimaryButtonVariant::TextSize400(TextContent::from(
                self.text_lines[0].clone(),
            ))),
            "size-800" => Some(PrimaryButtonVariant::MultilineTextSize800(
                self.text_lines
                    .iter()
                    .map(|l| TextContent::from(l.clone()))
                    .collect(),
            )),
            _ => None,
        }
    }
}

pub trait ButtonsParsing {
    fn is_horizontal_layout(&self) -> bool;
}

impl ButtonsParsing for Buttons {
    fn is_horizontal_layout(&self) -> bool {
        matches!(self.layout, Layout::Horizontal)
    }
}

impl TTSExtractable for CheckoutButton {
    fn extract_specific_tts(&self) -> Option<TextContent> {
        if self.text_lines.is_empty() {
            None
        } else {
            Some(TextContent::String(self.text_lines.join(" ")))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_button() -> CheckoutButton {
        CheckoutButton {
            size: "size-400".to_string(),
            text_lines: vec!["Button Text".to_string()],
            workflow_action: WorkflowAction {
                action_type: "action".to_string(),
                option_id: "option1".to_string(),
                confirmation_modal: ConfirmationModal {
                    message_body: None,
                    cancel_button_text: None,
                    accept_button_text: None,
                    title: None,
                },
            },
            test_id: None,
        }
    }

    #[test]
    fn test_get_test_id() {
        let button = create_test_button();
        assert_eq!(
            button.get_test_id("section-name", 3),
            "section-name-button-3"
        );

        let mut button = create_test_button();
        button.test_id = Some("custom-test-id".to_string());
        assert_eq!(button.get_test_id("section-name", 3), "custom-test-id");
    }

    #[test]
    fn test_to_button_variant() {
        let mut button = create_test_button();
        button.size = "size-200".to_string();
        button.text_lines = vec!["Small Button".to_string()];
        let variant = button.to_button_variant();
        assert!(matches!(
            variant,
            Some(PrimaryButtonVariant::TextSize200(_))
        ));

        let mut button = create_test_button();
        button.size = "size-400".to_string();
        button.text_lines = vec!["Medium Button".to_string()];
        let variant = button.to_button_variant();
        assert!(matches!(
            variant,
            Some(PrimaryButtonVariant::TextSize400(_))
        ));

        let mut button = create_test_button();
        button.size = "size-800".to_string();
        button.text_lines = vec!["Line 1".to_string(), "Line 2".to_string()];
        let variant = button.to_button_variant();
        assert!(matches!(
            variant,
            Some(PrimaryButtonVariant::MultilineTextSize800(_))
        ));

        let mut button = create_test_button();
        button.size = "unknown-size".to_string();
        assert_eq!(button.to_button_variant(), None);

        let mut button = create_test_button();
        button.text_lines = vec![];
        assert_eq!(button.to_button_variant(), None);
    }
    #[test]
    fn test_is_horizontal_layout() {
        let button = CheckoutButton {
            size: "size-400".to_string(),
            text_lines: vec!["Button Text".to_string()],
            workflow_action: WorkflowAction {
                action_type: "action".to_string(),
                option_id: "option1".to_string(),
                confirmation_modal: ConfirmationModal {
                    message_body: None,
                    cancel_button_text: None,
                    accept_button_text: None,
                    title: None,
                },
            },
            test_id: None,
        };

        let horizontal_buttons = Buttons {
            layout: Layout::Horizontal,
            buttons: vec![button.clone()],
        };
        assert!(horizontal_buttons.is_horizontal_layout());

        let vertical_buttons = Buttons {
            layout: Layout::Vertical,
            buttons: vec![button],
        };
        assert!(!vertical_buttons.is_horizontal_layout());
    }

    #[test]
    fn test_checkout_button_extract_tts_empty() {
        let button = CheckoutButton {
            size: "size-400".to_string(),
            text_lines: vec![],
            workflow_action: WorkflowAction {
                action_type: "action".to_string(),
                option_id: "option1".to_string(),
                confirmation_modal: ConfirmationModal {
                    message_body: None,
                    cancel_button_text: None,
                    accept_button_text: None,
                    title: None,
                },
            },
            test_id: None,
        };

        assert_eq!(button.extract_specific_tts(), None);
    }

    #[test]
    fn test_checkout_button_extract_tts_single_line() {
        let button = CheckoutButton {
            size: "size-400".to_string(),
            text_lines: vec!["Button Text".to_string()],
            workflow_action: WorkflowAction {
                action_type: "action".to_string(),
                option_id: "option1".to_string(),
                confirmation_modal: ConfirmationModal {
                    message_body: None,
                    cancel_button_text: None,
                    accept_button_text: None,
                    title: None,
                },
            },
            test_id: None,
        };

        assert_eq!(
            button.extract_specific_tts(),
            Some(TextContent::String("Button Text".to_string()))
        );
    }

    #[test]
    fn test_checkout_button_extract_tts_multiple_lines() {
        let button = CheckoutButton {
            size: "size-800".to_string(),
            text_lines: vec!["Line 1".to_string(), "Line 2".to_string()],
            workflow_action: WorkflowAction {
                action_type: "action".to_string(),
                option_id: "option1".to_string(),
                confirmation_modal: ConfirmationModal {
                    message_body: None,
                    cancel_button_text: None,
                    accept_button_text: None,
                    title: None,
                },
            },
            test_id: None,
        };

        assert_eq!(
            button.extract_specific_tts(),
            Some(TextContent::String("Line 1 Line 2".to_string()))
        );
    }
}
