use network::RequestError;
use network_parser::core::network_parse_from_str;

use super::view_data::ViewData;

#[allow(dead_code)]
pub fn view_data_parser(view_data: String) -> Result<ViewData, RequestError> {
    network_parse_from_str(&view_data).map_err(RequestError::DeserializationFailed)
}

#[cfg(test)]
mod tests {
    macro_rules! generate_parse_tests {
        ($($name:ident: $value:expr,)*) => {
        $(
            #[test]
            fn $name() {
                use super::view_data_parser;
                let result = view_data_parser(include_str!($value).to_owned());
                result.expect("should be OK");
            }
        )*
        }
    }

    generate_parse_tests! {
        test_parse_view_data_01:  "../../test_data/bolt_confirmation_page_view_data/single_tier_bolt_confirmation_page_1883.json",
        test_parse_view_data_v2_02: "../../test_data/bolt_confirmation_page_view_data_v2/channels_single_monthly_offer_NOR.json",
        test_parse_view_data_v2_03: "../../test_data/bolt_confirmation_page_view_data_v2/channels_single_monthly_offer.json",
        test_parse_view_data_v2_04: "../../test_data/bolt_confirmation_page_view_data_v2/channels_monthly_and_annual_offers_NOR.json",
        test_parse_view_data_v2_05: "../../test_data/bolt_confirmation_page_view_data_v2/channels_single_monthly_offer_missing_fields.json",
        test_parse_view_data_v2_06: "../../test_data/bolt_confirmation_page_view_data_v2/prime.json",
    }

    #[test]
    #[should_panic]
    fn should_panic_on_failure() {
        use super::view_data_parser;
        let response = "{}".to_string();
        let parsed_response = view_data_parser(response);
        parsed_response.unwrap();
    }

    macro_rules! generate_invalid_tests {
        ($($name:ident: $value:expr,)*) => {
        $(
            #[test]
            #[should_panic]
            fn $name() {
                use super::view_data_parser;
                let response = include_str!($value).to_owned();
                let parsed_response = view_data_parser(response);
                parsed_response.unwrap();
            }
        )*
        }
    }

    generate_invalid_tests! {
        // Bolt confirmation page V1
        test_parser_rejects_missing_buttons_section: "../../test_data/bolt_confirmation_page_view_data/invalid_view_data/single_tier_bolt_confirmation_page_1883_missing_rightSectionButtons.json",
        test_parser_rejects_buttons_missing_layout: "../../test_data/bolt_confirmation_page_view_data/invalid_view_data/single_tier_bolt_confirmation_page_1883_rightSectionButtons_missing_layout.json",
        test_parser_rejects_buttons_invalid_layout: "../../test_data/bolt_confirmation_page_view_data/invalid_view_data/single_tier_bolt_confirmation_page_1883_rightSectionButtons_invalid_layout.json",
        test_parser_rejects_buttons_missing_buttons_array: "../../test_data/bolt_confirmation_page_view_data/invalid_view_data/single_tier_bolt_confirmation_page_1883_rightSectionButtons_missing_buttons.json",
        test_parser_rejects_button_missing_size: "../../test_data/bolt_confirmation_page_view_data/invalid_view_data/single_tier_bolt_confirmation_page_1883_rightSectionButtons_button_missing_size.json",
        test_parser_rejects_button_invalid_size: "../../test_data/bolt_confirmation_page_view_data/invalid_view_data/single_tier_bolt_confirmation_page_1883_rightSectionButtons_button_invalid_size.json",

        //  Bolt confirmation page V2 (new api)
        test_parser_v2_rejects_missing_legal: "../../test_data/bolt_confirmation_page_view_data_v2/invalid_view_data/missing_legal.json",
        test_parser_v2_rejects_missing_payment: "../../test_data/bolt_confirmation_page_view_data_v2/invalid_view_data/missing_payment.json",
        test_parser_v2_rejects_missing_main_product: "../../test_data/bolt_confirmation_page_view_data_v2/invalid_view_data/missing_main_product.json",
        test_parser_v2_rejects_invalid_primary_offer: "../../test_data/bolt_confirmation_page_view_data_v2/invalid_view_data/invalid_primary_offer.json",
        //FIXME: currently receiving modal with null fields, although smithy modal fields are @required -> clarify why that happens
        // test_parser_v2_rejects_invalid_disclosure_modal: "../../test_data/bolt_confirmation_page_view_data_v2/invalid_view_data/invalid_disclosure_modal.json",
    }
}
