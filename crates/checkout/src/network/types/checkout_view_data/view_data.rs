use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

use super::confirmation_page_view_data::{
    bolt_single_tier_confirmation::ConfirmationBoltSingleTierViewData,
    bolt_single_tier_confirmation_v2::ConfirmationBoltSingleTierViewDataV2,
};

#[derive_test_only(Debug, PartialEq)]
#[derive(NetworkParsed, Clone)]
#[network(camelCaseAll, untagged)]
#[allow(dead_code)]
#[allow(clippy::large_enum_variant)]
pub enum ViewData {
    ConfirmationBoltSingleTierViewData(ConfirmationBoltSingleTierViewData),
    ConfirmationBoltSingleTierViewDataV2(ConfirmationBoltSingleTierViewDataV2),
    //Add new view data variants here...
}
