use amzn_fable_tokens::{FableColor, FableSpacing};
use fableous::{
    typography::type_ramp::TypeRamp,
    utils::{get_ignx_color, parse_color_name, parse_spacing_name},
};
use ignx_compositron::{color::Color, font::FontWeight, text::TextContent};
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

use crate::utils::tts::TTSExtractable;

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct LrdView {
    pub width: Option<String>,
    pub content: Vec<LrdContent>,
}

#[derive(PartialEq, NetworkParsed, Clone, Debug)]
#[network(camelCaseAll, tag = "type")]
pub enum LrdContent {
    #[network(rename = "text")]
    Text(TextPropertiesContainer),
    #[network(rename = "textrow")]
    TextRow(TextRowPropertiesContainer),
    #[network(rename = "image")]
    Image(ImagePropertiesContainer),
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct LrdStyling {
    #[network(default, rename = "marginTop")]
    pub margin_top: Option<String>,
    #[network(default, rename = "marginStart")]
    pub margin_start: Option<String>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct TextPropertiesContainer {
    pub properties: TextProperties,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct TextRowPropertiesContainer {
    pub properties: Vec<TextProperties>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct TextProperties {
    #[network(default)]
    pub typography: Option<String>,
    pub content: String,
    #[network(default, rename = "testID")]
    pub test_id: Option<String>,
    #[network(default)]
    pub styling: Option<LrdStyling>,
    #[network(default)]
    pub color: Option<String>,
    #[network(default, rename = "fontWeight")]
    pub font_weight: Option<String>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct ImagePropertiesContainer {
    pub properties: ImageProperties,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct ImageProperties {
    #[network(rename = "downstreamImageUrl")]
    pub downstream_image_url: String,
    pub height: f32,
    pub width: f32,
    #[network(rename = "testID")]
    pub test_id: Option<String>,
    pub styling: Option<LrdStyling>,
}

impl ImageProperties {
    pub fn get_top_margin(&self) -> f32 {
        self.styling
            .as_ref()
            .and_then(|styling| styling.margin_top.as_ref())
            .map_or(FableSpacing::NONE, |str| parse_spacing_name(str))
    }

    pub fn get_start_margin(&self) -> f32 {
        self.styling
            .as_ref()
            .and_then(|styling| styling.margin_start.as_ref())
            .map_or(FableSpacing::NONE, |str| parse_spacing_name(str))
    }

    pub fn get_test_id(&self, item_path: &str, suffix: &str) -> String {
        self.test_id
            .clone()
            .unwrap_or_else(|| format!("{}-{}", item_path, suffix))
    }

    pub fn get_image_url(&self) -> String {
        self.downstream_image_url.clone()
    }

    pub fn get_width(&self) -> f32 {
        self.width
    }

    pub fn get_height(&self) -> f32 {
        self.height
    }
}

impl TextProperties {
    pub fn get_fable_type_ramp(&self) -> TypeRamp {
        self.typography.as_deref().unwrap_or("body-200").into()
    }

    pub fn get_paragraphs(&self) -> Vec<String> {
        let content = self.content.clone();
        content.split("\n\n").map(|s| s.to_string()).collect()
    }

    pub fn get_color(&self) -> Color {
        let fable_color = self
            .color
            .as_deref()
            .and_then(parse_color_name)
            .unwrap_or(FableColor::PRIMARY);
        get_ignx_color(fable_color)
    }

    pub fn get_top_margin(&self) -> f32 {
        self.styling
            .as_ref()
            .and_then(|styling| styling.margin_top.as_ref())
            .map_or(FableSpacing::NONE, |str| parse_spacing_name(str))
    }

    pub fn get_start_margin(&self) -> f32 {
        self.styling
            .as_ref()
            .and_then(|styling| styling.margin_start.as_ref())
            .map_or(FableSpacing::NONE, |str| parse_spacing_name(str))
    }

    pub fn get_test_id(&self, item_path: &str, suffix: &str) -> String {
        self.test_id
            .clone()
            .unwrap_or_else(|| format!("{}-{}", item_path, suffix))
    }

    pub fn get_font_weight(&self) -> Option<FontWeight> {
        self.font_weight.clone().and_then(|s| Some(s.into()))
    }
}

impl TTSExtractable for LrdView {
    fn extract_context_tts(&self) -> Vec<TextContent> {
        let mut result = Vec::new();

        for component in &self.content {
            result.extend(component.extract_context_tts());
        }

        result
    }
}

fn add_text_tts(properties: &TextProperties, messages: &mut Vec<TextContent>) {
    for line in properties.get_paragraphs() {
        if !line.trim().is_empty() {
            messages.push(TextContent::String(line));
        }
    }
}

impl TTSExtractable for LrdContent {
    fn extract_context_tts(&self) -> Vec<TextContent> {
        let mut result = Vec::new();

        match &self {
            LrdContent::Text(TextPropertiesContainer { properties }) => {
                add_text_tts(properties, &mut result);
            }
            LrdContent::TextRow(TextRowPropertiesContainer { properties }) => {
                for text_properties in properties {
                    add_text_tts(text_properties, &mut result);
                }
            }
            LrdContent::Image(_) => {}
        }

        result
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use amzn_fable_tokens::{FableColor, FableSpacing};

    fn create_test_text_properties() -> TextProperties {
        TextProperties {
            typography: None,
            content: "".to_string(),
            test_id: None,
            styling: None,
            color: None,
            font_weight: None,
        }
    }

    fn create_test_image_properties() -> ImageProperties {
        ImageProperties {
            test_id: None,
            styling: None,
            downstream_image_url: "url".to_string(),
            height: 100.0,
            width: 100.0,
        }
    }

    #[test]
    fn test_get_fable_type_ramp() {
        let props = create_test_text_properties();
        assert_eq!(props.get_fable_type_ramp(), TypeRamp::from("body-200"));

        let mut props = create_test_text_properties();
        props.typography = Some("heading-800".to_string());
        assert_eq!(props.get_fable_type_ramp(), TypeRamp::from("heading-800"));
    }

    #[test]
    fn test_get_paragraphs() {
        let props = create_test_text_properties();
        assert_eq!(props.get_paragraphs(), vec!["".to_string()]);

        let mut props = create_test_text_properties();
        props.content = "First paragraph\n\nSecond paragraph\n\nThird paragraph".to_string();
        let paragraphs = props.get_paragraphs();
        assert_eq!(paragraphs.len(), 3);
        assert_eq!(
            paragraphs,
            vec![
                "First paragraph".to_string(),
                "Second paragraph".to_string(),
                "Third paragraph".to_string(),
            ]
        );
    }

    #[test]
    fn test_get_color() {
        let props = create_test_text_properties();
        assert_eq!(props.get_color(), get_ignx_color(FableColor::PRIMARY));

        let mut props = create_test_text_properties();
        props.color = Some("Secondary".to_string());
        assert_eq!(props.get_color(), get_ignx_color(FableColor::SECONDARY));
    }

    #[test]
    fn test_get_top_margin() {
        let props = create_test_text_properties();
        assert_eq!(props.get_top_margin(), FableSpacing::NONE);

        let mut props = create_test_text_properties();
        props.styling = Some(LrdStyling {
            margin_top: None,
            margin_start: None,
        });
        assert_eq!(props.get_top_margin(), FableSpacing::NONE);

        let mut props = create_test_image_properties();
        props.styling = Some(LrdStyling {
            margin_top: Some("spacing-050".to_string()),
            margin_start: Some("spacing-200".to_string()),
        });
        assert_eq!(props.get_top_margin(), FableSpacing::SPACING050);
        assert_eq!(props.get_start_margin(), FableSpacing::SPACING200);
    }

    #[test]
    fn test_get_test_id() {
        let props = create_test_text_properties();
        assert_eq!(
            props.get_test_id("section-path", "content-type"),
            "section-path-content-type"
        );

        let mut props = create_test_image_properties();
        props.test_id = Some("custom-test-id".to_string());
        assert_eq!(
            props.get_test_id("section-path", "content-type"),
            "custom-test-id"
        );
    }

    #[test]
    fn test_get_image_url() {
        let mut props = create_test_image_properties();
        props.downstream_image_url = "https://example.com/image.png".to_string();
        assert_eq!(
            props.get_image_url(),
            "https://example.com/image.png".to_string()
        );
    }

    #[test]
    fn test_get_font_weight() {
        let props = create_test_text_properties();
        assert_eq!(props.get_font_weight(), None);

        let mut props = create_test_text_properties();
        props.font_weight = Some("heavy".to_string());
        assert_eq!(props.get_font_weight(), Some(FontWeight::Heavy));
    }

    #[test]
    fn test_get_dimensions() {
        let props = create_test_image_properties();
        assert_eq!(props.get_width(), 100.0);
        assert_eq!(props.get_height(), 100.0);

        let mut props = create_test_image_properties();
        props.width = 200.0;
        props.height = 150.0;
        assert_eq!(props.get_width(), 200.0);
        assert_eq!(props.get_height(), 150.0);
    }

    #[test]
    fn test_lrd_view_extract_tts_empty() {
        let view = LrdView {
            width: None,
            content: Vec::new(),
        };
        let result = view.extract_context_tts();
        assert!(result.is_empty());
    }

    #[test]
    fn test_lrd_view_extract_tts_with_content() {
        let view = LrdView {
            width: None,
            content: vec![
                LrdContent::Text(TextPropertiesContainer {
                    properties: TextProperties {
                        content: "Paragraph 1\n\nParagraph 2".to_string(),
                        typography: None,
                        test_id: None,
                        styling: None,
                        color: None,
                        font_weight: None,
                    },
                }),
                LrdContent::TextRow(TextRowPropertiesContainer {
                    properties: vec![
                        TextProperties {
                            content: "Paragraph 3".to_string(),
                            typography: None,
                            test_id: None,
                            styling: None,
                            color: None,
                            font_weight: None,
                        },
                        TextProperties {
                            content: "Paragraph 4".to_string(),
                            typography: None,
                            test_id: None,
                            styling: None,
                            color: None,
                            font_weight: None,
                        },
                    ],
                }),
            ],
        };

        let result = view.extract_context_tts();
        assert_eq!(result.len(), 4);
        assert_eq!(result[0], TextContent::String("Paragraph 1".to_string()));
        assert_eq!(result[1], TextContent::String("Paragraph 2".to_string()));
        assert_eq!(result[2], TextContent::String("Paragraph 3".to_string()));
        assert_eq!(result[3], TextContent::String("Paragraph 4".to_string()));
    }

    #[test]
    fn test_lrd_content_extract_tts_empty() {
        let content = LrdContent::Text(TextPropertiesContainer {
            properties: TextProperties {
                content: "".to_string(),
                typography: None,
                test_id: None,
                styling: None,
                color: None,
                font_weight: None,
            },
        });

        let result = content.extract_context_tts();
        assert!(result.is_empty());
    }

    #[test]
    fn test_lrd_content_extract_tts_with_paragraphs() {
        let content = LrdContent::Text(TextPropertiesContainer {
            properties: TextProperties {
                content: "First paragraph\n\nSecond paragraph\n\n  ".to_string(),
                typography: None,
                test_id: None,
                styling: None,
                color: None,
                font_weight: None,
            },
        });

        let result = content.extract_context_tts();
        assert_eq!(result.len(), 2);
        assert_eq!(
            result[0],
            TextContent::String("First paragraph".to_string())
        );
        assert_eq!(
            result[1],
            TextContent::String("Second paragraph".to_string())
        );
    }
}
