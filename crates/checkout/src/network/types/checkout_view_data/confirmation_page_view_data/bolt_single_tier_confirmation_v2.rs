use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

use crate::network::types::checkout_view_data::view_data_v2_common::{
    action::ActionPointer, legal::Legal, payment::PresentablePayment, product::Product,
};

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
#[network(camelCaseAll)]
pub struct ConfirmationBoltSingleTierViewDataV2 {
    pub legal: Legal,
    pub payment: PresentablePayment,
    #[network(alias = "mainProduct")]
    pub main_product: Product,
    #[network(alias = "prerequisiteProducts")]
    pub prerequisite_products: Vec<Product>,
    #[network(alias = "confirmOrderAction")]
    pub confirm_order_action: ActionPointer,
    #[network(alias = "cancelOrderAction")]
    pub cancel_order_action: Option<ActionPointer>,
}
