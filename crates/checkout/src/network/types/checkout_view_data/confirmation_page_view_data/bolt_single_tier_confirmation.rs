use ignx_compositron::text::TextContent;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

use crate::{
    network::types::checkout_view_data::{common::Buttons, lrd_common::LrdView},
    utils::tts::TTSExtractable,
};

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
#[network(camelCaseAll)]
pub struct ConfirmationBoltSingleTierViewData {
    #[network(alias = "topView")]
    pub top_view: Option<LrdView>,

    #[network(alias = "leftSection")]
    pub left_section: Option<LrdView>,

    #[network(alias = "rightSectionTopView")]
    pub right_section_top_view: Option<LrdView>,

    #[network(alias = "rightSectionButtons")]
    pub right_section_buttons: Buttons,

    #[network(alias = "rightSectionBottomView")]
    pub right_section_bottom_view: Option<LrdView>,

    #[network(alias = "bottomView")]
    pub bottom_view: Option<LrdView>,

    #[network(alias = "backgroundImage")]
    pub background_image: Option<String>,
}

impl TTSExtractable for ConfirmationBoltSingleTierViewData {
    fn extract_context_tts(&self) -> Vec<TextContent> {
        // Combine context TTS from all sections except button-specific content
        let mut result = Vec::new();

        if let Some(top_view) = &self.top_view {
            result.extend(top_view.extract_context_tts());
        }

        if let Some(left_section) = &self.left_section {
            result.extend(left_section.extract_context_tts());
        }

        if let Some(right_section_top) = &self.right_section_top_view {
            result.extend(right_section_top.extract_context_tts());
        }

        // Intentionally skipping right_section_buttons here

        if let Some(right_section_bottom) = &self.right_section_bottom_view {
            result.extend(right_section_bottom.extract_context_tts());
        }

        if let Some(bottom_view) = &self.bottom_view {
            result.extend(bottom_view.extract_context_tts());
        }

        result
    }
}
