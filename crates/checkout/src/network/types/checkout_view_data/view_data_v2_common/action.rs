use ignx_compositron::base64::encode_url_safe;
use network_parser::{custom_network_parser, prelude::*};
use network_parser_derive::NetworkParsed;
use serde::Serialize;

#[derive(NetworkParsed, <PERSON>lone, PartialEq, Debug, Serialize)]
#[network(camelCaseAll)]
pub struct ActionPointer {
    pub button_text: String,
    pub description_text: Option<String>,
    pub action: Option<Action>,
}

#[derive(Clone, PartialEq, Debug, Serialize)]
pub struct Action {
    pub encoded_action_data: String,
}

custom_network_parser!(Action, |value: &Value| {
    Ok(Action {
        encoded_action_data: encode_action(value),
    })
});

fn encode_action(action_data: &Value) -> String {
    let bytes = action_data.to_string().into_bytes();
    encode_url_safe(&bytes)
}

#[cfg(test)]
mod tests {
    use serde_json::json;

    use super::*;

    #[test]
    fn test_encode_action() {
        let action_data = json!({
            "actionData": {
                "startCheckoutAction": null,
                "confirmOrderAction": {
                    "offerTokens": null,
                    "useRewardsAccount": null
                },
                "cancelOrderAction": null,
                "changeBillingAction": null,
                "changePaymentAction": null,
                "selectOfferAction": null,
                "requestSelectOfferAction": null,
                "pageRefreshAction": null,
                "enterPinAction": null
            }
        });

        let encoded_action = encode_action(&action_data);
        let expected_encoded_action = concat!(
            "eyJhY3Rpb25EYXRhIjp7ImNhbmNlbE9yZGVyQWN0aW9uIjpudWxsLCJjaGFuZ2VCaWxsaW5nQWN0aW9uIjpu",
            "dWxsLCJjaGFuZ2VQYXltZW50QWN0aW9uIjpudWxsLCJjb25maXJtT3JkZXJBY3Rpb24iOnsib2ZmZXJUb2tl",
            "bnMiOm51bGwsInVzZVJld2FyZHNBY2NvdW50IjpudWxsfSwiZW50ZXJQaW5BY3Rpb24iOm51bGwsInBhZ2VS",
            "ZWZyZXNoQWN0aW9uIjpudWxsLCJyZXF1ZXN0U2VsZWN0T2ZmZXJBY3Rpb24iOm51bGwsInNlbGVjdE9mZmVy",
            "QWN0aW9uIjpudWxsLCJzdGFydENoZWNrb3V0QWN0aW9uIjpudWxsfX0="
        );

        assert_eq!(encoded_action, expected_encoded_action);
    }
}
