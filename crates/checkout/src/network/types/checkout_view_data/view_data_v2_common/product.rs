use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

use crate::network::types::checkout_view_data::view_data_v2_common::action::ActionPointer;

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct Product {
    pub name: String,
    pub description: Option<String>,
    pub images: Images,
    #[network(alias = "primaryOffer")]
    pub primary_offer: OfferDetails,
    #[network(alias = "secondaryOffer")]
    pub secondary_offer: Option<TransactableOffer>,
    #[network(alias = "changePlanAction")]
    pub change_plan_action: Option<ActionPointer>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct Images {
    pub logo: Image,
    #[network(alias = "heroImage")]
    pub hero_image: Option<Image>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct Image {
    pub url: String,
    #[network(alias = "altText")]
    pub alt_text: Option<String>,
}

#[derive(NetworkParsed, <PERSON><PERSON>, PartialEq, Debug)]
pub struct TransactableOffer {
    pub offer: OfferDetails,
    #[network(alias = "confirmOrderAction")]
    pub confirm_order_action: ActionPointer,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct OfferDetails {
    #[network(alias = "planName")]
    pub plan_name: String,
    #[network(alias = "productName")]
    pub product_name: String,
    #[network(alias = "type")]
    pub offer_type: Option<String>,
    pub pricing: Pricing,
    #[network(alias = "planBenefits")]
    pub plan_benefits: Option<Vec<String>>,
    #[network(alias = "renewalInfo")]
    pub renewal_info: String,
    #[network(alias = "optionId")]
    pub option_id: Option<String>,
    #[network(alias = "toggleButtonText")]
    pub toggle_button_text: Option<String>,
    #[network(alias = "incentiveText")]
    pub incentive_text: Option<String>,
    #[network(alias = "isSparkled")]
    pub is_sparkled: Option<bool>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct Pricing {
    #[network(alias = "fullPriceDescription")]
    pub full_price_description: Option<String>,
    #[network(alias = "fullPriceDescriptionWithDueToday")]
    pub full_price_description_with_due_today: String,
    #[network(alias = "fullPriceDescriptionSummary", default)]
    pub full_price_description_summary: Option<String>,
    #[network(alias = "fullPriceDescriptionNextCharge", default)]
    pub full_price_description_next_charge: String,
    #[network(alias = "freeTrialInfo")]
    pub free_trial_info: Option<String>,
    #[network(alias = "dueTodayWithPrerequisites", default)]
    pub due_today_with_prerequisites: Option<String>,
    #[network(alias = "confirmButtonText")]
    pub confirm_button_text: Option<String>,
}
