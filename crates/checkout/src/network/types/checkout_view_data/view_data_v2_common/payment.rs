use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

use crate::network::types::checkout_view_data::view_data_v2_common::action::ActionPointer;

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct PresentablePayment {
    #[network(alias = "paymentMethodHeader")]
    payment_method_header: String,
    #[network(alias = "dueTodayText")]
    pub due_today_text: Option<String>,
    #[network(alias = "formattedPaymentDetailsList")]
    pub formatted_payment_details_list: Vec<String>,
    #[network(alias = "changePaymentAction")]
    pub change_payment_action: Option<ActionPointer>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct Payment {
    #[network(alias = "paymentPreferenceId")]
    payment_preference_id: String,
    #[network(alias = "walletType")]
    pub wallet_type: WalletType,
    #[network(alias = "paymentPreference")]
    pub payment_preference: PaymentPreference,
    #[network(alias = "paymentContract")]
    pub payment_contract: PaymentContract,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub enum WalletType {
    #[network(alias = "AMAZON_WALLET")]
    AmazonWallet,
    #[network(alias = "GOOGLE_WALLET")]
    GoogleWallet,
    #[network(alias = "APPLE_WALLET")]
    AppleWallet,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct PaymentPreference {
    #[network(alias = "paymentPreferenceId")]
    payment_preference_id: String,
    #[network(alias = "paymentMethods")]
    pub payment_methods: Vec<PaymentMethod>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct PaymentContract {
    #[network(alias = "paymentContractId")]
    payment_contract_id: String,
    #[network(alias = "paymentMethods")]
    pub payment_methods: Vec<PaymentMethod>,
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct PaymentMethod {
    #[network(alias = "paymentMethodId")]
    pub payment_method_id: String,
}
