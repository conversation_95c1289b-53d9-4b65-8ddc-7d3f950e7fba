use std::collections::HashMap;

use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

pub type Disclosures = HashMap<String, Disclosure>;

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct Legal {
    #[network(alias = "termsAndConditionsTitle")]
    pub terms_and_conditions_title: String,
    #[network(alias = "termsAndConditions")]
    pub terms_and_conditions: String,
    pub disclosures: Option<HashMap<String, Disclosure>>,
}

pub struct DisclosureKey {}
impl DisclosureKey {
    pub const LOCATION_DISCLOSURE: &str = "LOCATION_DISCLOSURE";
    pub const BLACKOUT_DISCLOSURE: &str = "BLACKOUT_DISCLOSURE";
    pub const PARTIAL_BLACKOUT_DISCLOSURE: &str = "PARTIAL_BLACKOUT_DISCLOSURE";
    pub const AD_DISCLOSURE: &str = "AD_DISCLOSURE";
    pub const OVERLAPPING_SUBSCRIPTIONS: &str = "OVERLAPPING_SUBSCRIPTIONS";
    pub const AUTO_RENEWAL: &str = "AUTO_RENEWAL";
    pub const VIEWERSHIP_DATA_SHARING: &str = "VIEWERSHIP_DATA_SHARING";
    pub const MARKETING_DATA_SHARING: &str = "MARKETING_DATA_SHARING";
    pub const CANCELLATION_DISCLOSURE: &str = "CANCELLATION_DISCLOSURE";
    pub const DISCLAIMER: &str = "DISCLAIMER";
}

#[derive(NetworkParsed, Clone, PartialEq, Debug)]
pub struct Disclosure {
    #[network(alias = "explicitConsentRequired")]
    pub explicit_consent_required: bool,
    pub text: String,
    #[network(alias = "messageType")]
    message_type: String,
    title: Option<String>,
    #[network(alias = "errorMessage")]
    pub error_message: Option<String>,
}
