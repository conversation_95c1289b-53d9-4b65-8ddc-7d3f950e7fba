use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

use super::workflow_v2_raw_response::{
    StepOption, TaskDataType, WorkflowV2RawResponse, WorkflowV2ServiceData,
};

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct WorkflowV2Response {
    pub task_data: TaskDataType,
    pub options: Vec<StepOption>,
    pub service_data: WorkflowV2ServiceData,
}

#[allow(dead_code)]
pub fn convert_workflow_v2_raw_response_to_workflow_v2_response(
    mut raw_response: WorkflowV2RawResponse,
) -> WorkflowV2Response {
    let task_data = raw_response.client_contracts.remove(0).task_data;

    WorkflowV2Response {
        task_data,
        options: raw_response.options.clone(),
        service_data: raw_response.service_data.clone(),
    }
}

#[cfg(test)]
mod tests {
    use core::assert_eq;

    use super::*;
    use crate::network::types::callback_types::CallbackVariation;
    use crate::network::types::callback_types::CommonCallbackFields;
    use crate::network::types::callback_types::StandardCallback;
    use crate::network::types::widget_types::CommonWidgetFields;
    use crate::network::types::widget_types::WidgetVariation;
    use crate::network::types::workflow_v2_raw_response::StepOption;
    use crate::network::types::workflow_v2_raw_response::TaskDataType;
    use crate::network::types::workflow_v2_raw_response::WorkflowResponseClientContract;

    #[test]
    fn test_convert_workflow_v2_raw_response_for_widget_to_workflow_v2_response() {
        let raw_response = WorkflowV2RawResponse {
            client_contracts: vec![WorkflowResponseClientContract {
                task_type: "task_type".to_string(),
                task_data: TaskDataType::Widget(WidgetVariation::StandardWidget(CommonWidgetFields {
                    template_data: "template_data".to_string(),
                    template_id: "template_id".to_string(),
                    view_data: "{\"backgroundImage\":\"https://example.com/image.jpg\",\"horizontalAlign\":\"center\"}".to_string(),
                    process_viewer_url: "process_viewer_url".to_string(),
                }))
            }],
            options: vec![StepOption {
                next_option: "next_option".to_string(),
                option_id: "option_id".to_string(),
                action: "action".to_string(),
            }],
            service_data: WorkflowV2ServiceData {
                data: "service_data".to_string(),
                version: "2".to_string(),
            },
            original_response: None
        };

        let response =
            convert_workflow_v2_raw_response_to_workflow_v2_response(raw_response.clone());

        assert_eq!(
            response.options[0].next_option,
            raw_response.options[0].next_option
        );
        assert_eq!(
            response.options[0].option_id,
            raw_response.options[0].option_id
        );
        assert_eq!(response.options[0].action, raw_response.options[0].action);

        assert_eq!(response.service_data.data, raw_response.service_data.data);
        assert_eq!(
            response.service_data.version,
            raw_response.service_data.version
        );

        match response.task_data {
            TaskDataType::Widget(widget) => match widget {
                WidgetVariation::StandardWidget(fields) => {
                    assert_eq!(fields.template_data, "template_data");
                    assert_eq!(fields.template_id, "template_id");
                    assert_eq!(fields.view_data, "{\"backgroundImage\":\"https://example.com/image.jpg\",\"horizontalAlign\":\"center\"}");
                }
            },
            _ => panic!("Expected TaskDataType::Widget"),
        }
    }

    #[test]
    fn test_convert_workflow_v2_raw_response_for_callback_to_workflow_v2_response() {
        let raw_response = WorkflowV2RawResponse {
            client_contracts: vec![WorkflowResponseClientContract {
                task_type: "task_type".to_string(),
                task_data: TaskDataType::Callback(CallbackVariation::StandardCallback(
                    StandardCallback {
                        common_callback_fields: CommonCallbackFields {
                            callback_id: "callback_id".to_string(),
                            process_viewer_url: "process_viewer_url".to_string(),
                        },
                    },
                )),
            }],
            options: vec![StepOption {
                next_option: "next_option".to_string(),
                option_id: "option_id".to_string(),
                action: "action".to_string(),
            }],
            service_data: WorkflowV2ServiceData {
                data: "service_data".to_string(),
                version: "2".to_string(),
            },
            original_response: None,
        };

        let response =
            convert_workflow_v2_raw_response_to_workflow_v2_response(raw_response.clone());

        assert_eq!(
            response.options[0].next_option,
            raw_response.options[0].next_option
        );
        assert_eq!(
            response.options[0].option_id,
            raw_response.options[0].option_id
        );
        assert_eq!(response.options[0].action, raw_response.options[0].action);

        assert_eq!(response.service_data.data, raw_response.service_data.data);
        assert_eq!(
            response.service_data.version,
            raw_response.service_data.version
        );

        match response.task_data {
            TaskDataType::Callback(callback) => match callback {
                CallbackVariation::StandardCallback(callback) => {
                    assert_eq!(callback.common_callback_fields.callback_id, "callback_id");
                    assert_eq!(
                        callback.common_callback_fields.process_viewer_url,
                        "process_viewer_url"
                    );
                }
                _ => panic!("Expected CallbackVariation::StandardCallback"),
            },
            _ => panic!("Expected TaskDataType::Callback"),
        }
    }
}
