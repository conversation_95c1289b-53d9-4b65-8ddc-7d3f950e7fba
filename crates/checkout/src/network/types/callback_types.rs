use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll, untagged)]
#[allow(dead_code)]
pub enum CallbackVariation {
    RetentionCallback(RetentionCallback),
    StandardCallback(StandardCallback),
}

impl CallbackVariation {
    pub fn get_common_fields(&self) -> CommonCallbackFields {
        match self {
            CallbackVariation::RetentionCallback(retention_callback) => {
                retention_callback.common_callback_fields.clone()
            }
            CallbackVariation::StandardCallback(standard_callback) => {
                standard_callback.common_callback_fields.clone()
            }
        }
    }
}

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct StandardCallback {
    #[network(flatten)]
    pub common_callback_fields: CommonCallbackFields,
}

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct RetentionCallback {
    #[network(flatten)]
    pub common_callback_fields: CommonCallbackFields,
    pub retention_string_id: String,
    pub retention_update_result: String,
}

#[derive(NetworkParsed, Clone, Debug)]
#[network(camelCaseAll)]
#[allow(dead_code)]
pub struct CommonCallbackFields {
    pub callback_id: String,
    pub process_viewer_url: String,
}
