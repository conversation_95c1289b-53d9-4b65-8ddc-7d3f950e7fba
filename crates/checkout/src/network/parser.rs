use ignx_compositron::prelude::AppContext;
use network::common::{DeviceProxyResponse, LRCEdgeResponse};
use network::RequestError;
use network_parser::core::network_parse_from_str;

use super::types::workflow_v2_raw_response::WorkflowV2RawResponse;

pub fn cos_response_parser_callback(
    _ctx: &AppContext,
    response: String,
    _status_code: i32,
    cb: Box<dyn FnOnce(Result<DeviceProxyResponse<WorkflowV2RawResponse>, RequestError>)>,
) {
    let result = cos_response_parser(response);
    cb(result)
}

pub fn cos_response_parser(
    response: String,
) -> Result<DeviceProxyResponse<WorkflowV2RawResponse>, RequestError> {
    let mut parsed: WorkflowV2RawResponse =
        network_parse_from_str(&response).map_err(RequestError::DeserializationFailed)?;
    parsed.original_response = Some(response);

    let parsed = LRCEdgeResponse::with_resource(parsed);
    let parsed = DeviceProxyResponse::LRCEdgeResponse(parsed);

    Ok(parsed)
}

#[cfg(test)]
mod tests {
    macro_rules! generate_parse_tests {
        ($($name:ident: $value:expr,)*) => {
        $(
            #[test]
            fn $name() {
                use super::cos_response_parser;
                let result = cos_response_parser(include_str!($value).to_owned());
                result.expect("should be OK");
            }
        )*
        }
    }

    generate_parse_tests! {
        test_parse_cos_response_multi_button_page: "test_data/cos_response_multi_button_page.json",
        test_parse_cos_response_channel_confirmation: "test_data/cos_response_channel_confirmation.json",
        test_parse_cos_response_rearchitecture: "test_data/cos_response_rearchitecture.json",

    }

    #[test]
    #[should_panic]
    fn should_panic_on_failure() {
        use super::cos_response_parser;
        let response = "{}".to_string();
        let parsed_response = cos_response_parser(response);
        parsed_response.unwrap();
    }
}
