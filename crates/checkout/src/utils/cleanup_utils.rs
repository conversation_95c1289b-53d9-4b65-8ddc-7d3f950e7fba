use ignx_compositron::prelude::{use_context, <PERSON>ope, SignalSet};
use media_background::types::TrailerPlaybackContext;
use navigation_menu::context::nav_context::NavControl;

pub fn cleanup_checkout(scope: Scope) {
    reset_nav_control(scope);
    reset_playback_context(scope);
}

fn reset_nav_control(scope: Scope) {
    if let Some(nav_control) = use_context::<NavControl>(scope) {
        nav_control.show_utility_nav.set(true);
        nav_control.show_top_nav_intent.set(true);
    }
}

fn reset_playback_context(scope: Scope) {
    if let Some(playback_context) = use_context::<TrailerPlaybackContext>(scope) {
        playback_context.suppress_trailer_playback.set(false);
    }
}
