use ignx_compositron::text::TextContent;

pub trait TTSExtractable {
    /// Extract all text content to be used for accessibility/TTS
    fn extract_tts(&self) -> Vec<TextContent> {
        self.extract_context_tts()
    }

    /// Extract context text content (excluding element-specific messages like button labels)
    fn extract_context_tts(&self) -> Vec<TextContent> {
        Vec::new()
    }

    /// Extract element-specific message (like a button label)
    fn extract_specific_tts(&self) -> Option<TextContent> {
        None
    }
}
