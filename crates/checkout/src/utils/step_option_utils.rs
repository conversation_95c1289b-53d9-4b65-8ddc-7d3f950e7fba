use crate::error::CheckoutError;
use crate::{
    checkout_controller::WorkflowStage, network::types::workflow_v2_raw_response::StepOption,
};

fn get_step_option_from_option_id(option_id: &str, options: &[StepOption]) -> Option<StepOption> {
    options
        .iter()
        .find(|&step_option| step_option.option_id == option_id)
        .cloned()
}

pub fn get_step_option_from_stage(
    option_id: &str,
    stage: Option<&WorkflowStage>,
) -> Result<StepOption, CheckoutError> {
    if let Some(stage) = stage {
        if let Some(next_option) = get_step_option_from_option_id(option_id, &stage.options) {
            return Ok(next_option);
        }
    }
    Err(CheckoutError::InvalidStepOptionError(format!(
        "Option '{}' not found. Available options: {}",
        option_id,
        stage.map_or_else(
            || String::from("none"),
            |s| {
                s.options
                    .iter()
                    .map(|opt| format!("'{}'", opt.option_id))
                    .collect::<Vec<_>>()
                    .join(", ")
            }
        )
    )))
}

#[cfg(test)]
mod tests {
    use crate::network::types::workflow_v2_raw_response::WorkflowV2ServiceData;

    use super::*;

    #[test]
    fn test_get_next_option_or_handle_error_gets_next_option() {
        let options = vec![
            StepOption {
                action: "action1".to_string(),
                option_id: "option1".to_string(),
                next_option: "next_option1".to_string(),
            },
            StepOption {
                action: "action2".to_string(),
                option_id: "option2".to_string(),
                next_option: "next_option2".to_string(),
            },
        ];

        let step_option = get_step_option_from_stage(
            "option1",
            Some(&WorkflowStage {
                options: options.clone(),
                workflow_data: WorkflowV2ServiceData {
                    data: "data".to_string(),
                    version: "version".to_string(),
                },
            }),
        )
        .expect("Should return Ok variant");

        assert_eq!(step_option.option_id, "option1".to_string());
        assert_eq!(step_option.action, "action1".to_string());
        assert_eq!(step_option.next_option, "next_option1".to_string());
    }

    #[test]
    fn test_get_next_option_or_handle_error_panics_if_option_not_found() {
        let options = vec![
            StepOption {
                action: "action1".to_string(),
                option_id: "XXXXXXX".to_string(),
                next_option: "next_option1".to_string(),
            },
            StepOption {
                action: "action2".to_string(),
                option_id: "XXXXXXX".to_string(),
                next_option: "next_option2".to_string(),
            },
        ];

        let result = get_step_option_from_stage(
            "option3",
            Some(&WorkflowStage {
                options: options.clone(),
                workflow_data: WorkflowV2ServiceData {
                    data: "data".to_string(),
                    version: "version".to_string(),
                },
            }),
        );

        assert!(matches!(
            result,
            Err(CheckoutError::InvalidStepOptionError(_))
        ));
    }

    #[test]
    fn test_get_next_option_or_handle_error_panics_if_no_stage_is_provided() {
        let result = get_step_option_from_stage("option3", None);

        assert!(matches!(
            result,
            Err(CheckoutError::InvalidStepOptionError(_))
        ));
    }
}
