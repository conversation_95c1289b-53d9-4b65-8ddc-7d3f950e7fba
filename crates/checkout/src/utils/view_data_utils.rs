use crate::{
    error::CheckoutError,
    network::types::checkout_view_data::{
        confirmation_page_view_data::bolt_single_tier_confirmation_v2::ConfirmationBoltSingleTierViewDataV2,
        view_data::ViewData,
    },
};

macro_rules! check_required_conditions {
    ($($check_expr:expr),* $(,)?) => {{
        $(
            let field_checker = || -> Option<bool> {
                Some($check_expr)
            };
            match field_checker() {
                Some(false) => {
                    return Err(CheckoutError::InvalidViewData(format!("failed condition: {}", stringify!($check_expr))));
                },
                _ => {}
            }
        )*
    }};
}

fn check_bolt_confirmation_v2_required_fields(
    view_data: &ConfirmationBoltSingleTierViewDataV2,
) -> Result<(), CheckoutError> {
    check_required_conditions!(
        view_data.payment.due_today_text.is_some(),
        view_data
            .main_product
            .primary_offer
            .pricing
            .due_today_with_prerequisites
            .is_some(),
        view_data
            .main_product
            .secondary_offer
            .as_ref()?
            .offer
            .pricing
            .due_today_with_prerequisites
            .is_some(),
    );

    Ok(())
}

pub fn validate_view_data(view_data: &ViewData) -> Result<(), CheckoutError> {
    match view_data {
        ViewData::ConfirmationBoltSingleTierViewDataV2(view_data) => {
            check_bolt_confirmation_v2_required_fields(view_data)?;
        }
        ViewData::ConfirmationBoltSingleTierViewData(_) => (),
    }
    Ok(())
}

fn check_bolt_confirmation_page_prerequisite_products(
    view_data: &ConfirmationBoltSingleTierViewDataV2,
) -> Result<(), CheckoutError> {
    if view_data.prerequisite_products.is_empty() {
        Ok(())
    } else {
        Err(CheckoutError::GatingError(
            "view data contains prerequisite products".to_string(),
        ))
    }
}

pub fn gate_view_data(view_data: &ViewData) -> Result<(), CheckoutError> {
    match view_data {
        ViewData::ConfirmationBoltSingleTierViewDataV2(view_data) => {
            check_bolt_confirmation_page_prerequisite_products(view_data)?
        }
        ViewData::ConfirmationBoltSingleTierViewData(_) => (),
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use ignx_compositron::lazy_static::lazy_static;

    use crate::{
        error::CheckoutError,
        network::types::checkout_view_data::{
            view_data::ViewData, view_data_parser::view_data_parser,
        },
        utils::view_data_utils::validate_view_data,
    };

    use super::gate_view_data;

    lazy_static! {
        pub static ref BOLT_CONFIRMATION_V2_VIEW_DATA: ViewData = view_data_parser(include_str!("../network/test_data/bolt_confirmation_page_view_data_v2/channels_monthly_and_annual_offers_NOR.json").to_string()).unwrap();
    }

    #[test]
    fn valid_bolt_confirmation_v2_view_data_passes_validation() {
        let check_result = validate_view_data(&BOLT_CONFIRMATION_V2_VIEW_DATA);

        assert!(check_result.is_ok());
    }

    #[test]
    fn bolt_confirmation_v2_with_missing_due_today_text_fails_validation() {
        let mut view_data = BOLT_CONFIRMATION_V2_VIEW_DATA.clone();

        if let ViewData::ConfirmationBoltSingleTierViewDataV2(data) = &mut view_data {
            data.payment.due_today_text = None;
        } else {
            panic!("");
        }

        let check_result = validate_view_data(&view_data);

        assert!(check_result.is_err());
        assert!(check_result.is_err_and(|error| match error {
            CheckoutError::InvalidViewData(error) =>
                error == "failed condition: view_data.payment.due_today_text.is_some()",
            _ => false,
        }));
    }

    #[test]
    fn valid_bolt_confirmation_v2_view_data_passes_gating() {
        let check_result = gate_view_data(&BOLT_CONFIRMATION_V2_VIEW_DATA);

        assert!(check_result.is_ok());
    }

    #[test]
    fn bolt_confirmation_v2_with_prerequisite_products_fails_gating() {
        let mut view_data = BOLT_CONFIRMATION_V2_VIEW_DATA.clone();

        if let ViewData::ConfirmationBoltSingleTierViewDataV2(data) = &mut view_data {
            data.prerequisite_products = vec![data.main_product.clone()];
        } else {
            panic!("")
        }

        let check_result = gate_view_data(&view_data);

        assert!(check_result.is_err());
        assert!(check_result.is_err_and(|error| match error {
            CheckoutError::GatingError(error) =>
                error == "view data contains prerequisite products",
            _ => false,
        }))
    }
}
