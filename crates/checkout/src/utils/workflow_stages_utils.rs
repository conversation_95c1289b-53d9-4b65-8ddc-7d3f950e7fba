use crate::{
    checkout_controller::WorkflowStage,
    network::types::workflow_v2_raw_response::WorkflowV2ServiceData,
};

pub fn get_optional_workflow_service_data_from_stages(
    stages: &Vec<WorkflowStage>,
) -> Option<WorkflowV2ServiceData> {
    if let Some(stage) = stages.last() {
        return Some(stage.workflow_data.clone());
    }

    None
}

#[cfg(test)]
mod tests {
    use crate::{
        checkout_controller::WorkflowStage,
        network::types::workflow_v2_raw_response::WorkflowV2ServiceData,
    };

    use super::get_optional_workflow_service_data_from_stages;

    #[test]
    fn test_get_optional_workflow_service_data_from_stages() {
        let stages = vec![
            WorkflowStage {
                workflow_data: WorkflowV2ServiceData {
                    data: "data1".to_string(),
                    version: "version1".to_string(),
                },
                options: vec![],
            },
            WorkflowStage {
                workflow_data: WorkflowV2ServiceData {
                    data: "data2".to_string(),
                    version: "version2".to_string(),
                },
                options: vec![],
            },
        ];

        let workflow_service_data = get_optional_workflow_service_data_from_stages(&stages);

        assert_eq!(
            workflow_service_data.unwrap(),
            WorkflowV2ServiceData {
                data: "data2".to_string(),
                version: "version2".to_string(),
            }
        );
    }

    #[test]
    fn test_get_optional_workflow_service_data_from_stages_empty() {
        let stages = vec![];

        let workflow_service_data = get_optional_workflow_service_data_from_stages(&stages);

        assert!(workflow_service_data.is_none());
    }
}
