use crate::impls::cards::IsTitleCard;
use auth::is_last_known_state_signed_in;
use common_transform_types::{
    actions::{Action, TransitionAction},
    containers::CommonCarouselTitleCardItem,
};
use container_types::container_item_parsing::common::get_title_card_contextual_menu_metadata;
use contextual_menu_types::buttons::WatchNowVariants;
use contextual_menu_types::prelude::{
    ContextualMenuButton, ContextualMenuData, ContextualMenuMetadata, ContextualMenuMetricsContext,
    ContextualMenuProperties, MoreDetailsButtonData, SubscribeButtonData, WatchNowButtonData,
    WatchlistButtonData,
};
use ignx_compositron::prelude::Scope;
use location::RustPage;
use watch_modal::helpers::event_card::create_watch_modal_data_for_event_cards;

#[cfg(test)]
fn should_show_watch_modal(scope: Scope, _is_resiliency_cx_enabled: Option<bool>) -> bool {
    use rust_features::use_mock_rust_features;
    use_mock_rust_features(scope).is_watch_modal_enabled()
}
#[cfg(not(test))]
use watch_modal::data_provider::should_show_watch_modal;

pub trait ToContextualMenu {
    fn contextual_menu_metadata(&self) -> ContextualMenuMetadata;
    fn contextual_menu(&self, scope: Scope, page_type: RustPage) -> Option<ContextualMenuData>;
}

impl<T> ToContextualMenu for T
where
    T: IsTitleCard,
{
    fn contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        get_title_card_contextual_menu_metadata(&self.to_common())
    }

    // This is only configured for search.
    // When onboarding the collections page, we need to update this logic to support both.
    fn contextual_menu(&self, scope: Scope, page_type: RustPage) -> Option<ContextualMenuData> {
        let metadata = self.contextual_menu_metadata();

        if self.is_event_card() && should_show_watch_modal(scope, None) {
            return create_watch_modal_data_for_event_cards(scope, &metadata);
        }

        let is_last_known_state_signed_in = is_last_known_state_signed_in(scope);
        let ref_marker = metadata
            .primary_action
            .as_ref()
            .map(|action| action.get_ref_marker());

        let mut buttons = vec![];

        let linear_vod_action = match self.to_common() {
            CommonCarouselTitleCardItem::SEASON(season) => season
                .linearAttributes
                .as_ref()
                .and_then(|linear_attributes| {
                    linear_attributes.linearAttributes.linearVodAction.clone()
                }),
            CommonCarouselTitleCardItem::MOVIE(movie) => movie
                .linearAttributes
                .as_ref()
                .and_then(|linear_attributes| linear_attributes.linearVodAction.clone()),
            _ => None,
        };

        if let Some(Action::TransitionAction(transition_action)) = linear_vod_action {
            if let Some(button) =
                linear_action_to_button(&transition_action, metadata.gti.clone(), false)
            {
                buttons.push(button);
            }
        }

        if let CommonCarouselTitleCardItem::EVENT(_) = self.to_common() {
            if let Some(is_in_watchlist) = metadata.is_in_watchlist {
                if is_last_known_state_signed_in {
                    buttons.push(ContextualMenuButton::AddToWatchlist(Box::new(
                        WatchlistButtonData {
                            id: "Watchlist".to_string(),
                            is_in_watchlist,
                            gti: metadata.gti.clone().unwrap_or_default(),
                            ref_marker: ref_marker.clone(),
                            on_select_cb: None,
                        },
                    )));
                }
            }
        }

        if let Some(action) = metadata.primary_action {
            buttons.push(ContextualMenuButton::MoreDetails(Box::new(
                MoreDetailsButtonData {
                    id: "MoreDetails".to_string(),
                    action,
                    on_select_cb: None,
                },
            )));
        }

        Some(ContextualMenuData::ContextualMenu(
            ContextualMenuProperties {
                title: metadata.title.unwrap_or_default(),
                gti: metadata.gti.unwrap_or_default(),
                metrics_context: ContextualMenuMetricsContext {
                    page_type,
                    is_linear_title: metadata.is_linear_title,
                },
                ref_marker,
                maturity_rating_image: metadata.maturity_rating_image,
                maturity_rating_string: metadata.maturity_rating_string,
                buttons,
                title_actions: None,
                event_metadata: metadata.event_metadata,
                metadata_row: None,
                tnf_properties: None,
                liveliness_data: metadata.liveliness_data,
                on_dismiss_cb: None,
                entitlement_label_data: None,
                journey_ingress_context: None,
                show_skeleton: None,
                content_type: metadata.content_type,
            },
        ))
    }
}

pub(crate) fn linear_action_to_button(
    action: &TransitionAction,
    gti: Option<String>,
    support_player_action: bool,
) -> Option<ContextualMenuButton> {
    match &action {
        TransitionAction::linearStationDetail(_) => Some(ContextualMenuButton::MoreDetails(
            Box::new(MoreDetailsButtonData {
                id: "MoreDetails".to_string(),
                action: action.clone(),
                on_select_cb: None,
            }),
        )),
        TransitionAction::signUp(_) => Some(ContextualMenuButton::Subscribe(Box::new(
            SubscribeButtonData {
                id: "Subscription".to_string(),
                action: action.clone(),
            },
        ))),
        TransitionAction::playback(_) => Some(ContextualMenuButton::WatchNow(Box::new(
            WatchNowButtonData {
                id: "WatchNow".to_string(),
                action: action.clone(),
                gti,
                seamless_transition: false,
                playback_origin: None,
                variant: WatchNowVariants::WatchNow,
            },
        ))),
        TransitionAction::player(_) if support_player_action => Some(
            ContextualMenuButton::WatchNow(Box::new(WatchNowButtonData {
                id: "WatchNow".to_string(),
                action: action.clone(),
                gti,
                seamless_transition: false,
                playback_origin: None,
                variant: WatchNowVariants::WatchLive,
            })),
        ),
        _ => None,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth::{AuthContext, MockAuth};
    use common_transform_types::{
        actions::{Action, TransitionAction},
        container_items::{
            CarouselItemData, EventCard, EventMetadata, GenericTitleCard, LinearAttributes,
            LinearSeriesAttributes, MovieCard, SeriesCard, TitleCardBaseMetadata,
        },
    };
    use ignx_compositron::{app::launch_only_app_context, prelude::provide_context};
    use rstest::*;
    use rust_features::MockRustFeaturesBuilder;
    use std::rc::Rc;

    const PRIMARY_REF_MARKER: &str = "primary_ref_marker";
    const SECONDARY_REF_MARKER: &str = "secondary_ref_marker";
    const TEST_GTI: &str = "test-gti";

    fn create_detail_action(ref_marker: &str) -> TransitionAction {
        TransitionAction::create_detail().with_ref_marker(ref_marker.to_string())
    }

    #[test]
    fn test_more_details_button_when_action_available() {
        launch_only_app_context(move |ctx| {
            let item = GenericTitleCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata::default(),
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 1);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::MoreDetails(_)
                ));
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_no_more_details_button_when_no_action_available() {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_watch_modal_enabled(true)
                .build_into_context(ctx.scope);

            let item = GenericTitleCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: None,
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata::default(),
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert!(props.buttons.is_empty());
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_watch_now_button_for_linear_movie_title() {
        launch_only_app_context(move |ctx| {
            let gti = TEST_GTI.to_string();
            let item = MovieCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    gti: Some(gti.clone()),
                    ..Default::default()
                },
                linearAttributes: Some(LinearAttributes {
                    isOnNow: Some(true),
                    schedule: None,
                    linearVodAction: Some(Action::TransitionAction(
                        TransitionAction::create_playback(),
                    )),
                    stationName: None,
                    upNextShowTitle: None,
                }),
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 2);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::WatchNow(_)
                ));
                assert!(matches!(
                    props.buttons[1],
                    ContextualMenuButton::MoreDetails(_)
                ));

                if let ContextualMenuButton::WatchNow(watch_now_data) = &props.buttons[0] {
                    assert_eq!(watch_now_data.id, "WatchNow");
                    assert_eq!(watch_now_data.gti, Some(gti));
                    assert_eq!(watch_now_data.seamless_transition, false);
                    assert_eq!(watch_now_data.playback_origin, None);
                }
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_watch_now_button_for_linear_season_title() {
        launch_only_app_context(move |ctx| {
            let gti = TEST_GTI.to_string();
            let item = SeriesCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    gti: Some(gti.clone()),
                    ..Default::default()
                },
                linearAttributes: Some(LinearSeriesAttributes {
                    linearAttributes: LinearAttributes {
                        isOnNow: Some(true),
                        schedule: None,
                        linearVodAction: Some(Action::TransitionAction(
                            TransitionAction::create_playback(),
                        )),
                        stationName: None,
                        upNextShowTitle: None,
                    },
                    episodeNumber: None,
                    episodeTitle: None,
                    episodeSynopsis: None,
                    episodeGti: None,
                }),
                seasonNumber: None,
                numberOfSeasons: None,
                showName: None,
                episodeNumber: None,
                episodicSynopsis: None,
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 2);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::WatchNow(_)
                ));
                assert!(matches!(
                    props.buttons[1],
                    ContextualMenuButton::MoreDetails(_)
                ));

                if let ContextualMenuButton::WatchNow(watch_now_data) = &props.buttons[0] {
                    assert_eq!(watch_now_data.id, "WatchNow");
                    assert_eq!(watch_now_data.gti, Some(gti));
                    assert_eq!(watch_now_data.seamless_transition, false);
                    assert_eq!(watch_now_data.playback_origin, None);
                }
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_subscribe_button_for_signup_action() {
        launch_only_app_context(move |ctx| {
            let item = MovieCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata::default(),
                linearAttributes: Some(LinearAttributes {
                    isOnNow: Some(true),
                    schedule: None,
                    linearVodAction: Some(Action::TransitionAction(
                        TransitionAction::create_sign_up(),
                    )),
                    stationName: None,
                    upNextShowTitle: None,
                }),
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 2);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::Subscribe(_)
                ));
                assert!(matches!(
                    props.buttons[1],
                    ContextualMenuButton::MoreDetails(_)
                ));

                if let ContextualMenuButton::Subscribe(subscribe_data) = &props.buttons[0] {
                    assert_eq!(subscribe_data.id, "Subscription");
                }
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_no_special_button_for_other_action_types() {
        launch_only_app_context(move |ctx| {
            let item = MovieCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata::default(),
                linearAttributes: Some(LinearAttributes {
                    isOnNow: Some(true),
                    schedule: None,
                    linearVodAction: Some(Action::TransitionAction(
                        TransitionAction::create_player(),
                    )),
                    stationName: None,
                    upNextShowTitle: None,
                }),
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 1);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::MoreDetails(_)
                ));
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn test_watchlist_button_for_event_items_signed_in(#[case] in_watchlist: bool) {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_watch_modal_enabled(false)
                .build_into_context(ctx.scope);

            let mut mock_auth = MockAuth::new_without_params(ctx.scope());
            mock_auth.set_access_token(Some("mock-token".to_string()));
            provide_context::<AuthContext>(ctx.scope(), Rc::new(mock_auth));

            let gti = TEST_GTI.to_string();
            let item = EventCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    gti: Some(gti.clone()),
                    isInWatchlist: Some(in_watchlist),
                    ..Default::default()
                },
                eventMetadata: EventMetadata {
                    liveliness: None,
                    liveEventDateBadge: None,
                    liveEventDateHeader: None,
                    venue: None,
                    scoreBug: None,
                },
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 2);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::AddToWatchlist(_)
                ));
                assert!(matches!(
                    props.buttons[1],
                    ContextualMenuButton::MoreDetails(_)
                ));

                if let ContextualMenuButton::AddToWatchlist(watchlist_data) = &props.buttons[0] {
                    assert_eq!(watchlist_data.id, "Watchlist");
                    assert_eq!(watchlist_data.is_in_watchlist, in_watchlist);
                    assert_eq!(watchlist_data.gti, gti);
                }
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_uses_watch_modal_for_event_cards_when_enabled() {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_watch_modal_enabled(true)
                .build_into_context(ctx.scope);

            let mock_auth = MockAuth::new_without_params(ctx.scope());
            provide_context::<AuthContext>(ctx.scope(), Rc::new(mock_auth));

            let item = EventCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    gti: Some(TEST_GTI.to_string()),
                    isInWatchlist: Some(true),
                    ..Default::default()
                },
                eventMetadata: EventMetadata {
                    liveliness: None,
                    liveEventDateBadge: None,
                    liveEventDateHeader: None,
                    venue: None,
                    scoreBug: None,
                },
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            assert!(matches!(cm_data, ContextualMenuData::WatchModal(_)));
        });
    }

    #[test]
    fn test_watchlist_button_for_event_items_not_signed_in() {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_watch_modal_enabled(false)
                .build_into_context(ctx.scope);

            let mock_auth = MockAuth::new_without_params(ctx.scope());
            provide_context::<AuthContext>(ctx.scope(), Rc::new(mock_auth));

            let item = EventCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    gti: Some(TEST_GTI.to_string()),
                    isInWatchlist: Some(true),
                    ..Default::default()
                },
                eventMetadata: EventMetadata {
                    liveliness: None,
                    liveEventDateBadge: None,
                    liveEventDateHeader: None,
                    venue: None,
                    scoreBug: None,
                },
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 1);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::MoreDetails(_)
                ));
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_no_watchlist_button_for_non_event_items() {
        launch_only_app_context(move |ctx| {
            let mut mock_auth = MockAuth::new_without_params(ctx.scope());
            mock_auth.set_access_token(Some("mock-token".to_string()));
            provide_context::<AuthContext>(ctx.scope(), Rc::new(mock_auth));

            let item = GenericTitleCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    isInWatchlist: Some(true),
                    ..Default::default()
                },
            };

            let cm_data = item
                .contextual_menu(ctx.scope(), RustPage::RUST_SEARCH)
                .unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.buttons.len(), 1);
                assert!(matches!(
                    props.buttons[0],
                    ContextualMenuButton::MoreDetails(_)
                ));
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }

    #[test]
    fn test_metrics_context() {
        launch_only_app_context(move |ctx| {
            let page_type = RustPage::RUST_SEARCH;

            let item = MovieCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("id".to_string()),
                    title: Some("title".to_string()),
                    synopsis: None,
                    action: Some(Action::TransitionAction(create_detail_action(
                        PRIMARY_REF_MARKER,
                    ))),
                    deferredAction: None,
                    actions: vec![],
                    widgetType: None,
                },
                titleCardBaseMetadata: TitleCardBaseMetadata::default(),
                linearAttributes: Some(LinearAttributes {
                    isOnNow: Some(true),
                    schedule: None,
                    linearVodAction: None,
                    stationName: None,
                    upNextShowTitle: None,
                }),
            };

            let cm_data = item.contextual_menu(ctx.scope(), page_type).unwrap();

            if let ContextualMenuData::ContextualMenu(props) = cm_data {
                assert_eq!(props.metrics_context.is_linear_title, true);
                assert_eq!(props.metrics_context.page_type, page_type);
            } else {
                panic!("Expected ContextualMenu variant");
            }
        });
    }
}
