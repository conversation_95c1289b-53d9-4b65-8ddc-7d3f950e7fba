use cfg_test_attr_derive::derive_test_only;
use explicit_signals_service::prelude::ExplicitSignalsServiceEntityType;
use ignx_compositron::prelude::RwSignal;
use std::fmt::Debug;

#[derive(Debug, Clone, PartialEq)]
pub enum FavoriteItemType {
    Team,
    League,
    Station,
}

impl ToString for FavoriteItemType {
    fn to_string(&self) -> String {
        match self {
            FavoriteItemType::Team => "Team".to_string(),
            FavoriteItemType::League => "League".to_string(),
            FavoriteItemType::Station => "Station".to_string(),
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum Domain {
    Sports,
    Linear,
}

impl ToString for Domain {
    fn to_string(&self) -> String {
        match self {
            Domain::Sports => "Sports".to_string(),
            Domain::Linear => "Linear".to_string(),
        }
    }
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct FavoriteItemMetadata {
    pub id: String,
    pub name: String,
    pub item_type: FavoriteItemType,
    pub domain: Domain,
    pub is_favorite: RwSignal<bool>,
}

impl Into<ExplicitSignalsServiceEntityType> for FavoriteItemType {
    fn into(self) -> ExplicitSignalsServiceEntityType {
        match self {
            FavoriteItemType::Team => ExplicitSignalsServiceEntityType::Team,
            FavoriteItemType::League => ExplicitSignalsServiceEntityType::League,
            FavoriteItemType::Station => ExplicitSignalsServiceEntityType::Station,
        }
    }
}

pub trait UseDefaultFavoriteData {}

pub trait HasFavoriteData {
    fn to_favorite_data(&self) -> Option<FavoriteItemMetadata>;
}

impl<T> HasFavoriteData for T
where
    T: UseDefaultFavoriteData,
{
    fn to_favorite_data(&self) -> Option<FavoriteItemMetadata> {
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_favorite_item_type_into_explicit_signals_service_entity_type() {
        assert_eq!(
            ExplicitSignalsServiceEntityType::Team,
            FavoriteItemType::Team.into()
        );
        assert_eq!(
            ExplicitSignalsServiceEntityType::League,
            FavoriteItemType::League.into()
        );
        assert_eq!(
            ExplicitSignalsServiceEntityType::Station,
            FavoriteItemType::Station.into()
        );
    }
}
