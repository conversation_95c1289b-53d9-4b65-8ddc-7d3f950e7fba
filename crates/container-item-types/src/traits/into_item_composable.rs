use crate::types::item_composable::ItemComposable;
use crate::types::updatable_properties::ToUpdatableProgressBarProps;
use fableous::cards::beard_supported_card::BeardSupportedCardDataProps;
use fableous::cards::chart_card::ChartCardDataProps;
use fableous::cards::portrait_card::PortraitCardDataProps;
use fableous::cards::sizing::CardSize;
use fableous::cards::small_card::SmallCardDataProps;
use fableous::cards::standard_card::StandardCardDataProps;

pub trait IntoItemComposable: Clone + ToUpdatableProgressBarProps {
    fn into_item_composable(self) -> (ItemComposable, CardSize);

    fn to_card_size(&self) -> CardSize;
}

impl IntoItemComposable for Box<StandardCardDataProps> {
    fn into_item_composable(self) -> (ItemComposable, CardSize) {
        (ItemComposable::StandardCard(self), CardSize::Standard)
    }

    fn to_card_size(&self) -> CardSize {
        CardSize::Standard
    }
}

impl IntoItemComposable for Box<SmallCardDataProps> {
    fn into_item_composable(self) -> (ItemComposable, CardSize) {
        (ItemComposable::SmallCard(self), CardSize::Small)
    }

    fn to_card_size(&self) -> CardSize {
        CardSize::Small
    }
}

impl IntoItemComposable for Box<ChartCardDataProps> {
    fn into_item_composable(self) -> (ItemComposable, CardSize) {
        (ItemComposable::ChartCard(self), CardSize::Charts)
    }

    fn to_card_size(&self) -> CardSize {
        CardSize::Charts
    }
}

impl IntoItemComposable for Box<PortraitCardDataProps> {
    fn into_item_composable(self) -> (ItemComposable, CardSize) {
        (ItemComposable::PortraitCard(self), CardSize::Portrait)
    }

    fn to_card_size(&self) -> CardSize {
        CardSize::Portrait
    }
}

impl IntoItemComposable for Box<BeardSupportedCardDataProps> {
    fn into_item_composable(self) -> (ItemComposable, CardSize) {
        (
            ItemComposable::BeardSupportedCard(self),
            CardSize::BeardSupported,
        )
    }

    fn to_card_size(&self) -> CardSize {
        CardSize::BeardSupported
    }
}
