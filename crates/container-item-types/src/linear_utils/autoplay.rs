use common_transform_types::actions::{Action, TransitionAction};
use ignx_compositron::prelude::SignalGetUntracked;
use ignx_compositron::reactive::safe::StaticScope as Scope;
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use settings_manager::try_use_settings_manager;

fn are_firetv_pcon_viewing_restrictions_enabled(scope: Scope) -> bool {
    try_use_settings_manager(scope).is_some_and(|settings| {
        settings
            .get_firetv_pcon_viewing_restrictions_enabled()
            .try_get_untracked()
            .unwrap_or(false)
    })
}

pub fn is_linear_autoplay_feature_enabled(scope: Scope) -> bool {
    let are_firetv_pcon_viewing_restrictions_enabled =
        are_firetv_pcon_viewing_restrictions_enabled(scope);
    let is_linear_station_autoplay_in_carousels_enabled = try_use_rust_features(scope)
        .is_some_and(|features| features.is_linear_station_autoplay_in_carousels_enabled());

    !are_firetv_pcon_viewing_restrictions_enabled && is_linear_station_autoplay_in_carousels_enabled
}

pub fn is_autoplay_enabled(scope: Scope) -> bool {
    let is_setting_enabled =
        try_use_settings_manager(scope).is_some_and(|sm| sm.get_autoplay_enabled().get_untracked());

    let are_firetv_pcon_viewing_restrictions_enabled =
        are_firetv_pcon_viewing_restrictions_enabled(scope);

    is_setting_enabled && !are_firetv_pcon_viewing_restrictions_enabled
}

pub fn get_linear_autoplay_video_id(action: &Option<Action>, scope: Scope) -> Option<String> {
    is_linear_autoplay_feature_enabled(scope).then(|| extract_autoplay_video_id(action))?
}

pub fn get_linear_hero_autoplay_video_id(
    actions: &Vec<Action>,
    is_news_page: bool,
    scope: Scope,
) -> Option<String> {
    // Linear Hero only autoplays on news page
    if !is_news_page {
        return None;
    }

    let are_firetv_pcon_viewing_restrictions_enabled =
        are_firetv_pcon_viewing_restrictions_enabled(scope);

    if are_firetv_pcon_viewing_restrictions_enabled {
        return None;
    }

    // get primary action
    let actions = actions.iter().collect::<Vec<&Action>>();
    let primary = Action::get_hero_buttons_from_actions(actions).0;
    primary.and_then(|action| extract_autoplay_video_id(&Some(Action::TransitionAction(action))))
}

fn extract_autoplay_video_id(action: &Option<Action>) -> Option<String> {
    match action {
        Some(Action::TransitionAction(TransitionAction::player(play_action))) => {
            Some(play_action.uri.clone())
        }
        _ => None,
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use container_types::container_item_parsing::{create_player_action, create_signup_action};
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::prelude::*;
    use rstest::rstest;
    use settings_manager::{ApplicationSettingsContext, MockApplicationSettings};
    use std::rc::Rc;

    #[rstest]
    #[case::no_action(None, true, false, None)]
    #[case::signup_action(
        Some(TransitionAction::signUp(create_signup_action())),
        true,
        false,
        None
    )]
    #[case::player_action_not_news(Some(create_player_action()), false, false, None)]
    #[case::player_action_news(Some(create_player_action()), true, false, Some("uri".to_string()))]
    #[case::player_action_news_firetv_pcon_enabled(Some(create_player_action()), true, true, None)]
    fn test_get_hero_autoplay_video_id_on_news_page(
        #[case] action: Option<TransitionAction>,
        #[case] is_news_page: bool,
        #[case] is_firetv_pcon_viewing_restrictions_enabled: bool,
        #[case] expected: Option<String>,
    ) {
        launch_only_scope(move |scope| {
            // Mock settings manager
            let mut mock_settings_manager = MockApplicationSettings::default();
            mock_settings_manager
                .expect_get_firetv_pcon_viewing_restrictions_enabled()
                .return_const_st(Signal::derive(scope, move || {
                    is_firetv_pcon_viewing_restrictions_enabled
                }));
            let settings_context: ApplicationSettingsContext = Rc::new(mock_settings_manager);
            provide_context(scope, settings_context);

            let actions = action
                .map(|action| vec![Action::TransitionAction(action)])
                .unwrap_or_default();
            let result = get_linear_hero_autoplay_video_id(&actions, is_news_page, scope);

            assert_eq!(result, expected)
        })
    }
}
