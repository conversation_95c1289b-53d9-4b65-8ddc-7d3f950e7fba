use crate::linear_utils::autoplay::{
    get_linear_autoplay_video_id, get_linear_hero_autoplay_video_id,
};
use common_transform_types::actions::{Action, TransitionAction};
use common_transform_types::container_items::HeroCard;
use common_transform_types::containers::{
    ChartsCarouselItem, CommonCarouselTitleCardItem, ScheduleCarouselItem, SeeMoreLink,
    StandardCarouselItem, StandardHeroItem, SuperCarouselItem, TentpoleHeroItem,
};

use container_types::container_item_parsing::parsing_options::{
    ContainerItemParsingOptions, ParserPageType,
};
use ignx_compositron::prelude::{create_rw_signal, Scope};
use media_background::types::{
    AutoplayInteractionSource, MediaBackgroundType, MediaStrategy, ResizableStandardBackgroundData,
    ResizableSuperCarouselBackgroundData, RotationDirection, StandardBackgroundData,
    StandardHeroBackgroundData, SuperCarouselBackgroundData, TentpoleBackgroundData,
};
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;

fn get_strategy_for_transition_action(action: &TransitionAction) -> MediaStrategy {
    match action {
        TransitionAction::playback(action) => {
            if action.is_valid_title_playback_action() {
                MediaStrategy::Live
            } else {
                MediaStrategy::Promo
            }
        }
        TransitionAction::openModal(action) => {
            let mut action_found = false;
            for segment in &action.actionSegments {
                for child_action in &segment.childActions {
                    if let TransitionAction::playback(child_action) = child_action {
                        if child_action.playbackMetadata.videoMaterialType == *"LiveStreaming"
                            && child_action
                                .playbackMetadata
                                .playbackExperienceMetadata
                                .is_some()
                        {
                            action_found = true;
                        }
                    }
                }
            }
            if action_found {
                MediaStrategy::Live
            } else {
                MediaStrategy::Promo
            }
        }
        _ => MediaStrategy::Promo,
    }
}

fn get_event_card_media_strategy(entitlement_status: Option<&str>) -> MediaStrategy {
    match entitlement_status {
        Some("UNENTITLED") => MediaStrategy::Promo,
        Some(_) => MediaStrategy::Live,
        None => MediaStrategy::Promo,
    }
}

fn convert_to_resizable_standard(background_data: StandardBackgroundData) -> MediaBackgroundType {
    MediaBackgroundType::ResizableStandard(ResizableStandardBackgroundData {
        id: background_data.id,
        image_url: background_data.image_url,
        video_id: background_data.video_id,
        enter_immediately: background_data.enter_immediately,
        placement: background_data.placement,
        media_strategy: background_data.media_strategy,
        csm_data: background_data.csm_data,
        interaction_source_override: background_data.interaction_source_override,
        resized: false,
    })
}

pub fn generate_see_more_link_media_background_data(
    see_more: &SeeMoreLink,
    id: String,
    carousel_analytics: Option<String>,
    is_rust_quickplay_v2_enabled: bool,
) -> MediaBackgroundType {
    let common_data = StandardBackgroundData {
        id,
        image_url: see_more.backgroundImage.clone(),
        video_id: None,
        enter_immediately: false,
        placement: "PVBrowse".to_string(),
        media_strategy: MediaStrategy::Promo,
        csm_data: carousel_analytics,
        interaction_source_override: None,
    };
    if is_rust_quickplay_v2_enabled {
        convert_to_resizable_standard(common_data)
    } else {
        MediaBackgroundType::Standard(common_data)
    }
}

pub fn generate_standard_carousel_media_background_data(
    item: &StandardCarouselItem,
    carousel_analytics: Option<String>,
    scope: Scope,
    is_rust_quickplay_v2_enabled: bool,
) -> MediaBackgroundType {
    match item {
        StandardCarouselItem::TITLE_CARD(card) => {
            let hero_image = &card.get_base_title_card_metadata().heroImage;
            let common_data = StandardBackgroundData {
                id: card
                    .get_base_title_card_metadata()
                    .gti
                    .clone()
                    .unwrap_or_else(|| hero_image.clone().unwrap_or_else(|| "NONE".to_string())),
                image_url: hero_image.clone(),
                video_id: card.get_base_title_card_metadata().gti.clone(),
                enter_immediately: false,
                placement: "PVBrowse".to_string(),
                media_strategy: match card {
                    CommonCarouselTitleCardItem::SEASON(_)
                    | CommonCarouselTitleCardItem::SHOW(_)
                    | CommonCarouselTitleCardItem::MOVIE(_)
                    | CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(_)
                    | CommonCarouselTitleCardItem::EPISODE(_)
                    | CommonCarouselTitleCardItem::SERIES(_)
                    | CommonCarouselTitleCardItem::SPORT(_)
                    | CommonCarouselTitleCardItem::LEAGUE(_)
                    | CommonCarouselTitleCardItem::TOURNAMENT(_)
                    | CommonCarouselTitleCardItem::TEAM(_)
                    | CommonCarouselTitleCardItem::PLAYER(_)
                    | CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(_) => MediaStrategy::Promo,
                    CommonCarouselTitleCardItem::VOD_EVENT_ITEM(item) => {
                        get_event_card_media_strategy(
                            item.titleCardBaseMetadata.entitlementStatus.as_deref(),
                        )
                    }
                    CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(item) => {
                        get_event_card_media_strategy(
                            item.titleCardBaseMetadata.entitlementStatus.as_deref(),
                        )
                    }
                    CommonCarouselTitleCardItem::EVENT(item) => get_event_card_media_strategy(
                        item.titleCardBaseMetadata.entitlementStatus.as_deref(),
                    ),
                },
                csm_data: carousel_analytics,
                interaction_source_override: None,
            };
            if is_rust_quickplay_v2_enabled {
                convert_to_resizable_standard(common_data)
            } else {
                MediaBackgroundType::Standard(common_data)
            }
        }
        StandardCarouselItem::LIVE_LINEAR_CARD(card) => {
            let hero_image = card
                .schedule
                .first()
                .and_then(|linear_airing| linear_airing.heroImage.clone());

            get_media_background_data_for_linear_card(
                &card.carouselCardMetadata.action,
                hero_image,
                scope,
                is_rust_quickplay_v2_enabled,
            )
        }
        StandardCarouselItem::LINEAR_STATION(card) => {
            let hero_image = card
                .schedule
                .first()
                .and_then(|linear_airing| linear_airing.heroImage.clone());

            get_media_background_data_for_linear_card(
                &card.action,
                hero_image,
                scope,
                is_rust_quickplay_v2_enabled,
            )
        }
        StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => {
            let hero_image = &card.titleCardBaseMetadata.heroImage;

            let common_data =
                StandardBackgroundData {
                    id: card.titleCardBaseMetadata.gti.clone().unwrap_or_else(|| {
                        hero_image.clone().unwrap_or_else(|| "NONE".to_string())
                    }),
                    image_url: hero_image.clone(),
                    video_id: card.titleCardBaseMetadata.gti.clone(),
                    enter_immediately: false,
                    placement: "PVBrowse".to_string(),
                    media_strategy: get_event_card_media_strategy(
                        card.titleCardBaseMetadata.entitlementStatus.as_deref(),
                    ),
                    csm_data: carousel_analytics,
                    interaction_source_override: None,
                };
            if is_rust_quickplay_v2_enabled {
                convert_to_resizable_standard(common_data)
            } else {
                MediaBackgroundType::Standard(common_data)
            }
        }
        _ => MediaBackgroundType::None,
    }
}

fn get_live_playback_title_id_for_hero(hero: &HeroCard) -> Option<String> {
    hero.carouselCardMetadata
        .actions
        .iter()
        .filter_map(|action| match action {
            Action::TransitionAction(action) => Some(action),
            _ => None,
        })
        .find_map(|action| match action {
            TransitionAction::openModal(action) => action.extract_playable_title_id(),
            TransitionAction::playback(action) => action.playbackMetadata.playbackTitle.clone(),
            _ => None,
        })
}

pub fn get_media_background_data_for_linear_card(
    action: &Option<Action>,
    hero_image: Option<String>,
    scope: Scope,
    is_rust_quickplay_v2_enabled: bool,
) -> MediaBackgroundType {
    let common_data = StandardBackgroundData {
        id: hero_image.clone().unwrap_or_else(|| "NONE".to_string()),
        image_url: hero_image,
        video_id: get_linear_autoplay_video_id(action, scope),
        enter_immediately: false,
        placement: "PVBrowse".to_string(),
        media_strategy: MediaStrategy::Linear,
        csm_data: None,
        interaction_source_override: Some(AutoplayInteractionSource::HomePageLinearCarousel),
    };
    if is_rust_quickplay_v2_enabled {
        convert_to_resizable_standard(common_data)
    } else {
        MediaBackgroundType::Standard(common_data)
    }
}

pub fn generate_standard_hero_media_background_data(
    item: &StandardHeroItem,
    carousel_analytics: Option<String>,
    scope: Scope,
    parsing_options: ContainerItemParsingOptions,
) -> MediaBackgroundType {
    let hero_image_url = {
        match item {
            StandardHeroItem::HERO_CARD(card) => card.titleCardBaseMetadata.heroImage.clone(),
            StandardHeroItem::LINK_CARD(card) => card.imageUrl.clone(),
            StandardHeroItem::CHANNEL_CARD(card) => card.heroImage.clone(),
            StandardHeroItem::LINEAR_STATION(card) => card
                .schedule
                .first()
                .and_then(|airing| airing.heroImage.clone()),
        }
    };

    let media_strategy = match item {
        StandardHeroItem::HERO_CARD(card) => get_media_strategy_for_hero_card(card),
        StandardHeroItem::LINEAR_STATION(_) => MediaStrategy::Linear,
        StandardHeroItem::LINK_CARD(_) | StandardHeroItem::CHANNEL_CARD(_) => MediaStrategy::Promo,
    };

    let live_seamless_transition = try_use_rust_features(scope).is_some_and(|v| {
        v.is_seamless_transition_on_rust_live_hero_enabled()
            && media_strategy == MediaStrategy::Live
    });

    let gti = match (item, live_seamless_transition) {
        (StandardHeroItem::HERO_CARD(card), true) => get_live_playback_title_id_for_hero(card)
            .or_else(|| card.titleCardBaseMetadata.gti.clone()),
        (StandardHeroItem::HERO_CARD(card), false) => card.titleCardBaseMetadata.gti.clone(),
        (StandardHeroItem::LINEAR_STATION(card), _) => card.gti.clone(),
        (StandardHeroItem::LINK_CARD(_) | StandardHeroItem::CHANNEL_CARD(_), _) => None,
    };

    let video_id = match item {
        StandardHeroItem::LINEAR_STATION(card) => get_linear_hero_autoplay_video_id(
            &card.actions,
            matches!(parsing_options.current_page, ParserPageType::News),
            scope,
        ),
        StandardHeroItem::CHANNEL_CARD(_)
        | StandardHeroItem::HERO_CARD(_)
        | StandardHeroItem::LINK_CARD(_) => gti.clone(),
    };

    MediaBackgroundType::StandardHero(StandardHeroBackgroundData {
        id: gti.unwrap_or_else(|| hero_image_url.clone().unwrap_or_else(|| "NONE".to_string())),
        image_url: hero_image_url,
        video_id,
        enter_immediately: false,
        rotation_direction: RotationDirection::NONE,
        is_expanded: create_rw_signal(scope, false),
        placement: "StorefrontHero".to_string(),
        media_strategy,
        csm_data: carousel_analytics,
    })
}

pub fn generate_charts_carousel_media_background_data(
    item: &ChartsCarouselItem,
    carousel_analytics: Option<String>,
    is_rust_quickplay_v2_enabled: bool,
) -> MediaBackgroundType {
    match item {
        ChartsCarouselItem::TITLE_CARD(card) => {
            let hero_image_url = &card.get_base_title_card_metadata().heroImage;
            let common_data = StandardBackgroundData {
                id: card
                    .get_base_title_card_metadata()
                    .gti
                    .clone()
                    .unwrap_or_else(|| {
                        hero_image_url.clone().unwrap_or_else(|| "NONE".to_string())
                    }),
                image_url: hero_image_url.clone(),
                video_id: card.get_base_title_card_metadata().gti.clone(),
                enter_immediately: false,
                placement: "Top10List".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: carousel_analytics,
                interaction_source_override: None,
            };
            if is_rust_quickplay_v2_enabled {
                convert_to_resizable_standard(common_data)
            } else {
                MediaBackgroundType::Standard(common_data)
            }
        }
    }
}

fn get_media_strategy_for_hero_card(card: &HeroCard) -> MediaStrategy {
    if card.titleCardBaseMetadata.contentType == Some("EVENT".to_string()) {
        let actions = card
            .carouselCardMetadata
            .actions
            .iter()
            .collect::<Vec<&Action>>();

        // This logic is duplicated due to the media background being created seperately to the UI data. We should share this in future.
        // TODO:
        let (primary_button_action, _) = Action::get_hero_buttons_from_actions(actions);

        if let Some(primary_button_action) = &primary_button_action {
            get_strategy_for_transition_action(primary_button_action)
        } else {
            MediaStrategy::Promo
        }
    } else {
        MediaStrategy::Promo
    }
}

pub fn generate_tentpole_hero_media_background_data(
    item: &TentpoleHeroItem,
    carousel_analytics: Option<String>,
    scope: Scope,
) -> MediaBackgroundType {
    match item {
        TentpoleHeroItem::HERO_CARD(card) => {
            if let Some(hero_image) = &card.titleCardBaseMetadata.heroImage {
                let media_strategy = get_media_strategy_for_hero_card(card);
                let live_seamless_transition = try_use_rust_features(scope).is_some_and(|v| {
                    v.is_seamless_transition_on_rust_live_hero_enabled()
                        && media_strategy == MediaStrategy::Live
                });

                let gti = if live_seamless_transition {
                    get_live_playback_title_id_for_hero(card)
                        .or_else(|| card.titleCardBaseMetadata.gti.clone())
                } else {
                    card.titleCardBaseMetadata.gti.clone()
                };

                MediaBackgroundType::Tentpole(TentpoleBackgroundData {
                    id: gti.clone().unwrap_or_else(|| hero_image.clone()),
                    image_url: hero_image.clone(),
                    video_id: gti,
                    enter_immediately: false,
                    placement: "TentpoleHero".to_string(),
                    media_strategy,
                    csm_data: carousel_analytics,
                })
            } else {
                MediaBackgroundType::None
            }
        }
    }
}

pub fn generate_super_carousel_media_background_data(
    item: &SuperCarouselItem,
    carousel_has_title: bool,
    carousel_analytics: Option<String>,
    is_rust_quickplay_v2_enabled: bool,
) -> MediaBackgroundType {
    match item {
        SuperCarouselItem::TITLE_CARD(card) => {
            let common_data = SuperCarouselBackgroundData {
                id: card
                    .get_base_title_card_metadata()
                    .gti
                    .clone()
                    .unwrap_or_default(),
                video_id: card.get_base_title_card_metadata().gti.clone(),
                enter_immediately: false,
                placement: "PVBrowse".to_string(),
                media_strategy: match card {
                    CommonCarouselTitleCardItem::SEASON(_)
                    | CommonCarouselTitleCardItem::SHOW(_)
                    | CommonCarouselTitleCardItem::MOVIE(_)
                    | CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(_)
                    | CommonCarouselTitleCardItem::EPISODE(_)
                    | CommonCarouselTitleCardItem::SERIES(_)
                    | CommonCarouselTitleCardItem::SPORT(_)
                    | CommonCarouselTitleCardItem::LEAGUE(_)
                    | CommonCarouselTitleCardItem::TOURNAMENT(_)
                    | CommonCarouselTitleCardItem::TEAM(_)
                    | CommonCarouselTitleCardItem::PLAYER(_)
                    | CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(_) => MediaStrategy::Promo,
                    CommonCarouselTitleCardItem::VOD_EVENT_ITEM(item) => {
                        get_event_card_media_strategy(
                            item.titleCardBaseMetadata.entitlementStatus.as_deref(),
                        )
                    }
                    CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(item) => {
                        get_event_card_media_strategy(
                            item.titleCardBaseMetadata.entitlementStatus.as_deref(),
                        )
                    }
                    CommonCarouselTitleCardItem::EVENT(item) => get_event_card_media_strategy(
                        item.titleCardBaseMetadata.entitlementStatus.as_deref(),
                    ),
                },
                has_title: carousel_has_title,
                csm_data: carousel_analytics,
            };
            if is_rust_quickplay_v2_enabled {
                MediaBackgroundType::ResizableSuperCarousel(ResizableSuperCarouselBackgroundData {
                    id: common_data.id,
                    video_id: common_data.video_id,
                    enter_immediately: common_data.enter_immediately,
                    media_strategy: common_data.media_strategy,
                    has_title: common_data.has_title,
                    csm_data: common_data.csm_data,
                    placement: common_data.placement,
                    image_url: None,
                    resized: false,
                })
            } else {
                MediaBackgroundType::SuperCarousel(common_data)
            }
        }
        SuperCarouselItem::LINK_CARD(_) => MediaBackgroundType::None, // LINK cards are dropped when the carousel is expandable anyway
    }
}

pub fn generate_schedule_carousel_media_background_data(
    item: &ScheduleCarouselItem,
    carousel_analytics: Option<String>,
    is_rust_quickplay_v2_enabled: bool,
) -> MediaBackgroundType {
    let hero_image = &item.eventCard.titleCardBaseMetadata.heroImage;

    let common_data = StandardBackgroundData {
        id: item
            .eventCard
            .titleCardBaseMetadata
            .gti
            .clone()
            .unwrap_or_else(|| hero_image.clone().unwrap_or_else(|| "NONE".to_string())),
        image_url: hero_image.clone(),
        video_id: item.eventCard.titleCardBaseMetadata.gti.clone(),
        enter_immediately: false,
        placement: "PVBrowse".to_string(),
        media_strategy: get_event_card_media_strategy(
            item.eventCard
                .titleCardBaseMetadata
                .entitlementStatus
                .as_deref(),
        ),
        csm_data: carousel_analytics,
        interaction_source_override: None,
    };
    if is_rust_quickplay_v2_enabled {
        convert_to_resizable_standard(common_data)
    } else {
        MediaBackgroundType::Standard(common_data)
    }
}

#[cfg(test)]
mod test {
    use std::collections::HashMap;

    use super::*;
    use common_transform_types::actions::{
        LinkAction, LinkActionTarget, PlaybackGroupMetadata, TitleAcquisitionAction,
        TitleActionSegment, TitleOpenModalAction, TitlePlaybackAction, TitlePlaybackGroupAction,
        TransitionAction,
    };
    use common_transform_types::container_items::{
        Badges, CarouselItemData, ChannelCard, EventCard, EventMetadata, HeroCard, ImageAttributes,
        IndividualImageMetadata, IndividualImageMetadataMapping, LinkCard, MovieCard,
        TitleCardBaseMetadata,
    };
    use common_transform_types::containers::{CommonCarouselTitleCardItem, DisplayPlacement};
    use common_transform_types::playback_metadata::{
        PlaybackExperienceMetadata, PlaybackMetadata, TitleActionMetadataType,
        UserEntitlementMetadata, UserPlaybackMetadata,
    };
    use container_types::container_item_parsing::create_event_metadata;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::reactive::SignalGetUntracked;
    use rstest::*;
    use rust_features::MockRustFeaturesBuilder;

    fn carousel_card_metadata() -> CarouselItemData {
        CarouselItemData {
            transformItemId: Some("a mock id".to_string()),
            title: Some("a mock title".to_string()),
            synopsis: Some("a mock synopsis".to_string()),
            action: None,
            deferredAction: None,
            actions: vec![],
            widgetType: Some("a mock widget type".to_string()),
        }
    }

    fn title_card_metadata(
        hero_image_available: bool,
        gti_available: bool,
        entitlement_status: Option<String>,
    ) -> TitleCardBaseMetadata {
        TitleCardBaseMetadata {
            coverImage: Some("cover image".to_string()),
            boxartImage: Some("box art image".to_string()),
            titleLogoImage: Some("title logo image".to_string()),
            providerLogoImage: Some("provider logo image".to_string()),
            poster2x3Image: Some("poster image".to_string()),
            heroImage: if hero_image_available {
                Some("hero image".to_string())
            } else {
                None
            },
            totalReviewCount: Some(0),
            overallRating: Some(0.0),
            gti: if gti_available {
                Some("gti".to_string())
            } else {
                None
            },
            badges: Some(Badges {
                applyAudioDescription: false,
                applyCC: false,
                applyDolby: false,
                applyDolbyAtmos: false,
                applyDolbyVision: false,
                applyHdr10: false,
                applyPrime: false,
                applyUhd: false,
                regulatoryRating: Some("18".to_string()),
                showPSE: false,
            }),
            contentType: Some("MOVIE".to_string()),
            publicReleaseDate: None,
            runtimeSeconds: None,
            entitlementStatus: entitlement_status,
            entitlementMessaging: None,
            genres: vec![],
            watchProgress: None,
            imageAlternateText: None,
            isEntitled: None,
            offerText: None,
            isInWatchlist: None,
            maturityRatingString: None,
            maturityRatingImage: None,
            regulatoryLabel: None,
            imageAttributes: Some(ImageAttributes {
                isAdult: Some(true),
                isRestricted: None,
                individualImageMetadata: Some(IndividualImageMetadataMapping {
                    providerLogoImage: Some(IndividualImageMetadata {
                        height: Some(200),
                        width: Some(300),
                        scalarHorizontal: None,
                        scalarStacked: None,
                        safeToOverlay: None,
                    }),
                }),
            }),
            contextualActions: None,
        }
    }

    fn title_card(hero_image_available: bool, gti_available: bool) -> CommonCarouselTitleCardItem {
        CommonCarouselTitleCardItem::MOVIE(MovieCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(hero_image_available, gti_available, None),
            linearAttributes: None,
        })
    }

    fn event_card(
        action: Option<Action>,
        entitlement_status: Option<String>,
    ) -> CommonCarouselTitleCardItem {
        let mut card = EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(true, true, entitlement_status),
            eventMetadata: EventMetadata {
                liveliness: None,
                liveEventDateHeader: None,
                liveEventDateBadge: None,
                venue: None,
                scoreBug: None,
            },
        };

        card.carouselCardMetadata.action = action;
        CommonCarouselTitleCardItem::EVENT(card)
    }

    fn bonus_schedule_card(
        action: Option<Action>,
        entitlement_status: Option<String>,
    ) -> EventCard {
        let mut card = EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(true, true, entitlement_status),
            eventMetadata: EventMetadata {
                liveliness: None,
                liveEventDateHeader: None,
                liveEventDateBadge: None,
                venue: None,
                scoreBug: None,
            },
        };

        card.carouselCardMetadata.action = action;
        card
    }

    fn playback_action_with_title(
        metadata_action_type: TitleActionMetadataType,
        label: Option<String>,
        title: Option<String>,
    ) -> TransitionAction {
        TransitionAction::playback(TitlePlaybackAction {
            playbackMetadata: PlaybackMetadata {
                refMarker: "ref_marker".to_string(),
                contentDescriptors: None,
                playbackExperienceMetadata: Some(PlaybackExperienceMetadata {
                    playbackEnvelope: "".to_string(),
                    expiryTime: 0,
                    correlationId: "".to_string(),
                }),
                position:
                    common_transform_types::playback_metadata::PlaybackPosition::FeatureFinished,
                startPositionEpochUtc: None,
                userPlaybackMetadata: UserPlaybackMetadata {
                    runtimeSeconds: Some(0),
                    timecodeSeconds: Some(0),
                    hasStreamed: Some(false),
                    isLinear: None,
                    linearStartTime: None,
                    linearEndTime: None,
                },
                userEntitlementMetadata: UserEntitlementMetadata {
                    entitlementType: "type".to_string(),
                    benefitType: vec![],
                },
                videoMaterialType: "LiveStreaming".to_string(),
                channelId: None,
                playbackTitle: title,
                metadataActionType: metadata_action_type,
                catalogMetadata: None,
                isTrailer: None,
                isUnopenedRental: false,
            },
            label,
            refMarker: "ref_marker".to_string(),
        })
    }

    fn playback_action(
        metadata_action_type: TitleActionMetadataType,
        label: Option<String>,
    ) -> TransitionAction {
        playback_action_with_title(metadata_action_type, label, None)
    }

    fn aquisition_action() -> TransitionAction {
        TransitionAction::acquisition(TitleAcquisitionAction {
            label: "label".to_string(),
            metadata: None,
            refMarker: "ref_marker".to_string(),
        })
    }

    fn title_playback_group_action() -> TitlePlaybackGroupAction {
        TitlePlaybackGroupAction {
            playbackGroupMetadata: PlaybackGroupMetadata {
                refMarker: None,
                metadataActionType: None,
            },
            groupLabel: "".to_string(),
            childActions: vec![],
            label: None,
            refMarker: "".to_string(),
            target: "".to_string(),
        }
    }

    fn link_action() -> LinkAction {
        LinkAction {
            refMarker: Some("".to_string()),
            target: LinkActionTarget::Category,
            pageId: "".to_string(),
            pageType: "".to_string(),
            analytics: HashMap::new(),
        }
    }

    fn open_modal_action(transition_actions: Vec<TransitionAction>) -> TransitionAction {
        TransitionAction::openModal(TitleOpenModalAction {
            refMarker: "".to_string(),
            label: "".to_string(),
            modalHeader: "".to_string(),
            actionSegments: vec![TitleActionSegment {
                childActions: transition_actions,
                entitlementMessaging: None,
            }],
        })
    }

    #[rstest]
    #[case(
        StandardCarouselItem::TITLE_CARD(title_card(true, true)),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(title_card(true, false)),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "hero image".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: None,
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(title_card(false, true)),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: None,
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(title_card(false, false)),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "NONE".to_string(),
            image_url: None,
            video_id: None,
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(event_card(Some(Action::TransitionAction(playback_action(TitleActionMetadataType::Playback, Some("text".to_string())))), Some("ENTITLED".to_string()))),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(event_card(Some(Action::TransitionAction(aquisition_action())), None)),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(event_card(Some(Action::TitlePlaybackGroupAction(title_playback_group_action())), Some("ENTITLED".to_string()))),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(event_card(Some(Action::LinkAction(link_action())), Some("UNENTITLED".to_string()))),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    #[case(
        StandardCarouselItem::BONUS_SCHEDULE_CARD(bonus_schedule_card(Some(Action::LinkAction(link_action())), Some("UNENTITLED".to_string()))),
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None
        })
    )]
    fn standard_carousel_media_background(
        #[case] item: StandardCarouselItem,
        #[case] expected_media_background: MediaBackgroundType,
    ) {
        launch_only_scope(move |scope| {
            assert_eq!(
                expected_media_background,
                generate_standard_carousel_media_background_data(
                    &item,
                    Some("CSMData".to_string()),
                    scope,
                    false
                )
            );
        });
    }

    fn hero_card(hero_image_available: bool, gti_available: bool) -> HeroCard {
        HeroCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(hero_image_available, gti_available, None),
            providerLogoImageMetadata: None,
            eventMetadata: create_event_metadata(None),
            moreDetailsAction: None,
            tnfProperty: None,
            callToAction: None,
            tournamentIcid: None,
            regulatoryLabel: None,
        }
    }

    fn hero_card_with_action(
        hero_image_available: bool,
        gti_available: bool,
        action: Action,
        content_type: String,
    ) -> HeroCard {
        let mut card = HeroCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(hero_image_available, gti_available, None),
            providerLogoImageMetadata: None,
            eventMetadata: create_event_metadata(None),
            moreDetailsAction: None,
            tnfProperty: None,
            callToAction: None,
            tournamentIcid: None,
            regulatoryLabel: None,
        };

        card.titleCardBaseMetadata.contentType = Some(content_type);
        card.carouselCardMetadata.actions = vec![action];
        card
    }

    fn channel_card(hero_image_available: bool) -> ChannelCard {
        ChannelCard {
            carouselCardMetadata: carousel_card_metadata(),
            headerText: None,
            heroImage: if hero_image_available {
                Some("hero image".to_owned())
            } else {
                None
            },
            titleLogoImage: None,
            entitlementMessaging: None,
            regulatoryLabel: None,
            ..ChannelCard::populated_default()
        }
    }

    fn link_card(hero_image_available: bool) -> LinkCard {
        LinkCard {
            carouselCardMetadata: carousel_card_metadata(),
            imageAlternateText: Some("Alternate text".to_string()),
            headerText: Some("Header".to_string()),
            imageUrl: if hero_image_available {
                Some("hero image".to_string())
            } else {
                None
            },
            backgroundImageUrl: None,
            isEntitled: Some(false),
            offerText: None,
            overlayTextPosition: None,
            logoImageUrl: Some("logo image".to_string()),
            entitlementMessaging: None,
            regulatoryLabel: None,
            description: None,
        }
    }

    mod standard_hero {
        use super::*;

        #[rstest]
        #[case(hero_card(false, false), "NONE", None, None)]
        #[case(hero_card(true, false), "hero image", None,  Some("hero image".to_string()))]
        #[case(hero_card(false, true), "gti", Some("gti".to_string()), None)]
        #[case(hero_card(true, true), "gti", Some("gti".to_string()), Some("hero image".to_string()))]
        fn hero_card_standard_hero_media_background(
            #[case] hero_card: HeroCard,
            #[case] expected_id: &'static str,
            #[case] expected_video_id: Option<String>,
            #[case] expected_image_url: Option<String>,
        ) {
            launch_only_scope(move |scope| {
                let expected = StandardHeroBackgroundData {
                    id: expected_id.to_string(),
                    image_url: expected_image_url,
                    video_id: expected_video_id,
                    enter_immediately: false,
                    rotation_direction: RotationDirection::NONE,
                    is_expanded: create_rw_signal(scope, false),
                    placement: "PVBrowse".to_string(),
                    media_strategy: MediaStrategy::Promo,
                    csm_data: None,
                };

                let carousel_analytics = Some("CSMData".to_string());
                match generate_standard_hero_media_background_data(
                    &StandardHeroItem::HERO_CARD(hero_card),
                    carousel_analytics,
                    scope,
                    ContainerItemParsingOptions::default(),
                ) {
                    MediaBackgroundType::StandardHero(generated) => {
                        assert_eq!(expected.id, generated.id);
                        assert_eq!(expected.image_url, generated.image_url);
                        assert_eq!(expected.video_id, generated.video_id);
                        assert_eq!(expected.enter_immediately, generated.enter_immediately);
                        assert_eq!(expected.rotation_direction, generated.rotation_direction);
                        assert_eq!(
                            expected.is_expanded.get_untracked(),
                            generated.is_expanded.get_untracked()
                        );
                    }
                    _ => panic!("Expected StandardHeroBackgroundData"),
                }
            })
        }

        #[rstest]
        #[case(
            true,
            Action::TransitionAction(open_modal_action(vec![playback_action_with_title(
                TitleActionMetadataType::Playback,
                Some("foo-label".to_string()),
                Some("title-id".to_string()),
            )])),
            Some("title-id".to_string())
        )]
        #[case(
            true,
            Action::TransitionAction(playback_action_with_title(
                TitleActionMetadataType::Playback,
                Some("foo-label".to_string()),
                Some("title-id".to_string()),
            )),
            Some("title-id".to_string())
        )]
        #[case(
            false,
            Action::TransitionAction(open_modal_action(vec![playback_action_with_title(
                TitleActionMetadataType::Playback,
                Some("foo-label".to_string()),
                Some("title-id".to_string()),
            )])),
            Some("gti".to_string())
    )]
        #[case(
            true,
            Action::TransitionAction(playback_action_with_title(
                TitleActionMetadataType::Playback,
                Some("foo-label".to_string()),
                None
            )),
            Some("gti".to_string())
    )]
        fn live_seamless_standard_hero_media_background(
            #[case] seamless_treatment: bool,
            #[case] action: Action,
            #[case] video_id: Option<String>,
        ) {
            launch_only_scope(move |scope| {
                MockRustFeaturesBuilder::new()
                    .set_is_seamless_transition_on_rust_live_hero_enabled(seamless_treatment)
                    .build_into_context(scope);

                let item = StandardHeroItem::HERO_CARD(hero_card_with_action(
                    true,
                    true,
                    action,
                    "EVENT".to_string(),
                ));

                let mb = generate_standard_hero_media_background_data(
                    &item,
                    Some("CSMData".to_string()),
                    scope,
                    ContainerItemParsingOptions::default(),
                );

                let MediaBackgroundType::StandardHero(mb) = mb else {
                    panic!("Expected StandardHeroBackgroundData")
                };
                assert_eq!(mb.video_id, video_id);
            })
        }

        #[rstest]
        #[case(link_card(false), "NONE", None)]
        #[case(link_card(true), "hero image",  Some("hero image".to_string()))]
        fn link_card_standard_hero_media_background(
            #[case] link_card: LinkCard,
            #[case] expected_id: &'static str,
            #[case] expected_image_url: Option<String>,
        ) {
            launch_only_scope(move |scope| {
                let expected = StandardHeroBackgroundData {
                    id: expected_id.to_string(),
                    image_url: expected_image_url,
                    video_id: None,
                    enter_immediately: false,
                    rotation_direction: RotationDirection::NONE,
                    is_expanded: create_rw_signal(scope, false),
                    placement: "PVBrowse".to_string(),
                    media_strategy: MediaStrategy::Promo,
                    csm_data: None,
                };

                let carousel_analytics = Some("CSMData".to_string());
                match generate_standard_hero_media_background_data(
                    &StandardHeroItem::LINK_CARD(link_card),
                    carousel_analytics,
                    scope,
                    ContainerItemParsingOptions::default(),
                ) {
                    MediaBackgroundType::StandardHero(generated) => {
                        assert_eq!(expected.id, generated.id);
                        assert_eq!(expected.image_url, generated.image_url);
                        assert_eq!(expected.video_id, generated.video_id);
                        assert_eq!(expected.enter_immediately, generated.enter_immediately);
                        assert_eq!(expected.rotation_direction, generated.rotation_direction);
                        assert_eq!(
                            expected.is_expanded.get_untracked(),
                            generated.is_expanded.get_untracked()
                        );
                    }
                    _ => panic!("Expected StandardHeroBackgroundData"),
                }
            })
        }

        #[rstest]
        #[case(channel_card(false), "NONE", None)]
        #[case(channel_card(true), "hero image",  Some("hero image".to_string()))]
        fn channel_card_standard_hero_media_background(
            #[case] channel_card: ChannelCard,
            #[case] expected_id: &'static str,
            #[case] expected_image_url: Option<String>,
        ) {
            launch_only_scope(move |scope| {
                let expected = StandardHeroBackgroundData {
                    id: expected_id.to_string(),
                    image_url: expected_image_url,
                    video_id: None,
                    enter_immediately: false,
                    rotation_direction: RotationDirection::NONE,
                    is_expanded: create_rw_signal(scope, false),
                    placement: "PVBrowse".to_string(),
                    media_strategy: MediaStrategy::Promo,
                    csm_data: None,
                };

                let carousel_analytics = Some("CSMData".to_string());

                match generate_standard_hero_media_background_data(
                    &StandardHeroItem::CHANNEL_CARD(channel_card),
                    carousel_analytics,
                    scope,
                    ContainerItemParsingOptions::default(),
                ) {
                    MediaBackgroundType::StandardHero(generated) => {
                        assert_eq!(expected.id, generated.id);
                        assert_eq!(expected.image_url, generated.image_url);
                        assert_eq!(expected.video_id, generated.video_id);
                        assert_eq!(expected.enter_immediately, generated.enter_immediately);
                        assert_eq!(expected.rotation_direction, generated.rotation_direction);
                        assert_eq!(
                            expected.is_expanded.get_untracked(),
                            generated.is_expanded.get_untracked()
                        );
                    }
                    _ => panic!("Expected StandardHeroBackgroundData"),
                }
            })
        }

        #[rstest]
        #[case(None, "NONE", None)]
        #[case(Some("hero image".to_string()), "hero image",  Some("hero image".to_string()))]
        fn linear_station_card_standard_hero_media_background(
            #[case] hero_image: Option<String>,
            #[case] expected_id: &'static str,
            #[case] expected_image_url: Option<String>,
        ) {
            use crate::mocks::create_valid_linear_station_card;

            launch_only_scope(move |scope| {
                let mut card = create_valid_linear_station_card(1);
                card.schedule
                    .first_mut()
                    .map(|airing| airing.heroImage = hero_image);

                let expected = StandardHeroBackgroundData {
                    id: expected_id.to_string(),
                    image_url: expected_image_url,
                    video_id: None,
                    enter_immediately: false,
                    rotation_direction: RotationDirection::NONE,
                    is_expanded: create_rw_signal(scope, false),
                    placement: "StorefrontHero".to_string(),
                    media_strategy: MediaStrategy::Linear,
                    csm_data: None,
                };

                let carousel_analytics = Some("CSMData".to_string());

                match generate_standard_hero_media_background_data(
                    &StandardHeroItem::LINEAR_STATION(card),
                    carousel_analytics,
                    scope,
                    ContainerItemParsingOptions::default(),
                ) {
                    MediaBackgroundType::StandardHero(generated) => {
                        assert_eq!(expected.id, generated.id);
                        assert_eq!(expected.image_url, generated.image_url);
                        assert_eq!(expected.video_id, generated.video_id);
                        assert_eq!(expected.enter_immediately, generated.enter_immediately);
                        assert_eq!(expected.rotation_direction, generated.rotation_direction);
                        assert_eq!(
                            expected.is_expanded.get_untracked(),
                            generated.is_expanded.get_untracked()
                        );
                    }
                    _ => panic!("Expected StandardHeroBackgroundData"),
                }
            })
        }

        #[rstest]
        #[case::news_page(ParserPageType::News, Some("gti".to_string()))]
        #[case::not_news(ParserPageType::Other, None)]
        fn linear_station_card_standard_hero_media_background_generates_video_id(
            #[case] current_page: ParserPageType,
            #[case] expected_id: Option<String>,
        ) {
            use crate::mocks::create_valid_linear_station_card;

            launch_only_scope(move |scope| {
                use common_transform_types::actions::PlaybackAction;

                let mut card = create_valid_linear_station_card(1);
                // Playback action should be converted to video_id if non-news
                card.actions = vec![Action::TransitionAction(TransitionAction::player(
                    PlaybackAction::create_populated_action("gti"),
                ))];

                match generate_standard_hero_media_background_data(
                    &StandardHeroItem::LINEAR_STATION(card),
                    None,
                    scope,
                    ContainerItemParsingOptions {
                        current_page,
                        ..Default::default()
                    },
                ) {
                    MediaBackgroundType::StandardHero(generated) => {
                        assert_eq!(expected_id, generated.video_id);
                    }
                    _ => panic!("Expected StandardHeroBackgroundData"),
                }
            })
        }
    }

    mod tentpole_hero {
        use super::*;

        #[rstest]
        #[case(
            TentpoleHeroItem::HERO_CARD(hero_card(true, false)),
            MediaBackgroundType::Tentpole(TentpoleBackgroundData {
                id: "hero image".to_string(),
                image_url: "hero image".to_string(),
                video_id: None,
                enter_immediately: false,
                placement: "TentpoleHero".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: Some("CSMData".to_string())
            })
        )]
        #[case(
            TentpoleHeroItem::HERO_CARD(hero_card(false, false)),
            MediaBackgroundType::None
        )]
        #[case(
            TentpoleHeroItem::HERO_CARD(hero_card_with_action(true, false, Action::TransitionAction(playback_action(TitleActionMetadataType::Playback, Some("text".to_string()))), "EVENT".to_string())),
            MediaBackgroundType::Tentpole(TentpoleBackgroundData {
                id: "hero image".to_string(),
                image_url: "hero image".to_string(),
                video_id: None,
                enter_immediately: false,
                placement: "TentpoleHero".to_string(),
                media_strategy: MediaStrategy::Live,
                csm_data: Some("CSMData".to_string())
            })
        )]
        #[case(
            TentpoleHeroItem::HERO_CARD(hero_card_with_action(true, false, Action::TransitionAction(playback_action(TitleActionMetadataType::Playback, Some("text".to_string()))), "MOVIE".to_string())),
            MediaBackgroundType::Tentpole(TentpoleBackgroundData {
                id: "hero image".to_string(),
                image_url: "hero image".to_string(),
                video_id: None,
                enter_immediately: false,
                placement: "TentpoleHero".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: Some("CSMData".to_string())
            })
        )]
        #[case(
            TentpoleHeroItem::HERO_CARD(hero_card_with_action(true, false, Action::TransitionAction(aquisition_action()), "EVENT".to_string())),
            MediaBackgroundType::Tentpole(TentpoleBackgroundData {
                id: "hero image".to_string(),
                image_url: "hero image".to_string(),
                video_id: None,
                enter_immediately: false,
                placement: "TentpoleHero".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: Some("CSMData".to_string())
            })
        )]
        #[case(
            TentpoleHeroItem::HERO_CARD(hero_card_with_action(true, false, Action::TitlePlaybackGroupAction(title_playback_group_action()), "EVENT".to_string())),
            MediaBackgroundType::Tentpole(TentpoleBackgroundData {
                id: "hero image".to_string(),
                image_url: "hero image".to_string(),
                video_id: None,
                enter_immediately: false,
                placement: "TentpoleHero".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: Some("CSMData".to_string())
            })
        )]
        #[case(
            TentpoleHeroItem::HERO_CARD(hero_card_with_action(true, false, Action::LinkAction(link_action()), "EVENT".to_string())),
            MediaBackgroundType::Tentpole(TentpoleBackgroundData {
                id: "hero image".to_string(),
                image_url: "hero image".to_string(),
                video_id: None,
                enter_immediately: false,
                placement: "TentpoleHero".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: Some("CSMData".to_string())
            })
        )]
        fn tentpole_hero_media_background(
            #[case] item: TentpoleHeroItem,
            #[case] expected_media_background: MediaBackgroundType,
        ) {
            launch_only_scope(move |scope| {
                assert_eq!(
                    expected_media_background,
                    generate_tentpole_hero_media_background_data(
                        &item,
                        Some("CSMData".to_string()),
                        scope
                    )
                );
            })
        }
    }

    #[rstest]
    #[case(
        true,
        Action::TransitionAction(open_modal_action(vec![playback_action_with_title(
            TitleActionMetadataType::Playback,
            Some("foo-label".to_string()),
            Some("title-id".to_string()),
        )])),
        MediaBackgroundType::Tentpole(TentpoleBackgroundData {
            id: "title-id".to_string(),
            image_url: "hero image".to_string(),
            video_id: Some("title-id".to_string()),
            enter_immediately: false,
            placement: "TentpoleHero".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: Some("CSMData".to_string())
        })
    )]
    #[case(
        true,
        Action::TransitionAction(playback_action_with_title(
            TitleActionMetadataType::Playback,
            Some("foo-label".to_string()),
            Some("title-id".to_string()),
        )),
        MediaBackgroundType::Tentpole(TentpoleBackgroundData {
            id: "title-id".to_string(),
            image_url: "hero image".to_string(),
            video_id: Some("title-id".to_string()),
            enter_immediately: false,
            placement: "TentpoleHero".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: Some("CSMData".to_string())
        })
    )]
    #[case(
        false,
        Action::TransitionAction(open_modal_action(vec![playback_action_with_title(
            TitleActionMetadataType::Playback,
            Some("foo-label".to_string()),
            Some("title-id".to_string()),
        )])),
        MediaBackgroundType::Tentpole(TentpoleBackgroundData {
            id: "gti".to_string(),
            image_url: "hero image".to_string(),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "TentpoleHero".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: Some("CSMData".to_string())
        })
    )]
    #[case(
        true,
        Action::TransitionAction(open_modal_action(vec![playback_action_with_title(
            TitleActionMetadataType::Playback,
            Some("foo-label".to_string()),
            None
        )])),
        MediaBackgroundType::Tentpole(TentpoleBackgroundData {
            id: "gti".to_string(),
            image_url: "hero image".to_string(),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "TentpoleHero".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: Some("CSMData".to_string())
        })
    )]
    fn live_seamless_tentpole_hero_media_background(
        #[case] seamless_treatment: bool,
        #[case] action: Action,
        #[case] expected_mb: MediaBackgroundType,
    ) {
        launch_only_scope(move |scope| {
            MockRustFeaturesBuilder::new()
                .set_is_seamless_transition_on_rust_live_hero_enabled(seamless_treatment)
                .build_into_context(scope);

            let item = TentpoleHeroItem::HERO_CARD(hero_card_with_action(
                true,
                true,
                action,
                "EVENT".to_string(),
            ));

            let mb = generate_tentpole_hero_media_background_data(
                &item,
                Some("CSMData".to_string()),
                scope,
            );

            assert_eq!(mb, expected_mb);
        })
    }

    mod super_carousel {
        use super::*;

        #[rstest]
        #[case(
            SuperCarouselItem::TITLE_CARD(title_card(true, false)),
            MediaBackgroundType::SuperCarousel(SuperCarouselBackgroundData {
                id: "".to_string(),
                video_id: None,
                enter_immediately: false,
                placement: "PVBrowse".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: Some("CSMData".to_string()),
                has_title: true
            }),
            true
        )]
        #[case(
            SuperCarouselItem::TITLE_CARD(title_card(true, true)),
            MediaBackgroundType::SuperCarousel(SuperCarouselBackgroundData {
                id: "gti".to_string(),
                video_id: Some("gti".to_string()),
                enter_immediately: false,
                placement: "PVBrowse".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: Some("CSMData".to_string()),
                has_title: true
            }),
            true
        )]
        #[case(
            SuperCarouselItem::LINK_CARD(link_card(true)),
            MediaBackgroundType::None,
            true
        )]
        fn super_carousel_media_background(
            #[case] item: SuperCarouselItem,
            #[case] expected_media_background: MediaBackgroundType,
            #[case] has_title: bool,
        ) {
            assert_eq!(
                expected_media_background,
                generate_super_carousel_media_background_data(
                    &item,
                    has_title,
                    Some("CSMData".to_string()),
                    false
                )
            );
        }
    }

    #[test]
    fn get_strategy_for_transition_action_returns_live_for_playback_action() {
        let action = playback_action(TitleActionMetadataType::Playback, Some("text".to_string()));

        let media_strategy = get_strategy_for_transition_action(&action);

        assert!(matches!(media_strategy, MediaStrategy::Live));
    }

    #[rstest]
    #[case(aquisition_action(), MediaStrategy::Promo)]
    #[case(playback_action(TitleActionMetadataType::Playback, Some("text".to_string())), MediaStrategy::Live)]
    fn get_strategy_for_transition_action_returns_live_for_open_modal_action(
        #[case] action: TransitionAction,
        #[case] strategy: MediaStrategy,
    ) {
        let open_modal_action = open_modal_action(vec![action]);
        assert_eq!(
            get_strategy_for_transition_action(&open_modal_action),
            strategy
        );
    }

    fn see_more_link(image_url: Option<String>) -> SeeMoreLink {
        SeeMoreLink {
            displayPlacement: Some(DisplayPlacement::Start),
            title: Some("test_title".to_string()),
            linkText: Some("test_link_text".to_string()),
            accessibilityText: Some("".to_string()),
            action: None,
            linkAction: None,
            backgroundImage: image_url,
            linkImage: None,
            logoImage: None,
            description: Some("test_description".to_string()),
        }
    }

    #[test]
    fn get_media_background_data_for_see_more_link() {
        let image_url = Some("test_image_url".to_string());
        let see_more_link = see_more_link(image_url.clone());

        let id = "test_id".to_string();
        let carousel_analytics = Some("test_carousel_analytics".to_string());

        let mb_data = generate_see_more_link_media_background_data(
            &see_more_link,
            id.clone(),
            carousel_analytics.clone(),
            false,
        );

        assert_eq!(
            mb_data,
            MediaBackgroundType::Standard(StandardBackgroundData {
                id,
                image_url,
                video_id: None,
                enter_immediately: false,
                placement: "PVBrowse".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: carousel_analytics,
                interaction_source_override: None
            })
        )
    }

    #[test]
    fn get_media_background_data_for_linear_card_sets_correct_video_id() {
        launch_only_scope(move |scope| {
            let result_bg = get_media_background_data_for_linear_card(
                &None,
                Some("heroImage".to_string()),
                scope,
                false,
            );

            assert_eq!(
                result_bg,
                MediaBackgroundType::Standard(StandardBackgroundData {
                    id: "heroImage".to_string(),
                    image_url: Some("heroImage".to_string()),
                    video_id: None,
                    enter_immediately: false,
                    placement: "PVBrowse".to_string(),
                    media_strategy: MediaStrategy::Linear,
                    csm_data: None,
                    interaction_source_override: Some(
                        AutoplayInteractionSource::HomePageLinearCarousel
                    )
                })
            );
        })
    }

    #[test]
    fn get_media_background_data_for_schedule_carousel_card() {
        let item = ScheduleCarouselItem {
            eventCard: EventCard {
                carouselCardMetadata: carousel_card_metadata(),
                titleCardBaseMetadata: title_card_metadata(true, true, None),
                eventMetadata: Default::default(),
            },
            isOffPlatformTitle: Some(false),
            isCustomerGeoRestrictedToLiveEvent: None,
        };

        assert_eq!(
            MediaBackgroundType::Standard(StandardBackgroundData {
                id: "gti".to_string(),
                image_url: Some("hero image".to_string()),
                video_id: Some("gti".to_string()),
                enter_immediately: false,
                placement: "PVBrowse".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: None,
                interaction_source_override: None
            }),
            generate_schedule_carousel_media_background_data(&item, None, false)
        );
    }
}
