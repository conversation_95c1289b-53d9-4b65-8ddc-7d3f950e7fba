use common_transform_types::container_items::LinearStationCard;
use container_types::container_item_parsing::schedule_utils::get_formatted_time_range;
use linear_common::util::schedule_util::get_liveliness;
use title_details::types::common::{
    LiveLinearMetadata, StandardTitleDetailsData, TitleData, TitleDetailsMetadata,
};

use crate::title_details_parsing::utils::entitlement::parse_entitlement_label_data;
use crate::title_details_parsing::utils::title::validate_title_data;
use crate::title_details_parsing::GeneratorErrorType;

pub fn parse_linear_station_card(
    card: &LinearStationCard,
    item_idx: usize,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    // title details content is based on the currently playing airing of the LinearStationCard
    if let Some(currently_playing_airing) = card.schedule.get(item_idx) {
        let title_data = validate_title_data(TitleData {
            title_text: currently_playing_airing
                .title
                .to_owned()
                .unwrap_or_default(),
            title_art_url: None,
            provider_logo_url: None,
        })?;

        let formatted_schedule_time_range = get_formatted_time_range(&(
            currently_playing_airing.startTime.as_str(),
            currently_playing_airing.endTime.as_str(),
        ));

        let next_airing_label = card
            .schedule
            .get(item_idx.saturating_add(1))
            .and_then(|linear_airing| linear_airing.title.clone())
            // TODO: use a localized string: https://sim.amazon.com/issues/LEX3PLR-1261
            .map(|title| format!("Up Next: {}", title));

        let maturity_rating = if currently_playing_airing.rating.is_some() {
            &currently_playing_airing.rating
        } else {
            &card.rating
        };

        let entitlement_data = parse_entitlement_label_data(card.entitlementMessaging.as_ref());

        let content_descriptors = if currently_playing_airing.contentDescriptors.is_empty() {
            None
        } else {
            Some(currently_playing_airing.contentDescriptors.join(", "))
        };

        let metadata = TitleDetailsMetadata::LiveLinear(LiveLinearMetadata {
            station_name: card.name.clone(),
            schedule_time: Some(formatted_schedule_time_range),
            maturity_rating_image: maturity_rating.clone(),
            content_descriptors,
            up_next: next_airing_label,
            liveliness: get_liveliness(true, currently_playing_airing.get_apply_live()),
        });

        Ok(StandardTitleDetailsData {
            title_data,
            metadata,
            synopsis: currently_playing_airing.synopsis_with_prefix(),
            entitlement_data,
        })
    } else {
        // TODO: the carousel as a whole should likely be dropped/skipped if its schedule is empty
        Ok(StandardTitleDetailsData {
            title_data: Default::default(),
            metadata: TitleDetailsMetadata::None,
            synopsis: None,
            entitlement_data: Default::default(),
        })
    }
}

#[cfg(test)]
pub mod test {
    use common_transform_types::container_items::LinearBadges;
    use liveliness_types::Liveliness;
    use title_details::types::common::TitleDetailsMetadata;

    use crate::mocks::create_valid_linear_station_card;

    use super::*;
    use rstest::*;

    #[rstest]
    #[case(0, Some("Up Next: mock LinearAiringCard title 2".to_string()), "mock LinearAiringCard title 1".to_string())]
    #[case(1, None, "mock LinearAiringCard title 2".to_string())]
    fn test_linear_station_card(
        #[case] item_idx: usize,
        #[case] up_next: Option<String>,
        #[case] title: String,
    ) {
        let linear_station_card = create_valid_linear_station_card(2);
        let title_details_data = parse_linear_station_card(&linear_station_card, item_idx).unwrap();
        let title_data = &title_details_data.title_data;

        assert_eq!(
            title_data.title_art_url, None,
            "LINEAR_STATION cards should not display a title art"
        );
        assert_eq!(
            title_data.provider_logo_url, None,
            "LINEAR_STATION cards should not display a provider logo"
        );

        assert_eq!(title_data.title_text, title);

        let test_meta_data = LiveLinearMetadata {
            content_descriptors: Some("content, descriptor, 2".to_string()),
            schedule_time: Some("3:41 - 4:41 AM".to_string()),
            station_name: Some("mock LinearStationCard title".to_string()),
            maturity_rating_image: Some("mock LinearAiringCard rating".to_string()),
            up_next,
            liveliness: Liveliness::OnNow,
        };

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert_eq!(
                generated_meta_data.station_name,
                test_meta_data.station_name
            );
            assert_eq!(
                generated_meta_data.maturity_rating_image,
                test_meta_data.maturity_rating_image
            );
            assert_eq!(generated_meta_data.up_next, test_meta_data.up_next);
        } else {
            panic!("Expected metadata to be LiveLinearMetadata");
        }
    }

    #[rstest]
    fn test_linear_station_card_with_empty_schedule() {
        let live_linear_card = create_valid_linear_station_card(0);
        let title_details_data = parse_linear_station_card(&live_linear_card, 0).unwrap();

        assert_eq!(
            title_details_data,
            StandardTitleDetailsData {
                title_data: Default::default(),
                metadata: TitleDetailsMetadata::None,
                synopsis: None,
                entitlement_data: Default::default(),
            }
        );
    }

    #[rstest]
    fn test_linear_station_card_with_fallback_to_station_rating() {
        let mut linear_station_card = create_valid_linear_station_card(2);
        linear_station_card.schedule[0].rating = None;
        let title_details_data = parse_linear_station_card(&linear_station_card, 0).unwrap();

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert_eq!(
                generated_meta_data.maturity_rating_image,
                Some("mock LinearStationCard rating".to_string())
            );
        } else {
            panic!("Expected metadata to be LiveLinearMetadata");
        }
    }

    #[rstest]
    fn test_linear_station_card_with_no_content_descriptors() {
        let mut linear_station_card = create_valid_linear_station_card(2);
        linear_station_card.schedule[0].contentDescriptors = vec![];
        let title_details_data = parse_linear_station_card(&linear_station_card, 0).unwrap();

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert!(generated_meta_data.content_descriptors.is_none(),);
        } else {
            panic!("Expected metadata to be LiveLinearMetadata");
        }
    }

    #[rstest]
    fn test_linear_station_card_with_apply_live() {
        let mut linear_station_card = create_valid_linear_station_card(2);
        linear_station_card.schedule[0].badges = Some(LinearBadges {
            applyLive: true,
            ..Default::default()
        });

        let title_details_data = parse_linear_station_card(&linear_station_card, 0).unwrap();

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert_eq!(generated_meta_data.liveliness, Liveliness::Live);
        }
    }
}
