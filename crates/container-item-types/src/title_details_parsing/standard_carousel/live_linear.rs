use common_transform_types::container_items::LiveLinearCard;
use common_transform_types::string_builder::{build_description_string, build_prefix_string};
use container_types::container_item_parsing::schedule_utils::get_formatted_time_range;
use liveliness_types::Liveliness;
use title_details::types::common::{
    LiveLinearMetadata, StandardTitleDetailsData, TitleData, TitleDetailsMetadata,
};

use crate::title_details_parsing::utils::entitlement::parse_entitlement_label_data;
use crate::title_details_parsing::utils::title::validate_title_data;
use crate::title_details_parsing::GeneratorErrorType;

pub fn parse_live_linear_card(
    card: &LiveLinearCard,
    item_idx: usize,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    // title details content is based on the currently playing airing of the LiveLinearCard
    if let Some(currently_playing_airing) = card.schedule.get(item_idx) {
        let title_data = validate_title_data(TitleData {
            title_text: currently_playing_airing
                .title
                .to_owned()
                .unwrap_or_default(),
            title_art_url: None,
            provider_logo_url: None,
        })?;

        let prefix = build_prefix_string(&currently_playing_airing.showContext);
        let synopsis = build_description_string(&prefix, &currently_playing_airing.synopsis);

        let formatted_schedule_time_range = match (
            &currently_playing_airing.startTimeDate,
            &currently_playing_airing.endTimeDate,
        ) {
            (Some(start_time), Some(end_time)) => {
                get_formatted_time_range(&(start_time.as_str(), end_time.as_str()))
            }
            _ => String::new(),
        };

        let next_airing_label = card
            .schedule
            .get(item_idx.saturating_add(1))
            .and_then(|linear_airing| linear_airing.title.clone())
            .map(|title| format!("Up Next: {}", title));

        let maturity_rating = if currently_playing_airing.rating.is_some() {
            &currently_playing_airing.rating
        } else {
            &card.rating
        };

        let entitlement_data = parse_entitlement_label_data(card.entitlementMessaging.as_ref());

        let metadata = TitleDetailsMetadata::LiveLinear(LiveLinearMetadata {
            station_name: card.carouselCardMetadata.title.clone(),
            schedule_time: Some(formatted_schedule_time_range),
            maturity_rating_image: maturity_rating.clone(),
            up_next: next_airing_label,
            liveliness: Liveliness::OnNow,
            content_descriptors: None,
        });

        Ok(StandardTitleDetailsData {
            title_data,
            metadata,
            synopsis,
            entitlement_data,
        })
    } else {
        // TODO: the carousel as a whole should likely be dropped/skipped if its schedule is empty
        Ok(StandardTitleDetailsData {
            title_data: Default::default(),
            metadata: TitleDetailsMetadata::None,
            synopsis: None,
            entitlement_data: Default::default(),
        })
    }
}

#[cfg(test)]
mod test {
    use crate::mocks::create_valid_live_linear_card;

    use super::*;
    use rstest::*;

    #[rstest]
    #[case(0, Some("Up Next: mock ScheduledShow title 2".to_string()), "mock ScheduledShow title 1".to_string())]
    #[case(1, None, "mock ScheduledShow title 2".to_string())]
    fn test_live_linear_card(
        #[case] item_idx: usize,
        #[case] up_next: Option<String>,
        #[case] title: String,
    ) {
        let linear_station_card = create_valid_live_linear_card(2);
        let title_details_data = parse_live_linear_card(&linear_station_card, item_idx).unwrap();
        let title_data = &title_details_data.title_data;

        assert_eq!(
            title_data.title_art_url, None,
            "LIVE_LINEAR cards should not display a title art"
        );
        assert_eq!(
            title_data.provider_logo_url, None,
            "LIVE_LINEAR cards should not display a provider logo"
        );

        assert_eq!(title_data.title_text, title);

        let test_meta_data = LiveLinearMetadata {
            content_descriptors: None,
            schedule_time: Some("3:41 - 4:41 AM".to_string()),
            station_name: Some("a mock LiveLinearCard title".to_string()),
            maturity_rating_image: Some("mock ScheduleShow rating".to_string()),
            up_next,
            liveliness: Liveliness::OnNow,
        };

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert_eq!(
                generated_meta_data.station_name,
                test_meta_data.station_name
            );
            assert_eq!(
                generated_meta_data.maturity_rating_image,
                test_meta_data.maturity_rating_image
            );
            assert_eq!(generated_meta_data.up_next, test_meta_data.up_next);
        }
    }

    #[rstest]
    fn test_live_linear_card_with_empty_schedule() {
        let live_linear_card = create_valid_live_linear_card(0);
        let title_details_data = parse_live_linear_card(&live_linear_card, 0).unwrap();

        assert_eq!(
            title_details_data,
            StandardTitleDetailsData {
                title_data: Default::default(),
                metadata: TitleDetailsMetadata::None,
                synopsis: None,
                entitlement_data: Default::default(),
            }
        );
    }

    #[rstest]
    fn test_with_schedule_show_no_start_time() {
        let mut live_linear_card = create_valid_live_linear_card(2);
        live_linear_card.schedule[0].startTimeDate = None;
        let title_details_data = parse_live_linear_card(&live_linear_card, 0).unwrap();

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert_eq!(generated_meta_data.schedule_time, Some("".to_string()));
        }
    }

    #[rstest]
    fn test_with_schedule_show_no_end_time() {
        let mut live_linear_card = create_valid_live_linear_card(2);
        live_linear_card.schedule[0].endTimeDate = None;
        let title_details_data = parse_live_linear_card(&live_linear_card, 0).unwrap();

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert_eq!(generated_meta_data.schedule_time, Some("".to_string()));
        }
    }

    #[rstest]
    fn test_with_schedule_show_fallback_to_station_rating() {
        let mut live_linear_card = create_valid_live_linear_card(2);
        live_linear_card.schedule[0].rating = None;
        let title_details_data = parse_live_linear_card(&live_linear_card, 0).unwrap();

        if let TitleDetailsMetadata::LiveLinear(generated_meta_data) = title_details_data.metadata {
            assert_eq!(
                generated_meta_data.maturity_rating_image,
                Some("mock LiveLinearCard rating".to_string())
            );
        }
    }
}
