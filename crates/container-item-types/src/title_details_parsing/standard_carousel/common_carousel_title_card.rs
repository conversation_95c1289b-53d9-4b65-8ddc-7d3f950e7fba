use std::time::Duration;

use common_transform_types::container_items::LiveEventDateTime::LOCALIZED_HEADER;
use common_transform_types::container_items::{
    EntitlementMessaging, EventCard, GenericTitleCard, LinearAttributes, LinearSeriesAttributes,
    MovieCard, SeriesCard, ShowCard, TitleCardBaseMetadata, TitleMetadataBadge,
    TitleMetadataBadgeLevel,
};
use common_transform_types::containers::{CommonCarouselTitleCardItem, SeeMoreLink};
use liveliness_types::Liveliness;
use std::str::FromStr;
use title_details::types::common::{
    BadgeLayouts, BadgeOptions, BadgeTypes, Dimensions, EntitlementLabelData, EventMetadata,
    LiveLinearMetadata, LivelinessData, MaturityRatingData, MaturityRatingImageData,
    MetadataOptions, StandardTitleDetailsData, StarRatingData, StarRatingOptions, TitleData,
    TitleDetailsMetadata, VodMetadata,
};

use crate::title_details_parsing::utils::dates::parse_release_year;
use crate::title_details_parsing::utils::entitlement::parse_entitlement_label_data;
use crate::title_details_parsing::utils::genres::parse_genres;
use crate::title_details_parsing::utils::hvm::parse_hvm_data;
use crate::title_details_parsing::utils::metadata_badge::parse_metadata_badge;
use crate::title_details_parsing::utils::title::{
    parse_show_name_when_present, validate_title_data,
};
use crate::title_details_parsing::GeneratorErrorType;

pub fn parse_common_carousel_title_card_item(
    card: &CommonCarouselTitleCardItem,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    let base_metadata = card.get_base_title_card_metadata();
    let carousel_card_metadata = card.get_carousel_card_metadata();
    let show_name = parse_show_name_when_present(card);

    let title_data = validate_title_data(TitleData {
        title_art_url: base_metadata.titleLogoImage.to_owned(),
        provider_logo_url: base_metadata.providerLogoImage.to_owned(),
        title_text: show_name
            .unwrap_or_else(|| carousel_card_metadata.title.to_owned().unwrap_or_default()),
    })?;

    let synopsis = carousel_card_metadata.synopsis.to_owned();
    let entitlement_messaging = base_metadata.entitlementMessaging.as_ref();
    let entitlement_data = parse_entitlement_label_data(entitlement_messaging);
    let metadata_options = MetadataOptions {
        badge_options: BadgeOptions::new(&BadgeLayouts::None).build(),
        star_rating_options: StarRatingOptions::DisplayFull,
    };
    let metadata = parse_metadata(card, &metadata_options);

    Ok(StandardTitleDetailsData {
        title_data,
        metadata,
        synopsis,
        entitlement_data,
    })
}

pub fn parse_see_more_link_card_item(
    see_more: &SeeMoreLink,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    let title_data = TitleData {
        title_art_url: None,
        provider_logo_url: see_more.logoImage.to_owned(),
        title_text: see_more.title.to_owned().unwrap_or_default(),
    };
    let metadata = TitleDetailsMetadata::None;
    let synopsis = see_more.description.to_owned();
    let entitlement_data = EntitlementLabelData {
        entitlement_icon: None,
        entitlement_label: None,
    };

    Ok(StandardTitleDetailsData {
        title_data,
        metadata,
        synopsis,
        entitlement_data,
    })
}

// TODO: Parse LiveLinear data for Movies and Series
fn linear_attributes_metadata(_linear_attributes: &LinearAttributes) -> LiveLinearMetadata {
    LiveLinearMetadata {
        maturity_rating_image: None,
        schedule_time: None,
        station_name: None,
        content_descriptors: None,
        up_next: None,
        liveliness: Liveliness::OnNow,
    }
}

fn linear_series_atributes_metadata(
    linear_series_attributes: &LinearSeriesAttributes,
) -> LiveLinearMetadata {
    linear_attributes_metadata(&linear_series_attributes.linearAttributes)
}

pub fn parse_common_vod_metadata(
    data: &TitleCardBaseMetadata,
    metadata_options: &MetadataOptions,
) -> VodMetadata {
    let mut metadata = VodMetadata::new();

    metadata.genres = parse_genres(&data.genres);
    metadata.release_year = parse_release_year(data.publicReleaseDate);
    metadata.high_value_message = parse_hvm_data(data.entitlementMessaging.as_ref());
    metadata.metadata_badge_message = parse_metadata_badge(data.entitlementMessaging.as_ref());
    metadata.duration = data.runtimeSeconds.map(Duration::from_secs);

    metadata.star_rating = StarRatingData::parse(data, &metadata_options.star_rating_options);

    metadata.maturity_rating = parse_maturity_rating_data(data);

    // TODO: Parse other badges once device capabilities are also implemented: https://sim.amazon.com/issues/LR-10482
    // https://code.amazon.com/packages/AVLivingRoomClient/blobs/ec46cc6a2fc8c1f6c46e1fe4f021a47c78e7ddb1/--/packages/avlrc-react-components-new-metadata/buildMetadata.ts#L175
    metadata.badges = BadgeTypes::parse_list(data.badges.as_ref(), &metadata_options.badge_options);

    metadata
}

pub fn parse_vod_metadata_series(
    card: &SeriesCard,
    metadata_options: &MetadataOptions,
) -> VodMetadata {
    let mut vod = parse_common_vod_metadata(&card.titleCardBaseMetadata, metadata_options);
    vod.number_of_seasons = card.numberOfSeasons;
    vod
}

pub fn parse_vod_metadata_movies(
    card: &MovieCard,
    metadata_options: &MetadataOptions,
) -> VodMetadata {
    parse_common_vod_metadata(&card.titleCardBaseMetadata, metadata_options)
}
pub fn parse_vod_metadata_show(card: &ShowCard, metadata_options: &MetadataOptions) -> VodMetadata {
    let mut vod = parse_common_vod_metadata(&card.titleCardBaseMetadata, metadata_options);
    // Insufficient coverage
    vod.star_rating = None;
    vod.number_of_seasons = card.numberOfSeasons;
    vod
}

fn parse_metadata_movie(
    card: &MovieCard,
    metadata_options: &MetadataOptions,
) -> TitleDetailsMetadata {
    if let Some(linear_attributes) = card.linearAttributes.as_ref() {
        TitleDetailsMetadata::LiveLinear(linear_attributes_metadata(linear_attributes))
    } else {
        TitleDetailsMetadata::Vod(parse_vod_metadata_movies(card, metadata_options))
    }
}

fn parse_metadata_series(
    card: &SeriesCard,
    metadata_options: &MetadataOptions,
) -> TitleDetailsMetadata {
    if let Some(linear_attributes) = card.linearAttributes.as_ref() {
        TitleDetailsMetadata::LiveLinear(linear_series_atributes_metadata(linear_attributes))
    } else {
        TitleDetailsMetadata::Vod(parse_vod_metadata_series(card, metadata_options))
    }
}

fn parse_metadata_show(
    card: &ShowCard,
    metadata_options: &MetadataOptions,
) -> TitleDetailsMetadata {
    TitleDetailsMetadata::Vod(parse_vod_metadata_show(card, metadata_options))
}

fn parse_metadata_generic_title_card(
    card: &GenericTitleCard,
    metadata_options: &MetadataOptions,
) -> TitleDetailsMetadata {
    let metadata = parse_common_vod_metadata(&card.titleCardBaseMetadata, metadata_options);
    TitleDetailsMetadata::Vod(metadata)
}

fn parse_maturity_rating_data(data: &TitleCardBaseMetadata) -> Option<MaturityRatingData> {
    let textual_description = data.maturityRatingString.as_ref().cloned().or_else(|| {
        data.badges
            .as_ref()
            .and_then(|badges| badges.regulatoryRating.clone())
    });

    if let Some(maturity_image) = data.maturityRatingImage.as_ref() {
        let d = &maturity_image.dimension;
        Some(MaturityRatingData::Image(MaturityRatingImageData {
            dimension: Dimensions::new(d.width as f32, d.height as f32),
            url: maturity_image.url.to_owned(),
            textual_description,
        }))
    } else {
        textual_description
            .as_ref()
            .map(|maturity_string| MaturityRatingData::TextBadge(maturity_string.clone()))
    }
}

fn parse_liveliness_data(card: &EventCard) -> Option<LivelinessData> {
    let message = card
        .titleCardBaseMetadata
        .entitlementMessaging
        .as_ref()
        .and_then(<&EntitlementMessaging as Into<Option<&TitleMetadataBadge>>>::into)
        .and_then(<&TitleMetadataBadge as Into<Option<&String>>>::into)
        .cloned();
    let level = card
        .titleCardBaseMetadata
        .entitlementMessaging
        .as_ref()
        .and_then(<&EntitlementMessaging as Into<Option<&TitleMetadataBadgeLevel>>>::into)
        .cloned();

    let liveliness = card
        .eventMetadata
        .liveliness
        .as_deref()
        .and_then(|l| Liveliness::from_str(l).ok());

    Some(LivelinessData {
        message,
        level: level.map(|level| level.into()),
        liveliness,
    })
}

pub fn parse_metadata_event(card: &EventCard) -> EventMetadata {
    let (event_date, event_time) = if let Some(LOCALIZED_HEADER(ref header)) =
        card.eventMetadata.liveEventDateHeader.as_ref()
    {
        (header.date.to_owned(), header.time.to_owned())
    } else {
        (None, None)
    };

    let event_venue = card.eventMetadata.venue.to_owned();
    let maturity_rating = parse_maturity_rating_data(&card.titleCardBaseMetadata);
    let liveliness_data = parse_liveliness_data(card);
    let event_gti = card.titleCardBaseMetadata.gti.to_owned();

    EventMetadata {
        liveliness_data,
        event_date,
        event_time,
        event_venue,
        maturity_rating,
        event_gti,
        service_announcement: None,
    }
}

pub fn parse_metadata(
    card: &CommonCarouselTitleCardItem,
    metadata_options: &MetadataOptions,
) -> TitleDetailsMetadata {
    match card {
        CommonCarouselTitleCardItem::MOVIE(card) => parse_metadata_movie(card, metadata_options),
        CommonCarouselTitleCardItem::SEASON(card) => parse_metadata_series(card, metadata_options),
        CommonCarouselTitleCardItem::SHOW(card) => parse_metadata_show(card, metadata_options),
        CommonCarouselTitleCardItem::EVENT(card)
        | CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => {
            TitleDetailsMetadata::Event(parse_metadata_event(card))
        }
        CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card)
        | CommonCarouselTitleCardItem::EPISODE(card)
        | CommonCarouselTitleCardItem::LEAGUE(card)
        | CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card)
        | CommonCarouselTitleCardItem::TEAM(card)
        | CommonCarouselTitleCardItem::TOURNAMENT(card)
        | CommonCarouselTitleCardItem::PLAYER(card)
        | CommonCarouselTitleCardItem::SERIES(card)
        | CommonCarouselTitleCardItem::SPORT(card)
        | CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(card) => {
            parse_metadata_generic_title_card(card, metadata_options)
        }
    }
}

#[cfg(test)]
pub mod test {
    use insta::{allow_duplicates, assert_debug_snapshot};
    use rstest::rstest;

    use common_transform_types::{
        container_items::{
            Badges, CarouselItemData, Dimension, EntitlementMessage, EntitlementMessageIcons,
            EntitlementMessaging, EventCard, GenericTitleCard, HighValueMessage,
            HighValueMessageLite, ImageAttributes, IndividualImageMetadata,
            IndividualImageMetadataMapping, MaturityRatingImage, VodExtraContentCard,
        },
        containers::DisplayPlacement,
    };
    use container_types::container_item_parsing::{
        create_entitlement_messaging, create_event_metadata, EntitlementOptions,
    };
    use title_details::types::common::LivelinessData;

    use super::*;

    pub fn create_valid_title_card_base_metadata() -> TitleCardBaseMetadata {
        TitleCardBaseMetadata {
            coverImage: Some("cover image".to_string()),
            titleLogoImage: Some("title logo image / title art".to_string()),
            providerLogoImage: Some("provider logo image".to_string()),
            poster2x3Image: Some("poster image".to_string()),
            heroImage: Some("hero image".to_string()),
            boxartImage: Some("box art image".to_string()),
            totalReviewCount: Some(1234),
            overallRating: Some(4.7),
            gti: Some("gti".to_string()),
            badges: Some(Badges {
                applyAudioDescription: false,
                applyCC: false,
                applyDolby: false,
                applyDolbyVision: false,
                applyDolbyAtmos: false,
                applyHdr10: false,
                applyPrime: false,
                applyUhd: false,
                regulatoryRating: Some("18".to_string()),
                showPSE: false,
            }),
            publicReleaseDate: Some(1677801600000),
            runtimeSeconds: Some(6646),
            entitlementStatus: None,
            entitlementMessaging: Some(create_valid_entitlement_messaging()),
            genres: vec![
                "Drama".to_string(),
                "Adventure".to_string(),
                "Comedy".to_string(),
            ],
            watchProgress: None,
            imageAlternateText: None,
            isEntitled: None,
            offerText: None,
            isInWatchlist: None,
            maturityRatingString: None,
            maturityRatingImage: Some(MaturityRatingImage {
                dimension: Dimension {
                    height: 100,
                    width: 200,
                },
                url: "mock maturity rating image url".to_string(),
            }),
            regulatoryLabel: None,
            imageAttributes: Some(ImageAttributes {
                isAdult: Some(true),
                isRestricted: None,
                individualImageMetadata: Some(IndividualImageMetadataMapping {
                    providerLogoImage: Some(IndividualImageMetadata {
                        height: Some(200),
                        width: Some(300),
                        scalarHorizontal: None,
                        scalarStacked: None,
                        safeToOverlay: None,
                    }),
                }),
            }),
            contentType: Some("MOVIE".to_string()),
            contextualActions: None,
        }
    }

    pub fn carousel_card_metadata() -> CarouselItemData {
        CarouselItemData {
            transformItemId: Some("a mock id".to_string()),
            title: Some("some mock title".to_string()),
            synopsis: Some("a mock synopsis".to_string()),
            action: None,
            deferredAction: None,
            actions: vec![],
            widgetType: Some("a mock widget type".to_string()),
        }
    }

    pub fn create_valid_title_card_movie() -> MovieCard {
        MovieCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: create_valid_title_card_base_metadata(),
            linearAttributes: None,
        }
    }

    pub fn create_valid_linear_movie_card() -> MovieCard {
        MovieCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: create_valid_title_card_base_metadata(),
            linearAttributes: Some(LinearAttributes {
                isOnNow: Some(true),
                schedule: None,
                linearVodAction: None,
                stationName: None,
                upNextShowTitle: None,
            }),
        }
    }

    pub fn create_valid_title_card_series() -> SeriesCard {
        SeriesCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                runtimeSeconds: None,
                ..create_valid_title_card_base_metadata()
            },
            linearAttributes: None,

            // Series specific
            numberOfSeasons: Some(3),
            seasonNumber: Some(2),
            episodeNumber: Some(3),
            episodicSynopsis: Some("some mock episodic synopsis".to_string()),
            showName: Some("some mock show name".to_string()),
        }
    }
    pub fn create_valid_linear_series_card() -> SeriesCard {
        SeriesCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                runtimeSeconds: None,
                ..create_valid_title_card_base_metadata()
            },
            linearAttributes: Some(LinearSeriesAttributes {
                linearAttributes: LinearAttributes {
                    stationName: None,
                    schedule: None,
                    upNextShowTitle: None,
                    isOnNow: None,
                    linearVodAction: None,
                },
                episodeNumber: None,
                episodeTitle: None,
                episodeSynopsis: None,
                episodeGti: None,
            }),

            // Series specific
            numberOfSeasons: None,
            seasonNumber: None,
            episodeNumber: None,
            episodicSynopsis: None,
            showName: None,
        }
    }

    pub fn create_valid_title_card_show() -> ShowCard {
        ShowCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                runtimeSeconds: None,
                ..create_valid_title_card_base_metadata()
            },
            numberOfSeasons: Some(3),
            showName: Some("some mock show name".to_string()),
        }
    }

    pub fn create_valid_title_card_event() -> EventCard {
        EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: create_valid_title_card_base_metadata(),
            eventMetadata: create_event_metadata(Some("UPCOMING".to_string())),
        }
    }

    pub fn create_valid_title_card_event_with_entitlement_options(
        entitlement_options: &EntitlementOptions,
    ) -> EventCard {
        EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                entitlementMessaging: create_entitlement_messaging(vec![entitlement_options]),
                ..create_valid_title_card_base_metadata()
            },
            eventMetadata: create_event_metadata(Some("Upcoming".to_string())),
        }
    }

    pub fn create_valid_generic_title_card() -> GenericTitleCard {
        GenericTitleCard {
            titleCardBaseMetadata: create_valid_title_card_base_metadata(),
            carouselCardMetadata: carousel_card_metadata(),
        }
    }

    pub fn create_valid_vod_extra_content_card() -> VodExtraContentCard {
        VodExtraContentCard {
            titleCardBaseMetadata: create_valid_title_card_base_metadata(),
            carouselCardMetadata: carousel_card_metadata(),
        }
    }

    pub fn create_valid_entitlement_messaging() -> EntitlementMessaging {
        EntitlementMessaging {
            ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                message: Some(
                    "Watch with a 30 day free Prime trial, auto renews at £8.99/month".to_string(),
                ),
                icon: Some(EntitlementMessageIcons::OFFER_ICON),
            }),
            TITLE_METADATA_BADGE_SLOT: None,
            GLANCE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT: Some(HighValueMessage {
                message: Some("a mock hvm".to_string()),
                icon: Some("a mock hvm icon".to_string()),
                hvm_type: None,
                level: None,
            }),
            HIGH_VALUE_MESSAGE_SLOT_LITE: Some(HighValueMessageLite {
                message: Some("a mock hvm lite".to_string()),
                icon: Some("a mock hvm lite icon".to_string()),
                hvm_type: None,
                level: None,
            }),
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        }
    }

    pub fn create_valid_see_more_link() -> SeeMoreLink {
        SeeMoreLink {
            displayPlacement: Some(DisplayPlacement::Start),
            title: Some("test_title".to_string()),
            linkText: Some("test_link_text".to_string()),
            accessibilityText: Some("test_accessibility_text".to_string()),
            action: None,
            linkAction: None,
            backgroundImage: Some("test_bg_url".to_string()),
            linkImage: Some("test_link_url".to_string()),
            logoImage: Some("test_logo_url".to_string()),
            description: Some("test_description".to_string()),
        }
    }

    #[test]
    fn test_movie_card() {
        let movie_card = create_valid_title_card_movie();
        assert_debug_snapshot!(parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::MOVIE(movie_card)), @r###"
        Ok(
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: Some(
                        "title logo image / title art",
                    ),
                    provider_logo_url: Some(
                        "provider logo image",
                    ),
                    title_text: "some mock title",
                },
                metadata: Vod(
                    VodMetadata {
                        condensed_page: false,
                        high_value_message: Some(
                            HighValueMessageData {
                                message: "a mock hvm",
                                color: Color {
                                    r: 55,
                                    g: 241,
                                    b: 163,
                                    a: 255,
                                },
                            },
                        ),
                        metadata_badge_message: None,
                        star_rating: Some(
                            StarRatingData {
                                rating: 4.7,
                                votes: Some(
                                    1234,
                                ),
                            },
                        ),
                        duration: Some(
                            6646s,
                        ),
                        number_of_seasons: None,
                        release_year: Some(
                            2023,
                        ),
                        maturity_rating: Some(
                            Image(
                                MaturityRatingImageData {
                                    url: "mock maturity rating image url",
                                    dimension: Dimensions {
                                        width: 200.0,
                                        height: 100.0,
                                    },
                                    textual_description: Some(
                                        "18",
                                    ),
                                },
                            ),
                        ),
                        badges: [],
                        genres: [
                            "Drama",
                            "Adventure",
                        ],
                        is_prerelease: None,
                        upcoming_message: None,
                        card_badge: None,
                    },
                ),
                synopsis: Some(
                    "a mock synopsis",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: Some(
                        "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                    ),
                    entitlement_icon: Some(
                        OFFER,
                    ),
                },
            },
        )
        "###);
    }

    #[test]
    fn test_series_card() {
        let card = create_valid_title_card_series();
        assert_debug_snapshot!(parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::SEASON(card)), @r###"
        Ok(
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: Some(
                        "title logo image / title art",
                    ),
                    provider_logo_url: Some(
                        "provider logo image",
                    ),
                    title_text: "some mock show name",
                },
                metadata: Vod(
                    VodMetadata {
                        condensed_page: false,
                        high_value_message: Some(
                            HighValueMessageData {
                                message: "a mock hvm",
                                color: Color {
                                    r: 55,
                                    g: 241,
                                    b: 163,
                                    a: 255,
                                },
                            },
                        ),
                        metadata_badge_message: None,
                        star_rating: Some(
                            StarRatingData {
                                rating: 4.7,
                                votes: Some(
                                    1234,
                                ),
                            },
                        ),
                        duration: None,
                        number_of_seasons: Some(
                            3,
                        ),
                        release_year: Some(
                            2023,
                        ),
                        maturity_rating: Some(
                            Image(
                                MaturityRatingImageData {
                                    url: "mock maturity rating image url",
                                    dimension: Dimensions {
                                        width: 200.0,
                                        height: 100.0,
                                    },
                                    textual_description: Some(
                                        "18",
                                    ),
                                },
                            ),
                        ),
                        badges: [],
                        genres: [
                            "Drama",
                            "Adventure",
                        ],
                        is_prerelease: None,
                        upcoming_message: None,
                        card_badge: None,
                    },
                ),
                synopsis: Some(
                    "a mock synopsis",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: Some(
                        "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                    ),
                    entitlement_icon: Some(
                        OFFER,
                    ),
                },
            },
        )
        "###);
    }

    #[test]
    fn test_show_card() {
        let card = create_valid_title_card_show();
        let result =
            parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::SHOW(card))
                .expect("Should parse correctly");

        if let TitleDetailsMetadata::Vod(ref metadata) = result.metadata {
            assert_eq!(
                metadata.star_rating, None,
                "Star rating for SHOW is not shown because of insufficient coverage"
            );
        }

        assert_debug_snapshot!(result, @r###"
        StandardTitleDetailsData {
            title_data: TitleData {
                title_art_url: Some(
                    "title logo image / title art",
                ),
                provider_logo_url: Some(
                    "provider logo image",
                ),
                title_text: "some mock title",
            },
            metadata: Vod(
                VodMetadata {
                    condensed_page: false,
                    high_value_message: Some(
                        HighValueMessageData {
                            message: "a mock hvm",
                            color: Color {
                                r: 55,
                                g: 241,
                                b: 163,
                                a: 255,
                            },
                        },
                    ),
                    metadata_badge_message: None,
                    star_rating: None,
                    duration: None,
                    number_of_seasons: Some(
                        3,
                    ),
                    release_year: Some(
                        2023,
                    ),
                    maturity_rating: Some(
                        Image(
                            MaturityRatingImageData {
                                url: "mock maturity rating image url",
                                dimension: Dimensions {
                                    width: 200.0,
                                    height: 100.0,
                                },
                                textual_description: Some(
                                    "18",
                                ),
                            },
                        ),
                    ),
                    badges: [],
                    genres: [
                        "Drama",
                        "Adventure",
                    ],
                    is_prerelease: None,
                    upcoming_message: None,
                    card_badge: None,
                },
            ),
            synopsis: Some(
                "a mock synopsis",
            ),
            entitlement_data: EntitlementLabelData {
                entitlement_label: Some(
                    "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                ),
                entitlement_icon: Some(
                    OFFER,
                ),
            },
        }
        "###);
    }

    #[test]
    pub fn test_linear_movie() {
        let card = create_valid_linear_movie_card();
        let result =
            parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::MOVIE(card));
        assert_debug_snapshot!(result, @r###"
        Ok(
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: Some(
                        "title logo image / title art",
                    ),
                    provider_logo_url: Some(
                        "provider logo image",
                    ),
                    title_text: "some mock title",
                },
                metadata: LiveLinear(
                    LiveLinearMetadata {
                        schedule_time: None,
                        station_name: None,
                        liveliness: OnNow,
                        maturity_rating_image: None,
                        content_descriptors: None,
                        up_next: None,
                    },
                ),
                synopsis: Some(
                    "a mock synopsis",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: Some(
                        "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                    ),
                    entitlement_icon: Some(
                        OFFER,
                    ),
                },
            },
        )
        "###);
    }

    #[test]
    pub fn test_linear_series() {
        let card = create_valid_linear_series_card();
        let result =
            parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::SEASON(card));
        assert_debug_snapshot!(result, @r###"
        Ok(
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: Some(
                        "title logo image / title art",
                    ),
                    provider_logo_url: Some(
                        "provider logo image",
                    ),
                    title_text: "some mock title",
                },
                metadata: LiveLinear(
                    LiveLinearMetadata {
                        schedule_time: None,
                        station_name: None,
                        liveliness: OnNow,
                        maturity_rating_image: None,
                        content_descriptors: None,
                        up_next: None,
                    },
                ),
                synopsis: Some(
                    "a mock synopsis",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: Some(
                        "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                    ),
                    entitlement_icon: Some(
                        OFFER,
                    ),
                },
            },
        )
        "###);
    }

    #[test]
    pub fn test_generic_title_card() {
        let card = create_valid_generic_title_card();
        let items = vec![
            CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card.clone()),
            CommonCarouselTitleCardItem::EPISODE(card.clone()),
            CommonCarouselTitleCardItem::LEAGUE(card.clone()),
            CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card.clone()),
            CommonCarouselTitleCardItem::TEAM(card.clone()),
            CommonCarouselTitleCardItem::TOURNAMENT(card.clone()),
            CommonCarouselTitleCardItem::PLAYER(card.clone()),
            CommonCarouselTitleCardItem::SERIES(card.clone()),
            CommonCarouselTitleCardItem::SPORT(card),
        ];
        for item in items {
            let result = parse_common_carousel_title_card_item(&item).unwrap();
            allow_duplicates! {
                assert_debug_snapshot!(result, @r###"
                StandardTitleDetailsData {
                    title_data: TitleData {
                        title_art_url: Some(
                            "title logo image / title art",
                        ),
                        provider_logo_url: Some(
                            "provider logo image",
                        ),
                        title_text: "some mock title",
                    },
                    metadata: Vod(
                        VodMetadata {
                            condensed_page: false,
                            high_value_message: Some(
                                HighValueMessageData {
                                    message: "a mock hvm",
                                    color: Color {
                                        r: 55,
                                        g: 241,
                                        b: 163,
                                        a: 255,
                                    },
                                },
                            ),
                            metadata_badge_message: None,
                            star_rating: Some(
                                StarRatingData {
                                    rating: 4.7,
                                    votes: Some(
                                        1234,
                                    ),
                                },
                            ),
                            duration: Some(
                                6646s,
                            ),
                            number_of_seasons: None,
                            release_year: Some(
                                2023,
                            ),
                            maturity_rating: Some(
                                Image(
                                    MaturityRatingImageData {
                                        url: "mock maturity rating image url",
                                        dimension: Dimensions {
                                            width: 200.0,
                                            height: 100.0,
                                        },
                                        textual_description: Some(
                                            "18",
                                        ),
                                    },
                                ),
                            ),
                            badges: [],
                            genres: [
                                "Drama",
                                "Adventure",
                            ],
                            is_prerelease: None,
                            upcoming_message: None,
                            card_badge: None,
                        },
                    ),
                    synopsis: Some(
                        "a mock synopsis",
                    ),
                    entitlement_data: EntitlementLabelData {
                        entitlement_label: Some(
                            "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                        ),
                        entitlement_icon: Some(
                            OFFER,
                        ),
                    },
                }
                "###);
            }
        }
    }

    #[test]
    pub fn test_event() {
        let card = create_valid_title_card_event();
        let result =
            parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::EVENT(card))
                .unwrap();
        assert_debug_snapshot!(result, @r###"
        StandardTitleDetailsData {
            title_data: TitleData {
                title_art_url: Some(
                    "title logo image / title art",
                ),
                provider_logo_url: Some(
                    "provider logo image",
                ),
                title_text: "some mock title",
            },
            metadata: Event(
                EventMetadata {
                    maturity_rating: Some(
                        Image(
                            MaturityRatingImageData {
                                url: "mock maturity rating image url",
                                dimension: Dimensions {
                                    width: 200.0,
                                    height: 100.0,
                                },
                                textual_description: Some(
                                    "18",
                                ),
                            },
                        ),
                    ),
                    liveliness_data: Some(
                        LivelinessData {
                            message: None,
                            level: None,
                            liveliness: Some(
                                Upcoming,
                            ),
                        },
                    ),
                    event_date: Some(
                        "Some Date",
                    ),
                    event_time: Some(
                        "Some Time",
                    ),
                    event_venue: Some(
                        "Some venue",
                    ),
                    event_gti: Some(
                        "gti",
                    ),
                    service_announcement: None,
                },
            ),
            synopsis: Some(
                "a mock synopsis",
            ),
            entitlement_data: EntitlementLabelData {
                entitlement_label: Some(
                    "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                ),
                entitlement_icon: Some(
                    OFFER,
                ),
            },
        }
        "###);
    }

    #[test]
    pub fn test_schedule_card() {
        let card = create_valid_title_card_event();
        let result = parse_common_carousel_title_card_item(
            &CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card),
        )
        .unwrap();
        assert_debug_snapshot!(result, @r###"
        StandardTitleDetailsData {
            title_data: TitleData {
                title_art_url: Some(
                    "title logo image / title art",
                ),
                provider_logo_url: Some(
                    "provider logo image",
                ),
                title_text: "some mock title",
            },
            metadata: Event(
                EventMetadata {
                    maturity_rating: Some(
                        Image(
                            MaturityRatingImageData {
                                url: "mock maturity rating image url",
                                dimension: Dimensions {
                                    width: 200.0,
                                    height: 100.0,
                                },
                                textual_description: Some(
                                    "18",
                                ),
                            },
                        ),
                    ),
                    liveliness_data: Some(
                        LivelinessData {
                            message: None,
                            level: None,
                            liveliness: Some(
                                Upcoming,
                            ),
                        },
                    ),
                    event_date: Some(
                        "Some Date",
                    ),
                    event_time: Some(
                        "Some Time",
                    ),
                    event_venue: Some(
                        "Some venue",
                    ),
                    event_gti: Some(
                        "gti",
                    ),
                    service_announcement: None,
                },
            ),
            synopsis: Some(
                "a mock synopsis",
            ),
            entitlement_data: EntitlementLabelData {
                entitlement_label: Some(
                    "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                ),
                entitlement_icon: Some(
                    OFFER,
                ),
            },
        }
        "###);
    }

    #[test]
    pub fn test_see_more_link() {
        let see_more_link = create_valid_see_more_link();
        let result = parse_see_more_link_card_item(&see_more_link).unwrap();

        assert_debug_snapshot!(result, @r###"
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: None,
                    provider_logo_url: Some(
                        "test_logo_url",
                    ),
                    title_text: "test_title",
                },
                metadata: None,
                synopsis: Some(
                    "test_description",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: None,
                    entitlement_icon: None,
                },
            }
        "###)
    }

    #[rstest]
    #[case(None)]
    #[case(Some("".to_string()))]
    fn test_movie_card_empty_title(#[case] invalid_title: Option<String>) {
        allow_duplicates! {
            let mut movie_card = create_valid_title_card_movie();
            movie_card.carouselCardMetadata.title = invalid_title;
            let result = parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::MOVIE(movie_card));

            assert_debug_snapshot!(result, @r###"
            Err(
                "Field 'title_text' can not be empty",
            )
            "###);
        }
    }

    fn badges_with_regulatory_string(regulatory_string: String) -> Badges {
        Badges {
            regulatoryRating: Some(regulatory_string),
            applyAudioDescription: false,
            applyCC: false,
            applyDolby: false,
            applyDolbyVision: false,
            applyDolbyAtmos: false,
            applyHdr10: false,
            applyPrime: false,
            applyUhd: false,
            showPSE: false,
        }
    }

    fn some_maturity_rating_image() -> Option<MaturityRatingImage> {
        Some(MaturityRatingImage {
            url: "url".to_string(),
            dimension: Dimension {
                width: 32,
                height: 42,
            },
        })
    }
    fn expected_maturity_rating_image() -> Option<MaturityRatingData> {
        Some(MaturityRatingData::Image(MaturityRatingImageData {
            url: "url".to_string(),
            dimension: Dimensions {
                width: 32.0,
                height: 42.0,
            },
            textual_description: Some("rating".to_string()),
        }))
    }
    fn expected_maturity_rating_text(s: &str) -> Option<MaturityRatingData> {
        Some(MaturityRatingData::TextBadge(s.to_owned()))
    }

    #[rstest]
    #[case(None, None, None, None)]
    #[case(some_maturity_rating_image(), Some("rating".to_owned()), Some("regulatory".to_owned()), expected_maturity_rating_image())]
    #[case(None, Some("rating".to_owned()), Some("regulatory".to_owned()), expected_maturity_rating_text("rating"))]
    #[case(None, None, Some("regulatory".to_owned()), expected_maturity_rating_text("regulatory"))]
    fn test_parses_maturity_rating_correctly(
        #[case] maturity_rating_image: Option<MaturityRatingImage>,
        #[case] maturity_rating_string: Option<String>,
        #[case] regulatory_string: Option<String>,
        #[case] expected: Option<MaturityRatingData>,
    ) {
        {
            let mut event_card = create_valid_title_card_event();

            event_card
                .titleCardBaseMetadata
                .maturityRatingImage
                .clone_from(&maturity_rating_image);
            event_card
                .titleCardBaseMetadata
                .maturityRatingString
                .clone_from(&maturity_rating_string);
            event_card.titleCardBaseMetadata.badges = regulatory_string
                .to_owned()
                .map(badges_with_regulatory_string);

            let data = parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::EVENT(
                event_card,
            ))
            .unwrap();

            let TitleDetailsMetadata::Event(event_metadata) = data.metadata else {
                panic!("Not event_metadata");
            };
            assert_eq!(event_metadata.maturity_rating, expected);
        }

        {
            let mut movie_card = create_valid_title_card_movie();
            movie_card.titleCardBaseMetadata.maturityRatingImage = maturity_rating_image;
            movie_card.titleCardBaseMetadata.maturityRatingString = maturity_rating_string;
            movie_card.titleCardBaseMetadata.badges =
                regulatory_string.map(badges_with_regulatory_string);

            let data = parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::MOVIE(
                movie_card,
            ))
            .unwrap();
            let TitleDetailsMetadata::Vod(vod_metadata) = data.metadata else {
                panic!("Not vod_metadata");
            };

            assert_eq!(vod_metadata.maturity_rating, expected);
        }
    }

    #[rstest]
    #[case(EntitlementOptions::TitleMetadataBadgeInfoHighlight, Some("LIVE".to_string()), Some(LivelinessData {message: Some("badge message".to_string()), level: Some("INFO_HIGHLIGHT".to_string()), liveliness: Some(Liveliness::Live)}))]
    #[case(EntitlementOptions::TitleMetadataBadgeInfoInactive, Some("UPCOMING".to_string()), Some(LivelinessData {message: Some("badge message".to_string()), level: Some("INFO_INACTIVE".to_string()), liveliness: Some(Liveliness::Upcoming)}))]
    #[case(EntitlementOptions::TitleMetadataBadgeInfo, Some("ON NOW".to_string()), Some(LivelinessData {message: Some("badge message".to_string()), level: Some("INFO".to_string()), liveliness: Some(Liveliness::OnNow)}))]
    #[case(EntitlementOptions::None, Some("ENDED".to_string()), Some(LivelinessData {message: None, level: None, liveliness: Some(Liveliness::Ended)}))]
    #[case(EntitlementOptions::None, None, Some(LivelinessData {message: None, level: None, liveliness: None}))]
    fn test_parses_liveliness_data_correctly(
        #[case] entitlement_options: EntitlementOptions,
        #[case] liveliness: Option<String>,
        #[case] expected: Option<LivelinessData>,
    ) {
        {
            let mut event_card =
                create_valid_title_card_event_with_entitlement_options(&entitlement_options);

            event_card.eventMetadata.liveliness.clone_from(&liveliness);

            let data = parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::EVENT(
                event_card,
            ))
            .unwrap();

            let TitleDetailsMetadata::Event(event_metadata) = data.metadata else {
                panic!("Not event_metadata");
            };
            assert_eq!(event_metadata.liveliness_data, expected);
        }
    }
}
