use common_transform_types::container_items::BonusScheduleCard;
use title_details::types::common::{StandardTitleDetailsData, TitleData, TitleDetailsMetadata};

use crate::title_details_parsing::utils::entitlement::parse_entitlement_label_data;
use crate::title_details_parsing::utils::title::validate_title_data;
use crate::title_details_parsing::GeneratorErrorType;

use super::common_carousel_title_card::parse_metadata_event;

pub fn parse_bonus_schedule_card(
    card: &BonusScheduleCard,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    let base_metadata = &card.titleCardBaseMetadata;
    let carousel_card_metadata = &card.carouselCardMetadata;

    let title_data = validate_title_data(TitleData {
        title_art_url: base_metadata.titleLogoImage.to_owned(),
        provider_logo_url: base_metadata.providerLogoImage.to_owned(),
        title_text: carousel_card_metadata.title.to_owned().unwrap_or_default(),
    })?;

    let synopsis = carousel_card_metadata.synopsis.to_owned();
    let entitlement_messaging = base_metadata.entitlementMessaging.as_ref();
    let entitlement_data = parse_entitlement_label_data(entitlement_messaging);
    let metadata = TitleDetailsMetadata::Event(parse_metadata_event(card));

    Ok(StandardTitleDetailsData {
        title_data,
        metadata,
        synopsis,
        entitlement_data,
    })
}

#[cfg(test)]
pub mod test {
    use super::*;
    use insta::assert_debug_snapshot;
    use rstest::rstest;

    use super::super::common_carousel_title_card::test::{
        carousel_card_metadata, create_valid_title_card_base_metadata,
    };
    use common_transform_types::container_items::BonusScheduleCard;
    use container_types::container_item_parsing::create_event_metadata;

    pub fn create_valid_bonus_schedule_card() -> BonusScheduleCard {
        BonusScheduleCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: create_valid_title_card_base_metadata(),
            eventMetadata: create_event_metadata(None),
        }
    }

    #[test]
    fn test_valid_bonus_schedule_card() {
        let bonus_schedule_card = create_valid_bonus_schedule_card();
        let title_details_data = parse_bonus_schedule_card(&bonus_schedule_card).unwrap();

        assert_debug_snapshot!(title_details_data, @r###"
        StandardTitleDetailsData {
            title_data: TitleData {
                title_art_url: Some(
                    "title logo image / title art",
                ),
                provider_logo_url: Some(
                    "provider logo image",
                ),
                title_text: "some mock title",
            },
            metadata: Event(
                EventMetadata {
                    maturity_rating: Some(
                        Image(
                            MaturityRatingImageData {
                                url: "mock maturity rating image url",
                                dimension: Dimensions {
                                    width: 200.0,
                                    height: 100.0,
                                },
                                textual_description: Some(
                                    "18",
                                ),
                            },
                        ),
                    ),
                    liveliness_data: Some(
                        LivelinessData {
                            message: None,
                            level: None,
                            liveliness: None,
                        },
                    ),
                    event_date: Some(
                        "Some Date",
                    ),
                    event_time: Some(
                        "Some Time",
                    ),
                    event_venue: Some(
                        "Some venue",
                    ),
                    event_gti: Some(
                        "gti",
                    ),
                    service_announcement: None,
                },
            ),
            synopsis: Some(
                "a mock synopsis",
            ),
            entitlement_data: EntitlementLabelData {
                entitlement_label: Some(
                    "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                ),
                entitlement_icon: Some(
                    OFFER,
                ),
            },
        }
        "###);
    }

    #[rstest]
    #[case(None)]
    #[case(Some("".to_string()))]
    fn test_bonus_schedule_card_empty_title(#[case] invalid_title: Option<String>) {
        insta::allow_duplicates! {
            let mut bonus_schedule_card = create_valid_bonus_schedule_card();
            bonus_schedule_card.carouselCardMetadata.title = invalid_title;
            assert_debug_snapshot!(parse_bonus_schedule_card(&bonus_schedule_card), @r###"
            Err(
                "Field 'title_text' can not be empty",
            )
            "###);
        }
    }
}
