use common_transform_types::containers::{
    ChartsCarouselItem, CommonCarouselTitleCardItem, ScheduleCarouselItem, StandardCarouselItem,
};
use title_details::types::common::StandardTitleDetailsData;

use super::GeneratorErrorType;
use bonus_schedule_card::parse_bonus_schedule_card;
use common_carousel_title_card::parse_common_carousel_title_card_item;
use linear_station::parse_linear_station_card;
use link_card::parse_link_card;
use live_linear::parse_live_linear_card;
mod bonus_schedule_card;
pub mod common_carousel_title_card;
pub mod linear_station;
mod link_card;
pub mod live_linear;

pub fn parse_standard_carousel_item(
    card: &StandardCarouselItem,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    match card {
        StandardCarouselItem::VOD_EXTRA_CONTENT(card) | StandardCarouselItem::TITLE_CARD(card) => {
            parse_common_carousel_title_card_item(card)
        }
        StandardCarouselItem::LINK_CARD(card) => parse_link_card(card),
        StandardCarouselItem::LIVE_LINEAR_CARD(card) => parse_live_linear_card(card, 0),
        StandardCarouselItem::LINEAR_STATION(card) => parse_linear_station_card(card, 0),

        // These are card types found in DetailsPage
        StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => parse_bonus_schedule_card(card),
        StandardCarouselItem::HERO_CARD(_) => {
            Err("Hero Card not expected in standard carousel".to_string())
        }
        StandardCarouselItem::LINEAR_AIRING(_) => {
            Err("Linear Airing card not expected in this context".to_string())
        }
        StandardCarouselItem::CHANNEL_CARD(_) => {
            Err("Channel card not expected in this context".to_string())
        }
    }
}

pub fn parse_charts_carousel_item(
    card: &ChartsCarouselItem,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    match card {
        ChartsCarouselItem::TITLE_CARD(card) => parse_common_carousel_title_card_item(card),
    }
}

pub fn parse_schedule_carousel_item(
    card: &ScheduleCarouselItem,
) -> Result<StandardTitleDetailsData, GeneratorErrorType> {
    parse_common_carousel_title_card_item(&CommonCarouselTitleCardItem::EVENT(
        card.eventCard.clone(),
    ))
}

#[cfg(test)]
mod test {
    use super::{
        bonus_schedule_card::test::create_valid_bonus_schedule_card,
        common_carousel_title_card::test::create_valid_title_card_movie,
        link_card::test::create_valid_link_card,
    };
    use crate::mocks::create_valid_linear_station_card;
    use crate::mocks::create_valid_live_linear_card;
    use common_transform_types::containers::{CommonCarouselTitleCardItem, StandardCarouselItem};
    use insta::assert_debug_snapshot;
    use liveliness_types::Liveliness;
    use title_details::types::common::{LiveLinearMetadata, TitleDetailsMetadata};

    use super::*;
    use rstest::*;

    #[test]
    fn test_title_card() {
        let title_card = StandardCarouselItem::TITLE_CARD(CommonCarouselTitleCardItem::MOVIE(
            create_valid_title_card_movie(),
        ));
        let title_details_data = parse_standard_carousel_item(&title_card);

        assert_debug_snapshot!(title_details_data, @r###"
        Ok(
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: Some(
                        "title logo image / title art",
                    ),
                    provider_logo_url: Some(
                        "provider logo image",
                    ),
                    title_text: "some mock title",
                },
                metadata: Vod(
                    VodMetadata {
                        condensed_page: false,
                        high_value_message: Some(
                            HighValueMessageData {
                                message: "a mock hvm",
                                color: Color {
                                    r: 55,
                                    g: 241,
                                    b: 163,
                                    a: 255,
                                },
                            },
                        ),
                        metadata_badge_message: None,
                        star_rating: Some(
                            StarRatingData {
                                rating: 4.7,
                                votes: Some(
                                    1234,
                                ),
                            },
                        ),
                        duration: Some(
                            6646s,
                        ),
                        number_of_seasons: None,
                        release_year: Some(
                            2023,
                        ),
                        maturity_rating: Some(
                            Image(
                                MaturityRatingImageData {
                                    url: "mock maturity rating image url",
                                    dimension: Dimensions {
                                        width: 200.0,
                                        height: 100.0,
                                    },
                                    textual_description: Some(
                                        "18",
                                    ),
                                },
                            ),
                        ),
                        badges: [],
                        genres: [
                            "Drama",
                            "Adventure",
                        ],
                        is_prerelease: None,
                        upcoming_message: None,
                        card_badge: None,
                    },
                ),
                synopsis: Some(
                    "a mock synopsis",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: Some(
                        "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                    ),
                    entitlement_icon: Some(
                        OFFER,
                    ),
                },
            },
        )
        "###);
    }

    #[test]
    fn test_link_card() {
        let title_card = StandardCarouselItem::LINK_CARD(create_valid_link_card());
        let title_details_data = parse_standard_carousel_item(&title_card);

        assert_debug_snapshot!(title_details_data, @r###"
        Ok(
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: None,
                    provider_logo_url: None,
                    title_text: "a mock title",
                },
                metadata: None,
                synopsis: Some(
                    "a mock description",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: Some(
                        "a mock entitlement message slot message",
                    ),
                    entitlement_icon: Some(
                        OFFER,
                    ),
                },
            },
        )
        "###);
    }

    #[rstest]
    #[case(2, Some("Up Next: mock ScheduledShow title 2".to_string()))]
    #[case(1, None)]
    fn test_live_linear_card(#[case] show_count: u8, #[case] up_next: Option<String>) {
        let title_card =
            StandardCarouselItem::LIVE_LINEAR_CARD(create_valid_live_linear_card(show_count));
        let title_details_data = parse_standard_carousel_item(&title_card);

        let test_meta_data = LiveLinearMetadata {
            content_descriptors: None,
            schedule_time: Some("3:41 - 4:41 AM".to_string()),
            station_name: Some("a mock LiveLinearCard title".to_string()),
            maturity_rating_image: Some("mock ScheduleShow rating".to_string()),
            up_next,
            liveliness: Liveliness::OnNow,
        };

        if let Ok(StandardTitleDetailsData {
            metadata: TitleDetailsMetadata::LiveLinear(ll_metadata),
            ..
        }) = title_details_data
        {
            assert_eq!(ll_metadata.station_name, test_meta_data.station_name);
            assert_eq!(
                ll_metadata.maturity_rating_image,
                test_meta_data.maturity_rating_image
            );
            assert_eq!(ll_metadata.up_next, test_meta_data.up_next);
        }
    }

    #[rstest]
    #[case(2, Some("Up Next: mock LinearAiringCard title 2".to_string()))]
    #[case(1, None)]
    fn test_linear_station_card(#[case] airing_count: u8, #[case] up_next: Option<String>) {
        let title_card =
            StandardCarouselItem::LINEAR_STATION(create_valid_linear_station_card(airing_count));
        let title_details_data = parse_standard_carousel_item(&title_card);

        let test_meta_data = LiveLinearMetadata {
            content_descriptors: Some("content, descriptor, 2".to_string()),
            schedule_time: Some("3:41 - 4:41 AM".to_string()),
            station_name: Some("mock LinearStationCard title".to_string()),
            maturity_rating_image: Some("mock LinearAiringCard rating".to_string()),
            up_next,
            liveliness: Liveliness::OnNow,
        };

        if let Ok(StandardTitleDetailsData {
            metadata: TitleDetailsMetadata::LiveLinear(ll_metadata),
            ..
        }) = title_details_data
        {
            assert_eq!(ll_metadata.station_name, test_meta_data.station_name);
            assert_eq!(
                ll_metadata.maturity_rating_image,
                test_meta_data.maturity_rating_image
            );
            assert_eq!(ll_metadata.up_next, test_meta_data.up_next);
        }
    }

    #[test]
    fn test_bonus_schedule_card() {
        let bonus_schedule_card =
            StandardCarouselItem::BONUS_SCHEDULE_CARD(create_valid_bonus_schedule_card());
        let title_details_data = parse_standard_carousel_item(&bonus_schedule_card);

        assert_debug_snapshot!(title_details_data, @r###"
        Ok(
            StandardTitleDetailsData {
                title_data: TitleData {
                    title_art_url: Some(
                        "title logo image / title art",
                    ),
                    provider_logo_url: Some(
                        "provider logo image",
                    ),
                    title_text: "some mock title",
                },
                metadata: Event(
                    EventMetadata {
                        maturity_rating: Some(
                            Image(
                                MaturityRatingImageData {
                                    url: "mock maturity rating image url",
                                    dimension: Dimensions {
                                        width: 200.0,
                                        height: 100.0,
                                    },
                                    textual_description: Some(
                                        "18",
                                    ),
                                },
                            ),
                        ),
                        liveliness_data: Some(
                            LivelinessData {
                                message: None,
                                level: None,
                                liveliness: None,
                            },
                        ),
                        event_date: Some(
                            "Some Date",
                        ),
                        event_time: Some(
                            "Some Time",
                        ),
                        event_venue: Some(
                            "Some venue",
                        ),
                        event_gti: Some(
                            "gti",
                        ),
                        service_announcement: None,
                    },
                ),
                synopsis: Some(
                    "a mock synopsis",
                ),
                entitlement_data: EntitlementLabelData {
                    entitlement_label: Some(
                        "Watch with a 30 day free Prime trial, auto renews at £8.99/month",
                    ),
                    entitlement_icon: Some(
                        OFFER,
                    ),
                },
            },
        )
        "###);
    }
}
