use common_transform_types::container_items::{EntitlementMessageIcons, EntitlementMessaging};
use title_details::types::common::{EntitlementIconType, EntitlementLabelData};

fn map_entitlement_message_icon(icon: &EntitlementMessageIcons) -> Option<EntitlementIconType> {
    match icon {
        EntitlementMessageIcons::ENTITLED_ICON => Some(EntitlementIconType::ENTITLED),
        EntitlementMessageIcons::OFFER_ICON => Some(EntitlementIconType::OFFER),
        // Ads icon in the entitlement label is removed since the Remaster redesign.
        EntitlementMessageIcons::ADS_ICON => None,
    }
}

pub fn parse_entitlement_label_data(
    entitlement_messaging: Option<&EntitlementMessaging>,
) -> EntitlementLabelData {
    let entitlement_message =
        entitlement_messaging.and_then(|e| e.ENTITLEMENT_MESSAGE_SLOT.as_ref());
    let entitlement_label = entitlement_message.and_then(|e| e.message.clone());
    let entitlement_icon = entitlement_message
        .and_then(|e| e.icon.as_ref())
        .and_then(map_entitlement_message_icon);
    EntitlementLabelData {
        entitlement_label,
        entitlement_icon,
    }
}

#[cfg(test)]
mod test {
    use rstest::rstest;

    use common_transform_types::container_items::EntitlementMessageIcons;
    use title_details::types::common::{EntitlementIconType, EntitlementLabelData};

    use super::*;

    #[test]
    fn test_parse_entitlement_label_data_none() {
        assert_eq!(
            parse_entitlement_label_data(None),
            EntitlementLabelData {
                entitlement_icon: None,
                entitlement_label: None,
            }
        );
    }

    #[rstest]
    #[case(
        EntitlementMessageIcons::ENTITLED_ICON,
        Some(EntitlementIconType::ENTITLED)
    )]
    #[case(EntitlementMessageIcons::OFFER_ICON, Some(EntitlementIconType::OFFER))]
    #[case(EntitlementMessageIcons::ADS_ICON, None)]
    fn test_map_entitlement_message_icon(
        #[case] received_icon: EntitlementMessageIcons,
        #[case] expected_icon: Option<EntitlementIconType>,
    ) {
        assert_eq!(map_entitlement_message_icon(&received_icon), expected_icon);
    }
}
