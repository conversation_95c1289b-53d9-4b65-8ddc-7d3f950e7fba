use common_transform_types::container_items::EntitlementMessaging;
use title_details::types::common::MetadataBadgeData;

// This should only return Some(MetadataBadgeData) if the message is not empty, otherwise the users of this data might
// unnecessarily create a LabelBadge component for it.
pub fn parse_metadata_badge(
    entitlement_messaging: Option<&EntitlementMessaging>,
) -> Option<MetadataBadgeData> {
    let entitlement_message = entitlement_messaging?;
    {
        let badge = entitlement_message
            .TITLE_METADATA_BADGE_SLOT
            .as_ref()
            .map(|badge| MetadataBadgeData {
                message: badge.message.clone(),
                level: badge.level.clone().map(|level| level.into()),
            });

        if badge
            .as_ref()
            .is_some_and(|data| data.message.as_ref().is_some_and(|m| !m.is_empty()))
        {
            return badge;
        }
    }

    None
}

#[cfg(test)]
mod test {
    use common_transform_types::container_items::{TitleMetadataBadge, TitleMetadataBadgeLevel};

    use super::*;

    #[test]
    fn test_parse_metadata_badge_none() {
        assert_eq!(parse_metadata_badge(None), None);
    }

    #[test]
    fn test_parse_metadata_badge_empty_badge_slot() {
        let entitlement_messaging = Some(EntitlementMessaging {
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            ENTITLEMENT_MESSAGE_SLOT: None,
            TITLE_METADATA_BADGE_SLOT: None,
            GLANCE_MESSAGE_SLOT: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        });
        assert_eq!(parse_metadata_badge(entitlement_messaging.as_ref()), None);
    }

    #[test]
    fn test_parse_metadata_badge_empty_message() {
        let entitlement_messaging = Some(EntitlementMessaging {
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            ENTITLEMENT_MESSAGE_SLOT: None,
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                message: Some("".to_string()),
                level: Some(TitleMetadataBadgeLevel::INFO),
            }),
            GLANCE_MESSAGE_SLOT: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        });
        assert_eq!(parse_metadata_badge(entitlement_messaging.as_ref()), None);
    }

    #[test]
    fn test_parse_metadata_badge() {
        let entitlement_messaging = Some(EntitlementMessaging {
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            ENTITLEMENT_MESSAGE_SLOT: None,
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                message: Some("NEW MOVIE".to_string()),
                level: Some(TitleMetadataBadgeLevel::INFO),
            }),
            GLANCE_MESSAGE_SLOT: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        });

        let expected = Some(MetadataBadgeData {
            message: Some("NEW MOVIE".to_string()),
            level: Some("INFO".to_string()),
        });
        assert_eq!(
            parse_metadata_badge(entitlement_messaging.as_ref()),
            expected
        );
    }
}
