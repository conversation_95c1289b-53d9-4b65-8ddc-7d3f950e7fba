use amzn_fable_tokens::FableColor;
use common_transform_types::container_items::{EntitlementMessaging, HighValueMessage};
use fableous::utils::get_ignx_color;
use title_details::types::common::HighValueMessageData;

// This should only return Some(String) if the message is not empty, otherwise the users of this data might
// unnecessarily create a HVM component for it.
pub fn parse_hvm_data(
    entitlement_messaging: Option<&EntitlementMessaging>,
) -> Option<HighValueMessageData> {
    let entitlement_message = entitlement_messaging?;
    {
        let hvm = entitlement_message
            .HIGH_VALUE_MESSAGE_SLOT
            .as_ref()
            .and_then(|hvm| convert_hvm_if_valid(hvm));

        // In React, if HIGH_VALUE_MESSAGE_SLOT.is_some(), we would prefer it over LITE regardless of whether the message itself is empty or not, which doesn't seem right.
        // We should also check whether the message is empty
        if hvm.is_some() {
            return hvm;
        }
    }

    {
        let hvm_lite = entitlement_message
            .HIGH_VALUE_MESSAGE_SLOT_LITE
            .as_ref()
            .and_then(|hvm| convert_hvm_if_valid(hvm));

        if hvm_lite.is_some() {
            return hvm_lite;
        }
    }

    None
}

fn convert_hvm_if_valid(hvm: &HighValueMessage) -> Option<HighValueMessageData> {
    if let Some(message) = &hvm.message {
        if !message.is_empty() {
            return Some(HighValueMessageData {
                message: message.clone(),
                color: get_ignx_color(FableColor::EXPLORER300),
            });
        }
    }
    None
}

#[cfg(test)]
mod test {
    use common_transform_types::container_items::{HighValueMessage, HighValueMessageLite};
    use rstest::rstest;

    use super::*;

    fn create_test_entitlement_message<S: Into<String>>(
        hvm_primary: Option<S>,
        hvm_lite: Option<S>,
    ) -> EntitlementMessaging {
        EntitlementMessaging {
            HIGH_VALUE_MESSAGE_SLOT: Some(HighValueMessage {
                message: hvm_primary.map(|s| s.into()),
                hvm_type: None,
                level: None,
                icon: None,
            }),
            HIGH_VALUE_MESSAGE_SLOT_LITE: Some(HighValueMessageLite {
                message: hvm_lite.map(|s| s.into()),
                hvm_type: None,
                level: None,
                icon: None,
            }),
            ENTITLEMENT_MESSAGE_SLOT: None,
            TITLE_METADATA_BADGE_SLOT: None,
            GLANCE_MESSAGE_SLOT: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        }
    }

    #[rstest]
    #[case(Some(""), Some(""), None)]
    #[case(Some("primary"), None, Some("primary"))]
    #[case(Some(""), Some("lite"), Some("lite"))]
    #[case(Some("primary"), Some("lite"), Some("primary"))]
    #[case(None, Some("lite"), Some("lite"))]
    #[case(None, Some(""), None)]
    #[case(None, None, None)]
    fn test_parse_hvm_primary_used_over_lite(
        #[case] hvm_primary: Option<&'static str>,
        #[case] hvm_lite: Option<&'static str>,
        #[case] expected: Option<&'static str>,
    ) {
        let expected: Option<String> = expected.map(|s| s.to_string());
        let hvm_data = create_test_entitlement_message(hvm_primary, hvm_lite);
        let result = parse_hvm_data(Some(&hvm_data));

        assert_eq!(
            result,
            expected.map(|expected| HighValueMessageData {
                message: expected.clone(),
                color: get_ignx_color(FableColor::EXPLORER300)
            })
        );
    }

    #[test]
    fn test_parse_hvm_none() {
        assert_eq!(parse_hvm_data(None), None);
    }
}
