use crate::title_details_parsing::GeneratorErrorType;
use common_transform_types::containers::CommonCarouselTitleCardItem;
use title_details::types::common::TitleData;

pub fn validate_title_data(data: TitleData) -> Result<TitleData, GeneratorErrorType> {
    if data.title_text.is_empty() {
        Err("Field 'title_text' can not be empty".to_string())
    } else {
        Ok(data)
    }
}

pub fn parse_show_name_when_present(card: &CommonCarouselTitleCardItem) -> Option<String> {
    match card {
        CommonCarouselTitleCardItem::SEASON(card) => card.showName.to_owned(),
        _ => None,
    }
}
