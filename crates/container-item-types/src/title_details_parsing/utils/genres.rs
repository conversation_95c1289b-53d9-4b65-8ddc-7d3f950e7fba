use title_details::types::common::GenresListData;

pub fn parse_genres(genres: &[String]) -> GenresListData {
    // TODO: Check if it's still true that genres contain '>' which we would need to filter out like described in React:
    // https://code.amazon.com/packages/AVLivingRoomClient/blobs/ec46cc6a2fc8c1f6c46e1fe4f021a47c78e7ddb1/--/packages/avlrc-react-components-new-metadata/buildMetadata.ts#L262-L263
    let max_genres_to_display = 2;
    genres
        .iter()
        .filter(|&s| !s.contains('>'))
        .map(String::from)
        .take(max_genres_to_display)
        .collect::<GenresListData>()
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    pub fn test_filter_out_special_char() {
        assert_eq!(
            parse_genres(&[
                "Test>".to_string(),
                ">Test".to_string(),
                "A".to_string(),
                "D".to_string()
            ]),
            vec!["A".to_string(), "D".to_string()]
        );
    }

    #[test]
    pub fn test_takes_two() {
        assert_eq!(
            parse_genres(&[
                "A".to_string(),
                "B".to_string(),
                "C".to_string(),
                "D".to_string()
            ]),
            vec!["A".to_string(), "B".to_string()]
        );
        assert_eq!(parse_genres(&["A".to_string()]), vec!["A".to_string()]);
        assert!(parse_genres(&[]).is_empty());
    }
}
