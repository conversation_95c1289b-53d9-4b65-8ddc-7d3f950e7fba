//! Temp module to hold title details parsing logic while it still needs to be accessible from the
//! Collections page

pub mod standard_carousel;
pub mod utils;

// For now, we are going to use String as a temporary error type. Later on, we can substitute this with a more sophisticated error type that will carry more information when we need to do proper error/metric reporting.
pub type GeneratorErrorType = String;
