use crate::traits::card_ui_properties::UseDefaultTitleCardUIProperties;
use crate::traits::displayable_as_item::DisplayableAsItem;
use crate::traits::metadata_properties::UseTitleCardBaseMetadataForTransition;
use crate::traits::selectable::SelectableStrategy;
use crate::traits::to_contextual_menu::ToContextualMenu;
use crate::traits::to_csm_impression_data::UseCardCSMImpressionData;
use crate::traits::toggle_favorite::UseDefaultFavoriteData;
use crate::traits::with_updatable_ui::WithUpdatableUI;
use crate::types::updatable_properties::{ToUpdatableCardSignals, UpdatableCardProperties};
use common_transform_types::container_items::{
    GenericTitleCard, MovieCard, SeriesCard, ShowCard, VodExtraContentCard,
};
use contextual_menu_types::prelude::{ContextualMenuData, ContextualMenuMetadata};
use ignx_compositron::prelude::safe::StoredValue;
use ignx_compositron::prelude::Scope;
use title_details::core::TitleDetailsData;

/// # Title Cards Implementation
///
/// This module implements the necessary traits for all title card types.
///
/// ## Architecture
///
/// Title cards follow a common pattern:
/// 1. They implement `TitleCardProperties` to provide card properties
/// 2. They implement `UseDefaultTitleCardProperties` as a marker trait to use the default implementation
/// 3. They implement `DisplayableAsItem` to provide a test ID for the card
///
/// The `HasCardProperties` trait is automatically implemented for types that implement both
/// `TitleCardProperties` and `UseDefaultTitleCardProperties`.
/// This allows all title cards to share the same implementation of card properties.
// MovieCard implementations
impl UseDefaultTitleCardUIProperties for MovieCard {}

impl DisplayableAsItem for MovieCard {
    fn get_item_type_test_id(&self) -> &'static str {
        "movie-card"
    }
}

impl UseTitleCardBaseMetadataForTransition for MovieCard {}

impl SelectableStrategy for MovieCard {}

impl UseDefaultFavoriteData for MovieCard {}

// SeriesCard implementations
impl UseDefaultTitleCardUIProperties for SeriesCard {}

impl DisplayableAsItem for SeriesCard {
    fn get_item_type_test_id(&self) -> &'static str {
        "series-card"
    }
}

impl UseTitleCardBaseMetadataForTransition for SeriesCard {}

impl SelectableStrategy for SeriesCard {}

impl UseDefaultFavoriteData for SeriesCard {}

// ShowCard implementations
impl UseDefaultTitleCardUIProperties for ShowCard {}

impl DisplayableAsItem for ShowCard {
    fn get_item_type_test_id(&self) -> &'static str {
        "show-card"
    }
}

impl UseTitleCardBaseMetadataForTransition for ShowCard {}

impl SelectableStrategy for ShowCard {}

impl UseDefaultFavoriteData for ShowCard {}

// GenericTitleCard implementations
impl UseDefaultTitleCardUIProperties for GenericTitleCard {}

impl DisplayableAsItem for GenericTitleCard {
    fn get_item_type_test_id(&self) -> &'static str {
        "generic-title-card"
    }
}

impl UseTitleCardBaseMetadataForTransition for GenericTitleCard {}

impl SelectableStrategy for GenericTitleCard {}

impl UseDefaultFavoriteData for GenericTitleCard {}

// VodExtraContentCard implementations
impl UseDefaultTitleCardUIProperties for VodExtraContentCard {}

impl DisplayableAsItem for VodExtraContentCard {
    fn get_item_type_test_id(&self) -> &'static str {
        "vod-extra-content-card"
    }
}

impl UseTitleCardBaseMetadataForTransition for VodExtraContentCard {}

impl SelectableStrategy for VodExtraContentCard {}

impl ToContextualMenu for VodExtraContentCard {
    fn contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        ContextualMenuMetadata::empty()
    }

    fn contextual_menu(
        &self,
        _scope: Scope,
        _page_type: location::RustPage,
    ) -> Option<ContextualMenuData> {
        None
    }
}

impl UseDefaultFavoriteData for VodExtraContentCard {}

impl<'a, T> WithUpdatableUI<'a> for T
where
    T: UseDefaultTitleCardUIProperties,
{
    fn to_updatable_ui(
        &self,
        ui: &dyn ToUpdatableCardSignals<'a>,
        _title_details_store: Option<StoredValue<'a, TitleDetailsData>>,
    ) -> UpdatableCardProperties<'a> {
        if let Some(schedule) = self.linear_attributes().and_then(|a| a.schedule.as_ref()) {
            if let Some(updatable_properties) =
                ui.to_updatable_progress_bar_props(schedule.clone().into())
            {
                return UpdatableCardProperties::ProgressBarOnly(updatable_properties);
            }
        }
        UpdatableCardProperties::None
    }
}

impl<T> UseCardCSMImpressionData for T where T: UseDefaultTitleCardUIProperties {}

#[cfg(test)]
mod test {
    use super::*;
    use crate::impls::card_test_utils::*;
    use crate::traits::selectable::Selectable;
    use crate::traits::selectable::{SelectParsingError, TransitionConfiguration};
    use crate::types::select_behavior::{ItemSelectBehavior, ItemSelectStrategy};
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::prelude::SignalGetUntracked;
    use location::RustPage;
    use rstest::*;

    mod generic_title_card {
        use super::*;
        use amzn_fable_tokens::{FableColor, FableIcon};
        use common_transform_types::actions::{Action, TransitionAction};
        use common_transform_types::container_items::{
            EntitlementGlanceIcons, TitleMetadataBadgeLevel,
        };
        use fableous::badges::label_badge::LabelBadgeColorScheme;
        use fableous::utils::get_ignx_color;
        use ignx_compositron::text::TextContent;

        /// Tests for the Selectable trait functionality on GenericTitleCards
        mod select_handling {
            use super::*;
            use ignx_compositron::app::launch_only_scope;

            #[test]
            fn with_transition_action_produces_expected_transition() {
                launch_only_scope(|scope| {
                    // GIVEN specific expected values
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "generic".to_string();

                    // AND transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // AND a generic title card with those values
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_generic_title_card();

                    // WHEN get_select_behavior is called
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, false);
                    assert_eq!(transition.content_type, Some("generic".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn with_seamless_if_possible_config_creates_appropriate_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a card with transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "generic".to_string();
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_generic_title_card();

                    // AND seamless transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: true,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called with seamless config
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, true);
                    assert_eq!(transition.content_type, Some("generic".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn without_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a generic title card with no action
                    let _setup = CardParsingTestSetup::new(scope);
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(None)
                        .build_generic_title_card();
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn with_non_transition_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a generic title card with a non-transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(Some(Action::create_client_default()))
                        .build_generic_title_card();

                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn open_contextual_menu_works() {
                launch_only_scope(|scope| {
                    // GIVEN a generic title card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_generic_title_card();

                    // WHEN get_select_behavior is called with OpenContextualMenu strategy
                    let behaviour = card
                        .get_select_behavior(
                            scope,
                            ItemSelectStrategy::OpenContextualMenu,
                            RustPage::RUST_SEARCH,
                        )
                        .expect("Select behavior should be defined");

                    // Extract the menu
                    let menu = match behaviour {
                        ItemSelectBehavior::OpenContextualMenu(boxed_menu) => *boxed_menu,
                        _ => panic!("Expected a OpenContextualMenu behavior"),
                    };

                    // THEN the menu contains expected type
                    assert!(matches!(menu, ContextualMenuData::ContextualMenu(_)));
                });
            }

            #[test]
            fn gets_select_strategy_correctly() {
                launch_only_scope(|scope| {
                    // GIVEN a generic title card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_generic_title_card();

                    // WHEN item_select_strategy called
                    let strategy = card.item_select_strategy(scope);

                    // THEN we get expected strategy
                    assert_eq!(strategy, ItemSelectStrategy::default())
                });
            }
        }

        /// Tests for transforming GenericTitleCard to StandardCardProps
        mod standard_card {
            use super::*;

            /// Tests for image properties of GenericTitleCards
            mod image_handling {
                use super::*;

                #[test]
                fn generic_title_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn generic_title_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn generic_title_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with only box art image (no cover image)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string())
                        );
                    });
                }

                #[test]
                fn generic_title_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn generic_title_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(
                            props.secondary_base_image.is_none(),
                            "Secondary image should be None when box art is blurred due to adult content"
                        );
                    });
                }

                #[test]
                fn generic_title_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when adult content is true, even with both image types");
                    });
                }

                #[test]
                fn generic_title_card_without_adult_content_uses_unblurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the image should not be blurred
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            !base_attrs.blur,
                            "Image should not be blurred when adult content flag is false"
                        );
                    });
                }

                #[test]
                fn generic_title_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card.to_standard_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            /// Tests for badge and icon properties of GenericTitleCards
            mod entitlement {
                use super::*;

                #[test]
                fn generic_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn generic_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a generic card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn generic_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a generic card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn generic_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn generic_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of GenericTitleCards
            /// Notes: GenericTitleCard does not have linear attributes.
            mod progress_bar {
                use super::*;

                #[test]
                fn generic_title_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn generic_title_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }
            }

            /// Tests for provider logo properties of GenericTitleCards
            mod provider_logo {
                use super::*;

                #[test]
                fn generic_title_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[test]
                fn generic_title_card_without_provider_logo_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(ProviderLogoData::None)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when no provider logo is set"
                        );
                    });
                }

                #[test]
                fn generic_title_card_with_incomplete_provider_logo_data_has_no_provider_logo_props(
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with incomplete provider logo (URL only)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_provider_logo_data(ProviderLogoData::Url)
                            .build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when provider logo data is incomplete"
                        );
                    });
                }
            }

            /// Tests for text properties of GenericTitleCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn generic_title_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card =
                            CardConfigBuilder::new().build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert!(props.title.get_untracked().is_none());
                        assert!(props.subtitle.get_untracked().is_none());
                    });
                }

                #[test]
                fn generic_title_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card =
                            CardConfigBuilder::new().build_generic_title_card();

                        // WHEN converted to StandardCardProps
                        let props = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN fallback text should be None
                        assert!(props.fallback_text.is_none());
                    });
                }
            }

            /// Obtaining the updatable UI for GenericTitleCards displayed as StandardCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::UpdatableCardProperties;

                #[test]
                fn has_no_updatable_ui_properties() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic_title_card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card =
                            CardConfigBuilder::new().build_generic_title_card();

                        // WHEN we get updatable properties from the standard card
                        let card = generic_title_card
                            .to_standard_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = generic_title_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since generic_title_card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }
            }
        }

        /// Tests for transforming GenericTitleCard to SmallCardProps
        mod small_card {
            use super::*;

            /// Tests for image properties of GenericTitleCards
            mod image_handling {
                use super::*;

                #[test]
                fn generic_title_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn generic_title_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn generic_title_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with only box art image (no cover image)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string())
                        );
                    });
                }

                #[test]
                fn generic_title_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn generic_title_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(
                            props.secondary_base_image.is_none(),
                            "Secondary image should be None when box art is blurred due to adult content"
                        );
                    });
                }

                #[test]
                fn generic_title_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when adult content is true, even with both image types");
                    });
                }

                #[test]
                fn generic_title_card_without_adult_content_uses_unblurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the image should not be blurred
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            !base_attrs.blur,
                            "Image should not be blurred when adult content flag is false"
                        );
                    });
                }

                #[test]
                fn generic_title_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            /// Tests for badge and icon properties of GenericTitleCards
            mod entitlement {
                use super::*;

                #[test]
                fn generic_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn generic_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a generic card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn generic_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a generic card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn generic_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn generic_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of GenericTitleCards
            /// Notes: GenericTitleCard does not have linear attributes.
            mod progress_bar {
                use super::*;

                #[test]
                fn generic_title_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn generic_title_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }
            }

            /// Tests for provider logo properties of GenericTitleCards
            mod provider_logo {
                use super::*;

                #[test]
                fn generic_title_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[test]
                fn generic_title_card_without_provider_logo_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card without provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(ProviderLogoData::None)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when no provider logo is set"
                        );
                    });
                }

                #[test]
                fn generic_title_card_with_incomplete_provider_logo_data_has_no_provider_logo_props(
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card with incomplete provider logo (URL only)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_provider_logo_data(ProviderLogoData::Url)
                            .build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when provider logo data is incomplete"
                        );
                    });
                }
            }

            /// Tests for text properties of GenericTitleCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn generic_title_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card =
                            CardConfigBuilder::new().build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert!(props.title.get_untracked().is_none());
                        assert!(props.subtitle.get_untracked().is_none());
                    });
                }

                #[test]
                fn generic_title_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic title card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card =
                            CardConfigBuilder::new().build_generic_title_card();

                        // WHEN converted to SmallCardProps
                        let props = generic_title_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN fallback text should be None
                        assert!(props.fallback_text.is_none());
                    });
                }
            }

            /// Obtaining the updatable UI for GenericTitleCards displayed as SmallCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::UpdatableCardProperties;

                #[test]
                fn has_no_updatable_ui_properties() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a generic_title_card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let generic_title_card =
                            CardConfigBuilder::new().build_generic_title_card();

                        // WHEN we get updatable properties from the small card
                        let card = generic_title_card
                            .to_small_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = generic_title_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since generic_title_card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }
            }
        }

        /// Tests for metadata properties of GenericTitleCards
        mod metadata {
            use super::*;

            #[test]
            fn generic_title_card_test_id_is_correct() {
                // GIVEN a generic title card (don't need context or setup for this test)
                let generic_title_card = CardConfigBuilder::new().build_generic_title_card();

                // WHEN we get the test ID
                let test_id = generic_title_card.get_item_type_test_id();

                // THEN it should be "generic-title-card"
                assert_eq!(test_id, "generic-title-card");
            }
        }

        mod csm_data {
            use super::*;
            use crate::traits::to_csm_impression_data::ToCSMImpressionData;
            use cross_app_events::ImpressionData;
            use ignx_compositron::app::launch_only_scope;
            use std::collections::HashMap;

            #[test]
            fn creates_expected_card_csm_data() {
                launch_only_scope(|scope| {
                    // GIVEN a generic title card and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let generic_title_card = CardConfigBuilder::new().build_generic_title_card();

                    // WHEN we get the CSM impression data
                    let csm_data = generic_title_card
                        .to_csm_impression_data(&analytics)
                        .expect("csm data should be defined");

                    // THEN it should contain the expected data
                    assert_eq!(
                        csm_data,
                        ImpressionData {
                            widget_type: Some("titleCard".into()),
                            ref_marker: Some("ref_marker".into()),
                            content_type: None,
                            benefit_id: Some("home".into()),
                            content_id: Some("home:home".into()),
                            creative_id: Some("cover image".into()),
                            analytics: Some(HashMap::new()),
                            slot_id: None,
                            size: None,
                            carousel_analytics: analytics,
                        }
                    );
                })
            }

            #[test]
            fn without_action_csm_data_is_none() {
                launch_only_scope(|scope| {
                    // GIVEN a generic_title card without action and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let generic_title_card = CardConfigBuilder::new()
                        .with_action(None)
                        .build_generic_title_card();

                    // WHEN we get the CSM impression data
                    let csm_data = generic_title_card.to_csm_impression_data(&analytics);

                    // THEN it should be None
                    assert!(csm_data.is_none());
                })
            }
        }
    }

    mod movie_card {
        use super::*;
        use amzn_fable_tokens::{FableColor, FableIcon};
        use common_transform_types::actions::{Action, TransitionAction};

        /// Tests for the Selectable trait functionality on MovieCards
        mod select_handling {
            use super::*;
            use crate::traits::selectable::TransitionConfiguration;
            use ignx_compositron::app::launch_only_scope;

            #[test]
            fn with_transition_action_produces_expected_transition() {
                launch_only_scope(|scope| {
                    // GIVEN specific expected values
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "movie".to_string();

                    // AND transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // AND a movie card with those values
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_movie_card();

                    // WHEN get_select_behavior is called
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, false);
                    assert_eq!(transition.content_type, Some("movie".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn with_seamless_if_possible_config_creates_appropriate_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a card with transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "movie".to_string();
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_movie_card();

                    // AND seamless transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: true,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called with seamless config
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN a transition is returned
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, true);
                    assert_eq!(transition.content_type, Some("movie".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn without_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a movie card with no action
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(None)
                        .build_movie_card();

                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn with_non_transition_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a movie card with a non-transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(Some(Action::create_client_default()))
                        .build_movie_card();

                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn open_contextual_menu_works() {
                launch_only_scope(|scope| {
                    // GIVEN a movie card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_movie_card();

                    // WHEN get_select_behavior is called with OpenContextualMenu strategy
                    let behaviour = card
                        .get_select_behavior(
                            scope,
                            ItemSelectStrategy::OpenContextualMenu,
                            RustPage::RUST_SEARCH,
                        )
                        .expect("Select behavior should be defined");

                    // Extract the menu
                    let menu = match behaviour {
                        ItemSelectBehavior::OpenContextualMenu(boxed_menu) => *boxed_menu,
                        _ => panic!("Expected a OpenContextualMenu behavior"),
                    };

                    // THEN the menu contains expected type
                    assert!(matches!(menu, ContextualMenuData::ContextualMenu(_)));
                });
            }

            #[test]
            fn gets_select_strategy_correctly() {
                launch_only_scope(|scope| {
                    // GIVEN a movie card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_movie_card();

                    // WHEN item_select_strategy called
                    let strategy = card.item_select_strategy(scope);

                    // THEN we get expected strategy
                    assert_eq!(strategy, ItemSelectStrategy::default())
                });
            }
        }

        mod standard_card {
            use super::*;

            // Tests for image properties of MovieCards
            mod image_handling {
                use super::*;

                #[test]
                fn movie_card_with_cover_image_only_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn movie_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn movie_card_with_box_art_only_uses_box_art_with_stretching_and_blurring() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with only box art image (no cover image) and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched and blurred ("base" image)
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());
                        assert!(
                            base_attrs.blur,
                            "Box art should always be blurred, even when not adult content"
                        );

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string()),
                            "Secondary image should be set to box art when primary image is box art and not adult content"
                        );
                    });
                }

                #[test]
                fn movie_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn movie_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn movie_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even with adult content");
                    });
                }

                #[test]
                fn movie_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            // Tests for badge and icon properties of MovieCards
            mod badge_and_icon {
                use super::*;
                use common_transform_types::container_items::{
                    EntitlementGlanceIcons, TitleMetadataBadgeLevel,
                };
                use fableous::badges::label_badge::LabelBadgeColorScheme;
                use fableous::utils::get_ignx_color;
                use ignx_compositron::text::TextContent;

                #[test]
                fn movie_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::None)
                            .build_movie_card(); // Required for card to not be dropped

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn movie_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a movie card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn movie_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a movie card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn movie_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn movie_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of MovieCards
            mod progress_bar {
                use super::*;

                #[test]
                fn movie_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn movie_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }

                #[test]
                fn movie_card_with_linear_schedule_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with linear attributes schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set with OnAir variant
                        let progress_bar =
                            props.progress_bar_props.expect("Should have progress bar");

                        // Check that it's using the OnAir variant
                        assert_eq!(
                            progress_bar.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::OnAir
                        );
                    });
                }

                #[test]
                fn movie_card_without_linear_schedule_has_no_on_air_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without linear schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            // Explicitly not calling with_linear_schedule()
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar of type OnAir
                        // (Note: there might still be a progress bar if watch_progress is set,
                        // but it wouldn't be OnAir variant)
                        if let Some(progress_bar) = props.progress_bar_props {
                            let variant = progress_bar.variant.get_untracked();
                            assert_ne!(
                                variant,
                                fableous::progress_bar::ProgressBarVariant::OnAir,
                                "There should not be an OnAir progress bar without linear schedule"
                            );
                        }
                    });
                }
            }

            // Tests for provider logo properties of MovieCards
            mod provider_logo {
                use super::*;

                #[test]
                fn movie_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[rstest]
                #[case(ProviderLogoData::Url)]
                #[case(ProviderLogoData::Metadata)]
                #[case(ProviderLogoData::None)]
                fn movie_card_without_complete_provider_logo_data_has_no_logo_props(
                    #[case] provider_logo_data: ProviderLogoData,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with incomplete provider logo data
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_provider_logo_data(provider_logo_data)
                            .build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(provider_logo_props.is_none());
                    });
                }
            }

            /// Tests for text properties of MovieCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn movie_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new().build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert_eq!(props.title.get_untracked(), None);
                        assert_eq!(props.subtitle.get_untracked(), None);
                    });
                }

                #[test]
                fn movie_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new().build_movie_card();

                        // WHEN converted to StandardCardProps
                        let props = movie_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN fallback text should be None
                        assert_eq!(props.fallback_text, None);
                    });
                }
            }

            /// Obtaining the updatable UI for MovieCards displayed as StandardCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::{
                    ProgressBarTimePair, UpdatableCardProperties, UpdatableProgressBarProperties,
                };
                use fableous::progress_bar::ProgressBarVariant;
                use fableous::tiles::tile::TileProgressBarProps;
                use ignx_compositron::prelude::create_rw_signal;

                #[test]
                fn has_no_updatable_ui_properties_when_no_linear_attributes() {
                    // todo: with/without linear attributes
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie_card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new().build_movie_card();

                        // WHEN we get updatable properties from the standard card
                        let card = movie_card
                            .to_standard_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = movie_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since generic_title_card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }

                #[test]
                fn has_correct_updatable_ui_properties_when_linear_attributes() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie_card with linear attributes
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_movie_card();

                        // WHEN we get updatable properties from the standard card
                        let card = movie_card
                            .to_standard_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = movie_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the progress bar variant
                        let UpdatableCardProperties::ProgressBarOnly(props) = props else {
                            panic!("should be progress bar variant");
                        };
                        let progress = 1f32 / 3f32;
                        let schedule = movie_card.linearAttributes.unwrap().schedule.unwrap();
                        assert_eq!(
                            props,
                            UpdatableProgressBarProperties::test_new(
                                TileProgressBarProps {
                                    variant: ProgressBarVariant::OnAir.into(),
                                    progress: create_rw_signal(ctx.scope(), progress),
                                    tts_total_minutes: create_rw_signal(ctx.scope(), Some(180)),
                                },
                                ProgressBarTimePair::LinearShow(schedule),
                            )
                        );
                    })
                }
            }
        }

        mod small_card {
            use super::*;

            // Tests for image properties of MovieCards
            mod image_handling {
                use super::*;

                #[test]
                fn movie_card_with_cover_image_only_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn movie_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn movie_card_with_box_art_only_uses_box_art_with_stretching_and_blurring() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with only box art image (no cover image) and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched and blurred ("base" image)
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());
                        assert!(
                            base_attrs.blur,
                            "Box art should always be blurred, even when not adult content"
                        );

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string()),
                            "Secondary image should be set to box art when primary image is box art and not adult content"
                        );
                    });
                }

                #[test]
                fn movie_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn movie_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn movie_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even with adult content");
                    });
                }

                #[test]
                fn movie_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            // Tests for badge and icon properties of MovieCards
            mod badge_and_icon {
                use super::*;
                use common_transform_types::container_items::{
                    EntitlementGlanceIcons, TitleMetadataBadgeLevel,
                };
                use fableous::badges::label_badge::LabelBadgeColorScheme;
                use fableous::utils::get_ignx_color;
                use ignx_compositron::text::TextContent;

                #[test]
                fn movie_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::None)
                            .build_movie_card(); // Required for card to not be dropped

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn movie_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a movie card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn movie_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a movie card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn movie_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn movie_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of MovieCards
            mod progress_bar {
                use super::*;

                #[test]
                fn movie_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn movie_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }

                #[test]
                fn movie_card_with_linear_schedule_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with linear attributes schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set with OnAir variant
                        let progress_bar =
                            props.progress_bar_props.expect("Should have progress bar");

                        // Check that it's using the OnAir variant
                        assert_eq!(
                            progress_bar.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::OnAir
                        );
                    });
                }

                #[test]
                fn movie_card_without_linear_schedule_has_no_on_air_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card without linear schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            // Explicitly not calling with_linear_schedule()
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar of type OnAir
                        // (Note: there might still be a progress bar if watch_progress is set,
                        // but it wouldn't be OnAir variant)
                        if let Some(progress_bar) = props.progress_bar_props {
                            let variant = progress_bar.variant.get_untracked();
                            assert_ne!(
                                variant,
                                fableous::progress_bar::ProgressBarVariant::OnAir,
                                "There should not be an OnAir progress bar without linear schedule"
                            );
                        }
                    });
                }
            }

            // Tests for provider logo properties of MovieCards
            mod provider_logo {
                use super::*;

                #[test]
                fn movie_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[rstest]
                #[case(ProviderLogoData::Url)]
                #[case(ProviderLogoData::Metadata)]
                #[case(ProviderLogoData::None)]
                fn movie_card_without_complete_provider_logo_data_has_no_logo_props(
                    #[case] provider_logo_data: ProviderLogoData,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card with incomplete provider logo data
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_provider_logo_data(provider_logo_data)
                            .build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(provider_logo_props.is_none());
                    });
                }
            }

            /// Tests for text properties of MovieCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn movie_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new().build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert_eq!(props.title.get_untracked(), None);
                        assert_eq!(props.subtitle.get_untracked(), None);
                    });
                }

                #[test]
                fn movie_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new().build_movie_card();

                        // WHEN converted to SmallCardProps
                        let props = movie_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN fallback text should be None
                        assert_eq!(props.fallback_text, None);
                    });
                }
            }

            /// Obtaining the updatable UI for MovieCards displayed as SmallCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::{
                    ProgressBarTimePair, UpdatableCardProperties, UpdatableProgressBarProperties,
                };
                use fableous::progress_bar::ProgressBarVariant;
                use fableous::tiles::tile::TileProgressBarProps;
                use ignx_compositron::prelude::create_rw_signal;

                #[test]
                fn has_no_updatable_ui_properties_when_no_linear_attributes() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie_card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new().build_movie_card();

                        // WHEN we get updatable properties from the small card
                        let card = movie_card
                            .to_small_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = movie_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since generic_title_card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }

                #[test]
                fn has_correct_updatable_ui_properties_when_linear_attributes() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a movie_card with linear attributes
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let movie_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_movie_card();

                        // WHEN we get updatable properties from the standard card
                        let card = movie_card
                            .to_small_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = movie_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the progress bar variant
                        let UpdatableCardProperties::ProgressBarOnly(props) = props else {
                            panic!("should be progress bar variant");
                        };
                        let progress = 1f32 / 3f32;
                        let schedule = movie_card.linearAttributes.unwrap().schedule.unwrap();
                        assert_eq!(
                            props,
                            UpdatableProgressBarProperties::test_new(
                                TileProgressBarProps {
                                    variant: ProgressBarVariant::OnAir.into(),
                                    progress: create_rw_signal(ctx.scope(), progress),
                                    tts_total_minutes: create_rw_signal(ctx.scope(), Some(180)),
                                },
                                ProgressBarTimePair::LinearShow(schedule),
                            )
                        );
                    })
                }
            }
        }

        /// Tests for metadata properties of MovieCards
        mod metadata {
            use super::*;

            #[test]
            fn movie_card_test_id_is_correct() {
                // GIVEN a movie card (don't need context or setup for this test)
                let movie_card = CardConfigBuilder::new().build_movie_card();

                // WHEN we get the test ID
                let test_id = movie_card.get_item_type_test_id();

                // THEN it should be "movie-card"
                assert_eq!(test_id, "movie-card");
            }
        }

        mod csm_data {
            use super::*;
            use crate::traits::to_csm_impression_data::ToCSMImpressionData;
            use cross_app_events::ImpressionData;
            use ignx_compositron::app::launch_only_scope;
            use std::collections::HashMap;

            #[test]
            fn creates_expected_card_csm_data() {
                launch_only_scope(|scope| {
                    // GIVEN a movie card and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let movie_card = CardConfigBuilder::new().build_movie_card();

                    // WHEN we get the CSM impression data
                    let csm_data = movie_card
                        .to_csm_impression_data(&analytics)
                        .expect("csm data should be defined");

                    // THEN it should contain the expected data
                    assert_eq!(
                        csm_data,
                        ImpressionData {
                            widget_type: Some("titleCard".into()),
                            ref_marker: Some("ref_marker".into()),
                            content_type: None,
                            benefit_id: Some("home".into()),
                            content_id: Some("home:home".into()),
                            creative_id: Some("cover image".into()),
                            analytics: Some(HashMap::new()),
                            slot_id: None,
                            size: None,
                            carousel_analytics: analytics,
                        }
                    );
                })
            }

            #[test]
            fn without_action_csm_data_is_none() {
                launch_only_scope(|scope| {
                    // GIVEN a movie card without action and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let movie_card = CardConfigBuilder::new()
                        .with_action(None)
                        .build_movie_card();

                    // WHEN we get the CSM impression data
                    let csm_data = movie_card.to_csm_impression_data(&analytics);

                    // THEN it should be None
                    assert!(csm_data.is_none());
                })
            }
        }
    }

    mod series_card {
        use super::*;
        use common_transform_types::actions::{Action, TransitionAction};

        /// Tests for the Selectable trait functionality on SeriesCards
        mod select_handling {
            use super::*;
            use crate::traits::selectable::TransitionConfiguration;
            use ignx_compositron::app::launch_only_scope;

            #[test]
            fn with_transition_action_produces_expected_transition() {
                // GIVEN specific expected values
                launch_only_scope(|scope| {
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "series".to_string();

                    // AND transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // AND a series card with those values
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_series_card();

                    // WHEN get_select_behavior is called with seamless config
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, false);
                    assert_eq!(transition.content_type, Some("series".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                })
            }

            #[test]
            fn with_seamless_if_possible_config_creates_appropriate_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a card with transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "series".to_string();
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_series_card();

                    // AND seamless transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: true,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called with seamless config
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, true);
                    assert_eq!(transition.content_type, Some("series".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn without_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a series card with no action
                    let _setup = CardParsingTestSetup::new(scope);
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(None)
                        .build_series_card();
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                })
            }

            #[test]
            fn with_non_transition_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a generic title card with a non-transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(Some(Action::create_client_default()))
                        .build_series_card();

                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn open_contextual_menu_works() {
                launch_only_scope(|scope| {
                    // GIVEN a series card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_series_card();

                    // WHEN get_select_behavior is called with OpenContextualMenu strategy
                    let behaviour = card
                        .get_select_behavior(
                            scope,
                            ItemSelectStrategy::OpenContextualMenu,
                            RustPage::RUST_SEARCH,
                        )
                        .expect("Select behavior should be defined");

                    // Extract the menu
                    let menu = match behaviour {
                        ItemSelectBehavior::OpenContextualMenu(boxed_menu) => *boxed_menu,
                        _ => panic!("Expected a OpenContextualMenu behavior"),
                    };

                    // THEN the menu contains expected type
                    assert!(matches!(menu, ContextualMenuData::ContextualMenu(_)));
                });
            }

            #[test]
            fn gets_select_strategy_correctly() {
                launch_only_scope(|scope| {
                    // GIVEN a series card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_series_card();

                    // WHEN item_select_strategy called
                    let strategy = card.item_select_strategy(scope);

                    // THEN we get expected strategy
                    assert_eq!(strategy, ItemSelectStrategy::default())
                });
            }
        }

        /// Tests for transforming SeriesCard to StandardCardProps
        mod standard_card {
            use super::*;

            /// Tests for image properties of SeriesCards
            mod image_handling {
                use super::*;

                #[test]
                fn series_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn series_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn series_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with only box art image (no cover image) and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched and possibly blurred (depending on implementation)
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string()),
                            "Secondary image should be set to box art when primary image is box art and not adult content"
                        );
                    });
                }

                #[test]
                fn series_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn series_card_without_adult_content_uses_unblurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the image should not be blurred
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            !base_attrs.blur,
                            "Image should not be blurred when adult content flag is false"
                        );
                    });
                }

                #[test]
                fn series_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            /// Tests for badge and icon properties of SeriesCards
            mod entitlement {
                use super::*;
                use amzn_fable_tokens::{FableColor, FableIcon};
                use common_transform_types::container_items::{
                    EntitlementGlanceIcons, TitleMetadataBadgeLevel,
                };
                use fableous::badges::label_badge::LabelBadgeColorScheme;
                use fableous::utils::get_ignx_color;
                use ignx_compositron::text::TextContent;
                use rstest::rstest;

                #[test]
                fn series_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn series_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a series card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn series_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a series card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn series_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn series_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of SeriesCards
            mod progress_bar {
                use super::*;

                #[test]
                fn series_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn series_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }

                #[test]
                fn series_card_with_linear_schedule_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with linear attributes schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set with OnAir variant
                        let progress_bar =
                            props.progress_bar_props.expect("Should have progress bar");

                        // Check that it's using the OnAir variant
                        assert_eq!(
                            progress_bar.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::OnAir
                        );
                    });
                }

                #[test]
                fn series_card_without_linear_schedule_has_no_on_air_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without linear schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            // Explicitly not calling with_linear_schedule()
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar of type OnAir
                        if let Some(progress_bar) = props.progress_bar_props {
                            let variant = progress_bar.variant.get_untracked();
                            assert_ne!(
                                variant,
                                fableous::progress_bar::ProgressBarVariant::OnAir,
                                "There should not be an OnAir progress bar without linear schedule"
                            );
                        }
                    });
                }
            }

            /// Tests for provider logo properties of SeriesCards
            mod provider_logo {
                use super::*;

                #[test]
                fn series_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[test]
                fn series_card_without_provider_logo_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(ProviderLogoData::None)
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when no provider logo is set"
                        );
                    });
                }

                #[test]
                fn series_card_with_incomplete_provider_logo_data_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with incomplete provider logo (URL only)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_provider_logo_data(ProviderLogoData::Url)
                            .build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when provider logo data is incomplete"
                        );
                    });
                }
            }

            /// Tests for text properties of SeriesCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn series_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new().build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert!(props.title.get_untracked().is_none());
                        assert!(props.subtitle.get_untracked().is_none());
                    });
                }

                #[test]
                fn series_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new().build_series_card();

                        // WHEN converted to StandardCardProps
                        let props = series_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN fallback text should be None
                        assert!(props.fallback_text.is_none());
                    });
                }
            }

            /// Obtaining the updatable UI for SeriesCards displayed as StandardCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::{
                    ProgressBarTimePair, UpdatableCardProperties, UpdatableProgressBarProperties,
                };
                use fableous::progress_bar::ProgressBarVariant;
                use fableous::tiles::tile::TileProgressBarProps;
                use ignx_compositron::prelude::create_rw_signal;

                #[test]
                fn has_no_updatable_ui_properties_when_no_linear_attributes() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new().build_series_card();

                        // WHEN we get updatable properties from the standard card
                        let card = series_card
                            .to_standard_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = series_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since series card without linear attributes has no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }

                #[test]
                fn has_correct_updatable_ui_properties_when_linear_attributes() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with linear attributes
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_series_card();

                        // WHEN we get updatable properties from the standard card
                        let card = series_card
                            .to_standard_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = series_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the progress bar variant
                        let UpdatableCardProperties::ProgressBarOnly(props) = props else {
                            panic!("should be progress bar variant");
                        };
                        let progress = 1f32 / 3f32;
                        let schedule = series_card
                            .linearAttributes
                            .unwrap()
                            .linearAttributes
                            .schedule
                            .unwrap();
                        assert_eq!(
                            props,
                            UpdatableProgressBarProperties::test_new(
                                TileProgressBarProps {
                                    variant: ProgressBarVariant::OnAir.into(),
                                    progress: create_rw_signal(ctx.scope(), progress),
                                    tts_total_minutes: create_rw_signal(ctx.scope(), Some(180)),
                                },
                                ProgressBarTimePair::LinearShow(schedule),
                            )
                        );
                    })
                }
            }
        }

        /// Tests for transforming SeriesCard to SmallCardProps
        mod small_card {
            use super::*;

            /// Tests for image properties of SeriesCards
            mod image_handling {
                use super::*;

                #[test]
                fn series_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn series_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn series_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with only box art image (no cover image) and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched and possibly blurred (depending on implementation)
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string()),
                            "Secondary image should be set to box art when primary image is box art and not adult content"
                        );
                    });
                }

                #[test]
                fn series_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn series_card_without_adult_content_uses_unblurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the image should not be blurred
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            !base_attrs.blur,
                            "Image should not be blurred when adult content flag is false"
                        );
                    });
                }

                #[test]
                fn series_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            /// Tests for badge and icon properties of SeriesCards
            mod entitlement {
                use super::*;
                use amzn_fable_tokens::{FableColor, FableIcon};
                use common_transform_types::container_items::{
                    EntitlementGlanceIcons, TitleMetadataBadgeLevel,
                };
                use fableous::badges::label_badge::LabelBadgeColorScheme;
                use fableous::utils::get_ignx_color;
                use ignx_compositron::text::TextContent;
                use rstest::rstest;

                #[test]
                fn series_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn series_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a series card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn series_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a series card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn series_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn series_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of SeriesCards
            mod progress_bar {
                use super::*;

                #[test]
                fn series_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn series_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }

                #[test]
                fn series_card_with_linear_schedule_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with linear attributes schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set with OnAir variant
                        let progress_bar =
                            props.progress_bar_props.expect("Should have progress bar");

                        // Check that it's using the OnAir variant
                        assert_eq!(
                            progress_bar.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::OnAir
                        );
                    });
                }

                #[test]
                fn series_card_without_linear_schedule_has_no_on_air_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without linear schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            // Explicitly not calling with_linear_schedule()
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar of type OnAir
                        if let Some(progress_bar) = props.progress_bar_props {
                            let variant = progress_bar.variant.get_untracked();
                            assert_ne!(
                                variant,
                                fableous::progress_bar::ProgressBarVariant::OnAir,
                                "There should not be an OnAir progress bar without linear schedule"
                            );
                        }
                    });
                }
            }

            /// Tests for provider logo properties of SeriesCards
            mod provider_logo {
                use super::*;

                #[test]
                fn series_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[test]
                fn series_card_without_provider_logo_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card without provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(ProviderLogoData::None)
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when no provider logo is set"
                        );
                    });
                }

                #[test]
                fn series_card_with_incomplete_provider_logo_data_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with incomplete provider logo (URL only)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_provider_logo_data(ProviderLogoData::Url)
                            .build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when provider logo data is incomplete"
                        );
                    });
                }
            }

            /// Tests for text properties of SeriesCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn series_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new().build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert!(props.title.get_untracked().is_none());
                        assert!(props.subtitle.get_untracked().is_none());
                    });
                }

                #[test]
                fn series_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new().build_series_card();

                        // WHEN converted to SmallCardProps
                        let props = series_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN fallback text should be None
                        assert!(props.fallback_text.is_none());
                    });
                }
            }

            /// Obtaining the updatable UI for SeriesCards displayed as SmallCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::{
                    ProgressBarTimePair, UpdatableCardProperties, UpdatableProgressBarProperties,
                };
                use fableous::progress_bar::ProgressBarVariant;
                use fableous::tiles::tile::TileProgressBarProps;
                use ignx_compositron::prelude::create_rw_signal;

                #[test]
                fn has_no_updatable_ui_properties_when_no_linear_attributes() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new().build_series_card();

                        // WHEN we get updatable properties from the small card
                        let card = series_card
                            .to_small_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = series_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since series card without linear attributes has no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }

                #[test]
                fn has_correct_updatable_ui_properties_when_linear_attributes() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a series card with linear attributes
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let series_card = CardConfigBuilder::new()
                            .with_linear_schedule()
                            .build_series_card();

                        // WHEN we get updatable properties from the small card
                        let card = series_card
                            .to_small_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = series_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the progress bar variant
                        let UpdatableCardProperties::ProgressBarOnly(props) = props else {
                            panic!("should be progress bar variant");
                        };
                        let progress = 1f32 / 3f32;
                        let schedule = series_card
                            .linearAttributes
                            .unwrap()
                            .linearAttributes
                            .schedule
                            .unwrap();
                        assert_eq!(
                            props,
                            UpdatableProgressBarProperties::test_new(
                                TileProgressBarProps {
                                    variant: ProgressBarVariant::OnAir.into(),
                                    progress: create_rw_signal(ctx.scope(), progress),
                                    tts_total_minutes: create_rw_signal(ctx.scope(), Some(180)),
                                },
                                ProgressBarTimePair::LinearShow(schedule),
                            )
                        );
                    })
                }
            }
        }

        /// Tests for metadata properties of SeriesCards
        mod metadata {
            use super::*;

            #[test]
            fn series_card_test_id_is_correct() {
                // GIVEN a series card (don't need context or setup for this test)
                let series_card = CardConfigBuilder::new().build_series_card();

                // WHEN we get the test ID
                let test_id = series_card.get_item_type_test_id();

                // THEN it should be "series-card"
                assert_eq!(test_id, "series-card");
            }
        }

        mod csm_data {
            use super::*;
            use crate::traits::to_csm_impression_data::ToCSMImpressionData;
            use cross_app_events::ImpressionData;
            use ignx_compositron::app::launch_only_scope;
            use std::collections::HashMap;

            #[test]
            fn creates_expected_card_csm_data() {
                launch_only_scope(|scope| {
                    // GIVEN a series card and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let series_card = CardConfigBuilder::new().build_series_card();

                    // WHEN we get the CSM impression data
                    let csm_data = series_card
                        .to_csm_impression_data(&analytics)
                        .expect("csm data should be defined");

                    // THEN it should contain the expected data
                    assert_eq!(
                        csm_data,
                        ImpressionData {
                            widget_type: Some("titleCard".into()),
                            ref_marker: Some("ref_marker".into()),
                            content_type: None,
                            benefit_id: Some("home".into()),
                            content_id: Some("home:home".into()),
                            creative_id: Some("cover image".into()),
                            analytics: Some(HashMap::new()),
                            slot_id: None,
                            size: None,
                            carousel_analytics: analytics,
                        }
                    );
                })
            }

            #[test]
            fn without_action_csm_data_is_none() {
                launch_only_scope(|scope| {
                    // GIVEN a series card without action and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let series_card = CardConfigBuilder::new()
                        .with_action(None)
                        .build_series_card();

                    // WHEN we get the CSM impression data
                    let csm_data = series_card.to_csm_impression_data(&analytics);

                    // THEN it should be None
                    assert!(csm_data.is_none());
                })
            }
        }
    }

    mod show_card {
        use super::*;
        use crate::impls::card_test_utils::{
            CardConfigBuilder, CardImageSource, CardParsingTestSetup, EntitlementType,
            ProviderLogoData,
        };
        use crate::traits::displayable_as_item::DisplayableAsItem;
        use amzn_fable_tokens::{FableColor, FableIcon};
        use common_transform_types::actions::{Action, TransitionAction};
        use common_transform_types::container_items::{
            EntitlementGlanceIcons, TitleMetadataBadgeLevel,
        };
        use fableous::badges::label_badge::LabelBadgeColorScheme;
        use fableous::utils::get_ignx_color;
        use ignx_compositron::app::launch_only_app_context;
        use ignx_compositron::prelude::SignalGetUntracked;
        use ignx_compositron::text::TextContent;

        /// Tests for the Selectable trait functionality on ShowCards
        mod select_handling {
            use super::*;
            use crate::traits::selectable::TransitionConfiguration;
            use ignx_compositron::app::launch_only_scope;

            #[test]
            fn with_transition_action_produces_expected_transition() {
                launch_only_scope(|scope| {
                    // GIVEN specific expected values
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "show".to_string();

                    // AND transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // AND a show card with those values
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_show_card();

                    // WHEN get_select_behavior is called
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, false);
                    assert_eq!(transition.content_type, Some("show".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn with_seamless_if_possible_config_creates_appropriate_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a card with transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "show".to_string();

                    // AND a show card with those values
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_show_card();

                    // AND seamless transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: true,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called with seamless config
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, true);
                    assert_eq!(transition.content_type, Some("show".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn without_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a show card with no action
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(None)
                        .build_show_card();

                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn with_non_transition_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a show card with a non-transition action
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(Some(Action::create_client_default()))
                        .build_show_card();

                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn open_contextual_menu_works() {
                launch_only_scope(|scope| {
                    // GIVEN a show card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_show_card();

                    // WHEN get_select_behavior is called with OpenContextualMenu strategy
                    let behaviour = card
                        .get_select_behavior(
                            scope,
                            ItemSelectStrategy::OpenContextualMenu,
                            RustPage::RUST_SEARCH,
                        )
                        .expect("Select behavior should be defined");

                    // Extract the menu
                    let menu = match behaviour {
                        ItemSelectBehavior::OpenContextualMenu(boxed_menu) => *boxed_menu,
                        _ => panic!("Expected a OpenContextualMenu behavior"),
                    };

                    // THEN the menu contains expected type
                    assert!(matches!(menu, ContextualMenuData::ContextualMenu(_)));
                });
            }

            #[test]
            fn gets_select_strategy_correctly() {
                launch_only_scope(|scope| {
                    // GIVEN a show card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_show_card();

                    // WHEN item_select_strategy called
                    let strategy = card.item_select_strategy(scope);

                    // THEN we get expected strategy
                    assert_eq!(strategy, ItemSelectStrategy::default())
                });
            }
        }

        // Tests for transforming ShowCard to StandardCardProps
        mod standard_card {
            use super::*;

            // Tests for image properties of ShowCards
            mod image_handling {
                use super::*;

                #[test]
                fn show_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn show_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn show_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with only box art image (no cover image) and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched and possibly blurred (depending on implementation)
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string()),
                            "Secondary image should be set to box art when primary image is box art and not adult content"
                        );
                    });
                }

                #[test]
                fn show_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with adult content flag and cover image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn show_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(
                            props.secondary_base_image.is_none(),
                            "Secondary image should be None when box art is blurred due to adult content"
                        );
                    });
                }

                #[test]
                fn show_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when adult content is true, even with both image types");
                    });
                }

                #[test]
                fn show_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            // Tests for badge and icon properties of ShowCards
            mod entitlement {
                use super::*;

                #[test]
                fn show_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn show_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a show card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn show_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a show card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn show_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn show_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            // Tests for progress bar properties of ShowCards
            mod progress_bar {
                use super::*;

                #[test]
                fn show_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn show_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }

                // Note: ShowCard doesn't have linearAttributes field and its HasLinearAttributes implementation
                // always returns None, so we can't test for progress bar with linear schedule

                #[test]
                fn show_card_without_linear_schedule_has_no_on_air_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without linear schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            // Explicitly not calling with_linear_schedule()
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar of type OnAir
                        if let Some(progress_bar) = props.progress_bar_props {
                            let variant = progress_bar.variant.get_untracked();
                            assert_ne!(
                                variant,
                                fableous::progress_bar::ProgressBarVariant::OnAir,
                                "There should not be an OnAir progress bar without linear schedule"
                            );
                        }
                    });
                }
            }

            // Tests for provider logo properties of ShowCards
            mod provider_logo {
                use super::*;

                #[test]
                fn show_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[rstest]
                #[case(ProviderLogoData::Url)]
                #[case(ProviderLogoData::Metadata)]
                #[case(ProviderLogoData::None)]
                fn show_card_without_complete_provider_logo_data_has_no_logo_props(
                    #[case] provider_logo_data: ProviderLogoData,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with incomplete provider logo data
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(provider_logo_data)
                            .build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(provider_logo_props.is_none());
                    });
                }
            }

            // Tests for text properties of ShowCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn show_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new().build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert!(props.title.get_untracked().is_none());
                        assert!(props.subtitle.get_untracked().is_none());
                    });
                }

                #[test]
                fn show_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new().build_show_card();

                        // WHEN converted to StandardCardProps
                        let props = show_card.to_standard_card(&ctx, setup.now).unwrap();

                        // THEN fallback text should be None
                        assert!(props.fallback_text.is_none());
                    });
                }
            }

            /// Obtaining the updatable UI for ShowCards displayed as StandardCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::UpdatableCardProperties;

                #[test]
                fn has_no_updatable_ui_properties() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new().build_show_card();

                        // WHEN we get updatable properties from the standard card
                        let card = show_card
                            .to_standard_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = show_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since show card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }
            }
        }

        // Tests for transforming ShowCard to SmallCardProps
        mod small_card {
            use super::*;

            // Tests for image properties of ShowCards
            mod image_handling {
                use super::*;

                #[test]
                fn show_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn show_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn show_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with only box art image (no cover image) and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched and possibly blurred (depending on implementation)
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string()),
                            "Secondary image should be set to box art when primary image is box art and not adult content"
                        );
                    });
                }

                #[test]
                fn show_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with adult content flag and cover image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert!(props.secondary_base_image.is_none());
                    });
                }

                #[test]
                fn show_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(
                            props.secondary_base_image.is_none(),
                            "Secondary image should be None when box art is blurred due to adult content"
                        );
                    });
                }

                #[test]
                fn show_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when adult content is true, even with both image types");
                    });
                }

                #[test]
                fn show_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            // Tests for badge and icon properties of ShowCards
            mod entitlement {
                use super::*;

                #[test]
                fn show_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn show_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a show card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn show_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a show card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn show_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn show_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            // Tests for progress bar properties of ShowCards
            mod progress_bar {
                use super::*;

                #[test]
                fn show_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            fableous::progress_bar::ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn show_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }

                // Note: ShowCard doesn't have linearAttributes field and its HasLinearAttributes implementation
                // always returns None, so we can't test for progress bar with linear schedule

                #[test]
                fn show_card_without_linear_schedule_has_no_on_air_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card without linear schedule
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            // Explicitly not calling with_linear_schedule()
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN there should be no progress bar of type OnAir
                        if let Some(progress_bar) = props.progress_bar_props {
                            let variant = progress_bar.variant.get_untracked();
                            assert_ne!(
                                variant,
                                fableous::progress_bar::ProgressBarVariant::OnAir,
                                "There should not be an OnAir progress bar without linear schedule"
                            );
                        }
                    });
                }
            }

            // Tests for provider logo properties of ShowCards
            mod provider_logo {
                use super::*;

                #[test]
                fn show_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[rstest]
                #[case(ProviderLogoData::Url)]
                #[case(ProviderLogoData::Metadata)]
                #[case(ProviderLogoData::None)]
                fn show_card_without_complete_provider_logo_data_has_no_logo_props(
                    #[case] provider_logo_data: ProviderLogoData,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card with incomplete provider logo data
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(provider_logo_data)
                            .build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(provider_logo_props.is_none());
                    });
                }
            }

            // Tests for text properties of ShowCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn show_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new().build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert!(props.title.get_untracked().is_none());
                        assert!(props.subtitle.get_untracked().is_none());
                    });
                }

                #[test]
                fn show_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new().build_show_card();

                        // WHEN converted to SmallCardProps
                        let props = show_card.to_small_card(&ctx, setup.now).unwrap();

                        // THEN fallback text should be None
                        assert!(props.fallback_text.is_none());
                    });
                }
            }

            /// Obtaining the updatable UI for ShowCards displayed as SmallCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::UpdatableCardProperties;

                #[test]
                fn has_no_updatable_ui_properties() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a show card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let show_card = CardConfigBuilder::new().build_show_card();

                        // WHEN we get updatable properties from the small card
                        let card = show_card
                            .to_small_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = show_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since show card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }
            }
        }

        // Tests for metadata properties of ShowCards
        mod metadata {
            use super::*;

            #[test]
            fn show_card_test_id_is_correct() {
                // GIVEN a show card (don't need context or setup for this test)
                let show_card = CardConfigBuilder::new().build_show_card();

                // WHEN we get the test ID
                let test_id = show_card.get_item_type_test_id();

                // THEN it should be "show-card"
                assert_eq!(test_id, "show-card");
            }
        }

        mod csm_data {
            use super::*;
            use crate::traits::to_csm_impression_data::ToCSMImpressionData;
            use cross_app_events::ImpressionData;
            use ignx_compositron::app::launch_only_scope;
            use std::collections::HashMap;

            #[test]
            fn creates_expected_card_csm_data() {
                launch_only_scope(|scope| {
                    // GIVEN a show card and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let show_card = CardConfigBuilder::new().build_show_card();

                    // WHEN we get the CSM impression data
                    let csm_data = show_card
                        .to_csm_impression_data(&analytics)
                        .expect("csm data should be defined");

                    // THEN it should contain the expected data
                    assert_eq!(
                        csm_data,
                        ImpressionData {
                            widget_type: Some("titleCard".into()),
                            ref_marker: Some("ref_marker".into()),
                            content_type: None,
                            benefit_id: Some("home".into()),
                            content_id: Some("home:home".into()),
                            creative_id: Some("cover image".into()),
                            analytics: Some(HashMap::new()),
                            slot_id: None,
                            size: None,
                            carousel_analytics: analytics,
                        }
                    );
                })
            }

            #[test]
            fn without_action_csm_data_is_none() {
                launch_only_scope(|scope| {
                    // GIVEN a show card without action and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let show_card = CardConfigBuilder::new().with_action(None).build_show_card();

                    // WHEN we get the CSM impression data
                    let csm_data = show_card.to_csm_impression_data(&analytics);

                    // THEN it should be None
                    assert!(csm_data.is_none());
                })
            }
        }
    }

    mod vod_extra_content_card {
        use super::*;
        use crate::impls::card_test_utils::{
            CardConfigBuilder, CardImageSource, CardParsingTestSetup, EntitlementType,
            ProviderLogoData,
        };
        use crate::traits::displayable_as_item::DisplayableAsItem;
        use common_transform_types::actions::{Action, TransitionAction};
        use ignx_compositron::app::launch_only_app_context;
        use ignx_compositron::prelude::SignalGetUntracked;

        /// Tests for the Selectable trait functionality on VodExtraContentCards
        mod select_handling {
            use super::*;
            use crate::traits::selectable::TransitionConfiguration;
            use ignx_compositron::app::launch_only_scope;
            use location::RustPage;

            #[test]
            fn with_transition_action_produces_expected_transition() {
                launch_only_scope(|scope| {
                    // GIVEN specific expected values
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "vod-extra".to_string();

                    // AND transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // AND a vod extra content card with those values
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_vod_extra_content_card();

                    // WHEN get_select_behavior is called
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, false);
                    assert_eq!(transition.content_type, Some("vod-extra".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn with_seamless_if_possible_config_creates_appropriate_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a card with transition action
                    let _setup = CardParsingTestSetup::new(scope);
                    let expected_action = TransitionAction::create_landing_with_service_token(None);
                    let expected_gti = "test-gti-123".to_string();
                    let expected_content_type = "vod-extra".to_string();
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_gti(Some(expected_gti.clone()))
                        .with_content_type(Some(expected_content_type.clone()))
                        .with_action(Some(Action::TransitionAction(expected_action.clone())))
                        .build_vod_extra_content_card();

                    // AND seamless transition configuration
                    let config = TransitionConfiguration {
                        seamless_if_possible: true,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called with seamless config
                    let behavior = card
                        .get_select_behavior(scope, strategy, RustPage::RUST_SEARCH)
                        .expect("select behavior should be defined");

                    // Extract the transition
                    let transition = match behavior {
                        ItemSelectBehavior::Transition(boxed_transition) => *boxed_transition,
                        _ => panic!("Expected a Transition behavior"),
                    };

                    // THEN the transition contains expected values
                    assert_eq!(transition.action, expected_action);
                    assert_eq!(transition.enable_duplicate_destination_navigation, None);
                    assert_eq!(transition.deferred_action, None);
                    assert_eq!(transition.is_signed_in, true);
                    assert!(transition.rust_features.is_some());
                    assert_eq!(transition.seamless_if_possible, true);
                    assert_eq!(transition.content_type, Some("vod-extra".to_string()));
                    assert_eq!(transition.title_id, Some(expected_gti));
                    assert_eq!(transition.playback_origin_override, None);
                    assert_eq!(transition.ingress_source, None);
                });
            }

            #[test]
            fn without_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a vod extra content card with no action
                    let _setup = CardParsingTestSetup::new(scope);
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(None)
                        .build_vod_extra_content_card();
                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn with_non_transition_action_produces_no_transition() {
                launch_only_scope(|scope| {
                    // GIVEN a vod extra content card with a non-transition action
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .with_action(Some(Action::create_client_default()))
                        .build_vod_extra_content_card();

                    let config = TransitionConfiguration {
                        seamless_if_possible: false,
                        ..TransitionConfiguration::default()
                    };
                    let strategy = ItemSelectStrategy::Transition(config);

                    // WHEN get_select_behavior is called
                    let result = card.get_select_behavior(scope, strategy, RustPage::RUST_SEARCH);

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::NoTransitionAction
                    ));
                });
            }

            #[test]
            fn contextual_menu_metadata_is_none() {
                launch_only_scope(|scope| {
                    // GIVEN a vod extra content card
                    let card = CardConfigBuilder::new()
                        .with_image_source(CardImageSource::CoverImage)
                        .build_vod_extra_content_card();

                    // WHEN get_select_behavior is called with OpenContextualMenu strategy
                    let result = card.get_select_behavior(
                        scope,
                        ItemSelectStrategy::OpenContextualMenu,
                        RustPage::RUST_SEARCH,
                    );

                    // THEN an error is returned
                    assert!(result.is_err());
                    assert!(matches!(
                        result.unwrap_err(),
                        SelectParsingError::CouldNotConstructContextualMenu
                    ));
                });
            }
        }

        /// Tests for transforming VodExtraContentCard to SmallCardProps
        mod small_card {
            use super::*;

            /// Tests for image properties of VodExtraContentCards
            mod image_handling {
                use super::*;

                #[test]
                fn vod_extra_content_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn vod_extra_content_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn vod_extra_content_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with only box art image (no cover image)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string())
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn vod_extra_content_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(
                            props.secondary_base_image.is_none(),
                            "Secondary image should be None when box art is blurred due to adult content"
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when adult content is true, even with both image types");
                    });
                }

                #[test]
                fn vod_extra_content_card_without_adult_content_uses_unblurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the image should not be blurred
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            !base_attrs.blur,
                            "Image should not be blurred when adult content flag is false"
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card.to_small_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            /// Tests for badge and icon properties of VodExtraContentCards
            mod entitlement {
                use super::*;
                use amzn_fable_tokens::{FableColor, FableIcon};
                use common_transform_types::container_items::{
                    EntitlementGlanceIcons, TitleMetadataBadgeLevel,
                };
                use fableous::badges::label_badge::LabelBadgeColorScheme;
                use fableous::utils::get_ignx_color;
                use ignx_compositron::text::TextContent;

                #[test]
                fn vod_extra_content_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a vod_extra_content card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn vod_extra_content_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a vod_extra_content card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn vod_extra_content_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a vod_extra_content card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn vod_extra_content_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn vod_extra_content_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of VodExtraContentCards
            mod progress_bar {
                use super::*;
                use fableous::progress_bar::ProgressBarVariant;

                #[test]
                fn vod_extra_content_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }
            }

            /// Tests for provider logo properties of VodExtraContentCards
            mod provider_logo {
                use super::*;

                #[test]
                fn vod_extra_content_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[test]
                fn vod_extra_content_card_without_provider_logo_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(ProviderLogoData::None)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when no provider logo is set"
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_with_incomplete_provider_logo_data_has_no_provider_logo_props(
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with incomplete provider logo (URL only)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_provider_logo_data(ProviderLogoData::Url)
                            .build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when provider logo data is incomplete"
                        );
                    });
                }
            }

            /// Tests for text properties of VodExtraContentCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn vod_extra_content_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card =
                            CardConfigBuilder::new().build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert_eq!(props.title.get_untracked(), None);
                        assert_eq!(props.subtitle.get_untracked(), None);
                    });
                }

                #[test]
                fn vod_extra_content_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card =
                            CardConfigBuilder::new().build_vod_extra_content_card();

                        // WHEN converted to SmallCardProps
                        let props = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .unwrap();

                        // THEN fallback text should be None
                        assert_eq!(props.fallback_text, None);
                    });
                }
            }

            /// Obtaining the updatable UI for VodExtraContent displayed as SmallCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::UpdatableCardProperties;

                #[test]
                fn has_no_updatable_ui_properties() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VodExtraContentCard
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card =
                            CardConfigBuilder::new().build_vod_extra_content_card();

                        // WHEN we get updatable properties from the small card
                        let card = vod_extra_content_card
                            .to_small_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = vod_extra_content_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since vod_extra_content_card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }
            }
        }

        /// Tests for transforming VodExtraContentCard to StandardCardProps
        mod standard_card {
            use super::*;

            /// Tests for image properties of VodExtraContentCards
            mod image_handling {
                use super::*;

                #[test]
                fn vod_extra_content_card_with_cover_image_sets_correct_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with a cover image only and explicitly not adult
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn vod_extra_content_card_with_both_cover_image_and_box_art_prefers_cover_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with both cover image and box art
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(false) // Explicitly set to false
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should not be blurred or stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(!base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when cover image is available, even if box art is also present");
                    });
                }

                #[test]
                fn vod_extra_content_card_with_box_art_only_uses_box_art_with_stretching() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with only box art image (no cover image)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the box art image should be used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND image should be stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should also be set as secondary image ("clean" top image)
                        assert_eq!(
                            props.secondary_base_image,
                            Some("box art image".to_string())
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_with_adult_content_uses_blurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(true)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None);
                    });
                }

                #[test]
                fn vod_extra_content_card_with_adult_content_and_boxart_only() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with adult content flag and only box art image
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::BoxArt)
                            .with_adult_content(true)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the box art image should be used
                        let image = props.image;
                        assert_eq!(image, Some("box art image".to_string()));

                        // AND the image should be both blurred and stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(base_attrs.blur);
                        assert!(base_attrs.stretch_background.has_stretch());

                        // AND box art should NOT be set as secondary image (since it is blurred anyway)
                        assert!(
                            props.secondary_base_image.is_none(),
                            "Secondary image should be None when box art is blurred due to adult content"
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_with_both_cover_image_and_box_art_and_adult_content() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with both cover image and box art and adult content
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImageAndBoxArt)
                            .with_adult_content(true) // Explicitly set to true
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the cover image should be preferred and used as the card image
                        let image = props.image;
                        assert_eq!(image, Some("cover image".to_string()));

                        // AND image should be blurred but not stretched
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            base_attrs.blur,
                            "Cover image should be blurred when adult content is true"
                        );
                        assert!(!base_attrs.stretch_background.has_stretch());

                        // AND no secondary image should be set
                        assert_eq!(props.secondary_base_image, None,
                                   "Secondary image should be None when adult content is true, even with both image types");
                    });
                }

                #[test]
                fn vod_extra_content_card_without_adult_content_uses_unblurred_image() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without adult content flag
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_adult_content(false) // Explicitly set to false
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN the image should not be blurred
                        let base_attrs = props.base_image_attributes.expect("defined");
                        assert!(
                            !base_attrs.blur,
                            "Image should not be blurred when adult content flag is false"
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_without_images_is_dropped() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without any images
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::None)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card.to_standard_card(&ctx, setup.now);

                        // THEN the result should be None
                        assert!(props.is_none());
                    });
                }
            }

            /// Tests for badge and icon properties of VodExtraContentCards
            mod entitlement {
                use super::*;
                use amzn_fable_tokens::{FableColor, FableIcon};
                use common_transform_types::container_items::{
                    EntitlementGlanceIcons, TitleMetadataBadgeLevel,
                };
                use fableous::badges::label_badge::LabelBadgeColorScheme;
                use fableous::utils::get_ignx_color;
                use ignx_compositron::text::TextContent;

                #[test]
                fn vod_extra_content_card_without_entitlements_has_no_badge_or_icon() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a vod_extra_content card without any entitlements
                        // (using the default CardConfigBuilder which has EntitlementType::None)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no badge props should be set
                        assert!(
                            props.badge_props.get_untracked().is_none(),
                            "Badge props should be None when no entitlements are set"
                        );

                        // AND no icon props should be set
                        assert!(
                            props.icon_props.is_none(),
                            "Icon props should be None when no entitlements are set"
                        );
                    });
                }

                #[rstest]
                #[case(TitleMetadataBadgeLevel::INFO, LabelBadgeColorScheme::PRIMARY)]
                #[case(TitleMetadataBadgeLevel::INFO_ACTIVE, LabelBadgeColorScheme::PRIMARY)]
                #[case(
                    TitleMetadataBadgeLevel::INFO_INACTIVE,
                    LabelBadgeColorScheme::SECONDARY
                )]
                #[case(TitleMetadataBadgeLevel::INFO_HIGHLIGHT, LabelBadgeColorScheme::LIVE)]
                fn vod_extra_content_card_with_badge_entitlement_has_badge_props(
                    #[case] metadata_badge_level: TitleMetadataBadgeLevel,
                    #[case] expected_color_scheme: LabelBadgeColorScheme,
                ) {
                    launch_only_app_context(move |ctx| {
                        // GIVEN a vod_extra_content card with badge entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Badge(metadata_badge_level))
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN entitlement badge props should be set
                        let badge_props = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be defined");

                        // AND have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge_props.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge_props.color_scheme.get_untracked(),
                            expected_color_scheme
                        );
                    });
                }

                #[rstest]
                #[case(
                    EntitlementGlanceIcons::OFFER_ICON,
                    FableIcon::STORE_FILLED,
                    FableColor::STORE
                )]
                #[case(
                    EntitlementGlanceIcons::ERROR_ICON,
                    FableIcon::ERROR,
                    FableColor::ERROR
                )]
                fn vod_extra_content_card_with_allowed_icon_entitlement_has_icon_props(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                    #[case] expected_icon: &str,
                    #[case] expected_color: FableColor,
                ) {
                    let expected_icon_string: String = expected_icon.into();
                    launch_only_app_context(move |ctx| {
                        // GIVEN a vod_extra_content card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN icon props should be set
                        let icon_props = props.icon_props;
                        assert!(icon_props.is_some());

                        // AND the icon should be the correct type
                        let icon = icon_props.expect("Icon props should be present");
                        assert_eq!(icon.icon.get_untracked(), expected_icon_string,);

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(expected_color).to_owned(),
                            "Icon should have the correct color"
                        );
                    });
                }

                #[rstest]
                #[case(EntitlementGlanceIcons::ENTITLED_ICON)]
                #[case(EntitlementGlanceIcons::ADS_ICON)]
                fn vod_extra_content_card_with_disallowed_entitlement_icon_doesnt_show_icon(
                    #[case] entitlement_icon: EntitlementGlanceIcons,
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_entitlement(EntitlementType::Icon(entitlement_icon))
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN icon props should not be set
                        assert!(props.icon_props.is_none());
                    });
                }

                #[test]
                fn vod_extra_content_card_with_complex_entitlement() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a gen card with both badge and icon entitlement
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_entitlement(EntitlementType::BadgeAndIcon(
                                TitleMetadataBadgeLevel::INFO,
                                EntitlementGlanceIcons::OFFER_ICON,
                            ))
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN both badge and icon props should be set
                        let badge = props
                            .badge_props
                            .get_untracked()
                            .expect("Badge props should be present");
                        let icon = props.icon_props.expect("Icon props should be present");

                        // AND the icon should be the correct type
                        assert_eq!(
                            icon.icon.get_untracked(),
                            FableIcon::STORE_FILLED.to_string()
                        );

                        // AND the icon should have the correct color
                        assert_eq!(
                            icon.color.get_untracked(),
                            get_ignx_color(FableColor::STORE).to_owned(),
                            "Icon should have the correct color"
                        );

                        // AND the badge should have correct text
                        let expected_text = TextContent::String("badge message".into());
                        assert_eq!(badge.text.get_untracked(), expected_text);

                        // AND have correct color scheme
                        assert_eq!(
                            badge.color_scheme.get_untracked(),
                            LabelBadgeColorScheme::PRIMARY
                        );
                    });
                }
            }

            /// Tests for progress bar properties of VodExtraContentCards
            mod progress_bar {
                use super::*;
                use fableous::progress_bar::ProgressBarVariant;

                #[test]
                fn vod_extra_content_card_with_watch_progress_sets_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_watch_progress(Some(0.5))
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN progress bar props should be set
                        let progress_bar_props = props
                            .progress_bar_props
                            .expect("Progress bar props should be defined");

                        // Verify progress value
                        assert_eq!(progress_bar_props.progress.get_untracked(), 0.5);

                        // Verify progress bar variant is set
                        assert_eq!(
                            progress_bar_props.variant.get_untracked(),
                            ProgressBarVariant::VOD
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_without_watch_progress_has_no_progress_bar() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without watch progress
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_watch_progress(None)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN there should be no progress bar
                        assert!(
                            props.progress_bar_props.is_none(),
                            "Progress bar props should be None when no watch progress is set"
                        );
                    });
                }
            }

            /// Tests for provider logo properties of VodExtraContentCards
            mod provider_logo {
                use super::*;

                #[test]
                fn vod_extra_content_card_with_provider_logo_data_sets_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_provider_logo_data(ProviderLogoData::UrlAndMetadata)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN provider logo props should be set
                        let provider_logo_props =
                            props.provider_logo_props.expect("defined provider logo");

                        // Check image URL when present
                        assert_eq!(provider_logo_props.url, "provider logo url");
                        assert!(provider_logo_props.metadata.is_some());
                    });
                }

                #[test]
                fn vod_extra_content_card_without_provider_logo_has_no_provider_logo_props() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card without provider logo
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage) // Required for card to not be dropped
                            .with_provider_logo_data(ProviderLogoData::None)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when no provider logo is set"
                        );
                    });
                }

                #[test]
                fn vod_extra_content_card_with_incomplete_provider_logo_data_has_no_provider_logo_props(
                ) {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card with incomplete provider logo (URL only)
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card = CardConfigBuilder::new()
                            .with_image_source(CardImageSource::CoverImage)
                            .with_provider_logo_data(ProviderLogoData::Url)
                            .build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN no provider logo props should be set
                        let provider_logo_props = props.provider_logo_props;
                        assert!(
                            provider_logo_props.is_none(),
                            "Provider logo props should be None when provider logo data is incomplete"
                        );
                    });
                }
            }

            /// Tests for text properties of VodExtraContentCards (title and subtitle)
            mod text_props {
                use super::*;

                #[test]
                fn vod_extra_content_card_has_no_title_or_subtitle() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card =
                            CardConfigBuilder::new().build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN title and subtitle should be None (rendered externally)
                        assert_eq!(props.title.get_untracked(), None);
                        assert_eq!(props.subtitle.get_untracked(), None);
                    });
                }

                #[test]
                fn vod_extra_content_card_has_no_fallback_text() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VOD extra content card
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card =
                            CardConfigBuilder::new().build_vod_extra_content_card();

                        // WHEN converted to StandardCardProps
                        let props = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .unwrap();

                        // THEN fallback text should be None
                        assert_eq!(props.fallback_text, None);
                    });
                }
            }

            /// Obtaining the updatable UI for VodExtraContent displayed as StandardCards
            mod updatable_ui {
                use super::*;
                use crate::types::updatable_properties::UpdatableCardProperties;

                #[test]
                fn has_no_updatable_ui_properties() {
                    launch_only_app_context(|ctx| {
                        // GIVEN a VodExtraContentCard
                        let setup = CardParsingTestSetup::new(ctx.scope);
                        let vod_extra_content_card =
                            CardConfigBuilder::new().build_vod_extra_content_card();

                        // WHEN we get updatable properties from the standard card
                        let card = vod_extra_content_card
                            .to_standard_card(&ctx, setup.now)
                            .expect("card defined");
                        let props = vod_extra_content_card.to_updatable_ui(&Box::new(card), None);

                        // THEN it should be the None variant since vod_extra_content_card doesn't have linear attributes so no updatable UI elements
                        assert!(matches!(props, UpdatableCardProperties::None));
                    })
                }
            }
        }

        /// Tests for metadata properties of VodExtraContentCards
        mod metadata {
            use super::*;

            #[test]
            fn vod_extra_content_card_test_id_is_correct() {
                // GIVEN a VOD extra content card (don't need context or setup for this test)
                let vod_extra_content_card =
                    CardConfigBuilder::new().build_vod_extra_content_card();

                // WHEN we get the test ID
                let test_id = vod_extra_content_card.get_item_type_test_id();

                // THEN it should be "vod-extra-content-card"
                assert_eq!(test_id, "vod-extra-content-card");
            }
        }

        mod csm_data {
            use super::*;
            use crate::traits::to_csm_impression_data::ToCSMImpressionData;
            use cross_app_events::ImpressionData;
            use ignx_compositron::app::launch_only_scope;
            use std::collections::HashMap;

            #[test]
            fn creates_expected_card_csm_data() {
                launch_only_scope(|scope| {
                    // GIVEN a vod_extra_content card and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let vod_extra_content_card =
                        CardConfigBuilder::new().build_vod_extra_content_card();

                    // WHEN we get the CSM impression data
                    let csm_data = vod_extra_content_card
                        .to_csm_impression_data(&analytics)
                        .expect("csm data should be defined");

                    // THEN it should contain the expected data
                    assert_eq!(
                        csm_data,
                        ImpressionData {
                            widget_type: Some("titleCard".into()),
                            ref_marker: Some("ref_marker".into()),
                            content_type: None,
                            benefit_id: Some("home".into()),
                            content_id: Some("card gti".into()),
                            creative_id: Some("cover image".into()),
                            analytics: Some(HashMap::new()),
                            slot_id: None,
                            size: None,
                            carousel_analytics: analytics,
                        }
                    );
                })
            }

            #[test]
            fn without_action_csm_data_is_none() {
                launch_only_scope(|scope| {
                    // GIVEN a vod_extra_content card without action and carousel analytics
                    let analytics = CardParsingTestSetup::new(scope).carousel_analytics;
                    let vod_extra_content_card = CardConfigBuilder::new()
                        .with_action(None)
                        .build_vod_extra_content_card();

                    // WHEN we get the CSM impression data
                    let csm_data = vod_extra_content_card.to_csm_impression_data(&analytics);

                    // THEN it should be None
                    assert!(csm_data.is_none());
                })
            }
        }
    }
}
