use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::TransitionAction;
use ignx_compositron::id::Id;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use strum_macros::Display;

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct Recap {
    pub recap_type: RecapType,
    pub background_image: Option<RecapBackgroundImage>,
    pub recap_segments: Vec<RecapSegment>,
    pub video_recap_action: Option<TransitionAction>,
}

#[derive(NetworkParsed, Clone, Hash, Display)]
#[derive_test_only(Debug, PartialEq)]
#[network(SNAKE_CASE_ALL)]
pub enum RecapType {
    Episode,
    Season,
    PreviousSeason,
    Movie,
}

impl Id for RecapType {
    type Id = RecapType;

    fn id(&self) -> &Self::Id {
        self
    }
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct RecapBackgroundImage {
    pub url: String,
    pub height: Option<i32>,
    pub width: Option<i32>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
#[network(camelCaseAll)]
pub struct RecapSegment {
    pub background_image: Option<RecapBackgroundImage>,
    pub text: Option<String>,
}
