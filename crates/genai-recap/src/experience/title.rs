use crate::types::RecapExperienceTitle;
use ignx_compositron::text::TextContent;

// TODO: Localization
pub(crate) fn create_title_text_content(
    show_title: &String,
    title: &RecapExperienceTitle,
) -> TextContent {
    match title {
        RecapExperienceTitle::ThisEpisode(data) => TextContent::String({
            match &data.episode_title {
                Some(episode_title) => format!(
                    "{}, Season {}, Ep. {}: {}",
                    show_title, data.season, data.episode, episode_title
                ),
                None => format!(
                    "{}, Season {}, Ep. {}",
                    show_title, data.season, data.episode
                ),
            }
        }),
        RecapExperienceTitle::ThisSeason(data) => {
            if data.selected_episode <= 2 {
                TextContent::String(format!(
                    "{}, Season {}, Episode 1",
                    show_title, data.selected_season,
                ))
            } else {
                TextContent::String(format!(
                    "{}, Season {}, Episodes 1 - {}",
                    show_title,
                    data.selected_season,
                    data.selected_episode - 1
                ))
            }
        }
        RecapExperienceTitle::PreviousSeason(data) => {
            TextContent::String(format!("{}, Season {}", show_title, data.previous_season,))
        }
        RecapExperienceTitle::Movie => TextContent::String(show_title.clone()),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{
        RecapExperienceTitle, RecapExperienceTitlePreviousSeason, RecapExperienceTitleThisEpisode,
        RecapExperienceTitleThisSeason,
    };

    #[test]
    pub fn test_this_episode() {
        let show_title = "Fallout".to_string();
        let data = RecapExperienceTitle::ThisEpisode(RecapExperienceTitleThisEpisode {
            season: 1,
            episode: 2,
            episode_title: Some("The End".to_string()),
        });
        let text_content = create_title_text_content(&show_title, &data);
        let expected = TextContent::String("Fallout, Season 1, Ep. 2: The End".to_string());
        assert_eq!(text_content, expected);
    }

    #[test]
    pub fn test_this_season_single_episode() {
        let show_title = "Fallout".to_string();
        let data = RecapExperienceTitle::ThisSeason(RecapExperienceTitleThisSeason {
            selected_season: 1,
            selected_episode: 2,
        });
        let text_content = create_title_text_content(&show_title, &data);
        let expected = TextContent::String("Fallout, Season 1, Episode 1".to_string());
        assert_eq!(text_content, expected);
    }

    #[test]
    pub fn test_this_season_multiple_episodes() {
        let show_title = "Fallout".to_string();
        let data = RecapExperienceTitle::ThisSeason(RecapExperienceTitleThisSeason {
            selected_season: 1,
            selected_episode: 5,
        });
        let text_content = create_title_text_content(&show_title, &data);
        let expected = TextContent::String("Fallout, Season 1, Episodes 1 - 4".to_string());
        assert_eq!(text_content, expected);
    }

    #[test]
    pub fn test_previous_season() {
        let show_title = "Fallout".to_string();
        let data = RecapExperienceTitle::PreviousSeason(RecapExperienceTitlePreviousSeason {
            previous_season: 1,
        });
        let text_content = create_title_text_content(&show_title, &data);
        let expected = TextContent::String("Fallout, Season 1".to_string());
        assert_eq!(text_content, expected);
    }

    #[test]
    pub fn test_movie() {
        let show_title = "Oppenheimer".to_string();
        let data = RecapExperienceTitle::Movie;
        let text_content = create_title_text_content(&show_title, &data);
        let expected = TextContent::String("Oppenheimer".to_string());
        assert_eq!(text_content, expected);
    }
}
