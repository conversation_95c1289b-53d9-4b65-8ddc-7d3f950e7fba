#[double]
use crate::app_events::GenAIRecapAppEventReporter;
use crate::app_events::{use_recap_event_reporter, RecapPaginationDirection};
use crate::experience::title::create_title_text_content;
use crate::network::RecapSegment;
use crate::types::metrics::{EXPERIENCE_MISSING_BACKGROUND_METRIC, EXPERIENCE_PAGINATED_METRIC};
use crate::types::{RecapAction, RecapExperienceModel};
use amzn_fable_tokens::{FableSpacing, FableText};
use fableous::animations::{FableMotionDuration, MotionDuration};
use fableous::buttons::play_button::*;
use fableous::buttons::primary_button::*;
use fableous::pagination_dots::pagination_dots::*;
use fableous::pointer_control::is_pointer_control_active;
use fableous::progress_bar::ProgressBarVariant;
use fableous::typography::type_ramp::TypeRamp;
use fableous::typography::typography::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::input::KeyCode;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::tts::TTSEnabledContext;
use ignx_compositron::{compose, compose_option, Composer};
use media_background::backgrounds::recap_background::*;
use media_background::types::RecapBackgroundData;
use mockall_double::double;
use std::rc::Rc;

pub const RECAP_EXPERIENCE_TEST_ID: &str = "recap-experience";
pub const RECAP_EXPERIENCE_BUTTON_TEST_ID: &str = "recap-experience-button";
pub const RECAP_EXPERIENCE_BUTTON_WRAPPER_TEST_ID: &str = "recap-experience-button-wrapper";
pub const RECAP_EXPERIENCE_GHOST_BUTTON_TEST_ID: &str = "recap-experience-ghost-button";
pub const RECAP_EXPERIENCE_ACCESSIBILITY_BORDER_TEST_ID: &str =
    "recap-experience-accessibility-border";

pub const RECAP_CONTENT_CURRENT_TEST_ID: &str = "recap-content-current";
pub const RECAP_CONTENT_NEXT_TEST_ID: &str = "recap-content-next";
pub const RECAP_CONTENT_TEXT_TEST_ID: &str = "recap-content-text-test-id";
pub const RECAP_CONTENT_TITLE_TEST_ID: &str = "recap-content-title-test-id";

#[Composer]
pub fn Content(
    ctx: &AppContext,
    #[into] text: Signal<String>,
    title: TextContent,
) -> impl VisualComposable<'static> {
    // TODO: Render like/dislike feedback buttons (state is persistent across all segments)

    compose! {
        Column() {
            Typography(
                content: title,
                type_ramp: TypeRamp::from(FableText::TYPE_LABEL500_EMPHASIS)
            )
            .test_id(RECAP_CONTENT_TITLE_TEST_ID)
            Typography(
                content: text,
                type_ramp: TypeRamp::from(FableText::TYPE_BODY400)
            )
            .test_id(RECAP_CONTENT_TEXT_TEST_ID)
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING075))
    }
}

#[Composer]
pub fn RecapButton(
    ctx: &AppContext,
    play_action: Option<RecapAction>,
    app_events_reporter: Rc<GenAIRecapAppEventReporter>,
    #[optional = false] ghost_version: bool,
    #[optional = false] tts_enabled: bool,
) -> impl VisualComposable<'static> {
    let on_play_button_select = Rc::new({
        let app_events_reporter = Rc::clone(&app_events_reporter);
        move || {
            app_events_reporter.on_viewing_play_button_pressed();
        }
    });

    let button_test_id = if !ghost_version {
        RECAP_EXPERIENCE_BUTTON_TEST_ID
    } else {
        RECAP_EXPERIENCE_GHOST_BUTTON_TEST_ID
    };

    let button_builder = Box::new(move |ctx: &AppContext| {
        if let Some(play_button_data) = &play_action {
            match play_button_data {
                RecapAction::Play(data) => {
                    let on_select = {
                        let data_on_select = Rc::clone(&data.on_select);
                        let on_play_button_select = Rc::clone(&on_play_button_select);
                        move || {
                            if !ghost_version {
                                on_play_button_select();
                                data_on_select();
                            }
                        }
                    };

                    compose_option! {
                        Column() {
                            PlayButton(
                                content: data.title.clone(),
                                height: data.height.clone(),
                                variant: ProgressBarVariant::VOD,
                                width: None,
                                progress: data.progress
                            )
                            .test_id(button_test_id)
                            .on_select(on_select)
                            .accessibility_highlight_disabled(true)
                        }
                    }
                }
                RecapAction::Primary(data) => {
                    let on_select = {
                        let data_on_select = Rc::clone(&data.on_select);
                        let on_play_button_select = Rc::clone(&on_play_button_select);
                        move || {
                            if !ghost_version {
                                on_play_button_select();
                                data_on_select();
                            }
                        }
                    };

                    compose_option! {
                        Column(){
                            PrimaryButton(variant: data.variant.clone())
                            .test_id(button_test_id)
                            .on_select(on_select)
                            .accessibility_highlight_disabled(true)
                        }
                    }
                }
            }
        } else {
            None
        }
    });

    if tts_enabled {
        // This is a temporary solution to avoid the accessibility border flickering when moving between items.
        // It is caused by the movement from the ghost button to the real button. We will remove this when the SDK
        // work is done to allow manual TTS speech.
        compose! {
            Column() {
                Stack() {
                    Memo(item_builder: button_builder)
                }
                .test_id(RECAP_EXPERIENCE_ACCESSIBILITY_BORDER_TEST_ID)
                .border_color(Color::accessibility_green())
                .border_radius(24.0)
                .border_width(12.0)
                .padding(Padding::all(12.0))
                .translate_x(-12.0)
                .translate_y(-12.0)
            }
        }
    } else {
        compose! {
            Column() {
                Memo(item_builder: button_builder)
            }
        }
    }
}

#[Composer]
pub fn RecapExperience(
    ctx: &AppContext,
    show_title: &String,
    data: RecapExperienceModel,
    #[optional = std::rc::Rc::new(|| {})] on_closed: Rc<dyn Fn()>,
) -> impl VisualComposable<'static> {
    // Combination of signals to toggle the resetting of focus to the recap button and cause TTS messages to be spoken
    let ghost_focusable_enabled = create_rw_signal(ctx.scope(), false);
    let ghost_focusable_focused = create_focus_signal(ctx.scope());

    let num_items = data.segments.len();

    let first_item = if let Some(first_item) = data.segments.first() {
        first_item.clone()
    } else {
        log::error!("RecapExperience: No items provided");
        return compose! { Stack() {} };
    };

    let focused_index = create_rw_signal(ctx.scope(), 0);
    let default_background_image = data.default_background_image.clone();

    let app_events_reporter: Rc<GenAIRecapAppEventReporter> =
        Rc::new(use_recap_event_reporter(ctx.scope()));
    let on_closed = Rc::new({
        let app_events_reporter = Rc::clone(&app_events_reporter);
        move || {
            app_events_reporter.on_viewing_exit();
            on_closed();
        }
    });
    let on_close_backspace = on_closed.clone();
    let on_close_escape = on_closed.clone();

    let get_background_data = Rc::new(move |segment: Option<&RecapSegment>| {
        let bg = if let Some(background_image) =
            segment.and_then(|segment| segment.background_image.as_ref())
        {
            Some(background_image)
        } else if let Some(background_image) = &default_background_image {
            Some(background_image)
        } else {
            None
        };

        let image_url = bg.map_or_else(|| "".to_string(), |bg| bg.url.clone());

        RecapBackgroundData {
            id: image_url.clone(),
            image_url,
            enter_immediately: true,
            width: bg.and_then(|bg| bg.width),
            height: bg.and_then(|bg| bg.width),
        }
    });

    let first_item_text = first_item.text.clone().unwrap_or_default();
    let background_data: RwSignal<RecapBackgroundData> =
        create_rw_signal(ctx.scope(), get_background_data(Some(&first_item)));
    let text = create_rw_signal(ctx.scope(), first_item_text.clone());
    let next_text = create_rw_signal(ctx.scope(), first_item_text);
    let text_opacity = create_rw_signal(ctx.scope(), 1.0);
    let next_text_opacity = create_rw_signal(ctx.scope(), 0.0);

    let on_move_to_page = Rc::new({
        let ctx = ctx.clone();
        let get_background_data = Rc::clone(&get_background_data);
        let recap_type = data.recap_type.clone();

        move |delta: i32| {
            let current_index = focused_index.get_untracked();
            let new_index = (current_index as i32 + delta).clamp(0, num_items as i32 - 1) as usize;

            if new_index == current_index {
                return false; // Not paginated
            }

            focused_index.set(new_index);
            metric!(EXPERIENCE_PAGINATED_METRIC, 1);

            let new_segment = data.segments.get(new_index);
            let background = get_background_data(new_segment);

            if background.image_url.is_empty() {
                metric!(EXPERIENCE_MISSING_BACKGROUND_METRIC, 1);
                log::warn!(
                    "[GenAIRecap] Missing background for recap type: {}",
                    recap_type
                );
            }

            background_data.set(background);
            text.set(next_text.get_untracked());
            text_opacity.try_set(1.0);
            next_text_opacity.try_set(0.0);
            next_text.set(
                new_segment
                    .and_then(|new_segment| new_segment.text.clone())
                    .unwrap_or_default(),
            );
            ctx.with_animation(
                Animation::default().with_duration(FableMotionDuration::Medium.to_duration()),
                move || {
                    text_opacity.try_set(0.0);
                    next_text_opacity.try_set(1.0);
                },
            );

            // Trigger Memo to update and render dummy button
            ghost_focusable_enabled.set(true);
            // Trigger focus moving to the dummy button
            ghost_focusable_focused.set(true);
            // Trigger Memo to unmount the dummy button, focus returns to original
            ghost_focusable_enabled.set(false);

            true // Paginated
        }
    });

    let on_move_to_next_page = Rc::new({
        let on_move_to_page = Rc::clone(&on_move_to_page);
        let app_events_reporter = Rc::clone(&app_events_reporter);
        move || {
            if on_move_to_page(1) {
                app_events_reporter.on_viewing_paginate(RecapPaginationDirection::Next);
            }
        }
    });

    let on_move_to_prev_page = Rc::new({
        let on_move_to_page = Rc::clone(&on_move_to_page);
        let app_events_reporter = Rc::clone(&app_events_reporter);
        move || {
            if on_move_to_page(-1) {
                app_events_reporter.on_viewing_paginate(RecapPaginationDirection::Prev);
            }
        }
    });

    let on_left_click = {
        let on_move_to_prev_page = Rc::clone(&on_move_to_prev_page);
        move || {
            on_move_to_prev_page();
        }
    };

    let on_right_click = {
        let on_move_to_next_page = Rc::clone(&on_move_to_next_page);
        move || {
            on_move_to_next_page();
        }
    };

    let pointer_control_active = is_pointer_control_active(ctx.scope);
    let pointer_controls_visible = Signal::derive(ctx.scope, move || pointer_control_active.get());

    let pointer_control_on_left = Rc::new(on_left_click.clone());
    let pointer_control_on_right = Rc::new(on_right_click.clone());

    let title = create_title_text_content(show_title, &data.title);
    let title_closing_message = create_title_text_content(show_title, &data.title);

    let context_messages = Signal::derive(ctx.scope(), move || {
        vec![
            title_closing_message.clone(),
            TextContent::String(next_text.get()),
        ]
    });

    let closing_context_messages = Signal::derive(ctx.scope(), move || {
        get_closing_context_messages(focused_index.into(), num_items)
    });

    let pagination_dots_builder = Box::new(move |ctx: &AppContext| {
        let pointer_control_on_left = pointer_control_on_left.clone();
        let pointer_control_on_right = pointer_control_on_right.clone();

        if num_items > 1 {
            compose_option! {
                Stack() {
                    PaginationDots(
                        num_items,
                        focused_index,
                        pointer_controls_visible,
                        pointer_control_on_left,
                        pointer_control_on_right,
                        enable_background:true
                    )
                }
                .width(SCREEN_WIDTH - 192.0)
                .alignment(Alignment::Center)
            }
        } else {
            None
        }
    });

    let tts_enabled = use_context::<TTSEnabledContext>(ctx.scope())
        .map(|tts| tts.0)
        .is_some_and(|context| context.get_untracked());

    compose! {
        Stack() {
            Stack() {
                RecapBackground(
                    data: background_data.read_only(),
                )
            }

            Column() {
                Stack() {
                    Content(text, title: title.clone())
                        .opacity(text_opacity)
                        .test_id(RECAP_CONTENT_CURRENT_TEST_ID)

                    Content(text: next_text, title)
                        .opacity(next_text_opacity)
                        .test_id(RECAP_CONTENT_NEXT_TEST_ID)
                }
                .alignment(Alignment::StartBottom)
                .padding(Padding::vertical(36.0))

                Stack() {
                    RecapButton(
                        play_action: data.play_action.clone(),
                        app_events_reporter: Rc::clone(&app_events_reporter),
                        tts_enabled
                    )
                    .test_id(RECAP_EXPERIENCE_BUTTON_WRAPPER_TEST_ID)
                    .accessibility_context_messages(context_messages)
                    .accessibility_closing_context_messages(closing_context_messages)
                    // Using a dummy version of the recap button to reset focus back to the original version
                    // This is purely for tts purposes and allows for the context messages to be spoken when scrolling
                    // This component is mounted/unmouted when a move occurrs
                    Memo(item_builder: Box::new(move |ctx| {
                        ghost_focusable_enabled.get().then(|| {
                            compose!{
                                RecapButton(
                                    play_action: data.play_action.clone(),
                                    ghost_version: true,
                                    app_events_reporter: Rc::clone(&app_events_reporter)
                                )
                                .focused(ghost_focusable_focused)
                            }
                        })
                    }))
                    Memo(item_builder: pagination_dots_builder)
                }
                .alignment(Alignment::StartCenter)
            }
            .main_axis_alignment(MainAxisAlignment::End)
            .padding(Padding::new(96.0, 96.0, 0.0, 96.0))
            .size(Vec2::new(SCREEN_WIDTH, SCREEN_HEIGHT))
        }
        .on_key_down(KeyCode::Left, on_left_click)
        .on_key_down(KeyCode::Right, on_right_click)
        .on_key_down(KeyCode::Escape, move || on_close_escape())
        .on_key_down(KeyCode::Backspace, move || on_close_backspace())
        .size(Vec2::new(SCREEN_WIDTH, SCREEN_HEIGHT))
        .test_id(RECAP_EXPERIENCE_TEST_ID)
    }
}

// TODO: Localization https://issues.amazon.com/issues/670d9254-46e2-4881-b619-9bb113f8fd47
fn get_closing_context_messages(
    focused_index: Signal<usize>,
    num_items: usize,
) -> Vec<TextContent> {
    let mut messages: Vec<TextContent> = vec![TextContent::String(format!(
        "Page {} of {}",
        focused_index.get() + 1,
        num_items
    ))];
    if focused_index.get() == 0 {
        messages.push(TextContent::String(
            "Move right for next recap page. Press back to exit.".to_string(),
        ))
    } else if focused_index.get() == num_items - 1 {
        messages.push(TextContent::String(
            "Move left for previous recap page. Press back to exit.".to_string(),
        ))
    } else {
        messages.push(TextContent::String(
            "Move left or right to navigate between recap pages. Press back to exit.".to_string(),
        ))
    };

    messages
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data::create_recap_experience_mock_data;
    use crate::types::{PlayVariant, PrimaryVariant};
    use amzn_fable_tokens::FableIcon;
    use fableous::pointer_control::POINTER_CONTROL_ICON_TEST_ID;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::node_properties::{NodeQuery, SceneNodeTree};

    use crate::app_events::MockGenAIRecapAppEventReporter;
    use ignx_compositron::test_utils::node_properties::NodeTypeProperties;
    use ignx_compositron::test_utils::{
        assert_node_does_not_exist, assert_node_exists, ComposableType, TestRendererGameLoop,
    };
    use ignx_compositron::text::TextContent;
    use mockall::Sequence;
    use rstest::rstest;
    use rust_features::*;
    use std::cell::RefCell;

    fn create_example_play_button_play_action() -> Option<RecapAction> {
        Some(RecapAction::Play(PlayVariant {
            title: String::from("Resume").into(),
            height: PlayButtonHeight::Size400,
            progress: 0.5,
            on_select: Rc::new(|| {}),
            variant: ProgressBarVariant::VOD,
        }))
    }

    fn create_example_primary_button_play_action() -> Option<RecapAction> {
        Some(RecapAction::Primary(PrimaryVariant {
            variant: ignx_compositron::reactive::MaybeSignal::Static(
                PrimaryButtonVariant::IconAndTextSize800(
                    FableIcon::PLAY.to_string(),
                    TextContent::String("Watch Episode 1".to_string()),
                ),
            ),
            on_select: Rc::new(|| {}),
        }))
    }

    pub fn assert_image_url_contains(scene_node_tree: &SceneNodeTree, expected: &str) {
        let image = scene_node_tree
            .find_by_props()
            .composable_type(ComposableType::Image)
            .find_first();
        match image.get_props().node_type_props {
            NodeTypeProperties::Image(source) => {
                let uri = source.uri.unwrap();
                assert!(uri.contains(expected));
            }
            _ => {
                panic!("expanded_image does not have props of the type NodeTypeProperties::Image");
            }
        }
    }

    pub fn press_key(
        test_game_loop: &mut TestRendererGameLoop,
        scene_node_tree: &SceneNodeTree,
        key: KeyCode,
    ) -> SceneNodeTree {
        let btn = scene_node_tree
            .find_by_test_id(RECAP_EXPERIENCE_BUTTON_TEST_ID)
            .borrow_props()
            .node_id;

        test_game_loop.send_key_down_up_event_to_node(btn, key);
        test_game_loop.tick_until_done()
    }

    fn assert_text_of_node(button: &'_ NodeQuery<'_>, expected_line: &'_ str) {
        let button_text_lines = button
            .find_any_child_with()
            .composable_type(ComposableType::RichTextLabel)
            .find_all();
        let line_of_text_button = button_text_lines.first().unwrap();
        let line_of_text_props = line_of_text_button.borrow_props();
        assert_eq!(
            line_of_text_props.text.clone().unwrap(),
            expected_line.to_string()
        );
    }

    pub fn move_to_previous_segment(
        test_game_loop: &mut TestRendererGameLoop,
        scene_node_tree: &SceneNodeTree,
    ) -> SceneNodeTree {
        press_key(test_game_loop, scene_node_tree, KeyCode::Left)
    }

    pub fn move_to_next_segment(
        test_game_loop: &mut TestRendererGameLoop,
        scene_node_tree: &SceneNodeTree,
    ) -> SceneNodeTree {
        press_key(test_game_loop, scene_node_tree, KeyCode::Right)
    }

    pub fn assert_text(node_tree: &SceneNodeTree, expected_text: &str, has_transitioned: bool) {
        let content = node_tree.find_by_test_id(if has_transitioned {
            RECAP_CONTENT_NEXT_TEST_ID
        } else {
            RECAP_CONTENT_CURRENT_TEST_ID
        });
        let text = content
            .find_any_child_with()
            .test_id(RECAP_CONTENT_TEXT_TEST_ID)
            .find_first();

        assert_eq!(content.borrow_props().base_styles.opacity, Some(1.0));
        assert_eq!(*text.borrow_props().text.as_ref().unwrap(), expected_text);
    }

    #[test]
    fn should_render_correctly() {
        launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                let recap_action = create_example_play_button_play_action();
                let data = create_recap_experience_mock_data(recap_action);
                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                assert_image_url_contains(&node_tree, "fallout_0");

                let title = node_tree.find_by_test_id(RECAP_CONTENT_TITLE_TEST_ID);
                assert_eq!(
                    *title.borrow_props().text.as_ref().unwrap(),
                    "Fallout, Season 2, Episodes 1 - 3".to_string()
                );
                let play_action = node_tree.find_by_test_id(RECAP_EXPERIENCE_BUTTON_TEST_ID);
                assert_text_of_node(&play_action, "Resume");

                let pagination_dots = node_tree.find_by_test_id(PAGINATION_DOTS_CONTAINER_TEST_ID);
                assert_node_exists!(pagination_dots);
            },
        )
    }

    #[rstest]
    #[case(create_example_play_button_play_action(), "Resume".to_string())]
    #[case(create_example_primary_button_play_action(), "Watch Episode 1".to_string())]
    fn should_render_play_action_correctly(
        #[case] recap_action: Option<RecapAction>,
        #[case] expected_button_text: String,
    ) {
        launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                let data = create_recap_experience_mock_data(recap_action);
                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let play_action = node_tree.find_by_test_id(RECAP_EXPERIENCE_BUTTON_TEST_ID);
                assert_text_of_node(&play_action, &expected_button_text);
            },
        )
    }

    #[rstest]
    #[should_panic = "No node found: NodeNotFound { query: \"node.test_id.is_some_and(|tid| tid == \\\"recap-experience-button\\\")\" }"]
    fn should_not_render_action_if_data_not_present() {
        launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                let data = create_recap_experience_mock_data(None);
                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let play_button = node_tree.find_by_test_id(RECAP_EXPERIENCE_BUTTON_TEST_ID);
                play_button.get_props();
            },
        )
    }

    #[test]
    fn should_not_render_pagination_dots_if_only_1_item() {
        launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                let mut data = create_recap_experience_mock_data(None);
                data.segments = vec![data.segments[0].clone()];

                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let pagination_dots = node_tree.find_by_test_id(PAGINATION_DOTS_CONTAINER_TEST_ID);

                assert_node_does_not_exist!(pagination_dots);
            },
        )
    }

    #[test]
    fn should_render_default_background_image_if_segment_image_not_provided() {
        launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());
                let recap_action = create_example_play_button_play_action();
                let mut data = create_recap_experience_mock_data(recap_action);

                data.segments.get_mut(0).unwrap().background_image = None;
                data.segments.get_mut(2).unwrap().background_image = None;
                data.segments.get_mut(4).unwrap().background_image = None;

                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                assert_image_url_contains(&node_tree, "oppenheimer");

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                assert_image_url_contains(&node_tree, "fallout_1");

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                assert_image_url_contains(&node_tree, "oppenheimer");

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                assert_image_url_contains(&node_tree, "fallout_0_cropped");

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                assert_image_url_contains(&node_tree, "oppenheimer");
            },
        )
    }

    #[test]
    fn switching_segment_correctly_renders_new_segment() {
        launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());

                let data =
                    create_recap_experience_mock_data(create_example_play_button_play_action());

                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                assert_image_url_contains(&node_tree, "fallout_0");
                assert_text(
                    &node_tree,
                    "The debut episode begins with a prologue showcasing the moments leading up to a nuclear apocalypse. A cowboy named Cooper Howard tries to flee the disaster with his son, but their fate remains uncertain as multiple nuclear bombs are detonated.\n\nThe story then jumps 200 years into the future, introducing Lucy Maclean, a resident of Vault 33. The debut episode begins with a prologue showcasing the moments leading up to a nuclear apocalypse. A cowboy named Cooper Howard tries to flee the disaster with his son, but their fate remains uncertain as multiple nuclear bombs are detonated.",
                    false,
                );

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);

                assert_image_url_contains(&node_tree, "fallout_1");
                assert_text(
                    &node_tree,
                    "Lucy continues her perilous journey, meeting Dr. Wilzig, who cautions her about the surface's dangers but cannot dissuade her from her quest. Maximus and Titus encounter a yao guai in a cave, leading to a brutal fight where Maximus lets Titus suffer before taking his armor and abandoning him.",
                    true,
                );

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);

                assert_image_url_contains(&node_tree, "fallout_2");
                assert_text(
                    &node_tree,
                    "Lucy navigates the treacherous wasteland, confronting various threats while grappling with the harsh realities of life outside the vault. Maximus, now questioning his mission, moves forward with a growing sense of doubt about the Brotherhood's true intentions. Dr. Wilzig and his dog continue evading capture, seeking to understand and neutralize the potentially catastrophic object they possess.",
                    true,
                );

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);

                assert_image_url_contains(&node_tree, "fallout_0_cropped");
                assert_text(&node_tree, "Short description", true);

                // reach end and click again to ensure it stops.
                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);

                assert_image_url_contains(&node_tree, "fallout_1_cropped");
                assert_text(&node_tree, "Final string", true);

                let node_tree = move_to_previous_segment(&mut test_game_loop, &node_tree);

                assert_image_url_contains(&node_tree, "fallout_0_cropped");
                assert_text(&node_tree, "Short description", true);
            },
        )
    }

    #[test]
    fn assert_pointer_control_arrows_shown_when_pointer_controls_enabled() {
        launch_test(
            |ctx| {
                MockRustFeaturesBuilder::new()
                    .build_as_mock_and_real_into_context(true, ctx.scope());

                let data =
                    create_recap_experience_mock_data(create_example_play_button_play_action());
                let show_title = "mock_title".to_string();

                compose! {
                    RecapExperience(data, show_title: &show_title)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let left_arrow = node_tree
                    .find_by_props()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_by_index(0);
                let right_arrow = node_tree
                    .find_by_props()
                    .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    .find_by_index(1);

                assert_node_exists!(left_arrow);
                assert_node_exists!(right_arrow);
            },
        )
    }

    #[test]
    fn assert_correct_accessibility_messages_assigned() {
        launch_test(
            |ctx| {
                provide_context_test_rust_features(ctx.scope());

                let data =
                    create_recap_experience_mock_data(create_example_play_button_play_action());

                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let recap_button_wrapper = node_tree
                    .find_by_props()
                    .test_id(RECAP_EXPERIENCE_BUTTON_WRAPPER_TEST_ID)
                    .find_first();

                let accessibility = recap_button_wrapper
                    .borrow_props()
                    .accessibility
                    .as_ref()
                    .unwrap();

                assert_eq!(
                    accessibility.context_messages,
                    vec![
                        ("Fallout, Season 2, Episodes 1 - 3".to_string(), None),
                        ("The debut episode begins with a prologue showcasing the moments leading up to a nuclear apocalypse. A cowboy named Cooper Howard tries to flee the disaster with his son, but their fate remains uncertain as multiple nuclear bombs are detonated.\n\nThe story then jumps 200 years into the future, introducing Lucy Maclean, a resident of Vault 33. The debut episode begins with a prologue showcasing the moments leading up to a nuclear apocalypse. A cowboy named Cooper Howard tries to flee the disaster with his son, but their fate remains uncertain as multiple nuclear bombs are detonated.".to_string(), None)
                    ]
                );
                assert_eq!(
                    accessibility.closing_context_messages,
                    vec![
                        ("Page 1 of 5".to_string(), None),
                        (
                            "Move right for next recap page. Press back to exit.".to_string(),
                            None
                        )
                    ]
                );

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                let recap_button_wrapper = node_tree
                    .find_by_props()
                    .test_id(RECAP_EXPERIENCE_BUTTON_WRAPPER_TEST_ID)
                    .find_first();
                let accessibility = recap_button_wrapper
                    .borrow_props()
                    .accessibility
                    .as_ref()
                    .unwrap();

                assert_eq!(
                    accessibility.context_messages,
                    vec![
                        ("Fallout, Season 2, Episodes 1 - 3".to_string(), None),
                        ("Lucy continues her perilous journey, meeting Dr. Wilzig, who cautions her about the surface's dangers but cannot dissuade her from her quest. Maximus and Titus encounter a yao guai in a cave, leading to a brutal fight where Maximus lets Titus suffer before taking his armor and abandoning him.".to_string(), None)
                    ]
                );
                assert_eq!(
                    accessibility.closing_context_messages,
                    vec![
                        ("Page 2 of 5".to_string(), None),
                        ("Move left or right to navigate between recap pages. Press back to exit.".to_string(), None)
                    ]
                );

                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                let recap_button_wrapper = node_tree
                    .find_by_props()
                    .test_id(RECAP_EXPERIENCE_BUTTON_WRAPPER_TEST_ID)
                    .find_first();
                let accessibility = recap_button_wrapper
                    .borrow_props()
                    .accessibility
                    .as_ref()
                    .unwrap();

                assert_eq!(
                    accessibility.context_messages,
                    vec![
                        ("Fallout, Season 2, Episodes 1 - 3".to_string(), None),
                        ("Final string".to_string(), None)
                    ]
                );
                assert_eq!(
                    accessibility.closing_context_messages,
                    vec![
                        ("Page 5 of 5".to_string(), None),
                        (
                            "Move left for previous recap page. Press back to exit.".to_string(),
                            None
                        )
                    ]
                );
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Backspace)]
    #[case(KeyCode::Escape)]
    pub fn should_close_on_back(#[case] key: KeyCode) {
        launch_test(
            move |ctx| {
                provide_context_test_rust_features(ctx.scope());
                let recap_action = create_example_play_button_play_action();
                let data = create_recap_experience_mock_data(recap_action);
                let on_closed_called = Rc::new(RefCell::new(0));
                provide_context(ctx.scope(), on_closed_called.clone());
                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(
                        data,
                        show_title: &title,
                        on_closed: Rc::new(move || {
                            *on_closed_called.borrow_mut() += 1;
                        })
                )}
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let experience = tree.find_by_test_id(RECAP_EXPERIENCE_TEST_ID);
                let on_closed_called = use_context::<Rc<RefCell<i32>>>(scope).unwrap();

                assert_eq!(*on_closed_called.borrow_mut(), 0);

                test_game_loop
                    .send_key_down_up_event_to_node(experience.borrow_props().node_id, key);
                test_game_loop.tick_until_done();

                assert_eq!(*on_closed_called.borrow_mut(), 1);
            },
        );
    }

    mod app_events {
        use super::*;

        #[test]
        fn on_exit() {
            launch_test(
                move |ctx| {
                    let mut app_event_reporter = MockGenAIRecapAppEventReporter::default();
                    app_event_reporter.expect_clone().return_once_st({
                        move || {
                            let mut clone = MockGenAIRecapAppEventReporter::default();
                            clone.expect_on_viewing_exit().once().return_once_st(|| ());
                            clone
                        }
                    });
                    provide_context(ctx.scope(), app_event_reporter);
                    provide_context_test_rust_features(ctx.scope());
                    let recap_action = create_example_play_button_play_action();
                    let data = create_recap_experience_mock_data(recap_action);
                    let title = "Fallout".to_string();

                    compose! {
                        RecapExperience(data, show_title: &title)
                    }
                },
                move |_scope, mut test_game_loop| {
                    let tree = test_game_loop.tick_until_done();
                    let experience = tree.find_by_test_id(RECAP_EXPERIENCE_TEST_ID);

                    test_game_loop.send_key_down_up_event_to_node(
                        experience.borrow_props().node_id,
                        KeyCode::Backspace,
                    );
                    test_game_loop.tick_until_done();
                },
            );
        }

        #[test]
        fn on_pagination_prev_and_next() {
            launch_test(
                |ctx| {
                    let mut app_event_reporter = MockGenAIRecapAppEventReporter::default();
                    app_event_reporter.expect_clone().return_once_st({
                        move || {
                            let mut seq = Sequence::new();
                            let mut clone = MockGenAIRecapAppEventReporter::default();
                            clone
                                .expect_on_viewing_paginate()
                                .withf(|p| matches!(*p, RecapPaginationDirection::Next))
                                .times(2)
                                .in_sequence(&mut seq)
                                .returning(|_| {});
                            clone
                                .expect_on_viewing_paginate()
                                .withf(|p| matches!(*p, RecapPaginationDirection::Prev))
                                .times(1)
                                .in_sequence(&mut seq)
                                .returning(|_| {});
                            clone
                                .expect_on_viewing_paginate()
                                .withf(|p| matches!(*p, RecapPaginationDirection::Next))
                                .times(1)
                                .in_sequence(&mut seq)
                                .returning(|_| {});
                            clone
                                .expect_on_viewing_paginate()
                                .withf(|p| matches!(*p, RecapPaginationDirection::Prev))
                                .times(2)
                                .in_sequence(&mut seq)
                                .returning(|_| {});
                            clone
                        }
                    });
                    provide_context(ctx.scope(), app_event_reporter);

                    provide_context_test_rust_features(ctx.scope());
                    let recap_action = create_example_play_button_play_action();
                    let data = create_recap_experience_mock_data(recap_action);
                    let title = "Fallout".to_string();

                    compose! {
                        RecapExperience(data, show_title: &title)
                    }
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();
                    let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                    let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                    let node_tree = move_to_previous_segment(&mut test_game_loop, &node_tree);
                    let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                    let node_tree = move_to_previous_segment(&mut test_game_loop, &node_tree);
                    move_to_previous_segment(&mut test_game_loop, &node_tree);
                },
            )
        }

        #[test]
        fn pagination_event_should_not_be_sent_if_not_paginated() {
            launch_test(
                |ctx| {
                    let mut app_event_reporter = MockGenAIRecapAppEventReporter::default();
                    app_event_reporter.expect_clone().return_once_st({
                        move || {
                            let mut seq = Sequence::new();
                            let mut clone = MockGenAIRecapAppEventReporter::default();
                            clone
                                .expect_on_viewing_paginate()
                                .withf(|p| matches!(*p, RecapPaginationDirection::Next))
                                .times(4)
                                .in_sequence(&mut seq)
                                .returning(|_| {});
                            clone
                        }
                    });
                    provide_context(ctx.scope(), app_event_reporter);

                    provide_context_test_rust_features(ctx.scope());
                    let recap_action = create_example_play_button_play_action();
                    let data = create_recap_experience_mock_data(recap_action);
                    let title = "Fallout".to_string();

                    compose! {
                        RecapExperience(data, show_title: &title)
                    }
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    // Should not send pagination event because we are at the first page
                    let node_tree = move_to_previous_segment(&mut test_game_loop, &node_tree);

                    let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                    let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                    let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);
                    let node_tree = move_to_next_segment(&mut test_game_loop, &node_tree);

                    // Should not paginate as well since we are the end
                    move_to_next_segment(&mut test_game_loop, &node_tree);
                },
            )
        }

        #[rstest]
        #[case(create_example_play_button_play_action())]
        #[case(create_example_primary_button_play_action())]
        fn on_play_button_click(#[case] recap_action: Option<RecapAction>) {
            launch_test(
                |ctx| {
                    let mut app_event_reporter = MockGenAIRecapAppEventReporter::default();
                    app_event_reporter.expect_clone().return_once_st({
                        move || {
                            let mut clone = MockGenAIRecapAppEventReporter::default();
                            clone
                                .expect_on_viewing_play_button_pressed()
                                .once()
                                .return_once_st(|| ());
                            clone
                        }
                    });
                    provide_context(ctx.scope(), app_event_reporter);

                    provide_context_test_rust_features(ctx.scope());
                    let data = create_recap_experience_mock_data(recap_action);
                    let title = "Fallout".to_string();

                    compose! {
                        RecapExperience(data, show_title: &title)
                    }
                },
                move |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();
                    let play_action = node_tree.find_by_test_id(RECAP_EXPERIENCE_BUTTON_TEST_ID);
                    test_game_loop.send_on_select_event(play_action.borrow_props().node_id);
                    test_game_loop.tick_until_done();
                },
            )
        }
    }

    #[test]
    pub fn should_draw_accessibility_border_if_tts_is_enabled() {
        launch_test(
            |ctx| {
                let tts_enabled_context = TTSEnabledContext(create_signal(ctx.scope(), true).0);
                provide_context(ctx.scope(), tts_enabled_context);
                provide_context_test_rust_features(ctx.scope());

                let data =
                    create_recap_experience_mock_data(create_example_play_button_play_action());
                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let accessibility_border =
                    node_tree.find_by_test_id(RECAP_EXPERIENCE_ACCESSIBILITY_BORDER_TEST_ID);

                assert_node_exists!(&accessibility_border);
            },
        )
    }

    #[rstest]
    #[case(|scope| Some(TTSEnabledContext(create_signal(scope, false).0)))]
    #[case(|_| None)]
    pub fn should_not_draw_accessibility_border_if_tts_is_enabled(
        #[case] get_tts_context: impl Fn(Scope) -> Option<TTSEnabledContext> + 'static,
    ) {
        launch_test(
            move |ctx| {
                if let Some(tts_context) = get_tts_context(ctx.scope()) {
                    provide_context(ctx.scope(), tts_context);
                }

                provide_context_test_rust_features(ctx.scope());

                let data =
                    create_recap_experience_mock_data(create_example_play_button_play_action());
                let title = "Fallout".to_string();

                compose! {
                    RecapExperience(data, show_title: &title)
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let accessibility_border =
                    node_tree.find_by_test_id(RECAP_EXPERIENCE_ACCESSIBILITY_BORDER_TEST_ID);

                assert_node_does_not_exist!(&accessibility_border);
            },
        )
    }
}
