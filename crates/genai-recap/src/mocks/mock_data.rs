use crate::{
    network::{RecapBackgroundImage, RecapSegment, RecapType},
    types::{
        RecapAction, RecapExperienceModel, RecapExperienceTitle, RecapExperienceTitleThisSeason,
    },
};

pub fn create_recap_experience_mock_data(
    default_recap_action: Option<RecapAction>,
) -> RecapExperienceModel {
    let background_images = [
        RecapBackgroundImage {
            url: "https://m.media-amazon.com/images/S/pv-lite-dev/yegary/fallout_0.png".to_string(),
            height: Some(1920),
            width: Some(1080),
        },
        RecapBackgroundImage {
            url: "https://m.media-amazon.com/images/S/pv-lite-dev/yegary/fallout_1.png".to_string(),
            height: Some(1920),
            width: Some(1080),
        },
        RecapBackgroundImage {
            url: "https://m.media-amazon.com/images/S/pv-lite-dev/yegary/fallout_2.png".to_string(),
            height: Some(1920),
            width: Some(1080),
        },
        RecapBackgroundImage {
            url: "https://m.media-amazon.com/images/S/pv-lite-dev/yegary/fallout_0_cropped.png"
                .to_string(),
            height: Some(1920),
            width: Some(835),
        },
        RecapBackgroundImage {
            url: "https://m.media-amazon.com/images/S/pv-lite-dev/yegary/fallout_1_cropped.png"
                .to_string(),
            height: Some(1242),
            width: Some(1079),
        },
    ];
    let texts = ["The debut episode begins with a prologue showcasing the moments leading up to a nuclear apocalypse. A cowboy named Cooper Howard tries to flee the disaster with his son, but their fate remains uncertain as multiple nuclear bombs are detonated.\n\nThe story then jumps 200 years into the future, introducing Lucy Maclean, a resident of Vault 33. The debut episode begins with a prologue showcasing the moments leading up to a nuclear apocalypse. A cowboy named Cooper Howard tries to flee the disaster with his son, but their fate remains uncertain as multiple nuclear bombs are detonated.",
                     "Lucy continues her perilous journey, meeting Dr. Wilzig, who cautions her about the surface's dangers but cannot dissuade her from her quest. Maximus and Titus encounter a yao guai in a cave, leading to a brutal fight where Maximus lets Titus suffer before taking his armor and abandoning him.",
                     "Lucy navigates the treacherous wasteland, confronting various threats while grappling with the harsh realities of life outside the vault. Maximus, now questioning his mission, moves forward with a growing sense of doubt about the Brotherhood's true intentions. Dr. Wilzig and his dog continue evading capture, seeking to understand and neutralize the potentially catastrophic object they possess.",
                     "Short description",
                     "Final string",
    ];

    let segments: Vec<RecapSegment> = background_images
        .iter()
        .zip(texts)
        .map(|(image, text)| RecapSegment {
            text: Some(text.to_owned()),
            background_image: Some(image.clone()),
        })
        .collect();

    let default_background_image = RecapBackgroundImage {
        url: "https://m.media-amazon.com/images/S/pv-lite-dev/yegary/oppenheimer.png".to_string(),
        height: Some(1920),
        width: Some(1080),
    };

    RecapExperienceModel {
        default_background_image: Some(default_background_image),
        segments,
        title: RecapExperienceTitle::ThisSeason(RecapExperienceTitleThisSeason {
            selected_season: 2,
            selected_episode: 4,
        }),
        play_action: default_recap_action,
        recap_type: RecapType::Season,
    }
}
