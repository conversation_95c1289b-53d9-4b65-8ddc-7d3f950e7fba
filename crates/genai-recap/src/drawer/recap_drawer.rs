use crate::app_events::use_recap_event_reporter;
use crate::network::{Recap, RecapType};
use amzn_fable_tokens::FableSpacing;
use drawer::drawer::*;
use fableous::{badges::gen_ai_badge::*, buttons::primary_button::*, typography::typography::*};
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{context::AppContext, input::KeyCode, prelude::*};
use std::rc::*;

const PADDING: f32 = FableSpacing::SPACING333;
const CONTENT_WIDTH: f32 = 522.0;
const DRAWER_WIDTH: f32 = CONTENT_WIDTH + (PADDING * 2.0);
const BUTTON_WIDTH: f32 = CONTENT_WIDTH;
const BUTTON_SPACING: f32 = 20.0;

fn get_drawer_button_text_content(
    recap_type: &RecapType,
    season_number: Option<i32>,
) -> TextContent {
    match recap_type {
        RecapType::Episode => TextContent::String("Recap episode".to_string()),
        RecapType::Season => {
            if let Some(season_number) = season_number {
                TextContent::String(format!("Recap season {}", season_number))
            } else {
                log::error!("[GenAI-Recap] RecapType::Season no season_number given!");
                TextContent::String("Recap this season".to_string())
            }
        }
        RecapType::PreviousSeason => TextContent::String("Recap previous season".to_string()),
        RecapType::Movie => TextContent::String("Recap movie".to_string()),
    }
}

#[Composer]
pub fn RecapDrawer(
    ctx: &AppContext,
    title: String,
    season_number: Option<i32>,
    recaps: &'SELF [Recap],
    on_recap_selected: Rc<dyn Fn(usize)>,
    on_closed: Rc<dyn Fn()>,
) -> StackComposable {
    let open = create_rw_signal(ctx.scope(), true);
    let app_event_reporter = use_recap_event_reporter(ctx.scope());
    let recaps: Vec<RecapType> = recaps
        .iter()
        .map(|recap| recap.recap_type.clone())
        .collect();

    let on_recap_selected = Rc::new({
        move |recap_type: &RecapType, idx: usize| {
            app_event_reporter.on_drawer_select(recap_type);
            on_recap_selected(idx);
        }
    });

    let button_builder = Rc::new(
        move |ctx: &AppContext, recap_type: &RecapType, idx: usize| {
            let on_recap_selected = on_recap_selected.clone();
            let recap_type = recap_type.clone();
            let button_text_content = get_drawer_button_text_content(&recap_type, season_number);

            compose! {
                Stack() {
                    PrimaryButton(variant: PrimaryButtonVariant::TextSize400(button_text_content))
                        .focusable()
                        .width(BUTTON_WIDTH)
                        .preferred_focus(true)
                        .test_id(format!("recap-button-{}", idx))
                        .on_select(move || on_recap_selected(&recap_type, idx))
                }
                .padding(Padding::new(0.0, 0.0, 0.0, BUTTON_SPACING))
            }
        },
    );

    let item_builder = Box::new(move |ctx: &AppContext| {
        let title = title.clone();
        let recaps = recaps.clone();
        let button_builder = button_builder.clone();

        compose_option! {
            Column() {
                TypographyHeading400(content: title)
                Stack() {
                    GenAIBadge()
                }
                .padding(Padding::new(0.0, 0.0, 12.0, 36.0))
                TypographyBody200(content: "Review what's happened so far (no spoilers).")

                Rectangle().height(36.0)
                ColumnList(items: recaps, item_builder: button_builder)
            }
            .focus_window()
            .on_key_down(KeyCode::Backspace, {
                move || open.set(false)
            })
            .on_key_down(KeyCode::Escape, {
                move || open.set(false)
            })
        }
    });

    compose! {
        Drawer(item_builder, open, on_closed: Some(on_closed), width: DRAWER_WIDTH, padding: Padding::all(PADDING))
            .test_id("recap-drawer")
    }
}

#[cfg(test)]
pub mod test {
    use crate::app_events::MockGenAIRecapAppEventReporter;
    use ignx_compositron::test_utils::ComposableType;
    use ignx_compositron::{app::launch_test, test_utils::assert_node_exists};
    use rstest::*;
    use std::cell::RefCell;

    use super::*;

    fn get_mock_recaps() -> Vec<Recap> {
        vec![
            Recap {
                recap_type: RecapType::Season,
                background_image: None,
                recap_segments: vec![],
                video_recap_action: None,
            },
            Recap {
                recap_type: RecapType::Episode,
                background_image: None,
                recap_segments: vec![],
                video_recap_action: None,
            },
        ]
    }

    #[test]
    pub fn should_render() {
        launch_test(
            move |ctx| {
                let mock_recaps = get_mock_recaps();

                compose! {
                    RecapDrawer(
                        title: "My Title".to_string(),
                        recaps: &mock_recaps,
                        season_number: Some(3),
                        on_closed: Rc::new(move || {}),
                        on_recap_selected: Rc::new(|_| {})
                    )
                }
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id("drawer");

                assert_node_exists!(&drawer);
                assert_eq!(drawer.borrow_props().layout.size.width, DRAWER_WIDTH);

                let title = drawer
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_first();

                assert_node_exists!(&title);
                assert_eq!(title.borrow_props().text, Some("My Title".to_string()));
            },
        );
    }

    #[rstest]
    #[case(RecapType::PreviousSeason, None, "Recap previous season")]
    #[case(RecapType::PreviousSeason, Some(3), "Recap previous season")]
    #[case(RecapType::Season, Some(2), "Recap season 2")]
    #[case(RecapType::Season, None, "Recap this season")]
    #[case(RecapType::Episode, None, "Recap episode")]
    #[case(RecapType::Movie, None, "Recap movie")]
    pub fn should_render_button_strings_correctly(
        #[case] recap_type: RecapType,
        #[case] season_number: Option<i32>,
        #[case] expected_title: &'static str,
    ) {
        launch_test(
            move |ctx| {
                let mock_recaps = vec![Recap {
                    recap_type,
                    background_image: None,
                    recap_segments: vec![],
                    video_recap_action: None,
                }];

                compose! {
                    RecapDrawer(
                        title: "My Title".to_string(),
                        recaps: &mock_recaps,
                        season_number,
                        on_closed: Rc::new(move || {}),
                        on_recap_selected: Rc::new(|_| {})
                    )
                }
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id("drawer");

                let button1 = drawer
                    .find_any_child_with()
                    .test_id("recap-button-0")
                    .find_first();

                assert_node_exists!(&button1);

                let label = button1
                    .find_any_child_with()
                    .text(expected_title)
                    .find_first();
                assert_node_exists!(&label);
            },
        );
    }

    #[rstest]
    #[case(KeyCode::Backspace)]
    #[case(KeyCode::Escape)]
    pub fn should_close_on_back(#[case] key: KeyCode) {
        launch_test(
            move |ctx| {
                let on_closed_called = Rc::new(RefCell::new(0));
                provide_context(ctx.scope(), on_closed_called.clone());
                let mock_recaps = get_mock_recaps();

                compose! { RecapDrawer(
                    title: "My Title".to_string(),
                    recaps: &mock_recaps,
                    season_number: None,
                    on_closed: Rc::new(move || {
                        *on_closed_called.borrow_mut() += 1;
                    }),
                    on_recap_selected: Rc::new(|_| {})
                )}
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id("recap-drawer");
                let button = drawer
                    .find_any_child_with()
                    .test_id("recap-button-0")
                    .find_first();
                let on_closed_called = use_context::<Rc<RefCell<i32>>>(scope).unwrap();

                assert_eq!(*on_closed_called.borrow_mut(), 0);

                test_game_loop.send_key_down_up_event_to_node(button.borrow_props().node_id, key);
                test_game_loop.tick_until_done();

                assert_eq!(*on_closed_called.borrow_mut(), 1);
            },
        );
    }

    #[test]
    pub fn should_select_buttons() {
        launch_test(
            move |ctx| {
                let on_select_called = Rc::new(RefCell::new((0, 0)));
                provide_context(ctx.scope(), on_select_called.clone());
                let mock_recaps = get_mock_recaps();

                compose! { RecapDrawer(
                    title: "My Title".to_string(),
                    recaps: &mock_recaps,
                    season_number: Some(3),
                    on_closed: Rc::new(move || {}),
                    on_recap_selected: Rc::new(move |idx| {
                        let called = on_select_called.borrow().0 + 1;
                        on_select_called.replace((called, idx));
                    })
                )}
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id("recap-drawer");

                let button1 = drawer
                    .find_any_child_with()
                    .test_id("recap-button-0")
                    .find_first();

                let button1_label = button1
                    .find_any_child_with()
                    .text("Recap season 3")
                    .find_first();
                assert_node_exists!(button1_label);

                let on_select_called = use_context::<Rc<RefCell<(i32, usize)>>>(scope).unwrap();

                assert_eq!(*on_select_called.borrow_mut(), (0, 0));

                test_game_loop.send_on_select_event(button1.borrow_props().node_id);
                test_game_loop.tick_until_done();

                assert_eq!(*on_select_called.borrow_mut(), (1, 0));

                let button2 = drawer
                    .find_any_child_with()
                    .test_id("recap-button-1")
                    .find_first();

                let button2_label = button2
                    .find_any_child_with()
                    .text("Recap episode")
                    .find_first();
                assert_node_exists!(button2_label);

                test_game_loop.send_on_select_event(button2.borrow_props().node_id);
                test_game_loop.tick_until_done();

                assert_eq!(*on_select_called.borrow_mut(), (2, 1));
            },
        );
    }

    mod app_events {
        use super::*;

        #[rstest]
        #[case(RecapType::Episode)]
        #[case(RecapType::Season)]
        #[case(RecapType::PreviousSeason)]
        #[case(RecapType::Movie)]
        pub fn on_drawer_select(#[case] recap_type: RecapType) {
            launch_test(
                move |ctx| {
                    let mut app_event_reporter = MockGenAIRecapAppEventReporter::default();
                    app_event_reporter.expect_clone().return_once_st({
                        let recap_type = recap_type.clone();
                        move || {
                            let mut clone = MockGenAIRecapAppEventReporter::default();
                            clone
                                .expect_on_drawer_select()
                                .withf(move |t| *t == recap_type)
                                .once()
                                .returning(|_| ());
                            clone
                        }
                    });
                    provide_context(ctx.scope(), app_event_reporter);

                    let mock_recaps = vec![Recap {
                        recap_type,
                        background_image: None,
                        recap_segments: vec![],
                        video_recap_action: None,
                    }];
                    compose! {
                        RecapDrawer(
                        title: "My Title".to_string(),
                        recaps: &mock_recaps,
                        season_number: None,
                        on_closed: Rc::new(move || {}),
                        on_recap_selected: Rc::new(|_| {})
                    )}
                },
                move |_scope, mut test_game_loop| {
                    let tree = test_game_loop.tick_until_done();

                    let season_button = tree.find_by_test_id("recap-button-0");
                    test_game_loop.send_on_select_event(season_button.borrow_props().node_id);
                    test_game_loop.tick_until_done();
                },
            );
        }
    }
}
