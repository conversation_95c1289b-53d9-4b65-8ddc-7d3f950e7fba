use crate::network::RecapType;
use ignx_compositron::prelude::*;

#[double]
use cross_app_events::app_event::AppEventReporter;
use cross_app_events::create_serde_map;
use mockall::mock;
use mockall_double::double;
use serde_json::Value;

pub const GEN_AI_RECAP: &str = "GEN_AI_RECAP";

pub const GEN_AI_RECAP_DRAWER_OPTION_SELECT: &str = "GEN_AI_RECAP_DRAWER_OPTION_SELECT";
pub const GEN_AI_RECAP_VIEWING_EXIT: &str = "GEN_AI_RECAP_VIEWING_EXIT";
pub const GEN_AI_RECAP_VIEWING_PAGINATE: &str = "GEN_AI_RECAP_VIEWING_PAGINATE";
pub const GEN_AI_RECAP_VIEWING_PLAY_BUTTON_PRESSED: &str = "GEN_AI_RECAP_VIEWING_PLAY";

#[derive(Clone)]
pub(crate) struct GenAIRecapAppEventReporter {
    app_event_reporter: AppEventReporter,
}

#[derive(Clone)]
pub(crate) enum RecapPaginationDirection {
    Prev,
    Next,
}

/// This is a lightweight wrapper
impl GenAIRecapAppEventReporter {
    pub fn new(scope: Scope) -> Self {
        Self {
            app_event_reporter: AppEventReporter::new(scope),
        }
    }

    pub fn on_drawer_select(&self, recap_type: &RecapType) {
        let option = match recap_type {
            RecapType::Episode => "EPISODE",
            RecapType::PreviousSeason => "PREVIOUS_SEASON",
            RecapType::Season => "THIS_SEASON",
            RecapType::Movie => "MOVIE",
        };

        self.app_event_reporter.send_app_event(
            GEN_AI_RECAP_DRAWER_OPTION_SELECT,
            GEN_AI_RECAP,
            Some(create_serde_map([(
                "option",
                Value::String(option.to_string()),
            )])),
        );
    }

    pub fn on_viewing_exit(&self) {
        self.app_event_reporter
            .send_app_event(GEN_AI_RECAP_VIEWING_EXIT, GEN_AI_RECAP, None);
    }

    pub fn on_viewing_paginate(&self, direction: RecapPaginationDirection) {
        let direction = match direction {
            RecapPaginationDirection::Prev => "prev",
            RecapPaginationDirection::Next => "next",
        };
        self.app_event_reporter.send_app_event(
            GEN_AI_RECAP_VIEWING_PAGINATE,
            GEN_AI_RECAP,
            Some(create_serde_map([(
                "direction",
                Value::String(direction.to_string()),
            )])),
        );
    }

    pub fn on_viewing_play_button_pressed(&self) {
        self.app_event_reporter.send_app_event(
            GEN_AI_RECAP_VIEWING_PLAY_BUTTON_PRESSED,
            GEN_AI_RECAP,
            None,
        );
    }
}

mock! {
    pub GenAIRecapAppEventReporter {
        pub fn new(scope: Scope) -> Self;
        pub fn on_drawer_select(&self, recap_type: &RecapType);
        pub fn on_viewing_exit(&self);
        pub fn on_viewing_paginate(&self, direction: RecapPaginationDirection);
        pub fn on_viewing_play_button_pressed(&self);
    }

    impl Clone for GenAIRecapAppEventReporter {
        fn clone(&self) -> Self;
    }
}

#[cfg(not(test))]
type Reporter = GenAIRecapAppEventReporter;
#[cfg(test)]
type Reporter = MockGenAIRecapAppEventReporter;

pub(crate) fn use_recap_event_reporter(scope: Scope) -> Reporter {
    #[cfg(not(test))]
    {
        GenAIRecapAppEventReporter::new(scope)
    }
    #[cfg(test)]
    {
        use_context::<MockGenAIRecapAppEventReporter>(scope).unwrap_or_else(|| {
            let mut mock = MockGenAIRecapAppEventReporter::default();
            mock.expect_clone()
                .returning(MockGenAIRecapAppEventReporter::default);
            mock.expect_on_viewing_paginate().returning(|_| ());
            mock.expect_on_viewing_exit().returning(|| ());
            mock.expect_on_drawer_select().returning(|_| ());
            mock.expect_on_viewing_play_button_pressed()
                .returning(|| ());
            mock
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::app_events::GenAIRecapAppEventReporter;
    use crate::network::RecapType;
    use cross_app_events::app_event::MockAppEventReporter;
    use network_parser::prelude::Value;
    use rstest::*;

    #[rstest]
    #[case(RecapType::Episode, "EPISODE")]
    #[case(RecapType::PreviousSeason, "PREVIOUS_SEASON")]
    #[case(RecapType::Season, "THIS_SEASON")]
    #[case(RecapType::Movie, "MOVIE")]
    fn test_on_drawer_select(#[case] recap_type: RecapType, #[case] expected_option: &'static str) {
        let mut mocked_reporter = MockAppEventReporter::default();
        mocked_reporter
            .expect_send_app_event()
            .withf(move |name, source, params| {
                assert_eq!(name, GEN_AI_RECAP_DRAWER_OPTION_SELECT);
                assert_eq!(source, GEN_AI_RECAP);
                let params = params.as_ref().unwrap();
                assert_eq!(params.len(), 1);
                assert_eq!(params["option"], Value::String(expected_option.into()));
                true
            })
            .once()
            .return_const(());

        let genai_reporter = GenAIRecapAppEventReporter {
            app_event_reporter: mocked_reporter,
        };
        genai_reporter.on_drawer_select(&recap_type);
    }

    #[test]
    fn test_viewing_exit() {
        let mut mocked_reporter = MockAppEventReporter::default();
        mocked_reporter
            .expect_send_app_event()
            .withf(|name, source, params| {
                assert_eq!(name, GEN_AI_RECAP_VIEWING_EXIT);
                assert_eq!(source, GEN_AI_RECAP);
                assert!(params.is_none());
                true
            })
            .once()
            .return_const(());

        let genai_reporter = GenAIRecapAppEventReporter {
            app_event_reporter: mocked_reporter,
        };
        genai_reporter.on_viewing_exit();
    }

    #[rstest]
    #[case(RecapPaginationDirection::Next, "next")]
    #[case(RecapPaginationDirection::Prev, "prev")]
    fn test_viewing_paginate(
        #[case] direction: RecapPaginationDirection,
        #[case] expected_direction_string: &'static str,
    ) {
        let mut mocked_reporter = MockAppEventReporter::default();
        mocked_reporter
            .expect_send_app_event()
            .withf(move |name, source, params| {
                assert_eq!(name, GEN_AI_RECAP_VIEWING_PAGINATE);
                assert_eq!(source, GEN_AI_RECAP);
                let params = params.as_ref().unwrap();
                assert_eq!(params.len(), 1);
                assert_eq!(
                    params["direction"],
                    Value::String(expected_direction_string.into())
                );
                true
            })
            .once()
            .return_const(());

        let genai_reporter = GenAIRecapAppEventReporter {
            app_event_reporter: mocked_reporter,
        };
        genai_reporter.on_viewing_paginate(direction);
    }

    #[test]
    fn test_viewing_resume_pressed() {
        let mut mocked_reporter = MockAppEventReporter::default();
        mocked_reporter
            .expect_send_app_event()
            .withf(|name, source, params| {
                assert_eq!(name, GEN_AI_RECAP_VIEWING_PLAY_BUTTON_PRESSED);
                assert_eq!(source, GEN_AI_RECAP);
                assert!(params.is_none());
                true
            })
            .once()
            .return_const(());

        let genai_reporter = GenAIRecapAppEventReporter {
            app_event_reporter: mocked_reporter,
        };
        genai_reporter.on_viewing_play_button_pressed();
    }
}
