use ignx_compositron::prelude::metric;

use crate::network::{Recap, RecapType};

pub const REQUESTED_METRIC: &str = "GenAIRecap.Requested";
pub const AVAILABLE_METRIC: &str = "GenAIRecap.Available";
pub const AVAILABLE_TYPE_METRIC: &str = "GenAIRecap.Available.Type";
pub const DRAWER_OPENED_METRIC: &str = "GenAIRecap.Drawer.Opened";
pub const DRAWER_CLOSED_METRIC: &str = "GenAIRecap.Drawer.Closed";
pub const EXPERIENCE_OPENED_METRIC: &str = "GenAIRecap.Experience.Opened";
pub const EXPERIENCE_OPENED_TYPE_METRIC: &str = "GenAIRecap.Experience.Opened.{}";
pub const EXPERIENCE_CLOSED_METRIC: &str = "GenAIRecap.Experience.Closed";
pub const EXPERIENCE_PAGINATED_METRIC: &str = "GenAIRecap.Experience.Paginated";
pub const EXPERIENCE_MISSING_BACKGROUND_METRIC: &str = "GenAIRecap.Experience.MissingBackground";

pub fn report_recap_availability(recaps: &[Recap]) {
    if !recaps.is_empty() {
        metric!(AVAILABLE_METRIC, 1);
    }

    recaps.iter().for_each(|recap| {
        metric!(AVAILABLE_TYPE_METRIC, 1, "recapType" => recap.recap_type.to_string());

        if recap.video_recap_action.is_some() {
            report_video_recap_metric(
                "VideoRecapPresent".to_string(),
                recap.recap_type.to_string(),
            );
        }
    });
}

pub fn report_experience_opened(recap_type: &RecapType) {
    metric!(EXPERIENCE_OPENED_METRIC, 1, "recapType" => recap_type.to_string());
}

pub fn report_video_recap_metric(prefix: String, recap_type: String) {
    metric!("PageAction.Count", 1,
        "pageType" => "DetailsATF",
        "actionName" => format!("{}.{}", prefix, recap_type)
    );
}
