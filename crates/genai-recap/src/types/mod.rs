use crate::network::{RecapBackgroundImage, RecapSegment, RecapType};
use cfg_test_attr_derive::derive_test_only;
use fableous::{
    buttons::{play_button::PlayButtonHeight, primary_button::PrimaryButtonVariant},
    progress_bar::ProgressBarVariant,
};
use ignx_compositron::{prelude::MaybeSignal, text::TextContent};
use std::rc::Rc;

#[derive(Clone)]
pub struct RecapExperienceModel {
    pub recap_type: RecapType,
    pub default_background_image: Option<RecapBackgroundImage>,
    pub segments: Vec<RecapSegment>,
    pub title: RecapExperienceTitle,
    pub play_action: Option<RecapAction>,
}

type TitleString = String;
type SeasonNumber = u32;
type EpisodeNumber = u32;

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum RecapExperienceTitle {
    ThisEpisode(RecapExperienceTitleThisEpisode),
    ThisSeason(RecapExperienceTitleThisSeason),
    PreviousSeason(RecapExperienceTitlePreviousSeason),
    Movie,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct RecapExperienceTitleThisEpisode {
    pub season: SeasonNumber,
    pub episode: EpisodeNumber,
    pub episode_title: Option<TitleString>,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct RecapExperienceTitleThisSeason {
    pub selected_season: SeasonNumber,
    pub selected_episode: EpisodeNumber,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct RecapExperienceTitlePreviousSeason {
    pub previous_season: SeasonNumber,
}

#[derive(Clone)]
pub enum RecapAction {
    Play(PlayVariant),
    Primary(PrimaryVariant),
}

#[derive(Clone)]
pub struct PlayVariant {
    pub title: TextContent,
    pub height: PlayButtonHeight,
    pub variant: ProgressBarVariant,
    pub on_select: Rc<dyn Fn()>,
    pub progress: f32,
}

#[derive(Clone)]
pub struct PrimaryVariant {
    pub variant: MaybeSignal<PrimaryButtonVariant>,
    pub on_select: Rc<dyn Fn()>,
}

pub mod metrics;
