[package]
name = "genai-recap"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
log.workspace = true
cfg-test-attr-derive.workspace = true
fableous.workspace = true
drawer.workspace = true
rust-features.workspace = true
media-background.workspace = true
network.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
cross-app-events.workspace = true
mockall.workspace = true
mockall_double.workspace = true
serde_json.workspace = true
strum_macros.workspace = true
common-transform-types.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
    "mock_timer",
] }
rstest.workspace = true

[[example]]
name = "recap_experience"
crate-type = ["cdylib"]
path = "examples/recap_experience.rs"

[features]
debug_impl = []
