use amzn_fable_tokens::FableColor;
use fableous::font_icon::*;
use fableous::utils::get_ignx_color;
use genai_recap::experience::recap_experience::*;
use genai_recap::mocks::mock_data::*;
use ignx_compositron::compose;

use ignx_compositron::prelude::*;
use ignx_compositron::time::Instant;
use rust_features::provide_context_test_rust_features;
use std::time::Duration;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let start_rendering = create_rw_signal(ctx.scope(), false);
        ctx.schedule_task(Instant::now() + Duration::from_millis(1000), move || {
            start_rendering.set(true);
        });
        provide_context_test_rust_features(ctx.scope());

        let title = "Fallout".to_string();

        compose! {
            Column() {
                FontIcon(icon:"", color: get_ignx_color(FableColor::PRIMARY), size: FontSize(5))

                if start_rendering.get() {
                    RecapExperience(show_title: &title, data: create_recap_experience_mock_data(None))
                }
            }
        }
    });
}
