# FireTV Playback

This crate provides functionality for handling playback events on FireTV devices for the Prime Video Living Room Rust Client.

## Overview

The `firetv-playback` crate is responsible for:

1. Listening to Generic Message Bus (GMB) events from FireBat for playback-related actions
2. Processing playback load, cache, and start events
3. Updating the playback context and cache based on these events

## Key Components

- `FireTVPlaybackContext`: Main context that manages GMB event handlers for playback events
- `PlaybackEvent`: Enum representing different types of playback events (Load, Cache, Start)
- Payload parsers for handling GMB message content

## Usage

The `FireTVPlaybackContext` is typically used through the `firetv` crate, which integrates it into the broader FireTV detection and handling system:

```rust
use firetv_playback::FireTVPlaybackContext;
use playback::context::PlaybackContext;
use playback_cache::PlaybackCache;

// Create a new FireTV playback context
let playback_context = /* obtain PlaybackContext */;
let playback_cache = /* obtain PlaybackCache */;
let mut firetv_playback = FireTVPlaybackContext::new(playback_context, playback_cache);

// Register event handlers
firetv_playback.register_playback_load_event(&app_context);
firetv_playback.register_playback_cache_event(&app_context);
firetv_playback.register_playback_play_event(&app_context);
```

## GMB Message Types

The crate handles the following GMB message types:

- `firetv.playback.load`: Loads content for playback
- `firetv.playback.cache`: Caches content for future playback
- `firetv.playback.start`: Starts playing a content

## Testing

Tests are available for all components. Some tests that require `PlaybackCache` mocks are currently marked with `#[ignore]` until those mocks are available.
