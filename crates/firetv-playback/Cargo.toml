[package]
name = "firetv-playback"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true
description = "FireTV Playback module for PV LRC"

[dependencies]
amzn-ignx-compositron.workspace = true
playback.workspace = true
mockall.workspace = true
serde_json.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true

[lints]
workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["test_utils"] }
playback = { workspace = true, features = ["test_utils"] }
rstest.workspace = true

[features]
# Enable debug implementations
debug_impl = []
# Enable mock implementations for testing
test_utils = []
