/// Enum representing different types of playback events for `FireTV`.
///
/// These events correspond to GMB message types that are sent from firebat
/// to trigger playback-related actions.
pub enum PlaybackEvent {
    Load,
    Cache,
    Start,
}

impl PlaybackEvent {
    pub fn to_gmb_message_type(&self) -> &str {
        match self {
            PlaybackEvent::Load => "gmb.firetv.playback.load",
            PlaybackEvent::Cache => "gmb.firetv.playback.cache",
            PlaybackEvent::Start => "gmb.firetv.playback.start",
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_playback_event_to_gmb_message_type() {
        assert_eq!(
            PlaybackEvent::Load.to_gmb_message_type(),
            "gmb.firetv.playback.load"
        );
        assert_eq!(
            PlaybackEvent::Cache.to_gmb_message_type(),
            "gmb.firetv.playback.cache"
        );
        assert_eq!(
            PlaybackEvent::Start.to_gmb_message_type(),
            "gmb.firetv.playback.start"
        );
    }
}
