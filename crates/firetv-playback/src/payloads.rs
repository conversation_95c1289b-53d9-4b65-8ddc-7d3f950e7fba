#![allow(dead_code, reason = "https://taskei.amazon.dev/tasks/pyro-340")]

use ignx_compositron::log;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

/// Payload structure for playback content GMB messages.
#[derive(NetworkParsed)]
pub struct PlaybackContentPayload {
    pub title_id: String,
    pub client_id: Option<String>,
    pub ref_marker: Option<String>,
    pub version: i32,
}

/// Payload structure for playback start GMB messages.
#[derive(NetworkParsed)]
pub struct PlaybackStartContentPayload {
    pub title_id: String,
    pub video_material_type: PlaybackStartVideoMaterialType,
    pub is_linear: bool,
    pub client_id: Option<String>,
    pub ref_marker: Option<String>,
    pub external_surface_hash: Option<String>,
    pub version: i32,
}

#[derive(NetworkParsed)]
pub enum PlaybackStartVideoMaterialType {
    LiveStreaming,
}

pub fn parse_playback_content_gmb_message(
    payload: &str,
    action: &str,
) -> serde_json::Result<PlaybackContentPayload> {
    network_parse_from_str(payload).inspect_err(|err| {
        log::error!(
            "[firetv-playback.parse_gmb] Cannot parse {} gmb message. Error {}",
            action,
            err
        );
    })
}

pub fn parse_playback_start_content_gmb_message(
    payload: &str,
) -> serde_json::Result<PlaybackStartContentPayload> {
    network_parse_from_str(payload).inspect_err(|err| {
        log::error!(
            "[firetv-playback.parse_gmb] Cannot parse playback start gmb message. Error {}",
            err
        );
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_playback_content_gmb_message_valid() {
        let payload = r#"{
            "client_id": "test-client-id",
            "title_id": "test-title-123",
            "ref_marker": "test-ref-marker",
            "version": 1
        }"#;

        let result = parse_playback_content_gmb_message(payload, "test");
        assert!(result.is_ok());

        let message = result.unwrap();
        assert_eq!(message.client_id, Some("test-client-id".to_string()));
        assert_eq!(message.title_id, "test-title-123");
        assert_eq!(message.ref_marker, Some("test-ref-marker".to_string()));
        assert_eq!(message.version, 1);
    }

    #[test]
    fn test_parse_playback_content_gmb_message_minimum_valid_content() {
        let payload = r#"{
            "title_id": "test-title-123",
            "version": 1
        }"#;

        let result = parse_playback_content_gmb_message(payload, "test");

        assert!(result.is_ok());

        let message = result.unwrap();
        assert_eq!(message.client_id, None);
        assert_eq!(message.title_id, "test-title-123");
        assert_eq!(message.ref_marker, None);
        assert_eq!(message.version, 1);
    }

    #[test]
    fn test_parse_playback_content_gmb_message_invalid() {
        let payload = r#"{
            "clientId": "test-client-id",
            "invalid": true
        }"#;

        let result = parse_playback_content_gmb_message(payload, "test");
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_playback_start_content_gmb_message_valid() {
        let payload = r#"{
            "client_id": "test-client-id",
            "title_id": "test-title-123",
            "ref_marker": "test-ref-marker",
            "external_surface_hash": "some-surface-hash-str",
            "video_material_type": "LiveStreaming",
            "is_linear": true,
            "version": 1
        }"#;

        let result = parse_playback_start_content_gmb_message(payload);
        assert!(result.is_ok());

        let message = result.unwrap();
        assert_eq!(message.client_id, Some("test-client-id".to_string()));
        assert_eq!(message.title_id, "test-title-123");
        assert_eq!(message.ref_marker, Some("test-ref-marker".to_string()));
        assert_eq!(
            message.external_surface_hash,
            Some("some-surface-hash-str".to_string())
        );
        assert!(matches!(
            message.video_material_type,
            PlaybackStartVideoMaterialType::LiveStreaming
        ));
        assert!(message.is_linear);
        assert_eq!(message.version, 1);
    }

    #[test]
    fn test_parse_playback_start_content_gmb_message_minimum_valid_content() {
        let payload = r#"{
            "title_id": "test-title-123",
            "video_material_type": "LiveStreaming",
            "is_linear": false,
            "version": 1
        }"#;

        let result = parse_playback_start_content_gmb_message(payload);

        assert!(result.is_ok());

        let message = result.unwrap();
        assert_eq!(message.client_id, None);
        assert_eq!(message.title_id, "test-title-123");
        assert_eq!(message.ref_marker, None);
        assert_eq!(message.external_surface_hash, None);
        assert!(matches!(
            message.video_material_type,
            PlaybackStartVideoMaterialType::LiveStreaming
        ));
        assert!(!message.is_linear);
        assert_eq!(message.version, 1);
    }

    #[test]
    fn test_parse_playback_start_content_gmb_message_invalid_video_material_type() {
        let payload = r#"{
            "title_id": "test-title-123",
            "video_material_type": "Invalid",
            "is_linear": true,
            "version": 1
        }"#;

        let result = parse_playback_start_content_gmb_message(payload);
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_playback_start_content_gmb_message_invalid() {
        let payload = r#"{
            "clientId": "test-client-id",
            "invalid": true
        }"#;

        let result = parse_playback_start_content_gmb_message(payload);
        assert!(result.is_err());
    }
}
