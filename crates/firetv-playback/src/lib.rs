/// `FireTVPlayback` module for handling playback events on `FireTV` devices.
///
/// This module provides functionality to listen to Generic Message Bus (GMB) events
/// from firebat for playback-related actions and update the playback context accordingly.
mod events;
mod payloads;

use crate::events::PlaybackEvent;
use crate::payloads::{parse_playback_start_content_gmb_message, PlaybackStartVideoMaterialType};
use ignx_compositron::{
    gmb::{on_generic_message_bus_event, GenericMessageBusEventGuard},
    log,
    prelude::*,
};
use payloads::parse_playback_content_gmb_message;
use playback::context::{PlaybackSurface, PlaybackView, Viewport};
use playback::src::{LinearSrc, LinearStartMode, LiveEventSrc, LiveEventStartMode};
use playback::{
    cache::{CachePriority, PlaybackCache, PlaybackCacheEvent},
    context::{PlaybackContext, PlaybackOptions},
    src::{MinimalSrc, PlaybackOrigin, PlaybackSrc},
};

/// Context for managing `FireTV` playback events.
///
/// This struct is responsible for registering event handlers for playback-related
/// GMB events and processing them to update the playback context and cache.
pub struct FireTVPlaybackContext {
    playback_context: PlaybackContext,
    playback_cache: PlaybackCache,
    pub(crate) gmb_event_guards: Vec<GenericMessageBusEventGuard>,
}

impl FireTVPlaybackContext {
    pub fn new(playback_context: PlaybackContext, playback_cache: PlaybackCache) -> Self {
        Self {
            playback_context,
            playback_cache,
            gmb_event_guards: Vec::new(),
        }
    }

    pub fn register_playback_load_event(&mut self, ctx: &AppContext) {
        let playback_cache = self.playback_cache;
        self.gmb_event_guards.push(on_generic_message_bus_event(
            ctx,
            PlaybackEvent::Load.to_gmb_message_type(),
            move |payload: &str| on_playback_load_event(&playback_cache, payload),
        ));
    }

    pub fn register_playback_cache_event(&mut self, ctx: &AppContext) {
        let playback_cache = self.playback_cache;
        self.gmb_event_guards.push(on_generic_message_bus_event(
            ctx,
            PlaybackEvent::Cache.to_gmb_message_type(),
            move |payload: &str| on_playback_cache_event(&playback_cache, payload),
        ));
    }

    pub fn register_playback_play_event(&mut self, ctx: &AppContext) {
        let playback_context = self.playback_context.clone();
        self.gmb_event_guards.push(on_generic_message_bus_event(
            ctx,
            PlaybackEvent::Start.to_gmb_message_type(),
            move |payload: &str| on_playback_play_event(&playback_context, payload),
        ));
    }
}

fn on_playback_play_event(playback_context: &PlaybackContext, payload: &str) {
    let Ok(message) = parse_playback_start_content_gmb_message(payload) else {
        return;
    };

    log::info!(
        "[FireTVPlaybackContext] received play command for {}",
        message.title_id
    );

    let playback_src = match (message.video_material_type, message.is_linear) {
        (PlaybackStartVideoMaterialType::LiveStreaming, true) => PlaybackSrc::Linear(LinearSrc {
            title_id: message.title_id,
            start_mode: LinearStartMode::AtLive,
            client_id: None,
        }),
        (PlaybackStartVideoMaterialType::LiveStreaming, false) => {
            PlaybackSrc::LiveEvent(LiveEventSrc {
                title_id: message.title_id,
                playback_envelope: None,
                start_mode: LiveEventStartMode::AtLive,
                client_id: None,
            })
        }
    };

    log::debug!(
        "[FireTVPlaybackContext] starting playback with src {:?}",
        playback_src
    );

    playback_context.start_with_options(
        playback_src,
        PlaybackOptions::default().origin(PlaybackOrigin::FireTV),
    );

    let playback_surface = if let Some(external_surface_hash) = message.external_surface_hash {
        PlaybackSurface::TIF(external_surface_hash)
    } else {
        PlaybackSurface::PV
    };

    playback_context.view.set(PlaybackView::NoControls {
        viewport: Viewport::fullscreen(),
        surface: playback_surface,
    });
}

fn on_playback_load_event(playback_cache: &PlaybackCache, payload: &str) {
    let Ok(message) = parse_playback_content_gmb_message(payload, "load") else {
        return;
    };

    log::info!(
        "[FireTVPlaybackContext] received load command for {}",
        message.title_id
    );

    // TODO: report metrics https://taskei.amazon.dev/tasks/pyro-340
    playback_cache.event(PlaybackCacheEvent::FireTV {
        src: PlaybackSrc::Minimal(MinimalSrc {
            title_id: message.title_id,
        }),
        priority: CachePriority::High,
    });
}

fn on_playback_cache_event(playback_cache: &PlaybackCache, payload: &str) {
    let Ok(message) = parse_playback_content_gmb_message(payload, "cache") else {
        return;
    };

    log::info!(
        "[FireTVPlaybackContext] received cache command for {}",
        message.title_id
    );

    // TODO: report metrics https://taskei.amazon.dev/tasks/pyro-340
    playback_cache.event(PlaybackCacheEvent::FireTV {
        src: PlaybackSrc::Minimal(MinimalSrc {
            title_id: message.title_id,
        }),
        priority: CachePriority::High,
    });
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_only_app_context;
    use playback::{cache::provide_playback_cache, context::provide_playback};

    #[allow(unused, reason = "will be used for tests later")]
    fn create_playback_content_payload(title_id: &str) -> String {
        format!(
            r#"{{
                "client_id": "test-client-id",
                "title_id": "{}",
                "ref_marker": "test-ref-marker",
                "version": 1
            }}"#,
            title_id
        )
    }

    fn create_playback_start_content_payload(
        title_id: &str,
        surface_ref: &str,
        is_linear: bool,
    ) -> String {
        format!(
            r#"{{
                "client_id": "test-client-id",
                "title_id": "{}",
                "ref_marker": "test-ref-marker",
                "external_surface_hash": "{}",
                "video_material_type": "LiveStreaming",
                "is_linear": {},
                "version": 1
            }}"#,
            title_id, surface_ref, is_linear
        )
    }

    // Helper function to create an invalid JSON payload
    fn create_invalid_payload() -> String {
        r#"{
            "client_id": "test-client-id",
            "invalid": true,
        }"#
        .to_string()
    }

    #[test]
    fn test_on_playback_play_event_valid_linear_payload() {
        launch_only_app_context(|ctx| {
            let playback_ctx = provide_playback(&ctx);
            playback_ctx.set_content(None);

            assert!(playback_ctx.src.with(|v| v.is_none()));
            assert!(playback_ctx.view.with(|v| v == &PlaybackView::default()));
            assert!(!playback_ctx.playing.get());

            let title_id = "test-title-456";
            let expected_surface_ref = "test-surface-hash";
            let is_linear = true;
            let payload =
                create_playback_start_content_payload(title_id, expected_surface_ref, is_linear);
            on_playback_play_event(&playback_ctx, &payload);

            playback_ctx.src.with(|src| {
                let src = src.as_ref().expect("Source should be set");
                match src {
                    PlaybackSrc::Linear(linear_src) => {
                        assert_eq!(linear_src.title_id, title_id);
                        assert_eq!(linear_src.start_mode, LinearStartMode::AtLive);
                        assert_eq!(linear_src.client_id, None);
                    }
                    _ => panic!("Expected PlaybackSrc::Linear"),
                }
            });

            playback_ctx.view.with(|playback_view| {
                assert_eq!(
                    playback_view.to_owned(),
                    PlaybackView::NoControls {
                        viewport: Viewport::fullscreen(),
                        surface: PlaybackSurface::TIF(expected_surface_ref.to_string())
                    }
                );
            });

            assert!(
                playback_ctx.playing.get(),
                "Playing state should be set to true"
            );
        });
    }

    #[test]
    fn test_on_playback_play_event_valid_live_payload() {
        launch_only_app_context(|ctx| {
            let playback_ctx = provide_playback(&ctx);
            playback_ctx.set_content(None);

            assert!(playback_ctx.src.with(|v| v.is_none()));
            assert!(playback_ctx.view.with(|v| v == &PlaybackView::default()));
            assert!(!playback_ctx.playing.get());

            let title_id = "test-title-456";
            let expected_surface_ref = "test-surface-hash";
            let is_linear = false;
            let payload =
                create_playback_start_content_payload(title_id, expected_surface_ref, is_linear);
            on_playback_play_event(&playback_ctx, &payload);

            playback_ctx.src.with(|src| {
                let src = src.as_ref().expect("Source should be set");
                match src {
                    PlaybackSrc::LiveEvent(live_event_src) => {
                        assert_eq!(live_event_src.title_id, title_id);
                        assert_eq!(live_event_src.start_mode, LiveEventStartMode::AtLive);
                        assert_eq!(live_event_src.client_id, None);
                        assert_eq!(live_event_src.playback_envelope, None);
                    }
                    _ => panic!("Expected PlaybackSrc::LiveEvent"),
                }
            });

            playback_ctx.view.with(|playback_view| {
                assert_eq!(
                    playback_view.to_owned(),
                    PlaybackView::NoControls {
                        viewport: Viewport::fullscreen(),
                        surface: PlaybackSurface::TIF(expected_surface_ref.to_string())
                    }
                );
            });

            assert!(
                playback_ctx.playing.get(),
                "Playing state should be set to true"
            );
        });
    }

    #[test]
    fn test_on_playback_play_event_invalid_payload() {
        launch_only_app_context(|ctx| {
            let playback_ctx = provide_playback(&ctx);

            let test_src = PlaybackSrc::Minimal(MinimalSrc {
                title_id: "test-title-123".to_string(),
            });
            playback_ctx.set_content(Some(test_src.into()));

            assert!(playback_ctx.src.with(|v| v.is_some()));
            assert!(playback_ctx.view.with(|v| v == &PlaybackView::default()));
            assert!(!playback_ctx.playing.get());

            let payload = create_invalid_payload();
            on_playback_play_event(&playback_ctx, &payload);

            // Verify the source remains unchanged (function should return early on parse error)
            playback_ctx.src.with(|src| {
                let src = src.as_ref().expect("Source should still be set");
                match src {
                    PlaybackSrc::Minimal(minimal_src) => {
                        assert_eq!(minimal_src.title_id, "test-title-123");
                    }
                    _ => panic!("Expected MinimalSrc"),
                }
            });

            // Verify the playing state remains unchanged
            assert!(
                !playback_ctx.playing.get(),
                "Playing state should remain false for invalid payload"
            );

            // Verify the surface_ref remains unchanged
            assert!(playback_ctx.view.with(|v| v == &PlaybackView::default()));
        });
    }

    #[test]
    fn test_on_playback_play_event_without_surface() {
        launch_only_app_context(|ctx| {
            let playback_ctx = provide_playback(&ctx);
            playback_ctx.set_content(None);
            playback_ctx.view.set(PlaybackView::Full);

            assert!(playback_ctx.src.with(|v| v.is_none()));
            assert!(!playback_ctx.playing.get());

            let title_id = "test-title-456";
            let payload = format!(
                r#"{{
                    "client_id": "test-client-id",
                    "title_id": "{}",
                    "ref_marker": "test-ref-marker",
                    "video_material_type": "LiveStreaming",
                    "is_linear": true,
                    "version": 1
                }}"#,
                title_id
            );

            on_playback_play_event(&playback_ctx, &payload);

            playback_ctx.src.with(|src| {
                let src = src.as_ref().expect("Source should be set");
                match src {
                    PlaybackSrc::Linear(linear_src) => {
                        assert_eq!(linear_src.title_id, title_id);
                        assert_eq!(linear_src.start_mode, LinearStartMode::AtLive);
                        assert_eq!(linear_src.client_id, None);
                    }
                    _ => panic!("Expected PlaybackSrc::Linear"),
                }
            });

            // PlaybackSurface::PV should be used when external_surface_hash is not set
            playback_ctx.view.with(|playback_view| {
                assert_eq!(
                    playback_view.to_owned(),
                    PlaybackView::NoControls {
                        viewport: Viewport::fullscreen(),
                        surface: PlaybackSurface::PV
                    }
                );
            });

            assert!(
                playback_ctx.playing.get(),
                "Playing state should be set to true"
            );
        });
    }

    #[test]
    fn test_on_playback_load_event_valid_payload() {
        launch_only_app_context(|ctx| {
            let cache = provide_playback_cache(&ctx);
            let title_id = "test-title-123";
            let payload = create_playback_content_payload(title_id);

            on_playback_load_event(&cache, &payload);

            cache.validate_events(|events| {
                assert_eq!(events.len(), 1);
                match &events[0] {
                    PlaybackCacheEvent::FireTV { src, priority } => {
                        assert_eq!(
                            src,
                            &PlaybackSrc::Minimal(MinimalSrc {
                                title_id: title_id.to_string()
                            })
                        );
                        assert_eq!(priority, &CachePriority::High);
                    }
                    _ => panic!("Expected FireTV event"),
                }
            });
        });
    }

    #[test]
    fn test_on_playback_load_event_invalid_payload() {
        launch_only_app_context(|ctx| {
            let cache = provide_playback_cache(&ctx);
            let payload = create_invalid_payload();

            on_playback_load_event(&cache, &payload);

            // Verify no events were recorded due to invalid payload
            cache.validate_events(|events| {
                assert_eq!(
                    events.len(),
                    0,
                    "No events should be recorded for invalid payload"
                );
            });
        });
    }

    #[test]
    fn test_on_playback_cache_event_valid_payload() {
        launch_only_app_context(|ctx| {
            let cache = provide_playback_cache(&ctx);
            let title_id = "test-title-456";
            let payload = create_playback_content_payload(title_id);

            on_playback_cache_event(&cache, &payload);

            cache.validate_events(|events| {
                assert_eq!(events.len(), 1);
                match &events[0] {
                    PlaybackCacheEvent::FireTV { src, priority } => {
                        assert_eq!(
                            src,
                            &PlaybackSrc::Minimal(MinimalSrc {
                                title_id: title_id.to_string()
                            })
                        );
                        assert_eq!(priority, &CachePriority::High);
                    }
                    _ => panic!("Expected FireTV event"),
                }
            });
        });
    }

    #[test]
    fn test_on_playback_cache_event_invalid_payload() {
        launch_only_app_context(|ctx| {
            let cache = provide_playback_cache(&ctx);
            let payload = create_invalid_payload();

            on_playback_cache_event(&cache, &payload);

            // Verify no events were recorded due to invalid payload
            cache.validate_events(|events| {
                assert_eq!(
                    events.len(),
                    0,
                    "No events should be recorded for invalid payload"
                );
            });
        });
    }

    #[test]
    fn test_multiple_cache_events() {
        launch_only_app_context(|ctx| {
            let cache = provide_playback_cache(&ctx);
            let title_id_1 = "test-title-123";
            let title_id_2 = "test-title-456";

            // Send both a load and cache event
            on_playback_load_event(&cache, &create_playback_content_payload(title_id_1));
            on_playback_cache_event(&cache, &create_playback_content_payload(title_id_2));

            cache.validate_events(|events| {
                assert_eq!(events.len(), 2, "Should have recorded both events");

                // Verify first event (load)
                match &events[0] {
                    PlaybackCacheEvent::FireTV { src, priority } => {
                        assert_eq!(
                            src,
                            &PlaybackSrc::Minimal(MinimalSrc {
                                title_id: title_id_1.to_string()
                            })
                        );
                        assert_eq!(priority, &CachePriority::High);
                    }
                    _ => panic!("Expected FireTV event for load"),
                }

                // Verify second event (cache)
                match &events[1] {
                    PlaybackCacheEvent::FireTV { src, priority } => {
                        assert_eq!(
                            src,
                            &PlaybackSrc::Minimal(MinimalSrc {
                                title_id: title_id_2.to_string()
                            })
                        );
                        assert_eq!(priority, &CachePriority::High);
                    }
                    _ => panic!("Expected FireTV event for cache"),
                }
            });
        });
    }

    #[test]
    fn test_register_playback_load_event() {
        launch_only_app_context(|ctx| {
            let playback_ctx = provide_playback(&ctx);
            let playback_cache = provide_playback_cache(&ctx);

            let mut firetv_ctx = FireTVPlaybackContext::new(playback_ctx, playback_cache);

            assert_eq!(firetv_ctx.gmb_event_guards.len(), 0);

            firetv_ctx.register_playback_load_event(&ctx);

            assert_eq!(firetv_ctx.gmb_event_guards.len(), 1);
        });
    }

    #[test]
    fn test_register_playback_cache_event() {
        launch_only_app_context(|ctx| {
            let playback_ctx = provide_playback(&ctx);
            let playback_cache = provide_playback_cache(&ctx);

            let mut firetv_ctx = FireTVPlaybackContext::new(playback_ctx, playback_cache);

            assert_eq!(firetv_ctx.gmb_event_guards.len(), 0);

            firetv_ctx.register_playback_cache_event(&ctx);

            assert_eq!(firetv_ctx.gmb_event_guards.len(), 1);
        });
    }

    #[test]
    fn test_register_playback_play_event() {
        launch_only_app_context(|ctx| {
            let playback_ctx = provide_playback(&ctx);
            let playback_cache = provide_playback_cache(&ctx);

            let mut firetv_ctx = FireTVPlaybackContext::new(playback_ctx, playback_cache);

            assert_eq!(firetv_ctx.gmb_event_guards.len(), 0);

            firetv_ctx.register_playback_play_event(&ctx);

            assert_eq!(firetv_ctx.gmb_event_guards.len(), 1);
        });
    }
}
