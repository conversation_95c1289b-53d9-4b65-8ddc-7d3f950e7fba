use ignx_compositron::{compose, prelude::*};
use media_background::types::MediaBackgroundType;
use navigation_menu::context::nav_context::NavControl;
use search_page::components::page_wrapper::*;
use title_details::core::TitleDetailsChangeRequest;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let mb = create_rw_signal(ctx.scope(), MediaBackgroundType::create_mock());
        let td = create_rw_signal(ctx.scope(), TitleDetailsChangeRequest::create_mock());
        provide_context(ctx.scope(), NavControl::create_mock(ctx.scope()));

        compose! {
            SearchPage(
                update_media_background: mb.write_only(),
                update_title_details: td.write_only()
            )
        }
    })
}
