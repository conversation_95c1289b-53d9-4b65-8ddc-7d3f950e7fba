use std::{
    char,
    fmt::{self, Display, Formatter},
    rc::Rc,
};

use fableous::buttons::tertiary_button::*;
use ignx_compositron::{compose, id::Id, prelude::*, Composer, WrapDirection};

const KEYBOARD_TEST_ID: &str = "KEYBOARD_TEST_ID";

#[derive(<PERSON><PERSON>, <PERSON><PERSON>)]
enum KeyboardKey {
    Key(char),
    Space,
    Backspace,
    Clear,
}

impl KeyboardKey {
    fn handle_key(&self, current_string: String) -> String {
        match self {
            KeyboardKey::Key(key) => {
                let mut new_string = current_string;
                new_string.push(*key);
                new_string
            }
            KeyboardKey::Space => {
                let mut new_string = current_string;
                new_string.push(' ');
                new_string
            }
            KeyboardKey::Backspace => {
                let mut new_string = current_string;
                new_string.pop();
                new_string
            }
            KeyboardKey::Clear => "".to_string(),
        }
    }

    fn get_width(&self) -> f32 {
        match self {
            KeyboardKey::Key(_) => 56.0,
            KeyboardKey::Space => 56.0 * 2.0,
            KeyboardKey::Backspace => 56.0 * 2.0,
            KeyboardKey::Clear => 56.0 * 2.0,
        }
    }

    fn get_height(&self) -> f32 {
        match self {
            KeyboardKey::Key(_) => 56.0,
            KeyboardKey::Space => 56.0,
            KeyboardKey::Backspace => 56.0,
            KeyboardKey::Clear => 56.0,
        }
    }
}

impl Display for KeyboardKey {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        match self {
            KeyboardKey::Key(key) => write!(f, "{}", key),
            KeyboardKey::Space => write!(f, "Space"),
            KeyboardKey::Backspace => write!(f, "Delete"),
            KeyboardKey::Clear => write!(f, "Clear"),
        }
    }
}

impl Id for KeyboardKey {
    type Id = KeyboardKey;

    fn id(&self) -> &Self::Id {
        self
    }
}

const EN_KEYS: [KeyboardKey; 39] = [
    KeyboardKey::Key('a'),
    KeyboardKey::Key('b'),
    KeyboardKey::Key('c'),
    KeyboardKey::Key('d'),
    KeyboardKey::Key('e'),
    KeyboardKey::Key('f'),
    KeyboardKey::Key('g'),
    KeyboardKey::Key('h'),
    KeyboardKey::Key('i'),
    KeyboardKey::Key('j'),
    KeyboardKey::Key('k'),
    KeyboardKey::Key('l'),
    KeyboardKey::Key('m'),
    KeyboardKey::Key('n'),
    KeyboardKey::Key('o'),
    KeyboardKey::Key('p'),
    KeyboardKey::Key('q'),
    KeyboardKey::Key('r'),
    KeyboardKey::Key('s'),
    KeyboardKey::Key('t'),
    KeyboardKey::Key('u'),
    KeyboardKey::Key('v'),
    KeyboardKey::Key('w'),
    KeyboardKey::Key('x'),
    KeyboardKey::Key('y'),
    KeyboardKey::Key('z'),
    KeyboardKey::Key('1'),
    KeyboardKey::Key('2'),
    KeyboardKey::Key('3'),
    KeyboardKey::Key('4'),
    KeyboardKey::Key('5'),
    KeyboardKey::Key('6'),
    KeyboardKey::Key('7'),
    KeyboardKey::Key('8'),
    KeyboardKey::Key('9'),
    KeyboardKey::Key('0'),
    KeyboardKey::Space,
    KeyboardKey::Backspace,
    KeyboardKey::Clear,
];

#[Composer]
fn Keyboard(
    ctx: &AppContext,
    current_string: RwSignal<String>,
    #[optional = Rc::new(|_| {})] on_change: Rc<dyn Fn(String)>,
) -> impl Composable<'static> + 'static {
    let current_keys = EN_KEYS.to_vec();

    compose! {
        Column() {
            WrapBoxForEach(items: current_keys, wrap_direction: WrapDirection::Row, item_builder: Rc::new(
                move |ctx, key, _i| {
                    let text = key.to_string();
                    let test_id = format!("KEYBOARD_BUTTON_{}", text);

                    compose! {
                        TertiaryButton(variant: TertiaryButtonVariant::TextSize200(text.into()))
                        .test_id(test_id)
                        .main_axis_alignment(MainAxisAlignment::Center)
                        .cross_axis_alignment(CrossAxisAlignment::Center)
                        .width(key.get_width())
                        .height(key.get_height())
                        .on_select({
                            let key = key.clone();
                            let on_change = on_change.clone();
                            move || {
                                let new_string = key.handle_key(current_string.get_untracked());
                                current_string.set(new_string.clone());
                                on_change(new_string);
                            }
                        })
                    }
                }
            ))
        }
        .width(355.0)
        .test_id(KEYBOARD_TEST_ID)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::input::{KeyCode, KeyEventType};
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::test_utils::*;

    fn press_keys(test_game_loop: &mut TestRendererGameLoop, keys: &[KeyCode]) -> SceneNodeTree {
        for key in keys {
            test_game_loop.send_key_press_event(KeyEventType::ButtonDown, key.clone());
            test_game_loop.send_key_press_event(KeyEventType::ButtonUp, key.clone());
            test_game_loop.tick_until_done();
        }
        test_game_loop.tick_until_done()
    }

    #[test]
    fn should_render_component() {
        launch_test(
            move |ctx| {
                compose! {
                    Keyboard(current_string: create_rw_signal(ctx.scope(), "".to_string()))
                }
            },
            |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                assert_node_exists!(node_tree.find_by_test_id(KEYBOARD_TEST_ID));
            },
        )
    }

    #[test]
    fn should_have_default_focus_on_first_button() {
        launch_test(
            move |ctx| {
                compose! {
                    Keyboard(current_string: create_rw_signal(ctx.scope(), "".to_string()))
                }
            },
            |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let current = node_tree.find_by_props().is_focused().find_first();
                let test_id = current.get_props().test_id.expect("Expect test id");
                assert_eq!(test_id, "KEYBOARD_BUTTON_a");
            },
        )
    }

    #[test]
    fn should_handle_text_updates() {
        launch_test(
            move |ctx| {
                let current_string = create_rw_signal(ctx.scope(), "".to_string());
                provide_context(ctx.scope(), current_string);
                compose! {
                    Stack() {
                        Keyboard(current_string)
                    }
                    .width(355.0)
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let current_string = use_context::<RwSignal<String>>(scope).expect("Shared signal");

                // String is empty
                assert_eq!(current_string.get_untracked(), "");

                // Press first button – "a"
                press_keys(&mut test_game_loop, &[KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "a");

                // Move down a row and press first button – "g"
                press_keys(&mut test_game_loop, &[KeyCode::Down, KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "ag");

                // Move down a row and press first button – "5"
                press_keys(
                    &mut test_game_loop,
                    &[
                        KeyCode::Down,
                        KeyCode::Down,
                        KeyCode::Down,
                        KeyCode::Down,
                        KeyCode::Select,
                    ],
                );
                assert_eq!(current_string.get_untracked(), "ag5");

                // Move down a row and press first button – "Space"
                press_keys(&mut test_game_loop, &[KeyCode::Down, KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "ag5 ");

                // Move right and press first button – "Delete"
                press_keys(&mut test_game_loop, &[KeyCode::Right, KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "ag5");

                // Delete again
                press_keys(&mut test_game_loop, &[KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "ag");

                // Move right and press clear
                press_keys(&mut test_game_loop, &[KeyCode::Right, KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "");

                // Move up and press button – 9
                press_keys(&mut test_game_loop, &[KeyCode::Up, KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "9");
            },
        )
    }

    #[test]
    fn should_respond_to_updates_to_string_signal() {
        launch_test(
            move |ctx| {
                let current_string = create_rw_signal(ctx.scope(), "test".to_string());
                provide_context(ctx.scope(), current_string);
                compose! {
                    Stack() {
                        Keyboard(current_string)
                    }
                    .width(355.0)
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let current_string = use_context::<RwSignal<String>>(scope).expect("Shared signal");

                // Press first button – "a"
                press_keys(&mut test_game_loop, &[KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "testa");

                // Simulate change externally
                current_string.set("Apple".to_string());

                // Press first button – "a"
                press_keys(&mut test_game_loop, &[KeyCode::Select]);
                assert_eq!(current_string.get_untracked(), "Applea");
            },
        )
    }

    #[test]
    fn should_handle_optional_on_change_callback() {
        launch_test(
            move |ctx| {
                let callback_string = create_rw_signal(ctx.scope(), String::new());
                provide_context(ctx.scope(), callback_string);

                compose! {
                    Stack() {
                        Keyboard(current_string: create_rw_signal(ctx.scope(), String::new()), on_change: Rc::new(move |new_string| {
                            callback_string.set(new_string);
                        }))
                    }
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let callback_string =
                    use_context::<RwSignal<String>>(scope).expect("Shared signal");

                // String is empty
                assert_eq!(callback_string.get_untracked(), "");

                // Press first button – "a"
                press_keys(&mut test_game_loop, &[KeyCode::Select]);
                assert_eq!(callback_string.get_untracked(), "a");

                // Move down a row and press first button – "g"
                press_keys(&mut test_game_loop, &[KeyCode::Down, KeyCode::Select]);
                assert_eq!(callback_string.get_untracked(), "ag");

                // Move down a row and press first button – "5"
                press_keys(
                    &mut test_game_loop,
                    &[
                        KeyCode::Down,
                        KeyCode::Down,
                        KeyCode::Down,
                        KeyCode::Down,
                        KeyCode::Select,
                    ],
                );
                assert_eq!(callback_string.get_untracked(), "ag5");

                // Move down a row and press first button – "Space"
                press_keys(&mut test_game_loop, &[KeyCode::Down, KeyCode::Select]);
                assert_eq!(callback_string.get_untracked(), "ag5 ");

                // Move right and press first button – "Delete"
                press_keys(&mut test_game_loop, &[KeyCode::Right, KeyCode::Select]);
                assert_eq!(callback_string.get_untracked(), "ag5");

                // Delete again
                press_keys(&mut test_game_loop, &[KeyCode::Select]);
                assert_eq!(callback_string.get_untracked(), "ag");

                // Move right and press clear
                press_keys(&mut test_game_loop, &[KeyCode::Right, KeyCode::Select]);
                assert_eq!(callback_string.get_untracked(), "");

                // Move up and press button – 9
                press_keys(&mut test_game_loop, &[KeyCode::Up, KeyCode::Select]);
                assert_eq!(callback_string.get_untracked(), "9");
            },
        )
    }
}
