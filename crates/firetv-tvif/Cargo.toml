[package]
name = "firetv-tvif"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
router.workspace = true
amzn-ignx-compositron.workspace = true
navigation-menu.workspace = true
firetv.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_app", "mock_timer"] }
rstest.workspace = true
serde_json.workspace = true
serial_test.workspace = true
mockall.workspace = true
mockall_double.workspace = true

[lints]
workspace = true

[features]
debug_impl = []
test_utils = []
