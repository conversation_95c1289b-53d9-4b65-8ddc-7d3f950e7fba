use firetv::use_firetv_context;
use ignx_compositron::{compose, log, prelude::*, Composer};
use router::hooks::use_router;

const BACK_TRANSITION_SOURCE: &'static str = "RUST_TVIF";

/// This page is used on FireTV to support "Gordon", i.e. playback
/// of linear content from the "Live TV" tab in the launcher.
/// For this kind of playback, a separate app handles the actual playing
/// back of content and we need to perform auxiliary actions, such as
/// temporarily switching profiles to the profile selected in the launcher.
#[Composer]
pub fn TvifPage(ctx: &AppContext) -> StackComposable {
    let router = use_router(ctx.scope());
    let firetv_context = use_firetv_context(ctx);

    if !firetv_context.is_firetv() {
        log::error!(
            "[TVIF] This page was opened on a non-FireTV device which is unexpected. Going back."
        );
        router.back(BACK_TRANSITION_SOURCE);
        return compose! { Stack() {} };
    }

    compose! { Stack() {} }
}
