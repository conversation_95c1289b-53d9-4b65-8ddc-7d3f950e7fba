#![allow(unused)]
use crate::{
    mock_time_data, sample_timeline, ContentConfig, ContentState, PlaybackState, TimeData,
    TimeUnit, Timeline,
};
use fableous::typography::typography::*;
pub use ignx_compositron::player_data::{
    AdReportingFeature, DiagnosticsFeature, MetricsReportingFeature, PlayerEvent, PlayerFeature,
    StreamFeature,
};
use ignx_compositron::player_data::{PlayerCapabilities, PlayerConfig};
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{
    log,
    prelude::*,
    reactive::{create_rw_signal, store_value, StoredValue},
    task::TaskID,
    time::{Duration, Instant},
};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use std::collections::{HashMap, VecDeque};

use super::{Player<PERSON>allbacks, PlayerCommand, PlayerComposable};

const BUS_LATENCY_MS: u64 = 10;
const PLAYER_INITIALIZE_LATENCY_MS: u64 = 500;
const TIME_DATA_PERIOD_MS: u64 = 200;
const TIME_DATA_PERIOD_MS_F: f64 = 200.;
const TIMELINE_LATENCY_MS: u64 = 500;
const BUFFERING_LATENCY_MS: u64 = 2_000;

fn do_in_ms(ctx: &AppContext, ms: u64, fun: impl FnOnce() + 'static) -> TaskID {
    ctx.schedule_task(Instant::now() + Duration::from_millis(ms), fun)
}

const P: &str = "[playback.player]";

trait PlayerCallbacksContext {
    fn on_player_event(&self, ctx: &AppContext, e: &PlayerEvent);
    fn on_player_ready(&self);
    fn on_command_error(&self, err: String);
    #[allow(dead_code)]
    fn on_feature(&self, ctx: &AppContext, feat: PlayerFeature);
}

// internal helpers to avoid unwrapping stored values all the time
impl PlayerCallbacksContext for StoredValue<PlayerCallbacks> {
    fn on_player_event(&self, ctx: &AppContext, e: &PlayerEvent) {
        do_in_ms(ctx, BUS_LATENCY_MS, {
            let self_ = *self;
            let e = e.clone();
            move || {
                self_.with_value(|inner| {
                    if let Some(cb) = &inner.on_player_event {
                        cb(&e)
                    }
                })
            }
        });
    }

    fn on_player_ready(&self) {
        self.with_value(|inner| {
            if let Some(cb) = &inner.on_player_event {
                cb(&PlayerEvent::Initialisation(Ok(PlayerCapabilities {
                    hdr_support: None,
                    uhd_support: true,
                    resize_support: None,
                })))
            }
        })
    }

    fn on_command_error(&self, err: String) {
        self.with_value(|inner| {
            if let Some(cb) = &inner.on_command_error {
                cb(err)
            }
        })
    }

    fn on_feature(&self, ctx: &AppContext, feat: PlayerFeature) {
        do_in_ms(ctx, BUS_LATENCY_MS, {
            let self_ = *self;
            move || {
                self_.with_value(|inner| {
                    if let Some(cb) = &inner.on_player_feature {
                        cb(&feat)
                    }
                })
            }
        });
    }
}

#[derive(Clone, Default)]
enum LoadingState {
    #[default]
    Unloaded,
    WaitingForTimeline(ContentConfig),
    Buffering(ContentConfig, Timeline),
    Ready(ContentConfig, Timeline),
}

impl LoadingState {
    fn to_content_state(&self) -> ContentState {
        match self {
            Self::Unloaded => ContentState::Unloaded,
            Self::WaitingForTimeline(..) | Self::Buffering(..) => ContentState::Waiting,
            Self::Ready(..) => ContentState::Ready,
        }
    }

    fn try_to_content_config_ref(&self) -> Option<&ContentConfig> {
        match self {
            Self::WaitingForTimeline(v) | Self::Buffering(v, ..) | Self::Ready(v, ..) => Some(v),
            Self::Unloaded => None,
        }
    }

    fn try_to_timeline_ref(&self) -> Option<&Timeline> {
        match self {
            Self::Buffering(_, v) | Self::Ready(_, v) => Some(v),
            _ => None,
        }
    }
}

#[Composer]
pub fn Player(
    ctx: &AppContext,
    #[into] player_command: MaybeSignal<Option<PlayerCommand>>,
    #[optional=ignx_compositron::player_data::PlayerConfig::default()] player_config: PlayerConfig,
) -> PlayerComposable {
    let player_command = Signal::derive(ctx.scope(), move || player_command.get());
    let player_callbacks = store_value(ctx.scope(), PlayerCallbacks::default());

    // effect simulating the player creation
    // player_ready is an internal signal used to reject any incoming
    // commands when the player is not yet ready
    let player_ready = {
        let player_ready = create_rw_signal(ctx.scope(), false);
        create_effect(ctx.scope(), {
            let ctx = ctx.clone();
            move |_| {
                do_in_ms(&ctx, PLAYER_INITIALIZE_LATENCY_MS, move || {
                    player_ready.set(true);
                    player_callbacks.on_player_ready();
                })
            }
        });
        player_ready.read_only()
    };

    // XP treats PlaybackState as "the clients intended state",
    // which is directly controlled by `Play` and `Pause`.
    //
    // PlaybackState::Playing does not actually mean playing,
    // instead actual playing is:
    //
    // ContentState::Ready & PlaybackState::Playing
    let playback_state = create_rw_signal(ctx.scope(), PlaybackState::Paused);

    let requested_content = create_rw_signal(ctx.scope(), None);

    // For now, we assume there is a single timeline item
    let time_data = create_rw_signal(
        ctx.scope(),
        TimeData {
            unit: TimeUnit::ContentMillis,
            current_position: 0.,
            current_timeline_item_index: 0,
            playable_ranges: HashMap::new(),
            media_wall_clock_utc_time: None,
        },
    );

    // effect handling all player commands
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let Some(player_command) = player_command.get() else {
                return;
            };
            //log::debug!("{P} event: {player_command:?}");

            let player_ready = player_ready.get();
            if !player_ready {
                // all commands are dropped if the player is not ready
                player_callbacks.on_command_error("Player not ready".into());
                return;
            }

            do_in_ms(&ctx, BUS_LATENCY_MS, {
                //let ctx = ctx.clone();
                move || {
                    match player_command {
                        PlayerCommand::Play(_) => {
                            if matches!(playback_state.get_untracked(), PlaybackState::Playing) {
                                return;
                            }
                            playback_state.set(PlaybackState::Playing);
                        }
                        PlayerCommand::Pause => {
                            if matches!(playback_state.get_untracked(), PlaybackState::Paused) {
                                return;
                            }
                            playback_state.set(PlaybackState::Paused);
                        }

                        PlayerCommand::LoadV2(content) => requested_content.set(Some(content)),
                        PlayerCommand::Unload => requested_content.set(None),

                        PlayerCommand::Start(content) => {
                            requested_content.set(Some(content));
                            if matches!(playback_state.get_untracked(), PlaybackState::Playing) {
                                return;
                            }
                            playback_state.set(PlaybackState::Playing);
                        } // PlayerCommand::Seek(current_position, _) => {
                        //     // todo: this should move to `Waiting` before
                        //     // performing the seek
                        //     let time_data_val = time_data.get_untracked();
                        //     time_data.set(TimeData {
                        //         current_position,
                        //         ..time_data_val
                        //     });
                        // }
                        // PlayerCommand::GetFeature(feature_type) => match feature_type {
                        //     PlayerFeatureType::Stream => player_callbacks
                        //         .on_feature(&ctx, PlayerFeature::Stream(StreamFeature {})),
                        //     PlayerFeatureType::VideoPreview => player_callbacks
                        //         .on_command_error("Feature not implemented".to_string()),
                        // },
                        _ => (),
                    };
                }
            });
        }
    });

    // As mentioned, `playback_state` is externalised as-is.
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let player_ready = player_ready.get();
            let playback_state = playback_state.get();

            if !player_ready {
                return;
            };

            player_callbacks
                .on_player_event(&ctx, &PlayerEvent::PlaybackStateChange(playback_state));
        }
    });

    // Mini state machine for LoadingState
    let loading_state = {
        let loading_state = create_rw_signal::<LoadingState>(ctx.scope(), Default::default());
        // Effect for handling inputs (`requested_content`)
        create_effect(ctx.scope(), move |_| {
            let requested_content = requested_content.get();
            let loading_state_val = loading_state.get_untracked();

            // asap behaviour
            let current_content = loading_state_val.try_to_content_config_ref();

            let new_state = match (current_content, requested_content) {
                (Some(_), None) => LoadingState::Unloaded,
                (Some(current_content), Some(requested_content)) => {
                    if current_content == &requested_content {
                        return; // asap behaviour
                    } else {
                        LoadingState::WaitingForTimeline(requested_content)
                    }
                }
                (None, Some(requested_content)) => {
                    LoadingState::WaitingForTimeline(requested_content)
                }
                (None, None) => return,
            };

            loading_state.set(new_state)
        });

        // Effect for handling state entrance
        create_effect(ctx.scope(), {
            let ctx = ctx.clone();
            move |task_id: Option<Option<TaskID>>| {
                let loading_state_val = loading_state.get();

                // clean-up for previous
                if let Some(Some(task_id)) = task_id {
                    ctx.cancel_task(task_id)
                }

                // on enter
                match loading_state_val {
                    LoadingState::WaitingForTimeline(v) => {
                        Some(do_in_ms(&ctx, TIMELINE_LATENCY_MS, {
                            let ctx = ctx.clone();
                            move || {
                                let timeline = sample_timeline(v.title_id.clone());
                                player_callbacks.on_player_event(
                                    &ctx,
                                    &PlayerEvent::TimelineChange(timeline.clone()),
                                );
                                loading_state.set(LoadingState::Buffering(v, timeline));
                            }
                        }))
                    }
                    LoadingState::Buffering(v, timeline) => {
                        Some(do_in_ms(&ctx, BUFFERING_LATENCY_MS, move || {
                            loading_state.set(LoadingState::Ready(v, timeline));
                        }))
                    }
                    _ => None,
                }
            }
        });

        loading_state.read_only()
    };

    // Emit content state events
    let content_state = {
        let content_state = create_rw_signal(
            ctx.scope(),
            loading_state.get_untracked().to_content_state(),
        );

        create_effect(ctx.scope(), {
            let ctx = ctx.clone();
            move |_| {
                let loading_state = loading_state.get();
                let new_content_state = loading_state.to_content_state();
                let current_content_state = content_state.get_untracked();

                if current_content_state != new_content_state {
                    content_state.set(new_content_state.clone());
                    player_callbacks.on_player_event(
                        &ctx,
                        &PlayerEvent::ContentStateChange(Ok(new_content_state)),
                    )
                }
            }
        });

        content_state.read_only()
    };

    // Emit time data events
    let time_data = {
        let task_guard = store_value(ctx.scope(), None);

        create_effect(ctx.scope(), {
            let ctx = ctx.clone();
            move |_| {
                let content_state = content_state.get();
                let playback_state_val = playback_state.get();

                match (content_state, playback_state_val) {
                    (ContentState::Ready, PlaybackState::Playing) => {
                        task_guard.set_value(Some(ctx.schedule_repeating_task(
                            Duration::from_millis(TIME_DATA_PERIOD_MS),
                            move || {
                                let time_data_val = time_data.get_untracked();

                                let LoadingState::Ready(_, timeline) =
                                    loading_state.get_untracked()
                                else {
                                    log::debug!(
                                        "{P} time data task executed without LoadingState::Ready"
                                    );
                                    return;
                                };

                                let Some(item) = timeline.items.first() else {
                                    log::debug!("{P} expecting at least one timeline item");
                                    return;
                                };

                                let target_position =
                                    time_data_val.current_position + TIME_DATA_PERIOD_MS_F;

                                let Some(end_time) = item.end_time else {
                                    let new_time_data = mock_time_data(target_position, 0);
                                    time_data.set(new_time_data);
                                    return;
                                };

                                if target_position > end_time as f64 {
                                    let new_time_data = TimeData {
                                        current_position: end_time as f64,
                                        ..time_data_val
                                    };
                                    time_data.set(new_time_data);
                                    playback_state.set(PlaybackState::Paused);
                                    return;
                                    // ^ todo: should emit TimelineEnded event instead
                                }

                                let new_time_data = mock_time_data(target_position, 0);
                                time_data.set(new_time_data);
                            },
                        )));
                    }
                    _ => task_guard.set_value(None),
                }
            }
        });

        create_effect(ctx.scope(), {
            let ctx = ctx.clone();
            move |_| {
                let player_ready = player_ready.get();
                let time_data = time_data.get();

                if !player_ready {
                    return;
                }
                player_callbacks.on_player_event(&ctx, &PlayerEvent::TimeDataChange(time_data));
            }
        });

        time_data
    };

    let image_data = {
        fn url_to_image_data(url: &str) -> ImageData {
            ImageData {
                url: url.to_string(),
                tags: vec![],
                width: 1920.,
                height: 1080.,
            }
        }

        let images = store_value(ctx.scope(), VecDeque::from([
            "https://m.media-amazon.com/images/S/pv-target-images/d83183f55682e5d36e69b00d4ca776f03f164c5e5e89aab2af32b163af25c257.jpg",
            "https://m.media-amazon.com/images/S/pv-target-images/0ef1a2e4d921eacdac5d1861e08c1b195c9930e528ea90c0b2fff50cca54abae._UR1920,1080_RI_.jpg",
            "https://m.media-amazon.com/images/S/pv-target-images/dca021138a4b3c954f55a44a44f7fb10a0eb67dd68063e24dbb85d579d234d0e.png",
            "https://m.media-amazon.com/images/S/pv-target-images/6837e492b6c53921992e95087ec42a1daee714c1cbfd6b33d4b50813456f4f76.png",
            "https://m.media-amazon.com/images/S/pv-target-images/507d78c85b9339057c1300d65ad2940fb7bc6960d3ea5594d69f671a25b2d084.jpg",
            "https://m.media-amazon.com/images/G/01/BrowseCom/test/grandturismo.jpg"
        ]));

        let image_data = create_rw_signal(ctx.scope(), None);

        let time_data_counter = create_rw_signal(ctx.scope(), 0);

        create_effect(ctx.scope(), move |_| {
            time_data.with(|_| {
                time_data_counter.update(|v| *v += 1);
            })
        });

        create_effect(ctx.scope(), move |prev_index| {
            let time_data_counter = time_data_counter.get();
            let current_index = (time_data_counter / 6) % 6;
            let image_data_val = image_data.get_untracked();

            if Some(current_index) != prev_index || image_data_val.is_none() {
                image_data.set(Some(url_to_image_data(
                    #[allow(clippy::unwrap_used, reason = "fake player not in prod")]
                    images.get_value().get(current_index).unwrap(),
                )));
            }

            current_index
        });

        create_effect(ctx.scope(), move |_| {
            content_state.with(|_| {
                image_data.set(None);
            });
        });

        image_data.read_only()
    };

    let content_state_display = Signal::derive(ctx.scope(), move || {
        let content_state = content_state.get();
        format!(
            "ContentState: {}",
            match content_state {
                ContentState::Ready => "Ready",
                ContentState::Waiting => "Waiting",
                ContentState::Unloaded => "Unloaded",
            }
        )
    });

    let playback_state_display = Signal::derive(ctx.scope(), move || {
        let playback_state = playback_state.get();
        format!(
            "PlaybackState: {}",
            match playback_state {
                PlaybackState::Playing => "Playing",
                PlaybackState::Paused => "Paused",
            }
        )
    });

    let pos_display = Signal::derive(ctx.scope(), move || {
        let time_data = time_data.get();
        let loading_state = loading_state.get();

        let timeline = loading_state.try_to_timeline_ref();
        let total_str = match timeline {
            Some(timeline) => timeline
                .items
                .first()
                .and_then(|v| v.end_time.map(|v| format!("{}", v)))
                .unwrap_or_else(|| "?".to_string()),
            None => "?".to_string(),
        };

        let current_str = format!("{}", time_data.current_position);

        format!("Position: {current_str}/{total_str}")
    });

    let image_builder = Box::new(move |ctx: &AppContext| {
        let image_data = image_data.get();

        match image_data {
            Some(image_data) => compose_option! {
                LRCImage(data: image_data)
            },
            None => None,
        }
    });

    PlayerComposable {
        scope: ctx.scope(),
        player_callbacks,
        composable: compose! {
            Stack() {
                Memo(item_builder: image_builder)
                Column() {
                    Column() {
                        TypographyLabel100(content: content_state_display)
                        TypographyLabel100(content: playback_state_display)
                        TypographyLabel100(content: pos_display)
                    }
                    .padding(Padding::all(32.))
                    .background_color(Color::new(24,30,38,204))
                    .border_radius(36.)
                }
                .main_axis_alignment(MainAxisAlignment::Center)
                .height(1080.)
                .padding(Padding::horizontal(32.))
            }
        },
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::time::{Duration, MockClock};

    //#[test]
    //fn should_invoke_feature_cb() {
    //    launch_test(
    //        |ctx| {
    //            let player_command = create_rw_signal(ctx.scope(), None);
    //            let feature = store_value(ctx.scope(), None);
    //            let on_feature = move |f| {
    //                feature.set_value(Some(f));
    //            };
    //            provide_context(ctx.scope(), player_command.clone());
    //            provide_context(ctx.scope(), feature);
    //            compose! { Player(player_command).test_id("player").on_feature(on_feature) }
    //        },
    //        |scope, mut gl| {
    //            gl.tick_until_done();
    //            let player_command = expect_context::<RwSignal<Option<PlayerCommand>>>(scope);
    //            let feature = expect_context::<StoredValue<Option<PlayerFeature>>>(scope);

    //            gl.advance_time(Duration::from_millis(PLAYER_INITIALIZE_LATENCY_MS));
    //            gl.tick_until_done();

    //            player_command.set(Some(PlayerCommand::GetFeature(PlayerFeatureType::Stream)));

    //            gl.tick_until_done();

    //            gl.advance_time(Duration::from_millis(BUS_LATENCY_MS));
    //            gl.tick_until_done();

    //            // takes a round trip, two bus latencies
    //            feature.with_value(|v| assert!(v.is_none()));

    //            gl.advance_time(Duration::from_millis(BUS_LATENCY_MS));
    //            gl.tick_until_done();

    //            feature.with_value(|v| assert!(matches!(v, Some(PlayerFeature::Stream(_)))));
    //        },
    //    )
    //}

    // #[test]
    // fn should_invoke_command_error() {
    //     launch_test(
    //         |ctx| {
    //             let player_command = create_rw_signal(ctx.scope(), None);
    //             let err = store_value(ctx.scope(), None);
    //             let on_command_error = move |e| {
    //                 err.set_value(Some(e));
    //             };
    //             provide_context(ctx.scope(), player_command.clone());
    //             provide_context(ctx.scope(), err);
    //             compose! { Player(player_command).test_id("player").on_command_error(on_command_error) }
    //         },
    //         |scope, mut gl| {
    //             gl.tick_until_done();
    //             let player_command = expect_context::<RwSignal<Option<PlayerCommand>>>(scope);
    //             let err = expect_context::<StoredValue<Option<String>>>(scope);

    //             player_command.set(Some(PlayerCommand::GetFeature(
    //                 PlayerFeatureType::VideoPreview,
    //             )));

    //             err.with_value(|v| assert_eq!(v, &Some("Player not ready".into())));
    //             err.set_value(None);

    //             gl.advance_time(Duration::from_millis(PLAYER_INITIALIZE_LATENCY_MS));
    //             gl.tick_until_done();

    //             player_command.set(Some(PlayerCommand::GetFeature(
    //                 PlayerFeatureType::VideoPreview,
    //             )));

    //             gl.advance_time(Duration::from_millis(BUS_LATENCY_MS - 1));
    //             gl.tick_until_done();

    //             err.with_value(|v| assert!(v.is_none()));

    //             gl.advance_time(Duration::from_millis(1));
    //             gl.tick_until_done();

    //             err.with_value(|v| assert_eq!(v, &Some("Feature not implemented".into())));
    //         },
    //     )
    // }

    #[test]
    fn on_ready_is_invoked() {
        launch_test(
            |ctx| {
                let player_command = create_rw_signal(ctx.scope(), None);
                let ready_called = store_value(ctx.scope(), false);
                provide_context(ctx.scope(), ready_called);
                let on_player_ready = move || {
                    ready_called.set_value(true);
                };

                compose! {
                    Player(player_command)
                        .on_player_ready(on_player_ready)
                }
            },
            |scope, mut gl| {
                let ready_called = expect_context::<StoredValue<bool>>(scope);
                gl.tick_until_done();

                assert_eq!(ready_called.get_value(), false);

                MockClock::advance(Duration::from_millis(PLAYER_INITIALIZE_LATENCY_MS - 1));
                gl.tick_until_done();

                assert_eq!(ready_called.get_value(), false);

                MockClock::advance(Duration::from_millis(1));
                gl.tick_until_done();

                assert_eq!(ready_called.get_value(), true);
            },
        )
    }
}
