use crate::{PlayerEvent, PlayerFeature};
use ignx_compositron::{
    composable::BaseAttributeRef,
    prelude::*,
    reactive::StoredValue,
    widgets::{Widget, WidgetPod},
};

#[derive(Default)]
pub struct PlayerCallbacks {
    pub on_player_event: Option<Box<dyn Fn(&PlayerEvent)>>,
    pub on_player_ready: Option<Box<dyn Fn()>>,
    pub on_command_error: Option<Box<dyn Fn(String)>>,
    pub on_player_feature: Option<Box<dyn Fn(&PlayerFeature)>>,
}

pub struct PlayerComposable {
    pub composable: StackComposable,
    pub scope: Scope,
    pub player_callbacks: StoredValue<PlayerCallbacks>,
}

impl BaseAttributeRef for PlayerComposable {
    fn base_style_attrs(&mut self) -> &mut Option<ignx_compositron::BaseStyleAttributes> {
        self.composable.base_style_attrs()
    }

    fn layout_attrs(&mut self) -> &mut Option<ignx_compositron::LayoutAttributes> {
        self.composable.layout_attrs()
    }
}

impl IntoWidget for PlayerComposable {
    fn into_widget(self) -> WidgetPod<dyn Widget> {
        self.composable.into_widget()
    }
}

impl Composable<'static> for PlayerComposable {
    type Target = <StackComposable as Composable<'static>>::Target;

    #[allow(deprecated, reason = "Legacy SDK compatibility")]
    fn widget(&self) -> &WidgetPod<Self::Target> {
        self.composable.widget()
    }

    fn widget_ref(&self) -> &WidgetPod<Self::Target> {
        self.composable.widget_ref()
    }

    fn widget_mut(&mut self) -> &mut WidgetPod<Self::Target> {
        self.composable.widget_mut()
    }

    fn scope(&self) -> Scope {
        self.scope
    }
}

impl PlayerComposable {
    pub fn on_player_event(self, cb: impl Fn(&PlayerEvent) + 'static) -> Self {
        self.player_callbacks
            .update_value(|cbs| cbs.on_player_event = Some(Box::new(cb)));
        self
    }

    pub fn on_player_ready(self, cb: impl Fn() + 'static) -> Self {
        self.player_callbacks
            .update_value(|cbs| cbs.on_player_ready = Some(Box::new(cb)));
        self
    }

    pub fn on_command_error(self, cb: impl Fn(String) + 'static) -> Self {
        self.player_callbacks
            .update_value(|cbs| cbs.on_command_error = Some(Box::new(cb)));
        self
    }

    pub fn on_player_feature(self, cb: impl Fn(&PlayerFeature) + 'static) -> Self {
        self.player_callbacks
            .update_value(|cbs| cbs.on_player_feature = Some(Box::new(cb)));
        self
    }
}
