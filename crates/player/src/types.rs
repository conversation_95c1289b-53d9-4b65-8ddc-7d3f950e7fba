pub use ignx_compositron::player_data::{
    AcquisitionPerformance, AcquisitionRequest, AcquisitionResponse, AdFreeSignupInPlaybackData,
    AdInteractionEvent, AdMetadata, AdSystem, AssetType, AudioStream, AudioStreamMatcher,
    AudioType, AuxiliaryMetadata, ContentConfigv2 as ContentConfig, ContentInfo, ContentInfoData,
    ContentState, DiagnosticsSubscribeEvent, ElementNode, EncodePreferences,
    FeatureContentMetadata, FeatureFlagState, FeatureFlagType, FeatureMethodError, HdrFormat,
    InteractionCause, InteractionInput, InteractionNavigation, InteractionSource, InteractionType,
    InteractionUIClick, InterfaceComponent, InterfaceSource, InterfaceState, LiveAds,
    MetricErrorSource, MetricErrorType, Node, PauseBehavior, PictureMode, PinRestrictions,
    PlayOptions, PlayableRange, PlaybackAction, PlaybackEnvelope, PlaybackState,
    PlayerCapabilities, PlayerCapabilityType, PlayerConfig, PlayerFeatureType, RatingType,
    RecapMetadata, RemoteActionType, RemoteTransportType, Resolution, SkipElement, Stream,
    StreamRestriction, StreamSubscribeEvent, StreamType, StreamingTechnology, TextNode, TimeData,
    TimeUnit, TimedTextFormat, TimedTextStream, TimedTextStreamData, TimedTextSubType,
    TimedTextType, TimedTextVariant, Timeline, TimelineItem, TransitionTimecodes,
    VideoMaterialType, VideoPreviewAsset, VideoPreviewAssetQuality, ViewportMode, XpError,
};

#[cfg(any(test, feature = "test_utils"))]
pub mod test_utils {
    use super::*;
    use ignx_compositron::player_data::{
        AuxiliaryContentSubType, ContentConfigv2, ElementNode, VideoPreviewAssetFormat,
    };
    use std::collections::HashMap;

    ///
    /// This lets us easily build up mocks for `TimeData`, and avoid
    /// boilerplate code in tests.
    ///
    /// ```ignore
    /// fn on_time_data_change(_: &TimeData) {}
    ///
    /// fn my_test() {
    ///     let time_data_builder = || {
    ///         time_data_builder()
    ///             .current_timeline_item_index(0)
    ///             .unit(TimeUnit::ContentMillis)
    ///     };
    ///
    ///     on_time_data_change(&time_data_builder().current_position(0).build());
    ///     on_time_data_change(&time_data_builder().current_position(5).build());
    ///
    ///     let time_data = || time_data_builder().current_timeline_item_index(1);
    ///     on_time_data_change(&time_data().current_position(0).build());
    ///     on_time_data_change(&time_data().current_position(5).build());
    ///     on_time_data_change(&time_data().current_position(10).build());
    /// }
    /// ```
    ///
    #[derive(Clone, Default)]
    pub struct TimeDataBuilder {
        current_position: Option<f64>,
        unit: Option<TimeUnit>,
        current_timeline_item_index: Option<u64>,
        playable_ranges: HashMap<u64, (Option<f64>, Option<f64>)>,
    }

    impl TimeDataBuilder {
        pub fn current_position(mut self, current_position: f64) -> Self {
            self.current_position = Some(current_position);
            self
        }

        pub fn unit(mut self, unit: TimeUnit) -> Self {
            self.unit = Some(unit);
            self
        }

        pub fn current_timeline_item_index(mut self, current_timeline_item_index: u64) -> Self {
            self.current_timeline_item_index = Some(current_timeline_item_index);
            self
        }

        pub fn add_playable_range(
            mut self,
            index: u64,
            start: Option<f64>,
            end: Option<f64>,
        ) -> Self {
            self.playable_ranges.insert(index, (start, end));
            self
        }

        pub fn add_playable_ranges(
            mut self,
            ranges: impl IntoIterator<Item = (u64, Option<f64>, Option<f64>)>,
        ) -> Self {
            for range in ranges.into_iter() {
                self = self.add_playable_range(range.0, range.1, range.2);
            }
            self
        }

        pub fn build(self) -> TimeData {
            let unit = self.unit.unwrap_or(TimeUnit::EpochMillis);
            TimeData {
                current_position: self.current_position.unwrap_or(0.),
                unit: unit.clone(),
                current_timeline_item_index: self.current_timeline_item_index.unwrap_or(0),
                media_wall_clock_utc_time: None,
                playable_ranges: self
                    .playable_ranges
                    .into_iter()
                    .map(|(k, v)| {
                        (
                            k.to_string(),
                            PlayableRange {
                                unit: unit.clone(),
                                start: v.0,
                                end: v.1,
                            },
                        )
                    })
                    .collect(),
            }
        }
    }

    impl From<TimeDataBuilder> for TimeData {
        fn from(value: TimeDataBuilder) -> Self {
            value.build()
        }
    }

    pub trait TimeDataBuildable {
        fn builder() -> TimeDataBuilder;
    }

    impl TimeDataBuildable for TimeData {
        fn builder() -> TimeDataBuilder {
            TimeDataBuilder::default()
        }
    }

    #[derive(Clone, Default)]
    pub struct TimelineBuilder {
        items: Vec<TimelineItem>,
        content_infos: HashMap<String, ContentInfo>,
    }

    impl TimelineBuilder {
        pub fn add_item(mut self, timeline_item: impl Into<TimelineItem>) -> Self {
            self.items.push(timeline_item.into());
            self
        }

        pub fn add_content_info(
            mut self,
            content_id: impl Into<String>,
            content_info: impl Into<ContentInfo>,
        ) -> Self {
            let content_id = content_id.into();
            if self.content_infos.contains_key(&content_id) {
                panic!("Content id '{:?}' already exists", content_id)
            }
            self.content_infos.insert(content_id, content_info.into());
            self
        }

        pub fn build(self) -> Timeline {
            let items_with_missing_content_ids: Vec<_> = self
                .items
                .iter()
                .filter(|item| self.content_infos.keys().all(|k| *k != item.content_id))
                .collect();

            if !items_with_missing_content_ids.is_empty() {
                panic!(
                    "The following timeline item(s) are missing content infos: {:?}",
                    items_with_missing_content_ids
                )
            }

            Timeline {
                content_infos: self.content_infos,
                items: self.items,
            }
        }

        pub fn unsafe_build(self) -> Timeline {
            Timeline {
                content_infos: self.content_infos,
                items: self.items,
            }
        }
    }

    impl From<TimelineBuilder> for Timeline {
        fn from(value: TimelineBuilder) -> Self {
            value.build()
        }
    }

    pub struct TimelineSamples;

    impl TimelineSamples {
        ///  A basic timeline with
        ///  - 1 feature
        pub fn basic_feature(&self) -> Timeline {
            Timeline::builder()
                .add_content_info(
                    "feature",
                    ContentInfo::builder().feature_metadata(None).build(),
                )
                .add_item(
                    TimelineItem::builder()
                        .content_id("feature".to_string())
                        .start_time(0)
                        .end_time(40000),
                )
                .build()
        }

        ///  A simple timeline with
        ///  - 2 ads
        ///  - 2 aux
        ///  - 1 feature (with BIF Video Preview asset)
        ///  - 1 ads
        ///  - 1 feature
        pub fn ads_and_aux(&self) -> Timeline {
            let item = || TimelineItem::builder();
            let feature = ContentInfo::builder()
                .feature_metadata(None)
                .add_video_preview_asset(
                    VideoPreviewAsset {
                        urls: vec![
                            "https://cf-trickplay.aux.pv-cdn.net/6b0b/0af4/1ba5/43fa-9e3f-37cb8db7177c/5477ec86-f592-4f09-a801-e87fecd10e12_240w.bif".to_string()
                        ],
                        quality: VideoPreviewAssetQuality::Medium,
                        format: VideoPreviewAssetFormat::Bif
                });
            let aux = ContentInfo::builder().aux_metadata(AuxiliaryMetadata::builder());
            let ad = ContentInfo::builder().ad_metadata(AdMetadata::builder());
            let ad_with_watchlist = ContentInfo::builder().ad_metadata(
                AdMetadata::builder()
                    .skip_offset(0)
                    .add_custom_click("watchlist"),
            );

            Timeline::builder()
                .add_content_info("ad-1", ad_with_watchlist.build())
                .add_content_info("ad-2", ad.clone().build())
                .add_content_info("ad-3", ad.build())
                .add_content_info("feature", feature.build())
                .add_content_info("aux-1", aux.clone().build())
                .add_content_info("aux-2", aux.build())
                .add_item(item().content_id("ad-1").start_time(0).end_time(10_000))
                .add_item(item().content_id("ad-2").start_time(0).end_time(10_000))
                .add_item(item().content_id("aux-1").start_time(0).end_time(10_000))
                .add_item(item().content_id("aux-2").start_time(0).end_time(20_000))
                .add_item(item().content_id("feature").start_time(0).end_time(40_000))
                .add_item(item().content_id("ad-3").start_time(0).end_time(1000))
                .add_item(
                    item()
                        .content_id("feature")
                        .start_time(40_000)
                        .end_time(80_000),
                )
                .build()
        }

        ///  A simple timeline with
        ///  - 2 ads
        ///  - 2 aux
        ///  - 1 feature (without BIF Video Preview assets)
        ///  - 1 ads
        ///  - 1 feature
        pub fn ads_and_aux_no_images(&self) -> Timeline {
            let item = || TimelineItem::builder();
            let feature = ContentInfo::builder().feature_metadata(None);
            let aux = ContentInfo::builder().aux_metadata(AuxiliaryMetadata::builder());
            let ad = ContentInfo::builder().ad_metadata(AdMetadata::builder());
            let ad_with_watchlist = ContentInfo::builder().ad_metadata(
                AdMetadata::builder()
                    .skip_offset(0)
                    .add_custom_click("watchlist"),
            );

            Timeline::builder()
                .add_content_info("ad-1", ad_with_watchlist.build())
                .add_content_info("ad-2", ad.clone().build())
                .add_content_info("ad-3", ad.build())
                .add_content_info("feature", feature.build())
                .add_content_info("aux-1", aux.clone().build())
                .add_content_info("aux-2", aux.build())
                .add_item(item().content_id("ad-1").start_time(0).end_time(10_000))
                .add_item(item().content_id("ad-2").start_time(0).end_time(10_000))
                .add_item(item().content_id("aux-1").start_time(0).end_time(10_000))
                .add_item(item().content_id("aux-2").start_time(0).end_time(20_000))
                .add_item(item().content_id("feature").start_time(0).end_time(40_000))
                .add_item(item().content_id("ad-3").start_time(0).end_time(1000))
                .add_item(
                    item()
                        .content_id("feature")
                        .start_time(40_000)
                        .end_time(80_000),
                )
                .build()
        }

        pub fn aux_recap_and_feature_with_intro(&self) -> Timeline {
            let item = || TimelineItem::builder();
            let feature = ContentInfo::builder()
                .feature_metadata(None)
                .transition_timecodes(TransitionTimecodes {
                    outro_credits_start: 70000 as _,
                    auto_play_timer: None,
                    autoplay_settings: None,
                    skip_elements: Some(vec![SkipElement {
                        button_showtime_ms: 7000.,
                        element_type: "INTRO".to_string(),
                        start_timecode_ms: 2000.,
                        end_timecode_ms: 12000.,
                    }]),
                    mature_content_elements: None,
                });
            let aux = ContentInfo::builder().aux_metadata(
                AuxiliaryMetadata::builder()
                    .content_sub_type(AuxiliaryContentSubType::PreviouslyOn)
                    .skip_offset(0)
                    .button_show_time_ms(7_000.),
            );

            Timeline::builder()
                .add_content_info("aux", aux.clone().build())
                .add_content_info("feature", feature.build())
                .add_item(item().content_id("aux").start_time(0).end_time(20_000))
                .add_item(item().content_id("feature").start_time(0).end_time(100_000))
                .build()
        }

        // Built from a real Brasileiro live event timeline
        pub fn live_event(&self, start_end_time: Option<(u64, u64)>) -> Timeline {
            let start_end_time = start_end_time.unwrap_or((1747607401676, 1747632601676));

            Timeline::builder()
                .add_content_info(
                    "live-event-gti_main",
                    ContentInfo::builder()
                        .title_id("live-event-gti")
                        .feature_metadata(None),
                )
                .add_item(
                    TimelineItem::builder()
                        .time_unit(TimeUnit::EpochMillis)
                        .start_time(start_end_time.0)
                        .end_time(start_end_time.1)
                        .content_id("live-event-gti_main")
                        .group_id("live-event-gti"),
                )
                .build()
        }

        pub fn linear(&self, start_end_time: Option<(u64, u64)>) -> Timeline {
            let start_end_time = start_end_time.unwrap_or((1747607401676, 1747632601676));

            Timeline::builder()
                .add_content_info(
                    "linear-gti_main",
                    ContentInfo::builder()
                        .title_id("linear-gti")
                        .feature_metadata(None),
                )
                .add_item(
                    TimelineItem::builder()
                        .time_unit(TimeUnit::EpochMillis)
                        .start_time(start_end_time.0)
                        .end_time(start_end_time.1)
                        .content_id("linear-gti_main")
                        .group_id("linear-gti"),
                )
                .build()
        }
    }

    pub trait TimelineTestUtils {
        fn builder() -> TimelineBuilder;
        fn samples() -> TimelineSamples;
        /// Generates playable ranges as if all content in the timeline is
        /// playable.
        fn full_playable_ranges(&self) -> Vec<(u64, Option<f64>, Option<f64>)>;
    }

    impl TimelineTestUtils for Timeline {
        fn builder() -> TimelineBuilder {
            TimelineBuilder::default()
        }

        fn samples() -> TimelineSamples {
            TimelineSamples
        }

        fn full_playable_ranges(&self) -> Vec<(u64, Option<f64>, Option<f64>)> {
            self.items
                .iter()
                .enumerate()
                .map(|(i, v)| {
                    (
                        i as u64,
                        v.start_time.map(|v| v as f64),
                        v.end_time.map(|v| v as f64),
                    )
                })
                .collect()
        }
    }

    #[derive(Clone, Default)]
    pub struct TimelineItemBuilder {
        time_unit: Option<TimeUnit>,
        start_time: Option<u64>,
        end_time: Option<u64>,
        error: Option<String>,
        content_id: Option<String>,
        group_id: Option<String>,
    }

    impl TimelineItemBuilder {
        pub fn time_unit(mut self, time_unit: TimeUnit) -> Self {
            self.time_unit = Some(time_unit);
            self
        }

        pub fn start_time(mut self, start_time: u64) -> Self {
            self.start_time = Some(start_time);
            self
        }

        pub fn end_time(mut self, end_time: u64) -> Self {
            self.end_time = Some(end_time);
            self
        }

        pub fn error(mut self, error: impl Into<String>) -> Self {
            self.error = Some(error.into());
            self
        }

        pub fn content_id(mut self, content_id: impl Into<String>) -> Self {
            self.content_id = Some(content_id.into());
            self
        }

        pub fn group_id(mut self, group_id: impl Into<String>) -> Self {
            self.group_id = Some(group_id.into());
            self
        }

        pub fn build(self) -> TimelineItem {
            TimelineItem {
                time_unit: self.time_unit.unwrap_or(TimeUnit::ContentMillis),
                start_time: self.start_time.map(|v| v as _),
                end_time: self.end_time.map(|v| v as _),
                error: self.error,
                content_id: self.content_id.unwrap_or_else(|| "content_id".to_string()),
                group_id: self.group_id.unwrap_or_else(|| "group_id".to_string()),
            }
        }
    }

    impl From<TimelineItemBuilder> for TimelineItem {
        fn from(value: TimelineItemBuilder) -> Self {
            value.build()
        }
    }

    pub trait TimelineItemBuildable {
        fn builder() -> TimelineItemBuilder;
    }

    impl TimelineItemBuildable for TimelineItem {
        fn builder() -> TimelineItemBuilder {
            TimelineItemBuilder::default()
        }
    }

    impl From<AuxiliaryMetadataBuilder> for AuxiliaryMetadata {
        fn from(value: AuxiliaryMetadataBuilder) -> Self {
            value.build()
        }
    }

    pub trait AuxiliaryMetadataBuildable {
        fn builder() -> AuxiliaryMetadataBuilder;
    }

    impl AuxiliaryMetadataBuildable for AuxiliaryMetadata {
        fn builder() -> AuxiliaryMetadataBuilder {
            AuxiliaryMetadataBuilder::default()
        }
    }

    #[derive(Default, Clone)]
    pub struct AuxiliaryMetadataBuilder {
        show_countdown_timer: Option<bool>,
        skip_offset: Option<u64>,
        show_skip_countdown_timer: Option<bool>,
        button_show_time_ms: Option<f64>,
        extensions: Option<Vec<ElementNode>>,
        content_sub_type: Option<AuxiliaryContentSubType>,
    }

    impl AuxiliaryMetadataBuilder {
        pub fn show_countdown_timer(mut self, show_countdown_timer: bool) -> Self {
            self.show_countdown_timer = Some(show_countdown_timer);
            self
        }

        pub fn skip_offset(mut self, skip_offset: u64) -> Self {
            self.skip_offset = Some(skip_offset);
            self
        }

        pub fn show_skip_countdown_timer(mut self, show_skip_countdown_timer: bool) -> Self {
            self.show_skip_countdown_timer = Some(show_skip_countdown_timer);
            self
        }

        pub fn button_show_time_ms(mut self, button_show_time_ms: f64) -> Self {
            self.button_show_time_ms = Some(button_show_time_ms);
            self
        }

        pub fn add_extension(mut self, node: ElementNode) -> Self {
            self.extensions.get_or_insert_default().push(node);
            self
        }

        pub fn content_sub_type(mut self, content_sub_type: AuxiliaryContentSubType) -> Self {
            self.content_sub_type = Some(content_sub_type);
            self
        }

        pub fn build(self) -> AuxiliaryMetadata {
            AuxiliaryMetadata {
                show_countdown_timer: self.show_countdown_timer.unwrap_or(true),
                content_sub_type: self.content_sub_type,
                skip_offset: self.skip_offset.map(|v| v as _),
                show_skip_countdown_timer: self.show_skip_countdown_timer,
                button_show_time_ms: self.button_show_time_ms,
                extensions: self.extensions,
            }
        }
    }

    #[derive(Clone)]
    enum ContentMetadata {
        Feature(FeatureContentMetadata),
        Ad(AdMetadata),
        Recap(RecapMetadata),
        Auxiliary(AuxiliaryMetadata),
    }

    #[derive(Default, Clone)]
    pub struct ContentInfoBuilder {
        title_id: Option<String>,
        video_preview_assets: Vec<VideoPreviewAsset>,
        transition_timecodes: Option<TransitionTimecodes>,
        pin_restrictions: Option<PinRestrictions>,
        channel_name: Option<String>,
        pause_behavior: Option<PauseBehavior>,
        live_ads: Option<Vec<LiveAds>>,
        xray_metadata: String,
        metadata: Option<ContentMetadata>,
    }

    impl ContentInfoBuilder {
        pub fn title_id(mut self, title_id: impl Into<String>) -> Self {
            self.title_id = Some(title_id.into());
            self
        }

        pub fn add_video_preview_asset(mut self, video_preview_asset: VideoPreviewAsset) -> Self {
            self.video_preview_assets.push(video_preview_asset);
            self
        }

        pub fn transition_timecodes(mut self, transition_timecodes: TransitionTimecodes) -> Self {
            self.transition_timecodes = Some(transition_timecodes);
            self
        }

        pub fn pin_restrictions(mut self, pin_restrictions: PinRestrictions) -> Self {
            self.pin_restrictions = Some(pin_restrictions);
            self
        }

        pub fn channel_name(mut self, channel_name: impl Into<String>) -> Self {
            self.channel_name = Some(channel_name.into());
            self
        }

        pub fn pause_behavior(mut self, pause_behavior: PauseBehavior) -> Self {
            self.pause_behavior = Some(pause_behavior);
            self
        }

        pub fn add_live_ads(mut self, live_ads: LiveAds) -> Self {
            self.live_ads.get_or_insert_default().push(live_ads);
            self
        }

        pub fn xray_metadata(mut self, xray_metadata: String) -> Self {
            self.xray_metadata = xray_metadata;
            self
        }

        pub fn feature_metadata(mut self, metadata: Option<FeatureContentMetadata>) -> Self {
            self.metadata = Some(ContentMetadata::Feature(metadata.unwrap_or_default()));
            self
        }

        pub fn ad_metadata(mut self, ad_metadata: impl Into<AdMetadata>) -> Self {
            self.metadata = Some(ContentMetadata::Ad(ad_metadata.into()));
            self
        }

        pub fn aux_metadata(mut self, aux_metadata: impl Into<AuxiliaryMetadata>) -> Self {
            self.metadata = Some(ContentMetadata::Auxiliary(aux_metadata.into()));
            self
        }

        pub fn recap_metadata(mut self, recap_metadata: impl Into<RecapMetadata>) -> Self {
            self.metadata = Some(ContentMetadata::Recap(recap_metadata.into()));
            self
        }

        pub fn build(self) -> ContentInfo {
            let content_info_data = ContentInfoData {
                title_id: self.title_id.unwrap_or_else(|| "title_id".to_string()),
                video_preview_assets: self.video_preview_assets,
                channel_name: self.channel_name,
                pause_behavior: self.pause_behavior,
                pin_restrictions: self.pin_restrictions,
                transition_timecodes: self.transition_timecodes,
                live_ads: self.live_ads,
                xray_metadata: Some(self.xray_metadata),
            };
            match self
                .metadata
                .expect("metadata must be specified (feature_metadata, ad_metadata, etc.)")
            {
                ContentMetadata::Feature(v) => ContentInfo::Feature(v, content_info_data),
                ContentMetadata::Ad(v) => ContentInfo::Advertisement(v, content_info_data),
                ContentMetadata::Recap(v) => ContentInfo::Recap(v, content_info_data),
                ContentMetadata::Auxiliary(v) => ContentInfo::Auxiliary(v, content_info_data),
            }
        }
    }

    impl From<ContentInfoBuilder> for ContentInfo {
        fn from(value: ContentInfoBuilder) -> Self {
            value.build()
        }
    }

    pub trait ContentInfoBuildable {
        fn builder() -> ContentInfoBuilder;
    }

    impl ContentInfoBuildable for ContentInfo {
        fn builder() -> ContentInfoBuilder {
            ContentInfoBuilder::default()
        }
    }

    #[derive(Default, Clone)]
    pub struct ContentConfigBuilder {
        title_id: Option<String>,
        video_material_type: Option<VideoMaterialType>,
        position: Option<u32>,
        is_autoplay_session: Option<bool>,
        accepted_creative_apis: Option<Vec<u32>>,
    }

    impl ContentConfigBuilder {
        pub fn title_id(mut self, title_id: impl Into<String>) -> Self {
            self.title_id = Some(title_id.into());
            self
        }

        pub fn video_material_type(mut self, video_material_type: VideoMaterialType) -> Self {
            self.video_material_type = Some(video_material_type);
            self
        }

        pub fn position(mut self, position: u32) -> Self {
            self.position = Some(position);
            self
        }

        pub fn is_autoplay_session(mut self, is_autoplay_session: bool) -> Self {
            self.is_autoplay_session = Some(is_autoplay_session);
            self
        }

        pub fn add_accepted_creative_api(mut self, accepted_creative_api: u32) -> Self {
            if let Some(ref mut accepted_creative_apis) = self.accepted_creative_apis {
                accepted_creative_apis.push(accepted_creative_api);
            } else {
                self.accepted_creative_apis = Some(vec![accepted_creative_api]);
            }
            self
        }

        pub fn build(self) -> ContentConfig {
            ContentConfig {
                title_id: self.title_id.unwrap_or_else(|| "title_id".to_string()),
                video_material_type: self
                    .video_material_type
                    .unwrap_or(VideoMaterialType::Feature),
                position: self.position,
                is_autoplay_session: self.is_autoplay_session,
                accepted_creative_apis: self.accepted_creative_apis,
                ..Default::default()
            }
        }
    }

    // impl ContentConfig {
    //     pub fn builder() -> ContentConfigBuilder {
    //         ContentConfigBuilder::default()
    //     }
    // }

    pub trait ContentConfigv2Builder {
        fn builder() -> ContentConfigBuilder;
    }

    impl ContentConfigv2Builder for ContentConfigv2 {
        fn builder() -> ContentConfigBuilder {
            ContentConfigBuilder::default()
        }
    }

    #[derive(Default, Clone)]
    pub struct AdMetadataBuilder {
        ad_system: Option<AdSystem>,
        ad_instance_id: Option<String>,
        show_countdown_timer: Option<bool>,
        button_show_time_ms: Option<f64>,
        skip_offset: Option<u64>,
        show_skip_countdown_timer: Option<bool>,
        click_through_uri: Option<String>,
        custom_clicks: Option<Vec<String>>,
        creative_id: Option<String>,
    }

    impl AdMetadataBuilder {
        pub fn ad_system(mut self, ad_system: AdSystem) -> Self {
            self.ad_system = Some(ad_system);
            self
        }

        pub fn ad_instance_id(mut self, ad_instance_id: impl Into<String>) -> Self {
            self.ad_instance_id = Some(ad_instance_id.into());
            self
        }

        pub fn show_countdown_timer(mut self, show_countdown_timer: bool) -> Self {
            self.show_countdown_timer = Some(show_countdown_timer);
            self
        }

        pub fn button_show_time_ms(mut self, button_show_time_ms: f64) -> Self {
            self.button_show_time_ms = Some(button_show_time_ms);
            self
        }

        pub fn skip_offset(mut self, skip_offset: u64) -> Self {
            self.skip_offset = Some(skip_offset);
            self
        }

        pub fn show_skip_countdown_timer(mut self, show_skip_countdown_timer: bool) -> Self {
            self.show_skip_countdown_timer = Some(show_skip_countdown_timer);
            self
        }

        pub fn click_through_uri(mut self, click_through_ui: impl Into<String>) -> Self {
            self.click_through_uri = Some(click_through_ui.into());
            self
        }

        pub fn add_custom_click(mut self, custom_click: impl Into<String>) -> Self {
            if let Some(ref mut custom_clicks) = self.custom_clicks {
                custom_clicks.push(custom_click.into());
            } else {
                self.custom_clicks = Some(vec![custom_click.into()]);
            }
            self
        }

        pub fn creative_id(mut self, creative_id: impl Into<String>) -> Self {
            self.creative_id = Some(creative_id.into());
            self
        }

        pub fn build(self) -> AdMetadata {
            AdMetadata {
                ad_system: self
                    .ad_system
                    .unwrap_or_else(|| AdSystem::builder().build()),
                ad_instance_id: self
                    .ad_instance_id
                    .unwrap_or_else(|| "default_id".to_string()),
                show_countdown_timer: self.show_countdown_timer.unwrap_or(false),
                button_show_time_ms: self.button_show_time_ms,
                skip_offset: self.skip_offset.map(|v| v as _),
                show_skip_countdown_timer: self.show_skip_countdown_timer,
                click_through_uri: self.click_through_uri,
                custom_clicks: self.custom_clicks,
                creative_id: self.creative_id,
                extensions: None,
            }
        }
    }

    pub trait AdMetadataBuildable {
        fn builder() -> AdMetadataBuilder;
    }

    impl AdMetadataBuildable for AdMetadata {
        fn builder() -> AdMetadataBuilder {
            AdMetadataBuilder::default()
        }
    }

    impl From<AdMetadataBuilder> for AdMetadata {
        fn from(value: AdMetadataBuilder) -> Self {
            value.build()
        }
    }
    #[derive(Default, Clone)]
    pub struct AdSystemBuilder {
        name: Option<String>,
        version: Option<String>,
    }

    impl AdSystemBuilder {
        pub fn name(mut self, name: impl Into<String>) -> Self {
            self.name = Some(name.into());
            self
        }

        pub fn version(mut self, version: impl Into<String>) -> Self {
            self.version = Some(version.into());
            self
        }

        pub fn build(self) -> AdSystem {
            AdSystem {
                name: self.name.unwrap_or_else(|| "default_ad_system".to_string()),
                version: self.version,
            }
        }
    }

    pub trait AdSystemBuildable {
        fn builder() -> AdSystemBuilder;
    }

    impl AdSystemBuildable for AdSystem {
        fn builder() -> AdSystemBuilder {
            AdSystemBuilder::default()
        }
    }

    impl From<AdSystemBuilder> for AdSystem {
        fn from(value: AdSystemBuilder) -> Self {
            value.build()
        }
    }
}
