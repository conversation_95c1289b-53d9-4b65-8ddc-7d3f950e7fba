use amzn_fable_tokens::{FableColor, FableSpacing};
use fableous::{
    animations::{FableMotionDuration, FableMotionEasing, MotionEasing},
    utils::get_ignx_color,
    SCREEN_HEIGHT, SCREEN_WIDTH,
};
use ignx_compositron::{compose, Composer};
use ignx_compositron::{memo::*, prelude::*, time::Duration, time::Instant};
use std::rc::Rc;

pub const DEFAULT_WIDTH: f32 = 789.0;

pub fn get_default_padding() -> Padding {
    Padding::new(
        FableSpacing::SPACING267,
        FableSpacing::SPACING333,
        FableSpacing::SPACING333,
        FableSpacing::SPACING333,
    )
}

#[Composer]
pub fn Drawer<C: Composable<'static> + 'static>(
    ctx: &AppContext,
    item_builder: Box<ComposableBuilder<C>>,
    #[into] open: MaybeSignal<bool>,
    #[optional] on_closed: Option<Rc<dyn Fn()>>,
    #[optional = DEFAULT_WIDTH] width: f32,
    #[optional = get_default_padding()] padding: Padding,
) -> StackComposable {
    let (x_position, set_x_position) = create_signal(ctx.scope(), width);

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let on_closed = on_closed.clone();

        move |has_rendered| {
            if open.get() {
                let open_drawer = {
                    let ctx = ctx.clone();
                    move || {
                        ctx.with_animation(
                            Animation::default()
                                .with_duration(FableMotionDuration::Standard.into())
                                .with_interpolation(FableMotionEasing::Enter.to_interpolation()),
                            move || {
                                set_x_position.set(0.0);
                            },
                        );
                    }
                };

                if has_rendered.is_none() {
                    ctx.schedule_task(Instant::now() + Duration::from_millis(1), open_drawer);
                } else {
                    open_drawer();
                }
            } else if has_rendered.is_some() {
                let on_closed = on_closed.clone();
                ctx.with_animation_completion(
                    Animation::default()
                        .with_duration(FableMotionDuration::Medium.into())
                        .with_interpolation(FableMotionEasing::Exit.to_interpolation()),
                    move || {
                        set_x_position.set(width);
                    },
                    move || {
                        if let Some(on_closed) = &on_closed {
                            on_closed();
                        }
                    },
                );
            }
            true
        }
    });

    compose! {
        Stack() {
            Stack() {
                Memo(item_builder)
            }
            .width(width)
            .height(SCREEN_HEIGHT)
            .translate_x(x_position)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
            .padding(padding)
            .test_id("drawer")
            .focus_hierarchical_container(NavigationStrategy::Vertical)
        }
        .alignment(Alignment::EndTop)
        .height(SCREEN_HEIGHT)
        .width(SCREEN_WIDTH)
        .test_id("drawer-wrapper")
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use fableous::typography::typography::*;
    use ignx_compositron::compose_option;
    use ignx_compositron::{app::launch_test, test_utils::assert_node_exists, time::MockClock};
    use rstest::rstest;

    #[rstest]
    #[case(true)]
    #[case(false)]
    pub fn render_initial_state(#[case] open: bool) {
        launch_test(
            move |ctx| {
                compose! { Drawer(
                    item_builder: Box::new(|ctx| {
                        compose_option! {
                            TypographyHeading100(content: "Testing".to_string()).test_id("inner-content")
                        }
                    }),
                    open,
                    on_closed: None
                )}
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let wrapper = tree.find_by_test_id("drawer-wrapper");

                assert_node_exists!(&wrapper);

                let drawer = wrapper.find_child_with().find_first();

                assert_node_exists!(&drawer);
                assert_eq!(drawer.borrow_props().test_id, Some("drawer".to_string()));
                assert_eq!(drawer.borrow_props().layout.position.x, 1920.0);

                let inner_content = wrapper
                    .find_any_child_with()
                    .test_id("inner-content")
                    .find_first();

                assert_node_exists!(&inner_content);
                assert_eq!(
                    inner_content.borrow_props().text,
                    Some("Testing".to_string())
                );
            },
        );
    }

    #[test]
    pub fn render_with_custom_width() {
        launch_test(
            move |ctx| {
                compose! { Drawer(
                    item_builder: Box::new(|ctx| {
                        compose_option! {
                            TypographyHeading100(content: "Testing".to_string()).test_id("inner-content")
                        }
                    }),
                    open: true,
                    on_closed: None,
                    width: 100.0
                )}
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let drawer = tree.find_by_test_id("drawer");

                assert_node_exists!(&drawer);
                assert_eq!(drawer.borrow_props().test_id, Some("drawer".to_string()));
                assert_eq!(drawer.borrow_props().layout.size.width, 100.0);
            },
        );
    }

    #[rstest]
    #[case(true, 1131.0)]
    #[case(false, 1920.0)]
    pub fn open_after_1ms_when_initially_open(
        #[case] open: bool,
        #[case] expected_pos_after_1ms: f32,
    ) {
        launch_test(
            move |ctx| {
                compose! { Drawer(
                    item_builder: Box::new(|ctx| {
                        compose_option! {
                            TypographyHeading100(content: "Testing".to_string()).test_id("inner-content")
                        }
                    }),
                    open,
                    on_closed: None
                )}
            },
            move |_scope, mut test_game_loop| {
                let mut tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1920.0);

                MockClock::advance(Duration::from_millis(1));
                tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(
                    drawer.borrow_props().layout.position.x,
                    expected_pos_after_1ms
                );
            },
        );
    }

    #[test]
    pub fn open_manually_after_rendering_closed() {
        launch_test(
            move |ctx| {
                let open = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), open);

                compose! { Drawer(
                    item_builder: Box::new(|ctx| {
                        compose_option! {
                            TypographyHeading100(content: "Testing".to_string()).test_id("inner-content")
                        }
                    }),
                    open: open.read_only(),
                    on_closed: None
                )}
            },
            move |scope, mut test_game_loop| {
                let mut tree = test_game_loop.tick_until_done();
                let open = use_context::<RwSignal<bool>>(scope).unwrap();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1920.0);

                open.set(true);
                tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1131.0);
            },
        );
    }

    #[test]
    pub fn open_and_close() {
        launch_test(
            move |ctx| {
                let open = create_rw_signal(ctx.scope(), false);
                let (on_closed_called, set_on_closed_called) = create_signal(ctx.scope(), 0);

                provide_context(ctx.scope(), open);
                provide_context(ctx.scope(), on_closed_called);

                compose! { Drawer(
                    item_builder: Box::new(|ctx| {
                        compose_option! {
                            TypographyHeading100(content: "Testing".to_string()).test_id("inner-content")
                        }
                    }),
                    open: open.read_only(),
                    on_closed: Some(Rc::new(move || {
                        set_on_closed_called.set_untracked(on_closed_called.get_untracked() + 1);
                    }))
                )}
            },
            move |scope, mut test_game_loop| {
                let mut tree = test_game_loop.tick_until_done();
                let open = use_context::<RwSignal<bool>>(scope).unwrap();
                let on_closed_called = use_context::<ReadSignal<i32>>(scope).unwrap();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1920.0);

                open.set(true);
                tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1131.0);
                assert_eq!(on_closed_called.get(), 0);

                open.set(false);
                tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1920.0);
                assert_eq!(on_closed_called.get(), 1);

                open.set(true);
                tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1131.0);
                assert_eq!(on_closed_called.get(), 1);

                open.set(false);
                tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id("drawer");
                assert_eq!(drawer.borrow_props().layout.position.x, 1920.0);
                assert_eq!(on_closed_called.get(), 2);
            },
        );
    }
}
