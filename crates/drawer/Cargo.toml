[package]
name = "drawer"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
log.workspace = true
cfg-test-attr-derive.workspace = true
fableous.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
    "mock_timer",
] }
rstest.workspace = true
