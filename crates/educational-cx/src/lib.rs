#[cfg(any(test, feature = "test_utils"))]
use crate::test_utils::MockNetworkClient as NetworkClient;
use app_events::educational_cx::{report_button_clicked, report_page_display};
use common_transform_types::actions::Action;
use common_transform_types::modals::{MarketingModalAttributedButton, MarketingModalData};
use cross_app_events::app_event::AppEventReporter;
use fableous::modals::types::FloatingModalButtonAction;
use fableous::modals::types::{
    FloatingModalButton, FloatingModalData, FloatingModalHeader, FloatingModalTile,
};
#[cfg(not(any(test, feature = "test_utils")))]
use ignx_compositron::network::http::HttpMethod;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
#[cfg(not(any(test, feature = "test_utils")))]
use network::common::{DeviceProxyResponse, LRCEdgeResponse};
#[cfg(not(any(test, feature = "test_utils")))]
use network::{NetworkClient, RequestError, URLBuilder};
use router::hooks::use_navigation;
use serde::Serialize;
use std::collections::HashMap;
use std::rc::Rc;
#[cfg(not(any(test, feature = "test_utils")))]
use std::time::{SystemTime, UNIX_EPOCH};
use transition_executor::Transition;

const EDUCATIONAL_CX_THEME: &str = "EducationalCX";
const EDUCATIONAL_CX_ACTION: &str = "EducationalCX.Action";
const EDUCATIONAL_CX_REQUEST: &str = "EducationalCX.Request";
const REPORT_NOTIFICATION_DISPLAYED_ENDPOINT: &str = "/cdp/usage/ReportCustomerAction";

const CTA_BUTTON_TYPE: &str = "ctabutton";
const DISMISS_BUTTON_TYPE: &str = "dismissbutton";

pub fn get_metadata(
    item_id: Option<String>,
    attributed_buttons: Option<Vec<MarketingModalAttributedButton>>,
) -> Option<HashMap<String, Option<String>>> {
    let mut map: HashMap<String, Option<String>> = HashMap::new();

    if let Some(next_button) = attributed_buttons.and_then(|buttons| buttons.first().cloned()) {
        if let Some(item) = item_id
            .as_deref()
            .and_then(|id| next_button.attributes.get(id))
        {
            let next_ref_marker = item.refMarker.clone();

            map.insert("next_ref_marker".into(), next_ref_marker.clone());

            let dismiss_ref_marker = next_ref_marker.map(|text| text.replace("next", "close"));

            map.insert("dismiss_ref_marker".into(), dismiss_ref_marker);
        }
    };

    Some(map)
}

pub fn generate_edcx_modal(
    ctx: AppContext,
    data: &MarketingModalData,
) -> Option<FloatingModalData> {
    if data
        .theme
        .as_ref()
        .is_none_or(|theme| theme != EDUCATIONAL_CX_THEME)
    {
        return None;
    }

    let network_client = NetworkClient::new(&ctx);

    let notification_id = data.notificationId.clone().unwrap_or_default();
    let client_side_metrics = data.clientSideMetrics.clone().unwrap_or_default();
    let ref_marker = data.refMarker.clone();
    let ref_marker_1 = ref_marker.clone();
    let ref_marker_2 = ref_marker.clone();
    let container_metadata_ref_marker = data.containerMetadataRefMarker.clone();

    let has_multiple_items = data
        .marketingModalItems
        .as_ref()
        .is_some_and(|items| items.len() >= 2);

    let cta_button = data.buttons.as_ref().and_then(|buttons| {
        buttons
            .iter()
            .find(|button| button.button_type.to_lowercase() == CTA_BUTTON_TYPE)
            .map(|button| FloatingModalButton {
                button_type: fableous::modals::types::FloatingModalButtonType::Secondary,
                action: button
                    .action
                    .as_ref()
                    .map_or(FloatingModalButtonAction::Dismiss(None), |action| {
                        generate_cta_button_action(&ctx, action)
                    }),
                text: TextContent::String(button.title.clone()),
                default_focus: false,
            })
    });

    let dismiss_button = data.buttons.as_ref().and_then(|buttons| {
        buttons
            .iter()
            .find(|button| button.button_type.to_lowercase() == DISMISS_BUTTON_TYPE)
            .map(|button| FloatingModalButton {
                button_type: fableous::modals::types::FloatingModalButtonType::Secondary,
                action: FloatingModalButtonAction::Dismiss(None),
                text: TextContent::String(button.title.clone()),
                default_focus: !has_multiple_items,
            })
    });

    let next_button = if !has_multiple_items {
        None
    } else {
        data.attributedButtons
            .iter()
            .flat_map(|buttons| buttons.first())
            .map(|button| FloatingModalButton {
                button_type: fableous::modals::types::FloatingModalButtonType::Primary,
                action: FloatingModalButtonAction::NextItem(None),
                text: TextContent::String(button.title.clone()),
                default_focus: true,
            })
            .next()
    };

    let app_event_reporter = AppEventReporter::new(ctx.scope());

    // tile will be dropped if there is no image as the variant does not support text only
    Some(FloatingModalData {
        tiles: data
            .marketingModalItems
            .as_ref()?
            .iter()
            .filter_map(|item| {
                item.benefitImage
                    .as_ref()?
                    .url
                    .as_ref()
                    .map(|image_url| FloatingModalTile {
                        header: Some(FloatingModalHeader {
                            text: TextContent::String(item.title.clone().unwrap_or_default()),
                            color: None,
                        }),
                        body: Some(TextContent::String(
                            item.description.clone().unwrap_or_default(),
                        )),
                        image: Some(image_url.clone()),
                        image_sub_text: None,
                        metadata: get_metadata(item.itemId.clone(), data.attributedButtons.clone()),
                        action: None,
                        qr_code: None,
                    })
            })
            .collect(),
        buttons: vec![cta_button, dismiss_button, next_button]
            .into_iter()
            .flatten()
            .collect(),
        variant: fableous::modals::types::FableFloatingModalVariant::StartAlign,
        on_load: Some(Rc::new(move || {
            network_client.report_notification_displayed(
                notification_id.clone(),
                client_side_metrics.clone(),
            );
            metric!(EDUCATIONAL_CX_ACTION, 1, "Action" => "DISPLAY");

            report_page_display(
                app_event_reporter,
                ref_marker.clone(),
                container_metadata_ref_marker.clone(),
            );
        })),
        on_next: Some(Rc::new(move |metadata| {
            metric!(EDUCATIONAL_CX_ACTION, 1, "Action" => "NEXT");

            let mut next_ref_marker: Option<String> = None;

            if let Some(metadata) = metadata {
                next_ref_marker = metadata.get("next_ref_marker").cloned().unwrap_or_default();
            }

            report_button_clicked(app_event_reporter, next_ref_marker, ref_marker_1.clone());
        })),
        on_close: Some(Rc::new(move |metadata| {
            metric!(EDUCATIONAL_CX_ACTION, 1, "Action" => "DISMISS");
            let mut dismiss_ref_marker: Option<String> = None;

            if let Some(metadata) = metadata {
                dismiss_ref_marker = metadata
                    .get("dismiss_ref_marker")
                    .cloned()
                    .unwrap_or_default();
            }

            report_button_clicked(app_event_reporter, dismiss_ref_marker, ref_marker_2.clone());
        })),
        on_page_change: Some(Rc::new(|index| {
            metric!(EDUCATIONAL_CX_ACTION, 1,
                "Action" => "PAGE_CHANGE",
                "Page" => index,
            );
        })),
        on_back_pressed: None,
    })
}

fn generate_cta_button_action(ctx: &AppContext, action: &Action) -> FloatingModalButtonAction {
    let location = match action {
        Action::TransitionAction(transition_action) => {
            Transition::from_action_with_scope(transition_action, ctx.scope()).to_location()
        }
        _ => None,
    };

    FloatingModalButtonAction::Dismiss(location.map(|loc| {
        let ctx = ctx.clone();
        let navigate = use_navigation(ctx.scope());

        Rc::new(move || navigate(loc.clone(), "EDUCATIONAL_CX")) as Rc<dyn Fn()>
    }))
}

#[allow(nonstandard_style, reason = "Follows JSON pattern")]
#[derive(Serialize)]
struct ActionAttributes {
    notificationType: String,
    eventTimestamp: String,
}

#[allow(nonstandard_style, reason = "Follows JSON pattern")]
#[derive(Serialize)]
struct ReportNotificationDisplayedRequest {
    clientTimestamp: Option<u64>,
    actionAttributes: ActionAttributes,
    deviceAppGroup: String,
    isTest: bool,
    encodedClientSideMetrics: String,
}

pub trait EducationalCXRequests {
    fn report_notification_displayed(&self, notification_id: String, client_side_metrics: String);
}

#[cfg(not(any(test, feature = "test_utils")))]
impl EducationalCXRequests for NetworkClient {
    fn report_notification_displayed(&self, notification_id: String, client_side_metrics: String) {
        let success_cb = move |_: String, _| {
            metric!(EDUCATIONAL_CX_REQUEST, 1, "Action" => "SUCCESS");
        };

        let failure_cb = move |e: RequestError, _| {
            metric!(EDUCATIONAL_CX_REQUEST, 1, "Action" => "FAILURE");
            log::error!(
                "EducationalCX: Failed to report notification displayed with error: {:?}",
                e
            );
        };

        let url = match URLBuilder::for_device_proxy(
            REPORT_NOTIFICATION_DISPLAYED_ENDPOINT,
            vec![],
            false,
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!("EducationalCX: Failed to build URL with error:{:?}", e);
                return;
            }
        };

        let timestamp = match SystemTime::now().duration_since(UNIX_EPOCH) {
            Ok(current_time) => current_time.as_secs(),
            Err(e) => {
                log::error!(
                    "EducationalCX: Failed to get current time with error:{:?}",
                    e
                );
                return;
            }
        };

        let body = match serde_json::to_string(&ReportNotificationDisplayedRequest {
            clientTimestamp: Some(timestamp),
            actionAttributes: ActionAttributes {
                notificationType: notification_id,
                eventTimestamp: timestamp.to_string(),
            },
            deviceAppGroup: "blast".to_owned(),
            isTest: false,
            encodedClientSideMetrics: client_side_metrics,
        }) {
            Ok(str) => str,
            Err(e) => {
                log::error!("EducationalCX: Failed to build request with error:{:?}", e);
                return;
            }
        };

        self.builder(url, HttpMethod::Post, "educationalCX")
            .data(body)
            .with_parser(|_, _, _, cb| {
                cb(Ok(DeviceProxyResponse::LRCEdgeResponse(
                    LRCEdgeResponse::with_resource(String::new()),
                )))
            })
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb))
            .execute();
    }
}

#[cfg(any(feature = "test_utils", test))]
pub mod test_utils {
    use super::*;
    use __mock_MockNetworkClient::__new::Context;
    use common_transform_types::actions::{ClientAction, TransitionAction};
    use common_transform_types::modals::{
        BenefitImage, ButtonAttribute, MarketingModalButton, MarketingModalItem,
    };
    use mockall::mock;
    use router::{MockRouting, RoutingContext};
    use std::collections::HashMap;

    mock! {
        pub NetworkClient {
            pub fn new(ctx: &AppContext) -> Self;
        }

        impl EducationalCXRequests for NetworkClient {
            fn report_notification_displayed(&self, notification_id: String, client_side_metrics: String);
        }
    }

    pub fn setup_edcx_mocks(ctx: &AppContext) -> Context {
        setup_mock_routing(&ctx);

        setup_mock_network_client()
    }

    fn setup_mock_routing(ctx: &AppContext) {
        let mut mock_routing_context = MockRouting::new();
        mock_routing_context.expect_navigate().return_const(());
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
    }

    fn setup_mock_network_client() -> Context {
        let mut network_client = NetworkClient::default();
        network_client
            .expect_report_notification_displayed()
            .returning(|_, __| {});

        let context = NetworkClient::new_context();
        context.expect().once().return_once(move |_| network_client);

        context
    }

    pub(crate) fn get_mock_ecx_modal_data(multiple_items: bool) -> MarketingModalData {
        MarketingModalData {
            title: None,
            subtitle: None,
            marketingModalItems: if multiple_items {
                Some(vec![get_mock_modal_item(), get_mock_modal_item()])
            } else {
                Some(vec![get_mock_modal_item()])
            },
            clientSideMetrics: Some("416|CkcKJ0VDWExFQ1hUZXN0R2xv".to_string()),
            refMarker: Some("sportspage_ecx".to_string()),
            theme: Some("EducationalCX".to_string()),
            buttons: Some(get_mock_buttons()),
            attributedButtons: Some(get_mock_attributed_buttons()),
            notificationId: Some("mockECX".to_string()),
            containerMetadataRefMarker: Some("_ecx_vw".to_string()),
        }
    }

    fn get_mock_modal_item() -> MarketingModalItem {
        MarketingModalItem {
            swiftId: Some("V2=4AEA6u69gYPeuYe".to_string()),
            itemId: Some("ecxlecxtestitem01".to_string()),
            action: None,
            benefitImage: Some(BenefitImage {
                url: Some("mockBenfitImageUrl".to_string()),
                height: Some(818),
                width: Some(1400),
                alternateText: Some("Alternate text".to_string()),
                gradientRequired: Some(false),
            }),
            title: Some("mockItemTitle".to_string()),
            description: Some("mockItemDescription".to_string()),
        }
    }

    fn get_mock_buttons() -> Vec<MarketingModalButton> {
        let mut items: Vec<MarketingModalButton> = vec![];
        items.push(MarketingModalButton {
            title: "Change Home Region".to_string(),
            button_type: "ctaButton".to_string(),
            action: Some(Action::TransitionAction(
                TransitionAction::changeHomeRegion(ClientAction {
                    text: None,
                    target: "changeHomeRegion".to_string(),
                    refMarker: String::from("ch_button"),
                }),
            )),
        });
        items.push(MarketingModalButton {
            title: "Got it".to_string(),
            button_type: "dismissButton".to_string(),
            action: None,
        });
        items
    }

    fn get_mock_attributed_buttons() -> Vec<MarketingModalAttributedButton> {
        let mut items: Vec<MarketingModalAttributedButton> = vec![];
        items.push(MarketingModalAttributedButton {
            title: "Next".to_string(),
            attributes: {
                let mut map = HashMap::new();
                map.insert(
                    "ecxlecxtestitem01".to_string(),
                    ButtonAttribute {
                        refMarker: Some(String::from("_ecx_1_next")),
                        focusState: Some(true),
                    },
                );
                map
            },
        });
        items
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::{get_mock_ecx_modal_data, setup_edcx_mocks};
    use ignx_compositron::app::launch_only_app_context;

    #[test]
    fn test_button_creation_single_modal_item() {
        launch_only_app_context(|ctx| {
            let _mock_network_client_context = setup_edcx_mocks(&ctx);

            let mock_data = get_mock_ecx_modal_data(false);

            let result = generate_edcx_modal(ctx, &mock_data);
            assert!(result.is_some());

            let modal = result.unwrap();
            let buttons = modal.buttons;

            assert_eq!(buttons.len(), 2);

            let cta_button = &buttons[0];
            assert_eq!(
                cta_button.text,
                TextContent::String("Change Home Region".to_string())
            );
            assert_eq!(cta_button.default_focus, false);
            match &cta_button.action {
                FloatingModalButtonAction::Dismiss(Some(_)) => {}
                _ => panic!("Expected a Dismiss action with callback"),
            }

            let dissmiss_button = &buttons[1];
            assert_eq!(
                dissmiss_button.text,
                TextContent::String("Got it".to_string())
            );
            assert_eq!(dissmiss_button.default_focus, true);
            match &dissmiss_button.action {
                FloatingModalButtonAction::Dismiss(None) => {}
                _ => panic!("Expected a Dismiss action with no callback"),
            }
        })
    }

    #[test]
    fn test_button_creation_multiple_modal_items() {
        launch_only_app_context(|ctx| {
            let _mock_network_client_context = setup_edcx_mocks(&ctx);

            let mock_data = get_mock_ecx_modal_data(true);

            let result = generate_edcx_modal(ctx, &mock_data);
            assert!(result.is_some());

            let modal = result.unwrap();
            let buttons = modal.buttons;

            assert_eq!(buttons.len(), 3);

            let cta_button = &buttons[0];
            assert_eq!(
                cta_button.text,
                TextContent::String("Change Home Region".to_string())
            );
            assert_eq!(cta_button.default_focus, false);
            match &cta_button.action {
                FloatingModalButtonAction::Dismiss(Some(_)) => {}
                _ => panic!("Expected a Dismiss action with callback"),
            }

            let dissmiss_button = &buttons[1];
            assert_eq!(
                dissmiss_button.text,
                TextContent::String("Got it".to_string())
            );
            assert_eq!(dissmiss_button.default_focus, false);
            match &dissmiss_button.action {
                FloatingModalButtonAction::Dismiss(None) => {}
                _ => panic!("Expected a Dismiss action with no callback"),
            }

            let next_button = &buttons[2];
            assert_eq!(next_button.text, TextContent::String("Next".to_string()));
            assert_eq!(next_button.default_focus, true);
            match &next_button.action {
                FloatingModalButtonAction::NextItem(None) => {}
                _ => panic!("Expected a NextItem action with no callback"),
            }
        })
    }
}
