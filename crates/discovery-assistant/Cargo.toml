[package]
name = "discovery-assistant"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
auth.workspace = true
app-config.workspace = true
fableous.workspace = true
lrc-image.workspace = true
containers.workspace = true
container-types.workspace = true
container-list.workspace = true
cacheable-derive.workspace = true
cross-app-events.workspace = true
clickstream.workspace = true
router.workspace = true
location.workspace = true
common-transform-types.workspace = true
serde.workspace = true
serde_json.workspace = true
strum.workspace = true
strum_macros.workspace = true
title-details.workspace = true
media-background.workspace = true
navigation-menu.workspace = true
mockall.workspace = true
mockall_double.workspace = true
network.workspace = true
network-parser.workspace = true
cfg-test-attr-derive.workspace = true
network-parser-derive.workspace = true
transition-executor.workspace = true
cache.workspace = true
app-reporting.workspace = true
app-events.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
rstest.workspace = true
network = { workspace = true, features = ["test_utils", "mock_network"] }
common-transform-types.workspace = true
format-url.workspace = true
serial_test.workspace = true
router = { workspace = true, features = ["test_utils"] }
rust-features.workspace = true
transition-executor = { workspace = true, features = ["test_utils"] }
cache = { workspace = true, features = ["test-utils"] }
app-config = { workspace = true, features = ["test_utils"] }
mock_instant.workspace = true
container-types = { workspace = true, features = ["example_data"] }
clickstream = { workspace = true, features = ["test_utils"] }
insta.workspace = true

[lints]
workspace = true

[features]
debug_impl = []
