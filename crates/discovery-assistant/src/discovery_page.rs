use crate::{state::page_state::provide_discovery_page_ctx, ui::discovery_collection::*};
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use media_background::types::MediaBackgroundType;
use navigation_menu::context::nav_context::NavControl;
use title_details::core::TitleDetailsChangeRequest;

const TEST_ID_DISCOVERY_COLLECTION: &str = "DISCOVERY_PAGE_TEST_ID_COLLECTION";

fn init_discovery_page(
    ctx: &AppContext,
    update_media_background: WriteSignal<MediaBackgroundType>,
    update_title_details: WriteSignal<TitleDetailsChangeRequest>,
) {
    // Clean up any existing MB / TD
    update_media_background.set(MediaBackgroundType::None);
    update_title_details.set(TitleDetailsChangeRequest::EMPTY);

    // Hide top nav
    let nav_control = use_context::<NavControl>(ctx.scope());
    if let Some(nav_control) = nav_control {
        nav_control.show_top_nav_intent.set(false);
    }

    // Provide page ctx for data and actions
    provide_discovery_page_ctx(ctx);
}

#[Composer]
pub fn DiscoveryPage(
    ctx: &AppContext,
    update_media_background: WriteSignal<MediaBackgroundType>,
    update_title_details: WriteSignal<TitleDetailsChangeRequest>,
) -> ColumnComposable {
    init_discovery_page(ctx, update_media_background, update_title_details);

    compose! {
        Column() {
            DiscoveryCollection()
            .test_id(TEST_ID_DISCOVERY_COLLECTION)
        }
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        state::page_state::use_discovery_page_ctx,
        utils::{
            network::{MockNetworkClient, __mock_MockNetworkClient::__new::Context},
            test_utils::{create_navigation_mock, ReportingMock},
        },
    };
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_exists, TestRendererGameLoop},
    };
    use media_background::types::{MediaStrategy, StandardBackgroundData};
    use navigation_menu::context::nav_context::TopNavMode;
    use router::{rust_location, MockRouting, RoutingContext};
    use serial_test::serial;
    use std::rc::Rc;
    use title_details::{
        core::{NavigationDirection, TitleDetailsData},
        types::common::{StandardTitleDetailsData, TitleDetailsMetadata},
    };

    fn create_mock_td_data(id: impl Into<String>) -> TitleDetailsChangeRequest {
        TitleDetailsChangeRequest {
            data: TitleDetailsData::CollectionAndDetailsPageLayout(StandardTitleDetailsData {
                title_data: Default::default(),
                metadata: TitleDetailsMetadata::None,
                synopsis: Some(id.into()),
                entitlement_data: Default::default(),
            }),
            navigation_direction: NavigationDirection::DOWN,
        }
    }

    fn create_mock_mb_data() -> MediaBackgroundType {
        MediaBackgroundType::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None,
        })
    }

    fn create_mock_nav_control(ctx: &AppContext) -> NavControl {
        NavControl {
            disable_top_nav_focus_trap: create_rw_signal(ctx.scope(), false),
            disable_utility_nav_focus_trap: create_rw_signal(ctx.scope(), false),
            show_top_nav_intent: create_rw_signal(ctx.scope(), true),
            show_utility_nav: create_rw_signal(ctx.scope(), true),
            set_report_top_nav_visibility: create_rw_signal(ctx.scope(), None).write_only(),
            focused_nav: create_focus_value_signal(ctx.scope()),
            top_nav_mode: create_signal(ctx.scope(), TopNavMode::TopNav).0,
            enable_focus: create_rw_signal(ctx.scope(), true),
        }
    }

    /// This function will setup the page to enter its "Loading" state. Use `tick_once` rather than
    /// `tick_until_done` to avoid the test hanging, waiting for the spinner to "complete".
    fn create_mock_network() -> Context {
        let client_context = MockNetworkClient::new_context();
        client_context.expect().returning(|_| {
            let mut mock_client = MockNetworkClient::default();
            mock_client.expect_get_collection().return_const(());
            mock_client
        });

        client_context
    }

    fn setup_page(ctx: AppContext) -> ColumnComposable {
        let _mock_network_ctx = create_mock_network();
        let _mock_navigation = create_navigation_mock(None, None, 0);
        let _mock_reporter = ReportingMock::mock_catch_all_metrics();

        let (get_media_background, update_media_background) =
            create_signal(ctx.scope(), create_mock_mb_data());
        let (get_title_details, update_title_details) =
            create_signal(ctx.scope(), create_mock_td_data("Test"));
        let nav_control = create_mock_nav_control(&ctx);

        let mut mock_routing = MockRouting::new();
        let location = rust_location!(RUST_COLLECTIONS, {"pageType" => "home", "pageId" => "assistant", "serviceToken" => None::<String>, "ingressSource" => "SeeMore"});
        let location_sig = create_rw_signal(ctx.scope(), location);
        mock_routing
            .expect_location()
            .return_const_st(location_sig.read_only());
        provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));

        provide_context(ctx.scope(), get_title_details);
        provide_context(ctx.scope(), get_media_background);
        provide_context(ctx.scope(), nav_control);

        compose! {
            DiscoveryPage(update_title_details, update_media_background)
        }
    }

    #[test]
    #[serial]
    fn it_displays() {
        launch_test(setup_page, move |_, mut test_game_loop| {
            let node_tree = test_game_loop.tick_once().node_tree;
            let collection = node_tree.find_by_test_id(TEST_ID_DISCOVERY_COLLECTION);
            assert_node_exists!(&collection);
        });
    }

    #[test]
    #[serial]
    fn it_resets_mb_td_and_top_nav() {
        let test = move |scope, mut test_game_loop: TestRendererGameLoop| {
            let _node_tree = test_game_loop.tick_once().node_tree;

            let update_media_background = use_context::<ReadSignal<MediaBackgroundType>>(scope);
            let update_title_details = use_context::<ReadSignal<TitleDetailsChangeRequest>>(scope);
            let nav_control = use_context::<NavControl>(scope);

            assert_eq!(
                update_title_details.unwrap().get_untracked(),
                TitleDetailsChangeRequest::EMPTY
            );
            assert_eq!(
                update_media_background.unwrap().get_untracked(),
                MediaBackgroundType::None
            );
            assert!(!nav_control.unwrap().show_top_nav_intent.get_untracked());
        };

        launch_test(setup_page, test);
    }

    #[test]
    #[serial]
    fn it_provides_correct_ctx() {
        let test = move |scope, mut test_game_loop: TestRendererGameLoop| {
            test_game_loop.tick_once().node_tree;

            // If ctx is not available this will panic and the test will fail.
            use_discovery_page_ctx(scope);
        };

        launch_test(setup_page, test);
    }
}
