{"resource": {"containerList": [{"facet": {}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSRjIyMjNBNjVGOUE4IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "Fast X", "gti": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "transformItemId": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "synopsis": "<PERSON> and his beloved family confront an ominous threat with a personal vendetta against their unbreakable bond.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Suspense"], "starringCast": ["Vin Diesel", "<PERSON>", "<PERSON>"], "maturityRatingString": "PG-13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/609c48a31bb4f3d576d8d10f4908b43a578d533b0885a4c2a9af0fea02327d25.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/fbc8a887d3dc7f931975430c359a46e2d23fbccd898f888340ce7976d2c7f6bb.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5bdb029710d0f8fcb1e8c45a0ebc0ef424951b49a5c5a4ea292ef8816a86c945.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c4c743c1aa9e18d57fd64ba385cc9794288714f14aab537cbc515ac5e43f4bea.jpg", "publicReleaseDate": 1684454400000, "runtimeSeconds": 8451, "runtime": "140 min", "overallRating": 4.5, "totalReviewCount": 27271, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_1", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_1"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 10 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_1", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_1"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>", "gti": "amzn1.dv.gti.47db6971-feec-45b6-bf64-601bee7f0576", "transformItemId": "amzn1.dv.gti.47db6971-feec-45b6-bf64-601bee7f0576", "synopsis": "When retired Military Police Officer <PERSON> is arrested for a murder he did not commit, he finds himself in the middle of a deadly conspiracy full of dirty cops, shady businessmen and scheming politicians. With nothing but his wits, he must figure out what is happening in Margrave, Georgia. The first season of <PERSON> is based on the international bestseller, Killing Floor by <PERSON>.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-14", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "TV-14", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/56d391fc82789f8a0f04649842743bc853bebf2619d45d85e3486fb25a8f2d66.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9fa12c574ee4067adc660031fcef647fda388c96c9b312b1ea047f6236493449.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5df5cf7164affdc53b79d571c39f15d0fc2cc71b3782cc8f1baea6c907aa790b.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/34dcf97468563cb2d03824a83f526036a3f595d293843fde2345a87cb91310b4.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9c4c7c4270b676d009a9a0a395334f678869df4e227ad3e98b8f5d3161c67dad.jpg", "publicReleaseDate": 1643932800000, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_2", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_2"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_2", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_2"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>", "gti": "amzn1.dv.gti.f6a9f73a-1f25-13ec-3fe4-9b8c18e5e8cb", "transformItemId": "amzn1.dv.gti.f6a9f73a-1f25-13ec-3fe4-9b8c18e5e8cb", "synopsis": "Detective <PERSON> (<PERSON>) is a Portland, Oregon homicide detective with a strange secret: he's a descendant of an elite group of hunters who are charged with stopping the proliferation of supernatural creatures in the world.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "NR", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Drama", "Suspense", "Science Fiction", "Horror", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "NR", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/6fe0b50a4ed489f0e455b7fefa0d5c7a5b2e69588ba7cf5404aea4dbf8a58c3e.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/072c22dfd23691dbc8a21cdceab7dd94b09454155278eb86a8920dc10f47259a.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/eb031c21cfeeba2b0769bd8727145b5fb1a4dafb98fdadcdbe3b1352ea04a882.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "publicReleaseDate": 1319760000000, "numberOfSeasons": 6, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.0ca9f740-b2aa-1c5a-9c45-bb370a74e45c", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_3", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_3"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2014"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.0ca9f740-b2aa-1c5a-9c45-bb370a74e45c", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_3", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_3"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>'s <PERSON>", "gti": "amzn1.dv.gti.8764cdd8-18e9-49c9-8d4f-5e4ef39ba4ef", "transformItemId": "amzn1.dv.gti.8764cdd8-18e9-49c9-8d4f-5e4ef39ba4ef", "synopsis": "When CIA analyst <PERSON> stumbles upon a suspicious series of bank transfers his search for answers pulls him from the safety of his desk job and catapults him into a deadly game of cat and mouse throughout Europe and the Middle East, with a rising terrorist figurehead preparing for a massive attack against the US and her allies.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-14", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Action", "Drama"], "starringCast": ["<PERSON>", "<PERSON>", "A<PERSON><PERSON>"], "maturityRatingString": "TV-14", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/00cefb3afa2e22129ef54b7490153bdf5563cb16d8dffc008701748e30d74d15.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/5b4ae0efa96c09972da167f70b30f4f723fa0b4b99d5b85fd1c3ab266468fef8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/6438b5bd7aeeabf01229914f3e745321c9aa7c04daefbbef3d5f5c599c3d084d.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/58684ed93429974928a5299eb46f248730223d2a7d8b6e155d18b40072d6b569.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/156eb6ba535126a24c4630c5d08793005c8acbd7d51ca9eef55e2fc0250dcf71.png", "publicReleaseDate": 1535673600000, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a06579a1-22e2-41e2-8412-d20fc4ffd91a", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_4", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_4"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2020"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a06579a1-22e2-41e2-8412-d20fc4ffd91a", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_4", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_4"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON><PERSON>", "gti": "amzn1.dv.gti.a6b886eb-47c1-90f9-7508-d37c218381bb", "transformItemId": "amzn1.dv.gti.a6b886eb-47c1-90f9-7508-d37c218381bb", "synopsis": "FBI agent <PERSON> is recruited for a perilous operation against a ruthless Mexican drug cartel, following a deadly bombing.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense", "Special Interest"], "starringCast": ["<PERSON>", "<PERSON><PERSON>o <PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d5e9975cb2070b792d6f5e870d1bf168eb5e6a9681f0259040021e48fbb8a9de.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/218c511f1afd40bc67d1ab57a2597a6d100ab3a661a8222ef894cb26e4a28703.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/6f3c6ba6d803ca7c15c8cc70daa3c85ce9553eb03cecc3e80fdeff28634ab397.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/51ae8db5514984c8356c337fb8971404184b000f08cdf88808821e2c4d877a07.jpg", "publicReleaseDate": 1443744000000, "runtimeSeconds": 7277, "runtime": "121 min", "overallRating": 4.7, "totalReviewCount": 10813, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a6b886eb-47c1-90f9-7508-d37c218381bb", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_5", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_5"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 3X nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a6b886eb-47c1-90f9-7508-d37c218381bb", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_5", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_5"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "From", "gti": "amzn1.dv.gti.c1ae1fb3-98a8-4c5f-b845-d014a0897573", "transformItemId": "amzn1.dv.gti.c1ae1fb3-98a8-4c5f-b845-d014a0897573", "synopsis": "FROM unravels the mystery of a nightmarish town that traps all those who enter. As the unwilling residents search for a way out, they must also survive the threats of the forest including the terrifying creatures that come out at night.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Science Fiction", "Horror"], "starringCast": ["<PERSON>", "Catalina <PERSON>ino Moreno", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8f2831ac050828ce9a534280416c77d73e7d69d1b116303cba3c1b09ed1d7d80.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/b5ab0e5792e602302fe439cb0fd2365980ce21c3cc97d6f345d7a5bb7a2c3fe1.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4769439e9af72b9dafa95cd0f32b8b3244574b23e96cdcffb6e8c7784c09732f.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7f37f5a6af1316d4a479ee0fcb6b77a03d1a77901389f0a0e9626c3f6dd0a4b4.jpg", "publicReleaseDate": 1645315200000, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.49d1c808-9e41-4551-83d5-192cf516c34b", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_6", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_6"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": ""}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.49d1c808-9e41-4551-83d5-192cf516c34b", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_6", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_6"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "WILD CARD", "gti": "amzn1.dv.gti.9cbada7a-ebe7-ed26-926f-012584906234", "transformItemId": "amzn1.dv.gti.9cbada7a-ebe7-ed26-926f-012584906234", "synopsis": "A compulsive gambler turns guardian, pursuing vengeance against a ruthless mobster who assaulted his friend in the gritty gambling underworld.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c5d73dd0136ac15b43663d20e110db6e27c33386fec849b2f34f024e282377ab.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/84f880341b33bebdb70e9fab1e4cec7bd7e774e82e6fc06a4690c7d64a3b9ed8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/ff9038b26b04d6bc94263481d7352ad83049f6b9cf884b256404622bbd69d305.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/09f983ddad719f8ab6a7dde68a7cdd2af68835cde22f97bc07c3cf96009595b2.jpg", "publicReleaseDate": 1422576000000, "runtimeSeconds": 5290, "runtime": "88 min", "overallRating": 4.2, "totalReviewCount": 9086, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.9cbada7a-ebe7-ed26-926f-012584906234", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_7", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_7"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.9cbada7a-ebe7-ed26-926f-012584906234", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_7", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_7"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "Lioness", "gti": "amzn1.dv.gti.033228a7-472c-4425-9ac7-d77d5acc3d3e", "transformItemId": "amzn1.dv.gti.033228a7-472c-4425-9ac7-d77d5acc3d3e", "synopsis": "<PERSON>’s (1923, Tulsa King) spy thriller follows <PERSON> (<PERSON>) who leads an undercover operation to take down a terrorist group from within.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON>", "LaM<PERSON><PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c7d8d1179b60fa67feb57f32b54ea5465ee2a03cca511def756c5e6123f63be5.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/204def7d2ef030547e3f37e208cc7deed26ef2a586789c30d6ae2d0e401be212.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/be76edcbebca08653aaf73e9f8f7bd589fe8782cdc83fd79c3ee3b2cea8631db.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "publicReleaseDate": 1690070400000, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.111794a7-9233-4b4d-b751-fefdd9e4e019", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_8", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_8"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+ or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.111794a7-9233-4b4d-b751-fefdd9e4e019", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_1_8", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr5ec29d_dast_1_8"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_ass_c_sr5ec29d_1", "ClientSideMetrics": "960|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1JGMjIyM0E2NUY5QTgaACIIc3I1ZWMyOWQSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oKU2VhcmNoRmxleEoZYXNzaXN0YW50VGl0bGVzQWJvdmVQaWxsc1ILbm90RW50aXRsZWRaAGIER3JpZGgBcgB6OGV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09ggEDYWxsigEAkgEA"}, "tags": [], "type": "GRID"}, {"title": "Explore more", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSMDEzODgxODMyODJDIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "items": [{"title": "Global espionage", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-4.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_1", "pillText": "Global espionage", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "946", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_1", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pT1RRMklpd2ljR2h5WVhObElqb2lSMnh2WW1Gc0lHVnpjR2x2Ym1GblpTSjlmU3dpYVc1MFpYSnVZV3hCYzNOcGMzUmhiblJUZEdGMFpTSTZleUp6Wld4bFkzUmxaRkJwYkd4SlpDSTZJamswTmlJc0luQnBiR3h6Vm1WeWMybHZiaUk2SW1SbGJXOHRNakF5TkRFd01qVWlMQ0p6ZEdGMFpVdGxlU0k2SW1Nd05ERXhaVFU1TFdRMVlUTXRORFZpTXkxaVptSmpMV0l5WkdVeU5qYzBZamcwWlNJc0luQnBiR3hXWVhKcFpYUjVWRzlNWVhOMFVtVjBjbWxsZG1Wa1NXNWtaWGhOWVhBaU9uc2lWVzV3WlhKemIyNWhiR2w2WldSQ2NtOWhaQ0k2TVRsOUxDSnBjMFpwY25OMFVHRm5aU0k2Wm1Gc2MyVjlmUT09Il19LCJvZmZzZXQiOjAsIm5wc2kiOjAsIm9yZXEiOiJldllqNVh0TWtVd1J5cVB6b1VFVkQ4bEY4TUtvOW4zdkdTVUF5WXBjSjBSSG1PWTYzdHc1Ymc9PToxNzMwODA4MDkzMDAwIiwib3JlcWsiOiJ5VHZJc1kyY0hFZlZvODQ2Mzk1KzJRZm9oeUpFSG1ycHJwQ0ZxSDlsbVc0PSIsIm9yZXFrdiI6MX0="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Urban thrills", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-10.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_2", "pillText": "Urban thrills", "pillVisibility": "ROOT", "pillSource": "search_concept", "pillId": "1418", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_2", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRReE9DSXNJbkJvY21GelpTSTZJbFZ5WW1GdUlIUm9jbWxzYkhNaWZYMHNJbWx1ZEdWeWJtRnNRWE56YVhOMFlXNTBVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzU1dRaU9pSXhOREU0SWl3aWNHbHNiSE5XWlhKemFXOXVJam9pWkdWdGJ5MHlNREkwTVRBeU5TSXNJbk4wWVhSbFMyVjVJam9pWXpBME1URmxOVGt0WkRWaE15MDBOV0l6TFdKbVltTXRZakprWlRJMk56UmlPRFJsSWl3aWNHbHNiRlpoY21sbGRIbFViMHhoYzNSU1pYUnlhV1YyWldSSmJtUmxlRTFoY0NJNmV5SlZibkJsY25OdmJtRnNhWHBsWkVKeWIyRmtJam94T1gwc0ltbHpSbWx5YzNSUVlXZGxJanBtWVd4elpYMTkiXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Cerebral sci-fi adventures", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-3.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_3", "pillText": "Cerebral sci-fi adventures", "pillVisibility": "ROOT", "pillSource": "search_concept", "pillId": "381", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_3", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTXpneElpd2ljR2h5WVhObElqb2lRMlZ5WldKeVlXd2djMk5wTFdacElHRmtkbVZ1ZEhWeVpYTWlmWDBzSW1sdWRHVnlibUZzUVhOemFYTjBZVzUwVTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c1NXUWlPaUl6T0RFaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9Il19LCJvZmZzZXQiOjAsIm5wc2kiOjAsIm9yZXEiOiJldllqNVh0TWtVd1J5cVB6b1VFVkQ4bEY4TUtvOW4zdkdTVUF5WXBjSjBSSG1PWTYzdHc1Ymc9PToxNzMwODA4MDkzMDAwIiwib3JlcWsiOiJ5VHZJc1kyY0hFZlZvODQ2Mzk1KzJRZm9oeUpFSG1ycHJwQ0ZxSDlsbVc0PSIsIm9yZXFrdiI6MX0="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Secretive Affairs", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/large_pill/Image-29.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_4", "pillText": "Secretive Affairs", "pillVisibility": "ROOT", "pillSource": "search_concept", "pillId": "1723", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_4", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRjeU15SXNJbkJvY21GelpTSTZJbE5sWTNKbGRHbDJaU0JCWm1aaGFYSnpJbjE5TENKcGJuUmxjbTVoYkVGemMybHpkR0Z1ZEZOMFlYUmxJanA3SW5ObGJHVmpkR1ZrVUdsc2JFbGtJam9pTVRjeU15SXNJbkJwYkd4elZtVnljMmx2YmlJNkltUmxiVzh0TWpBeU5ERXdNalVpTENKemRHRjBaVXRsZVNJNkltTXdOREV4WlRVNUxXUTFZVE10TkRWaU15MWlabUpqTFdJeVpHVXlOamMwWWpnMFpTSXNJbkJwYkd4V1lYSnBaWFI1Vkc5TVlYTjBVbVYwY21sbGRtVmtTVzVrWlhoTllYQWlPbnNpVlc1d1pYSnpiMjVoYkdsNlpXUkNjbTloWkNJNk1UbDlMQ0pwYzBacGNuTjBVR0ZuWlNJNlptRnNjMlY5ZlE9PSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT06MTczMDgwODA5MzAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9"}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Suburban nightmares", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-12.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_5", "pillText": "Suburban nightmares", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "877", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_5", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pT0RjM0lpd2ljR2h5WVhObElqb2lVM1ZpZFhKaVlXNGdibWxuYUhSdFlYSmxjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnM055SXNJbkJwYkd4elZtVnljMmx2YmlJNkltUmxiVzh0TWpBeU5ERXdNalVpTENKemRHRjBaVXRsZVNJNkltTXdOREV4WlRVNUxXUTFZVE10TkRWaU15MWlabUpqTFdJeVpHVXlOamMwWWpnMFpTSXNJbkJwYkd4V1lYSnBaWFI1Vkc5TVlYTjBVbVYwY21sbGRtVmtTVzVrWlhoTllYQWlPbnNpVlc1d1pYSnpiMjVoYkdsNlpXUkNjbTloWkNJNk1UbDlMQ0pwYzBacGNuTjBVR0ZuWlNJNlptRnNjMlY5ZlE9PSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT06MTczMDgwODA5MzAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9"}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Martial arts fantasies", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-9.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_6", "pillText": "Martial arts fantasies", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1457", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_6", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRRMU55SXNJbkJvY21GelpTSTZJazFoY25ScFlXd2dZWEowY3lCbVlXNTBZWE5wWlhNaWZYMHNJbWx1ZEdWeWJtRnNRWE56YVhOMFlXNTBVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzU1dRaU9pSXhORFUzSWl3aWNHbHNiSE5XWlhKemFXOXVJam9pWkdWdGJ5MHlNREkwTVRBeU5TSXNJbk4wWVhSbFMyVjVJam9pWXpBME1URmxOVGt0WkRWaE15MDBOV0l6TFdKbVltTXRZakprWlRJMk56UmlPRFJsSWl3aWNHbHNiRlpoY21sbGRIbFViMHhoYzNSU1pYUnlhV1YyWldSSmJtUmxlRTFoY0NJNmV5SlZibkJsY25OdmJtRnNhWHBsWkVKeWIyRmtJam94T1gwc0ltbHpSbWx5YzNSUVlXZGxJanBtWVd4elpYMTkiXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Underworld thrills", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-7.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_7", "pillText": "Underworld thrills", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1568", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_7", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRVMk9DSXNJbkJvY21GelpTSTZJbFZ1WkdWeWQyOXliR1FnZEdoeWFXeHNjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpFMU5qZ2lMQ0p3YVd4c2MxWmxjbk5wYjI0aU9pSmtaVzF2TFRJd01qUXhNREkxSWl3aWMzUmhkR1ZMWlhraU9pSmpNRFF4TVdVMU9TMWtOV0V6TFRRMVlqTXRZbVppWXkxaU1tUmxNalkzTkdJNE5HVWlMQ0p3YVd4c1ZtRnlhV1YwZVZSdlRHRnpkRkpsZEhKcFpYWmxaRWx1WkdWNFRXRndJanA3SWxWdWNHVnljMjl1WVd4cGVtVmtRbkp2WVdRaU9qRTVmU3dpYVhOR2FYSnpkRkJoWjJVaU9tWmhiSE5sZlgwPSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT06MTczMDgwODA5MzAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9"}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Vengeance thrillers", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-10.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_8", "pillText": "Vengeance thrillers", "pillVisibility": "ROOT", "pillSource": "search_concept", "pillId": "486", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_8", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTkRnMklpd2ljR2h5WVhObElqb2lWbVZ1WjJWaGJtTmxJSFJvY21sc2JHVnljeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpRNE5pSXNJbkJwYkd4elZtVnljMmx2YmlJNkltUmxiVzh0TWpBeU5ERXdNalVpTENKemRHRjBaVXRsZVNJNkltTXdOREV4WlRVNUxXUTFZVE10TkRWaU15MWlabUpqTFdJeVpHVXlOamMwWWpnMFpTSXNJbkJwYkd4V1lYSnBaWFI1Vkc5TVlYTjBVbVYwY21sbGRtVmtTVzVrWlhoTllYQWlPbnNpVlc1d1pYSnpiMjVoYkdsNlpXUkNjbTloWkNJNk1UbDlMQ0pwYzBacGNuTjBVR0ZuWlNJNlptRnNjMlY5ZlE9PSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT06MTczMDgwODA5MzAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9"}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Hidden marital lives", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-12.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_9", "pillText": "Hidden marital lives", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1921", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_9", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRreU1TSXNJbkJvY21GelpTSTZJa2hwWkdSbGJpQnRZWEpwZEdGc0lHeHBkbVZ6SW4xOUxDSnBiblJsY201aGJFRnpjMmx6ZEdGdWRGTjBZWFJsSWpwN0luTmxiR1ZqZEdWa1VHbHNiRWxrSWpvaU1Ua3lNU0lzSW5CcGJHeHpWbVZ5YzJsdmJpSTZJbVJsYlc4dE1qQXlOREV3TWpVaUxDSnpkR0YwWlV0bGVTSTZJbU13TkRFeFpUVTVMV1ExWVRNdE5EVmlNeTFpWm1KakxXSXlaR1V5TmpjMFlqZzBaU0lzSW5CcGJHeFdZWEpwWlhSNVZHOU1ZWE4wVW1WMGNtbGxkbVZrU1c1a1pYaE5ZWEFpT25zaVZXNXdaWEp6YjI1aGJHbDZaV1JDY205aFpDSTZNVGw5TENKcGMwWnBjbk4wVUdGblpTSTZabUZzYzJWOWZRPT0iXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "International intrigue", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/large_pill/Image-21.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_10", "pillText": "International intrigue", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "306", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_10", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTXpBMklpd2ljR2h5WVhObElqb2lTVzUwWlhKdVlYUnBiMjVoYkNCcGJuUnlhV2QxWlNKOWZTd2lhVzUwWlhKdVlXeEJjM05wYzNSaGJuUlRkR0YwWlNJNmV5SnpaV3hsWTNSbFpGQnBiR3hKWkNJNklqTXdOaUlzSW5CcGJHeHpWbVZ5YzJsdmJpSTZJbVJsYlc4dE1qQXlOREV3TWpVaUxDSnpkR0YwWlV0bGVTSTZJbU13TkRFeFpUVTVMV1ExWVRNdE5EVmlNeTFpWm1KakxXSXlaR1V5TmpjMFlqZzBaU0lzSW5CcGJHeFdZWEpwWlhSNVZHOU1ZWE4wVW1WMGNtbGxkbVZrU1c1a1pYaE5ZWEFpT25zaVZXNXdaWEp6YjI1aGJHbDZaV1JDY205aFpDSTZNVGw5TENKcGMwWnBjbk4wVUdGblpTSTZabUZzYzJWOWZRPT0iXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Elite ops missions", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-10.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_11", "pillText": "Elite ops missions", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "400", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_11", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTkRBd0lpd2ljR2h5WVhObElqb2lSV3hwZEdVZ2IzQnpJRzFwYzNOcGIyNXpJbjE5TENKcGJuUmxjbTVoYkVGemMybHpkR0Z1ZEZOMFlYUmxJanA3SW5ObGJHVmpkR1ZrVUdsc2JFbGtJam9pTkRBd0lpd2ljR2xzYkhOV1pYSnphVzl1SWpvaVpHVnRieTB5TURJME1UQXlOU0lzSW5OMFlYUmxTMlY1SWpvaVl6QTBNVEZsTlRrdFpEVmhNeTAwTldJekxXSm1ZbU10WWpKa1pUSTJOelJpT0RSbElpd2ljR2xzYkZaaGNtbGxkSGxVYjB4aGMzUlNaWFJ5YVdWMlpXUkpibVJsZUUxaGNDSTZleUpWYm5CbGNuTnZibUZzYVhwbFpFSnliMkZrSWpveE9YMHNJbWx6Um1seWMzUlFZV2RsSWpwbVlXeHpaWDE5Il19LCJvZmZzZXQiOjAsIm5wc2kiOjAsIm9yZXEiOiJldllqNVh0TWtVd1J5cVB6b1VFVkQ4bEY4TUtvOW4zdkdTVUF5WXBjSjBSSG1PWTYzdHc1Ymc9PToxNzMwODA4MDkzMDAwIiwib3JlcWsiOiJ5VHZJc1kyY0hFZlZvODQ2Mzk1KzJRZm9oeUpFSG1ycHJwQ0ZxSDlsbVc0PSIsIm9yZXFrdiI6MX0="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Gritty superhero sagas", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-10.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_12", "pillText": "Gritty superhero sagas", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "678", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_12", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTmpjNElpd2ljR2h5WVhObElqb2lSM0pwZEhSNUlITjFjR1Z5YUdWeWJ5QnpZV2RoY3lKOWZTd2lhVzUwWlhKdVlXeEJjM05wYzNSaGJuUlRkR0YwWlNJNmV5SnpaV3hsWTNSbFpGQnBiR3hKWkNJNklqWTNPQ0lzSW5CcGJHeHpWbVZ5YzJsdmJpSTZJbVJsYlc4dE1qQXlOREV3TWpVaUxDSnpkR0YwWlV0bGVTSTZJbU13TkRFeFpUVTVMV1ExWVRNdE5EVmlNeTFpWm1KakxXSXlaR1V5TmpjMFlqZzBaU0lzSW5CcGJHeFdZWEpwWlhSNVZHOU1ZWE4wVW1WMGNtbGxkbVZrU1c1a1pYaE5ZWEFpT25zaVZXNXdaWEp6YjI1aGJHbDZaV1JDY205aFpDSTZNVGw5TENKcGMwWnBjbk4wVUdGblpTSTZabUZzYzJWOWZRPT0iXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Elite military operations", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-11.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_13", "pillText": "Elite military operations", "pillVisibility": "ROOT", "pillSource": "search_concept", "pillId": "475", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_13", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTkRjMUlpd2ljR2h5WVhObElqb2lSV3hwZEdVZ2JXbHNhWFJoY25rZ2IzQmxjbUYwYVc5dWN5SjlmU3dpYVc1MFpYSnVZV3hCYzNOcGMzUmhiblJUZEdGMFpTSTZleUp6Wld4bFkzUmxaRkJwYkd4SlpDSTZJalEzTlNJc0luQnBiR3h6Vm1WeWMybHZiaUk2SW1SbGJXOHRNakF5TkRFd01qVWlMQ0p6ZEdGMFpVdGxlU0k2SW1Nd05ERXhaVFU1TFdRMVlUTXRORFZpTXkxaVptSmpMV0l5WkdVeU5qYzBZamcwWlNJc0luQnBiR3hXWVhKcFpYUjVWRzlNWVhOMFVtVjBjbWxsZG1Wa1NXNWtaWGhOWVhBaU9uc2lWVzV3WlhKemIyNWhiR2w2WldSQ2NtOWhaQ0k2TVRsOUxDSnBjMFpwY25OMFVHRm5aU0k2Wm1Gc2MyVjlmUT09Il19LCJvZmZzZXQiOjAsIm5wc2kiOjAsIm9yZXEiOiJldllqNVh0TWtVd1J5cVB6b1VFVkQ4bEY4TUtvOW4zdkdTVUF5WXBjSjBSSG1PWTYzdHc1Ymc9PToxNzMwODA4MDkzMDAwIiwib3JlcWsiOiJ5VHZJc1kyY0hFZlZvODQ2Mzk1KzJRZm9oeUpFSG1ycHJwQ0ZxSDlsbVc0PSIsIm9yZXFrdiI6MX0="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Found footage frights", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/large_pill/Image-1.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_14", "pillText": "Found footage frights", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "49", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_14", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTkRraUxDSndhSEpoYzJVaU9pSkdiM1Z1WkNCbWIyOTBZV2RsSUdaeWFXZG9kSE1pZlgwc0ltbHVkR1Z5Ym1Gc1FYTnphWE4wWVc1MFUzUmhkR1VpT25zaWMyVnNaV04wWldSUWFXeHNTV1FpT2lJME9TSXNJbkJwYkd4elZtVnljMmx2YmlJNkltUmxiVzh0TWpBeU5ERXdNalVpTENKemRHRjBaVXRsZVNJNkltTXdOREV4WlRVNUxXUTFZVE10TkRWaU15MWlabUpqTFdJeVpHVXlOamMwWWpnMFpTSXNJbkJwYkd4V1lYSnBaWFI1Vkc5TVlYTjBVbVYwY21sbGRtVmtTVzVrWlhoTllYQWlPbnNpVlc1d1pYSnpiMjVoYkdsNlpXUkNjbTloWkNJNk1UbDlMQ0pwYzBacGNuTjBVR0ZuWlNJNlptRnNjMlY5ZlE9PSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT06MTczMDgwODA5MzAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9"}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Vin Diesel thrillers", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-14.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_15", "pillText": "Vin Diesel thrillers", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1487", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_15", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRRNE55SXNJbkJvY21GelpTSTZJbFpwYmlCRWFXVnpaV3dnZEdoeWFXeHNaWEp6SW4xOUxDSnBiblJsY201aGJFRnpjMmx6ZEdGdWRGTjBZWFJsSWpwN0luTmxiR1ZqZEdWa1VHbHNiRWxrSWpvaU1UUTROeUlzSW5CcGJHeHpWbVZ5YzJsdmJpSTZJbVJsYlc4dE1qQXlOREV3TWpVaUxDSnpkR0YwWlV0bGVTSTZJbU13TkRFeFpUVTVMV1ExWVRNdE5EVmlNeTFpWm1KakxXSXlaR1V5TmpjMFlqZzBaU0lzSW5CcGJHeFdZWEpwWlhSNVZHOU1ZWE4wVW1WMGNtbGxkbVZrU1c1a1pYaE5ZWEFpT25zaVZXNXdaWEp6YjI1aGJHbDZaV1JDY205aFpDSTZNVGw5TENKcGMwWnBjbk4wVUdGblpTSTZabUZzYzJWOWZRPT0iXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Middle-earth epics", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-6.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_16", "pillText": "Middle-earth epics", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1770", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_16", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRjM01DSXNJbkJvY21GelpTSTZJazFwWkdSc1pTMWxZWEowYUNCbGNHbGpjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpFM056QWlMQ0p3YVd4c2MxWmxjbk5wYjI0aU9pSmtaVzF2TFRJd01qUXhNREkxSWl3aWMzUmhkR1ZMWlhraU9pSmpNRFF4TVdVMU9TMWtOV0V6TFRRMVlqTXRZbVppWXkxaU1tUmxNalkzTkdJNE5HVWlMQ0p3YVd4c1ZtRnlhV1YwZVZSdlRHRnpkRkpsZEhKcFpYWmxaRWx1WkdWNFRXRndJanA3SWxWdWNHVnljMjl1WVd4cGVtVmtRbkp2WVdRaU9qRTVmU3dpYVhOR2FYSnpkRkJoWjJVaU9tWmhiSE5sZlgwPSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT06MTczMDgwODA5MzAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9"}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Government conspiracy", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-10.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_17", "pillText": "Government conspiracy", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1799", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_17", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRjNU9TSXNJbkJvY21GelpTSTZJa2R2ZG1WeWJtMWxiblFnWTI5dWMzQnBjbUZqZVNKOWZTd2lhVzUwWlhKdVlXeEJjM05wYzNSaGJuUlRkR0YwWlNJNmV5SnpaV3hsWTNSbFpGQnBiR3hKWkNJNklqRTNPVGtpTENKd2FXeHNjMVpsY25OcGIyNGlPaUprWlcxdkxUSXdNalF4TURJMUlpd2ljM1JoZEdWTFpYa2lPaUpqTURReE1XVTFPUzFrTldFekxUUTFZak10WW1aaVl5MWlNbVJsTWpZM05HSTROR1VpTENKd2FXeHNWbUZ5YVdWMGVWUnZUR0Z6ZEZKbGRISnBaWFpsWkVsdVpHVjRUV0Z3SWpwN0lsVnVjR1Z5YzI5dVlXeHBlbVZrUW5KdllXUWlPakU1ZlN3aWFYTkdhWEp6ZEZCaFoyVWlPbVpoYkhObGZYMD0iXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Revenge narratives", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-4.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_18", "pillText": "Revenge narratives", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1308", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_18", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRNd09DSXNJbkJvY21GelpTSTZJbEpsZG1WdVoyVWdibUZ5Y21GMGFYWmxjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpFek1EZ2lMQ0p3YVd4c2MxWmxjbk5wYjI0aU9pSmtaVzF2TFRJd01qUXhNREkxSWl3aWMzUmhkR1ZMWlhraU9pSmpNRFF4TVdVMU9TMWtOV0V6TFRRMVlqTXRZbVppWXkxaU1tUmxNalkzTkdJNE5HVWlMQ0p3YVd4c1ZtRnlhV1YwZVZSdlRHRnpkRkpsZEhKcFpYWmxaRWx1WkdWNFRXRndJanA3SWxWdWNHVnljMjl1WVd4cGVtVmtRbkp2WVdRaU9qRTVmU3dpYVhOR2FYSnpkRkJoWjJVaU9tWmhiSE5sZlgwPSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT06MTczMDgwODA5MzAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9"}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Superpowered spectacles", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-5.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_19", "pillText": "Superpowered spectacles", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "437", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_19", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTkRNM0lpd2ljR2h5WVhObElqb2lVM1Z3WlhKd2IzZGxjbVZrSUhOd1pXTjBZV05zWlhNaWZYMHNJbWx1ZEdWeWJtRnNRWE56YVhOMFlXNTBVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzU1dRaU9pSTBNemNpTENKd2FXeHNjMVpsY25OcGIyNGlPaUprWlcxdkxUSXdNalF4TURJMUlpd2ljM1JoZEdWTFpYa2lPaUpqTURReE1XVTFPUzFrTldFekxUUTFZak10WW1aaVl5MWlNbVJsTWpZM05HSTROR1VpTENKd2FXeHNWbUZ5YVdWMGVWUnZUR0Z6ZEZKbGRISnBaWFpsWkVsdVpHVjRUV0Z3SWpwN0lsVnVjR1Z5YzI5dVlXeHBlbVZrUW5KdllXUWlPakU1ZlN3aWFYTkdhWEp6ZEZCaFoyVWlPbVpoYkhObGZYMD0iXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}, {"title": "Covert action", "imageUrl": "https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/large_pill/Image-4.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sre5b23c_dast_2_20", "pillText": "Covert action", "pillVisibility": "INTERNAL", "pillSource": "search_concept", "pillId": "1446", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sre5b23c_dast_2_20", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTVRRME5pSXNJbkJvY21GelpTSTZJa052ZG1WeWRDQmhZM1JwYjI0aWZYMHNJbWx1ZEdWeWJtRnNRWE56YVhOMFlXNTBVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzU1dRaU9pSXhORFEySWl3aWNHbHNiSE5XWlhKemFXOXVJam9pWkdWdGJ5MHlNREkwTVRBeU5TSXNJbk4wWVhSbFMyVjVJam9pWXpBME1URmxOVGt0WkRWaE15MDBOV0l6TFdKbVltTXRZakprWlRJMk56UmlPRFJsSWl3aWNHbHNiRlpoY21sbGRIbFViMHhoYzNSU1pYUnlhV1YyWldSSmJtUmxlRTFoY0NJNmV5SlZibkJsY25OdmJtRnNhWHBsWkVKeWIyRmtJam94T1gwc0ltbHpSbWx5YzNSUVlXZGxJanBtWVd4elpYMTkiXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}], "analytics": {"refMarker": "hm_ass_c_sre5b23c_2", "ClientSideMetrics": "1008|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1IwMTM4ODE4MzI4MkMaACIIc3JlNWIyM2MSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6G1BWRGlzY292ZXJ5QXNzaXN0YW50U2VydmljZUIXRGlzY292ZXJ5QXNzaXN0YW50UGlsbHNKClNlYXJjaEZsZXhKDmFzc2lzdGFudFBpbGxzUgtub3RFbnRpdGxlZFoAYhJEaXNjb3ZlcnlBc3Npc3RhbnRoAnIAejhldllqNVh0TWtVd1J5cVB6b1VFVkQ4bEY4TUtvOW4zdkdTVUF5WXBjSjBSSG1PWTYzdHc1Ymc9PYIBA2FsbIoBAJIBAA=="}, "tags": [], "itemDisplaySize": "Small", "type": "DISCOVERY_ASSISTANT"}, {"facet": {}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSQjVFNjBCQzYwMUE0IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "<PERSON><PERSON><PERSON>", "gti": "amzn1.dv.gti.ae311bb4-e7a1-4741-a9c7-9ebcb2a4f683", "transformItemId": "amzn1.dv.gti.ae311bb4-e7a1-4741-a9c7-9ebcb2a4f683", "synopsis": "1930s Los Angeles: Private detective <PERSON>'s search for a movie star's daughter's ex-lover leads to a dangerous mystery.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/08f70569565c60e5fc88dbd3b8eae1096cf93fb295bb0f8bde1a9df0f7735045.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/5485f32adf7d3c378a9dcd2f5bae13aa4c369d90a1654415199f5fe89285da42.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d3a940f8ab035c465c5bb42be4ea7ca2af846e059f717530b5846087a5b44a9b.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f4395ab016d67d780d08502c1f71f7f778c55ebf37725de151eaf36758858e74.jpg", "publicReleaseDate": 1676419200000, "runtimeSeconds": 6554, "runtime": "109 min", "overallRating": 3.7, "totalReviewCount": 2090, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.ae311bb4-e7a1-4741-a9c7-9ebcb2a4f683", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_1", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_1"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.ae311bb4-e7a1-4741-a9c7-9ebcb2a4f683", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_1", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_1"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "The Terminal List", "gti": "amzn1.dv.gti.8d963fda-4e3a-4f48-8f5d-3490e66abcd5", "transformItemId": "amzn1.dv.gti.8d963fda-4e3a-4f48-8f5d-3490e66abcd5", "synopsis": "Navy SEAL Commander <PERSON> (<PERSON>) battles a far-reaching conspiracy and a damaged mind as he seeks revenge for the deaths of his fallen men.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "18+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "18+", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4c4755af828364c0ad39a28cabb2e79dc990aace02f459446b0ca6302438e668.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/0c8fa316e5735859bd2a3e48e25c743f4e5b61fd431dbd40a28a0f71a6647ebe.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b8d15fd8fed59c2b9a5bd6d5251f012d834946e1f6c7196e47c287d8cdcf23bb.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/8e42a0761e6293f9a599ab09b2a2710f6801b005ac632af96eca36f333852dfd.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/337b4ecd4c3b165d81f744da2b0afdc78846cd19c4aa326db5e3b2533e383d3f.jpg", "publicReleaseDate": 1656633600000, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.8c26c602-5afc-44f9-b55b-2ca9394461bf", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_2", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_2"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.8c26c602-5afc-44f9-b55b-2ca9394461bf", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_2", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_2"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>'s <PERSON><PERSON>", "gti": "amzn1.dv.gti.429219c8-2f37-431a-8faa-292d53e400c2", "transformItemId": "amzn1.dv.gti.429219c8-2f37-431a-8faa-292d53e400c2", "synopsis": "Based on the best-selling book series by <PERSON>, <PERSON><PERSON> follows the world of high school student <PERSON> after he moves to Kasselton, New Jersey. Aided by his loyal friends, <PERSON><PERSON> and <PERSON><PERSON>, and with <PERSON><PERSON>'s signature blend of suspense and intricate storytelling, <PERSON> unravels a series of interconnected mysteries in this otherwise sleepy suburban town.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "18+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Suspense", "Action"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "18+", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/599bec778f86cf35cbbcd6818bfcb395d4af2aeb37575040f8116c3114527a9f.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/704a101a0493ed7f80f4302b020b95a8fa0a659d2b3692ba316886b14072be31.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/466a315d1fcf712f85d5e88d1f49864a4cbbeee142b2415e17a82a275e6cfa80.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/8f89caa1ba93d50090b8ba84cd34f52654f6d7d4d0b0c04bb7ab6e91313547c3.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/53a475d638fb3d589efd2a79057f75de564d3115f3031450dbce91dd75a568fa.jpg", "publicReleaseDate": 1692316800000, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.54fe3ad8-7dad-4f9f-bafb-5b381183d678", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_3", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_3"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.54fe3ad8-7dad-4f9f-bafb-5b381183d678", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_3", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_3"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "The Godfather", "gti": "amzn1.dv.gti.64a9f786-efb1-28d3-bf27-6038d12cc53a", "transformItemId": "amzn1.dv.gti.64a9f786-efb1-28d3-bf27-6038d12cc53a", "synopsis": "<PERSON>ito Corleone es el jefe de la familia mafiosa en Nueva York. <PERSON>, el hijo menor de Vito y un infante de marina condecorado de la Segunda Guerra Mundial, parecen no estar...", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense", "Drama"], "starringCast": ["Al Pacino", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/13f09361925ed0ee97b30eaf29b477114becb81754e4365d3f1fa0f754671e8b.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/980b3c1da66ef3e6c89f2584601f6c6d371a4cacda1eb1e4c94fc5afd9a98d17.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/26f2fc4c76463d6a95efa8be35228ab09de058e6a046e20e21c6036b3b4b3ca2.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/4aab4fac29ff5ab7a2a4c4859bfbb1fe348f2139b246728d863860f55673f40c.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f74de7b55afa88aea216ac32f40b74428e0893929bdf41c143ec102c841925aa.jpg", "publicReleaseDate": 70243200000, "runtimeSeconds": 10176, "runtime": "169 min", "overallRating": 4.8, "totalReviewCount": 24217, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.64a9f786-efb1-28d3-bf27-6038d12cc53a", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_4", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_4"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 3X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.64a9f786-efb1-28d3-bf27-6038d12cc53a", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_4", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_4"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Message Man", "gti": "amzn1.dv.gti.28b46ed2-7ded-bea8-c310-d955ba5fafd2", "transformItemId": "amzn1.dv.gti.28b46ed2-7ded-bea8-c310-d955ba5fafd2", "synopsis": "A retired assassin's past catches up with him and his brutality surfaces as he goes on a final killing spree to make things right.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16+", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/acc6c22a27f84b7f903149aaee49b4dfc60bbbc6b1456061b0439c8434868027.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6fa123dcf9748181ba23e439319190f304f4d58488609b0f33869781f912d67d.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/24ead603da0900ef701e5ea20b274d359241a8e14101254a1d8bcd3dd7943110.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "publicReleaseDate": 1551139200000, "runtimeSeconds": 5410, "runtime": "90 min", "overallRating": 4.1, "totalReviewCount": 648, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.28b46ed2-7ded-bea8-c310-d955ba5fafd2", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_5", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_5"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.28b46ed2-7ded-bea8-c310-d955ba5fafd2", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_5", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_5"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "Mirzapur", "gti": "amzn1.dv.gti.06b3417e-ddab-ac39-25a8-e2a0bfcc59b7", "transformItemId": "amzn1.dv.gti.06b3417e-ddab-ac39-25a8-e2a0bfcc59b7", "synopsis": "The iron-fisted <PERSON><PERSON><PERSON><PERSON><PERSON> is a millionaire carpet exporter and the mafia don of Mirzapur. His son, <PERSON><PERSON>, is an unworthy heir who will stop at nothing to inherit his father’s legacy. An incident at a wedding forces him to cross paths with <PERSON><PERSON><PERSON>, an upstanding lawyer, and his sons, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>. It snowballs into a game of ambition, power and greed in this lawless city.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/96a00698329270009f7f4c9605ef8efb612a9ee867d0451badbb9a6441ebe6b3.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8c8fd6eae9245c7ee93eeea824c8092055fbcd90e96067947e49ed0d3500ec4d.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9c869b13efd390823231e6c841b7cae7a0890104f6cf66f8e7a8f9803900b3d6.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/2fba6e656fc96aff9befb79816ed7f61b0e99ec83370984e3997da9d48da1efd.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/837d9d82ce0bfa9b98a6c0739e51108695e2e64624e0db9317790c193c83c45b.png", "publicReleaseDate": 1542240000000, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.16b3417f-2d64-022e-1ab5-ff0ad034364c", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_6", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_6"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": ""}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.16b3417f-2d64-022e-1ab5-ff0ad034364c", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_6", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_6"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": true}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Kill Chain", "gti": "amzn1.dv.gti.3ab6cc31-1d85-2acb-8e2d-ab1a01bbb13e", "transformItemId": "amzn1.dv.gti.3ab6cc31-1d85-2acb-8e2d-ab1a01bbb13e", "synopsis": "In a web of violence, a hotel shootout ignites a deadly chain reaction of cops, gangsters, and killers entangled in murder, betrayal, and vengeance.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense", "Action"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/0c8f8832d294de041ad06b4864085ef0d0613dbb6b00d4d74f62010ef4f42b71.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/faa18a2917b8eeb1cbe6f5adf180c84321b093c332146ee6c7f3441f1d227b41.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3f56d48b33ebd58c93f73b3914970aef2a91b8a94a29846df65759735c1d8b0b.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/ec9a2a9deb43faa338af0c50a15042154f7326dc42eff67e1d8301d55226b61b.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d7310fac370c4710052747cfa0d2b57921c9f65235bc4666fc66cda76b396259.jpg", "publicReleaseDate": 1571356800000, "runtimeSeconds": 5478, "runtime": "91 min", "overallRating": 3.7, "totalReviewCount": 614, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3ab6cc31-1d85-2acb-8e2d-ab1a01bbb13e", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_7", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_7"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3ab6cc31-1d85-2acb-8e2d-ab1a01bbb13e", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_7", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_7"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON>", "gti": "amzn1.dv.gti.c4ab074c-6ee1-ec5b-6889-ccacbc10259e", "transformItemId": "amzn1.dv.gti.c4ab074c-6ee1-ec5b-6889-ccacbc10259e", "synopsis": "Based on <PERSON>’s best-selling novels, <PERSON> (<PERSON>), an LAPD homicide detective, stands trial for the shooting of a serial murder suspect - just as a cold case involving the remains of a boy forces <PERSON><PERSON> to confront his past. As daring recruit, <PERSON> (<PERSON>), catches his eye, and departmental politics heat up, <PERSON><PERSON> will pursue justice at all costs.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7843a151e2a08b74feea8a0a144071eb0b78d274ac4a8b866b8ad09a9107f387.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/98b0eda50d087b341ca1b6a4ad0d5244d9a3955722151350eecfb604adedbb62.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f7a8f1c71909f00dad57c9ccb4b4077d845d50130fc751c7ebb8cab3c9407468.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/e3229cff68ef21cb473a110e6a1674b310bdb81e5bc031295913b039180eef26.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/27c9025725864ab43622ce932f47f4582db9e8dd7a055fdd3d80c358274a229e.jpg", "publicReleaseDate": 1391644800000, "overallRating": 4.6, "totalReviewCount": 101710, "numberOfSeasons": 7, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2ab0767-236e-26d2-c742-48a3f46ddb2c", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_8", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_8"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2ab0767-236e-26d2-c742-48a3f46ddb2c", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_8", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_8"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "The Baytown Outlaws", "gti": "amzn1.dv.gti.4aba97cd-0ada-091c-dcf6-7b91a0604d69", "transformItemId": "amzn1.dv.gti.4aba97cd-0ada-091c-dcf6-7b91a0604d69", "synopsis": "After a violent attack by her ex-husband, <PERSON> recruits three outlawed brothers to retrieve her godson <PERSON>, igniting a Southern showdown.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Action", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9d561fb54eea988b297eaa634481fdb85469afacb4292af170a538dbf67f79a6.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/a9cb95dd73ef5d11a0964ea9f430bf3a3edfb0d00bd288780c898299a9f5f4d9.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5e6a3d6407c04a5252c6db993a8d63239ef610f16ca58d7001c24ae40cc5f4f7.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "publicReleaseDate": 1357862400000, "runtimeSeconds": 5909, "runtime": "98 min", "overallRating": 4.3, "totalReviewCount": 2391, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.4aba97cd-0ada-091c-dcf6-7b91a0604d69", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_9", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_9"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.4aba97cd-0ada-091c-dcf6-7b91a0604d69", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_9", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_9"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "Dexter: New Blood", "gti": "amzn1.dv.gti.e545965b-c7fc-4dc3-ba03-96ddaa031b04", "transformItemId": "amzn1.dv.gti.e545965b-c7fc-4dc3-ba03-96ddaa031b04", "synopsis": "America's favorite serial killer is back and so is his Dark Passenger.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d597f7753c45756381b26fe1177aea39e3e18e719fd44822ca31a1a8bb31396e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/fc8f5f250d097aac64567a05cd8ee68018b781382393b332939305c57ab8fa46.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e83dbe828b52ada51e5c7909742c1edeab84a3f47ce54e758ef3cfc758717199.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/paramountpremium/logos/channels-logo-white._CB583166680_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4338a2a98855088fea53d4a227a73a2d4a81d249c897bd88902a06919fa7f0af.jpg", "publicReleaseDate": 1636243200000, "overallRating": 4.6, "totalReviewCount": 888, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1da5fbd5-a3ed-42f0-ad0a-992afd21d796", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_10", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_10"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": ""}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1da5fbd5-a3ed-42f0-ad0a-992afd21d796", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_10", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_10"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Gladiator", "gti": "amzn1.dv.gti.f2a9f680-2275-2949-77fd-e67f07b680f0", "transformItemId": "amzn1.dv.gti.f2a9f680-2275-2949-77fd-e67f07b680f0", "synopsis": "Set in ancient times, <PERSON>'s epic historical drama follows a man's quest to reclaim his identity and fight for his people's freedom.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Action", "Adventure"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8069034c05b657fa953f72f0f2c7271d5dfe0a4760afb8ec00d2f6e64fbed16c.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/5d3e8ce8420c2661d9afea3c468ecefc77682f06db190bbbd166a0a93664b3ed.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9adfb5d2e8424c54f7e4ebc09214fbd6cff7ab4a6c67b7dbe3619939c3b77d2b.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/357866cc73776c51a546ec748e42304c622a5570da8ae6be531b040e132694bc.jpg", "publicReleaseDate": 957484800000, "runtimeSeconds": 8920, "runtime": "148 min", "overallRating": 4.8, "totalReviewCount": 12780, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2a9f680-2275-2949-77fd-e67f07b680f0", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_11", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_11"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 5X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2a9f680-2275-2949-77fd-e67f07b680f0", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_11", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_11"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON>", "gti": "amzn1.dv.gti.04b2b177-798b-2b58-9c62-1fda83d36dc6", "transformItemId": "amzn1.dv.gti.04b2b177-798b-2b58-9c62-1fda83d36dc6", "synopsis": "An incredible true story depicting <PERSON>'s journey to the top of the Gambino Family, one of North America's most formidable crime organizations.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/feb51c7bb769ff3d6618039c9264e8d2fbc0e5d4e972ceee300bd4bb4159bc53.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9c2ad31d0f1ba3b18dccd228d2135730f75707be1e61355b663052d24df28b3c.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/2eb21cec369b435ac7dc221b0601a6b3c83ea0037493de1a949c1d1664211d3b.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "publicReleaseDate": 1529020800000, "runtimeSeconds": 6247, "runtime": "104 min", "overallRating": 4.1, "totalReviewCount": 2975, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.04b2b177-798b-2b58-9c62-1fda83d36dc6", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_12", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_12"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.04b2b177-798b-2b58-9c62-1fda83d36dc6", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr61ee64_dast_3_12", "pillId": "8", "pillVersion": "demo-20241025"}, "refMarker": "hm_ass_c_sr61ee64_dast_3_12"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_ass_c_sr61ee64_3", "ClientSideMetrics": "1004|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1JCNUU2MEJDNjAxQTQaACIIc3I2MWVlNjQSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6G1BWRGlzY292ZXJ5QXNzaXN0YW50U2VydmljZUIYRGlzY292ZXJ5QXNzaXN0YW50VGl0bGVzSgpTZWFyY2hGbGV4Shlhc3Npc3RhbnRUaXRsZXNCZWxvd1BpbGxzUgtub3RFbnRpdGxlZFoAYgRHcmlkaANyAHo4ZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT2CAQNhbGyKAQCSAQA="}, "tags": [], "type": "GRID"}, {"title": "", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSQkY4RUFEQzk5MkM5IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "items": [{"title": "Explore more topics", "imageUrl": "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v1.png", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "assistant", "pageType": "home", "analytics": {"refMarker": "hm_ass_c_sr5ec29d_dast_4_1"}, "refMarker": "hm_ass_c_sr5ec29d_dast_4_1", "serviceToken": "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwdWRXeHNmU3dpYVc1MFpYSnVZV3hCYzNOcGMzUmhiblJUZEdGMFpTSTZleUp6Wld4bFkzUmxaRkJwYkd4SlpDSTZiblZzYkN3aWNHbHNiSE5XWlhKemFXOXVJam9pWkdWdGJ5MHlNREkwTVRBeU5TSXNJbk4wWVhSbFMyVjVJam9pWXpBME1URmxOVGt0WkRWaE15MDBOV0l6TFdKbVltTXRZakprWlRJMk56UmlPRFJsSWl3aWNHbHNiRlpoY21sbGRIbFViMHhoYzNSU1pYUnlhV1YyWldSSmJtUmxlRTFoY0NJNmV5SlZibkJsY25OdmJtRnNhWHBsWkVKeWIyRmtJam94T1gwc0ltbHpSbWx5YzNSUVlXZGxJanBtWVd4elpYMTkiXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6ImV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09OjE3MzA4MDgwOTMwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="}, "widgetType": "imageTextLink", "transformItemId": "landing:home:assistant", "cardType": "LINK_CARD"}], "analytics": {"refMarker": "hm_ass_c_sr5ec29d_4", "ClientSideMetrics": "988|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1JCRjhFQURDOTkyQzkaACIIc3I1ZWMyOWQSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oKU2VhcmNoRmxleEofYXNzaXN0YW50R2VuZXJhdGVOZXdQaWxsc0J1dHRvblILbm90RW50aXRsZWRaAGISRGlzY292ZXJ5QXNzaXN0YW50aARyAHo4ZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT2CAQNhbGyKAQCSAQA="}, "tags": [], "itemDisplaySize": "Small", "type": "DISCOVERY_ASSISTANT"}], "subNav": [], "pageMetadata": {"title": "AI Topics", "type": "SelectedPillPageMetadata", "badge": "CoolBadge", "selectedPill": "Passionate romance", "displayMiniDetails": false}}, "metadata": {"requestId": "evYj5XtMkUwRyqPzoUEVD8lF8MKo9n3vGSUAyYpcJ0RHmOY63tw5bg==", "requestedTransformId": "lr/collections/collectionsPageInitial", "domain": "prod", "realm": "us-east-1", "timestamp": "2024-11-05T12:01:34.182652Z"}}