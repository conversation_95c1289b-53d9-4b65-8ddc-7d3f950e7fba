use ignx_compositron::prelude::metric;
use mockall::automock;
use strum_macros::EnumIter;

#[derive(Debug, PartialEq, <PERSON>lone, EnumIter)]
pub enum MetricNames {
    PageActionNetworkLatency,
    PageActionProcessingLatency,
    PageActionOverallLatency,
    PageActionBegin,
    PageActionSuccessRate,
    ComponentActionCount,
}

impl MetricNames {
    pub fn as_str(&self) -> &'static str {
        match self {
            MetricNames::PageActionNetworkLatency => "PageAction.NetworkLatency",
            MetricNames::PageActionProcessingLatency => "PageAction.ProcessingLatency",
            MetricNames::PageActionOverallLatency => "PageAction.OverallLatency",
            MetricNames::PageActionBegin => "PageAction.Begin",
            MetricNames::PageActionSuccessRate => "PageAction.SuccessRate",
            MetricNames::ComponentActionCount => "ComponentAction.Count",
        }
    }
}

pub struct MetricCaller;

#[automock]
impl MetricCaller {
    pub fn report_metric<'a>(metric: MetricNames, value: u32, dimensions: Vec<(&'a str, &'a str)>) {
        let dimensions: Vec<(String, String)> = dimensions
            .into_iter()
            .map(|(k, v)| (k.to_string(), v.to_string()))
            .collect();

        metric!(metric.as_str(), value, dimensions);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use strum::IntoEnumIterator;

    #[test]
    fn metric_names_map_correctly() {
        for name in MetricNames::iter() {
            match name {
                MetricNames::PageActionNetworkLatency => {
                    assert_eq!(name.as_str(), "PageAction.NetworkLatency")
                }
                MetricNames::PageActionProcessingLatency => {
                    assert_eq!(name.as_str(), "PageAction.ProcessingLatency")
                }
                MetricNames::PageActionOverallLatency => {
                    assert_eq!(name.as_str(), "PageAction.OverallLatency")
                }
                MetricNames::PageActionBegin => {
                    assert_eq!(name.as_str(), "PageAction.Begin")
                }
                MetricNames::PageActionSuccessRate => {
                    assert_eq!(name.as_str(), "PageAction.SuccessRate")
                }
                MetricNames::ComponentActionCount => {
                    assert_eq!(name.as_str(), "ComponentAction.Count")
                }
            }
        }
    }

    #[test]
    fn report_metric_works_correctly() {
        MetricCaller::report_metric(
            MetricNames::PageActionNetworkLatency,
            1,
            vec![("TestKey", "TestValue")],
        );

        // TODO: Ensure metric! called correctly when we have a way of covering it`.
    }
}
