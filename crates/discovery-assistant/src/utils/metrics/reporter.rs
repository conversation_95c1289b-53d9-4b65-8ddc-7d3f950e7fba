use ignx_compositron::time::Duration;
use location::PageType;
#[cfg(test)]
use mock_instant::Instant;
#[cfg(not(test))]
use std::time::Instant;

use mockall::automock;
use mockall_double::double;

use crate::types::SupportedContainer;

#[double]
use super::caller::MetricCaller;
use super::caller::MetricNames;

pub struct DiscoveryAssistantReporter;

#[automock]
impl DiscoveryAssistantReporter {
    const PAGE_TYPE: &'static str = "DiscoveryAssistant";
    const DOWNSTREAM_NAME: &'static str = "collectionPageInitial";

    pub fn begin_request() {
        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", Self::DOWNSTREAM_NAME),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(MetricNames::PageActionBegin, 1, dimensions);
    }

    pub fn request_succeeded() {
        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", Self::DOWNSTREAM_NAME),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(MetricNames::PageActionSuccessRate, 1, dimensions);
    }

    pub fn request_failed() {
        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", Self::DOWNSTREAM_NAME),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(MetricNames::PageActionSuccessRate, 0, dimensions);
    }

    pub fn network_latency(duration: Duration) {
        let latency = duration.as_millis() as u32;
        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", Self::DOWNSTREAM_NAME),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(MetricNames::PageActionNetworkLatency, latency, dimensions);
    }

    pub fn processing_latency(duration: Duration) {
        let latency = duration.as_millis() as u32;
        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", Self::DOWNSTREAM_NAME),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(
            MetricNames::PageActionProcessingLatency,
            latency,
            dimensions,
        );
    }

    pub fn overall_latency(start_time: Instant) {
        let now = Instant::now();
        let latency = now.duration_since(start_time).as_millis() as u32;
        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", "Load"),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(MetricNames::PageActionOverallLatency, latency, dimensions);
    }

    pub fn container_mounted(name: &SupportedContainer, _position: usize) {
        let container_name = name.to_string();

        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", "Mounted"),
            ("componentName", &container_name),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(MetricNames::ComponentActionCount, 1, dimensions);
    }

    pub fn transition_to(next_page: PageType) {
        let next_page_name = next_page.to_string();

        let dimensions = vec![
            ("pageType", Self::PAGE_TYPE),
            ("actionName", "TransitionTo"),
            ("componentName", &next_page_name),
            ("activeLayer", "Wasm"),
        ];

        MetricCaller::report_metric(MetricNames::ComponentActionCount, 1, dimensions);
    }
}

#[cfg(test)]
mod tests {
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::prelude::SignalGetUntracked;
    use ignx_compositron::time::Duration;
    use mock_instant::Instant;
    use mock_instant::MockClock;
    use serial_test::serial;

    use crate::utils::containers::get_containers;
    use crate::utils::metrics::caller::MetricNames;
    use crate::utils::metrics::caller::__mock_MockMetricCaller::__report_metric::Context;
    use crate::utils::test_utils::default_collection_response;

    use super::*;

    fn expect_metric(
        expected_metric_name: MetricNames,
        expected_value: u32,
        expected_dimensions: Vec<(&'static str, &'static str)>,
    ) -> Context {
        let report_metric_ctx = MetricCaller::report_metric_context();
        report_metric_ctx
            .expect()
            .withf(move |metric, value, dimensions| {
                metric == &expected_metric_name
                    && value == &expected_value
                    && dimensions == &expected_dimensions
            })
            .times(1)
            .returning(|_, _, _| ());

        report_metric_ctx
    }

    #[test]
    #[serial]
    fn should_report_begin_request() {
        let _ctx = expect_metric(
            MetricNames::PageActionBegin,
            1,
            vec![
                ("pageType", "DiscoveryAssistant"),
                ("actionName", "collectionPageInitial"),
                ("activeLayer", "Wasm"),
            ],
        );

        DiscoveryAssistantReporter::begin_request();
    }

    #[test]
    #[serial]
    fn should_report_request_succeeded() {
        let _ctx = expect_metric(
            MetricNames::PageActionSuccessRate,
            1,
            vec![
                ("pageType", "DiscoveryAssistant"),
                ("actionName", "collectionPageInitial"),
                ("activeLayer", "Wasm"),
            ],
        );

        DiscoveryAssistantReporter::request_succeeded();
    }

    #[test]
    #[serial]
    fn should_report_request_failed() {
        let _ctx = expect_metric(
            MetricNames::PageActionSuccessRate,
            0,
            vec![
                ("pageType", "DiscoveryAssistant"),
                ("actionName", "collectionPageInitial"),
                ("activeLayer", "Wasm"),
            ],
        );

        DiscoveryAssistantReporter::request_failed();
    }

    #[test]
    #[serial]
    fn should_report_network_latency() {
        let _ctx = expect_metric(
            MetricNames::PageActionNetworkLatency,
            42,
            vec![
                ("pageType", "DiscoveryAssistant"),
                ("actionName", "collectionPageInitial"),
                ("activeLayer", "Wasm"),
            ],
        );

        DiscoveryAssistantReporter::network_latency(Duration::from_millis(42));
    }

    #[test]
    #[serial]
    fn should_report_parsing_latency() {
        let _ctx = expect_metric(
            MetricNames::PageActionProcessingLatency,
            42,
            vec![
                ("pageType", "DiscoveryAssistant"),
                ("actionName", "collectionPageInitial"),
                ("activeLayer", "Wasm"),
            ],
        );

        DiscoveryAssistantReporter::processing_latency(Duration::from_millis(42));
    }

    #[test]
    #[serial]
    fn should_report_overall_latency() {
        let _ctx = expect_metric(
            MetricNames::PageActionOverallLatency,
            42,
            vec![
                ("pageType", "DiscoveryAssistant"),
                ("actionName", "Load"),
                ("activeLayer", "Wasm"),
            ],
        );

        let start_time = Instant::now();
        MockClock::advance(Duration::from_millis(42));

        DiscoveryAssistantReporter::overall_latency(start_time);
    }

    #[test]
    #[serial]
    fn should_report_container_mounts() {
        launch_only_scope(|scope| {
            let _ctx = expect_metric(
                MetricNames::ComponentActionCount,
                1,
                vec![
                    ("pageType", "DiscoveryAssistant"),
                    ("actionName", "Mounted"),
                    ("componentName", "DiscoveryAssistantHeader"),
                    ("activeLayer", "Wasm"),
                ],
            );

            let response = default_collection_response();
            let containers = get_containers(scope, response);
            let first_container = containers.first().unwrap().model.get_untracked();

            DiscoveryAssistantReporter::container_mounted(&first_container, 3);
        });
    }

    #[test]
    #[serial]
    fn should_report_transitions_to() {
        let _ctx = expect_metric(
            MetricNames::ComponentActionCount,
            1,
            vec![
                ("pageType", "DiscoveryAssistant"),
                ("actionName", "TransitionTo"),
                ("componentName", "RUST_DETAILS"),
                ("activeLayer", "Wasm"),
            ],
        );

        let next_page = PageType::Rust(location::RustPage::RUST_DETAILS);

        DiscoveryAssistantReporter::transition_to(next_page);
    }
}
