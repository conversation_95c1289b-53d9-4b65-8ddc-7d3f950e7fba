use super::{
    metrics::reporter::{
        MockDiscoveryAssistantReporter,
        __mock_MockDiscoveryAssistantReporter::{self},
    },
    navigation::DiscoveryPageParams,
    network::{collection_parser, CollectionResponse},
};
use crate::cache::{CachedAssistantPage, DiscoveryAssistantCache, DiscoveryAssistantCaching};
use crate::utils::impressions::ImpressionsSender;
use crate::{
    state::page_state::provide_discovery_page_ctx,
    utils::{
        navigation::{
            MockDiscoveryPageNavigation,
            __mock_MockDiscoveryPageNavigation::__new::Context as NavContext,
        },
        network::{MockNetworkClient, __mock_MockNetworkClient::__new::Context as NetworkContext},
    },
};
use cache::cache_invalidation::emitter::CacheInvalidationEventEmitter;
use clickstream::test_utils::provide_clickstream_client_spy;
use common_transform_types::actions::{SwiftAction, TitleOpenModalAction, TransitionAction};
use container_types::ui_signals::CommonCarouselCardMetadata;
use cross_app_events::ImpressionData;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::prelude::{create_rw_signal, provide_context, AppContext, Scope};
use location::{Location, PageType, RustPage};
use mockall::{predicate::eq, Sequence};
use network::{common::DeviceProxyResponse, RequestError};
use router::hooks::setup_mock_routing_ctx;
use rust_features::provide_context_test_rust_features;
use rust_features::MockRustFeaturesBuilder;
use std::cell::RefCell;
use std::rc::Rc;

#[derive(Clone)]
pub enum DiscoveryResponse {
    TopLevel,
    SubLevel,
}

#[derive(Clone)]
pub enum NetworkResponseType {
    Loading,
    Loaded(DiscoveryResponse),
    Error,
}

pub fn get_discovery_response(response: &DiscoveryResponse) -> CollectionResponse {
    let json = match response {
        DiscoveryResponse::TopLevel => {
            include_str!("../mocks/discovery_assistant_top_level.json").to_string()
        }
        DiscoveryResponse::SubLevel => {
            include_str!("../mocks/discovery_assistant_sub_level.json").to_string()
        }
    };
    collection_parser(json)
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .expect("Expected response to be parsed correctly")
}

fn create_network_mock_from_responses(
    client_context: &NetworkContext,
    responses: Vec<NetworkResponseType>,
) {
    client_context.expect().returning(move |_| {
        let mut seq = Sequence::new();
        let mut mock_client = MockNetworkClient::default();

        responses.iter().for_each(|r| {
            let res = r.clone();

            mock_client
                .expect_get_collection()
                .once()
                .in_sequence(&mut seq)
                .returning(move |_service_token, success_cb, failure_cb| match &res {
                    NetworkResponseType::Loading => (),
                    NetworkResponseType::Loaded(response_type) => {
                        let response = get_discovery_response(response_type);
                        success_cb(response);
                        ()
                    }
                    NetworkResponseType::Error => {
                        failure_cb(RequestError::Http {
                            code: 500,
                            body: None,
                            headers: vec![],
                        });
                        ()
                    }
                });
        });

        mock_client
    });
}

fn create_cache_mock_from_entries(scope: Scope, cache_entries: Vec<CacheEntry>) {
    let invalidation_event_emitter = CacheInvalidationEventEmitter::new_rc_without_rpc();
    let page_cache: DiscoveryAssistantCache =
        DiscoveryAssistantCaching::new(scope, invalidation_event_emitter);
    for entry in cache_entries {
        page_cache.put(entry.key, entry.value)
    }
    provide_context(scope, page_cache);
}

pub fn create_navigation_mock(
    expected_navigation: Option<Location>,
    current_location: Option<Location>,
    expected_back: usize,
) -> NavContext {
    let nav_client_context = MockDiscoveryPageNavigation::new_context();
    nav_client_context.expect().returning(move |_| {
        let mut mock_client = MockDiscoveryPageNavigation::default();

        let expected_params = current_location.as_ref().map_or_else(
            || DiscoveryPageParams::default(),
            |l| DiscoveryPageParams::from(l.clone()),
        );

        let is_back = current_location
            .as_ref()
            .map(|l| l.is_back("dummy"))
            .unwrap_or_default();
        if is_back {
            mock_client.expect_entered_from_back().return_const(true);
        } else {
            mock_client.expect_entered_from_back().return_const(false);
        }

        mock_client
            .expect_handle_page_enter()
            .returning(move |scope, on_enter| {
                on_enter(scope, expected_params.clone());
            });

        if let Some(location) = expected_navigation.clone() {
            mock_client
                .expect_navigate()
                .once()
                .with(eq(location))
                .return_const(());
        } else {
            mock_client.expect_navigate().return_const(());
        }

        if expected_back > 0 {
            mock_client
                .expect_back()
                .times(expected_back)
                .return_const(());
        }

        mock_client
    });

    nav_client_context
}

pub struct ReportingMock {
    begin_request_context: __mock_MockDiscoveryAssistantReporter::__begin_request::Context,
    request_succeeded_context: __mock_MockDiscoveryAssistantReporter::__request_succeeded::Context,
    request_failed_context: __mock_MockDiscoveryAssistantReporter::__request_failed::Context,
    container_mounted_context: __mock_MockDiscoveryAssistantReporter::__container_mounted::Context,
    transition_to_context: __mock_MockDiscoveryAssistantReporter::__transition_to::Context,
    overall_latency_context: __mock_MockDiscoveryAssistantReporter::__overall_latency::Context,
}

impl ReportingMock {
    pub fn mock_metrics(
        number_of_begins: Option<usize>,
        number_of_successes: Option<usize>,
        number_of_failures: Option<usize>,
        number_of_container_mounts: Option<usize>,
        number_of_transitions: Option<usize>,
        number_of_overall_latency: Option<usize>,
    ) -> Self {
        fn get_times(times: Option<usize>) -> mockall::TimesRange {
            let range: mockall::TimesRange = match times {
                Some(times) => times.into(),
                None => (0..).into(),
            };

            range
        }

        let begin_request_context = MockDiscoveryAssistantReporter::begin_request_context();
        begin_request_context
            .expect()
            .times(get_times(number_of_begins))
            .return_const(());

        let request_succeeded_context = MockDiscoveryAssistantReporter::request_succeeded_context();
        request_succeeded_context
            .expect()
            .times(get_times(number_of_successes))
            .return_const(());

        let request_failed_context = MockDiscoveryAssistantReporter::request_failed_context();
        request_failed_context
            .expect()
            .times(get_times(number_of_failures))
            .return_const(());

        let container_mounted_context = MockDiscoveryAssistantReporter::container_mounted_context();
        container_mounted_context
            .expect()
            .times(get_times(number_of_container_mounts))
            .return_const(());

        let transition_to_context = MockDiscoveryAssistantReporter::transition_to_context();
        transition_to_context
            .expect()
            .times(get_times(number_of_transitions))
            .return_const(());

        let overall_latency_context = MockDiscoveryAssistantReporter::overall_latency_context();
        overall_latency_context
            .expect()
            .times(get_times(number_of_overall_latency))
            .return_const(());

        Self {
            begin_request_context,
            request_succeeded_context,
            request_failed_context,
            container_mounted_context,
            transition_to_context,
            overall_latency_context,
        }
    }

    pub fn mock_catch_all_metrics() -> Self {
        let begin_request_context = MockDiscoveryAssistantReporter::begin_request_context();
        begin_request_context.expect().return_const(());

        let request_succeeded_context = MockDiscoveryAssistantReporter::request_succeeded_context();
        request_succeeded_context.expect().return_const(());

        let request_failed_context = MockDiscoveryAssistantReporter::request_failed_context();
        request_failed_context.expect().return_const(());

        let container_mounted_context = MockDiscoveryAssistantReporter::container_mounted_context();
        container_mounted_context.expect().return_const(());

        let transition_to_context = MockDiscoveryAssistantReporter::transition_to_context();
        transition_to_context.expect().return_const(());

        let overall_latency_context = MockDiscoveryAssistantReporter::overall_latency_context();
        overall_latency_context.expect().return_const(());

        Self {
            begin_request_context,
            request_succeeded_context,
            request_failed_context,
            container_mounted_context,
            transition_to_context,
            overall_latency_context,
        }
    }
}

pub struct DiscoveryMocks {
    #[allow(dead_code, reason = "not read but used in mocks")]
    network_client_context: NetworkContext,
    #[allow(dead_code, reason = "not read but used in mocks")]
    nav_client_context: NavContext,
    reporter_client_context: Option<ReportingMock>,
}

fn create_non_transitionable_action() -> TransitionAction {
    TransitionAction::openModal(TitleOpenModalAction {
        refMarker: "ref marker".to_string(),
        label: "open modal".to_string(),
        modalHeader: "modal header".to_string(),
        actionSegments: vec![],
    })
}

pub(crate) fn create_transitionable_action() -> TransitionAction {
    TransitionAction::legacyDetail(SwiftAction {
        text: None,
        analytics: Default::default(),
        refMarker: "ref marker".to_string(),
        pageId: "gti".to_string(),
        pageType: "legacyDetail".to_string(),
        serviceToken: None,
        journeyIngressContext: None,
    })
}

pub(crate) fn create_common_carousel_card_metadata(
    transitionable: bool,
) -> CommonCarouselCardMetadata {
    CommonCarouselCardMetadata::from_id_and_action(
        "carousel card id".to_string(),
        if transitionable {
            create_transitionable_action()
        } else {
            create_non_transitionable_action()
        },
    )
}

#[derive(Debug, Clone)]
pub struct ImpressionTestHelper {
    view_impressions_sent: Rc<RefCell<Vec<(ViewImpressionData, ImpressionData)>>>,
    select_impressions_sent: Rc<RefCell<Vec<ImpressionData>>>,
    highlight_impressions_sent: Rc<RefCell<Vec<ImpressionData>>>,
}

impl ImpressionTestHelper {
    pub fn new() -> Self {
        ImpressionTestHelper {
            view_impressions_sent: Rc::new(RefCell::new(Vec::new())),
            select_impressions_sent: Rc::new(RefCell::new(Vec::new())),
            highlight_impressions_sent: Rc::new(RefCell::new(Vec::new())),
        }
    }

    pub fn setup_mocks(&self, scope: Scope) {
        let view_context = ImpressionsSender::send_view_impression_event_context();
        let view_impressions_sent = self.view_impressions_sent.clone();
        view_context.expect().times(..).returning_st(
            move |_, view_impression_data: ViewImpressionData, impression_data: ImpressionData| {
                view_impressions_sent
                    .borrow_mut()
                    .push((view_impression_data.clone(), impression_data.clone()));
            },
        );
        provide_context(scope, Rc::new(view_context));

        let select_context = ImpressionsSender::send_select_impression_event_context();
        let select_impressions_sent = self.select_impressions_sent.clone();
        select_context
            .expect()
            .times(..)
            .returning_st(move |_, data| {
                select_impressions_sent.borrow_mut().push(data.clone());
            });
        provide_context(scope, Rc::new(select_context));

        let highlight_context = ImpressionsSender::send_highlight_impression_event_context();
        let highlight_impressions_sent = self.highlight_impressions_sent.clone();
        highlight_context
            .expect()
            .times(..)
            .returning_st(move |_, data| {
                highlight_impressions_sent.borrow_mut().push(data.clone());
            });

        provide_context(scope, Rc::new(highlight_context));
    }

    pub fn assert_view_impression_was_sent_for_ref_marker(
        &self,
        ref_marker: &str,
        expected_impression_data: ImpressionData,
    ) {
        let view_impressions = self.view_impressions_sent.borrow();
        let impression_data = view_impressions
            .iter()
            .map(|(_, impression)| impression)
            .find(|impression| {
                impression
                    .ref_marker
                    .as_ref()
                    .is_some_and(|x| x == ref_marker)
            })
            .expect(
                format!(
                    "No impression with ref_marker: {ref_marker} was sent, sent: {:?}",
                    view_impressions
                )
                .as_str(),
            );

        assert_eq!(*impression_data, expected_impression_data);
    }

    pub fn get_view_impressions_count(&self) -> usize {
        let view_impressions = self.view_impressions_sent.borrow();
        view_impressions.iter().len()
    }

    pub fn assert_select_impression_was_sent(
        &self,
        ref_marker: &str,
        expected_data: ImpressionData,
    ) {
        let select_impressions = self.select_impressions_sent.borrow();
        let impression_data = select_impressions
            .iter()
            .find(|&data| data.ref_marker == Some(ref_marker.to_string()))
            .expect(format!(
                "No select impression with ref_marker: {ref_marker} was sent. Impressions sent were: {:?}", select_impressions
            ).as_str(), );
        assert_eq!(*impression_data, expected_data);
    }

    pub fn assert_highlight_impression_was_sent(
        &self,
        ref_marker: &str,
        expected_data: ImpressionData,
    ) {
        let highlight_impressions = self.highlight_impressions_sent.borrow();
        let impression_data = highlight_impressions
            .iter()
            .find(|&data| data.ref_marker == Some(ref_marker.to_string()))
            .expect(
                format!(
                    "No highlight impression with ref_marker: {ref_marker} was sent. Impressions sent were: {:?}",
                    highlight_impressions
                )
                    .as_str(),
            );
        assert_eq!(*impression_data, expected_data);
    }
}

pub struct DiscoveryAssistantTestSetup<'a> {
    ctx: &'a AppContext,
    responses: Vec<NetworkResponseType>,
    cache_entries: Vec<CacheEntry>,
    expected_navigation: Option<Location>,
    current_location: Option<Location>,
    impressions: ImpressionTestHelper,
    expected_back: usize,
    include_metric_mocking: bool,
}

impl<'a> DiscoveryAssistantTestSetup<'a> {
    pub fn default(ctx: &'a AppContext) -> DiscoveryAssistantTestSetup<'a> {
        Self {
            ctx,
            responses: vec![],
            cache_entries: vec![],
            expected_navigation: None,
            current_location: None,
            expected_back: 0,
            include_metric_mocking: true,
            impressions: ImpressionTestHelper::new(),
        }
    }

    pub fn with_responses(mut self, responses: Vec<NetworkResponseType>) -> Self {
        self.responses = responses;
        self
    }

    pub fn with_cache_entries(mut self, cache_entries: Vec<CacheEntry>) -> Self {
        self.cache_entries = cache_entries;
        self
    }

    pub fn with_expected_navigation(mut self, expected_navigation: Location) -> Self {
        self.expected_navigation = Some(expected_navigation);
        self
    }

    pub fn with_current_location(mut self, current_location: Location) -> Self {
        self.current_location = Some(current_location);
        self
    }

    pub fn exclude_metric_mocking(mut self) -> Self {
        self.include_metric_mocking = false;
        self
    }

    pub fn with_expected_back_navigation(mut self, expected_back: usize) -> Self {
        self.expected_back = expected_back;
        self
    }

    pub fn setup(self) {
        let DiscoveryAssistantTestSetup {
            ctx,
            responses,
            cache_entries,
            expected_navigation,
            current_location,
            expected_back,
            include_metric_mocking,
            impressions,
        } = self;
        let scope = ctx.scope();
        let network_client_context = MockNetworkClient::new_context();
        let nav_client_context =
            create_navigation_mock(expected_navigation, current_location.clone(), expected_back);

        let reporter_client_context: Option<ReportingMock> =
            include_metric_mocking.then(ReportingMock::mock_catch_all_metrics);

        create_network_mock_from_responses(&network_client_context, responses);
        create_cache_mock_from_entries(scope, cache_entries);

        provide_clickstream_client_spy(scope);

        provide_discovery_page_ctx(&ctx);

        // Temp until all dependencies are onboarded to use MockRustFeatures in tests – https://issues.amazon.com/issues/LR-Rust-570
        provide_context_test_rust_features(ctx.scope());

        MockRustFeaturesBuilder::new()
            .set_is_rust_details_enabled(true)
            .build_into_context(scope);

        let location_for_mock_routing = current_location.unwrap_or_else(|| Location {
            pageType: PageType::Rust(RustPage::RUST_DISCOVERY_PAGE),
            ..Default::default()
        });
        setup_mock_routing_ctx(scope, |mock_routing| {
            mock_routing
                .expect_location()
                .return_const(create_rw_signal(scope, location_for_mock_routing.clone()));
        });

        impressions.setup_mocks(scope);
        provide_context(scope, impressions);

        create_rw_signal(
            ctx.scope(),
            DiscoveryMocks {
                network_client_context,
                nav_client_context,
                reporter_client_context,
            },
        );
    }
}

pub fn default_collection_response() -> CollectionResponse {
    get_discovery_response(&DiscoveryResponse::TopLevel)
}

#[derive(Clone)]
pub struct CacheEntry {
    pub key: String,
    pub value: CachedAssistantPage,
}
