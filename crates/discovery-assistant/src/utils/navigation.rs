use crate::DISCOVERY_PAGE_SOURCE;
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::prelude::{create_effect, Scope, SignalGetUntracked};
use ignx_compositron::reactive::SignalGet;
use location::{Location, PageType, RustPage};
use mockall::automock;
use router::hooks::{use_location_with_effect, use_router};
use router::RoutingContext;
use std::rc::Rc;
use transition_executor::IngressSource;

pub struct DiscoveryPageNavigation {
    router: RoutingContext,
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct DiscoveryPageParams {
    pub service_token: Option<String>,
    pub ingress_source: Option<IngressSource>,
}

impl DiscoveryPageParams {
    #[cfg(test)] // Enable in tests until its used in non-test code
    pub fn new(service_token: Option<String>, ingress_source: Option<IngressSource>) -> Self {
        Self {
            service_token,
            ingress_source,
        }
    }

    #[cfg(test)] // Enable in tests until its used in non-test code
    pub fn default() -> Self {
        Self {
            service_token: None,
            ingress_source: None,
        }
    }
}

impl From<Location> for DiscoveryPageParams {
    fn from(location: Location) -> Self {
        let service_token = location
            .pageParams
            .get("serviceToken")
            .and_then(|v| v.as_str())
            .map(String::from);

        let ingress_source = location
            .pageParams
            .get("ingressSource")
            .and_then(|value| serde_json::from_value(value.clone()).ok());

        Self {
            service_token,
            ingress_source,
        }
    }
}

#[automock]
impl DiscoveryPageNavigation {
    pub fn new(scope: Scope) -> Self {
        let router = use_router(scope);
        Self { router }
    }

    pub fn navigate(&self, location: Location) {
        self.router.navigate(location, DISCOVERY_PAGE_SOURCE)
    }

    pub fn handle_page_enter(
        &self,
        scope: Scope,
        on_enter: Rc<dyn Fn(Scope, DiscoveryPageParams)>,
    ) {
        let _ = self;
        let location = use_location_with_effect(scope);
        create_effect(scope, move |_| {
            let location = location.get();
            let page_type = PageType::Rust(RustPage::RUST_DISCOVERY_PAGE);
            let is_entering_page = location.to_location.pageType == page_type;
            let is_exiting_page = location
                .clone()
                .from_location
                .is_some_and(|from| from.pageType == page_type);

            if !is_entering_page && !is_exiting_page {
                return;
            }

            if is_entering_page {
                let page_params = DiscoveryPageParams::from(location.to_location);
                on_enter(scope, page_params);
            }
        });
    }

    /// Navigate back to the previous page
    pub fn back(&self) {
        self.router.back("DISCOVERY_PAGE_BACK")
    }

    /// If this page was entered by navigating back from another page
    pub fn entered_from_back(&self) -> bool {
        self.router
            .location()
            .get_untracked()
            .is_back(DISCOVERY_PAGE_SOURCE)
    }
}

#[cfg(test)]
mod tests {
    use std::rc::Rc;

    use ignx_compositron::{
        app::launch_only_app_context,
        prelude::{
            create_rw_signal, provide_context, RwSignal, SignalGetUntracked, SignalSet,
            SignalUpdate,
        },
    };
    use location::{Location, LocationWithEffect};
    use mockall::predicate::eq;
    use router::{rust_location, MockRouting, RoutingContext};
    use rstest::rstest;
    use serial_test::serial;

    use crate::utils::navigation::DiscoveryPageParams;

    use super::DiscoveryPageNavigation;

    struct States {
        has_entered: RwSignal<bool>,
    }

    fn create_location(to: Location, from: Option<Location>) -> LocationWithEffect {
        LocationWithEffect {
            from_location: from,
            to_location: to,
            exit_effect: None,
            enter_effect: None,
        }
    }

    #[test]
    #[serial]
    fn should_handle_entering_exiting_callbacks() {
        launch_only_app_context(|ctx| {
            let mut mock_routing_context = MockRouting::new();

            let current_location = create_location(rust_location!(RUST_COLLECTIONS), None);
            let location_with_effect = create_rw_signal(ctx.scope(), current_location);
            mock_routing_context
                .expect_location_with_effect()
                .return_const_st(location_with_effect.read_only());

            provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));

            let nav = DiscoveryPageNavigation::new(ctx.scope());

            let has_entered = create_rw_signal(ctx.scope(), false);
            let state = States { has_entered };

            nav.handle_page_enter(
                ctx.scope(),
                Rc::new(move |_, _| {
                    has_entered.set(true);
                }),
            );

            // We are on another page
            assert_eq!(state.has_entered.get_untracked(), false);

            // Enter the discovery page
            location_with_effect.update(|current| {
                *current = create_location(
                    rust_location!(RUST_DISCOVERY_PAGE),
                    Some(current.to_location.clone()),
                )
            });

            assert_eq!(state.has_entered.get_untracked(), true);

            // Leave the page
            location_with_effect.update(|current| {
                *current = create_location(
                    rust_location!(RUST_COLLECTIONS),
                    Some(current.to_location.clone()),
                )
            });

            assert_eq!(state.has_entered.get_untracked(), true);
        });
    }

    #[rstest]
    #[serial]
    #[case (rust_location!(RUST_DISCOVERY_PAGE), DiscoveryPageParams::default())]
    #[serial]
    #[case (rust_location!(RUST_DISCOVERY_PAGE, { "serviceToken" => "test" }), DiscoveryPageParams::new(Some("test".into()), None))]
    fn should_pass_page_params_to_on_enter(
        #[case] location: Location,
        #[case] expected_params: DiscoveryPageParams,
    ) {
        launch_only_app_context(move |ctx| {
            let mut mock_routing_context = MockRouting::new();

            let current_location = create_location(location, None);
            let location_with_effect = create_rw_signal(ctx.scope(), current_location);
            mock_routing_context
                .expect_location_with_effect()
                .return_const_st(location_with_effect.read_only());

            provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));

            let nav = DiscoveryPageNavigation::new(ctx.scope());

            let params = create_rw_signal::<Option<DiscoveryPageParams>>(ctx.scope(), None);

            nav.handle_page_enter(
                ctx.scope(),
                Rc::new(move |_, page_params| {
                    params.set(Some(page_params));
                }),
            );

            let params = params.get_untracked().expect("We should get params");
            assert_eq!(params, expected_params);
        });
    }

    #[test]
    #[serial]
    fn should_navigate() {
        launch_only_app_context(|ctx| {
            let mut mock_routing_context = MockRouting::new();

            mock_routing_context
                .expect_navigate()
                .times(1)
                .with(eq(rust_location!(RUST_DETAILS)), eq("DISCOVERY_ASSISTANT"))
                .return_const(());

            provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));

            let nav = DiscoveryPageNavigation::new(ctx.scope());

            nav.navigate(rust_location!(RUST_DETAILS));
        });
    }

    #[test]
    #[serial]
    fn should_navigate_back() {
        launch_only_app_context(|ctx| {
            let mut mock_routing_context = MockRouting::new();

            mock_routing_context.expect_back().times(1).return_const(());

            provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));

            let nav = DiscoveryPageNavigation::new(ctx.scope());

            nav.back();
        });
    }
}
