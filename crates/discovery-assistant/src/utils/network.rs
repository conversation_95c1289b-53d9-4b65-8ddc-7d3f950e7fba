use app_config::AppConfigContext;
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::{containers::Container, page_metadata::PageMetadata};
use container_types::network::container_query_params_constructor;
use ignx_compositron::device_information::DeviceInformation;
use ignx_compositron::network::http::HttpMethod;
use network::common::lrc_edge_constants::LRCEdgeTransforms;
use network::common::DeviceProxyResponse;
use network::URLBuilder;

use super::metrics::reporter::DiscoveryAssistantReporter;
#[cfg(test)]
use crate::utils::test_utils::default_collection_response;
use ignx_compositron::prelude::{AppContext, Scope};
use location::RustPage;
use mockall::mock;
use network::common::lrc_edge_constants::{CollectionInitialTypes, DynamicFeature, WidgetSchemes};
pub use network::NetworkClient;
use network::{RequestError, URLBuilderResult};
use network_parser::core::network_parse_from_str;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::rc::Rc;

pub fn generate_collections_url(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
    service_token: Option<String>,
    scope: Scope,
) -> URLBuilderResult {
    let mut dynamic_features = vec![];
    if let Some(f) = DynamicFeature::discovery_assistant_supported(scope) {
        dynamic_features.push(f);
    }
    let mut query_params = container_query_params_constructor(
        &RustPage::RUST_DISCOVERY_PAGE,
        "assistant",
        "home",
        &WidgetSchemes::DiscoveryAssistantV1,
        &String::from("living-room-react-focus"),
        &[],
        &dynamic_features,
        &[],
    )?;

    if let Some(service_token) = service_token {
        query_params.push(("serviceToken", service_token));
    }

    URLBuilder::for_lrc_edge(
        &LRCEdgeTransforms::CollectionInitial(CollectionInitialTypes::Landing),
        query_params,
        false,
        device_info,
        app_config,
    )
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(PartialEq, Debug)]
#[network(camelCaseAll)]
pub struct CollectionResponse {
    pub container_list: Vec<Container>,
    pub page_metadata: PageMetadata,
}

#[cfg(test)]
impl Default for CollectionResponse {
    fn default() -> Self {
        default_collection_response()
    }
}

pub fn collection_parser_callback(
    _ctx: &AppContext,
    response: String,
    _status_code: i32,
    cb: Box<dyn FnOnce(Result<DeviceProxyResponse<CollectionResponse>, RequestError>)>,
) {
    cb(collection_parser(response))
}

pub fn collection_parser(
    response: String,
) -> Result<DeviceProxyResponse<CollectionResponse>, RequestError> {
    network_parse_from_str(&response).map_err(RequestError::DeserializationFailed)
}

type SuccessCallback = Box<dyn FnOnce(CollectionResponse) + 'static>;
type FailureCallback = Box<dyn FnOnce(RequestError) + 'static>;

pub trait DiscoveryQueries {
    fn get_collection(
        &self,
        service_token: Option<String>,
        success_callback: SuccessCallback,
        failure_callback: FailureCallback,
    );
}

impl DiscoveryQueries for NetworkClient {
    fn get_collection(
        &self,
        service_token: Option<String>,
        success_callback: SuccessCallback,
        failure_callback: FailureCallback,
    ) {
        let url_result = generate_collections_url(
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
            service_token,
            self.ctx.scope,
        );

        let url = match url_result {
            Ok(url) => url,
            Err(e) => {
                failure_callback(RequestError::Builder(e.to_string()));
                return;
            }
        };

        let success = Box::new(move |v, _| success_callback(v));
        let failure = Box::new(move |e, _| failure_callback(e));

        #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-785")]
        let builder = self
            .builder(url, HttpMethod::Get, "collectionPageInitial")
            .with_network_latency(Rc::new(move |_, duration| {
                DiscoveryAssistantReporter::network_latency(duration);
            }))
            .with_processing_latency(Rc::new(move |_, duration| {
                DiscoveryAssistantReporter::processing_latency(duration);
            }))
            .with_parser(collection_parser_callback)
            .on_success(success)
            .on_failure(failure);

        builder.execute();
    }
}

mock! {
    pub NetworkClient {
        pub fn new(ctx: &AppContext) -> Self;
    }

    impl DiscoveryQueries for NetworkClient {
        fn get_collection(
            &self,
            service_token: Option<String>,
            success_callback: SuccessCallback,
            failure_callback: FailureCallback,
        );
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::utils::test_utils::{
        DiscoveryAssistantTestSetup, DiscoveryResponse, NetworkResponseType,
    };
    use app_config::test_utils::MockAppConfigBuilder;
    use auth::{AuthContext, MockAuth};
    use format_url::FormatUrl;
    use ignx_compositron::{
        app::launch_only_app_context,
        network::http::{HttpMethod, MockHttpRequestContext},
        prelude::provide_context,
    };
    use network::common::lrc_edge_constants::client_page_from_page_type;
    use router::{MockRouting, RoutingContext};
    use rust_features::MockRustFeaturesBuilder;
    use serial_test::serial;

    fn mock_context(scope: Scope) {
        let mock_routing_context = MockRouting::new();
        provide_context::<RoutingContext>(scope, Rc::new(mock_routing_context));

        let auth = Rc::new(MockAuth::new_without_params(scope));
        provide_context::<AuthContext>(scope, auth);

        MockRustFeaturesBuilder::new().build_into_context(scope);
        MockAppConfigBuilder::new()
            .set_ux_locale("en_US".to_string())
            .set_base_url(Some("https://base_url.com".to_string()))
            .set_supported_locales(vec!["de_DE".into(), "en_US".into(), "fr_FR".into()])
            .set_geo_location(Some("IE".into()))
            .build_into_context(scope);
    }

    fn expected_url(
        client_page: RustPage,
        page_id: &str,
        page_type: &str,
        widget_schema: WidgetSchemes,
        service_token: Option<&str>,
    ) -> String {
        let schema = String::from(widget_schema);
        let page = client_page_from_page_type(&client_page)
            .expect("Should be able to get client page type");
        let client_page = format!("{:?}", page);

        let params = vec![
                Some(("pageId", page_id)),
                Some(("pageType", page_type)),
                Some(("widgetScheme", &schema)),
                Some(("featureScheme", "react-v5")),
                Some(("decorationScheme", "lr-decoration-gen4-v4")),
                Some(("clientPage", &client_page)),
                Some(("clientFeatures", "DynamicBadging,Remaster,UseDynamicDatumParameters,UseV12TitleActionsView")),
                Some(("dynamicFeatures", "AppleTVODEnabled,CLIENT_DECORATION_ENABLE_DAAPI,DigitalBundlePvcPvc,DiscoveryAssistantSupported,LinearTitles,LiveTab,RustLRSupported,SupportsImageTextLinkTextInStandardHero")),
                Some(("presentationScheme", "living-room-react-focus")),
                Some(("isBrandingEnabled", "true")),
                Some(("regulatoryLabel", "true")),
                Some(("tentpoleSSMEnabled", "true")),
                Some(("useEpisodicSynopsis", "true")),
                Some(("useMiniSynopsis", "true")),
                match service_token {
                    Some(token) => Some(("serviceToken", token)),
                    None => None,
                },
                Some(("clientId", "pv-lrc-rust")),
                Some(("transformStore", "local")),
                Some(("transformStage", "prod")),
                Some(("javaTransformTimeout", "5000")),
                Some(("timeZoneId", "MockTimeZoneId")),
                Some(("gascEnabled", "true")),
                Some(("uxLocale", "en_US")),
                Some(("geoLocation", "IE")),
                Some(("supportedLocales", "de_DE,en_US,fr_FR")),
                Some(("firmware", "0.0.0")),
                Some(("manufacturer", "manufacturer")),
                Some(("chipset", "chipset")),
                Some(("model", "model")),
                Some(("operatingSystem", "osx")),
                Some(("deviceTypeID", "A71I8788P1ZV8")),
                Some(("deviceID", "random123456789")),
                Some(("osLocale", "GB")),
            ]
            .into_iter()
            .filter_map(|i| i)
            .collect();

        FormatUrl::new("https://base_url.com/lrcedge/getDataByJavaTransform/v1/lr/collections/collectionsPageInitial")
            .with_query_params(params)
            .format_url()
    }

    #[test]
    #[serial]
    fn should_call_network_client_with_right_params() {
        launch_only_app_context(|ctx| {
            mock_context(ctx.scope());

            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(vec![NetworkResponseType::Loaded(
                    DiscoveryResponse::TopLevel,
                )])
                .setup();

            let network_client = NetworkClient::new(&ctx);

            network_client.get_collection(
                Some(String::from("Test")),
                Box::new(|_r| {}),
                Box::new(|_f| {}),
            );

            let props = MockHttpRequestContext::get();
            assert_eq!(props.method, HttpMethod::Get);

            let expected_url = expected_url(
                RustPage::RUST_DISCOVERY_PAGE,
                "assistant",
                "home",
                WidgetSchemes::DiscoveryAssistantV1,
                Some("Test"),
            );
            assert_eq!(props.url, expected_url);
        })
    }

    #[test]
    #[serial]
    fn should_call_network_client_with_right_params_when_no_token() {
        launch_only_app_context(|ctx| {
            mock_context(ctx.scope());
            let responses = vec![NetworkResponseType::Loaded(DiscoveryResponse::TopLevel)];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .setup();
            let network_client = NetworkClient::new(&ctx);

            network_client.get_collection(None, Box::new(|_r| {}), Box::new(|_f| {}));

            let props = MockHttpRequestContext::get();

            let expected_url = expected_url(
                RustPage::RUST_DISCOVERY_PAGE,
                "assistant",
                "home",
                WidgetSchemes::DiscoveryAssistantV1,
                None,
            );
            assert_eq!(props.url, expected_url);
        })
    }
}
