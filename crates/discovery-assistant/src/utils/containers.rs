use crate::types::{ContainerType, SupportedContainer};
use common_transform_types::{
    containers::Container,
    page_metadata::{PageMetadata, PageMetadataType},
};
use container_types::{
    container_parsing::{IntoWithScope, TransformModelContainerUIParse},
    ui_signals::{CardGridModel, DiscoveryAssistantHeaderModel, DiscoveryAssistantModel},
};
use ignx_compositron::prelude::{create_rw_signal, Scope};
use location::{PageType, RustPage};

use super::network::CollectionResponse;

fn container_to_container_type(container: &Container, scope: Scope) -> Option<ContainerType> {
    match container {
        Container::DISCOVERY_ASSISTANT(container) => {
            let model = DiscoveryAssistantModel::from_transform_model(container, scope);
            model.map(|item| item.into_with_scope(scope))
        }
        Container::GRID(grid) => {
            let model = CardGridModel::from_transform_model(grid, scope);
            model.map(|item| item.into_with_scope(scope))
        }
        _ => None,
    }
}

fn metadata_to_container_type(metadata: PageMetadata, scope: Scope) -> Option<ContainerType> {
    match metadata.pageMetadataType {
        Some(PageMetadataType::SelectedPillPageMetadata) => Some(ContainerType {
            model: create_rw_signal(
                scope,
                SupportedContainer::DiscoveryAssistantHeader(create_rw_signal(
                    scope,
                    DiscoveryAssistantHeaderModel {
                        selected_pill: metadata.selectedPill,
                        logo_url: metadata.logoImage.and_then(|logo| logo.url),
                        title: metadata.title,
                        badge: metadata
                            .badge
                            .as_ref()
                            .and_then(|badge| {
                                badge.parse_resiliency_into_opt(
                                    PageType::Rust(RustPage::RUST_DISCOVERY_PAGE).to_string(),
                                    "DiscoveryPageHeader",
                                )
                            })
                            .cloned(),
                    },
                )),
            ),
            id: "header".into(),
        }),
        _ => None,
    }
}

pub fn get_containers(scope: Scope, results: CollectionResponse) -> Vec<ContainerType> {
    let mut container_types = results
        .container_list
        .iter()
        .filter_map(|container| container_to_container_type(container, scope))
        .collect::<Vec<ContainerType>>();

    if let Some(header) = metadata_to_container_type(results.page_metadata, scope) {
        container_types.insert(0, header);
    }

    container_types
}
