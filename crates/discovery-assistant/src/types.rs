use std::fmt::Display;

use cacheable_derive::Cacheable;
use container_types::container_parsing::FromWithScope;
use container_types::ui_signals::CardGridModelCacheable;
use container_types::ui_signals::ContainerModel;
use container_types::ui_signals::DiscoveryAssistantHeaderModel;
use container_types::ui_signals::DiscoveryAssistantHeaderModelCacheable;
use container_types::ui_signals::DiscoveryAssistantModelCacheable;
use container_types::ui_signals::{
    CardGridModel, CarouselIndexData, ContainerItem, DiscoveryAssistantModel, FocusRingProperties,
};
use containers::focus_ring::get_focus_ring_properties_for_container_sig;
use ignx_compositron::prelude::create_rw_signal;
use ignx_compositron::prelude::Scope;
use ignx_compositron::{
    id::Id,
    prelude::{RwSignal, SignalGetUntracked},
};

// NOTE: these are used as proxies to ContainerModels and cargo assumes dead_code as
// ContainerModels are used most of the time. We can consider refactoring these.
#[derive(Clone, Cacheable)]
pub enum SupportedContainer {
    #[allow(dead_code, reason = "Proxies to ContainerModel which uses them.")]
    DiscoveryAssistant(RwSignal<DiscoveryAssistantModel>),
    #[allow(dead_code, reason = "Proxies to ContainerModel which uses them.")]
    DiscoveryAssistantHeader(RwSignal<DiscoveryAssistantHeaderModel>),
    #[allow(dead_code, reason = "Proxies to ContainerModel which uses them.")]
    Grid(RwSignal<CardGridModel>),
}

impl From<SupportedContainer> for ContainerModel {
    fn from(container: SupportedContainer) -> Self {
        match container {
            SupportedContainer::DiscoveryAssistant(model) => {
                ContainerModel::DiscoveryAssistant(model)
            }
            SupportedContainer::Grid(model) => ContainerModel::Grid(model),
            SupportedContainer::DiscoveryAssistantHeader(model) => {
                ContainerModel::DiscoveryAssistantHeader(model)
            }
        }
    }
}

impl Display for SupportedContainer {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SupportedContainer::DiscoveryAssistant(model) => match model.get_untracked() {
                DiscoveryAssistantModel::Grid(_) => write!(f, "DiscoveryAssistantGrid"),
                DiscoveryAssistantModel::Pills(_) => write!(f, "DiscoveryAssistantPills"),
            },
            SupportedContainer::Grid(_) => write!(f, "Grid"),
            SupportedContainer::DiscoveryAssistantHeader(_) => {
                write!(f, "DiscoveryAssistantHeader")
            }
        }
    }
}

#[derive(Clone)]
pub struct ContainerType {
    pub model: RwSignal<SupportedContainer>,
    pub id: String,
}

impl ContainerType {
    pub fn new(scope: Scope, modal: SupportedContainer, id: String) -> Self {
        Self {
            model: create_rw_signal(scope, modal),
            id,
        }
    }
}

impl Id for ContainerType {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

impl PartialEq for ContainerType {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl FromWithScope<(DiscoveryAssistantModel, String)> for ContainerType {
    fn from_with_scope(scope: Scope, item: (DiscoveryAssistantModel, String)) -> Self {
        let (model, id) = item;
        let modal = SupportedContainer::DiscoveryAssistant(create_rw_signal(scope, model));
        ContainerType::new(scope, modal, id)
    }
}

impl FromWithScope<(CardGridModel, String)> for ContainerType {
    fn from_with_scope(scope: Scope, item: (CardGridModel, String)) -> Self {
        let (model, id) = item;
        let modal = SupportedContainer::Grid(create_rw_signal(scope, model));
        ContainerType::new(scope, modal, id)
    }
}

impl ContainerItem for ContainerType {
    fn get_opacity_for_index(&self, _index_offset: i32) -> f32 {
        1.0
    }

    fn get_focus_ring_properties(
        &self,
        data: CarouselIndexData,
        is_tts_enabled: bool,
    ) -> Option<FocusRingProperties> {
        get_focus_ring_properties_for_container_sig(
            self.model.get_untracked().into(),
            data,
            is_tts_enabled,
        )
    }
}

#[cfg(test)]
mod tests {
    use ignx_compositron::app::launch_only_scope;

    use crate::utils::{
        containers::get_containers,
        test_utils::{get_discovery_response, DiscoveryResponse},
    };

    use super::*;

    #[test]
    fn container_item_opacity() {
        launch_only_scope(|scope| {
            let response = get_discovery_response(&DiscoveryResponse::SubLevel);
            let containers = get_containers(scope, response);
            let container = containers.get(0).expect("We have a grid container");

            assert_eq!(container.get_opacity_for_index(-1), 1.0);
            assert_eq!(container.get_opacity_for_index(0), 1.0);
            assert_eq!(container.get_opacity_for_index(1), 1.0);
        });
    }

    #[test]
    fn container_item_focus_ring() {
        launch_only_scope(|scope| {
            let response = get_discovery_response(&DiscoveryResponse::TopLevel);
            let containers = get_containers(scope, response);
            let container = containers
                .get(1)
                .expect("We have a discover assistant container");

            assert!(container
                .get_focus_ring_properties(
                    CarouselIndexData {
                        scroll_index: 1,
                        focus_index: 3,
                        sub_index: Some(1),
                    },
                    false
                )
                .is_some());

            let container = containers
                .get(2)
                .expect("We have a pill version of discover container");

            assert!(container
                .get_focus_ring_properties(
                    CarouselIndexData {
                        scroll_index: 1,
                        focus_index: 1,
                        sub_index: None
                    },
                    false
                )
                .is_none());
        });
    }
}
