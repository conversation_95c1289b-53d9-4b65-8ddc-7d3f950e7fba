use crate::utils::network::CollectionResponse;
use cache::cache_invalidation::emitter::CacheInvalidationEventEmitterRc;
use cache::cache_invalidation::events::CacheInvalidationEvent;
use cache::expirable_lru_cache::ExpirableLruCache;
use cache::ExpirableLruCacheRc;
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::prelude::Scope;
use std::time::Duration;

pub type DiscoveryAssistantCache = ExpirableLruCacheRc<String, CachedAssistantPage>;

pub trait DiscoveryAssistantCaching: Clone {
    fn new(scope: Scope, invalidation_event_emitter: CacheInvalidationEventEmitterRc) -> Self
    where
        Self: Sized;
    #[allow(clippy::ptr_arg, reason = "Cache is defined to use String")]
    fn get(&self, key: &String) -> Option<CachedAssistantPage>;
    fn put(&self, key: String, value: CachedAssistantPage);
}

impl DiscoveryAssistantCaching for DiscoveryAssistantCache {
    fn new(scope: Scope, invalidation_event_emitter: CacheInvalidationEventEmitterRc) -> Self
    where
        Self: Sized,
    {
        let cache = ExpirableLruCache::new_rc_with_const_ttl(10, Duration::MAX);
        ExpirableLruCache::add_invalidation_event_handling(
            cache.clone(),
            scope,
            invalidation_event_emitter,
            on_cache_invalidation_event,
        );
        cache
    }

    fn get(&self, key: &String) -> Option<CachedAssistantPage> {
        self.borrow_mut().get(key).cloned()
    }

    fn put(&self, key: String, value: CachedAssistantPage) {
        self.borrow_mut().put(key, value);
    }
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub(crate) enum FocusPosition {
    Index(usize, Option<usize>),
    Id(String),
}

#[cfg(any(debug_assertions, test, not(target_arch = "wasm32")))]
impl Default for FocusPosition {
    fn default() -> Self {
        FocusPosition::Index(0, None)
    }
}

#[derive(Clone)]
#[derive_test_only(Default, PartialEq, Debug)]
pub(crate) struct Focus {
    pub container: FocusPosition,
    pub item: FocusPosition,
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct CachedAssistantPage {
    pub page: Option<CollectionResponse>,
    pub(crate) focus: Option<Focus>,
}

fn on_cache_invalidation_event(event: CacheInvalidationEvent, cache: DiscoveryAssistantCache) {
    match event {
        CacheInvalidationEvent::DroppedCard | CacheInvalidationEvent::WatchlistToggled => {}
        CacheInvalidationEvent::LegacyUndefinedReasonFromJS
        | CacheInvalidationEvent::TransitionedToPlayback
        | CacheInvalidationEvent::AcquisitionStart
        | CacheInvalidationEvent::TitleReaction
        | CacheInvalidationEvent::Inactivity
        | CacheInvalidationEvent::AuthChange
        | CacheInvalidationEvent::LocaleChange
        | CacheInvalidationEvent::ProfileChange
        | CacheInvalidationEvent::SyncContent => {
            cache.borrow_mut().mutate_all(move |_, value| {
                value.page = None;
            });
        }
    };
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::utils::test_utils::CacheEntry;
    use cache::cache_invalidation::emitter::CacheInvalidationEventEmitter;
    use ignx_compositron::app::launch_only_scope;
    use rstest::*;

    impl Default for CacheEntry {
        fn default() -> Self {
            CacheEntry {
                key: "key".to_string(),
                value: CachedAssistantPage {
                    page: Some(CollectionResponse::default()),
                    focus: Some(Focus::default()),
                },
            }
        }
    }

    trait TestCache {
        fn setup_cache() -> Self;
        fn with_entry(self, entry: CacheEntry) -> Self;
        fn with_default_entry(self) -> Self
        where
            Self: Sized,
        {
            self.with_entry(CacheEntry::default())
        }
        fn with_default_data_and_custom_key_entry(self, key: String) -> Self
        where
            Self: Sized,
        {
            self.with_entry(CacheEntry {
                key,
                ..CacheEntry::default()
            })
        }
    }

    impl TestCache for DiscoveryAssistantCache {
        fn setup_cache() -> Self {
            ExpirableLruCache::new_rc_with_const_ttl(10, Duration::MAX)
        }

        fn with_entry(self, entry: CacheEntry) -> Self {
            self.borrow_mut().put(entry.key, entry.value);
            self
        }
    }

    #[rstest]
    #[case(CacheInvalidationEvent::LegacyUndefinedReasonFromJS)]
    #[case(CacheInvalidationEvent::TransitionedToPlayback)]
    #[case(CacheInvalidationEvent::AcquisitionStart)]
    #[case(CacheInvalidationEvent::TitleReaction)]
    #[case(CacheInvalidationEvent::Inactivity)]
    #[case(CacheInvalidationEvent::AuthChange)]
    #[case(CacheInvalidationEvent::LocaleChange)]
    #[case(CacheInvalidationEvent::ProfileChange)]
    fn cache_invalidation_events_evict_all_page_data_but_not_focus_data(
        #[case] event: CacheInvalidationEvent,
    ) {
        let keys: [&str; 3] = ["key-0", "key-1", "key-2"];
        let cache = DiscoveryAssistantCache::setup_cache()
            .with_default_data_and_custom_key_entry(keys[0].to_string())
            .with_default_data_and_custom_key_entry(keys[1].to_string())
            .with_default_data_and_custom_key_entry(keys[2].to_string());

        on_cache_invalidation_event(event, cache.clone());
        let mut cache_mut = cache.borrow_mut();
        for key in keys {
            let entry = cache_mut.get(&key.to_string());
            assert!(entry.is_some());
            assert!(entry.unwrap().focus.is_some());
            assert!(entry.unwrap().page.is_none());
        }
    }

    #[rstest]
    #[case(CacheInvalidationEvent::DroppedCard)]
    #[case(CacheInvalidationEvent::WatchlistToggled)]
    fn cache_invalidation_events_evict_none(#[case] event: CacheInvalidationEvent) {
        let keys: [&str; 3] = ["key-0", "key-1", "key-2"];
        let cache = DiscoveryAssistantCache::setup_cache()
            .with_default_data_and_custom_key_entry(keys[0].to_string())
            .with_default_data_and_custom_key_entry(keys[1].to_string())
            .with_default_data_and_custom_key_entry(keys[2].to_string());

        on_cache_invalidation_event(event, cache.clone());
        let mut cache_mut = cache.borrow_mut();
        for key in keys {
            let entry = cache_mut.get(&key.to_string());
            assert!(entry.is_some());
            assert!(entry.unwrap().focus.is_some());
            assert!(entry.unwrap().page.is_some());
        }
    }

    #[test]
    fn new_cache() {
        launch_only_scope(|scope| {
            let invalidation_event_emitter = CacheInvalidationEventEmitter::new_rc_without_rpc();
            let cache: DiscoveryAssistantCache =
                DiscoveryAssistantCaching::new(scope, invalidation_event_emitter.clone());
            let cache = cache.with_default_entry();

            let entry = cache.get(&"key".to_string());
            assert!(entry.is_some());
            invalidation_event_emitter
                .borrow()
                .emit(CacheInvalidationEvent::TransitionedToPlayback);
            let entry = cache.get(&"key".to_string());
            assert!(entry.is_some());
            let entry = entry.unwrap();
            assert!(entry.focus.is_some());
            assert!(entry.page.is_none());
        });
    }

    #[test]
    fn get_cache() {
        let cache = DiscoveryAssistantCache::setup_cache()
            .with_default_entry()
            .with_default_entry()
            .with_default_entry();

        let entry = cache.get(&"key".to_string());
        let other_entry = cache.borrow_mut().get(&"key".to_string()).cloned();
        assert_eq!(entry, other_entry);
    }

    #[test]
    fn put_cache() {
        let cache = DiscoveryAssistantCache::setup_cache();

        let entry = CacheEntry::default();
        cache.put(entry.key.clone(), entry.value.clone());
        assert_eq!(cache.get(&entry.key), Some(entry.value));
    }
}
