#[cfg_attr(test, double)]
use crate::utils::network::NetworkClient;
use crate::utils::{
    navigation::DiscoveryPageParams,
    network::{CollectionResponse, DiscoveryQueries},
};

#[cfg_attr(test, double)]
use crate::utils::metrics::reporter::DiscoveryAssistantReporter;

#[cfg(test)]
use mock_instant::Instant;
#[cfg(not(test))]
use std::time::Instant;

use ignx_compositron::{
    prelude::{
        create_rw_signal, provide_context, use_context, AppContext, ReadSignal, RwSignal, Scope,
        SignalSet, SignalWithUntracked, WriteSignal,
    },
    reactive::store_value,
};
use location::Location;
use mockall::automock;
#[cfg(test)]
use mockall_double::double;

#[cfg_attr(test, double)]
use crate::utils::navigation::DiscoveryPageNavigation;

use super::cache::Focus;
use crate::cache::{CachedAssistantPage, DiscoveryAssistantCache, DiscoveryAssistantCaching};
use crate::utils::impressions::{AugmentedImpressionData, ImpressionManager};
use clickstream::client::{use_clickstream_client, ClickstreamClient};
use container_types::ui_signals::{
    ClickstreamData, CommonCarouselCardMetadata, DiscoveryAssistantGridItemModel,
    DiscoveryInfoColumn, StandardCardContainerItemType, Transitionable,
};
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::log;
use ignx_compositron::reactive::StoredValue;
use network::RequestError;
use std::rc::Rc;
use transition_executor::IngressSource;

#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
#[derive(Clone)]
pub enum RequestState {
    Loading,
    Loaded(CollectionResponse, String),
    // includes the service token that failed to retry.
    Error(Option<String>),
}

#[derive(Clone)]
pub struct DiscoveryPage {
    pub start_time: RwSignal<Instant>,
    data: WriteSignal<RequestState>,
    network_client: Rc<dyn DiscoveryQueries>,
    navigation: Rc<DiscoveryPageNavigation>,
    pub cache: Option<DiscoveryAssistantCache>,
    pub results: ReadSignal<RequestState>,
    impressions_manager: ImpressionManager,
    default_focus: StoredValue<Option<Focus>>,
    current_focus: StoredValue<Option<Focus>>,
    clickstream_client: ClickstreamClient,
}

// extend this trait with Into<Foo>, Into<Bar> where Foo and Bar are the types required for
// clickstream and CSM when implementing those.
pub trait Selectable: Transitionable + ClickstreamData + 'static {}

impl Selectable for StoredValue<CommonCarouselCardMetadata> {}
impl Selectable for RwSignal<DiscoveryAssistantGridItemModel> {}
impl Selectable for RwSignal<StandardCardContainerItemType> {}
impl Selectable for RwSignal<DiscoveryInfoColumn> {}

#[automock]
impl DiscoveryPage {
    pub fn new(
        ctx: &AppContext,
        network_client: Rc<NetworkClient>,
        navigation: Rc<DiscoveryPageNavigation>,
    ) -> Self {
        let data_signal = create_rw_signal(ctx.scope(), RequestState::Loading);
        let cache = use_context::<DiscoveryAssistantCache>(ctx.scope());
        let start_time = create_rw_signal(ctx.scope(), Instant::now());
        let clickstream_client = use_clickstream_client(ctx.scope());

        Self {
            start_time,
            network_client,
            navigation,
            cache,
            data: data_signal.write_only(),
            results: data_signal.read_only(),
            impressions_manager: ImpressionManager::new(),
            default_focus: store_value(ctx.scope(), None),
            current_focus: store_value(ctx.scope(), None),
            clickstream_client,
        }
    }

    pub fn get_results(&self, service_token: Option<String>) {
        let unique_page_key = Self::get_unique_page_key(service_token.clone());

        let (page, focus) = self
            .get_results_from_cache(&unique_page_key)
            .map_or((None, None), |cached_page| {
                (cached_page.page, cached_page.focus)
            });

        // default focus should only be set during back navigations.
        if self.navigation.entered_from_back() {
            self.default_focus.set_value(focus);
        }

        if let Some(cached_page_data) = page {
            self.data
                .set(RequestState::Loaded(cached_page_data, unique_page_key));
        } else {
            self.data.set(RequestState::Loading);
            self.get_results_from_network(service_token, unique_page_key);
        }
    }

    pub fn navigate(&self, location: Location) {
        self.navigation.navigate(location);
    }

    pub fn back(&self) {
        self.navigation.back();
    }

    pub fn on_item_select<T: Selectable>(
        &self,
        scope: Scope,
        item: T,
        impression_data: Option<AugmentedImpressionData>,
        ingress_source: Option<IngressSource>,
    ) {
        if let Some(impression_data) = impression_data {
            self.impressions_manager
                .send_select_impression(scope, &impression_data);
        }
        if let Some(params) = item.to_clickstream_data() {
            self.clickstream_client.track(params);
        }
        self.handle_forward_transition(scope, item, ingress_source);
    }

    pub fn on_item_viewed(
        &self,
        scope: Scope,
        view_impression_data: ViewImpressionData,
        impression_data: Option<AugmentedImpressionData>,
    ) {
        if let Some(impression_data) = impression_data {
            self.impressions_manager.send_view_impression(
                scope,
                view_impression_data,
                &impression_data,
            );
        }
    }

    pub fn on_item_highlighted(
        &self,
        scope: Scope,
        impression_data: Option<AugmentedImpressionData>,
    ) {
        if let Some(impression_data) = impression_data {
            self.impressions_manager
                .send_highlight_impression(scope, &impression_data);
        }
    }

    pub fn on_init_page(self, page_params: DiscoveryPageParams) {
        self.start_time.set(Instant::now());
        self.get_results(page_params.service_token);
    }

    pub fn get_focus(&self) -> Option<Focus> {
        self.default_focus.get_value()
    }

    pub fn set_focus(&self, focus: Option<Focus>) {
        self.current_focus.set_value(focus);
    }

    fn handle_forward_transition<T: Transitionable>(
        &self,
        scope: Scope,
        item: T,
        ingress_source: Option<IngressSource>,
    ) {
        let Some(transition) = item.to_transition(scope) else {
            return;
        };
        let Some(mut location) = transition.to_location() else {
            return;
        };

        // Handles two separate cases:
        //  - pill works as SeeMore link
        //  - pill works as link to specific topic
        // TODO: refactor https://sim.amazon.com/issues/LR-Rust-637
        if let Some(ingress_source) = ingress_source {
            location.pageParams.insert(
                "ingressSource".to_string(),
                serde_json::to_value(&ingress_source).unwrap_or_default(),
            );
        }

        self.cache_current_page();

        DiscoveryAssistantReporter::transition_to(location.pageType);

        self.navigate(location);
    }

    fn get_unique_page_key(service_token: Option<String>) -> String {
        service_token.unwrap_or_else(|| "no-token".to_string())
    }

    fn cache_current_page(&self) {
        let Some(cache) = self.cache.as_ref() else {
            return;
        };

        let Some((page_data, unique_page_key)) = self.results.with_untracked(|res| {
            if let RequestState::Loaded(response, unique_page_key) = res {
                Some((response.clone(), unique_page_key.clone()))
            } else {
                None
            }
        }) else {
            return;
        };
        let page_data_for_cache = CachedAssistantPage {
            page: Some(page_data),
            focus: self.current_focus.get_value(),
        };
        cache.put(unique_page_key, page_data_for_cache);
    }

    fn get_results_from_cache(&self, unique_page_key: &String) -> Option<CachedAssistantPage> {
        let cache = self.cache.as_ref()?;

        cache.get(unique_page_key)
    }

    fn get_results_from_network(&self, service_token: Option<String>, unique_page_key: String) {
        DiscoveryAssistantReporter::begin_request();
        self.network_client.get_collection(
            service_token.clone(),
            {
                let data = self.data;
                Box::new(move |response: CollectionResponse| {
                    DiscoveryAssistantReporter::request_succeeded();
                    data.set(RequestState::Loaded(response, unique_page_key));
                })
            },
            {
                let data = self.data;
                Box::new(move |_error: RequestError| {
                    DiscoveryAssistantReporter::request_failed();
                    data.set(RequestState::Error(service_token));
                })
            },
        )
    }
}

pub fn provide_discovery_page_ctx(ctx: &AppContext) {
    let navigation = Rc::new(DiscoveryPageNavigation::new(ctx.scope()));
    let network = Rc::new(NetworkClient::new(ctx));
    let page = DiscoveryPage::new(ctx, network, Rc::clone(&navigation));

    provide_context::<DiscoveryPage>(ctx.scope(), page);

    navigation.handle_page_enter(
        ctx.scope(),
        Rc::new(|scope, page_params| {
            let Some(page) = use_discovery_page_ctx(scope) else {
                return;
            };
            page.on_init_page(page_params)
        }),
    );
}

pub fn use_discovery_page_ctx(scope: Scope) -> Option<DiscoveryPage> {
    let data = use_context::<DiscoveryPage>(scope);
    if data.is_none() {
        log::error!("DiscoveryPageContext used without being provided by a parent, returning None");
    }
    data
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::cache::Focus;
    use crate::utils::metrics::reporter::MockDiscoveryAssistantReporter;
    use crate::utils::test_utils::{
        create_common_carousel_card_metadata, create_transitionable_action, CacheEntry,
        DiscoveryAssistantTestSetup, DiscoveryResponse, NetworkResponseType,
    };
    use clickstream::events::cards::CardClickedEvent;
    use clickstream::test_utils::use_clickstream_emitter_spy;
    use ignx_compositron::app::{launch_only_app_context, launch_test};
    use ignx_compositron::compose;
    use ignx_compositron::prelude::*;
    use ignx_compositron::reactive::SignalGet;
    use mockall::predicate::eq;
    use mockall::Sequence;
    use router::rust_location;
    use rstest::*;
    use serial_test::serial;

    #[test]
    #[serial]
    fn should_provide_and_load_ctx() {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Loaded(DiscoveryResponse::TopLevel)];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .setup();

            // We don't need to assert here we are just ensuring there is no panic.
            provide_discovery_page_ctx(&ctx);
            use_discovery_page_ctx(ctx.scope());
        })
    }

    #[test]
    #[serial]
    fn should_trigger_and_share_initial_data() {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Loaded(DiscoveryResponse::TopLevel)];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .setup();
            provide_discovery_page_ctx(&ctx);
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");

            let is_loaded = match page.results.get() {
                RequestState::Loaded(_response, _key) => true,
                _ => false,
            };

            assert_eq!(is_loaded, true);
        })
    }

    #[test]
    #[serial]
    fn should_handle_errors() {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Error];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .setup();
            provide_discovery_page_ctx(&ctx);
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");

            let is_error = match page.results.get() {
                RequestState::Error(_) => true,
                _ => false,
            };

            assert_eq!(is_error, true);
        })
    }

    #[rstest]
    #[serial]
    #[case::with_focus_when_not_back(Some(Focus::default()), false, None)]
    #[serial]
    #[case::with_focus_when_back(Some(Focus::default()), true, Some(Focus::default()))]
    #[serial]
    #[case::without_focus_when_not_back(None, false, None)]
    #[serial]
    #[case::without_focus_when_back(None, true, None)]
    fn should_retrieve_results_from_cache_if_possible(
        #[case] focus: Option<Focus>,
        #[case] is_back: bool,
        #[case] expected_focus: Option<Focus>,
    ) {
        launch_only_app_context(move |ctx| {
            let cache_entry = CacheEntry {
                value: CachedAssistantPage {
                    page: CacheEntry::default().value.page,
                    focus,
                },
                ..CacheEntry::default()
            };
            // we don't expect to call the network client at all
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_cache_entries(vec![cache_entry.clone()])
                .with_current_location(
                    rust_location!(RUST_DISCOVERY_PAGE, { "serviceToken" => "key", "back" => is_back }),
                )
                .setup();
            provide_discovery_page_ctx(&ctx);
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");

            let (is_loaded, res) = match page.results.get() {
                RequestState::Loaded(response, _key) => (true, Some(response)),
                _ => (false, None),
            };

            assert_eq!(is_loaded, true);
            assert_eq!(res, cache_entry.value.page);
            assert_eq!(page.get_focus(), expected_focus);
        })
    }

    #[rstest]
    #[serial]
    #[case::with_focus_when_not_back(Some(Focus::default()), false, None)]
    #[serial]
    #[case::with_focus_when_back(Some(Focus::default()), true, Some(Focus::default()))]
    #[serial]
    #[case::without_focus_when_not_back(None, false, None)]
    #[serial]
    #[case::without_focus_when_back(None, true, None)]
    fn should_retrieve_results_from_network_if_cache_hit_but_data_evicted(
        #[case] focus: Option<Focus>,
        #[case] is_back: bool,
        #[case] expected_focus: Option<Focus>,
    ) {
        launch_only_app_context(move |ctx| {
            let cache_entry = CacheEntry {
                key: "key".to_string(),
                value: CachedAssistantPage { page: None, focus },
            };
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_cache_entries(vec![cache_entry.clone()])
                .with_current_location(
                    rust_location!(RUST_DISCOVERY_PAGE, { "serviceToken" => "key", "back" => is_back }),
                )
                .with_responses(vec![NetworkResponseType::Loaded(
                    DiscoveryResponse::TopLevel,
                )])
                .setup();
            provide_discovery_page_ctx(&ctx);
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");

            let (is_loaded, res) = match page.results.get() {
                RequestState::Loaded(response, _key) => (true, Some(response)),
                _ => (false, None),
            };

            assert_eq!(is_loaded, true);
            assert_ne!(res, cache_entry.value.page);
            assert_eq!(page.get_focus(), expected_focus);
        })
    }

    #[test]
    #[serial]
    fn should_retrieve_results_from_network_if_no_cache_hit() {
        launch_only_app_context(|ctx| {
            let cache_entry = CacheEntry::default();
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_cache_entries(vec![cache_entry.clone()])
                .with_current_location(
                    rust_location!(RUST_DISCOVERY_PAGE, { "serviceToken" => "other_key" }),
                )
                .with_responses(vec![NetworkResponseType::Loaded(
                    DiscoveryResponse::SubLevel,
                )])
                .setup();
            provide_discovery_page_ctx(&ctx);
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");

            let (is_loaded, res) = match page.results.get() {
                RequestState::Loaded(response, _key) => (true, Some(response)),
                _ => (false, None),
            };

            assert_eq!(is_loaded, true);
            assert_ne!(res, cache_entry.value.page); // default cache entry uses DiscoveryResponse::TopLevel
        })
    }

    #[test]
    #[serial]
    fn should_allow_triggering_of_get_results() {
        launch_test(
            move |ctx| {
                let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![
                        NetworkResponseType::Loading,
                        NetworkResponseType::Loaded(DiscoveryResponse::TopLevel),
                        NetworkResponseType::Loading,
                    ])
                    .setup();

                compose! {
                    Stack() {}
                }
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let page = use_discovery_page_ctx(scope).expect("expected page context");

                // Should be loading from initial call
                let is_loading = match page.results.get() {
                    RequestState::Loading => true,
                    _ => false,
                };
                assert_eq!(is_loading, true);

                // Trigger results and ensure loaded
                page.get_results(Some(String::from("Test")));
                let is_loaded = match page.results.get() {
                    RequestState::Loaded(_response, _key) => true,
                    _ => false,
                };
                assert_eq!(is_loaded, true);

                // Trigger another but with network mock that doesn't succeed or error to ensure set back to loading
                page.get_results(Some(String::from("Test1")));
                let is_loading = match page.results.get() {
                    RequestState::Loading => true,
                    _ => false,
                };
                assert_eq!(is_loading, true);
            },
        );
    }

    #[test]
    #[serial]
    fn should_trigger_correct_reporting() {
        launch_test(
            move |ctx| {
                let begin_request_context = MockDiscoveryAssistantReporter::begin_request_context();
                begin_request_context.expect().times(1).return_const(());

                let request_succeeded_context =
                    MockDiscoveryAssistantReporter::request_succeeded_context();
                request_succeeded_context.expect().times(1).return_const(());

                let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![
                        NetworkResponseType::Loaded(DiscoveryResponse::TopLevel),
                        NetworkResponseType::Loaded(DiscoveryResponse::SubLevel),
                        NetworkResponseType::Error,
                    ])
                    .exclude_metric_mocking()
                    .setup();

                compose! {
                    Stack() {}
                }
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let page = use_discovery_page_ctx(scope).expect("expected page context");

                let mut seq = Sequence::new();

                let begin_request_context = MockDiscoveryAssistantReporter::begin_request_context();
                begin_request_context
                    .expect()
                    .times(1)
                    .in_sequence(&mut seq)
                    .return_const(());

                let request_succeeded_context =
                    MockDiscoveryAssistantReporter::request_succeeded_context();
                request_succeeded_context
                    .expect()
                    .times(1)
                    .in_sequence(&mut seq)
                    .return_const(());

                page.get_results(Some(String::from("Test")));
                test_game_loop.tick_until_done();

                let begin_request_context = MockDiscoveryAssistantReporter::begin_request_context();
                begin_request_context
                    .expect()
                    .times(1)
                    .in_sequence(&mut seq)
                    .return_const(());

                let request_failed_context =
                    MockDiscoveryAssistantReporter::request_failed_context();
                request_failed_context
                    .expect()
                    .times(1)
                    .in_sequence(&mut seq)
                    .return_const(());

                page.get_results(Some(String::from("Test1")));
                test_game_loop.tick_until_done();
            },
        );
    }

    #[test]
    #[serial]
    fn should_handle_navigation() {
        launch_test(
            move |ctx| {
                let responses = vec![NetworkResponseType::Loading];
                let location = rust_location!(RUST_DETAILS);
                let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(responses)
                    .with_expected_navigation(location)
                    .setup();

                compose! {
                    Stack() {}
                }
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let page = use_discovery_page_ctx(scope).expect("expected page context");
                page.navigate(rust_location!(RUST_DETAILS));
            },
        );
    }

    #[test]
    #[serial]
    fn should_handle_back_navigation() {
        launch_only_app_context(move |ctx| {
            let responses = vec![NetworkResponseType::Loaded(DiscoveryResponse::TopLevel)];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .with_expected_back_navigation(1)
                .setup();

            let page = use_discovery_page_ctx(ctx.scope).expect("expected page context");
            page.back();
        });
    }

    #[test]
    #[serial]
    fn item_select_for_common_carousel_card_metadata_transitions_when_action_type_transitionable() {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Loading];
            let location = rust_location!(
            RUST_DETAILS,
            {
                "titleId" => "gti",
                "isLiveDetailsPage" => false,
                "journeyIngressContext" => None::<String>,
                "seamlessTransitionEnabled" => false
            });
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .with_expected_navigation(location)
                .setup();
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");
            let item = create_common_carousel_card_metadata(true).to_stored(ctx.scope());
            page.on_item_select(ctx.scope(), item, None, None);
        });
    }

    #[test]
    #[serial]
    fn item_select_for_common_carousel_card_metadata_doesnt_transition_when_action_type_not_transitionable(
    ) {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Loading];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .setup();
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");
            let item = create_common_carousel_card_metadata(false).to_stored(ctx.scope());
            page.on_item_select(ctx.scope(), item, None, Some(IngressSource::SeeMore));
        });
    }

    #[test]
    #[serial]
    fn item_select_reports_clickstream_when_data_available() {
        launch_only_app_context(|ctx| {
            let item_action = create_transitionable_action();
            let params = CardClickedEvent::new_from_action(&item_action);

            let responses = vec![NetworkResponseType::Loading];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .setup();

            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");
            let item = CommonCarouselCardMetadata::from_id_and_action(
                "metadata_id".to_string(),
                item_action,
            )
            .with_clickstream_data(params);

            let reporter = use_clickstream_emitter_spy(ctx.scope());
            let item = item.to_stored(ctx.scope());

            assert_eq!(reporter.recorded_events().len(), 0);

            page.on_item_select(ctx.scope(), item, None, Some(IngressSource::SeeMore));

            let calls = reporter.recorded_events();
            insta::assert_yaml_snapshot!(calls, @r###"
            - source: CARD
              name: CARD_CLICKED
              params:
                analytics: {}
                refMarker: ref marker
            "###);
        });
    }

    #[test]
    #[serial]
    fn should_report_when_transitioning() {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Loading];
            let location = rust_location!(RUST_DETAILS, {
                "titleId" => "gti",
                "isLiveDetailsPage" => false,
                "journeyIngressContext" => None::<String>,
                "seamlessTransitionEnabled" => false
            });

            let begin_request_context = MockDiscoveryAssistantReporter::begin_request_context();
            begin_request_context.expect().times(1).return_const(());

            let transition_to_context = MockDiscoveryAssistantReporter::transition_to_context();
            transition_to_context
                .expect()
                .times(1)
                .with(eq(location.pageType))
                .return_const(());

            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .with_expected_navigation(location)
                .exclude_metric_mocking()
                .setup();

            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");
            let item = create_common_carousel_card_metadata(true).to_stored(ctx.scope());

            page.on_item_select(ctx.scope(), item, None, None);
        });
    }

    #[test]
    #[serial]
    fn item_select_for_transitionable_items_caches_the_current_page_data() {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Loaded(DiscoveryResponse::TopLevel)];
            let location = rust_location!(
            RUST_DETAILS,
            {
                "titleId" => "gti",
                "isLiveDetailsPage" => false,
                "journeyIngressContext" => None::<String>,
                "seamlessTransitionEnabled" => false
            });
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .with_expected_navigation(location)
                .with_current_location(
                    rust_location!(RUST_DISCOVERY_PAGE, { "serviceToken" => "current_service_token" }),
                )
                .setup();
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");
            let item = create_common_carousel_card_metadata(true).to_stored(ctx.scope());
            page.set_focus(Some(Focus::default()));
            page.on_item_select(ctx.scope(), item, None, None);

            let (_, res) = match page.results.get() {
                RequestState::Loaded(response, _key) => (true, Some(response)),
                _ => (false, None),
            };

            assert_eq!(
                Some(CachedAssistantPage {
                    page: res,
                    focus: Some(Focus::default())
                }),
                page.cache
                    .unwrap()
                    .get(&"current_service_token".to_string())
            );
        });
    }

    #[test]
    #[serial]
    fn item_select_for_non_transitionable_item_does_not_cache_the_page() {
        launch_only_app_context(|ctx| {
            let responses = vec![NetworkResponseType::Loaded(DiscoveryResponse::TopLevel)];
            let _client_context = DiscoveryAssistantTestSetup::default(&ctx)
                .with_responses(responses)
                .with_current_location(
                    rust_location!(RUST_DISCOVERY_PAGE, { "serviceToken" => "current_service_token" }),
                )
                .setup();
            let page = use_discovery_page_ctx(ctx.scope()).expect("expected page context");
            let item = create_common_carousel_card_metadata(false).to_stored(ctx.scope());
            page.set_focus(Some(Focus::default()));
            page.on_item_select(ctx.scope(), item, None, None);

            assert_eq!(
                None,
                page.cache
                    .unwrap()
                    .get(&"current_service_token".to_string())
            );
        });
    }
}
