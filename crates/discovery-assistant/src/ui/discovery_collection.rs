use crate::{
    cache::{Focus, FocusPosition},
    state::page_state::{use_discovery_page_ctx, RequestState},
    ui::page_header::*,
    utils::network::CollectionResponse,
};

#[double]
use crate::utils::metrics::reporter::DiscoveryAssistantReporter;

use amzn_fable_tokens::{FableSize, FableSpacing};
use container_list::container_list_sig::*;
use container_list::scroll_to_row::*;
use container_types::ui_signals::{
    CardGridModel, CarouselIndexData, CommonCarouselCardMetadata, DiscoveryAssistantGridItemModel,
    DiscoveryAssistantModel, DiscoveryInfoColumn, ImpressionsData, StandardCardContainerItemType,
};
use containers::card_grid::card_grid_ui_builder::{
    CardGridUIBuilder, GridAccessibilityMetadata, GridScroll, OnBackPressed, OnItemFocused,
    OnItemHighlighted, OnItemSelected, OnItemViewed,
};
use containers::discovery_assistant::discovery_card_grid::*;
use containers::discovery_assistant::discovery_line_divider::*;
use containers::pills::*;
use fableous::{animations::fable_motion_move_vertical, spinner::*, SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::{compose, compose_option, Composer};

use crate::utils::impressions::{AugmentedImpressionData, SlotRowCol};
use fableous::cards::sizing::CardDimensions;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::reactive::store_value;
use ignx_compositron::reactive::Namespace;
use ignx_compositron::{accessbility::CustomAccessibilityBorderStyle, prelude::*};
use ignx_compositron::{input::KeyCode, reactive::StoredValue};
use mockall_double::double;
use router::hooks::use_location;
use std::rc::Rc;
use transition_executor::IngressSource;

use crate::types::{ContainerType, SupportedContainer};
use crate::ui::error_screen::*;
use crate::utils::containers::get_containers;

const DISCOVERY_COLLECTION_LOADING: &str = "DISCOVERY_COLLECTION_LOADING";
const DISCOVERY_COLLECTION_LOADED: &str = "DISCOVERY_COLLECTION_LOADED";
const DISCOVERY_LOADING_TEST_ID_SPINNER: &str = "DISCOVERY_LOADING_TEST_ID_SPINNER";
const DISCOVERY_LOADING_TEST_ID_GHOST: &str = "DISCOVERY_LOADING_TEST_ID_GHOST";
const DISCOVERY_RESULTS_TEST_ID: &str = "DISCOVERY_RESULTS_TEST_ID";
const DISCOVERY_CONTAINER_TEST_ID: &str = "DISCOVERY_CONTAINER_TEST_ID";

const TITLE_CARD_GRID_NUM_COLUMNS: usize = 4;

#[Composer]
pub fn DiscoveryCollection(ctx: &AppContext) -> StackComposable {
    let page = use_discovery_page_ctx(ctx.scope());

    compose! {
        Stack() {
            Memo(item_builder: Box::new(move |ctx| {
                let page = page.as_ref();
                let start_time = page.map(|p| p.start_time.get_untracked());
                let results = page.map(|p| p.results.get());

                match results {
                    Some(RequestState::Loading) => compose_option! {
                        DiscoveryLoading()
                        .test_id(DISCOVERY_COLLECTION_LOADING)
                    },
                    Some(RequestState::Loaded(results, _)) => {
                        let ui = compose_option! {
                            DiscoveryResults(results)
                            .test_id(DISCOVERY_COLLECTION_LOADED)
                        };

                        if let Some(start_time) = start_time {
                            DiscoveryAssistantReporter::overall_latency(start_time)
                        };

                        ui
                    },
                    Some(RequestState::Error(failed_service_token)) => {
                        let ui = compose_option! {
                            DiscoveryError(failed_service_token)
                        };

                        if let Some(start_time) = start_time {
                            DiscoveryAssistantReporter::overall_latency(start_time)
                        };

                        ui
                    },
                    None => {
                        let ui = compose_option! {
                            DiscoveryError(failed_service_token: None)
                        };

                        if let Some(start_time) = start_time {
                            DiscoveryAssistantReporter::overall_latency(start_time)
                        };

                        ui
                    }
                }
            }))
        }
    }
}

#[Composer]
pub fn DiscoveryLoading(ctx: &AppContext) -> StackComposable {
    let location = use_location(ctx.scope());

    let ingressSource: Option<IngressSource> = location
        .get_untracked()
        .pageParams
        .get("ingressSource")
        .and_then(|value| serde_json::from_value(value.clone()).ok());

    let accessibility_description = match ingressSource {
        Some(ingressSource) => match ingressSource {
            IngressSource::SeeMore => "Loading more topics.".to_string(),
            IngressSource::Item(title) => {
                format!("Loading results for {}.", title)
            }
        },
        None => "Loading results.".to_string(),
    };

    compose! {
        Stack() {
            Spinner()
            .test_id(DISCOVERY_LOADING_TEST_ID_SPINNER)

            // Ghost focusable
            Rectangle()
            .opacity(0.0)
            .focusable()
            .accessibility_description(accessibility_description)
            .accessibility_highlight_disabled(true)
            .accessibility_highlight_custom(CustomAccessibilityBorderStyle {
                width: 0.0,
                color: Default::default(),
                padding: 0.0
            })
            .test_id(DISCOVERY_LOADING_TEST_ID_GHOST)
        }
        .alignment(Alignment::Center)
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
    }
}

#[Composer]
pub fn DiscoveryError(ctx: &AppContext, failed_service_token: Option<String>) -> StackComposable {
    let page = use_discovery_page_ctx(ctx.scope());
    let on_back_press = {
        let page = page.clone();
        move || {
            let Some(page) = page.clone() else {
                return;
            };
            page.back();
        }
    };

    let on_retry = {
        Rc::new({
            move || {
                let Some(page) = page.clone() else {
                    return;
                };
                page.get_results(failed_service_token.clone());
            }
        })
    };

    compose! {
        DiscoveryAssistantErrorScreen(on_retry)
        .on_key_down(KeyCode::Backspace, on_back_press.clone())
        .on_key_down(KeyCode::Escape, on_back_press)
    }
}

fn get_container_test_id(container_type: &str, id: String) -> String {
    format!("{}_{}_{}", DISCOVERY_CONTAINER_TEST_ID, container_type, id)
}

#[Composer]
pub(crate) fn GridModelToUI(
    ctx: &AppContext,
    grid: RwSignal<CardGridModel>,
    on_item_viewed: OnItemViewed,
    on_item_highlighted: OnItemHighlighted,
    on_item_selected: OnItemSelected,
    on_item_focused: OnItemFocused,
    on_back_pressed: OnBackPressed,
    default_focus_id: Signal<Option<String>>,
    i: usize,
    has_header: bool,
    scroll_to_container: Rc<dyn Fn(u32)>,
    focused_container: FocusValueSignal<usize>,
) -> StackComposable {
    CardGridUIBuilder::new(
        ctx.clone(),
        grid.into(),
        TITLE_CARD_GRID_NUM_COLUMNS,
        GridScroll::None,
        on_item_viewed.clone(),
        on_item_highlighted.clone(),
        default_focus_id,
    )
    .with_item_selected(on_item_selected.clone())
    .with_item_focused(on_item_focused.clone())
    .with_back_pressed_external(on_back_pressed.clone())
    .with_accessibility_metadata(GridAccessibilityMetadata {
        move_left_hint: Some("Move left to navigate to the main menu.".to_string()),
        first_container: Some(i == has_header as usize),
    })
    .build()
    .on_focus(move || scroll_to_container(i as u32))
    .test_id(get_container_test_id(
        "Grid",
        grid.get_untracked()
            .common_carousel_metadata
            .get_untracked()
            .id,
    ))
    .focused_value(focused_container, i)
    .focus_area(FocusAreaEntryMode::Closest)
}

#[Composer]
pub fn DiscoveryResults(ctx: &AppContext, results: CollectionResponse) -> StackComposable {
    let selected_pill = results.page_metadata.selectedPill.clone();
    let containers = get_containers(ctx.scope(), results);
    // TODO update CarouselIndexData to work with ID so we can remove the additional focused_item signal
    let focus_carousel_index_data = create_rw_signal(
        ctx.scope(),
        CarouselIndexData {
            scroll_index: 0,
            focus_index: 0,
            sub_index: None,
        },
    );

    let initial_scroll = ScrollTo::Index(0, Pivot::End, Animation::default());
    let scroll_to: RwSignal<ScrollTo> = create_rw_signal(ctx.scope(), initial_scroll);
    let scroll_to_container = move |i: u32| {
        scroll_to.set(ScrollTo::Index(i, Pivot::End, fable_motion_move_vertical()));
    };
    let focused_container = create_focus_value_signal(ctx.scope());
    let has_header = containers.first().is_some_and(|container| {
        matches!(
            container.model.get_untracked(),
            SupportedContainer::DiscoveryAssistantHeader(_)
        )
    });
    let has_topics_at_the_top = containers
        .get(has_header as usize)
        .is_some_and(|container| {
            matches!(
                container.model.get_untracked(),
                SupportedContainer::DiscoveryAssistant(_)
            )
        });
    let containers_signal = create_rw_signal(ctx.scope(), containers);

    let page = use_discovery_page_ctx(ctx.scope());
    let focused_item: RwSignal<Option<Focus>> = create_rw_signal(ctx.scope(), None);
    let default_focus = store_value(ctx.scope(), page.as_ref().and_then(|page| page.get_focus()));

    create_effect(ctx.scope(), move |_| {
        if let Some(page) = &page {
            page.set_focus(focused_item.get());
        }
    });

    compose! {
        Stack() {
            ContainerListSig(
                items: containers_signal,
                on_container_focused: Rc::new(|_| {}),
                on_end_reached: Rc::new(|| {}),
                alignment: MainAxisAlignment::SpacedBy(FableSpacing::SPACING267),
                focus_carousel_index_data: Some(focus_carousel_index_data.read_only()),
                lock_edges: true,
                padding: Padding::new(0.0, 0.0, FableSpacing::SPACING400, FableSpacing::SPACING400),
                scroll_to_row: scroll_to.into_scroll_to_row(ctx.scope()),
                navigation_strategy: None,
                item_builder: Rc::new(move |ctx, item: &(ContainerType, Namespace), i| {
                    let (container, namespace) = item.clone();
                    let model = container.model.get();
                    DiscoveryAssistantReporter::container_mounted(&model, i);

                    match model {
                        SupportedContainer::DiscoveryAssistantHeader(model) => {
                            compose! {
                                PageHeader(model)
                                .test_id(get_container_test_id("PageHeader", i.to_string()))
                            }
                        },
                        SupportedContainer::DiscoveryAssistant(assistant) => {
                            match assistant.get() {
                                DiscoveryAssistantModel::Grid(grid) => {
                                    let scope = ctx.scope;
                                    let on_item_selected = {
                                        Rc::new(move |item: RwSignal<DiscoveryAssistantGridItemModel>, col: usize, dimensions: Option<CardDimensions>| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, col as u32), dimensions)
                                            });
                                            page.on_item_select(scope, item, impression_data, None)
                                        })
                                    };

                                    let on_item_view = {
                                        Rc::new(move |view_impression_data: ViewImpressionData, item: RwSignal<DiscoveryAssistantGridItemModel>, idx: usize, card_dimensions| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                 return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                            });
                                            page.on_item_viewed(scope, view_impression_data, impression_data);
                                        })
                                    };

                                    let on_item_highlight = {
                                        Rc::new(move |item: RwSignal<DiscoveryAssistantGridItemModel>, idx: usize, card_dimensions| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                 return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                            });
                                            page.on_item_highlighted(scope, impression_data);
                                        })
                                    };

                                    let on_see_more_selected = {
                                        Rc::new(move |item: RwSignal<DiscoveryInfoColumn>, col, dimensions| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, col as u32), dimensions)
                                            });
                                            page.on_item_select(scope, item, impression_data, None)
                                        })
                                    };
                                    let on_see_more_view = {
                                        Rc::new(move |view_impression_data: ViewImpressionData, item: RwSignal<DiscoveryInfoColumn>, idx: usize, card_dimensions| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                 return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                            });
                                            page.on_item_viewed(scope, view_impression_data, impression_data);
                                        })
                                    };
                                    let on_see_more_highlight = {
                                        Rc::new(move |item: RwSignal<DiscoveryInfoColumn>, idx: usize, card_dimensions| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                 return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                            });
                                            page.on_item_highlighted(scope, impression_data);
                                        })
                                    };
                                    let on_back_pressed = Rc::new(move || on_back_press_in_container(focused_container, scope, has_header));
                                    let on_item_focus = Rc::new(move |focus_index: usize, sub_index: Option<usize>| {
                                        focus_carousel_index_data.set(CarouselIndexData {
                                            scroll_index: i,
                                            focus_index,
                                            sub_index
                                        });

                                        focused_item.set(Some(Focus{
                                            container: FocusPosition::Index(i, None),
                                            item: FocusPosition::Index(focus_index, sub_index)
                                        }))});
                                    let default_focus_index = Signal::derive(ctx.scope(), move || {
                                        if let Some(focus) = default_focus.get_value() {
                                            if let FocusPosition::Index(idx, _) = focus.container {
                                                if idx == i {
                                                    if let FocusPosition::Index(idx, _) = focus.item {
                                                        return idx
                                                    }
                                                }

                                            }
                                        }
                                        0
                                    });
                                    let default_focus_sub_index = Signal::derive(ctx.scope(), move || {
                                        if let Some(focus) = default_focus.get_value() {
                                            if let FocusPosition::Index(idx, _) = focus.container {
                                                if idx == i {
                                                    if let FocusPosition::Index(_, idx) = focus.item {
                                                        return idx
                                                    }
                                                }

                                            }
                                        }
                                        None
                                    });
                                    compose! {
                                        Stack() {
                                            Column() {
                                                DiscoveryCardGrid(
                                                    data: grid,
                                                    focus_ring_namespace: namespace,
                                                    on_item_selected,
                                                    on_see_more_selected,
                                                    on_item_focus,
                                                    on_back_pressed,
                                                    on_item_view,
                                                    on_item_highlight,
                                                    default_focus_index,
                                                    default_focus_sub_index,
                                                    on_see_more_view,
                                                    on_see_more_highlight,
                                                    accessibility_metadata: DiscoveryCardGridAccessibilityMetadata {
                                                        first_container: Some(i == has_header as usize),
                                                        ..DiscoveryCardGridAccessibilityMetadata::default()
                                                    }
                                                )
                                                .on_focus(move || scroll_to_container(i as u32))
                                                .test_id(get_container_test_id("DiscoveryGrid", grid.get_untracked().common_carousel_metadata.get_untracked().id))
                                                .focused_value(focused_container, i)

                                                DiscoveryLineDivider()
                                            }
                                            // Temporary setting 122 to center grid vertically until we have proper vertical scrolling
                                            .padding(Padding::new(0.0, 0.0, 122.0, 0.0))
                                            .main_axis_alignment(MainAxisAlignment::SpacedBy(48.0))
                                            .focus_area(FocusAreaEntryMode::Closest)
                                        }
                                    }
                                },
                                DiscoveryAssistantModel::Pills(pills) => {
                                    let pills_count = pills.get_untracked().items.get_untracked().len();
                                    let scope = ctx.scope;
                                    let on_item_selected = {
                                        Rc::new(move |item: StoredValue<CommonCarouselCardMetadata>, col: usize, dimensions: Option<CardDimensions>, text| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, col as u32), dimensions)
                                            });

                                            let ingress_source = if pills_count == 1 {
                                                IngressSource::SeeMore
                                            } else {
                                                IngressSource::Item(text)
                                            };

                                            page.on_item_select(scope, item, impression_data, Some(ingress_source));
                                        })
                                    };

                                    let on_item_view = {
                                        Rc::new(move |view_impression_data: ViewImpressionData, item: StoredValue<CommonCarouselCardMetadata>, idx: usize, card_dimensions| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                 return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                            });
                                            page.on_item_viewed(scope, view_impression_data, impression_data);
                                        })
                                    };

                                    let on_item_highlight = {
                                        Rc::new(move |item: StoredValue<CommonCarouselCardMetadata>, idx: usize, card_dimensions| {
                                            let Some(page) = use_discovery_page_ctx(scope) else {
                                                 return;
                                            };
                                            let impression_data = item.to_impressions_data().map(|data| {
                                                AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                            });
                                            page.on_item_highlighted(scope, impression_data);
                                        })
                                    };
                                    let on_back_pressed = Rc::new(move || on_back_press_in_container(focused_container, scope, has_header));
                                    let on_item_focus = Rc::new(move |item: StoredValue<CommonCarouselCardMetadata>| focused_item.set(Some(Focus{
                                        container: FocusPosition::Index(i, None),
                                        item: FocusPosition::Id(item.get_value().id)
                                    })));
                                    let default_focus_id = Signal::derive(ctx.scope(), move || {
                                        if let Some(focus) = default_focus.get_value() {
                                            if let FocusPosition::Index(idx, _) = focus.container {
                                                if idx == i {
                                                    if let FocusPosition::Id(id) = focus.item {
                                                        return Some(id)
                                                    }
                                                }

                                            }
                                        }
                                        None
                                    });

                                    // If there is only one pill, we don't want it to be considered
                                    // as 1-element row of pills (e.g. avoid reading "1 of 1").
                                    let accessibility_setting = {
                                        if pills_count == 1 {
                                            PillsAccessibilitySetting::Disabled
                                        } else {
                                            PillsAccessibilitySetting::Enabled(PillsAccessibilityMetadata {
                                                last_container: Some(false),
                                                move_left_hint: Some("Move left to navigate to the main menu.".to_string())
                                            })
                                        }
                                    };

                                    let (accessibility_context_message, accessibility_closing_context_message) = if accessibility_setting == PillsAccessibilitySetting::Disabled {
                                        (
                                            pills.get_untracked().pills_metadata.title.title_text,
                                            Some(format!("Press select to load a new screen. Move left to navigate to main menu. Move up to browse lists. {}", if has_topics_at_the_top { "Press back to go to the first topic in the list.".to_string() } else { "".to_string() }))
                                        )
                                    } else { (pills.get_untracked().pills_metadata.title.title_text, None) };


                                    compose! {
                                        Stack() {
                                            Pills(
                                                data: pills,
                                                on_item_selected,
                                                on_item_focus,
                                                on_back_pressed,
                                                default_focus_id,
                                                on_item_view,
                                                on_item_highlight,
                                                accessibility_setting
                                            )
                                        }
                                        .min_width(SCREEN_WIDTH)
                                        .focus_area(FocusAreaEntryMode::Closest)
                                        .accessibility_context_message(accessibility_context_message.unwrap_or_default())
                                        .accessibility_closing_context_message(accessibility_closing_context_message.unwrap_or_default())
                                        .on_focus(move || scroll_to_container(i as u32))
                                        .test_id(get_container_test_id("DiscoveryPills", pills.get_untracked().metadata.get_untracked().id))
                                        .focused_value(focused_container, i)
                                    }
                                }
                            }
                        },
                        SupportedContainer::Grid(grid) => {
                            let scope = ctx.scope;
                            let on_item_selected = {
                                let scope = ctx.scope;
                                Rc::new(move |item: RwSignal<StandardCardContainerItemType>, row: usize, col: usize, dimensions: Option<CardDimensions>, _| {
                                    let Some(page) = use_discovery_page_ctx(scope) else {
                                        return;
                                    };
                                    let idx = row * TITLE_CARD_GRID_NUM_COLUMNS + col;
                                    let impression_data = item.to_impressions_data().map(|data| {
                                        AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), dimensions)
                                    });
                                    page.on_item_select(scope, item, impression_data, None);
                                })
                            };

                            let on_item_viewed = {
                                Rc::new(move |view_impression_data: ViewImpressionData, item: RwSignal<StandardCardContainerItemType>, row: usize, col: usize, card_dimensions| {
                                    let Some(page) = use_discovery_page_ctx(scope) else {
                                         return;
                                    };
                                    let idx = row * TITLE_CARD_GRID_NUM_COLUMNS + col;
                                    let impression_data = item.to_impressions_data().map(|data| {
                                        AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                    });
                                    page.on_item_viewed(scope, view_impression_data, impression_data);
                                })
                            };

                            let on_item_highlighted = {
                                Rc::new(move |item: RwSignal<StandardCardContainerItemType>, row: usize, col: usize, card_dimensions| {
                                    let Some(page) = use_discovery_page_ctx(scope) else {
                                         return;
                                    };
                                    let idx = row * TITLE_CARD_GRID_NUM_COLUMNS + col;
                                    let impression_data = item.to_impressions_data().map(|data| {
                                        AugmentedImpressionData::new(data, SlotRowCol(i as u32, idx as u32), Some(card_dimensions))
                                    });
                                    page.on_item_highlighted(scope, impression_data);
                                })
                            };

                            let on_back_pressed = Rc::new(move || on_back_press_in_container(focused_container, scope, has_header));
                            let on_item_focused = Rc::new(move |item: RwSignal<StandardCardContainerItemType>, _, _| focused_item.set(Some(Focus{
                                container: FocusPosition::Index(i, None),
                                item: FocusPosition::Id(item.get_untracked().get_card_data().get_untracked().metadata.get_untracked().id)
                            })));
                            let default_focus_id = Signal::derive(ctx.scope(), move || {
                                if let Some(focus) = default_focus.get_value() {
                                    if let FocusPosition::Index(idx, _) = focus.container {
                                        if idx == i {
                                            if let FocusPosition::Id(id) = focus.item {
                                                return Some(id)
                                            }
                                        }

                                    }
                                }
                                None
                            });

                            compose! {
                                GridModelToUI(
                                    grid,
                                    on_item_viewed,
                                    on_item_highlighted,
                                    on_item_selected,
                                    on_item_focused,
                                    on_back_pressed,
                                    default_focus_id,
                                    i,
                                    has_header,
                                    scroll_to_container: Rc::new(scroll_to_container),
                                    focused_container,
                                )
                            }
                        }
                    }
            }))
            .accessibility_context_message(selected_pill.unwrap_or_default())
            .test_id(DISCOVERY_RESULTS_TEST_ID)
            .padding(Padding::new(FableSize::SIZE600, FableSpacing::SPACING333, FableSize::SIZE000, FableSize::SIZE000))
        }
    }
}

fn on_back_press_in_container(
    focused_container: FocusValueSignal<usize>,
    scope: Scope,
    has_header: bool,
) {
    let first_focusable_index = if has_header { 1 } else { 0 };
    if focused_container.get_untracked() != Some(first_focusable_index) {
        focused_container.set(first_focusable_index);
    } else if let Some(page) = use_discovery_page_ctx(scope) {
        page.back();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::test_utils::{
        DiscoveryAssistantTestSetup, DiscoveryResponse, NetworkResponseType, ReportingMock,
    };
    use ignx_compositron::input::{KeyCode, KeyEventType};
    use ignx_compositron::test_utils::node_properties::NodeQuery;
    use ignx_compositron::test_utils::TestRendererGameLoop;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_exists, node_properties::SceneNodeTree},
    };
    use location::Location;
    use router::rust_location;
    use rstest::rstest;
    use serial_test::serial;

    fn get_focused_test_ids(test_game_loop: &mut TestRendererGameLoop) -> Vec<Option<String>> {
        let tree = test_game_loop.tick_until_done();

        let focused = tree.find_by_props().is_focused().find_all();
        focused
            .iter()
            .map(|f| f.borrow_props().test_id.clone())
            .collect::<Vec<_>>()
    }

    #[rstest]
    #[serial]
    #[case(NetworkResponseType::Loading, DISCOVERY_COLLECTION_LOADING.to_string())]
    #[serial]
    #[case(NetworkResponseType::Error, DISCOVERY_COLLECTION_ERROR.to_string())]
    #[serial]
    #[case(NetworkResponseType::Loaded(DiscoveryResponse::TopLevel), DISCOVERY_COLLECTION_LOADED.to_string())]
    fn it_displays_correct_main_container_for_different_network_states(
        #[case] response_type: NetworkResponseType,
        #[case] test_id: String,
    ) {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![response_type])
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_once().node_tree;
                let node = node_tree.find_by_test_id(test_id);
                assert_node_exists!(node);
            },
        );
    }

    #[rstest]
    #[serial]
    #[case(NetworkResponseType::Loading, 0)]
    #[serial]
    #[case(NetworkResponseType::Error, 1)]
    #[serial]
    #[case(NetworkResponseType::Loaded(DiscoveryResponse::TopLevel), 1)]
    fn it_reports_overall_latency_correcty(
        #[case] response_type: NetworkResponseType,
        #[case] latency_reported: usize,
    ) {
        launch_test(
            move |ctx| {
                let metrics = ReportingMock::mock_metrics(
                    None,
                    None,
                    None,
                    None,
                    None,
                    Some(latency_reported),
                );

                create_rw_signal(ctx.scope(), metrics);

                DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![response_type])
                    .exclude_metric_mocking()
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                test_game_loop.tick_once();
            },
        );
    }

    #[serial]
    #[test]
    fn it_displays_loading_correctly() {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![NetworkResponseType::Loading])
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_once().node_tree;

                let spinner = node_tree.find_by_test_id(DISCOVERY_LOADING_TEST_ID_SPINNER);
                let ghost = node_tree.find_by_test_id(DISCOVERY_LOADING_TEST_ID_GHOST);

                assert_node_exists!(spinner);
                assert_node_exists!(ghost);
            },
        );
    }

    #[serial]
    #[test]
    fn it_should_have_focus_on_ghost_when_loading() {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![NetworkResponseType::Loading])
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                test_game_loop.tick_once().node_tree;
                let node_tree = test_game_loop.tick_once().node_tree;
                let ghost = node_tree.find_by_test_id(DISCOVERY_LOADING_TEST_ID_GHOST);
                assert_eq!(ghost.borrow_props().is_focused, true);
            },
        );
    }

    #[serial]
    #[test]
    fn it_displays_errors_correctly() {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![NetworkResponseType::Error])
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let button = node_tree.find_by_test_id(DISCOVERY_ERROR_TEST_ID_BUTTON);
                assert_node_exists!(button);
            },
        );
    }

    #[serial]
    #[test]
    fn it_handles_retrying() {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![
                        NetworkResponseType::Error,
                        NetworkResponseType::Loaded(DiscoveryResponse::TopLevel),
                    ])
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let button = node_tree.find_by_test_id(DISCOVERY_ERROR_TEST_ID_BUTTON);

                test_game_loop.send_on_select_event(button.borrow_props().node_id);

                let node_tree = test_game_loop.tick_until_done();

                let loaded = node_tree.find_by_test_id(DISCOVERY_COLLECTION_LOADED);
                assert_node_exists!(loaded);
            },
        );
    }

    fn get_container_nodes(node_tree: &SceneNodeTree) -> Vec<NodeQuery<'_>> {
        node_tree
            .find_where(|node| {
                node.props
                    .test_id
                    .as_ref()
                    .is_some_and(|t| t.starts_with(DISCOVERY_CONTAINER_TEST_ID))
            })
            .expect("Expecting containers")
    }

    fn get_containers(node_tree: &SceneNodeTree) -> Vec<String> {
        let containers = get_container_nodes(&node_tree);

        containers
            .iter()
            .map(|i| i.borrow_props().test_id.clone().unwrap_or_default())
            .collect()
    }

    #[rstest]
    #[serial]
    #[case(DiscoveryResponse::TopLevel, vec![
        get_container_test_id("PageHeader", 0.to_string()),
        get_container_test_id("DiscoveryGrid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSOTc2OENCNjk3NUU2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        get_container_test_id("DiscoveryPills", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSNDUzQ0M4Njk5OEQ2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),

    ])]
    #[serial]
    #[case(DiscoveryResponse::SubLevel, vec![
        get_container_test_id("PageHeader", 0.to_string()),
        get_container_test_id("Grid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSRjIyMjNBNjVGOUE4IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        get_container_test_id("DiscoveryPills", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSMDEzODgxODMyODJDIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        get_container_test_id("Grid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSQjVFNjBCQzYwMUE0IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        get_container_test_id("DiscoveryPills", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSQkY4RUFEQzk5MkM5IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI="))
    ])]
    fn it_renders_correct_containers(
        #[case] response_type: DiscoveryResponse,
        #[case] expected_containers: Vec<String>,
    ) {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![NetworkResponseType::Loaded(response_type)])
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let containers = get_containers(&node_tree);
                assert_eq!(expected_containers, containers);
            },
        );
    }

    #[rstest]
    #[serial]
    #[case(
        DiscoveryResponse::TopLevel,
        get_container_test_id("DiscoveryGrid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSOTc2OENCNjk3NUU2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        true,
        rust_location!(RUST_DISCOVERY_PAGE, {
             "pageType" => "home",
             "pageId" => "assistant",
             "serviceToken" => "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwN0ltbGtJam9pTkRnMUlpd2ljR2h5WVhObElqb2lTVzV6Y0dseWFXNW5JSFZ1WkdWeVpHOW5jeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpRNE5TSXNJbkJwYkd4elZtVnljMmx2YmlJNkltUmxiVzh0TWpBeU5ERXdNalVpTENKemRHRjBaVXRsZVNJNkltTm1aVE15WmpobUxXTTBNRE10TkdZeU1DMWhNakJtTFRObVpHRmtOekk1TWpRMU5pSXNJbkJwYkd4V1lYSnBaWFI1Vkc5TVlYTjBVbVYwY21sbGRtVmtTVzVrWlhoTllYQWlPbnNpVlc1d1pYSnpiMjVoYkdsNlpXUkNjbTloWkNJNk1UbDlMQ0pwYzBacGNuTjBVR0ZuWlNJNlptRnNjMlY5ZlE9PSJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiSm1UVXYzVlFCRllIS282MDJEVmZEWTUyUW9PWVByeU1GbFhEVGo3dHptZ1kzYUxjODRkT2lnPT06MTczMDI5ODk0OTAwMCIsIm9yZXFrIjoieVR2SXNZMmNIRWZWbzg0NjM5NSsyUWZvaHlKRUhtcnBycENGcUg5bG1XND0iLCJvcmVxa3YiOjF9",
             "ingressSource" => IngressSource::Item("Inspiring underdogs".to_string())
         })
    )]
    #[serial]
    #[case(
        DiscoveryResponse::TopLevel,
        get_container_test_id("DiscoveryPills", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSNDUzQ0M4Njk5OEQ2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        false,
        rust_location!(RUST_DISCOVERY_PAGE, {
             "pageType" => "home",
             "pageId" => "assistant",
             "serviceToken" => "eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiZXlKemNHOXpVM1JoZEdVaU9uc2ljMlZzWldOMFpXUlFhV3hzSWpwdWRXeHNmU3dpYVc1MFpYSnVZV3hCYzNOcGMzUmhiblJUZEdGMFpTSTZleUp6Wld4bFkzUmxaRkJwYkd4SlpDSTZiblZzYkN3aWNHbHNiSE5XWlhKemFXOXVJam9pWkdWdGJ5MHlNREkwTVRBeU5TSXNJbk4wWVhSbFMyVjVJam9pWTJabE16Sm1PR1l0WXpRd015MDBaakl3TFdFeU1HWXRNMlprWVdRM01qa3lORFUySWl3aWNHbHNiRlpoY21sbGRIbFViMHhoYzNSU1pYUnlhV1YyWldSSmJtUmxlRTFoY0NJNmV5SlZibkJsY25OdmJtRnNhWHBsWkVKeWIyRmtJam94T1gwc0ltbHpSbWx5YzNSUVlXZGxJanBtWVd4elpYMTkiXX0sIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6IkptVFV2M1ZRQkZZSEtvNjAyRFZmRFk1MlFvT1lQcnlNRmxYRFRqN3R6bWdZM2FMYzg0ZE9pZz09OjE3MzAyOTg5NDkwMDAiLCJvcmVxayI6InlUdklzWTJjSEVmVm84NDYzOTUrMlFmb2h5SkVIbXJwcnBDRnFIOWxtVzQ9Iiwib3JlcWt2IjoxfQ=="
        ,             "ingressSource" => "SeeMore"
        })
    )]
    #[serial]
    #[case(
        DiscoveryResponse::SubLevel,
        get_container_test_id("Grid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSRjIyMjNBNjVGOUE4IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        false,
        rust_location!(RUST_DETAILS, {
             "isLiveDetailsPage" => false,
             "journeyIngressContext" => None::<String>,
             "seamlessTransitionEnabled" => false,
             "titleId" => "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a"
         })
    )]
    #[serial]
    #[case(
        DiscoveryResponse::TopLevel,
        get_container_test_id("DiscoveryGrid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSOTc2OENCNjk3NUU2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        false,
        rust_location!(RUST_DISCOVERY_PAGE, {
             "pageType" => "home",
             "pageId" => "assistant",
             "serviceToken" => None::<String>,
             "ingressSource" => "SeeMore"
         })
    )]
    fn it_handles_on_select_within_containers(
        #[case] response: DiscoveryResponse,
        #[case] container_id: String,
        #[case] move_right: bool,
        #[case] expected_location: Location,
    ) {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![NetworkResponseType::Loaded(response)])
                    .with_expected_navigation(expected_location)
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let container = node_tree.find_by_test_id(container_id);

                let first_focusable = container.find_any_child_with().is_focusable().find_first();

                if move_right {
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Right);
                }

                test_game_loop.send_on_select_event(first_focusable.borrow_props().node_id);

                test_game_loop.tick_until_done();
            },
        );
    }

    #[rstest]
    #[serial]
    #[case(
        DiscoveryResponse::TopLevel,
        get_container_test_id("DiscoveryGrid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSOTc2OENCNjk3NUU2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        false
    )]
    #[serial]
    #[case(
        DiscoveryResponse::SubLevel,
        get_container_test_id("DiscoveryPills", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSMDEzODgxODMyODJDIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        true
    )]
    #[serial]
    #[case(
        DiscoveryResponse::SubLevel,
        get_container_test_id("Grid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSRjIyMjNBNjVGOUE4IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=")),
        false
    )]
    fn it_handles_on_back_press_within_containers(
        #[case] response: DiscoveryResponse,
        #[case] container_id: String,
        #[case] extra_back: bool, // containers that are not the first one have a back-press to go to the top
    ) {
        for k in [KeyCode::Escape, KeyCode::Backspace] {
            let response = response.clone();
            let container_id = container_id.clone();
            launch_test(
                move |ctx| {
                    let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                        .with_responses(vec![NetworkResponseType::Loaded(response.clone())])
                        .with_expected_back_navigation(1)
                        .setup();

                    compose! {
                        DiscoveryCollection()
                    }
                },
                move |_, mut test_game_loop| {
                    let initial_focusables = get_focused_test_ids(&mut test_game_loop);

                    let node_tree = test_game_loop.tick_until_done();
                    let container = node_tree.find_by_test_id(container_id.clone());
                    let first_container_focusable =
                        container.find_any_child_with().is_focusable().find_first();
                    let first_container_focusable_node_id =
                        first_container_focusable.borrow_props().node_id;
                    assert_node_exists!(first_container_focusable);
                    test_game_loop.send_on_focus_event(first_container_focusable_node_id);

                    let container_initial_focusables = get_focused_test_ids(&mut test_game_loop);

                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Right);

                    assert_ne!(
                        container_initial_focusables.clone(),
                        get_focused_test_ids(&mut test_game_loop)
                    );
                    // this one shouldn't trigger the router back
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, k.clone());

                    assert_eq!(
                        container_initial_focusables,
                        get_focused_test_ids(&mut test_game_loop)
                    );

                    if extra_back {
                        test_game_loop.send_key_press_event(KeyEventType::ButtonDown, k.clone());
                    }

                    assert_eq!(
                        initial_focusables,
                        get_focused_test_ids(&mut test_game_loop)
                    );

                    // this one should trigger the router back
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, k);
                    test_game_loop.tick_until_done();
                },
            );
        }
    }

    #[test]
    #[serial]
    fn it_handles_moving_between_containers_and_scrolling_correctly() {
        launch_test(
            move |ctx| {
                let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                    .with_responses(vec![NetworkResponseType::Loaded(
                        DiscoveryResponse::SubLevel,
                    )])
                    .setup();

                compose! {
                    DiscoveryCollection()
                }
            },
            move |_, mut test_game_loop| {
                let expected_padding = 72.0;
                let node_tree = test_game_loop.tick_until_done();
                let containers = get_container_nodes(&node_tree);
                assert_eq!(containers.len(), 5);

                // First container is on screen
                let first_container: &ignx_compositron::test_utils::node_properties::NodeProperties = containers.first().unwrap().borrow_props();
                assert_eq!(first_container.is_on_screen, true);
                assert_eq!(first_container.layout.border_box.top, expected_padding);

                // Last container is off screen.
                let last_container = containers.last().unwrap().borrow_props();
                assert_eq!(last_container.is_on_screen, false);

                // Focusing on second container shouldn't move anything as we have space
                test_game_loop
                    .send_on_focus_event(containers.get(1).unwrap().borrow_props().node_id);
                let node_tree = test_game_loop.tick_until_done();
                let containers = get_container_nodes(&node_tree);
                let first_container = containers.first().unwrap().borrow_props();
                assert_eq!(first_container.layout.border_box.top, expected_padding);

                // Focusing on fourth container should move but locked to bottom
                test_game_loop
                    .send_on_focus_event(containers.get(3).unwrap().borrow_props().node_id);
                let node_tree = test_game_loop.tick_until_done();
                let containers = get_container_nodes(&node_tree);
                let current_container = containers.get(3).unwrap().borrow_props();
                assert_eq!(current_container.is_on_screen, true);
                assert_eq!(
                    current_container.layout.border_box.bottom(),
                    1080.0 - expected_padding
                );

                // Focus on last container – should be locked to bottom.
                test_game_loop
                    .send_on_focus_event(containers.last().unwrap().borrow_props().node_id);
                let node_tree = test_game_loop.tick_until_done();
                let containers = get_container_nodes(&node_tree);
                let current_container = containers.last().unwrap().borrow_props();
                assert_eq!(current_container.is_on_screen, true);
                assert_eq!(
                    current_container.layout.border_box.bottom(),
                    1080.0 - expected_padding
                );
            },
        );
    }

    mod csm {
        use super::*;
        use cross_app_events::{ImpressionData, IMPRESSION_DURATION};
        use ignx_compositron::time::MockClock;
        use std::collections::HashMap;
        use std::time::Duration;

        use crate::utils::test_utils::ImpressionTestHelper;

        #[serial]
        #[test]
        fn it_sends_impression_for_pills() {
            launch_test(
                {
                    move |ctx| {
                        let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                            .with_responses(vec![NetworkResponseType::Loaded(
                                DiscoveryResponse::TopLevel,
                            )])
                            .setup();
                        compose! {
                            DiscoveryCollection()
                        }
                    }
                },
                move |scope: Scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_once().node_tree;
                    let impression_helper = expect_context::<ImpressionTestHelper>(scope);

                    let container_id = get_container_test_id("DiscoveryPills", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSNDUzQ0M4Njk5OEQ2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI="));
                    let container = node_tree.find_by_test_id(container_id.clone());
                    let first_container_focusable =
                        container.find_any_child_with().is_focusable().find_first();
                    let first_container_focusable_node_id =
                        first_container_focusable.borrow_props().node_id;
                    assert_node_exists!(first_container_focusable);
                    test_game_loop.send_on_focus_event(first_container_focusable_node_id);
                    test_game_loop.tick_until_done();
                    MockClock::advance(IMPRESSION_DURATION + Duration::from_millis(1));
                    test_game_loop.tick_until_done();

                    let data = ImpressionData {
                        widget_type: Some("imageTextLink".to_string()),
                        ref_marker: Some("hm_ass_c_sr300d10_dast_2_1".to_string()),
                        content_type: Some("LINK".to_string()),
                        benefit_id: Some("assistant".to_string()),
                        content_id: Some("home:assistant".to_string()),
                        creative_id: Some("https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v1.png".to_string()),
                        analytics: Some(HashMap::from([
                            ("refMarker".to_string(), "hm_ass_c_sr300d10_dast_2_1".to_string()),
                        ])),
                        slot_id: Some((0, 2)),
                        size: Some((57.0, 100.0)),
                        carousel_analytics: Some("456|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1I0NTNDQzg2OTk4RDYaACIIc3IzMDBkMTASQQoEaG9tZRIJYXNzaXN0YW50IgZjZW50ZXIqADIkY2E3MTBkMzMtODIxMC00NmI3LWI0MTYtYmE5YzBmM2U3NDEwGgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oKU2VhcmNoRmxleEofYXNzaXN0YW50R2VuZXJhdGVOZXdQaWxsc0J1dHRvblILbm90RW50aXRsZWRaAGISRGlzY292ZXJ5QXNzaXN0YW50aAJyAHo4Sm1UVXYzVlFCRllIS282MDJEVmZEWTUyUW9PWVByeU1GbFhEVGo3dHptZ1kzYUxjODRkT2lnPT2CAQNhbGyKAQCSAQA=".to_string()),
                    };

                    test_game_loop.send_on_select_event(first_container_focusable_node_id);
                    test_game_loop.tick_until_done();
                    impression_helper
                        .assert_select_impression_was_sent("hm_ass_c_sr300d10_dast_2_1", data);
                },
            );
        }
        #[serial]
        #[test]
        fn it_sends_impressions_for_title_card_grid() {
            launch_test(
                move |ctx| {
                    let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                        .with_responses(vec![NetworkResponseType::Loaded(
                            DiscoveryResponse::SubLevel,
                        )])
                        .setup();

                    compose! {
                        DiscoveryCollection()
                    }
                },
                move |scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_once().node_tree;
                    let impression_helper = expect_context::<ImpressionTestHelper>(scope);

                    let container_id = get_container_test_id("Grid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSRjIyMjNBNjVGOUE4IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI="));
                    let container = node_tree.find_by_test_id(container_id.clone());
                    let first_container_focusable =
                        container.find_any_child_with().is_focusable().find_first();
                    let first_container_focusable_node_id =
                        first_container_focusable.borrow_props().node_id;
                    assert_node_exists!(first_container_focusable);
                    test_game_loop.send_on_focus_event(first_container_focusable_node_id);
                    test_game_loop.tick_until_done();

                    // Might be flaky if removing this
                    // This loop keeps ticking the gameloop to update impressions
                    // until SDK has improve support for testing
                    // https://issues.amazon.com/issues/LRNP-4239
                    for _ in 0..10 {
                        MockClock::advance(IMPRESSION_DURATION + Duration::from_millis(100));
                        test_game_loop.tick_until_done();
                    }

                    let impression_data = ImpressionData {
                        widget_type: Some("titleCard".to_string()),
                        ref_marker: Some("hm_ass_c_sr5ec29d_dast_1_1".to_string()),
                        content_type: None,
                        benefit_id: None,
                        content_id: Some("amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a".to_string()),
                        creative_id: Some("https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5bdb029710d0f8fcb1e8c45a0ebc0ef424951b49a5c5a4ea292ef8816a86c945.jpg".to_string()),
                        analytics: Some(HashMap::from([
                            ("pillId".to_string(), "8".to_string()),
                            ("pillVersion".to_string(), "demo-20241025".to_string()),
                            ("refMarker".to_string(), "hm_ass_c_sr5ec29d_dast_1_1".to_string()),
                        ])),
                        slot_id: Some((0, 1)),
                        size: Some((384.0, 216.0)),
                        carousel_analytics: Some("960|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1JGMjIyM0E2NUY5QTgaACIIc3I1ZWMyOWQSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oKU2VhcmNoRmxleEoZYXNzaXN0YW50VGl0bGVzQWJvdmVQaWxsc1ILbm90RW50aXRsZWRaAGIER3JpZGgBcgB6OGV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09ggEDYWxsigEAkgEA".to_string()),
                    };

                    impression_helper.assert_highlight_impression_was_sent(
                        "hm_ass_c_sr5ec29d_dast_1_1",
                        impression_data.clone(),
                    );

                    test_game_loop.send_on_select_event(first_container_focusable_node_id);
                    test_game_loop.tick_until_done();

                    impression_helper.assert_select_impression_was_sent(
                        "hm_ass_c_sr5ec29d_dast_1_1",
                        impression_data.clone(),
                    );

                    impression_helper.assert_view_impression_was_sent_for_ref_marker(
                        "hm_ass_c_sr5ec29d_dast_1_1",
                        impression_data,
                    );
                },
            );
        }

        #[serial]
        #[test]
        fn it_sends_impression_for_discovery_grid_see_more() {
            launch_test(
                move |ctx| {
                    let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                        .with_responses(vec![NetworkResponseType::Loaded(
                            DiscoveryResponse::TopLevel,
                        )])
                        .setup();

                    compose! {
                        DiscoveryCollection()
                    }
                },
                move |scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_once().node_tree;
                    let impression_helper = expect_context::<ImpressionTestHelper>(scope);
                    let container_id = get_container_test_id("DiscoveryGrid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSOTc2OENCNjk3NUU2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI="));
                    let container = node_tree.find_by_test_id(container_id.clone());
                    let first_container_focusable =
                        container.find_any_child_with().is_focusable().find_first();
                    let first_container_focusable_node_id =
                        first_container_focusable.borrow_props().node_id;
                    assert_node_exists!(first_container_focusable);
                    test_game_loop.send_on_focus_event(first_container_focusable_node_id);
                    test_game_loop.tick_until_done();

                    MockClock::advance(IMPRESSION_DURATION + Duration::from_millis(1));
                    test_game_loop.tick_until_done();

                    let expected_data = ImpressionData {
                        widget_type: Some("imageTextLink".to_string()),
                        ref_marker: Some("".to_string()),
                        content_type: Some("LINK".to_string()),
                        benefit_id: Some("assistant".to_string()),
                        content_id: Some("home:assistant".to_string()),
                        creative_id: None,
                        analytics: Some(HashMap::from([
                            ("refMarker".to_string(), "".to_string()),
                        ])),
                        slot_id: Some((0, 1)),
                        size: Some((100.0, 57.0)),
                        carousel_analytics: Some("476|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1I5NzY4Q0I2OTc1RTYaACIIc3JiOTJkMDUSQQoEaG9tZRIJYXNzaXN0YW50IgZjZW50ZXIqADIkY2E3MTBkMzMtODIxMC00NmI3LWI0MTYtYmE5YzBmM2U3NDEwGgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6G1BWRGlzY292ZXJ5QXNzaXN0YW50U2VydmljZUIXRGlzY292ZXJ5QXNzaXN0YW50UGlsbHNKClNlYXJjaEZsZXhKDmFzc2lzdGFudFBpbGxzUgtub3RFbnRpdGxlZFoAYhJEaXNjb3ZlcnlBc3Npc3RhbnRoAXIAejhKbVRVdjNWUUJGWUhLbzYwMkRWZkRZNTJRb09ZUHJ5TUZsWERUajd0em1nWTNhTGM4NGRPaWc9PYIBA2FsbIoBAJIBAA==".to_string()),
                    };
                    impression_helper
                        .assert_highlight_impression_was_sent("", expected_data.clone());

                    test_game_loop.send_on_select_event(first_container_focusable_node_id);
                    test_game_loop.tick_until_done();

                    impression_helper.assert_select_impression_was_sent("", expected_data.clone());

                    impression_helper
                        .assert_view_impression_was_sent_for_ref_marker("", expected_data.clone());
                },
            );
        }

        #[serial]
        #[test]
        fn it_sends_impression_for_discovery_grid_card() {
            launch_test(
                move |ctx| {
                    let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                        .with_responses(vec![NetworkResponseType::Loaded(
                            DiscoveryResponse::TopLevel,
                        )])
                        .setup();

                    compose! {
                        DiscoveryCollection()
                    }
                },
                move |scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_once().node_tree;
                    let impression_helper = expect_context::<ImpressionTestHelper>(scope);
                    let container_id = get_container_test_id("DiscoveryGrid", String::from("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSOTc2OENCNjk3NUU2IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI="));
                    let container = node_tree.find_by_test_id(container_id.clone());

                    let focusables = container.find_any_child_with().is_focusable().find_all();
                    let second_container_focusable = focusables.get(2).unwrap();
                    let second_container_focusable_node_id =
                        second_container_focusable.borrow_props().node_id;

                    assert_node_exists!(second_container_focusable);
                    test_game_loop.send_on_focus_event(second_container_focusable_node_id);
                    test_game_loop.tick_until_done();

                    MockClock::advance(IMPRESSION_DURATION + Duration::from_millis(1));
                    test_game_loop.tick_until_done();

                    let impression_data = ImpressionData {
                        widget_type: Some("imageTextLink".to_string()),
                        ref_marker: Some("hm_ass_c_srb92d05_dast_1_1".to_string()),
                        content_type: Some("LINK".to_string()),
                        benefit_id: Some("assistant".to_string()),
                        content_id: Some("home:assistant".to_string()),
                        creative_id: Some("https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/small_pill/rm-tile-short-2.png".to_string()),
                        analytics: Some(HashMap::from([
                            ("refMarker".to_string(), "hm_ass_c_srb92d05_dast_1_1".to_string()),
                            ("pillId".to_string(), "485".to_string()),
                            ("pillSource".to_string(), "search_concept".to_string()),
                            ("pillText".to_string(), "Inspiring underdogs".to_string()),
                            ("pillVersion".to_string(), "demo-20241025".to_string()),
                            ("pillVisibility".to_string(), "ROOT".to_string()),
                        ])),
                        slot_id: Some((1, 1)),
                        size: Some((384.0, 144.0)),
                        carousel_analytics: Some("476|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1I5NzY4Q0I2OTc1RTYaACIIc3JiOTJkMDUSQQoEaG9tZRIJYXNzaXN0YW50IgZjZW50ZXIqADIkY2E3MTBkMzMtODIxMC00NmI3LWI0MTYtYmE5YzBmM2U3NDEwGgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6G1BWRGlzY292ZXJ5QXNzaXN0YW50U2VydmljZUIXRGlzY292ZXJ5QXNzaXN0YW50UGlsbHNKClNlYXJjaEZsZXhKDmFzc2lzdGFudFBpbGxzUgtub3RFbnRpdGxlZFoAYhJEaXNjb3ZlcnlBc3Npc3RhbnRoAXIAejhKbVRVdjNWUUJGWUhLbzYwMkRWZkRZNTJRb09ZUHJ5TUZsWERUajd0em1nWTNhTGM4NGRPaWc9PYIBA2FsbIoBAJIBAA==".to_string()),
                    };

                    impression_helper.assert_highlight_impression_was_sent(
                        "hm_ass_c_srb92d05_dast_1_1",
                        impression_data.clone(),
                    );
                    impression_helper.assert_view_impression_was_sent_for_ref_marker(
                        "hm_ass_c_srb92d05_dast_1_1",
                        impression_data.clone(),
                    );

                    test_game_loop.send_on_select_event(second_container_focusable_node_id);
                    test_game_loop.tick_until_done();

                    impression_helper.assert_select_impression_was_sent(
                        "hm_ass_c_srb92d05_dast_1_1",
                        impression_data,
                    );
                },
            );
        }

        #[test]
        #[serial]
        #[ignore = "https://code.amazon.com/reviews/CR-201353308/revisions/2#/details"]
        fn it_sends_view_impressions_for_non_focused_elements() {
            launch_test(
                move |ctx| {
                    let _client_ctx = DiscoveryAssistantTestSetup::default(&ctx)
                        .with_responses(vec![NetworkResponseType::Loaded(
                            DiscoveryResponse::SubLevel,
                        )])
                        .setup();

                    compose! {
                        DiscoveryCollection()
                    }
                },
                move |scope, mut test_game_loop| {
                    let impression_helper = expect_context::<ImpressionTestHelper>(scope);

                    let node_tree = test_game_loop.tick_until_done();

                    // View impressions for the ones that are available; +1ms because otherwise it doesn't trigger for some reason
                    let containers = get_container_nodes(&node_tree);
                    test_game_loop
                        .send_on_focus_event(containers.first().unwrap().borrow_props().node_id);
                    test_game_loop.tick_until_done();

                    MockClock::advance(IMPRESSION_DURATION + Duration::from_millis(1));
                    test_game_loop.tick_until_done();

                    // 8 Grid items + 10 Pill items (the other 10 are not fully in view)
                    assert_eq!(impression_helper.get_view_impressions_count(), 18);

                    // Focusing on fourth container to get view impressions for SeeMoreInfo and the last grid
                    let node_tree = test_game_loop.tick_until_done();
                    let containers = get_container_nodes(&node_tree);
                    test_game_loop
                        .send_on_focus_event(containers.last().unwrap().borrow_props().node_id);
                    test_game_loop.tick_until_done();

                    // This loop keeps ticking the gameloop to update impressions
                    // until SDK has improve support for testing
                    // https://issues.amazon.com/issues/LRNP-4239
                    for _ in 0..10 {
                        MockClock::advance(IMPRESSION_DURATION + Duration::from_millis(1000));
                        test_game_loop.tick_until_done();
                        if impression_helper.get_view_impressions_count() >= 31 {
                            break;
                        }
                    }

                    // Impressions from before (18) plus 8 grid items + 4 pill items + 1 More Info button
                    assert_eq!(impression_helper.get_view_impressions_count(), 31);

                    // Grid: First card
                    impression_helper.assert_view_impression_was_sent_for_ref_marker(
                        "hm_ass_c_sr5ec29d_dast_1_1",
                        ImpressionData {
                            widget_type: Some("titleCard".to_string()),
                            ref_marker: Some("hm_ass_c_sr5ec29d_dast_1_1".to_string()),
                            content_type: None,
                            benefit_id: None,
                            content_id: Some("amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a".to_string()),
                            creative_id: Some("https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5bdb029710d0f8fcb1e8c45a0ebc0ef424951b49a5c5a4ea292ef8816a86c945.jpg".to_string()),
                            analytics: Some({
                                let mut map = HashMap::new();
                                map.insert("refMarker".to_string(), "hm_ass_c_sr5ec29d_dast_1_1".to_string());
                                map.insert("pillId".to_string(), "8".to_string());
                                map.insert("pillVersion".to_string(), "demo-20241025".to_string());
                                map
                            }),
                            slot_id: Some((0, 1)),
                            size: Some((384.0, 216.0)),
                            carousel_analytics: Some("960|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1JGMjIyM0E2NUY5QTgaACIIc3I1ZWMyOWQSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oKU2VhcmNoRmxleEoZYXNzaXN0YW50VGl0bGVzQWJvdmVQaWxsc1ILbm90RW50aXRsZWRaAGIER3JpZGgBcgB6OGV2WWo1WHRNa1V3UnlxUHpvVUVWRDhsRjhNS285bjN2R1NVQXlZcGNKMFJIbU9ZNjN0dzViZz09ggEDYWxsigEAkgEA".to_string()),
                        });

                    // DiscoveryPill: 4th pill
                    impression_helper.assert_view_impression_was_sent_for_ref_marker(
                        "hm_ass_c_sre5b23c_dast_2_4",
                        ImpressionData {
                            widget_type: Some("imageTextLink".to_string()),
                            ref_marker: Some("hm_ass_c_sre5b23c_dast_2_4".to_string()),
                            content_type: Some("LINK".to_string()),
                            benefit_id: Some("assistant".to_string()),
                            content_id: Some("home:assistant".to_string()),
                            creative_id: Some("https://m.media-amazon.com/images/G/01/digtial/video/search/beacon/large_pill/Image-29.png".to_string()),
                            analytics: Some(HashMap::from([
                                ("refMarker".to_string(), "hm_ass_c_sre5b23c_dast_2_4".to_string()),
                                ("pillSource".to_string(), "search_concept".to_string()),
                                ("pillId".to_string(), "1723".to_string()),
                                ("pillVisibility".to_string(), "ROOT".to_string()),
                                ("pillVersion".to_string(), "demo-20241025".to_string()),
                                ("pillText".to_string(), "Secretive Affairs".to_string()),
                            ])),
                            slot_id: Some((3, 2)),
                            size: Some((57.0, 100.0)),
                            carousel_analytics: Some("1008|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1IwMTM4ODE4MzI4MkMaACIIc3JlNWIyM2MSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6G1BWRGlzY292ZXJ5QXNzaXN0YW50U2VydmljZUIXRGlzY292ZXJ5QXNzaXN0YW50UGlsbHNKClNlYXJjaEZsZXhKDmFzc2lzdGFudFBpbGxzUgtub3RFbnRpdGxlZFoAYhJEaXNjb3ZlcnlBc3Npc3RhbnRoAnIAejhldllqNVh0TWtVd1J5cVB6b1VFVkQ4bEY4TUtvOW4zdkdTVUF5WXBjSjBSSG1PWTYzdHc1Ymc9PYIBA2FsbIoBAJIBAA==".to_string()),
                        });

                    // Discovery Grid: See more button
                    impression_helper.assert_view_impression_was_sent_for_ref_marker(
                    "hm_ass_c_sr5ec29d_dast_4_1",
                    ImpressionData {
                        widget_type: Some("imageTextLink".to_string()),
                        ref_marker: Some("hm_ass_c_sr5ec29d_dast_4_1".to_string()),
                        content_type: Some("LINK".to_string()),
                        benefit_id: Some("assistant".to_string()),
                        content_id: Some("home:assistant".to_string()),
                        creative_id: Some("https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v1.png".to_string()),
                        analytics: Some(HashMap::from([
                            ("refMarker".to_string(), "hm_ass_c_sr5ec29d_dast_4_1".to_string()),
                        ])),
                        slot_id: Some((0, 4)),
                        size: Some((57.0, 100.0)),
                        carousel_analytics: Some("988|CkgKKERpc2NvdmVyeUFzc2lzdGFudFJTSVBMaXZlRGVmYXVsdERlZmF1bHQSEDI6U1JCRjhFQURDOTkyQzkaACIIc3I1ZWMyOWQSzwMKBGhvbWUSCWFzc2lzdGFudBqLA2Fzc2lzdGFudFN0YXRlLmV5SnpjRzl6VTNSaGRHVWlPbnNpYzJWc1pXTjBaV1JRYVd4c0lqcDdJbWxrSWpvaU9DSXNJbkJvY21GelpTSTZJa2R5YVhSMGVTQmpjbWx0WlNCellXZGhjeUo5ZlN3aWFXNTBaWEp1WVd4QmMzTnBjM1JoYm5SVGRHRjBaU0k2ZXlKelpXeGxZM1JsWkZCcGJHeEpaQ0k2SWpnaUxDSndhV3hzYzFabGNuTnBiMjRpT2lKa1pXMXZMVEl3TWpReE1ESTFJaXdpYzNSaGRHVkxaWGtpT2lKak1EUXhNV1UxT1Mxa05XRXpMVFExWWpNdFltWmlZeTFpTW1SbE1qWTNOR0k0TkdVaUxDSndhV3hzVm1GeWFXVjBlVlJ2VEdGemRGSmxkSEpwWlhabFpFbHVaR1Y0VFdGd0lqcDdJbFZ1Y0dWeWMyOXVZV3hwZW1Wa1FuSnZZV1FpT2pFNWZTd2lhWE5HYVhKemRGQmhaMlVpT21aaGJITmxmWDA9IgZjZW50ZXIqADIkYjdmZjc2NmEtMDFhYi00MDYyLThlMzAtYWZiOWIxY2FjOGQ2GgAiACoAMg9mYWNldGVkQ2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oKU2VhcmNoRmxleEofYXNzaXN0YW50R2VuZXJhdGVOZXdQaWxsc0J1dHRvblILbm90RW50aXRsZWRaAGISRGlzY292ZXJ5QXNzaXN0YW50aARyAHo4ZXZZajVYdE1rVXdSeXFQem9VRVZEOGxGOE1LbzluM3ZHU1VBeVlwY0owUkhtT1k2M3R3NWJnPT2CAQNhbGyKAQCSAQA=".to_string())
                    });
                },
            );
        }
    }
}
