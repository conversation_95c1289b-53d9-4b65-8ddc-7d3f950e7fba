use amzn_fable_tokens::{FableIcon, FableSize, FableSpacing};
use common_transform_types::container_items::IconBadges;
use container_types::ui_signals::DiscoveryAssistantHeaderModel;
use fableous::{buttons::secondary_button::*, typography::typography::*};
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{prelude::*, text::TextContent};
use lrc_image::{
    lrc_image::*,
    types::{ImageData, ImageFormat, ImageTag},
};

use crate::ui::beta_badge::*;

const DISCOVERY_HEADER_LOGO_TEST_ID: &str = "discovery_header_logo_test_id";
const HEADER_WIDTH: f32 = 1644.0;

#[Composer]
pub fn PageHeader(
    ctx: &AppContext,
    model: RwSignal<DiscoveryAssistantHeaderModel>,
) -> StackComposable {
    let model = model.get_untracked();

    compose! {
            Stack() {
                Row() {
                    Memo(item_builder: Box::new(move |ctx| {
                    model.selected_pill.as_ref().map_or_else(
                        // Ghost element when selectedPill is None to push the title to the end using SpaceBetween
                        || compose_option!{ Row(){} },
                        |selected_pill| {
                            let selected_pill = selected_pill.clone();
                            compose_option! {
                                SecondaryButton(variant: SecondaryButtonVariant::IconAndTextSize400(
                                    FableIcon::GEN_AI.to_string(),
                                    TextContent::String(selected_pill)))
                                .focus_enabled(false)
                            }
                        })
                    }))

                    Memo(item_builder: Box::new(move |ctx| {
                        if let Some(title) = &model.title {
                            let logo_url = model.logo_url.clone();
                            let badge = model.badge.clone();

                            compose_option! {
                                Row() {
                                    Memo(item_builder: Box::new(move |ctx| {
                                        logo_url.as_ref()
                                        .map(|url| {
                                            compose! {
                                                Stack() {LRCImage(data: ImageData{
                                                    url: url.clone(),
                                                    height: FableSize::SIZE200,
                                                    width: FableSize::SIZE200,
                                                    tags: vec![ImageTag::Format(ImageFormat::PNG)]
                                                })}
                                                .padding(Padding::new(0.0, FableSpacing::SPACING113, 0.0, 0.0))
                                                .test_id(DISCOVERY_HEADER_LOGO_TEST_ID)
                                            }
                                        })
                                    }))

                                    TypographyHeading600(content: TextContent::String(title.clone()))

                                    Memo(item_builder: Box::new(move |ctx| {
                                        badge.as_ref()
                                            .filter(|badge| matches!(badge, IconBadges::GenAI))
                                            .map(|_| {
                                                compose! {
                                                    BetaBadge()
                                                    .padding(Padding::new(FableSpacing::SPACING065, 0.0, 0.0, 0.0))
                                                }
                                            })
                                    }))
                                }
                                .cross_axis_alignment(CrossAxisAlignment::Center)
                            }

                        } else {
                            None
                        }
                    }))
                }
                .main_axis_alignment(MainAxisAlignment::SpaceBetween)
                .width(HEADER_WIDTH)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::assert_node_does_not_exist;
    use ignx_compositron::test_utils::assert_node_exists;
    use rstest::*;
    use serial_test::serial;

    #[rstest]
    #[serial]
    #[case(DiscoveryAssistantHeaderModel{ selected_pill: None, logo_url: None, title: None, badge: None },
        false, false, false, false)]
    #[case(DiscoveryAssistantHeaderModel{ selected_pill: Some("selected-pill".into()), logo_url: None, title: None, badge: None },
        true, false, false, false)]
    #[case(DiscoveryAssistantHeaderModel{ selected_pill: None, logo_url: None, title: Some("title".into()), badge: None },
        false, false, true, false)]
    #[case(DiscoveryAssistantHeaderModel{ selected_pill: Some("selected-pill".into()), logo_url: Some("url".into()), title: Some("title".into()), badge: Some(IconBadges::GenAI) },
        true, true, true, true)]
    #[case(DiscoveryAssistantHeaderModel{ selected_pill: Some("selected-pill".into()), logo_url: Some("url".into()), title: None, badge: Some(IconBadges::GenAI) },
        true, false, false, false)]
    fn it_displays_expected_ui_for_model(
        #[case] model: DiscoveryAssistantHeaderModel,
        #[case] expect_selected_pill: bool,
        #[case] expect_logo: bool,
        #[case] expect_title: bool,
        #[case] expect_badge: bool,
    ) {
        launch_test(
            move |ctx| {
                compose! {
                    PageHeader(model: create_rw_signal(ctx.scope(), model))
                }
            },
            move |_, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let selected_pill = node_tree
                    .find_by_props()
                    .contains_text("selected-pill")
                    .find_first();
                if expect_selected_pill {
                    assert_node_exists!(selected_pill);
                } else {
                    assert_node_does_not_exist!(selected_pill);
                }

                let logo = node_tree
                    .find_by_props()
                    .test_id(DISCOVERY_HEADER_LOGO_TEST_ID)
                    .find_first();
                if expect_logo {
                    assert_node_exists!(logo);
                } else {
                    assert_node_does_not_exist!(logo);
                }

                let title = node_tree
                    .find_by_props()
                    .contains_text("title")
                    .find_first();
                if expect_title {
                    assert_node_exists!(title);
                } else {
                    assert_node_does_not_exist!(title);
                }

                let badge = node_tree
                    .find_by_props()
                    .test_id(BETA_BADGE_TEST_ID)
                    .find_first();
                if expect_badge {
                    assert_node_exists!(badge);
                } else {
                    assert_node_does_not_exist!(badge);
                }
            },
        );
    }
}
