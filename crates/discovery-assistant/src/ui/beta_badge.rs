use amzn_fable_tokens::*;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

pub const BETA_BADGE_TEST_ID: &str = "da-beta-badge-test-id";

#[Composer]
pub fn BetaBadge(ctx: &AppContext) -> RowComposable {
    let color = get_ignx_color(FableColor::MAGICIAN500);

    compose! {
        Row() {
            Typography(
                content: "Beta",
                color,
                type_ramp: MaybeSignal::Static(FableBadge::LABEL_TEXT_TYPE.into())
            )
            .translate_x(FableSpacing::SPACING015)
        }
        .test_id(BETA_BADGE_TEST_ID)
    }
}
