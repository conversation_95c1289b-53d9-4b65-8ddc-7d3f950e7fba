use crate::font_icon::*;
use crate::typography::typography::*;
use crate::utils::get_ignx_color;
use amzn_fable_tokens::{FableColor, FableIcon};
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::prelude::safe::*;
use ignx_compositron::{compose, Composer};

pub const EOC_ICON_TEST_ID: &str = "fable-icon-eoc-test-id";
pub const EOC_LABEL_TEST_ID: &str = "fable-label-eoc-test-id";
pub const EOC_TEST_ID: &str = "fable-eoc-test-id";

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h)]
#[derive_test_only(PartialEq, Debug)]
pub enum EntitlementType {
    Entitled,
    Unentitled,
    Unavailable,
    None,
}

#[Composer]
pub fn EntitlementIcon<'s>(
    ctx: &AppContext<'s>,
    #[into] entitlement_type: MaybeSignal<'s, EntitlementType>,
    #[into] focused: MaybeSignal<'s, bool>,
) -> ShowComposable<'s> {
    let icon = Signal::derive(ctx.scope(), move || match entitlement_type.get() {
        EntitlementType::Entitled => FableIcon::ENTITLED.to_owned(),
        EntitlementType::Unentitled => FableIcon::STORE_FILLED.to_owned(),
        EntitlementType::Unavailable => FableIcon::ERROR.to_owned(),
        EntitlementType::None => "".to_string(),
    });

    let icon_color = Signal::derive(ctx.scope(), move || {
        if focused.get() {
            return get_ignx_color(FableColor::INVERSE);
        };

        match entitlement_type.get() {
            EntitlementType::Entitled => Color::new(26, 152, 255, 255),
            EntitlementType::Unentitled => Color::new(255, 214, 44, 255),
            EntitlementType::Unavailable => Color::new(255, 159, 160, 255),
            EntitlementType::None => Color::white(),
        }
    });

    let should_render = Signal::derive(ctx.scope(), move || {
        !matches!(entitlement_type.get(), EntitlementType::None)
    });
    compose! {
        if should_render.get() {
            FontIcon(icon, color: icon_color, size: FontSize(24))
                .test_id(EOC_ICON_TEST_ID)
        }
    }
}

#[Composer]
pub fn EOC<'s>(
    ctx: &AppContext<'s>,
    #[into] entitlement_type: MaybeSignal<'s, EntitlementType>,
    #[into] content: MaybeSignalTextContent<'s>,
) -> RowComposable<'s> {
    compose! {
        Row() {
            EntitlementIcon(entitlement_type, focused: false)
            TypographyLabel100(content)
                .test_id(EOC_LABEL_TEST_ID)
        }
        .test_id(EOC_TEST_ID)
        .main_axis_alignment(MainAxisAlignment::SpacedBy(12.0))
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::test_utils::assert_node_does_not_exist;
    use ignx_compositron::test_utils::node_properties::NodeTypeProperties;
    use rstest::rstest;

    #[rstest]
    #[case(EntitlementType::Entitled, "entitled".to_string(), FableIcon::ENTITLED.to_string(), Color::new(26, 152, 255, 255))]
    #[case(EntitlementType::Unentitled, "untitled".to_string(), FableIcon::STORE_FILLED.to_string(), Color::new(255, 214, 44, 255))]
    #[case(EntitlementType::Unavailable, "unavailable".to_string(), FableIcon::ERROR.to_string(), Color::new(255, 159, 160, 255))]
    fn should_render_correct_icon_and_content(
        #[case] ty: EntitlementType,
        #[case] content: String,
        #[case] icon: String,
        #[case] color: Color,
    ) {
        ignx_compositron::app::launch_test(
            {
                let content = content.clone();
                move |ctx| {
                    compose! {
                        EOC(entitlement_type: MaybeSignal::Static(ty), content: MaybeSignalTextContent::String(MaybeSignal::Static(content)))
                    }
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let icon_node = node_tree.find_by_test_id(EOC_ICON_TEST_ID);
                let icon_props = icon_node.borrow_props();
                assert_eq!(icon_props.text.clone().unwrap(), icon);
                match &icon_props.node_type_props {
                    NodeTypeProperties::Text(text_props) => {
                        assert_eq!(text_props.color, color);
                    }
                    _ => panic!("Unexpected node type"),
                }

                let label = node_tree.find_by_test_id(EOC_LABEL_TEST_ID);
                let label_props = label.borrow_props();
                assert_eq!(label_props.text.clone().unwrap(), content);
            },
        )
    }

    fn should_not_render_icon_entitlement_type() {
        ignx_compositron::app::launch_test(
            |ctx| {
                compose! {
                    EOC(entitlement_type: MaybeSignal::Static(EntitlementType::None), content: MaybeSignalTextContent::String(MaybeSignal::Static("exists".to_string())))
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let icon_node = node_tree.find_by_test_id(EOC_ICON_TEST_ID);
                assert_node_does_not_exist!(icon_node);

                let label = node_tree.find_by_test_id(EOC_LABEL_TEST_ID);
                let label_props = label.borrow_props();
                assert_eq!(label_props.text.clone().unwrap(), "exists".to_string());
            },
        )
    }
}
