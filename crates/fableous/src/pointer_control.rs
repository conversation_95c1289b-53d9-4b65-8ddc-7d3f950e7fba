use crate::animations::fable_motion_linear_medium;
use crate::font_icon::*;
use crate::utils::get_ignx_color;
use amzn_fable_tokens::*;
use ignx_compositron::app::wasm_app::HoverEvent;
use ignx_compositron::focus::*;
use ignx_compositron::pointer_control::*;
use ignx_compositron::prelude::safe::*;
use ignx_compositron::{compose, compose_option, Composer};
use rust_features::use_rust_features;

use std::rc::Rc;

const COLOR_DEFAULT: Color = get_ignx_color(FableColor::PRIMARY_EMPHASIS);
const COLOR_HOVERED: Color = get_ignx_color(FableColor::WHITE);
const COLOR_SHADOW: Color = get_ignx_color(FableColor::BLACK);

pub const POINTER_CONTROL_ARROW_TEST_ID: &str = "pointer-control-arrow-test-id";
pub const POINTER_CONTROL_CARET_TEST_ID: &str = "pointer-control-caret-test-id";
pub const POINTER_CONTROL_ICON_TEST_ID: &str = "pointer-control-icon-test-id";

#[derive(PartialEq)]
enum PointerControlIconType {
    Caret,
    Arrow,
}

#[derive(Clone)]
pub enum Direction {
    Up,
    Left,
    Down,
    Right,
}

#[derive(Clone)]
pub struct PointerControlArrowHoverContext<'a>(pub RwSignal<'a, bool>);

// Add this function to check if a pointer control arrow is hovered
pub fn is_pointer_control_arrow_hovered(scope: Scope<'_>) -> MaybeSignal<'_, bool> {
    if let Some(PointerControlArrowHoverContext(hovered)) = use_context(scope) {
        hovered.into()
    } else {
        MaybeSignal::Static(false)
    }
}

pub fn is_pointer_control_enabled(scope: Scope<'_>) -> bool {
    use_rust_features(scope).is_pointer_control_enabled()
}

pub fn is_pointer_control_active(scope: Scope<'_>) -> MaybeSignal<'_, bool> {
    if let Some(PointerControlActiveContext(active)) = use_context(scope) {
        active.into()
    } else {
        MaybeSignal::Static(false)
    }
}

pub fn use_focus_pointer_control(scope: Scope<'_>) -> Signal<'_, FocusPointerControl> {
    let enabled = is_pointer_control_enabled(scope);
    Signal::derive(scope, move || {
        if enabled {
            FocusPointerControl::Enabled
        } else {
            FocusPointerControl::Inherit
        }
    })
}

#[Composer]
pub fn PointerControlFocusTrap<'s>(
    ctx: &AppContext<'s>,
    width: f32,
    height: f32,
    #[into]
    #[optional = true]
    enabled: MaybeSignal<'s, bool>,
    #[into] focus_signal: FocusSignal<'s>,
    #[optional = Rc::new(|| {})] on_hover: Rc<dyn Fn() + 's>,
) -> RowComposable<'s> {
    compose! {
        Row() {
            Memo(item_builder: Box::new(move |ctx|  {
                if !focus_signal.get() && enabled.get() && is_pointer_control_enabled(ctx.scope) {
                    let on_hover = on_hover.clone();
                    compose_option! {
                        Rectangle()
                        .pointer_control_target(None)
                        .on_hover(move |event: &HoverEvent| {
                            if event == &HoverEvent::Enter {
                                on_hover();
                                focus_signal.set(true);
                            }
                        })
                        .background_color(Color::transparent())
                        .width(width)
                        .height(height)
                    }
                } else {
                    None
                }
            }))
        }
    }
}

#[Composer]
pub fn PointerControlArrow<'s>(
    ctx: &AppContext<'s>,
    direction: Direction,
    #[optional = Rc::new(|| {})] on_select: Rc<dyn Fn() + 's>,
    #[optional = true]
    #[into]
    visible: MaybeSignal<'s, bool>,
    #[optional = MaybeSignal::Static(Some(Vec2::new(50.0, 50.0)))] focus_rectangle: MaybeSignal<
        's,
        Option<Vec2>,
    >,
) -> impl VisualComposable<'s> {
    compose! {
        PointerControlIcon(direction, icon_type: PointerControlIconType::Arrow, on_select, visible, focus_rectangle)
        .test_id(POINTER_CONTROL_ARROW_TEST_ID);
    }
}

#[Composer]
pub fn PointerControlCaret<'s>(
    ctx: &AppContext<'s>,
    direction: Direction,
    #[optional = Rc::new(|| {})] on_select: Rc<dyn Fn() + 's>,
    #[optional = true]
    #[into]
    visible: MaybeSignal<'s, bool>,
    #[optional = MaybeSignal::Static(Some(Vec2::new(200.0, 200.0)))] focus_rectangle: MaybeSignal<
        's,
        Option<Vec2>,
    >,
    #[optional = Rc::new(|_| {})] on_hover_change: Rc<dyn Fn(bool) + 's>,
) -> impl VisualComposable<'s> {
    compose! {
        PointerControlIcon(direction, icon_type: PointerControlIconType::Caret, on_select, visible, focus_rectangle, on_hover_change)
        .test_id(POINTER_CONTROL_CARET_TEST_ID);
    }
}

#[Composer]
fn PointerControlFocusRectangle<'s>(
    ctx: &AppContext<'s>,
    focus_rectangle: MaybeSignal<'s, Option<Vec2>>,
) -> MemoComposable<'s> {
    let item_builder: Box<ComposableBuilder<'s, ColumnComposable<'s>>> =
        Box::new(move |ctx| match focus_rectangle.get() {
            None => None,
            Some(rectangle) => {
                compose_option! {
                    Column() {
                        Rectangle()
                            .width(rectangle.x)
                            .height(rectangle.y)
                    }
                }
            }
        });

    compose! {
        Memo(item_builder)
    }
}

#[Composer]
pub fn PointerControlIcon<'s>(
    ctx: &AppContext<'s>,
    direction: Direction,
    icon_type: PointerControlIconType,
    #[optional = true]
    #[into]
    visible: MaybeSignal<'s, bool>,
    #[optional = Rc::new(|| {})] on_select: Rc<dyn Fn() + 's>,
    #[optional = Vec2::new(100, 100)] focus_rectangle: MaybeSignal<'s, Option<Vec2>>,
    #[optional = Rc::new(|_| {})] on_hover_change: Rc<dyn Fn(bool) + 's>,
) -> impl VisualComposable<'s> {
    // In the spec, pointer control icons are either arrows or carets.
    let icon = match icon_type {
        PointerControlIconType::Caret => match direction {
            Direction::Up => FableIcon::CARET_UP,
            Direction::Left => FableIcon::CARET_LEFT,
            Direction::Down => FableIcon::CARET_DOWN,
            Direction::Right => FableIcon::CARET_RIGHT,
        },
        PointerControlIconType::Arrow => match direction {
            Direction::Up => FableIcon::ARROW_UP,
            Direction::Left => FableIcon::ARROW_LEFT,
            Direction::Down => FableIcon::ARROW_DOWN,
            Direction::Right => FableIcon::ARROW_RIGHT,
        },
    };

    let focus_direction = match direction {
        Direction::Up => FocusDirection::Up,
        Direction::Left => FocusDirection::Left,
        Direction::Down => FocusDirection::Down,
        Direction::Right => FocusDirection::Right,
    };

    let size = match icon_type {
        PointerControlIconType::Caret => FontSize(150),
        PointerControlIconType::Arrow => FontSize(45),
    };

    let has_shadow = icon_type == PointerControlIconType::Caret;

    compose! {
        Row() {
            Memo(item_builder: Box::new(move |ctx| {
                if is_pointer_control_enabled(ctx.scope) {
                    let color = create_rw_signal(ctx.scope(), COLOR_DEFAULT);
                    let opacity = create_rw_signal(ctx.scope(), 0.0);

                    let animation_ctx = ctx.clone();
                    create_effect(ctx.scope(), move |_| {
                        let target_opacity = if visible.get() { 1.0 } else { 0.0 };
                        animation_ctx.with_animation(fable_motion_linear_medium(), move || {
                            opacity.set(target_opacity);
                        })
                    });

                    let on_hover_change = on_hover_change.clone();
                    let on_select = on_select.clone();
                    compose_option!{
                        Stack() {
                            PointerControlFocusRectangle(focus_rectangle)
                            PointerControlIconShadow(icon, visible: has_shadow)
                            FontIcon(icon, color, size)
                        }
                        .alignment(Alignment::Center)
                        .opacity(opacity)
                        .pointer_control_target(Some(focus_direction))
                        .on_hover(move |hover_event| {
                            match hover_event {
                                HoverEvent::Enter => {
                                    color.set(COLOR_HOVERED);
                                    on_hover_change(true);
                                },
                                HoverEvent::Exit => {
                                    color.set(COLOR_DEFAULT);
                                    on_hover_change(false)
                                },
                            };
                        })
                        .on_select(move || on_select())
                        .test_id(POINTER_CONTROL_ICON_TEST_ID)
                    }
                } else {
                    None
                }
            }))
        }
    }
}

#[Composer]
pub fn PointerControlIconShadow<'s>(
    ctx: &AppContext<'s>,
    icon: &'static str,
    visible: bool,
) -> MemoComposable<'s> {
    compose! {
        Memo(item_builder: Box::new(move |ctx| {
            if visible {
                compose_option! {
                    FontIcon(icon, color: COLOR_SHADOW, size: FontSize(150))
                    .translate(Vec2::new(3.0, 3.0))
                    .opacity(0.8)
                }
            } else {
                None
            }
        }))
    }
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn should_provide_and_use_pointer_control_arrow_hover_context() {
        ignx_compositron::app::launch_test(
            move |ctx| {
                let hover_signal = create_rw_signal(ctx.scope(), true);
                provide_context(ctx.scope(), PointerControlArrowHoverContext(hover_signal));

                compose! {
                    Column() {
                        // Component that uses the context
                        Memo(item_builder: Box::new(move |ctx| {
                            let is_hovered = is_pointer_control_arrow_hovered(ctx.scope);
                            compose_option! {
                                Rectangle()
                                .width(if is_hovered.get() { 100.0 } else { 50.0 })
                                .height(50.0)
                                .test_id("test-rectangle")
                            }
                        }))
                    }
                }
            },
            move |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Rectangle should use the context value to set its width
                let rectangle_node = node_tree.find_by_test_id("test-rectangle");
                let rectangle_props = rectangle_node.borrow_props();
                assert_eq!(rectangle_props.layout.size.width, 100.0);

                // Update the context value
                let hover_signal = use_context::<PointerControlArrowHoverContext<'_>>(scope)
                    .map(|ctx| ctx.0)
                    .unwrap();

                hover_signal.set(false);
                test_game_loop.tick_until_done();

                // Rectangle should update based on the new context value
                let node_tree = test_game_loop.tick_until_done();
                let rectangle_node = node_tree.find_by_test_id("test-rectangle");
                let rectangle_props = rectangle_node.borrow_props();
                assert_eq!(rectangle_props.layout.size.width, 50.0);
            },
        )
    }
}
