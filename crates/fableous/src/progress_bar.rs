use crate::utils::get_ignx_color;
use amzn_fable_tokens::*;
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::prelude::safe::*;
use ignx_compositron::{compose, compose_option, Composer};

pub const PROGRESS_BAR_TEST_ID: &str = "progress-bar-test-id";
pub const PROGRESS_BAR_FILLED_TEST_ID: &str = "progress-bar-filled-test-id";
pub const PROGRESS_BAR_EMPTY_TEST_ID: &str = "progress-bar-empty-test-id";

#[derive(Clone, PartialEq, Copy)]
#[derive_test_only(Debug)]
pub enum ProgressBarVariant {
    VOD,
    OnAir,
}

// TODO: build theme
// Specify LTR on stack
#[Composer]
pub fn ProgressBar<'s>(
    ctx: &AppContext<'s>,
    #[into] progress: MaybeSignal<'s, f32>,
    #[into] variant: MaybeSignal<'s, ProgressBarVariant>,
    #[optional] rounded: bool,
    #[optional] width: MaybeSignal<'s, Option<f32>>,
) -> MemoComposable<'s> {
    let border_radius = create_memo(ctx.scope(), move |_| {
        if rounded {
            FableProgressBar::BORDER_RADIUS
        } else {
            FableBorder::RADIUS000
        }
    });

    let progress_color = Signal::derive(ctx.scope(), {
        move || {
            get_ignx_color(match variant.get() {
                ProgressBarVariant::VOD => FableProgressBar::SURFACE_COLOR_VOD,
                ProgressBarVariant::OnAir => FableProgressBar::SURFACE_COLOR_ONAIR,
            })
        }
    });

    compose! {
        Memo(item_builder: Box::new(move |ctx| {
            if let Some(width) = width.get(){
                let progress_width = width * progress.get().clamp(0.0, 1.0);

                compose_option! {
                    Column() {
                        Stack() {
                            Rectangle()
                                .width(width)
                                .height(FableProgressBar::SIZE)
                                .background_color(get_ignx_color(FableProgressBar::BACKGROUND_COLOR))
                                .border_radius(border_radius)
                            Rectangle()
                                .width(progress_width)
                                .height(FableProgressBar::SIZE)
                                .background_color(progress_color)
                                .border_radius(border_radius)
                                .test_id(PROGRESS_BAR_FILLED_TEST_ID)
                        }.test_id(PROGRESS_BAR_TEST_ID)
                    }.cross_axis_stretch(true)
                }
            }
            else {
                compose_option! {
                    Column() {
                        Rectangle()
                            .flex(1.0)
                            .height(FableProgressBar::SIZE)
                            .background_color(get_ignx_color(FableProgressBar::BACKGROUND_COLOR))
                            .border_radius(border_radius)
                        Row() {
                            Rectangle()
                                .flex(progress)
                                .height(FableProgressBar::SIZE)
                                .background_color(progress_color)
                                .border_radius(border_radius)
                                .test_id(PROGRESS_BAR_FILLED_TEST_ID)
                            Rectangle()
                                .flex(1.0 - progress.get())
                                .height(FableProgressBar::SIZE)
                                .border_radius(border_radius)
                                .test_id(PROGRESS_BAR_EMPTY_TEST_ID)
                        }
                        .flex(1.0)
                        .translate_y(-FableProgressBar::SIZE)
                    }
                    .height(FableProgressBar::SIZE)
                    .test_id(PROGRESS_BAR_TEST_ID)
                    .cross_axis_stretch(true)
                    .padding(Padding::horizontal(FableSpacing::SPACING065))
                }
            }
        }))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use rstest::rstest;

    const TEST_WIDTH: f32 = 200.0;

    #[test]
    fn test_progress_bar_renders_with_flex_layout() {
        launch_test(
            |ctx| {
                let progress = create_rw_signal(ctx.scope(), 0.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);

                compose! {
                    ProgressBar(progress, variant)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify the progress bar container exists
                let container = node_tree.find_by_test_id(PROGRESS_BAR_TEST_ID);
                assert_node_exists!(container);

                // Verify filled part exists
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_node_exists!(filled);

                // Verify empty part exists
                let empty = node_tree.find_by_test_id(PROGRESS_BAR_EMPTY_TEST_ID);
                assert_node_exists!(empty);
            },
        );
    }

    #[test]
    fn test_progress_bar_with_explicit_width() {
        launch_test(
            |ctx| {
                let progress = create_rw_signal(ctx.scope(), 0.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                compose! {
                    ProgressBar(progress, variant, width)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify the progress bar container exists
                let container = node_tree.find_by_test_id(PROGRESS_BAR_TEST_ID);
                assert_node_exists!(container);

                // Verify filled part exists
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_node_exists!(&filled);

                // With explicit width, we shouldn't have the empty part
                let empty = node_tree.find_by_test_id(PROGRESS_BAR_EMPTY_TEST_ID);
                assert_node_does_not_exist!(empty);

                // Check that the filled width is correct (200.0 * 0.5 = 100.0)
                let filled_width = filled.borrow_props().layout.size.width;
                assert_eq!(filled_width, 100.0);
            },
        );
    }

    #[rstest]
    #[ignore]
    #[case(0.0, 0.0, 1.0)]
    #[ignore]
    #[case(0.5, 0.5, 0.5)]
    #[ignore]
    #[case(1.0, 1.0, 0.0)]
    fn test_progress_bar_flex_values(
        #[case] progress: f32,
        #[case] expected_filled_flex: f32,
        #[case] expected_empty_flex: f32,
    ) {
        launch_test(
            move |ctx| {
                let progress = create_rw_signal(ctx.scope(), progress);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);

                compose! {
                    Column() {
                        ProgressBar(progress, variant)
                    }
                    .width(100.0)
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let progress_bar = node_tree.find_by_test_id(PROGRESS_BAR_TEST_ID);
                let progress_bar_width = progress_bar.borrow_props().layout.size.width;

                // Verify filled part has correct flex
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_node_exists!(&filled);
                let expected_filled_width = progress_bar_width * expected_filled_flex;
                assert_eq!(
                    filled.borrow_props().layout.size.width,
                    expected_filled_width
                );

                // Verify empty part has correct flex
                let empty = node_tree.find_by_test_id(PROGRESS_BAR_EMPTY_TEST_ID);
                assert_node_exists!(&empty);
                let expected_empty_width = progress_bar_width * expected_empty_flex;
                assert_eq!(empty.borrow_props().layout.size.width, expected_empty_width);
            },
        );
    }

    #[test]
    fn test_progress_bar_clamps_out_of_range_values_high() {
        launch_test(
            |ctx| {
                // Test with progress > 1.0
                let progress = create_rw_signal(ctx.scope(), 1.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                compose! {
                    ProgressBar(progress, variant, width)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify filled part has width = TEST_WIDTH (clamped to 1.0)
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_eq!(filled.borrow_props().layout.size.width, TEST_WIDTH);
            },
        );
    }

    #[test]
    fn test_progress_bar_clamps_out_of_range_values_low() {
        launch_test(
            |ctx| {
                // Test with progress < 0.0
                let progress = create_rw_signal(ctx.scope(), -0.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                compose! {
                    ProgressBar(progress, variant, width)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify filled part has width = 0.0 (clamped)
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_eq!(filled.borrow_props().layout.size.width, 0.0);
            },
        );
    }

    #[test]
    fn test_progress_bar_rounded_corners() {
        launch_test(
            |ctx| {
                let progress = create_rw_signal(ctx.scope(), 0.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                compose! {
                    ProgressBar(progress, variant, rounded: true, width)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify filled part has the correct border radius
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_eq!(
                    filled.borrow_props().base_styles.border_radius.unwrap(),
                    FableProgressBar::BORDER_RADIUS
                );
            },
        );

        launch_test(
            |ctx| {
                let progress = create_rw_signal(ctx.scope(), 0.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                compose! {
                    ProgressBar(progress, variant, rounded: false, width)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify filled part has the default border radius
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_eq!(
                    filled.borrow_props().base_styles.border_radius.unwrap(),
                    FableBorder::RADIUS000
                );
            },
        );
    }

    #[test]
    fn test_progress_bar_vod_variant_color() {
        launch_test(
            |ctx| {
                let progress = create_rw_signal(ctx.scope(), 0.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                compose! {
                    ProgressBar(progress, variant, width)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify filled part has the correct color
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                let vod_color = get_ignx_color(FableProgressBar::SURFACE_COLOR_VOD);
                assert_eq!(
                    filled.borrow_props().base_styles.background_color.unwrap(),
                    vod_color
                );
            },
        );
    }

    #[test]
    fn test_progress_bar_onair_variant_color() {
        launch_test(
            |ctx| {
                let progress = create_rw_signal(ctx.scope(), 0.5);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::OnAir);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                compose! {
                    ProgressBar(progress, variant, width)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Verify filled part has the correct color
                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                let onair_color = get_ignx_color(FableProgressBar::SURFACE_COLOR_ONAIR);
                assert_eq!(
                    filled.borrow_props().base_styles.background_color.unwrap(),
                    onair_color
                );
            },
        );
    }

    #[test]
    fn test_progress_bar_updates_with_signal_changes() {
        launch_test(
            |ctx| {
                let progress = create_rw_signal(ctx.scope(), 0.3);
                let variant = create_rw_signal(ctx.scope(), ProgressBarVariant::VOD);
                let width = create_rw_signal(ctx.scope(), Some(TEST_WIDTH)).into();

                provide_context(ctx.scope(), progress);

                compose! {
                    Column() {
                        ProgressBar(progress, variant, width)
                    }
                }
            },
            |scope, mut test_game_loop| {
                // Initial state
                let node_tree = test_game_loop.tick_until_done();

                let filled = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_eq!(filled.borrow_props().layout.size.width, 0.3 * TEST_WIDTH);

                // Update progress signal
                let progress = use_context::<RwSignal<'_, f32>>(scope).unwrap();
                progress.set(0.7);

                // Verify progress bar updated
                let node_tree = test_game_loop.tick_until_done();
                let filled_updated = node_tree.find_by_test_id(PROGRESS_BAR_FILLED_TEST_ID);
                assert_eq!(
                    filled_updated.borrow_props().layout.size.width,
                    0.7 * TEST_WIDTH
                );
            },
        );
    }
}
