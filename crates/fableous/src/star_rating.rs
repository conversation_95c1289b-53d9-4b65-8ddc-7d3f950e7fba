use crate::font_icon::*;
use amzn_fable_tokens::*;
use core::cmp::*;
use ignx_compositron::color::Color;
use ignx_compositron::font::FontSize;
use ignx_compositron::id::Id;
use ignx_compositron::prelude::create_memo;
use ignx_compositron::prelude::safe::*;
use ignx_compositron::{compose, Composer};
use std::rc::Rc;

const NUMBER_OF_STARS: u8 = 5;

#[derive(Clone, Eq, PartialEq, Debug)]
enum StarVariant {
    Filled,
    LeftHalf,
    RightHalf,
    Empty,
}

impl Id for StarVariant {
    type Id = usize;

    fn id(&self) -> &Self::Id {
        match self {
            StarVariant::Filled => &0,
            StarVariant::LeftHalf => &1,
            StarVariant::RightHalf => &2,
            StarVariant::Empty => &3,
        }
    }
}

impl StarVariant {
    pub fn get_icon(&self) -> &str {
        match self {
            StarVariant::Filled => FableIcon::STAR_FILLED,
            StarVariant::LeftHalf => FableIcon::STAR_HALF,
            // TODO: change to STAR_HALF_MIRRORED once supported in the tokens
            StarVariant::RightHalf => FableIcon::STAR_HALF,
            StarVariant::Empty => FableIcon::STAR_EMPTY,
        }
    }
}

fn get_stars(rating: f32, is_rtl: bool) -> Vec<StarVariant> {
    let mut stars: Vec<StarVariant> = Vec::with_capacity(NUMBER_OF_STARS.into());
    // stars are rounded to the nearest 0.5
    let rating = (rating.max(0.0).min(NUMBER_OF_STARS as f32) * 2.0).round() / 2.0;
    let full_stars = rating.trunc() as u8;

    for _ in 0..full_stars {
        stars.push(StarVariant::Filled)
    }

    if rating > full_stars as f32 {
        stars.push(if is_rtl {
            StarVariant::RightHalf
        } else {
            StarVariant::LeftHalf
        })
    }

    for _ in 0..NUMBER_OF_STARS - stars.len() as u8 {
        stars.push(StarVariant::Empty);
    }

    stars
}

#[Composer]
pub fn StarRating<'s>(
    ctx: &AppContext<'s>,
    #[into] rating: MaybeSignal<'s, f32>,
    #[optional = FontSize(24)]
    #[into]
    star_size: MaybeSignal<'s, FontSize>,
    #[optional = 9.0] gap: f32,
    #[optional = get_ignx_color(FableColor::PRIMARY)]
    #[into]
    full_star_color: MaybeSignal<'s, Color>,
    #[optional = get_ignx_color(FableColor::PRIMARY)]
    #[into]
    empty_star_color: MaybeSignal<'s, Color>,
) -> impl Composable<'s> {
    let stars = create_memo(ctx.scope(), move |_| {
        // TODO: add the logic for rtl when it is supported
        get_stars(rating.get(), false)
    });

    let star_builder = move |ctx: &AppContext<'s>, item: &StarVariant, _idx| {
        let color = if *item == StarVariant::Empty {
            empty_star_color
        } else {
            full_star_color
        };

        compose! {
            FontIcon(icon: item.get_icon(), color, size: star_size).into_any()
        }
    };

    compose! {
        RowList(items: stars, item_builder: Rc::new(star_builder)).main_axis_alignment(MainAxisAlignment::SpacedBy(gap))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::get_ignx_color;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::node_properties::*;
    use rstest::*;

    #[rstest]
    #[case(-2.0, false, vec![StarVariant::Empty; 5])]
    #[case(0.0, false, vec![StarVariant::Empty; 5])]
    #[case(0.24, false, vec![StarVariant::Empty; 5])]
    #[case(0.25, false, vec![StarVariant::LeftHalf, StarVariant::Empty, StarVariant::Empty, StarVariant::Empty, StarVariant::Empty])]
    #[case(0.25, true, vec![StarVariant::RightHalf, StarVariant::Empty, StarVariant::Empty, StarVariant::Empty, StarVariant::Empty])]
    #[case(1.0, false, vec![StarVariant::Filled, StarVariant::Empty, StarVariant::Empty, StarVariant::Empty, StarVariant::Empty])]
    #[case(2.5, false, vec![StarVariant::Filled, StarVariant::Filled, StarVariant::LeftHalf, StarVariant::Empty, StarVariant::Empty])]
    #[case(2.5, true, vec![StarVariant::Filled, StarVariant::Filled, StarVariant::RightHalf, StarVariant::Empty, StarVariant::Empty])]
    #[case(2.7, true, vec![StarVariant::Filled, StarVariant::Filled, StarVariant::RightHalf, StarVariant::Empty, StarVariant::Empty])]
    #[case(4.75, false, vec![StarVariant::Filled; 5])]
    #[case(5.0, false, vec![StarVariant::Filled; 5])]
    #[case(5.1, false, vec![StarVariant::Filled; 5])]
    fn returns_the_correct_stars_for_rating(
        #[case] rating: f32,
        #[case] is_rtl: bool,
        #[case] expected_output: Vec<StarVariant>,
    ) {
        let output = get_stars(rating, is_rtl);
        assert_eq!(output, expected_output);
    }

    #[test]
    fn should_render_stars_with_the_correct_gaps() {
        launch_test(
            |ctx| {
                compose! {
                    StarRating(rating: 2.2, gap: 5.0)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let icons = node_tree
                    .find_by_props()
                    .test_id(FONT_ICON_TEST_ID)
                    .find_all();

                assert_eq!(icons.len(), 5);

                let mut previous_icon: Option<&NodeQuery<'_>> = None;

                // assert positions to make sure the gap is there
                for icon in icons.iter() {
                    if let Some(previous_icon) = previous_icon {
                        let prev_right = previous_icon
                            .borrow_props()
                            .layout
                            .bounding_box
                            .unwrap()
                            .right();
                        let current_left = icon.borrow_props().layout.bounding_box.unwrap().left;
                        assert_eq!(prev_right + 5.0, current_left)
                    }
                    previous_icon = Some(icon);
                }
            },
        );
    }

    #[test]
    fn text_elements_should_have_the_correct_props() {
        launch_test(
            |ctx| {
                compose! {
                    StarRating(
                        rating: 2.25, gap: 5.0,
                        full_star_color: get_ignx_color(FableColor::BLACK),
                        empty_star_color: get_ignx_color(FableColor::SURFACE),
                        star_size: FontSize(24)
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let icons = node_tree
                    .find_by_props()
                    .test_id(FONT_ICON_TEST_ID)
                    .find_all();

                assert_eq!(icons.len(), 5);

                for (index, icon) in icons.iter().enumerate() {
                    let props = icon.borrow_props().clone();
                    let expected_text = if index < 2 {
                        FableIcon::STAR_FILLED
                    } else if index == 2 {
                        FableIcon::STAR_HALF
                    } else {
                        FableIcon::STAR_EMPTY
                    };

                    let expected_color = get_ignx_color(if index > 2 {
                        FableColor::SURFACE
                    } else {
                        FableColor::BLACK
                    });

                    let expected_font_size = FontSize(24);

                    assert_eq!(props.text.unwrap(), expected_text);
                    if let NodeTypeProperties::Text(text_props) = props.node_type_props {
                        assert_eq!(text_props.color, expected_color);
                        assert_eq!(text_props.font.size, expected_font_size);
                    } else {
                        panic!("icon label is not a text node");
                    }
                }
            },
        );
    }
}
