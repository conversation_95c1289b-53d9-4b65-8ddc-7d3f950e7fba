use amzn_fable_tokens::FableIcon;
use ignx_compositron::color::Color;
use ignx_compositron::composable::*;
use ignx_compositron::composables::label::*;
use ignx_compositron::context::safe::AppContext;
use ignx_compositron::font::{FontFamilies, FontSize, FontStyle, FontWeight};
use ignx_compositron::reactive::safe::MaybeSignal;
use ignx_compositron::{compose, Composer};

pub const FONT_ICON_TEST_ID: &str = "font-icon-test-id";

#[Composer]
pub fn FontIcon<'s>(
    ctx: &AppContext<'s>,
    #[into] icon: MaybeSignal<'s, String>,
    #[into] color: MaybeSignal<'s, Color>,
    #[into] size: MaybeSignal<'s, FontSize>,
) -> LabelComposable<'s> {
    compose! {
        Label(text: icon)
        .color(color)
        .font_size(size)
        .font_families(FontFamilies::from(vec![FableIcon::FONT_FAMILY_NAME.to_string()]))
        .font_weight(FontWeight::Normal)
        .font_style(FontStyle::Normal)
        .test_id(FONT_ICON_TEST_ID)
    }
}
