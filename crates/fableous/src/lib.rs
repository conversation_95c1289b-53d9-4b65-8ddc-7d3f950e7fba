//! Taken from [quip](https://quip-amazon.com/ud7zAvYmX8p1/PV-Motion-System-Guidelines)
pub mod animations;
pub mod background;
pub mod badges;
pub mod buttons;
pub mod cards;
pub mod collaged_image;
pub mod common_string_ids;
pub mod eoc;
pub mod font_icon;
pub mod gradients;
pub mod inputs;
pub mod modals;
pub mod pagination_dots;
pub mod pointer_control;
pub mod progress_bar;
pub mod sliding_focus_list;
pub mod spinner;
pub mod star_rating;
pub mod tiles;
pub mod toasts;
pub mod typography;
pub mod utils;

/// These should maybe come from a rust bundle API, but they are basically style values so will
/// keep in this crate for the time being.
pub const SCREEN_WIDTH: f32 = 1920.0;
pub const SCREEN_HEIGHT: f32 = 1080.0;
