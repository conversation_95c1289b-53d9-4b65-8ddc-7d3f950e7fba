use ignx_compositron::{compose, Composer};

use amzn_fable_tokens::*;
use ignx_compositron::image::ImageComposable;
use ignx_compositron::spinner::SpinnerAttributes;
use lrc_image::lrc_image::*;
use lrc_image::types::{ImageData, ImageFormat, ImageTag};

use ignx_compositron::composable::*;
use ignx_compositron::context::AppContext;

const SPINNER_TEST_ID: &str = "spinner-test-id";

#[Composer]
pub fn Spinner(ctx: &AppContext) -> ImageComposable {
    let data = ImageData {
        url: FableStatus::SPINNER_URL_ON_DARK800.to_string(),
        width: FableStatus::SPINNER_SIZE800,
        height: FableStatus::SPINNER_SIZE800,
        tags: vec![ImageTag::Format(ImageFormat::PNG)],
    };
    compose! {
        LRCImage(data)
            .spin(SpinnerAttributes::infinite(FableStatus::SPINNER_ROTATION_DURATION800/1000.0))
            .test_id(SPINNER_TEST_ID)
    }
}

#[cfg(test)]
mod tests {
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::node_properties::NodeTypeProperties;
    use ignx_compositron::test_utils::*;

    use super::*;

    #[test]
    pub fn should_render_spinner_image() {
        launch_test(
            |ctx| {
                compose! { Spinner() }
            },
            |_scope, mut game_loop| {
                let node_tree = game_loop.tick_once().node_tree;
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();

                assert_node_exists!(&image_node);

                if let NodeTypeProperties::Image(image_props) =
                    image_node.get_props().node_type_props
                {
                    assert_ne!(image_props.uri, None);
                } else {
                    panic!("Spinner is not an image node");
                }
            },
        )
    }
}
