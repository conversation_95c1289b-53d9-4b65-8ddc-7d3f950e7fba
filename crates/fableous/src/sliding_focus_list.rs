use crate::animations::fable_motion_linear_medium;
use crate::typography::type_ramp::TypeRamp;
use crate::typography::typography::*;
use crate::utils::get_ignx_color;
use amzn_fable_tokens::{FableBorder, FableColor, FableText};
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::id::Id;
use ignx_compositron::list::ItemBuilderFn;
use ignx_compositron::node::NodeID;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::{create_namespace, Namespace};
use ignx_compositron::text::{LocalizedText, TextContent};
use ignx_compositron::{compose, Composer};
use std::hash::Hash;
use std::rc::Rc;

pub const SLIDING_FOCUS_LIST_TEST_ID: &str = "SLIDING_FOCUS_LIST_TEST_ID";

#[derive(Clone, PartialEq)]
#[derive_test_only(Debug)]
pub struct SlidingFocusItem<T: Hash + Clone + PartialEq> {
    pub id: T,
    pub sliding_background_color: Color,
    pub content: TextContent,
    pub unfocused_text_color: Color,
    pub focused_text_color: Color,
    pub test_id: String,
}

/// Creates a sliding focus item with an ID and content
/// It assumes a default white sliding background color and that `unfocused_text_color` is white
/// and `focused_text_color` is black
pub fn default_sliding_focus_item<T: Hash + Clone + PartialEq>(
    id: T,
    content: TextContent,
    test_id: String,
) -> SlidingFocusItem<T> {
    SlidingFocusItem {
        id,
        sliding_background_color: Color::white(),
        content,
        unfocused_text_color: Color::white(),
        focused_text_color: Color::black(),
        test_id,
    }
}

impl<T: Hash + Clone + PartialEq> Id for SlidingFocusItem<T> {
    type Id = T;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

#[Composer]
fn SlidingFocusRectangle(
    ctx: &AppContext,
    matched_geometry_namespace: Namespace,
    color: ReadSignal<Color>,
) -> RectangleComposable {
    compose! {
        Rectangle()
            .background_color(color)
            .border_radius(FableBorder::RADIUS063)
            .matched_geometry(matched_geometry_namespace, false, true)
    }
}

#[Composer]
pub fn SlidingFocusList<T: Hash + Clone + PartialEq + 'static>(
    ctx: &AppContext,
    #[into] items: MaybeSignal<Vec<SlidingFocusItem<T>>>,
    on_item_select: Rc<dyn Fn(&SlidingFocusItem<T>)>,
    #[optional = None] default_focus_item: Option<T>,
) -> StackComposable {
    let sliding_focus_background_color = create_rw_signal(ctx.scope(), Color::white());

    // Suffixed with 'fable' to not conflict with top_nav
    let sliding_focus_namespace = create_namespace(ctx.scope(), "sliding_focus_fable");

    let has_focus_moved = create_rw_signal(ctx.scope(), false);

    let focused_background_node_id = create_rw_signal(ctx.scope(), NodeID(0));
    let focused_opacity = create_rw_signal(ctx.scope(), 0.0);
    let focused_item_matched_geometry: RwSignal<Option<T>> =
        create_rw_signal(ctx.scope(), default_focus_item);
    let focused = create_focus_signal(ctx.scope());

    create_effect(ctx.scope(), {
        move |_| {
            let focused = focused.get();
            match focused {
                true => {
                    sliding_focus_background_color.set(Color::white());
                    has_focus_moved.set(false);
                    focused_opacity.set(1.0)
                }
                false => {
                    sliding_focus_background_color.set(get_ignx_color(FableColor::PRIMARY020));
                    focused_opacity.set(0.0)
                }
            }
        }
    });

    let row_list_builder: ItemBuilderFn<SlidingFocusItem<T>, RowComposable> =
        Rc::new(move |ctx, item, _idx| {
            let on_item_select = on_item_select.clone();

            let focus_wrapper = {
                let item = item.clone();
                let ctx = ctx.clone();
                move || {
                    ctx.with_animation(fable_motion_linear_medium(), {
                        let item = item.clone();
                        move || {
                            if focused_item_matched_geometry.get() != Some(item.id.clone()) {
                                has_focus_moved.set(true);
                                focused_item_matched_geometry.set(Some(item.id));
                            }
                        }
                    });
                    on_item_select(&item)
                }
            };

            let focus_signal = create_focus_signal(ctx.scope());
            let unfocused_text_color = item.unfocused_text_color;
            let focused_text_color = item.focused_text_color;

            let id = item.id.clone();
            let is_focused = Signal::derive(ctx.scope(), move || {
                let current_item = focused_item_matched_geometry.get();
                current_item == Some(id.clone())
            });

            let type_ramp_unfocused: TypeRamp = FableText::TYPE_LABEL600.into();

            // FIXME: This won't be needed once the mask problem is fixed and can always be set to the unfocused color
            let color = Signal::derive(ctx.scope(), move || match has_focus_moved.get() {
                true => unfocused_text_color,
                false => {
                    let is_focused = focused.get() && is_focused.get();
                    match is_focused {
                        true => focused_text_color,
                        false => unfocused_text_color,
                    }
                }
            });

            compose! {
                // Unfocused layer
                Row() {
                    Typography(type_ramp: type_ramp_unfocused, color, content: item.content.clone())
                }
                .on_focus(focus_wrapper)
                .preferred_focus(is_focused)
                .matched_geometry(sliding_focus_namespace, true, is_focused)
                .padding(Padding::new(15.0, 15.0, 10.0, 10.0))
                .test_id(item.test_id.clone())
                .focused(focus_signal)
                .focusable()
                .accessibility_description(item.content.clone())
                .accessibility_role(LocalizedText::new("AIV_BLAST_TTS_ROLE_BUTTON"))
            }
        });

    let focus_item_builder: ItemBuilderFn<SlidingFocusItem<T>, RowComposable> = Rc::new(
        move |ctx, item, _idx| {
            let focused_text_color = item.focused_text_color;

            let type_ramp_focus: TypeRamp = FableText::TYPE_LABEL600.into();
            compose! {
                Row() {
                    Typography(type_ramp: type_ramp_focus, color: focused_text_color, content: item.content.clone())
                }
                .padding(Padding::new(15.0, 15.0, 10.0, 10.0))
            }
        },
    );

    compose! {
        Stack() {
            SlidingFocusRectangle(
                color: sliding_focus_background_color.read_only(),
                matched_geometry_namespace: sliding_focus_namespace,
            ).get_node_id(focused_background_node_id)

            RowList(item_builder: row_list_builder, items: items.clone())
                .focused(focused)
                .main_axis_alignment(MainAxisAlignment::SpacedBy(16.0))

            //FIXME: This is a hack as currently mask doesn't work correctly when the element is moving with an animation
            //       We use this show composable to make the mask only execute when the focus moves so that the mask is correct when initially focusing the list
            if has_focus_moved.get() {
                RowList(item_builder: focus_item_builder.clone(), items: items.clone())
                    .focused(focused)
                    .main_axis_alignment(MainAxisAlignment::SpacedBy(16.0))
                    .opacity(focused_opacity)
                    .mask(focused_background_node_id)
            }
        }.test_id(SLIDING_FOCUS_LIST_TEST_ID)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::input::KeyCode;

    #[test]
    fn should_call_focus_callback_when_item_is_focused() {
        ignx_compositron::app::launch_test(
            |ctx| {
                let items = vec![
                    default_sliding_focus_item(
                        "test-1".to_string(),
                        TextContent::String("Test1".to_string()),
                        "test-1".to_string(),
                    ),
                    default_sliding_focus_item(
                        "test-2".to_string(),
                        TextContent::String("Test1".to_string()),
                        "test-2".to_string(),
                    ),
                    default_sliding_focus_item(
                        "test-3".to_string(),
                        TextContent::String("Test1".to_string()),
                        "test-3".to_string(),
                    ),
                ];

                let last_selected: RwSignal<Option<String>> = create_rw_signal(ctx.scope(), None);
                let on_item_select = Rc::new(move |item: &SlidingFocusItem<String>| {
                    last_selected.set(Some(item.id.clone()))
                });

                provide_context(ctx.scope(), last_selected);
                compose! {
                    SlidingFocusList(items, on_item_select)
                }
            },
            |scope, mut test_game_loop| {
                let last_selected = use_context::<RwSignal<Option<String>>>(scope)
                    .expect("Expected last selected signal to exist");
                let node_tree = test_game_loop.tick_until_done();

                let sliding_focus = node_tree.find_by_test_id(SLIDING_FOCUS_LIST_TEST_ID);
                test_game_loop.send_on_focus_event(sliding_focus.borrow_props().node_id);
                test_game_loop.tick_until_done();

                assert_eq!(last_selected.get_untracked(), Some("test-1".to_string()));

                test_game_loop.send_key_down_up_event_to_node(
                    sliding_focus.borrow_props().node_id,
                    KeyCode::Right,
                );
                test_game_loop.tick_until_done();

                assert_eq!(last_selected.get_untracked(), Some("test-2".to_string()));

                test_game_loop.send_key_down_up_event_to_node(
                    sliding_focus.borrow_props().node_id,
                    KeyCode::Left,
                );
                test_game_loop.tick_until_done();

                assert_eq!(last_selected.get_untracked(), Some("test-1".to_string()));
            },
        )
    }
}
