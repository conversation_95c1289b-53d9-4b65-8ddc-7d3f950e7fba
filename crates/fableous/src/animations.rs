#![allow(dead_code)]
/// Taken from [quip](https://quip-amazon.com/ud7zAvYmX8p1/PV-Motion-System-Guidelines)
use ignx_compositron::animation::{Animation, Interpolation};
use ignx_compositron::time::Duration;

/// Types that represent animation or motion duration should convert to [`Duration`]
pub trait MotionDuration {
    fn to_duration(&self) -> Duration;
}

/// Types that represent animation or motion easing should convert to [`Interpolation`]
pub trait MotionEasing {
    fn to_interpolation(&self) -> Interpolation;
}

enum FableMotionDurationFoundation {
    Duration000,
    Duration025,
    Duration050,
    Duration100,
    Duration150,
    Duration200,
    Duration400,
    Duration600,
    Duration800,
}

impl MotionDuration for FableMotionDurationFoundation {
    fn to_duration(&self) -> Duration {
        match self {
            FableMotionDurationFoundation::Duration000 => Duration::ZERO,
            FableMotionDurationFoundation::Duration025 => Duration::from_millis(100),
            FableMotionDurationFoundation::Duration050 => Duration::from_millis(200),
            FableMotionDurationFoundation::Duration100 => Duration::from_millis(400),
            FableMotionDurationFoundation::Duration150 => Duration::from_millis(600),
            FableMotionDurationFoundation::Duration200 => Duration::from_millis(800),
            FableMotionDurationFoundation::Duration400 => Duration::from_millis(2000),
            FableMotionDurationFoundation::Duration600 => Duration::from_millis(3000),
            FableMotionDurationFoundation::Duration800 => Duration::from_millis(4000),
        }
    }
}

/// Enum representing various durations used for fable motion
#[allow(missing_docs)]
pub enum FableMotionDuration {
    Instant,
    Fast,
    Medium,
    Standard,
    Slow,
    Gradual,
}

impl MotionDuration for FableMotionDuration {
    fn to_duration(&self) -> Duration {
        match self {
            FableMotionDuration::Instant => {
                FableMotionDurationFoundation::Duration000.to_duration()
            }
            FableMotionDuration::Fast => FableMotionDurationFoundation::Duration025.to_duration(),
            FableMotionDuration::Medium => FableMotionDurationFoundation::Duration050.to_duration(),
            FableMotionDuration::Standard => {
                FableMotionDurationFoundation::Duration100.to_duration()
            }
            FableMotionDuration::Slow => FableMotionDurationFoundation::Duration200.to_duration(),
            FableMotionDuration::Gradual => {
                FableMotionDurationFoundation::Duration150.to_duration()
            }
        }
    }
}
impl From<FableMotionDuration> for Duration {
    fn from(value: FableMotionDuration) -> Self {
        value.to_duration()
    }
}

/// Enum representing various easing functions used for fable motion
#[allow(missing_docs)]
pub enum FableMotionEasing {
    Linear,
    Exit,
    Enter,
    Persistent,
}

impl MotionEasing for FableMotionEasing {
    // TODO: define correct interpolations to match Fable easing token specs https://issues.amazon.com/issues/LR-9034
    fn to_interpolation(&self) -> Interpolation {
        match self {
            FableMotionEasing::Linear => Interpolation::Linear,
            FableMotionEasing::Exit => Interpolation::EaseInCirc,
            FableMotionEasing::Enter => Interpolation::EaseOutExpo,
            FableMotionEasing::Persistent => Interpolation::EaseOutCubic,
        }
    }
}
impl From<FableMotionEasing> for Interpolation {
    fn from(value: FableMotionEasing) -> Self {
        value.to_interpolation()
    }
}

/// The fable motion animation for Linear easing and Fast duration
pub fn fable_motion_linear_fast() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Linear.to_interpolation())
        .with_duration(FableMotionDuration::Fast.to_duration())
}

/// The fable motion animation for Linear easing and Medium duration
pub fn fable_motion_linear_medium() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Linear.to_interpolation())
        .with_duration(FableMotionDuration::Medium.to_duration())
}

/// The fable motion animation for Linear easing and Standard duration
pub fn fable_motion_linear_standard() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Linear.to_interpolation())
        .with_duration(FableMotionDuration::Standard.to_duration())
}

/// The fable motion animation for Persistent easing and Standard duration
pub fn fable_motion_persistent_standard() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Persistent.to_interpolation())
        .with_duration(FableMotionDuration::Standard.to_duration())
}

/// The fable motion animation for Persistent easing and Medium duration.
pub fn fable_motion_persistent_medium() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Persistent.to_interpolation())
        .with_duration(FableMotionDuration::Medium.to_duration())
}

/// The fable motion animation for Persistent easing and Fast duration.
pub fn fable_motion_persistent_fast() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Persistent.to_interpolation())
        .with_duration(FableMotionDuration::Fast.to_duration())
}

/// The fable motion animation for Enter easing and Medium duration.
pub fn fable_motion_enter_medium() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Enter.to_interpolation())
        .with_duration(FableMotionDuration::Medium.to_duration())
}

/// The fable motion animation for Exit easing and Medium duration.
pub fn fable_motion_exit_medium() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Exit.to_interpolation())
        .with_duration(FableMotionDuration::Medium.to_duration())
}

/// The fable motion animation for Enter easing and Standard duration
pub fn fable_motion_enter_standard() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Enter.to_interpolation())
        .with_duration(FableMotionDuration::Standard.to_duration())
}

/// The fable motion animation for Enter easing and Standard duration
pub fn fable_motion_exit_standard() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Exit.to_interpolation())
        .with_duration(FableMotionDuration::Standard.to_duration())
}

/// The fable motion animation for Enter easing and Fast duration
pub fn fable_motion_enter_fast() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Enter.to_interpolation())
        .with_duration(FableMotionDuration::Fast.to_duration())
}

pub fn fable_motion_instant() -> Animation {
    Animation::default().with_duration(FableMotionDuration::Instant.into())
}

// Specs are from: https://www.figma.com/file/Al1xZQBwpYewKwvzBn4uVn/Fable-Motion-System-Guidelines?type=design&node-id=3394-21134&mode=design&t=hRUaRvAKAzMWHZnm-0

pub fn fable_motion_move_vertical() -> Animation {
    fable_motion_persistent_standard()
}

pub fn fable_motion_move_horizontal() -> Animation {
    fable_motion_persistent_standard()
}

pub fn fable_motion_enter_ambience() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Linear.into())
        .with_duration(FableMotionDuration::Gradual.into())
}

pub fn fable_motion_exit_ambience() -> Animation {
    Animation::default()
        .with_interpolation(FableMotionEasing::Linear.into())
        .with_duration(FableMotionDuration::Slow.into())
        .with_delay(FableMotionDuration::Standard.into())
}
