[package]
name = "fableous"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
lrc-image.workspace = true
serde_json.workspace = true
common-transform-types.workspace = true
cacheable-derive.workspace = true
cfg-test-attr-derive.workspace = true
strum.workspace = true
rust-features.workspace = true
liveliness-types.workspace = true
signal-wrappers.workspace = true
log = "0.4.27"

[lints]
workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
insta.workspace = true
network-parser.workspace = true
rstest.workspace = true
serial_test.workspace = true


[[example]]
name = "button_list_example"
crate-type = ["cdylib"]
[[example]]
name = "primary_button_example"
crate-type = ["cdylib"]
[[example]]
name = "secondary_button_example"
crate-type = ["cdylib"]
[[example]]
name = "tertiary_button_example"
crate-type = ["cdylib"]
[[example]]
name = "toggle_button_example"
crate-type = ["cdylib"]
[[example]]
name = "pill_button_example"
crate-type = ["cdylib"]
[[example]]
name = "play_button_example"
crate-type = ["cdylib"]
[[example]]
name = "icon_button_example"
crate-type = ["cdylib"]
[[example]]
name = "star_rating_example"
crate-type = ["cdylib"]
[[example]]
name = "checkbox_button_example"
crate-type = ["cdylib"]

[[example]]
name = "badges_example"
crate-type = ["cdylib"]
path = "examples/badges_example.rs"
[[example]]
name = "progress_bar_example"
crate-type = ["cdylib"]
path = "examples/progress_bar_example.rs"
[[example]]
name = "typography_example"
crate-type = ["cdylib"]
path = "examples/typography_example.rs"
[[example]]
name = "gradients_example"
crate-type = ["cdylib"]
path = "examples/gradients_example.rs"
[[example]]
name = "cards_tiles_example"
crate-type = ["cdylib"]
path = "examples/cards_tiles_example.rs"
[[example]]
name = "pagination_dots_example"
crate-type = ["cdylib"]
path = "examples/pagination_dots_example.rs"
[[example]]
name = "modal_example"
crate-type = ["cdylib"]
path = "examples/modal_example.rs"
[[example]]
name = "toast_example"
crate-type = ["cdylib"]
path = "examples/toast_example.rs"
[[example]]
name = "spinner_example"
crate-type = ["cdylib"]
path = "examples/spinner_example.rs"
[[example]]
name = "radio_buttons_example"
crate-type = ["cdylib"]
path = "examples/radio_buttons_example.rs"

[features]
default = []
debug_impl = []
test_utils = []
