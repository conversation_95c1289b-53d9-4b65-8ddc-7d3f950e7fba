use cfg_test_attr_derive::derive_test_only;
use strum::*;

#[derive(<PERSON><PERSON>, PartialEq, Eq, EnumString, AsRefStr)]
#[derive_test_only(Debug)]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum CacheInvalidationEvent {
    DroppedCard,
    WatchlistToggled,
    TransitionedToPlayback,
    AcquisitionStart,
    TitleReaction,
    Inactivity,
    AuthChange,
    LocaleChange,
    ProfileChange,
    SyncContent,
    /// In the Rust client, we should never use this variant. This is a legacy reason from the JS client, which
    /// we should migrate away from.
    ///
    /// A defined invalidation reason should always be preferred so that we only evict the caches when absolutely
    /// necessary, as this will drastically increase the loading performance for the customer.
    /// Correctness is more important than performance, so we should also evict the cache in those undefined reasons,
    /// but make an effort to migrate away from those undefined reasons to gain performance.
    /// See <https://sim.amazon.com/issues/LRC-SaBr-4722/>
    LegacyUndefinedReasonFromJS,
}

#[cfg(test)]
pub mod test {
    use super::*;
    use rstest::*;
    use std::str::FromStr;
    use strum::ParseError::VariantNotFound;

    // The serialization strings are automatically generated with Strum, therefore don't remove these
    // tests. They ensure that we don't accidentally rename the variants and inadvertently the generated strings.
    // Cross-check against: https://code.amazon.com/packages/AVLivingRoomClient/blobs/mainline/--/packages/avlrc-collection-ui-events/types/CacheInvalidationEvent.ts#L1-L9

    #[rstest]
    #[case("DROPPED_CARD", Ok(CacheInvalidationEvent::DroppedCard))]
    #[case("WATCHLIST_TOGGLED", Ok(CacheInvalidationEvent::WatchlistToggled))]
    #[case(
        "TRANSITIONED_TO_PLAYBACK",
        Ok(CacheInvalidationEvent::TransitionedToPlayback)
    )]
    #[case("ACQUISITION_START", Ok(CacheInvalidationEvent::AcquisitionStart))]
    #[case("TITLE_REACTION", Ok(CacheInvalidationEvent::TitleReaction))]
    #[case("INACTIVITY", Ok(CacheInvalidationEvent::Inactivity))]
    #[case("AUTH_CHANGE", Ok(CacheInvalidationEvent::AuthChange))]
    #[case("LOCALE_CHANGE", Ok(CacheInvalidationEvent::LocaleChange))]
    #[case(
        "LEGACY_UNDEFINED_REASON_FROM_JS",
        Ok(CacheInvalidationEvent::LegacyUndefinedReasonFromJS)
    )]
    #[case("INVALID_EVENT", Err(VariantNotFound))]
    fn test_from_str(
        #[case] input: &str,
        #[case] expected: Result<CacheInvalidationEvent, ParseError>,
    ) {
        assert_eq!(CacheInvalidationEvent::from_str(input), expected);
    }

    #[rstest]
    #[case(CacheInvalidationEvent::DroppedCard, "DROPPED_CARD")]
    #[case(CacheInvalidationEvent::WatchlistToggled, "WATCHLIST_TOGGLED")]
    #[case(
        CacheInvalidationEvent::TransitionedToPlayback,
        "TRANSITIONED_TO_PLAYBACK"
    )]
    #[case(CacheInvalidationEvent::AcquisitionStart, "ACQUISITION_START")]
    #[case(CacheInvalidationEvent::TitleReaction, "TITLE_REACTION")]
    #[case(CacheInvalidationEvent::Inactivity, "INACTIVITY")]
    #[case(CacheInvalidationEvent::AuthChange, "AUTH_CHANGE")]
    #[case(CacheInvalidationEvent::LocaleChange, "LOCALE_CHANGE")]
    #[case(
        CacheInvalidationEvent::LegacyUndefinedReasonFromJS,
        "LEGACY_UNDEFINED_REASON_FROM_JS"
    )]
    #[case(CacheInvalidationEvent::ProfileChange, "PROFILE_CHANGE")]
    #[case(CacheInvalidationEvent::SyncContent, "SYNC_CONTENT")]
    fn test_as_ref(#[case] input: CacheInvalidationEvent, #[case] expected: &str) {
        assert_eq!(input.as_ref(), expected);
    }
}
