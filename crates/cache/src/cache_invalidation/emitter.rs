use std::str::FromStr;
use std::{cell::RefCell, collections::HashMap, rc::Rc};

use cfg_test_attr_derive::derive_test_only;
#[double]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::reactive::{on_cleanup, Scope};
use mockall_double::double;
use serde::{Deserialize, Serialize};

use super::events::CacheInvalidationEvent;

type Listener = Box<dyn Fn(CacheInvalidationEvent) + 'static>;

pub struct CacheInvalidationEventEmitter {
    listeners: Rc<RefCell<HashMap<usize, Listener>>>,
    rpc_manager: Option<RPCManager>,
    next_id: usize,
}

pub type CacheInvalidationEventEmitterRc = Rc<RefCell<CacheInvalidationEventEmitter>>;

#[derive(Serialize, Deserialize)]
#[derive_test_only(Clone)]
struct CacheInvalidationRPCArgs {
    event: String,
}

#[derive(Serialize, Deserialize)]
pub struct CacheInvalidationRPCResponse {}

const CACHE_INVALIDATION_FN: &str = "cacheInvalidation";

impl CacheInvalidationEventEmitter {
    fn new() -> Self {
        Self {
            listeners: Rc::new(RefCell::new(HashMap::new())),
            rpc_manager: None,
            next_id: 0,
        }
    }

    fn with_rpc(mut self, rpc_manager: RPCManager) -> Self {
        self.rpc_manager = Some(rpc_manager);
        self.listen_for_rpc_events();

        self
    }

    pub fn new_rc_with_rpc(rpc_manager: RPCManager) -> CacheInvalidationEventEmitterRc {
        Rc::new(RefCell::new(Self::new().with_rpc(rpc_manager)))
    }

    #[cfg(feature = "test-utils")]
    pub fn new_rc_without_rpc() -> CacheInvalidationEventEmitterRc {
        Rc::new(RefCell::new(Self::new()))
    }

    fn listen_for_rpc_events(&self) {
        let Some(ref rpc_manager) = self.rpc_manager else {
            return;
        };
        rpc_manager.register_cross_app_function(CACHE_INVALIDATION_FN.into(), {
            let listeners = self.listeners.clone();
            move |args: CacheInvalidationRPCArgs| {
                let event = CacheInvalidationEvent::from_str(&args.event);

                if let Ok(event) = event {
                    for listener in listeners.borrow().values() {
                        listener(event.clone());
                    }
                } else {
                    log::error!(
                        "Unknown CacheInvalidationEvent received via RPC: {:?}",
                        &args.event
                    );
                }

                Ok(true)
            }
        });
    }

    pub fn emit(&self, event: CacheInvalidationEvent) {
        for listener in self.listeners.borrow().values() {
            listener(event.clone());
        }
    }

    pub fn subscribe(&mut self, scope: Scope, cb: Listener) {
        let id = self.next_id;

        self.next_id += 1;
        self.listeners.borrow_mut().insert(id, cb);

        let listeners = self.listeners.clone();
        on_cleanup(scope, move || {
            listeners.borrow_mut().remove(&id);
        });
    }
}

#[cfg(test)]
mod tests {
    use std::error::Error;

    use super::*;
    use ignx_compositron::{
        app::{
            launch_only_scope,
            rpc::{MockRPCCall, MockRPCManager, RPCError},
        },
        prelude::*,
    };

    #[test]
    fn test_new_rc() {
        let mut rpc_manager = MockRPCManager::new();
        expect_rpc_register(&mut rpc_manager, None);
        let emitter_rc = CacheInvalidationEventEmitter::new_rc_with_rpc(rpc_manager);
        assert!(emitter_rc.borrow().listeners.borrow().is_empty());
        assert_eq!(emitter_rc.borrow().next_id, 0);
    }

    fn expect_rpc_call(rpc_manager: &mut RPCManager, event: &str, should_fail: bool) {
        let event = event.to_owned();

        rpc_manager
            .expect_call()
            .times(1)
            .withf(|f| f.eq(CACHE_INVALIDATION_FN))
            .return_once(move |_| {
                let mut call = MockRPCCall::<CacheInvalidationRPCResponse>::new();

                call.expect_arg()
                    .withf(move |key, val| key.eq("event") && val.eq(&event))
                    .return_once(move |_, _| {
                        let mut call = MockRPCCall::<CacheInvalidationRPCResponse>::new();
                        call.expect_error_callback()
                            .return_once(move |error_callback| {
                                let mut call = MockRPCCall::<CacheInvalidationRPCResponse>::new();
                                call.expect_send().return_once(|| ());

                                if should_fail {
                                    error_callback(RPCError::TimeoutExceeded);
                                }

                                call
                            });
                        call
                    });
                call
            });
    }

    fn expect_rpc_register(rpc_manager: &mut RPCManager, args: Option<CacheInvalidationRPCArgs>) {
        rpc_manager
            .expect_register_cross_app_function()
            .times(1)
            .withf(
                move |name: &String,
                      _callback: &Box<
                    dyn Fn(CacheInvalidationRPCArgs) -> Result<bool, Box<dyn Error>>,
                >| { name.eq(CACHE_INVALIDATION_FN) },
            )
            .return_once(
                move |_,
                      callback: Box<
                    dyn Fn(CacheInvalidationRPCArgs) -> Result<bool, Box<dyn Error>>,
                >| {
                    if let Some(args) = args {
                        callback(args);
                    }
                },
            );
    }

    #[test]
    fn test_subscribe_and_emit() {
        launch_only_scope(|scope| {
            testing_logger::setup();

            let event_count = create_rw_signal(scope, 0);
            let last_event: RwSignal<Option<CacheInvalidationEvent>> =
                create_rw_signal(scope, None);
            let mut rpc_manager = MockRPCManager::new();
            expect_rpc_register(&mut rpc_manager, None);

            let emitter = CacheInvalidationEventEmitter::new_rc_with_rpc(rpc_manager);

            let listener = Box::new(move |event| {
                event_count.set(event_count.get() + 1);
                last_event.set(Some(event));
            });

            emitter.borrow_mut().subscribe(scope, listener);

            emitter.borrow().emit(CacheInvalidationEvent::DroppedCard);
            assert_eq!(event_count.get(), 1);
            assert_eq!(last_event.get(), Some(CacheInvalidationEvent::DroppedCard));

            emitter.borrow().emit(CacheInvalidationEvent::Inactivity);
            assert_eq!(event_count.get(), 2);
            assert_eq!(last_event.get(), Some(CacheInvalidationEvent::Inactivity));

            testing_logger::validate(|logs| {
                let errors = logs
                    .iter()
                    .filter(|l| l.level == log::Level::Error)
                    .collect::<Vec<&testing_logger::CapturedLog>>();

                assert_eq!(errors.len(), 0);
            });
        });
    }

    #[test]
    fn test_multiple_listeners() {
        launch_only_scope(|scope| {
            let event_count = create_rw_signal(scope, 0);
            let last_event: RwSignal<Option<CacheInvalidationEvent>> =
                create_rw_signal(scope, None);
            let mut rpc_manager = MockRPCManager::new();
            expect_rpc_register(&mut rpc_manager, None);

            let emitter = CacheInvalidationEventEmitter::new_rc_with_rpc(rpc_manager);

            // listener 1 updates the event count.
            emitter.borrow_mut().subscribe(
                scope,
                Box::new(move |_| {
                    event_count.set(event_count.get() + 1);
                }),
            );

            // listener 2 updates the last event.
            emitter.borrow_mut().subscribe(
                scope,
                Box::new(move |event| {
                    last_event.set(Some(event));
                }),
            );

            emitter.borrow().emit(CacheInvalidationEvent::DroppedCard);
            assert_eq!(event_count.get(), 1);
            assert_eq!(last_event.get(), Some(CacheInvalidationEvent::DroppedCard));

            emitter.borrow().emit(CacheInvalidationEvent::Inactivity);
            assert_eq!(event_count.get(), 2);
            assert_eq!(last_event.get(), Some(CacheInvalidationEvent::Inactivity));
        });
    }

    #[test]
    fn test_unsubscribes_un_unmount() {
        launch_only_scope(|scope| {
            scope.child_scope(move |child_scope| {
                let event_count = create_rw_signal(scope, 0);
                let last_event: RwSignal<Option<CacheInvalidationEvent>> =
                    create_rw_signal(scope, None);
                let mut rpc_manager = MockRPCManager::new();
                expect_rpc_register(&mut rpc_manager, None);

                let emitter = CacheInvalidationEventEmitter::new_rc_with_rpc(rpc_manager);

                let listener = Box::new(move |event| {
                    event_count.set(event_count.get() + 1);
                    last_event.set(Some(event));
                });

                emitter.borrow_mut().subscribe(child_scope, listener);

                emitter.borrow().emit(CacheInvalidationEvent::DroppedCard);
                assert_eq!(event_count.get(), 1);
                assert_eq!(last_event.get(), Some(CacheInvalidationEvent::DroppedCard));

                child_scope.dispose();

                // doesn't update.
                emitter.borrow().emit(CacheInvalidationEvent::Inactivity);
                assert_eq!(event_count.get(), 1);
                assert_eq!(last_event.get(), Some(CacheInvalidationEvent::DroppedCard));
            });
        });
    }

    #[test]
    pub fn test_receive_rpc_events() {
        launch_only_scope(|scope| {
            let event_count = create_rw_signal(scope, 0);
            let last_event: RwSignal<Option<CacheInvalidationEvent>> =
                create_rw_signal(scope, None);
            let mut rpc_manager = MockRPCManager::new();
            expect_rpc_register(&mut rpc_manager, None);

            // expects the registration and fires the DROPPED_CARD message once registration is done.
            expect_rpc_register(
                &mut rpc_manager,
                Some(CacheInvalidationRPCArgs {
                    event: "DROPPED_CARD".to_string(),
                }),
            );

            let emitter = CacheInvalidationEventEmitter::new_rc_with_rpc(rpc_manager);

            let listener = Box::new(move |event| {
                event_count.set(event_count.get() + 1);
                last_event.set(Some(event));
            });

            emitter.borrow_mut().subscribe(scope, listener);

            assert_eq!(event_count.get(), 0);
            assert_eq!(last_event.get(), None);

            // fire the message.
            emitter.borrow_mut().listen_for_rpc_events();

            assert_eq!(event_count.get(), 1);
            assert_eq!(last_event.get(), Some(CacheInvalidationEvent::DroppedCard));
        });
    }
}
