[package]
name = "collection-types"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[features]
default = []
debug_impl = []
test_utils = []

[dependencies]
common-transform-types.workspace = true
location.workspace = true
in-app-survey.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
cfg-test-attr-derive.workspace = true
derive_more.workspace = true

[lints]
workspace = true

[dev-dependencies]
rstest.workspace = true
