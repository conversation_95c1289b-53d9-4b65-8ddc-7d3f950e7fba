#![allow(
    nonstandard_style,
    missing_docs,
    clippy::upper_case_acronyms,
    reason = "compatibility"
)]

use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::Action;
use common_transform_types::containers::{
    ContainerWithResiliency, PaginatableComponent, PaginationLink,
};
use common_transform_types::modals::MarketingModalData;
use common_transform_types::page_metadata::PageMetadata;
use common_transform_types::resiliency::WithResiliency;
use in_app_survey::types::SurveyData;
use location::{PageType, RustPage};
use network_parser::core::{NetworkBool, NetworkString, NetworkVec};
use network_parser::prelude::*;
use network_parser::wrappers::parse_within_object_allow_parsing_nulls;
use network_parser_derive::*;

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct LogoImage {
    pub url: Option<String>,
    pub aspectRatio: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct SubNavItem {
    pub id: String,
    pub text: String,
    pub action: Action,
    pub isSelected: bool,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct OnboardingButton {
    pub title: Option<String>,
    pub reftag: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct OnboardingModalData {
    pub title: Option<String>,
    pub subtitle: Option<String>,
    pub landingButton: Option<OnboardingButton>,
    pub dismissButton: Option<OnboardingButton>,
    pub scrollDepth: Option<u32>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum VoiceHintType {
    SEARCH,
    DISCOVERY,
    PLAY_SOMETHING,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum RemoteIconType {
    MICROPHONE,
    ALEXA,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct RemoteIcon {
    #[network(rename = "type")]
    pub remoteIconType: RemoteIconType,
    pub replaceText: String,
    pub description: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct VoiceHintAnalytics {
    pub refMarker: Option<String>,
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct VoiceHint {
    #[network(rename = "type")]
    pub voiceHintType: VoiceHintType,
    pub body: Option<String>,
    pub remoteIcon: Option<RemoteIcon>,
    pub analytics: Option<VoiceHintAnalytics>,
}

pub type CollectionsPagePaginationLink = WithResiliency<NetworkOptional<PaginationLink>>;
pub type CollectionsPageSubNav = WithResiliency<NetworkVec<SubNavItem>>;
pub type CollectionsPageTitle = WithResiliency<NetworkOptional<NetworkString>>;
pub type CollectionsPageMetadata = WithResiliency<NetworkOptional<PageMetadata>>;
pub type CollectionsPageMarketingModalData = WithResiliency<NetworkOptional<MarketingModalData>>;
type ResiliencyOptionalBool = WithResiliency<NetworkOptional<NetworkBool>>;
pub type CollectionsPageMatchFound = ResiliencyOptionalBool;
pub type CollectionsPageOnboardingModalData = WithResiliency<NetworkOptional<OnboardingModalData>>;
pub type CollectionsPageIsPriorityCenter = ResiliencyOptionalBool;
pub type CollectionsPageVoiceHints = WithResiliency<NetworkOptional<NetworkVec<VoiceHint>>>;
pub type CollectionsPageId = WithResiliency<NetworkOptional<NetworkString>>;

#[derive(NetworkParsed, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct CollectionsPage {
    pub containerList: Vec<ContainerWithResiliency>,
    #[network(allow_parsing_nulls)]
    pub paginationLink: CollectionsPagePaginationLink,
    #[network(allow_parsing_nulls)]
    pub subNav: CollectionsPageSubNav,
    #[network(allow_parsing_nulls)]
    pub pageTitle: CollectionsPageTitle,
    #[network(allow_parsing_nulls)]
    pub pageMetadata: CollectionsPageMetadata,
    #[network(allow_parsing_nulls)]
    pub marketingModalData: CollectionsPageMarketingModalData,
    #[network(allow_parsing_nulls)]
    pub matchFound: CollectionsPageMatchFound,
    #[network(allow_parsing_nulls)]
    pub onboardingModalData: CollectionsPageOnboardingModalData,
    #[network(allow_parsing_nulls)]
    pub isPriorityCenter: CollectionsPageIsPriorityCenter,
    #[network(allow_parsing_nulls)]
    pub voiceHints: CollectionsPageVoiceHints,
    #[network(allow_parsing_nulls)]
    pub page_id: CollectionsPageId,
    #[network(allow_parsing_nulls)]
    pub surveyData: Option<SurveyData>,
}

impl From<&CollectionsPage> for PaginatableComponent {
    fn from(value: &CollectionsPage) -> Self {
        let link: Option<PaginationLink> = value.paginationLink.clone().parse_resiliency(
            PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
            "PaginationLink",
        );
        PaginatableComponent::ContainerList(link, None)
    }
}
