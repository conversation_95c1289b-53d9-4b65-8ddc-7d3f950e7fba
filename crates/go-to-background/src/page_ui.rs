use firetv::use_firetv_context;
#[cfg_attr(test, double)]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::{
    compose,
    lifecycle::{LifecycleState, *},
    log,
    prelude::*,
    Composer,
};
#[cfg(test)]
use mockall_double::double;
use router::hooks::use_router;

const BACK_TRANSITION_SOURCE: &str = "RUST_GO_TO_BACKGROUND";

#[Composer]
pub fn GoToBackground(ctx: &AppContext) -> StackComposable {
    let router = use_router(ctx.scope());
    let firetv_context = use_firetv_context(ctx);

    if !firetv_context.is_firetv() {
        log::error!("[GoToBackground] This page was reached from a non-FireTV device, which is unexpected. Going back the navigation stack.");
        router.back(BACK_TRANSITION_SOURCE);
        return compose! { Stack() {} };
    }

    // We **must** be able to determine the life cycle state otherwise we will stay stuck in this screen.
    let lifecycle_state = expect_context::<LifecycleStateContext>(ctx.scope());

    let page_params = router.get_page_params();
    if let Some(transition_source) = page_params.get("transitionSource") {
        log::info!("[GoToBackground] Transition source: {}", transition_source);
    }

    // Subscribe to lifecycle state changes
    create_effect(ctx.scope(), move |_| {
        let state = lifecycle_state.0.get();
        match state {
            LifecycleState::Background => {
                log::info!(
                    "[GoToBackground] Background mode reached, going back the navigation stack."
                );
                router.back(BACK_TRANSITION_SOURCE);
            }
            LifecycleState::Undefined
            | LifecycleState::Foreground
            | LifecycleState::ForegroundUnfocused
            | LifecycleState::Termination => {
                log::info!("[GoToBackground] LifecycleState = {state:?}");
            }
        }
    });

    // TODO: Fail-safe or retry mechanism, so the customer is not stuck in this screen in case something
    //  goes wrong. https://sim.amazon.com/issues/pyro-422

    // As of now (2025-06), the life cycle manager is still in JS, so we need to send an RPC request and
    // call the "send to background" function from there.
    // TODO: Migrate to Wasm LifecycleManager calls once available
    let rpc_manager = use_context::<RPCManager>(ctx.scope());
    if let Some(rpc_manager) = rpc_manager {
        log::info!("[GoToBackground] Sending requestBackground RPC call");
        rpc_manager.call::<bool>("requestBackground").send();
        log::info!("[GoToBackground] Sent requestBackground RPC call. Expecting to be in background shortly.");
    }

    compose! { Stack() {} }
}

#[cfg(test)]
mod tests {
    use super::*;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::app::rpc::{MockRPCCall, MockRPCManager};
    use ignx_compositron::compose;
    use ignx_compositron::lifecycle::{LifecycleState, LifecycleStateContext};
    use mockall::predicate;
    use router::{MockRouting, RoutingContext};
    use serial_test::serial;
    use std::rc::Rc;

    fn create_default_mock_routing() -> MockRouting {
        let mut mock_routing_context = MockRouting::default();
        mock_routing_context
            .expect_get_page_params()
            .returning(|| serde_json::map::Map::new());
        mock_routing_context
    }

    fn provide_mock_lifecycle_state(scope: Scope) {
        let lifecycle_stage_signal = create_rw_signal(scope, LifecycleState::Foreground);
        provide_context(
            scope,
            LifecycleStateContext(lifecycle_stage_signal.read_only()),
        );
        provide_context(scope, lifecycle_stage_signal);
    }

    fn provide_mock_is_firetv(scope: Scope, enabled: bool) {
        let mut mock = MockFireTV::new();
        mock.expect_is_firetv().return_const(enabled);
        mock.provide_mock(scope);
    }

    /// Create a mock RPCManager that will record any RPC calls that it sends using the given recorder.
    fn create_mock_rpc_manager(expected_calls: usize) -> MockRPCManager {
        let mut rpc_manager = MockRPCManager::new();

        rpc_manager
            .expect_clone()
            .times(..)
            .returning_st(move || -> MockRPCManager {
                let mut rpc_manager_clone = MockRPCManager::new();
                let mock_success_call: MockRPCCall<()> = MockRPCCall::new();
                rpc_manager_clone
                    .expect_call()
                    .times(expected_calls)
                    .with(predicate::eq("requestBackground"))
                    .return_once(move |_| mock_success_call);
                rpc_manager_clone
            });
        rpc_manager
    }

    #[test]
    #[serial]
    pub fn should_be_gated_behind_firetv_guard() {
        launch_test(
            |ctx| {
                let scope = ctx.scope();
                provide_mock_lifecycle_state(scope);

                // Should still go back
                let mut mock_routing = create_default_mock_routing();
                mock_routing
                    .expect_back()
                    .once()
                    .withf(|src| src == "RUST_GO_TO_BACKGROUND")
                    .return_const(());
                provide_context::<RoutingContext>(scope, Rc::new(mock_routing));

                create_mock_rpc_manager(0);

                provide_mock_is_firetv(scope, false);

                compose! {
                    GoToBackground()
                }
            },
            |scope, mut gl| {
                let _ = gl.tick_until_done();
                let lifecycle_state_signal = expect_context::<RwSignal<LifecycleState>>(scope);
                lifecycle_state_signal.set(LifecycleState::Background);
                let _ = gl.tick_until_done();
            },
        );
    }

    #[test]
    #[serial]
    pub fn should_send_gmb_message_and_go_back_when_background_mode_reached() {
        launch_test(
            |ctx| {
                let scope = ctx.scope();
                provide_mock_lifecycle_state(scope);

                let mut mock_routing = create_default_mock_routing();
                mock_routing
                    .expect_back()
                    .once()
                    .withf(|src| src == "RUST_GO_TO_BACKGROUND")
                    .return_const(());
                provide_context::<RoutingContext>(scope, Rc::new(mock_routing));

                // Expecting one call to requestBackground
                create_mock_rpc_manager(1);

                provide_mock_is_firetv(scope, true);

                compose! {
                    GoToBackground()
                }
            },
            |scope, mut gl| {
                let _ = gl.tick_until_done();
                let lifecycle_state_signal = expect_context::<RwSignal<LifecycleState>>(scope);
                lifecycle_state_signal.set(LifecycleState::Background);
                let _ = gl.tick_until_done();
            },
        );
    }
}
