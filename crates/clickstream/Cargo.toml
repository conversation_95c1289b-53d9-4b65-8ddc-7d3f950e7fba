[package]
name = "clickstream"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
cfg-test-attr-derive.workspace = true
cross-app-events.workspace = true
serde.workspace = true
serde_json.workspace = true
mockall.workspace = true
mockall_double.workspace = true
common-transform-types.workspace = true
downcast-rs.workspace = true
strum.workspace = true
strum_macros.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_app", "mock_timer"] }
rstest.workspace = true
insta.workspace = true

[lints]
workspace = true

[features]
debug_impl = []
test_utils = []
