use crate::client::*;
use ignx_compositron::prelude::*;
use std::cell::RefCell;
use std::rc::Rc;

/// A test utility for verifying clickstream events in tests.
///
/// `ClickstreamEmitterSpy` provides an ergonomic way to record and verify clickstream events
/// during testing. It's particularly useful for snapshot-style testing where you want to
/// verify the exact sequence and content of events emitted by your components.
///
/// # Example
/// ```
/// use ignx_compositron::app::launch_only_scope;
/// use clickstream::test_utils::{provide_clickstream_client_spy, use_clickstream_emitter_spy};
///
/// launch_only_scope(|scope| {
///     // Set up the spy in your test context
///     provide_clickstream_client_spy(scope);
///     let spy = use_clickstream_emitter_spy(scope);
///
///     // Your test code that generates clickstream events...
///
///     // Verify the recorded events
///     let events = spy.recorded_events();
///     assert_eq!(events.len(), 0); // No events in this example
/// });
/// ```
///
/// Key features:
/// - Records all clickstream events for later verification
/// - Maintains event order
/// - Shares recording state between clones
/// - Integrates with the component testing context
///
/// # Note
/// Available only when the `test_utils` feature is enabled.
#[derive(Clone)]
pub struct ClickstreamEmitterSpy {
    recorded_events: Rc<RefCell<Vec<CrossAppClickstreamEventPayload>>>,
}
// Add this implementation
impl ClickstreamEmitterSpy {
    pub fn new() -> Self {
        Self {
            recorded_events: Rc::new(RefCell::new(Vec::new())),
        }
    }

    pub fn recorded_events(&self) -> Vec<CrossAppClickstreamEventPayload> {
        self.recorded_events.borrow().clone()
    }
}

impl ClickstreamEmitter for ClickstreamEmitterSpy {
    fn track(&self, payload: CrossAppClickstreamEventPayload) {
        self.recorded_events.borrow_mut().push(payload);
    }
}

/// Provides a recording reporter to the context for tests
pub fn provide_clickstream_client_spy(scope: Scope) {
    let spy = ClickstreamClient(Rc::new(ClickstreamEmitterSpy::new()));
    provide_context::<ClickstreamClient>(scope, spy);
}

pub fn use_clickstream_emitter_spy(scope: Scope) -> Rc<ClickstreamEmitterSpy> {
    #![allow(clippy::expect_used, reason = "Used for testing only")]
    let reporter = use_context::<ClickstreamClient>(scope)
        .expect("ClickstreamClient should be provided in test context");
    if let Ok(reporter) = reporter.0.downcast_rc::<ClickstreamEmitterSpy>() {
        reporter
    } else {
        panic!(
            "ClickstreamEmitter could not be down-casted to ClickstreamEmitterSpy. Make sure to \
            use `provide_recording_clickstream_reporter` to provide a spying ClickstreamClient."
        );
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_only_scope;
    use serde_json::json;

    /// Helper function to create a test event
    fn create_test_event(
        source: &'static str,
        name: &'static str,
    ) -> CrossAppClickstreamEventPayload {
        CrossAppClickstreamEventPayload {
            source,
            name,
            params: serde_json::Map::new(),
        }
    }

    mod spy_behavior {
        use super::*;

        #[test]
        fn records_single_event() {
            launch_only_scope(|scope| {
                provide_clickstream_client_spy(scope);
                let spy = use_clickstream_emitter_spy(scope);

                let event = create_test_event("test_source", "test_name");
                spy.track(event.clone());

                let recorded = spy.recorded_events();
                assert_eq!(recorded.len(), 1);
                assert_eq!(recorded[0], event);
            });
        }

        #[test]
        fn records_multiple_events_in_order() {
            launch_only_scope(|scope| {
                provide_clickstream_client_spy(scope);
                let spy = use_clickstream_emitter_spy(scope);

                let event1 = create_test_event("source1", "name1");
                let event2 = create_test_event("source2", "name2");

                spy.track(event1.clone());
                spy.track(event2.clone());

                let recorded = spy.recorded_events();
                assert_eq!(recorded, vec![event1, event2]);
            });
        }

        #[test]
        fn shares_state_between_clones() {
            launch_only_scope(|scope| {
                provide_clickstream_client_spy(scope);
                let spy1 = use_clickstream_emitter_spy(scope);
                let spy2 = use_clickstream_emitter_spy(scope);

                let event = create_test_event("test_source", "test_name");
                spy1.track(event.clone());

                assert_eq!(spy1.recorded_events(), spy2.recorded_events());
                assert_eq!(spy1.recorded_events()[0], event);
            });
        }
    }

    mod event_params {
        use super::*;

        #[test]
        fn handles_complex_params() {
            launch_only_scope(|scope| {
                provide_clickstream_client_spy(scope);
                let spy = use_clickstream_emitter_spy(scope);

                let mut params = serde_json::Map::new();
                params.insert("number".to_string(), json!(42));
                params.insert("string".to_string(), json!("value"));
                params.insert(
                    "object".to_string(),
                    json!({
                        "nested": "value"
                    }),
                );

                let event = CrossAppClickstreamEventPayload {
                    source: "test_source",
                    name: "test_name",
                    params,
                };

                spy.track(event.clone());
                assert_eq!(spy.recorded_events()[0], event);
            });
        }
    }

    mod context_integration {
        use super::*;
        use std::rc::Rc;

        #[test]
        fn provides_and_retrieves_spy_from_context() {
            launch_only_scope(|scope| {
                provide_clickstream_client_spy(scope);
                let spy = use_clickstream_emitter_spy(scope);

                assert!(Rc::strong_count(&spy) >= 1);
            });
        }
    }
}
