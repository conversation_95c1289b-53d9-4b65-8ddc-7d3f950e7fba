use cfg_test_attr_derive::derive_test_only;
#[cfg(not(test))]
use cross_app_events::app_event::AppEventReporter;
#[cfg(test)]
use cross_app_events::app_event::MockAppEventReporter as AppEventReporter;
use downcast_rs::{impl_downcast, Downcast};
use ignx_compositron::log;
use ignx_compositron::prelude::*;
use serde::Serialize;
use serde_json::Value;
use std::fmt::Debug;
use std::rc::Rc;

/// # Note:
/// `source` and `name` are static strings because `AppEvents` in the React `AppEventHandler` are also
/// dealing with static strings.
#[derive(Clone)]
#[derive_test_only(Serialize, PartialEq, Debug)]
pub struct CrossAppClickstreamEventPayload {
    pub source: &'static str,
    pub name: &'static str,

    /// Note that these are partial params and more might be added in the JS event handler.
    pub params: serde_json::Map<String, Value>,
}

pub trait ClickstreamEmitter: Downcast {
    /// Sends a cross app event to the JS app, which will take care of the clickstream reporting.
    ///
    /// Note the following:
    /// * The app event handlers might populate the Clickstream params with some fields
    ///   derived from the app state.
    /// * The Clickstream might not be immediately sent since they are recorded into a buffer. For
    ///   instance, the "flushing" might happen after a page navigation for performance reasons.
    fn track(&self, payload: CrossAppClickstreamEventPayload);
}

// Required so that it can be down-casted to the mock/spy version
impl_downcast!(ClickstreamEmitter);

impl ClickstreamEmitter for AppEventReporter {
    fn track(&self, event: CrossAppClickstreamEventPayload) {
        self.send_app_event(event.name, event.source, Some(event.params));
    }
}

#[derive(Clone)]
pub struct ClickstreamClient(pub(crate) Rc<dyn ClickstreamEmitter>);

#[derive(Debug)]
pub enum ClickstreamError {
    SerializationError(serde_json::Error),
    NotAnObject,
}

impl From<serde_json::Error> for ClickstreamError {
    fn from(err: serde_json::Error) -> Self {
        ClickstreamError::SerializationError(err)
    }
}

pub trait CrossAppEventKeys {
    fn source(&self) -> &'static str;
    fn name(&self) -> &'static str;
}

// Can not provide a blanket implementation for TryFrom generically, so we are using another trait
pub trait IntoCrossAppClickstreamEventPayload {
    fn into_cross_app_clickstream_event_payload(
        self,
    ) -> Result<CrossAppClickstreamEventPayload, ClickstreamError>;
}

impl<T> IntoCrossAppClickstreamEventPayload for T
where
    T: CrossAppEventKeys + Serialize,
{
    fn into_cross_app_clickstream_event_payload(
        self,
    ) -> Result<CrossAppClickstreamEventPayload, ClickstreamError> {
        let source = self.source();
        let name = self.name();
        let value = serde_json::to_value(self).map_err(ClickstreamError::from)?;
        match value {
            Value::Object(obj) => Ok(CrossAppClickstreamEventPayload {
                source,
                name,
                params: obj,
            }),
            Value::Null => Ok(CrossAppClickstreamEventPayload {
                source,
                name,
                params: Default::default(),
            }),
            _ => Err(ClickstreamError::NotAnObject),
        }
    }
}

impl ClickstreamClient {
    pub fn track<T>(&self, event: T)
    where
        T: IntoCrossAppClickstreamEventPayload,
    {
        match event.into_cross_app_clickstream_event_payload() {
            Ok(value) => {
                self.0.track(value);
            }
            Err(err) => {
                log::error!(
                    "[Clickstream] Failed to serialize clickstream event: {:?}",
                    err
                );
            }
        }
    }
}

pub fn provide_clickstream_client(scope: Scope, emitter: Rc<dyn ClickstreamEmitter>) {
    provide_context::<ClickstreamClient>(scope, ClickstreamClient(emitter));
}

/// Uses the global app-level `ClickstreamClient` as there will be mechanisms in place such as buffering/flushing that
/// should be shared across the application.
pub fn use_clickstream_client(scope: Scope) -> ClickstreamClient {
    use_context::<ClickstreamClient>(scope).unwrap_or_else(|| {
        log::error!("ClickstreamClient not found in Context. Constructing a fallback client, which means reporting should still work. \
        However, this is not shared with the global client so there could be performance issues due to independent flushing in the future.\
        Buffering/flushing is not implemented yet, but will likely be with the completion of the pure Rust migration");
        ClickstreamClient(Rc::new(AppEventReporter::new(scope)))
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_only_scope;
    use rstest::rstest;
    use std::cell::RefCell;

    #[derive(Default)]
    struct MockClickstreamEmitter {
        events: RefCell<Vec<CrossAppClickstreamEventPayload>>,
    }

    impl ClickstreamEmitter for MockClickstreamEmitter {
        fn track(&self, event: CrossAppClickstreamEventPayload) {
            self.events.borrow_mut().push(event);
        }
    }

    impl MockClickstreamEmitter {
        fn get_events(&self) -> Vec<CrossAppClickstreamEventPayload> {
            self.events.borrow().clone()
        }
    }

    // Test structures
    #[derive(Serialize)]
    struct ValidTestEvent {
        field1: String,
        field2: i32,
    }

    impl CrossAppEventKeys for ValidTestEvent {
        fn source(&self) -> &'static str {
            "test_source"
        }
        fn name(&self) -> &'static str {
            "test_event"
        }
    }

    #[derive(Serialize)]
    struct NonObjectTestEvent(String);

    impl CrossAppEventKeys for NonObjectTestEvent {
        fn source(&self) -> &'static str {
            "test_source"
        }
        fn name(&self) -> &'static str {
            "test_event"
        }
    }

    #[test]
    fn test_successful_event_tracking() {
        launch_only_scope(|scope| {
            let emitter = Rc::new(MockClickstreamEmitter::default());
            provide_clickstream_client(scope, emitter.clone());
            let client = use_clickstream_client(scope);

            let test_event = ValidTestEvent {
                field1: "test".to_string(),
                field2: 42,
            };

            client.track(test_event);

            let events = emitter.get_events();
            assert_eq!(events.len(), 1);

            let event = &events[0];
            assert_eq!(event.source, "test_source");
            assert_eq!(event.name, "test_event");
            assert_eq!(
                event.params.get("field1").and_then(|v| v.as_str()),
                Some("test")
            );
            assert_eq!(
                event.params.get("field2").and_then(|v| v.as_i64()),
                Some(42)
            );
        });
    }

    #[test]
    fn test_non_object_event_tracking() {
        launch_only_scope(|scope| {
            let emitter = Rc::new(MockClickstreamEmitter::default());
            provide_clickstream_client(scope, emitter.clone());
            let client = use_clickstream_client(scope);

            let test_event = NonObjectTestEvent("test".to_string());
            client.track(test_event);

            // Should not track due to error, but should not panic
            assert_eq!(emitter.get_events().len(), 0);
        });
    }

    #[rstest]
    fn test_into_cross_app_clickstream_event_payload() {
        launch_only_scope(|scope| {
            let emitter = Rc::new(MockClickstreamEmitter::default());
            provide_clickstream_client(scope, emitter.clone());
            let client = use_clickstream_client(scope);

            // Test object payload (should succeed)
            let valid_event = ValidTestEvent {
                field1: "test".to_string(),
                field2: 42,
            };
            client.track(valid_event);
            assert_eq!(emitter.get_events().len(), 1);

            // Test non-object payload (should fail gracefully)
            let invalid_event = NonObjectTestEvent("test".to_string());
            client.track(invalid_event);
            assert_eq!(emitter.get_events().len(), 1); // Count shouldn't increase
        });
    }

    #[test]
    fn test_error_handling() {
        launch_only_scope(|scope| {
            let emitter = Rc::new(MockClickstreamEmitter::default());
            provide_clickstream_client(scope, emitter.clone());
            let client = use_clickstream_client(scope);

            // Test non-object
            #[derive(Serialize)]
            struct BadEvent(Vec<u32>);

            impl CrossAppEventKeys for BadEvent {
                fn source(&self) -> &'static str {
                    "test"
                }
                fn name(&self) -> &'static str {
                    "test"
                }
            }

            let bad_event = BadEvent(vec![0, 1, 2]);
            client.track(bad_event);

            // Should not track due to serialization error, but should not panic
            assert_eq!(emitter.get_events().len(), 0);
        });
    }
}
