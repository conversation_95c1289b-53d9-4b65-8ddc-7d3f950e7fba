use crate::client::CrossAppEventKeys;
use crate::events::common::types::{
    Analytics, ClickstreamPageTypes, ClickstreamSubPageTypes, RefMarker,
};
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::TransitionAction;
use serde::Serialize;
use std::collections::HashMap;

// https://code.amazon.com/packages/AVLivingRoomClient/blobs/7259c30b4195a1f2502ab6eafade7b1f80f7077e/--/packages/avlrc-react-components-card-events/types/CardEvents.ts#L13

// A very cross app event specific type, see the React implementation
#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug)]
#[serde(rename_all = "camelCase")]
pub struct ClickstreamPageParams {
    pub page_type: ClickstreamPageTypes,
    pub sub_page_type: ClickstreamSubPageTypes,
}

/*
See Remaster clickstream spec:
    https://quip-amazon.com/D5k6Aagktb8j/Remaster-Telemetry-Clickstream-Spec
override pageType, subPageType when ShortCarouselCard or Nodes SeeMoreLink is pressed as
cleanSlate logic to define these fields based on page params deviates from the above spec
We can rely on cleanSlate logic for pre-Remaster-existing ChannelCardCircular/ChannelCardRectangular
 */
impl ClickstreamPageParams {
    pub fn from_short_carousel_card_action(action: &TransitionAction) -> Self {
        let sub_page_type = action
            .is_swift_action_and(|a| ClickstreamSubPageTypes::Known(a.pageId.clone()))
            .unwrap_or(ClickstreamSubPageTypes::Unknown);

        Self {
            page_type: ClickstreamPageTypes::Browse,
            sub_page_type,
        }
    }

    pub fn from_nodes_leading_button_action(action: &TransitionAction) -> Self {
        let page_type = match action {
            TransitionAction::manageSubscription(_action) => ClickstreamPageTypes::Manage,
            _ => ClickstreamPageTypes::Browse,
        };

        Self {
            page_type,
            sub_page_type: ClickstreamSubPageTypes::Subscription,
        }
    }
}

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct CardClickedEvent {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub analytics: Option<Analytics>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub ref_marker: Option<RefMarker>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub clickstream_page_params: Option<ClickstreamPageParams>,
}

impl CrossAppEventKeys for CardClickedEvent {
    fn source(&self) -> &'static str {
        "CARD"
    }

    fn name(&self) -> &'static str {
        "CARD_CLICKED"
    }
}

// TODO: This is basically just copy pasted, probably a lot of common things can be extracted
impl CardClickedEvent {
    pub fn new_from_action(action: &TransitionAction) -> Self {
        let (ref_marker, analytics) = match action {
            TransitionAction::landing(action)
            | TransitionAction::registration(action)
            | TransitionAction::settings(action)
            | TransitionAction::search(action)
            | TransitionAction::clientSearch(action)
            | TransitionAction::watchList(action)
            | TransitionAction::yvl(action)
            | TransitionAction::legacyDetail(action)
            | TransitionAction::primeSignUp(action)
            | TransitionAction::detail(action)
            | TransitionAction::tournament(action)
            | TransitionAction::category(action)
            | TransitionAction::store(action)
            | TransitionAction::live(action)
            | TransitionAction::linearStationDetail(action)
            | TransitionAction::freeWithAds(action)
            | TransitionAction::myStuff(action)
            | TransitionAction::collection(action)
            | TransitionAction::testPlayground(action) => (
                Some(action.refMarker.clone()),
                Some(action.analytics.clone()),
            ),
            TransitionAction::signUp(action) => (
                Some(action.refMarker.clone()),
                Some(action.analytics.clone()),
            ),
            TransitionAction::player(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::playback(action) => (Some(action.refMarker.clone()), None),
            // only a card action for BonusScheduleCards on the Details Page
            TransitionAction::playbackGroup(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::acquisition(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::manageSubscription(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::profileSettings(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::consent(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::browse(_action) => (None, None),
            TransitionAction::openModal(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::fuseOfferActivation(action)
            | TransitionAction::sportsFavorites(action)
            | TransitionAction::removeFromFavorites(action)
            | TransitionAction::changeHomeRegion(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::primeRetention(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::sportsSchedule(action) => (Some(action.refMarker.clone()), None),
            TransitionAction::liveSportsNoOp => (None, None),
            TransitionAction::apiLink(action) => (Some(action.refMarker.clone()), None),
        };

        Self {
            clickstream_page_params: None,
            ref_marker,
            analytics,
        }
    }

    pub fn with_page_params(self, clickstream_page_params: ClickstreamPageParams) -> Self {
        Self {
            clickstream_page_params: Some(clickstream_page_params),
            ..self
        }
    }

    pub fn with_ref_marker(self, ref_marker: String) -> Self {
        Self {
            ref_marker: Some(ref_marker),
            ..self
        }
    }

    pub fn with_analytics(self, analytics: HashMap<String, String>) -> Self {
        Self {
            analytics: Some(analytics),
            ..self
        }
    }
}

pub type ActionToClickstreamPageParamsFn = fn(&TransitionAction) -> ClickstreamPageParams;

#[cfg(test)]
mod tests {
    use super::*;
    use crate::client::{CrossAppClickstreamEventPayload, IntoCrossAppClickstreamEventPayload};
    fn create_test_payload() -> CardClickedEvent {
        CardClickedEvent {
            analytics: Some(HashMap::from([(
                "mock key".to_string(),
                "mock value".to_string(),
            )])),
            ref_marker: Some("mock ref marker".to_string()),
            clickstream_page_params: Some(ClickstreamPageParams {
                page_type: ClickstreamPageTypes::Details,
                sub_page_type: ClickstreamSubPageTypes::Subscription,
            }),
        }
    }
    #[test]
    pub fn serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");
        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "CARD",
          "name": "CARD_CLICKED",
          "params": {
            "analytics": {
              "mock key": "mock value"
            },
            "clickstreamPageParams": {
              "pageType": "ATVDetail",
              "subPageType": "Subscription"
            },
            "refMarker": "mock ref marker"
          }
        }
        "###);
    }

    #[test]
    pub fn test_cross_app_event_fields() {
        let payload = create_test_payload();
        assert_eq!(payload.name(), "CARD_CLICKED");
        assert_eq!(payload.source(), "CARD");
    }
}
