use crate::client::CrossAppEventKeys;
use cfg_test_attr_derive::derive_test_only;
use serde::Serialize;

const SOURCE: &str = "ONBOARDING_PAGE";

pub const ONBOARDING_PAGE_SPEEDBUMP_OPEN: &str = "ONBOARDING_PAGE_SPEEDBUMP_OPEN";
pub const ONBOARDING_PAGE_SPEEDBUMP_LIKE: &str = "ONBOARDING_PAGE_SPEEDBUMP_LIKE";
pub const ONBOARDING_PAGE_SPEEDBUMP_REMOVE_LIKE: &str = "ONBOARDING_PAGE_SPEEDBUMP_REMOVE_LIKE";
pub const ONBOARDING_PAGE_SPEEDBUMP_DONE: &str = "ONBOARDING_PAGE_SPEEDBUMP_DONE";
pub const ONBOARDING_PAGE_LIKE: &str = "ONBOARDING_PAGE_LIKE";
pub const ONBOARDING_PAGE_REMOVE_LIKE: &str = "ONBOARDING_PAGE_REMOVE_LIKE";
pub const ONBOARDING_PAGE_DONE: &str = "ONBOARDING_PAGE_DONE";

// PopUp events are not used currently, as Modal variant is not implemented in Rust
pub const ONBOARDING_PAGE_POPUP_SHOW: &str = "ONBOARDING_PAGE_POPUP_SHOW";
pub const ONBOARDING_PAGE_POPUP_CLOSE: &str = "ONBOARDING_PAGE_POPUP_CLOSE";
pub const ONBOARDING_PAGE_POPUP_OPEN: &str = "ONBOARDING_PAGE_POPUP_OPEN";

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
pub struct OnboardingPageSpeedBumpOpenEvent;

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct OnboardingPageSpeedBumpLikeEvent {
    pub page_type_id: String,
}

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct OnboardingPageSpeedBumpRemoveLikeEvent {
    pub page_type_id: String,
}

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
pub struct OnboardingPageSpeedBumpDoneEvent;

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
pub struct OnboardingPagePopUpShowEvent;

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
pub struct OnboardingPagePopUpCloseEvent;

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
pub struct OnboardingPagePopUpOpenEvent;

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct OnboardingPageLikeEvent {
    pub page_type_id: String,
}

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct OnboardingPageRemoveLikeEvent {
    pub page_type_id: String,
}

#[derive(Clone, Serialize)]
#[derive_test_only(PartialEq, Debug, Default)]
pub struct OnboardingPageDoneEvent;

// Implement CrossAppEventKeys for all events
impl CrossAppEventKeys for OnboardingPageSpeedBumpOpenEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_SPEEDBUMP_OPEN
    }
}

impl CrossAppEventKeys for OnboardingPageSpeedBumpLikeEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_SPEEDBUMP_LIKE
    }
}

impl CrossAppEventKeys for OnboardingPageSpeedBumpRemoveLikeEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_SPEEDBUMP_REMOVE_LIKE
    }
}

impl CrossAppEventKeys for OnboardingPageSpeedBumpDoneEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_SPEEDBUMP_DONE
    }
}

impl CrossAppEventKeys for OnboardingPagePopUpShowEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_POPUP_SHOW
    }
}

impl CrossAppEventKeys for OnboardingPagePopUpCloseEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_POPUP_CLOSE
    }
}

impl CrossAppEventKeys for OnboardingPagePopUpOpenEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_POPUP_OPEN
    }
}

impl CrossAppEventKeys for OnboardingPageLikeEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_LIKE
    }
}

impl CrossAppEventKeys for OnboardingPageRemoveLikeEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_REMOVE_LIKE
    }
}

impl CrossAppEventKeys for OnboardingPageDoneEvent {
    fn source(&self) -> &'static str {
        SOURCE
    }
    fn name(&self) -> &'static str {
        ONBOARDING_PAGE_DONE
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::client::{CrossAppClickstreamEventPayload, IntoCrossAppClickstreamEventPayload};

    // Test helpers
    fn create_test_like_payload() -> OnboardingPageLikeEvent {
        OnboardingPageLikeEvent {
            page_type_id: "test_page_type".to_string(),
        }
    }

    fn create_test_speedbump_like_payload() -> OnboardingPageSpeedBumpLikeEvent {
        OnboardingPageSpeedBumpLikeEvent {
            page_type_id: "test_page_type".to_string(),
        }
    }

    fn create_test_speedbump_open_payload() -> OnboardingPageSpeedBumpOpenEvent {
        OnboardingPageSpeedBumpOpenEvent
    }

    // Tests for OnboardingPageLikeEvent
    #[test]
    fn like_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_like_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_LIKE",
          "params": {
            "pageTypeId": "test_page_type"
          }
        }
        "###);
    }

    #[test]
    fn like_event_cross_app_event_fields() {
        let payload = create_test_like_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_LIKE);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPageSpeedBumpLikeEvent
    #[test]
    fn speedbump_like_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_speedbump_like_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_SPEEDBUMP_LIKE",
          "params": {
            "pageTypeId": "test_page_type"
          }
        }
        "###);
    }

    #[test]
    fn speedbump_like_event_cross_app_event_fields() {
        let payload = create_test_speedbump_like_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_SPEEDBUMP_LIKE);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPageSpeedBumpOpenEvent
    #[test]
    fn speedbump_open_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_speedbump_open_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_SPEEDBUMP_OPEN",
          "params": {}
        }
        "###);
    }

    #[test]
    fn speedbump_open_event_cross_app_event_fields() {
        let payload = create_test_speedbump_open_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_SPEEDBUMP_OPEN);
        assert_eq!(payload.source(), SOURCE);
    }

    fn create_test_speedbump_remove_like_payload() -> OnboardingPageSpeedBumpRemoveLikeEvent {
        OnboardingPageSpeedBumpRemoveLikeEvent {
            page_type_id: "test_page_type".to_string(),
        }
    }

    fn create_test_speedbump_done_payload() -> OnboardingPageSpeedBumpDoneEvent {
        OnboardingPageSpeedBumpDoneEvent
    }

    fn create_test_popup_show_payload() -> OnboardingPagePopUpShowEvent {
        OnboardingPagePopUpShowEvent
    }

    fn create_test_popup_close_payload() -> OnboardingPagePopUpCloseEvent {
        OnboardingPagePopUpCloseEvent
    }

    fn create_test_popup_open_payload() -> OnboardingPagePopUpOpenEvent {
        OnboardingPagePopUpOpenEvent
    }

    fn create_test_remove_like_payload() -> OnboardingPageRemoveLikeEvent {
        OnboardingPageRemoveLikeEvent {
            page_type_id: "test_page_type".to_string(),
        }
    }

    fn create_test_done_payload() -> OnboardingPageDoneEvent {
        OnboardingPageDoneEvent
    }

    // Tests for OnboardingPageSpeedBumpRemoveLikeEvent
    #[test]
    fn speedbump_remove_like_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_speedbump_remove_like_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_SPEEDBUMP_REMOVE_LIKE",
          "params": {
            "pageTypeId": "test_page_type"
          }
        }
        "###);
    }

    #[test]
    fn speedbump_remove_like_event_cross_app_event_fields() {
        let payload = create_test_speedbump_remove_like_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_SPEEDBUMP_REMOVE_LIKE);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPageSpeedBumpDoneEvent
    #[test]
    fn speedbump_done_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_speedbump_done_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_SPEEDBUMP_DONE",
          "params": {}
        }
        "###);
    }

    #[test]
    fn speedbump_done_event_cross_app_event_fields() {
        let payload = create_test_speedbump_done_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_SPEEDBUMP_DONE);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPagePopUpShowEvent
    #[test]
    fn popup_show_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_popup_show_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_POPUP_SHOW",
          "params": {}
        }
        "###);
    }

    #[test]
    fn popup_show_event_cross_app_event_fields() {
        let payload = create_test_popup_show_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_POPUP_SHOW);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPagePopUpCloseEvent
    #[test]
    fn popup_close_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_popup_close_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_POPUP_CLOSE",
          "params": {}
        }
        "###);
    }

    #[test]
    fn popup_close_event_cross_app_event_fields() {
        let payload = create_test_popup_close_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_POPUP_CLOSE);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPagePopUpOpenEvent
    #[test]
    fn popup_open_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_popup_open_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_POPUP_OPEN",
          "params": {}
        }
        "###);
    }

    #[test]
    fn popup_open_event_cross_app_event_fields() {
        let payload = create_test_popup_open_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_POPUP_OPEN);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPageRemoveLikeEvent
    #[test]
    fn remove_like_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_remove_like_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_REMOVE_LIKE",
          "params": {
            "pageTypeId": "test_page_type"
          }
        }
        "###);
    }

    #[test]
    fn remove_like_event_cross_app_event_fields() {
        let payload = create_test_remove_like_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_REMOVE_LIKE);
        assert_eq!(payload.source(), SOURCE);
    }

    // Tests for OnboardingPageDoneEvent
    #[test]
    fn done_event_serializes_correctly() {
        let payload: CrossAppClickstreamEventPayload = create_test_done_payload()
            .into_cross_app_clickstream_event_payload()
            .expect("Failed to serialize payload");

        insta::assert_json_snapshot!(payload, @r###"
        {
          "source": "ONBOARDING_PAGE",
          "name": "ONBOARDING_PAGE_DONE",
          "params": {}
        }
        "###);
    }

    #[test]
    fn done_event_cross_app_event_fields() {
        let payload = create_test_done_payload();
        assert_eq!(payload.name(), ONBOARDING_PAGE_DONE);
        assert_eq!(payload.source(), SOURCE);
    }
}
