use cfg_test_attr_derive::derive_test_only;
use serde::{Serialize, Serializer};
use std::collections::HashMap;
use std::string::ToString;
use strum::*;

pub type RefMarker = String;
pub type Analytics = HashMap<String, String>;

#[derive(Clone, Display)]
#[derive_test_only(Debug, PartialEq)]
pub enum ClickstreamPageTypes {
    #[strum(to_string = "ATVDetail")]
    Details,
    #[strum(to_string = "ATVBrowse")]
    Browse,
    #[strum(to_string = "ATVManage")]
    Manage,
}

impl Serialize for ClickstreamPageTypes {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}

#[derive(<PERSON>lone, Display)]
#[derive_test_only(Debug, PartialEq)]
pub enum ClickstreamSubPageTypes {
    #[strum(to_string = "{0}")]
    Known(String),
    #[strum(to_string = "Subscription")]
    Subscription,
    #[strum(to_string = "unknownPageType")]
    Unknown,
}

impl Serialize for ClickstreamSubPageTypes {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;
    use serde_json;
    use serde_json::Value::String;

    fn assert_serializes_str<T: Serialize>(value: &T, expected: &str) {
        let value = serde_json::to_value(value).expect("Should be serializable");
        let String(actual) = value else {
            panic!("Should be a string: {:?}", value);
        };

        assert_eq!(actual, expected);
    }

    #[rstest]
    #[case(ClickstreamPageTypes::Details, "ATVDetail")]
    #[case(ClickstreamPageTypes::Browse, "ATVBrowse")]
    #[case(ClickstreamPageTypes::Manage, "ATVManage")]
    fn test_clickstream_page_types_serialization(
        #[case] page_type: ClickstreamPageTypes,
        #[case] expected: &str,
    ) {
        assert_serializes_str(&page_type, expected);
    }

    #[rstest]
    #[case(ClickstreamSubPageTypes::Known("TestPage".to_string()), "TestPage")]
    #[case(ClickstreamSubPageTypes::Subscription, "Subscription")]
    #[case(ClickstreamSubPageTypes::Unknown, "unknownPageType")]
    fn test_clickstream_sub_page_types_serialization(
        #[case] sub_page_type: ClickstreamSubPageTypes,
        #[case] expected: &str,
    ) {
        assert_serializes_str(&sub_page_type, expected);
    }
}
