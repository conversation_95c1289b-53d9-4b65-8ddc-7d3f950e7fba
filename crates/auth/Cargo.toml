[package]
name = "auth"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
synchronized-state-store.workspace = true
serde.workspace = true
serde_json.workspace = true
log.workspace = true
cross-app-message.workspace = true
mockall_double.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }

[lints]
workspace = true
