use std::time::{SystemTime, SystemTimeError, UNIX_EPOCH};

use crate::{AccessTokenData, AuthContext};

pub fn should_refresh_auth_token(auth: &AuthContext) -> Result<bool, SystemTimeError> {
    let token_data = match auth.get_token_data() {
        Some(data) => data,
        None => return Ok(false),
    };

    let expires_at = derive_expires_at_from_token_data(&token_data);

    let now = match SystemTime::now().duration_since(UNIX_EPOCH) {
        Ok(t) => t,
        Err(e) => {
            log::error!(
                "SystemTime failed while calculating the epoch time: {:?}",
                e
            );
            return Err(e);
        }
    };

    Ok(now.as_millis() > expires_at.into())
}

pub fn derive_access_token_from_token_data(token_data: &AccessTokenData) -> String {
    match token_data {
        AccessTokenData::ACTOR(token) => token.accessToken.clone(),
        AccessTokenData::ACCOUNT(token) => token.accessToken.clone(),
    }
}

pub fn derive_refresh_token_from_token_data(token_data: &AccessTokenData) -> String {
    match token_data {
        AccessTokenData::ACTOR(token) => token.refreshToken.clone(),
        AccessTokenData::ACCOUNT(token) => token.refreshToken.clone(),
    }
}

pub fn derive_expires_at_from_token_data(token_data: &AccessTokenData) -> u64 {
    match token_data {
        AccessTokenData::ACTOR(token) => token.accessTokenExpiresAt,
        AccessTokenData::ACCOUNT(token) => token.accessTokenExpiresAt,
    }
}
