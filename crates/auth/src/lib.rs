#![allow(nonstandard_style, reason = "Follows JSON pattern")]
pub mod helpers;
pub mod hooks;

use helpers::{derive_access_token_from_token_data, derive_refresh_token_from_token_data};
#[double]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::reactive::*;
use ignx_compositron::{app::rpc::RPCError, reactive::use_context};
use mockall_double::double;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::cell::RefCell;
use std::rc::Rc;
use synchronized_state_store::{MockStateDispatcher, StateDispatcher, SynchronizedStateStore};

const AUTH_STORE_ID: &str = "auth";
const EXCHANGE_ACCOUNT_TOKEN_FOR_ACTOR_FN: &str = "exchangeAccountAccessTokenForActorAccessToken";

// The below three structs cannot be completely migrated to NetworkParsed due to being used with RPC
// DO NOT remove the serde tag until RPC is fully compatible with NetworkParsed and does not need
// serde, or we will cause availability drops and potentially sign customers out: https://t.corp.amazon.com/D138639529/communication https://t.corp.amazon.com/D137429943/communication
#[derive(Serialize, Deserialize, NetworkParsed, Debug, Clone, PartialEq)]
#[network(tag = "accessTokenType")]
#[serde(tag = "accessTokenType")]
pub enum AccessTokenData {
    ACCOUNT(AccountAccessTokenData),
    ACTOR(ActorAccessTokenData),
}

#[derive(Serialize, Deserialize, NetworkParsed, Debug, Clone, PartialEq)]
pub struct AccountAccessTokenData {
    accessToken: String,
    accessTokenExpiresAt: u64,
    refreshToken: String,
}

#[derive(Serialize, Deserialize, NetworkParsed, Debug, Clone, PartialEq)]
pub struct ActorAccessTokenData {
    pub accessToken: String,
    pub accessTokenExpiresAt: u64,
    pub refreshToken: String,
    pub actorId: String,
}

type AuthData = NetworkOptional<AccessTokenData>;

pub trait AuthTokens {
    fn get_access_token(&self) -> Option<String>;
    fn access_token(&self) -> Signal<Option<String>>;

    fn is_last_known_state_signed_in(&self) -> bool;
    fn is_signed_in(&self) -> Signal<bool>;

    fn exchange_account_access_token_for_actor_access_token(
        &self,
        profile_id: String,
        on_success: Option<Box<dyn FnOnce()>>,
        on_failure: Option<Box<dyn FnOnce()>>,
    );
    fn get_token_data(&self) -> Option<AccessTokenData>;
    fn set_token_from_rpc_call(&self, token_data: Option<AccessTokenData>);
    fn refresh_auth_token(
        &self,
        success_callback: Box<dyn FnOnce(AccessTokenData)>,
        error_callback: Box<dyn FnOnce(RPCError)>,
    );
    fn deregister_device(&self, success_callback: Box<dyn FnOnce(serde_json::Value)>);
    fn clear_access_token(&self, success_callback: Box<dyn FnOnce(serde_json::Value)>);
}

pub type AuthContext = Rc<dyn AuthTokens>;

#[derive(Clone)]
pub struct Auth {
    synchronized_state_store: SynchronizedStateStore<AuthData>,
    rpc_manager: RPCManager,
    is_signed_in: Signal<bool>,
    access_token: Signal<Option<String>>,
}

impl Auth {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher, rpc_manager: RPCManager) -> Self {
        let synchronized_state_store: SynchronizedStateStore<AuthData> =
            SynchronizedStateStore::new(
                scope,
                state_dispatcher,
                AUTH_STORE_ID.into(),
                NetworkOptional::None,
            );

        let sss_read_signal = synchronized_state_store.get_read_signal();

        Self {
            synchronized_state_store,
            rpc_manager,
            is_signed_in: create_memo(scope, move |_| {
                let auth_data: Option<AccessTokenData> = sss_read_signal.get().into();
                auth_data.is_some()
            })
            .into(),
            access_token: create_memo(scope, move |_| {
                sss_read_signal
                    .get()
                    .map(derive_access_token_from_token_data)
                    .into()
            })
            .into(),
        }
    }
}

impl AuthTokens for Auth {
    fn get_access_token(&self) -> Option<String> {
        self.synchronized_state_store
            .derive(|d| d.map(derive_access_token_from_token_data))
            .into()
    }

    fn access_token(&self) -> Signal<Option<String>> {
        self.access_token
    }

    fn is_last_known_state_signed_in(&self) -> bool {
        self.get_token_data().is_some()
    }

    fn is_signed_in(&self) -> Signal<bool> {
        self.is_signed_in
    }

    fn exchange_account_access_token_for_actor_access_token(
        &self,
        profile_id: String,
        on_success: Option<Box<dyn FnOnce()>>,
        on_failure: Option<Box<dyn FnOnce()>>,
    ) {
        let success_callback = Box::new(|_result: ActorAccessTokenData| {
            if let Some(on_success_fn) = on_success {
                on_success_fn();
            }
        });

        let failure_callback = Box::new(|_error: RPCError| {
            if let Some(on_failure) = on_failure {
                on_failure();
            }
        });

        self.rpc_manager
            .call::<ActorAccessTokenData>(EXCHANGE_ACCOUNT_TOKEN_FOR_ACTOR_FN)
            .arg("profileId", profile_id.into())
            .success_callback(success_callback)
            .error_callback(failure_callback)
            .send();
    }

    fn get_token_data(&self) -> Option<AccessTokenData> {
        self.synchronized_state_store
            .derive(|d| d.map(|d| d.to_owned()))
            .into()
    }

    fn set_token_from_rpc_call(&self, token_data: Option<AccessTokenData>) {
        let sss_data = self.get_token_data();

        if token_data != sss_data {
            #[allow(
                deprecated,
                reason = "This is from before we had RPC calls, once we're moving to pure Rust auth, it will be removed"
            )]
            self.synchronized_state_store.set(token_data.into());
        }
    }

    fn deregister_device(&self, success_callback: Box<dyn FnOnce(serde_json::Value)>) {
        self.rpc_manager
            .call::<serde_json::Value>("deregisterAuthentication")
            .success_callback(success_callback)
            .send();
    }

    fn refresh_auth_token(
        &self,
        success_callback: Box<dyn FnOnce(AccessTokenData)>,
        error_callback: Box<dyn FnOnce(RPCError)>,
    ) {
        let token = self
            .synchronized_state_store
            .derive(|d| d.map(derive_refresh_token_from_token_data));

        let token_optional: Option<String> = token.into();

        self.rpc_manager
            .call("refreshAccessToken")
            .arg("refreshToken", token_optional.unwrap_or_default().into())
            .success_callback(success_callback)
            .error_callback(error_callback)
            .send();
    }

    fn clear_access_token(&self, success_callback: Box<dyn FnOnce(serde_json::Value)>) {
        self.rpc_manager
            .call("clearAccessToken")
            .success_callback(success_callback)
            .send()
    }
}

pub fn is_last_known_state_signed_in(scope: Scope) -> bool {
    match use_context::<AuthContext>(scope) {
        Some(a) => a.is_last_known_state_signed_in(),
        None => {
            log::error!("Cannot find AuthContext in current scope. Returning state as signed out.");
            false
        }
    }
}

type Callback = Option<Box<dyn FnOnce()>>;
type CallbackData = (String, Callback, Callback);
type PendingCallbacks = Rc<RefCell<Option<CallbackData>>>;

#[derive(Clone)]
pub struct MockAuth {
    mock_access_token_signal: RwSignal<Option<String>>,
    exchange_token_call_should_succeed: bool,
    is_signed_in: RwSignal<bool>,
    // Set defer callback mode if you want token exchanges to happen async
    is_defer_callback_mode: bool,
    pending_cbs: PendingCallbacks,
    // Use below to set the access token type to actor
    is_access_token_actor: bool,
    actor_id: Option<String>,
}

impl MockAuth {
    pub fn new(
        scope: Scope,
        _state_dispatcher: &MockStateDispatcher,
        _rpc_manager: RPCManager,
    ) -> Self {
        Self::new_without_params(scope)
    }

    pub fn new_without_params(scope: Scope) -> Self {
        Self {
            exchange_token_call_should_succeed: true,
            mock_access_token_signal: create_rw_signal(scope, None),
            is_signed_in: create_rw_signal(scope, false),
            is_defer_callback_mode: false,
            pending_cbs: Rc::new(RefCell::new(None)),
            is_access_token_actor: false,
            actor_id: None,
        }
    }

    pub fn provide(self, scope: Scope) {
        provide_context::<AuthContext>(scope, Rc::new(self));
    }

    pub fn set_defer_callback_mode(&mut self) {
        self.is_defer_callback_mode = true;
    }

    pub fn get_pending_request_profile_id(&self) -> Option<String> {
        self.pending_cbs
            .borrow()
            .as_ref()
            .map(|(profile_id, _, _)| profile_id.clone())
    }

    pub fn execute_pending_request_callback(&mut self, should_succeed: bool) {
        let pending_cb = self.pending_cbs.replace(None);
        if let Some((_, success_cb, failure_cb)) = pending_cb {
            if should_succeed {
                if let Some(success_cb) = success_cb {
                    success_cb();
                }
            } else if let Some(failure_cb) = failure_cb {
                failure_cb();
            }
        }
    }

    pub fn set_access_token(&mut self, access_token: Option<String>) {
        self.mock_access_token_signal.set(access_token);
    }

    pub fn set_actor_token_info(
        &mut self,
        should_set_access_token_as_actor: bool,
        actor_id: Option<String>,
    ) {
        self.is_access_token_actor = should_set_access_token_as_actor;
        self.actor_id = actor_id;
    }

    pub fn set_access_token_signal(&mut self, access_token_signal: RwSignal<Option<String>>) {
        self.mock_access_token_signal = access_token_signal;
    }

    pub fn set_exchange_token_call_should_succeed(&mut self, should_succeed: bool) {
        self.exchange_token_call_should_succeed = should_succeed;
    }

    pub fn set_is_signed_in(&self, is_signed_in: bool) {
        self.is_signed_in.set(is_signed_in)
    }
}

impl AuthTokens for MockAuth {
    fn get_access_token(&self) -> Option<String> {
        self.mock_access_token_signal.get_untracked()
    }

    fn access_token(&self) -> Signal<Option<String>> {
        self.mock_access_token_signal.into()
    }

    fn is_last_known_state_signed_in(&self) -> bool {
        self.mock_access_token_signal.get_untracked().is_some()
    }

    fn is_signed_in(&self) -> Signal<bool> {
        self.is_signed_in.into()
    }

    fn exchange_account_access_token_for_actor_access_token(
        &self,
        profile_id: String,
        on_success: Option<Box<dyn FnOnce()>>,
        on_failure: Option<Box<dyn FnOnce()>>,
    ) {
        if self.is_defer_callback_mode {
            *self.pending_cbs.borrow_mut() = Some((profile_id, on_success, on_failure));
        } else if self.exchange_token_call_should_succeed {
            if let Some(success_cb) = on_success {
                success_cb();
            }
        } else if let Some(failure_cb) = on_failure {
            failure_cb();
        }
    }

    fn get_token_data(&self) -> Option<AccessTokenData> {
        if self.is_access_token_actor {
            Some(AccessTokenData::ACTOR(ActorAccessTokenData {
                accessToken: "Some".to_string(),
                accessTokenExpiresAt: *************, // some time in ms in 2040
                refreshToken: "emoS".to_string(),
                actorId: self.actor_id.clone().unwrap_or_default(),
            }))
        } else {
            Some(AccessTokenData::ACCOUNT(AccountAccessTokenData {
                accessToken: "Some".to_string(),
                accessTokenExpiresAt: *************, // some time in ms in 2040
                refreshToken: "emoS".to_string(),
            }))
        }
    }

    fn refresh_auth_token(
        &self,
        _success_callback: Box<dyn FnOnce(AccessTokenData)>,
        _error_callback: Box<dyn FnOnce(RPCError)>,
    ) {
        log::warn!("refresh_auth_token called");
    }

    fn set_token_from_rpc_call(&self, _token_data: Option<AccessTokenData>) {
        log::warn!("set_token_from_rpc_call called")
    }

    fn deregister_device(&self, _success_callback: Box<dyn FnOnce(serde_json::Value)>) {
        log::warn!("deregister_device called")
    }

    fn clear_access_token(&self, _success_callback: Box<dyn FnOnce(serde_json::Value)>) {
        log::warn!("clear_access_token called")
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::app::rpc::{MockRPCCall, MockRPCManager};

    #[test]
    fn it_issues_rpc_to_exchange_the_refresh_token_for_actor_token() {
        launch_only_scope(|scope| {
            let state_dispatcher = StateDispatcher::new();
            let mut mock_rpc_manager = MockRPCManager::new();

            // Mock the RPCCall builder calls
            let mut first_mock = MockRPCCall::<ActorAccessTokenData>::new();
            let mut second_mock = MockRPCCall::<ActorAccessTokenData>::new();
            let mut third_mock = MockRPCCall::<ActorAccessTokenData>::new();
            let mut fourth_mock = MockRPCCall::<ActorAccessTokenData>::new();
            fourth_mock.expect_send().return_once(|| ());
            third_mock
                .expect_error_callback()
                .return_once(|_| fourth_mock);
            second_mock
                .expect_success_callback()
                .return_once(|_callback| third_mock);

            first_mock
                .expect_arg()
                .withf(|key, val| key.eq("profileId") && val.eq("mock_profile_id"))
                .return_once(|_, _| second_mock);
            mock_rpc_manager
                .expect_call()
                .withf(|f| f.eq(EXCHANGE_ACCOUNT_TOKEN_FOR_ACTOR_FN))
                .return_once(|_| first_mock);

            let auth = Auth::new(scope, &state_dispatcher, mock_rpc_manager);
            auth.exchange_account_access_token_for_actor_access_token(
                "mock_profile_id".into(),
                None,
                None,
            );
        });
    }
}
