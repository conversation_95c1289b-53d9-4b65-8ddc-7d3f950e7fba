use crate::AuthContext;
use ignx_compositron::prelude::*;

/// Retrieves the authentication context from the current scope.
///
/// This function provides access to the full `AuthContext`.
///
/// # Arguments
///
/// * `scope` - The current scope from which to retrieve the authentication context.
///
/// # Returns
///
/// Returns the `AuthContext` containing authentication-related information and methods.
///
/// # Panics
///
/// This function will panic if the `AuthContext` is not found in the given scope.
///
/// # Warning
///
/// We don't recommend using this hook in Composers. Use specialized versions for your exact
/// use case, such as `use_access_token`.
///
/// # See Also
///
/// - [`use_access_token`] - For accessing just the access token.
/// - [`AuthContext`] - For more details on the authentication context structure.
pub fn use_auth(scope: Scope) -> AuthContext {
    #[allow(clippy::expect_used)]
    use_context::<AuthContext>(scope).expect("Auth was not found in the given scope")
}

/// Retrieves the access token from the authentication context.
///
/// This function provides a convenient way to access the current access token
/// within a component or other parts of your application.
///
/// # Arguments
///
/// * `scope` - The current scope from which to retrieve the authentication context.
///
/// # Returns
///
/// Returns a `Signal<Option<String>>` representing the access token. The signal
/// will update whenever the access token changes in the authentication context.
/// It is memoized.
///
/// # Panics
///
/// This function will panic if the `AuthContext` is not found in the given scope.
///
/// # See Also
///
/// - [`use_auth`] - For accessing the full authentication context.
/// - [`AuthContext`] - For more details on the authentication context structure.
pub fn use_access_token(scope: Scope) -> Signal<Option<String>> {
    let auth = use_auth(scope);
    auth.access_token()
}
