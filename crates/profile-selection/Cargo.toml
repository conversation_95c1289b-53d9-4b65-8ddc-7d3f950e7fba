[package]
name = "profile-selection"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
popups.workspace = true
log.workspace = true
serde.workspace = true
serde_json.workspace = true
location.workspace = true
router.workspace = true
fableous.workspace = true
title-details.workspace = true
auth.workspace = true
cross-app-events.workspace = true
app-events.workspace = true
network.workspace = true
hero.workspace = true
common-transform-types.workspace = true
derive_more.workspace = true
lrc-image.workspace = true
profile-manager.workspace = true
media-background.workspace = true
mockall_double.workspace = true
deferred-navigation.workspace = true
mockall.workspace = true
app-config.workspace = true
rust-features.workspace = true
cache.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
app-reporting.workspace = true
collections-ui.workspace = true
resiliency-store.workspace = true
container-types.workspace = true
containers.workspace = true
taps-parameters.workspace = true
profile-selection-shared.workspace = true
strum.workspace = true
cfg-test-attr-derive.workspace = true
transition-executor.workspace = true
storage-events.workspace = true
reduce-steps-to-mlp.workspace = true
payment-risk-message.workspace = true
navigation-menu.workspace = true
settings-manager.workspace = true

[dev-dependencies]
rstest.workspace = true
mockall.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_app", "mock_timer"] }
network = { workspace = true, features = ["test_utils", "mock_network"] }
serial_test.workspace = true
router = { workspace = true, features = ["test_utils"] }
profile-selection-shared = { workspace = true, features = ["test_utils"] }
transition-executor = { workspace = true, features = ["test_utils"] }

[features]
default = []
debug_impl = []

[[example]]
name = "profile_buttons"
crate-type = ["cdylib"]
path = "examples/profile_buttons.rs"

[[example]]
name = "profile_error_modal"
crate-type = ["cdylib"]
path = "examples/profile_error_modal.rs"

[[example]]
name = "profile_pin_dialog"
crate-type = ["cdylib"]
path = "examples/profile_pin_dialog.rs"

[[example]]
name = "profile_selection_page_stubbed"
crate-type = ["cdylib"]
path = "examples/profile_selection_page_stubbed/profile_selection_page_stubbed.rs"

[[example]]
name = "profile_selection_page"
crate-type = ["cdylib"]
path = "examples/profile_selection_page.rs"

[[example]]
name = "profile_selection_page_ui_v2"
crate-type = ["cdylib"]
path = "examples/profile_selection_page_ui_v2.rs"

[[example]]
name = "profiles_v2"
crate-type = ["cdylib"]
path = "examples/profiles_v2.rs"
