use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::layout::*;
use ignx_compositron::composables::label::*;
use ignx_compositron::reactive::SignalWith;
use ignx_compositron::stack::*;
use ignx_compositron::{compose, Composer};
use mockall_double::*;
#[double]
use profile_manager::use_profile_manager;

use crate::ui::compound_components::avatar_grid::{AvatarGrid, AvatarGridCaller, AvatarGridProps};
use crate::ui::compound_components::profile_avatar_v2::{ProfileAvatarImageV2, ProfileAvatarImageV2Caller, ProfileAvatarImageV2Props};

#[Composer]
pub fn AvatarSelectionPage(ctx: &AppContext) -> impl VisualComposable<'static> {
    log::info!("[avatar_selection_page] Avatar selection page start.");

    // Get the current user's profile to use their avatar URL
    let profile_manager = use_profile_manager(ctx.scope());
    let current_user_avatar_url = profile_manager
        .get_active_profile()
        .with(|profile_opt| {
            profile_opt
                .as_ref()
                .map(|profile| profile.avatar.avatarUrl.clone())
                .unwrap_or_else(|| "https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=User".to_string())
        });

    compose! {
        Stack() {
            // Main content
            Column() {
                // Main title "Change your profile image"
                Label(text: "Change your profile image")
                    .color(Color::white())
                    .test_id("page-title")

                // First avatar grid - The Summer I Turned Pretty
                AvatarGrid(
                    category_name: "The Summer I Turned Pretty",
                    current_user_avatar_url: &current_user_avatar_url
                )

                // Second avatar grid - The Boys
                AvatarGrid(
                    category_name: "The Boys",
                    current_user_avatar_url: &current_user_avatar_url
                )

                // Third avatar grid - Jack Ryan
                AvatarGrid(
                    category_name: "Jack Ryan",
                    current_user_avatar_url: &current_user_avatar_url
                )
            }
            .main_axis_alignment(MainAxisAlignment::SpacedBy(48.0)) // Increased spacing between category sections to match JS
            .padding(Padding::new(72.0, 48.0, 48.0, 96.0)) // Match JS page padding: top matches header margin, sides for content

            // Small square profile avatar in top left corner
            ProfileAvatarImageV2(
                url: current_user_avatar_url.clone(),
                avatar_radius: 0.0, // Square avatar (no border radius)
                base_image_radius: 20.0
            )
            .width(39.0)  // Match JS avatar dimension
            .height(39.0) // Match JS avatar dimension
            .translate_x(72.0)  // Match JS avatar margin start
            .translate_y(54.0)  // Match JS avatar margin top
            .test_id("small-profile-avatar")
        }
        .alignment(Alignment::StartTop)

    }
}
