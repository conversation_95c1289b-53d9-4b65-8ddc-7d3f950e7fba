use std::rc::Rc;

use amzn_fable_tokens::{FableSpacing, FableBorder, FableColor};
use fableous::typography::typography::{TypographyBody200, TypographyBody200Caller, TypographyBody200Props};
use fableous::utils::get_ignx_color;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::id::Id;
use ignx_compositron::layout::*;
use ignx_compositron::reactive::{SignalWith, create_namespace};
use ignx_compositron::stack::*;
use ignx_compositron::widgets::*;
use ignx_compositron::{compose, Composer};
use mockall_double::*;
#[double]
use profile_manager::use_profile_manager;

// Import BasicCarouselUI and related types
use containers::basic_carousel::{BasicCarouselUI, BasicCarouselUICaller, BasicCarouselUIProps, HeaderContent, DEFAULT_END_REACHED_BUFFER};
use crate::ui::basic_components::avatar_grid_item::{AvatarGridItem, AvatarGridItemCaller, AvatarGridItemProps};

/// Avatar data structure
#[derive(Clone)]
pub struct Avatar {
    pub id: String,
    pub name: String,
    pub image_url: Option<String>,
}

impl Id for Avatar {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Create mock avatar data for a category using the current user's avatar URL
fn create_avatar_data_for_category(category_name: &str, current_user_avatar_url: &str) -> Vec<Avatar> {
    // Base avatar data for different categories
    let base_avatars = vec![
        ("1", "Avatar 1"),
        ("2", "Avatar 2"),
        ("3", "Avatar 3"),
        ("4", "Avatar 4"),
        ("5", "Avatar 5"),
        ("6", "Avatar 6"),
        ("7", "Avatar 7"),
        ("8", "Avatar 8"),
        ("9", "Avatar 9"),
        ("10", "Avatar 10"),
        ("11", "Avatar 11"),
        ("12", "Avatar 12"),
    ];

    // Use the current user's avatar URL for all avatar options
    base_avatars.into_iter().map(|(id, name)| Avatar {
        id: format!("{}_{}", category_name.to_lowercase().replace(" ", "_"), id),
        name: name.to_string(),
        image_url: Some(current_user_avatar_url.to_string()),
    }).collect()
}

#[Composer]
pub fn AvatarSelectionPage(ctx: &AppContext) -> impl VisualComposable<'static> {
    log::info!("[avatar_selection_page] Avatar selection page start.");

    // Get the current user's profile to use their avatar URL
    let profile_manager = use_profile_manager(ctx.scope());
    let current_user_avatar_url = profile_manager
        .get_active_profile()
        .with(|profile_opt| {
            profile_opt
                .as_ref()
                .map(|profile| profile.avatar.avatarUrl.clone())
                .unwrap_or_else(|| "https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=User".to_string())
        });

    // Helper function to create avatar carousel for a category
    let create_avatar_carousel = |ctx: &AppContext, category_name: &str| {
        let avatars = create_avatar_data_for_category(category_name, &current_user_avatar_url);
        let focus_namespace = create_namespace(ctx.scope(), &format!("avatar-carousel-{}", category_name.to_lowercase().replace(" ", "-")));

        let avatar_builder = Rc::new(move |ctx: &AppContext, avatar: &Avatar, avatar_index: Signal<usize>| {
            let avatar_index_val = avatar_index.get_untracked();
            let avatar_url = avatar.image_url.as_ref()
                .map(|s| s.as_str())
                .unwrap_or("https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=Avatar");

            // Only the first avatar gets matched geometry for focus ring
            let is_first_avatar = avatar_index_val == 0;

            compose! {
                AvatarGridItem(
                    avatar_url: avatar_url,
                    focus_namespace: if is_first_avatar { Some(focus_namespace) } else { None },
                    is_focused: None
                )
                .test_id(format!("avatar-item-{}-{}", category_name.to_lowercase().replace(" ", "-"), avatar_index_val))
            }
        });

        compose! {
            Column() {
                // Category title
                TypographyBody200(content: category_name, color: Color::white())
                    .test_id(format!("category-title-{}", category_name.to_lowercase().replace(" ", "-")))
                    .padding(Padding::new(0.0, 0.0, FableSpacing::SPACING150, 0.0))

                // Avatar carousel using BasicCarouselUI
                Stack() {
                    BasicCarouselUI(
                        items: avatars,
                        item_builder: avatar_builder,
                        spacing: FableSpacing::SPACING100,
                        on_end_reached: Rc::new(|| {}),
                        header: HeaderContent::None,
                        on_back_pressed: Rc::new(|| {}),
                        default_focus_index: 0,
                        focus_ring_namespace: Some(focus_namespace)
                    )
                    .test_id(format!("avatar-carousel-{}", category_name.to_lowercase().replace(" ", "-")))

                    // Focus ring - always shows at the first position
                    Rectangle()
                        .matched_geometry(focus_namespace, false, true)
                        .border_color(get_ignx_color(FableColor::PRIMARY))
                        .border_width(FableBorder::FOCUSED_WIDTH)
                        .border_radius(20.0) // Match avatar border radius
                        .test_id(format!("avatar-focus-ring-{}", category_name.to_lowercase().replace(" ", "-")))
                }
            }
            .test_id(format!("avatar-category-{}", category_name.to_lowercase().replace(" ", "-")))
        }
    };

    compose! {
        Stack() {
            // Small square profile avatar in top left corner
            ProfileAvatarImageV2(
                url: current_user_avatar_url.clone(),
                avatar_radius: 0.0, // Square avatar (no border radius)
                base_image_radius: 20.0
            )
            .width(39.0)  // Match JS avatar dimension
            .height(39.0) // Match JS avatar dimension
            .translate_x(FableSpacing::SPACING200)  // 32px from left edge - slightly closer to edge
            .translate_y(FableSpacing::SPACING200)  // 32px from top edge - better vertical alignment
            .test_id("small-profile-avatar")

            // Main content
            Column() {
                // Main title "Change your profile image"
                Label(text: "Change your profile image")
                    .color(Color::white())
                    .test_id("page-title")

                // First avatar carousel - The Summer I Turned Pretty
                create_avatar_carousel(ctx, "The Summer I Turned Pretty")

                // Second avatar carousel - The Boys
                create_avatar_carousel(ctx, "The Boys")

                // Third avatar carousel - Jack Ryan
                create_avatar_carousel(ctx, "Jack Ryan")
            }
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING300)) // 48px spacing between category sections to match JS
            .padding(Padding::new(64.0, FableSpacing::SPACING300, FableSpacing::SPACING300, 96.0)) // Optimized top padding for better title positioning
        }
        .alignment(Alignment::StartTop)

    }
}
