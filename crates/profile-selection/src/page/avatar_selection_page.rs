use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::layout::*;
use ignx_compositron::composables::label::*;
use ignx_compositron::reactive::SignalWith;
use ignx_compositron::{compose, Composer};
use mockall_double::*;
#[double]
use profile_manager::use_profile_manager;

use crate::ui::compound_components::avatar_grid::{AvatarGrid, AvatarGridCaller, AvatarGridProps};

#[Composer]
pub fn AvatarSelectionPage(ctx: &AppContext) -> impl VisualComposable<'static> {
    log::info!("[avatar_selection_page] Avatar selection page start.");

    // Get the current user's profile to use their avatar URL
    let profile_manager = use_profile_manager(ctx.scope());
    let current_user_avatar_url = profile_manager
        .get_active_profile()
        .with(|profile_opt| {
            profile_opt
                .as_ref()
                .map(|profile| profile.avatar.avatarUrl.clone())
                .unwrap_or_else(|| "https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=User".to_string())
        });

    compose! {
        Column() {
            // Main title "Change your profile image"
            Label(text: "Change your profile image")
                .color(Color::white())
                .test_id("page-title")

            // First avatar grid - The Summer I Turned Pretty
            AvatarGrid(
                category_name: "The Summer I Turned Pretty",
                current_user_avatar_url: &current_user_avatar_url
            )

            // Second avatar grid - The Boys
            AvatarGrid(
                category_name: "The Boys",
                current_user_avatar_url: &current_user_avatar_url
            )

            // Third avatar grid - Jack Ryan
            AvatarGrid(
                category_name: "Jack Ryan",
                current_user_avatar_url: &current_user_avatar_url
            )
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(20.0))

    }
}
