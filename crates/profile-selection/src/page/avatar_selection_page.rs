use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::layout::*;
use ignx_compositron::label::{Label, LabelProps};
use ignx_compositron::{compose, Composer, LabelCaller};
use router::hooks::{use_location, use_navigation};

use crate::ui::compound_components::avatar_grid::{AvatarGrid, AvatarGridCaller, AvatarGridProps};

/// Test ID for avatar selection page
const AVATAR_SELECTION_PAGE_TEST_ID: &str = "avatar-selection-page";

/// Component 1: Main Page Component
#[Composer]
pub fn AvatarSelectionPage(ctx: &AppContext) -> impl VisualComposable<'static> {
    log::info!("[avatar_selection_page] Avatar selection page start.");
    
    // Get location and extract parameters
    let _location = use_location(ctx.scope());

    // Navigation hook
    let _navigate = use_navigation(ctx.scope());

    // Mock error state for demonstration (set to false to show normal view, true to show error)
    let _show_error = false;

    // Retry handler for the error view
    let _handle_retry = move || {
        log::info!("[avatar_selection_page] Retry button clicked, would reload data");
        // In a real implementation, we would attempt to reload data or retry the failed operation
    };
    
    compose! {
        Column() {
            // Main title "Change your profile image"
            Label(text: "Change your profile image")
                .color(Color::white())
                .test_id("page-title")
            
            // First avatar grid - The Summer I Turned Pretty
            AvatarGrid(
                category_name: "The Summer I Turned Pretty"
            )

            // Second avatar grid - The Boys
            AvatarGrid(
                category_name: "The Boys"
            )

            // Third avatar grid - Jack Ryan
            AvatarGrid(
                category_name: "Jack Ryan"
            )
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(20.0))
    
    }
}
