use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::layout::*;
use ignx_compositron::composables::label::*;
use ignx_compositron::{compose, Composer};

use crate::ui::compound_components::avatar_grid::{AvatarGrid, AvatarGridCaller, AvatarGridProps};

#[Composer]
pub fn AvatarSelectionPage(ctx: &AppContext) -> impl VisualComposable<'static> {
    log::info!("[avatar_selection_page] Avatar selection page start.");
    
    compose! {
        Column() {
            // Main title "Change your profile image"
            Label(text: "Change your profile image")
                .color(Color::white())
                .test_id("page-title")
            
            // First avatar grid - The Summer I Turned Pretty
            AvatarGrid(
                category_name: "The Summer I Turned Pretty"
            )

            // Second avatar grid - The Boys
            AvatarGrid(
                category_name: "The Boys"
            )

            // Third avatar grid - <PERSON>(
                category_name: "<PERSON>"
            )
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(20.0))
    
    }
}
