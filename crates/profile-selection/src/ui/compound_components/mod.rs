/// Composer that displays the "New Profile" UI component ("avatar" image and text)
pub mod new_profile;

/// Composer that displays the Profile UI component (profile avatar, text)
pub mod profile;

/// Composer that displays the Profile Avatar UI components (avatar image, lock icon)
pub mod profile_avatar;

/// Composer that displays a row-list of profile UI components, and potentially a "new profile" UI component
pub mod profiles_row;

/// Composer that displays the v2 Profile Avatar UI components (avatar image, lock icon)
pub mod profile_avatar_v2;

/// Composer that displays a column-list of v2 profile UI components, and potentially a "new profile" UI component
pub mod profiles_vertical;

/// Composer that displays the v2 Profile UI component (profile avatar, text)
pub mod profile_v2;

/// Composer that displays the v2 "New Profile" UI component ("avatar" image and text)
pub mod new_profile_v2;

/// Composer that displays the PIN entry UI.
pub mod pin_entry;

pub mod verifying_pin;

pub mod pin_dialog;

pub(crate) mod ambiance_layers;

pub(crate) mod background;


