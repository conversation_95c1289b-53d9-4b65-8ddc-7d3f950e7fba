use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::row::*;
use ignx_compositron::{compose, Composer};
use ignx_compositron::composables::label::*;

/// Test ID for avatar grid component
pub const AVATAR_GRID_TEST_ID: &str = "avatar-grid";

/// Component 2: Avatar Grid Component
#[Composer]
pub fn AvatarGrid(
    ctx: &AppContext,
    category_name: &str,
) -> impl VisualComposable<'static> {
    compose! {
        Column() {
            // Category title
            Label(text: category_name)
                .color(Color::white())
                .test_id("category-title")
            
            // Horizontal row of avatars
            Row() {
                // Use named parameters when calling AvatarGridItem
                AvatarGridItem(
                    ctx: ctx,
                    avatar_id: "avatar1"
                )
                .test_id("avatar-item-1")
                
                AvatarGridItem(
                    ctx: ctx,
                    avatar_id: "avatar2"
                )
                .test_id("avatar-item-2")
                
                AvatarGridItem(
                    ctx: ctx,
                    avatar_id: "avatar3"
                )
                .test_id("avatar-item-3")
                
                AvatarGridItem(
                    ctx: ctx,
                    avatar_id: "avatar4"
                )
                .test_id("avatar-item-4")
            }
            .spacing(20.0)
        }
        .spacing(15.0)
        .test_id(AVATAR_GRID_TEST_ID)
    }
}