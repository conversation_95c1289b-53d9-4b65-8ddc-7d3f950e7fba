use std::rc::Rc;

use amzn_fable_tokens::FableSpacing;
use fableous::buttons::button_list::*;
use fableous::buttons::scrolling_list::{Dimensions, Direction};
use fableous::buttons::secondary_button::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::id::Id;
use ignx_compositron::layout::*;
use ignx_compositron::list::*;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, Composer};

/// Test ID for avatar grid component
pub const AVATAR_GRID_TEST_ID: &str = "avatar-grid";

/// Avatar data structure
#[derive(Clone)]
pub struct Avatar {
    pub id: String,
    pub name: String,
    pub image_url: Option<String>,
}

/// Avatar row wrapper for ColumnList
#[derive(Clone)]
pub struct AvatarRow {
    pub id: String,
    pub avatars: Vec<Avatar>,
}

impl Id for AvatarRow {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Component: Avatar Grid Component
/// Creates a grid of avatar buttons using ButtonList and ColumnList
#[Composer]
pub fn AvatarGrid(
    ctx: &AppContext,
    category_name: &str,
) -> impl VisualComposable<'static> {
    // Mock avatar data for the category
    let avatars = create_avatar_data_for_category(category_name);

    // Create signal for selected avatar
    let selected_avatar_id = create_rw_signal(ctx.scope(), None::<String>);

    // Create rows of avatar buttons (4 avatars per row)
    let avatar_rows: Vec<AvatarRow> = avatars.chunks(4).enumerate().map(|(idx, chunk)| {
        AvatarRow {
            id: format!("row-{}", idx),
            avatars: chunk.to_vec(),
        }
    }).collect();
    let avatar_rows_signal = create_rw_signal(ctx.scope(), avatar_rows);

    compose! {
        Column() {
            // Category title
            Label(text: category_name)
                .color(Color::white())
                .test_id("category-title")

            // Grid of avatar buttons using ColumnList for rows
            ColumnList(
                items: avatar_rows_signal.read_only(),
                id: |row| &row.id,
                item_builder: Rc::new({
                    let selected_avatar_id = selected_avatar_id;
                    move |ctx: &AppContext, row: &AvatarRow, row_idx: usize| {
                        // Create buttons for this row
                        let buttons: Vec<ButtonListButton> = row.avatars.iter().map(|avatar| {
                            ButtonListButton::Secondary(
                                SecondaryButtonVariant::TextSize200(TextContent::String(avatar.name.clone())),
                                avatar.id.clone()
                            )
                        }).collect();

                        let buttons_signal = create_rw_signal(ctx.scope(), buttons);

                        compose! {
                            ButtonList(
                                direction: Direction::Horizontal,
                                buttons: buttons_signal.read_only(),
                                spacing: FableSpacing::SPACING100,
                                scroll_padding: 0.0,
                                dimensions: Dimensions { height: 60.0, width: 800.0 },
                                on_item_selected: Rc::new({
                                    let selected_avatar_id = selected_avatar_id;
                                    let avatars = row.avatars.clone();
                                    move |idx: usize| {
                                        if let Some(avatar) = avatars.get(idx) {
                                            selected_avatar_id.set(Some(avatar.id.clone()));
                                            log::info!("[avatar_grid] Selected avatar: {}", avatar.id);
                                        }
                                    }
                                })
                            )
                            .test_id(format!("avatar-row-{}", row_idx))
                        }
                    }
                })
            )
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
        }
        .test_id(AVATAR_GRID_TEST_ID)
    }
}

/// Helper function to create mock avatar data for a category
fn create_avatar_data_for_category(category_name: &str) -> Vec<Avatar> {
    let base_avatars = match category_name {
        "The Summer I Turned Pretty" => vec![
            ("belly", "Belly"),
            ("conrad", "Conrad"),
            ("jeremiah", "Jeremiah"),
            ("taylor", "Taylor"),
        ],
        "The Boys" => vec![
            ("homelander", "Homelander"),
            ("starlight", "Starlight"),
            ("butcher", "Butcher"),
            ("hughie", "Hughie"),
        ],
        "Jack Ryan" => vec![
            ("jack", "Jack Ryan"),
            ("greer", "Greer"),
            ("cathy", "Cathy"),
            ("wright", "Wright"),
        ],
        _ => vec![
            ("default1", "Avatar 1"),
            ("default2", "Avatar 2"),
            ("default3", "Avatar 3"),
            ("default4", "Avatar 4"),
        ],
    };

    base_avatars.into_iter().map(|(id, name)| Avatar {
        id: format!("{}_{}", category_name.to_lowercase().replace(" ", "_"), id),
        name: name.to_string(),
        image_url: None, // TODO: Add actual image URLs
    }).collect()
}