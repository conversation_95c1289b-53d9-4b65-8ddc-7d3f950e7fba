use std::rc::Rc;

use amzn_fable_tokens::{FableSpacing, FableBorder, FableColor};
use fableous::typography::typography::{TypographyBody200, TypographyBody200Caller, TypographyBody200Props};
use fableous::utils::get_ignx_color;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::id::Id;
use ignx_compositron::layout::*;
use ignx_compositron::list::*;
use ignx_compositron::prelude::*;
use containers::utils::list_fvs::{create_list_focus_value_signal, ListFocusValueSignal};



use ignx_compositron::{compose, Composer};

use crate::ui::basic_components::avatar_grid_item::{AvatarGridItem, AvatarGridItemCaller, AvatarGridItemProps};

/// Test ID for avatar grid component
pub const AVATAR_GRID_TEST_ID: &str = "avatar-grid";

/// Avatar data structure
#[derive(Clone)]
pub struct Avatar {
    pub id: String,
    pub name: String,
    pub image_url: Option<String>,
}

impl Id for Avatar {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Avatar row wrapper for ColumnList
#[derive(Clone)]
pub struct AvatarRow {
    pub id: String,
    pub avatars: Vec<Avatar>,
}

impl Id for AvatarRow {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Component: Avatar Grid Component
/// Creates a focusable grid of avatars using RowList for focus management
#[Composer]
pub fn AvatarGrid(
    ctx: &AppContext,
    category_name: &str,
    current_user_avatar_url: &str,
) -> impl VisualComposable<'static> {
    // Mock avatar data for the category using the current user's avatar URL
    let avatars = create_avatar_data_for_category(category_name, current_user_avatar_url);
    let avatars_signal = create_rw_signal(ctx.scope(), avatars);

    // Create focus signals for the grid using ListFocusValueSignal
    let list_focus_signal = create_list_focus_value_signal(ctx.scope(), avatars_signal.into());
    let focus_namespace = create_namespace(ctx.scope(), "avatar-grid-focus");

    // Focus the first avatar by default
    list_focus_signal.focus_first();

    compose! {
        Column() {
            // Category title with smaller typography
            TypographyBody200(content: category_name, color: Color::white())
                .test_id("category-title")
                .padding(Padding::new(0.0, 0.0, FableSpacing::SPACING150, 0.0)) // Add bottom margin to match JS header spacing (24px)

            // Focusable grid of avatars using RowList for horizontal navigation
            Stack() {
                RowList(
                    items: avatars_signal.read_only(),
                    id: |avatar| &avatar.id,
                    item_builder: Rc::new({
                        let list_focus_signal = list_focus_signal.clone();
                        move |ctx: &AppContext, avatar: &Avatar, idx: usize| {
                            let avatar_url = avatar.image_url.as_ref()
                                .map(|s| s.as_str())
                                .unwrap_or("https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=Avatar");

                            compose! {
                                AvatarGridItem(
                                    avatar_url: avatar_url
                                )
                                .focused_value(list_focus_signal.signal(), avatar.id.clone())
                                .test_id(format!("avatar-item-{}", idx))
                            }
                        }
                    })
                )
                .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100)) // 16px spacing between items
                .focus_area(FocusAreaEntryMode::Memo) // Enable focus management
                .test_id(format!("{}-row", AVATAR_GRID_TEST_ID))

                // Focus ring - shows when an avatar is focused
                if list_focus_signal.signal().get().is_some() {
                    Rectangle()
                        .matched_geometry(focus_namespace, false, true)
                        .border_color(get_ignx_color(FableColor::PRIMARY))
                        .border_width(FableBorder::FOCUSED_WIDTH)
                        .border_radius(20.0) // Match avatar border radius
                        .test_id("avatar-focus-ring")
                }
            }
        }
        .test_id(AVATAR_GRID_TEST_ID)
    }
}

/// Helper function to create mock avatar data for a category
fn create_avatar_data_for_category(category_name: &str, current_user_avatar_url: &str) -> Vec<Avatar> {
    let base_avatars = match category_name {
        "The Summer I Turned Pretty" => vec![
            ("belly", "Belly"),
            ("conrad", "Conrad"),
            ("jeremiah", "Jeremiah"),
            ("taylor", "Taylor"),
        ],
        "The Boys" => vec![
            ("homelander", "Homelander"),
            ("starlight", "Starlight"),
            ("butcher", "Butcher"),
            ("hughie", "Hughie"),
        ],
        "Jack Ryan" => vec![
            ("jack", "Jack Ryan"),
            ("greer", "Greer"),
            ("cathy", "Cathy"),
            ("wright", "Wright"),
        ],
        _ => vec![
            ("default1", "Avatar 1"),
            ("default2", "Avatar 2"),
            ("default3", "Avatar 3"),
            ("default4", "Avatar 4"),
        ],
    };

    // Use the current user's avatar URL for all avatar options
    base_avatars.into_iter().map(|(id, name)| Avatar {
        id: format!("{}_{}", category_name.to_lowercase().replace(" ", "_"), id),
        name: name.to_string(),
        image_url: Some(current_user_avatar_url.to_string()),
    }).collect()
}