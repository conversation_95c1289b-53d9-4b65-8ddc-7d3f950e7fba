use std::rc::Rc;

use amzn_fable_tokens::FableSpacing;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::id::Id;
use ignx_compositron::layout::*;
use ignx_compositron::list::*;
use ignx_compositron::prelude::*;

use ignx_compositron::{compose, Composer};

use crate::ui::basic_components::avatar_grid_item::{AvatarGridItem, AvatarGridItemCaller, AvatarGridItemProps};

/// Test ID for avatar grid component
pub const AVATAR_GRID_TEST_ID: &str = "avatar-grid";

/// Avatar data structure
#[derive(Clone)]
pub struct Avatar {
    pub id: String,
    pub name: String,
    pub image_url: Option<String>,
}

impl Id for Avatar {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Avatar row wrapper for ColumnList
#[derive(Clone)]
pub struct AvatarRow {
    pub id: String,
    pub avatars: Vec<Avatar>,
}

impl Id for AvatarRow {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Component: Avatar Grid Component
/// Creates a grid of avatars using ColumnList for rows
#[Composer]
pub fn AvatarGrid(
    ctx: &AppContext,
    category_name: &str,
) -> impl VisualComposable<'static> {
    // Mock avatar data for the category
    let avatars = create_avatar_data_for_category(category_name);

    // Create rows of avatars (4 avatars per row)
    let avatar_rows: Vec<AvatarRow> = avatars.chunks(4).enumerate().map(|(idx, chunk)| {
        AvatarRow {
            id: format!("row-{}", idx),
            avatars: chunk.to_vec(),
        }
    }).collect();
    let avatar_rows_signal = create_rw_signal(ctx.scope(), avatar_rows);

    compose! {
        Column() {
            // Category title
            Label(text: category_name)
                .color(Color::white())
                .test_id("category-title")

            // Grid of avatars using ColumnList for rows
            ColumnList(
                items: avatar_rows_signal.read_only(),
                id: |row| &row.id,
                item_builder: Rc::new({
                    move |ctx: &AppContext, row: &AvatarRow, row_idx: usize| {
                        // Get avatar URLs with fallbacks
                        let avatar_0_url = row.avatars.get(0)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=1");
                        let avatar_1_url = row.avatars.get(1)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/4ECDC4/FFFFFF?text=2");
                        let avatar_2_url = row.avatars.get(2)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/45B7D1/FFFFFF?text=3");
                        let avatar_3_url = row.avatars.get(3)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/96CEB4/FFFFFF?text=4");

                        compose! {
                            Row() {
                                // Create avatar items for this row using actual avatar data
                                AvatarGridItem(
                                    avatar_url: avatar_0_url
                                )
                                .test_id(format!("avatar-item-{}-0", row_idx))

                                AvatarGridItem(
                                    avatar_url: avatar_1_url
                                )
                                .test_id(format!("avatar-item-{}-1", row_idx))

                                AvatarGridItem(
                                    avatar_url: avatar_2_url
                                )
                                .test_id(format!("avatar-item-{}-2", row_idx))

                                AvatarGridItem(
                                    avatar_url: avatar_3_url
                                )
                                .test_id(format!("avatar-item-{}-3", row_idx))
                            }
                            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
                            .test_id(format!("avatar-row-{}", row_idx))
                        }
                    }
                })
            )
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
        }
        .test_id(AVATAR_GRID_TEST_ID)
    }
}

/// Helper function to create mock avatar data for a category
fn create_avatar_data_for_category(category_name: &str) -> Vec<Avatar> {
    let base_avatars = match category_name {
        "The Summer I Turned Pretty" => vec![
            ("belly", "Belly", "https://m.media-amazon.com/images/S/pv-target-images/6c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c8c._UR204,204_FMjpg_.jpg"),
            ("conrad", "Conrad", "https://m.media-amazon.com/images/S/pv-target-images/7d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d9d._UR204,204_FMjpg_.jpg"),
            ("jeremiah", "Jeremiah", "https://m.media-amazon.com/images/S/pv-target-images/8e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e0e._UR204,204_FMjpg_.jpg"),
            ("taylor", "Taylor", "https://m.media-amazon.com/images/S/pv-target-images/9f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f1f._UR204,204_FMjpg_.jpg"),
        ],
        "The Boys" => vec![
            ("homelander", "Homelander", "https://m.media-amazon.com/images/S/pv-target-images/a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2a2._UR204,204_FMjpg_.jpg"),
            ("starlight", "Starlight", "https://m.media-amazon.com/images/S/pv-target-images/b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3b3._UR204,204_FMjpg_.jpg"),
            ("butcher", "Butcher", "https://m.media-amazon.com/images/S/pv-target-images/c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4c4._UR204,204_FMjpg_.jpg"),
            ("hughie", "Hughie", "https://m.media-amazon.com/images/S/pv-target-images/d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5d5._UR204,204_FMjpg_.jpg"),
        ],
        "Jack Ryan" => vec![
            ("jack", "Jack Ryan", "https://m.media-amazon.com/images/S/pv-target-images/e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6e6._UR204,204_FMjpg_.jpg"),
            ("greer", "Greer", "https://m.media-amazon.com/images/S/pv-target-images/f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7f7._UR204,204_FMjpg_.jpg"),
            ("cathy", "Cathy", "https://m.media-amazon.com/images/S/pv-target-images/a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8a8._UR204,204_FMjpg_.jpg"),
            ("wright", "Wright", "https://m.media-amazon.com/images/S/pv-target-images/b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9b9._UR204,204_FMjpg_.jpg"),
        ],
        _ => vec![
            ("default1", "Avatar 1", "https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=1"),
            ("default2", "Avatar 2", "https://via.placeholder.com/204x204/4ECDC4/FFFFFF?text=2"),
            ("default3", "Avatar 3", "https://via.placeholder.com/204x204/45B7D1/FFFFFF?text=3"),
            ("default4", "Avatar 4", "https://via.placeholder.com/204x204/96CEB4/FFFFFF?text=4"),
        ],
    };

    base_avatars.into_iter().map(|(id, name, image_url)| Avatar {
        id: format!("{}_{}", category_name.to_lowercase().replace(" ", "_"), id),
        name: name.to_string(),
        image_url: Some(image_url.to_string()),
    }).collect()
}