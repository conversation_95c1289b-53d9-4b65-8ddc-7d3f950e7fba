use std::rc::Rc;

use amzn_fable_tokens::FableSpacing;
use fableous::typography::typography::{TypographyBody200, TypographyBody200Caller, TypographyBody200Props};
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::id::Id;
use ignx_compositron::layout::*;
use ignx_compositron::list::*;
use ignx_compositron::prelude::*;

use ignx_compositron::{compose, Composer};

use crate::ui::basic_components::avatar_grid_item::{AvatarGridItem, AvatarGridItemCaller, AvatarGridItemProps};

/// Test ID for avatar grid component
pub const AVATAR_GRID_TEST_ID: &str = "avatar-grid";

/// Avatar data structure
#[derive(Clone)]
pub struct Avatar {
    pub id: String,
    pub name: String,
    pub image_url: Option<String>,
}

impl Id for Avatar {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Avatar row wrapper for ColumnList
#[derive(Clone)]
pub struct AvatarRow {
    pub id: String,
    pub avatars: Vec<Avatar>,
}

impl Id for AvatarRow {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Component: Avatar Grid Component
/// Creates a grid of avatars using ColumnList for rows
#[Composer]
pub fn AvatarGrid(
    ctx: &AppContext,
    category_name: &str,
    current_user_avatar_url: &str,
) -> impl VisualComposable<'static> {
    // Mock avatar data for the category using the current user's avatar URL
    let avatars = create_avatar_data_for_category(category_name, current_user_avatar_url);

    // Create rows of avatars (4 avatars per row)
    let avatar_rows: Vec<AvatarRow> = avatars.chunks(4).enumerate().map(|(idx, chunk)| {
        AvatarRow {
            id: format!("row-{}", idx),
            avatars: chunk.to_vec(),
        }
    }).collect();
    let avatar_rows_signal = create_rw_signal(ctx.scope(), avatar_rows);

    compose! {
        Column() {
            // Category title with smaller typography
            TypographyBody200(content: category_name, color: Color::white())
                .test_id("category-title")

            // Grid of avatars using ColumnList for rows
            ColumnList(
                items: avatar_rows_signal.read_only(),
                id: |row| &row.id,
                item_builder: Rc::new({
                    move |ctx: &AppContext, row: &AvatarRow, row_idx: usize| {
                        // Get avatar URLs from the avatar data (which now contains the current user's avatar URL)
                        let avatar_0_url = row.avatars.get(0)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=1");
                        let avatar_1_url = row.avatars.get(1)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/4ECDC4/FFFFFF?text=2");
                        let avatar_2_url = row.avatars.get(2)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/45B7D1/FFFFFF?text=3");
                        let avatar_3_url = row.avatars.get(3)
                            .and_then(|a| a.image_url.as_ref())
                            .map(|s| s.as_str())
                            .unwrap_or("https://via.placeholder.com/204x204/96CEB4/FFFFFF?text=4");

                        compose! {
                            Row() {
                                // Create avatar items for this row using actual avatar data
                                AvatarGridItem(
                                    avatar_url: avatar_0_url
                                )
                                .test_id(format!("avatar-item-{}-0", row_idx))

                                AvatarGridItem(
                                    avatar_url: avatar_1_url
                                )
                                .test_id(format!("avatar-item-{}-1", row_idx))

                                AvatarGridItem(
                                    avatar_url: avatar_2_url
                                )
                                .test_id(format!("avatar-item-{}-2", row_idx))

                                AvatarGridItem(
                                    avatar_url: avatar_3_url
                                )
                                .test_id(format!("avatar-item-{}-3", row_idx))
                            }
                            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
                            .test_id(format!("avatar-row-{}", row_idx))
                        }
                    }
                })
            )
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
        }
        .test_id(AVATAR_GRID_TEST_ID)
    }
}

/// Helper function to create mock avatar data for a category
fn create_avatar_data_for_category(category_name: &str, current_user_avatar_url: &str) -> Vec<Avatar> {
    let base_avatars = match category_name {
        "The Summer I Turned Pretty" => vec![
            ("belly", "Belly"),
            ("conrad", "Conrad"),
            ("jeremiah", "Jeremiah"),
            ("taylor", "Taylor"),
        ],
        "The Boys" => vec![
            ("homelander", "Homelander"),
            ("starlight", "Starlight"),
            ("butcher", "Butcher"),
            ("hughie", "Hughie"),
        ],
        "Jack Ryan" => vec![
            ("jack", "Jack Ryan"),
            ("greer", "Greer"),
            ("cathy", "Cathy"),
            ("wright", "Wright"),
        ],
        _ => vec![
            ("default1", "Avatar 1"),
            ("default2", "Avatar 2"),
            ("default3", "Avatar 3"),
            ("default4", "Avatar 4"),
        ],
    };

    // Use the current user's avatar URL for all avatar options
    base_avatars.into_iter().map(|(id, name)| Avatar {
        id: format!("{}_{}", category_name.to_lowercase().replace(" ", "_"), id),
        name: name.to_string(),
        image_url: Some(current_user_avatar_url.to_string()),
    }).collect()
}