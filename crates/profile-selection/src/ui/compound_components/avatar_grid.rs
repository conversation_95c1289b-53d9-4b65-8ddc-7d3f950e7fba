use std::rc::Rc;

use amzn_fable_tokens::{FableSpacing, FableBorder, FableColor};
use fableous::typography::typography::{TypographyBody200, TypographyBody200Caller, TypographyBody200Props};
use fableous::utils::get_ignx_color;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::id::Id;
use ignx_compositron::layout::*;
use ignx_compositron::list::*;
use ignx_compositron::prelude::*;




use ignx_compositron::{compose, Composer};

use crate::ui::basic_components::avatar_grid_item::{AvatarGridItem, AvatarGridItemCaller, AvatarGridItemProps};

/// Test ID for avatar grid component
pub const AVATAR_GRID_TEST_ID: &str = "avatar-grid";

/// Avatar data structure
#[derive(Clone)]
pub struct Avatar {
    pub id: String,
    pub name: String,
    pub image_url: Option<String>,
}

impl Id for Avatar {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Avatar row wrapper for ColumnList
#[derive(Clone)]
pub struct AvatarRow {
    pub id: String,
    pub avatars: Vec<Avatar>,
}

impl Id for AvatarRow {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Component: Avatar Grid Component
/// Creates a focusable grid of avatars using ColumnList containing RowList components
#[Composer]
pub fn AvatarGrid(
    ctx: &AppContext,
    category_name: &str,
    current_user_avatar_url: &str,
    #[optional] _on_avatar_select: Option<Box<dyn Fn(Avatar) + 'static>>,
) -> impl VisualComposable<'static> {
    // Mock avatar data for the category using the current user's avatar URL
    let avatars = create_avatar_data_for_category(category_name, current_user_avatar_url);
    let avatars_signal = create_rw_signal(ctx.scope(), avatars);

    // Configuration for the grid
    const AVATARS_PER_ROW: usize = 6;

    // Create focus signals for the grid
    let grid_focused = create_focus_signal(ctx.scope());
    let focus_namespace = create_namespace(ctx.scope(), "avatar-grid-focus");

    // Track which avatar is currently focused (for content scrolling)
    let focused_avatar_index = create_rw_signal(ctx.scope(), 0usize); // Start with first avatar

    // The focus ring always stays at position (0, 0) - first row, first column
    let focus_ring_position = (0usize, 0usize); // (row, column)

    compose! {
        Column() {
            // Category title with smaller typography
            TypographyBody200(content: category_name, color: Color::white())
                .test_id("category-title")
                .padding(Padding::new(0.0, 0.0, FableSpacing::SPACING150, 0.0)) // Add bottom margin to match JS header spacing (24px)

            // Grid structure: ColumnList containing RowList components with focus ring overlay
            Stack() {
                Memo(item_builder: Box::new({
                    let focused_avatar_index = focused_avatar_index.clone();
                    let focus_namespace = focus_namespace.clone();
                    move |ctx| {
                        let avatars = avatars_signal.get();

                        // Chunk avatars into rows with IDs
                        let rows: Vec<(Vec<Avatar>, String)> = avatars
                            .chunks(AVATARS_PER_ROW)
                            .enumerate()
                            .map(|(idx, chunk)| (chunk.to_vec(), format!("row-{}", idx)))
                            .collect();

                        if rows.is_empty() {
                            return None;
                        }

                        Some(compose! {
                            ColumnList(
                                items: rows,
                                id: |(_, row_id)| row_id,
                                item_builder: Rc::new({
                                    let focused_avatar_index = focused_avatar_index.clone();
                                    let focus_namespace = focus_namespace.clone();
                                    move |ctx: &AppContext, (row, _): &(Vec<Avatar>, String), row_idx: usize| {
                                        compose! {
                                            RowList(
                                                items: row.clone(),
                                                id: |avatar| &avatar.id,
                                                item_builder: Rc::new({
                                                    let focused_avatar_index = focused_avatar_index.clone();
                                                    let focus_namespace = focus_namespace.clone();
                                                    move |ctx: &AppContext, avatar: &Avatar, col_idx: usize| {
                                                        let avatar_url = avatar.image_url.as_ref()
                                                            .map(|s| s.as_str())
                                                            .unwrap_or("https://via.placeholder.com/204x204/FF6B6B/FFFFFF?text=Avatar");

                                                        // Calculate global avatar index
                                                        let global_idx = row_idx * AVATARS_PER_ROW + col_idx;

                                                        // Create focus signal for this avatar
                                                        let avatar_focused = create_focus_signal(ctx.scope());

                                                        // Only the avatar at the focus ring position gets focus and matched geometry
                                                        let is_at_focus_position = row_idx == focus_ring_position.0 && col_idx == focus_ring_position.1;

                                                        let is_focused = Signal::derive(ctx.scope(), {
                                                            let focused_avatar_index = focused_avatar_index.clone();
                                                            move || {
                                                                let is_at_focus_position = row_idx == focus_ring_position.0 && col_idx == focus_ring_position.1;
                                                                let is_focused_avatar = focused_avatar_index.get() == global_idx;
                                                                is_at_focus_position && is_focused_avatar
                                                            }
                                                        });

                                                        // Update focus signal when this avatar should be focused
                                                        create_effect(ctx.scope(), {
                                                            let avatar_focused = avatar_focused.clone();
                                                            move |_| {
                                                                avatar_focused.set(is_focused.get());
                                                            }
                                                        });

                                                        compose! {
                                                            AvatarGridItem(
                                                                avatar_url: avatar_url,
                                                                focus_namespace: if is_at_focus_position { Some(focus_namespace) } else { None },
                                                                is_focused: Some(is_focused)
                                                            )
                                                            .focused(avatar_focused.clone())
                                                            .focusable()
                                                            .on_focus({
                                                                let focused_avatar_index = focused_avatar_index.clone();
                                                                move || {
                                                                    focused_avatar_index.set(global_idx);
                                                                }
                                                            })
                                                            .test_id(format!("avatar-item-{}-{}", row_idx, col_idx))
                                                        }
                                                    }
                                                })
                                            )
                                            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100)) // 16px spacing between avatars
                                            .focus_area(FocusAreaEntryMode::Closest)
                                            .test_id(format!("{}-row-{}", AVATAR_GRID_TEST_ID, row_idx))
                                        }
                                    }
                                })
                            )
                            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100)) // 16px spacing between rows
                            .focused(grid_focused)
                            .test_id(format!("{}-column-list", AVATAR_GRID_TEST_ID))
                        })
                    }
                }))

                // Focus ring - always shows at the fixed focus position (0, 0)
                Rectangle()
                    .matched_geometry(focus_namespace, false, true)
                    .border_color(get_ignx_color(FableColor::PRIMARY))
                    .border_width(FableBorder::FOCUSED_WIDTH)
                    .border_radius(20.0) // Match avatar border radius
                    .test_id("avatar-focus-ring")
            }
        }
        .test_id(AVATAR_GRID_TEST_ID)
    }
}

/// Helper function to create mock avatar data for a category
fn create_avatar_data_for_category(category_name: &str, current_user_avatar_url: &str) -> Vec<Avatar> {
    let base_avatars = match category_name {
        "The Summer I Turned Pretty" => vec![
            ("belly", "Belly"),
            ("conrad", "Conrad"),
            ("jeremiah", "Jeremiah"),
            ("taylor", "Taylor"),
        ],
        "The Boys" => vec![
            ("homelander", "Homelander"),
            ("starlight", "Starlight"),
            ("butcher", "Butcher"),
            ("hughie", "Hughie"),
        ],
        "Jack Ryan" => vec![
            ("jack", "Jack Ryan"),
            ("greer", "Greer"),
            ("cathy", "Cathy"),
            ("wright", "Wright"),
        ],
        _ => vec![
            ("default1", "Avatar 1"),
            ("default2", "Avatar 2"),
            ("default3", "Avatar 3"),
            ("default4", "Avatar 4"),
        ],
    };

    // Use the current user's avatar URL for all avatar options
    base_avatars.into_iter().map(|(id, name)| Avatar {
        id: format!("{}_{}", category_name.to_lowercase().replace(" ", "_"), id),
        name: name.to_string(),
        image_url: Some(current_user_avatar_url.to_string()),
    }).collect()
}