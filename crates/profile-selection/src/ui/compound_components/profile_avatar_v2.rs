use amzn_fable_tokens::FableBorder;
use amzn_fable_tokens::FableColor;
use amzn_fable_tokens::FableIcon;
use fableous::font_icon::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;

use crate::constants::profiles_v2_constants::FOCUSED_AVATAR_RADIUS;

#[Composer]
pub fn ProfileAvatarPlaceholer(
    ctx: &AppContext,
    #[into] avatar_radius: MaybeSignal<f32>,
) -> StackComposable {
    let side = Signal::derive(ctx.scope, move || avatar_radius.get() * 2.0);
    compose! {
        Stack(){
            Rectangle()
            .background_color(get_ignx_color(FableColor::COOL500))
            .width(side)
            .height(side)
            .border_radius(0.0)  // Changed from avatar_radius to 0.0 for square avatars
            .opacity(0.2)
        }
        .alignment(Alignment::Center)
    }
}

#[Composer]
pub fn ProfileAvatarImageV2(
    ctx: &AppContext,
    base_image_radius: f32,
    #[into] url: MaybeSignal<String>,
    #[into] avatar_radius: MaybeSignal<f32>,
) -> ImageComposable {
    let side = Signal::derive(ctx.scope, move || avatar_radius.get() * 2.0);
    compose! {
        LRCImage(data: ImageData{
            url: url.get_untracked(),
            height: base_image_radius * 2.0,
            width: base_image_radius * 2.0,
            tags: vec![]
        })
        .width(side)
        .height(side)
        .border_radius(0.0)  // Changed from avatar_radius to 0.0 for square avatars
    }
}

#[Composer]
pub fn NewProfileAvatarV2(
    ctx: &AppContext,
    #[into] avatar_radius: MaybeSignal<f32>,
) -> StackComposable {
    let side = Signal::derive(ctx.scope, move || avatar_radius.get() * 2.0);
    let add_icon = FableIcon::ADD;
    let add_icon_downscale_factor = Signal::derive(ctx.scope, move || {
        let scale_factor = avatar_radius.get() / FOCUSED_AVATAR_RADIUS;
        Vec2::new(scale_factor, scale_factor)
    });
    compose! {
        Stack(){
            Rectangle()
            .background_color(get_ignx_color(FableColor::COOL500))
            .width(side)
            .height(side)
            .border_radius(0.0)  // Changed from avatar_radius to 0.0 for square avatars
            FontIcon(icon: add_icon, color: get_ignx_color(FableColor::WARM200), size: FontSize(60)).scale(add_icon_downscale_factor)
        }
        .alignment(Alignment::Center)
    }
}

#[Composer]
pub fn ProfileAvatarFocusRing(
    ctx: &AppContext,
    #[into] radius: MaybeSignal<f32>,
    #[into] opacity: MaybeSignal<f32>,
) -> RectangleComposable {
    let side = Signal::derive(ctx.scope, move || radius.get() * 2.0);
    compose! {
        Rectangle()
        .width(side)
        .height(side)
        .border_color(get_ignx_color(FableColor::PRIMARY))
        .border_width(FableBorder::RADIUS025)
        .border_radius(0.0)  // Changed from radius to 0.0 for square avatars
        .opacity(opacity)
    }
}
