use crate::ui::basic_components::avatar_image::*;
use crate::ui::basic_components::locked_hint::*;
use crate::ui::basic_components::profile_avatar_placeholder::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::layout::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

const PROFILE_AVATAR_TEST_ID: &str = "PROFILE_AVATAR_TEST_ID";

/// Displays a [`ProfileAvatar()`] on a page
/// Includes the:
///  * [`AvatarImage()`]
///  * [`LockedHint()`]
///
/// Requires a `MaybeSignal<String>` for the avatar url and a `MaybeSignal<bool>` to indicate if a
/// lock icon should show.
///
/// This differs from the [`AvatarImage()`] in that the [`ProfileAvatar()`] contains all the
///     elements needed for an avatar relating to an existing profile, while the former contains
///     only the image.
///
/// ## Example
/// ```no_run
/// use profile_selection::ui::compound_components::profile_avatar::*;
/// # use ignx_compositron::{compose, compose_option, Composer};
/// # use ignx_compositron::app::launch_composable;
/// # use ignx_compositron::color::Color;
/// # use ignx_compositron::reactive::*;
///
/// # launch_composable(|ctx| {
/// let url = create_rw_signal(ctx.scope(), "an image url".to_string());
/// let locked = create_rw_signal(ctx.scope(), false);
///
/// compose! {
///     ProfileAvatar(url, locked);
/// }
/// # })
/// ```
#[Composer]
pub fn ProfileAvatar(
    ctx: &AppContext,
    #[into] url: Signal<String>,
    #[into] locked: Signal<bool>,
) -> StackComposable {
    compose! {
        Stack() {
            Stack() {
                ProfileAvatarPlaceholder()
                AvatarImage(url)
                if locked.get() {
                    LockedHint()
                }
            }
            .alignment(Alignment::EndBottom)
        }
        .height(210.0)
        .width(210.0)
        .border_radius(0.0)  // Changed from 105.0 to 0.0 for square avatars
        .border_width(3.0)
        .alignment(Alignment::Center)
        .test_id(PROFILE_AVATAR_TEST_ID)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::node_properties::NodeTypeProperties;
    use ignx_compositron::test_utils::{
        assert_node_does_not_exist, assert_node_exists, ComposableType,
    };
    use rstest::*;

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn test_profile_avatar(#[case] locked: bool) {
        launch_test(
            move |ctx| {
                let url = create_rw_signal(ctx.scope(), "https://www.image.url/1.jpg".to_string());
                let locked = create_rw_signal(ctx.scope(), locked);
                compose! {
                    ProfileAvatar(url, locked)
                }
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();

                let avatar = tree.find_by_test_id(PROFILE_AVATAR_TEST_ID);
                assert_node_exists!(&avatar);

                // the differences in the top-level component and the wrapper around the children is
                // to enable the gap in the border radius
                let props = avatar.borrow_props();
                assert_eq!(props.base_styles.border_radius, Some(105.0));
                assert_eq!(props.base_styles.border_width, Some(3.0));
                assert_eq!(props.layout.size.width, 210.0);
                assert_eq!(props.layout.size.height, 210.0);
                assert_eq!(props.composable_type, ComposableType::Stack);

                let wrapper = avatar
                    .find_child_with()
                    .composable_type(ComposableType::Stack)
                    .find_first();
                assert_node_exists!(&wrapper);

                let wrapper_props = wrapper.borrow_props();
                assert_eq!(wrapper_props.layout.size.width, 204.0);
                assert_eq!(wrapper_props.layout.size.height, 204.0);
                assert_eq!(wrapper_props.composable_type, ComposableType::Stack);

                let placeholder = avatar
                    .find_any_child_with()
                    .test_id(PROFILE_AVATAR_PLACEHOLDER_TEST_ID)
                    .find_first();
                assert_node_exists!(&placeholder);
                let image = avatar
                    .find_any_child_with()
                    .test_id(AVATAR_IMAGE_TEST_ID)
                    .find_first();
                assert_node_exists!(&image);
                let NodeTypeProperties::Image(ref i) = image.borrow_props().node_type_props else {
                    panic!("Expected an image node");
                };
                let Some(ref i) = i.uri else {
                    panic!("Expected a defined image uri");
                };
                assert!(i.contains("www.image.url/1"));
                assert!(i.contains(".jpg"));
                let hint = avatar
                    .find_any_child_with()
                    .test_id(LOCKED_HINT_TEST_ID)
                    .find_first();
                if locked {
                    assert_node_exists!(&hint);
                } else {
                    assert_node_does_not_exist!(&hint);
                }
            },
        );
    }
}
