use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::color::Color;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use ignx_compositron::rectangle::Rectangle;

/// Test ID for avatar grid item
pub const AVATAR_GRID_ITEM_TEST_ID: &str = "avatar-grid-item";

/// Component: Avatar Grid Item
/// Represents a single selectable avatar in the grid
#[Composer]
pub fn AvatarGridItem(
    ctx: &AppContext,
    avatar_id: &str
) -> impl VisualComposable<'static> {
    // Use the avatar_id to display or track the selected avatar
    let _avatar_id = avatar_id; // Use underscore to avoid unused warning
    
    compose! {
        Rectangle()
            .width(100.0)
            .height(100.0)
            .background_color(Color::yellow()) // Yellow background for demo
            .test_id(AVATAR_GRID_ITEM_TEST_ID)
    }
}