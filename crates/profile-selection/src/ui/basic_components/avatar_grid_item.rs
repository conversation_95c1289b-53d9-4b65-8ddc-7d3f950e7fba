use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

use crate::ui::compound_components::profile_avatar_v2::*;

/// Test ID for avatar grid item
pub const AVATAR_GRID_ITEM_TEST_ID: &str = "avatar-grid-item";

/// Component: Avatar Grid Item
/// Represents a single selectable avatar in the grid
#[Composer]
pub fn AvatarGridItem(
    ctx: &AppContext,
    avatar_url: &str
) -> impl VisualComposable<'static> {
    // Create a signal for the avatar URL
    let url_signal = create_rw_signal(ctx.scope(), avatar_url.to_string());

    compose! {
        ProfileAvatarImageV2(
            url: url_signal.read_only(),
            avatar_radius: 0.0,  // Square avatars for avatar selection
            base_image_radius: 80.0  // Increased to match JS avatar size better
        )
        .width(160.0)  // Match closer to JS 168px width
        .height(160.0) // Match closer to JS 168px height
        .test_id(AVATAR_GRID_ITEM_TEST_ID)
    }
}