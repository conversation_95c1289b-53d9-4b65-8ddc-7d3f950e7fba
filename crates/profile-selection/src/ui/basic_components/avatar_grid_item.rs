use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

use crate::ui::basic_components::avatar_image::{AvatarImage, AvatarImageCaller, AvatarImageProps};

/// Test ID for avatar grid item
pub const AVATAR_GRID_ITEM_TEST_ID: &str = "avatar-grid-item";

/// Component: Avatar Grid Item
/// Represents a single selectable avatar in the grid
#[Composer]
pub fn AvatarGridItem(
    ctx: &AppContext,
    avatar_url: &str
) -> impl VisualComposable<'static> {
    // Create a signal for the avatar URL
    let url_signal = create_rw_signal(ctx.scope(), avatar_url.to_string());

    compose! {
        AvatarImage(url: url_signal.read_only())
            .width(100.0)
            .height(100.0)
            .test_id(AVATAR_GRID_ITEM_TEST_ID)
    }
}