use fableous::typography::typography::*;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::reactive::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::*;

const KID_PROFILE_STATIC_IMAGE_ID: &str = "KID_PROFILE_STATIC_IMAGE_ID";
const KID_PROFILE_STATIC_LABEL_ID: &str = "KID_PROFILE_STATIC_LABEL_ID";

#[Composer]
pub fn KidProfile(ctx: &AppContext) -> ColumnComposable {
    //TODO: After the string translation is available, replace the harcoded string with localized text.
    let kid_profile_lable_text_signal = TextContent::String("Kid profile".to_string());
    //let kid_profile_lable_text_signal = TextContent::LocalizedText(LocalizedText::new(AV_LRC_PROFILES_KID_PROFILE_LABEL));

    compose! {
        Column() {
            LRCImage(data: ImageData {
                url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/profiles/star-icon.png".to_string(),
                height: 160.0,
                width: 160.0,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::ScaleToWidth),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .test_id(KID_PROFILE_STATIC_IMAGE_ID);

            TypographyHeading200(content: MaybeSignalTextContent::TextContent(kid_profile_lable_text_signal.into()))
            .max_width(172.0)
            .max_height(39.0)
            .max_lines(Some(1))
            .test_id(KID_PROFILE_STATIC_LABEL_ID);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::*;
    use rstest::*;

    #[rstest]
    fn test_kid_profile() {
        launch_test(
            move |ctx| {
                compose! {
                    KidProfile()
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                assert_node_exists!(&node);

                let image_node = node
                    .find_any_child_with()
                    .test_id(KID_PROFILE_STATIC_IMAGE_ID)
                    .find_first();
                assert_node_exists!(image_node);

                let label_node = node
                    .find_any_child_with()
                    .test_id(KID_PROFILE_STATIC_LABEL_ID)
                    .find_first();
                assert_node_exists!(label_node);
            },
        );
    }
}
