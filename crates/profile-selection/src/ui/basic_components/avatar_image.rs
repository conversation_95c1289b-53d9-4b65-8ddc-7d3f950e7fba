use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::*;
use ignx_compositron::reactive::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;

pub(crate) const AVATAR_IMAGE_TEST_ID: &str = "AVATAR_IMAGE_TEST_ID";

/// Displays the [`AvatarImage()`] for a profile.
/// Requires a `url` to pass to the [`Image()`] composer.
///
/// ## Example
///
/// ```no_run
/// use profile_selection::ui::basic_components::avatar_image::*;
/// # use ignx_compositron::{compose, compose_option, Composer};
/// # use ignx_compositron::app::launch_composable;
/// # use ignx_compositron::color::Color;
/// # use ignx_compositron::reactive::*;
///
/// # launch_composable(|ctx| {
///
/// let url = create_rw_signal(ctx.scope(), "a-mock-url.jpg".to_string());
///
/// compose! {
///     AvatarImage(url);
/// }
/// # })
/// ```
#[Composer]
pub fn AvatarImage(ctx: &AppContext, #[into] url: Signal<String>) -> ImageComposable {
    let image_data = Signal::derive(ctx.scope(), move || ImageData {
        url: url.get(),
        height: 204.0,
        width: 204.0,
        tags: vec![],
    });

    compose! {
        LRCImage(data: image_data)
            .border_radius(0.0)  // Changed from 102.0 to 0.0 for square avatars
            .border_width(3.0)
            .test_id(AVATAR_IMAGE_TEST_ID)
    }
}
