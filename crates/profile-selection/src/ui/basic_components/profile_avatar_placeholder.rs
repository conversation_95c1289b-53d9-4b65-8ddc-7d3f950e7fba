use ignx_compositron::color::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::rectangle::*;
use ignx_compositron::{compose, Composer};

pub(crate) const PROFILE_AVATAR_PLACEHOLDER_TEST_ID: &str = "PROFILE_AVATAR_PLACEHOLDER_TEST_ID";

#[Composer]
pub fn ProfileAvatarPlaceholder(ctx: &AppContext) -> impl Composable<'static> {
    let placeholder_color = Color {
        r: 37,
        g: 46,
        b: 57,
        a: 255,
    };
    compose! {
        Rectangle()
            .background_color(placeholder_color)
            .height(204.0)
            .width(204.0)
            .border_radius(0.0)  // Changed from 102.0 to 0.0 for square avatars
            .border_width(3.0)
            .test_id(PROFILE_AVATAR_PLACEHOLDER_TEST_ID)
    }
}
