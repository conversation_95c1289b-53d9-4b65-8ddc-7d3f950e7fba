use std::rc::Rc;

use app_config::AppConfigContext;
use ignx_compositron::device_information::DeviceInformation;
use network::{common::lrc_edge_constants::LRCEdgeTransforms, URLBuilder, URLBuilderResult};

const GENERATE_PIN_PROOF_URL: &str = "/pin/v1/pinProof";

pub(crate) fn profile_selection_url_constructor(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
) -> URLBuilderResult {
    URLBuilder::for_lrc_edge(
        &LRCEdgeTransforms::ProfileSelectionPage,
        vec![],
        false,
        device_info,
        app_config,
    )
}

pub(crate) fn generate_pin_proof_url_constructor(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
) -> URLBuilderResult {
    URLBuilder::for_device_proxy(
        GENERATE_PIN_PROOF_URL,
        vec![],
        false,
        device_info,
        app_config,
    )
}
