use std::rc::Rc;

use common_transform_types::profile::*;
use ignx_compositron::app::launch_composable;
use ignx_compositron::composable::*;
use ignx_compositron::compose;
use ignx_compositron::layout::*;
use ignx_compositron::reactive::*;
use ignx_compositron::row::*;
use location::Location;
use mockall::predicate;
use mockall::predicate::eq;
use mockall::predicate::function;
use profile_selection::ui::compound_components::new_profile::*;
use profile_selection::ui::compound_components::profile::*;
use router::{MockRouting, RoutingContext};

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let mut mock_routing_context = MockRouting::new();
        mock_routing_context
            .expect_navigate()
            .with(
                function(|loc: &Location| {
                    println!("navigate called with {:?}", loc);
                    true
                }),
                eq(""),
            )
            .return_const(());
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
        let profile_data = Profile {
            id: "a profile id".to_string(),
            name: "a profile name".to_string(),
            avatar: ProfileAvatar {
                avatarId: "an avatar id".to_string(),
                avatarUrl: "https://m.media-amazon.com/images/G/02/CerberusPrimeVideo-FN38FSBD/adult-1-square.png".to_string(),
                avatarDescription: Some("an avatar description".to_string()),
            },
            isActive: true,
            isAdult: true,
            profileIsImplicit: false,
            permissions: Some(ProfilePermissions {
                edit: ProfilePermission {
                    permissionType: ProfilePermissionType::ALLOWED,
                    challenge: None,
                    reason: Some(ProfilePermissionReason::NO_RESTRICTIONS),
                },
                entry: ProfilePermission {
                    permissionType: ProfilePermissionType::CHALLENGE,
                    challenge: Some(ProfileChallenge::PIN_REQUIRED(PinRequiredChallenge {
                        profileId: "a profile id".to_string(),
                        msg: "you need to enter a pin".to_string(),
                        pinLength: 4,
                        alternateChallenge: None,
                    })),
                    reason: Some(ProfilePermissionReason::CUSTOMER_PREFERENCE),
                },
            }),
            translationDetails: None,
        };

        let mut mock_routing_context = MockRouting::new();
        mock_routing_context
            .expect_navigate()
            .with(
                function(|loc: &Location| {
                    println!("navigate called with {:?}", loc);
                    true
                }),
                predicate::always(),
            )
            .return_const(());
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));

        let (profile, _set_profile) = create_signal(ctx.scope(), profile_data);
        let show_all_edit_buttons = create_rw_signal(ctx.scope(), false);
        let locked = create_rw_signal(ctx.scope(), false);

        compose! {
            Row() {
                Profile(profile, show_all_edit_buttons, on_profile_select: Rc::new(|_,_,_| {}), on_edit_profile: Rc::new(|_,_| {}), accessibility_enumerable_index: 1, accessibility_enumerable_count: 2)
                NewProfile(show_all_edit_buttons, on_new_profile: Rc::new(|_| {}), locked)
            }
            .padding(Padding::all(100.0))
        }
    })
}
