use std::rc::Rc;

use ignx_compositron::accessibility::*;
use ignx_compositron::app::launch_composable;
use ignx_compositron::compose;
use ignx_compositron::reactive::*;
use ignx_compositron::reactive::{create_rw_signal, MaybeSignal};
use ignx_compositron::text::TextContent;
use location::Location;
use log::info;
use mockall::predicate::{self, function};
use profile_selection::ui::basic_components::modal_page::*;
use router::{MockRouting, RoutingContext};

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let mut mock_routing_context = MockRouting::new();
        mock_routing_context
            .expect_navigate()
            .with(
                function(|loc: &Location| {
                    println!("navigate called with {:?}", loc);
                    true
                }),
                predicate::always(),
            )
            .return_const(());
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
        let buttons = create_rw_signal(
            ctx.scope(),
            vec![ModalButtonInformation {
                text: TextContent::String("OK".to_string()),
                id: 0,
                on_select: Option::Some(Rc::new(move |_id: i32| {
                    info!("OK Button pressed");
                })),
            }],
        );

        let title = TextContent::String("Something went wrong".to_string());
        let text_blocks = MaybeSignal::Static(vec![vec![TextContent::String(
            "We're unable to load your profiles at this time".to_string(),
        )]]);

        compose! {
            ModalPage(title, text_blocks, buttons)
            .accessibility_context_message("Something went wrong")
            .accessibility_description("We're unable to load your profiles at this time")
        }
    })
}
