// bug in clippy https://github.com/rust-lang/rust-clippy/issues/12824
#![allow(clippy::indexing_slicing)]

use crate::types::*;
use ignx_compositron::network::http::HttpMethod;
use ignx_compositron::prelude::AppContext;
use mockall::mock;
use network::common::DeviceProxyResponse;
use network::common::LRCEdgeResponse;
use network::NetworkClient;
use network::RequestError;
use network::URLBuilder;
use serde_json::from_str;
use std::rc::Rc;
use std::time::Duration;

const CONSENT_BASE_URL: &str = "/cdp/privacyprefs/dp/consent/v2";

fn cookie_consent_response_data_parser_callback(
    _ctx: &AppContext,
    response: String,
    _status_code: i32,
    cb: Box<dyn FnOnce(Result<DeviceProxyResponse<CookieConsentResponse>, RequestError>)>,
) {
    cb(cookie_consent_response_data_parser(response))
}

fn cookie_consent_response_data_parser(
    response: String,
) -> Result<DeviceProxyResponse<CookieConsentResponse>, RequestError> {
    from_str(&response)
        .map(LRCEdgeResponse::with_resource)
        .map(DeviceProxyResponse::LRCEdgeResponse)
        .map_err(RequestError::DeserializationFailed)
}

fn cookie_consent_request_composer(
    consent_ids: Vec<ConsentType>,
) -> Result<String, serde_json::Error> {
    serde_json::to_string(&CookieConsentRequest {
        consents: consent_ids
            .iter()
            .map(|id| MappedConsent {
                consentType: id.get_type_id().to_string(),
                marketplaceId: id.get_marketplace_id().to_string(),
                domainId: id.get_domain_id().to_string(),
            })
            .collect(),
    })
}

pub(crate) trait ConsentRequests {
    fn submit_consent<S, F, L>(
        &self,
        action: ConsentAction,
        consent_ids: Vec<ConsentType>,
        success_callback: S,
        failure_callback: F,
        latency_callback: L,
    ) where
        S: FnOnce(CookieConsentResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
        L: Fn(Duration) + 'static;
}

impl ConsentRequests for NetworkClient {
    fn submit_consent<S, F, L>(
        &self,
        action: ConsentAction,
        consent_ids: Vec<ConsentType>,
        success_callback: S,
        failure_callback: F,
        latency_callback: L,
    ) where
        S: FnOnce(CookieConsentResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
        L: Fn(Duration) + 'static,
    {
        let url = match URLBuilder::for_device_proxy(
            &format!("{}{}", CONSENT_BASE_URL, action.to_url_path()),
            vec![],
            false,
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!(
                    "DMAConsent#submit_consent: Cannot create the request url. Error: {:?}",
                    e
                );
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let body_str = match cookie_consent_request_composer(consent_ids) {
            Ok(s) => s,
            Err(serde_error) => {
                return failure_callback(RequestError::SerializationFailed(serde_error));
            }
        };

        let success_cb = move |v: CookieConsentResponse, _| {
            success_callback(v);
        };

        let failure_cb = move |e: RequestError, _| {
            log::error!("DMAConsent#submit_consent: Error: {:?}", e);
            failure_callback(e);
        };

        let latency_cb = move |_, duration: Duration| {
            latency_callback(duration);
        };

        #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-785")]
        self.builder(url, HttpMethod::Post, "submit_consent")
            .data(body_str)
            .with_parser(cookie_consent_response_data_parser_callback)
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb))
            .with_network_latency(Rc::new(latency_cb))
            .execute();
    }
}

mock! {
    pub NetworkClient {
        pub fn new(ctx: &AppContext) -> Self;
    }
    impl ConsentRequests for NetworkClient {
    fn submit_consent<S, F, L>(
        &self,
        action: ConsentAction,
        consent_ids: Vec<ConsentType>,
        success_callback: S,
        failure_callback: F,
        latency_callback: L,
    ) where
        S: FnOnce(CookieConsentResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
        L: Fn(Duration) + 'static;
    }
}

#[cfg(test)]
mod test {

    use super::*;

    #[test]
    fn it_deserializes_consent_response() {
        let response_str = r#"{"failedConsents":[]}"#;

        let result = cookie_consent_response_data_parser(response_str.to_string());

        let expected = CookieConsentResponse::failedConsents(vec![]);

        assert!(
            matches!(result, Ok(DeviceProxyResponse::LRCEdgeResponse(r)) if r.resource == expected)
        );
    }

    #[test]
    fn it_serializes_cookie_consent_request() {
        let consent_ids = vec![ConsentType::QuebecCookie];

        let result = cookie_consent_request_composer(consent_ids);

        let expected = r#"{"consents":[{"consentType":"42","marketplaceId":"A2EUQ1WTGCTBG2","domainId":"0"}]}"#;

        assert_eq!(result.unwrap(), expected);
    }

    #[test]
    fn it_serializes_dma_consent_request() {
        let consent_ids = vec![ConsentType::DMACookie];

        let result = cookie_consent_request_composer(consent_ids);

        let expected =
            r#"{"consents":[{"consentType":"4","marketplaceId":"DEFAULTEU","domainId":"0"}]}"#;

        assert_eq!(result.unwrap(), expected);
    }
}
