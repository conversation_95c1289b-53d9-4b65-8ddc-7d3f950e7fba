use crate::types::{ConsentModalData, CriticalNotificationId};
use amzn_fable_tokens::FableColor;
use cfg_test_attr_derive::derive_test_only;
use chrono::serde::ts_milliseconds;
use chrono::{DateTime, Utc};
#[allow(deprecated, reason = "waiting for ACM rewrite")]
use cross_app_events::{create_serde_map, send_app_event};
use fableous::buttons::primary_button::*;
use fableous::gradients::gradient::*;
use fableous::pointer_control::*;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::id::Id;
use ignx_compositron::storage;
use ignx_compositron::text::{LocalizedText, TextContent};
use ignx_compositron::{compose, Composer};
use ignx_compositron::{prelude::*, ItemVisibility, VisibleRange};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::ops::{Add, Mul};
use std::rc::Rc;
use strum::Display;

pub const MODAL_BORDER: f32 = 144.0;
pub const MODAL_WIDTH: f32 = 1740.0;
pub const MODAL_HEIGHT: f32 = 900.0;
pub const SCROLL_AREA_WIDTH: f32 = 1480.0;
pub const TITLE_AREA_SIZE: f32 = 120.0;
pub const BUTTON_HEIGHT: f32 = 24.0;
pub const BUTTON_AREA_SIZE: f32 = 180.0;
pub const SCROLL_AREA_HEIGHT: f32 = 540.0;
pub const GRADIENT_HEIGHT: f32 = 150.0;
pub const SCROLL_HEIGHT: f32 = SCROLL_AREA_HEIGHT - (GRADIENT_HEIGHT / 2.0);
pub const SCROLL_PADDING: f32 = SCROLL_AREA_HEIGHT - SCROLL_HEIGHT;
pub const VERTICAL_SPACING: f32 = 36.0;

pub const CONSENT_MODAL_TEST_ID: &str = "consent-modal";

const DMA_HINT_MESSAGE: &str = "AV_LRC_DMA_CONSENT_BUTTONS_HINT";

// Clickstrem
const LOAD_EVENT: &str = "DMA_BUTTON";
const CLICK_EVENT: &str = "DMA_BUTTON_CLICKED";
const SCROLL_EVENT: &str = "DMA_SCROLL";

// Metrics
const METRIC_NAME: &str = "DMA.Modal.Action";
const COOKIE_CONSENT_LAST_DISPLAYED_AT_STORAGE_KEY: &str = "CookieConsentLastDisplayedAt";
#[derive(Display)]
enum MetricPrefix {
    Cookie,
    DmaCookie,
    QuebecCookie,
}
#[derive(Display, Debug, PartialEq)]
pub enum MetricAction {
    Accept,
    Reject,
    Shown,
    ShownEarly,
    EmptyConsentStrings,
}

#[derive(Serialize, Deserialize)]
#[derive_test_only(Debug, PartialEq)]
struct UnixTimestamp(#[serde(with = "ts_milliseconds")] DateTime<Utc>);

#[derive(Serialize, Deserialize, Default)]
#[derive_test_only(Debug, PartialEq)]
struct LastDisplayedAt {
    #[serde(skip_serializing_if = "Option::is_none")]
    dma: Option<UnixTimestamp>,
    #[serde(skip_serializing_if = "Option::is_none")]
    cookie: Option<UnixTimestamp>,
    #[serde(skip_serializing_if = "Option::is_none")]
    quebec: Option<UnixTimestamp>,
}
const ONE_YEAR_MINUS_ONE_WEEK_DAYS: i64 = 358;

fn get_last_displayed_at_data() -> Option<LastDisplayedAt> {
    storage::get(COOKIE_CONSENT_LAST_DISPLAYED_AT_STORAGE_KEY)
        .ok()
        .and_then(|last_displayed_at| {
            serde_json::from_str::<LastDisplayedAt>(&last_displayed_at).ok()
        })
}

pub fn report_modal_metric(modal_type: &CriticalNotificationId, action: MetricAction) {
    let prefix = match modal_type {
        CriticalNotificationId::CookieConsent => MetricPrefix::Cookie,
        CriticalNotificationId::DMACookieConsent => MetricPrefix::DmaCookie,
        CriticalNotificationId::QuebecCookieConsent => MetricPrefix::QuebecCookie,
    };

    let now = Utc::now();
    if matches!(action, MetricAction::Accept | MetricAction::Reject) {
        let mut last_displayed_at = get_last_displayed_at_data().unwrap_or_default();

        match modal_type {
            CriticalNotificationId::CookieConsent => {
                last_displayed_at.cookie = Some(UnixTimestamp(now))
            }
            CriticalNotificationId::DMACookieConsent => {
                last_displayed_at.dma = Some(UnixTimestamp(now))
            }
            CriticalNotificationId::QuebecCookieConsent => {
                last_displayed_at.quebec = Some(UnixTimestamp(now))
            }
        };

        let _ = storage::set(
            COOKIE_CONSENT_LAST_DISPLAYED_AT_STORAGE_KEY,
            &json!(last_displayed_at).to_string(),
        );
    }

    if matches!(action, MetricAction::Shown) {
        if let Some(last_displayed_at) = get_last_displayed_at_data() {
            if let Some(UnixTimestamp(last_displayed_at)) = match modal_type {
                CriticalNotificationId::CookieConsent => last_displayed_at.cookie,
                CriticalNotificationId::DMACookieConsent => last_displayed_at.dma,
                CriticalNotificationId::QuebecCookieConsent => last_displayed_at.quebec,
            } {
                let delta_days = (now - last_displayed_at).num_days();
                if delta_days < ONE_YEAR_MINUS_ONE_WEEK_DAYS {
                    metric!(METRIC_NAME, delta_days as f64, "Action" => format!("{}{}", prefix, MetricAction::ShownEarly))
                }
            }
        }
    }

    metric!(METRIC_NAME, 1, "Action" => format!("{}{}", prefix, action))
}

const SCROLL_AMOUNT: f32 = 100.0;

#[derive(Clone, Hash)]
pub enum FocusedButton {
    ACCEPT,
    REJECT,
}

#[derive(Clone)]
pub struct SplitText {
    id: usize,
    content: String,
}

impl Id for SplitText {
    type Id = usize;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

enum ScrollDirection {
    Up,
    Down,
}

#[Composer]
pub fn ConsentModal(
    ctx: &AppContext,
    consent_data_model: &ConsentModalData,
    on_dismiss: Rc<dyn Fn()>,
) -> StackComposable {
    let visible_range: RwSignal<Option<VisibleRange>> = create_rw_signal(ctx.scope(), None);
    let pointer_control_enabled = is_pointer_control_enabled(ctx.scope);
    let pointer_control_active = is_pointer_control_active(ctx.scope);
    let pointer_control_up_caret = create_rw_signal(ctx.scope, true);
    let pointer_control_down_caret = create_rw_signal(ctx.scope, true);
    let up_pointer_visible = create_rw_signal(ctx.scope, true);
    let down_pointer_visible = create_rw_signal(ctx.scope, true);
    create_effect(ctx.scope, move |_| {
        if pointer_control_enabled {
            pointer_control_up_caret.set(pointer_control_active.get() && up_pointer_visible.get());
            pointer_control_down_caret
                .set(pointer_control_active.get() && down_pointer_visible.get());
        }
    });

    let focused_button = create_focus_value_signal(ctx.scope());
    let scroll = create_rw_signal(ctx.scope(), 0.0);
    let scroll_debounce = create_rw_signal(ctx.scope(), false);
    let scroll_to = Signal::derive(ctx.scope(), move || {
        ScrollTo::Offset(
            scroll.get() * -1.0,
            Pivot::Start,
            Animation::default().with_on_finish(move || scroll_debounce.set(false)),
        )
    });

    fn get_hint_text(hint_text: &Option<String>) -> TextContent {
        hint_text.as_ref().map_or(
            TextContent::LocalizedText(LocalizedText::new(DMA_HINT_MESSAGE)),
            |content| TextContent::String(content.clone()),
        )
    }

    create_effect(ctx.scope(), {
        let scope = ctx.scope();
        let ref_marker = consent_data_model.scroll_ref_marker.clone();
        let modal_type = consent_data_model.modal_type.clone();
        move |_| {
            scroll.with(|_| {});
            report_click_stream(scope, SCROLL_EVENT, ref_marker.clone(), modal_type.clone());
        }
    });

    let scrollable_content: Vec<SplitText> = consent_data_model
        .content
        .clone()
        .unwrap_or_default()
        .split("\n\n")
        .enumerate()
        .map(|(id, content)| SplitText {
            id,
            content: content.into(),
        })
        .collect();

    let max_index = scrollable_content.len().saturating_sub(1);

    let on_scroll = move |direction: ScrollDirection| {
        let Some(visible_range) = visible_range.try_get_untracked().flatten() else {
            return;
        };

        if scroll_debounce.get() {
            return;
        };

        let multiplier;

        if visible_range.end.index == max_index {
            down_pointer_visible.set(false);
        } else {
            down_pointer_visible.set(true);
        }

        if visible_range.start.index == 0 {
            up_pointer_visible.set(false);
        } else {
            up_pointer_visible.set(true);
        }

        match direction {
            ScrollDirection::Up => {
                if !(visible_range.start.index > 0
                    || visible_range.start.visibility != ItemVisibility::Visible)
                {
                    return;
                } else {
                    multiplier = -1.0;
                }
            }
            ScrollDirection::Down => {
                if !(visible_range.end.index < max_index
                    || visible_range.end.visibility != ItemVisibility::Visible)
                {
                    return;
                } else {
                    multiplier = 1.0;
                }
            }
        }

        scroll_debounce.set(true);
        scroll.update(|offset| *offset = offset.add(SCROLL_AMOUNT.mul(multiplier)))
    };

    let on_accept = {
        let callback = Rc::clone(&consent_data_model.on_accept);
        let scope = ctx.scope();
        let ref_marker = consent_data_model.accept_ref_marker.clone();
        let modal_type = consent_data_model.modal_type.clone();
        let on_dismiss = Rc::clone(&on_dismiss);
        move || {
            report_click_stream(scope, CLICK_EVENT, ref_marker.clone(), modal_type.clone());
            report_modal_metric(&modal_type, MetricAction::Accept);
            callback();
            on_dismiss();
        }
    };

    let on_reject = {
        let callback = Rc::clone(&consent_data_model.on_reject);
        let scope = ctx.scope();
        let ref_marker = consent_data_model.reject_ref_marker.clone();
        let modal_type = consent_data_model.modal_type.clone();
        let on_dismiss = Rc::clone(&on_dismiss);
        move || {
            report_click_stream(scope, CLICK_EVENT, ref_marker.clone(), modal_type.clone());
            report_modal_metric(&modal_type, MetricAction::Reject);
            callback();
            on_dismiss();
        }
    };

    report_click_stream(
        ctx.scope(),
        LOAD_EVENT,
        consent_data_model.render_ref_marker.clone(),
        consent_data_model.modal_type.clone(),
    );
    report_modal_metric(&consent_data_model.modal_type, MetricAction::Shown);

    compose!(
        Stack() {
        Column() {
            PointerControlCaret(direction: Direction::Up, visible: pointer_control_up_caret, on_select: Rc::new(move || {on_scroll(ScrollDirection::Up)}))
            PointerControlCaret(direction: Direction::Down, visible: pointer_control_down_caret, on_select: Rc::new(move || {on_scroll(ScrollDirection::Down)}))
        }
        .main_axis_size(MainAxisSize::Max)
        .main_axis_alignment(MainAxisAlignment::SpaceBetween)
        Column() {
            Stack(){
            TypographyHeading400(content: consent_data_model.title.clone())
            }
            .padding(Padding::new( 0.0, 0.0, 54.0, VERTICAL_SPACING))

            Stack(){
                ColumnList(
                    items: scrollable_content,
                        item_builder: Rc::new(move |ctx, item, _: usize| {
                            compose! {
                                Column() {
                                    TypographyBody200(content: item.content.clone())
                                 }
                             }
                        })
                )
                .main_axis_alignment(MainAxisAlignment::SpacedBy(VERTICAL_SPACING))
                .height(SCROLL_HEIGHT)
                .overflow_behavior(OverflowBehavior::Hidden)
                .padding(Padding::new(0.0,0.0,0.0, SCROLL_PADDING))
                .on_visible_range_changed(move |vr| visible_range.set(vr))
                .scroll_to(scroll_to)

                Gradient(gradient: GradientName::ConsentModal, width: MODAL_WIDTH, height: GRADIENT_HEIGHT)
            }
            .width(SCROLL_AREA_WIDTH)
            .height(SCROLL_AREA_HEIGHT)
            .alignment(Alignment::CenterBottom)
            .padding(Padding::new(0.0,0.0,0.0,VERTICAL_SPACING))

            Row() {
                PrimaryButton(variant: PrimaryButtonVariant::TextSize800(TextContent::String(consent_data_model.accept_button_text.clone())))
                .focused_value(focused_button, FocusedButton::ACCEPT)
                .on_select(on_accept)
                .pointer_control_target(None)
                .on_hover(move |_| focused_button.set(FocusedButton::ACCEPT))

                PrimaryButton(variant: PrimaryButtonVariant::TextSize800(TextContent::String(consent_data_model.reject_button_text.clone())))
                .focused_value(focused_button, FocusedButton::REJECT)
                .on_select(on_reject)
                .pointer_control_target(None)
                .on_hover(move |_| focused_button.set(FocusedButton::REJECT))
            }
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(30.0))
            .padding(Padding::new(0.0,0.0,0.0,VERTICAL_SPACING))
            .on_key_down(ignx_compositron::input::KeyCode::Left, move || focused_button.set(FocusedButton::ACCEPT))
            .on_key_down(ignx_compositron::input::KeyCode::Right, move || focused_button.set(FocusedButton::REJECT))
            .on_key_down(ignx_compositron::input::KeyCode::Up,  move || on_scroll(ScrollDirection::Up))
            .on_key_down(ignx_compositron::input::KeyCode::Down, move || on_scroll(ScrollDirection::Down))
            .focusable()
            .preferred_focus(true)
            .test_id(CONSENT_MODAL_TEST_ID)

            TypographyBody200(content: get_hint_text(&consent_data_model.navigate_hint_text))
        }
        .border_color(get_ignx_color(FableColor::PRIMARY))
        .width(MODAL_WIDTH)
        .height(MODAL_HEIGHT)
        .background_color(get_ignx_color(FableColor::BACKGROUND))
        .cross_axis_alignment(CrossAxisAlignment::Center)
        .focus_window()
        .accessibility_context_message(consent_data_model.title.clone())
        .accessibility_description(consent_data_model.content.clone().unwrap_or_default().replace('\n', ""))
        .accessibility_closing_context_message(get_hint_text(&consent_data_model.navigate_hint_text))
    }
    .height(SCREEN_HEIGHT)
    .width(SCREEN_WIDTH)
    .alignment(Alignment::Center)
    )
}

fn report_click_stream(
    scope: Scope,
    event: &'static str,
    ref_marker: Option<String>,
    modal_type: CriticalNotificationId,
) {
    if let Some(ref_marker) = ref_marker {
        #[allow(deprecated)]
        send_app_event(
            scope,
            event,
            "DMA",
            Some(create_serde_map([
                ("refMarker", Value::String(ref_marker)),
                ("modalType", json!(modal_type)),
            ])),
        );
    }
}

#[cfg(test)]
mod test {
    use std::cell::Cell;

    use ignx_compositron::app::launch_test;
    use ignx_compositron::input::KeyCode;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use rstest::rstest;
    use rust_features::provide_context_test_rust_features;

    use super::*;

    #[rstest]
    #[case(None, false, false)]
    #[case(Some(KeyCode::Left), true, false)]
    #[case(Some(KeyCode::Right), false, true)]
    fn on_select(
        #[case] button_press: Option<KeyCode>,
        #[case] should_call_accept: bool,
        #[case] should_call_reject: bool,
    ) {
        let accept_called = Rc::new(Cell::new(false));
        let accept_called_clone = accept_called.clone();
        let reject_called = Rc::new(Cell::new(false));
        let reject_called_clone = reject_called.clone();
        let dismiss_called = Rc::new(Cell::new(false));
        let dismiss_called_clone = dismiss_called.clone();

        launch_test(
            |ctx| {
                let consent_data_model = ConsentModalData {
                    title: String::new(),
                    modal_type: CriticalNotificationId::DMACookieConsent,
                    content: None,
                    accept_button_text: String::new(),
                    reject_button_text: String::new(),
                    navigate_hint_text: None,
                    on_accept: Rc::new(move || accept_called_clone.set(true)),
                    on_reject: Rc::new(move || reject_called_clone.set(true)),
                    render_ref_marker: None,
                    accept_ref_marker: None,
                    reject_ref_marker: None,
                    scroll_ref_marker: None,
                };

                provide_context_test_rust_features(ctx.scope());

                compose! {
                    ConsentModal(consent_data_model: &consent_data_model, on_dismiss: Rc::new(move || dismiss_called_clone.set(true)))
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let button_row = node_tree
                    .find_by_test_id(CONSENT_MODAL_TEST_ID)
                    .get_props()
                    .node_id;

                if let Some(key_code) = button_press {
                    test_game_loop.send_key_down_up_event_to_node(button_row, key_code);
                }

                let node_tree = test_game_loop.tick_until_done();

                if let Some(focused_button) = node_tree
                    .find_where(|node| {
                        node.props.is_focused
                            && node
                                .props
                                .test_id
                                .as_ref()
                                .is_some_and(|test_id| test_id == PRIMARY_BUTTON_TEST_ID)
                    })
                    .ok()
                    .and_then(|res| res.first().map(|node| node.borrow_props().node_id))
                {
                    test_game_loop.send_on_select_event(focused_button);
                }

                test_game_loop.tick_until_done();

                assert_eq!(accept_called.get(), should_call_accept);
                assert_eq!(reject_called.get(), should_call_reject);
                assert_eq!(
                    dismiss_called.get(),
                    should_call_accept || should_call_reject
                );
            },
        )
    }

    #[test]
    fn scroll() {
        let text = "top text".to_owned() + &".\n\n".repeat(20) + "bottom text";

        launch_test(
            |ctx| {
                let consent_data_model = ConsentModalData {
                    title: String::new(),
                    modal_type: CriticalNotificationId::DMACookieConsent,
                    content: Some(text),
                    accept_button_text: String::new(),
                    reject_button_text: String::new(),
                    navigate_hint_text: None,
                    on_accept: Rc::new(|| ()),
                    on_reject: Rc::new(|| ()),
                    render_ref_marker: None,
                    accept_ref_marker: None,
                    reject_ref_marker: None,
                    scroll_ref_marker: None,
                };

                provide_context_test_rust_features(ctx.scope());

                compose! {
                    ConsentModal(consent_data_model: &consent_data_model, on_dismiss: Rc::new(|| ()))
                }
            },
            move |_scope, mut test_game_loop| {
                let mut node_tree = test_game_loop.tick_until_done();

                assert_node_exists!(node_tree
                    .find_by_props()
                    .contains_text("top text")
                    .is_visible()
                    .find_first());
                assert_node_does_not_exist!(node_tree
                    .find_by_props()
                    .contains_text("bottom text")
                    .is_visible()
                    .find_first());

                let button_row = node_tree
                    .find_by_test_id(CONSENT_MODAL_TEST_ID)
                    .get_props()
                    .node_id;

                for _ in 1..20 {
                    test_game_loop.send_key_down_up_event_to_node(button_row, KeyCode::Down);
                    node_tree = test_game_loop.tick_until_done();
                }

                assert_node_does_not_exist!(node_tree
                    .find_by_props()
                    .contains_text("top text")
                    .is_visible()
                    .find_first());
                assert_node_exists!(node_tree
                    .find_by_props()
                    .contains_text("bottom text")
                    .is_visible()
                    .find_first());

                for _ in 1..20 {
                    test_game_loop.send_key_down_up_event_to_node(button_row, KeyCode::Up);
                    node_tree = test_game_loop.tick_until_done();
                }

                assert_node_exists!(node_tree
                    .find_by_props()
                    .contains_text("top text")
                    .is_visible()
                    .find_first());
                assert_node_does_not_exist!(node_tree
                    .find_by_props()
                    .contains_text("bottom text")
                    .is_visible()
                    .find_first());
            },
        )
    }

    #[test]
    fn serializes_last_displayed_at() {
        let actual = serde_json::to_string(&LastDisplayedAt {
            dma: None,
            cookie: Some(UnixTimestamp(
                DateTime::from_timestamp_millis(1725876140427).unwrap(),
            )),
            quebec: None,
        })
        .unwrap();
        let expected = "{\"cookie\":1725876140427}";

        assert_eq!(actual, expected);
    }

    #[test]
    fn deserializes_last_displayed_at() {
        let actual: LastDisplayedAt = serde_json::from_str("{\"dma\":1725876460146}").unwrap();
        let expected = LastDisplayedAt {
            dma: Some(UnixTimestamp(
                DateTime::from_timestamp_millis(1725876460146).unwrap(),
            )),
            cookie: None,
            quebec: None,
        };

        assert_eq!(actual, expected);
    }
}
