#![allow(clippy::string_to_string)]

use crate::types::DmaAndCookieConsentData;

use crate::types::*;

#[double]
use crate::metric::MetricCaller;
#[cfg(test)]
use crate::network::MockNetworkClient as NetworkClient;
use crate::network::*;
#[cfg(not(test))]
use ::network::NetworkClient;
use ignx_compositron::prelude::AppContext;
use ignx_compositron::prelude::*;
use mockall_double::double;
#[double]
use rust_features::RustFeatures;
use std::rc::Rc;
use std::str::FromStr;
use std::time::Duration;

mod metric;
pub mod types;
pub mod ui;

mod network;

fn submit_consent(
    ctx: AppContext,
    action: ConsentAction,
    consent_type: Rc<CriticalNotificationId>,
) {
    let consent_ids = match *consent_type {
        CriticalNotificationId::CookieConsent => vec![ConsentType::<PERSON><PERSON>],
        CriticalNotificationId::DMACookieConsent => {
            vec![ConsentType::DMACookie, ConsentType::<PERSON>ie]
        }
        CriticalNotificationId::QuebecCookieConsent => vec![ConsentType::QuebecCookie],
    };

    let consent_type_cloned = consent_type.clone();
    let action_path = action.to_url_path().to_string();
    let success_callback = move |response| match response {
        CookieConsentResponse::consents(_) => {
            MetricCaller::metric_if_quebec(
                consent_type_cloned,
                "DMA.Request.Quebec.Success",
                1.0,
                &action_path,
            );

            MetricCaller::metric("DMA.Request.Success", 1.0, &action_path);
        }
        CookieConsentResponse::failedConsents(consents) => {
            if consents.is_empty() {
                MetricCaller::metric_if_quebec(
                    consent_type_cloned,
                    "DMA.Request.Quebec.Success",
                    1.0,
                    &action_path,
                );
                MetricCaller::metric("DMA.Request.Success", 1.0, &action_path);
            } else {
                MetricCaller::metric_if_quebec(
                    consent_type_cloned,
                    "DMA.Request.Quebec.Failure",
                    1.0,
                    &action_path,
                );

                MetricCaller::metric("DMA.Request.Failure", 1.0, &action_path);
                log::error!(
                    "DMAConsent#submit_consent: Failed consents received: {:?}",
                    consents
                );
            }
        }
    };

    let failure_callback = {
        let consent_type = consent_type.clone();
        let action_path = action.to_url_path().to_string();
        move |_| {
            MetricCaller::metric_if_quebec(
                consent_type,
                "DMA.Request.Quebec.Failure",
                1.0,
                &action_path,
            );
            MetricCaller::metric("DMA.Request.Failure", 1.0, &action_path);
        }
    };

    let latency_callback = {
        let action_path = action.to_url_path().to_string();
        move |duration: Duration| {
            MetricCaller::metric_if_quebec(
                consent_type.clone(),
                "DMA.Request.Quebec.Latency",
                duration.as_millis() as f64,
                &action_path,
            );
            MetricCaller::metric(
                "DMA.Request.Latency",
                duration.as_millis() as f64,
                &action_path,
            );
        }
    };

    let network_client = NetworkClient::new(&ctx);

    network_client.submit_consent(
        action,
        consent_ids,
        success_callback,
        failure_callback,
        latency_callback,
    );
}

fn generate_modal_body_text_from_response(data: &DmaAndCookieConsentData) -> String {
    let Some(mut text) = data.descriptionText.clone() else {
        return String::new();
    };

    if let Some(hyperlinks) = &data.hyperLinks {
        for (hyperlink_name, hyperlink) in hyperlinks {
            text = text.replace(
                &format!("{{link|${{{hyperlink_name}}}}}{{end}}"),
                &hyperlink.hyperlink,
            );
        }
    }

    text
}

pub fn generate_consent_modal(
    ctx: AppContext,
    data: &DmaAndCookieConsentData,
) -> Option<ConsentModalData> {
    let modal_type = CriticalNotificationId::from_str(&data.criticalNotificationId).ok()?;

    if let CriticalNotificationId::QuebecCookieConsent = modal_type {
        let rust_features = use_context::<RustFeatures>(ctx.scope())?;
        if !rust_features.is_quebec_cookie_consent_enabled() {
            return None;
        }
    }

    let consent_type = Rc::new(modal_type.clone());

    let mut attributes = data.attributes.clone()?.into_iter();
    let accept_button = attributes.find(|attr| attr.attributeType == AttributeType::accept)?;
    let reject_button = attributes.find(|attr| attr.attributeType == AttributeType::decline)?;
    let navigate_hint = attributes.find(|attr| attr.attributeType == AttributeType::navigate);

    let on_accept = {
        let ctx = ctx.clone();
        let consent_type = Rc::clone(&consent_type);

        Rc::new(move || {
            submit_consent(ctx.clone(), ConsentAction::Accept, consent_type.clone());
        })
    };

    let on_reject = {
        Rc::new(move || {
            submit_consent(ctx.clone(), ConsentAction::Reject, consent_type.clone());
        })
    };

    Some(ConsentModalData {
        title: data.headerText.clone()?,
        modal_type,
        content: Some(generate_modal_body_text_from_response(data)),
        accept_button_text: accept_button.text?,
        reject_button_text: reject_button.text?,
        navigate_hint_text: navigate_hint.clone().and_then(|hint| hint.text),
        on_accept,
        on_reject,
        render_ref_marker: data
            .renderAnalytics
            .as_ref()
            .map(|analytics| analytics.refMarker.clone()),
        accept_ref_marker: accept_button
            .analytics
            .as_ref()
            .map(|analytics| analytics.refMarker.clone()),
        reject_ref_marker: reject_button
            .analytics
            .as_ref()
            .map(|analytics| analytics.refMarker.clone()),
        scroll_ref_marker: navigate_hint
            .and_then(|attr| attr.analytics)
            .map(|analytics| analytics.refMarker),
    })
}

#[cfg(test)]
mod tests {

    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::prelude::provide_context;
    use ignx_compositron::prelude::AppContext;
    use mockall::predicate::{always, eq};
    use rust_features::MockRustFeaturesBuilder;

    use crate::__mock_MockNetworkClient::__new::Context;
    use crate::generate_consent_modal;
    use crate::generate_modal_body_text_from_response;
    use crate::metric::MockMetricCaller;
    use crate::metric::__mock_MockMetricCaller::__metric;
    use crate::metric::__mock_MockMetricCaller::__metric_if_quebec;
    use crate::network::MockNetworkClient;
    use crate::submit_consent;
    use crate::types::*;
    use rstest::rstest;
    use serial_test::*;
    use std::collections::HashMap;
    use std::mem;
    use std::rc::Rc;
    use std::time::Duration;

    #[rstest]
    #[serial]
    #[case(CriticalNotificationId::CookieConsent, vec![ConsentType::Cookie], ConsentAction::Accept, CookieConsentResponse::consents(mock_valid_responses(&vec![ConsentType::Cookie])))]
    #[serial]
    #[case(CriticalNotificationId::QuebecCookieConsent, vec![ConsentType::QuebecCookie], ConsentAction::Reject, CookieConsentResponse::consents(mock_valid_responses(&vec![ConsentType::QuebecCookie])))]
    #[serial]
    #[case(CriticalNotificationId::CookieConsent, vec![ConsentType::Cookie], ConsentAction::Accept, CookieConsentResponse::failedConsents(mock_mapped_consents(&vec![])))]
    fn submit_consent_success(
        #[case] id: CriticalNotificationId,
        #[case] consent_ids: Vec<ConsentType>,
        #[case] action: ConsentAction,
        #[case] response: CookieConsentResponse,
    ) {
        launch_only_app_context(move |ctx| {
            let _context = setup_test(&ctx, action.clone(), consent_ids.clone(), Some(response));

            let _success_metric_context =
                expect_metric("DMA.Request.Success", 1.0, action.to_url_path().to_string());
            let _success_metric_if_quebec_context = expect_quebec_metric(
                &id,
                "DMA.Request.Quebec.Success",
                1.0,
                action.to_url_path().to_string(),
            );

            let _latency_metric_context = expect_metric(
                "DMA.Request.Latency",
                150.0,
                action.to_url_path().to_string(),
            );
            let _latency_metric_if_quebec_context = expect_quebec_metric(
                &id,
                "DMA.Request.Quebec.Latency",
                150.0,
                action.to_url_path().to_string(),
            );

            submit_consent(ctx, action, Rc::new(id));
        });
    }

    #[rstest]
    #[serial]
    #[case(CriticalNotificationId::CookieConsent, vec![ConsentType::Cookie], ConsentAction::Accept, Some(CookieConsentResponse::failedConsents(mock_mapped_consents(&vec![ConsentType::Cookie]))))]
    #[serial]
    #[case(CriticalNotificationId::CookieConsent, vec![ConsentType::Cookie], ConsentAction::Accept, None)]
    fn submit_consent_failure(
        #[case] id: CriticalNotificationId,
        #[case] consent_ids: Vec<ConsentType>,
        #[case] action: ConsentAction,
        #[case] response: Option<CookieConsentResponse>,
    ) {
        launch_only_app_context(move |ctx| {
            let has_response = response.is_some();
            let _context = setup_test(&ctx, action.clone(), consent_ids.clone(), response);

            let _metric_context =
                expect_metric("DMA.Request.Failure", 1.0, action.to_url_path().to_string());
            let _metric_if_quebec_context = expect_quebec_metric(
                &id,
                "DMA.Request.Quebec.Failure",
                1.0,
                action.to_url_path().to_string(),
            );

            let _latency_metric_contexts = if has_response {
                Some((
                    expect_metric(
                        "DMA.Request.Latency",
                        150.0,
                        action.to_url_path().to_string(),
                    ),
                    expect_quebec_metric(
                        &id,
                        "DMA.Request.Quebec.Latency",
                        150.0,
                        action.to_url_path().to_string(),
                    ),
                ))
            } else {
                None
            };

            submit_consent(ctx, action, Rc::new(id));
        });
    }

    fn expect_metric(metric: &'static str, val: f64, action_path: String) -> __metric::Context {
        let metric_context = MockMetricCaller::metric_context();

        metric_context
            .expect()
            .once()
            .withf(move |m, v, path| m == metric && *v == val && path == &action_path)
            .returning(|_, _, _| ());

        metric_context
    }

    fn expect_quebec_metric(
        id: &CriticalNotificationId,
        metric: &'static str,
        val: f64,
        action_path: String,
    ) -> __metric_if_quebec::Context {
        let metric_context = MockMetricCaller::metric_if_quebec_context();
        let id = id.clone();

        metric_context
            .expect()
            .once()
            .withf(move |i: &Rc<CriticalNotificationId>, m, v, path| {
                mem::discriminant(&**i) == mem::discriminant(&id)
                    && m == metric
                    && *v == val
                    && path == &action_path
            })
            .returning(|_, _, _, _| ());

        metric_context
    }

    fn setup_test(
        _ctx: &AppContext,
        action: ConsentAction,
        consent_ids: Vec<ConsentType>,
        result: Option<CookieConsentResponse>,
    ) -> Context {
        let mut mock_network_client = MockNetworkClient::default();

        mock_network_client
            .expect_submit_consent()
            .with(eq(action), eq(consent_ids), always(), always(), always())
            .return_once(
                move |_, _, success_cb, failure_cb, latency_cb| match result {
                    Some(response) => {
                        success_cb(response);
                        latency_cb(Duration::from_millis(150));
                    }
                    None => failure_cb(network::RequestError::Http {
                        code: 404,
                        body: "".to_string().into(),
                        headers: vec![],
                    }),
                },
            );

        let network_context = MockNetworkClient::new_context();

        network_context
            .expect()
            .return_once(|_| mock_network_client);

        network_context
    }

    fn mock_valid_responses(consent_ids: &Vec<ConsentType>) -> Vec<ConsentResponse> {
        consent_ids
            .iter()
            .map(|id| ConsentResponse {
                mappedConsent: MappedConsent {
                    consentType: id.get_type_id().to_string(),
                    marketplaceId: id.get_marketplace_id().to_string(),
                    domainId: id.get_domain_id().to_string(),
                },
                consent: vec![Consent {
                    avlTcfString: "".to_string(),
                    gvlTcfString: "".to_string(),
                    categoryConsents: vec![],
                    createdAt: 0,
                    expiresAt: 0,
                    lastUpdatedAt: 0,
                }],
            })
            .collect()
    }

    fn mock_mapped_consents(consent_ids: &Vec<ConsentType>) -> Vec<MappedConsent> {
        consent_ids
            .iter()
            .map(|id| MappedConsent {
                consentType: id.get_type_id().to_string(),
                marketplaceId: id.get_marketplace_id().to_string(),
                domainId: id.get_domain_id().to_string(),
            })
            .collect()
    }

    #[test]
    fn generate_modal_data() {
        launch_only_app_context(move |ctx| {
            let res = generate_consent_modal(ctx, &mock_dma_response()).unwrap();

            assert_eq!(res.title, "title");
            assert_eq!(res.content, Some("description".into()));
            assert_eq!(res.accept_button_text, "accept");
            assert_eq!(res.reject_button_text, "reject");
            assert_eq!(res.navigate_hint_text, Some("hint".into()));
            assert_eq!(res.modal_type, CriticalNotificationId::DMACookieConsent);
            assert_eq!(res.accept_ref_marker, Some("accept-ref-marker".into()));
            assert_eq!(res.reject_ref_marker, Some("reject-ref-marker".into()));
            assert_eq!(res.scroll_ref_marker, Some("navigate-ref-marker".into()));
            assert_eq!(res.render_ref_marker, Some("render-ref-marker".into()));
        });
    }

    #[test]
    fn should_generate_modal_quebec_cookie_consent_modal() {
        launch_only_app_context(move |ctx| {
            let mut response = mock_dma_response();
            response.criticalNotificationId = "QuebecCookieConsent".to_string();

            let rust_features: rust_features::MockRustFeatures = MockRustFeaturesBuilder::new()
                .set_is_quebec_cookie_consent_enabled(true)
                .build();
            provide_context(ctx.scope(), rust_features);

            let res = generate_consent_modal(ctx, &response).unwrap();

            assert_eq!(res.title, "title");
            assert_eq!(res.content, Some("description".into()));
            assert_eq!(res.accept_button_text, "accept");
            assert_eq!(res.reject_button_text, "reject");
            assert_eq!(res.navigate_hint_text, Some("hint".into()));
            assert_eq!(res.modal_type, CriticalNotificationId::QuebecCookieConsent);
            assert_eq!(res.accept_ref_marker, Some("accept-ref-marker".into()));
            assert_eq!(res.reject_ref_marker, Some("reject-ref-marker".into()));
            assert_eq!(res.scroll_ref_marker, Some("navigate-ref-marker".into()));
            assert_eq!(res.render_ref_marker, Some("render-ref-marker".into()));
        });
    }

    #[test]
    fn should_not_generate_modal_when_type_is_quebec_and_feature_is_disabled() {
        launch_only_app_context(move |ctx| {
            let mut response = mock_dma_response();
            response.criticalNotificationId = "QuebecCookieConsent".to_string();

            let rust_features = MockRustFeaturesBuilder::new()
                .set_is_quebec_cookie_consent_enabled(false)
                .build();
            provide_context(ctx.scope(), rust_features);

            let res = generate_consent_modal(ctx, &response);
            assert!(res.is_none());
        });
    }

    #[test]
    fn should_not_generate_modal_when_type_is_quebec_and_rust_features_is_missing() {
        launch_only_app_context(move |ctx| {
            let mut response = mock_dma_response();
            response.criticalNotificationId = "QuebecCookieConsent".to_string();

            let res = generate_consent_modal(ctx, &response);
            assert!(res.is_none());
        });
    }

    #[rstest]
    #[case("", None, "")]
    #[case("abc", None, "abc")]
    #[case("link here {link|${amazonServices}}{end} description", Some(HashMap::from( [(
        "amazonServices".to_owned(),
        HyperLink {
            hyperlink: "primevideo.com/amazonservices".to_owned(),
            label: "primevideo.com/amazonservices".to_owned(),
        },
    )])), "link here primevideo.com/amazonservices description")]
    #[case("link here {link|${amazonServices}}{end} description {link|${privacy}}{end} and again {link|${amazonServices}}{end}", Some(HashMap::from( [(
        "amazonServices".to_owned(),
        HyperLink {
            hyperlink: "primevideo.com/amazonservices".to_owned(),
            label: "primevideo.com/amazonservices".to_owned(),
        },
    ), (
        "privacy".to_owned(),
        HyperLink {
            hyperlink: "www.primevideo.com/privacy-notice".to_owned(),
            label: "www.primevideo.com/privacy-notice".to_owned(),
        },
    )])), "link here primevideo.com/amazonservices description www.primevideo.com/privacy-notice and again primevideo.com/amazonservices")]
    fn generates_modal_body_text(
        #[case] string: &str,
        #[case] hyper_links: Option<HashMap<String, HyperLink>>,
        #[case] expected: String,
    ) {
        let data = DmaAndCookieConsentData {
            criticalNotificationId: "DMACookieConsent".into(),
            attributes: None,
            descriptionText: Some(string.to_string()),
            headerText: None,
            renderDelay: None,
            renderDuration: None,
            hyperLinks: hyper_links,
            renderAnalytics: None,
            consentStrings: None,
        };

        let res = generate_modal_body_text_from_response(&data);

        assert_eq!(res, expected);
    }

    fn mock_dma_response() -> DmaAndCookieConsentData {
        DmaAndCookieConsentData {
            criticalNotificationId: "DMACookieConsent".into(),
            attributes: Some(vec![
                Attributes {
                    attributeType: AttributeType::accept,
                    text: Some("accept".into()),
                    analytics: Some(Analytics {
                        refMarker: "accept-ref-marker".into(),
                        pageType: "".into(),
                        subPageType: "".into(),
                        action: "".into(),
                        hitType: "".into(),
                    }),
                },
                Attributes {
                    attributeType: AttributeType::decline,
                    text: Some("reject".into()),
                    analytics: Some(Analytics {
                        refMarker: "reject-ref-marker".into(),
                        pageType: "".into(),
                        subPageType: "".into(),
                        action: "".into(),
                        hitType: "".into(),
                    }),
                },
                Attributes {
                    attributeType: AttributeType::navigate,
                    text: Some("hint".into()),
                    analytics: Some(Analytics {
                        refMarker: "navigate-ref-marker".into(),
                        pageType: "".into(),
                        subPageType: "".into(),
                        action: "".into(),
                        hitType: "".into(),
                    }),
                },
            ]),
            descriptionText: Some("description".into()),
            headerText: Some("title".into()),
            renderDelay: None,
            renderDuration: None,
            hyperLinks: None,
            renderAnalytics: Some(Analytics {
                refMarker: "render-ref-marker".into(),
                pageType: "".into(),
                subPageType: "".into(),
                action: "".into(),
                hitType: "".into(),
            }),
            consentStrings: None,
        }
    }
}
