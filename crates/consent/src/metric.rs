use std::rc::Rc;

use ignx_compositron::prelude::metric;
use mockall::automock;

use crate::CriticalNotificationId;

pub struct MetricCaller;

#[automock]
impl MetricCaller {
    pub fn metric(metric_name: &'static str, val: f64, action_path: &str) {
        metric!(metric_name, val, "Action" => action_path);
    }

    pub fn metric_if_quebec(
        consent_type: Rc<CriticalNotificationId>,
        metric_name: &'static str,
        val: f64,
        action_path: &str,
    ) {
        if let CriticalNotificationId::QuebecCookieConsent = *consent_type {
            MetricCaller::metric(metric_name, val, action_path);
        }
    }
}
