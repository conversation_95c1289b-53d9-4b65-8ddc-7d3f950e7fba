#![allow(nonstandard_style)]

use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::rc::Rc;
use strum_macros::EnumString;

#[derive(Clone, Serialize, EnumString)]
#[derive_test_only(Debug, PartialEq)]
pub enum CriticalNotificationId {
    CookieConsent,
    DMACookieConsent,
    QuebecCookieConsent,
}

#[derive(Clone)]
pub struct ConsentModalData {
    pub title: String,
    pub modal_type: CriticalNotificationId,
    pub content: Option<String>,
    pub accept_button_text: String,
    pub reject_button_text: String,
    pub navigate_hint_text: Option<String>,
    pub on_accept: Rc<dyn Fn()>,
    pub on_reject: Rc<dyn Fn()>,
    pub render_ref_marker: Option<String>,
    pub accept_ref_marker: Option<String>,
    pub reject_ref_marker: Option<String>,
    pub scroll_ref_marker: Option<String>,
}

#[derive(<PERSON>lone, NetworkParsed, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub struct DmaAndCookieConsentData {
    pub criticalNotificationId: String, // Required field. DMA or Cookie
    pub attributes: Option<Vec<Attributes>>, // Used for buttons
    pub descriptionText: Option<String>,
    pub headerText: Option<String>,
    pub renderDelay: Option<u64>, // Delay in milleseconds before a prompt can be displayed - not used
    pub renderDuration: Option<u64>, // Duration of the prompt in milliseconds - not used
    pub hyperLinks: Option<HashMap<String, HyperLink>>, // Maps hyperlink template names to hyperlink URLs and labels, to be injected into the description text.
    pub renderAnalytics: Option<Analytics>,
    pub consentStrings: Option<ConsentStrings>,
}

#[derive(Clone, NetworkParsed, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub struct Attributes {
    pub attributeType: AttributeType,
    pub text: Option<String>,
    pub analytics: Option<Analytics>,
}

#[derive(Clone, NetworkParsed, Serialize, PartialEq)]
#[derive_test_only(Debug)]
pub enum AttributeType {
    accept,
    decline,
    navigate,
}

#[derive(Clone, NetworkParsed, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub struct Analytics {
    pub refMarker: String,
    pub pageType: String,
    pub subPageType: String,
    pub action: String,
    pub hitType: String,
}

#[derive(Clone, NetworkParsed, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub struct ConsentStrings {
    pub maxAgeMillis: Option<u64>, // milliseconds. Client needs to delete consent past this expiration
    pub consentString: Option<ConsentString>, // key: "avlTcf" and "gvlTcf"
}

#[derive(Clone, NetworkParsed, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub struct ConsentString {
    pub avlTcf: Option<String>,
    pub gvlTcf: Option<String>,
}

#[derive(Clone, NetworkParsed, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub struct HyperLink {
    pub hyperlink: String,
    pub label: String,
}

#[derive_test_only(Debug, PartialEq, Clone)]
pub(crate) enum ConsentAction {
    Accept,
    Reject,
}

impl ConsentAction {
    pub(crate) fn to_url_path(&self) -> &str {
        match self {
            ConsentAction::Accept => "/acceptall",
            ConsentAction::Reject => "/rejectall",
        }
    }
}

#[derive_test_only(Debug, PartialEq, Clone)]
pub enum ConsentType {
    DMACookie,
    Cookie,
    QuebecCookie,
}

const ROW_EU_ID: &str = "A2MFUE2XK8ZSSY";
const CANADA_ID: &str = "A2EUQ1WTGCTBG2";

impl ConsentType {
    pub fn get_type_id(&self) -> &str {
        match &self {
            ConsentType::DMACookie => "4",
            ConsentType::Cookie => "0",
            ConsentType::QuebecCookie => "42",
        }
    }

    pub fn get_marketplace_id(&self) -> &str {
        match &self {
            ConsentType::DMACookie => "DEFAULTEU",
            ConsentType::Cookie => ROW_EU_ID,
            ConsentType::QuebecCookie => CANADA_ID,
        }
    }

    pub fn get_domain_id(&self) -> &str {
        match &self {
            ConsentType::QuebecCookie | ConsentType::DMACookie => "0",
            ConsentType::Cookie => "2",
        }
    }
}

#[derive(Clone, Deserialize, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub(crate) enum CookieConsentResponse {
    consents(Vec<ConsentResponse>),
    failedConsents(Vec<MappedConsent>),
}

#[derive(Clone, Deserialize, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub(crate) struct ConsentResponse {
    #[serde(flatten)]
    pub(crate) mappedConsent: MappedConsent,
    pub(crate) consent: Vec<Consent>,
}

#[derive(Clone, Deserialize, Serialize)]
#[derive_test_only(Debug, PartialEq)]
pub(crate) struct Consent {
    pub(crate) avlTcfString: String,
    pub(crate) categoryConsents: Vec<String>,
    pub(crate) createdAt: u64,     // ms
    pub(crate) expiresAt: u64, // sec - service uses DynamoDB TTL to do the expiry and that requires a value in seconds.
    pub(crate) lastUpdatedAt: u64, // ms
    pub(crate) gvlTcfString: String,
}

#[derive(Clone, Deserialize, Serialize, Debug)]
#[derive_test_only(PartialEq)]
pub(crate) struct MappedConsent {
    pub(crate) consentType: String,
    pub(crate) marketplaceId: String,
    pub(crate) domainId: String,
}

#[derive(Serialize)]
pub(crate) struct CookieConsentRequest {
    pub(crate) consents: Vec<MappedConsent>,
}

#[derive(NetworkParsed, Serialize, Clone, Default, Debug)]
#[derive_test_only(PartialEq)]
pub struct GdprPrivacyConsent {
    pub applicable: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub gvlString: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub avlString: Option<String>,
}

impl From<Option<&DmaAndCookieConsentData>> for GdprPrivacyConsent {
    fn from(data: Option<&DmaAndCookieConsentData>) -> Self {
        GdprPrivacyConsent {
            applicable: data.is_some(),
            gvlString: data.as_ref().and_then(|data| {
                data.consentStrings
                    .as_ref()?
                    .consentString
                    .as_ref()?
                    .gvlTcf
                    .clone()
            }),
            avlString: data.and_then(|data| {
                data.consentStrings
                    .as_ref()?
                    .consentString
                    .as_ref()?
                    .avlTcf
                    .clone()
            }),
        }
    }
}

#[cfg(test)]
mod tests {
    use serde_json::json;

    use super::*;
    use rstest::*;
    use std::collections::HashMap;

    #[test]
    fn it_deserializes_critical_notification_response() {
        let response_str = r#"{"criticalNotificationId":"DMACookieConsent","attributes":[{"attributeType":"accept","text":"Accept","analytics":{"refMarker":"atv_dma_[pagetype]_dmacookienotf_accept","pageType":"ATVModal","subPageType":"DMACookie","action":"Click","hitType":"pageHit"}},{"attributeType":"decline","text":"Decline","analytics":{"refMarker":"atv_dma_[pagetype]_dmacookienotf_reject","pageType":"ATVModal","subPageType":"DMACookie","action":"Click","hitType":"pageHit"}},{"attributeType":"navigate","text":"Press left or right to accept or decline","analytics":{"refMarker":"atv_dma_[pagetype]_dmacookienotf_scroll","pageType":"ATVModal","subPageType":"DMACookies","action":"PageNavigate","hitType":"pageHit"}}],"consentStrings":{"maxAgeMillis":86400000,"consentString":{"avlTcf":"avlTcfText","gvlTcf":"gvlTcfText"}},"descriptionText":"description text","headerText":"Cookies and Advertising Choices","hyperLinks":{"privacy":{"hyperlink":"www.primevideo.com/privacy-notice","label":"www.primevideo.com/privacy-notice"},"amazonServices":{"hyperlink":"primevideo.com/amazonservices","label":"primevideo.com/amazonservices"},"cookieprefs":{"hyperlink":"www.primevideo.com/cookie-adprefs","label":"www.primevideo.com/cookie-adprefs"},"thirdParties":{"hyperlink":"www.primevideo.com/cookie-adprefs-partners","label":"www.primevideo.com/cookie-adprefs-partners"}},"renderAnalytics":{"refMarker":"atv_dma_[pagetype]_dmacookienotf_popup","pageType":"ATVModal","subPageType":"DMACookie","action":"ModalRendered","hitType":"popUp"}}"#;

        let result: DmaAndCookieConsentData = network_parse_from_str(response_str).unwrap();

        let expected = DmaAndCookieConsentData {
            criticalNotificationId: "DMACookieConsent".into(),
            attributes: Some(
                [
                    Attributes {
                        attributeType: AttributeType::accept,
                        text: Some("Accept".to_owned()),
                        analytics: Some(Analytics {
                            refMarker: "atv_dma_[pagetype]_dmacookienotf_accept".to_owned(),
                            pageType: "ATVModal".to_owned(),
                            subPageType: "DMACookie".to_owned(),
                            action: "Click".to_owned(),
                            hitType: "pageHit".to_owned(),
                        }),
                    },
                    Attributes {
                        attributeType: AttributeType::decline,
                        text: Some("Decline".to_owned()),
                        analytics: Some(Analytics {
                            refMarker: "atv_dma_[pagetype]_dmacookienotf_reject".to_owned(),
                            pageType: "ATVModal".to_owned(),
                            subPageType: "DMACookie".to_owned(),
                            action: "Click".to_owned(),
                            hitType: "pageHit".to_owned(),
                        }),
                    },
                    Attributes {
                        attributeType: AttributeType::navigate,
                        text: Some("Press left or right to accept or decline".to_owned()),
                        analytics: Some(Analytics {
                            refMarker: "atv_dma_[pagetype]_dmacookienotf_scroll".to_owned(),
                            pageType: "ATVModal".to_owned(),
                            subPageType: "DMACookies".to_owned(),
                            action: "PageNavigate".to_owned(),
                            hitType: "pageHit".to_owned(),
                        }),
                    },
                ]
                .to_vec(),
            ),
            descriptionText: Some("description text".to_string()),
            headerText: Some("Cookies and Advertising Choices".to_string()),
            renderDelay: None,
            renderDuration: None,
            hyperLinks: Some(HashMap::from([
                (
                    "thirdParties".to_owned(),
                    HyperLink {
                        hyperlink: "www.primevideo.com/cookie-adprefs-partners".to_owned(),
                        label: "www.primevideo.com/cookie-adprefs-partners".to_owned(),
                    },
                ),
                (
                    "privacy".to_owned(),
                    HyperLink {
                        hyperlink: "www.primevideo.com/privacy-notice".to_owned(),
                        label: "www.primevideo.com/privacy-notice".to_owned(),
                    },
                ),
                (
                    "amazonServices".to_owned(),
                    HyperLink {
                        hyperlink: "primevideo.com/amazonservices".to_owned(),
                        label: "primevideo.com/amazonservices".to_owned(),
                    },
                ),
                (
                    "cookieprefs".to_owned(),
                    HyperLink {
                        hyperlink: "www.primevideo.com/cookie-adprefs".to_owned(),
                        label: "www.primevideo.com/cookie-adprefs".to_owned(),
                    },
                ),
            ])),
            renderAnalytics: Some(Analytics {
                refMarker: "atv_dma_[pagetype]_dmacookienotf_popup".to_owned(),
                pageType: "ATVModal".to_owned(),
                subPageType: "DMACookie".to_owned(),
                action: "ModalRendered".to_owned(),
                hitType: "popUp".to_owned(),
            }),
            consentStrings: Some(ConsentStrings {
                maxAgeMillis: Some(86400000),
                consentString: Some(ConsentString {
                    avlTcf: Some("avlTcfText".to_owned()),
                    gvlTcf: Some("gvlTcfText".to_owned()),
                }),
            }),
        };

        assert_eq!(result, expected);
    }

    #[test]
    fn it_serializes_gdpr_privacy_consent() {
        let consent = json!{GdprPrivacyConsent{ applicable: true, gvlString: Some("gvlString".into()), avlString: None }}.to_string();
        let expected = "{\"applicable\":true,\"gvlString\":\"gvlString\"}";
        assert_eq!(consent, expected);
    }

    #[test]
    fn it_deserializes_gdpr_privacy_consent() {
        let consent: GdprPrivacyConsent =
            network_parse_from_str("{\"applicable\":false,\"avlString\":\"avlString\"}").unwrap();
        let expected = GdprPrivacyConsent {
            applicable: false,
            gvlString: None,
            avlString: Some("avlString".into()),
        };
        assert_eq!(consent, expected);
    }

    #[rstest]
    #[case(Some(ConsentStrings {
        maxAgeMillis: None,
        consentString: Some(ConsentString {
            avlTcf: Some("avlString".into()),
            gvlTcf: Some("gvlString".into()),
        }),
    }), Some("gvlString".to_string()), Some("avlString".to_string()))]
    #[case(Some(ConsentStrings {
        maxAgeMillis: None,
        consentString: Some(ConsentString {
            avlTcf: Some("avlString".into()),
            gvlTcf: None,
        }),
    }), None, Some("avlString".to_string()))]
    #[case(Some(ConsentStrings {
        maxAgeMillis: None,
        consentString: Some(ConsentString {
            avlTcf: None,
            gvlTcf: Some("gvlString".into()),
        }),
    }), Some("gvlString".to_string()), None)]
    #[case(Some(ConsentStrings {
        maxAgeMillis: None,
        consentString: None,
    }), None, None)]
    #[case(None, None, None)]
    fn gdpr_privacy_consent_from_dma_cookie_consent_data_some(
        #[case] consent_strings: Option<ConsentStrings>,
        #[case] gvl_string: Option<String>,
        #[case] avl_string: Option<String>,
    ) {
        let consent = GdprPrivacyConsent::from(Some(&DmaAndCookieConsentData {
            criticalNotificationId: String::default(),
            attributes: None,
            descriptionText: None,
            headerText: None,
            renderDelay: None,
            renderDuration: None,
            hyperLinks: None,
            renderAnalytics: None,
            consentStrings: consent_strings,
        }));
        let expected = GdprPrivacyConsent {
            applicable: true,
            gvlString: gvl_string,
            avlString: avl_string,
        };
        assert_eq!(consent, expected)
    }

    #[test]
    fn gdpr_privacy_consent_from_dma_cookie_consent_data_none() {
        let consent = GdprPrivacyConsent::from(None);
        let expected = GdprPrivacyConsent {
            applicable: false,
            gvlString: None,
            avlString: None,
        };
        assert_eq!(consent, expected)
    }

    #[test]
    fn test_get_type_id() {
        assert_eq!(ConsentType::DMACookie.get_type_id(), "4");
        assert_eq!(ConsentType::Cookie.get_type_id(), "0");
        assert_eq!(ConsentType::QuebecCookie.get_type_id(), "42");
    }

    #[test]
    fn test_get_marketplace_id() {
        assert_eq!(ConsentType::DMACookie.get_marketplace_id(), "DEFAULTEU");
        assert_eq!(ConsentType::Cookie.get_marketplace_id(), ROW_EU_ID);
        assert_eq!(ConsentType::QuebecCookie.get_marketplace_id(), CANADA_ID);
    }

    #[test]
    fn test_get_domain_id() {
        assert_eq!(ConsentType::DMACookie.get_domain_id(), "0");
        assert_eq!(ConsentType::Cookie.get_domain_id(), "2");
        assert_eq!(ConsentType::QuebecCookie.get_domain_id(), "0");
    }
}
