[package]
name = "consent"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
strum.workspace = true
strum_macros.workspace = true
cfg-test-attr-derive.workspace = true
log.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
fableous.workspace = true
cross-app-events.workspace = true
serde.workspace = true
serde_json.workspace = true
network.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
common-transform-types.workspace = true
mockall.workspace = true
chrono = { workspace = true, features = ["serde"]}
rust-features.workspace = true
mockall_double.workspace = true

[dev-dependencies]
rstest.workspace = true
rust-features.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }
serial_test.workspace = true

[lints]
workspace = true

[features]
debug_impl = []
test_utils = []
