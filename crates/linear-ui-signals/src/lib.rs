use ignx_compositron::reactive::*;

/// `LivePageStateOverrides` provides configuration options page loading behavior
///
/// # Fields
/// * `should_reload` - Signal controlling whether page should perform a full reload.
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct LivePageStateOverrides {
    pub should_reload: RwSignal<bool>,
}

impl LivePageStateOverrides {
    pub fn new(scope: Scope) -> LivePageStateOverrides {
        Self {
            should_reload: create_rw_signal(scope, false),
        }
    }
}
