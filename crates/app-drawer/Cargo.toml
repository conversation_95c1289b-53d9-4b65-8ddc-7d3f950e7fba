[package]
name = "app-drawer"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron.workspace = true
amzn-fable-tokens.workspace = true
log.workspace = true
fableous.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = [
    "test_utils",
    "mock_timer",
] }
rust-features.workspace = true

[[example]]
name = "drawer"
crate-type = ["cdylib"]
path = "examples/drawer.rs"
required-features = ["amzn-ignx-compositron/skip_timer_mocks"]
