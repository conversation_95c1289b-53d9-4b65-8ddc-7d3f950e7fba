use app_drawer::{
    component::*,
    context::AppDrawerContext,
    drawer::{Drawer, DrawerContainer},
};

use fableous::typography::typography::*;
use fableous::SCREEN_HEIGHT;
use fableous::{buttons::primary_button::*, SCREEN_WIDTH};
use ignx_compositron::{compose, id::Id, main, prelude::*, reactive::MaybeSignalTextContent};
use rust_features::MockRustFeaturesBuilder;
use std::rc::Rc;

#[derive(Clone)]
struct DrawerItem {
    text: String,
}

impl Id for DrawerItem {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.text
    }
}

struct SomeDrawer {
    items: VecSignal<DrawerItem>,
    selected_items: RwSignal<Vec<DrawerItem>>,
}

impl DrawerContainer for SomeDrawer {}

impl Drawer for SomeDrawer {
    fn on_closed(&self, _ctx: &AppContext) {
        log::warn!("drawer closing");
    }

    fn render(&self, ctx: &AppContext, drawer_context: Rc<AppDrawerContext>) -> ColumnComposable {
        let items = self.items;
        let selected_items = self.selected_items;

        let filter_builder = Rc::new(move |ctx: &AppContext, item: &DrawerItem, _idx: usize| {
            let selected_items = selected_items;
            let drawer_item = item.clone();

            compose! {
                PrimaryButton(variant: PrimaryButtonVariant::TextSize800(drawer_item.text.clone().into()))
                    .on_select(move || {
                        let item = (&drawer_item).clone();
                        selected_items.update(|v| v.push(item));
                    })
            }
        });

        compose! {
            Column() {
                TypographyHeading100(content: "Filter Options")
                PrimaryButton(variant: PrimaryButtonVariant::TextSize800("Clear all filters".into()))
                    .on_select(move || {
                        selected_items.update(|v| v.clear());
                        let _ = drawer_context.close_drawer();
                    })
                ColumnList(items, item_builder: filter_builder)
            }
            .cross_axis_stretch(true)
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .main_axis_alignment(MainAxisAlignment::Center)
        }
    }
}

#[main]
fn main() {
    launch_composable(|ctx| {
        let app_drawer_context = Rc::new(AppDrawerContext::new(ctx.scope()));
        provide_context(ctx.scope(), app_drawer_context.clone());
        MockRustFeaturesBuilder::new().build_as_mock_and_real_into_context(true, ctx.scope());

        let content = get_content(ctx.scope());
        let label = Signal::derive(ctx.scope(), {
            let selected_items = content.selected_items;
            move || {
                let selection = selected_items
                    .get()
                    .iter()
                    .map(|item| item.text.clone())
                    .collect::<Vec<String>>()
                    .join(",");
                format!("Selected: {selection}")
            }
        });

        let open_drawer = {
            move || {
                let content = Rc::clone(&content);
                let _ = app_drawer_context.open_drawer(content);
            }
        };
        compose! {
            Stack(){
                Column(){
                    TypographyLabel800(content: MaybeSignalTextContent::String(MaybeSignal::Dynamic(label)))
                    Button(text: "open").on_select(open_drawer)
                }
                AppDrawer()
            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
        }
    });
}

fn get_content(scope: Scope) -> Rc<SomeDrawer> {
    Rc::new(SomeDrawer {
        items: create_vec_signal(
            scope,
            vec![
                DrawerItem { text: "One".into() },
                DrawerItem { text: "two".into() },
                DrawerItem { text: "six".into() },
                DrawerItem { text: "ten".into() },
            ],
        ),
        selected_items: create_rw_signal(scope, vec![]),
    })
}
