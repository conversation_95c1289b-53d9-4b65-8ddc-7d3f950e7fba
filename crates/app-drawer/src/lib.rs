//! A flexible drawer system for Fable UI applications.
//!
//! This crate provides a customizable drawer implementation that slides in from the right edge
//! of the screen. It includes a context-based management system, trait-based content definition,
//! and animated transitions.
//!
//! Key features:
//! - Context-based drawer state management
//! - Customizable drawer dimensions and styling
//! - Animated open/close transitions
//! - Type-safe content rendering through traits
//!
//! The crate is built on top of the Fable UI system and integrates with its design tokens
//! and component patterns
//!
//!
//! # Usage
//! [`AppDrawer`][comp] lives in app level which can be opened from any page. Use
//! [`use_drawer_context`][hook] hook to open and close the drawer.
//!
//! ```
//! # use ignx_compositron::prelude::*;
//! # use std::rc::Rc;
//! use app_drawer::{context::use_drawer_context, drawer::Drawer};
//!
//! fn open_drawer_with_content(scope: Scope, content: Rc<dyn Drawer>){
//!     let drawer_context = use_drawer_context(scope);
//!     let result = drawer_context.open_drawer(content);
//!     if let Err(e) = result {
//!         log::error!("Cannot open the app drawer. {:?}", e)
//!     }
//! }
//! ```
//!
//! [comp]: crate::component::AppDrawer
//! [hook]: crate::context::use_drawer_context

pub mod component;
pub mod context;
mod defaults;
pub mod drawer;
