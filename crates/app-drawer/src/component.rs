use amzn_fable_tokens::FableColor;
use fableous::{
    pointer_control::use_focus_pointer_control, utils::get_ignx_color, SCREEN_HEIGHT, SCREEN_WIDTH,
};
use ignx_compositron::{compose, compose_option, prelude::*, Composer};

use crate::context::{use_drawer_context, DrawerContent};
use inner::*;

#[Composer]
pub fn AppDrawer(ctx: &AppContext) -> StackComposable {
    let modal_opacity: RwSignal<f32> = create_rw_signal(ctx.scope(), 0.0);

    let item_builder = Box::new({
        move |ctx: &AppContext| {
            let drawer_context = use_drawer_context(ctx.scope());
            let DrawerContent::Content(content) = drawer_context.content.get() else {
                return None;
            };

            compose_option! {
                AppDrawerInner(content, background_opacity: modal_opacity.write_only())
                    .focus_window()
                    .focus_pointer_control(use_focus_pointer_control(ctx.scope()))
            }
        }
    });

    compose! {
        Stack() {
            Rectangle()
            .height(SCREEN_HEIGHT)
            .width(SCREEN_WIDTH)
            .background_color(get_ignx_color(FableColor::OVERLAY))
            .opacity(modal_opacity)
            Memo(item_builder)
        }
        .alignment(Alignment::EndTop)
        .height(SCREEN_HEIGHT)
        .width(SCREEN_WIDTH)
        .test_id("app-drawer-wrapper")
    }
}

mod inner {
    use fableous::{
        animations::{
            fable_motion_linear_medium, FableMotionDuration, FableMotionEasing, MotionEasing,
        },
        utils::get_ignx_color,
        SCREEN_HEIGHT,
    };
    use ignx_compositron::{
        prelude::*,
        time::{Duration, Instant},
        Composer,
    };
    use std::rc::Rc;

    use crate::{
        context::{use_drawer_context, DrawerContent},
        drawer::Drawer,
    };

    #[Composer]
    pub fn AppDrawerInner(
        ctx: &AppContext,
        content: Rc<dyn Drawer>,
        background_opacity: WriteSignal<f32>,
    ) -> ColumnComposable {
        let drawer_context = use_drawer_context(ctx.scope());
        let width = content.width();
        let padding = content.padding();
        let background_color = content.background_color();
        let drawer_content = content.render(ctx, drawer_context);
        let (x_position, set_x_position) = create_signal(ctx.scope(), width);

        // Schedule the animation to next tick to properly animate the component. Initial execution of
        // this component has to finish before animation kicks in.
        ctx.schedule_task(Instant::now() + Duration::from_millis(1), {
            let ctx = ctx.clone();
            move || {
                ctx.with_animation(
                    Animation::default()
                        .with_duration(FableMotionDuration::Standard.into())
                        .with_interpolation(FableMotionEasing::Enter.to_interpolation()),
                    move || {
                        set_x_position.try_set(0.0);
                    },
                );
                ctx.with_animation(fable_motion_linear_medium(), || {
                    background_opacity.try_set(1.0);
                });
            }
        });

        create_effect(ctx.scope(), {
            let ctx = ctx.clone();
            let drawer_context = use_drawer_context(ctx.scope());
            move |_| {
                let content = content.clone();
                let drawer_context = drawer_context.clone();

                if drawer_context.close_signal.get() {
                    let ctx_on_closed = ctx.clone();
                    ctx.with_animation_completion(
                        Animation::default()
                            .with_duration(FableMotionDuration::Medium.into())
                            .with_interpolation(FableMotionEasing::Exit.to_interpolation()),
                        move || {
                            set_x_position.try_set(width);
                        },
                        move || {
                            content.on_closed(&ctx_on_closed);
                            if drawer_context.close_signal.try_set(false).is_some() {
                                log::error!("[scope disposed] Cannot set app-drawer close_signal.");
                            };
                            if drawer_context
                                .content
                                .try_set(DrawerContent::Empty)
                                .is_some()
                            {
                                log::error!("[scope disposed] Cannot set app-drawer content.");
                            };
                        },
                    );
                    ctx.with_animation(fable_motion_linear_medium(), || {
                        background_opacity.try_set(0.0)
                    });
                }
            }
        });

        Column(
            ColumnProps {
                __marker: Default::default(),
                ctx,
            },
            ignx_compositron::composable::builder_fn(|parent| {
                HierarchicalComposable::with_child(parent, drawer_content);
            }),
        )
        .width(width)
        .height(SCREEN_HEIGHT)
        .translate_x(x_position)
        .background_color(get_ignx_color(background_color))
        .padding(padding)
        .test_id("app-drawer")
    }
}

#[cfg(test)]
mod tests {
    use amzn_fable_tokens::FableColor;
    use fableous::utils::get_ignx_color;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_exists, node_properties::SceneNodeTree, ComposableType},
        time::{Duration, MockClock},
    };
    use rust_features::MockRustFeaturesBuilder;

    use std::rc::Rc;

    use super::*;
    use crate::context::AppDrawerContext;
    use drawer_impl::*;

    fn provide_app_drawer_context(scope: Scope) {
        let adc = Rc::new(AppDrawerContext::new(scope));
        MockRustFeaturesBuilder::new().build_as_mock_and_real_into_context(true, scope);
        provide_context(scope, adc);
    }

    #[test]
    fn renders_app_drawer() {
        launch_test(
            move |ctx| {
                provide_app_drawer_context(ctx.scope());
                compose! {
                    AppDrawer()
                }
            },
            move |_scope, mut gl| {
                let tree = gl.tick_until_done();

                let wrapper = tree.find_by_test_id("app-drawer-wrapper");
                assert_node_exists!(&wrapper);

                let background = wrapper.find_child_with().find_first();
                assert_eq!(background.borrow_props().is_visible, false);
                assert_eq!(
                    background.borrow_props().layout.border_box.height,
                    SCREEN_HEIGHT
                );
                assert_eq!(
                    background.borrow_props().layout.border_box.width,
                    SCREEN_WIDTH
                );

                let childrens = wrapper.find_child_with().find_all();
                assert_eq!(childrens.len(), 2);
                let inner = childrens.get(1);
                assert!(inner.is_some());
                let inner = inner.unwrap();
                // renders nothing initially
                assert_eq!(
                    inner.borrow_props().composable_type,
                    ComposableType::Nothing
                );
            },
        );
    }

    #[test]
    fn opens_drawer_when_content_set() {
        launch_test(
            move |ctx| {
                provide_app_drawer_context(ctx.scope());
                compose! {
                    AppDrawer()
                }
            },
            move |scope, mut gl| {
                let tree = gl.tick_until_done();
                rendered_drawer_wrapper_without_content(&tree);
                open_drawer(scope);

                // advance clock to animate the drawer
                MockClock::advance(Duration::from_millis(1));
                let tree = gl.tick_until_done();

                let drawer = tree.find_by_test_id("app-drawer");
                assert_node_exists!(&drawer);

                // renders the content from Drawer trait impl
                let heading = drawer
                    .find_child_with()
                    .filter_for_child_with()
                    .text("Example Drawer")
                    .find_first();
                assert_node_exists!(&heading);

                let some_item = drawer.find_any_child_with().text("just field").find_first();
                assert_node_exists!(&some_item);

                let another_item = drawer
                    .find_any_child_with()
                    .text("another field")
                    .find_first();
                assert_node_exists!(&another_item);
            },
        )
    }

    #[test]
    fn closes_drawer_when_requested() {
        launch_test(
            move |ctx| {
                provide_app_drawer_context(ctx.scope());
                compose! {
                    AppDrawer()
                }
            },
            move |scope, mut gl| {
                let tree = gl.tick_until_done();
                rendered_drawer_wrapper_without_content(&tree);
                open_drawer(scope);

                // advance clock to animate the drawer
                MockClock::advance(Duration::from_millis(1));
                let tree = gl.tick_until_done();

                let drawer = tree.find_by_test_id("app-drawer");
                assert_node_exists!(&drawer);

                let item_in_drawer = drawer.find_any_child_with().text("holy field").find_first();
                assert_node_exists!(&item_in_drawer);

                let app_drawer_context = use_drawer_context(scope);
                let result = app_drawer_context.close_drawer();
                assert!(result.is_ok());

                let tree = gl.tick_until_done();
                rendered_drawer_wrapper_without_content(&tree);
            },
        )
    }

    #[test]
    fn opens_custom_drawer() {
        launch_test(
            move |ctx| {
                provide_app_drawer_context(ctx.scope());
                compose! {
                    AppDrawer()
                }
            },
            move |scope, mut gl| {
                let tree = gl.tick_until_done();
                rendered_drawer_wrapper_without_content(&tree);
                let custom_drawer = Rc::new(get_custom_drawer(scope));
                let app_drawer_context = use_drawer_context(scope);
                let result = app_drawer_context.open_drawer(custom_drawer.clone());
                assert!(result.is_ok());

                // advance clock to animate the drawer
                MockClock::advance(Duration::from_millis(1));
                let tree = gl.tick_until_done();

                let drawer = tree.find_by_test_id("app-drawer");
                assert_node_exists!(&drawer);

                assert_eq!(drawer.borrow_props().layout.size.width, 1881.0);
                assert_eq!(
                    drawer.borrow_props().base_styles.background_color,
                    Some(get_ignx_color(FableColor::COOL400))
                );

                let item_in_drawer = drawer.find_any_child_with().text("widdee").find_first();
                assert_node_exists!(&item_in_drawer);
            },
        )
    }

    fn rendered_drawer_wrapper_without_content(tree: &SceneNodeTree) {
        let wrapper = tree.find_by_test_id("app-drawer-wrapper");
        let drawer_placeholder = wrapper.find_child_with().find_by_index(1);
        assert_node_exists!(&drawer_placeholder);
        assert_eq!(
            drawer_placeholder.borrow_props().composable_type,
            ComposableType::Nothing
        );
    }

    fn open_drawer(scope: Scope) {
        let app_drawer_context = use_drawer_context(scope);
        let drawer_items = Rc::new(get_some_drawer(scope));
        let result = app_drawer_context.open_drawer(drawer_items.clone());
        assert!(result.is_ok());
    }

    // implementation for Drawer to use in tests
    mod drawer_impl {
        use crate::{
            context::AppDrawerContext,
            drawer::{Drawer, DrawerContainer},
        };
        use amzn_fable_tokens::FableColor;
        use fableous::buttons::primary_button::*;
        use fableous::typography::typography::*;
        use ignx_compositron::id::Id;
        use ignx_compositron::reactive::MaybeSignalTextContent;
        use ignx_compositron::{compose, prelude::*};
        use std::rc::Rc;

        pub fn get_some_drawer(scope: Scope) -> SomeDrawer {
            let items = vec![
                DrawerItem {
                    text: "field".into(),
                },
                DrawerItem {
                    text: "another field".into(),
                },
                DrawerItem {
                    text: "one more field".into(),
                },
                DrawerItem {
                    text: "just field".into(),
                },
                DrawerItem {
                    text: "holy field".into(),
                },
            ];
            SomeDrawer {
                items: create_vec_signal(scope, items),
            }
        }

        pub fn get_custom_drawer(scope: Scope) -> CustomDrawer {
            let items = vec![DrawerItem {
                text: "widdee".into(),
            }];
            CustomDrawer {
                items: create_vec_signal(scope, items),
            }
        }

        #[derive(Clone)]
        pub struct DrawerItem {
            text: String,
        }

        impl Id for DrawerItem {
            type Id = String;

            fn id(&self) -> &Self::Id {
                &self.text
            }
        }

        pub struct SomeDrawer {
            items: VecSignal<DrawerItem>,
        }

        impl DrawerContainer for SomeDrawer {}

        impl Drawer for SomeDrawer {
            fn on_closed(&self, _ctx: &AppContext) {
                log::warn!("drawer closing");
            }

            fn render(
                &self,
                ctx: &AppContext,
                drawer_context: Rc<AppDrawerContext>,
            ) -> ColumnComposable {
                let items = self.items;

                compose! {
                    Column() {
                        TypographyHeading100(content: "Example Drawer")
                        PrimaryButton(variant: PrimaryButtonVariant::TextSize800("Close Drawer".into()))
                        .preferred_focus(true)
                        .on_select(move || {
                                let _ = drawer_context.close_drawer();
                        })
                        ColumnList(items, item_builder: Rc::new(|ctx, item, _idx|{
                                compose!{
                                    PrimaryButton(variant: PrimaryButtonVariant::TextSize400(item.text.clone().into()))
                                    .on_select(move || {
                                        log::info!("selected")
                                    })
                                }
                            }))
                    }
                    .cross_axis_stretch(true)
                    .cross_axis_alignment(CrossAxisAlignment::Center)
                    .main_axis_alignment(MainAxisAlignment::Center)
                }
            }
        }

        pub struct CustomDrawer {
            items: VecSignal<DrawerItem>,
        }

        impl DrawerContainer for CustomDrawer {
            fn width(&self) -> f32 {
                1881.0
            }

            fn padding(&self) -> Padding {
                Padding {
                    start: 42.0,
                    end: 42.0,
                    top: 42.0,
                    bottom: 42.0,
                }
            }

            fn background_color(&self) -> amzn_fable_tokens::FableColor {
                FableColor::COOL400
            }
        }

        impl Drawer for CustomDrawer {
            fn on_closed(&self, ctx: &AppContext) {
                let counter = use_context::<RwSignal<i32>>(ctx.scope());
                if let Some(counter) = counter {
                    counter.update(|c| *c += 1);
                }
            }

            fn render(
                &self,
                ctx: &AppContext,
                _drawer_context: Rc<AppDrawerContext>,
            ) -> ColumnComposable {
                let items = self.items;
                compose! {
                    Column(){
                        TypographyHeading400(content: "Wider Drawer")
                        ColumnList(items, item_builder: Rc::new(|ctx, item, _idx|{
                            compose!{
                                TypographyHeading100(content:
                                    MaybeSignalTextContent::String(item.text.clone().into())
                                )
                            }
                        }))

                    }
                }
            }
        }
    }
}
