//! Drawer context management and global state handling.
//!
//! This module provides the global context and state management for the application's drawer system.
//! It includes functionality to open and close drawers, manage drawer content, and handle drawer-related
//! actions throughout the application.

use ignx_compositron::prelude::*;
use std::rc::Rc;

use crate::drawer::Drawer;

/// Represents the content state of a drawer.
///
/// This enum is used to manage the content of the drawer, allowing it to either
/// contain actual drawer content or be empty.
#[derive(Clone)]
pub enum DrawerContent {
    Content(Rc<dyn Drawer>),
    Empty,
}

/// Global context to interact with the application drawer system.
///
/// This context provides methods to manage drawer state and content throughout the application.
/// It can be accessed using the [`use_drawer_context`] function.
///
/// # Example
/// ```
/// # use ignx_compositron::{prelude::*, compose};
/// use app_drawer::context::use_drawer_context;
///
/// fn MyComponent(ctx: &AppContext) ->  ButtonComposable {
///     let drawer_ctx = use_drawer_context(ctx.scope());
///     
///     // Use drawer context to manage drawer state
///     compose! {
///         Button(text: "Close Drawer")
///         .on_select(move || {
///             let _ = drawer_ctx.close_drawer();
///         })
///     }
/// }
/// ```
pub struct AppDrawerContext {
    pub(crate) content: RwSignal<DrawerContent>,
    pub(crate) close_signal: RwSignal<bool>,
    pub(crate) is_drawer_open: Signal<bool>,
}

/// Errors that can occur during drawer operations.
#[derive(Debug)]
pub enum DrawerActionError {
    InvalidSignal,
    NoContent,
}

/// Retrieves or creates the drawer context for the current scope.
///
/// This function will either return an existing drawer context from the current scope
/// or create a new one if none exists.
pub fn use_drawer_context(scope: Scope) -> Rc<AppDrawerContext> {
    match use_context::<Rc<AppDrawerContext>>(scope) {
        Some(c) => c,
        None => {
            let drawer_context = Rc::new(AppDrawerContext::new(scope));
            provide_context(scope, drawer_context.clone());
            drawer_context
        }
    }
}

impl AppDrawerContext {
    /// Creates a new drawer context with empty content.
    ///
    /// # Arguments
    /// * `scope` - The scope in which to create the signals for the context
    ///
    /// # Usage
    /// **Don't** use this function. Use [`use_drawer_context`] to retrieve [`AppDrawerContext`].
    pub fn new(scope: Scope) -> Self {
        let content = create_rw_signal(scope, DrawerContent::Empty);
        let is_drawer_open = Signal::derive(scope, move || {
            content.with(|c| matches!(c, DrawerContent::Content(_)))
        });

        Self {
            content,
            close_signal: create_rw_signal(scope, false),
            is_drawer_open,
        }
    }

    /// Sets the content of the drawer and opens it.
    ///
    /// # Attention
    /// This method immediately executes the `AppDrawer` composable with the provided content.
    ///
    /// # Example
    /// ```
    /// # use ignx_compositron::prelude::*;
    /// use app_drawer::context::use_drawer_context;
    /// use app_drawer::drawer::Drawer;
    /// use std::rc::Rc;
    /// # fn example(scope: Scope, content: Rc<dyn Drawer>){
    /// let drawer_ctx = use_drawer_context(scope);
    ///
    /// if let Err(e) = drawer_ctx.open_drawer(content) {
    ///     log::error!("Failed to open drawer: {:?}", e);
    /// }
    /// # }
    /// ```
    pub fn open_drawer(&self, content: Rc<dyn Drawer>) -> Result<(), DrawerActionError> {
        match self.content.try_set(DrawerContent::Content(content)) {
            Some(_) => Err(DrawerActionError::InvalidSignal),
            None => Ok(()),
        }
    }

    /// Signals the drawer to close.
    ///
    /// # Returns
    /// * `Ok(())` if the close signal was set successfully
    /// * `Err(DrawerActionError::NoContent)` if there is no drawer content to close
    ///
    /// # Example
    /// ```
    /// # use ignx_compositron::prelude::*;
    /// use app_drawer::context::use_drawer_context;
    /// # fn example(scope: Scope) {
    /// let drawer_ctx = use_drawer_context(scope);
    ///
    /// if let Err(e) = drawer_ctx.close_drawer() {
    ///     log::error!("Failed to close drawer: {:?}", e);
    /// }
    /// # }
    /// ```
    pub fn close_drawer(&self) -> Result<(), DrawerActionError> {
        let _ = match self.content.get_untracked() {
            DrawerContent::Content(c) => c,
            DrawerContent::Empty => return Err(DrawerActionError::NoContent),
        };

        self.close_signal.set(true);
        Ok(())
    }

    /// Returns a derived signal that indicates whether the drawer is currently open.
    ///
    /// This signal updates automatically when the drawer's content changes, making it useful
    /// for reactive UI components that need to respond to drawer state changes.
    ///
    /// # Example
    /// ```
    /// # use ignx_compositron::{prelude::*, compose};
    /// use app_drawer::context::use_drawer_context;
    ///
    /// fn MyComponent(ctx: &AppContext) -> StackComposable {
    ///     let drawer_ctx = use_drawer_context(ctx.scope());
    ///     let is_open = drawer_ctx.is_drawer_open();
    ///
    ///     compose! {
    ///         Stack() {
    ///             // Render different UI based on drawer state
    ///             if is_open.get() {
    ///                 Label(text: "Drawer is open")
    ///             } else {
    ///                 Label(text: "Drawer is closed")
    ///             }
    ///         }
    ///     }
    /// }
    /// ```
    pub fn is_drawer_open(&self) -> Signal<bool> {
        self.is_drawer_open
    }

    /// Provides the drawer context to the given scope.
    ///
    /// This is a test utility function that creates a new [`AppDrawerContext`] and provides it
    /// to the specified scope. It's intended to be used in test scenarios to set up the necessary
    /// context without the full application initialization. Its also used in app-start to
    /// initialize the [`AppDrawerContext`]
    ///
    /// # Warning
    /// Don't use this function to provide context outside of app-start and tests
    ///
    /// # Example
    /// ```
    /// # use ignx_compositron::prelude::*;
    /// # use app_drawer::context::AppDrawerContext;
    /// # use ignx_compositron::app::launch_test;
    /// #[test]
    /// fn test_drawer_component() {
    ///     launch_test(
    ///         |ctx: AppContext| {
    ///             AppDrawerContext::provide_context(ctx.scope());
    ///             // Test component that uses drawer context
    ///         },
    ///         |scope: Scope, gl: TestRendererGameLoop| {
    ///             // Test assertions
    ///         }
    ///     );
    /// }
    /// ```
    pub fn provide_context(scope: Scope) {
        provide_context(scope, Rc::new(AppDrawerContext::new(scope)));
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::drawer::DrawerContainer;
    use ignx_compositron::{app::launch_only_scope, compose};

    #[test]
    fn creates_app_drawer_context() {
        launch_only_scope(|scope| {
            let ctx = AppDrawerContext::new(scope);
            assert!(matches!(ctx.content.get_untracked(), DrawerContent::Empty));
        });
    }

    mod open_drawer {
        use super::*;

        #[test]
        fn sets_content_when_there_are_none() {
            launch_only_scope(|scope| {
                let ctx = AppDrawerContext::new(scope);
                let content = Rc::new(Content {});

                let result = ctx.open_drawer(content);
                assert!(result.is_ok());
            });
        }

        #[test]
        fn returns_error_when_scope_is_disposed() {
            launch_only_scope(|scope| {
                let ctx = AppDrawerContext::new(scope);
                let content = Rc::new(Content {});

                scope.dispose();

                let result = ctx.open_drawer(content);
                assert!(result.is_err());
            });
        }
    }

    mod close_drawer {
        use super::*;

        #[test]
        fn sets_close_signal_true_if_there_are_content() {
            launch_only_scope(|scope| {
                let ctx = AppDrawerContext::new(scope);

                // set content first
                let content = Rc::new(Content {});
                let _ = ctx.open_drawer(content);

                assert_eq!(ctx.close_signal.get_untracked(), false);
                let result = ctx.close_drawer();
                assert!(result.is_ok());
                assert_eq!(ctx.close_signal.get_untracked(), true);
            });
        }

        #[test]
        fn returns_error_if_there_are_no_content() {
            launch_only_scope(|scope| {
                let ctx = AppDrawerContext::new(scope);

                assert_eq!(ctx.close_signal.get_untracked(), false);
                let result = ctx.close_drawer();
                assert!(result.is_err());
            });
        }
    }

    mod use_drawer_context {
        use super::*;

        #[test]
        fn gets_context() {
            launch_only_scope(|scope| {
                provide_context(scope, Rc::new(AppDrawerContext::new(scope)));
                let ctx = use_drawer_context(scope);
                assert!(matches!(ctx.content.get_untracked(), DrawerContent::Empty));
            });
        }

        #[test]
        fn provides_context_if_not_provided() {
            launch_only_scope(|scope| {
                let ctx = use_drawer_context(scope);
                assert!(matches!(ctx.content.get_untracked(), DrawerContent::Empty));
            });
        }
    }

    #[cfg(feature = "test_utils")]
    mod provide_context {
        use super::*;

        #[test]
        fn provide_context_in_tests() {
            launch_only_scope(|scope| {
                AppDrawerContext::provide_context(scope);
                let ctx = use_context::<Rc<AppDrawerContext>>(scope);
                assert!(ctx.is_some());
            });
        }
    }

    struct Content {}
    impl DrawerContainer for Content {}
    impl Drawer for Content {
        fn on_closed(&self, _ctx: &AppContext) {}

        fn render(
            &self,
            ctx: &AppContext,
            _drawer_context: Rc<AppDrawerContext>,
        ) -> ColumnComposable {
            compose! {
                Column(){}
            }
        }
    }
}
