//! Core traits for implementing drawer components in the UI system.
//!
//! This module provides the fundamental traits needed to create and customize drawer components:
//! - [`Drawer`]: Core behavior and content rendering
//! - [`DrawerContainer`]: Visual and layout customization
//!
//! # Usage
//! To render content in the drawer:
//! 1. Create a struct that holds the drawer content.
//! 2. Implement [`DrawerContainer`] (defaults provided)
//! 3. Implement [`Drawer`] to define content and behavior
//! 4. Open drawer via [`AppDrawerContext`]
//!
//! [`AppDrawerContext`]: crate::context::AppDrawerContext

use crate::{
    context::AppDrawerContext,
    defaults::{get_default_padding, DEFAULT_WIDTH},
};
use amzn_fable_tokens::FableColor;
use ignx_compositron::{layout::Padding, prelude::*};
use std::rc::Rc;

/// Defines the core behavior of a Drawer component.
///
/// This trait provides the essential functionality for drawer components in the UI system.
/// It requires implementation of both rendering logic and close handling, while inheriting
/// container customization options from [`DrawerContainer`].
///
/// # Required Methods
/// - [`on_closed`](Drawer::on_closed): perform operations when drawer is closed for your data.
/// - [`render`](Drawer::render): Defines the content and layout of the drawer
///
/// # Example
/// ```
/// # use app_drawer::{context::AppDrawerContext, drawer::{Drawer, DrawerContainer}};
/// # use fableous::buttons::primary_button::*;
/// # use fableous::typography::typography::*;
/// # use ignx_compositron::{column::*, id::Id, prelude::*, compose};
/// # use std::rc::Rc;
///
/// #[derive(Clone)]
/// struct DrawerItem {
///     text: String,
///     id: i32,
/// }
/// # impl Id for DrawerItem {
/// #   type Id = i32;
/// #
/// #   fn id(&self) -> &Self::Id {
/// #       &self.id
/// #   }
/// # }
/// struct MyDrawer {
///     items: VecSignal<DrawerItem>,
/// }
///  
/// impl DrawerContainer for MyDrawer {} // Use default container settings
///  
/// impl Drawer for MyDrawer {
///     fn on_closed(&self, _ctx: &AppContext) {
///         log::info!("MyDrawer closed");
///         // metric!("MyDrawer.Closed", 1);
///     }
///  
///     fn render(&self, ctx: &AppContext, drawer_context: Rc<AppDrawerContext>) -> ColumnComposable {
///         let items = self.items;
///  
///         compose! {
///             Column() {
///                 TypographyHeading800(content: "Drawer Header")
///                 ColumnList(items, item_builder: Rc::new(|ctx,item,_idx|{
///                     compose! {
///                         TypographyHeading100(content: item.clone().text)
///                     }
///                 }))
///                 PrimaryButton(variant: PrimaryButtonVariant::TextSize400("Close Drawer".into()))
///                 .on_select(move || {
///                     let _ = drawer_context.close_drawer();
///                 })
///             }
///             .cross_axis_stretch(true)
///             .cross_axis_alignment(CrossAxisAlignment::Center)
///             .main_axis_alignment(MainAxisAlignment::Center)
///         }
///     }
/// }
/// ```
pub trait Drawer: DrawerContainer {
    fn on_closed(&self, ctx: &AppContext);
    fn render(&self, ctx: &AppContext, drawer_context: Rc<AppDrawerContext>) -> ColumnComposable;
}

/// Customizes the container properties of a Drawer component.
///
/// This trait allows customization of the drawer's visual and layout properties.
/// All methods have default implementations matching Fable Drawer's standard appearance.
///
/// # Default Values
/// - Width: 789.0
/// - Padding: Standard Fable drawer padding
/// - Background Color: [`FableColor::BACKGROUND`]
///
/// # Example
/// ```
/// use app_drawer::drawer::DrawerContainer;
///
/// struct CustomDrawer {
///     // Your drawer state
/// }
///
/// impl DrawerContainer for CustomDrawer {
///     // Override only what you need
///     fn width(&self) -> f32 {
///         1881.0  // Custom width
///     }
///
///     // Other properties will use defaults
/// }
/// ```
pub trait DrawerContainer {
    fn width(&self) -> f32 {
        DEFAULT_WIDTH
    }

    fn padding(&self) -> Padding {
        get_default_padding()
    }

    fn background_color(&self) -> FableColor {
        FableColor::BACKGROUND
    }
}
