use ignx_compositron::metrics::metric;
use location::RustPage;
use std::fmt;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq)]
pub enum ATVPageType {
    ATVAuthentication,
    ATVHome,
    ATVPlayer,
    ATVSearch,
    ATVMyStuff,
    ATVSubscription,
    ATVSignIn,
    ATVMovie,
    ATVTV,
    ATVSettings,
    ATVDetail,
    ATVAIVSignup,
    ATV3PSignup,
    ATVBrowse,
    ATVXRay,
    ATVImdb,
    ATVProfiles,
    ATVDownloads,
    ATVDeepLinking,
    ATVMerch,
    ATVLinking,
    ATVModal,
    ATVHelp,
    ATVError,
    ATVDiscovery,
    ATVSportsFavoritesDiscovery,
    ATVSportsSchedule,
    Other,
}

impl fmt::Display for ATVPageType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{:?}", self)
    }
}

// The format of the following CX fatal / CX error logs and metrics are defined by https://quip-amazon.com/0te5ATdPTsjV/App-Experience-Availability-KPIs
pub fn report_cx_fatal(page: RustPage) {
    let atv_page_type = get_atv_page_type(page);
    metric!("PageCxFatal", 1, "pageType" => atv_page_type);
    log::error!(page = atv_page_type.to_string(), reportOwner = "RUST"; "PageCxFatal");
}

pub fn report_cx_error(page: RustPage) {
    let atv_page_type = get_atv_page_type(page);
    metric!("PageCxError", 1, "pageType" => atv_page_type);
    log::error!(page = atv_page_type.to_string(), reportOwner = "RUST"; "PageCxError");
}

fn get_atv_page_type(page: RustPage) -> ATVPageType {
    match page {
        RustPage::RUST_PROFILE_SELECTION | RustPage::RUST_AVATAR_SELECTION => ATVPageType::ATVProfiles,
        RustPage::RUST_COLLECTIONS | RustPage::RUST_LIVE_TV => ATVPageType::ATVHome,
        RustPage::RUST_DETAILS | RustPage::RUST_STATION_DETAILS => ATVPageType::ATVDetail,
        RustPage::RUST_EMPTY
        | RustPage::RUST_WELCOME
        | RustPage::RUST_CHECKOUT
        | RustPage::RUST_GO_TO_BACKGROUND
        | RustPage::RUST_TVIF => ATVPageType::Other,
        RustPage::RUST_DISCOVERY_PAGE => ATVPageType::ATVDiscovery,
        RustPage::RUST_PLAYBACK => ATVPageType::ATVPlayer,
        RustPage::RUST_SEARCH => ATVPageType::ATVSearch,
        RustPage::RUST_SPORTS_FAVORITES_DISCOVERY => ATVPageType::ATVSportsFavoritesDiscovery,
        RustPage::RUST_SPORTS_SCHEDULE_PAGE => ATVPageType::ATVSportsSchedule,
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use location::RustPage;
    use rstest::rstest;

    #[rstest]
    #[case(RustPage::RUST_PROFILE_SELECTION, ATVPageType::ATVProfiles)]
    #[case(RustPage::RUST_COLLECTIONS, ATVPageType::ATVHome)]
    #[case(RustPage::RUST_LIVE_TV, ATVPageType::ATVHome)]
    #[case(RustPage::RUST_DETAILS, ATVPageType::ATVDetail)]
    #[case(RustPage::RUST_STATION_DETAILS, ATVPageType::ATVDetail)]
    #[case(RustPage::RUST_EMPTY, ATVPageType::Other)]
    #[case(RustPage::RUST_WELCOME, ATVPageType::Other)]
    #[case(RustPage::RUST_DISCOVERY_PAGE, ATVPageType::ATVDiscovery)]
    #[case(RustPage::RUST_PLAYBACK, ATVPageType::ATVPlayer)]
    #[case(RustPage::RUST_SEARCH, ATVPageType::ATVSearch)]
    fn should_get_atv_page_type(#[case] rust_page: RustPage, #[case] atv_page_type: ATVPageType) {
        assert_eq!(get_atv_page_type(rust_page), atv_page_type);
    }
}
