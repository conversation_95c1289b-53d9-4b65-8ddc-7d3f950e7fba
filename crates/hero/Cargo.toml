[package]
name = "hero"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
log.workspace = true
serde.workspace = true
serde_json.workspace = true
fableous.workspace = true
auth.workspace = true
common-transform-types.workspace = true
title-details.workspace = true
lrc-image.workspace = true
container-types.workspace = true
media-background.workspace = true
transition-executor.workspace = true
router.workspace = true
location.workspace = true
navigation-menu.workspace = true
app-events.workspace = true
settings-manager.workspace = true
contextual-menu.workspace = true
contextual-menu-types.workspace = true
rust-features.workspace = true
cross-app-events.workspace = true
watch-modal.workspace = true
cfg-test-attr-derive.workspace = true
acm-config.workspace = true
playback-navigation.workspace = true
metadata-components.workspace = true
checkout-navigation.workspace = true
uuid.workspace = true
linear-common.workspace = true
liveliness-types.workspace = true
firetv.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
    "mock_timer",
] }
rstest.workspace = true
serial_test.workspace = true
media-background.workspace = true
network-parser.workspace = true
mockall.workspace = true
synchronized-state-store = { workspace = true, features = ["test-utils"] }
rust-features.workspace = true
watch-modal = { workspace = true, features = ["test_utils"] }
firetv = { workspace = true, features = ["test_utils"] }

[features]
test_utils = []
test_utils_debug_renderer = []
debug_impl = []
