use cfg_test_attr_derive::derive_test_only;
use common_transform_types::{
    container_items::{HeroCard, LinearStationCard, LinkCard},
    containers::{StandardHeroItem, TentpoleHeroItem},
};
use ignx_compositron::prelude::*;
use std::rc::Rc;
use std::time::Duration;

#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
#[derive(Clone)]
#[derive_test_only(Debug)]
pub enum HeroItem {
    #[allow(dead_code, reason = "proxy for specific heros")]
    HeroCard(HeroCard),
    #[allow(dead_code, reason = "proxy for specific heros")]
    LinkCard(LinkCard),
    #[allow(dead_code, reason = "proxy for specific heros")]
    LinearStationCard(LinearStationCard),
}

impl From<&StandardHeroItem> for HeroItem {
    fn from(item: &StandardHeroItem) -> HeroItem {
        match item {
            StandardHeroItem::HERO_CARD(card) => HeroItem::HeroCard(card.clone()),
            StandardHeroItem::LINK_CARD(card) => HeroItem::LinkCard(card.clone()),
            StandardHeroItem::CHANNEL_CARD(_) => unreachable!("Deprecated and unused code, that's why I won't be creating a HeroItem::ChannelCard(_) which makes me have to add even more code to deprecated sections"),
            StandardHeroItem::LINEAR_STATION(card) => HeroItem::LinearStationCard(card.clone()),
        }
    }
}

impl From<&TentpoleHeroItem> for HeroItem {
    fn from(item: &TentpoleHeroItem) -> HeroItem {
        match item {
            TentpoleHeroItem::HERO_CARD(card) => HeroItem::HeroCard(card.clone()),
        }
    }
}

#[derive(Clone, Copy, Hash, PartialEq, Debug)]
pub enum ButtonVariant {
    Primary,
    Secondary,
}

#[derive(Clone, Copy)]
pub enum SizeConfig {
    AlwaysCollapsed,
    AlwaysExpanded,
    Expandable(Signal<bool>),
}

// Subset of MediaStrategies that are supported in the Hero
#[derive(Clone, Copy, PartialEq, Debug)]
pub enum HeroItemMediaStrategy {
    Promo,
    Live,
    Linear,
}

// `None` means no override and autorotation logic will use the default wait time (as of writing 5s, but could change in the future)
pub type HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy =
    Rc<dyn Fn(HeroItemMediaStrategy) -> Option<Duration>>;
