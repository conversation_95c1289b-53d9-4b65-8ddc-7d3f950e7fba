use crate::hero_sig::*;
use crate::types::{ButtonVariant, SizeConfig};
use container_types::ui_signals::{HeroItemModel, TentpoleHeroModel};
use contextual_menu_types::prelude::ContextualMenuData;
use fableous::cards::sizing::CardDimensions;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, compose_option, Composer};
use std::rc::Rc;

pub const TENTPOLE_HERO_TEST_ID: &str = "tentpole-hero";

#[Composer]
pub fn TentpoleHeroSig(
    ctx: &AppContext,
    #[into] data: RwSignal<TentpoleHeroModel>,
    #[optional = Rc::new(move |_| {})] on_focus: Rc<dyn Fn(HeroItemModel)>,
    #[optional = Rc::new(move |_,_| {})] open_contextual_menu: Rc<
        dyn Fn(HeroItemModel, ButtonVariant),
    >,
    on_back_pressed: Rc<dyn Fn()>,
    on_ready: Rc<dyn Fn()>,
    on_item_view: Rc<dyn Fn(ViewImpressionData, RwSignal<HeroItemModel>, usize, CardDimensions)>,
    on_item_highlight: Rc<dyn Fn(RwSignal<HeroItemModel>, usize, CardDimensions)>,
    on_item_select: Rc<
        dyn Fn(RwSignal<HeroItemModel>, usize, Option<CardDimensions>, ButtonVariant),
    >,
    update_cm: WriteSignal<Option<ContextualMenuData>>,
) -> ColumnComposable {
    compose! {
        Column() {
            Memo(item_builder: Box::new(move |ctx| {
                let on_ready = Rc::clone(&on_ready);
                data.with(|data| {
                    let is_empty = data.item.with_untracked(|items| items.is_empty());
                    if is_empty {
                        None
                    } else {
                        let on_focus = Rc::clone(&on_focus);
                        let on_back_pressed = Rc::clone(&on_back_pressed);
                        let open_contextual_menu = Rc::clone(&open_contextual_menu);
                        let on_item_view = Rc::clone(&on_item_view);
                        let on_item_highlight = Rc::clone(&on_item_highlight);
                        let on_item_select = Rc::clone(&on_item_select);
                        let items = data.item.get_untracked();
                        let wrapped_on_focus = Rc::new(move || {
                            let Some(item) = items.first() else {
                                return;
                            };
                            on_focus(item.clone());
                        });
                        let items = data.item.get_untracked();
                        let wrapped_open_contextual_menu = Rc::new(move |_, button: ButtonVariant| {
                            let Some(item) = items.first() else {
                                return;
                            };
                            open_contextual_menu(item.clone(), button);
                        });
                        let size_config: SizeConfig = SizeConfig::AlwaysExpanded;

                        compose_option! {
                            Column() {
                                HeroSig(items: data.item,
                                        size_config,
                                        on_focus: wrapped_on_focus,
                                        open_item_contextual_menu: wrapped_open_contextual_menu,
                                        on_back_pressed,
                                        metadata: data.common_carousel_metadata,
                                        on_ready,
                                        is_tentpole: true,
                                        update_cm,
                                        on_item_view,
                                        on_item_highlight,
                                        on_item_select,
                                )
                            }
                        }
                    }
                })
            }))
        }.test_id(TENTPOLE_HERO_TEST_ID)
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::hero_sig::test::setup_mock_routing_with_past_location;
    use crate::utils::test::generate_mock_hero_card;
    use container_types::ui_signals::CommonCarouselMetadata;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::composable::Composable;
    use ignx_compositron::compose;
    use ignx_compositron::context::AppContext;
    use ignx_compositron::input::KeyCode;
    use ignx_compositron::reactive::{
        create_rw_signal, provide_context, use_context, RwSignal, SignalGetUntracked, SignalSet,
    };
    use ignx_compositron::test_utils::assert_node_exists;
    use ignx_compositron::tts::TTSEnabledContext;
    use rstest::*;
    use rust_features::provide_context_test_rust_features;

    fn generate_mock_tentpole_data(scope: Scope) -> TentpoleHeroModel {
        let item = generate_mock_hero_card(scope, 0, true);
        TentpoleHeroModel {
            common_carousel_metadata: create_rw_signal(
                scope,
                CommonCarouselMetadata {
                    id: "tentpole hero id".to_string(),
                    analytics: create_rw_signal(scope, Default::default()),
                    journey_ingress_context: create_rw_signal(scope, Some("jic".to_string())),
                },
            ),
            item: create_rw_signal(scope, vec![item]),
        }
    }

    fn provide_firetv_context(ctx: &AppContext, is_firetv: bool) {
        let mut firetv = MockFireTV::default();
        firetv.expect_is_firetv().return_const(is_firetv);
        firetv.provide_mock(ctx.scope());
    }

    fn create_tentpole_hero(
        ctx: &AppContext,
        get_data: impl Fn(Scope) -> TentpoleHeroModel,
        on_focus: Rc<dyn Fn(HeroItemModel)>,
        open_contextual_menu: Rc<dyn Fn(HeroItemModel, ButtonVariant)>,
        // Menu button exists on fire tv remote and opens/closes the contextual menu
        has_menu_button: bool,
    ) -> impl Composable<'static> {
        let data = get_data(ctx.scope());
        let data = create_rw_signal(ctx.scope(), data);
        let (_, update_cm) = create_signal(ctx.scope(), None);

        provide_context(
            ctx.scope(),
            TTSEnabledContext(create_signal(ctx.scope(), false).0),
        );
        provide_firetv_context(&ctx, has_menu_button);
        compose! {
            Column() {
                TentpoleHeroSig(
                    data,
                    on_focus,
                    open_contextual_menu,
                    on_back_pressed: Rc::new(|| {}),
                    on_ready: Rc::new(|| {}),
                    on_item_highlight: Rc::new(|_, _, _| {}),
                    on_item_view: Rc::new(|_, _, _, _| {}),
                    on_item_select: Rc::new(|_, _, _, _| {}),
                    update_cm
                )
                Button(text: "Other content button".to_string()).test_id("other_button")
            }
        }
    }

    #[rstest]
    #[case(generate_mock_tentpole_data)]
    fn renders_tentpole_hero(#[case] data: impl Fn(Scope) -> TentpoleHeroModel + 'static) {
        launch_test(
            move |ctx| {
                setup_mock_routing_with_past_location(&ctx);
                provide_context_test_rust_features(ctx.scope());

                create_tentpole_hero(&ctx, data, Rc::new(|_| {}), Rc::new(|_, _| {}), false)
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let container = tree.find_by_test_id("tentpole-hero");

                assert_node_exists!(&container);

                let hero = container.find_any_child_with().test_id("hero").find_first();

                assert_node_exists!(&hero);
            },
        )
    }

    #[test]
    fn on_focus_called() {
        launch_test(
            |ctx| {
                let on_focus_called = create_rw_signal(ctx.scope(), None);
                let on_focus = Rc::new(move |item: HeroItemModel| {
                    on_focus_called.set(Some(item));
                });
                provide_context(ctx.scope(), on_focus_called);

                setup_mock_routing_with_past_location(&ctx);
                provide_context_test_rust_features(ctx.scope());

                create_tentpole_hero(
                    &ctx,
                    generate_mock_tentpole_data,
                    on_focus,
                    Rc::new(|_, _| {}),
                    false,
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();

                let on_focus_called = use_context::<RwSignal<Option<HeroItemModel>>>(scope);
                let on_focus_called =
                    on_focus_called.expect("signal not found for on_focus_called");

                // reset on focus called signal and send focus elsewhere
                on_focus_called.set(None);
                let other_button = tree.find_by_test_id("other_button");
                test_game_loop.send_on_focus_event(other_button.borrow_props().node_id);
                let tree = test_game_loop.tick_until_done();
                assert!(on_focus_called.get_untracked().is_none());

                let parent = tree.find_by_test_id("hero");
                test_game_loop.send_on_focus_event(parent.borrow_props().node_id);
                test_game_loop.tick_until_done();

                on_focus_called
                    .get_untracked()
                    .expect("expected on_focus_called to contain a defined item");
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Enter)]
    #[case(KeyCode::Select)]
    fn open_contextual_menu_called(#[case] key_code: KeyCode) {
        launch_test(
            |ctx| {
                let last_contextual_menu_item_opened = create_rw_signal(ctx.scope(), None);
                let open_contextual_menu =
                    Rc::new(move |item: HeroItemModel, _button: ButtonVariant| {
                        last_contextual_menu_item_opened.set(Some(item));
                    });
                provide_context(ctx.scope(), last_contextual_menu_item_opened);
                setup_mock_routing_with_past_location(&ctx);
                create_tentpole_hero(
                    &ctx,
                    generate_mock_tentpole_data,
                    Rc::new(|_| {}),
                    open_contextual_menu,
                    true,
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let parent = tree.find_by_test_id("hero");

                test_game_loop
                    .send_long_key_press_event_to_node(parent.borrow_props().node_id, key_code);
                test_game_loop.tick_until_done();

                let last_contextual_menu_item_opened =
                    use_context::<RwSignal<Option<HeroItemModel>>>(scope);
                let last_contextual_menu_item_opened = last_contextual_menu_item_opened
                    .expect("signal not found for last_contextual_menu_item_opened");

                let item = last_contextual_menu_item_opened
                    .get_untracked()
                    .expect("expected last item with contextual menu open to be defined");
                let gti = item
                    .gti
                    .get_untracked()
                    .expect("expected to have opened the contextual menu on a title card!");
                assert_eq!(gti, "hero gti 0");
            },
        )
    }
}
