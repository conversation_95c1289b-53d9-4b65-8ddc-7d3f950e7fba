use amzn_fable_tokens::FableColor;
use common_transform_types::actions::{
    AcquisitionSVODMetadata, AcquisitionTVODMetadata, BaseTitleActionMetadata, SwiftAction,
    TitleAcquisitionAction, TitleActionMetadata, TitleActionSegment, TitleOpenModalAction,
    TitlePlaybackAction, TransitionAction,
};
use common_transform_types::container_items::{
    Dimension, EventMetadata, LiveEventDateTime, LocalizedLiveEventBadge, LocalizedLiveEventHeader,
    MaturityRatingImage, ShowContext,
};
use common_transform_types::playback_metadata::TitleActionMetadataType::{
    self, AcquisitionPrime, AcquisitionSVOD, AcquisitionTVOD,
};
use common_transform_types::playback_metadata::{
    PlaybackMetadata, PlaybackPosition, UserEntitlementMetadata, UserPlaybackMetadata,
};
use common_transform_types::{
    actions::Action,
    container_items::{
        Badges, CarouselItemData, ImageAttributes, IndividualImageMetadata,
        IndividualImageMetadataMapping, TitleCardBaseMetadata,
    },
};
use container_types::ui_signals::{HeroCardLinearAiringUIData, HeroItemModel, HeroItemUIData};
use contextual_menu_types::prelude::*;
use fableous::buttons::primary_button::PrimaryButtonVariant;
use fableous::buttons::secondary_button::SecondaryButtonVariant;
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::{create_rw_signal, Scope};
use ignx_compositron::text::TextContent;
use media_background::types::{
    MediaBackgroundType, MediaStrategy, RotationDirection, StandardHeroBackgroundData,
};
use serde_json::{Map, Value};
use title_details::components::entitlement::{EntitlementLabelData, EntitlementLabelSize};
use title_details::components::title::TitleData;
use title_details::types::common::{HighValueMessageData, MetadataRowType, TTSTitleDetailsData};
use transition_executor::{RPCManagerWrapper, Transition};
use uuid::Uuid;

pub fn carousel_card_metadata(action: Option<Action>) -> CarouselItemData {
    CarouselItemData {
        transformItemId: Some("a mock id".to_string()),
        title: Some("a mock title".to_string()),
        synopsis: Some("a mock synopsis".to_string()),
        action,
        deferredAction: None,
        actions: vec![],
        widgetType: Some("a mock widget type".to_string()),
    }
}

pub fn event_metadata() -> EventMetadata {
    EventMetadata {
        liveliness: Some("LIVE".to_string()),
        liveEventDateBadge: Some(LiveEventDateTime::LOCALIZED_BADGE(
            LocalizedLiveEventBadge {
                text: "Some Date and Time".to_string(),
            },
        )),
        liveEventDateHeader: Some(LiveEventDateTime::LOCALIZED_HEADER(
            LocalizedLiveEventHeader {
                date: Some("Some Date".to_string()),
                time: Some("Some Time".to_string()),
            },
        )),
        venue: Some("Some venue".to_string()),
        scoreBug: None,
    }
}

pub fn title_card_metadata() -> TitleCardBaseMetadata {
    TitleCardBaseMetadata {
        coverImage: Some("cover image".to_string()),
        boxartImage: Some("box art image".to_string()),
        titleLogoImage: Some("title logo image".to_string()),
        providerLogoImage: Some("provider logo image".to_string()),
        poster2x3Image: Some("poster image".to_string()),
        heroImage: None,
        totalReviewCount: Some(0),
        overallRating: Some(0.0),
        gti: Some("gti".to_string()),
        badges: Some(Badges {
            applyAudioDescription: false,
            applyCC: false,
            applyDolby: false,
            applyDolbyAtmos: false,
            applyDolbyVision: false,
            applyHdr10: false,
            applyPrime: false,
            applyUhd: false,
            regulatoryRating: Some("18".to_string()),
            showPSE: false,
        }),
        publicReleaseDate: None,
        runtimeSeconds: None,
        entitlementStatus: None,
        entitlementMessaging: None,
        genres: vec![],
        watchProgress: None,
        imageAlternateText: None,
        isEntitled: None,
        offerText: None,
        isInWatchlist: None,
        maturityRatingString: None,
        maturityRatingImage: None,
        regulatoryLabel: None,
        imageAttributes: Some(ImageAttributes {
            isAdult: Some(true),
            isRestricted: None,
            individualImageMetadata: Some(IndividualImageMetadataMapping {
                providerLogoImage: Some(IndividualImageMetadata {
                    height: Some(200),
                    width: Some(300),
                    scalarHorizontal: None,
                    scalarStacked: None,
                    safeToOverlay: None,
                }),
            }),
        }),
        contentType: Some("MOVIE".to_string()),
        contextualActions: None,
    }
}

pub fn generate_mock_link_card(scope: Scope, index: usize) -> HeroItemModel {
    HeroItemModel {
        uuid: Uuid::new_v4(),
        id: "id".to_string(),
        ui_data: create_rw_signal(
            scope,
            HeroItemUIData {
                title_data: create_rw_signal(
                    scope,
                    TitleData {
                        title_art_url: None,
                        provider_logo_url: None,
                        title_text: "hero title".to_string(),
                        size: None,
                        provider_logo_size: None,
                    },
                ),
                synopsis: create_rw_signal(scope, None),
                primary_button_action: create_rw_signal(
                    scope,
                    Some(TransitionAction::acquisition(TitleAcquisitionAction {
                        label: "".to_string(),
                        metadata: None,
                        refMarker: "".to_string(),
                    })),
                ),
                secondary_button_action: create_rw_signal(
                    scope,
                    Some(TransitionAction::detail(SwiftAction {
                        text: None,
                        analytics: Default::default(),
                        refMarker: "".to_string(),
                        pageId: "".to_string(),
                        pageType: "".to_string(),
                        serviceToken: None,
                        journeyIngressContext: None,
                    })),
                ),
                metadata_rows: create_rw_signal(
                    scope,
                    vec![MetadataRowType::HighValueMessage(HighValueMessageData {
                        message: "Leaving Prime Video Soon".to_string(),
                        color: get_ignx_color(FableColor::EXPLORER300),
                    })],
                ),
                primary_button_variant: create_rw_signal(
                    scope,
                    PrimaryButtonVariant::TextSize400("More details".to_string().into()),
                ),
                secondary_button_variant: create_rw_signal(scope, None),
                legal_messages: create_rw_signal(scope, vec!["legal message".to_string()]),
                maturity_rating_image: create_rw_signal(scope, None),
                entitlement_label: create_rw_signal(
                    scope,
                    Some(EntitlementLabelData {
                        size: EntitlementLabelSize::Small,
                        max_lines: 2,
                        content: Some(format!("hero entitlement label {index}")),
                        icon_type: None,
                    }),
                ),
                maturity_rating_string: create_rw_signal(scope, None),
                regulatory_label_string: create_rw_signal(scope, None),
                progress_bar_percentage: create_rw_signal(scope, None),
            },
        ),
        gti: create_rw_signal(scope, None),
        media_background_data: create_rw_signal(
            scope,
            MediaBackgroundType::StandardHero(StandardHeroBackgroundData {
                id: "".to_string(),
                image_url: Some("".to_string()),
                video_id: None,
                enter_immediately: false,
                rotation_direction: RotationDirection::NONE,
                is_expanded: create_rw_signal(scope, false),
                placement: "PVBrowse".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: None,
            }),
        ),
        contextual_menu_metadata: create_rw_signal(scope, ContextualMenuMetadata::default()),
        impression_data: Default::default(),
        swift_content_type: Some("MOVIE".to_string()),
        linear_schedule_data: vec![],
    }
}

pub fn generate_mock_channel_card(scope: Scope, index: usize) -> HeroItemModel {
    HeroItemModel {
        uuid: Uuid::new_v4(),
        id: "id".to_string(),
        ui_data: create_rw_signal(
            scope,
            HeroItemUIData {
                title_data: create_rw_signal(
                    scope,
                    TitleData {
                        title_art_url: None,
                        provider_logo_url: None,
                        title_text: format!("channel card {index}"),
                        size: None,
                        provider_logo_size: None,
                    },
                ),
                synopsis: create_rw_signal(scope, Some("some synopsis".to_string())),
                primary_button_action: create_rw_signal(
                    scope,
                    Some(TransitionAction::acquisition(TitleAcquisitionAction {
                        label: "".to_string(),
                        metadata: None,
                        refMarker: "".to_string(),
                    })),
                ),
                secondary_button_action: create_rw_signal(scope, None),
                metadata_rows: create_rw_signal(scope, vec![]),
                primary_button_variant: create_rw_signal(
                    scope,
                    PrimaryButtonVariant::TextSize400("More details".to_string().into()),
                ),
                secondary_button_variant: create_rw_signal(scope, None),
                legal_messages: create_rw_signal(scope, vec!["legal message".to_string()]),
                maturity_rating_image: create_rw_signal(scope, None),
                entitlement_label: create_rw_signal(
                    scope,
                    Some(EntitlementLabelData {
                        size: EntitlementLabelSize::Small,
                        max_lines: 2,
                        content: Some(format!("hero entitlement label {index}")),
                        icon_type: None,
                    }),
                ),
                maturity_rating_string: create_rw_signal(scope, None),
                regulatory_label_string: create_rw_signal(scope, None),
                progress_bar_percentage: create_rw_signal(scope, None),
            },
        ),
        gti: create_rw_signal(scope, None),
        media_background_data: create_rw_signal(
            scope,
            MediaBackgroundType::StandardHero(StandardHeroBackgroundData {
                id: "".to_string(),
                image_url: Some("".to_string()),
                video_id: None,
                enter_immediately: false,
                media_strategy: MediaStrategy::Promo,
                rotation_direction: RotationDirection::NONE,
                is_expanded: create_rw_signal(scope, false),
                placement: "PVBrowse".to_string(),
                csm_data: None,
            }),
        ),
        contextual_menu_metadata: create_rw_signal(scope, ContextualMenuMetadata::default()),
        impression_data: Default::default(),
        swift_content_type: Some("MOVIE".to_string()),
        linear_schedule_data: vec![],
    }
}

pub fn generate_mock_hero_card(scope: Scope, index: usize, use_gti: bool) -> HeroItemModel {
    generate_mock_hero_card_with_actions(
        scope,
        index,
        Some(generate_aquisition_action(None)),
        Some(generate_details_action()),
        use_gti,
        None,
        None,
    )
}

pub fn generate_mock_hero_card_with_linear_schedule(
    scope: Scope,
    index: usize,
    use_gti: bool,
) -> HeroItemModel {
    let mut hero = generate_mock_hero_card_with_actions(
        scope,
        index,
        Some(generate_aquisition_action(None)),
        Some(generate_details_action()),
        use_gti,
        None,
        None,
    );

    hero.linear_schedule_data = vec![mock_linear_hero_ui_data(0), mock_linear_hero_ui_data(1)];
    hero
}

pub const MOCK_NOW_MS: i64 = 1709760330000; // March 6, 2024 9:25:30 PM
pub const HOUR_IN_MS: i64 = 3_600_000;
fn mock_linear_hero_ui_data(idx: usize) -> HeroCardLinearAiringUIData {
    let start = MOCK_NOW_MS + { idx as i64 * HOUR_IN_MS };
    HeroCardLinearAiringUIData {
        start_time: start,
        end_time: start + HOUR_IN_MS,
        metadata_rows: vec![MetadataRowType::PrimaryLabel(TextContent::String(format!(
            "metadata_{}",
            idx
        )))],
        maturity_rating_string: Some(format!("rating_{}", idx)),
        content_descriptors_string: format!("content_descriptors_{}", idx),
        synopsis: Some(format!("S1 E2 - synopsis_{}", idx)),
        background_image: Some(format!("background_img_{}", idx)),
        title_text: format!("title_{}", idx),
        tts_data: TTSTitleDetailsData {
            liveliness: Some(TextContent::String(format!("liveliness_{}", idx))),
            station_name: Some(format!("station_name_{}", idx)),
            schedule_time: Some(format!("schedule_time_{}", idx)),
            content_descriptors: Some(format!("content_descriptors_{}", idx)),
            synopsis: Some(format!("synopsis_{}", idx)),
            show_context: Some(ShowContext {
                episode: Some(idx as u32),
                season: Some(idx as u32),
                episodeTitle: Some(format!("episode_title_{}", idx)),
            }),
            ..Default::default()
        },
    }
}

pub fn generate_mock_prime_acquisition_metadata() -> TitleActionMetadata {
    TitleActionMetadata::AcquisitionPrime(BaseTitleActionMetadata {
        refMarker: Some("ref_marker".to_string()),
        metadataActionType: Some(AcquisitionPrime),
    })
}

pub fn generate_mock_svod_acquisition_metadata(benefit_id: String) -> TitleActionMetadata {
    TitleActionMetadata::AcquisitionSVOD(AcquisitionSVODMetadata {
        benefitId: benefit_id,
        refMarker: "ref_marker".to_string(),
        metadataActionType: Some(AcquisitionSVOD),
    })
}

pub fn generate_mock_tvod_acquisition_metadata() -> TitleActionMetadata {
    TitleActionMetadata::AcquisitionTVOD(AcquisitionTVODMetadata {
        refMarker: Some("mockRefMarker".to_string()),
        offerToken: "mockOfferToken".to_string(),
        contentType: "mockContentType".to_string(),
        offerType: "mockOfferType".to_string(),
        videoQuality: Some("mockVideoQuality".to_string()),
        message: Some("mockMessage".to_string()),
        metadataActionType: Some(AcquisitionTVOD),
    })
}

pub fn generate_aquisition_action(metadata: Option<TitleActionMetadata>) -> TransitionAction {
    TransitionAction::acquisition(TitleAcquisitionAction {
        label: "".to_string(),
        metadata,
        refMarker: "".to_string(),
    })
}

pub fn generate_playback_action() -> TransitionAction {
    generate_playback_action_with_title_and_position(Some("random.gti.of.title".into()), None)
}

pub fn generate_playback_action_with_title_and_position(
    title: Option<String>,
    position: Option<PlaybackPosition>,
) -> TransitionAction {
    TransitionAction::playback(TitlePlaybackAction {
        playbackMetadata: PlaybackMetadata {
            refMarker: "a ref marker".to_string(),
            contentDescriptors: None,
            playbackExperienceMetadata: None,
            position: position.unwrap_or(PlaybackPosition::FeatureFinished),
            startPositionEpochUtc: None,
            userPlaybackMetadata: UserPlaybackMetadata {
                runtimeSeconds: Some(0),
                timecodeSeconds: Some(0),
                hasStreamed: Some(false),
                isLinear: None,
                linearStartTime: None,
                linearEndTime: None,
            },
            userEntitlementMetadata: UserEntitlementMetadata {
                entitlementType: "some type".to_string(),
                benefitType: vec![],
            },
            videoMaterialType: "link_video_material_type".to_string(),
            channelId: None,
            playbackTitle: title,
            metadataActionType: TitleActionMetadataType::Playback,
            catalogMetadata: None,
            isTrailer: None,
            isUnopenedRental: false,
        },
        label: Some("label".to_string()),
        refMarker: "a ref marker".to_string(),
    })
}

pub fn generate_open_modal_action() -> TransitionAction {
    TransitionAction::openModal(TitleOpenModalAction {
        refMarker: "Test refmarker".to_string(),
        label: "Test label".to_string(),
        modalHeader: "Test header".to_string(),
        actionSegments: vec![TitleActionSegment {
            childActions: vec![generate_playback_action()],
            entitlementMessaging: None,
        }],
    })
}

pub fn generate_details_action() -> TransitionAction {
    TransitionAction::detail(SwiftAction {
        text: Some("More details".to_string()),
        analytics: Default::default(),
        refMarker: "refMarker".to_string(),
        pageId: "details".to_string(),
        pageType: "more".to_string(),
        serviceToken: None,
        journeyIngressContext: None,
    })
}

pub fn generate_mock_hero_card_with_actions(
    scope: Scope,
    index: usize,
    primary_action: Option<TransitionAction>,
    secondary_action: Option<TransitionAction>,
    use_gti: bool,
    primary_button_label: Option<String>,
    secondary_button_label: Option<String>,
) -> HeroItemModel {
    let gti = if use_gti {
        Some(format!("hero gti {index}"))
    } else {
        None
    };
    HeroItemModel {
        uuid: Uuid::new_v4(),
        id: "id".to_string(),
        ui_data: create_rw_signal(
            scope,
            HeroItemUIData {
                title_data: create_rw_signal(
                    scope,
                    TitleData {
                        title_art_url: None,
                        provider_logo_url: None,
                        title_text: format!("hero title {index}"),
                        size: None,
                        provider_logo_size: None,
                    },
                ),
                synopsis: create_rw_signal(scope, Some(format!("hero synopsis {index}"))),
                primary_button_action: create_rw_signal(scope, primary_action),
                secondary_button_action: create_rw_signal(scope, secondary_action),
                metadata_rows: create_rw_signal(
                    scope,
                    vec![MetadataRowType::HighValueMessage(HighValueMessageData {
                        message: "Leaving Prime Video Soon".to_string(),
                        color: get_ignx_color(FableColor::EXPLORER300),
                    })],
                ),
                primary_button_variant: create_rw_signal(
                    scope,
                    PrimaryButtonVariant::TextSize400(
                        primary_button_label
                            .unwrap_or("Start your free trial".to_string())
                            .into(),
                    ),
                ),
                secondary_button_variant: create_rw_signal(
                    scope,
                    Some(SecondaryButtonVariant::TextSize400(
                        secondary_button_label
                            .unwrap_or("More details".to_string())
                            .into(),
                    )),
                ),
                legal_messages: create_rw_signal(scope, vec!["legal message".to_string()]),
                maturity_rating_image: create_rw_signal(
                    scope,
                    Some(MaturityRatingImage {
                        url: "http://maturity-rating-image.url".to_string(),
                        dimension: Dimension {
                            width: 10,
                            height: 10,
                        },
                    }),
                ),
                regulatory_label_string: create_rw_signal(
                    scope,
                    Some("regulatory label".to_string()),
                ),
                maturity_rating_string: create_rw_signal(
                    scope,
                    Some("maturity string".to_string()),
                ),
                entitlement_label: create_rw_signal(
                    scope,
                    Some(EntitlementLabelData {
                        size: EntitlementLabelSize::Small,
                        max_lines: 2,
                        content: Some(format!("hero entitlement label {index}")),
                        icon_type: None,
                    }),
                ),
                progress_bar_percentage: create_rw_signal(scope, Some(0.2)),
            },
        ),
        gti: create_rw_signal(scope, gti),
        media_background_data: create_rw_signal(
            scope,
            MediaBackgroundType::StandardHero(StandardHeroBackgroundData {
                id: format!("mb_id_{index}"),
                image_url: Some("".to_string()),
                video_id: None,
                enter_immediately: false,
                rotation_direction: RotationDirection::NONE,
                is_expanded: create_rw_signal(scope, false),
                placement: "PVBrowse".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: None,
            }),
        ),
        contextual_menu_metadata: create_rw_signal(scope, ContextualMenuMetadata::default()),
        impression_data: Default::default(),
        swift_content_type: Some("MOVIE".to_string()),
        linear_schedule_data: vec![],
    }
}

pub fn generate_mock_data(scope: Scope, use_gti: bool) -> Vec<HeroItemModel> {
    vec![
        generate_mock_hero_card(scope, 0, use_gti),
        generate_mock_link_card(scope, 1),
        generate_mock_hero_card(scope, 2, use_gti),
        generate_mock_hero_card(scope, 3, use_gti),
        generate_mock_hero_card(scope, 4, use_gti),
        generate_mock_hero_card(scope, 5, use_gti),
        generate_mock_hero_card(scope, 6, use_gti),
        generate_mock_hero_card(scope, 7, use_gti),
        generate_mock_hero_card(scope, 8, use_gti),
        generate_mock_hero_card(scope, 9, use_gti),
    ]
}

pub fn generate_params_for_prime_acquisition() -> Map<String, Value> {
    let mut params = Map::new();

    params.insert("benefitId".to_string(), Value::String("Prime".to_string()));
    params.insert(
        "ingressPoint".to_string(),
        Value::String("StandardHeroContainer".to_string()),
    );
    params.insert(
        "titleId".to_string(),
        Value::String("hero gti 0".to_string()),
    );
    params
}

pub fn generate_params_for_svod_acquisition(benefit_id: String) -> Map<String, Value> {
    let mut params = Map::new();

    params.insert(
        "checkout_request_type".to_string(),
        Value::String("Channels".to_string()),
    );
    params.insert("benefitId".to_string(), Value::String(benefit_id));
    params.insert(
        "ingressPoint".to_string(),
        Value::String("StandardHeroContainer".to_string()),
    );
    params.insert(
        "titleId".to_string(),
        Value::String("hero gti 0".to_string()),
    );

    params
}

pub fn add_deferred_action_to_params(
    mut params: Map<String, Value>,
    deferred_action: TransitionAction,
) -> Map<String, Value> {
    let location = Transition {
        action: deferred_action,
        deferred_action: None,
        enable_duplicate_destination_navigation: None,
        is_signed_in: true,
        rpc_manager_wrapper: RPCManagerWrapper(None),
        rust_features: None,
        seamless_if_possible: false,
        content_type: None,
        title_id: None,
        playback_origin_override: None,
        ingress_source: None,
    }
    .to_location()
    .unwrap();

    params.insert(
        "destinationPageType".to_string(),
        location.pageType.to_string().into(),
    );
    params.insert(
        "destinationPageParams".to_string(),
        location.pageParams.into(),
    );

    params
}

pub fn generate_params_for_tvod_acquisition(title_id: String) -> Map<String, Value> {
    let mut params = Map::new();
    let mut metadata = Map::new();

    metadata.insert(
        "offerToken".to_string(),
        Value::String("mockOfferToken".to_string()),
    );
    metadata.insert(
        "contentType".to_string(),
        Value::String("mockContentType".to_string()),
    );
    metadata.insert(
        "refMarker".to_string(),
        Value::String("mockRefMarker".to_string()),
    );
    metadata.insert(
        "offerType".to_string(),
        Value::String("mockOfferType".to_string()),
    );
    metadata.insert(
        "videoQuality".to_string(),
        Value::String("mockVideoQuality".to_string()),
    );
    metadata.insert(
        "message".to_string(),
        Value::String("mockMessage".to_string()),
    );
    metadata.insert(
        "metadataActionType".to_string(),
        Value::String("AcquisitionTVOD".to_string()),
    );

    params.insert("titleId".to_string(), Value::String(title_id));
    params.insert("metadata".to_string(), Value::Object(metadata));
    params.insert(
        "contentType".to_string(),
        Value::String("mockContentType".to_string()),
    );
    params
}
