use container_types::ui_signals::HeroItemModel;
use fableous::tiles::tile::TileBadgeProps;
use ignx_compositron::{
    chrono::{DateTime, Utc},
    prelude::{safe::Signal, *},
    text::{LocalizedText, TextContent},
};
use linear_common::util::{
    datetime_util::get_time_remaining, schedule_util::find_current_item_from_schedule,
};
use title_details::{
    components::entitlement::EntitlementLabelData,
    types::common::{MetadataRowType, TTSTitleDetailsData},
};

fn get_hvm_message_strings(metadata_row: Vec<MetadataRowType>) -> Vec<TextContent> {
    metadata_row
        .iter()
        .filter_map(|item: &MetadataRowType| match item {
            MetadataRowType::HighValueMessage(message) => Some(message.message.clone().into()),
            MetadataRowType::MarketingMessage(message) => Some(message.clone().into()),
            MetadataRowType::LivelinessBadge(message) => {
                if message.message.is_some() {
                    Some(message.message.clone().unwrap_or_default().into())
                } else {
                    message
                        .liveliness
                        .as_ref()
                        .map(TileBadgeProps::live_state_to_badge_text)
                }
            }

            _ => None,
        })
        .collect()
}

fn get_entitlement_tts_message(entitlement_label: Option<EntitlementLabelData>) -> TextContent {
    match entitlement_label {
        Some(label_data) => TextContent::String(label_data.content.unwrap_or_default()),
        None => TextContent::String("".to_string()),
    }
}

fn get_maturity_rating_tts_messages(maturity_rating: Option<String>) -> Vec<TextContent> {
    let mut v = vec![];
    if let Some(maturity_rating) = maturity_rating {
        v.push(LocalizedText::new("AV_LRC_AGE_RATING").into());
        v.push(TextContent::String(maturity_rating));
    }
    v
}

fn hero_metadata_descriptions(focused_item: &HeroItemModel) -> Vec<TextContent> {
    focused_item.ui_data.with_untracked(|data| {
        let title = data
            .title_data
            .with_untracked(|data| data.title_text.clone());
        // Title
        let mut v = vec![TextContent::String(title)];

        // HVM strings
        v.extend(get_hvm_message_strings(data.metadata_rows.get_untracked()));

        // Entitlement Label
        v.push(get_entitlement_tts_message(
            data.entitlement_label.get_untracked(),
        ));

        // Maturity rating
        v.extend(get_maturity_rating_tts_messages(
            data.maturity_rating_string.get_untracked(),
        ));

        // Synopsis
        if let Some(synopsis) = data.synopsis.get_untracked() {
            v.push(TextContent::String(synopsis));
        }
        v
    })
}

fn linear_hero_metadata_descriptions(
    focused_item: &HeroItemModel,
    now: DateTime<Utc>,
) -> Vec<TextContent> {
    let (title, entitlement_label, maturity_rating_string) =
        focused_item.ui_data.with_untracked(|data| {
            let title = data
                .title_data
                .with_untracked(|data| data.title_text.clone());
            let entitlement_label = data.entitlement_label.get_untracked();
            let maturity_rating_string = data.maturity_rating_string.get_untracked();
            (title, entitlement_label, maturity_rating_string)
        });

    let airing =
        find_current_item_from_schedule(&focused_item.linear_schedule_data, now.timestamp_millis())
            .or_else(|| focused_item.linear_schedule_data.last());

    let (tts_data, end_time) = airing.map_or_else(
        || (TTSTitleDetailsData::default(), None),
        |a| (a.tts_data.clone(), Some(a.end_time)),
    );

    // Title
    let mut v = vec![TextContent::String(title)];

    // Liveliness
    if let Some(liveliness_msg) = tts_data.liveliness {
        v.push(liveliness_msg);
    }

    // Time remaining
    if let Some(end_time) = end_time {
        v.push(get_time_remaining(now.timestamp_millis(), end_time, true));
    }

    // Entitlement
    v.push(get_entitlement_tts_message(entitlement_label));

    // Station Name
    if let Some(station_name) = tts_data.station_name {
        v.push(TextContent::String(station_name));
    }

    // Time Range
    if let Some(schedule_time) = tts_data.schedule_time {
        v.push(TextContent::String(schedule_time));
    }

    // Rating
    v.extend(get_maturity_rating_tts_messages(maturity_rating_string));

    // Content descriptors
    if let Some(content_descriptors) = tts_data.content_descriptors {
        v.push(TextContent::String(content_descriptors));
    }

    if let Some(show_context) = tts_data.show_context {
        v.extend(show_context.to_tts_messages());
    }

    // Synopsis
    if let Some(synopsis) = tts_data.synopsis {
        v.push(TextContent::String(synopsis));
    }
    v
}

pub fn get_hero_metadata_descriptions(
    scope: Scope,
    focused_item: MaybeSignal<HeroItemModel>,
) -> Signal<'static, Vec<TextContent>> {
    Signal::derive(scope, move || {
        // TTS should always update if focused item changes
        focused_item.with(|hero| {
            if hero.linear_schedule_data.is_empty() {
                // Non-linear hero
                hero_metadata_descriptions(hero)
            } else {
                // Linear hero updates ui_data if current airing is changed
                hero.ui_data.track();

                // Allow for mocking current time in tests
                #[cfg(not(test))]
                let now = Utc::now();
                #[cfg(test)]
                let now = use_context::<RwSignal<DateTime<Utc>>>(scope)
                    .map_or(Utc::now(), |sig| sig.get_untracked());

                linear_hero_metadata_descriptions(hero, now)
            }
        })
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::test::{
        generate_mock_hero_card, generate_mock_hero_card_with_linear_schedule, HOUR_IN_MS,
        MOCK_NOW_MS,
    };
    use ignx_compositron::{app::launch_only_scope, text::SubstitutionParameters};
    use liveliness_types::Liveliness;
    use rstest::rstest;
    use std::collections::HashMap;
    use title_details::{
        components::entitlement::EntitlementLabelSize,
        types::common::{HighValueMessageData, LivelinessData},
    };

    #[test]
    fn test_get_hvm_message_strings_empty() {
        let result = get_hvm_message_strings(vec![]);
        assert!(result.is_empty());
    }

    #[test]
    fn test_get_hvm_message_strings() {
        let metadata = vec![
            // HighValueMessage
            MetadataRowType::HighValueMessage(HighValueMessageData {
                message: "test message".to_string(),
                color: Default::default(),
            }),
            // Marketing
            MetadataRowType::MarketingMessage("marketing msg".to_string()),
            // LivelinessBadge w/message
            MetadataRowType::LivelinessBadge(LivelinessData {
                message: Some("liveliness msg".to_string()),
                liveliness: Some(Liveliness::Live),
                level: None,
            }),
            // LivelinessBadge w/liveliness
            MetadataRowType::LivelinessBadge(LivelinessData {
                message: None,
                liveliness: Some(Liveliness::Live),
                level: None,
            }),
            // LivelinessBadge w/o liveliness or message
            MetadataRowType::LivelinessBadge(LivelinessData {
                message: None,
                liveliness: None,
                level: None,
            }),
            MetadataRowType::PrimaryLabel(TextContent::String("label".to_string())),
        ];
        let result = get_hvm_message_strings(metadata);
        assert_eq!(
            result,
            vec![
                TextContent::String("test message".to_string()),
                TextContent::String("marketing msg".to_string()),
                TextContent::String("liveliness msg".to_string()),
                TextContent::LocalizedText(LocalizedText::new("AIV_BLAST_LIVE_BADGE_TEXT")),
            ]
        );
    }

    #[rstest]
    #[case::has_label(Some(EntitlementLabelData {
            size: EntitlementLabelSize::Small,
    max_lines: 0,
    content: Some("label".to_string()),
    icon_type: None,
        }), TextContent::String("label".to_string()))]
    #[case::no_label(Some(EntitlementLabelData {
            size: EntitlementLabelSize::Small,
    max_lines: 0,
    content: None,
    icon_type: None,
        }), TextContent::String(String::default()))]
    #[case::no_entitlement(None, TextContent::String(String::default()))]
    fn test_get_entitlement_tts_message_some(
        #[case] entitlement_label: Option<EntitlementLabelData>,
        #[case] expected: TextContent,
    ) {
        let result = get_entitlement_tts_message(entitlement_label);
        assert_eq!(result, expected);
    }

    #[test]
    fn test_get_maturity_rating_tts_messages_with_rating() {
        let result = get_maturity_rating_tts_messages(Some("PG-13".to_string()));
        assert_eq!(
            result,
            vec![
                TextContent::LocalizedText(LocalizedText::new("AV_LRC_AGE_RATING")),
                TextContent::String("PG-13".to_string()),
            ]
        );
    }

    #[test]
    fn test_get_maturity_rating_tts_messages_none() {
        let result = get_maturity_rating_tts_messages(None);
        assert!(result.is_empty());
    }

    #[test]
    fn test_get_hero_metadata_descriptions() {
        launch_only_scope(|scope| {
            let hero_item = generate_mock_hero_card(scope, 0, true);
            let result = hero_metadata_descriptions(&hero_item);
            assert_non_linear_hero_metadata_descriptions(result, 0);
        })
    }

    #[test]
    fn test_get_linear_hero_metadata_descriptions() {
        launch_only_scope(|scope| {
            let hero_item = generate_mock_hero_card_with_linear_schedule(scope, 0, true);
            let result = linear_hero_metadata_descriptions(
                &hero_item,
                DateTime::from_timestamp_millis(MOCK_NOW_MS).unwrap(),
            );
            assert_linear_hero_metadata_descriptions(result, 0, 0);
        });
    }

    #[test]
    fn test_get_hero_metadata_descriptions_updates_on_data_change() {
        launch_only_scope(move |scope| {
            // We will change focused card twice
            let non_linear_hero_item_0 = generate_mock_hero_card(scope, 0, true);
            let non_linear_hero_item_1 = generate_mock_hero_card(scope, 1, true);
            let linear_hero_item = generate_mock_hero_card_with_linear_schedule(scope, 2, true);

            let focused_item = create_rw_signal(scope, non_linear_hero_item_0);

            // Provide time signal so we can test updates on linear hero
            // Inital time is the start of first linear airing
            let now_sig =
                create_rw_signal(scope, DateTime::from_timestamp_millis(MOCK_NOW_MS).unwrap());
            provide_context(scope, now_sig);

            // Create signal
            let tts_messages_sig = get_hero_metadata_descriptions(scope, focused_item.into());

            // Initial state is TTS for first item
            assert_non_linear_hero_metadata_descriptions(tts_messages_sig.get_untracked(), 0);

            // Focus on next hero
            focused_item.set(non_linear_hero_item_1);
            assert_non_linear_hero_metadata_descriptions(tts_messages_sig.get_untracked(), 1);

            // Focus on linear hero
            focused_item.set(linear_hero_item.clone());
            assert_linear_hero_metadata_descriptions(tts_messages_sig.get_untracked(), 2, 0);

            // Update time to next linear airing and trigger update
            now_sig
                .set_untracked(DateTime::from_timestamp_millis(MOCK_NOW_MS + HOUR_IN_MS).unwrap());
            linear_hero_item.ui_data.update(|_| {});
            assert_linear_hero_metadata_descriptions(tts_messages_sig.get_untracked(), 2, 1);
        });
    }

    fn assert_non_linear_hero_metadata_descriptions(result: Vec<TextContent>, index: usize) {
        assert_eq!(
            result,
            vec![
                TextContent::String(format!("hero title {}", index)),
                TextContent::String("Leaving Prime Video Soon".to_string()),
                TextContent::String(format!("hero entitlement label {}", index)),
                TextContent::LocalizedText(LocalizedText::new("AV_LRC_AGE_RATING")),
                TextContent::String("maturity string".to_string()),
                TextContent::String(format!("hero synopsis {}", index)),
            ]
        );
    }

    fn assert_linear_hero_metadata_descriptions(
        result: Vec<TextContent>,
        hero_index: usize,
        airing_index: usize,
    ) {
        assert_eq!(
            result,
            vec![
                // Some fields (title, rating) are updated on the HeroItemModel itself and are not tested here
                TextContent::String(format!("hero title {}", hero_index)),
                TextContent::String(format!("liveliness_{}", airing_index)),
                TextContent::LocalizedText(LocalizedText {
                    string_id: "AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR".into(),
                    substitution_parameters: Some(SubstitutionParameters(HashMap::new())),
                }),
                TextContent::String(format!("hero entitlement label {}", hero_index)),
                TextContent::String(format!("station_name_{}", airing_index)),
                TextContent::String(format!("schedule_time_{}", airing_index)),
                TextContent::LocalizedText(LocalizedText::new("AV_LRC_AGE_RATING")),
                TextContent::String("maturity string".to_string()),
                TextContent::String(format!("content_descriptors_{}", airing_index)),
                LocalizedText {
                    string_id: "AV_LRC_TITLE_DETAILS_SYNOPSIS_SEASON".into(),
                    substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                        "number".into(),
                        TextContent::String(airing_index.to_string()),
                    )]))),
                }
                .into(),
                LocalizedText {
                    string_id: "AV_LRC_EPISODE_PREFIX".into(),
                    substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                        "number".into(),
                        TextContent::String(airing_index.to_string()),
                    )])))
                }
                .into(),
                TextContent::String(format!("episode_title_{}", airing_index)),
                TextContent::String(format!("synopsis_{}", airing_index)),
            ]
        );
    }
}
