use checkout_navigation::start_checkout::{prepare_navigation_to_checkout, StartCheckoutRequest};
use common_transform_types::actions::{TitleActionMetadata, TransitionAction};
#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
use cross_app_events::send_app_event;
use ignx_compositron::metrics::metric;
use ignx_compositron::prelude::Scope;
use location::PageType::Js;
use location::{JSPage, Location, RustPage};
use playback_navigation::types::PlaybackOrigin;
use serde_json::{Map, Value};
use transition_executor::Transition;

// For subscription flows, we should navigate to details page after successful sign up.
pub fn get_deferred_destination_params(
    secondary_button_action: &Option<TransitionAction>,
    scope: Scope,
) -> Map<String, Value> {
    let deferred_location = get_deferred_destination_location(secondary_button_action, scope);

    let mut params = Map::new();

    if let Some(deferred_location) = deferred_location {
        params.insert(
            "destinationPageType".to_string(),
            deferred_location.pageType.to_string().into(),
        );
        params.insert(
            "destinationPageParams".to_string(),
            deferred_location.pageParams.into(),
        );
    }

    params
}

pub fn get_deferred_destination_location(
    secondary_button_action: &Option<TransitionAction>,
    scope: Scope,
) -> Option<Location> {
    if let Some(action) = secondary_button_action {
        if matches!(
            action,
            TransitionAction::detail(_) | TransitionAction::legacyDetail(_)
        ) {
            return Transition::from_action_with_scope(action, scope).to_location();
        }
    }
    None
}

fn report_play_button_event(component_name: &str, scope: Scope) {
    let params: Map<String, Value> = Map::from_iter([(
        "heroType".to_string(),
        Value::String(component_name.to_owned()),
    )]);
    #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
    send_app_event(
        scope,
        "COLLECTION_PAGE_HERO_PLAY_BUTTON_PRESSED",
        "COLLECTION_PAGE",
        Some(params),
    )
}

pub fn get_location_for_transition_action(
    transition_action: &TransitionAction,
    secondary_button_action: &Option<TransitionAction>,
    signed_in: bool,
    gti: Option<String>,
    component_name: &str,
    scope: Scope,
    // content type is required for playback
    content_type: Option<String>,
    playback_origin_override: Option<PlaybackOrigin>,
) -> Option<Location> {
    let default_location = Transition::from_action_with_scope(transition_action, scope)
        .with_content_type(content_type)
        .with_title_id(gti.clone())
        .with_playback_origin_override(playback_origin_override)
        .to_location();

    let sign_in_location = Some(Location {
        pageType: Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE),
        pageParams: Map::new(),
    });

    match transition_action {
        TransitionAction::landing(_)
        | TransitionAction::registration(_)
        | TransitionAction::settings(_)
        | TransitionAction::search(_)
        | TransitionAction::clientSearch(_)
        | TransitionAction::watchList(_)
        | TransitionAction::yvl(_)
        | TransitionAction::legacyDetail(_)
        | TransitionAction::primeSignUp(_)
        | TransitionAction::detail(_)
        | TransitionAction::tournament(_)
        | TransitionAction::category(_)
        | TransitionAction::store(_)
        | TransitionAction::live(_)
        | TransitionAction::freeWithAds(_)
        | TransitionAction::myStuff(_)
        | TransitionAction::collection(_)
        | TransitionAction::testPlayground(_) => {
            metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "SwiftButtonPressed");
            default_location
        }
        TransitionAction::acquisition(target) => match &target.metadata {
            Some(TitleActionMetadata::AcquisitionTVOD(tvod_metadata)) => {
                metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "TVODButtonPressed");
                if !signed_in {
                    return sign_in_location;
                }

                let title_id_entry = (
                    "titleId".to_string(),
                    Value::String(gti.unwrap_or_default()),
                );

                #[allow(nonstandard_style, reason = "JSON compability")]
                let pageParams = if let Ok(metadata) = serde_json::to_value(tvod_metadata) {
                    Map::from_iter([
                        title_id_entry,
                        ("metadata".to_string(), metadata),
                        (
                            "contentType".to_string(),
                            Value::String(tvod_metadata.contentType.clone()),
                        ),
                    ])
                } else {
                    Map::from_iter([title_id_entry])
                };

                Some(Location {
                    pageType: Js(JSPage::TVOD_MODAL_HOST),
                    pageParams,
                })
            }
            Some(TitleActionMetadata::AcquisitionSVOD(benefit_id)) => {
                metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "SVODButtonPressed");
                if !signed_in {
                    return sign_in_location;
                }

                Some(prepare_navigation_to_checkout(
                    scope,
                    StartCheckoutRequest::Channels {
                        benefit_id: benefit_id.benefitId.clone(),
                        ref_marker: None,
                        ingress_point: component_name.to_string(),
                        rust_source_name: None,
                        title_id: Some(gti.unwrap_or_default()),
                        deferred_navigation_location: get_deferred_destination_location(
                            secondary_button_action,
                            scope,
                        ),
                        bundle_id: None,
                    },
                ))
            }
            Some(TitleActionMetadata::AcquisitionPrime(_)) => {
                metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "SVODButtonPressed");
                if !signed_in {
                    return sign_in_location;
                }

                let title_id_entry = (
                    "titleId".to_string(),
                    Value::String(gti.unwrap_or_default()),
                );

                #[allow(nonstandard_style, reason = "JSON compability")]
                let mut pageParams = Map::from_iter([
                    ("benefitId".to_string(), Value::String("Prime".to_string())),
                    (
                        "ingressPoint".to_string(),
                        Value::String(component_name.to_string()),
                    ),
                    title_id_entry,
                ]);

                pageParams.extend(get_deferred_destination_params(
                    secondary_button_action,
                    scope,
                ));
                Some(Location {
                    pageType: Js(JSPage::NEW_CONFIRMATION_PAGE),
                    pageParams,
                })
            }
            _ => default_location,
        },
        TransitionAction::player(_) | TransitionAction::playback(_) => {
            metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "PlaybackButtonPressed");
            if !signed_in {
                return sign_in_location;
            }

            report_play_button_event(component_name, scope);
            default_location
        }
        TransitionAction::openModal(_) => {
            metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "OpenModalButtonPressed");
            default_location
        }
        TransitionAction::consent(_) => {
            metric!("PageAction.Count", 1, "pageType" => "Collection", "actionName" => format!("WORKFLOW_REDIRECTION.on{}.{}", RustPage::RUST_COLLECTIONS, JSPage::CUSTOMER_CONSENT_PAGE));
            default_location
        }
        _ => default_location,
    }
}

#[cfg(test)]
pub mod test {
    use ignx_compositron::app::launch_only_scope;
    use location::PageType;
    use rstest::rstest;
    use rust_features::provide_context_test_rust_features;
    use std::collections::HashMap;
    use transition_executor::RPCManagerWrapper;

    use common_transform_types::{
        actions::{AcquisitionTVODMetadata, SwiftAction, TitleAcquisitionAction},
        playback_metadata::TitleActionMetadataType,
    };

    use super::*;

    fn mock_acquisition_action(metadata: TitleActionMetadata) -> TransitionAction {
        TransitionAction::acquisition(TitleAcquisitionAction {
            label: "Label".to_string(),
            metadata: Some(metadata),
            refMarker: "refMarker".to_string(),
        })
    }

    fn mock_details_page_action(page_id: String) -> TransitionAction {
        TransitionAction::detail(SwiftAction {
            text: None,
            analytics: HashMap::new(),
            refMarker: "ref_marker".to_string(),
            pageId: page_id,
            pageType: "pageType".to_string(),
            serviceToken: None,
            journeyIngressContext: None,
        })
    }

    fn mock_deferred_transition(action: TransitionAction) -> Transition {
        Transition {
            action,
            enable_duplicate_destination_navigation: None,
            deferred_action: None,
            is_signed_in: true,
            rpc_manager_wrapper: RPCManagerWrapper(None),
            rust_features: None,
            seamless_if_possible: false,
            content_type: None,
            title_id: None,
            playback_origin_override: None,
            ingress_source: None,
        }
    }

    fn expected_sign_in_location() -> Location {
        let expected_params: Map<String, Value> = Map::new();
        let expected_page: PageType = Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE);
        Location {
            pageType: expected_page,
            pageParams: expected_params,
        }
    }

    mod acquisition_tvod {
        use super::*;

        fn mock_acquisition_tvod() -> TransitionAction {
            mock_acquisition_action(TitleActionMetadata::AcquisitionTVOD(
                AcquisitionTVODMetadata {
                    refMarker: None,
                    offerToken: "mock offer token".to_string(),
                    contentType: "mock content type".to_string(),
                    offerType: "mock offer type".to_string(),
                    videoQuality: None,
                    message: None,
                    metadataActionType: None,
                },
            ))
        }

        #[test]
        fn not_signed_in_returns_open_code_based_registration_page() {
            let action = mock_acquisition_tvod();
            let signed_in = false;

            launch_only_scope(move |scope| {
                provide_context_test_rust_features(scope);
                assert_eq!(
                    get_location_for_transition_action(
                        &action,
                        &None,
                        signed_in,
                        Some("gti".to_string()),
                        "component_name",
                        scope,
                        None,
                        None,
                    ),
                    Some(expected_sign_in_location())
                );
            });
        }

        #[test]
        fn uses_gti_metadata_content_type_correctly() {
            let signed_in = true;
            let gti = Some("some gti".to_string());
            let action = mock_acquisition_tvod();

            let expected_page: PageType = Js(JSPage::TVOD_MODAL_HOST);
            let expected_params: Map<String, Value> = Map::from_iter([
                ("titleId".to_string(), Value::String("some gti".to_string())),
                (
                    "metadata".to_string(),
                    Value::Object(Map::from_iter([
                        (
                            "offerToken".to_string(),
                            "mock offer token".to_string().into(),
                        ),
                        (
                            "contentType".to_string(),
                            "mock content type".to_string().into(),
                        ),
                        (
                            "offerType".to_string(),
                            "mock offer type".to_string().into(),
                        ),
                    ])),
                ),
                (
                    "contentType".to_string(),
                    Value::String("mock content type".to_string()),
                ),
            ]);

            let expected = Location {
                pageType: expected_page,
                pageParams: expected_params,
            };

            launch_only_scope(move |scope| {
                provide_context_test_rust_features(scope);
                assert_eq!(
                    get_location_for_transition_action(
                        &action,
                        &None,
                        signed_in,
                        gti,
                        "irrelevant component name",
                        scope,
                        Some("MOVIE".to_string()),
                        None,
                    ),
                    Some(expected)
                );
            });
        }
    }

    mod acquisition_svod {
        use super::*;
        use common_transform_types::actions::{AcquisitionSVODMetadata, BaseTitleActionMetadata};
        use location::PageType::Rust;
        use rust_features::MockRustFeaturesBuilder;

        #[rstest]
        #[case(true, false, Some(mock_details_page_action("some_page".to_string())), Js(JSPage::NEW_CONFIRMATION_PAGE), Map::from_iter([
        (
        "checkout_request_type".to_string(),
        Value::String("Channels".to_string()),
        ),
        (
        "benefitId".to_string(),
        Value::String("benefit_id".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        (
        "destinationPageType".to_string(),
        Value::String("OPEN_DETAIL_PAGE".to_string()),
        ),
        (
        "destinationPageParams".to_string(),
        mock_deferred_transition(mock_details_page_action("some_page".to_string())).to_location().unwrap().pageParams.into()
        )
        ]))]
        #[case(true, false, Some(mock_details_page_action("".to_string())), Js(JSPage::NEW_CONFIRMATION_PAGE), Map::from_iter([
        (
        "checkout_request_type".to_string(),
        Value::String("Channels".to_string()),
        ),
        (
        "benefitId".to_string(),
        Value::String("benefit_id".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        ]))]
        #[case(true, false, None, Js(JSPage::NEW_CONFIRMATION_PAGE), Map::from_iter([
        (
        "checkout_request_type".to_string(),
        Value::String("Channels".to_string()),
        ),
        (
        "benefitId".to_string(),
        Value::String("benefit_id".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        ]))]
        #[case(
            false,
            false,
            None,
            Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE),
            Map::new()
        )]
        #[case(true, true, Some(mock_details_page_action("some_page".to_string())), Rust(RustPage::RUST_CHECKOUT), Map::from_iter([
        (
        "checkout_request_type".to_string(),
        Value::String("Channels".to_string()),
        ),
        (
        "benefitId".to_string(),
        Value::String("benefit_id".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        (
        "destinationPageType".to_string(),
        Value::String("OPEN_DETAIL_PAGE".to_string()),
        ),
        (
        "destinationPageParams".to_string(),
        mock_deferred_transition(mock_details_page_action("some_page".to_string())).to_location().unwrap().pageParams.into()
        )
        ]))]
        #[case(true, true, Some(mock_details_page_action("".to_string())), Rust(RustPage::RUST_CHECKOUT), Map::from_iter([
        (
        "checkout_request_type".to_string(),
        Value::String("Channels".to_string()),
        ),
        (
        "benefitId".to_string(),
        Value::String("benefit_id".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        ]))]
        #[case(true, true, None, Rust(RustPage::RUST_CHECKOUT), Map::from_iter([
        (
        "checkout_request_type".to_string(),
        Value::String("Channels".to_string()),
        ),
        (
        "benefitId".to_string(),
        Value::String("benefit_id".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        ]))]
        #[case(
            false,
            true,
            None,
            Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE),
            Map::new()
        )]
        fn acquisition_svod(
            #[case] signed_in: bool,
            #[case] rust_checkout_enabled: bool,
            #[case] secondary_action: Option<TransitionAction>,
            #[case] expected_page: PageType,
            #[case] expected_params: Map<String, Value>,
        ) {
            let action = mock_acquisition_action(TitleActionMetadata::AcquisitionSVOD(
                AcquisitionSVODMetadata {
                    refMarker: "refMarker".to_string(),
                    benefitId: "benefit_id".to_string(),
                    metadataActionType: Some(TitleActionMetadataType::AcquisitionSVOD),
                },
            ));

            let expected = Location {
                pageType: expected_page,
                pageParams: expected_params,
            };
            launch_only_scope(move |scope| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_checkout_enabled(rust_checkout_enabled)
                    .build_as_mock_and_real_into_context(false, scope);
                assert_eq!(
                    get_location_for_transition_action(
                        &action,
                        &secondary_action,
                        signed_in,
                        Some("gti".to_string()),
                        "component_name",
                        scope,
                        Some("MOVIE".to_string()),
                        None,
                    ),
                    Some(expected)
                );
            });
        }

        #[rstest]
        #[case(true, Some(mock_details_page_action("some_page".to_string())), Js(JSPage::NEW_CONFIRMATION_PAGE), Map::from_iter([
        (
        "benefitId".to_string(),
        Value::String("Prime".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        (
        "destinationPageType".to_string(),
        Value::String("OPEN_DETAIL_PAGE".to_string()),
        ),
        (
        "destinationPageParams".to_string(),
        mock_deferred_transition(mock_details_page_action("some_page".to_string())).to_location().unwrap().pageParams.into()
        )
        ]))]
        #[case(true, Some(mock_details_page_action("".to_string())), Js(JSPage::NEW_CONFIRMATION_PAGE), Map::from_iter([
        (
        "benefitId".to_string(),
        Value::String("Prime".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        ]))]
        #[case(true, None, Js(JSPage::NEW_CONFIRMATION_PAGE), Map::from_iter([
        (
        "benefitId".to_string(),
        Value::String("Prime".to_string()),
        ),
        (
        "ingressPoint".to_string(),
        Value::String("component_name".to_string()),
        ),
        (
        "titleId".to_string(),
        Value::String("gti".to_string()),
        ),
        ]))]
        #[case(false, None, Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE), Map::new())]
        fn acquisition_prime(
            #[case] signed_in: bool,
            #[case] secondary_action: Option<TransitionAction>,
            #[case] expected_page: PageType,
            #[case] expected_params: Map<String, Value>,
        ) {
            let action = mock_acquisition_action(TitleActionMetadata::AcquisitionPrime(
                BaseTitleActionMetadata {
                    refMarker: None,
                    metadataActionType: Some(TitleActionMetadataType::AcquisitionPrime),
                },
            ));

            let expected = Location {
                pageType: expected_page,
                pageParams: expected_params,
            };

            launch_only_scope(move |scope| {
                provide_context_test_rust_features(scope);
                assert_eq!(
                    get_location_for_transition_action(
                        &action,
                        &secondary_action,
                        signed_in,
                        Some("gti".to_string()),
                        "component_name",
                        scope,
                        Some("MOVIE".to_string()),
                        None,
                    ),
                    Some(expected)
                );
            });
        }
    }

    mod playback {
        use super::*;
        use common_transform_types::actions::{PlaybackAction, TitlePlaybackAction};
        use common_transform_types::playback_metadata::{
            PlaybackMetadata, PlaybackPosition, UserEntitlementMetadata, UserPlaybackMetadata,
        };

        fn mock_playback_action() -> TransitionAction {
            TransitionAction::playback(TitlePlaybackAction {
                playbackMetadata: PlaybackMetadata {
                    refMarker: "fake marker".to_string(),
                    metadataActionType: TitleActionMetadataType::AcquisitionSVOD,
                    userPlaybackMetadata: UserPlaybackMetadata {
                        runtimeSeconds: None,
                        timecodeSeconds: None,
                        hasStreamed: None,
                        isLinear: None,
                        linearStartTime: None,
                        linearEndTime: None,
                    },
                    userEntitlementMetadata: UserEntitlementMetadata {
                        entitlementType: "".to_string(),
                        benefitType: vec![],
                    },
                    playbackExperienceMetadata: None,
                    videoMaterialType: "".to_string(),
                    position: PlaybackPosition::FeatureFinished,
                    contentDescriptors: None,
                    startPositionEpochUtc: None,
                    channelId: None,
                    playbackTitle: Some("a gti here".to_string()),
                    catalogMetadata: None,
                    isTrailer: None,
                    isUnopenedRental: false,
                },
                label: None,
                refMarker: "fake marker".to_string(),
            })
        }

        fn mock_player_action() -> TransitionAction {
            TransitionAction::player(
                PlaybackAction::create_populated_action("a gti here")
                    .with_ref_marker("fake marker".to_string()),
            )
        }

        fn expected_playback_params(is_player: bool) -> Map<String, Value> {
            let playback_title = if is_player {
                None
            } else {
                Some("a gti here".to_string())
            };
            let metadata_action_type = if is_player {
                "Playback".to_string()
            } else {
                "AcquisitionSVOD".to_string()
            };
            let position = if is_player {
                "LIVE_STREAM_WATCH_NOW".to_string()
            } else {
                "FEATURE_FINISHED".to_string()
            };
            let linear = if is_player { Some(true) } else { None };
            let video_material_type = if is_player {
                "LiveStreaming".to_string()
            } else {
                "".to_string()
            };
            Map::from_iter([
                ("contentType".into(), Value::String("MOVIE".to_string())),
                ("maturityRating".into(), Value::Null),
                (
                    "metadata".into(),
                    Value::Object(Map::from_iter([
                        ("catalogMetadata".into(), Value::Null),
                        ("channelId".into(), Value::Null),
                        ("contentDescriptors".into(), Value::Null),
                        ("isTrailer".into(), Value::Null),
                        ("isUnopenedRental".into(), false.into()),
                        ("metadataActionType".into(), metadata_action_type.into()),
                        ("playbackExperienceMetadata".into(), Value::Null),
                        ("playbackTitle".into(), playback_title.into()),
                        ("position".into(), position.into()),
                        ("refMarker".into(), "fake marker".into()),
                        ("startPositionEpochUtc".into(), Value::Null),
                        (
                            "userEntitlementMetadata".into(),
                            Value::Object(Map::from_iter([
                                ("benefitType".into(), Value::Array(vec![])),
                                ("entitlementType".into(), "".into()),
                            ])),
                        ),
                        (
                            "userPlaybackMetadata".into(),
                            Value::Object(Map::from_iter([
                                ("hasStreamed".into(), Value::Null),
                                ("isLinear".into(), linear.into()),
                                ("linearEndTime".into(), Value::Null),
                                ("linearStartTime".into(), Value::Null),
                                ("runtimeSeconds".into(), Value::Null),
                                ("timecodeSeconds".into(), Value::Null),
                            ])),
                        ),
                        ("videoMaterialType".into(), video_material_type.into()),
                    ])),
                ),
                ("playbackOrigin".into(), "Rust".into()),
                ("titleId".into(), "a gti here".into()),
                ("trailerOrExtraContent".into(), false.into()),
            ])
        }

        #[rstest]
        #[case(mock_playback_action(), Location { pageType: Js(JSPage::START_PLAYBACK), pageParams: expected_playback_params(false) }, true)]
        #[case(mock_playback_action(), expected_sign_in_location(), false)]
        #[case(mock_player_action(), Location { pageType: Js(JSPage::START_PLAYBACK), pageParams: expected_playback_params(true) }, true)]
        #[case(mock_player_action(), expected_sign_in_location(), false)]
        fn player_action(
            #[case] action: TransitionAction,
            #[case] expected_location: Location,
            #[case] signed_in: bool,
        ) {
            launch_only_scope(move |scope| {
                provide_context_test_rust_features(scope);
                assert_eq!(
                    get_location_for_transition_action(
                        &action,
                        &None,
                        signed_in,
                        Some("a gti here".to_string()),
                        "component_name",
                        scope,
                        Some("MOVIE".to_string()),
                        None,
                    ),
                    Some(expected_location)
                );
            });
        }
    }
}
