use crate::auto_rotation::{
    setup_auto_rotation_controller, AutoRotationControllerArgs, AutoRotationFocusArea,
    UserHeroInteraction,
};
use crate::types::{ButtonVariant, HeroItemMediaStrategy, SizeConfig};
use crate::utils::hero::get_location_for_transition_action;
use crate::utils::tts::get_hero_metadata_descriptions;
use crate::HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy;
use amzn_fable_tokens::*;
use app_events::collection::contextual_menu::report_page_metadata_change;
use app_events::collection::hero::{
    report_hero_action_button_pressed, report_hero_scroll_clickstream,
};
use auth::AuthContext;
use common_transform_types::actions::{
    TitleAcquisitionAction, TitleActionMetadata, TransitionAction,
};
use container_types::ui_signals::{CommonCarouselMetadata, HeroItemModel};
use contextual_menu_types::prelude::ContextualMenuData;
use fableous::animations::MotionDuration;
use fableous::animations::*;
use fableous::buttons::primary_button::*;
use fableous::buttons::secondary_button::*;
use fableous::cards::sizing::CardDimensions;
use fableous::pagination_dots::pagination_dots::*;
use fableous::pointer_control::is_pointer_control_active;
use fableous::progress_bar::*;
use fableous::typography::type_ramp::TypeRamp;
use fableous::typography::typography::*;
use firetv::use_firetv_context;
use ignx_compositron::impression::{ViewImpressionData, ViewStripe};
use ignx_compositron::input::KeyCode;
use ignx_compositron::prelude::use_context;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::{on_cleanup, store_value};
use ignx_compositron::text::LocalizedText;
use ignx_compositron::time::*;
use ignx_compositron::tts::TTSEnabledContext;
use ignx_compositron::{compose, compose_option, Composer};
use linear_common::get_news_page_params;
use location::JSPage::OPEN_DETAIL_PAGE;
use location::RustPage::RUST_DETAILS;
use location::{JSPage, Location, PageType, RustPage};
use media_background::types::context::{BackgroundState, MediaBackgroundStatusContext};
use media_background::types::{
    derive_is_hero_trailer_playing_signal, HybridPlaybackContext, MediaBackgroundType,
    MediaStrategy, ReleaseState, TrailerPlaybackContext,
};
use metadata_components::ui::legal_messages::*;
use metadata_components::ui::maturity_rating::*;
use metadata_components::ui::regulatory_label::*;
use navigation_menu::context::nav_context::{FocusedNav, NavControl, TopNavMode};
use navigation_menu::utils::{get_page_params, CollectionsPageParams, PageParams};
use playback_navigation::get_release_state_and_playback_origin_from_hero_action;
use router::hooks::{use_navigation, use_router};
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;

use rust_features::WeblabTreatmentString;

use cross_app_events::app_event::AppEventReporter;
use settings_manager::ApplicationSettingsContext;
use std::rc::Rc;
use title_details::components::entitlement::*;
use title_details::components::metadata::*;
use title_details::components::title::*;
use transition_executor::Transition;
use watch_modal::helpers::hero_data::create_hero_ssm_data;

const EXPANDED_HEIGHT: f32 = 876.0;
const COLLAPSED_HEIGHT: f32 = 626.0;

// f32::INFINITY doesn't work, so I used hero's expanded height
const EXPANDED_COLLAPSIBLE_MAX_HEIGHT: f32 = EXPANDED_HEIGHT;
const COLLAPSED_COLLAPSIBLE_MAX_HEIGHT: f32 = 0f32;

const EXPANDED_PADDING: Padding = Padding {
    start: 0.0,
    end: 0.0,
    top: 0.0,
    bottom: 68.0,
};
const COLLAPSED_PADDING: Padding = Padding {
    start: 0.0,
    end: 0.0,
    top: 0.0,
    bottom: 12.0,
};

// Setting the max_height of the container containing the SecondaryRow to 0,
// or any value lower than the min_height (57) defined by Fable will cause
// a panic so we set a negative bottom padding instead when collapsed
const EXPANDED_SECONDARY_PADDING: Padding = Padding {
    start: 0.0,
    end: 0.0,
    top: 0.0,
    bottom: 0.0,
};
const COLLAPSED_SECONDARY_PADDING: Padding = Padding {
    start: 0.0,
    end: 0.0,
    top: 0.0,
    bottom: -57.0,
};

const TOTAL_WIDTH: f32 = 1680.0;
const LEFT_SECTION_WIDTH: f32 = 720.0;
const RIGHT_SECTION_WIDTH: f32 = 600.0;
const MID_SECTION_WIDTH: f32 = TOTAL_WIDTH - (RIGHT_SECTION_WIDTH + LEFT_SECTION_WIDTH);

const INCOMING_HERO_OFFSET: f32 = 50.0f32;

const LEGAL_TEXT_SECTION_TEST_ID: &str = "legal-text-section-test-id";
pub const HERO_CARD_TEST_ID: &str = "hero";

type ButtonBuilder = dyn Fn(&AppContext) -> Option<RowComposable>;

fn hero_item_exit_duration() -> Duration {
    FableMotionDuration::Medium.to_duration()
}

const LONG_SYNOPSIS_CHARACTER_LIMIT: usize = 24;

pub const IMPRESSION_DURATION: Duration = Duration::from_secs(1);
pub const IMPRESSION_STRIPE: ViewStripe = ViewStripe::Area(1.0);

/**
 * Hero component synopsis should always be 3 lines except for the following exceptions where it should be 2:
 *
 * 1) Hero item has a missing title art and a text title that is longer than 24 characters long
 * 2) Hero container is always collapsed, i.e. channel hero
 *
 * For more info see the [Figma specs](https://www.figma.com/design/b6O2spfMF2bLv0wbEKF4OE/Remaster---LR-Design-Specs---Phase-1-[fbl-1.3]?node-id=621-106176&t=f67m8mLV56v4ez31-0)
 */
fn calculate_synopsis_max_lines(
    size_config: SizeConfig,
    title_art_exists: bool,
    fallback_title_length: usize,
) -> u32 {
    match size_config {
        SizeConfig::AlwaysCollapsed => 2,
        SizeConfig::AlwaysExpanded | SizeConfig::Expandable(_) => {
            if title_art_exists || fallback_title_length <= LONG_SYNOPSIS_CHARACTER_LIMIT {
                3
            } else {
                2
            }
        }
    }
}

fn prepare_contextual_menu_telemetry(ctx: AppContext, location: &Location) {
    let page_params = get_page_params(location, ctx.scope());
    if let Some(PageParams::Collections(page_params)) = page_params {
        let CollectionsPageParams {
            page_id,
            page_type,
            service_token: _,
        } = page_params;
        report_page_metadata_change(AppEventReporter::new(ctx.scope()), page_type, page_id);
    }
}

fn get_card_dimensions(expanded: bool) -> CardDimensions {
    if expanded {
        CardDimensions {
            width: TOTAL_WIDTH,
            height: EXPANDED_HEIGHT,
        }
    } else {
        CardDimensions {
            width: TOTAL_WIDTH,
            height: COLLAPSED_HEIGHT,
        }
    }
}

fn get_hero_item_media_strategy(item: &HeroItemModel) -> Option<HeroItemMediaStrategy> {
    let media_strategy = item
        .media_background_data
        .with_untracked(|data| match data {
            MediaBackgroundType::StandardHero(data) => Some(data.media_strategy.clone()),
            MediaBackgroundType::Tentpole(data) => Some(data.media_strategy.clone()),
            MediaBackgroundType::Linear(data) => Some(data.media_strategy.clone()),
            _ => None,
        })?;
    match media_strategy {
        MediaStrategy::Live => Some(HeroItemMediaStrategy::Live),
        MediaStrategy::Promo => Some(HeroItemMediaStrategy::Promo),
        MediaStrategy::Linear => Some(HeroItemMediaStrategy::Linear),
    }
}

type ButtonVariantIndexFocusedValue = (ButtonVariant, usize);

#[Composer]
pub fn HeroSig(
    ctx: &AppContext,
    #[into] items: RwSignal<Vec<HeroItemModel>>,
    #[into] size_config: SizeConfig,
    #[optional = Rc::new(move |_| {})] on_item_focus: Rc<dyn Fn(usize)>,
    #[optional = Rc::new(move |_, _| {})] open_item_contextual_menu: Rc<
        dyn Fn(usize, ButtonVariant),
    >,
    #[optional = Rc::new(move || {})] on_focus: Rc<dyn Fn()>,
    #[optional = Rc::new(move || {})] on_ready: Rc<dyn Fn()>,
    on_back_pressed: Rc<dyn Fn()>,
    #[into]
    #[optional = 0]
    default_focus_index: MaybeSignal<usize>,
    metadata: RwSignal<CommonCarouselMetadata>,
    #[optional = Rc::new(|| {})] on_move_to_second_container: Rc<dyn Fn()>,
    is_tentpole: bool,
    update_cm: WriteSignal<Option<ContextualMenuData>>,
    on_item_view: Rc<dyn Fn(ViewImpressionData, RwSignal<HeroItemModel>, usize, CardDimensions)>,
    on_item_highlight: Rc<dyn Fn(RwSignal<HeroItemModel>, usize, CardDimensions)>,
    #[optional = Rc::new(move |_,_,_,_| {})] on_item_select: Rc<
        dyn Fn(RwSignal<HeroItemModel>, usize, Option<CardDimensions>, ButtonVariant),
    >,
    #[optional = None] auto_rotation_wait_duration_override: Option<
        HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy,
    >,
) -> RowComposable {
    let scope = ctx.scope();

    let expanded = create_memo(ctx.scope(), move |_| match size_config {
        SizeConfig::AlwaysCollapsed => false,
        SizeConfig::AlwaysExpanded => true,
        SizeConfig::Expandable(signal) => signal.get(),
    });

    let hero_focused = create_focus_signal(ctx.scope());

    // focused_index is set after a delayed amount (because we want to wait a bit for the previous hero item to fade out)
    let focused_index = create_rw_signal(ctx.scope(), default_focus_index.get_untracked());
    // non_delayed_focused_index happens as soon as the (auto) rotation to the next item happens and is useful to
    // determine a few properties about the new item (such as if it contains a hero trailer or not).
    let non_delayed_focused_index =
        create_rw_signal(ctx.scope(), default_focus_index.get_untracked());

    // The Hero component reuses the same set of components for all the items regardless of how many hero items/cards it contains. If the user
    // navigates to the "next item", what happens is that the hero fades out the components used by the current item and everything goes black.
    // To display the next item, we reuse the same components to render the content of the next item, then fade in these components.
    //
    // So technically speaking, if the user is focused on the PrimaryButton and they press "Right", there is no button to the "left/right". This
    // means that we can't rely on the SDK focus management and therefore need to partially manage the focus logic ourselves.

    // This value is None if the user is not focused on any of the hero buttons, otherwise
    // it is a tuple (ButtonVariant, HeroItemIndex), e.g. (Primary, 0) is the primary button of the
    // first hero item.
    let button_focused_value =
        create_focus_value_signal::<ButtonVariantIndexFocusedValue>(ctx.scope());

    // In case the user is not focused on any hero buttons, we can retrieve the last focused button
    // with this memo.
    let last_button_focused = create_memo(
        ctx.scope(),
        move |old_value: Option<&Option<ButtonVariantIndexFocusedValue>>| {
            // If the Hero gets collapsed, we need to "forget" what was focused on, so that
            // we can re-apply the correct Default focus button again.
            if !expanded.get() {
                None
            } else {
                button_focused_value
                    .get()
                    .map_or_else(|| old_value.cloned().unwrap_or(None), Some)
            }
        },
    );

    let is_hero_initial_focus_swap_enabled = {
        try_use_rust_features(ctx.scope()).is_some_and(|t| t.is_hero_initial_focus_swap_enabled())
    };

    let default_focus_experiment_treatment = {
        try_use_rust_features(ctx.scope()).map_or(WeblabTreatmentString::C, |t| {
            t.get_home_default_focus_experiment_treatment_string()
        })
    };

    // If this is true the Primary button is shown above the More Details button
    let buttons_flipped = {
        matches!(
            default_focus_experiment_treatment,
            WeblabTreatmentString::T2
        )
    };

    // If the hero has never been focused yet, we will default to the PrimaryButton. Otherwise,
    // it makes sense to just memorise which button was last focused on and use that as the preferred
    // button.
    // TODO: In the AlwaysExpanded case (e.g. Tentpole), we always show the secondary button, do we want to "skip" the secondary button when navigating from TopNav?
    let preferred_button_focus = Signal::derive(ctx.scope(), move || {
        let last_button_focused = last_button_focused.get();
        if let Some((last_focused_variant, _)) = last_button_focused {
            last_focused_variant
        } else if is_hero_initial_focus_swap_enabled {
            ButtonVariant::Secondary
        } else {
            ButtonVariant::Primary
        }
    });
    let is_primary_button_preferred = Signal::derive(ctx.scope(), move || {
        preferred_button_focus.get() == ButtonVariant::Primary
    });
    let is_secondary_button_preferred = Signal::derive(ctx.scope(), move || {
        preferred_button_focus.get() == ButtonVariant::Secondary
    });

    let item_count = create_rw_signal(ctx.scope(), items.with_untracked(|items| items.len()));

    let TTSEnabledContext(is_tts_enabled) = match use_context::<TTSEnabledContext>(ctx.scope()) {
        Some(tts) => tts,
        None => {
            log::error!("[hero_sig] Cannot find TTSEnabledContext in scope. Fallback tts_enabled value to false");
            TTSEnabledContext(create_rw_signal(ctx.scope(), false).read_only())
        }
    };

    create_effect(ctx.scope(), move |_| {
        items.with(|items| {
            item_count.set(items.len());
        });
    });

    create_effect(ctx.scope(), move |_| {
        focused_index.set(default_focus_index.get());
        non_delayed_focused_index.set(default_focus_index.get());
    });

    let on_focus = {
        let on_item_focus = on_item_focus.clone();
        move || {
            let focused_index = focused_index.try_get_untracked();
            if let Some(focused_index) = focused_index {
                on_focus();
                on_item_focus(focused_index);
            }
        }
    };

    let on_long_press_primary_button = {
        let on_item_long_press = Rc::clone(&open_item_contextual_menu);
        Rc::new(move || {
            let focused_index = focused_index.try_get_untracked();
            if let Some(focused_index) = focused_index {
                on_item_long_press(focused_index, ButtonVariant::Primary);
            }
        })
    };

    let (on_long_press_secondary_button, _) = create_signal(ctx.scope(), {
        let on_item_long_press = Rc::clone(&open_item_contextual_menu);
        Rc::new(move || {
            let focused_index = focused_index.try_get_untracked();
            if let Some(focused_index) = focused_index {
                on_item_long_press(focused_index, ButtonVariant::Secondary);
            }
        })
    });

    let height = create_rw_signal(
        ctx.scope(),
        if expanded.get_untracked() {
            EXPANDED_HEIGHT
        } else {
            COLLAPSED_HEIGHT
        },
    );

    let padding = create_rw_signal(
        ctx.scope(),
        if expanded.get_untracked() {
            EXPANDED_PADDING
        } else {
            COLLAPSED_PADDING
        },
    );

    let secondary_padding = create_rw_signal(
        ctx.scope(),
        if expanded.get_untracked() {
            EXPANDED_SECONDARY_PADDING
        } else {
            COLLAPSED_SECONDARY_PADDING
        },
    );

    let show_collapsibles = create_rw_signal(
        ctx.scope(),
        match size_config {
            SizeConfig::AlwaysCollapsed => true,
            SizeConfig::AlwaysExpanded => true,
            SizeConfig::Expandable(initial) => initial.get_untracked(),
        },
    );

    let collapsible_opacity = create_rw_signal(
        ctx.scope(),
        if show_collapsibles.get_untracked() {
            1.0f32
        } else {
            0.0f32
        },
    );

    let collapsible_max_height = create_rw_signal(
        ctx.scope(),
        if show_collapsibles.get_untracked() {
            EXPANDED_COLLAPSIBLE_MAX_HEIGHT
        } else {
            COLLAPSED_COLLAPSIBLE_MAX_HEIGHT
        },
    );

    // Opacity for the hero item, which means excluding the pagination dots
    let hero_item_opacity = create_rw_signal(ctx.scope(), 1.0f32);
    let hero_offset = create_rw_signal(ctx.scope(), 0.0f32);

    create_effect(ctx.scope(), move |_| {
        items.with(|items| {
            let expanded = expanded.get();
            items.iter().for_each(|item| {
                if let MediaBackgroundType::StandardHero(mb) = item.media_background_data.get() {
                    mb.is_expanded.set(expanded);
                };
            })
        })
    });

    let router = use_router(ctx.scope());
    let location = router.location();
    let is_news_page = Signal::derive(ctx.scope(), move || {
        location.get().contains_page_params(get_news_page_params())
    });

    let is_initial_call = create_rw_signal(ctx.scope(), true);
    let opening_menu = create_rw_signal(ctx.scope(), false);
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let hero_focused = hero_focused.get();

            if let Some(hero_trailer_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                // Ignore the initial non-focus event if we just came from a rust details page
                // This allows us to restore the hero that is already playing once we re-focus (assuming the MB is correct)
                if router.get_last_navigation_action().from == PageType::Rust(RUST_DETAILS)
                    && hero_trailer_context.should_play_hero.get_untracked()
                    && is_initial_call.get_untracked()
                {
                    is_initial_call.set_untracked(false);
                    return;
                } else {
                    is_initial_call.set_untracked(false);
                }

                if opening_menu.get_untracked() {
                    opening_menu.set(false);

                    let seamless_transition_enabled = try_use_rust_features(ctx.scope())
                        .is_some_and(|v| v.is_seamless_transition_on_rust_live_hero_enabled());

                    if seamless_transition_enabled {
                        return;
                    }
                }
                // News page does not rely on hero focus to begin autoplay
                let should_play = is_news_page.get_untracked() || hero_focused;

                hero_trailer_context.should_play_hero.set(should_play)
            }
        }
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |prev| {
            if !matches!(size_config, SizeConfig::Expandable(_)) {
                return true;
            }

            let expanded = expanded.get();

            if Some(expanded) == prev {
                return expanded;
            }

            ctx.with_animation(fable_motion_persistent_standard(), move || {
                height.set(if expanded {
                    EXPANDED_HEIGHT
                } else {
                    COLLAPSED_HEIGHT
                });
                padding.set(if expanded {
                    EXPANDED_PADDING
                } else {
                    COLLAPSED_PADDING
                });
                secondary_padding.set(if expanded {
                    EXPANDED_SECONDARY_PADDING
                } else {
                    COLLAPSED_SECONDARY_PADDING
                });
                collapsible_max_height.set(if expanded {
                    EXPANDED_COLLAPSIBLE_MAX_HEIGHT
                } else {
                    COLLAPSED_COLLAPSIBLE_MAX_HEIGHT
                });
                show_collapsibles.set(expanded);
            });
            ctx.with_animation(fable_motion_linear_medium(), move || {
                collapsible_opacity.set(if expanded { 1.0f32 } else { 0.0f32 });
            });

            expanded
        }
    });

    let initial_item = items.with_untracked(|items| {
        let focused_index = focused_index.get_untracked();
        let initial_index = if focused_index < items.len() {
            focused_index
        } else {
            0
        };
        #[allow(
            clippy::indexing_slicing,
            reason = "Guarded in standard hero and tentpole hero to ensure non-empty items"
        )]
        items[initial_index].clone()
    });
    let initial_item_ui_data = initial_item
        .ui_data
        .with_untracked(|ui_data| ui_data.clone());

    let gti = create_rw_signal(ctx.scope(), initial_item.gti.get_untracked());
    let focused_item = create_rw_signal(ctx.scope(), initial_item);

    create_effect(ctx.scope(), move |_| {
        items.try_with(|items| {
            let focused_index = focused_index.get();
            if let Some(item) = items.get(focused_index).cloned() {
                focused_item.set(item);
            }
        });
    });

    let title_data = DataSignal::<TitleDataSignal>::from_data(
        ctx.scope(),
        initial_item_ui_data.title_data.get_untracked(),
    );
    let synopsis = create_rw_signal(ctx.scope(), initial_item_ui_data.synopsis.get_untracked());
    let synopsis_max_lines = Signal::derive(ctx.scope(), move || {
        Some(title_data.with(|t| {
            calculate_synopsis_max_lines(size_config, t.title_art_url.is_some(), t.title_text.len())
        }))
    });

    let primary_button = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data.primary_button_action.get_untracked(),
    );
    let (secondary_button, set_secondary_button) = create_signal(
        ctx.scope(),
        initial_item_ui_data.secondary_button_action.get_untracked(),
    );
    let entitlement_label = OptionSignal::<DataSignal<EntitlementLabelDataSignal>>::from_data(
        ctx.scope(),
        initial_item_ui_data.entitlement_label.get_untracked(),
    );
    let legal_messages = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data.legal_messages.get_untracked(),
    );
    let maturity_rating_string = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data.maturity_rating_string.get_untracked(),
    );
    let maturity_rating_image = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data.maturity_rating_image.get_untracked(),
    );
    let regulatory_label_string = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data.regulatory_label_string.get_untracked(),
    );

    let progress_bar_percentage = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data.progress_bar_percentage.get_untracked(),
    );

    let metadata_row: VecSignal<DataSignal<_>> = VecSignal::from_data(
        ctx.scope(),
        initial_item_ui_data.metadata_rows.get_untracked(),
    );
    let show_metadata_row = create_rw_signal(
        ctx.scope(),
        !initial_item_ui_data
            .metadata_rows
            .get_untracked()
            .is_empty(),
    );
    let primary_button_variant = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data.primary_button_variant.get_untracked(),
    );
    let secondary_button_variant = create_rw_signal(
        ctx.scope(),
        initial_item_ui_data
            .secondary_button_variant
            .get_untracked(),
    );

    let component_name = if is_tentpole {
        "TentpoleContainer"
    } else {
        "StandardHeroContainer"
    };

    create_effect(scope, move |_| {
        focused_item.with(|item| {
            item.ui_data.with(|data| {
                scope.batch(|| {
                    title_data.set_data(data.title_data.get_untracked());
                    synopsis.set(data.synopsis.get_untracked());
                    primary_button.set(data.primary_button_action.get_untracked());
                    gti.set(item.gti.get_untracked());
                    set_secondary_button.set(data.secondary_button_action.get_untracked());
                    entitlement_label.set_data(data.entitlement_label.get_untracked());
                    legal_messages.set(data.legal_messages.get_untracked());
                    maturity_rating_image.set(data.maturity_rating_image.get_untracked());
                    maturity_rating_string.set(data.maturity_rating_string.get_untracked());
                    regulatory_label_string.set(data.regulatory_label_string.get_untracked());
                    metadata_row.set_data(data.metadata_rows.get_untracked());
                    show_metadata_row.set(!data.metadata_rows.get_untracked().is_empty());
                    primary_button_variant.set(data.primary_button_variant.get_untracked());
                    secondary_button_variant.set(data.secondary_button_variant.get_untracked());
                    progress_bar_percentage.set(data.progress_bar_percentage.get_untracked());
                })
            });
        });
    });

    let metadata_descriptions = get_hero_metadata_descriptions(scope, focused_item.into());

    let movement_in_progress = create_rw_signal(ctx.scope(), false);

    let move_in_direction = {
        let ctx = ctx.clone();

        // Returns a boolean on whether a rotation has been scheduled or not.
        Rc::new(move |key: KeyCode, target_index: Option<usize>| {
            let Some(total_items) = item_count.try_get_untracked() else {
                // this closure can be called from a scheduled task
                // check validity of scope before continue
                return false;
            };
            let current_focus = focused_index.get_untracked();

            if movement_in_progress.get_untracked()
                || total_items <= 1
                || (key == KeyCode::Left && current_focus == 0)
            {
                return false;
            }

            movement_in_progress.set(true);

            let new_index = if let Some(target_index) = target_index {
                target_index
            } else if key == KeyCode::Left {
                current_focus - 1
            } else {
                (current_focus + 1) % total_items
            };

            ctx.with_animation(
                Animation::default()
                    .with_interpolation(FableMotionEasing::Linear.to_interpolation())
                    .with_duration(hero_item_exit_duration()),
                move || {
                    hero_item_opacity.set(0.0f32);
                },
            );

            non_delayed_focused_index.set(new_index);
            // eventually we should use Animation::on_finish here to ensure it operates synchronously.
            // blocked by https://issues.amazon.com/issues/LRCP-4083.
            ctx.schedule_task(Instant::now() + hero_item_exit_duration(), {
                let ctx = ctx.clone();

                move || {
                    focused_index.try_set(new_index);

                    let enter_duration = FableMotionDuration::Medium.to_duration();
                    ctx.with_animation(
                        Animation::default()
                            .with_duration(enter_duration)
                            .with_interpolation(FableMotionEasing::Linear.to_interpolation()),
                        move || hero_item_opacity.try_set(1.0f32),
                    );

                    hero_offset.try_set(if key == KeyCode::Right {
                        INCOMING_HERO_OFFSET
                    } else {
                        -INCOMING_HERO_OFFSET
                    });
                    ctx.with_animation(
                        Animation::default()
                            .with_interpolation(FableMotionEasing::Enter.to_interpolation())
                            .with_duration(enter_duration),
                        move || {
                            hero_offset.try_set(0.0f32);
                        },
                    );

                    ctx.schedule_task(Instant::now() + enter_duration, move || {
                        movement_in_progress.try_set(false);
                    });
                }
            });

            on_item_focus(new_index);

            true
        })
    };

    let time_of_last_manual_rotation: RwSignal<Option<Instant>> =
        create_rw_signal(ctx.scope(), None);

    // [BEGIN] AutoRotation effects / signals
    let user_hero_interaction = create_rw_signal(ctx.scope(), None::<UserHeroInteraction>);
    {
        // This effect determines if the user navigated between the hero buttons and sends a signal accordingly to the autorotation controller.
        create_effect(
            ctx.scope(),
            move |old_value: Option<Option<ButtonVariantIndexFocusedValue>>| {
                let (cur_variant, cur_idx) = button_focused_value.get()?;
                log::debug!(
                    "[HeroSig] Hero button focus changed: variant = {:?}, index = {:?}",
                    cur_variant,
                    cur_idx
                );
                if let Some(Some((old_variant, old_idx))) = old_value {
                    if old_variant != cur_variant && old_idx == cur_idx {
                        user_hero_interaction
                            .set(Some(UserHeroInteraction::BetweenButtonsNavigation));
                    }
                }
                Some((cur_variant, cur_idx))
            },
        );

        let hero_trailer_context = use_context::<TrailerPlaybackContext>(ctx.scope());

        let on_hero_trailer_playback_finish = Signal::derive(ctx.scope(), move || {
            let Some(hero_trailer_context) = hero_trailer_context else {
                return false;
            };
            let on_playback_finished = hero_trailer_context.on_playback_finished.get();
            // If the user triggered a manual rotation, this will "interrupt" the trailer playback.
            // Super draper is not aware whether it was interrupted (1) or the trailer actually finished (2).
            // In both cases, we receive an `on_playback_finished` event, but we only want to send (2) to
            // the autorotation controller. This is determined here by checking whether the last manual rotation
            // was "close enough" to the `on_playback_finished` event that we just received.
            let close_enough_duration = hero_item_exit_duration() + Duration::from_millis(200);
            if time_of_last_manual_rotation.get_untracked().is_some_and(
                |time_of_last_manual_rotation| {
                    time_of_last_manual_rotation.elapsed() <= close_enough_duration
                },
            ) {
                return false;
            }
            on_playback_finished
        });

        let on_hero_trailer_playback_start = Signal::derive(ctx.scope(), move || {
            let Some(hero_trailer_context) = hero_trailer_context else {
                return false;
            };
            hero_trailer_context.on_trailer_playback_start.get()
        });

        let rotate_once = {
            let move_in_direction = Rc::clone(&move_in_direction);
            Rc::new(move || {
                move_in_direction(KeyCode::Right, None);
            })
        };

        let focused_nav = use_context::<FocusValueSignal<FocusedNav>>(ctx.scope());
        let top_nav_mode = use_context::<ReadSignal<TopNavMode>>(ctx.scope());
        let top_nav_focused_signal = Signal::derive(ctx.scope(), move || {
            let (Some(focused_nav), Some(top_nav_mode)) = (focused_nav, top_nav_mode) else {
                return false;
            };
            if let TopNavMode::TopNav = top_nav_mode.get() {
                focused_nav.get() == Some(FocusedNav::TopNav)
            } else {
                false
            }
        });

        let hero_items_len = item_count.get_untracked();

        let settings_manager = use_context::<ApplicationSettingsContext>(ctx.scope());
        let is_autoplay_enabled = settings_manager
            .as_ref()
            .is_some_and(|sm| sm.get_autoplay_enabled().get_untracked());

        let trailer_playback_strategy = Rc::new(move || {
            if !is_autoplay_enabled {
                None
            } else {
                items.with_untracked(|items| {
                    let item: &HeroItemModel =
                        items.get(non_delayed_focused_index.get_untracked())?;
                    get_hero_item_media_strategy(item)
                })
            }
        });

        let auto_rotation_focused_area = create_memo(
            ctx.scope(),
            move |prev_value: Option<&AutoRotationFocusArea>| {
                let top_nav_focused = top_nav_focused_signal.get();
                let hero_focused = hero_focused.get();
                if hero_focused && top_nav_focused {
                    // Edge case: When navigating from hero to top nav, both signals will be `true` for a
                    // brief instant (intended behaviour by the SDK).
                    // We return the prev_value since the new value is about to be determined
                    prev_value
                        .cloned()
                        .unwrap_or(AutoRotationFocusArea::DisabledArea)
                } else if !hero_focused && !top_nav_focused {
                    AutoRotationFocusArea::DisabledArea
                } else if hero_focused {
                    AutoRotationFocusArea::Hero
                } else {
                    // top_nav_focused == true
                    AutoRotationFocusArea::TopNav
                }
            },
        );

        let mb_ready_context = use_context::<MediaBackgroundStatusContext>(ctx.scope());

        let mb_ready = create_memo(ctx.scope(), move |_| {
            let Some(ref mb_ready_context) = mb_ready_context else {
                return false;
            };

            let Some(state) = mb_ready_context.background_state.get() else {
                return false;
            };

            items.with(|items| {
                items
                    .get(non_delayed_focused_index.get())
                    .is_some_and(|item| {
                        // We need to check if it's the same `id` as the one we are focused on, otherwise we might be inadvertently using
                        // the stale Downloaded state from a previous item.
                        let same_id =
                            item.media_background_data
                                .with_untracked(|data| match data {
                                    MediaBackgroundType::StandardHero(data) => data.id == state.id,
                                    MediaBackgroundType::Tentpole(data) => data.id == state.id,
                                    MediaBackgroundType::Linear(data) => data.id == state.id,
                                    _ => false,
                                });
                        same_id && state.background_state == BackgroundState::Downloaded
                    })
            })
        });

        let is_hero_item_fully_visible = create_memo(ctx.scope(), move |_| {
            // For now, we only check if the background is downloaded.
            // In the future, when the hero item could be still be hidden behind the skeleton,
            // we should also check if the skeleton is unveiled
            mb_ready.get()
        });

        let is_first_item_focused =
            create_memo(ctx.scope(), move |_| non_delayed_focused_index.get() == 0);

        let reduced_motion_enabled =
            settings_manager.is_some_and(|sm| sm.get_reduced_motion_enabled().get_untracked());

        create_effect(ctx.scope(), move |has_ever_ready| {
            let hero_ready = is_hero_item_fully_visible.get();
            let has_ever_ready = has_ever_ready.unwrap_or(false);

            let is_first_time_ready = !has_ever_ready && hero_ready;
            if is_first_time_ready {
                on_ready();
            }

            has_ever_ready || hero_ready
        });

        let auto_rotation_args = AutoRotationControllerArgs {
            rotate_once,
            hero_items_len,
            focused_area: auto_rotation_focused_area.into(),
            user_hero_interaction: user_hero_interaction.into(),
            current_item_media_strategy: trailer_playback_strategy,
            media_strategy_wait_time_override: auto_rotation_wait_duration_override,
            on_hero_trailer_playback_finish,
            is_hero_item_fully_visible: is_hero_item_fully_visible.into(),
            on_hero_trailer_playback_start,
            first_item_focused: is_first_item_focused.into(),
            reduced_motion_enabled,
        };

        if !is_tts_enabled.get_untracked() {
            // TODO: When the proper skeleton unveiling work is done, we should initialise the auto-rotation only after the skeleton was unveiled
            setup_auto_rotation_controller(ctx, auto_rotation_args);
        }
    }

    let on_manual_horizontal_navigation = {
        let move_in_direction = Rc::clone(&move_in_direction);
        Rc::new(move |key_code: KeyCode, index: Option<usize>| {
            if !move_in_direction(key_code, index) {
                return false;
            }
            time_of_last_manual_rotation.set(Some(Instant::now()));
            user_hero_interaction.set(Some(UserHeroInteraction::ManualRotation));
            true
        })
    };

    let on_left_right_pressed = {
        let on_manual_horizontal_navigation = Rc::clone(&on_manual_horizontal_navigation);
        let ctx = ctx.clone();
        move |key_code: KeyCode| {
            if !on_manual_horizontal_navigation(key_code.clone(), None) {
                return;
            }

            let focused_index = non_delayed_focused_index.try_get_untracked();
            if let Some(focused_index) = focused_index {
                report_hero_scroll_clickstream(ctx.scope(), focused_index, key_code);
            }
        }
    };

    let on_left_pressed = {
        let on_left_right_pressed = on_left_right_pressed.clone();
        move || on_left_right_pressed(KeyCode::Left)
    };

    let on_right_pressed = { move || on_left_right_pressed(KeyCode::Right) };

    let (task_id, set_task_id) = create_signal(ctx.scope(), None);
    let cancel_timer_builder = {
        let ctx = ctx.clone();
        move || {
            let ctx = ctx.clone();
            move || {
                if let Some(task_id) = task_id.try_get_untracked().flatten() {
                    ctx.cancel_task(task_id);
                    set_task_id.set(None);
                }
            }
        }
    };

    let cancel_timer = cancel_timer_builder();

    let on_move_to_second_container = on_move_to_second_container.clone();

    let on_button_select_impression = store_value(
        ctx.scope(),
        Rc::new(move |button_variant: ButtonVariant| {
            let Some(focused_item_index) = focused_index.try_get_untracked() else {
                return;
            };
            let Some(expanded) = expanded.try_get_untracked() else {
                return;
            };
            let dimensions = Some(get_card_dimensions(expanded));
            on_item_select(focused_item, focused_item_index, dimensions, button_variant);
        }),
    );

    let on_select_primary_button = {
        let ctx: AppContext = ctx.clone();
        Rc::new(move |transition_action: &Option<TransitionAction>| {
            let focused_index = focused_index.try_get_untracked();
            let Some(focused_index) = focused_index else {
                // return early if scope is disposed
                return;
            };

            if matches!(
                transition_action,
                Some(TransitionAction::acquisition(TitleAcquisitionAction {
                    metadata: Some(TitleActionMetadata::AcquisitionSVOD(_)),
                    ..
                }))
            ) {
                if movement_in_progress.get() {
                    log::info!("ignoring hero primary button press during autorotation");
                    return;
                }
                user_hero_interaction.set(Some(UserHeroInteraction::PrimaryButtonPress));
            }

            report_hero_action_button_pressed(
                ctx.scope(),
                transition_action.clone(),
                focused_index,
                metadata.clone().get_untracked().analytics.get_untracked(),
                1, // TODO: This was hardcoded since the beginning, need to refactor. It's most likely correct because the Hero is always the first container
            );

            metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "PrimaryButtonPress");

            if let Some(TransitionAction::landing(action)) = transition_action {
                if action.pageId == "onboarding" {
                    // temp. metric ahead of OCX dialup
                    metric!("OnboardingPage.Event", 1, "event" => "Select", "action" => "Open", "context" => "HeroStandard", "activeLayer" => "Wasm");
                }
            }

            on_button_select_impression.get_value()(ButtonVariant::Primary);

            let contextual_menu_metadata = items.with_untracked(|items| {
                if let Some(item) = items.get(focused_index) {
                    item.contextual_menu_metadata.try_get_untracked()
                } else {
                    None
                }
            });

            let secondary_button_action = secondary_button.try_get_untracked().flatten();

            let Some(transition_action) = transition_action else {
                on_move_to_second_container();
                return;
            };
            let signed_in = if let Some(auth) = use_context::<AuthContext>(ctx.scope()) {
                auth.is_last_known_state_signed_in()
            } else {
                false
            };

            /*
             * The openModal action target returns for Live Event heroes with multiple playback actions.
             * Selection launches the Watch Modal (SSM variant) instead of executing any transitions.
             */
            if let TransitionAction::openModal(action) = transition_action {
                if let Some(cm_metadata) = contextual_menu_metadata {
                    location.with_untracked(|location| {
                        prepare_contextual_menu_telemetry(ctx.clone(), location);
                        let page = match location.pageType {
                            // This should never happen, we are in rust
                            PageType::Js(_) => RustPage::RUST_EMPTY,
                            PageType::Rust(page) => page,
                        };
                        opening_menu.set(true);
                        update_cm.set(Some(create_hero_ssm_data(
                            cm_metadata,
                            action.clone(),
                            page,
                        )));
                    });
                    return;
                }
            };

            let content_type = items
                .try_with_untracked(|items| {
                    items
                        .get(focused_index)
                        .and_then(|item| item.swift_content_type.clone())
                })
                .flatten();

            let (release_state, playback_origin_override) =
                get_release_state_and_playback_origin_from_hero_action(
                    ctx.scope(),
                    transition_action,
                );

            let transition_location = get_location_for_transition_action(
                transition_action,
                &secondary_button_action,
                signed_in,
                gti.get_untracked(),
                component_name,
                ctx.scope(),
                content_type,
                playback_origin_override,
            );

            if let Some(location) = transition_location {
                if let PageType::Js(_) = location.pageType {
                    if let Some(hybrid_playback_context) =
                        use_context::<HybridPlaybackContext>(ctx.scope())
                    {
                        hybrid_playback_context
                            .should_release
                            .set(Some(release_state));
                    }
                }

                cancel_timer();
                // schedule delay for navigation to allow for unloading player
                let new_task_id = ctx.schedule_task(Instant::now() + Duration::from_millis(300), {
                    let router = use_router(ctx.scope());
                    let scope = ctx.scope();
                    // coming back from playback should go to details page first so we create a
                    // details page history stack entry.
                    let transition_to_mutate_history =
                        if let (PageType::Js(JSPage::START_PLAYBACK), Some(action)) =
                            (&location.pageType, &secondary_button_action)
                        {
                            if matches!(
                                action,
                                TransitionAction::detail(_) | TransitionAction::legacyDetail(_)
                            ) {
                                Some(Transition::from_action_with_scope(action, scope))
                            } else {
                                None
                            }
                        } else {
                            None
                        };
                    move || {
                        if let Some(transition) = transition_to_mutate_history {
                            router.mutate_history(Box::new(move |history| {
                                if let Some(location) = transition.to_location() {
                                    history.push(location);
                                }
                            }));
                        }

                        router.navigate(location.clone(), "HERO");
                    }
                });

                set_task_id.set(Some(new_task_id));
            }
        })
    };

    // The behaviour when one of the back buttons ("Escape", "Back") was pressed.
    let on_back_pressed = {
        let on_manual_horizontal_navigation = Rc::clone(&on_manual_horizontal_navigation);
        let cancel_timer = cancel_timer_builder();
        Rc::new(move || {
            cancel_timer();

            let focused_index = focused_index.try_get_untracked();
            if let Some(focused_index) = focused_index {
                if focused_index > 0 {
                    on_manual_horizontal_navigation(KeyCode::Left, Some(0));
                } else {
                    on_back_pressed()
                }
            }
        })
    };

    let on_backspace_pressed = {
        let on_back_pressed = Rc::clone(&on_back_pressed);
        move || on_back_pressed()
    };

    let on_escape_pressed = {
        let on_back_pressed = Rc::clone(&on_back_pressed);
        move || on_back_pressed()
    };

    let hero_trailer_opacity = create_rw_signal(ctx.scope(), 1.0);

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let is_hero_trailer_playing = derive_is_hero_trailer_playing_signal(ctx.scope());
        move |_| {
            let new_opacity = if is_hero_trailer_playing.get() {
                0.7
            } else {
                1.0
            };
            ctx.with_animation(
                Animation::default()
                    .with_interpolation(FableMotionEasing::Linear.to_interpolation())
                    .with_duration(FableMotionDuration::Medium.to_duration()),
                move || {
                    hero_trailer_opacity.set(new_opacity);
                },
            );
        }
    });

    on_cleanup(ctx.scope(), cancel_timer_builder());

    let pointer_control_active = is_pointer_control_active(ctx.scope);
    let pointer_controls_visible = Signal::derive(ctx.scope, move || {
        pointer_control_active.get() && hero_focused.get()
    });
    let pointer_control_on_left = Rc::new(on_left_pressed.clone());
    let pointer_control_on_right = Rc::new(on_right_pressed.clone());

    let mount_time = Instant::now();

    let on_view = Rc::new(move |data: ViewImpressionData| {
        // Not sure this is the best solution long term.
        // Implementing for now to addresss sev 2: https://t.corp.amazon.com/D149065893/
        let time_to_view = Instant::now().checked_duration_since(mount_time);

        if let Some(time_to_view) = time_to_view {
            let Some(focused_item_index) = focused_index.try_get_untracked() else {
                return;
            };
            let Some(expanded) = expanded.try_get_untracked() else {
                return;
            };

            let data = ViewImpressionData {
                time_to_view,
                ..data
            };
            let dimensions = get_card_dimensions(expanded);

            on_item_view(data, focused_item, focused_item_index, dimensions);
        }
    });

    let on_highlight = Rc::new({
        let on_item_highlight = on_item_highlight.clone();
        move || {
            let Some(focused_item_index) = focused_index.try_get_untracked() else {
                return;
            };
            let Some(expanded) = expanded.try_get_untracked() else {
                return;
            };

            let dimensions = get_card_dimensions(expanded);
            on_item_highlight(focused_item, focused_item_index, dimensions);
        }
    });

    let (on_highlight_signal, _) = create_signal(ctx.scope(), Rc::clone(&on_highlight));

    let primary_button_builder = Box::new({
        move |ctx: &AppContext| {
            let focused_index = focused_index.get();
            let on_select = {
                let on_select_primary_button = Rc::clone(&on_select_primary_button);
                move || {
                    let primary_button = primary_button.try_get_untracked();
                    if let Some(primary_button) = primary_button {
                        on_select_primary_button(&primary_button);
                    }
                }
            };
            let open_contextual_menu_primary_button_wrapped = {
                let on_long_press_primary_button = Rc::clone(&on_long_press_primary_button);
                move || {
                    let primary_button = primary_button.try_get_untracked();
                    if let Some(transition_action) = primary_button {
                        if transition_action.is_some() {
                            on_long_press_primary_button();
                        }
                    }
                }
            };

            let on_long_press = open_contextual_menu_primary_button_wrapped.clone();
            let firetv_context = use_firetv_context(ctx);
            let on_menu_key_down = move || {
                if firetv_context.is_firetv() {
                    open_contextual_menu_primary_button_wrapped();
                }
            };

            let on_view = Rc::clone(&on_view);
            let on_highlight = Rc::clone(&on_highlight);

            compose_option! {
                Row() {
                    PrimaryButton(variant: primary_button_variant, animation: Some(fable_motion_linear_medium()))
                    .min_width(FableSize::SIZE1000)
                    .max_width(FableSize::SIZE2400)
                    .focusable()
                    .focused_value(button_focused_value, (ButtonVariant::Primary, focused_index))
                    .preferred_focus(is_primary_button_preferred)
                    .on_select(on_select)
                    .on_long_press_start(KeyCode::Enter, on_long_press.clone())
                    .on_long_press_start(KeyCode::Select, on_long_press)
                    .on_key_down(KeyCode::Menu, on_menu_key_down)
                    .test_id("primary-button")
                    .accessibility_metadata_descriptions(metadata_descriptions)
                    .accessibility_closing_context_message(LocalizedText::new("AV_LRC_STANDARD_HERO_REMASTER_TTS_SECONDARY"))
                    .on_view_impression(IMPRESSION_DURATION, IMPRESSION_STRIPE, move |data| on_view(data))
                    .on_highlight_impression(IMPRESSION_DURATION, move || on_highlight())

                }
                .test_id("primary-button-row")
                .cross_axis_alignment(CrossAxisAlignment::End)
            }
        }
    });

    let secondary_button_builder = Box::new(move |ctx: &AppContext| {
        let variant = secondary_button_variant.get()?;
        let focused_index = focused_index.get();

        let select_button = {
            let ctx: AppContext = ctx.clone();
            move |transition_action: &Option<TransitionAction>| {
                report_hero_action_button_pressed(
                    ctx.scope(),
                    transition_action.clone(),
                    focused_index,
                    metadata.clone().get_untracked().analytics.get_untracked(),
                    1, // TODO: This was hardcoded since the beginning, need to refactor. It's most likely correct because the Hero is always the first container
                );

                metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "SecondaryButtonPress");

                on_button_select_impression.get_value()(ButtonVariant::Secondary);

                if let Some(transition_action) = transition_action {
                    metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "SwiftButtonPressed");
                    let transition =
                        Transition::from_action_with_scope(transition_action, ctx.scope());

                    if let Some(location) = transition.to_location() {
                        let is_details_js = location.pageType == PageType::Js(OPEN_DETAIL_PAGE);
                        let is_rust_details = location.pageType == PageType::Rust(RUST_DETAILS);
                        if is_details_js || is_rust_details {
                            let nav_control = use_context::<NavControl>(ctx.scope());
                            if let Some(nav_control) = nav_control {
                                nav_control.show_top_nav_intent.set(false);
                            }
                        }

                        if let PageType::Js(_) = location.pageType {
                            if let Some(hybrid_playback_context) =
                                use_context::<HybridPlaybackContext>(ctx.scope())
                            {
                                hybrid_playback_context
                                    .should_release
                                    .set(Some(ReleaseState::NotSeamless));
                            }
                        }
                        if let Some(task_id) = task_id.try_get_untracked().flatten() {
                            ctx.cancel_task(task_id);
                            set_task_id.set(None);
                        }
                        let navigate = use_navigation(ctx.scope());
                        // schedule delay for navigation to allow for unloading player
                        let new_task_id = ctx.schedule_task(
                            Instant::now() + Duration::from_millis(300),
                            move || {
                                navigate(location.clone(), "HERO");
                            },
                        );
                        set_task_id.set(Some(new_task_id));
                    }
                }
            }
        };
        let on_select_secondary_wrapped = move || {
            let secondary_button = secondary_button.try_get_untracked();
            if let Some(secondary_button) = secondary_button {
                select_button(&secondary_button);
            }
        };

        let open_contextual_menu_secondary_button_wrapped = move || {
            let on_long_press_secondary_button = on_long_press_secondary_button.try_get_untracked();
            if let Some(on_long_press_secondary_button) = on_long_press_secondary_button {
                on_long_press_secondary_button();
            }
        };

        let on_long_press = open_contextual_menu_secondary_button_wrapped;
        let firetv_context = use_firetv_context(ctx);
        let on_menu_key_down = move || {
            if firetv_context.is_firetv() {
                open_contextual_menu_secondary_button_wrapped();
            }
        };

        let on_highlight_wrapped = move || {
            let on_highlight = on_highlight_signal.try_get_untracked();
            if let Some(on_highlight) = on_highlight {
                on_highlight();
            }
        };

        compose_option! {
            Row() {
                SecondaryButton(variant: variant, animation: Some(fable_motion_linear_medium()))
                    .min_width(FableSize::SIZE1000)
                    .max_width(FableSize::SIZE2400)
                    .focusable()
                    .focused_value(button_focused_value, (ButtonVariant::Secondary, focused_index))
                    .preferred_focus(is_secondary_button_preferred)
                    .test_id("secondary-button")
                    .on_highlight_impression(IMPRESSION_DURATION, on_highlight_wrapped)
            }
            .test_id("secondary-button-row")
            .on_select(on_select_secondary_wrapped)
            .on_long_press_start(KeyCode::Enter, on_long_press)
            .on_long_press_start(KeyCode::Select, on_long_press)
            .on_key_down(KeyCode::Menu, on_menu_key_down)
            .opacity(collapsible_opacity)
            .padding(secondary_padding)
        }
    });

    let synopsis_builder = Box::new(move |ctx: &AppContext| {
        synopsis.with(|content| {
            let Some(content) = content else {
                return None;
            };
            let content = content.to_owned();
            compose_option! {
                Row() {
                    Typography(content, type_ramp: <FableText as Into<TypeRamp>>::into(FableText::TYPE_BODY200))
                        .max_lines(synopsis_max_lines)
                }
                .test_id("synopsis-row")
                .padding(Padding::new(0.0, 0.0, 0.0, 16.0))
                .opacity(collapsible_opacity)
                .width(LEFT_SECTION_WIDTH)
                .max_height(collapsible_max_height)
            }
        })
    });

    let progress_bar_builder = Box::new(move |ctx: &AppContext| {
        // Don't show the progress bar if the Primary button is above the More Details button
        if buttons_flipped {
            return None;
        }
        let progress = progress_bar_percentage.get();
        if progress.is_some() {
            compose_option! {
                ProgressBar(progress: progress.unwrap_or(0.0), variant: ProgressBarVariant::VOD, rounded: true)
            }
        } else {
            None
        }
    });

    let (top_button, bottom_button): (Box<ButtonBuilder>, Box<ButtonBuilder>) = if buttons_flipped {
        (primary_button_builder, secondary_button_builder)
    } else {
        (secondary_button_builder, primary_button_builder)
    };

    compose! {
        Row() {
            Column() {
                Row() {
                    Title(data: title_data)
                }
                .test_id("title-row")
                .padding(Padding::new(0.0,0.0,0.0,24.0))

                if show_metadata_row.get() {
                    MetadataRow(items: metadata_row)
                        .padding(Padding::new(0.0,0.0,0.0,16.0))
                        .test_id("metadata-row")
                }

                Row() {
                    Memo(item_builder: synopsis_builder)
                }

                Row() {
                    Memo(item_builder: top_button)
                }
                .padding(Padding::new(0.0, 0.0, 8.0, 4.0))
                .test_id("top-button-row")

                Column() {
                    Memo(item_builder: bottom_button)
                    Memo(item_builder: progress_bar_builder)
                }
                .cross_axis_stretch(true)
                .cross_axis_alignment(CrossAxisAlignment::Center)
                .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING050))
                .padding(Padding::new(0.0, 0.0, 8.0, 20.0))
                .test_id("bottom-button-column")


                if show_collapsibles.get() {
                    Memo(item_builder: Box::new(move |ctx| {
                        let label = entitlement_label.get()?;

                        compose_option! {
                            Row() {
                                EntitlementLabel(data: label)
                            }
                            .test_id("entitlement-row")
                            .opacity(collapsible_opacity)
                        }
                    }))
                }
            }
            .width(LEFT_SECTION_WIDTH)
            .opacity(hero_item_opacity)
            .translate_x(hero_offset)
            .test_id("hero-left-column")
            // TODO: fix TTS enumerables for carousels https://issues.amazon.com/issues/LRNP-4115
            // Speaking 1 of N is a temporary solution agreed with @mbibik
            .accessibility_enumerable_index(1)

            Column() {
                Memo(item_builder: Box::new(move |ctx| {
                    let pointer_control_on_left = pointer_control_on_left.clone();
                    let pointer_control_on_right = pointer_control_on_right.clone();
                    if item_count.get() > 1 {
                        compose_option! {
                            PaginationDots(
                                num_items: item_count.get(),
                                focused_index,
                                pointer_controls_visible,
                                pointer_control_on_left,
                                pointer_control_on_right,
                            ).test_id("pagination-dots")
                        }
                    } else {
                        None
                    }
                }))
            }
            .width(MID_SECTION_WIDTH)
            .main_axis_alignment(MainAxisAlignment::End)
            .padding(Padding::new(27.0, 0.0, 0.0, 0.0))
            .test_id("hero-mid-column")

            Column() {
                Row() {
                    MaturityRating(image: maturity_rating_image, text: maturity_rating_string)
                    RegulatoryLabel(text: regulatory_label_string)
                }
                LegalMessages(messages: legal_messages)
                    .test_id(LEGAL_TEXT_SECTION_TEST_ID)
            }
            .width(RIGHT_SECTION_WIDTH)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(8.0))
            .cross_axis_alignment(CrossAxisAlignment::End)
            .opacity(hero_item_opacity)
            .test_id("hero-right-column")
        }
        .opacity(hero_trailer_opacity)
        .padding(padding)
        .cross_axis_alignment(CrossAxisAlignment::End)
        .height(height)
        .width(TOTAL_WIDTH)
        .focus_scope()
        .focused(hero_focused)
        .on_focus(on_focus)
        .on_key_down(KeyCode::Left, on_left_pressed)
        .on_key_down(KeyCode::Right, on_right_pressed)
        .on_key_down(KeyCode::Backspace, on_backspace_pressed)
        .on_key_down(KeyCode::Escape, on_escape_pressed)
        .test_id(HERO_CARD_TEST_ID)
        .accessibility_enumerable_count(item_count)
    }
}

#[cfg(test)]
pub mod test {
    use std::collections::HashMap;

    use super::*;
    use crate::utils::test::*;
    use auth::MockAuth;
    use container_types::ui_signals::CommonCarouselMetadata;
    use fableous::typography::typography::TYPOGRAPHY_TEST_ID;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::composable::Composable;
    use ignx_compositron::compose;
    use ignx_compositron::context::AppContext;
    use ignx_compositron::input::KeyEventType;
    use ignx_compositron::prelude::{provide_context, use_context, Scope};
    use ignx_compositron::reactive::{store_value, SignalSet};
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::test_utils::{
        assert_node_does_not_exist, assert_node_exists, TestRendererGameLoop,
    };
    use location::PageType::Js;
    use location::RustPage::RUST_COLLECTIONS;
    use location::{JSPage, NavigationAction, NavigationDirection, PageType};
    use media_background::types::context::MediaBackgroundStatus;
    use media_background::types::{RotationDirection, StandardHeroBackgroundData};
    use mockall::predicate::eq;
    use router::{MockRouting, RoutingContext};
    use rstest::*;
    use rust_features::{
        provide_context_test_rust_features, MockRustFeatures, MockRustFeaturesBuilder,
    };
    use serde_json::{Map, Value};
    use serial_test::serial;
    use transition_executor::RPCManagerWrapper;

    // This is semantically different from AlwaysCollapsed, AlwaysExpanded because the collapsibles
    // are only conditionally rendered in SizeConfig::Expandable. Handy for testing
    fn expandable_but_always(scope: Scope, but_always_set_to: bool) -> SizeConfig {
        SizeConfig::Expandable(Signal::derive(scope, move || but_always_set_to))
    }

    fn create_metadata(scope: Scope) -> RwSignal<CommonCarouselMetadata> {
        let analytics = HashMap::from([("str".to_string(), "str".to_string())]);

        create_rw_signal(
            scope,
            CommonCarouselMetadata {
                id: "id".to_string(),
                journey_ingress_context: create_rw_signal(scope, Some("ingress".to_string())),
                analytics: create_rw_signal(scope, analytics),
            },
        )
    }

    pub fn setup_mock_routing_with_past_location(ctx: &AppContext) {
        let mut mock_router = MockRouting::default();
        mock_router
            .expect_get_last_navigation_action()
            .returning(|| NavigationAction {
                direction: NavigationDirection::Forward,
                from: PageType::Rust(RUST_COLLECTIONS),
                to: PageType::Rust(RUST_COLLECTIONS),
            });
        mock_router
            .expect_location()
            .return_const(create_rw_signal(ctx.scope(), Location::default()));
        provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));
    }

    struct CreateTestHeroProps {
        items: Vec<HeroItemModel>,
        size_config: SizeConfig,
        default_focus_index: Option<MaybeSignal<usize>>,

        // Optional call backs
        on_focus: Option<Rc<dyn Fn()>>,
        open_contextual_menu: Option<Rc<dyn Fn(usize, ButtonVariant)>>,
        on_item_focus: Option<Rc<dyn Fn(usize)>>,
        on_back_pressed: Option<Rc<dyn Fn()>>,
        on_item_view:
            Option<Rc<dyn Fn(ViewImpressionData, RwSignal<HeroItemModel>, usize, CardDimensions)>>,
        on_item_highlight: Option<Rc<dyn Fn(RwSignal<HeroItemModel>, usize, CardDimensions)>>,
        on_item_select: Option<
            Rc<dyn Fn(RwSignal<HeroItemModel>, usize, Option<CardDimensions>, ButtonVariant)>,
        >,
        on_move_to_second_container: Option<Rc<dyn Fn()>>,
        update_cm: Option<WriteSignal<Option<ContextualMenuData>>>,
        features: Option<MockRustFeatures>,
        hero_initial_focus: bool,
        setup_routing: bool,
        // Menu button exists on fire tv remote and opens/closes the contextual menu
        has_menu_button: bool,
    }

    impl Default for CreateTestHeroProps {
        fn default() -> Self {
            Self {
                items: vec![],
                size_config: SizeConfig::AlwaysExpanded,
                default_focus_index: None,
                on_focus: None,
                open_contextual_menu: None,
                on_item_focus: None,
                on_back_pressed: None,
                on_item_view: None,
                on_item_highlight: None,
                on_item_select: None,
                on_move_to_second_container: None,
                update_cm: None,
                features: None,
                hero_initial_focus: true,
                setup_routing: true,
                has_menu_button: false,
            }
        }
    }

    fn create_test_hero(ctx: &AppContext, props: CreateTestHeroProps) -> impl Composable<'static> {
        let scope = ctx.scope();

        let size_config = props.size_config;
        let items = create_rw_signal(scope, props.items);
        let metadata = create_metadata(scope);
        let default_focus_index = props.default_focus_index.unwrap_or_else(|| 0.into());

        let on_focus = props.on_focus.unwrap_or(Rc::new(|| {}));
        let on_item_focus = props.on_item_focus.unwrap_or(Rc::new(|_| {}));
        let open_item_contextual_menu = props.open_contextual_menu.unwrap_or(Rc::new(|_, _| {}));
        let on_back_pressed = props.on_back_pressed.unwrap_or(Rc::new(|| {}));
        let on_item_view = props
            .on_item_view
            .unwrap_or_else(|| Rc::new(|_, _, _, _| {}));
        let on_item_highlight = props
            .on_item_highlight
            .unwrap_or_else(|| Rc::new(|_, _, _| {}));
        let on_item_select = props
            .on_item_select
            .unwrap_or_else(|| Rc::new(|_, _, _, _| {}));
        let on_move_to_second_container = props
            .on_move_to_second_container
            .unwrap_or_else(|| Rc::new(|| {}));
        let update_cm = props
            .update_cm
            .unwrap_or_else(|| create_rw_signal(scope, None).write_only());

        provide_context(
            scope,
            props
                .features
                .unwrap_or(MockRustFeaturesBuilder::new().build()),
        );
        provide_context_test_rust_features(scope);
        provide_context(scope, size_config);

        if use_context::<TTSEnabledContext>(ctx.scope()).is_none() {
            provide_context(
                ctx.scope(),
                TTSEnabledContext(create_signal(ctx.scope(), false).0),
            );
        }

        if props.setup_routing {
            // Required for button selections
            setup_mock_routing_with_past_location(ctx);
        }

        provide_firetv_context(&ctx, props.has_menu_button);
        compose! {
            Column() {
                HeroSig(items,
                        size_config,
                        on_focus,
                        on_item_focus,
                        open_item_contextual_menu,
                        on_back_pressed,
                        default_focus_index,
                        metadata,
                        on_move_to_second_container,
                        is_tentpole: false,
                        update_cm,
                        on_item_view,
                        on_item_highlight,
                        on_item_select,
                )
                .preferred_focus(props.hero_initial_focus)
                Button(text: "Other content button".to_string())
                    .test_id("other_button")
                    .preferred_focus(!props.hero_initial_focus)
            }
        }
    }

    fn link_items(scope: Scope) -> Vec<HeroItemModel> {
        vec![
            generate_mock_link_card(scope, 0),
            generate_mock_link_card(scope, 1),
            generate_mock_link_card(scope, 2),
            generate_mock_link_card(scope, 3),
        ]
    }

    fn channel_item(scope: Scope) -> Vec<HeroItemModel> {
        vec![generate_mock_channel_card(scope, 0)]
    }

    fn hero_items(scope: Scope) -> Vec<HeroItemModel> {
        vec![
            generate_mock_hero_card(scope, 0, true),
            generate_mock_hero_card(scope, 1, true),
            generate_mock_hero_card(scope, 2, true),
            generate_mock_hero_card(scope, 3, true),
            generate_mock_hero_card(scope, 4, true),
            generate_mock_hero_card(scope, 5, true),
            generate_mock_hero_card(scope, 6, true),
            generate_mock_hero_card(scope, 7, true),
        ]
    }

    fn hero_items_short_button_labels(scope: Scope) -> Vec<HeroItemModel> {
        vec![generate_mock_hero_card_with_actions(
            scope,
            0,
            Some(generate_playback_action()),
            Some(generate_details_action()),
            false,
            Some(String::from("L")),
            Some(String::from("I")),
        )]
    }

    fn hero_items_long_button_labels(scope: Scope) -> Vec<HeroItemModel> {
        vec![generate_mock_hero_card_with_actions(
            scope,
            0,
            Some(generate_playback_action()),
            Some(generate_details_action()),
            false,
            Some(String::from("Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem Lorem")),
            Some(String::from("Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum Ipsum")),
        )]
    }

    fn remove_metadata_row(items: &Vec<HeroItemModel>) {
        for hero_item in items {
            hero_item.ui_data.with_untracked(|data| {
                data.metadata_rows.set(vec![]);
            })
        }
    }

    fn remove_progress_bar_percentage(items: &Vec<HeroItemModel>) {
        for hero_item in items {
            hero_item.ui_data.with_untracked(|data| {
                data.progress_bar_percentage.set(None);
            })
        }
    }

    fn remove_maturity_rating_image(items: &Vec<HeroItemModel>) {
        for hero_item in items {
            hero_item.ui_data.with_untracked(|data| {
                data.maturity_rating_image.set(None);
            })
        }
    }

    fn remove_maturity_rating_string(items: &Vec<HeroItemModel>) {
        for hero_item in items {
            hero_item.ui_data.with_untracked(|data| {
                data.maturity_rating_string.set(None);
            })
        }
    }

    fn remove_regulatory_label(items: &Vec<HeroItemModel>) {
        for hero_item in items {
            hero_item.ui_data.with_untracked(|data| {
                data.regulatory_label_string.set(None);
            })
        }
    }

    fn remove_entitlement_label(items: &Vec<HeroItemModel>) {
        for hero_item in items {
            hero_item.ui_data.with_untracked(|data| {
                data.entitlement_label.set(None);
            })
        }
    }

    fn hero_items_remove_metadata(scope: Scope) -> Vec<HeroItemModel> {
        let items = hero_items(scope);
        remove_metadata_row(&items);
        items
    }

    fn hero_items_remove_progress_bar_percentages(scope: Scope) -> Vec<HeroItemModel> {
        let items = hero_items(scope);
        remove_progress_bar_percentage(&items);
        items
    }

    fn hero_items_remove_maturity_image(scope: Scope) -> Vec<HeroItemModel> {
        let items = hero_items(scope);
        remove_maturity_rating_image(&items);
        items
    }

    fn hero_items_remove_maturity_string(scope: Scope) -> Vec<HeroItemModel> {
        let items = hero_items(scope);
        remove_maturity_rating_string(&items);
        items
    }

    fn hero_items_remove_regulatory_label(scope: Scope) -> Vec<HeroItemModel> {
        let items = hero_items(scope);
        remove_regulatory_label(&items);
        items
    }

    fn hero_items_remove_maturity(scope: Scope) -> Vec<HeroItemModel> {
        let items = hero_items(scope);
        remove_maturity_rating_string(&items);
        remove_maturity_rating_image(&items);
        items
    }

    fn hero_items_remove_entitlement_label(scope: Scope) -> Vec<HeroItemModel> {
        let items = hero_items(scope);
        remove_entitlement_label(&items);
        items
    }

    fn link_items_remove_metadata(scope: Scope) -> Vec<HeroItemModel> {
        let items = link_items(scope);
        remove_metadata_row(&items);
        items
    }

    fn link_items_remove_entitlement(scope: Scope) -> Vec<HeroItemModel> {
        let items = link_items(scope);
        remove_entitlement_label(&items);
        items
    }

    fn first_hero_item_only(scope: Scope) -> Vec<HeroItemModel> {
        vec![generate_mock_hero_card(scope, 0, true)]
    }

    fn first_link_item_only(scope: Scope) -> Vec<HeroItemModel> {
        vec![generate_mock_link_card(scope, 0)]
    }

    fn expandable_but_always_expanded(scope: Scope) -> SizeConfig {
        expandable_but_always(scope, true)
    }

    fn expandable_but_always_collapsed(scope: Scope) -> SizeConfig {
        expandable_but_always(scope, false)
    }

    fn provide_firetv_context(ctx: &AppContext, is_firetv: bool) {
        let mut firetv = MockFireTV::default();
        firetv.expect_is_firetv().return_const(is_firetv);
        firetv.provide_mock(ctx.scope());
    }

    #[rstest]
    #[case(hero_items, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, true, true, true, true, true, true)]
    #[case(hero_items_remove_metadata, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), false, true, true, true, true, true, true)]
    #[case(hero_items, expandable_but_always_collapsed, "Start your free trial".to_string(), Some("More details".to_string()), true, true, true, false, true, true, true)]
    #[case(hero_items_remove_metadata, expandable_but_always_collapsed, "Start your free trial".to_string(), Some("More details".to_string()), false, true, true, false, true, true, true)]
    #[case(hero_items_remove_maturity_image, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, false, true, true, true, true, true)]
    #[case(hero_items_remove_maturity_string, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, true, true, true, true, true, true)]
    #[case(hero_items_remove_maturity, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, false, true, true, true, true, true)]
    #[case(hero_items_remove_regulatory_label, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, true, false, true, true, true, true)]
    #[case(hero_items_remove_entitlement_label, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, true, true, false, true, true, true)]
    #[case(hero_items_remove_progress_bar_percentages, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, true, true, true, true, true, false)]
    #[case(link_items, expandable_but_always_expanded, "More details".to_string(), None, true, false, false, true, false, true, false)]
    #[case(link_items_remove_metadata, expandable_but_always_expanded, "More details".to_string(), None, false, false,  false, true, false, true, false)]
    #[case(link_items, expandable_but_always_collapsed, "More details".to_string(), None, true, false, false, false, false, true, false)]
    #[case(link_items_remove_metadata, expandable_but_always_collapsed, "More details".to_string(), None, false, false, false, false, false, true, false)]
    #[case(link_items_remove_entitlement, expandable_but_always_expanded, "More details".to_string(), None, true, false, false, false, false, true, false)]
    #[case(first_hero_item_only, expandable_but_always_expanded, "Start your free trial".to_string(), Some("More details".to_string()), true, true, true, true, true, false, true)]
    #[case(first_link_item_only, expandable_but_always_expanded, "More details".to_string(), None, true, false, false, true, false, false, false)]
    #[case::always_collapsed_should_see_collapsable_section_with_synopsis_and_eoc(channel_item, |_| SizeConfig::AlwaysCollapsed, "More details".to_string(), None, false, false, false, true, true, false, false)]
    fn renders_card(
        #[case] items: impl Fn(Scope) -> Vec<HeroItemModel> + 'static,
        #[case] size_config: impl Fn(Scope) -> SizeConfig + 'static,
        #[case] primary_button_label: String,
        #[case] secondary_button_label: Option<String>,
        #[case] has_metadata_row: bool,
        #[case] has_maturity_rating: bool,
        #[case] has_regulatory_label: bool,
        #[case] has_entitlement_row: bool,
        #[case] has_synopsis: bool,
        #[case] has_pagination_dots: bool,
        #[case] has_progress_bar_percentage: bool,
    ) {
        launch_test(
            move |ctx| {
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: items(ctx.scope()),
                        size_config: size_config(ctx.scope()),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();

                let parent = tree.find_by_test_id(HERO_CARD_TEST_ID);

                let size_config = expect_context::<SizeConfig>(scope);
                let expanded = match size_config {
                    SizeConfig::AlwaysCollapsed => false,
                    SizeConfig::AlwaysExpanded => true,
                    SizeConfig::Expandable(s) => s.get_untracked(),
                };

                assert_node_exists!(&parent);
                assert_eq!(
                    parent.borrow_props().layout.size.height,
                    if expanded { 876.0 } else { 626.0 }
                );

                let left_column = parent
                    .find_child_with()
                    .test_id("hero-left-column")
                    .find_first();
                assert_node_exists!(&left_column);

                let title_row = left_column
                    .find_child_with()
                    .test_id("title-row")
                    .find_first();

                assert_node_exists!(&title_row);

                let title = title_row.find_child_with().find_first();

                assert_node_exists!(title);

                let metadata_row = left_column
                    .find_child_with()
                    .test_id("metadata-row")
                    .find_first();
                if has_metadata_row {
                    assert_node_exists!(&metadata_row);
                } else {
                    assert_node_does_not_exist!(&metadata_row);
                }

                let should_see_collapsibles = match size_config {
                    SizeConfig::AlwaysCollapsed | SizeConfig::AlwaysExpanded => true,
                    SizeConfig::Expandable(s) => s.get_untracked(),
                };

                let top_button_row = left_column
                    .find_child_with()
                    .test_id("top-button-row")
                    .find_first();

                assert_node_exists!(&top_button_row);

                let secondary_button_row = top_button_row
                    .find_child_with()
                    .test_id("secondary-button-row")
                    .find_first();

                if secondary_button_label.is_some() {
                    assert_node_exists!(&secondary_button_row);

                    if should_see_collapsibles {
                        assert_eq!(
                            secondary_button_row.borrow_props().base_styles.opacity,
                            Some(1.0f32)
                        );
                    } else {
                        assert_eq!(
                            secondary_button_row.borrow_props().base_styles.opacity,
                            Some(0.0f32)
                        );
                    }

                    let secondary_button = secondary_button_row
                        .find_child_with()
                        .test_id("secondary-button")
                        .find_first();

                    assert_node_exists!(&secondary_button);

                    let secondary_button_typography = secondary_button
                        .find_any_child_with()
                        .test_id(TYPOGRAPHY_TEST_ID)
                        .find_first();

                    assert_node_exists!(&secondary_button_typography);
                    assert_eq!(
                        secondary_button_typography.borrow_props().text,
                        secondary_button_label
                    );
                } else {
                    assert_node_does_not_exist!(&secondary_button_row);
                }

                let synopsis_row = left_column
                    .find_any_child_with()
                    .test_id("synopsis-row")
                    .find_first();

                if has_synopsis {
                    assert_node_exists!(&synopsis_row);

                    if should_see_collapsibles {
                        assert_eq!(
                            synopsis_row.borrow_props().base_styles.opacity,
                            Some(1.0f32)
                        );
                    } else {
                        assert_eq!(
                            synopsis_row.borrow_props().base_styles.opacity,
                            Some(0.0f32)
                        );
                    }

                    let synopsis = synopsis_row.find_child_with().find_first();

                    assert_node_exists!(synopsis);
                } else {
                    assert_node_does_not_exist!(&synopsis_row);
                }

                let bottom_button_column = left_column
                    .find_child_with()
                    .test_id("bottom-button-column")
                    .find_first();

                assert_node_exists!(&bottom_button_column);

                let primary_button_row = bottom_button_column
                    .find_child_with()
                    .test_id("primary-button-row")
                    .find_first();

                assert_node_exists!(&primary_button_row);

                let primary_button = primary_button_row
                    .find_any_child_with()
                    .test_id("primary-button")
                    .find_first();

                assert_node_exists!(&primary_button);

                let progress_bar_percentage = bottom_button_column
                    .find_child_with()
                    .test_id(PROGRESS_BAR_TEST_ID)
                    .find_first();

                if has_progress_bar_percentage {
                    assert_node_exists!(&progress_bar_percentage);
                } else {
                    assert_node_does_not_exist!(&progress_bar_percentage);
                }

                let entitlement_row = left_column
                    .find_child_with()
                    .test_id("entitlement-row")
                    .find_first();

                if has_entitlement_row {
                    assert_node_exists!(&entitlement_row);
                } else {
                    assert_node_does_not_exist!(&entitlement_row);
                }

                let mid_column = parent
                    .find_child_with()
                    .test_id("hero-mid-column")
                    .find_first();

                assert_node_exists!(&mid_column);

                let pagination_dots = mid_column
                    .find_child_with()
                    .test_id("pagination-dots")
                    .find_first();

                if has_pagination_dots {
                    assert_node_exists!(&pagination_dots);
                } else {
                    assert_node_does_not_exist!(&pagination_dots);
                }

                let primary_button_typography = primary_button
                    .find_any_child_with()
                    .test_id(TYPOGRAPHY_TEST_ID)
                    .find_first();

                assert_node_exists!(&primary_button_typography);
                assert_eq!(
                    primary_button_typography.borrow_props().text,
                    Some(primary_button_label)
                );

                let right_column = parent
                    .find_child_with()
                    .test_id("hero-right-column")
                    .find_first();

                assert_node_exists!(&right_column);

                if has_maturity_rating {
                    let maturity_rating = right_column
                        .find_any_child_with()
                        .test_id("maturity-rating")
                        .find_first();
                    assert_node_exists!(&maturity_rating);
                }

                if has_regulatory_label {
                    let regulatory_label = right_column
                        .find_any_child_with()
                        .test_id("regulatory-label")
                        .find_first();
                    assert_node_exists!(&regulatory_label);
                }

                let legal_messages = right_column
                    .find_any_child_with()
                    .test_id(LEGAL_TEXT_SECTION_TEST_ID)
                    .find_first();
                assert_node_exists!(&legal_messages);
            },
        )
    }

    mod legal_messaging_rendering {
        use super::*;

        fn create_hero_item(scope: Scope, legal_messages: &[String]) -> Vec<HeroItemModel> {
            let items = generate_mock_data(scope, false);
            items[0].ui_data.update_untracked(|data| {
                data.legal_messages.update_untracked(|vec| {
                    vec.clear();
                    vec.extend(legal_messages.iter().cloned());
                });
            });
            items
        }
    }

    #[rstest]
    #[case(hero_items_short_button_labels, FableSize::SIZE1000)]
    #[case(hero_items_long_button_labels, FableSize::SIZE2400)]
    #[ignore]
    fn renders_buttons_with_correct_min_and_max_width(
        #[case] items: impl Fn(Scope) -> Vec<HeroItemModel> + 'static,
        #[case] expected_width: f32,
    ) {
        launch_test(
            move |ctx| {
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: items(ctx.scope()),
                        size_config: expandable_but_always_expanded(ctx.scope()),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            move |_scope, mut test_game_loop| {
                let _ = test_game_loop.tick_until_done();
                MockClock::advance(Duration::from_millis(200));
                let tree = test_game_loop.tick_until_done();

                let parent = tree.find_by_test_id(HERO_CARD_TEST_ID);
                let collapsable_row = tree.find_by_test_id("collapsable-row");

                let left_column = parent
                    .find_child_with()
                    .test_id("hero-left-column")
                    .find_first();

                let collapsable_column = collapsable_row
                    .find_child_with()
                    .test_id("collapsable-column")
                    .find_first();

                assert_node_exists!(&collapsable_column);

                let primary_button_row = left_column
                    .find_child_with()
                    .test_id("primary-button-row")
                    .find_first();

                let secondary_button_row = collapsable_column
                    .find_child_with()
                    .test_id("secondary-button-row")
                    .find_first();

                let primary_button = primary_button_row
                    .find_child_with()
                    .test_id("primary-button")
                    .find_first();

                let secondary_button = secondary_button_row
                    .find_child_with()
                    .test_id("secondary-button")
                    .find_first();

                let p = primary_button;

                let w = p.borrow_props().layout.size.width;

                let primary_button_width = w.round();

                assert_eq!(primary_button_width, expected_width);

                assert_eq!(
                    primary_button_width,
                    secondary_button.borrow_props().layout.size.width.round()
                );
            },
        )
    }

    #[test]
    fn on_focus_called() {
        launch_test(
            |ctx| {
                let last_focused_card = create_rw_signal(ctx.scope(), 5);
                let on_focus_called = create_rw_signal(ctx.scope(), false);
                let on_item_focus = Rc::new(move |idx: usize| {
                    last_focused_card.set(idx);
                });
                let on_focus = Rc::new(move || {
                    on_focus_called.set(true);
                });
                provide_context(ctx.scope(), last_focused_card);
                provide_context(ctx.scope(), on_focus_called);
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), false),
                        size_config: expandable_but_always(ctx.scope(), true),
                        on_focus: Some(on_focus),
                        on_item_focus: Some(on_item_focus),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();

                let on_focus_called = use_context::<RwSignal<bool>>(scope);
                let on_focus_called =
                    on_focus_called.expect("signal not found for on_focus_called");

                // reset on focus called signal and send focus elsewhere
                on_focus_called.set(false);
                let other_button = tree.find_by_test_id("other_button");
                test_game_loop.send_on_focus_event(other_button.borrow_props().node_id);
                let tree = test_game_loop.tick_until_done();
                assert!(!on_focus_called.get_untracked());

                let hero_id = tree
                    .find_by_test_id(HERO_CARD_TEST_ID)
                    .borrow_props()
                    .node_id;
                test_game_loop.send_on_focus_event(hero_id);
                let _ = test_game_loop.tick_until_done();

                assert!(on_focus_called.get_untracked());

                let last_focused_card = use_context::<RwSignal<usize>>(scope);
                let last_focused_card =
                    last_focused_card.expect("signal not found for last_focused_card");

                // initial on_focus calls on_item_focus for index 0
                assert_eq!(last_focused_card.get_untracked(), 0);

                test_game_loop.send_key_down_up_event_to_node(hero_id, KeyCode::Right);
                let _ = test_game_loop.tick_until_done();

                run_animation(&mut test_game_loop);

                // after scrolling right, index 1 is focused
                assert_eq!(last_focused_card.get_untracked(), 1);

                last_focused_card.set(5);

                // reset on focus called signal and send focus elsewhere
                on_focus_called.set(false);
                let other_button = tree.find_by_test_id("other_button");
                test_game_loop.send_on_focus_event(other_button.borrow_props().node_id);
                let _ = test_game_loop.tick_until_done();

                assert!(!on_focus_called.get_untracked());

                test_game_loop.send_on_focus_event(hero_id);
                let _ = test_game_loop.tick_until_done();

                assert!(on_focus_called.get_untracked());
                // subsequent on_focus calls on_item_focus for index 1
                assert_eq!(last_focused_card.get_untracked(), 1);
            },
        )
    }

    #[rstest]
    #[case(ButtonVariant::Primary, "primary-button")]
    #[case(ButtonVariant::Secondary, "secondary-button")]
    fn open_contextual_menu_called_with_menu_button(
        #[case] button_variant: ButtonVariant,
        #[case] button_name: &'static str,
    ) {
        launch_test(
            move |ctx| {
                let items = generate_mock_data(ctx.scope(), false);
                let last_contextual_menu_open = create_rw_signal(ctx.scope(), None);
                let open_contextual_menu = Rc::new(move |idx: usize, _button: ButtonVariant| {
                    last_contextual_menu_open.set(Some((idx, button_variant)));
                });

                provide_context(ctx.scope(), last_contextual_menu_open);
                let props = CreateTestHeroProps {
                    items,
                    open_contextual_menu: Some(open_contextual_menu),
                    has_menu_button: true,
                    ..CreateTestHeroProps::default()
                };
                create_test_hero(&ctx, props)
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let button = tree.find_by_test_id(button_name);
                test_game_loop
                    .send_key_down_up_event_to_node(button.get_props().node_id, KeyCode::Menu);

                test_game_loop.tick_until_done();

                let last_contextual_menu_item_opened =
                    use_context::<RwSignal<Option<(usize, ButtonVariant)>>>(scope)
                        .expect("Expected to find last_contextual_menu_item_opened context");

                assert_eq!(
                    last_contextual_menu_item_opened.get_untracked(),
                    Some((0, button_variant))
                );
            },
        )
    }

    #[rstest]
    #[case(ButtonVariant::Primary, "primary-button")]
    #[case(ButtonVariant::Secondary, "secondary-button")]
    fn contextual_menu_does_not_open_with_menu_if_not_firetv(
        #[case] button_variant: ButtonVariant,
        #[case] button_name: &'static str,
    ) {
        launch_test(
            move |ctx| {
                let items = generate_mock_data(ctx.scope(), false);
                let last_contextual_menu_open = create_rw_signal(ctx.scope(), None);
                let open_contextual_menu = Rc::new(move |idx: usize, _button: ButtonVariant| {
                    last_contextual_menu_open.set(Some((idx, button_variant)));
                });

                provide_context(ctx.scope(), last_contextual_menu_open);
                let props = CreateTestHeroProps {
                    items,
                    open_contextual_menu: Some(open_contextual_menu),
                    ..CreateTestHeroProps::default()
                };
                create_test_hero(&ctx, props)
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let button = tree.find_by_test_id(button_name);
                test_game_loop
                    .send_key_down_up_event_to_node(button.get_props().node_id, KeyCode::Menu);

                test_game_loop.tick_until_done();

                let last_contextual_menu_item_opened =
                    use_context::<RwSignal<Option<(usize, ButtonVariant)>>>(scope)
                        .expect("Expected to find last_contextual_menu_item_opened context");

                assert_eq!(last_contextual_menu_item_opened.get_untracked(), None);
            },
        )
    }

    #[rstest]
    #[case(ButtonVariant::Primary, "primary-button", KeyCode::Enter)]
    #[case(ButtonVariant::Primary, "primary-button", KeyCode::Select)]
    #[case(ButtonVariant::Secondary, "secondary-button", KeyCode::Enter)]
    #[case(ButtonVariant::Secondary, "secondary-button", KeyCode::Select)]
    fn open_contextual_menu_called_with_long_press(
        #[case] button_variant: ButtonVariant,
        #[case] button_name: &'static str,
        #[case] key_code: KeyCode,
    ) {
        launch_test(
            move |ctx| {
                let items = generate_mock_data(ctx.scope(), false);
                let last_contextual_menu_open = create_rw_signal(ctx.scope(), None);
                let open_contextual_menu = Rc::new(move |idx: usize, _button: ButtonVariant| {
                    last_contextual_menu_open.set(Some((idx, button_variant)));
                });

                provide_context(ctx.scope(), last_contextual_menu_open);
                let props = CreateTestHeroProps {
                    items,
                    open_contextual_menu: Some(open_contextual_menu),
                    ..CreateTestHeroProps::default()
                };
                create_test_hero(&ctx, props)
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let button = tree.find_by_test_id(button_name);
                test_game_loop
                    .send_long_key_press_event_to_node(button.get_props().node_id, key_code);

                test_game_loop.tick_until_done();

                let last_contextual_menu_item_opened =
                    use_context::<RwSignal<Option<(usize, ButtonVariant)>>>(scope)
                        .expect("Expected to find last_contextual_menu_item_opened context");

                assert_eq!(
                    last_contextual_menu_item_opened.get_untracked(),
                    Some((0, button_variant))
                );
            },
        )
    }

    #[test]
    fn fades_when_trailer_is_playing() {
        launch_test(
            |ctx| {
                let last_focused_card = create_rw_signal(ctx.scope(), 5);
                let on_focus_called = create_rw_signal(ctx.scope(), false);
                let on_item_focus = Rc::new(move |idx: usize| {
                    last_focused_card.set(idx);
                });
                let on_focus = Rc::new(move || {
                    on_focus_called.set(true);
                });
                let hero_trailer_context = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_context);
                provide_context(
                    ctx.scope(),
                    create_rw_signal(
                        ctx.scope(),
                        MediaBackgroundType::StandardHero(StandardHeroBackgroundData {
                            id: "gti".to_string(),
                            image_url: None,
                            video_id: None,
                            enter_immediately: false,
                            media_strategy: MediaStrategy::Promo,
                            csm_data: None,
                            rotation_direction: RotationDirection::NONE,
                            is_expanded: create_rw_signal(ctx.scope(), false),
                            placement: "".to_string(),
                        }),
                    ),
                );
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), false),
                        size_config: expandable_but_always(ctx.scope(), true),
                        on_focus: Some(on_focus),
                        on_item_focus: Some(on_item_focus),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let hero_trailer_context = use_context::<TrailerPlaybackContext>(scope).unwrap();
                hero_trailer_context.on_trailer_playback_start.set(true);
                MockClock::advance(Duration::from_millis(200));
                let tree = test_game_loop.tick_until_done();

                let hero_opacity = tree
                    .find_by_test_id(HERO_CARD_TEST_ID)
                    .borrow_props()
                    .base_styles
                    .opacity;

                assert_eq!(hero_opacity, Some(0.7));
                hero_trailer_context.on_trailer_playback_start.set(false);
                let tree = test_game_loop.tick_until_done();
                let hero_opacity = tree
                    .find_by_test_id(HERO_CARD_TEST_ID)
                    .borrow_props()
                    .base_styles
                    .opacity;

                assert_eq!(hero_opacity, Some(1.0));
            },
        )
    }

    #[rstest]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_aquisition_action(Some(generate_mock_prime_acquisition_metadata()))), Some(generate_details_action()), true, None, None)],
        Js(JSPage::NEW_CONFIRMATION_PAGE),
        add_deferred_action_to_params(generate_params_for_prime_acquisition(), generate_details_action()), false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_aquisition_action(Some(generate_mock_prime_acquisition_metadata()))), Some(generate_playback_action()), true, None, None)],
        Js(JSPage::NEW_CONFIRMATION_PAGE),
        generate_params_for_prime_acquisition(), false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_aquisition_action(Some(generate_mock_prime_acquisition_metadata()))), None, true, None, None)],
        Js(JSPage::NEW_CONFIRMATION_PAGE),
        generate_params_for_prime_acquisition(), false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_aquisition_action(Some(generate_mock_svod_acquisition_metadata("mockBenefitId".to_string())))), Some(generate_details_action()), true, None, None)],
        Js(JSPage::NEW_CONFIRMATION_PAGE),
        add_deferred_action_to_params(generate_params_for_svod_acquisition("mockBenefitId".to_string()), generate_details_action()), false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_aquisition_action(Some(generate_mock_svod_acquisition_metadata("mockBenefitId".to_string())))), None, true, None, None)],
        Js(JSPage::NEW_CONFIRMATION_PAGE),
        generate_params_for_svod_acquisition("mockBenefitId".to_string()), false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_aquisition_action(Some(generate_mock_tvod_acquisition_metadata()))), Some(generate_details_action()), true, None, None)],
        Js(JSPage::TVOD_MODAL_HOST),
        generate_params_for_tvod_acquisition("hero gti 0".to_string()), false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_playback_action()), None, true, None, None)],
        Js(JSPage::START_PLAYBACK),
        Transition {
            action: generate_playback_action(),
            deferred_action: None,
            enable_duplicate_destination_navigation: None,
            is_signed_in: true,
            rpc_manager_wrapper: RPCManagerWrapper(None),
            rust_features: None,
            seamless_if_possible: false,
            content_type: Some("MOVIE".to_string()),
            title_id: None,
            playback_origin_override: None,
            ingress_source: None
        }.to_location().unwrap().pageParams,
        false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_playback_action()), Some(generate_aquisition_action(Some(generate_mock_prime_acquisition_metadata()))), true, None, None)],
        Js(JSPage::START_PLAYBACK),
        Transition {
            action: generate_playback_action(),
            deferred_action: None,
            enable_duplicate_destination_navigation: None,
            is_signed_in: true,
            rpc_manager_wrapper: RPCManagerWrapper(None),
            rust_features: None,
            seamless_if_possible: false,
            content_type: Some("MOVIE".to_string()),
            title_id: None,
            playback_origin_override: None,
            ingress_source: None
        }.to_location().unwrap().pageParams,
        false)
    ]
    #[serial]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, Some(generate_playback_action()), Some(generate_details_action()), true, None, None)],
        Js(JSPage::START_PLAYBACK),
        Transition {
            action: generate_playback_action(),
            deferred_action: None,
            enable_duplicate_destination_navigation: None,
            is_signed_in: true,
            rpc_manager_wrapper: RPCManagerWrapper(None),
            rust_features: None,
            seamless_if_possible: false,
            content_type: Some("MOVIE".to_string()),
            title_id: None,
            playback_origin_override: None,
            ingress_source: None
        }.to_location().unwrap().pageParams,
        true)
    ]
    fn on_select_primary_button(
        #[case] items: impl Fn(Scope) -> Vec<HeroItemModel> + 'static,
        #[case] expected_location: PageType,
        #[case] expected_params: Map<String, Value>,
        #[case] should_go_back_to_details: bool,
    ) {
        let mut mock_routing = MockRouting::new();

        mock_routing
            .expect_navigate()
            .times(1)
            .with(
                eq(Location {
                    pageType: expected_location,
                    pageParams: expected_params,
                }),
                eq("HERO"),
            )
            .return_const(());

        if should_go_back_to_details {
            mock_routing
                .expect_mutate_history()
                .returning(move |callback| {
                    let mut locations = vec![
                        Location {
                            pageType: PageType::Js(JSPage::OPEN_DETAIL_PAGE),
                            pageParams: Default::default(),
                        },
                        Location {
                            pageType: PageType::Js(JSPage::COLLECTION_PAGE),
                            pageParams: Default::default(),
                        },
                        Location {
                            pageType: PageType::Js(JSPage::OPEN_HELP_PAGE),
                            pageParams: Default::default(),
                        },
                    ];

                    callback(&mut locations);

                    assert_eq!(
                        locations,
                        vec![
                            Location {
                                pageType: PageType::Js(JSPage::OPEN_DETAIL_PAGE),
                                pageParams: Default::default(),
                            },
                            Location {
                                pageType: PageType::Js(JSPage::COLLECTION_PAGE),
                                pageParams: Default::default(),
                            },
                            Location {
                                pageType: PageType::Js(JSPage::OPEN_HELP_PAGE),
                                pageParams: Default::default(),
                            },
                            Location {
                                pageType: PageType::Js(JSPage::OPEN_DETAIL_PAGE),
                                pageParams: {
                                    let mut params = Map::new();

                                    params.insert("isLiveDetailsPage".to_string(), true.into());
                                    params.insert(
                                        "seamlessTransitionEnabled".to_string(),
                                        false.into(),
                                    );
                                    params.insert("titleId".to_string(), "details".into());
                                    params.insert("journeyIngressContext".to_string(), Value::Null);

                                    params
                                }
                            },
                        ]
                    );
                })
                .times(1);
        } else {
            mock_routing.expect_mutate_history().never();
        }

        launch_test(
            move |ctx| {
                mock_routing
                    .expect_location()
                    .return_const(create_rw_signal(ctx.scope(), Location::default()));
                let mut auth = MockAuth::new_without_params(ctx.scope());
                auth.set_access_token(Some("access_token".to_string()));
                let hybrid_playback_context = HybridPlaybackContext {
                    should_release: create_rw_signal(ctx.scope(), None),
                    seamless_egress: store_value(ctx.scope(), false),
                };

                provide_context::<HybridPlaybackContext>(ctx.scope(), hybrid_playback_context);
                provide_context::<AuthContext>(ctx.scope, Rc::new(auth));
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: items(ctx.scope()),
                        size_config: expandable_but_always(ctx.scope(), true),
                        setup_routing: false,
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let hybrid_playback_context = use_context::<HybridPlaybackContext>(scope).unwrap();
                let primary_button = tree.find_by_test_id("primary-button");
                test_loop.send_on_select_event(primary_button.get_props().node_id);
                test_loop.tick_until_done();
                assert_eq!(
                    hybrid_playback_context.should_release.get_untracked(),
                    Some(ReleaseState::NotSeamless)
                );
                MockClock::advance(Duration::from_millis(350));
                test_loop.tick_until_done();
            },
        )
    }

    #[test]
    fn on_select_primary_button_open_modal_action_launches_ssm() {
        launch_test(
            move |ctx| {
                let mut auth = MockAuth::new_without_params(ctx.scope());
                auth.set_access_token(Some("access_token".to_string()));
                let (read_cm, update_cm) = create_signal(ctx.scope(), None);
                let items = vec![generate_mock_hero_card_with_actions(
                    ctx.scope(),
                    0,
                    Some(generate_open_modal_action()),
                    Some(generate_details_action()),
                    false,
                    None,
                    None,
                )];

                provide_context::<AuthContext>(ctx.scope, Rc::new(auth));
                provide_context::<ReadSignal<Option<ContextualMenuData>>>(ctx.scope(), read_cm);

                let hero_trailer_context = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_context);
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items,
                        size_config: expandable_but_always(ctx.scope(), true),
                        update_cm: Some(update_cm),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let read_cm = use_context::<ReadSignal<Option<ContextualMenuData>>>(scope)
                    .expect("ContextualMenu Signal not found in context");
                let primary_button = tree.find_by_test_id("primary-button");
                let hero_trailer_context = use_context::<TrailerPlaybackContext>(scope).unwrap();

                assert!(hero_trailer_context.should_play_hero.get_untracked());

                test_loop.send_on_select_event(primary_button.get_props().node_id);
                test_loop
                    .send_on_focus_event(tree.find_by_test_id("other_button").get_props().node_id);
                test_loop.tick_until_done();

                assert!(!hero_trailer_context.should_play_hero.get_untracked());

                let cm_data = read_cm.get_untracked();
                assert!(matches!(cm_data, Some(ContextualMenuData::WatchModal(_))));
            },
        )
    }

    #[test]
    fn news_page_continues_autoplay_when_hero_unfocused() {
        launch_test(
            move |ctx| {
                let mut auth = MockAuth::new_without_params(ctx.scope());
                auth.set_access_token(Some("access_token".to_string()));
                let items = vec![generate_mock_hero_card_with_actions(
                    ctx.scope(),
                    0,
                    Some(generate_open_modal_action()),
                    Some(generate_details_action()),
                    false,
                    None,
                    None,
                )];

                let mut mock_router = MockRouting::default();
                mock_router
                    .expect_get_last_navigation_action()
                    .returning(|| NavigationAction {
                        direction: NavigationDirection::Forward,
                        from: PageType::Rust(RUST_COLLECTIONS),
                        to: PageType::Rust(RUST_COLLECTIONS),
                    });
                mock_router.expect_location().return_const(create_rw_signal(
                    ctx.scope(),
                    Location {
                        pageType: PageType::Rust(RUST_COLLECTIONS),
                        pageParams: get_news_page_params(),
                    },
                ));

                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));
                provide_context_test_rust_features(ctx.scope);
                provide_context::<AuthContext>(ctx.scope, Rc::new(auth));

                let hero_trailer_context = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_context);

                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items,
                        hero_initial_focus: false,
                        setup_routing: false,
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Validate hero is not focused
                let primary_button = tree.find_by_test_id("primary-button");
                assert!(!primary_button.borrow_props().is_focused);

                // autoplay should play
                let hero_trailer_context = use_context::<TrailerPlaybackContext>(scope).unwrap();
                assert!(hero_trailer_context.should_play_hero.get_untracked());
            },
        )
    }

    #[test]
    fn modal_does_not_distrupt_autoplay_when_seamless_transition_enabled() {
        launch_test(
            move |ctx| {
                let mut auth = MockAuth::new_without_params(ctx.scope());
                auth.set_access_token(Some("access_token".to_string()));
                let (read_cm, update_cm) = create_signal(ctx.scope(), None);
                let items = vec![generate_mock_hero_card_with_actions(
                    ctx.scope(),
                    0,
                    Some(generate_open_modal_action()),
                    Some(generate_details_action()),
                    false,
                    None,
                    None,
                )];

                provide_context_test_rust_features(ctx.scope);
                provide_context::<AuthContext>(ctx.scope, Rc::new(auth));
                provide_context::<ReadSignal<Option<ContextualMenuData>>>(ctx.scope(), read_cm);

                let hero_trailer_context = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_context);

                let features = MockRustFeaturesBuilder::new()
                    .set_is_seamless_transition_on_rust_live_hero_enabled(true)
                    .build();

                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items,
                        size_config: expandable_but_always(ctx.scope(), true),
                        features: Some(features),
                        update_cm: Some(update_cm),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let read_cm = use_context::<ReadSignal<Option<ContextualMenuData>>>(scope)
                    .expect("ContextualMenu Signal not found in context");
                let primary_button = tree.find_by_test_id("primary-button");
                let hero_trailer_context = use_context::<TrailerPlaybackContext>(scope).unwrap();

                assert!(hero_trailer_context.should_play_hero.get_untracked());

                test_loop.send_on_select_event(primary_button.get_props().node_id);
                test_loop
                    .send_on_focus_event(tree.find_by_test_id("other_button").get_props().node_id);
                test_loop.tick_until_done();

                assert!(hero_trailer_context.should_play_hero.get_untracked());

                let cm_data = read_cm.get_untracked();
                assert!(matches!(cm_data, Some(ContextualMenuData::WatchModal(_))));
            },
        )
    }

    #[rstest]
    #[case(
        |scope: Scope| vec![generate_mock_hero_card_with_actions(scope, 0, None, None, true, None, None)],
    )
    ]
    fn test_on_move_to_second_container_called(
        #[case] items: impl Fn(Scope) -> Vec<HeroItemModel> + 'static,
    ) {
        launch_test(
            move |ctx| {
                let on_move_to_second_container_called = create_rw_signal(ctx.scope(), false);
                let on_move_to_second_container = Rc::new(move || {
                    on_move_to_second_container_called.set(true);
                });
                provide_context(ctx.scope(), on_move_to_second_container_called);
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: items(ctx.scope()),
                        size_config: expandable_but_always(ctx.scope(), true),
                        on_move_to_second_container: Some(on_move_to_second_container),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let on_move_to_second_container_called = use_context::<RwSignal<bool>>(scope);
                let on_move_to_second_container_called = on_move_to_second_container_called
                    .expect("signal not found for on_move_to_second_container_called");
                let primary_button = tree.find_by_test_id("primary-button");
                test_game_loop.send_on_select_event(primary_button.get_props().node_id);
                let _ = test_game_loop.tick_until_done();
                assert!(on_move_to_second_container_called.get_untracked());
            },
        )
    }

    #[test]
    #[serial]
    fn on_select_primary_button_only_navigates_once_when_pressed_twice() {
        let items = |scope: Scope| {
            vec![generate_mock_hero_card_with_actions(
                scope,
                0,
                Some(generate_aquisition_action(Some(
                    generate_mock_tvod_acquisition_metadata(),
                ))),
                Some(generate_details_action()),
                true,
                None,
                None,
            )]
        };
        let expected_location = Js(JSPage::TVOD_MODAL_HOST);
        let expected_params = generate_params_for_tvod_acquisition("hero gti 0".to_string());
        let mut mock_routing = MockRouting::new();

        mock_routing
            .expect_get_last_navigation_action()
            .returning(|| NavigationAction {
                direction: NavigationDirection::Forward,
                from: PageType::Rust(RUST_COLLECTIONS),
                to: PageType::Rust(RUST_COLLECTIONS),
            });

        mock_routing
            .expect_navigate()
            .times(1)
            .with(
                eq(Location {
                    pageType: expected_location,
                    pageParams: expected_params,
                }),
                eq("HERO"),
            )
            .return_const(());

        launch_test(
            move |ctx| {
                mock_routing
                    .expect_location()
                    .return_const(create_rw_signal(ctx.scope(), Location::default()));
                let mut auth = MockAuth::new_without_params(ctx.scope());
                auth.set_access_token(Some("access_token".to_string()));
                let hybrid_playback_context = HybridPlaybackContext {
                    should_release: create_rw_signal(ctx.scope(), None),
                    seamless_egress: store_value(ctx.scope(), false),
                };

                provide_context::<HybridPlaybackContext>(ctx.scope(), hybrid_playback_context);
                provide_context::<AuthContext>(ctx.scope, Rc::new(auth));
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: items(ctx.scope()),
                        size_config: expandable_but_always(ctx.scope(), true),
                        setup_routing: false,
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_loop| {
                let hybrid_playback_context = use_context::<HybridPlaybackContext>(scope).unwrap();
                let tree = test_loop.tick_until_done();

                let primary_button = tree.find_by_test_id("primary-button");
                test_loop.send_on_select_event(primary_button.clone().get_props().node_id);
                test_loop.tick_until_done();
                assert_eq!(
                    hybrid_playback_context.should_release.get_untracked(),
                    Some(ReleaseState::NotSeamless)
                );
                test_loop.send_on_select_event(primary_button.get_props().node_id);
                test_loop.tick_until_done();
                MockClock::advance(Duration::from_millis(350));
                test_loop.tick_until_done();
            },
        )
    }

    #[test]
    fn on_select_primary_button_should_not_navigate_if_on_back_pressed() {
        launch_test(
            |ctx| {
                let back_pressed_signal = create_rw_signal(ctx.scope(), "no");
                let on_back_pressed = Rc::new(move || back_pressed_signal.set("yes"));
                provide_context(ctx.scope(), back_pressed_signal);
                let mut auth = MockAuth::new_without_params(ctx.scope());
                auth.set_access_token(Some("access_token".to_string()));
                provide_context::<AuthContext>(ctx.scope, Rc::new(auth));

                let mut mock_routing = MockRouting::new();

                mock_routing
                    .expect_location()
                    .return_const(create_rw_signal(ctx.scope(), Location::default()));
                mock_routing
                    .expect_get_last_navigation_action()
                    .returning(|| NavigationAction {
                        direction: NavigationDirection::Forward,
                        from: PageType::Rust(RUST_COLLECTIONS),
                        to: PageType::Rust(RUST_COLLECTIONS),
                    });
                mock_routing.expect_navigate().times(0);
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing));

                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), true),
                        size_config: expandable_but_always(ctx.scope(), true),
                        on_back_pressed: Some(on_back_pressed),
                        setup_routing: false,
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let mock_data = generate_mock_data(scope, true);

                let mut tree = test_game_loop.tick_until_done();

                let back_pressed: RwSignal<&str> = use_context(scope).unwrap();
                assert_eq!(back_pressed.get_untracked(), "no");

                let primary_button = tree.find_by_test_id("primary-button");
                test_game_loop.send_on_select_event(primary_button.clone().get_props().node_id);
                let _ = test_game_loop.tick_until_done();

                assert_eq!(back_pressed.get_untracked(), "no");
                validate_on_item(0, &tree, &mock_data);
                MockClock::advance(Duration::from_millis(250));

                test_game_loop.send_key_down_up_event_to_node(
                    primary_button.clone().get_props().node_id,
                    KeyCode::Backspace,
                );
                MockClock::advance(Duration::from_millis(50));
                tree = test_game_loop.tick_until_done();
                assert_eq!(back_pressed.get_untracked(), "yes");
            },
        )
    }

    mod focus_management {
        use super::*;

        // Essentially ensures that we use something like .focus_scope(), which makes sure that the .preferred_focus is
        // only local within the hero component. We had a bug where the .preferred_focus would take
        // away the focus from all the other containers on the page during focus restoration
        #[test]
        fn preferred_focus_on_hero_buttons_should_not_take_away_the_focus_from_all_the_other_containers(
        ) {
            launch_test(
                |ctx| {
                    provide_firetv_context(&ctx, false);
                    provide_context_test_rust_features(ctx.scope);

                    setup_mock_routing_with_past_location(&ctx);
                    provide_context(
                        ctx.scope(),
                        TTSEnabledContext(create_signal(ctx.scope(), false).0),
                    );

                    let items =
                        create_rw_signal(ctx.scope(), generate_mock_data(ctx.scope(), false));
                    let size_config = SizeConfig::Expandable(Signal::derive(ctx.scope(), || true));
                    let (_, update_cm) = create_signal(ctx.scope(), None);
                    compose! {
                        Column() {
                            // In this test the order of these two containers is important as well. If we don't have focus_scope it will take the first
                            // preferred_focus that it sees during the tree traversal.
                            HeroSig(items, size_config, on_item_focus: Rc::new(|_|{}), on_back_pressed: Rc::new(||{}), metadata:create_metadata(ctx.scope()), is_tentpole: false, update_cm, on_item_view: Rc::new(|_,_,_,_| {}), on_item_highlight: Rc::new(|_,_,_| {}))
                            Button(text: "This carousel has preferred_focus due to focus restoration logic".to_string())
                                .preferred_focus(true)
                                .test_id("standard-carousel")
                        }
                    }
                },
                |_scope, mut test_game_loop| {
                    let tree = test_game_loop.tick_until_done();
                    let carousel = tree.find_by_test_id("standard-carousel");
                    assert!(carousel.borrow_props().is_focused);
                },
            );
        }

        // Scenarios where the user was focused on the hero -> focuses on something else -> refocuses back to the hero
        #[rstest]
        fn should_refocus_on_last_focused_button() {
            launch_test(
                |ctx| {
                    provide_firetv_context(&ctx, false);
                    provide_context_test_rust_features(ctx.scope);

                    setup_mock_routing_with_past_location(&ctx);
                    provide_context(
                        ctx.scope(),
                        TTSEnabledContext(create_signal(ctx.scope(), false).0),
                    );

                    let items =
                        create_rw_signal(ctx.scope(), generate_mock_data(ctx.scope(), false));
                    let size_config = SizeConfig::Expandable(Signal::derive(ctx.scope(), || true));
                    let (_, update_cm) = create_signal(ctx.scope(), None);
                    compose! {
                        Column() {
                            Button(text: "Mock Utility".to_string())
                                .test_id("utility-nav")
                            HeroSig(items, size_config, on_item_focus: Rc::new(|_|{}), on_back_pressed: Rc::new(||{}), metadata:create_metadata(ctx.scope()), is_tentpole: false, update_cm, on_item_view: Rc::new(|_,_,_,_| {}), on_item_highlight: Rc::new(|_,_,_| {}))
                                .preferred_focus(true)
                        }
                    }
                },
                |_scope, mut test_game_loop| {
                    let tree = test_game_loop.tick_until_done();

                    let button = tree.find_by_test_id("primary-button").get_props();
                    assert!(button.is_focused);

                    test_game_loop.send_on_focus_event(
                        tree.find_by_test_id("utility-nav").get_props().node_id,
                    );
                    let _ = test_game_loop.tick_until_done();

                    test_game_loop.send_on_focus_event(
                        tree.find_by_test_id(HERO_CARD_TEST_ID).get_props().node_id,
                    );
                    let _ = test_game_loop.tick_until_done();

                    let primary_button = tree.find_by_test_id("primary-button").get_props();
                    assert!(primary_button.is_focused);

                    let secondary_button = tree.find_by_test_id("secondary-button").get_props();
                    assert!(!secondary_button.is_focused);
                    test_game_loop.send_on_focus_event(secondary_button.node_id);
                    let _ = test_game_loop.tick_until_done();

                    test_game_loop.send_on_focus_event(
                        tree.find_by_test_id("utility-nav").get_props().node_id,
                    );
                    let _ = test_game_loop.tick_until_done();

                    test_game_loop.send_on_focus_event(
                        tree.find_by_test_id(HERO_CARD_TEST_ID).get_props().node_id,
                    );
                    let tree = test_game_loop.tick_until_done();

                    let secondary_button = tree.find_by_test_id("secondary-button").get_props();
                    assert!(secondary_button.is_focused);
                },
            );
        }
    }

    // Tests the integration of the autorotation controller.
    mod auto_rotation_integration {
        use super::*;
        use media_background::types::context::MediaBackgroundStatus;
        use settings_manager::try_use_settings_manager;
        use std::cell::Cell;

        #[derive(Clone)]
        struct AutoRotationTestContextInner {
            last_item_focused: Rc<Cell<usize>>,
            focused_nav_signal: FocusValueSignal<FocusedNav>,
            top_nav_mode: RwSignal<TopNavMode>,
            mock_data: Vec<HeroItemModel>,
            hero_trailer_context: TrailerPlaybackContext,
            background_state: RwSignal<Option<MediaBackgroundStatus>>,
        }
        struct AutoRotationTestContext {
            context: AutoRotationTestContextInner,
            game_loop: TestRendererGameLoop,
            tree: SceneNodeTree,
        }

        impl AutoRotationTestContext {
            fn new(scope: Scope, mut game_loop: TestRendererGameLoop) -> Self {
                let tree = game_loop.tick_until_done();
                provide_context_test_rust_features(scope);
                Self {
                    context: expect_context::<AutoRotationTestContextInner>(scope),
                    game_loop,
                    tree,
                }
            }
            fn advance_time(&mut self, duration: Duration) {
                MockClock::advance(duration);
                self.game_loop.tick_until_done();
                self.tree = run_animation(&mut self.game_loop)
            }

            fn assert_focused_index(&self, expected_index: usize) {
                assert_eq!(
                    expected_index,
                    self.context.last_item_focused.get(),
                    "Expected focused index {:} != Actual focused index {:}",
                    expected_index,
                    self.context.last_item_focused.get()
                );
            }

            fn send_hero_item_visible_event(&mut self) {
                let item = &self.context.mock_data[self.context.last_item_focused.get()];
                let id = match item.media_background_data.get_untracked() {
                    MediaBackgroundType::StandardHero(data) => data.id,
                    MediaBackgroundType::Tentpole(data) => data.id,
                    _ => panic!(""),
                };
                self.context
                    .background_state
                    .set(Some(MediaBackgroundStatus {
                        id,
                        background_state: BackgroundState::Downloaded,
                    }));
                self.tree = self.game_loop.tick_once().node_tree;
            }

            fn send_hero_trailer_playback_finished_event(&mut self, value: bool) {
                self.context
                    .hero_trailer_context
                    .on_playback_finished
                    .set(value);
            }

            fn send_key_down_up_event_to_node(&mut self, code: KeyCode) {
                self.game_loop.send_key_down_up_event_to_node(
                    self.tree
                        .find_by_test_id(HERO_CARD_TEST_ID)
                        .get_props()
                        .node_id,
                    code,
                );
                self.tree = self.game_loop.tick_until_done();
            }
        }

        fn test_should_rotate_every_5s_and_fully(scope: Scope, game_loop: TestRendererGameLoop) {
            let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

            // Initially focused on the first item
            test_ctx.assert_focused_index(0);
            test_ctx.game_loop.tick_until_done();

            // We rotate n - 1 times and should land on the last item
            for i in 1..test_ctx.context.mock_data.len() {
                test_ctx.send_hero_item_visible_event();
                test_ctx.advance_time(Duration::from_millis(5000));
                test_ctx.assert_focused_index(i);
            }

            // Final rotation should make us focus back on the first item
            test_ctx.send_hero_item_visible_event();
            test_ctx.advance_time(Duration::from_millis(5000));
            test_ctx.assert_focused_index(0);
        }

        fn test_should_not_rotate(scope: Scope, game_loop: TestRendererGameLoop) {
            let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

            test_ctx.game_loop.tick_until_done();
            test_ctx.assert_focused_index(0);

            for _ in 0..10 {
                test_ctx.send_hero_item_visible_event();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_focused_index(0);
            }
        }

        struct TestHeroOption {
            has_trailers: bool,
            expected_navigation_location: Option<Location>,
            items: Option<Vec<HeroItemModel>>,
            // Menu button exists on fire tv remote and opens/closes the contextual menu
            has_menu_button: bool,
        }

        impl Default for TestHeroOption {
            fn default() -> Self {
                Self {
                    has_trailers: false,
                    items: None,
                    expected_navigation_location: None,
                    has_menu_button: false,
                }
            }
        }

        fn create_test_hero(
            ctx: AppContext,
            TestHeroOption {
                has_trailers: use_gti,
                items: maybe_items,
                expected_navigation_location,
                has_menu_button,
            }: TestHeroOption,
        ) -> impl Composable<'static> {
            let last_item_focused = Rc::new(Cell::new(0));
            let focused_nav_signal = create_focus_value_signal(ctx.scope());
            let top_nav_mode = create_rw_signal(ctx.scope(), TopNavMode::TopNav);
            let mock_data = maybe_items.unwrap_or_else(|| generate_mock_data(ctx.scope(), use_gti));
            let items = create_rw_signal(ctx.scope(), mock_data.clone());

            let on_item_focus = Rc::new({
                let last_item_focused = Rc::clone(&last_item_focused);
                move |index: usize| last_item_focused.set(index)
            });

            let on_playback_finished = create_rw_signal(ctx.scope(), false);
            let on_playback_start = create_rw_signal(ctx.scope(), false);
            let should_play_hero = create_rw_signal(ctx.scope(), false);
            let (_, update_cm) = create_signal(ctx.scope(), None);

            let hero_trailer_context = TrailerPlaybackContext {
                on_playback_finished,
                on_trailer_playback_start: on_playback_start,
                should_play_hero,
                suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
            };

            let background_state = create_rw_signal(ctx.scope(), None::<MediaBackgroundStatus>);
            let mb_ready_context = MediaBackgroundStatusContext {
                background_state: background_state.into(),
                player_resize_support: create_rw_signal(ctx.scope(), None).into(),
            };
            let size_config = expandable_but_always(ctx.scope(), true);

            let mut mock_router = MockRouting::default();
            mock_router
                .expect_location()
                .return_const(create_rw_signal(ctx.scope(), Location::default()));
            mock_router
                .expect_get_last_navigation_action()
                .returning(|| NavigationAction {
                    direction: NavigationDirection::Forward,
                    from: PageType::Rust(RUST_COLLECTIONS),
                    to: PageType::Rust(RUST_COLLECTIONS),
                });

            if let Some(expected_navigation_location) = expected_navigation_location {
                mock_router
                    .expect_navigate()
                    .with(eq(expected_navigation_location), eq("HERO"))
                    .once()
                    .return_const(());
            }

            provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_router));

            // Context for the Hero component
            provide_context(ctx.scope(), MockRustFeaturesBuilder::new().build());
            provide_context_test_rust_features(ctx.scope());
            provide_context::<FocusValueSignal<FocusedNav>>(ctx.scope(), focused_nav_signal);
            provide_context::<ReadSignal<TopNavMode>>(ctx.scope(), top_nav_mode.read_only());
            provide_context::<WriteSignal<TopNavMode>>(ctx.scope(), top_nav_mode.write_only());
            provide_context::<TrailerPlaybackContext>(ctx.scope(), hero_trailer_context.clone());
            provide_context::<MediaBackgroundStatusContext>(ctx.scope(), mb_ready_context);
            provide_firetv_context(&ctx, has_menu_button);

            if use_context::<TTSEnabledContext>(ctx.scope()).is_none() {
                provide_context(
                    ctx.scope(),
                    TTSEnabledContext(create_signal(ctx.scope(), false).0),
                );
            }

            try_use_settings_manager(ctx.scope());

            let test_ctx = AutoRotationTestContextInner {
                last_item_focused,
                focused_nav_signal,
                mock_data,
                hero_trailer_context,
                background_state,
                top_nav_mode,
            };
            provide_context(ctx.scope(), test_ctx);

            // A mock scene where we also want to simulate moving our focus to top nav / utility nav / some other container
            // to test autorotation properly.
            // By default, we are focused on the hero though
            compose! {
                Column() {
                    Button(text: "Mock TopNav".to_string())
                        .focused_value(focused_nav_signal, FocusedNav::TopNav)
                        .test_id("top-nav")
                    Button(text: "Mock UtilityNav".to_string())
                        .focused_value(focused_nav_signal, FocusedNav::UtilityNav)
                        .test_id("utility-nav")
                    Button(text: "Mock StandardCarousel".to_string())
                        .test_id("mock-standard-carousel")

                    HeroSig(items, size_config, on_item_focus, on_back_pressed: Rc::new(||{}), metadata:create_metadata(ctx.scope()), is_tentpole: false, update_cm, on_item_view: Rc::new(|_,_,_,_| {}), on_item_highlight: Rc::new(|_,_,_| {}))
                        .preferred_focus(true)
                }
            }
        }

        fn refocus_from_hero_to(test_id: &str, game_loop: &mut TestRendererGameLoop) {
            let tree = game_loop.tick_until_done();
            game_loop.send_on_focus_event(tree.find_by_test_id(test_id).get_props().node_id);
            game_loop.tick_until_done();
        }

        #[test]
        fn auto_rotation_happens_only_after_hero_item_visible_signals() {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    test_ctx.game_loop.tick_until_done();
                    test_ctx.assert_focused_index(0);

                    // No matter how long we wait, we stay at 0
                    for _ in 0..10 {
                        test_ctx.advance_time(Duration::from_millis(5_000));
                        test_ctx.assert_focused_index(0);
                    }

                    // Only once the hero is visible do we rotate
                    test_ctx.send_hero_item_visible_event();
                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_focused_index(1);

                    // Again, we need to wait for the signal before it rotates
                    for _ in 0..10 {
                        test_ctx.advance_time(Duration::from_millis(5_000));
                        test_ctx.assert_focused_index(1);
                    }

                    test_ctx.send_hero_item_visible_event();
                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_focused_index(2);
                },
            );
        }

        #[test]
        fn when_focused_on_hero_with_no_trailers_it_should_rotate_every_5s_and_fully() {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                // By default, we are focused on the hero (preferred focus)
                test_should_rotate_every_5s_and_fully,
            )
        }

        #[test]
        fn when_focused_on_top_nav_it_should_rotate_every_5s_and_fully() {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                |scope: Scope, mut game_loop: TestRendererGameLoop| {
                    refocus_from_hero_to("top-nav", &mut game_loop);
                    use_context::<WriteSignal<TopNavMode>>(scope)
                        .unwrap()
                        .set(TopNavMode::TopNav);
                    test_should_rotate_every_5s_and_fully(scope, game_loop)
                },
            )
        }

        #[test]
        fn when_focused_on_utility_nav_it_should_not_rotate() {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                |scope: Scope, mut game_loop: TestRendererGameLoop| {
                    refocus_from_hero_to("utility-nav", &mut game_loop);
                    use_context::<WriteSignal<TopNavMode>>(scope)
                        .unwrap()
                        .set(TopNavMode::TopNav);
                    test_should_not_rotate(scope, game_loop);
                },
            )
        }

        #[test]
        fn when_focused_on_quick_nav_it_should_not_rotate() {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                |scope: Scope, mut game_loop: TestRendererGameLoop| {
                    refocus_from_hero_to("top-nav", &mut game_loop);
                    use_context::<WriteSignal<TopNavMode>>(scope)
                        .unwrap()
                        .set(TopNavMode::QuickNav);
                    test_should_not_rotate(scope, game_loop);
                },
            )
        }

        #[test]
        fn when_focused_on_other_containers_it_should_not_rotate() {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                |scope: Scope, mut game_loop: TestRendererGameLoop| {
                    refocus_from_hero_to("mock-standard-carousel", &mut game_loop);
                    test_should_not_rotate(scope, game_loop);
                },
            )
        }

        #[rstest]
        #[case(KeyCode::Left, 2 - 1)]
        #[case(KeyCode::Right, 2 + 1)]
        #[case(KeyCode::Backspace, 0)]
        #[case(KeyCode::Escape, 0)]
        fn manual_rotation_should_restart_auto_rotation(
            #[case] user_key_pressed: KeyCode,
            #[case] expected_focus_after_press_when_original_focus_was_index_2: usize,
        ) {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    // It would be nice to somehow spy on signals being called, but for now we just test
                    // whether we restart the autorotation session if the user manually rotates via key presses (the only way to manually rotate)
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);
                    test_ctx.send_hero_trailer_playback_finished_event(false);

                    test_ctx.game_loop.tick_until_done();

                    // Normally rotation should happen after every 5s
                    test_ctx.send_hero_item_visible_event();
                    test_ctx.advance_time(Duration::from_millis(5000));
                    test_ctx.assert_focused_index(1);

                    test_ctx.send_hero_item_visible_event();
                    test_ctx.advance_time(Duration::from_millis(5000));
                    test_ctx.assert_focused_index(2);

                    // However, this time after 3s the user presses one of the specified keys to manually rotate to a different hero item
                    test_ctx.advance_time(Duration::from_millis(3000));
                    test_ctx.send_key_down_up_event_to_node(user_key_pressed);
                    test_ctx.assert_focused_index(
                        expected_focus_after_press_when_original_focus_was_index_2,
                    );

                    // The rotation should not happen so soon after a manual rotation even though it was 5s since the last rotation
                    test_ctx.send_hero_item_visible_event();
                    test_ctx.advance_time(Duration::from_millis(2000));
                    test_ctx.assert_focused_index(
                        expected_focus_after_press_when_original_focus_was_index_2,
                    );

                    // Rotation should rather happen 5s after the manual press
                    test_ctx.advance_time(Duration::from_millis(3000));
                    test_ctx.assert_focused_index(
                        expected_focus_after_press_when_original_focus_was_index_2 + 1,
                    );
                },
            )
        }

        #[test]
        fn selecting_primary_button_with_non_svod_acquisition_action_should_not_pause_rotation() {
            launch_test(
                |ctx| {
                    let items = Some(vec![
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            0,
                            Some(generate_aquisition_action(None)),
                            None,
                            false,
                            None,
                            None,
                        ),
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            1,
                            Some(generate_aquisition_action(None)),
                            None,
                            false,
                            None,
                            None,
                        ),
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            2,
                            Some(generate_aquisition_action(None)),
                            None,
                            false,
                            None,
                            None,
                        ),
                    ]);
                    create_test_hero(
                        ctx,
                        TestHeroOption {
                            items,
                            ..TestHeroOption::default()
                        },
                    )
                },
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    let tree = test_ctx.game_loop.tick_until_done();

                    test_ctx.send_hero_item_visible_event();

                    let primary_button = tree.find_by_test_id("primary-button");

                    test_ctx.assert_focused_index(0);

                    test_ctx
                        .game_loop
                        .send_on_select_event(primary_button.get_props().node_id);
                    test_ctx.game_loop.tick_until_done();

                    test_ctx.advance_time(Duration::from_millis(5000));
                    test_ctx.game_loop.tick_until_done();
                    test_ctx.assert_focused_index(1);
                },
            );
        }

        #[test]
        fn selecting_primary_button_with_svod_acquisition_action_should_pause_rotation() {
            launch_test(
                |ctx| {
                    let acquisition_action = Some(generate_aquisition_action(Some(
                        generate_mock_svod_acquisition_metadata("benefit_id".to_string()),
                    )));
                    let items = Some(vec![
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            0,
                            acquisition_action.clone(),
                            None,
                            false,
                            None,
                            None,
                        ),
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            1,
                            acquisition_action.clone(),
                            None,
                            false,
                            None,
                            None,
                        ),
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            2,
                            acquisition_action,
                            None,
                            false,
                            None,
                            None,
                        ),
                    ]);
                    let expected_navigation_location = Some(Location {
                        pageType: PageType::Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE),
                        pageParams: Map::new(),
                    });
                    create_test_hero(
                        ctx,
                        TestHeroOption {
                            items,
                            expected_navigation_location,
                            ..TestHeroOption::default()
                        },
                    )
                },
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    let tree = test_ctx.game_loop.tick_until_done();

                    test_ctx.send_hero_item_visible_event();

                    let primary_button = tree.find_by_test_id("primary-button");

                    test_ctx.assert_focused_index(0);

                    test_ctx
                        .game_loop
                        .send_on_select_event(primary_button.get_props().node_id);
                    test_ctx.game_loop.tick_until_done();

                    test_ctx.advance_time(Duration::from_millis(5000));
                    test_ctx.game_loop.tick_until_done();
                    test_ctx.assert_focused_index(0);
                },
            );
        }

        #[test]
        fn rotation_should_suppress_select_primary_button() {
            launch_test(
                |ctx| {
                    let acquisition_action = Some(generate_aquisition_action(Some(
                        generate_mock_svod_acquisition_metadata("benefit_id".to_string()),
                    )));
                    let items = Some(vec![
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            0,
                            acquisition_action.clone(),
                            None,
                            false,
                            None,
                            None,
                        ),
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            1,
                            acquisition_action.clone(),
                            None,
                            false,
                            None,
                            None,
                        ),
                        generate_mock_hero_card_with_actions(
                            ctx.scope(),
                            2,
                            acquisition_action,
                            None,
                            false,
                            None,
                            None,
                        ),
                    ]);
                    create_test_hero(
                        ctx,
                        TestHeroOption {
                            items,
                            ..TestHeroOption::default()
                        },
                    )
                },
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    let tree = test_ctx.game_loop.tick_until_done();

                    test_ctx.send_hero_item_visible_event();

                    let primary_button = tree.find_by_test_id("primary-button");

                    test_ctx.assert_focused_index(0);

                    // wait for autorotation to start
                    test_ctx.advance_time(Duration::from_millis(4900));
                    test_ctx.game_loop.tick_once();

                    // trigger primary button press during autorotation
                    test_ctx
                        .game_loop
                        .send_on_select_event(primary_button.get_props().node_id);
                    test_ctx.game_loop.tick_once();
                    // wait for navigation
                    // this test should not navigate, but we need to wait to ensure it fails if primary button press is not suppressed
                    test_ctx.advance_time(Duration::from_millis(350));
                    test_ctx.game_loop.tick_until_done();

                    test_ctx.assert_focused_index(1);
                },
            );
        }

        #[test]
        fn navigating_between_buttons_should_pause_rotation() {
            launch_test(
                |ctx| create_test_hero(ctx, TestHeroOption::default()),
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    // It would be nice to somehow spy on signals being called, but for now we just test
                    // whether we restart the autorotation session if the user manually rotates via key presses (the only way to manually rotate)
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);
                    let tree = test_ctx.game_loop.tick_until_done();

                    test_ctx.send_hero_item_visible_event();

                    let primary_button = tree.find_by_test_id("primary-button").get_props();
                    assert!(primary_button.is_focused);
                    test_ctx.game_loop.send_on_focus_event(
                        tree.find_by_test_id("secondary-button").get_props().node_id,
                    );
                    test_ctx.game_loop.tick_until_done();

                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_focused_index(0); // HeroCard

                    // Autorotation can resume after a manual rotation
                    test_ctx.send_key_down_up_event_to_node(KeyCode::Right);

                    // We will auto rotate to a LinkCard without secondary button. So the focus will be on its primary button.
                    // This should not be intepreted as a manual navigation between primary and secondary button and we should continue with autorotation.
                    test_ctx.advance_time(Duration::from_millis(500));
                    test_ctx.assert_focused_index(1); // LinkCard without secondary button

                    test_ctx.send_hero_item_visible_event();
                    test_ctx.advance_time(Duration::from_millis(5_000 + 500));
                    test_ctx.assert_focused_index(2); // LinkCard without secondary button
                },
            );
        }

        #[test]
        fn should_filter_out_interrupted_playback_finish_that_happen_on_manual_rotation() {
            launch_test(
                |ctx| {
                    create_test_hero(
                        ctx,
                        TestHeroOption {
                            has_trailers: true,
                            ..TestHeroOption::default()
                        },
                    )
                },
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    test_ctx.assert_focused_index(0);
                    test_ctx.send_hero_item_visible_event();

                    test_ctx.send_key_down_up_event_to_node(KeyCode::Right);

                    // After 200ms the focus index gets changed
                    MockClock::advance(Duration::from_millis(200));
                    // This event inadvertently gets sent by SuperDraper when the trailer gets interrupted (it can not distinguish between interruption and actual finish)
                    test_ctx.send_hero_trailer_playback_finished_event(true);
                    test_ctx.game_loop.tick_once();
                    test_ctx.assert_focused_index(1);

                    //
                    MockClock::advance(Duration::from_millis(2_000));
                    test_ctx.send_hero_item_visible_event();
                    test_ctx.assert_focused_index(1);
                },
            );
        }
        #[test]
        fn should_auto_rotate_on_trailer_finish() {
            launch_test(
                |ctx| {
                    create_test_hero(
                        ctx,
                        TestHeroOption {
                            has_trailers: true,
                            ..TestHeroOption::default()
                        },
                    )
                },
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    // Initially focused on the first item
                    test_ctx.assert_focused_index(0);
                    test_ctx.game_loop.tick_until_done();

                    // Trailer end response from media background
                    test_ctx.send_hero_trailer_playback_finished_event(true);
                    MockClock::advance(Duration::from_millis(1500));
                    test_ctx.game_loop.tick_until_done();
                    test_ctx.assert_focused_index(1);
                    test_ctx.send_hero_trailer_playback_finished_event(false);
                },
            );
        }

        #[test]
        fn should_not_auto_rotate_if_tts_enabled() {
            launch_test(
                |ctx| {
                    provide_context(
                        ctx.scope(),
                        TTSEnabledContext(create_signal(ctx.scope(), true).0),
                    );

                    create_test_hero(
                        ctx,
                        TestHeroOption {
                            has_trailers: true,
                            ..TestHeroOption::default()
                        },
                    )
                },
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    // Initially focused on the first item
                    test_ctx.assert_focused_index(0);
                    test_ctx.game_loop.tick_until_done();

                    // Trailer end response from media background
                    test_ctx.send_hero_trailer_playback_finished_event(true);
                    MockClock::advance(Duration::from_millis(1500));
                    test_ctx.game_loop.tick_until_done();
                    test_ctx.assert_focused_index(0);
                },
            );
        }

        #[test]
        fn should_not_request_draper_rotation_if_manual_rotation_triggered_during_transition() {
            launch_test(
                |ctx| {
                    create_test_hero(
                        ctx,
                        TestHeroOption {
                            has_trailers: true,
                            ..TestHeroOption::default()
                        },
                    )
                },
                move |scope: Scope, game_loop: TestRendererGameLoop| {
                    let mut test_ctx = AutoRotationTestContext::new(scope, game_loop);

                    // Initially focused on the first item
                    test_ctx.assert_focused_index(0);
                    test_ctx.game_loop.tick_until_done();

                    // Trailer end response from media background
                    test_ctx.send_hero_trailer_playback_finished_event(true);
                    MockClock::advance(Duration::from_millis(1000));
                    // Send key event for manual rotation
                    test_ctx.send_key_down_up_event_to_node(KeyCode::Right);
                    test_ctx.assert_focused_index(1);

                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_focused_index(1);
                },
            );
        }
    }

    mod synopsis_max_lines_rules {
        use super::*;

        struct GenerateOptions {
            title_art: Option<String>,
            title_text: Option<String>,
        }

        // We can't assert on `max_lines`, so we need to assert on the number of lines that are rendered.
        // So we need a long enough synopsis to do so.
        const LONG_ENOUGH_SYNOPSIS_TO_EXHAUST_THE_MAX_LINES: &str = r#"
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut lacinia feugiat risus eu mollis.
        Ut vel mauris volutpat, tincidunt quam rhoncus, ullamcorper magna. Integer gravida quam orci,
        eu pulvinar tellus imperdiet non. Donec in ipsum mi. Nunc vel ligula lobortis, finibus neque at,
         accumsan quam. Nulla dignissim nunc ut porta lacinia. Nulla facilisi. Sed eget lectus nunc.
         In sit amet erat sed lacus ultricies venenatis vitae sit amet sem. Nunc euismod ultricies risus vitae porttitor.
        "#;

        fn generate_items(scope: Scope, opts: GenerateOptions) -> Vec<HeroItemModel> {
            let GenerateOptions {
                title_art,
                title_text,
            } = opts;

            let item = generate_mock_hero_card(scope, 0, false);
            item.ui_data.update_untracked(|hero_item| {
                hero_item.synopsis.set(Some(
                    LONG_ENOUGH_SYNOPSIS_TO_EXHAUST_THE_MAX_LINES.to_owned(),
                ));
                hero_item.title_data.update_untracked(|title| {
                    title.title_text = title_text.unwrap_or_default();
                    title.title_art_url = title_art;
                });
            });
            vec![item]
        }

        const SHORT_FALLBACK_TITLE: Option<&'static str> = Some("exactly 24 characters !!");
        const LONG_FALLBACK_TITLE: Option<&'static str> =
            Some("more than 24 characters and mock title");

        // Always collapsed has max lines 2 in any case
        // Always expanded (e.g. tentpole hero) should honor this custom rule
        // Expandable (but expanded) should also honor this rule
        // Expandable (but collapsed) -> we shouldn't find a synopsis (it's tested elsewhere)
        #[rstest]
        #[case(|_| SizeConfig::AlwaysCollapsed, true, None, 2)]
        #[case(|_| SizeConfig::AlwaysCollapsed, true, LONG_FALLBACK_TITLE, 2)]
        #[case(|_| SizeConfig::AlwaysCollapsed, false, LONG_FALLBACK_TITLE, 2)]
        #[case(|_| SizeConfig::AlwaysCollapsed, false, SHORT_FALLBACK_TITLE, 2)]
        #[case(|_| SizeConfig::AlwaysExpanded, true, None, 3)]
        #[case(|_| SizeConfig::AlwaysExpanded, true, LONG_FALLBACK_TITLE, 3)]
        #[case(|_| SizeConfig::AlwaysExpanded, false, LONG_FALLBACK_TITLE, 2)]
        #[case(|_| SizeConfig::AlwaysExpanded, false, SHORT_FALLBACK_TITLE, 3)]
        #[case(expandable_but_always_expanded, true, None, 3)]
        #[case(expandable_but_always_expanded, true, LONG_FALLBACK_TITLE, 3)]
        #[case(expandable_but_always_expanded, false, LONG_FALLBACK_TITLE, 2)]
        #[case(expandable_but_always_expanded, false, SHORT_FALLBACK_TITLE, 3)]
        fn max_lines_rules_should_be_honored(
            #[case] size_config: impl Fn(Scope) -> SizeConfig + 'static,
            #[case] title_art_exists: bool,
            #[case] title_text: Option<&'static str>,
            #[case] expected_lines: usize,
        ) {
            launch_test(
                move |ctx| {
                    create_test_hero(
                        &ctx,
                        CreateTestHeroProps {
                            items: generate_items(
                                ctx.scope(),
                                GenerateOptions {
                                    title_art: title_art_exists
                                        .then_some("mock title art url".to_string()),
                                    title_text: title_text.map(|t| t.to_string()),
                                },
                            ),
                            size_config: size_config(ctx.scope()),
                            ..CreateTestHeroProps::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let tree = test_game_loop.tick_until_done();
                    let synopsis_row = tree.find_by_test_id("synopsis-row");
                    let synopsis_node = synopsis_row
                        .find_any_child_with()
                        .test_id(TYPOGRAPHY_TEST_ID)
                        .find_first();
                    let props = synopsis_node.borrow_props();
                    let text_layout = props.text_layout.clone().unwrap();
                    assert_eq!(text_layout.num_lines, expected_lines);
                },
            );
        }
    }

    mod clickstream {
        #[ignore]
        #[test]
        fn report_clickstream_on_select() {
            todo!("Tech debt")
        }

        #[ignore]
        #[test]
        fn report_clickstream_on_scroll() {
            todo!("Tech debt")
        }
    }

    #[test]
    fn on_expansion() {
        launch_test(
            |ctx| {
                let expanded = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), expanded);
                let hero_trailer_context = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_context);
                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), false),
                        size_config: SizeConfig::Expandable(expanded.into()),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let parent = tree.find_by_test_id(HERO_CARD_TEST_ID);
                let secondary_button_row = tree.find_by_test_id("secondary-button-row");
                let synopsis_row = tree.find_by_test_id("synopsis-row");

                assert_eq!(parent.borrow_props().layout.size.height, 626.0);
                assert_eq!(
                    secondary_button_row.borrow_props().base_styles.opacity,
                    Some(0.0f32)
                );
                assert_eq!(
                    synopsis_row.borrow_props().base_styles.opacity,
                    Some(0.0f32)
                );
                let hero_trailer_context = use_context::<TrailerPlaybackContext>(scope).unwrap();
                let expanded = use_context::<RwSignal<bool>>(scope).unwrap();

                expanded.set(true);

                let tree = test_game_loop.tick_until_done();
                let parent = tree.find_by_test_id(HERO_CARD_TEST_ID);
                let secondary_button_row = tree.find_by_test_id("secondary-button-row");
                let synopsis_row = tree.find_by_test_id("synopsis-row");
                assert!(hero_trailer_context.should_play_hero.get_untracked());
                assert_eq!(parent.borrow_props().layout.size.height, 876.0);
                assert_eq!(
                    secondary_button_row.borrow_props().base_styles.opacity,
                    Some(1.0f32)
                );
                assert_eq!(
                    synopsis_row.borrow_props().base_styles.opacity,
                    Some(1.0f32)
                );
            },
        )
    }

    #[test]
    fn on_collapse() {
        launch_test(
            |ctx| {
                let expanded = create_rw_signal(ctx.scope(), true);
                provide_context(ctx.scope(), expanded);

                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), true),
                        size_config: SizeConfig::Expandable(expanded.into()),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let parent = tree.find_by_test_id(HERO_CARD_TEST_ID);
                let secondary_button_row = tree.find_by_test_id("secondary-button-row");
                let synopsis_row = tree.find_by_test_id("synopsis-row");

                assert_eq!(parent.borrow_props().layout.size.height, 876.0);
                assert_eq!(
                    secondary_button_row.borrow_props().base_styles.opacity,
                    Some(1.0f32)
                );
                assert_eq!(
                    synopsis_row.borrow_props().base_styles.opacity,
                    Some(1.0f32)
                );
                let expanded = use_context::<RwSignal<bool>>(scope).unwrap();
                expanded.set(false);

                let tree = test_game_loop.tick_until_done();
                let parent = tree.find_by_test_id(HERO_CARD_TEST_ID);
                let secondary_button_row = tree.find_by_test_id("secondary-button-row");
                let synopsis_row = tree.find_by_test_id("synopsis-row");
                assert_eq!(parent.borrow_props().layout.size.height, 626.0);
                assert_eq!(
                    secondary_button_row.borrow_props().base_styles.opacity,
                    Some(0.0f32)
                );
                assert_eq!(
                    synopsis_row.borrow_props().base_styles.opacity,
                    Some(0.0f32)
                );
            },
        )
    }

    fn validate_on_item(i: usize, tree: &SceneNodeTree, mock_data: &Vec<HeroItemModel>) {
        // compare the first 50 characters because we don't know where it will be truncated.
        let expected_synopsis = mock_data[i]
            .ui_data
            .get_untracked()
            .synopsis
            .get_untracked()
            .as_ref()
            .map(|text| text.chars().take(50).collect::<String>());
        let synopsis_row = tree.find_by_test_id("synopsis-row");

        if expected_synopsis.is_some() {
            let synopsis_node = synopsis_row
                .find_any_child_with()
                .test_id(TYPOGRAPHY_TEST_ID)
                .find_first();

            let synopsis = synopsis_node
                .borrow_props()
                .text
                .as_ref()
                .map(|text| text.chars().take(50).collect::<String>());
            assert_eq!(synopsis, expected_synopsis);
        } else {
            assert_node_does_not_exist!(synopsis_row);
        }
    }

    mod manual_rotation {
        use super::*;
        use ignx_compositron::reactive::StoredValue;

        #[derive(Clone, PartialEq, Debug)]
        struct OnItemFocusCtx {
            idx: Option<usize>,
            call_times: usize,
        }

        fn mock_on_item_focus(scope: Scope) -> Rc<dyn Fn(usize)> {
            let on_item_focus_ctx = store_value(
                scope,
                OnItemFocusCtx {
                    idx: None,
                    call_times: 0,
                },
            );

            let on_item_focus = Rc::new(move |idx: usize| {
                on_item_focus_ctx.update_value(|pair| {
                    pair.idx = Some(idx);
                    pair.call_times += 1;
                });
            });

            provide_context::<StoredValue<OnItemFocusCtx>>(scope, on_item_focus_ctx);

            on_item_focus
        }
        #[test]
        fn should_not_rotate_for_one_item_only() {
            launch_test(
                |ctx| {
                    let items = Vec::from(&generate_mock_data(ctx.scope(), false)[..1]);
                    let on_item_focus = mock_on_item_focus(ctx.scope());

                    assert_eq!(items.len(), 1);

                    let props = CreateTestHeroProps {
                        items,
                        on_item_focus: Some(on_item_focus),
                        ..CreateTestHeroProps::default()
                    };
                    create_test_hero(&ctx, props)
                },
                |scope, mut test_game_loop| {
                    let mut tree = test_game_loop.tick_until_done();
                    let hero_id = tree
                        .find_by_test_id(HERO_CARD_TEST_ID)
                        .borrow_props()
                        .node_id;

                    let focus_ctx = expect_context::<StoredValue<OnItemFocusCtx>>(scope);
                    // OnItemFocus should be called once at the initial render
                    assert_eq!(
                        focus_ctx.get_value(),
                        OnItemFocusCtx {
                            idx: Some(0),
                            call_times: 1
                        }
                    );

                    test_game_loop.send_key_down_up_event_to_node(hero_id, KeyCode::Right);
                    tree = test_game_loop.tick_until_done();
                    tree = run_animation(&mut test_game_loop);
                    assert_eq!(
                        focus_ctx.get_value(),
                        OnItemFocusCtx {
                            idx: Some(0),
                            call_times: 1
                        }
                    );

                    test_game_loop.send_key_down_up_event_to_node(hero_id, KeyCode::Left);
                    tree = test_game_loop.tick_until_done();
                    tree = run_animation(&mut test_game_loop);
                    assert_eq!(
                        focus_ctx.get_value(),
                        OnItemFocusCtx {
                            idx: Some(0),
                            call_times: 1
                        }
                    );
                },
            );
        }

        #[test]
        fn full_rotation_tests() {
            launch_test(
                |ctx| {
                    let items = generate_mock_data(ctx.scope(), false);
                    let on_item_focus = mock_on_item_focus(ctx.scope());

                    let props = CreateTestHeroProps {
                        items,
                        on_item_focus: Some(on_item_focus),
                        ..CreateTestHeroProps::default()
                    };
                    create_test_hero(&ctx, props)
                },
                |scope, mut test_game_loop| {
                    let mock_data = generate_mock_data(scope, true);
                    let mut tree = test_game_loop.tick_until_done();
                    let hero_id = tree
                        .find_by_test_id(HERO_CARD_TEST_ID)
                        .borrow_props()
                        .node_id;
                    let on_item_focus_ctx = expect_context::<StoredValue<OnItemFocusCtx>>(scope);

                    let mut expected_call_times = 1;

                    for (i, _) in mock_data.iter().enumerate() {
                        validate_on_item(i, &tree, &mock_data);
                        assert_eq!(
                            on_item_focus_ctx.get_value(),
                            OnItemFocusCtx {
                                idx: Some(i),
                                call_times: expected_call_times
                            }
                        );

                        test_game_loop.send_key_down_up_event_to_node(hero_id, KeyCode::Right);
                        tree = test_game_loop.tick_until_done();
                        tree = run_animation(&mut test_game_loop);
                        expected_call_times += 1;
                    }

                    // clicking again at the end should return to the beginning.
                    validate_on_item(0, &tree, &mock_data);
                    assert_eq!(
                        on_item_focus_ctx.get_value(),
                        OnItemFocusCtx {
                            idx: Some(0),
                            call_times: expected_call_times
                        }
                    );

                    // clicking left from beginning should do nothing, not even animate
                    test_game_loop.send_key_down_up_event_to_node(hero_id, KeyCode::Left);
                    tree = test_game_loop.tick_until_done();
                    tree = run_animation(&mut test_game_loop);
                    validate_on_item(0, &tree, &mock_data);

                    assert_eq!(
                        on_item_focus_ctx.get_value(),
                        OnItemFocusCtx {
                            idx: Some(0),
                            call_times: expected_call_times // This should stay the same, otherwise there might have been an unexpected animation or on_item_focus call
                        }
                    );

                    // go right and then left to validate left rotation.
                    test_game_loop.send_key_down_up_event_to_node(hero_id, KeyCode::Right);
                    tree = test_game_loop.tick_until_done();
                    tree = run_animation(&mut test_game_loop);
                    validate_on_item(1, &tree, &mock_data);

                    expected_call_times += 1;
                    assert_eq!(
                        on_item_focus_ctx.get_value(),
                        OnItemFocusCtx {
                            idx: Some(1),
                            call_times: expected_call_times,
                        }
                    );

                    test_game_loop.send_key_down_up_event_to_node(hero_id, KeyCode::Left);
                    tree = test_game_loop.tick_until_done();
                    tree = run_animation(&mut test_game_loop);
                    validate_on_item(0, &tree, &mock_data);

                    expected_call_times += 1;
                    assert_eq!(
                        on_item_focus_ctx.get_value(),
                        OnItemFocusCtx {
                            idx: Some(0),
                            call_times: expected_call_times,
                        }
                    );
                },
            )
        }
    }

    #[test]
    fn has_default_focus() {
        launch_test(
            |ctx| {
                let (default_focus, set_default_focus) = create_signal(ctx.scope(), 2);
                provide_context(ctx.scope(), set_default_focus);

                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), false),
                        size_config: expandable_but_always(ctx.scope(), true),
                        default_focus_index: Some(default_focus.into()),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let mock_data = generate_mock_data(scope, true);
                let mut tree = test_game_loop.tick_until_done();

                // should be on index 2.
                validate_on_item(2, &tree, &mock_data);

                let set_default_focus = use_context::<WriteSignal<usize>>(scope).unwrap();
                set_default_focus.set(3);

                tree = test_game_loop.tick_until_done();

                // should be on index 3.
                validate_on_item(3, &tree, &mock_data);
            },
        )
    }

    // In the initial Remaster launch we would always focus on the Primary button when going from TopNav to Hero, but there is an experiment
    // to change this default focus to the Secondary button.
    #[rstest]
    #[case(false)]
    #[case(true)]
    fn should_focus_on_correct_default_button_when_hero_was_collapsed_or_has_been_collapsed(
        #[case] hero_initial_focus_swap_enabled: bool,
    ) {
        launch_test(
            move |ctx| {
                let expanded = create_rw_signal(ctx.scope(), false);
                let top_nav_on_focus = move || {
                    expanded.set(false);
                };
                let item_builder = Box::new(move |ctx: &AppContext| {
                    let on_focus = Rc::new(move || {
                        expanded.set(true);
                    });
                    let rust_features = MockRustFeaturesBuilder::new()
                        .set_is_hero_initial_swap_enabled(hero_initial_focus_swap_enabled)
                        .build();
                    let props = CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), true),
                        size_config: SizeConfig::Expandable(expanded.into()),
                        on_focus: Some(on_focus),
                        features: Some(rust_features),
                        ..Default::default()
                    };
                    Some(create_test_hero(ctx, props))
                });

                compose! {
                    Column() {
                        Button(text: "Mock TopNav".to_string())
                            .on_focus(top_nav_on_focus)
                            .test_id("top-nav")
                            .preferred_focus(true)
                        Memo(item_builder)
                    }
                }
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();

                let top_nav = tree.find_by_test_id("top-nav");
                let hero = tree.find_by_test_id(HERO_CARD_TEST_ID);
                assert!(top_nav.borrow_props().is_focused);

                test_game_loop.send_on_focus_event(hero.borrow_props().node_id);

                let assert_correct_button_focused = |test_game_loop: &mut TestRendererGameLoop| {
                    let tree = test_game_loop.tick_until_done();
                    let primary_button = tree.find_by_test_id("primary-button");
                    let secondary_button = tree.find_by_test_id("secondary-button");

                    if !hero_initial_focus_swap_enabled {
                        assert!(primary_button.borrow_props().is_focused);
                        assert!(!secondary_button.borrow_props().is_focused);
                    } else {
                        assert!(!primary_button.borrow_props().is_focused);
                        assert!(secondary_button.borrow_props().is_focused);
                    }
                };

                assert_correct_button_focused(&mut test_game_loop);

                // Now focus on the other unfocused button
                let other_unfocused_button_test_id = if hero_initial_focus_swap_enabled {
                    "primary-button"
                } else {
                    "secondary-button"
                };
                test_game_loop.send_on_focus_event(
                    tree.find_by_test_id(other_unfocused_button_test_id)
                        .borrow_props()
                        .node_id,
                );
                let _ = test_game_loop.tick_until_done();

                // Going back to TopNav then to Hero should reapply the same default focus again regardless
                // of what was focused on last time
                test_game_loop
                    .send_on_focus_event(tree.find_by_test_id("top-nav").borrow_props().node_id);
                let _ = test_game_loop.tick_until_done();

                test_game_loop.send_on_focus_event(
                    tree.find_by_test_id(HERO_CARD_TEST_ID)
                        .borrow_props()
                        .node_id,
                );

                assert_correct_button_focused(&mut test_game_loop);
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Backspace)]
    #[case(KeyCode::Escape)]
    fn on_back_pressed(#[case] key: KeyCode) {
        launch_test(
            |ctx| {
                let back_pressed_signal = create_rw_signal(ctx.scope(), "no");
                let on_back_pressed = Rc::new(move || back_pressed_signal.set("yes"));
                provide_context(ctx.scope(), back_pressed_signal);

                create_test_hero(
                    &ctx,
                    CreateTestHeroProps {
                        items: generate_mock_data(ctx.scope(), true),
                        size_config: expandable_but_always(ctx.scope(), true),
                        on_back_pressed: Some(on_back_pressed),
                        ..CreateTestHeroProps::default()
                    },
                )
            },
            |scope, mut test_game_loop| {
                let mock_data = generate_mock_data(scope, true);
                let mut tree = test_game_loop.tick_until_done();
                let hero = tree
                    .find_by_test_id(HERO_CARD_TEST_ID)
                    .borrow_props()
                    .node_id;

                let back_pressed: RwSignal<&str> = use_context(scope).unwrap();
                assert_eq!(back_pressed.get_untracked(), "no");

                test_game_loop.send_key_down_up_event_to_node(hero, KeyCode::Right);
                tree = test_game_loop.tick_until_done();
                tree = run_animation(&mut test_game_loop);

                validate_on_item(1, &tree, &mock_data);

                test_game_loop.send_key_down_up_event_to_node(hero, key);

                tree = test_game_loop.tick_until_done();
                tree = run_animation(&mut test_game_loop);

                assert_eq!(back_pressed.get_untracked(), "no");
                validate_on_item(0, &tree, &mock_data);

                test_game_loop.send_key_down_up_event_to_node(hero, KeyCode::Backspace);

                tree = test_game_loop.tick_until_done();
                assert_eq!(back_pressed.get_untracked(), "yes");
            },
        )
    }

    mod csm {
        use super::*;

        fn verify_view_impression_call(
            signal: RwSignal<Option<(ViewImpressionData, HeroItemModel, usize)>>,
            expected_time_to_view_time: Duration,
            expected_item_model: &HeroItemModel,
            expected_index: usize,
        ) {
            signal.with(|data| {
                assert!(data.is_some());

                let data = data.clone().unwrap();
                assert_eq!(data.0.time_to_view, expected_time_to_view_time);
                assert_eq!(data.1.id, expected_item_model.id);
                assert_eq!(data.2, expected_index);
            });
        }

        fn verify_highlight_impression_call(
            signal: RwSignal<Option<(HeroItemModel, usize)>>,
            expected_item_model: &HeroItemModel,
            expected_index: usize,
        ) {
            signal.with(|data| {
                assert!(data.is_some());

                let data = data.clone().unwrap();
                assert_eq!(data.0.id, expected_item_model.id);
                assert_eq!(data.1, expected_index);
            });
        }

        #[test]
        fn sends_view_event_for_items() {
            launch_test(
                |ctx| {
                    let background_state = create_rw_signal(
                        ctx.scope(),
                        Some(MediaBackgroundStatus {
                            id: "mb_id_0".to_string(),
                            background_state: BackgroundState::Downloaded,
                        }),
                    );
                    let mb_ready_context = MediaBackgroundStatusContext {
                        background_state: background_state.into(),
                        player_resize_support: create_rw_signal(ctx.scope(), None).into(),
                    };
                    provide_context::<MediaBackgroundStatusContext>(ctx.scope(), mb_ready_context);

                    let on_item_view_called_times = create_rw_signal(ctx.scope(), 0);
                    let on_item_view_last_call: RwSignal<
                        Option<(ViewImpressionData, HeroItemModel, usize)>,
                    > = create_rw_signal(ctx.scope(), None);

                    provide_context(ctx.scope(), on_item_view_last_call);
                    provide_context(ctx.scope(), on_item_view_called_times);

                    let on_item_view = Rc::new(
                        move |view_data: ViewImpressionData,
                              item: RwSignal<HeroItemModel>,
                              index: usize,
                              _| {
                            on_item_view_called_times.set(on_item_view_called_times.get() + 1);
                            on_item_view_last_call.set(Some((view_data, item.get(), index)));
                        },
                    );

                    create_test_hero(
                        &ctx,
                        CreateTestHeroProps {
                            items: generate_mock_data(ctx.scope(), true),
                            size_config: expandable_but_always(ctx.scope(), true),
                            on_item_view: Some(on_item_view),
                            ..CreateTestHeroProps::default()
                        },
                    )
                },
                |scope, mut test_game_loop| {
                    let mock_data = generate_mock_data(scope, true);
                    let _ = test_game_loop.tick_until_done();

                    let on_item_view_called_times = use_context::<RwSignal<i32>>(scope).unwrap();
                    let on_item_view_last_call: RwSignal<
                        Option<(ViewImpressionData, HeroItemModel, usize)>,
                    > = use_context::<RwSignal<Option<(ViewImpressionData, HeroItemModel, usize)>>>(
                        scope,
                    )
                        .unwrap();

                    MockClock::advance(Duration::from_millis(1000));
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_view_called_times.get(), 0);
                    assert!(on_item_view_last_call.get().is_none());

                    MockClock::advance(Duration::from_millis(1));
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_view_called_times.get(), 1);
                    verify_view_impression_call(
                        on_item_view_last_call,
                        Duration::from_millis(1001),
                        &mock_data[0],
                        0,
                    );

                    // do auto rotation.
                    MockClock::advance(Duration::from_millis(6000));
                    let _ = test_game_loop.tick_until_done();
                    run_animation(&mut test_game_loop);
                    assert_eq!(on_item_view_called_times.get(), 1);

                    MockClock::advance(Duration::from_secs(1));
                    // seems to be a bug in the SDK that means we need to tick several times.
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_view_called_times.get(), 2);
                    verify_view_impression_call(
                        on_item_view_last_call,
                        Duration::from_millis(8401),
                        &mock_data[1],
                        1,
                    );

                    // do manual rotation.
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Right);
                    let _ = test_game_loop.tick_until_done();
                    run_animation(&mut test_game_loop);

                    assert_eq!(on_item_view_called_times.get(), 2);

                    MockClock::advance(Duration::from_secs(1));
                    // seems to be a bug in the SDK that means we need to tick several times.
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_view_called_times.get(), 3);
                    verify_view_impression_call(
                        on_item_view_last_call,
                        Duration::from_millis(9801),
                        &mock_data[2],
                        2,
                    );
                },
            )
        }

        #[test]
        fn sends_highlight_event_for_items() {
            launch_test(
                |ctx| {
                    let background_state = create_rw_signal(
                        ctx.scope(),
                        Some(MediaBackgroundStatus {
                            id: "mb_id_0".to_string(),
                            background_state: BackgroundState::Downloaded,
                        }),
                    );
                    let mb_ready_context = MediaBackgroundStatusContext {
                        background_state: background_state.into(),
                        player_resize_support: create_rw_signal(ctx.scope(), None).into(),
                    };
                    provide_context::<MediaBackgroundStatusContext>(ctx.scope(), mb_ready_context);

                    let on_item_highlight_called_times = create_rw_signal(ctx.scope(), 0);
                    let on_item_highlight_last_call: RwSignal<Option<(HeroItemModel, usize)>> =
                        create_rw_signal(ctx.scope(), None);

                    provide_context(ctx.scope(), on_item_highlight_last_call);
                    provide_context(ctx.scope(), on_item_highlight_called_times);

                    let on_item_highlight =
                        Rc::new(move |item: RwSignal<HeroItemModel>, index: usize, _| {
                            on_item_highlight_called_times
                                .set(on_item_highlight_called_times.get() + 1);
                            on_item_highlight_last_call.set(Some((item.get(), index)));
                        });

                    create_test_hero(
                        &ctx,
                        CreateTestHeroProps {
                            items: generate_mock_data(ctx.scope(), true),
                            size_config: expandable_but_always(ctx.scope(), true),
                            on_item_highlight: Some(on_item_highlight),
                            ..CreateTestHeroProps::default()
                        },
                    )
                },
                |scope, mut test_game_loop| {
                    let mock_data = generate_mock_data(scope, true);
                    let _ = test_game_loop.tick_until_done();

                    let on_item_highlight_called_times =
                        use_context::<RwSignal<i32>>(scope).unwrap();
                    let on_item_highlight_last_call: RwSignal<Option<(HeroItemModel, usize)>> =
                        use_context::<RwSignal<Option<(HeroItemModel, usize)>>>(scope).unwrap();

                    MockClock::advance(Duration::from_millis(1000));
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_highlight_called_times.get(), 0);
                    assert!(on_item_highlight_last_call.get().is_none());

                    MockClock::advance(Duration::from_millis(1));
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_highlight_called_times.get(), 1);
                    verify_highlight_impression_call(on_item_highlight_last_call, &mock_data[0], 0);

                    // do auto rotation.
                    MockClock::advance(Duration::from_millis(6000));
                    let _ = test_game_loop.tick_until_done();
                    run_animation(&mut test_game_loop);

                    assert_eq!(on_item_highlight_called_times.get(), 1);

                    MockClock::advance(Duration::from_secs(1));
                    // seems to be a bug in the SDK that means we need to tick several times.
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_highlight_called_times.get(), 2);
                    verify_highlight_impression_call(on_item_highlight_last_call, &mock_data[1], 1);

                    // do manual rotation.
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Right);
                    let _ = test_game_loop.tick_until_done();
                    run_animation(&mut test_game_loop);

                    assert_eq!(on_item_highlight_called_times.get(), 2);

                    MockClock::advance(Duration::from_secs(1));
                    // seems to be a bug in the SDK that means we need to tick several times.
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(on_item_highlight_called_times.get(), 3);
                    verify_highlight_impression_call(on_item_highlight_last_call, &mock_data[2], 2);
                },
            )
        }

        #[test]
        fn sends_select_event_for_button_presses() {
            launch_test(
                |ctx| {
                    let items = generate_mock_data(ctx.scope(), true);
                    provide_context(ctx.scope(), items.clone());

                    let on_item_select_called_times = create_rw_signal(ctx.scope(), 0);
                    let on_item_select_last_call: RwSignal<
                        Option<(HeroItemModel, usize, ButtonVariant)>,
                    > = create_rw_signal(ctx.scope(), None);
                    let on_item_select = Rc::new(
                        move |item: RwSignal<HeroItemModel>,
                              index: usize,
                              _,
                              button_variant: ButtonVariant| {
                            on_item_select_called_times.update(|x| *x += 1);
                            on_item_select_last_call.set(Some((
                                item.get_untracked(),
                                index,
                                button_variant,
                            )));
                        },
                    );
                    provide_context(ctx.scope(), on_item_select_called_times);
                    provide_context(ctx.scope(), on_item_select_last_call);

                    let props = CreateTestHeroProps {
                        items,
                        on_item_select: Some(on_item_select),
                        ..CreateTestHeroProps::default()
                    };
                    create_test_hero(&ctx, props)
                },
                |scope, mut test_game_loop| {
                    let tree = test_game_loop.tick_until_done();
                    let items = expect_context::<Vec<HeroItemModel>>(scope);
                    let on_item_select_called_times = use_context::<RwSignal<i32>>(scope).unwrap();
                    let on_item_select_last_call = use_context::<
                        RwSignal<Option<(HeroItemModel, usize, ButtonVariant)>>,
                    >(scope)
                    .unwrap();

                    // Selecting Secondary Button of first hero item
                    let btn = tree
                        .find_by_test_id("secondary-button")
                        .borrow_props()
                        .node_id;
                    test_game_loop.send_on_select_event(btn);
                    let _ = test_game_loop.tick_until_done();
                    assert_eq!(on_item_select_called_times.get_untracked(), 1);

                    let last_call = on_item_select_last_call.get_untracked().unwrap();
                    assert_eq!(last_call.0.id, items[0].id);
                    assert_eq!(last_call.1, 0);
                    assert_eq!(last_call.2, ButtonVariant::Secondary);

                    // Manual rotation
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Right);
                    let _ = test_game_loop.tick_until_done();
                    run_animation(&mut test_game_loop);

                    // Selecting Primary Button of second hero item (which is a LinkCard without secondary button)
                    let btn = tree
                        .find_by_test_id("primary-button")
                        .borrow_props()
                        .node_id;
                    test_game_loop.send_on_select_event(btn);
                    let _ = test_game_loop.tick_until_done();
                    assert_eq!(on_item_select_called_times.get_untracked(), 2);

                    let last_call = on_item_select_last_call.get_untracked().unwrap();
                    assert_eq!(last_call.0.id, items[1].id);
                    assert_eq!(last_call.1, 1);
                    assert_eq!(last_call.2, ButtonVariant::Primary);
                },
            )
        }
    }

    fn run_animation(test_game_loop: &mut TestRendererGameLoop) -> SceneNodeTree {
        MockClock::advance(Duration::from_millis(200));
        let _ = test_game_loop.tick_until_done();
        MockClock::advance(Duration::from_millis(200));
        test_game_loop.tick_until_done()
    }

    mod default_focus_experiment {
        use super::*;

        #[rstest]
        #[case(WeblabTreatmentString::C, false)]
        #[case(WeblabTreatmentString::T1, false)]
        #[case(WeblabTreatmentString::T2, true)]
        fn renders_buttons_and_progress_bars_correctly(
            #[case] weblab_treatment: WeblabTreatmentString,
            #[case] buttons_flipped: bool,
        ) {
            launch_test(
                |ctx| {
                    let mock_rust_features = MockRustFeaturesBuilder::new()
                        .set_home_default_focus_experiment_treatment(weblab_treatment)
                        .build();

                    let items = generate_mock_data(ctx.scope(), true);
                    provide_context(ctx.scope(), items.clone());

                    let props = CreateTestHeroProps {
                        items,
                        features: Some(mock_rust_features),
                        ..CreateTestHeroProps::default()
                    };
                    create_test_hero(&ctx, props)
                },
                move |_scope, mut test_game_loop| {
                    let tree = test_game_loop.tick_until_done();

                    let left_column = tree.find_by_test_id("hero-left-column");
                    assert_node_exists!(&left_column);

                    let top_button_row = left_column
                        .find_child_with()
                        .test_id("top-button-row")
                        .find_first();

                    assert_node_exists!(&top_button_row);

                    let top_primary_button_row = top_button_row
                        .find_child_with()
                        .test_id("primary-button-row")
                        .find_first();

                    if buttons_flipped {
                        assert_node_exists!(&top_primary_button_row);
                    } else {
                        assert_node_does_not_exist!(&top_primary_button_row);
                    }

                    let top_secondary_button_row = top_button_row
                        .find_child_with()
                        .test_id("secondary-button-row")
                        .find_first();

                    if buttons_flipped {
                        assert_node_does_not_exist!(&top_secondary_button_row);
                    } else {
                        assert_node_exists!(&top_secondary_button_row);
                    }

                    let bottom_button_column = left_column
                        .find_child_with()
                        .test_id("bottom-button-column")
                        .find_first();

                    assert_node_exists!(&bottom_button_column);

                    let bottom_primary_button_row = bottom_button_column
                        .find_child_with()
                        .test_id("primary-button-row")
                        .find_first();

                    if buttons_flipped {
                        assert_node_does_not_exist!(&bottom_primary_button_row);
                    } else {
                        assert_node_exists!(&bottom_primary_button_row);
                    }

                    let bottom_secondary_button_row = bottom_button_column
                        .find_child_with()
                        .test_id("secondary-button-row")
                        .find_first();

                    if buttons_flipped {
                        assert_node_exists!(&bottom_secondary_button_row);
                    } else {
                        assert_node_does_not_exist!(&bottom_secondary_button_row);
                    }

                    let progress_bar_percentage = bottom_button_column
                        .find_child_with()
                        .test_id(PROGRESS_BAR_TEST_ID)
                        .find_first();

                    if buttons_flipped {
                        assert_node_does_not_exist!(&progress_bar_percentage);
                    } else {
                        assert_node_exists!(&progress_bar_percentage);
                    }
                },
            )
        }
    }
}
