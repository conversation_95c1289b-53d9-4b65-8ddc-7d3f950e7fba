pub(crate) mod auto_rotation;

pub(crate) mod types;

pub(crate) mod utils;

pub(crate) mod hero_sig;
#[cfg(test)]
pub(crate) mod mock_data;

pub mod standard_hero_sig;
pub mod tentpole_hero_sig;

pub use hero_sig::HERO_CARD_TEST_ID;

pub mod buttons {
    pub use crate::types::ButtonVariant;
}

pub mod config {
    pub use crate::types::SizeConfig;
}

pub use crate::types::HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy;
pub use crate::types::HeroItemMediaStrategy;
