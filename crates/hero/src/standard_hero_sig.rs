use crate::hero_sig::*;
use crate::types::{ButtonVariant, SizeConfig};
use crate::HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy;
use container_types::ui_signals::{HeroItemModel, StandardHeroModel};
use contextual_menu_types::prelude::ContextualMenuData;
use fableous::cards::sizing::CardDimensions;
use ignx_compositron::focus::FocusEdges;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, compose_option, Composer};
use std::rc::Rc;

pub const STANDARD_HERO_TEST_ID: &str = "standard-hero";

#[Composer]
pub fn StandardHeroSig(
    ctx: &AppContext,
    #[into] data: RwSignal<StandardHeroModel>,
    size_config: SizeConfig,
    on_item_focus: Rc<dyn Fn(HeroItemModel, usize)>,
    open_item_contextual_menu: Rc<dyn Fn(HeroItemModel, usize, ButtonVariant)>,
    on_back_pressed: Rc<dyn Fn()>,
    on_item_view: Rc<dyn Fn(ViewImpressionData, RwSignal<HeroItemModel>, usize, CardDimensions)>,
    on_item_highlight: Rc<dyn Fn(RwSignal<HeroItemModel>, usize, CardDimensions)>,
    #[optional = Rc::new(move |_,_,_,_| {})] on_item_select: Rc<
        dyn Fn(RwSignal<HeroItemModel>, usize, Option<CardDimensions>, ButtonVariant),
    >,
    #[into]
    #[optional = 0]
    default_focus_index: MaybeSignal<usize>,
    #[optional = Rc::new(|| {})] on_move_to_second_container: Rc<dyn Fn()>,
    on_ready: Rc<dyn Fn()>,
    update_cm: WriteSignal<Option<ContextualMenuData>>,
    #[optional = None] auto_rotation_wait_duration_override: Option<
        HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy,
    >,
) -> ColumnComposable {
    let blocked_edges = create_rw_signal(ctx.scope(), FocusEdges::END);

    // Wrapping function that turns the index into a StandardHeroItem.
    let on_item_focus = {
        let on_item_focus = on_item_focus.clone();

        Rc::new(move |index: usize| {
            let edges = match index {
                0 => FocusEdges::END,
                _ => FocusEdges::START | FocusEdges::END,
            };
            blocked_edges.set(edges);
            let item = data.with_untracked(|data| {
                data.items.with_untracked(|items| items.get(index).cloned())
            });
            if let Some(item) = item {
                on_item_focus(item, index);
            }
        })
    };

    // Wrapping function that turns the index into a StandardHeroItem.
    let open_item_contextual_menu = {
        let on_long_press = open_item_contextual_menu.clone();
        Rc::new(move |index: usize, button: ButtonVariant| {
            let item = data.with_untracked(|data| {
                data.items.with_untracked(|items| items.get(index).cloned())
            });
            if let Some(item) = item {
                on_long_press(item, index, button);
            }
        })
    };

    compose! {
        Column() {
            Memo(item_builder: Box::new(move |ctx| {
                let on_item_focus = Rc::clone(&on_item_focus);
                let on_back_pressed = Rc::clone(&on_back_pressed);
                let open_item_contextual_menu = Rc::clone(&open_item_contextual_menu);
                let on_move_to_second_container = on_move_to_second_container.clone();
                let on_ready = Rc::clone(&on_ready);
                let on_item_view = Rc::clone(&on_item_view);
                let on_item_highlight = Rc::clone(&on_item_highlight);
                let on_item_select = Rc::clone(&on_item_select);
                let auto_rotation_wait_duration_override = auto_rotation_wait_duration_override.clone();

                data.with(|data| {
                    let is_empty = data.items.with_untracked(|items| items.is_empty());
                    if is_empty {
                        None
                    } else {
                        compose_option! {
                            Column() {
                                HeroSig(items: data.items,
                                        size_config,
                                        on_item_focus,
                                        open_item_contextual_menu,
                                        on_back_pressed,
                                        on_item_view,
                                        on_item_highlight,
                                        on_item_select,
                                        default_focus_index,
                                        metadata: data.common_carousel_metadata,
                                        on_move_to_second_container,
                                        on_ready,
                                        is_tentpole: false,
                                        update_cm,
                                        auto_rotation_wait_duration_override
                                )
                            }
                        }
                    }
                })
            }))
        }
        .focus_hierarchical_container(NavigationStrategy::Vertical)
        .blocked_focus_edges(blocked_edges)
        .test_id(STANDARD_HERO_TEST_ID)
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::hero_sig::test::setup_mock_routing_with_past_location;
    use crate::utils::test::generate_mock_data;
    use container_types::ui_signals::CommonCarouselMetadata;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::composable::Composable;
    use ignx_compositron::compose;
    use ignx_compositron::context::AppContext;
    use ignx_compositron::input::KeyCode;
    use ignx_compositron::reactive::{
        create_rw_signal, provide_context, use_context, RwSignal, SignalGetUntracked, SignalSet,
    };
    use ignx_compositron::test_utils::assert_node_exists;
    use ignx_compositron::tts::TTSEnabledContext;
    use rstest::*;
    use rust_features::provide_context_test_rust_features;

    fn generate_mock_standard_data(scope: Scope) -> StandardHeroModel {
        let items = generate_mock_data(scope, true);
        StandardHeroModel {
            common_carousel_metadata: create_rw_signal(
                scope,
                CommonCarouselMetadata {
                    id: "standard hero id".to_string(),
                    analytics: create_rw_signal(scope, Default::default()),
                    journey_ingress_context: create_rw_signal(scope, Some("jic".to_string())),
                },
            ),
            items: create_rw_signal(scope, items),
        }
    }

    fn provide_firetv_context(ctx: &AppContext, is_firetv: bool) {
        let mut firetv = MockFireTV::default();
        firetv.expect_is_firetv().return_const(is_firetv);
        firetv.provide_mock(ctx.scope());
    }

    fn create_standard_hero(
        ctx: &AppContext,
        get_data: impl Fn(Scope) -> StandardHeroModel,
        expanded: bool,
        on_item_focus: Rc<dyn Fn(HeroItemModel, usize)>,
        open_item_contextual_menu: Rc<dyn Fn(HeroItemModel, usize, ButtonVariant)>,
        // Menu button exists on fire tv remote and opens/closes the contextual menu
        has_menu_button: bool,
    ) -> impl Composable<'static> {
        let data = get_data(ctx.scope());
        let data = create_rw_signal(ctx.scope(), data);
        let expanded = create_rw_signal(ctx.scope(), expanded);
        let size_config = SizeConfig::Expandable(expanded.into());
        let (_, update_cm) = create_signal(ctx.scope(), None);

        provide_context(
            ctx.scope(),
            TTSEnabledContext(create_signal(ctx.scope(), false).0),
        );

        provide_context_test_rust_features(ctx.scope());
        provide_firetv_context(&ctx, has_menu_button);
        compose! {
            StandardHeroSig(
                data,
                size_config,
                on_item_focus,
                open_item_contextual_menu,
                on_back_pressed: Rc::new(|| {}),
                on_move_to_second_container: Rc::new(|| {}),
                on_ready: Rc::new(|| {}),
                on_item_view: Rc::new(|_, _, _, _| {}),
                on_item_highlight: Rc::new(|_,_,_| {}),
                update_cm
            )
        }
    }

    #[rstest]
    #[case(generate_mock_standard_data, true)]
    #[case(generate_mock_standard_data, false)]
    fn renders_standard_hero(
        #[case] data: impl Fn(Scope) -> StandardHeroModel + 'static,
        #[case] expanded: bool,
    ) {
        launch_test(
            move |ctx| {
                setup_mock_routing_with_past_location(&ctx);
                create_standard_hero(
                    &ctx,
                    data,
                    expanded,
                    Rc::new(|_, _| {}),
                    Rc::new(|_, _, _| {}),
                    false,
                )
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let container = tree.find_by_test_id(STANDARD_HERO_TEST_ID);

                assert_node_exists!(&container);

                let hero = container.find_any_child_with().test_id("hero").find_first();

                assert_node_exists!(&hero);
            },
        )
    }

    #[test]
    fn on_focus_called() {
        launch_test(
            |ctx| {
                let last_focused_item = create_rw_signal(ctx.scope(), None);
                let on_item_focus = Rc::new(move |item: HeroItemModel, _idx: usize| {
                    last_focused_item.set(Some(item));
                });
                provide_context(ctx.scope(), last_focused_item);

                setup_mock_routing_with_past_location(&ctx);
                create_standard_hero(
                    &ctx,
                    generate_mock_standard_data,
                    true,
                    on_item_focus,
                    Rc::new(|_, _, _| {}),
                    false,
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let parent = tree.find_by_test_id("hero");

                test_game_loop.send_on_focus_event(parent.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let last_focused_item = use_context::<RwSignal<Option<HeroItemModel>>>(scope);
                let last_focused_item =
                    last_focused_item.expect("signal not found for last_focused_item");

                let item = last_focused_item
                    .get_untracked()
                    .expect("expected last focused item to be defined");
                let gti = item
                    .gti
                    .get_untracked()
                    .expect("expected to have focused a title card!");
                assert_eq!(gti, "hero gti 0");
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Enter)]
    #[case(KeyCode::Select)]
    fn open_contextual_menu_called(#[case] key_code: KeyCode) {
        launch_test(
            |ctx| {
                let last_contextual_menu_item_opened = create_rw_signal(ctx.scope(), None);
                let open_contextual_menu = Rc::new(
                    move |item: HeroItemModel, _idx: usize, _button: ButtonVariant| {
                        last_contextual_menu_item_opened.set(Some(item));
                    },
                );
                setup_mock_routing_with_past_location(&ctx);
                provide_context(ctx.scope(), last_contextual_menu_item_opened);
                create_standard_hero(
                    &ctx,
                    generate_mock_standard_data,
                    true,
                    Rc::new(|_, _| {}),
                    open_contextual_menu,
                    true,
                )
            },
            |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let parent = tree.find_by_test_id("hero");

                test_game_loop
                    .send_long_key_press_event_to_node(parent.borrow_props().node_id, key_code);
                test_game_loop.tick_until_done();

                let last_contextual_menu_item_opened =
                    use_context::<RwSignal<Option<HeroItemModel>>>(scope);
                let last_contextual_menu_item_opened = last_contextual_menu_item_opened
                    .expect("signal not found for last_contextual_menu_item_opened");

                let item = last_contextual_menu_item_opened
                    .get_untracked()
                    .expect("expected last item with contextual menu open to be defined");
                let gti = item
                    .gti
                    .get_untracked()
                    .expect("expected to have opened the contextual menu on a title card!");
                assert_eq!(gti, "hero gti 0");
            },
        )
    }
}
