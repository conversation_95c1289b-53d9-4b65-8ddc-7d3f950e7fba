use std::cell::Cell;
use std::cmp::PartialEq;
use std::rc::Rc;

use crate::types::{
    HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy, HeroItemMediaStrategy,
};
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::on_cleanup;
use ignx_compositron::task::TaskID;
use ignx_compositron::time::*;
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use rust_features::WeblabTreatmentString;

// We wait for this duration regardless of whether we are focused on the hero or on the top nav.
// https://issues.amazon.com/issues/PVRE-5216?selectedConversation=dce5d6b2-1c9f-4598-8273-bf3d93e5173b
#[cfg(not(feature = "test_utils_debug_renderer"))]
const ROTATION_WAIT_TIME: Duration = Duration::from_millis(5_000);
// The same usage as ROTATION_WAIT_TIME for experiment treatment
// https://quip-amazon.com/mgnFAyoWBSjV/EDD-Remaster-Reduce-Hero-Autorotation-Speed
#[cfg(not(feature = "test_utils_debug_renderer"))]
const INCREASED_ROTATION_WAIT_TIME: Duration = Duration::from_millis(10_000);
// Time for fade out and fade in to next title - used when checking if playback started within 5 seconds of a rotation
const TRANSITION_ANIMATION_TIME: Duration = Duration::from_millis(300);
const SUPER_DRAPER_ROTATION_DELAY: Duration = Duration::from_millis(1500);
// The area is used to determine some custom logic e.g. checking whether there is a trailer or not
// only happens when we are focused on the hero item.
#[derive(Clone, Copy, PartialEq)]
pub(crate) enum AutoRotationFocusArea {
    Hero,
    TopNav,
    DisabledArea,
}

#[derive(Clone, Copy, PartialEq)]
pub(crate) enum UserHeroInteraction {
    /// Manual rotated via button presses (left/right/back)
    ManualRotation,
    /// Navigation between the hero buttons should also pause the rotation as the user potentially
    /// wants to click on one of them.
    BetweenButtonsNavigation,
    PrimaryButtonPress,
}

pub struct AutoRotationControllerArgs {
    /// Calling this function will cause the hero to rotate once to the next item.
    pub rotate_once: Rc<dyn Fn()>,

    /// Number of items in the hero.
    /// Used to check whether a rotation is necessary `(hero_items_len > 1)` and for pausing the
    /// autorotations after we have rotated through all of them.
    pub hero_items_len: usize,

    /// The area the user is currently focused on. Autorotation will pause/resume when it gets a new signal.
    pub focused_area: Signal<AutoRotationFocusArea>,

    /// The autorotation behaviour will adapt based on the user-hero interactions
    pub user_hero_interaction: Signal<Option<UserHeroInteraction>>,

    pub current_item_media_strategy: Rc<dyn Fn() -> Option<HeroItemMediaStrategy>>,

    // Allows for custom waiting times based on certain conditions, see more: https://t.corp.amazon.com/P154113539/communication
    pub media_strategy_wait_time_override:
        Option<HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy>,

    /// When the hero trailer finished without interruption (which could happen due to manual rotation or similar)
    pub on_hero_trailer_playback_finish: Signal<bool>,

    /// The autorotation timers will start only if receiving a signal that the hero item became visible.
    pub is_hero_item_fully_visible: Signal<bool>,

    /// When trailer hero started.
    /// Used to check if we should rotate in case playback hasn't started after the auto rotation time
    pub on_hero_trailer_playback_start: Signal<bool>,

    /// Use increased rotation timeout for the first slot
    pub first_item_focused: Signal<bool>,

    /// Whether reduced motion is enabled, which will prevent autorotation
    pub reduced_motion_enabled: bool,
}

/// Creates the necessary signals and effects to autorotate the hero according to the Remaster specs.
/// There are two types of autorotations:
/// * Timer-based rotations (e.g. every 5 seconds)
/// * Playback-events-based rotations (e.g. rotate after trailer finishes)
pub fn setup_auto_rotation_controller(
    ctx: &AppContext,
    AutoRotationControllerArgs {
        rotate_once,
        hero_items_len,
        focused_area,
        user_hero_interaction,
        current_item_media_strategy,
        media_strategy_wait_time_override,
        on_hero_trailer_playback_finish,
        on_hero_trailer_playback_start,
        is_hero_item_fully_visible,
        first_item_focused,
        reduced_motion_enabled,
    }: AutoRotationControllerArgs,
) {
    let hero_auto_rotation_experiment_treatment = {
        try_use_rust_features(ctx.scope()).map_or_else(WeblabTreatmentString::default, |features| {
            features.get_hero_auto_rotation_experiment_treatment_string()
        })
    };

    let wait_time_based_on_strategy = Rc::new({
        let hero_auto_rotation_experiment_treatment =
            hero_auto_rotation_experiment_treatment.clone();

        move |media_strategy: HeroItemMediaStrategy| {
            let media_strategy_wait_time_override = media_strategy_wait_time_override.clone();
            let override_value = media_strategy_wait_time_override
                .and_then(|override_fn| override_fn(media_strategy));
            override_value.unwrap_or_else(|| {
                autorotation_timeout(
                    &hero_auto_rotation_experiment_treatment,
                    first_item_focused.get_untracked(),
                )
            })
        }
    });

    let task_id = Rc::new(Cell::new(None::<TaskID>));
    let is_rotation_paused = Rc::new(Cell::new(false));
    let auto_rotation_count = create_rw_signal(ctx.scope(), 0);
    let cancel_timers = {
        let ctx = ctx.clone();
        let task_id = Rc::clone(&task_id);
        Rc::new(move || {
            if let Some(task_id) = task_id.take() {
                log::debug!("[AutoRotation] Cancelling timer {:?}", task_id);
                ctx.cancel_task(task_id);
            }
        })
    };

    let do_auto_rotate = {
        let rotate_once = Rc::clone(&rotate_once);
        let rotation_paused = Rc::clone(&is_rotation_paused);
        Rc::new(move || {
            if reduced_motion_enabled {
                log::debug!("[AutoRotation] Reduced motion is enabled - no rotation happened");
                return;
            }
            if rotation_paused.get() {
                log::debug!("[AutoRotation] Auto rotation was paused - no rotation happened");
                return;
            }
            log::debug!(
                "[AutoRotation] Calling rotate_once() now {:?}",
                Instant::now()
            );
            rotate_once();
            auto_rotation_count.try_update(|c| *c += 1);
        })
    };

    let schedule_rotation_after_draper_finish = {
        let ctx = ctx.clone();
        let task_id = Rc::clone(&task_id);
        let cancel_timers = Rc::clone(&cancel_timers);
        let do_auto_rotate = Rc::clone(&do_auto_rotate);
        Rc::new(move || {
            cancel_timers();

            let new_task_id = ctx.schedule_task(Instant::now() + SUPER_DRAPER_ROTATION_DELAY, {
                let do_auto_rotate = Rc::clone(&do_auto_rotate);
                move || {
                    do_auto_rotate();
                }
            });

            log::debug!(
                "[AutoRotation] Scheduled Super Draper Rotation {:?} at {:?}",
                &new_task_id,
                Instant::now() + SUPER_DRAPER_ROTATION_DELAY
            );
            task_id.set(Some(new_task_id));
        })
    };

    let restart_timer = {
        let ctx = ctx.clone();
        let task_id = Rc::clone(&task_id);
        let cancel_timers = Rc::clone(&cancel_timers);
        let do_auto_rotate = Rc::clone(&do_auto_rotate);
        let hero_auto_rotation_experiment_treatment =
            hero_auto_rotation_experiment_treatment.clone();

        Rc::new(move || {
            cancel_timers();

            let autorotation_timeout = autorotation_timeout(
                &hero_auto_rotation_experiment_treatment,
                first_item_focused.get_untracked(),
            );

            let new_task_id = ctx.schedule_task(Instant::now() + autorotation_timeout, {
                let do_auto_rotate = Rc::clone(&do_auto_rotate);
                move || {
                    do_auto_rotate();
                }
            });

            log::debug!(
                "[AutoRotation] Scheduled timer {:?} at {:?}",
                &new_task_id,
                Instant::now() + autorotation_timeout
            );

            task_id.set(Some(new_task_id));
        })
    };

    let restart_timer_if_needed = {
        let ctx = ctx.clone();
        let restart_timer = Rc::clone(&restart_timer);
        let cancel_timers = Rc::clone(&cancel_timers);

        let do_auto_rotate = Rc::clone(&do_auto_rotate);
        let wait_time_based_on_strategy = Rc::clone(&wait_time_based_on_strategy);

        Rc::new(move || {
            // Reset timers / pausing in all cases
            cancel_timers();

            if hero_items_len <= 1 {
                return;
            }

            // Reason to stop after a full rotation is energy-saving and a few other reasons pertaining to avoiding noise in our metrics.
            if auto_rotation_count.get_untracked() >= hero_items_len {
                log::debug!(
                    "[AutoRotation] Rotation not scheduled: already rotated {:} times",
                    auto_rotation_count.get_untracked()
                );
                return;
            }

            if !is_hero_item_fully_visible.get_untracked() {
                log::debug!(
                    "[AutoRotation] Rotation not scheduled: current hero item not fully visible"
                );
                return;
            }

            // In case there is a media playback strategy (i.e. trailer or autoplay), we will
            // only auto rotate after the playback has finished. However, in certain cases
            // playback does not start within a reasonable time (i.e. playback error, slow device / internet).
            // This is why we will still force an auto rotate in those scenarios after a certain grace period.
            let media_strategy = current_item_media_strategy();
            if let Some(media_strategy) = media_strategy {
                if matches!(focused_area.get_untracked(), AutoRotationFocusArea::Hero) {
                    let playback_wait_time = wait_time_based_on_strategy(media_strategy);

                    // In this case we will listen to the trailer_has_finished() callback to rotate
                    log::info!(
                        "[HeroAutoRotation] Rotation not scheduled, waiting {:?} for item playback with media_strategy = {:?}", playback_wait_time, media_strategy
                    );

                    let new_task_id = ctx.schedule_task(
                        Instant::now() + playback_wait_time + TRANSITION_ANIMATION_TIME,
                        {
                            let do_auto_rotate = Rc::clone(&do_auto_rotate);
                            move || {
                                if !on_hero_trailer_playback_start.get_untracked() {
                                    do_auto_rotate();
                                }
                            }
                        },
                    );
                    task_id.set(Some(new_task_id));
                    return;
                }
            }

            restart_timer();
        })
    };

    let restart_auto_rotation_session = {
        let is_rotation_paused = Rc::clone(&is_rotation_paused);
        let restart_timer_if_needed = Rc::clone(&restart_timer_if_needed);

        Rc::new(move || {
            // TODO: We might need to rethink with UX on how we want to limit the rotation count. Currently
            // we always kick off a new session to rotate `n` times, which means the autorotation
            // could even stop when the user is not focused on the first item. It would feel more "natural",
            // if the rotation stopped at the first hero item, but then we need to keep track of more things and
            // it's not clear if UX is aligned on that. For now, let's keep this simple solution.
            auto_rotation_count.set_untracked(0);
            is_rotation_paused.set(false);
            restart_timer_if_needed();
        })
    };
    let pause_auto_rotation_session = {
        let cancel_timers = Rc::clone(&cancel_timers);
        let is_rotation_paused = Rc::clone(&is_rotation_paused);
        Rc::new(move || {
            is_rotation_paused.set(true);
            cancel_timers();
        })
    };

    // We only restart the timer if we get a signal that the hero item is visible now
    create_effect(ctx.scope(), {
        let restart_timer_if_needed = Rc::clone(&restart_timer_if_needed);
        move |_| {
            let is_hero_item_fully_visible = is_hero_item_fully_visible.get();
            log::debug!(
                "[AutoRotation] Hero item visible signal received = {:}",
                is_hero_item_fully_visible
            );
            if is_hero_item_fully_visible {
                restart_timer_if_needed();
            }
        }
    });

    // Subscribes to focused area changes and pauses/starts rotations accordingly
    create_effect(ctx.scope(), {
        let restart_auto_rotation_session = Rc::clone(&restart_auto_rotation_session);
        let pause_auto_rotation_session = Rc::clone(&pause_auto_rotation_session);

        move |_| {
            let focused_area = focused_area.get();
            match focused_area {
                AutoRotationFocusArea::Hero | AutoRotationFocusArea::TopNav => {
                    restart_auto_rotation_session();
                }
                AutoRotationFocusArea::DisabledArea => {
                    pause_auto_rotation_session();
                }
            }
        }
    });

    // Subscribe to manual user interactions within the hero container
    create_effect(ctx.scope(), {
        let restart_auto_rotation_session = Rc::clone(&restart_auto_rotation_session);
        let pause_auto_rotation_session = Rc::clone(&pause_auto_rotation_session);
        move |_| {
            let Some(user_hero_interaction) = user_hero_interaction.get() else {
                return;
            };
            match user_hero_interaction {
                UserHeroInteraction::ManualRotation => {
                    if hero_auto_rotation_experiment_treatment == WeblabTreatmentString::T3 {
                        log::debug!(
                                        "[AutoRotation] User manually rotated with Hero Rotation Experiment treatment enabled - pausing rotation."
                                    );
                        pause_auto_rotation_session();
                    } else {
                        log::debug!(
                                        "[AutoRotation] User manually rotated - restarting auto rotation session"
                                    );
                        restart_auto_rotation_session();
                    }

                    // TODO: SIM https://issues.amazon.com/issues/LR-11408
                    // Handle the case when manual rotations will stop auto rotations for the rest of the hero focused session based on a profile level setting
                    // e.g. `if !profile_level_setting_flag  { restart_auto_rotation_session(); }`
                }
                UserHeroInteraction::BetweenButtonsNavigation => {
                    log::debug!(
                        "[AutoRotation] User navigated between buttons - pausing rotation."
                    );
                    pause_auto_rotation_session();
                }
                UserHeroInteraction::PrimaryButtonPress => {
                    log::debug!("[AutoRotation] User pressed primary button - pausing rotation.");
                    pause_auto_rotation_session();
                }
            }
        }
    });

    // Subscribe to hero trailer playback finish
    create_effect(ctx.scope(), {
        let schedule_rotation_after_draper_finish =
            Rc::clone(&schedule_rotation_after_draper_finish);
        move |_| {
            if on_hero_trailer_playback_finish.get() {
                schedule_rotation_after_draper_finish();
            }
        }
    });

    on_cleanup(ctx.scope(), {
        let cancel_timers = Rc::clone(&cancel_timers);
        move || {
            cancel_timers();
        }
    });
}

// Increase rotation wait time in debug renderer mode to avoid test failure due to screenshot taken during rotation
#[cfg(feature = "test_utils_debug_renderer")]
fn autorotation_timeout(_: &WeblabTreatmentString, _: bool) -> Duration {
    Duration::from_millis(120_000)
}
#[cfg(not(feature = "test_utils_debug_renderer"))]
fn autorotation_timeout(
    experiment_treatment: &WeblabTreatmentString,
    first_item_focused: bool,
) -> Duration {
    match (experiment_treatment, first_item_focused) {
        (WeblabTreatmentString::T1 | WeblabTreatmentString::T3, _)
        | (WeblabTreatmentString::T2, true) => INCREASED_ROTATION_WAIT_TIME,
        (_, _) => ROTATION_WAIT_TIME,
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::test_utils::TestRendererGameLoop;
    use rstest::rstest;
    use rust_features::{provide_context_test_rust_features, MockRustFeaturesBuilder};

    #[derive(Clone)]
    struct TestContext {
        rotated_times: Rc<Cell<usize>>,

        focused_area: RwSignal<AutoRotationFocusArea>,
        user_hero_interaction: RwSignal<Option<UserHeroInteraction>>,

        on_hero_trailer_playback_finish: RwSignal<bool>,
        on_hero_trailer_playback_start: RwSignal<bool>,
        is_hero_item_visible: RwSignal<bool>,
        first_item_focused: RwSignal<bool>,
    }

    struct TestContextWithGameLoop {
        test_ctx: TestContext,
        game_loop: TestRendererGameLoop,
    }

    impl TestContextWithGameLoop {
        fn assert_rotated_n_times(&self, n: usize) {
            let rotation_times = self.test_ctx.rotated_times.get();
            assert_eq!(
                n, rotation_times,
                "Expected auto rotation count ({:}) != Actual auto rotation count ({:})",
                n, rotation_times
            );
        }

        fn send_hero_item_visible_signal(&mut self) {
            self.test_ctx.is_hero_item_visible.set(true);
            self.game_loop.tick_until_done();
        }

        fn mark_hero_item_as_not_visible(&mut self) {
            self.test_ctx.is_hero_item_visible.set(false);
            self.game_loop.tick_until_done();
        }

        fn use_regular_timeout(&mut self) {
            self.test_ctx.first_item_focused.set(false);
            self.game_loop.tick_until_done();
        }

        fn use_increased_timeout(&mut self) {
            self.test_ctx.first_item_focused.set(true);
            self.game_loop.tick_until_done();
        }
        fn set_focused_area(&mut self, focus_area: AutoRotationFocusArea) {
            self.test_ctx.focused_area.set(focus_area);
            self.game_loop.tick_until_done();
        }
        fn send_hero_trailer_finished_event(&mut self) {
            self.test_ctx.on_hero_trailer_playback_finish.set(true);
            self.game_loop.tick_until_done();
        }
        fn send_hero_trailer_started_event(&mut self) {
            self.test_ctx.on_hero_trailer_playback_start.set(true);
            self.game_loop.tick_until_done();
        }
        fn user_interacted_with_hero(&mut self, user_hero_interaction: UserHeroInteraction) {
            self.test_ctx
                .user_hero_interaction
                .set(Some(user_hero_interaction));
            self.game_loop.tick_until_done();
        }

        fn advance_time(&mut self, duration: Duration) {
            MockClock::advance(duration);
            self.game_loop.tick_until_done();
        }
    }

    #[derive(Default)]
    struct SetupOption {
        hero_items_len: Option<usize>,
        first_focused_area: Option<AutoRotationFocusArea>,
        items_media_strategies: Option<Vec<Option<HeroItemMediaStrategy>>>,
        playback_wait_duration: Option<HeroAutoRotationPlaybackWaitDurationBasedOnMediaStrategy>,
        reduced_motion_enabled: bool,
    }

    const DEFAULT_HERO_ITEMS_LEN: usize = 10;

    fn setup_composable(ctx: AppContext, options: SetupOption) -> impl Composable<'static> {
        let SetupOption {
            hero_items_len,
            first_focused_area,
            items_media_strategies,
            playback_wait_duration,
            reduced_motion_enabled,
        } = options;

        let hero_items_len = hero_items_len.unwrap_or(DEFAULT_HERO_ITEMS_LEN);
        let first_focused_area = first_focused_area.unwrap_or(AutoRotationFocusArea::DisabledArea);

        let rotated_times = Rc::new(Cell::new(0));

        let current_item_media_strategy = Rc::new({
            let rotated_times = Rc::clone(&rotated_times);
            move || {
                items_media_strategies.as_ref().and_then(|items| {
                    let idx = (rotated_times.get() % hero_items_len).clamp(0, items.len() - 1);
                    items[idx]
                })
            }
        });

        let rotate_once = Rc::new({
            let rotated_times = Rc::clone(&rotated_times);
            move || {
                rotated_times.set(rotated_times.get() + 1);
            }
        });

        let is_hero_item_fully_visible = create_rw_signal(ctx.scope(), false);
        let first_item_focused = create_rw_signal(ctx.scope(), false);

        let focused_area = create_rw_signal(ctx.scope(), first_focused_area);

        let user_hero_interaction = create_rw_signal(ctx.scope(), None::<UserHeroInteraction>);
        let on_hero_trailer_playback_finish = create_rw_signal(ctx.scope(), false);
        let on_hero_trailer_playback_start = create_rw_signal(ctx.scope(), false);

        let args = AutoRotationControllerArgs {
            rotate_once,
            hero_items_len,

            focused_area: focused_area.into(),
            user_hero_interaction: user_hero_interaction.into(),
            current_item_media_strategy,
            media_strategy_wait_time_override: playback_wait_duration,
            on_hero_trailer_playback_finish: on_hero_trailer_playback_finish.into(),
            on_hero_trailer_playback_start: on_hero_trailer_playback_start.into(),
            is_hero_item_fully_visible: is_hero_item_fully_visible.into(),
            first_item_focused: first_item_focused.into(),
            reduced_motion_enabled,
        };

        let test_ctx = TestContext {
            focused_area,
            rotated_times,
            user_hero_interaction,
            on_hero_trailer_playback_finish,
            on_hero_trailer_playback_start,
            is_hero_item_visible: is_hero_item_fully_visible,
            first_item_focused,
        };
        provide_context::<TestContext>(ctx.scope(), test_ctx);

        setup_auto_rotation_controller(&ctx, args);

        compose! {
            Stack() {
                Label(text: "Irrelevant compose!, but we need `ctx: AppContext` for scheduling.".to_owned())
            }
        }
    }

    fn test_should_rotate_every_5_seconds(scope: Scope, game_loop: TestRendererGameLoop) {
        let test_ctx = expect_context::<TestContext>(scope);
        let mut test_ctx = TestContextWithGameLoop {
            test_ctx,
            game_loop,
        };

        // t=0s
        test_ctx.send_hero_item_visible_signal();
        test_ctx.assert_rotated_n_times(0);
        // Testing that we do not rotate after t=1s, 2s, 3s, 4s
        for _ in 0..4 {
            test_ctx.advance_time(Duration::from_millis(1_000));
            test_ctx.assert_rotated_n_times(0);
        }

        // t=5s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(1);

        test_ctx.send_hero_item_visible_signal();

        // t=9s
        test_ctx.advance_time(Duration::from_millis(4_000));
        test_ctx.assert_rotated_n_times(1);

        // t=10s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(2);

        test_ctx.send_hero_item_visible_signal();

        // t=15s
        test_ctx.advance_time(Duration::from_millis(5_000));
        test_ctx.assert_rotated_n_times(3);
    }

    fn test_should_rotate_every_10_seconds(scope: Scope, game_loop: TestRendererGameLoop) {
        let test_ctx = expect_context::<TestContext>(scope);
        let mut test_ctx = TestContextWithGameLoop {
            test_ctx,
            game_loop,
        };

        // t=0s
        test_ctx.send_hero_item_visible_signal();
        test_ctx.assert_rotated_n_times(0);
        // Testing that we do not rotate after t=1s, 2s, 3s, 4s, 5s, 6s, 7s, 8s, 9s
        for _ in 0..9 {
            test_ctx.advance_time(Duration::from_millis(1_000));
            test_ctx.assert_rotated_n_times(0);
        }

        // t=10s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(1);

        test_ctx.send_hero_item_visible_signal();

        // t=19s
        test_ctx.advance_time(Duration::from_millis(9_000));
        test_ctx.assert_rotated_n_times(1);

        // t=20s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(2);

        test_ctx.send_hero_item_visible_signal();

        // t=30s
        test_ctx.advance_time(Duration::from_millis(10_000));
        test_ctx.assert_rotated_n_times(3);
    }

    fn test_should_rotate_with_increased_seconds(scope: Scope, game_loop: TestRendererGameLoop) {
        let test_ctx = expect_context::<TestContext>(scope);
        let mut test_ctx = TestContextWithGameLoop {
            test_ctx,
            game_loop,
        };

        // t=0s
        test_ctx.use_increased_timeout();
        test_ctx.send_hero_item_visible_signal();
        test_ctx.assert_rotated_n_times(0);
        // Testing that we do not rotate after t=1s, 2s, 3s, 4s, 5s, 6s, 7s, 8s, 9s
        for _ in 0..9 {
            test_ctx.advance_time(Duration::from_millis(1_000));
            test_ctx.assert_rotated_n_times(0);
        }

        // t=10s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(1);

        test_ctx.send_hero_item_visible_signal();

        // t=19s
        test_ctx.advance_time(Duration::from_millis(9_000));
        test_ctx.assert_rotated_n_times(1);

        // t=20s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(2);

        test_ctx.send_hero_item_visible_signal();

        // t=30s
        test_ctx.advance_time(Duration::from_millis(10_000));
        test_ctx.assert_rotated_n_times(3);
    }

    fn test_change_rotation_timeouts_correctly(scope: Scope, game_loop: TestRendererGameLoop) {
        let test_ctx = expect_context::<TestContext>(scope);
        let mut test_ctx = TestContextWithGameLoop {
            test_ctx,
            game_loop,
        };

        // t=0s
        test_ctx.use_increased_timeout();
        test_ctx.send_hero_item_visible_signal();
        test_ctx.assert_rotated_n_times(0);
        // Testing that we do not rotate after t=1s, 2s, 3s, 4s, 5s, 6s, 7s, 8s, 9s
        for _ in 0..9 {
            test_ctx.advance_time(Duration::from_millis(1_000));
            test_ctx.assert_rotated_n_times(0);
        }

        // t=10s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(1);

        // setting back the regular timeout
        test_ctx.use_regular_timeout();
        test_ctx.send_hero_item_visible_signal();

        // t=14s
        test_ctx.advance_time(Duration::from_millis(4_000));
        test_ctx.assert_rotated_n_times(1);

        // t=15s
        test_ctx.advance_time(Duration::from_millis(1_000));
        test_ctx.assert_rotated_n_times(2);

        // setting the increased timout back again
        test_ctx.use_increased_timeout();
        test_ctx.send_hero_item_visible_signal();

        // t=25s
        test_ctx.advance_time(Duration::from_millis(10_000));
        test_ctx.assert_rotated_n_times(3);
    }

    #[rstest]
    #[case(None)]
    #[case(Some(HeroItemMediaStrategy::Promo))]
    #[case(Some(HeroItemMediaStrategy::Live))]
    fn focused_on_top_nav_should_rotate_every_5_seconds_regardless_of_trailer_existing(
        #[case] item_media_strategy: Option<HeroItemMediaStrategy>,
    ) {
        launch_test(
            move |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::TopNav),
                        items_media_strategies: Some(vec![item_media_strategy]),
                        ..Default::default()
                    },
                )
            },
            test_should_rotate_every_5_seconds,
        );
    }

    #[test]
    fn focused_on_hero_without_trailers_should_rotate_every_5_seconds() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        hero_items_len: Some(10),
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: None,
                        ..Default::default()
                    },
                )
            },
            test_should_rotate_every_5_seconds,
        );
    }

    #[rstest]
    #[case(Some(WeblabTreatmentString::T1))]
    #[case(Some(WeblabTreatmentString::T3))]
    fn focused_on_hero_without_trailers_should_rotate_every_10_seconds_with_experiment_treatment(
        #[case] weblab_treatment_string: Option<WeblabTreatmentString>,
    ) {
        launch_test(
            |ctx| {
                let mock_features = MockRustFeaturesBuilder::new()
                    .set_hero_auto_rotation_experiment_treatment(weblab_treatment_string.unwrap())
                    .build();
                provide_context(ctx.scope(), mock_features);
                provide_context_test_rust_features(ctx.scope());

                setup_composable(
                    ctx,
                    SetupOption {
                        hero_items_len: Some(10),
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: None,
                        ..Default::default()
                    },
                )
            },
            test_should_rotate_every_10_seconds,
        );
    }

    #[test]
    fn focused_on_hero_without_trailers_should_rotate_using_increased_seconds_with_experiment_treatment(
    ) {
        launch_test(
            |ctx| {
                let mock_features = MockRustFeaturesBuilder::new()
                    .set_hero_auto_rotation_experiment_treatment(WeblabTreatmentString::T2)
                    .build();
                provide_context(ctx.scope(), mock_features);
                provide_context_test_rust_features(ctx.scope());

                setup_composable(
                    ctx,
                    SetupOption {
                        hero_items_len: Some(10),
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: None,
                        ..Default::default()
                    },
                )
            },
            test_should_rotate_with_increased_seconds,
        );
    }

    #[test]
    fn focused_on_hero_with_trailer_should_only_rotate_when_trailer_finished_and_after_1500ms() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: Some(vec![Some(HeroItemMediaStrategy::Promo)]),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                // No time based autorotation happening due to a potential trailer playing
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(0);

                // But once the trailer finishes we should see an auto-rotation.
                test_ctx.send_hero_trailer_finished_event();

                // However, we only do this after 1.5s because we want to show the static image to the user
                test_ctx.assert_rotated_n_times(0);
                test_ctx.advance_time(Duration::from_millis(1500));
                test_ctx.assert_rotated_n_times(1);
            },
        );
    }

    #[test]
    fn focused_on_hero_with_trailer_should_not_rotate_if_playback_starts() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: Some(vec![Some(HeroItemMediaStrategy::Promo)]),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                test_ctx.send_hero_item_visible_signal();
                test_ctx.send_hero_trailer_started_event();
                // No time based autorotation happening due to a potential trailer playing
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(0);
            },
        );
    }

    #[test]
    fn focused_on_hero_should_not_rotate_if_reduced_motion_is_enabled() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        hero_items_len: Some(10),
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: None,
                        reduced_motion_enabled: true,
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(10_000));
                test_ctx.assert_rotated_n_times(0);
            },
        );
    }

    #[test]
    pub fn focused_on_hero_should_rotate_if_playback_does_not_start_and_use_playback_wait_duration_override(
    ) {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: Option::from(vec![
                            Some(HeroItemMediaStrategy::Promo),
                            Some(HeroItemMediaStrategy::Live),
                            Some(HeroItemMediaStrategy::Promo),
                        ]),
                        hero_items_len: Some(10),
                        playback_wait_duration: Some(Rc::new(move |m| match m {
                            HeroItemMediaStrategy::Promo => None,
                            HeroItemMediaStrategy::Live => Some(Duration::from_secs(15)),
                            HeroItemMediaStrategy::Linear => None,
                        })),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000) + SUPER_DRAPER_ROTATION_DELAY);
                test_ctx.assert_rotated_n_times(1);

                // Should not rotate within 15s
                test_ctx.send_hero_item_visible_signal();
                for _ in 0..15 {
                    test_ctx.advance_time(Duration::from_millis(1_000));
                    test_ctx.assert_rotated_n_times(1);
                }
                test_ctx.advance_time(SUPER_DRAPER_ROTATION_DELAY);
                test_ctx.assert_rotated_n_times(2);

                // Should not rotate within 15s
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000) + SUPER_DRAPER_ROTATION_DELAY);
                test_ctx.assert_rotated_n_times(3);
            },
        );
    }

    #[test]
    fn manual_rotation_should_cancel_pending_super_draper_rotation() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: Some(vec![Some(HeroItemMediaStrategy::Promo)]),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };
                test_ctx.send_hero_item_visible_signal();
                test_ctx.send_hero_trailer_started_event();

                // No time based autorotation happening due to a potential trailer playing
                test_ctx.advance_time(Duration::from_millis(1_000));
                test_ctx.assert_rotated_n_times(0);
                test_ctx.user_interacted_with_hero(UserHeroInteraction::ManualRotation);
                test_ctx.assert_rotated_n_times(0);
                // Super draper trailer event triggers but should not rotate
                // since we performed a manual rotation
                test_ctx.advance_time(Duration::from_millis(500));
                test_ctx.assert_rotated_n_times(0);
            },
        );
    }

    #[test]
    fn manual_rotation_should_pause_auto_rotation_with_experiment_treatment() {
        launch_test(
            |ctx| {
                let mock_features: rust_features::MockRustFeatures = MockRustFeaturesBuilder::new()
                    .set_hero_auto_rotation_experiment_treatment(WeblabTreatmentString::T3)
                    .build();
                provide_context(ctx.scope(), mock_features);
                provide_context_test_rust_features(ctx.scope());

                setup_composable(
                    ctx,
                    SetupOption {
                        hero_items_len: Some(10),
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: None,
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };
                test_ctx.send_hero_item_visible_signal();
                test_ctx.send_hero_trailer_started_event();

                test_ctx.advance_time(Duration::from_millis(10_000));
                test_ctx.assert_rotated_n_times(1);
                test_ctx.user_interacted_with_hero(UserHeroInteraction::ManualRotation);
                test_ctx.assert_rotated_n_times(1);

                test_ctx.advance_time(Duration::from_millis(10_000));
                test_ctx.assert_rotated_n_times(1);
            },
        );
    }

    #[test]
    fn should_not_rotate_when_only_one_item_exists() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        hero_items_len: Some(1),
                        first_focused_area: Some(AutoRotationFocusArea::TopNav),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };
                test_ctx.send_hero_item_visible_signal();
                for _ in 0..5 {
                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_rotated_n_times(0);
                }
            },
        )
    }

    #[test]
    fn should_pause_rotation_when_focused_on_disabled_area_and_continue_when_returning_to_top_nav()
    {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::TopNav),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                // Autorotation happens as usual
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(1);

                // Once focused on a disabled area (e.g. utility nav) we should pause the rotation
                test_ctx.send_hero_item_visible_signal();
                test_ctx.set_focused_area(AutoRotationFocusArea::DisabledArea);
                // Should stay at n=1
                for _ in 0..5 {
                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_rotated_n_times(1);
                }

                // If we focus back, it should continue to rotate
                test_ctx.set_focused_area(AutoRotationFocusArea::TopNav);
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(2);
            },
        )
    }

    #[rstest]
    #[case(AutoRotationFocusArea::TopNav, AutoRotationFocusArea::Hero)]
    #[case(AutoRotationFocusArea::Hero, AutoRotationFocusArea::TopNav)]
    fn should_reset_rotation_timers_when_switching_between_areas(
        #[case] first_focused_area: AutoRotationFocusArea,
        #[case] next_area: AutoRotationFocusArea,
    ) {
        launch_test(
            move |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(first_focused_area),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                // t=5s
                // Usually hero rotation happens every 5s
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(1);

                // t=8s
                // However, if the user navigates from top nav to hero in the middle of the timer,
                // we want to reset the timer
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(3_000));
                test_ctx.set_focused_area(next_area);

                // t=10s
                test_ctx.advance_time(Duration::from_millis(2_000));
                test_ctx.assert_rotated_n_times(1);

                // t=13s
                test_ctx.advance_time(Duration::from_millis(3_000));
                test_ctx.assert_rotated_n_times(2);

                // t=18s
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(3);
            },
        )
    }

    #[test]
    fn should_reset_rotation_timers_when_user_manually_rotated() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };
                // This test makes sure that we don't accidentally auto rotate too fast because
                // we forgot to reset the timers.

                // t = 0s
                test_ctx.send_hero_item_visible_signal();

                // t = 3s
                test_ctx.advance_time(Duration::from_millis(3_000));

                // User will rotate, therefore next hero item is not ready then
                test_ctx.mark_hero_item_as_not_visible();
                test_ctx.user_interacted_with_hero(UserHeroInteraction::ManualRotation);

                // t = 8s, no rotation happened because hero item still not ready
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(0);

                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(1);

                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(2);
            },
        )
    }

    #[test]
    fn should_continue_auto_rotation_if_hero_had_trailer_but_user_moved_to_top_nav() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: Some(vec![Some(HeroItemMediaStrategy::Promo)]),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                // Usually rotation would be manually controlled depending on trailer finish event.
                // However, once we move back to top nav, the timer based autorotation should continue.
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(0);

                test_ctx.set_focused_area(AutoRotationFocusArea::TopNav);
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(1);

                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(2);
            },
        );
    }

    #[test]
    fn should_pause_rotation_when_user_navigated_between_hero_buttons_and_no_trailer() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: None,
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx: TestContextWithGameLoop = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                // Autorotation usually happens after every 5s
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(1);

                test_ctx.send_hero_item_visible_signal();
                // However, once the customer signals to us that they might want to press on a button,
                // we will stop the auto rotation to not disturb their flow.
                test_ctx.user_interacted_with_hero(UserHeroInteraction::BetweenButtonsNavigation);
                for _ in 0..5 {
                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_rotated_n_times(1);
                }

                // But will resume after user rotated manually
                test_ctx.user_interacted_with_hero(UserHeroInteraction::ManualRotation);
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(2);
            },
        );
    }

    #[test]
    fn should_pause_rotation_when_user_navigated_between_hero_buttons_and_with_trailer() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: Some(vec![
                            Some(HeroItemMediaStrategy::Promo),
                            None,
                        ]),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                // Autorotation usually happens after trailer ends
                test_ctx.send_hero_trailer_finished_event();
                test_ctx.advance_time(Duration::from_millis(1500));
                test_ctx.assert_rotated_n_times(1);

                // However, in case the user navigated between the hero buttons, we want to pause
                // the autorotation
                test_ctx.user_interacted_with_hero(UserHeroInteraction::BetweenButtonsNavigation);
                test_ctx.send_hero_trailer_finished_event();
                test_ctx.assert_rotated_n_times(1);

                // This pause can be broken up by rotating manually once
                test_ctx.user_interacted_with_hero(UserHeroInteraction::ManualRotation);
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(2);
            },
        );
    }

    /// Reason is that there were some energy-saving related discussions and some customers keep their devices
    /// running for a few days:
    /// * Stability issues due to slight memory leak
    /// * Skewing of performance metrics (FPS)
    /// * Generate unnecessary logs and maybe even CSM events adding noise to our telemetry
    #[test]
    fn should_pause_rotation_after_rotating_through_all_10_hero_items() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::TopNav),
                        items_media_strategies: None,
                        hero_items_len: Some(10),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                test_ctx.assert_rotated_n_times(0);

                for i in 1..=10 {
                    test_ctx.send_hero_item_visible_signal();
                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_rotated_n_times(i);
                }

                // Rotation count should stay at 10 now because we rotated through all items
                for _ in 0..5 {
                    test_ctx.send_hero_item_visible_signal();
                    test_ctx.advance_time(Duration::from_millis(5_000));
                    test_ctx.assert_rotated_n_times(10);
                }
            },
        );
    }

    #[test]
    fn should_only_auto_rotate_after_hero_item_visible_signals() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::TopNav),
                        hero_items_len: Some(10),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                test_ctx.assert_rotated_n_times(0);

                // We can't start the auto-rotation if the hero item isn't visible yet
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(0);

                // Now let's set it to be visible (e.g. media background was downloaded)
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(1);

                // We will keep not rotating until we get another hero item ready signal
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(1);

                // Let's send a signal then there should be a rotation queued up
                test_ctx.send_hero_item_visible_signal();
                test_ctx.advance_time(Duration::from_millis(5_000));
                test_ctx.assert_rotated_n_times(2);
            },
        );
    }

    #[test]
    fn should_pause_rotation_when_user_selects_primary_button() {
        launch_test(
            |ctx| {
                setup_composable(
                    ctx,
                    SetupOption {
                        first_focused_area: Some(AutoRotationFocusArea::Hero),
                        items_media_strategies: Some(vec![
                            Some(HeroItemMediaStrategy::Promo),
                            None,
                        ]),
                        ..Default::default()
                    },
                )
            },
            move |scope, game_loop| {
                let mut test_ctx = TestContextWithGameLoop {
                    test_ctx: expect_context::<TestContext>(scope),
                    game_loop,
                };

                test_ctx.send_hero_trailer_finished_event();
                test_ctx.advance_time(Duration::from_millis(1500));
                test_ctx.assert_rotated_n_times(1);

                test_ctx.send_hero_item_visible_signal();
                test_ctx.user_interacted_with_hero(UserHeroInteraction::PrimaryButtonPress);
                test_ctx.advance_time(Duration::from_millis(5000));
                test_ctx.assert_rotated_n_times(1);
            },
        );
    }
}
