use crate::client::MetricClientEmitter;
use cfg_test_attr_derive::derive_test_only;
use serde::Serialize;
use std::cell::RefCell;
use std::rc::Rc;

/// Helper structure for easier assertions in tests
///
/// [`Serialize`] is required to enable optional snapshots testing via insta
#[derive(<PERSON><PERSON>, Serialize)]
#[derive_test_only(PartialEq, Debug)]
pub struct MetricEventPayload {
    pub name: &'static str,
    pub value: u128,
    pub dimensions: Vec<(String, String)>,
}

/// Helper struct that stores emitted metrics instead of reporting them,
/// and has methods for getting and flushing them
pub struct MetricClientEmitterSpy {
    metrics_emitted: RefCell<Vec<MetricEventPayload>>,
}

/// Creates a [`MetricClientEmitterSpy`] version of [`MetricClient`]
pub fn get_spy_metric_client() -> Rc<MetricClientEmitterSpy> {
    Rc::new(MetricClientEmitterSpy::new())
}

impl MetricClientEmitterSpy {
    pub fn new() -> Self {
        Self {
            metrics_emitted: RefCell::new(Vec::new()),
        }
    }

    /// Returns metric emitted via `emit`
    pub fn recorded_metrics(&self) -> Vec<MetricEventPayload> {
        self.metrics_emitted.borrow().clone()
    }

    /// Clears the recorded metrics
    pub fn flush(&self) {
        self.metrics_emitted.borrow_mut().clear();
    }
}

impl MetricClientEmitter for MetricClientEmitterSpy {
    fn emit(&self, name: &'static str, value: u128, dimensions: Vec<(String, String)>) {
        self.metrics_emitted.borrow_mut().push(MetricEventPayload {
            name,
            value,
            dimensions,
        });
    }
}
