use crate::client::MetricClient;
use ignx_compositron::time::Instant;

pub struct TimedMetricReporter {
    was_reported: bool,
    start_time: Option<Instant>,
    client: MetricClient,
}

impl TimedMetricReporter {
    pub fn new(client: MetricClient) -> Self {
        Self {
            was_reported: false,
            start_time: None,
            client,
        }
    }
    pub fn report_start(&mut self, begin_metric: &'static str, dimensions: Vec<(String, String)>) {
        self.was_reported = true;
        self.start_time = Some(Instant::now());

        self.client.emit(begin_metric, 1, dimensions);
    }
    pub fn report_end(&mut self, end_metric: &'static str, dimensions: Vec<(String, String)>) {
        if self.was_reported {
            if let Some(start_time) = self.start_time {
                let elapsed = Instant::now().duration_since(start_time).as_millis();

                self.was_reported = false;
                self.start_time = None;

                self.client.emit(end_metric, elapsed, dimensions);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::get_spy_metric_client;
    use ignx_compositron::time::Mo<PERSON><PERSON><PERSON>;
    use std::time::Duration;

    #[test]
    fn test_timed_metric_reporter_reports_begin_and_end() {
        let spy = get_spy_metric_client();
        let mut reporter = TimedMetricReporter::new(spy.clone());

        // Report start event
        reporter.report_start(
            "test.begin",
            vec![("action".to_string(), "test_action".to_string())],
        );

        // Verify start event was reported
        let events = spy.recorded_metrics();
        assert_eq!(events.len(), 1);
        assert_eq!(events[0].name, "test.begin");
        assert_eq!(events[0].value, 1);
        assert_eq!(events[0].dimensions.len(), 1);
        assert_eq!(
            events[0].dimensions[0],
            ("action".to_string(), "test_action".to_string())
        );

        // Advance time
        MockClock::advance(Duration::from_millis(150));

        // Clear events to check only end event
        spy.flush();

        // Report end event
        reporter.report_end(
            "test.end",
            vec![("action".to_string(), "test_action".to_string())],
        );

        // Verify end event was reported with correct duration
        let events = spy.recorded_metrics();
        assert_eq!(events.len(), 1);
        assert_eq!(events[0].name, "test.end");
        assert_eq!(events[0].value, 150); // Should match the time we advanced
        assert_eq!(events[0].dimensions.len(), 1);
        assert_eq!(
            events[0].dimensions[0],
            ("action".to_string(), "test_action".to_string())
        );
    }

    #[test]
    fn test_timed_metric_reporter_end_without_start() {
        let spy = get_spy_metric_client();
        let mut reporter = TimedMetricReporter::new(spy.clone());

        // Report end without start
        reporter.report_end(
            "test.end",
            vec![("action".to_string(), "test_action".to_string())],
        );

        // Verify no events were reported
        let events = spy.recorded_metrics();
        assert_eq!(events.len(), 0);
    }

    #[test]
    fn test_timed_metric_reporter_multiple_starts() {
        let spy = get_spy_metric_client();
        let mut reporter = TimedMetricReporter::new(spy.clone());

        // Report first start
        reporter.report_start(
            "test.begin",
            vec![("action".to_string(), "first_action".to_string())],
        );

        // Advance time
        MockClock::advance(Duration::from_millis(100));

        // Report second start without ending first one
        reporter.report_start(
            "test.begin",
            vec![("action".to_string(), "second_action".to_string())],
        );

        // Clear previous events
        spy.flush();

        // Advance time again
        MockClock::advance(Duration::from_millis(50));

        // Report end
        reporter.report_end(
            "test.end",
            vec![("action".to_string(), "second_action".to_string())],
        );

        // Verify only the second timing was reported
        let events = spy.recorded_metrics();
        assert_eq!(events.len(), 1);
        assert_eq!(events[0].name, "test.end");
        assert_eq!(events[0].value, 50); // Should match only the second interval
        assert_eq!(
            events[0].dimensions[0],
            ("action".to_string(), "second_action".to_string())
        );
    }

    #[test]
    fn test_timed_metric_reporter_reset_after_end() {
        let spy = get_spy_metric_client();
        let mut reporter = TimedMetricReporter::new(spy.clone());

        // Report start
        reporter.report_start(
            "test.begin",
            vec![("action".to_string(), "test_action".to_string())],
        );

        // Advance time
        MockClock::advance(Duration::from_millis(100));

        // Report end
        reporter.report_end(
            "test.end",
            vec![("action".to_string(), "test_action".to_string())],
        );

        // Clear events
        spy.flush();

        // Try to report end again
        reporter.report_end(
            "test.end",
            vec![("action".to_string(), "test_action".to_string())],
        );

        // Verify no events were reported on second end call
        let events = spy.recorded_metrics();
        assert_eq!(events.len(), 0);
    }
}
