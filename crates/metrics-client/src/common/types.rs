#[derive(Clone)]
pub enum PageActionMetrics {
    Begin,
    Count,
    SuccessRate,
    Abandoned,
    OverallLatency,
    RenderLatency,
    ParsingLatency,
    ProcessingLatency,
    NetworkLatency,
}

impl PageActionMetrics {
    pub const fn as_str(&self) -> &'static str {
        match self {
            Self::Begin => "PageAction.Begin",
            Self::Count => "PageAction.Count",
            Self::SuccessRate => "PageAction.SuccessRate",
            Self::Abandoned => "PageAction.Abandoned",
            Self::OverallLatency => "PageAction.OverallLatency",
            Self::RenderLatency => "PageAction.RenderLatency",
            Self::ParsingLatency => "PageAction.ParsingLatency",
            Self::ProcessingLatency => "PageAction.ProcessingLatency",
            Self::NetworkLatency => "PageAction.NetworkLatency",
        }
    }
}

#[derive(Clone)]
pub enum ComponentActionMetrics {
    Begin,
    Count,
    SuccessRate,
    OverallLatency,
    RenderLatency,
    ParsingLatency,
    NetworkLatency,
}

impl ComponentActionMetrics {
    pub const fn as_str(&self) -> &'static str {
        match self {
            Self::Begin => "ComponentAction.Begin",
            Self::Count => "ComponentAction.Count",
            Self::SuccessRate => "ComponentAction.SuccessRate",
            Self::OverallLatency => "ComponentAction.OverallLatency",
            Self::RenderLatency => "ComponentAction.RenderLatency",
            Self::ParsingLatency => "ComponentAction.ParsingLatency",
            Self::NetworkLatency => "ComponentAction.NetworkLatency",
        }
    }
}
