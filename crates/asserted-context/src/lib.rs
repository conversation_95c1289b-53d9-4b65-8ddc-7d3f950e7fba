use ignx_compositron::prelude::safe::*;

/// Retrieves a context value from the current scope.
///
/// If the requested context is not found in a debug build this function will panic.
/// In a release build this function will log an error and the Default implementation for the
/// context will be used.
///
/// # Arguments
/// * `scope` - The current scope to search for context
///
/// # Example
/// ```no_run
/// use ignx_compositron::prelude::safe::*;
/// use asserted_context::use_asserted_context;
///
/// #[derive(Clone)]
/// struct FooContext;
///
///
/// fn use_foo(scope: Scope) -> FooContext {
///     // The value returned by the callback will be used when context is not found in provided Scope in release builds
///     // use_asserted_context will log an error when requested context is missing.
///     use_asserted_context(scope, || FooContext)
/// }
/// ```
pub fn use_asserted_context<'s, T>(scope: Scope<'s>, fallback: impl FnOnce() -> T) -> T
where
    T: Clone + 'static,
{
    let context = use_context(scope);

    #[cfg(not(any(test, feature = "test")))]
    debug_assert!(
        context.is_some(),
        "[asserted_context::use_asserted_context] Context '{}' not found in provided scope",
        std::any::type_name::<T>()
    );

    context.unwrap_or_else(|| {
        log::error!(
            "[asserted_context::use_asserted_context] Context '{}' not found in provided scope, using fallback implementation",
            std::any::type_name::<T>()
        );
        fallback()
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::reactive::provide_context;

    #[derive(Clone, Debug, PartialEq)]
    struct TestContext(i32);

    impl Default for TestContext {
        fn default() -> Self {
            TestContext(0)
        }
    }

    #[test]
    fn should_return_context_when_present() {
        launch_only_scope(|scope| {
            provide_context(scope, TestContext(42));

            let result = use_asserted_context(scope, || TestContext(0));
            assert_eq!(result, TestContext(42));
        });
    }

    #[test]
    fn should_use_fallback_and_log_error_when_context_is_missing() {
        launch_only_scope(|scope| {
            testing_logger::setup();
            let result = use_asserted_context(scope, || TestContext(0));

            testing_logger::validate(|logs| {
                assert_eq!(logs[0].body, "[asserted_context::use_asserted_context] Context 'asserted_context::tests::TestContext' not found in provided scope, using fallback implementation");
            });
            assert_eq!(result, TestContext(0));
        });
    }
}
