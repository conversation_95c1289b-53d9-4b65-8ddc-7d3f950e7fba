use std::rc::Rc;

use amzn_fable_tokens::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::app::launch_composable;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::compose;
use ignx_compositron::layout::*;
use ignx_compositron::prelude::create_rw_signal;
use ignx_compositron::row::*;
use ignx_compositron::text::TextContent;
use linear::constants::STATION_ROW_HEIGHT;
use linear::favorite_station::StationFavoriteTracker;
use linear::station_logo::*;
use network::NetworkClient;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx: ignx_compositron::context::AppContext| {
        let network_client = Rc::new(NetworkClient::new(&ctx));
        let tracker = Rc::new(StationFavoriteTracker::new(network_client));
        compose! {
            Row() {
                Column() {
                    Column() {
                        StationLogo(
                            station_name: "Station Logo Fallback Text".to_string(),
                            url: "https://m.media-amazon.com/images/G/01/LCXO_Station_Logos/ET._AC_SR100,67_SX100_FMpng_.png".to_string(),
                            height: STATION_ROW_HEIGHT,
                            show_shadow: false,
                            left_navigation_hint: TextContent::String("".to_string()),
                            index: 0,
                            on_key_down: Rc::new(move |_| false),
                            is_favorites_enabled: true,
                            is_favorite: create_rw_signal(ctx.scope(), true),
                            gti: "gti".to_string(),
                            favorites_request_tracker: Rc::clone(&tracker),
                            is_row_focused: true
                        )
                        StationLogo(
                            station_name: "Station Logo Fallback Text".to_string(),
                            url: "https://m.media-amazon.com/images/G/01/LCXO_Station_Logos/ET._AC_SR100,67_SX100_FMpng_.png".to_string(),
                            height: STATION_ROW_HEIGHT,
                            show_shadow: false,
                            left_navigation_hint: TextContent::String("".to_string()),
                            index: 0,
                            on_key_down: Rc::new(move |_| false),
                            is_favorites_enabled: true,
                            is_favorite: create_rw_signal(ctx.scope(), false),
                            gti: "gti".to_string(),
                            favorites_request_tracker: Rc::clone(&tracker),
                            is_row_focused: true
                        )
                        StationLogo(
                            station_name: "Station Logo Fallback Text".to_string(),
                            url: "https://m.media-amazon.com/images/G/01/LCXO_Station_Logos/ET._AC_SR100,67_SX100_FMpng_.png".to_string(),
                            height: STATION_ROW_HEIGHT,
                            show_shadow: false,
                            left_navigation_hint: TextContent::String("".to_string()),
                            index: 0,
                            on_key_down: Rc::new(move |_| false),
                            is_favorites_enabled: false,
                            is_favorite: create_rw_signal(ctx.scope(), true),
                            gti: "gti".to_string(),
                            favorites_request_tracker: Rc::clone(&tracker),
                            is_row_focused: true
                        )
                    }
                }
                .main_axis_size(MainAxisSize::Max)
                .main_axis_alignment(MainAxisAlignment::Center)
                .cross_axis_alignment(CrossAxisAlignment::Center)
                .background_color(get_ignx_color(FableColor::BACKGROUND))
            }
            .main_axis_size(MainAxisSize::Max)
            .main_axis_alignment(MainAxisAlignment::Center)
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
        }
    });
}
