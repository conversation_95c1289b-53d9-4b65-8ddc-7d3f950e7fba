use amzn_fable_tokens::FableColor;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::app::launch_composable;
use ignx_compositron::compose;
use ignx_compositron::prelude::*;
use linear::live_page::skeleton::*;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx: ignx_compositron::context::AppContext| {
        compose! {
            Column() {
                LivePageSkeleton()
            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
        }
    });
}
