use chrono::Utc;
use current_time::CurrentTimeContext;
use fableous::SCREEN_WIDTH;
use ignx_compositron::compose;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use linear::constants::{
    HOUR_WIDTH, LIVE_PAGE_CONTENT_HEIGHT_NO_HERO, LIVE_PAGE_EPG_WIDTH,
    STATION_HORIZONTAL_SCROLL_PEEK_WIDTH, STATION_LOGO_WIDTH, STATION_ROW_HEIGHT,
};
use linear::live_page::carousel_list::*;
use linear::live_page::reactive_row_helper::to_reactive;
use linear::mocks::mock_data_util::*;
use linear::ui::epg::epg_util::build_epg_context;
use network::NetworkClient;
use std::rc::Rc;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let (rows, _) = create_signal(
            ctx.scope(),
            to_reactive(
                ctx.scope(),
                (0..100)
                    .map(|i| mock_live_page_station_row(&format!("Station_{}", i)))
                    .collect(),
            ),
        );
        let (initial_focus, _) = create_signal(ctx.scope(), (0, 0, false));

        let now = Utc::now().timestamp_millis();
        let (current_time, _set_current_time) = create_signal(ctx.scope(), now);
        provide_context(ctx.scope(), CurrentTimeContext { current_time });
        let (schedule_data_window, _) = create_signal(ctx.scope(), (now, now + 8 * 3_600_000));
        provide_context(
            ctx.scope(),
            build_epg_context(
                ctx.scope(),
                current_time,
                schedule_data_window.into(),
                HOUR_WIDTH,
                STATION_HORIZONTAL_SCROLL_PEEK_WIDTH,
                (LIVE_PAGE_EPG_WIDTH - STATION_LOGO_WIDTH).into(),
                LIVE_PAGE_CONTENT_HEIGHT_NO_HERO,
                STATION_ROW_HEIGHT,
            ),
        );

        compose! {
            Column() {
                CarouselList(
                    rows,
                    initial_focus,
                    height: 1266.0,
                    width: LIVE_PAGE_EPG_WIDTH,
                    reset_scroll: create_rw_signal(ctx.scope(), ()),
                    left_navigation_hint: TextContent::String("".to_string()),
                    open_details_modal: Rc::new(|_| ()),
                    show_details_modal_hint: create_rw_signal(ctx.scope(), false),
                    transition_with_action: Rc::new(|_, _, _| ()),
                    bubble_up: Rc::new(|_| ()),
                    is_hovered: false,
                    is_pointer_control_enabled: false,
                    is_pointer_control_active: false,
                    contextual_menu_data_setter: create_rw_signal(ctx.scope(), None).write_only(),
                    network_client: Rc::new(NetworkClient::new(&ctx)),
                    is_favorites_enabled: true
                )
            }
            .width(SCREEN_WIDTH)
            .overflow_behavior(OverflowBehavior::Hidden)
        }
    });
}
