use chrono::Utc;
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use cross_app_events::ImpressionData;
use current_time::create_current_time_signal;
use current_time::CurrentTimeContext;
use fableous::buttons::primary_button::*;
use ignx_compositron::app::launch_composable;
use ignx_compositron::column::*;
use ignx_compositron::composable::VisualComposable;
use ignx_compositron::compose;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use linear::constants::SIDE_NAVIGATION_ALLOWANCE_PADDING;
use linear::mocks::mock_details_data_util::mock_station_airings_row_model;
use linear::network::NetworkClient;
use linear::station_details::epg::details_epg_row::*;
use linear::station_details::epg::details_epg_utils::build_details_epg_context;
use linear::station_details::types::StationAiringsRowModelReactive;
use linear::ui::epg::constants::HOUR_IN_MS;
use location::PageType;
use location::RustPage;
use std::rc::Rc;

#[ignx_compositron::main]
fn main() {
    const AIRING_LENGTH: i64 = 30;
    const AIRING_COUNT: usize = 20;

    launch_composable(|ctx: ignx_compositron::context::AppContext| {
        let (current_time, _) = create_current_time_signal(&ctx);
        provide_context(ctx.scope(), CurrentTimeContext { current_time });
        provide_context(
            ctx.scope(),
            build_details_epg_context(
                ctx.scope(),
                current_time.get_untracked() + (HOUR_IN_MS * 24 * 14),
            ),
        );
        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            ctx.scope(),
            vec![],
            PageType::Rust(RustPage::RUST_STATION_DETAILS),
            None,
        ));
        provide_context(ctx.scope(), tti_registry);

        let mut initial_data = mock_station_airings_row_model(
            "item",
            AIRING_COUNT,
            AIRING_LENGTH,
            Utc::now().timestamp_millis(),
        );
        let airings = initial_data.airings.drain(..).collect();
        let reactive = StationAiringsRowModelReactive {
            station: initial_data,
            airings_signal: create_rw_signal(ctx.scope(), airings),
            backward_pagination_data_signal: create_rw_signal(ctx.scope(), None),
            forward_pagination_data_signal: create_rw_signal(ctx.scope(), None),
        };

        compose! {
            Column() {
                PrimaryButton(variant: PrimaryButtonVariant::TextSize100(TextContent::String( "Button".to_string() )))
                .width(144.0)

                DetailsEpgRow(
                    network_client: Rc::new(NetworkClient::new(&ctx)),
                    bubble_up: Rc::new(move |_| {}),
                    content: reactive,
                    on_item_select: Rc::new(move |_| {}),
                    has_play_action: true,
                    initial_focus_id: "".to_string(),
                    impression_data: create_signal(ctx.scope(), ImpressionData::default()).0
                )
            }
            .padding(SIDE_NAVIGATION_ALLOWANCE_PADDING)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(100.0))
        }
    });
}
