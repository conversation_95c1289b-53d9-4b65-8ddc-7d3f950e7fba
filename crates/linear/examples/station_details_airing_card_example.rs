use amzn_fable_tokens::FableColor;
use chrono::Utc;
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use fableous::utils::get_ignx_color;
use fableous::SCREEN_HEIGHT;
use fableous::SCREEN_WIDTH;
use ignx_compositron::app::launch_composable;
use ignx_compositron::column::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use linear::constants::AIRING_CARD_FOCUSED_MIN_WIDTH;
use linear::constants::STATION_ROW_HEIGHT;
use linear::mocks::mock_data_util::mock_airing_item;
use linear::station_details::details_airing_card::*;
use linear::station_details::details_time_header::*;
use linear::ui::epg::constants::{HOUR_IN_MS, TEN_MIN_IN_MILLIS};
use linear::ui::epg::epg_util::round_down_to_epg_timeblock;
use linear_common::types::Schedule;
use location::{PageType, RustPage};
use std::rc::Rc;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx: ignx_compositron::context::AppContext| {
        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            ctx.scope(),
            vec![],
            PageType::Rust(RustPage::RUST_LIVE_TV),
            None,
        ));
        provide_context(ctx.scope(), tti_registry);

        compose! {
                Row() {
                    // Entitled
                    Column() {
                        // On Now airing, focused
                        AiringCardWithTimeHeader(is_focused: true, is_on_air: true)

                        // On Now airing, unfocused
                        AiringCardWithTimeHeader(is_focused: false, is_on_air: true)

                        // Upcoming Airing, focused and starting at a future date
                        AiringCardWithTimeHeader(is_focused: true, is_on_air: false)

                        // Upcoming Airing, unfocused
                        AiringCardWithTimeHeader(is_focused: false, is_on_air: false)
                }
                .width(SCREEN_WIDTH)
                .height(SCREEN_HEIGHT)
                .padding(Padding::all(12.0))
                .background_color(get_ignx_color(FableColor::BACKGROUND))
                .main_axis_alignment(MainAxisAlignment::SpaceBetween)
            }
        }
    });
}

#[Composer]
fn AiringCardWithTimeHeader(
    ctx: &AppContext,
    is_focused: bool,
    is_on_air: bool,
) -> ColumnComposable {
    let now = round_down_to_epg_timeblock(Utc::now().timestamp_millis());
    let mock_airing_card: linear::types::AiringModel =
        mock_airing_item("airing_id".to_string(), now, now + HOUR_IN_MS);
    // mocking dummy airing card
    let current_time = if is_on_air {
        now + TEN_MIN_IN_MILLIS
    } else {
        now - TEN_MIN_IN_MILLIS
    };

    let width = if is_on_air { 1000.0 } else { 700.0 };
    compose! {
        Column() {
            DetailsTimeHeader(
                current_time,
                width,
                is_on_air,
                time_range: mock_airing_card.get_time_range(),
                time_marker_with_date: None,
            )
            DetailsAiringCard(
                airing_card: mock_airing_card.clone(),
                current_time,
                is_focused,
                width,
                height: STATION_ROW_HEIGHT,
                is_last_card: false,
                max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
            )
        }
    }
}
