use amzn_fable_tokens::FableColor;
use chrono::Utc;
use common_transform_types::container_items::ShowContext;
use common_transform_types::container_items::{EntitlementMessage, EntitlementMessageIcons};
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use fableous::utils::get_ignx_color;
use fableous::SCREEN_HEIGHT;
use fableous::SCREEN_WIDTH;
use ignx_compositron::app::launch_composable;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::compose;
use ignx_compositron::layout::*;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use linear::airing_card::*;
use linear::constants::AIRING_CARD_FOCUSED_MIN_WIDTH;
use linear::constants::{
    STATION_ROW_HEIGHT, STATION_ROW_HEIGHT_FOCUSED, STATION_ROW_HEIGHT_FOCUSED_UNENTITLED,
};
use linear::mocks::mock_data_util::mock_airing_card_with_context;
use linear::ui::epg::constants::TEN_MIN_IN_MILLIS;
use linear::utils::context_utils::get_linear_tts_context;
use location::{PageType, RustPage};
use std::rc::Rc;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx: ignx_compositron::context::AppContext| {
        get_linear_tts_context(ctx.scope());

        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            ctx.scope(),
            vec![],
            PageType::Rust(RustPage::RUST_LIVE_TV),
            None,
        ));
        provide_context(ctx.scope(), tti_registry);

        // mocking dummy airing card
        let current_time_on_air = Utc::now().timestamp_millis() + TEN_MIN_IN_MILLIS;
        let current_time_upcoming = Utc::now().timestamp_millis() - TEN_MIN_IN_MILLIS;
        let mock_airing_card = mock_airing_card_with_context(
            Some(ShowContext {
                episode: Some(5),
                season: Some(6),
                episodeTitle: Some(String::from("test")),
            }),
            Some(String::from("This modern-day western follows the Duttons, who own the largest ranch in Montana and employ a ruthless mix of business, politics and violence to hold on to the precious land at the heart of their family empire.")),
        );
        let mock_airing_card_clone = mock_airing_card.clone();
        let entitlement_message = Some(EntitlementMessage {
            message: Some("Subscribe with AB".to_string()),
            icon: Some(EntitlementMessageIcons::OFFER_ICON),
        });
        let entitlement_message_clone = entitlement_message.clone();

        let (_, accessibility_messages_setter) = create_signal(ctx.scope(), vec![]);
        let (_, accessibility_hints_setter) = create_signal(ctx.scope(), vec![]);
        compose! {
            Row() {
                // Entitled
                Column(){
                    // Is airing now, row is active, card is focused
                    AiringCard(
                        airing_card: mock_airing_card.clone(),
                        is_entitled: true,
                        entitlement_message: entitlement_message.clone(),
                        current_time: current_time_on_air,
                        is_row_focused: true,
                        is_focused: true,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT_FOCUSED,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                    // Is airing now, row is active, card is not focused
                    AiringCard(
                        airing_card: mock_airing_card.clone(),
                        is_entitled: true,
                        entitlement_message: entitlement_message.clone(),
                        current_time: current_time_on_air,
                        is_row_focused: true,
                        is_focused: false,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT_FOCUSED,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                    // Is airing now, row is inactive
                    AiringCard(
                        airing_card: mock_airing_card.clone(),
                        is_entitled: true,
                        entitlement_message: entitlement_message.clone(),
                        current_time: current_time_on_air,
                        is_row_focused: false,
                        is_focused: false,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                    // Upcoming, row is active, card is focused
                    AiringCard(
                        airing_card: mock_airing_card.clone(),
                        is_entitled: true,
                        entitlement_message: entitlement_message.clone(),
                        current_time: current_time_upcoming,
                        is_row_focused: true,
                        is_focused: true,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT_FOCUSED,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                    // Upcoming, row is active, card is not focused
                    AiringCard(
                        airing_card: mock_airing_card.clone(),
                        is_entitled: true,
                        entitlement_message: entitlement_message.clone(),
                        current_time: current_time_upcoming,
                        is_row_focused: true,
                        is_focused: false,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT_FOCUSED,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                    // Upcoming, row is inactive
                    AiringCard(
                        airing_card: mock_airing_card.clone(),
                        is_entitled: true,
                        entitlement_message: entitlement_message.clone(),
                        current_time: current_time_upcoming,
                        is_row_focused: false,
                        is_focused: false,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT,
                        is_last_card: true,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                }
                .main_axis_alignment(MainAxisAlignment::SpacedBy(12.0))
                // Unentitled
                Column() {
                    // Is airing now, row is active, card is focused
                    AiringCard(
                        airing_card: mock_airing_card_clone.clone(),
                        is_entitled: false,
                        entitlement_message: entitlement_message_clone.clone(),
                        current_time: current_time_on_air,
                        is_row_focused: true,
                        is_focused: true,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT_FOCUSED_UNENTITLED,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                    // Is airing now, row is active, card is not focused
                    AiringCard(
                        airing_card: mock_airing_card_clone.clone(),
                        is_entitled: false,
                        entitlement_message: entitlement_message_clone.clone(),
                        current_time: current_time_on_air,
                        is_row_focused: true,
                        is_focused: false,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT_FOCUSED_UNENTITLED,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                    // Is airing now, row is inactive
                    AiringCard(
                        airing_card: mock_airing_card_clone.clone(),
                        is_entitled: false,
                        entitlement_message: entitlement_message_clone.clone(),
                        current_time: current_time_on_air,
                        is_row_focused: false,
                        is_focused: false,
                        width: 744.0,
                        height: STATION_ROW_HEIGHT,
                        is_last_card: true,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::String("".to_string()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                }
                .main_axis_alignment(MainAxisAlignment::SpacedBy(12.0))
            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .padding(Padding::all(12.0))
            .background_color(get_ignx_color(FableColor::BACKGROUND))
        }
    });
}
