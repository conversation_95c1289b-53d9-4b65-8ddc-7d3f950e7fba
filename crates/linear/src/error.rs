use crate::types::RequestError;
use amzn_fable_tokens::{FableColor, FableSpacing};
use app_reporting::kpi_reporting::report_cx_fatal;
use fableous::buttons::primary_button::*;
use fableous::common_string_ids::{
    AV_LRC_GENERIC_ERROR_TITLE, AV_LRC_LANDING_PAGE_EMPTY_CAROUSEL_LIST_ERROR_MESSAGE,
    AV_LRC_RETRY_BUTTON_TEXT,
};
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::focusable::FocusableComposable;
use ignx_compositron::layout::MainAxisAlignment;
use ignx_compositron::show::*;
use ignx_compositron::text::{LocalizedText, TextContent};
use ignx_compositron::{compose, Composer};
use location::RustPage;
use std::rc::Rc;

const OFFSET_TOP: f32 = 323.0;
const OFFSET_LEFT: f32 = 213.0;
const MAX_WIDTH: f32 = 882.0;

#[Composer]
pub fn LinearPageError(
    ctx: &AppContext,
    error: &RequestError,
    on_retry: Rc<dyn Fn()>,
    page_type: RustPage,
) -> ColumnComposable {
    report_cx_fatal(page_type);
    let (error_title, error_message, should_render_retry_button) =
        if let RequestError::TravelingCustomerException { heading, message } = error {
            (
                TextContent::String(heading.clone()),
                TextContent::String(message.clone()),
                false,
            )
        } else {
            (
                TextContent::LocalizedText(LocalizedText::new(AV_LRC_GENERIC_ERROR_TITLE)),
                TextContent::LocalizedText(LocalizedText::new(
                    AV_LRC_LANDING_PAGE_EMPTY_CAROUSEL_LIST_ERROR_MESSAGE,
                )),
                true,
            )
        };

    compose! {
        Column() {
            Column() {
                TypographyHeading600(content: error_title).test_id("linear_page_error_title")
                TypographyBody400(content: error_message).test_id("linear_page_error_message")
            }
            .width(MAX_WIDTH)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))

            if should_render_retry_button {
                PrimaryButton(variant: PrimaryButtonVariant::TextSize400(
                    TextContent::LocalizedText(LocalizedText::new(AV_LRC_RETRY_BUTTON_TEXT)),
                ))
                .on_select({
                    let on_retry = on_retry.clone();
                    move || on_retry()
                })
                .test_id("linear_page_error_retry_button")
            }
        }
        .width(fableous::SCREEN_WIDTH)
        .height(fableous::SCREEN_HEIGHT)
        .background_color(get_ignx_color(FableColor::BACKGROUND))
        .translate_x(OFFSET_LEFT)
        .translate_y(OFFSET_TOP)
        .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING200))
        .test_id("linear_page_error_screen")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::prelude::create_signal;
    use ignx_compositron::reactive::SignalSet;
    use ignx_compositron::stack::*;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use std::rc::Rc;

    #[test]
    fn renders_elements() {
        launch_test(
            |ctx| {
                compose! {
                    LinearPageError(error: &RequestError::BadRequest, on_retry: Rc::new(|| {}), page_type: RustPage::RUST_LIVE_TV)
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let title = tree.find_by_test_id("linear_page_error_title");
                let message = tree.find_by_test_id("linear_page_error_message");
                let retry_button = tree.find_by_test_id("linear_page_error_retry_button");
                assert_node_exists!(&title);
                assert_node_exists!(&message);
                assert_node_exists!(&retry_button);
            },
        )
    }

    #[test]
    fn does_not_render_retry_for_traveling_customer() {
        launch_test(
            |ctx| {
                compose! {
                    LinearPageError(error: &RequestError::TravelingCustomerException { heading: "heading".to_string(), message: "message".to_string() }, on_retry: Rc::new(|| {}), page_type: RustPage::RUST_LIVE_TV)
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let title = tree.find_by_test_id("linear_page_error_title");
                let message = tree.find_by_test_id("linear_page_error_message");
                let retry_button = tree.find_by_test_id("linear_page_error_retry_button");
                assert_node_exists!(&title);
                assert_node_exists!(&message);
                assert_node_does_not_exist!(&retry_button);
                assert_eq!(title.borrow_props().text, Some("heading".to_string()));
                assert_eq!(message.borrow_props().text, Some("message".to_string()));
            },
        )
    }

    #[test]
    fn on_retry_invokes_callback() {
        launch_test(
            |ctx| {
                let (callback_status, callback_status_setter) =
                    create_signal(ctx.scope(), "Not Called".to_string());
                let callback = Rc::new(move || callback_status_setter.set("Called".to_string()));

                compose! {
                    Stack() {
                        LinearPageError(error: &RequestError::UnknownError, on_retry: callback, page_type: RustPage::RUST_LIVE_TV)
                        TypographyBody400(content: callback_status).test_id("test_status")
                    }
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                assert_eq!(
                    tree.find_by_test_id("test_status").borrow_props().text,
                    Some("Not Called".to_string())
                );

                let retry_button = tree.find_by_test_id("linear_page_error_retry_button");
                test_loop.send_on_select_event(retry_button.borrow_props().node_id);
                let tree = test_loop.tick_until_done();

                assert_eq!(
                    tree.find_by_test_id("test_status").borrow_props().text,
                    Some("Called".to_string())
                );
            },
        )
    }
}
