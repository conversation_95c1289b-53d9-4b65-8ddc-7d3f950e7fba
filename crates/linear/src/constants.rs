use amzn_fable_tokens::{FableColor, FableSpacing, FableText};
use fableous::SCREEN_HEIGHT;
use ignx_compositron::color::Color;
use ignx_compositron::layout::{MainAxisAlignment, Padding};

use crate::thumbnail::ThumbnailDimensions;
use crate::ui::epg::station_row::AIRING_CARD_ROW_PADDING;

/// EXCEPTIONS
pub const TRAVELING_CUSTOMER_EXCEPTION: &str = "TravelingCustomerException";
pub const NO_CONTENT_EXCEPTION: &str = "NoContentException";

/// IDENTIFIERS
pub const FAVORITES_FILTER_TAG: &str = "linear-live-tv-favorites-filter";

/// TEST IDs
pub const HERO_DETAILS_TEST_ID: &str = "live_page_hero_details";
pub const FILTER_LIST_TEST_ID: &str = "live_page_filter_list";
pub const EPG_TEST_ID: &str = "live_page_epg";
pub const SIGN_IN_MODAL_ID: &str = "live_page_sign_in_modal";
pub const FAVORITES_FILTER_FALLBACK_TEST_ID: &str = "favorites_filter_fallback";

/// LIVE PAGE
pub const SIDE_NAVIGATION_ALLOWANCE_PADDING: Padding = Padding {
    start: 144.0,
    end: 0.0,
    top: 0.0,
    bottom: 0.0,
};
pub const LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN: f32 = 111.0;
pub const LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN_NO_HERO: f32 = 189.0;
pub const LIVE_PAGE_HERO_HEIGHT: f32 = 549.0;
// Content offset when content is focused
pub const LIVE_PAGE_CONTENT_OFFSET_FOCUSED_NO_HERO: f32 = 78.0;
pub const LIVE_PAGE_CONTENT_OFFSET_FOCUSED: f32 = 336.0 - LIVE_PAGE_HERO_HEIGHT;
pub const LIVE_PAGE_CONTENT_OFFSET_FOCUSED_STATIC_BG: f32 =
    LIVE_PAGE_CONTENT_OFFSET_FOCUSED_NO_HERO - LIVE_PAGE_HERO_HEIGHT;
// Content height (filter list / epg)
pub const LIVE_PAGE_CONTENT_HEIGHT: f32 = SCREEN_HEIGHT - 336.0;
pub const LIVE_PAGE_CONTENT_HEIGHT_NO_HERO: f32 =
    SCREEN_HEIGHT - LIVE_PAGE_CONTENT_OFFSET_FOCUSED_NO_HERO;
// Used to adjust offset/content height when pointer control is enabled but always on is disabled
pub const POINTER_CONTROL_ADJUST: f32 = 90.0;

/// -----------
/// FILTER LIST
/// -----------
// the gap spacing between the buttons in the filter list
pub const FILTER_LIST_BUTTONS_SPACING: MainAxisAlignment = MainAxisAlignment::SpacedBy(12.0);
// Width of the filter list container on Live Page
pub const FILTER_LIST_CONTAINER_WIDTH: f32 = 246.0;
// Space between filter list and EPG
pub const FILTER_LIST_CONTAINER_GAP: f32 = 24.0;
pub const FILTER_LIST_CONTAINER_SPACING: MainAxisAlignment =
    MainAxisAlignment::SpacedBy(FILTER_LIST_CONTAINER_GAP);
pub const FILTER_LIST_PADDING: Padding = Padding {
    start: 0.0,
    end: 0.0,
    top: HEADER_TIMELINE_HEIGHT - HEADER_TIMELINE_VERTICAL_OFFSET,
    bottom: 0.0,
};

/// -------------
/// LIVE PAGE EPG
/// -------------
pub const LIVE_PAGE_EPG_WIDTH: f32 = 1506.0;
// Width of the EPG when there's no Filter List
pub const LIVE_PAGE_EXPANDED_EPG_WIDTH: f32 =
    LIVE_PAGE_EPG_WIDTH + FILTER_LIST_CONTAINER_GAP + FILTER_LIST_CONTAINER_WIDTH;
// Width added to text area of all "currently showing" cards
pub const CURRENTLY_SHOWING_CARD_TEXT_MINIMUM_WIDTH: f32 = 282.0;
// Total width added to all "currently showing" cards
pub const CURRENTLY_SHOWING_CARD_ADDED_WIDTH: f32 =
    PROGRAM_IMAGE_WIDTH + CURRENTLY_SHOWING_CARD_TEXT_MINIMUM_WIDTH;
// Width of an hour in pixels
pub const HOUR_WIDTH: f32 = 720.0;
// Width of a half hour in pixels for time line segments
pub const HALF_HOUR_WIDTH: f32 = 360.0;

/// -----------------
/// HEADER & TIMELINE
/// -----------------
pub const EPG_GAP: f32 = 8.0;
pub const HEADER_CURRENT_TIME_MARKER_WIDTH: f32 = 115.0;
pub const HEADER_CURRENT_TIME_MARKER_START_OFFSET: f32 =
    PROGRAM_IMAGE_WIDTH_FOCUSED - STATION_HORIZONTAL_SCROLL_PEEK_WIDTH;
pub const HEADER_CURRENT_TIME_MARKER_TRACK_WIDTH: f32 = 568.0;
pub const HEADER_CURRENT_TIME_MARKER_CHEVRON_WIDTH: f32 = 30.0;
pub const HEADER_CURRENT_TIME_MARKER_CHEVRON_HEIGHT: f32 = 20.0;
pub const HEADER_TITLE_WIDTH: f32 =
    STATION_LOGO_WIDTH + EPG_GAP + STATION_HORIZONTAL_SCROLL_PEEK_WIDTH;
pub const HEADER_TIMELINE_HEIGHT: f32 = 57.0;
pub const HEADER_TIMELINE_LABEL_SPACING: f32 = 12.0;
pub const HEADER_TIMELINE_VERTICAL_OFFSET: f32 = 6.0;

/// -----------
/// STATION ROW
/// -----------
pub const STATION_ROW_HEIGHT: f32 = 128.0;
pub const STATION_ROW_HEIGHT_FOCUSED: f32 = 170.0;
pub const STATION_ROW_HEIGHT_FOCUSED_UNENTITLED: f32 = 201.0;
// Vertical gap between EPG station rows
pub const STATION_ROW_GAP: f32 = 8.0;
// Height station header text above station row
pub const STATION_HEADER_HEIGHT: f32 = 44.0;
pub const STATION_HORIZONTAL_SCROLL_PEEK_WIDTH: f32 = 207.0;
// Gap to screen edge when scrolled all the way
pub const STATION_ROW_END_MARGIN: f32 = 35.0 + AIRING_CARD_BORDER_WIDTH;

/// ------------
/// STATION LOGO
/// ------------
pub const STATION_LOGO_WIDTH: f32 = 180.0;
pub const STATION_LOGO_IMAGE_WIDTH: f32 = 150.0;
pub const STATION_LOGO_IMAGE_HEIGHT: f32 = 64.0;
pub const STATION_LOGO_BORDER_RADIUS: f32 = 12.0;
pub const FAVORITES_ICON_OFFSET: f32 = 6.0;

/// -----------
/// AIRING CARD
/// -----------
pub const AIRING_CARD_DESCRIPTION_MIN_WIDTH: f32 = 64.0;
pub const AIRING_CARD_DESCRIPTION_SPACING: f32 = 8.0;
pub const AIRING_CARD_RATING_AND_DESCRIPTOR_SPACING: f32 = 12.0;
pub const AIRING_CARD_DESCRIPTION_START_PADDING: f32 = 24.0;
pub const AIRING_CARD_PROGRESS_SCRIM_PADDING: Padding = Padding {
    start: PROGRAM_IMAGE_WIDTH,
    end: 0.0,
    top: 0.0,
    bottom: 0.0,
};
// space between AiringCards, either vertical or horizontal
pub const AIRING_CARD_MARGIN: f32 = 8.0;
pub const AIRING_CARD_BORDER_WIDTH: f32 = 4.0; // TODO: Update to 3.0 to match the standard carousel
pub const AIRING_CARD_BORDER_RADIUS: f32 = 12.0;
pub const AIRING_CARD_FOCUS_RING_BORDER_RADIUS: f32 =
    AIRING_CARD_BORDER_RADIUS + AIRING_CARD_BORDER_WIDTH;
pub const PROGRAM_IMAGE_WIDTH: f32 = 228.0;
pub const PROGRAM_IMAGE_WIDTH_FOCUSED: f32 = 302.0;
pub const IMAGE_CORNER_MASK_BORDER_WIDTH: f32 = 2.0;
pub const AIRING_CARD_THUMBNAIL_ICON_START_OFFSET: f32 = 14.0;
pub const AIRING_CARD_THUMBNAIL_ICON_TOP_OFFSET: f32 = 94.0;
pub const AIRING_CARD_END_GRADIENT_WIDTH: f32 = 32.0;
pub const AIRING_CARD_END_GRADIENT_OPACITY: f32 = 0.8;
pub const PROGRESS_BAR_GRADIENT_UNENTITLED_HEIGHT: f32 = 50.0;
pub const PROGRESS_BAR_GRADIENT_ENTITLED_HEIGHT: f32 = 66.0;
pub const PROGRESS_BAR_PADDING_ENTITLED: Padding = Padding {
    start: 12.0,
    end: 12.0,
    top: 0.0,
    bottom: 10.0,
};
pub const PROGRESS_BAR_PADDING_UNENTITLED: Padding = Padding {
    start: 12.0,
    end: 12.0,
    top: 0.0,
    bottom: 15.0,
};
pub const END_CARD_DURATION_MS: i64 = 8 * 3_600_000;
pub const AIRING_CARD_FOCUSED_MIN_WIDTH: f32 = 420.0;
pub const AIRING_CARD_TTS_BORDER_WIDTH: f32 = 12.0;
pub const TTS_BORDER_COLOR: FableColor = FableColor {
    r: 89,
    g: 184,
    b: 59,
    a: 255,
};

pub const LIVE_PAGE_THUMBNAIL_DIMENSIONS: ThumbnailDimensions = ThumbnailDimensions {
    gradient_top_offset: 104.0,
    gradient_top_offset_unentitled: 120.0,
    progress_bar_width: 278.0,
    height_with_progress_bar: STATION_ROW_HEIGHT_FOCUSED,
    height_with_progress_bar_unentitled: STATION_ROW_HEIGHT_FOCUSED_UNENTITLED,
    width_with_progress_bar: PROGRAM_IMAGE_WIDTH_FOCUSED,
};

pub const STATION_DETAILS_THUMBNAIL_DIMENSIONS: ThumbnailDimensions = ThumbnailDimensions {
    gradient_top_offset: 63.0,
    gradient_top_offset_unentitled: 63.0,
    progress_bar_width: 204.0,
    height_with_progress_bar: STATION_ROW_HEIGHT,
    height_with_progress_bar_unentitled: STATION_ROW_HEIGHT,
    width_with_progress_bar: PROGRAM_IMAGE_WIDTH,
};

/// -----------------
/// STANDARD CAROUSEL
/// -----------------
// Card
pub const STANDARD_CAROUSEL_CARD_SPACING: f32 = 24.0;
pub const STANDARD_CAROUSEL_MINI_DETAILS_HEIGHT: f32 = 169.0;
pub const STANDARD_CAROUSEL_MINI_DETAILS_MAX_WIDTH: f32 = 740.0;
pub const STANDARD_CAROUSEL_CARD_WIDTH: f32 = 384.0;
pub const STANDARD_CAROUSEL_CARD_HEIGHT: f32 = 216.0;
pub const STANDARD_CAROUSEL_CARD_BORDER_RADIUS: f32 = 12.0;
pub const STANDARD_CAROUSEL_CARD_AND_DETAILS_SPACING: f32 = 15.0;
pub const STANDARD_CAROUSEL_ROW_PADDING: Padding = Padding {
    start: 0.0,
    end: 0.0,
    top: STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH,
    bottom: 0.0,
};
pub const STANDARD_CAROUSEL_HEADER_PADDING: f32 = 9.0;
pub const STANDARD_CAROUSEL_HEADER_LINE_HEIGHT: f32 =
    FableText::TYPE_LABEL500_EMPHASIS.line_height as f32;
pub const STANDARD_CAROUSEL_HEIGHT: f32 = STANDARD_CAROUSEL_CARD_HEIGHT
    + STANDARD_CAROUSEL_HEADER_LINE_HEIGHT
    + STANDARD_CAROUSEL_HEADER_PADDING
    + STANDARD_CAROUSEL_ROW_PADDING.top
    + STANDARD_CAROUSEL_ROW_PADDING.bottom;

// Focus Ring
pub const STANDARD_CAROUSEL_FOCUS_RING_BORDER_RADIUS: f32 = 14.0;
pub const STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH: f32 = 3.0;
pub const STANDARD_CAROUSEL_FOCUS_RING_GAP: f32 = 3.0; // Gap between card and ring
pub const STANDARD_CAROUSEL_FOCUS_RING_WIDTH: f32 = STANDARD_CAROUSEL_CARD_WIDTH
    + STANDARD_CAROUSEL_FOCUS_RING_GAP * 2.0
    + STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH * 2.0;
pub const STANDARD_CAROUSEL_FOCUS_RING_HEIGHT: f32 = STANDARD_CAROUSEL_CARD_HEIGHT
    + STANDARD_CAROUSEL_FOCUS_RING_GAP * 2.0
    + STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH * 2.0;
pub const STANDARD_CAROUSEL_TITLE_HEIGHT: f32 = 42.0;
pub const STANDARD_CAROUSEL_FOCUS_RING_VERTICAL_OFFSET: f32 = STANDARD_CAROUSEL_TITLE_HEIGHT
    - STANDARD_CAROUSEL_FOCUS_RING_GAP
    - STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH;
pub const STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET: f32 =
    -STANDARD_CAROUSEL_FOCUS_RING_GAP - STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH;

// Mini Detials
pub const MINI_DETAILS_RATING_AND_TIME_SPACING: f32 = 16.0;
pub const MINI_DETAILS_DESCRIPTION_SPACING: f32 = 8.0;

/// -------------
/// DETAILS MODAL
/// -------------
pub const MODAL_OVERLAY_HEIGHT: f32 = 894.0;
// No Fable equivalent exists. UX asked that we use this custom color.
pub const MODAL_BACKGROUND_COLOR: Color = Color {
    r: 16,
    g: 18,
    b: 21,
    a: 255,
};
pub const MODAL_BORDER_RADIUS: f32 = 32.0;
pub const MODAL_WIDTH: f32 = 1581.0;
pub const MODAL_HEIGHT: f32 = 498.0;
pub const MODAL_BOTTOM_OFFSET: f32 = -71.0;

/// ----------------
/// POINTER CONTROLS
/// ----------------
pub const LIVE_PAGE_EPG_POINTER_CONTROLS_TOP_OFFSET: f32 = -102.0;
pub const LIVE_PAGE_EPG_POINTER_OVERLAY_HERO_HEIGHT: f32 = 396.0;
pub const LIVE_PAGE_EPG_POINTER_OVERLAY_HERO_HEIGHT_ALWAYS_ON_DISABLED: f32 = 498.0;

/// ----------------
/// STATION DETAILS
/// ----------------
/// Top edge of content shown below Title Details
pub const DETAILS_CONTENT_VERTICAL_OFFSET: f32 = 510.0;
// Space between StationDetailButtons and EPG
pub const DETAIL_BUTTONS_CONTAINER_SPACING: MainAxisAlignment =
    MainAxisAlignment::SpacedBy(FableSpacing::SPACING400);

// Station Logo dimensions
pub const STATION_DETAILS_LOGO_MAX_WIDTH: f32 = 400.0;
pub const STATION_DETAILS_LOGO_MAX_HEIGHT: f32 = 100.0;

// Width between trailing edge of On Now thumbnail and first time marker
pub const DETAILS_HEADER_CURRENT_TIME_MARKER_TRACK_WIDTH: f32 = 568.0;
pub const DETAILS_HEADER_TITLE_WIDTH: f32 = 300.0;

// Offset from left side of screen to first airing card
pub const FIRST_AIRING_CARD_HORIZONTAL_OFFSET: f32 =
    AIRING_CARD_ROW_PADDING.start + SIDE_NAVIGATION_ALLOWANCE_PADDING.start;
