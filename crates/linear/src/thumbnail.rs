use crate::constants::{
    AIRING_CARD_BORDER_RADIUS, AIRING_CARD_THUMBNAIL_ICON_START_OFFSET,
    AIRING_CARD_THUMBNAIL_ICON_TOP_OFFSET, IMAGE_CORNER_MASK_BORDER_WIDTH, PROGRAM_IMAGE_WIDTH,
    PROGRESS_BAR_GRADIENT_ENTITLED_HEIGHT, PROGRESS_BAR_GRADIENT_UNENTITLED_HEIGHT,
    PROGRESS_BAR_PADDING_ENTITLED, PROGRESS_BAR_PADDING_UNENTITLED, STATION_ROW_HEIGHT,
};
use crate::utils::context_utils::get_time_to_interactive_registry_context;
use crate::utils::tti_utils::TTI_PART_EPG_THUMBNAIL;
use amzn_fable_tokens::FableColor;
use fableous::animations::fable_motion_linear_medium;
use fableous::progress_bar::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::context::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, compose_option, Composer};
use location::{PageType, RustPage};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageAlignment;
use lrc_image::types::*;
use router::hooks::try_use_location;
use std::rc::Rc;
use title_details::components::entitlement::*;

pub struct ThumbnailDimensions {
    pub progress_bar_width: f32,
    pub gradient_top_offset: f32,
    pub gradient_top_offset_unentitled: f32,
    pub height_with_progress_bar: f32,
    pub height_with_progress_bar_unentitled: f32,
    pub width_with_progress_bar: f32,
}

#[Composer]
pub fn Thumbnail(
    ctx: &AppContext,
    url: Option<String>,
    is_entitled: bool,
    #[into] progress: MaybeSignal<f32>,
    #[into] show_progress_bar: MaybeSignal<bool>,
    #[into] should_animate: MaybeSignal<bool>,
    index: usize,
    dimensions: ThumbnailDimensions,
) -> impl Composable<'static> {
    let ThumbnailDimensions {
        progress_bar_width,
        width_with_progress_bar,
        height_with_progress_bar,
        height_with_progress_bar_unentitled,
        gradient_top_offset,
        gradient_top_offset_unentitled,
    } = dimensions;

    let width = create_rw_signal(ctx.scope(), PROGRAM_IMAGE_WIDTH);
    let height = create_rw_signal(ctx.scope(), STATION_ROW_HEIGHT);
    let image_height = create_rw_signal(ctx.scope(), STATION_ROW_HEIGHT);
    let progress_bar_opacity = create_rw_signal(ctx.scope(), 0.0);
    let unentitled_icon_opacity = create_rw_signal(ctx.scope(), 1.0);
    let page = match try_use_location(ctx.scope()) {
        Some(location) => location.get().pageType,
        None => PageType::Rust(RustPage::RUST_LIVE_TV),
    };

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let update = move || {
            if show_progress_bar.get() {
                width.set(width_with_progress_bar);
                image_height.set(height_with_progress_bar);
                progress_bar_opacity.set(1.0);
                unentitled_icon_opacity.set(0.0);

                if is_entitled {
                    height.set(height_with_progress_bar);
                } else {
                    height.set(height_with_progress_bar_unentitled);
                }
            } else {
                width.set(PROGRAM_IMAGE_WIDTH);
                height.set(STATION_ROW_HEIGHT);
                image_height.set(STATION_ROW_HEIGHT);
                progress_bar_opacity.set(0.0);
                unentitled_icon_opacity.set(1.0);
            }
        };

        move |_| {
            if should_animate.get() {
                ctx.with_animation(fable_motion_linear_medium(), update);
            } else {
                update();
            }
        }
    });

    let entitlement_icon_data = DataSignal::<EntitlementIconDataSignal>::from_data(
        ctx.scope(),
        EntitlementIconData {
            icon_type: EntitlementIconType::OFFER,
            size: EntitlementLabelSize::Small,
        },
    );

    let registry = get_time_to_interactive_registry_context(ctx.scope(), page);

    compose! {
        Stack() {
            // Thumbnail image
            Memo(item_builder: Box::new(move |ctx| {
                if let Some(url) = url.clone() {
                    compose_option! {
                        LRCImage(data: ImageData {
                            url,
                            width: width_with_progress_bar + IMAGE_CORNER_MASK_BORDER_WIDTH * 2.0,
                            height: height_with_progress_bar + IMAGE_CORNER_MASK_BORDER_WIDTH * 2.0,
                            tags: vec![
                                ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions {
                                    alignment: ImageAlignment::Centered,
                                    hide_canvas: true,
                                })),
                                ImageTag::RoundedCorners(lrc_image::types::RoundedCorners {
                                    radius: AIRING_CARD_BORDER_RADIUS as i32,
                                    border_width: IMAGE_CORNER_MASK_BORDER_WIDTH as i32,
                                    rgba_border_color: "0,0,0,0".to_string(),
                                    rgba_background_color: "0,0,0,0".to_string(),
                                    corner_mask: CornerMask::TopBottomLeft,
                                }),
                                ImageTag::Cropping(
                                    CroppingStrategy::Crop(
                                        CropDimensions {
                                            x: IMAGE_CORNER_MASK_BORDER_WIDTH as i32,
                                            y: IMAGE_CORNER_MASK_BORDER_WIDTH as i32,
                                            width: width_with_progress_bar as i32,
                                            height: height_with_progress_bar as i32,
                                        }
                                    )
                                ),
                                ImageTag::Format(ImageFormat::PNG),
                            ],
                        })
                        .width(width)
                        .height(image_height)
                        .test_id("thumbnail_image")
                        .on_image_result({
                            let reg = Rc::clone(&registry);
                            move |res: &ImageLoadResultEvent|
                                if res == &ImageLoadResultEvent::Success { reg.set_part_ready(format!("{TTI_PART_EPG_THUMBNAIL}_{index}")) }
                            }
                        )
                    }
                } else {
                    None
                }
            }))
            if !is_entitled {
                Stack() {
                    // Unentitled icon
                    Stack() {
                        LRCImage(data: ImageData {
                            url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/epg-card-thumbnail-unentitled-gradient.png".to_string(),
                            width: PROGRAM_IMAGE_WIDTH,
                            height: STATION_ROW_HEIGHT,
                            tags: vec![ImageTag::Format(ImageFormat::PNG)]
                        })
                        .width(width)
                        .height(image_height)
                        EntitlementIcon(data: entitlement_icon_data)
                        .translate_x(AIRING_CARD_THUMBNAIL_ICON_START_OFFSET)
                        .translate_y(AIRING_CARD_THUMBNAIL_ICON_TOP_OFFSET)
                    }
                    .opacity(unentitled_icon_opacity)
                    .test_id("thumbnail_unentitled_icon")
                    // Progress bar gradient
                    LRCImage(data: ImageData {
                        url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/epg-card-thumbnail-progress-bar-gradient.png".to_string(),
                        width: width_with_progress_bar,
                        height: PROGRESS_BAR_GRADIENT_UNENTITLED_HEIGHT,
                        tags: vec![
                            ImageTag::Scaling(ScalingStrategy::ScaleToHeight),
                            ImageTag::Format(ImageFormat::PNG)
                        ]
                    })
                    .translate_y(gradient_top_offset_unentitled)
                    .opacity(progress_bar_opacity)
                    .test_id("thumbnail_progress_bar_unentitled_gradient")
                }
            } else {
                // Entitled progress bar gradient
                    LRCImage(data: ImageData {
                        url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/epg-card-thumbnail-progress-bar-gradient-entitled.png".to_string(),
                        width: width_with_progress_bar,
                        height: PROGRESS_BAR_GRADIENT_ENTITLED_HEIGHT,
                        tags: vec![
                            ImageTag::Scaling(ScalingStrategy::ScaleToHeight),
                            ImageTag::Format(ImageFormat::PNG)
                        ]
                    })
                    .translate_y(gradient_top_offset)
                    .opacity(progress_bar_opacity)
                    .test_id("thumbnail_progress_bar_entitled_gradient")
            }

            // Progress bar
            Column() {
                ProgressBar(
                    width: MaybeSignal::Static(Some(progress_bar_width)),
                    progress,
                    variant: ProgressBarVariant::OnAir,
                    rounded: true
                )
            }
            .width(width)
            .height(height)
            .main_axis_alignment(MainAxisAlignment::End)
            .padding(if is_entitled { PROGRESS_BAR_PADDING_ENTITLED } else { PROGRESS_BAR_PADDING_UNENTITLED })
            .opacity(progress_bar_opacity)
            .test_id("thumbnail_progress_bar")
        }
        .background_color(get_ignx_color(FableColor::BLACK))
        .width(width)
        .height(height)
        .test_id("thumbnail_wrapper")
    }
}

#[cfg(test)]
mod test {
    use crate::constants::{PROGRAM_IMAGE_WIDTH, STATION_ROW_HEIGHT};
    use crate::thumbnail::*;
    use cross_app_events::tti_registry::TimeToInteractiveRegistry;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::*;
    use location::{PageType, RustPage};
    use rstest::*;
    use std::rc::Rc;

    const MOCK_DIMENSIONS: ThumbnailDimensions = ThumbnailDimensions {
        gradient_top_offset: 50.0,
        gradient_top_offset_unentitled: 40.0,
        progress_bar_width: 250.0,
        height_with_progress_bar: 165.0,
        height_with_progress_bar_unentitled: 180.0,
        width_with_progress_bar: 250.0,
    };

    fn setup_thumbnail(
        ctx: &AppContext,
        is_entitled: bool,
        show_progress_bar: bool,
    ) -> impl Composable<'static> {
        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            ctx.scope(),
            vec![],
            PageType::Rust(RustPage::RUST_LIVE_TV),
            None,
        ));
        provide_context(ctx.scope(), tti_registry);
        compose! {
            Thumbnail(
                url: Some("https://www.example.com/image.png".to_string()),
                progress: 0.5,
                is_entitled,
                show_progress_bar,
                should_animate: false,
                index: 0,
                dimensions: MOCK_DIMENSIONS,
            )
        }
    }

    #[rstest]
    #[case::entitled_hide_progress(
        true,
        false,
        PROGRAM_IMAGE_WIDTH,
        STATION_ROW_HEIGHT,
        STATION_ROW_HEIGHT
    )]
    #[case::entitled_show_progress(
        true,
        true,
        MOCK_DIMENSIONS.width_with_progress_bar,
        MOCK_DIMENSIONS.height_with_progress_bar,
        MOCK_DIMENSIONS.height_with_progress_bar,
    )]
    #[case::unentitled_hide_progress(
        false,
        false,
        PROGRAM_IMAGE_WIDTH,
        STATION_ROW_HEIGHT,
        STATION_ROW_HEIGHT
    )]
    // Unentitled progress bar
    #[case::unentitled_show_progress(
        false,
        true,
        MOCK_DIMENSIONS.width_with_progress_bar,
        MOCK_DIMENSIONS.height_with_progress_bar_unentitled,
        MOCK_DIMENSIONS.height_with_progress_bar,
    )]
    fn it_renders_with_correct_size(
        #[case] is_entitled: bool,
        #[case] show_progress: bool,
        #[case] expected_width: f32,
        #[case] expected_height: f32,
        #[case] expected_image_height: f32,
    ) {
        launch_test(
            move |ctx| setup_thumbnail(&ctx, is_entitled, show_progress),
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let wrapper = tree.find_by_test_id("thumbnail_wrapper");
                let image = tree.find_by_test_id("thumbnail_image");

                assert_node_exists!(&wrapper);
                assert_eq!(wrapper.borrow_props().layout.size.width, expected_width);
                assert_eq!(wrapper.borrow_props().layout.size.height, expected_height);

                assert_node_exists!(&image);
                assert_eq!(image.borrow_props().layout.size.width, expected_width);

                assert_eq!(
                    image.borrow_props().layout.size.height,
                    expected_image_height
                );
            },
        )
    }

    #[rstest]
    #[case(true, false, 0.0)]
    #[case(true, true, 1.0)]
    #[case(false, false, 0.0)]
    #[case(false, true, 1.0)]
    fn it_renders_progress_bar_correctly(
        #[case] is_entitled: bool,
        #[case] show_progress: bool,
        #[case] expected_opacity: f32,
    ) {
        launch_test(
            move |ctx| setup_thumbnail(&ctx, is_entitled, show_progress),
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let progress_bar = tree.find_by_test_id("thumbnail_progress_bar");
                let gradient_id = if is_entitled {
                    "thumbnail_progress_bar_entitled_gradient"
                } else {
                    "thumbnail_progress_bar_unentitled_gradient"
                };
                let gradient = tree.find_by_test_id(gradient_id);

                assert_node_exists!(&progress_bar);
                assert_eq!(
                    progress_bar.borrow_props().base_styles.opacity,
                    Some(expected_opacity)
                );
                assert_node_exists!(&gradient);
                assert_eq!(
                    gradient.borrow_props().base_styles.opacity,
                    Some(expected_opacity)
                );
            },
        )
    }

    #[rstest]
    #[case(false)]
    #[case(true)]
    fn it_does_not_render_unentitled_components_when_entitled(#[case] show_progress: bool) {
        launch_test(
            move |ctx| setup_thumbnail(&ctx, true, show_progress),
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let unentitled_gradient =
                    tree.find_by_test_id("thumbnail_progress_bar_unentitled_gradient");
                let unentitled_icon = tree.find_by_test_id("thumbnail_unentitled_icon");

                assert_node_does_not_exist!(&unentitled_gradient);
                assert_node_does_not_exist!(&unentitled_icon);
            },
        )
    }

    #[rstest]
    #[case(false)]
    #[case(true)]
    fn it_renders_unentitled_components(#[case] show_progress: bool) {
        launch_test(
            move |ctx| setup_thumbnail(&ctx, false, show_progress),
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let icon = tree.find_by_test_id("thumbnail_unentitled_icon");
                let gradient = tree.find_by_test_id("thumbnail_progress_bar_unentitled_gradient");

                assert_node_exists!(&gradient);
                assert_node_exists!(&icon);
                if show_progress {
                    assert_eq!(icon.borrow_props().base_styles.opacity, Some(0.0));
                    assert_eq!(gradient.borrow_props().base_styles.opacity, Some(1.0));
                } else {
                    assert_eq!(icon.borrow_props().base_styles.opacity, Some(1.0));
                    assert_eq!(gradient.borrow_props().base_styles.opacity, Some(0.0));
                }
            },
        )
    }

    #[test]
    fn it_renders_thumbnail_without_url() {
        launch_test(
            move |ctx| {
                let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
                    ctx.scope(),
                    vec![],
                    PageType::Rust(RustPage::RUST_LIVE_TV),
                    None,
                ));
                provide_context(ctx.scope(), tti_registry);
                compose! {
                    Thumbnail(
                        url: None,
                        progress: 0.5,
                        is_entitled: true,
                        show_progress_bar: true,
                        should_animate: false,
                        index: 0,
                        dimensions: MOCK_DIMENSIONS,
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let thumbnail_image = tree.find_by_test_id("thumbnail_image");

                assert_node_does_not_exist!(&thumbnail_image);
            },
        )
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn it_animates_only_if_should_animate_is_set(#[case] should_animate: bool) {
        launch_test(
            move |ctx| {
                let (show_progress_bar, show_progress_setter) = create_signal(ctx.scope(), false);
                let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
                    ctx.scope(),
                    vec![],
                    PageType::Rust(RustPage::RUST_LIVE_TV),
                    None,
                ));
                provide_context(ctx.scope(), tti_registry);
                compose! {
                    Stack() {
                        Thumbnail(
                            url: Some("https://www.example.com/image.png".to_string()),
                            is_entitled: false,
                            progress: 0.5,
                            show_progress_bar,
                            should_animate,
                            index: 0,
                            dimensions: MOCK_DIMENSIONS,
                        )

                        Button(text: "Show progress").on_select(move || {
                            show_progress_setter.set(true);
                        }).test_id("show_progress_button")
                    }
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let initial_width = tree
                    .find_by_test_id("thumbnail_wrapper")
                    .borrow_props()
                    .layout
                    .size
                    .width;
                assert_eq!(initial_width, PROGRAM_IMAGE_WIDTH);

                test_loop.send_on_select_event(
                    tree.find_by_test_id("show_progress_button")
                        .borrow_props()
                        .node_id,
                );

                let tree = test_loop.tick_once().node_tree;
                let intermediate_width = tree
                    .find_by_test_id("thumbnail_wrapper")
                    .borrow_props()
                    .layout
                    .size
                    .width;

                if should_animate {
                    assert!(
                        intermediate_width > PROGRAM_IMAGE_WIDTH
                            && intermediate_width < MOCK_DIMENSIONS.width_with_progress_bar,
                    );
                } else {
                    assert_eq!(intermediate_width, MOCK_DIMENSIONS.width_with_progress_bar);
                }

                let tree = test_loop.tick_until_done();
                let final_width = tree
                    .find_by_test_id("thumbnail_wrapper")
                    .borrow_props()
                    .layout
                    .size
                    .width;
                assert_eq!(final_width, MOCK_DIMENSIONS.width_with_progress_bar);
            },
        )
    }
}
