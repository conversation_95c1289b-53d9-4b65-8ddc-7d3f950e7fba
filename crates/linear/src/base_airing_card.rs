use crate::constants::*;
use crate::ui::compose_wrapper::*;
use crate::ui::epg::end_card::*;
use crate::utils::context_utils::get_tts_enabled;
use amzn_fable_tokens::FableColor;
use fableous::animations::fable_motion_linear_medium;
use fableous::utils::get_ignx_color;
use ignx_compositron::context::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::*;

const UNEXPANDED_BACKGROUND_OPACITY: f32 = 0.8;
const BACKGROUND_COLOR: Color = get_ignx_color(FableColor::INVERSE_EMPHASIS);

//  This component wraps the content of an airing card with:
//   End Card - when `is_last_card` == true
//   Progress Scrim - when `is_on_air` == true
//   End gradient
//   Focus Ring
// # Arguments
//   `is_focused` - focus state of card
//   `width` - width of the unfocused airing card in the EPG
//   `card_width` - current display width of the airing card
//   `height` - height of the card
//   `max_width` - maximum width the card can expand to
//   `progress_scrim_width` - width of the progress scrim on a On Now card
//   `end_gradient_opacity` - opacity of end gradient
//   `is_on_air` - is the airing On Now
//   `is_last_card` - determines if placholder card is displayed at end of card
#[Composer]
pub fn BaseAiringCard<F, C>(
    ctx: &AppContext,
    #[into] is_focused: MaybeSignal<bool>,
    #[into] width: MaybeSignal<f32>,
    #[into] card_width: RwSignal<f32>,
    #[into] height: MaybeSignal<f32>,
    #[into] max_width: MaybeSignal<f32>,
    #[into] progress_scrim_width: MaybeSignal<f32>,
    #[into] end_gradient_opacity: MaybeSignal<f32>,
    is_on_air: Memo<bool>,
    #[into] is_last_card: MaybeSignal<bool>,
    content: F,
) -> StackComposable
where
    C: Composable<'static> + 'static,
    F: Fn(&AppContext) -> C + 'static,
{
    let background_color = create_rw_signal(
        ctx.scope(),
        BACKGROUND_COLOR.apply_opacity(UNEXPANDED_BACKGROUND_OPACITY),
    );

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |was_focused| {
            let is_focused = is_focused.get();
            let focused_min_width = AIRING_CARD_FOCUSED_MIN_WIDTH.min(max_width.get());
            let unfocused_width = width.get() - AIRING_CARD_MARGIN;
            let should_expand = is_focused && focused_min_width > unfocused_width;

            let update_width = move || {
                if should_expand {
                    card_width.set(focused_min_width);
                    background_color.set(BACKGROUND_COLOR); // 100% opacity when expanded
                } else {
                    card_width.set(unfocused_width);
                    background_color
                        .set(BACKGROUND_COLOR.apply_opacity(UNEXPANDED_BACKGROUND_OPACITY));
                }
            };
            // Update with animation if focus state change
            if was_focused.is_some_and(|prev| prev != is_focused) {
                ctx.with_animation(fable_motion_linear_medium(), update_width);
            } else {
                update_width();
            }
            is_focused
        }
    });

    let focus_ring_width = Signal::derive(ctx.scope(), move || {
        card_width.get() + AIRING_CARD_BORDER_WIDTH * 2.0
    });

    let focus_ring_height = Signal::derive(ctx.scope(), move || {
        height.get() + AIRING_CARD_BORDER_WIDTH * 2.0
    });

    let focus_ring_opacity = Signal::derive(
        ctx.scope(),
        move || {
            if is_focused.get() {
                1.0
            } else {
                0.0
            }
        },
    );

    let end_gradient_offset = Signal::derive(ctx.scope(), move || {
        card_width.get() - AIRING_CARD_END_GRADIENT_WIDTH
    });

    let is_tts_enabled = get_tts_enabled(ctx.scope());

    let border_width = Signal::derive(ctx.scope(), move || {
        if is_tts_enabled.get() {
            AIRING_CARD_TTS_BORDER_WIDTH
        } else {
            AIRING_CARD_BORDER_WIDTH
        }
    });
    let border_color = Signal::derive(ctx.scope(), move || {
        if is_tts_enabled.get() {
            get_ignx_color(TTS_BORDER_COLOR)
        } else {
            get_ignx_color(FableColor::PRIMARY)
        }
    });

    compose! {
        Stack() {
            if is_last_card.get() {
                EndCard(height)
                    .translate_x(width)
            }
            // Follows EndCard to render on top
            Rectangle()
                .width(card_width)
                .height(height)
                .background_color(background_color)
                .border_radius(AIRING_CARD_BORDER_RADIUS)
                .test_id("airing_card_background")

            if is_on_air.get() {
                // Progress scrim - outside content row to not be affected by content animation
                Rectangle()
                .background_color(get_ignx_color(FableColor::WARM100).apply_opacity(0.05))
                .width(progress_scrim_width)
                .height(height)
                .test_id("airing_card_progress")
            }

            // Internal content
            Wrapper(component: content(ctx))

            // End gradient
            LRCImage(data: ImageData{
                url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/airing-card-end-gradient.png".to_string(),
                width: AIRING_CARD_END_GRADIENT_WIDTH,
                height: STATION_ROW_HEIGHT,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::UpscaleToRectangle),
                    ImageTag::RoundedCorners(
                        lrc_image::types::RoundedCorners {
                            radius: AIRING_CARD_BORDER_RADIUS as i32,
                            border_width: IMAGE_CORNER_MASK_BORDER_WIDTH as i32,
                            rgba_border_color: "0,0,0,0".to_string(),
                            rgba_background_color: "0,0,0,0".to_string(),
                            corner_mask: CornerMask::TopBottomRight
                        }),
                    ImageTag::Cropping(
                        CroppingStrategy::Crop(
                            CropDimensions {
                                x: IMAGE_CORNER_MASK_BORDER_WIDTH as i32,
                                y: IMAGE_CORNER_MASK_BORDER_WIDTH as i32,
                                width: AIRING_CARD_END_GRADIENT_WIDTH as i32,
                                height: STATION_ROW_HEIGHT as i32
                            }
                        )
                    ),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .opacity(end_gradient_opacity)
            .width(AIRING_CARD_END_GRADIENT_WIDTH)
            .height(height)
            .translate_x(end_gradient_offset)
            .test_id("airing_card_end_gradient")

            // Airing Card Focus Ring
            Rectangle()
            .test_id("airing_card_focus_ring")
            .width(focus_ring_width)
            .height(focus_ring_height)
            .background_color(get_ignx_color(FableColor::TRANSPARENT))
            .border_width(border_width)
            .border_color(border_color)
            .border_radius(AIRING_CARD_FOCUS_RING_BORDER_RADIUS)
            .translate_x(-AIRING_CARD_BORDER_WIDTH)
            .translate_y(-AIRING_CARD_BORDER_WIDTH)
            .opacity(focus_ring_opacity)
        }
        .width(card_width)
        .height(height)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::prelude::provide_context;
    use ignx_compositron::test_utils::*;
    use ignx_compositron::tts::TTSEnabledContext;
    use rstest::*;

    const TEST_CARD_WIDTH: f32 = 744.0;
    const BACKGROUND_COLOR: Color = get_ignx_color(FableColor::INVERSE_EMPHASIS);
    const SCRIM_WIDTH: f32 = TEST_CARD_WIDTH / 2.0;

    fn setup_contexts(ctx: &AppContext, is_tts_enabled: bool) {
        provide_context(
            ctx.scope(),
            TTSEnabledContext(create_signal(ctx.scope(), is_tts_enabled).0),
        );
    }

    fn setup_airing_card(
        ctx: &AppContext,
        is_on_air: bool,
        is_focused: bool,
        height: f32,
        is_last_card: bool,
        tts_enabled: bool,
    ) -> impl Composable<'static> {
        setup_contexts(ctx, tts_enabled);
        let is_on_air = create_memo(ctx.scope(), move |_| is_on_air);

        compose! {
          BaseAiringCard(
            is_on_air,
            is_focused,
            width: TEST_CARD_WIDTH,
            card_width: create_rw_signal(ctx.scope(), TEST_CARD_WIDTH),
            height,
            is_last_card,
            max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
            progress_scrim_width: SCRIM_WIDTH,
            end_gradient_opacity: 1.0,
            content: move |ctx| {
                compose! {
                    Label(text: "")
                    .test_id("airing_card_content")
               }
            }
          )
        }
    }

    #[rstest]
    #[case::on_now_and_focused(true, true)]
    #[case::on_now_and_not_focused(true, false)]
    #[case::not_on_now_and_not_focused(false, false)]
    fn it_renders_airing_card(#[case] is_on_air: bool, #[case] is_focused: bool) {
        launch_test(
            move |ctx| {
                setup_airing_card(
                    &ctx,
                    is_on_air,
                    is_focused,
                    STATION_ROW_HEIGHT,
                    false,
                    false,
                )
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let background = tree.find_by_test_id("airing_card_background");
                let content = tree.find_by_test_id("airing_card_content");
                let end_gradient = tree.find_by_test_id("airing_card_end_gradient");
                let focus_ring = tree.find_by_test_id("airing_card_focus_ring");

                assert_node_exists!(&content);
                assert_node_exists!(&end_gradient);
                assert_node_exists!(&background);
                assert_eq!(
                    background.borrow_props().layout.size.width,
                    TEST_CARD_WIDTH - AIRING_CARD_MARGIN
                );
                assert_eq!(
                    background.borrow_props().layout.size.height,
                    STATION_ROW_HEIGHT
                );
                let background_color = background
                    .borrow_props()
                    .base_styles
                    .background_color
                    .unwrap();
                assert_eq!(background_color, BACKGROUND_COLOR.apply_opacity(0.8));

                if is_focused {
                    assert_node_exists!(&focus_ring);
                    assert_eq!(focus_ring.borrow_props().base_styles.opacity.unwrap(), 1.0);
                    assert_eq!(
                        focus_ring.borrow_props().layout.size.width,
                        TEST_CARD_WIDTH - AIRING_CARD_MARGIN + AIRING_CARD_BORDER_WIDTH * 2.0,
                    );
                    assert_eq!(
                        focus_ring.borrow_props().layout.size.height,
                        STATION_ROW_HEIGHT + AIRING_CARD_BORDER_WIDTH * 2.0,
                    );
                } else {
                    assert_eq!(focus_ring.borrow_props().base_styles.opacity.unwrap(), 0.0);
                }

                if is_on_air {
                    assert_node_exists!(tree.find_by_test_id("airing_card_progress"));
                } else {
                    assert_node_does_not_exist!(tree.find_by_test_id("airing_card_progress"));
                }
            },
        )
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn it_renders_end_card_if_this_is_last_card(#[case] is_last_card: bool) {
        launch_test(
            move |ctx| setup_airing_card(&ctx, true, true, STATION_ROW_HEIGHT, is_last_card, false),
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let end_card = tree.find_by_test_id("station_row_end_card");

                if is_last_card {
                    assert_node_exists!(end_card);
                } else {
                    assert_node_does_not_exist!(end_card);
                }
            },
        )
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn it_changes_focus_ring_color_tts_enabled(#[case] tts_enabled: bool) {
        launch_test(
            move |ctx| setup_airing_card(&ctx, true, true, STATION_ROW_HEIGHT, false, tts_enabled),
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let focus_ring = tree.find_by_test_id("airing_card_focus_ring");

                let expected_border_width = if tts_enabled {
                    AIRING_CARD_TTS_BORDER_WIDTH
                } else {
                    AIRING_CARD_BORDER_WIDTH
                };
                let expected_border_color = if tts_enabled {
                    get_ignx_color(TTS_BORDER_COLOR)
                } else {
                    get_ignx_color(FableColor::PRIMARY)
                };

                assert_eq!(
                    focus_ring.borrow_props().base_styles.border_width.unwrap(),
                    expected_border_width
                );
                assert_eq!(
                    focus_ring.borrow_props().base_styles.border_color.unwrap(),
                    expected_border_color
                );
            },
        )
    }

    mod expand_on_focus {
        use super::*;
        use node_properties::SceneNodeTree;

        const UNFOCUSED_TOTAL_WIDTH: f32 = 20.0;
        const UNFOCUSED_CARD_WIDTH: f32 = UNFOCUSED_TOTAL_WIDTH - AIRING_CARD_MARGIN;

        fn setup(ctx: &AppContext, max_width: f32) -> impl Composable<'static> {
            let is_focused = create_rw_signal(ctx.scope(), false);
            setup_contexts(ctx, false);

            compose! {
              Stack() {
                  Button(text: "Focus card".to_string())
                      .on_select(move || is_focused.update(|is_focused| *is_focused = !*is_focused))
                      .test_id("toggle_focus_btn")

                      BaseAiringCard(
                        is_on_air: create_memo(ctx.scope(), move |_| false),
                        is_focused,
                        width: UNFOCUSED_TOTAL_WIDTH,
                        card_width: create_rw_signal(ctx.scope(), TEST_CARD_WIDTH),
                        height: STATION_ROW_HEIGHT,
                        is_last_card: false,
                        max_width,
                        progress_scrim_width: SCRIM_WIDTH,
                        end_gradient_opacity: 1.0,
                        content: move |ctx| {
                            compose! {
                                Label(text: "")
                                .test_id("airing_card_content")
                           }
                        }
                      )
                  .test_id("airing_card")
              }
            }
        }

        #[test]
        fn width_expands_and_shrinks_with_focus() {
            let max_width = AIRING_CARD_FOCUSED_MIN_WIDTH + 20.0;

            launch_test(
                move |ctx| setup(&ctx, max_width),
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();

                    // Unfocused
                    let card = tree.find_by_test_id("airing_card");
                    let card_width = card.borrow_props().layout.size.width;
                    assert_eq!(card_width, UNFOCUSED_CARD_WIDTH);

                    let btn = tree.find_by_test_id("toggle_focus_btn");
                    test_loop.send_on_select_event(btn.borrow_props().node_id);
                    let tree = test_loop.tick_until_done();

                    // Focused
                    let card = tree.find_by_test_id("airing_card");
                    let card_width = card.borrow_props().layout.size.width;
                    assert_eq!(card_width, AIRING_CARD_FOCUSED_MIN_WIDTH);

                    let btn = tree.find_by_test_id("toggle_focus_btn");
                    test_loop.send_on_select_event(btn.borrow_props().node_id);
                    let tree = test_loop.tick_until_done();

                    // Unfocused
                    let card = tree.find_by_test_id("airing_card");
                    let card_width = card.borrow_props().layout.size.width;
                    assert_eq!(card_width, UNFOCUSED_CARD_WIDTH);
                },
            )
        }

        #[test]
        fn expanded_width_does_not_exceed_max_width() {
            let max_width = AIRING_CARD_FOCUSED_MIN_WIDTH - 20.0;

            launch_test(
                move |ctx| setup(&ctx, max_width),
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();

                    // Unfocused
                    let card = tree.find_by_test_id("airing_card");
                    let card_width = card.borrow_props().layout.size.width;
                    assert_eq!(card_width, UNFOCUSED_CARD_WIDTH);

                    let btn = tree.find_by_test_id("toggle_focus_btn");
                    test_loop.send_on_select_event(btn.borrow_props().node_id);
                    let tree = test_loop.tick_until_done();

                    // Focused
                    let card = tree.find_by_test_id("airing_card");
                    let card_width = card.borrow_props().layout.size.width;
                    assert_eq!(card_width, max_width);
                },
            )
        }

        #[test]
        fn background_is_opaque_when_expanded() {
            let max_width = AIRING_CARD_FOCUSED_MIN_WIDTH + 20.0;

            launch_test(
                move |ctx| setup(&ctx, max_width),
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();
                    // Unfocused -- not expanded
                    assert_eq!(
                        get_background_color(&tree),
                        BACKGROUND_COLOR.apply_opacity(0.8)
                    );

                    let btn = tree.find_by_test_id("toggle_focus_btn");
                    test_loop.send_on_select_event(btn.borrow_props().node_id);
                    let tree = test_loop.tick_until_done();
                    // Focused -- expanded
                    assert_eq!(get_background_color(&tree), BACKGROUND_COLOR);

                    let btn = tree.find_by_test_id("toggle_focus_btn");
                    test_loop.send_on_select_event(btn.borrow_props().node_id);
                    let tree = test_loop.tick_until_done();
                    // Unfocused -- not expanded
                    assert_eq!(
                        get_background_color(&tree),
                        BACKGROUND_COLOR.apply_opacity(0.8)
                    );
                },
            )
        }

        #[test]
        fn card_width_updates_when_width_update() {
            let initial_width = 100.0;
            launch_test(
                move |ctx| {
                    let (width, set_width) = create_signal(ctx.scope(), initial_width);
                    provide_context(ctx.scope(), set_width);

                    setup_contexts(&ctx, false);
                    compose! {
                          BaseAiringCard(
                            is_on_air: create_memo(ctx.scope(), move |_| false),
                            is_focused: false,
                            width,
                            card_width: create_rw_signal(ctx.scope(), TEST_CARD_WIDTH),
                            height: STATION_ROW_HEIGHT,
                            is_last_card: false,
                            max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                            progress_scrim_width: SCRIM_WIDTH,
                            end_gradient_opacity: 1.0,
                            content: move |ctx| {
                                compose! {
                                    Label(text: "")
                                    .test_id("airing_card_content")
                               }
                            }
                          )
                      .test_id("airing_card")
                    }
                },
                move |scope, mut test_loop| {
                    let set_width = expect_context::<WriteSignal<f32>>(scope);

                    // Initial width
                    let tree = test_loop.tick_until_done();
                    let card = tree.find_by_test_id("airing_card");
                    let card_width = card.borrow_props().layout.size.width;
                    assert_eq!(card_width, initial_width - AIRING_CARD_MARGIN);

                    // Updated width
                    let new_width = 200.0;
                    set_width.set(new_width);

                    let tree = test_loop.tick_until_done();
                    let card = tree.find_by_test_id("airing_card");
                    let card_width = card.borrow_props().layout.size.width;
                    assert_eq!(card_width, new_width - AIRING_CARD_MARGIN);
                },
            );
        }

        fn get_background_color(tree: &SceneNodeTree) -> Color {
            tree.find_by_test_id("airing_card_background")
                .borrow_props()
                .base_styles
                .background_color
                .unwrap()
        }
    }
}
