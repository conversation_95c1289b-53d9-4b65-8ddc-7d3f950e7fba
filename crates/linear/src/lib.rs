pub mod cache;
pub mod constants;
pub mod features;
pub mod station_logo;

pub mod live_page {
    pub mod carousel_list;
    pub mod data_provider {
        pub mod network_data_provider;
        #[cfg(test)]
        pub use network_data_provider::mock_mocks as mock_network_data_provider;
        pub mod live_page_data_provider;
        pub mod types;
        pub mod watch_history_list_controller;
    }
    pub mod effects_manager;
    pub mod favorites_filter_fallback;
    pub mod hero_details;
    pub mod hero_details_adapter;
    pub mod live_page_content;
    pub mod live_page_ui;
    pub mod pointer_control_overlays;
    pub mod reactive_row_helper;
    pub mod skeleton;
}

pub mod station_details {
    pub mod details_airing_card;
    pub mod details_page_load;
    #[cfg(test)]
    pub use details_page_load::mock_mocks as mock_details_page_load;
    pub mod details_time_header;
    pub mod epg {
        pub mod details_epg_item;
        pub mod details_epg_row;
        pub mod details_epg_utils;
    }
    pub mod details_pointer_controls;
    pub mod favorite_button;
    pub mod station_actions_row;
    pub mod station_details_content;
    pub mod station_details_page;
    pub mod station_details_skeleton;
    pub mod types;
    pub mod utils {
        pub mod focus_effects_util;
        pub mod jump_to_live_utils;
        pub mod pagination_utils;
        pub mod reactive_helper;
    }
    pub mod data_provider {
        pub mod network_data_provider;
        #[cfg(test)]
        pub use network_data_provider::mock_mocks as mock_network_data_provider;
    }
}

pub mod modal {
    pub mod more_details;
    pub mod more_details_hint;
    pub mod more_details_modal;
    pub mod overlay;
    pub mod transition_controller;
    pub mod types;
}

pub mod ui {
    pub mod epg {
        pub mod constants;
        pub mod end_card;
        pub mod epg_item;
        pub mod epg_util;
        pub mod focus_calculator;
        pub mod pointer_controls;
        pub mod station_row;
        pub mod width_calculator;
    }

    pub mod standard_carousel {
        pub mod focus_ring;
        pub mod mini_details;
        pub mod standard_carousel_card;
        pub mod standard_carousel_row;
        pub mod standard_carousel_utils;
    }

    pub mod compose_wrapper;
    pub mod header_bar;
    pub mod pointer_control_button;
    pub mod time_header {
        pub mod current_time_marker;
        pub mod time_segments_list;
    }
}

pub mod network {
    pub mod cache_config;
    pub mod parser;
    pub mod traits;
    pub mod types;
    pub mod url_constructors;
    #[cfg(test)]
    pub use crate::network::traits::MockNetworkClient;
    #[cfg(not(test))]
    pub use network::NetworkClient;
}

pub mod airing_card;
pub mod base_airing_card;
pub mod error;
pub mod favorite_station;
pub mod filter_list;
pub mod thumbnail;
pub mod types;

pub mod utils {
    pub mod common_string_ids;
    pub mod context_utils;
    pub mod datetime_utils;
    pub mod entitlement_parser;
    pub mod favorites_util;
    pub mod location_utils;
    pub mod pagination_tracker;
    pub mod progress_calculator;
    pub mod test_utils;
    pub mod title_details;
    pub mod toast_utils;
    pub mod transition_with_action;
    pub mod tti_utils;
    pub mod tts_utils;
    pub mod types;
    pub mod watch_modal;
}
pub mod metrics {
    pub mod csm_reporter;
    pub mod linear_reporter;
    pub mod live_page_reporter;
    pub mod log_reporter;
    pub mod metric_reporter;
    pub mod page_load_tracker;
    pub mod schedule_data_tracker;
    pub mod station_details_reporter;
}

pub mod prefetch;

#[cfg(any(test, feature = "example_data"))]
pub mod mocks {
    pub mod mock_data_util;
    pub mod mock_details_data_util;
    pub mod mock_response;
    pub mod mock_utc;
}

#[cfg(feature = "example_data")]
pub mod mock_utils {
    pub use crate::network::traits::{
        MockNetworkClient, __mock_MockNetworkClient::__new::Context as MockNetworkClientContext,
    };
}
