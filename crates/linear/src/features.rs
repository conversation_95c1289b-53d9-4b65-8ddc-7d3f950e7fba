// bug in clippy https://github.com/rust-lang/rust-clippy/issues/12824
#![allow(clippy::indexing_slicing)]

use crate::utils::context_utils::{get_pointer_control_active, get_tts_enabled};
use ignx_compositron::device_config::is_resizable_player_enabled;
use ignx_compositron::reactive::*;
use media_background::types::context::MediaBackgroundStatusContext;
use mockall::mock;
#[cfg(not(test))]
use rust_features::{use_rust_features, RustFeatures};
use settings_manager::{try_use_settings_manager, ApplicationSettingsContext};

#[cfg(test)]
use rust_features::{
    use_mock_rust_features as use_rust_features, MockRustFeatures as RustFeatures,
};

pub struct LinearFeatures {
    rust_features: RustFeatures,
    settings_manager: Option<ApplicationSettingsContext>,
    scope: Scope,
}

impl LinearFeatures {
    pub fn new(scope: Scope) -> Self {
        Self {
            rust_features: use_rust_features(scope),
            settings_manager: try_use_settings_manager(scope),
            scope,
        }
    }

    pub fn get_is_always_on_enabled(&self) -> Signal<bool> {
        let rust_features_always_on_enabled = self.rust_features.is_livetv_always_on_enabled();
        let is_linear_always_on_expansion_enabled =
            self.rust_features.is_linear_always_on_expansion_enabled();
        let user_setting_autoplay_enabled = self.settings_manager.as_ref().map_or_else(
            || Signal::default(self.scope),
            |settings| settings.get_autoplay_enabled(),
        );

        let firetv_pcon_viewing_restrictions_enabled = self.settings_manager.as_ref().map_or_else(
            || Signal::default(self.scope),
            |settings| settings.get_firetv_pcon_viewing_restrictions_enabled(),
        );

        let is_resizable_player_enabled = is_resizable_player_enabled();
        let resize_support = use_context::<MediaBackgroundStatusContext>(self.scope)
            .map_or_else(|| Signal::default(self.scope), |c| c.player_resize_support);

        let is_tts_enabled = get_tts_enabled(self.scope);

        Signal::derive(self.scope, move || {
            rust_features_always_on_enabled
                && is_resizable_player_enabled
                && !is_tts_enabled.get()
                && user_setting_autoplay_enabled.get()
                && !firetv_pcon_viewing_restrictions_enabled.get()
                && (resize_support.get().is_some() || is_linear_always_on_expansion_enabled)
        })
    }

    pub fn get_is_pointer_control_enabled(&self) -> bool {
        self.rust_features.is_pointer_control_enabled()
    }

    pub fn get_is_pointer_control_active(&self) -> ReadSignal<bool> {
        get_pointer_control_active(self.scope)
    }

    pub fn get_is_favorite_stations_enabled(&self) -> bool {
        self.rust_features.is_linear_favorite_stations_enabled()
    }

    pub fn trigger_station_favorites_weblab(&self) {
        self.rust_features
            .trigger_feature("ENABLE_LINEAR_FAVORITE_STATIONS_RUST")
    }
}

mock! {
    pub LinearFeatures {
        pub fn new(scope: Scope) -> Self;
        pub fn get_is_always_on_enabled(&self) -> Signal<bool>;
        pub fn get_is_pointer_control_enabled(&self) -> bool;
        pub fn get_is_pointer_control_active(&self) -> ReadSignal<bool>;
        pub fn get_is_favorite_stations_enabled(&self) -> bool;
        pub fn trigger_station_favorites_weblab(&self);
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::pointer_control::PointerControlActiveContext;
    use ignx_compositron::{
        app::launch_only_scope, player_data::ResizeSupport, tts::TTSEnabledContext,
    };
    use mockall::predicate::eq;
    use rstest::*;
    use rust_features::MockRustFeatures;
    use settings_manager::MockApplicationSettings;
    use std::rc::Rc;

    const RUST_FEATURES_ALWAYS_ON_ENABLED: bool = true;
    const RUST_FEATURES_ALWAYS_ON_DISABLED: bool = false;
    const SETTINGS_MANAGER_AUTOPLAY_ENABLED: bool = true;
    const SETTINGS_MANAGER_AUTOPLAY_DISABLED: bool = false;
    const TTS_ENABLED: bool = true;
    const TTS_DISABLED: bool = false;
    const FIRETV_PCON_VIEWING_RESTRICTIONS_ENABLED: bool = true;
    const FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED: bool = false;
    #[rstest]
    #[case(
        RUST_FEATURES_ALWAYS_ON_ENABLED,
        SETTINGS_MANAGER_AUTOPLAY_ENABLED,
        TTS_ENABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_ENABLED,
        SETTINGS_MANAGER_AUTOPLAY_ENABLED,
        TTS_DISABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        true
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_ENABLED,
        SETTINGS_MANAGER_AUTOPLAY_DISABLED,
        TTS_ENABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_ENABLED,
        SETTINGS_MANAGER_AUTOPLAY_DISABLED,
        TTS_DISABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_DISABLED,
        SETTINGS_MANAGER_AUTOPLAY_ENABLED,
        TTS_ENABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_DISABLED,
        SETTINGS_MANAGER_AUTOPLAY_ENABLED,
        TTS_DISABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_DISABLED,
        SETTINGS_MANAGER_AUTOPLAY_DISABLED,
        TTS_ENABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_DISABLED,
        SETTINGS_MANAGER_AUTOPLAY_DISABLED,
        TTS_DISABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_DISABLED,
        false
    )]
    // Test cases where FireTV PCON viewing restrictions is enabled - should always result in false
    #[case(
        RUST_FEATURES_ALWAYS_ON_ENABLED,
        SETTINGS_MANAGER_AUTOPLAY_ENABLED,
        TTS_DISABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_ENABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_ENABLED,
        SETTINGS_MANAGER_AUTOPLAY_ENABLED,
        TTS_ENABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_ENABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_ENABLED,
        SETTINGS_MANAGER_AUTOPLAY_DISABLED,
        TTS_DISABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_ENABLED,
        false
    )]
    #[case(
        RUST_FEATURES_ALWAYS_ON_DISABLED,
        SETTINGS_MANAGER_AUTOPLAY_ENABLED,
        TTS_DISABLED,
        FIRETV_PCON_VIEWING_RESTRICTIONS_ENABLED,
        false
    )]
    fn it_returns_whether_always_on_is_enabled(
        #[case] rust_features_always_on_enabled: bool,
        #[case] user_setting_autoplay_enabled: bool,
        #[case] tts_enabled: bool,
        #[case] firetv_pcon_viewing_restrictions_enabled: bool,
        #[case] expected: bool,
    ) {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, rust_features_always_on_enabled, false, false);
            mock_settings_manager(
                scope,
                user_setting_autoplay_enabled,
                firetv_pcon_viewing_restrictions_enabled,
            );
            mock_tts_enabled(scope, tts_enabled);
            mock_player_resize_support(scope, true);

            let linear_features = LinearFeatures::new(scope);
            assert_eq!(
                linear_features.get_is_always_on_enabled().get_untracked(),
                expected
            );
        })
    }

    #[rstest]
    #[case(true, true)]
    #[case(false, false)]
    fn it_returns_always_on_enabled_when_resize_supported(
        #[case] is_resize_supported: bool,
        #[case] expected: bool,
    ) {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, true, false, false);
            mock_settings_manager(scope, true, false);
            mock_tts_enabled(scope, false);
            mock_player_resize_support(scope, is_resize_supported);

            let linear_features = LinearFeatures::new(scope);
            assert_eq!(
                linear_features.get_is_always_on_enabled().get_untracked(),
                expected
            );
        })
    }

    #[rstest]
    #[case(true, true)]
    #[case(false, false)]
    fn it_returns_always_on_enabled_when_always_on_expansion_enabled(
        #[case] always_on_expansion_enabled: bool,
        #[case] expected: bool,
    ) {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, true, false, always_on_expansion_enabled);
            mock_settings_manager(scope, true, false);
            mock_tts_enabled(scope, false);
            mock_player_resize_support(scope, false);

            let live_tv_features = LinearFeatures::new(scope);
            assert_eq!(
                live_tv_features.get_is_always_on_enabled().get_untracked(),
                expected
            );
        })
    }

    #[test]
    fn it_returns_always_on_disabled_when_media_background_status_not_provided() {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, true, false, false);
            mock_settings_manager(scope, true, false);
            mock_tts_enabled(scope, false);

            let linear_features = LinearFeatures::new(scope);
            assert!(!linear_features.get_is_always_on_enabled().get_untracked());
        })
    }

    #[test]
    fn it_returns_always_on_disabled_when_settings_manager_not_provided() {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, true, false, false);
            mock_tts_enabled(scope, false);
            mock_player_resize_support(scope, true);

            let linear_features = LinearFeatures::new(scope);
            assert!(!linear_features.get_is_always_on_enabled().get_untracked());
        })
    }

    #[rstest]
    // rust_features.is_pointer_control_enabled() returns true
    #[case(true, true)]
    // rust_features.is_pointer_control_enabled() returns false
    #[case(false, false)]
    fn it_returns_whether_pointer_control_is_enabled(
        #[case] rust_features_pointer_control_enabled: bool,
        #[case] expected: bool,
    ) {
        launch_only_scope(move |scope| {
            // Provide rust features
            mock_rust_feature(scope, true, rust_features_pointer_control_enabled, false);

            // Assert feature
            let linear_features = LinearFeatures::new(scope);
            let is_enabled = linear_features.get_is_pointer_control_enabled();
            assert_eq!(is_enabled, expected);
        })
    }

    #[rstest]
    // PointerControlActiveContext returns true
    #[case(true, true)]
    // PointerControlActiveContext returns false
    #[case(false, false)]
    fn it_returns_whether_pointer_control_is_active(
        #[case] pointer_active: bool,
        #[case] expected: bool,
    ) {
        launch_only_scope(move |scope| {
            // Provide rust features
            mock_rust_feature(scope, true, true, false);

            // Provide pointer control context
            let (is_pointer_active, _) = create_signal(scope, pointer_active);
            provide_context(scope, PointerControlActiveContext(is_pointer_active));

            // Assert feature
            let linear_features = LinearFeatures::new(scope);
            let is_active = linear_features
                .get_is_pointer_control_active()
                .get_untracked();
            assert_eq!(is_active, expected);
        })
    }

    #[test]
    fn it_calls_trigger_feature_for_station_favoriting() {
        launch_only_scope(move |scope| {
            // Provide rust features
            let mut mock_rust_features = MockRustFeatures::default();
            mock_rust_features.expect_clone().returning(move || {
                let mut clone = MockRustFeatures::default();
                clone
                    .expect_trigger_feature()
                    .with(eq("ENABLE_LINEAR_FAVORITE_STATIONS_RUST"))
                    .once()
                    .return_const(());
                clone
            });
            provide_context(scope, mock_rust_features);

            // Assert feature
            let linear_features = LinearFeatures::new(scope);
            linear_features.trigger_station_favorites_weblab();
        })
    }

    fn mock_rust_feature(
        scope: Scope,
        is_always_on_enabled: bool,
        is_pointer_control_enabled: bool,
        is_linear_always_on_expansion_enabled: bool,
    ) {
        let mut mock_rust_features = MockRustFeatures::default();
        mock_rust_features.expect_clone().returning(move || {
            let mut clone = MockRustFeatures::default();
            clone
                .expect_is_livetv_always_on_enabled()
                .return_const(is_always_on_enabled);
            clone
                .expect_is_pointer_control_enabled()
                .return_const(is_pointer_control_enabled);
            clone
                .expect_is_linear_always_on_expansion_enabled()
                .return_const(is_linear_always_on_expansion_enabled);
            clone
        });
        provide_context(scope, mock_rust_features);
    }

    fn mock_settings_autoplay_enabled(scope: Scope, is_autoplay_enabled: bool) {
        mock_settings_manager(scope, is_autoplay_enabled, false);
    }

    fn mock_settings_manager(
        scope: Scope,
        is_autoplay_enabled: bool,
        is_firetv_pcon_viewing_restrictions_enabled: bool,
    ) {
        let mut mock_settings_manager = MockApplicationSettings::default();
        mock_settings_manager
            .expect_get_autoplay_enabled()
            .return_const_st(Signal::derive(scope, move || is_autoplay_enabled));
        mock_settings_manager
            .expect_get_firetv_pcon_viewing_restrictions_enabled()
            .return_const_st(Signal::derive(scope, move || {
                is_firetv_pcon_viewing_restrictions_enabled
            }));

        let settings_context: ApplicationSettingsContext = Rc::new(mock_settings_manager);
        provide_context(scope, settings_context);
    }

    fn mock_tts_enabled(scope: Scope, is_tts_enabled: bool) {
        let (is_tts_enabled, _) = create_signal(scope, is_tts_enabled);
        provide_context(scope, TTSEnabledContext(is_tts_enabled));
    }

    fn mock_player_resize_support(scope: Scope, is_resize_supported: bool) {
        let media_background_context = MediaBackgroundStatusContext {
            background_state: Signal::derive(scope, || None),
            player_resize_support: Signal::derive(scope, move || {
                if is_resize_supported {
                    Some(ResizeSupport::Dynamic)
                } else {
                    None
                }
            }),
        };
        provide_context(scope, media_background_context);
    }
}
