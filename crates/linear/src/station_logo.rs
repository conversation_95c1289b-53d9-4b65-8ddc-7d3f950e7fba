use crate::constants::*;
use crate::favorite_station::show_toast;
use crate::favorite_station::{
    StationFavoriteAction::{AddFavorite, RemoveFavorite},
    StationFavoriteTracker,
};
use crate::types::EpgContext;
use crate::ui::epg::focus_calculator::get_station_logo_focus_id;
use crate::utils::common_string_ids::{
    AIV_BLAST_TTS_ROLE_BUTTON, AV_LRC_ADD_TO_FAVORITES, AV_LRC_MOVE_RIGHT_TO_BROWSE_PROGRAMS,
    AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS, AV_LRC_REMOVE_FROM_FAVORITES,
};
use crate::utils::context_utils::{
    get_epg_context, get_time_to_interactive_registry_context, get_tts_enabled,
};
use crate::utils::tti_utils::TTI_PART_STATION_LOGO;
use amzn_fable_tokens::*;
use fableous::animations::{FableMotionDuration, FableMotionEasing, MotionDuration, MotionEasing};
use fableous::badges::icon_badge::*;
use fableous::utils::*;
use ignx_compositron::animation::with_animation;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::focus::FocusEdges;
use ignx_compositron::font::{FontSize, FontWeight};
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::input::KeyCode;
use ignx_compositron::label::*;
use ignx_compositron::layout::*;
use ignx_compositron::prelude::{AccessibilityComposable, Animation, FocusableComposable};
use ignx_compositron::reactive::*;
use ignx_compositron::rectangle::*;
#[allow(unused)]
use ignx_compositron::show::*;
use ignx_compositron::stack::*;
use ignx_compositron::text::{LocalizedText, TextContent, TextVerticalAlignment, TruncationMode};
use ignx_compositron::{compose, Composer};
use location::{PageType, RustPage};
use lrc_image::lrc_image::*;
use lrc_image::types::*;
use router::hooks::try_use_location;
use std::rc::Rc;

#[Composer]
pub fn StationLogo(
    ctx: &AppContext,
    url: String,
    station_name: String,
    gti: String,
    #[into] height: MaybeSignal<f32>,
    #[into] show_shadow: MaybeSignal<bool>,
    #[into] left_navigation_hint: MaybeSignal<TextContent>,
    index: usize,
    on_key_down: Rc<dyn Fn(KeyCode) -> bool>,
    is_favorite: RwSignal<bool>,
    favorites_request_tracker: Rc<StationFavoriteTracker>,
    is_favorites_enabled: bool,
    #[into] is_row_focused: MaybeSignal<bool>,
) -> StackComposable {
    let scope = ctx.scope();
    let page = match try_use_location(scope) {
        Some(location) => location.get().pageType,
        None => PageType::Rust(RustPage::RUST_LIVE_TV),
    };

    let registry = get_time_to_interactive_registry_context(scope, page);
    let is_tts_enabled = get_tts_enabled(scope);

    let EpgContext {
        focus_signal,
        focus_state_id,
        ..
    } = get_epg_context(scope);

    let focus_id = get_station_logo_focus_id(index);
    let id_clone = focus_id.clone();
    let is_preferred_focus = Signal::derive(scope, move || focus_state_id.get() == id_clone);
    let focused = create_focus_signal(scope);

    let frame_height = Signal::derive(scope, move || height.get() + AIRING_CARD_BORDER_WIDTH * 2.0);

    let border_width = Signal::derive(scope, move || {
        if is_tts_enabled.get() {
            AIRING_CARD_TTS_BORDER_WIDTH
        } else {
            AIRING_CARD_BORDER_WIDTH
        }
    });
    let border_color = Signal::derive(scope, move || {
        if is_tts_enabled.get() {
            get_ignx_color(TTS_BORDER_COLOR)
        } else {
            get_ignx_color(FableColor::PRIMARY)
        }
    });
    let focus_ring_opacity = Signal::derive(scope, move || if focused.get() { 1.0 } else { 0.0 });

    let filled_icon_opacity = create_rw_signal(scope, 0.0);
    let empty_icon_opacity = create_rw_signal(scope, 0.0);

    let accessibility_description = Signal::derive(scope, move || {
        get_accessibility_description(is_favorite.get())
    });
    let accessibility_hints = Signal::derive(scope, move || {
        get_accessibility_hints(left_navigation_hint.get())
    });

    create_effect(scope, {
        let update_icon_opacities = move |new_opacity_state| {
            let (new_filled_opacity, new_empty_opacity) = new_opacity_state;
            filled_icon_opacity.set(new_filled_opacity);
            empty_icon_opacity.set(new_empty_opacity);
        };
        move |prev_opacities| {
            let (prev_filled_opacity, _) = prev_opacities.unwrap_or_default();
            let is_favorite = is_favorite.get();
            let new_opacitiy_state = if is_favorite {
                // always show filled icon if favorited
                (1.0, 0.0)
            } else if is_row_focused.get() {
                // only show empty icon if focused
                (0.0, 1.0)
            } else {
                // Hide icons
                (0.0, 0.0)
            };

            // only animate if icon is changing to or from filled state
            if prev_opacities.is_some() && new_opacitiy_state.0 + prev_filled_opacity > 0.0 {
                with_animation(
                    scope,
                    &Animation::default()
                        .with_interpolation(FableMotionEasing::Linear.to_interpolation())
                        .with_duration(FableMotionDuration::Standard.to_duration()),
                    move || update_icon_opacities(new_opacitiy_state),
                );
            } else {
                update_icon_opacities(new_opacitiy_state);
            }

            new_opacitiy_state
        }
    });

    let on_select = {
        let title = station_name.clone();
        move || {
            let new_state = !is_favorite.get_untracked();
            let action = match new_state {
                true => AddFavorite,
                false => RemoveFavorite,
            };

            // Optimistic UI Update
            is_favorite.set(new_state);

            let success_callback = {
                let action = action.clone();
                let title = title.clone();
                move || {
                    show_toast(scope, action.clone(), title.clone(), RustPage::RUST_LIVE_TV);
                }
            };

            let failure_callback = move || {
                // Revert optimistic update
                is_favorite.set(!new_state);
            };

            favorites_request_tracker.update_favorite(
                gti.clone(),
                action,
                success_callback,
                failure_callback,
            );
        }
    };

    compose! {
        Stack() {
            if show_shadow.get() {
                Rectangle()
                .width(STATION_LOGO_WIDTH - STATION_LOGO_BORDER_RADIUS)
                .height(frame_height)
                .translate_y(-AIRING_CARD_BORDER_WIDTH)
                .background_color(get_ignx_color(FableColor::BACKGROUND))
                .test_id("station_logo_background")
            }
            if show_shadow.get() {
                LRCImage(data: MaybeSignal::derive(scope, move || ImageData {
                    url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/epg-card-scroll-gradient-remaster.png".to_string(),
                    width: 72.0,
                    height: height.get() + AIRING_CARD_BORDER_WIDTH * 2.0,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::ScaleToWidth),
                        ImageTag::Format(ImageFormat::PNG)]
                }))
                .translate_x(STATION_LOGO_WIDTH - STATION_LOGO_BORDER_RADIUS)
                .translate_y(-AIRING_CARD_BORDER_WIDTH)
                .test_id("station_logo_shadow")
            }

            Stack() {
                // TODO: Display station logo fallback text
                Label(text: station_name.clone())
                    .vertical_alignment(TextVerticalAlignment::Center)
                    .font_size(FontSize(32)) // TODO: determine a desired font size for fallback text
                    .font_weight(FontWeight::Bold)
                    .color(get_ignx_color(FableColor::TRANSPARENT))
                    .max_lines(Some(3))
                    .truncation_mode(TruncationMode::Tail);

                LRCImage(data: ImageData {
                    url: url.clone(),
                    width: STATION_LOGO_IMAGE_WIDTH,
                    height: STATION_LOGO_IMAGE_HEIGHT,
                    tags: vec![
                        ImageTag::Scaling(
                            ScalingStrategy::ScaleToRectangle(
                                ScaleToRectangleOptions {
                                    alignment: ImageAlignment::Centered,
                                    hide_canvas: true
                                }
                            )
                        ),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .test_id("station_logo_image")
                .on_image_result(move |res: &ImageLoadResultEvent|
                    if res == &ImageLoadResultEvent::Success { registry.set_part_ready(format!("{TTI_PART_STATION_LOGO}_{index}")) }
                )
                // TODO: add fallback capabilities when ready & fail callbacks are implemented, https://issues.amazon.com/issues/LRCP-4197
            }
            .alignment(Alignment::Center)
            .background_color(get_ignx_color(FableColor::COOL800))
            .border_radius(STATION_LOGO_BORDER_RADIUS)
            .width(STATION_LOGO_WIDTH)
            .height(height)

            if is_favorites_enabled {
                Stack() {
                    // Favorited icon
                    IconBadge(icon: FableIcon::PLAYER_HEART_FILLED)
                    .background_color(get_ignx_color(FableColor::TRANSPARENT))
                    .opacity(filled_icon_opacity)
                    .test_id("favorite_icon_filled")
                    // Unfavorited icon
                    IconBadge(icon: FableIcon::PLAYER_HEART)
                    .background_color(get_ignx_color(FableColor::TRANSPARENT))
                    .opacity(empty_icon_opacity)
                    .test_id("favorite_icon_empty")
                }
                .translate_x(FAVORITES_ICON_OFFSET)
                .translate_y(FAVORITES_ICON_OFFSET)
            }

            // Airing Card Focus Ring
            Rectangle()
            .test_id("airing_card_focus_ring")
            .width(STATION_LOGO_WIDTH)
            .height(frame_height)
            .background_color(get_ignx_color(FableColor::TRANSPARENT))
            .border_width(border_width)
            .border_color(border_color)
            .border_radius(AIRING_CARD_FOCUS_RING_BORDER_RADIUS)
            .translate_y(-AIRING_CARD_BORDER_WIDTH)
            .opacity(focus_ring_opacity)
        }
        .focusable()
        .focused_value(focus_signal, focus_id.clone())
        .focused(focused)
        .preferred_focus(is_preferred_focus)
        .blocked_focus_edges(FocusEdges::all())
        .on_key_down(KeyCode::Up, { let on_key_down = Rc::clone(&on_key_down); move || { on_key_down(KeyCode::Up); }})
        .on_key_down(KeyCode::Down, { let on_key_down = Rc::clone(&on_key_down); move || { on_key_down(KeyCode::Down); }})
        .on_key_down(KeyCode::Left, { let on_key_down = Rc::clone(&on_key_down); move || { on_key_down(KeyCode::Left); }})
        .on_key_down(KeyCode::Right, { let on_key_down = Rc::clone(&on_key_down); move || { on_key_down(KeyCode::Right); }})
        .on_key_down(KeyCode::Backspace, { let on_key_down = Rc::clone(&on_key_down); move || { on_key_down(KeyCode::Backspace); }})
        .on_key_down(KeyCode::Escape, { let on_key_down = Rc::clone(&on_key_down); move || { on_key_down(KeyCode::Escape); }})
        .on_select(on_select)
        .test_id(focus_id)
        .accessibility_highlight_disabled(true)
        .accessibility_context_message(TextContent::String(station_name))
        .accessibility_description(accessibility_description)
        .accessibility_role(LocalizedText::new(AIV_BLAST_TTS_ROLE_BUTTON))
        .accessibility_hints(accessibility_hints)
    }
}

fn get_accessibility_description(is_favorite: bool) -> TextContent {
    if is_favorite {
        LocalizedText::new(AV_LRC_REMOVE_FROM_FAVORITES).into()
    } else {
        LocalizedText::new(AV_LRC_ADD_TO_FAVORITES).into()
    }
}

fn get_accessibility_hints(left_navigation_hint: TextContent) -> Vec<TextContent> {
    vec![
        LocalizedText::new(AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS).into(),
        LocalizedText::new(AV_LRC_MOVE_RIGHT_TO_BROWSE_PROGRAMS).into(),
        left_navigation_hint,
    ]
}

#[cfg(test)]
mod test {
    use crate::{
        mocks::mock_utc::MOCK_NOW_MS,
        ui::epg::{constants::HOUR_IN_MS, epg_util::build_epg_context},
        utils::{context_utils::get_toast_context, test_utils::press_select},
    };

    use super::*;
    use crate::network::MockNetworkClient as NetworkClient;
    use crate::utils::common_string_ids::{
        AV_LRC_ADDED_TO_FAVORITES, AV_LRC_REMOVED_FROM_FAVORITES,
    };
    use cross_app_events::tti_registry::TimeToInteractiveRegistry;
    use explicit_signals_service::prelude::ExplicitSignalsServiceOperation;
    use fableous::toasts::toast_context::ToastContext;
    use ignx_compositron::button::*;
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::test_utils::*;
    use ignx_compositron::text::TextContent;
    use ignx_compositron::{app::launch_test, tts::TTSEnabledContext};
    use location::{PageType, RustPage};
    use rstest::rstest;
    use std::rc::Rc;
    use std::sync::atomic::{AtomicBool, Ordering};
    use std::sync::Arc;

    #[test]
    fn it_renders_an_image() {
        launch_test(
            |ctx| setup(&ctx, TestConfig::default(ctx.scope())),
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let image_node = tree.find_by_test_id("station_logo_image");
                assert_node_exists!(&image_node);

                if let node_properties::NodeTypeProperties::Image(image_props) =
                    image_node.get_props().node_type_props
                {
                    assert_eq!(
                        image_props.uri,
                        Some("https://m.media-amazon.com/images/G/01/LCXO_Station_Logos/ET._SR150,64,0,C_FMpng_DB,0_.png".to_string())
                    );
                } else {
                    panic!("node is not an image node");
                }
            },
        )
    }

    #[rstest]
    fn it_only_renders_a_shadow_when_show_shadow_is_true(#[values(true, false)] show_shadow: bool) {
        launch_test(
            move |ctx| {
                setup(
                    &ctx,
                    TestConfig {
                        show_shadow,
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                if show_shadow {
                    assert_node_exists!(tree.find_by_test_id("station_logo_shadow"));
                    assert_node_exists!(tree.find_by_test_id("station_logo_background"));
                } else {
                    assert_node_does_not_exist!(tree.find_by_test_id("station_logo_shadow"));
                    assert_node_does_not_exist!(tree.find_by_test_id("station_logo_background"));
                }
            },
        )
    }

    #[rstest]
    #[case::focused_tts_enabled(true, true)]
    #[case::unfocused_tts_enabled(false, true)]
    #[case::focused_tts_disabled(true, false)]
    #[case::unfocused_tts_disabled(false, false)]
    fn it_renders_focus_ring_based_on_focus_state(
        #[case] focused: bool,
        #[case] tts_enabled: bool,
    ) {
        launch_test(
            move |ctx| {
                let tts_context: TTSEnabledContext =
                    TTSEnabledContext(create_rw_signal(ctx.scope(), tts_enabled).read_only());
                provide_context(ctx.scope(), tts_context);
                setup(
                    &ctx,
                    TestConfig {
                        logo_focused: focused,
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let focus_ring = tree.find_by_test_id("airing_card_focus_ring");

                if focused {
                    let expected_border_width = if tts_enabled {
                        AIRING_CARD_TTS_BORDER_WIDTH
                    } else {
                        AIRING_CARD_BORDER_WIDTH
                    };

                    let expected_border_color = if tts_enabled {
                        get_ignx_color(TTS_BORDER_COLOR)
                    } else {
                        get_ignx_color(FableColor::PRIMARY)
                    };

                    // visibility
                    assert_eq!(focus_ring.borrow_props().base_styles.opacity.unwrap(), 1.0);
                    // border width
                    assert_eq!(
                        focus_ring.borrow_props().base_styles.border_width.unwrap(),
                        expected_border_width
                    );
                    // border color
                    assert_eq!(
                        focus_ring.borrow_props().base_styles.border_color.unwrap(),
                        expected_border_color
                    );
                } else {
                    // visibility
                    assert_eq!(focus_ring.borrow_props().base_styles.opacity.unwrap(), 0.0);
                }
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Up)]
    #[case(KeyCode::Down)]
    #[case(KeyCode::Left)]
    #[case(KeyCode::Right)]
    #[case(KeyCode::Backspace)]
    #[case(KeyCode::Escape)]
    fn it_calls_on_key_down_on_navigation(#[case] key_code: KeyCode) {
        launch_test(
            |ctx| setup(&ctx, TestConfig::default(ctx.scope())),
            move |scope, mut test_loop| {
                let key_down_sig: RwSignal<Option<KeyCode>> = expect_context(scope);

                test_loop.send_key_down_up_event(key_code.clone());
                let _ = test_loop.tick_until_done();

                assert_eq!(key_down_sig.get_untracked(), Some(key_code));
            },
        )
    }

    #[test]
    fn it_does_not_render_favorites_if_disabled() {
        launch_test(
            move |ctx| {
                setup(
                    &ctx,
                    TestConfig {
                        is_favorites_enabled: false,
                        logo_focused: true,
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let empty_favorite_icon_node = tree.find_by_test_id("favorite_icon_empty");
                let filled_favorite_icon_node = tree.find_by_test_id("favorite_icon_filled");

                assert_node_does_not_exist!(&empty_favorite_icon_node);
                assert_node_does_not_exist!(&filled_favorite_icon_node);
            },
        )
    }

    #[rstest]
    fn it_renders_correct_favorites_icon(
        #[values(true, false)] favorites_enabled: bool,
        #[values(true, false)] row_focused: bool,
    ) {
        launch_test(
            move |ctx| {
                setup(
                    &ctx,
                    TestConfig {
                        is_favorites_enabled: favorites_enabled,
                        logo_focused: false,
                        row_focused,
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            move |scope, mut test_loop| {
                let is_favorite_sig = expect_context::<TestConfig>(scope).is_favorite;
                let tree = test_loop.tick_until_done();
                if !favorites_enabled {
                    assert_node_does_not_exist!(&tree.find_by_test_id("favorite_icon_empty"));
                    assert_node_does_not_exist!(&tree.find_by_test_id("favorite_icon_filled"));
                    return;
                }

                if row_focused {
                    assert_only_empty_heart_visible(tree);
                } else {
                    assert_no_heart_visible(tree);
                }

                // set favorite station
                is_favorite_sig.set(true);

                assert_only_filled_heart_visible(test_loop.tick_until_done());
            },
        )
    }

    #[test]
    fn it_uses_correct_add_or_remove_favorite_operation() {
        let is_first_call: Arc<AtomicBool> = Arc::new(AtomicBool::new(true));
        let is_first_call_clone: Arc<AtomicBool> = Arc::clone(&is_first_call);
        let mut network_client = NetworkClient::default();
        network_client
            .expect_call_explicit_signals_service()
            .times(2)
            .returning(
                move |_body, operation, success_callback, _failure_callback| {
                    if is_first_call_clone.load(Ordering::SeqCst) {
                        assert_eq!(operation, ExplicitSignalsServiceOperation::AddFavorite);
                        is_first_call_clone.store(false, Ordering::SeqCst);
                    } else {
                        assert_eq!(operation, ExplicitSignalsServiceOperation::RemoveFavorite)
                    }
                    success_callback();
                },
            );

        launch_test(
            move |ctx| {
                setup(
                    &ctx,
                    TestConfig {
                        is_favorites_enabled: true,
                        network_client: Rc::new(network_client),
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            move |scope, mut test_loop| {
                let is_favorite_sig = expect_context::<TestConfig>(scope).is_favorite;
                let _ = test_loop.tick_until_done();

                assert!(!is_favorite_sig.get_untracked());
                press_select(&mut test_loop, "station_logo_0"); // AddFavorite
                let _ = test_loop.tick_until_done();
                assert!(is_favorite_sig.get_untracked());
                press_select(&mut test_loop, "station_logo_0"); // RemoveFavorite
                let _ = test_loop.tick_until_done();
                assert!(!is_favorite_sig.get_untracked());
            },
        )
    }

    #[test]
    fn it_sets_correct_toast_message_on_favorite_success() {
        let mut network_client = NetworkClient::default();
        network_client
            .expect_call_explicit_signals_service()
            .times(2)
            .returning(|_body, _operation, success_callback, _failure_callback| {
                success_callback();
            });
        launch_test(
            |ctx| {
                setup(
                    &ctx,
                    TestConfig {
                        is_favorites_enabled: true,
                        network_client: Rc::new(network_client),
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            |scope, mut test_loop| {
                let is_favorite_sig = expect_context::<TestConfig>(scope).is_favorite;
                let toast_ctx = expect_context::<ToastContext>(scope);
                let _ = test_loop.tick_until_done();

                // TODO: If additional debounce is added to on_select and ESS calls, modify test to not only work on every on_select
                // Select the logo to trigger toast message
                press_select(&mut test_loop, "station_logo_0");

                // Assert toast is visible and has favorited message
                assert_toast_ctx(toast_ctx, AV_LRC_ADDED_TO_FAVORITES);

                // set station as favorite to ensure signal is correct
                is_favorite_sig.set(true);

                // Select the logo to trigger toast message
                press_select(&mut test_loop, "station_logo_0");

                // Assert toast is visible and has removed from favorites message
                assert_toast_ctx(toast_ctx, AV_LRC_REMOVED_FROM_FAVORITES);
            },
        )
    }

    #[test]
    fn it_optimistically_updates_heart_icon_on_favorite_action() {
        let mut network_client = NetworkClient::default();
        network_client
            .expect_call_explicit_signals_service()
            .times(1)
            .return_const(()); // Request won't complete

        launch_test(
            move |ctx| {
                setup(
                    &ctx,
                    TestConfig {
                        is_favorites_enabled: true,
                        network_client: Rc::new(network_client),
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            move |scope, mut test_loop| {
                let is_favorite_sig = expect_context::<TestConfig>(scope).is_favorite;
                assert_only_empty_heart_visible(test_loop.tick_until_done());
                assert!(!is_favorite_sig.get_untracked());

                press_select(&mut test_loop, "station_logo_0");

                // Immediately after selection, should be optimistically updated
                assert_only_filled_heart_visible(test_loop.tick_until_done());
                assert!(is_favorite_sig.get_untracked());
            },
        )
    }

    #[test]
    fn it_reverts_optimistic_update_on_failure() {
        let mut network_client = NetworkClient::default();

        network_client
            .expect_call_explicit_signals_service()
            .times(1)
            .returning(|_body, _operation, _success_callback, failure_callback| {
                // Simulate a failed request
                failure_callback(network::RequestError::Http {
                    code: 500,
                    body: None,
                    headers: vec![],
                });
            });

        launch_test(
            move |ctx| {
                setup(
                    &ctx,
                    TestConfig {
                        is_favorites_enabled: true,
                        network_client: Rc::new(network_client),
                        ..TestConfig::default(ctx.scope())
                    },
                )
            },
            |scope, mut test_loop| {
                let is_favorite_sig = expect_context::<TestConfig>(scope).is_favorite;
                assert_only_empty_heart_visible(test_loop.tick_until_done());
                assert!(!is_favorite_sig.get_untracked());

                press_select(&mut test_loop, "station_logo_0");

                // After failure, should revert back to initial state
                assert_only_empty_heart_visible(test_loop.tick_until_done());
                assert!(!is_favorite_sig.get_untracked());
            },
        )
    }

    mod tts {
        use crate::utils::common_string_ids::AIV_BLAST_TTS_ROLE_BUTTON;

        use super::*;

        #[test]
        fn it_sets_static_accessbility_props() {
            launch_test(
                |ctx| setup(&ctx, TestConfig::default(ctx.scope())),
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();
                    let accessibility = tree
                        .find_by_test_id("station_logo_0")
                        .get_props()
                        .accessibility
                        .unwrap();

                    assert_eq!(
                        accessibility.context_messages,
                        vec![("Station Name".to_string(), None)]
                    );
                    assert_eq!(
                        accessibility.role,
                        (
                            "".to_string(),
                            Some(LocalizedText::new(AIV_BLAST_TTS_ROLE_BUTTON))
                        )
                    );
                },
            )
        }

        #[rstest]
        #[case(true, AV_LRC_REMOVE_FROM_FAVORITES.to_string())]
        #[case(false, AV_LRC_ADD_TO_FAVORITES.to_string())]
        fn it_sets_accessbility_description(
            #[case] is_favorite: bool,
            #[case] expected_description: String,
        ) {
            launch_test(
                move |ctx| {
                    setup(
                        &ctx,
                        TestConfig {
                            is_favorite: create_rw_signal(ctx.scope(), is_favorite),
                            ..TestConfig::default(ctx.scope())
                        },
                    )
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();
                    let accessibility = tree
                        .find_by_test_id("station_logo_0")
                        .get_props()
                        .accessibility
                        .unwrap();

                    assert_eq!(
                        accessibility.descriptions,
                        vec![(
                            "".to_string(),
                            Some(LocalizedText::new(expected_description))
                        )]
                    );
                },
            )
        }

        #[test]
        fn it_sets_accessbility_hints() {
            launch_test(
                move |ctx| setup(&ctx, TestConfig::default(ctx.scope())),
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();
                    let accessibility = tree
                        .find_by_test_id("station_logo_0")
                        .get_props()
                        .accessibility
                        .unwrap();

                    let expected_hints: Vec<_> = vec![
                        AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS.to_string(),
                        AV_LRC_MOVE_RIGHT_TO_BROWSE_PROGRAMS.to_string(),
                        "LEFT_NAVIGATION_HINT".to_string(),
                    ]
                    .into_iter()
                    .map(|s| ("".to_string(), Some(LocalizedText::new(s))))
                    .collect();

                    assert_eq!(accessibility.hints, expected_hints);
                },
            )
        }
    }

    // TEST UTILITIES

    #[derive(Clone)]
    struct TestConfig {
        show_shadow: bool,
        is_favorites_enabled: bool,
        is_favorite: RwSignal<bool>,
        logo_focused: bool,
        row_focused: bool,
        network_client: Rc<NetworkClient>,
    }

    impl TestConfig {
        fn default(scope: Scope) -> Self {
            TestConfig {
                show_shadow: false,
                is_favorites_enabled: false,
                is_favorite: create_rw_signal(scope, false),
                logo_focused: true,
                row_focused: true,
                network_client: Rc::new(NetworkClient::default()),
            }
        }
    }

    fn setup(ctx: &AppContext, config: TestConfig) -> ignx_compositron::prelude::StackComposable {
        let scope = ctx.scope();
        let TestConfig {
            show_shadow,
            is_favorites_enabled,
            is_favorite,
            logo_focused,
            row_focused,
            network_client,
        } = config.clone();
        provide_context(scope, config);
        let key_down_sig: RwSignal<Option<KeyCode>> = create_rw_signal(scope, None);
        provide_context(scope, key_down_sig.clone());

        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            scope,
            vec![],
            PageType::Rust(RustPage::RUST_LIVE_TV),
            None,
        ));
        provide_context(scope, tti_registry);

        // provides ToastContext
        get_toast_context(scope);

        let (current_time, _) = create_signal(scope, MOCK_NOW_MS);

        let (schedule_data_window, _) =
            create_signal(scope, (MOCK_NOW_MS - HOUR_IN_MS, MOCK_NOW_MS + HOUR_IN_MS));
        let epg_context = build_epg_context(
            scope,
            current_time,
            schedule_data_window.into(),
            HOUR_WIDTH,
            100.0,
            100.0.into(),
            100.0,
            100.0,
        );
        provide_context(scope, epg_context);

        let favorites_request_tracker = Rc::new(StationFavoriteTracker::new(network_client));

        compose! {
            Stack() {
                StationLogo(
                    station_name: "Station Name".to_string(),
                    url: "https://m.media-amazon.com/images/G/01/LCXO_Station_Logos/ET.png".to_string(),
                    height: STATION_ROW_HEIGHT,
                    index: 0,
                    show_shadow,
                    left_navigation_hint: TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()),
                    on_key_down: Rc::new(move |key_code: KeyCode| {
                        key_down_sig.set(Some(key_code));
                        true
                    }),
                    gti: "gti".to_string(),
                    is_favorite,
                    favorites_request_tracker,
                    is_favorites_enabled,
                    is_row_focused: row_focused
                )
                Button(text: "dummy button")
                .preferred_focus(!logo_focused)
            }
        }
    }

    fn assert_toast_ctx(toast_ctx: ToastContext, string_id: &str) {
        assert!(toast_ctx.visible.get_untracked());
        if let Some(TextContent::LocalizedText(text)) = toast_ctx.message.get_untracked() {
            assert_eq!(text.string_id.as_str(), string_id,);
        } else {
            panic!("Toast should have localized text message");
        }
    }

    fn assert_only_filled_heart_visible(tree: SceneNodeTree) {
        assert_heart_visible(tree, false, true);
    }

    fn assert_only_empty_heart_visible(tree: SceneNodeTree) {
        assert_heart_visible(tree, true, false);
    }

    fn assert_no_heart_visible(tree: SceneNodeTree) {
        assert_heart_visible(tree, false, false);
    }

    fn assert_heart_visible(
        tree: SceneNodeTree,
        should_empty_be_visible: bool,
        should_filled_be_visible: bool,
    ) {
        let filled_heart_visible = tree
            .find_by_test_id("favorite_icon_filled")
            .borrow_props()
            .is_visible;

        let empty_heart_visible = tree
            .find_by_test_id("favorite_icon_empty")
            .borrow_props()
            .is_visible;

        // None visible
        if !should_empty_be_visible && !should_filled_be_visible {
            assert!(!filled_heart_visible);
            assert!(!empty_heart_visible);
            return;
        }

        // Only one visible
        if should_empty_be_visible {
            assert!(!filled_heart_visible);
            assert!(empty_heart_visible);
        } else {
            assert!(filled_heart_visible);
            assert!(!empty_heart_visible);
        }
    }
}
