use ignx_compositron::prelude::{create_rw_signal, Sc<PERSON>};

use crate::types::{ReactionSignals, StationRowModel};

use super::context_utils::get_stored_reaction_signals;

pub fn get_station_reactions(station: &StationRowModel, scope: Scope) -> ReactionSignals {
    let stored_reactions = get_stored_reaction_signals(scope);
    match stored_reactions.with_value(|map| map.get(&station.station_gti).cloned()) {
        Some(reactions) => reactions,
        // this should not be hit as all station rows are added to the map in `to_reactive`
        None => ReactionSignals {
            is_favorite: create_rw_signal(scope, station.reactions.is_favorite),
        },
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data_util::mock_station_row_model;
    use ignx_compositron::{
        app::launch_only_scope,
        prelude::{provide_context, SignalGetUntracked},
        reactive::store_value,
    };
    use std::collections::HashMap;

    #[test]
    fn it_gets_signal_from_stored_signals() {
        launch_only_scope(|scope| {
            let station = mock_station_row_model("station1", 3, 60);
            let mut map = HashMap::new();
            map.insert(
                "station1".to_string(),
                ReactionSignals {
                    // set the opposite of the station value in the signal
                    is_favorite: create_rw_signal(scope, !station.reactions.is_favorite),
                },
            );
            provide_context(scope, store_value(scope, map));

            let result_is_favorite = get_station_reactions(&station, scope).is_favorite;

            // value from signal is returned
            assert!(result_is_favorite.get_untracked());
        });
    }

    #[test]
    fn it_returns_new_signal() {
        launch_only_scope(|scope| {
            let station = mock_station_row_model("station1", 3, 60);
            let result_is_favorite = get_station_reactions(&station, scope).is_favorite;

            // value from station reactions is in the default signal
            assert!(!result_is_favorite.get_untracked());
        });
    }
}
