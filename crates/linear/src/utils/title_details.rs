use crate::{
    station_details::epg::details_epg_utils::show_date_for_airing, types::AiringModel,
    utils::datetime_utils::get_formatted_date,
};
use common_transform_types::container_items::{EntitlementMessage, EntitlementMessageIcons};
use linear_common::{
    types::Schedule,
    util::schedule_util::{get_liveliness, is_on_air_at_time},
};
use title_details::{
    components::entitlement::EntitlementIconType,
    core::TitleDetailsData::{self, StationDetailsLayout},
    types::common::{
        EntitlementLabelData, StationDetailsMetadata, StationDetailsTitleData,
        TitleDetailsMetadata::StationDetails,
    },
};

pub fn build_title_details_for_airing(
    prev_key: String,
    entitlement: &Option<EntitlementMessage>,
    airing_details: Option<&AiringModel>,
    current_time: i64,
) -> Option<(String, TitleDetailsData)> {
    if let Some(airing) = airing_details {
        let is_on_air = is_on_air_at_time(airing.get_time_range(), current_time);
        let focus_key = format!("{}_{}", airing.id.clone(), is_on_air);

        // do not rebuild details if they are already being displayed
        if focus_key == prev_key {
            return None;
        }

        // Airing content
        let prefix = airing.localized_hierarchy_context.clone();
        let synopsis = if prefix.is_some() {
            format!(" {}", airing.synopsis.clone().unwrap_or_default()).into()
        } else {
            airing.synopsis.clone()
        };
        let schedule_time = airing
            .localized_time_range
            .as_ref()
            .map(|range| range.as_str().into());
        let schedule_date =
            if schedule_time.is_some() && show_date_for_airing(current_time, airing.start_time) {
                get_formatted_date(airing.start_time)
            } else {
                None
            };
        let maturity_rating_image = airing.rating.clone();
        let content_descriptors = Some(airing.content_descriptors.join(", "));
        let liveliness = get_liveliness(
            is_on_air,
            airing
                .badges
                .as_ref()
                .is_some_and(|badges| badges.apply_live),
        );

        // Station content
        let entitlement_data = match entitlement {
            Some(entitlement) => EntitlementLabelData {
                entitlement_label: entitlement.message.clone(),
                entitlement_icon: match entitlement.icon {
                    Some(EntitlementMessageIcons::ENTITLED_ICON) => {
                        Some(EntitlementIconType::ENTITLED)
                    }
                    Some(EntitlementMessageIcons::OFFER_ICON) => Some(EntitlementIconType::OFFER),
                    Some(EntitlementMessageIcons::ADS_ICON) => None,
                    None => None,
                },
            },
            None => EntitlementLabelData {
                entitlement_icon: None,
                entitlement_label: None,
            },
        };

        let data = StationDetailsLayout(StationDetailsTitleData {
            title: airing.title.to_owned(),
            metadata: StationDetails(StationDetailsMetadata {
                schedule_date,
                schedule_time,
                maturity_rating_image,
                content_descriptors,
                liveliness: Some(liveliness.into()),
            }),
            prefix,
            synopsis,
            entitlement_data,
        });

        Some((focus_key, data))
    } else {
        Some((String::default(), TitleDetailsData::Empty))
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data_util::mock_airing_item;
    use crate::mocks::mock_utc::MOCK_NOW_MS;
    use crate::types::AiringModel;
    use crate::ui::epg::constants::{HOUR_IN_MS, TIME_BLOCK_DURATION_MS};
    use common_transform_types::container_items::ShowContext;
    use common_transform_types::container_items::{EntitlementMessage, EntitlementMessageIcons};
    use ignx_compositron::text::{LocalizedText, SubstitutionParameters, TextContent};
    use liveliness_types::Liveliness;
    use rstest::rstest;
    use std::collections::HashMap;
    use title_details::components::entitlement::EntitlementIconType;
    use title_details::core::TitleDetailsData::{self, StationDetailsLayout};
    use title_details::types::common::{
        EntitlementLabelData, StationDetailsMetadata, StationDetailsTitleData,
        TitleDetailsMetadata::StationDetails,
    };

    #[rstest]
    #[case::on_now(true)]
    #[case::upcoming(false)]
    fn builds_title_details_change_requests(#[case] on_air: bool) {
        let entitlement = Some(EntitlementMessage {
            message: Some("EntitlementMessage message".to_string()),
            icon: Some(EntitlementMessageIcons::ENTITLED_ICON),
        });

        let duration_millis = 30 * 60_000;
        let start: i64 = if on_air {
            MOCK_NOW_MS
        } else {
            MOCK_NOW_MS - TIME_BLOCK_DURATION_MS
        };

        let airing = AiringModel {
            id: "station_id_0".to_string(),
            title: "station_id_0".to_string(),
            start_time: start,
            end_time: start + duration_millis,
            image_url: Some(String::from(
                "https://m.media-amazon.com/images/I/A1-iT7YMNTL.jpg",
            )),
            background_image_url: Some(String::from(
                "https://m.media-amazon.com/images/I/817K9loS9JL.jpg",
            )),
            context: Some(ShowContext {
                season: Some(1),
                episode: Some(5),
                episodeTitle: Some(String::from("Some info")),
            }),
            synopsis: Some(String::from("Synopsis text")),
            rating: Some(String::from("18+")),
            content_descriptors: vec![String::from("Language"), String::from("Violence")],
            localized_hierarchy_context: Some(String::from("S1 E5 Some info")),
            localized_time_range: Some(String::from("6:09 - 6:39 PM")),
            badges: None,
        };

        let req = build_title_details_for_airing(
            String::default(),
            &entitlement,
            Some(&airing),
            MOCK_NOW_MS,
        );

        let expected_liveliness = if on_air {
            Liveliness::OnNow
        } else {
            Liveliness::Upcoming
        };
        assert_eq!(
            req,
            Some((
                format!("{}_{}", airing.id, on_air),
                StationDetailsLayout(StationDetailsTitleData {
                    title: "station_id_0".to_string(),
                    metadata: StationDetails(StationDetailsMetadata {
                        schedule_date: None,
                        schedule_time: Some("6:09 - 6:39 PM".into()),
                        maturity_rating_image: Some("18+".to_string()),
                        content_descriptors: "Language, Violence".to_string().into(),
                        liveliness: Some(expected_liveliness.into()),
                    }),
                    prefix: Some("S1 E5 Some info".to_string()),
                    synopsis: Some(" Synopsis text".to_string()),
                    entitlement_data: EntitlementLabelData {
                        entitlement_label: Some("EntitlementMessage message".to_string()),
                        entitlement_icon: Some(EntitlementIconType::ENTITLED)
                    }
                })
            ))
        );
    }

    #[rstest]
    #[case::on_now(true)]
    #[case::upcoming(false)]
    fn returns_none_when_key_unchanged(#[case] on_air: bool) {
        let duration_millis = 30 * 60_000;
        let start: i64 = if on_air {
            MOCK_NOW_MS
        } else {
            MOCK_NOW_MS - TIME_BLOCK_DURATION_MS
        };
        let airing = mock_airing_item("id".to_string(), start, start + duration_millis);
        let req = build_title_details_for_airing(
            format!("{}_{}", airing.id, on_air),
            &None,
            Some(&airing),
            MOCK_NOW_MS,
        );
        assert!(req.is_none());
    }

    #[test]
    fn builds_title_details_change_requests_when_airing_is_empty() {
        let entitlement = Some(EntitlementMessage {
            message: Some("EntitlementMessage message".to_string()),
            icon: Some(EntitlementMessageIcons::ENTITLED_ICON),
        });

        let duration_millis = 30 * 60_000;
        let start: i64 = MOCK_NOW_MS;

        let airing = AiringModel {
            id: "id".to_string(),
            title: "station_id_0".to_string(),
            start_time: start,
            end_time: start + duration_millis,
            image_url: Some(String::from(
                "https://m.media-amazon.com/images/I/A1-iT7YMNTL.jpg",
            )),
            background_image_url: Some(String::from(
                "https://m.media-amazon.com/images/I/817K9loS9JL.jpg",
            )),
            context: None,
            synopsis: None,
            rating: None,
            content_descriptors: vec![String::from("Language"), String::from("Violence")],
            localized_hierarchy_context: None,
            localized_time_range: Some(String::from("6:09 - 6:39 PM")),
            badges: None,
        };

        let req = build_title_details_for_airing(
            String::default(),
            &entitlement,
            Some(&airing),
            MOCK_NOW_MS,
        );

        assert_eq!(
            req,
            Some((
                format!("{}_{}", airing.id, true),
                StationDetailsLayout(StationDetailsTitleData {
                    title: "station_id_0".to_string(),
                    metadata: StationDetails(StationDetailsMetadata {
                        schedule_date: None,
                        schedule_time: Some("6:09 - 6:39 PM".into()),
                        maturity_rating_image: None,
                        content_descriptors: "Language, Violence".to_string().into(),
                        liveliness: Some(Liveliness::OnNow.into()),
                    }),
                    prefix: None,
                    synopsis: None,
                    entitlement_data: EntitlementLabelData {
                        entitlement_label: Some("EntitlementMessage message".to_string()),
                        entitlement_icon: Some(EntitlementIconType::ENTITLED)
                    }
                })
            ))
        );
    }

    #[test]
    fn builds_title_details_change_requests_with_offer_entitlements() {
        let entitlement = Some(EntitlementMessage {
            message: Some("Entitlement Message".to_string()),
            icon: Some(EntitlementMessageIcons::OFFER_ICON),
        });

        let req =
            build_title_details_for_airing(String::default(), &entitlement, None, MOCK_NOW_MS);

        assert_eq!(req, Some((String::default(), TitleDetailsData::Empty)));
    }

    #[test]
    fn builds_title_details_change_requests_with_ads_entitlements() {
        let entitlement = Some(EntitlementMessage {
            message: Some("Entitlement Message".to_string()),
            icon: Some(EntitlementMessageIcons::ADS_ICON),
        });

        let req =
            build_title_details_for_airing(String::default(), &entitlement, None, MOCK_NOW_MS);

        assert_eq!(req, Some((String::default(), TitleDetailsData::Empty)));
    }

    #[test]
    fn builds_title_details_change_requests_when_no_entitlement_message_exists() {
        let req = build_title_details_for_airing(String::default(), &None, None, MOCK_NOW_MS);

        assert_eq!(req, Some((String::default(), TitleDetailsData::Empty)));
    }

    #[test]
    fn builds_title_details_change_requests_for_airing_on_future_date() {
        let start = MOCK_NOW_MS + { HOUR_IN_MS * 24 };
        let req = build_title_details_for_airing(
            String::default(),
            &None,
            Some(&mock_airing_item(
                "id".to_string(),
                start,
                start + HOUR_IN_MS,
            )),
            MOCK_NOW_MS,
        );

        if let StationDetailsLayout(data) = req.unwrap().1 {
            if let StationDetails(metadata) = data.metadata {
                assert_eq!(
                    metadata.schedule_date.unwrap(),
                    TextContent::LocalizedText(LocalizedText {
                        string_id: "AV_LRC_SINGLE_DAY_DATE_NO_YEAR".into(),
                        substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                            (
                                "shortMonth".to_string(),
                                TextContent::LocalizedText(LocalizedText::new(
                                    "AV_LRC_SHORT_MONTH_MAR"
                                ))
                            ),
                            (
                                "dateNumber".to_string(),
                                TextContent::String("7".to_string())
                            ),
                        ])))
                    })
                );
                assert_eq!(metadata.schedule_time.unwrap(), "2:00 - 3:00".into());
                return;
            }
        }

        panic!("Expected StationDetailsMetadata");
    }
}
