use std::rc::Rc;

use crate::{
    constants::{
        LIVE_PAGE_HERO_HEIGHT, STANDARD_CAROUSEL_HEIGHT, STATION_HEADER_HEIGHT, STATION_ROW_GAP,
        STATION_ROW_HEIGHT,
    },
    station_details::epg::details_epg_utils::get_index_for_airing_id,
    types::{AiringModel, EpgRowModel, LivePageRowReactive},
};
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use fableous::SCREEN_HEIGHT;
use ignx_compositron::prelude::*;
use linear_common::{types::Schedule, util::schedule_util::is_on_air_at_time};
use media_background::types::context::{BackgroundState, MediaBackgroundStatusContext};

const CARDS_VISIBLE_WITH_FILTER_LIST: usize = 4;
const CARDS_VISIBLE_WITH_NO_FILTER_LIST: usize = 5;

pub const TTI_PART_MEDIA_BACKGROUND: &str = "MediaBackground";
pub const TTI_PART_EPG_THUMBNAIL: &str = "EpgThumbnail";
pub const TTI_PART_STATION_LOGO: &str = "StationLogo";
pub const TTI_PART_STANDARD_CAROUSEL_CARD: &str = "StandardCarouselCard";

pub fn get_visible_row_indices(
    content_top_offset: f32,
    has_hero: bool,
    content_list: &[LivePageRowReactive],
) -> (Vec<usize>, Vec<usize>) {
    let mut standard_carousel_indices = vec![];
    let mut station_row_indices = vec![];

    // Initialized to the visible height of the EPG
    let mut remaining_height =
        SCREEN_HEIGHT - content_top_offset - (if has_hero { LIVE_PAGE_HERO_HEIGHT } else { 0.0 });
    let mut row_index = 0;
    while remaining_height > 0.0 && row_index < content_list.len() {
        let row = content_list.get(row_index);
        match row {
            Some(LivePageRowReactive::StandardCarousel(_)) => {
                standard_carousel_indices.push(row_index);
                remaining_height -= STANDARD_CAROUSEL_HEIGHT + STATION_ROW_GAP;
            }
            Some(LivePageRowReactive::Station(station)) => {
                station_row_indices.push(row_index);
                remaining_height -= STATION_ROW_HEIGHT + STATION_ROW_GAP;
                if station.has_header() {
                    remaining_height -= STATION_HEADER_HEIGHT;
                }
            }
            _ => {}
        }

        row_index += 1;
    }

    (station_row_indices, standard_carousel_indices)
}

pub fn get_live_page_tracked_images(
    has_hero: bool,
    has_filter_list: bool,
    station_row_indices: Vec<usize>,
    standard_carousel_indices: Vec<usize>,
) -> Vec<String> {
    let mut tracked_images = vec![];

    if has_hero {
        tracked_images.push(TTI_PART_MEDIA_BACKGROUND.to_string());
    }

    for row_index in station_row_indices.iter() {
        tracked_images.push(format!("{TTI_PART_STATION_LOGO}_{row_index}"));
        tracked_images.push(format!("{TTI_PART_EPG_THUMBNAIL}_{row_index}"));
    }

    for row_index in standard_carousel_indices.iter() {
        let visible_card_count = if has_filter_list {
            CARDS_VISIBLE_WITH_FILTER_LIST
        } else {
            CARDS_VISIBLE_WITH_NO_FILTER_LIST
        };

        for card_index in 0..visible_card_count {
            tracked_images.push(format!(
                "{TTI_PART_STANDARD_CAROUSEL_CARD}_{row_index}_{card_index}"
            ));
        }
    }

    tracked_images
}

pub fn get_station_details_tracked_images(
    has_logo: bool,
    initial_focus_id: String,
    airings_signal: ReadSignal<Vec<AiringModel>>,
    current_time: i64,
) -> Vec<String> {
    let mut tracked_images = vec![];

    tracked_images.push(TTI_PART_MEDIA_BACKGROUND.to_string());

    // Use index 0 since there will be only one logo and thumbnail
    if has_logo {
        tracked_images.push(TTI_PART_STATION_LOGO.to_string());
    }

    // Only track thumbnail if we are initialy focused on on-air airing
    let initial_focus_index = get_index_for_airing_id(airings_signal, initial_focus_id);
    let initial_focus_on_air = airings_signal.with_untracked(|airings| {
        airings
            .get(initial_focus_index)
            .is_some_and(|airing| is_on_air_at_time(airing.get_time_range(), current_time))
    });

    if initial_focus_on_air {
        // Thumbnail component appends the index, which is always 0 for SDP
        tracked_images.push(format!("{TTI_PART_EPG_THUMBNAIL}_0"));
    }

    tracked_images
}

pub fn track_media_background_ready(
    scope: Scope,
    tti_registry: Rc<TimeToInteractiveRegistry>,
    background_id: Signal<String>,
    on_ready: Option<Rc<dyn Fn()>>,
) {
    let mb_ready_context = use_context::<MediaBackgroundStatusContext>(scope);
    let is_media_background_ready = create_memo(scope, move |_| {
        let Some(ref mb_ready_context) = mb_ready_context else {
            return false;
        };

        let Some(state) = mb_ready_context.background_state.get() else {
            return false;
        };

        let same_id = background_id.get() == state.id;

        same_id && state.background_state == BackgroundState::Downloaded
    });

    create_effect(scope, {
        move |has_ever_ready| {
            let mb_ready = is_media_background_ready.get();
            if mb_ready {
                tti_registry.set_part_ready(TTI_PART_MEDIA_BACKGROUND.to_string());
            }
            let has_ever_ready = has_ever_ready.unwrap_or(false);

            if let Some(on_ready) = &on_ready {
                let is_first_time_ready = !has_ever_ready && mb_ready;
                if is_first_time_ready {
                    on_ready();
                }
            }
            has_ever_ready || mb_ready
        }
    });
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data_util::mock_airing_items_with_start_time;
    use crate::{
        constants::{
            LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN, LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN_NO_HERO,
        },
        mocks::mock_data_util::{
            mock_live_page_standard_carousel_reactive, mock_station_row_model,
        },
        types::StationRowModelReactive,
    };
    use crate::{mocks::mock_utc::MOCK_NOW_MS, ui::epg::constants::HOUR_IN_MS};
    use cross_app_events::tti_registry::MockTimeToInteractiveRegistry;
    use ignx_compositron::app::launch_only_scope;
    use location::PageType;
    use media_background::types::context::MediaBackgroundStatus;
    use rstest::*;

    mod get_visible_row_indices {
        use super::*;

        #[test]
        fn returns_row_indices() {
            launch_only_scope(|scope| {
                let (station_row_indices, standard_carousel_indices) = get_visible_row_indices(
                    LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN_NO_HERO,
                    false,
                    &get_content_list(scope, 4, 1),
                );
                assert_eq!(standard_carousel_indices, vec![0]);
                assert_eq!(station_row_indices, vec![1, 2, 3, 4]);
            });
        }

        #[test]
        fn returns_no_indices_when_no_content() {
            let content_list: Vec<LivePageRowReactive> = vec![];
            let (station_row_indices, standard_carousel_indices) = get_visible_row_indices(
                LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN_NO_HERO,
                false,
                &content_list,
            );
            assert_eq!(standard_carousel_indices.len(), 0);
            assert_eq!(station_row_indices.len(), 0);
        }

        #[test]
        fn content_offset_limits_returned_indices() {
            launch_only_scope(|scope| {
                // With no hero, station rows only
                let content_list = get_content_list(scope, 20, 0);
                let (station_row_indices, standard_carousel_indices) = get_visible_row_indices(
                    LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN_NO_HERO,
                    false,
                    &content_list,
                );
                assert_eq!(station_row_indices.len(), 7);
                assert_eq!(standard_carousel_indices.len(), 0);

                // With hero, station rows only
                let content_list = get_content_list(scope, 20, 0);
                let (station_row_indices, standard_carousel_indices) = get_visible_row_indices(
                    LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN,
                    true,
                    &content_list,
                );
                assert_eq!(station_row_indices.len(), 3);
                assert_eq!(standard_carousel_indices.len(), 0);

                // With no hero, carousels only
                let content_list = get_content_list(scope, 0, 10);
                let (station_row_indices, standard_carousel_indices) = get_visible_row_indices(
                    LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN_NO_HERO,
                    false,
                    &content_list,
                );
                assert_eq!(station_row_indices.len(), 0);
                assert_eq!(standard_carousel_indices.len(), 4);

                // With hero, carousels only
                let content_list = get_content_list(scope, 0, 10);
                let (station_row_indices, standard_carousel_indices) = get_visible_row_indices(
                    LIVE_PAGE_CONTENT_OFFSET_TOPNAV_SHOWN,
                    true,
                    &content_list,
                );
                assert_eq!(station_row_indices.len(), 0);
                assert_eq!(standard_carousel_indices.len(), 2);
            });
        }

        fn get_content_list(
            scope: Scope,
            station_row_count: usize,
            standard_carousel_count: usize,
        ) -> Vec<LivePageRowReactive> {
            let row_model = StationRowModelReactive {
                station: mock_station_row_model("stationId", 5, 60),
                airings_signal: create_rw_signal(scope, vec![]),
            };
            let mut content_list = vec![];

            for _ in 0..standard_carousel_count {
                content_list.push(LivePageRowReactive::StandardCarousel(
                    mock_live_page_standard_carousel_reactive(scope, "id", 10, 1000),
                ))
            }

            for i in 0..station_row_count {
                let mut row_model = row_model.clone();
                row_model.station.index_in_epg_group = i;
                content_list.push(LivePageRowReactive::Station(row_model))
            }
            content_list
        }
    }

    mod get_live_page_tracked_images {
        use super::*;

        #[test]
        fn adds_station_logos_and_thumbnails() {
            let station_row_indices = vec![0, 1];
            let standard_carousel_indices = vec![];
            let tracked_images = get_live_page_tracked_images(
                false,
                false,
                station_row_indices,
                standard_carousel_indices,
            );
            assert_eq!(
                tracked_images,
                vec![
                    "StationLogo_0",
                    "EpgThumbnail_0",
                    "StationLogo_1",
                    "EpgThumbnail_1"
                ]
            );
        }

        #[test]
        fn adds_standard_carousel_cards() {
            let station_row_indices = vec![];
            let standard_carousel_indices = vec![0, 1];
            let tracked_images = get_live_page_tracked_images(
                false,
                false,
                station_row_indices,
                standard_carousel_indices,
            );
            assert_eq!(
                tracked_images,
                vec![
                    "StandardCarouselCard_0_0",
                    "StandardCarouselCard_0_1",
                    "StandardCarouselCard_0_2",
                    "StandardCarouselCard_0_3",
                    "StandardCarouselCard_0_4",
                    "StandardCarouselCard_1_0",
                    "StandardCarouselCard_1_1",
                    "StandardCarouselCard_1_2",
                    "StandardCarouselCard_1_3",
                    "StandardCarouselCard_1_4",
                ]
            );
        }

        #[test]
        fn combines_different_row_types() {
            let has_hero = true;
            let has_filter_list = true;
            let station_row_indices = vec![1, 2];
            let standard_carousel_indices = vec![0, 3];
            assert_eq!(
                get_live_page_tracked_images(
                    has_hero,
                    has_filter_list,
                    station_row_indices,
                    standard_carousel_indices,
                ),
                vec![
                    "MediaBackground",
                    "StationLogo_1",
                    "EpgThumbnail_1",
                    "StationLogo_2",
                    "EpgThumbnail_2",
                    "StandardCarouselCard_0_0",
                    "StandardCarouselCard_0_1",
                    "StandardCarouselCard_0_2",
                    "StandardCarouselCard_0_3",
                    "StandardCarouselCard_3_0",
                    "StandardCarouselCard_3_1",
                    "StandardCarouselCard_3_2",
                    "StandardCarouselCard_3_3",
                ]
            );
        }

        #[rstest]
        #[case::with_filter_list(true, CARDS_VISIBLE_WITH_FILTER_LIST)]
        #[case::with_no_filter_list(false, CARDS_VISIBLE_WITH_NO_FILTER_LIST)]
        fn adds_more_standard_carousel_cards_with_no_filter_list(
            #[case] has_filter_list: bool,
            #[case] expected_length: usize,
        ) {
            assert_eq!(
                get_live_page_tracked_images(false, has_filter_list, vec![], vec![0]).len(),
                expected_length
            );
        }

        #[test]
        fn only_adds_hero_if_present() {
            let tracked_images_with_hero =
                get_live_page_tracked_images(true, false, vec![], vec![]);
            assert_eq!(tracked_images_with_hero, vec!["MediaBackground"]);

            let tracked_images_no_hero = get_live_page_tracked_images(false, false, vec![], vec![]);
            assert_eq!(tracked_images_no_hero, vec![] as Vec<String>);
        }
    }

    mod get_station_details_tracked_images {
        use super::*;

        #[rstest]
        fn adds_media_background_and_station_logo(#[values(true, false)] has_logo: bool) {
            launch_only_scope(move |scope| {
                let tracked_images = get_station_details_tracked_images(
                    has_logo,
                    String::default(),
                    create_rw_signal(scope, vec![]).read_only(),
                    MOCK_NOW_MS,
                );

                let mut expected = vec![TTI_PART_MEDIA_BACKGROUND];
                if has_logo {
                    expected.push(TTI_PART_STATION_LOGO);
                }
                assert_eq!(tracked_images, expected);
            })
        }

        #[rstest]
        #[case::found_on_now_airing["station_id_0", MOCK_NOW_MS, true]]
        #[case::found_upcoming_airing["station_id_1", MOCK_NOW_MS, false]]
        #[case::no_match_with_on_now_airing["invalid", MOCK_NOW_MS, true]]
        #[case::no_match_with_upcoming_airings["invalid", MOCK_NOW_MS - HOUR_IN_MS, false]]
        fn adds_thumbnail(
            #[case] focus_id: String,
            #[case] current_time: i64,
            #[case] on_now: bool,
        ) {
            launch_only_scope(move |scope| {
                let (airings, _) = create_signal(
                    scope,
                    mock_airing_items_with_start_time("station_id", 10, 15, MOCK_NOW_MS),
                );
                let tracked_images =
                    get_station_details_tracked_images(false, focus_id, airings, current_time);

                let mut expected = vec![TTI_PART_MEDIA_BACKGROUND.to_string()];
                if on_now {
                    expected.push(format!("{TTI_PART_EPG_THUMBNAIL}_0"));
                }
                assert_eq!(tracked_images, expected);
            })
        }
    }

    mod track_media_background_ready {
        use super::*;

        fn get_tti_registry(scope: Scope) -> TimeToInteractiveRegistry {
            TimeToInteractiveRegistry::new(
                scope,
                vec![],
                PageType::Rust(location::RustPage::RUST_LIVE_TV),
                None,
            )
        }

        #[test]
        fn never_sets_ready_when_missing_mb_context() {
            launch_only_scope(move |scope| {
                // test will fail if on_ready is called
                let on_ready = Rc::new(|| panic!("on_ready should not be called"));
                let background_id: RwSignal<String> = create_rw_signal(scope, String::default());

                let mut tti_registry = MockTimeToInteractiveRegistry::default();
                tti_registry.expect_set_part_ready().never();

                track_media_background_ready(
                    scope,
                    Rc::new(get_tti_registry(scope)),
                    background_id.into(),
                    Some(on_ready),
                );

                // update to trigger recalculation
                background_id.set(String::default());

                // `on_ready` will panic if test fails
            })
        }

        #[test]
        fn never_sets_ready_when_missing_background_state() {
            launch_only_scope(move |scope| {
                // test will fail if on_ready is called
                let on_ready = Rc::new(|| panic!("on_ready should not be called"));

                let mb_context = MediaBackgroundStatusContext {
                    background_state: create_rw_signal(scope, None).into(),
                    player_resize_support: create_rw_signal(scope, None).into(),
                };
                provide_context(scope, mb_context);
                let background_id: RwSignal<String> = create_rw_signal(scope, String::default());

                track_media_background_ready(
                    scope,
                    Rc::new(get_tti_registry(scope)),
                    background_id.into(),
                    Some(on_ready),
                );

                // update to trigger recalculation
                background_id.set(String::default());

                // `on_ready` will panic if test fails
            })
        }

        #[rstest]
        #[case::id_doesnt_match(None)]
        #[case::id_doesnt_match(Some(MediaBackgroundStatus { id: "not_match".to_string(), background_state:  BackgroundState::Downloaded}))]
        #[case::mb_not_ready(Some(MediaBackgroundStatus{ id: "match_id".to_string(), background_state:  BackgroundState::Pending}))]
        fn never_sets_part_ready_when_mb_not_ready(
            #[case] background_status: Option<MediaBackgroundStatus>,
        ) {
            launch_only_scope(move |scope| {
                // test will fail if on_ready is called
                let on_ready = Rc::new(|| panic!("on_ready should not be called"));

                let mb_context = MediaBackgroundStatusContext {
                    background_state: create_rw_signal(scope, background_status).into(),
                    player_resize_support: create_rw_signal(scope, None).into(),
                };
                provide_context(scope, mb_context);
                let background_id: RwSignal<String> =
                    create_rw_signal(scope, "match_id".to_string());

                track_media_background_ready(
                    scope,
                    Rc::new(get_tti_registry(scope)),
                    background_id.into(),
                    Some(on_ready),
                );

                // `on_ready` will panic if test fails
            })
        }

        #[test]
        fn sets_part_ready_when_mb_ready() {
            launch_only_scope(move |scope| {
                let on_ready_called = create_rw_signal(scope, 0);
                let on_ready = Rc::new(move || on_ready_called.update(|count| *count += 1));

                let valid_background = Some(MediaBackgroundStatus {
                    background_state: BackgroundState::Downloaded,
                    id: "match_id".to_string(),
                });
                let background_signal = create_rw_signal(scope, valid_background.clone());
                let mb_context = MediaBackgroundStatusContext {
                    background_state: background_signal.into(),
                    player_resize_support: create_rw_signal(scope, None).into(),
                };
                provide_context(scope, mb_context);
                let background_id: RwSignal<String> =
                    create_rw_signal(scope, "match_id".to_string());

                track_media_background_ready(
                    scope,
                    Rc::new(get_tti_registry(scope)),
                    background_id.into(),
                    Some(on_ready),
                );

                // clear mb to force Memo to be false
                background_signal.set(None);
                // reapply to run effect again
                background_signal.set(valid_background);

                // on_ready should not be called the second time
                assert_eq!(on_ready_called.get_untracked(), 1);
            })
        }
    }
}
