use std::{
    cell::{Cell, RefCell},
    collections::HashSet,
    rc::Rc,
};

/// This struct tracks in asynchronous task and expose
/// functions to allow dedupliation before starting a task.
pub struct PaginationTracker {
    in_progress_set: Rc<RefCell<HashSet<String>>>,
}

impl PaginationTracker {
    pub fn new() -> PaginationTracker {
        PaginationTracker {
            in_progress_set: Rc::new(RefCell::new(HashSet::new())),
        }
    }

    /// If the given task id is already in progress, return None.
    /// Otherwise, add the id to in progress task and return closure to mark task as complete.
    /// This returns reference-counting closure to allow calling from from multiple callback
    /// such as on success and on failure. Only the first call will mark task complete.
    ///
    /// # Example
    /// ```no_run
    /// use linear::utils::pagination_tracker::PaginationTracker;
    ///
    /// let pagination_tracker = PaginationTracker::new();
    /// if let Some(release) = pagination_tracker.try_lock("task_id".to_string()) {
    ///     let release_on_failure = release.clone();
    ///     network_call(
    ///         move || {
    ///             // On success
    ///             release();
    ///         },
    ///         move || {
    ///             // On failure
    ///             release_on_failure();
    ///         }
    ///     )
    /// }
    /// fn network_call(success_callback: impl Fn(), failure_callback: impl Fn()) {}
    ///
    pub fn try_lock(&self, id: String) -> Option<Rc<dyn Fn()>> {
        if self.has_lock(&id) {
            return None;
        }
        self.in_progress_set.borrow_mut().insert(id.clone());

        let in_progress_clone = self.in_progress_set.clone();
        let release_pending = Cell::new(Some(move || {
            in_progress_clone.borrow_mut().remove(&id);
        }));

        Some(Rc::new(move || {
            if let Some(release) = release_pending.take() {
                release();
            }
        }))
    }

    /// Checks if the given task id is in progress
    pub fn has_lock(&self, id: &str) -> bool {
        return self.in_progress_set.borrow().contains(id);
    }
}

impl Clone for PaginationTracker {
    /// Create new `PaginationTracker` with the same `in_progress_set`
    /// to allow sharing tracker.
    fn clone(&self) -> Self {
        Self {
            in_progress_set: self.in_progress_set.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::PaginationTracker;

    #[test]
    fn it_insert_and_remove_task_id_when_task_not_already_in_progress() {
        let pagination_tracker = PaginationTracker::new();

        let release = pagination_tracker.try_lock("task_id".to_string()).unwrap();
        release();

        // Verify the same task id can be added
        assert!(pagination_tracker.try_lock("task_id".to_string()).is_some());
    }

    #[test]
    fn it_return_none_when_task_already_in_progress() {
        let pagination_tracker = PaginationTracker::new();
        assert!(pagination_tracker.try_lock("task_id".to_string()).is_some());

        // Verify the same task id cannot be added
        assert!(pagination_tracker.try_lock("task_id".to_string()).is_none());
        // Verify the another task id can be added
        assert!(pagination_tracker
            .try_lock("another_task_id".to_string())
            .is_some());
    }

    #[test]
    fn it_ignores_release_calls_after_the_first() {
        let pagination_tracker = PaginationTracker::new();
        let release = pagination_tracker.try_lock("task_id".to_string()).unwrap();

        // First release call should remove from in progress
        release();

        // Verify the same task id can be added once
        assert!(pagination_tracker.try_lock("task_id".to_string()).is_some());
        assert!(pagination_tracker.try_lock("task_id".to_string()).is_none());

        // Second release call should not remove task id
        release();
        assert!(pagination_tracker.try_lock("task_id".to_string()).is_none());
    }

    #[test]
    fn it_shares_same_in_progress_set_when_clone() {
        let pagination_tracker = PaginationTracker::new();
        pagination_tracker.try_lock("task_id".to_string()).unwrap();

        let pagination_tracker_clone = pagination_tracker;

        assert!(pagination_tracker_clone
            .try_lock("task_id".to_string())
            .is_none());
    }
}
