use fableous::toasts::toast_context::ToastContext;
use ignx_compositron::{prelude::*, text::TextContent};

use super::context_utils::get_toast_context;

pub fn update_toast_state(scope: Scope, new_message: Option<TextContent>) {
    let ToastContext { message, visible } = get_toast_context(scope);
    let set_visible = new_message.is_some();

    message.try_set(new_message);
    visible.try_set(set_visible);
}

#[cfg(test)]
mod test {
    use ignx_compositron::app::launch_only_scope;
    use rstest::rstest;

    use super::*;

    #[rstest]
    #[case::visible_when_message_provided(Some(TextContent::String("new_msg".to_string())), true)]
    #[case::hidden_when_no_message(None, false)]
    fn updates_toast_context(#[case] new_message: Option<TextContent>, #[case] expected_vis: bool) {
        launch_only_scope(move |scope| {
            let toast_context = ToastContext {
                message: create_rw_signal(
                    scope,
                    Some(TextContent::String("Initial Message".to_string())),
                ),
                visible: create_rw_signal(scope, !expected_vis),
            };
            provide_context(scope, toast_context);

            update_toast_state(scope, new_message.clone());
            assert_eq!(new_message, toast_context.message.get_untracked());
            assert_eq!(expected_vis, toast_context.visible.get_untracked());
        });
    }
}
