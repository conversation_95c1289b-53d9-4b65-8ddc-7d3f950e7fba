use linear_common::types::TimeRange;

pub fn calculate_progress(time_range: TimeRange, current_time: i64) -> f32 {
    let (start_time, end_time) = time_range;

    let total_duration = end_time - start_time;

    if total_duration <= 0 {
        return 0.0;
    }

    let elapsed_time = current_time - start_time;
    (elapsed_time as f32 / total_duration as f32).clamp(0.0, 1.0)
}

#[cfg(test)]
mod test {
    use super::*;
    use rstest::*;

    #[rstest]
    // start_time: March 6, 2024 9:00:00 PM
    // end_time: March 6, 2024 9:30:00 PM
    // current_time: March 6, 2024 9:25:30 PM
    // expected_progress: 0.85
    #[case(1709758800000, 1709760600000, 1709760330000, 0.85)]
    // start_time: March 6, 2024 9:30:00 PM
    // end_time: March 6, 2024 9:00:00 PM
    // current_time: March 6, 2024 9:25:30 PM
    // expected_progress: 0.0
    #[case(1709760600000, 1709758800000, 1709760330000, 0.0)]
    // start_time: March 6, 2024 9:25:30 PM
    // end_time: March 6, 2024 9:30:00 PM
    // current_time: March 6, 2024 9:00:00 PM
    // expected_progress: 0.0
    #[case(1709760330000, 1709760600000, 1709758800000, 0.0)]
    fn it_calculates_progress_correctly(
        #[case] start_time: i64,
        #[case] end_time: i64,
        #[case] current_time: i64,
        #[case] expected_progress: f32,
    ) {
        let progress = calculate_progress((start_time, end_time), current_time);
        assert_eq!(progress, expected_progress);
    }
}
