pub const AV_LRC_WATCH_LIVE: &str = "AV_LRC_WATCH_LIVE";
pub const AV_LRC_CLOSE_BUTTON_TEXT: &str = "AV_LRC_CLOSE_BUTTON_TEXT";
pub const AV_LRC_NOW_PLAYING_ON: &str = "AV_LRC_NOW_PLAYING_ON";
pub const AV_LRC_NOW_PLAYING_ON_STATION: &str = "AV_LRC_NOW_PLAYING_ON_STATION";
pub const AV_LRC_UPCOMING_ON_STATION: &str = "AV_LRC_UPCOMING_ON_STATION";
pub const AV_LRC_TTS_SVOD_SUBSCRIBE: &str = "AV_LRC_TTS_SVOD_SUBSCRIBE";
pub const AV_LRC_LIVE_TV_TIME_REMAINING_ONLY_HOURS: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_ONLY_HOURS";
pub const AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR: &str = "AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR";
pub const AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_WITH_HOURS: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_WITH_HOURS";
pub const AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_MINUTES_ONLY: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_MINUTES_ONLY";
pub const AV_LRC_LIVE_TV_AIRING_TIME_RANGE: &str = "AV_LRC_LIVE_TV_AIRING_TIME_RANGE";
pub const AV_LRC_LIVE_TV_TIME_REMAINING_HOURS_MINUTES: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_HOURS_MINUTES";
pub const AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES: &str = "AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES";
pub const AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR_MINUTES: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR_MINUTES";
pub const AV_LRC_GLOBAL_NAVIGATION_LIVE_FOCUS_TTS: &str = "AV_LRC_GLOBAL_NAVIGATION_LIVE_FOCUS_TTS";
pub const AV_LRC_LIVE_TV_EPISODE_NUMBER: &str = "AV_LRC_LIVE_TV_EPISODE_NUMBER";
pub const AV_LRC_LIVE_TV_FILTERS_MENU: &str = "AV_LRC_LIVE_TV_FILTERS_MENU";
pub const AV_LRC_LIVE_TV_INFO_MODAL: &str = "AV_LRC_LIVE_TV_INFO_MODAL";
pub const AV_LRC_LIVE_TV_OPEN_INFO_MODAL_HINT: &str = "AV_LRC_LIVE_TV_OPEN_INFO_MODAL_HINT";
pub const AV_LRC_LIVE_TV_INFO_MODAL_ON_NOW_CLOSE: &str = "AV_LRC_LIVE_TV_INFO_MODAL_ON_NOW_CLOSE";
pub const AV_LRC_LIVE_TV_INFO_MODAL_UPCOMING_CLOSE: &str =
    "AV_LRC_LIVE_TV_INFO_MODAL_UPCOMING_CLOSE";
pub const AV_LRC_LIVE_TV_NAVIGATION_FROM_EPG: &str = "AV_LRC_LIVE_TV_NAVIGATION_FROM_EPG";
pub const AV_LRC_LIVE_TV_NAVIGATION_FROM_FILTERS: &str = "AV_LRC_LIVE_TV_NAVIGATION_FROM_FILTERS";
pub const AV_LRC_LIVE_TV_NAVIGATION_FROM_HERO: &str = "AV_LRC_LIVE_TV_NAVIGATION_FROM_HERO";
pub const AV_LRC_LIVE_TV_NAVIGATION_FROM_STANDARD_CAROUSEL: &str =
    "AV_LRC_LIVE_TV_NAVIGATION_FROM_STANDARD_CAROUSEL";
pub const AV_LRC_LIVE_TV_NAVIGATION_ROW_END: &str = "AV_LRC_LIVE_TV_NAVIGATION_ROW_END";
pub const AV_LRC_LIVE_TV_ON_NOW_SELECT_MESSAGE: &str = "AV_LRC_LIVE_TV_ON_NOW_SELECT_MESSAGE";
pub const AV_LRC_LIVE_TV_SEASON_NUMBER: &str = "AV_LRC_LIVE_TV_SEASON_NUMBER";
pub const AV_LRC_LIVE_TV_UPCOMING_SELECT_MESSAGE: &str = "AV_LRC_LIVE_TV_UPCOMING_SELECT_MESSAGE";
pub const AV_LRC_LIVE_TV_TTS_SELECT_FOR_MORE_INFORMATION: &str =
    "AV_LRC_LIVE_TV_TTS_SELECT_FOR_MORE_INFORMATION";
pub const AV_LRC_LIVE_TV_NAVIGATION_FROM_EPG_SECONDARY_FOCUS: &str =
    "AV_LRC_LIVE_TV_NAVIGATION_FROM_EPG_SECONDARY_FOCUS";
pub const AV_LRC_PB_REG_RATING: &str = "AV_LRC_PB_REG_RATING";
pub const AV_LRC_TTS_BUTTON_IN_COLLECTION_INDEX_MESSAGE: &str =
    "AV_LRC_TTS_BUTTON_IN_COLLECTION_INDEX_MESSAGE";
pub const AIV_BLAST_TTS_ROLE_BUTTON: &str = "AIV_BLAST_TTS_ROLE_BUTTON";
pub const AV_LRC_LIVE_TV_PAGE_LOAD: &str = "LiveTvPageLoad";
pub const AV_LRC_LIVE_TV_PAGE_SHOW: &str = "LiveTvPageShow";
pub const AV_LRC_LIVE_TV_PAGE_HIDE: &str = "LiveTvPageHide";
pub const AV_LRC_PLAY_FROM_WATCH_LIVE: &str = "PlayFromWatchLive";
pub const AV_LRC_PLAY_FROM_LINEAR_STATION: &str = "PlayFromLinearStation";
pub const AV_LRC_PLAY_FROM_MORE_INFO: &str = "PlayFromMoreInfo";
pub const AV_LRC_MORE_INFO_MODAL_SHOW: &str = "MoreInfoModalShow";
pub const AV_LRC_MORE_INFO_MODAL_HIDE: &str = "MoreInfoModalHide";
pub const AV_LRC_LIVE_TV_TTS_ITEM_INDEX: &str = "AV_LRC_LIVE_TV_TTS_ITEM_INDEX";
pub const AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_FIRST_ITEM: &str =
    "AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_FIRST_ITEM";
pub const AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_SUBSEQUENT_ITEM: &str =
    "AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_SUBSEQUENT_ITEM";
pub const AV_LRC_ON_NOW_TTS: &str = "AV_LRC_ON_NOW_TTS";
pub const AV_LRC_TTS_NAVIGATION_NOT_POSSIBLE: &str = "AV_LRC_TTS_NAVIGATION_NOT_POSSIBLE";
pub const AV_LRC_ADD_TO_FAVORITES: &str = "AV_LRC_ADD_TO_FAVORITES";
pub const AV_LRC_REMOVE_FROM_FAVORITES: &str = "AV_LRC_REMOVE_FROM_FAVORITES";
pub const AV_LRC_ADDED_TO_FAVORITES: &str = "AV_LRC_ADDED_TO_FAVORITES";
pub const AV_LRC_REMOVED_FROM_FAVORITES: &str = "AV_LRC_REMOVED_FROM_FAVORITES";
pub const AV_LRC_MOVE_LEFT_FOR_FILTERS: &str = "AV_LRC_MOVE_LEFT_FOR_FILTERS";
pub const AV_LRC_MOVE_LEFT_FOR_FAVORITING: &str = "AV_LRC_MOVE_LEFT_FOR_FAVORITING";
pub const AV_LRC_MOVE_RIGHT_TO_BROWSE_PROGRAMS: &str = "AV_LRC_MOVE_RIGHT_TO_BROWSE_PROGRAMS";
pub const AV_LRC_MOVE_RIGHT_TO_BROWSE_UPCOMING: &str = "AV_LRC_MOVE_RIGHT_TO_BROWSE_UPCOMING";
pub const AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS: &str = "AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS";

// Station Details
pub const AV_LRC_MOVE_UP_FOR_FAVORITING: &str = "AV_LRC_MOVE_UP_FOR_FAVORITING";
pub const AV_LRC_MOVE_DOWN_FOR_MORE_ACTIONS: &str = "AV_LRC_MOVE_DOWN_FOR_MORE_ACTIONS";
pub const AV_LRC_MOVE_DOWN_FOR_MORE_PROGRAMS: &str = "AV_LRC_MOVE_DOWN_FOR_MORE_PROGRAMS";
pub const AV_LRC_MOVE_LEFT_FOR_MORE_ACTIONS: &str = "AV_LRC_MOVE_LEFT_FOR_MORE_ACTIONS";
pub const AV_LRC_MOVE_LEFT_FOR_UTILITY_MENU: &str = "AV_LRC_MOVE_LEFT_FOR_UTILITY_MENU";
pub const AV_LRC_MOVE_LEFT_OR_RIGHT_FOR_MORE_ACTIONS: &str =
    "AV_LRC_MOVE_LEFT_OR_RIGHT_FOR_MORE_ACTIONS";
pub const AV_LRC_MOVE_RIGHT_FOR_MORE_ACTIONS: &str = "AV_LRC_MOVE_RIGHT_FOR_MORE_ACTIONS";
pub const AV_LRC_PRESS_BACK_TO_EXIT: &str = "AV_LRC_PRESS_BACK_TO_EXIT";
pub const AV_LRC_PRESS_BACK_TO_JUMP_TO_LIVE: &str = "AV_LRC_PRESS_BACK_TO_JUMP_TO_LIVE";
pub const AV_LRC_PRESS_SELECT_TO_WATCH_LIVE: &str = "AV_LRC_PRESS_SELECT_TO_WATCH_LIVE";
pub const AV_LRC_STATION_DETAILS_NAVIGATION_FROM_EPG_ON_NOW: &str =
    "AV_LRC_STATION_DETAILS_NAVIGATION_FROM_EPG_ON_NOW";
pub const AV_LRC_STATION_DETAILS_NAVIGATION_FROM_EPG_UPCOMING: &str =
    "AV_LRC_STATION_DETAILS_NAVIGATION_FROM_EPG_UPCOMING";
pub const AV_LRC_STATION_DETAILS_NAVIGATION_ROW_END: &str =
    "AV_LRC_STATION_DETAILS_NAVIGATION_ROW_END";
pub const AV_LRC_STATION_DETAILS_UPCOMING_SELECT_MESSAGE: &str =
    "AV_LRC_STATION_DETAILS_UPCOMING_SELECT_MESSAGE";
// Why's it renamed? This message is shared by all non-Live TV pages as it directs users to Live TV.
pub const AV_LRC_ADDED_TO_FAVORITES_OUTSIDE_LIVE_PAGE: &str =
    "AV_LRC_ADDED_TO_FAVORITES_STATION_DETAIL";
pub const AV_LRC_ADD_STATION_TO_FAVORITES: &str = "AV_LRC_ADD_STATION_TO_FAVORITES";
pub const AV_LRC_FAVORITED: &str = "AV_LRC_FAVORITED";
