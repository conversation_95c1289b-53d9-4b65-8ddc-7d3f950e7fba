use std::collections::HashMap;

use chrono::Datelike;
use ignx_compositron::text::{LocalizedText, SubstitutionParameters, TextContent};
use linear_common::util::datetime_util::get_time_remaining;
use linear_common::{
    types::{TimeProvider, TimeRange},
    util::schedule_util::is_on_air_at_time,
};

const CHRONO_FORMAT_STRING_HOUR_MINUTE_PERIOD: &str = "%-I:%M %p";
pub const CHRONO_FORMAT_STRING_SHORT_DATE: &str = "%b %e";

const AV_LRC_SHORT_MONTH_PREFIX: &str = "AV_LRC_SHORT_MONTH_";
const MONTH_SHORT_NAMES: [&str; 12] = [
    "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC",
];
const AV_LRC_SINGLE_DAY_DATE_NO_YEAR: &str = "AV_LRC_SINGLE_DAY_DATE_NO_YEAR";

pub fn get_formatted_time(timestamp: i64) -> String {
    format_local_date_time(timestamp, CHRONO_FORMAT_STRING_HOUR_MINUTE_PERIOD)
}

pub fn get_formatted_date(timestamp: i64) -> Option<TextContent> {
    let local_time = TimeProvider::get_epoch_milli_as_local_time(timestamp);

    let month_text = usize::try_from(local_time.month0())
        .ok()
        .and_then(|month_index| MONTH_SHORT_NAMES.get(month_index))
        .map(|month_short_name| {
            TextContent::LocalizedText(LocalizedText::new(format!(
                "{}{}",
                AV_LRC_SHORT_MONTH_PREFIX, month_short_name
            )))
        })?;

    Some(TextContent::LocalizedText(LocalizedText {
        string_id: AV_LRC_SINGLE_DAY_DATE_NO_YEAR.into(),
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([
            ("shortMonth".to_string(), month_text),
            (
                "dateNumber".to_string(),
                TextContent::String(local_time.day().to_string()),
            ),
        ]))),
    }))
}

pub fn get_time_remaining_for_display(current_time: i64, end_time: i64) -> TextContent {
    get_time_remaining(current_time, end_time, false)
}

pub fn get_time_remaining_for_tts(current_time: i64, end_time: i64) -> TextContent {
    get_time_remaining(current_time, end_time, true)
}

pub fn get_display_time(current_time: i64, time_range: TimeRange) -> TextContent {
    let (start, end) = time_range;

    if is_on_air_at_time(time_range, current_time) {
        get_time_remaining_for_display(current_time, end)
    } else {
        TextContent::String(get_formatted_time(start))
    }
}

fn format_local_date_time(timestamp: i64, fmt: &str) -> String {
    let local_datetime = TimeProvider::get_epoch_milli_as_local_time(timestamp);
    local_datetime.format(fmt).to_string().trim().to_string()
}

pub fn local_days_differ(t1: i64, t2: i64) -> bool {
    TimeProvider::get_epoch_milli_as_local_time(t1).num_days_from_ce()
        != TimeProvider::get_epoch_milli_as_local_time(t2).num_days_from_ce()
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::any::TypeId;

    #[test]
    fn test_get_formatted_time() {
        // April 21, 2021 7:00:00 AM UTC
        let result = get_formatted_time(1618988400000);

        assert_eq!(result, "7:00 AM".to_string());
    }

    #[test]
    fn test_get_formatted_date() {
        // April 21, 2021 7:00:00 AM UTC
        let result = get_formatted_date(1618988400000);

        assert_eq!(
            result,
            Some(TextContent::LocalizedText(LocalizedText {
                string_id: "AV_LRC_SINGLE_DAY_DATE_NO_YEAR".into(),
                substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                    (
                        "dateNumber".to_string(),
                        TextContent::String("21".to_string())
                    ),
                    (
                        "shortMonth".to_string(),
                        TextContent::LocalizedText(LocalizedText::new("AV_LRC_SHORT_MONTH_APR"))
                    )
                ])))
            }))
        )
    }

    #[test]
    fn test_get_display_time_on_now() {
        let time_range = (1618988400000, 1618992000000); // April 21, 2021 7:00:00 AM UTC - April 19, 2021 8:00:00 AM UTC
        let result = get_display_time(time_range.0, time_range);

        assert_eq!(
            result,
            TextContent::LocalizedText(LocalizedText {
                string_id: "AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR".into(),
                substitution_parameters: Some(SubstitutionParameters(HashMap::new())),
            })
        );
    }

    #[test]
    fn test_get_display_time_not_on_now() {
        let time_range = (1618988400000, 1618992000000); // April 21, 2021 7:00:00 AM UTC - April 21, 2021 8:00:00 AM UTC
        let result = get_display_time(time_range.0 - 1, time_range);

        assert_eq!(result, TextContent::String("7:00 AM".to_string()));
    }

    #[test]
    fn format_local_date_time_returns() {
        let parsed = format_local_date_time(1709760330000, "%-I:%M"); // March 6, 2024 9:25:30 PM UTC

        fn is_local_string<T: 'static>(_d: &T) -> bool {
            TypeId::of::<String>() == TypeId::of::<T>()
        }

        assert!(is_local_string(&parsed));
    }

    #[test]
    fn test_local_days_differ() {
        let t1 = 1724371200000; // August 23, 2024 12:00:00 AM
        let t2 = 1724457599999; // August 23, 2024 11:59:59 PM

        assert!(!local_days_differ(t1, t2));
        assert!(local_days_differ(t1 - 1, t2));
        assert!(local_days_differ(t1, t2 + 1));
    }
}
