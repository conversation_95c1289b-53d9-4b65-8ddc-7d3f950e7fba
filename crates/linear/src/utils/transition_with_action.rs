use std::rc::Rc;

use common_transform_types::actions::TransitionAction;
use ignx_compositron::prelude::*;
use ignx_compositron::{
    player::PlayerCommand,
    prelude::{use_context, RwSignal},
};
use location::{JSPage, Location, PageType};
use media_background::types::{HybridPlaybackContext, ReleaseState};
use router::hooks::{use_navigation, use_router};
use serde_json::Value;
use title_details::core::{NavigationDirection, TitleDetailsChangeRequest, TitleDetailsData};
use transition_executor::Transition;

use crate::types::TransitionSource;

use super::common_string_ids::{
    AV_LRC_PLAY_FROM_LINEAR_STATION, AV_LRC_PLAY_FROM_MORE_INFO, AV_LRC_PLAY_FROM_WATCH_LIVE,
};

pub fn get_send_player_metric(scope: Scope) -> Rc<dyn Fn(&str)> {
    Rc::new({
        let live_page_player_command = use_context::<RwSignal<Option<PlayerCommand>>>(scope);
        move |event: &str| {
            if let Some(player_command) = live_page_player_command {
                player_command.set(Some(PlayerCommand::SendMetric(event.to_string())));
            }
        }
    })
}

// converts a transition action to a (action, location) tuple
pub fn build_transition_location(
    scope: Scope,
    action: TransitionAction,
    title_id: String,
) -> Option<(TransitionAction, Location)> {
    let transition =
        Transition::from_action_with_scope(&action, scope).with_title_id(Some(title_id));

    transition
        .to_location()
        .map(|location| (transition.action, location))
}

// Maps and sends a player metric based on TransitionSource only if the pageType is JS::START_PLAYBACK
pub fn send_transition_player_metric_if_necessary(
    page_type: PageType,
    send_player_metric: &Rc<dyn Fn(&str)>,
    transition_source: &TransitionSource,
) {
    if page_type == PageType::Js(JSPage::START_PLAYBACK) {
        match transition_source {
            TransitionSource::AiringCard => send_player_metric(AV_LRC_PLAY_FROM_LINEAR_STATION),
            TransitionSource::HeroDetails => send_player_metric(AV_LRC_PLAY_FROM_WATCH_LIVE),
            TransitionSource::MoreInfoModal => send_player_metric(AV_LRC_PLAY_FROM_MORE_INFO),
            TransitionSource::StandardCarouselCard => {}
            // TODO: add station details metric
            TransitionSource::StationDetails => {}
        }
    }
}

// Sets up the location for navigation from linear page and then performs navigation
//  Set HybridPlaybackContext
//  Update page params
//  Perform navigation with modified location
pub fn navigate_from_linear_page(
    scope: Scope,
    is_seamless_playback_egress: bool,
    is_seamless_playback_ingress: bool,
    mut location: Location,
    playback_origin: &str,
    update_td: WriteSignal<TitleDetailsChangeRequest>,
) {
    // Set playback context to notify media background
    if let PageType::Js(_) = location.pageType {
        if let Some(hybrid_playback_context) = use_context::<HybridPlaybackContext>(scope) {
            // seamless_egress must be set before should_release
            hybrid_playback_context
                .seamless_egress
                .try_set_value(is_seamless_playback_egress);

            let release_state = if is_seamless_playback_ingress {
                ReleaseState::Seamless
            } else {
                ReleaseState::NotSeamless
            };

            hybrid_playback_context
                .should_release
                .set(Some(release_state));
        }

        // Title Details should not persist through the transition
        update_td.set(TitleDetailsChangeRequest {
            data: TitleDetailsData::Empty,
            navigation_direction: NavigationDirection::NONE,
        });
    }
    // Modify page params for seamless playback
    if location.pageType.is_playback() {
        location.pageParams.insert(
            "seamlessEgress".to_string(),
            Value::Bool(is_seamless_playback_egress),
        );

        location
            .pageParams
            .insert("playbackOrigin".to_string(), playback_origin.into());
    }

    let navigate = use_navigation(scope);
    navigate(location, "LINEAR_PAGE");
}

// Navigates to the JS SignIn page if user is not signed in and is transitioning to a playback/signup page
// Returns true if the user is being redirected to sign in
pub fn sign_in_if_necessary(
    scope: Scope,
    is_signed_in: bool,
    transition_action: &TransitionAction,
) -> bool {
    if !is_signed_in
        && matches!(
            transition_action,
            TransitionAction::player(_)
                | TransitionAction::playback(_)
                | TransitionAction::signUp(_)
        )
    {
        let router = use_router(scope);

        if let Some(context) = use_context::<HybridPlaybackContext>(scope) {
            context.should_release.set(Some(ReleaseState::NotSeamless));
        }

        router.navigate(
            Location {
                pageType: PageType::Js(JSPage::OPEN_CODE_BASED_REGISTRATION_PAGE),
                pageParams: Default::default(),
            },
            "LINEAR_PAGE",
        );
        return true;
    }
    false
}

// Returns (is_seamless_playback_egress, is_seamless_playback_ingress) based on AlwaysOn state and action
pub fn get_seamless_state(
    action: &TransitionAction,
    always_on_gti: Option<String>,
    is_always_on_enabled: bool,
) -> (bool, bool) {
    let playback_gti = if let TransitionAction::player(playback_action) = action {
        Some(playback_action.uri.clone())
    } else {
        None
    };
    let seamless_egress: bool = is_always_on_enabled && playback_gti.is_some();

    let seamless_ingress =
        is_always_on_enabled && playback_gti.is_some() && playback_gti == always_on_gti;
    (seamless_egress, seamless_ingress)
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data_util::mock_consent_action;
    use common_transform_types::actions::TitleAcquisitionAction;
    use cross_app_events::create_serde_map;
    use ignx_compositron::app::launch_only_scope;
    use location::RustPage;
    use rstest::*;
    use rust_features::provide_context_test_rust_features;
    use serde_json::Value;

    use crate::{
        mocks::mock_data_util::{
            mock_playback_action, mock_signup_action, mock_swift_details_action,
            mock_title_playback_action,
        },
        utils::test_utils::{
            provide_hybrid_playback_context, provide_mock_routing_with_last_location,
        },
    };

    fn setup_with_router(scope: Scope, page_type: PageType) -> ReadSignal<Location> {
        provide_hybrid_playback_context(scope);
        provide_mock_routing_with_last_location(scope);

        create_signal(
            scope,
            Location {
                pageType: page_type,
                pageParams: Default::default(),
            },
        )
        .0
    }

    mod build_transition_location {
        use super::*;

        #[test]
        fn builds_location_for_playback_action() {
            launch_only_scope(|scope| {
                provide_context_test_rust_features(scope);
                let (action, location) =
                    build_transition_location(scope, mock_playback_action(), "gti".to_string())
                        .unwrap();
                assert_eq!(action, mock_playback_action());
                assert_eq!(location.pageType, PageType::Js(JSPage::START_PLAYBACK));
            });
        }

        #[test]
        fn builds_location_for_consent_action() {
            launch_only_scope(|scope| {
                provide_context_test_rust_features(scope);
                let (action, location) =
                    build_transition_location(scope, mock_consent_action(), "gti".to_string())
                        .unwrap();
                assert_eq!(action, mock_consent_action());
                assert_eq!(
                    location.pageType,
                    PageType::Js(JSPage::CUSTOMER_CONSENT_PAGE)
                );
                assert_eq!(
                    location.pageParams,
                    create_serde_map([
                        ("channelTierId", Value::String("channelTierId".to_string())),
                        ("consentType", Value::String("VPPA".to_string())),
                        ("titleId", Value::String("gti".to_string())),
                        ("workflowType", Value::String("Consent".to_string())),
                    ])
                );
            });
        }

        #[test]
        fn none_for_action_without_location() {
            launch_only_scope(|scope| {
                provide_context_test_rust_features(scope);
                // acquisition actions do not get converted to a location
                let action = TransitionAction::acquisition(TitleAcquisitionAction {
                    label: String::default(),
                    metadata: None,
                    refMarker: String::default(),
                });
                assert!(build_transition_location(scope, action, "gti".to_string()).is_none());
            });
        }
    }

    mod send_player_metric {
        use super::*;

        #[test]
        fn no_metric_sent_for_non_playback_location() {
            launch_only_scope(move |scope| {
                let player_command: RwSignal<Option<PlayerCommand>> = create_rw_signal(scope, None);
                provide_context(scope, player_command);

                let send_player_metric = get_send_player_metric(scope);

                send_transition_player_metric_if_necessary(
                    PageType::Rust(RustPage::RUST_LIVE_TV),
                    &send_player_metric,
                    &TransitionSource::AiringCard,
                );

                assert!(player_command.get_untracked().is_none());
            });
        }

        #[rstest]
        #[case::airing_card(TransitionSource::AiringCard, Some(AV_LRC_PLAY_FROM_LINEAR_STATION.to_string()))]
        #[case::hero_details(TransitionSource::HeroDetails, Some(AV_LRC_PLAY_FROM_WATCH_LIVE.to_string()))]
        #[case::more_info(TransitionSource::MoreInfoModal, Some(AV_LRC_PLAY_FROM_MORE_INFO.to_string()))]
        #[case::standard_carousel(TransitionSource::StandardCarouselCard, None)]
        #[case::station_details(TransitionSource::StationDetails, None)]
        fn sends_metric_for_source(
            #[case] source: TransitionSource,
            #[case] metric: Option<String>,
        ) {
            launch_only_scope(move |scope| {
                let player_command: RwSignal<Option<PlayerCommand>> = create_rw_signal(scope, None);
                provide_context(scope, player_command);

                let send_player_metric = get_send_player_metric(scope);

                send_transition_player_metric_if_necessary(
                    PageType::Js(JSPage::START_PLAYBACK),
                    &send_player_metric,
                    &source,
                );

                if let Some(metric) = metric {
                    let Some(PlayerCommand::SendMetric(event)) = player_command.get_untracked()
                    else {
                        return;
                    };
                    assert_eq!(event, metric);
                } else {
                    assert!(player_command.get_untracked().is_none());
                }
            });
        }
    }

    mod navigate_from_linear_page {
        use super::*;

        #[rstest]
        #[case::rust_page(PageType::Rust(RustPage::RUST_LIVE_TV), true, true, false, None)]
        #[case::seamless_egress(
            PageType::Js(JSPage::START_PLAYBACK),
            true,
            false,
            true,
            Some(ReleaseState::NotSeamless)
        )]
        #[case::seamless_ingress(
            PageType::Js(JSPage::START_PLAYBACK),
            false,
            true,
            false,
            Some(ReleaseState::Seamless)
        )]
        fn sets_hybrid_context(
            #[case] page_type: PageType,
            #[case] is_seamless_playback_egress: bool,
            #[case] is_seamless_playback_ingress: bool,
            #[case] expected_seamless_egress: bool,
            #[case] expected_release_state: Option<ReleaseState>,
        ) {
            launch_only_scope(move |scope| {
                let location = setup_with_router(scope, page_type);
                let HybridPlaybackContext {
                    should_release,
                    seamless_egress,
                } = expect_context::<HybridPlaybackContext>(scope);
                seamless_egress.set_value(!is_seamless_playback_egress);

                let (_, update_td) = create_signal(
                    scope,
                    TitleDetailsChangeRequest {
                        data: TitleDetailsData::Empty,
                        navigation_direction: NavigationDirection::NONE,
                    },
                );
                navigate_from_linear_page(
                    scope,
                    is_seamless_playback_egress,
                    is_seamless_playback_ingress,
                    location.get_untracked(),
                    "origin",
                    update_td,
                );

                assert_eq!(expected_seamless_egress, seamless_egress.get_value());
                assert_eq!(expected_release_state, should_release.get_untracked());
            });
        }

        #[rstest]
        #[case::non_playback_page(PageType::Js(JSPage::LIVE_PAGE), true, "origin".to_string(), false)]
        #[case::seamless_egress(PageType::Js(JSPage::START_PLAYBACK), true, "origin2".to_string(), true)]
        #[case::not_seamless_egress(PageType::Js(JSPage::START_PLAYBACK), false, "origin3".to_string(), true)]
        fn adds_playback_page_params(
            #[case] page_type: PageType,
            #[case] is_seamless_playback_egress: bool,
            #[case] origin: String,
            #[case] should_update: bool,
        ) {
            launch_only_scope(move |scope| {
                let location = setup_with_router(scope, page_type);

                let (_, update_td) = create_signal(
                    scope,
                    TitleDetailsChangeRequest {
                        data: TitleDetailsData::Empty,
                        navigation_direction: NavigationDirection::NONE,
                    },
                );
                navigate_from_linear_page(
                    scope,
                    is_seamless_playback_egress,
                    false,
                    location.get_untracked(),
                    &origin,
                    update_td,
                );

                let last_location = expect_context::<RwSignal<Option<Location>>>(scope);
                let page_params = last_location.get_untracked().unwrap().pageParams;

                if should_update {
                    assert_eq!(
                        page_params.get(&"seamlessEgress".to_string()),
                        Some(&Value::Bool(is_seamless_playback_egress))
                    );
                    assert_eq!(
                        page_params.get(&"playbackOrigin".to_string()),
                        Some(&Value::String(origin))
                    );
                } else {
                    assert_eq!(page_params, Default::default());
                }
            });
        }
    }

    #[rstest]
    #[case::signed_in(true, mock_playback_action(), false)]
    #[case::with_player_action(false, mock_playback_action(), true)]
    #[case::with_playback_action(false, mock_title_playback_action(), true)]
    #[case::with_signup_action(false, mock_signup_action(), true)]
    #[case::with_other_action(false, mock_swift_details_action(), false)]
    fn navigates_to_sign_in(
        #[case] signed_in: bool,
        #[case] tranistion_action: TransitionAction,
        #[case] should_redirect: bool,
    ) {
        launch_only_scope(move |scope| {
            setup_with_router(scope, PageType::Rust(RustPage::RUST_LIVE_TV));

            let result = sign_in_if_necessary(scope, signed_in, &tranistion_action);

            assert_eq!(result, should_redirect);
            let should_release = expect_context::<HybridPlaybackContext>(scope)
                .should_release
                .get_untracked();
            if should_redirect {
                assert_eq!(should_release, Some(ReleaseState::NotSeamless));
            } else {
                assert_eq!(should_release, None);
            }
        });
    }

    #[rstest]
    #[case::seamless_ingress_and_egress(mock_playback_action(), Some("gti".to_string()), true, (true, true))]
    #[case::seamless_egress(mock_playback_action(), Some("gti2".to_string()), true, (true, false))]
    #[case::signup_action(mock_signup_action(), Some("gti".to_string()), true, (false, false))]
    #[case::always_on_disabled(mock_playback_action(), Some("gti".to_string()), false, (false, false))]
    fn gets_seamless_state(
        #[case] action: TransitionAction,
        #[case] always_on_gti: Option<String>,
        #[case] always_on_enabled: bool,
        #[case] expected_result: (bool, bool),
    ) {
        let result = get_seamless_state(&action, always_on_gti, always_on_enabled);

        assert_eq!(result, expected_result);
    }
}
