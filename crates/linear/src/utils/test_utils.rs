use std::rc::Rc;

use auth::{AuthContext, MockAuth};
use ignx_compositron::{
    input::KeyCode,
    prelude::*,
    reactive::store_value,
    test_utils::{assert_node_exists, node_properties::SceneNodeTree, TestRendererGameLoop},
};
use location::{Location, PageType, RustPage};
use media_background::types::HybridPlaybackContext;
use mockall::predicate;
use router::{MockRouting, RoutingContext};

pub fn press_key(
    test_loop: &mut TestRendererGameLoop,
    test_id: &str,
    key: KeyCode,
) -> SceneNodeTree {
    assert_node_focused(test_loop, test_id);
    #[allow(deprecated)]
    test_loop.send_key_down_up_event(key);
    test_loop.tick_until_done()
}

pub fn assert_node_focused(test_loop: &mut TestRendererGameLoop, test_id: &str) -> SceneNodeTree {
    let tree = test_loop.tick_until_done();
    assert!(
        tree.find_by_test_id(test_id).borrow_props().is_focused,
        "{test_id} is not focused"
    );
    tree
}

pub fn assert_node_text(test_loop: &mut TestRendererGameLoop, test_id: &str, expected_text: &str) {
    let tree = test_loop.tick_until_done();
    assert_eq!(
        tree.find_by_test_id(test_id).borrow_props().text,
        Some(expected_text.to_string())
    );
}

pub fn assert_node_has_descendant_with_text(
    test_loop: &mut TestRendererGameLoop,
    test_id: &str,
    expected_text: &'static str, // text() requires static str
) {
    let tree = test_loop.tick_until_done();
    let node = tree.find_by_test_id(test_id);
    assert_node_exists!(node.find_child_with().text(expected_text).find_first());
}

pub fn press_select(test_loop: &mut TestRendererGameLoop, test_id: &str) -> SceneNodeTree {
    let tree = assert_node_focused(test_loop, test_id);
    test_loop.send_on_select_event(tree.find_by_test_id(test_id).borrow_props().node_id);
    test_loop.tick_until_done()
}

pub fn mock_authorized(scope: Scope) {
    let mut mock_auth = MockAuth::new_without_params(scope);
    mock_auth.set_access_token(Some("mock-access-token".to_string()));
    provide_context::<AuthContext>(scope, Rc::new(mock_auth));
}

pub fn provide_hybrid_playback_context(scope: Scope) {
    let hybrid_playback_context = HybridPlaybackContext {
        should_release: create_rw_signal(scope, None),
        seamless_egress: store_value(scope, false),
    };
    provide_context(scope, hybrid_playback_context);
}

pub fn setup_location_mock(mock_routing_context: &mut MockRouting, ctx_scope: Scope) {
    mock_routing_context.expect_location().returning(move || {
        create_rw_signal(
            ctx_scope,
            Location {
                pageType: PageType::Rust(RustPage::RUST_LIVE_TV),
                pageParams: Default::default(),
            },
        )
        .into()
    });
}

pub fn provide_mock_routing_with_last_location(scope: Scope) {
    let mut mock_routing = MockRouting::new();
    let last_location_transition = create_rw_signal(scope, None);
    mock_routing
        .expect_navigate()
        .with(
            predicate::function(move |loc: &Location| {
                last_location_transition.set(Some(loc.clone()));
                true
            }),
            predicate::always(),
        )
        .times(..=1)
        .returning(|_, _| ());
    mock_routing.expect_mutate_history().returning(|_| {});
    setup_location_mock(&mut mock_routing, scope);

    provide_context::<RwSignal<Option<Location>>>(scope, last_location_transition);

    let mock_routing_context = Rc::new(mock_routing);
    provide_context::<RoutingContext>(scope, mock_routing_context);
}
