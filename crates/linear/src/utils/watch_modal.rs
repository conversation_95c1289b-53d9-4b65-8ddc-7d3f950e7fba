use crate::types::TitleCard;
use auth::is_last_known_state_signed_in;
use common_transform_types::container_items::EventMetadata;
use contextual_menu_types::buttons::{
    ContextualMenuButton, MoreDetailsButtonData, WatchlistButtonData,
};
use contextual_menu_types::prelude::{
    ContextualMenuData, ContextualMenuMetricsContext, ContextualMenuProperties,
};

use ignx_compositron::prelude::AppContext;
use location::RustPage;
use title_details::types::common::LivelinessData;
use watch_modal::helpers::event_card::{
    on_more_details_select_callback, on_watchlist_select_callback,
};

// Cannot use shared watch_modal_data helper because TitleCard has a different shape than Core UI cards
pub fn create_watch_modal_data(ctx: &AppContext, item: &TitleCard) -> Option<ContextualMenuData> {
    let mut buttons = vec![];
    buttons.push(ContextualMenuButton::MoreDetails(Box::new(
        MoreDetailsButtonData {
            id: "MoreDetailsButton".to_string(),
            action: item.action.clone(),
            on_select_cb: Some(on_more_details_select_callback()),
        },
    )));

    if is_last_known_state_signed_in(ctx.scope()) {
        buttons.push(ContextualMenuButton::AddToWatchlist(Box::new(
            WatchlistButtonData {
                id: "AddToWatchlistButton".to_string(),
                is_in_watchlist: item.is_in_watchlist,
                gti: item.gti.clone(),
                ref_marker: Some(item.action.get_ref_marker()),
                on_select_cb: Some(on_watchlist_select_callback()),
            },
        )));
    };

    Some(ContextualMenuData::WatchModal(ContextualMenuProperties {
        title: item.title.clone().unwrap_or_default(),
        gti: item.gti.clone(),
        metrics_context: ContextualMenuMetricsContext {
            page_type: RustPage::RUST_LIVE_TV,
            is_linear_title: false,
        },
        ref_marker: Some(item.action.get_ref_marker()),
        maturity_rating_image: None, // not supported by linear edge
        maturity_rating_string: item.maturity_rating.clone(),
        buttons,
        metadata_row: None,
        title_actions: None,
        event_metadata: Some(EventMetadata {
            liveliness: item
                .liveliness
                .as_ref()
                .map(|liveliness| liveliness.clone().into()),
            liveEventDateHeader: item.live_event_date_header.clone(),
            liveEventDateBadge: item.live_event_date_badge.clone(),
            venue: item.venue.clone(),
            scoreBug: None,
        }),
        tnf_properties: None, // not supported by linear edge
        on_dismiss_cb: None,
        liveliness_data: Some(LivelinessData {
            message: item
                .title_metadata_badge
                .as_ref()
                .and_then(|badge| badge.clone().message),
            level: item
                .title_metadata_badge
                .as_ref()
                .and_then(|badge| badge.clone().level)
                .map(|level| level.into()),
            liveliness: item
                .liveliness
                .as_ref()
                .map(|liveliness| liveliness.clone().into()),
        }),
        entitlement_label_data: None,
        journey_ingress_context: item.action.get_journey_ingress_context(),
        show_skeleton: None,
        content_type: item.content_type.clone(),
    }))
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data_util::mock_title_card;
    use crate::utils::test_utils::mock_authorized;
    use common_transform_types::container_items::{
        LiveEventDateTime, LocalizedLiveEventBadge, LocalizedLiveEventHeader,
    };
    use ignx_compositron::app::launch_only_app_context;
    use linear_common::types::TimeRange;
    use liveliness_types::Liveliness;

    #[test]
    fn creates_expected_data_for_authenticated_user() {
        launch_only_app_context(|ctx| {
            let title_card = mock_title_card("test_card", TimeRange::default());

            mock_authorized(ctx.scope());

            let data =
                create_watch_modal_data(&ctx, &title_card).expect("ContextualMenuData is None");
            let props = match data {
                ContextualMenuData::WatchModal(props) => props,
                _ => panic!("Expected WatchModal ContextualMenuData"),
            };

            // should create expected buttons
            assert_eq!(props.buttons.len(), 2);
            assert!(matches!(
                props.buttons[0],
                ContextualMenuButton::MoreDetails(_)
            ));
            assert!(matches!(
                props.buttons[1],
                ContextualMenuButton::AddToWatchlist(_)
            ));

            // props verification
            assert_eq!(props.title, "Title test_card");
            assert_eq!(props.gti, "test_gti");
            assert_eq!(props.metrics_context.page_type, RustPage::RUST_LIVE_TV);
            assert_eq!(props.metrics_context.is_linear_title, false);
            assert_eq!(props.ref_marker, Some("refMarker".to_string()));
            assert_eq!(props.maturity_rating_image, None);
            assert_eq!(props.maturity_rating_string, Some("18+".to_string()));
            assert_eq!(props.metadata_row, None);
            assert_eq!(props.title_actions, None);
            assert_eq!(
                props.event_metadata,
                Some(EventMetadata {
                    liveliness: Some(String::from("LIVE")),
                    liveEventDateHeader: Some(LiveEventDateTime::LOCALIZED_HEADER(
                        LocalizedLiveEventHeader {
                            date: Some(String::from("March 6, 2024")),
                            time: Some(String::from("9:25 PM")),
                        }
                    )),
                    liveEventDateBadge: Some(LiveEventDateTime::LOCALIZED_BADGE(
                        LocalizedLiveEventBadge {
                            text: String::from("Live at 9:25 PM EDT")
                        }
                    )),
                    venue: Some("Venue text".to_string()),
                    scoreBug: None,
                })
            );
            assert_eq!(props.tnf_properties, None);
            assert_eq!(
                props.liveliness_data,
                Some(LivelinessData {
                    message: Some(String::from("LIVE")),
                    level: Some(String::from("INFO_HIGHLIGHT")),
                    liveliness: Some(Liveliness::Live),
                })
            );
            assert_eq!(props.entitlement_label_data, None);
            assert_eq!(props.journey_ingress_context, None); // None in mocked data
        })
    }

    #[test]
    fn creates_expected_data_for_unauthenticated_user() {
        launch_only_app_context(|ctx| {
            let title_card = mock_title_card("test_card", TimeRange::default());

            let data =
                create_watch_modal_data(&ctx, &title_card).expect("ContextualMenuData is None");
            let props = match data {
                ContextualMenuData::WatchModal(props) => props,
                _ => panic!("Expected WatchModal ContextualMenuData"),
            };

            // should create expected buttons
            assert_eq!(props.buttons.len(), 1);
            assert!(matches!(
                props.buttons[0],
                ContextualMenuButton::MoreDetails(_)
            ));

            // props verification
            assert_eq!(props.title, "Title test_card");
            assert_eq!(props.gti, "test_gti");
            assert_eq!(props.metrics_context.page_type, RustPage::RUST_LIVE_TV);
            assert_eq!(props.metrics_context.is_linear_title, false);
            assert_eq!(props.ref_marker, Some("refMarker".to_string()));
            assert_eq!(props.maturity_rating_image, None);
            assert_eq!(props.maturity_rating_string, Some("18+".to_string()));
            assert_eq!(props.metadata_row, None);
            assert_eq!(props.title_actions, None);
            assert_eq!(
                props.event_metadata,
                Some(EventMetadata {
                    liveliness: Some(String::from("LIVE")),
                    liveEventDateHeader: Some(LiveEventDateTime::LOCALIZED_HEADER(
                        LocalizedLiveEventHeader {
                            date: Some(String::from("March 6, 2024")),
                            time: Some(String::from("9:25 PM")),
                        }
                    )),
                    liveEventDateBadge: Some(LiveEventDateTime::LOCALIZED_BADGE(
                        LocalizedLiveEventBadge {
                            text: String::from("Live at 9:25 PM EDT")
                        }
                    )),
                    venue: Some("Venue text".to_string()),
                    scoreBug: None,
                })
            );
            assert_eq!(props.tnf_properties, None);
            assert_eq!(
                props.liveliness_data,
                Some(LivelinessData {
                    message: Some(String::from("LIVE")),
                    level: Some(String::from("INFO_HIGHLIGHT")),
                    liveliness: Some(Liveliness::Live),
                })
            );
            assert_eq!(props.entitlement_label_data, None);
            assert_eq!(props.journey_ingress_context, None); // None in mocked data
        })
    }
}
