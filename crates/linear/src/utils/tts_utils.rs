use crate::types::AiringModel;
use crate::types::LinearTTSContext;
use crate::utils::common_string_ids::*;
use common_transform_types::container_items::ShowContext;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::create_effect;
use ignx_compositron::reactive::FocusValueSignal;
use ignx_compositron::reactive::RwSignal;
use ignx_compositron::reactive::Signal;
use ignx_compositron::reactive::SignalGet;
use ignx_compositron::reactive::SignalGetUntracked;
use ignx_compositron::reactive::SignalSet;
use ignx_compositron::text::LocalizedText;
use ignx_compositron::text::SubstitutionParameters;
use ignx_compositron::text::TextContent;
use linear_common::types::Schedule;
use linear_common::types::TimeRange;
use linear_common::util::datetime_util::get_formatted_times;
use linear_common::util::schedule_util::is_on_air_at_time;
use std::collections::HashMap;

use super::context_utils::get_current_time;
use super::context_utils::get_linear_tts_context;
use super::context_utils::get_station_details_epg_context;
use super::context_utils::get_tts_enabled;
use super::datetime_utils::get_time_remaining_for_tts;

pub fn get_tts_on_air_message(is_on_air: bool, station_name: &String) -> TextContent {
    TextContent::LocalizedText(LocalizedText {
        string_id: if is_on_air {
            AV_LRC_NOW_PLAYING_ON_STATION.into()
        } else {
            AV_LRC_UPCOMING_ON_STATION.into()
        },
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
            "stationName".to_owned(),
            TextContent::String(station_name.to_owned()),
        )]))),
    })
}

pub fn get_tts_time_range(time_range: (i64, i64)) -> TextContent {
    let (formatted_start_time, formatted_end_time) =
        get_formatted_times(time_range.0, time_range.1);

    TextContent::LocalizedText(LocalizedText {
        string_id: AV_LRC_LIVE_TV_AIRING_TIME_RANGE.into(),
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([
            (
                "startTime".to_owned(),
                TextContent::String(formatted_start_time),
            ),
            (
                "endTime".to_owned(),
                TextContent::String(formatted_end_time),
            ),
        ]))),
    })
}

pub fn get_tts_rating(rating: &String) -> TextContent {
    TextContent::LocalizedText(LocalizedText {
        string_id: AV_LRC_PB_REG_RATING.into(),
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
            "rating".to_owned(),
            TextContent::String(rating.clone()),
        )]))),
    })
}

fn get_tts_season(season_number: u32) -> TextContent {
    TextContent::LocalizedText(LocalizedText {
        string_id: AV_LRC_LIVE_TV_SEASON_NUMBER.into(),
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
            "seasonNumber".to_owned(),
            TextContent::String(season_number.to_string()),
        )]))),
    })
}

fn get_tts_episode(episode_number: u32) -> TextContent {
    TextContent::LocalizedText(LocalizedText {
        string_id: AV_LRC_LIVE_TV_EPISODE_NUMBER.into(),
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
            "episodeNumber".to_owned(),
            TextContent::String(episode_number.to_string()),
        )]))),
    })
}

pub fn get_tts_episodic_context(context: ShowContext) -> Vec<TextContent> {
    let mut accessibility_messages = vec![];

    if let Some(season) = context.season {
        accessibility_messages.push(get_tts_season(season));
    }

    if let Some(episode) = context.episode {
        accessibility_messages.push(get_tts_episode(episode));
    }

    if let Some(episode_title) = context.episodeTitle {
        accessibility_messages.push(TextContent::String(episode_title));
    }

    accessibility_messages
}

pub fn get_tts_modal_hint(is_on_air: bool, is_entitled: bool) -> TextContent {
    TextContent::LocalizedText(LocalizedText::new(if !is_on_air && is_entitled {
        AV_LRC_LIVE_TV_INFO_MODAL_UPCOMING_CLOSE.to_string()
    } else {
        AV_LRC_LIVE_TV_INFO_MODAL_ON_NOW_CLOSE.to_string()
    }))
}

pub fn setup_live_page_new_row_effects_for_tts(
    scope: Scope,
    is_new_row: RwSignal<bool>,
    focus_signal: FocusValueSignal<String>,
    context_messages: RwSignal<Vec<TextContent>>,
    header: Signal<String>,
) {
    let is_tts_enabled = get_tts_enabled(scope);

    // When user scrolls out of EPG, set up TTS to treat next epg focus as new row
    create_effect(scope, move |_| {
        if is_tts_enabled.get() {
            // Focus is outside of EPG. Next epg focus will be treated as new row
            if focus_signal.get().is_none() {
                is_new_row.set(true);
                context_messages.set(vec![TextContent::String(header.get_untracked())]);
            }
        }
    });

    // Set the header tts message when it changes
    create_effect(scope, move |prev_header| {
        if is_tts_enabled.get() {
            if Some(header.get()) != prev_header {
                context_messages.set(vec![TextContent::String(header.get_untracked())]);
            } else if !context_messages.get_untracked().is_empty() {
                context_messages.set(vec![]);
            }
            header.get_untracked()
        } else {
            String::default()
        }
    });
}

pub fn create_accessibility_messages_effect(
    scope: Scope,
    airing: &AiringModel,
    entitlement_message: Option<String>,
    station_name: String,
    accessibility_messages_setter: WriteSignal<Vec<TextContent>>,
) {
    let current_time = get_current_time(scope);
    let is_tts_enabled = get_tts_enabled(scope);
    let is_new_row = get_linear_tts_context(scope).is_new_row;

    create_effect(scope, {
        let time_range = airing.get_time_range();
        let AiringModel {
            title,
            context,
            rating,
            content_descriptors,
            synopsis,
            ..
        } = airing;
        let title = title.clone();
        let context = context.clone();
        let rating = rating.clone();
        let content_descriptors = content_descriptors.clone();
        let synopsis = synopsis.clone();

        move |prev| {
            let is_tts_enabled = is_tts_enabled.get();
            let is_new_row = is_new_row.get();
            let current_time = current_time.get();
            let is_on_air = is_on_air_at_time(time_range, current_time);

            // Prevent unnecessary update when current time or state change
            let state = (is_tts_enabled, is_new_row, is_on_air);
            if prev.is_some_and(|p| p == state) {
                return state;
            }

            if is_tts_enabled {
                accessibility_messages_setter.set(build_airing_tts_message(
                    is_new_row,
                    current_time,
                    &title,
                    time_range,
                    &entitlement_message,
                    &synopsis,
                    &context,
                    &rating,
                    &content_descriptors,
                    &station_name,
                ));
            };
            state
        }
    });
}

fn build_airing_tts_message(
    first_focus_in_row: bool,
    current_time: i64,
    title: &str,
    time_range: TimeRange,
    entitlement_message: &Option<String>,
    synopsis: &Option<String>,
    context: &Option<ShowContext>,
    rating: &Option<String>,
    content_descriptors: &[String],
    station_name: &String,
) -> Vec<TextContent> {
    let mut accessibility_messages: Vec<TextContent> = vec![];

    let rating = rating.clone().unwrap_or_default();
    let content_descriptors = content_descriptors.join(", ");

    let is_on_air = is_on_air_at_time(time_range, current_time);

    if first_focus_in_row {
        accessibility_messages.push(get_tts_on_air_message(is_on_air, station_name));
    }

    if !is_on_air {
        accessibility_messages.push(get_tts_time_range(time_range));
    }

    accessibility_messages.push(TextContent::String(title.to_string()));

    if is_on_air {
        accessibility_messages.push(get_time_remaining_for_tts(current_time, time_range.1));
    }

    if first_focus_in_row {
        if let Some(entitlement_message) = entitlement_message.clone() {
            accessibility_messages.push(TextContent::String(entitlement_message));
        }
    }

    if !rating.is_empty() {
        accessibility_messages.push(get_tts_rating(&rating));
    }

    if !content_descriptors.is_empty() {
        accessibility_messages.push(TextContent::String(content_descriptors));
    }

    if let Some(context) = context.clone() {
        accessibility_messages.append(&mut get_tts_episodic_context(context));
    }

    if let Some(synopsis) = synopsis.clone() {
        accessibility_messages.push(TextContent::String(synopsis));
    }
    accessibility_messages
}

pub fn create_focused_airing_tts_effect(
    scope: Scope,
    airings_signal: ReadSignal<Vec<AiringModel>>,
    station_name: &String,
    entitlement_message: &Option<String>,
) {
    let focus_index = get_station_details_epg_context(scope).focus_index;

    let current_time = get_current_time(scope);
    let LinearTTSContext {
        is_new_row,
        focused_metadata_messages: focused_metadata_msg,
    } = get_linear_tts_context(scope);

    let is_tts_enabled = get_tts_enabled(scope);

    let station_name = station_name.to_owned();
    let entitlement_message = entitlement_message.to_owned();
    create_effect(scope, move |_| {
        if is_tts_enabled.get() {
            let message = airings_signal
                .with_untracked(|airings| {
                    let airing = airings.get(focus_index.get())?;
                    let current_time = current_time.get();
                    let time_range = airing.get_time_range();
                    let AiringModel {
                        title,
                        synopsis,
                        context,
                        rating,
                        content_descriptors,
                        ..
                    } = &airing;

                    Some(build_airing_tts_message(
                        is_new_row.get(),
                        current_time,
                        title,
                        time_range,
                        &entitlement_message,
                        synopsis,
                        context,
                        rating,
                        content_descriptors,
                        &station_name.clone(),
                    ))
                })
                .unwrap_or_default();
            focused_metadata_msg.set(message);
        }
    });
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::mocks::mock_data_util::mock_airing_item;
    use crate::mocks::mock_utc::Utc;
    use crate::ui::epg::constants::{HOUR_IN_MS, TEN_MIN_IN_MILLIS};
    use crate::{
        mocks::mock_data_util::mock_airing_items_with_start_time,
        mocks::mock_utc::MOCK_NOW_MS,
        station_details::{
            epg::details_epg_utils::build_details_epg_context,
            station_details_page::MAX_TIMELINE_LENGTH,
        },
    };
    use ignx_compositron::tts::TTSEnabledContext;
    use ignx_compositron::{
        app::launch_only_scope,
        reactive::{create_focus_value_signal, create_rw_signal},
    };
    use rstest::rstest;

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn test_get_tts_on_air_message(#[case] is_on_air: bool) {
        let station_name = "Station Name".to_owned();
        let result = get_tts_on_air_message(is_on_air, &station_name);

        match result {
            TextContent::LocalizedText(localized_text) => {
                assert_eq!(
                    localized_text.string_id.as_string(),
                    if is_on_air {
                        AV_LRC_NOW_PLAYING_ON_STATION
                    } else {
                        AV_LRC_UPCOMING_ON_STATION
                    }
                );

                assert_eq!(
                    localized_text.substitution_parameters.unwrap().0["stationName"],
                    TextContent::String(station_name)
                );
            }
            TextContent::String(_) => panic!("Expected LocalizedText"),
        }
    }

    #[test]
    fn test_get_tts_time_range() {
        // March 6, 2024 9:25:30 PM GMT
        let start_time = 1709760330000;
        // March 6, 2024 10:30:00 PM GMT
        let end_time = 1709764200000;

        let result = get_tts_time_range((start_time, end_time));

        match result {
            TextContent::LocalizedText(localized_text) => {
                assert_eq!(
                    localized_text.string_id.as_string(),
                    AV_LRC_LIVE_TV_AIRING_TIME_RANGE
                );

                let substitution_parameters = localized_text.substitution_parameters.unwrap().0;
                assert_eq!(
                    substitution_parameters["startTime"],
                    TextContent::String("9:25".to_string())
                );
                assert_eq!(
                    substitution_parameters["endTime"],
                    TextContent::String("10:30 PM".to_string())
                );
            }
            TextContent::String(_) => panic!("Expected LocalizedText"),
        }
    }

    #[test]
    fn test_get_tts_rating() {
        let rating = "R".to_string();
        let result = get_tts_rating(&rating);

        match result {
            TextContent::LocalizedText(localized_text) => {
                assert_eq!(localized_text.string_id.as_string(), AV_LRC_PB_REG_RATING);

                assert_eq!(
                    localized_text.substitution_parameters.unwrap().0["rating"],
                    TextContent::String(rating)
                );
            }
            TextContent::String(_) => panic!("Expected LocalizedText"),
        }
    }

    #[test]
    fn test_get_tts_season() {
        let season_number = 2;
        let result = get_tts_season(season_number);

        match result {
            TextContent::LocalizedText(localized_text) => {
                assert_eq!(
                    localized_text.string_id.as_string(),
                    "AV_LRC_LIVE_TV_SEASON_NUMBER"
                );

                assert_eq!(
                    localized_text.substitution_parameters.unwrap().0["seasonNumber"],
                    TextContent::String(season_number.to_string())
                );
            }
            TextContent::String(_) => panic!("Expected LocalizedText"),
        }
    }

    #[test]
    fn test_get_tts_episode() {
        let episode_number = 5;
        let result = get_tts_episode(episode_number);

        match result {
            TextContent::LocalizedText(localized_text) => {
                assert_eq!(
                    localized_text.string_id.as_string(),
                    "AV_LRC_LIVE_TV_EPISODE_NUMBER"
                );

                assert_eq!(
                    localized_text.substitution_parameters.unwrap().0["episodeNumber"],
                    TextContent::String(episode_number.to_string())
                );
            }
            TextContent::String(_) => panic!("Expected LocalizedText"),
        }
    }

    #[test]
    fn test_get_episodic_context() {
        let context = ShowContext {
            season: Some(2),
            episode: Some(5),
            episodeTitle: Some("Episode Title".to_owned()),
        };

        let result = get_tts_episodic_context(context);

        assert_eq!(result.len(), 3);
        assert_eq!(result[0].clone(), get_tts_season(2));
        assert_eq!(result[1].clone(), get_tts_episode(5));
        assert_eq!(
            result[2].clone(),
            TextContent::String("Episode Title".to_owned())
        );
    }

    #[rstest]
    #[case(true, true, true, 3)]
    #[case(true, true, false, 2)]
    #[case(true, false, true, 2)]
    #[case(true, false, false, 1)]
    #[case(false, true, true, 2)]
    #[case(false, true, false, 1)]
    #[case(false, false, true, 1)]
    #[case(false, false, false, 0)]
    fn test_get_episodic_context_with_optional_values(
        #[case] has_episode: bool,
        #[case] has_season: bool,
        #[case] has_title: bool,
        #[case] expected_length: usize,
    ) {
        let context = ShowContext {
            season: if has_season { Some(2) } else { None },
            episode: if has_episode { Some(5) } else { None },
            episodeTitle: if has_title {
                Some("Episode Title".to_owned())
            } else {
                None
            },
        };

        let result = get_tts_episodic_context(context);
        assert_eq!(result.len(), expected_length);
    }

    const ON_AIR: bool = true;
    const UPCOMING: bool = false;
    const ENTITLED: bool = true;
    const UNENTITLED: bool = false;
    #[rstest]
    #[case(ON_AIR, ENTITLED)]
    #[case(ON_AIR, UNENTITLED)]
    #[case(UPCOMING, ENTITLED)]
    #[case(UPCOMING, UNENTITLED)]
    fn test_get_tts_modal_hint(#[case] is_on_air: bool, #[case] is_entitled: bool) {
        let result = get_tts_modal_hint(is_on_air, is_entitled);

        match result {
            TextContent::LocalizedText(localized_text) => {
                assert_eq!(
                    localized_text.string_id.as_string(),
                    if !is_on_air && is_entitled {
                        AV_LRC_LIVE_TV_INFO_MODAL_UPCOMING_CLOSE
                    } else {
                        AV_LRC_LIVE_TV_INFO_MODAL_ON_NOW_CLOSE
                    }
                );
            }
            TextContent::String(_) => panic!("Expected LocalizedText"),
        }
    }

    #[test]
    fn test_setup_new_row_effects_for_tts_updates_effects() {
        launch_only_scope(|scope| {
            provide_context(scope, TTSEnabledContext(create_signal(scope, true).0));
            let is_new_row = create_rw_signal(scope, false);
            let focus_signal = create_focus_value_signal(scope);
            let context_messages: RwSignal<Vec<TextContent>> =
                create_rw_signal(scope, vec![TextContent::String("title".to_string())]);
            let title = create_rw_signal(scope, "Header title 1".to_string());
            let title_signal = Signal::derive(scope, move || title.get());
            setup_live_page_new_row_effects_for_tts(
                scope,
                is_new_row,
                focus_signal,
                context_messages,
                title_signal,
            );

            // context message and is_new_row are reset when focus is None
            assert_eq!(
                TextContent::String(title.get_untracked()),
                context_messages.get_untracked().first().unwrap().clone()
            );
            assert!(is_new_row.get_untracked());

            // context message is empty when header doesn't change
            title.set("Header title 1".to_string());
            assert!(context_messages.get_untracked().is_empty());

            // context message updates when header changes
            title.set("Header title 2".to_string());
            assert_eq!(
                TextContent::String(title.get_untracked()),
                context_messages.get_untracked().first().unwrap().clone()
            );
        });
    }

    #[test]
    fn test_setup_new_row_effects_for_tts_ignores_when_tts_disabled() {
        launch_only_scope(|scope| {
            provide_context(scope, TTSEnabledContext(create_signal(scope, false).0));
            let is_new_row = create_rw_signal(scope, false);
            let focus_signal = create_focus_value_signal(scope);
            let context_messages: RwSignal<Vec<TextContent>> =
                create_rw_signal(scope, vec![TextContent::String("title".to_string())]);
            let title = create_rw_signal(scope, "Header title 1".to_string());
            let title_signal = Signal::derive(scope, move || title.get());
            setup_live_page_new_row_effects_for_tts(
                scope,
                is_new_row,
                focus_signal,
                context_messages,
                title_signal,
            );

            // context message are unchanged on initial run
            assert_eq!(
                TextContent::String("title".to_string()),
                context_messages.get_untracked().first().unwrap().clone()
            );
            assert!(!is_new_row.get_untracked());

            // context message is not changed on header change
            title.set("Header title 1".to_string());
            assert_eq!(
                TextContent::String("title".to_string()),
                context_messages.get_untracked().first().unwrap().clone()
            );
        });
    }

    mod accessibility_messages_effect {
        use current_time::CurrentTimeContext;

        use super::*;

        fn setup_tts_test(
            scope: Scope,
            is_on_air: bool,
            is_new_row: bool,
            current_time: i64,
            tts_enabled: bool,
        ) -> ReadSignal<Vec<TextContent>> {
            provide_context(
                scope,
                LinearTTSContext {
                    is_new_row: create_rw_signal(scope, is_new_row),

                    focused_metadata_messages: create_rw_signal(scope, vec![]),
                },
            );
            provide_context(
                scope,
                TTSEnabledContext(create_signal(scope, tts_enabled).0),
            );

            let current_time_signal = create_rw_signal(scope, current_time);
            provide_context(
                scope,
                CurrentTimeContext {
                    current_time: current_time_signal.read_only(),
                },
            );
            provide_context(scope, current_time_signal);

            let (accessibility_messages, accessibility_messages_setter) =
                create_signal(scope, vec![]);

            let start_time = if is_on_air {
                current_time - TEN_MIN_IN_MILLIS
            } else {
                current_time + TEN_MIN_IN_MILLIS
            };
            let entitlement_message = Some("entitlement message".to_string());

            let station_name = "station name".to_string();
            let airing = mock_airing_item(
                "airing title".to_string(),
                start_time,
                current_time + HOUR_IN_MS,
            );

            create_accessibility_messages_effect(
                scope,
                &airing,
                entitlement_message,
                station_name,
                accessibility_messages_setter,
            );

            accessibility_messages
        }

        #[test]
        fn test_accessibility_messages_for_on_air_cards_first_focus_in_row() {
            launch_only_scope(|scope| {
                let is_on_air = true;
                let is_new_row = true;
                let current_time = Utc::now().timestamp_millis();
                let tts_enabled = true;
                let tts_messages =
                    setup_tts_test(scope, is_on_air, is_new_row, current_time, tts_enabled)
                        .get_untracked();

                assert_eq!(tts_messages.len(), 10);
                assert_eq!(
                    tts_messages[0],
                    get_tts_on_air_message(is_on_air, &"station name".to_string())
                );
                assert_eq!(
                    tts_messages[1],
                    TextContent::String("airing title".to_string())
                );
                assert_eq!(
                    tts_messages[2],
                    get_time_remaining_for_tts(current_time, current_time + HOUR_IN_MS)
                );
                assert_eq!(
                    tts_messages[3],
                    TextContent::String("entitlement message".to_string())
                );
                assert_eq!(tts_messages[4], get_tts_rating(&"18+".to_string()));
                assert_eq!(
                    tts_messages[5],
                    TextContent::String("Language, Violence".to_string())
                );
                assert_eq!(
                    tts_messages[6..=8],
                    get_tts_episodic_context(ShowContext {
                        episode: Some(5),
                        season: Some(1),
                        episodeTitle: Some("Some info".to_string()),
                    })
                );
                assert_eq!(
                    tts_messages[9],
                    TextContent::String("Synopsis text".to_string())
                );
            });
        }

        #[test]
        fn test_accessibility_messages_for_on_air_cards_second_focus_in_row() {
            launch_only_scope(|scope| {
                let is_on_air = true;
                let is_new_row = false;
                let current_time = Utc::now().timestamp_millis();
                let tts_enabled = true;
                let tts_messages =
                    setup_tts_test(scope, is_on_air, is_new_row, current_time, tts_enabled)
                        .get_untracked();

                assert_eq!(tts_messages.len(), 8);
                assert_eq!(
                    tts_messages[0],
                    TextContent::String("airing title".to_string())
                );
                assert_eq!(
                    tts_messages[1],
                    get_time_remaining_for_tts(current_time, current_time + HOUR_IN_MS)
                );
                assert_eq!(tts_messages[2], get_tts_rating(&"18+".to_string()));
                assert_eq!(
                    tts_messages[3],
                    TextContent::String("Language, Violence".to_string())
                );
                assert_eq!(
                    tts_messages[4..=6],
                    get_tts_episodic_context(ShowContext {
                        episode: Some(5),
                        season: Some(1),
                        episodeTitle: Some("Some info".to_string()),
                    })
                );
                assert_eq!(
                    tts_messages[7],
                    TextContent::String("Synopsis text".to_string())
                );
            });
        }

        #[test]
        fn test_accessibility_messages_for_upcoming_cards_first_focus_in_row() {
            launch_only_scope(|scope| {
                let is_on_air = false;
                let is_new_row = true;
                let current_time = Utc::now().timestamp_millis();
                let tts_enabled = true;
                let tts_messages =
                    setup_tts_test(scope, is_on_air, is_new_row, current_time, tts_enabled)
                        .get_untracked();

                assert_eq!(tts_messages.len(), 10);
                assert_eq!(
                    tts_messages[0],
                    get_tts_on_air_message(is_on_air, &"station name".to_string())
                );
                assert_eq!(
                    tts_messages[1],
                    get_tts_time_range((
                        current_time + TEN_MIN_IN_MILLIS,
                        current_time + HOUR_IN_MS
                    ))
                );
                assert_eq!(
                    tts_messages[2],
                    TextContent::String("airing title".to_string())
                );
                assert_eq!(
                    tts_messages[3],
                    TextContent::String("entitlement message".to_string())
                );
                assert_eq!(tts_messages[4], get_tts_rating(&"18+".to_string()));
                assert_eq!(
                    tts_messages[5],
                    TextContent::String("Language, Violence".to_string())
                );
                assert_eq!(
                    tts_messages[6..=8],
                    get_tts_episodic_context(ShowContext {
                        episode: Some(5),
                        season: Some(1),
                        episodeTitle: Some("Some info".to_string()),
                    })
                );
                assert_eq!(
                    tts_messages[9],
                    TextContent::String("Synopsis text".to_string())
                );
            });
        }

        #[test]
        fn test_accessibility_messages_for_upcoming_cards_second_focus_in_row() {
            launch_only_scope(|scope| {
                let is_on_air = false;
                let is_new_row = false;
                let current_time = Utc::now().timestamp_millis();
                let tts_enabled = true;
                let tts_messages =
                    setup_tts_test(scope, is_on_air, is_new_row, current_time, tts_enabled)
                        .get_untracked();

                assert_eq!(tts_messages.len(), 8);
                assert_eq!(
                    tts_messages[0],
                    get_tts_time_range((
                        current_time + TEN_MIN_IN_MILLIS,
                        current_time + HOUR_IN_MS
                    ))
                );
                assert_eq!(
                    tts_messages[1],
                    TextContent::String("airing title".to_string())
                );
                assert_eq!(tts_messages[2], get_tts_rating(&"18+".to_string()));
                assert_eq!(
                    tts_messages[3],
                    TextContent::String("Language, Violence".to_string())
                );
                assert_eq!(
                    tts_messages[4..=6],
                    get_tts_episodic_context(ShowContext {
                        episode: Some(5),
                        season: Some(1),
                        episodeTitle: Some("Some info".to_string()),
                    })
                );
                assert_eq!(
                    tts_messages[7],
                    TextContent::String("Synopsis text".to_string())
                );
            });
        }

        #[test]
        fn test_accessibility_messages_only_update_when_state_change() {
            launch_only_scope(|scope| {
                let is_on_air = false;
                let is_new_row = true;
                let current_time = Utc::now().timestamp_millis();
                let tts_enabled = true;
                let tts_messages =
                    setup_tts_test(scope, is_on_air, is_new_row, current_time, tts_enabled);

                let current_time_signal = expect_context::<RwSignal<i64>>(scope);

                let update_count = create_rw_signal(scope, 0);
                create_effect(scope, move |_| {
                    tts_messages.track();
                    update_count.set(update_count.get_untracked() + 1);
                });

                // Verify initial message set
                assert_eq!(update_count.get_untracked(), 1);
                assert!(tts_messages.get_untracked().len() > 0);

                // Verify message does not update after time change
                current_time_signal.set(current_time_signal.get_untracked() + 1);
                assert_eq!(update_count.get_untracked(), 1);

                // Verify message updates after airing is on now
                current_time_signal.set(current_time_signal.get_untracked() + TEN_MIN_IN_MILLIS);
                assert_eq!(update_count.get_untracked(), 2);
                assert!(tts_messages.get_untracked().len() > 0);
            });
        }

        #[test]
        fn no_messages_when_tts_disabled() {
            launch_only_scope(|scope| {
                let is_on_air = false;
                let is_new_row = false;
                let current_time = Utc::now().timestamp_millis();
                let tts_enabled = false;
                let tts_messages =
                    setup_tts_test(scope, is_on_air, is_new_row, current_time, tts_enabled)
                        .get_untracked();

                assert_eq!(tts_messages.len(), 0);
            });
        }
    }

    mod create_focused_airing_tts_effect {
        use current_time::CurrentTimeContext;

        use crate::station_details::types::StationDetailsEpgContext;

        use super::*;

        fn setup(scope: Scope, tts_enabled: bool) {
            provide_context(
                scope,
                CurrentTimeContext {
                    current_time: create_signal(scope, MOCK_NOW_MS).0,
                },
            );
            let airings_signal = create_signal(
                scope,
                mock_airing_items_with_start_time("station_id", 30, 10, MOCK_NOW_MS),
            )
            .0;
            let epg_context = build_details_epg_context(scope, MOCK_NOW_MS + MAX_TIMELINE_LENGTH);
            provide_context(scope, epg_context);
            provide_context(scope, get_linear_tts_context(scope));
            provide_context(
                scope,
                TTSEnabledContext(create_signal(scope, tts_enabled).0),
            );
            let entitlement_message = &Some("entitlement".to_string());

            create_focused_airing_tts_effect(
                scope,
                airings_signal,
                &"station_name".to_string(),
                entitlement_message,
            );
        }

        #[test]
        fn does_nothing_with_tts_disabled() {
            launch_only_scope(|scope| {
                setup(scope, false);
                let focused_metadata_messages =
                    get_linear_tts_context(scope).focused_metadata_messages;
                let epg_context = expect_context::<StationDetailsEpgContext>(scope);

                // set a fake message
                let mock_messages = vec![TextContent::String("mock".to_string())];
                focused_metadata_messages.set_untracked(mock_messages.clone());

                // Trigger update
                epg_context.focus_index.update(|_| {});

                // message should not be changed
                assert_eq!(focused_metadata_messages.get_untracked(), mock_messages);
            });
        }

        #[test]
        fn updates_on_focus_changes() {
            launch_only_scope(|scope| {
                setup(scope, true);

                let focused_metadata_messages =
                    get_linear_tts_context(scope).focused_metadata_messages;
                let epg_context = expect_context::<StationDetailsEpgContext>(scope);

                // verify initial run sets metadata for first, on now airing
                assert_eq!(
                    focused_metadata_messages.get_untracked()[1],
                    TextContent::String("station_id_0".to_string())
                );

                // move to next airing
                epg_context.focus_index.set(1);

                // verify messages updated for upcoming airing
                assert_eq!(
                    focused_metadata_messages.get_untracked()[2],
                    TextContent::String("station_id_1".to_string())
                );

                // move to non-existant airing
                epg_context.focus_index.set(100);

                // verify messages are empty
                assert_eq!(focused_metadata_messages.get_untracked(), vec![]);
            });
        }
    }
}
