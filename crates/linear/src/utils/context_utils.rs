use crate::constants::{
    HOUR_WIDTH, LIVE_PAGE_CONTENT_HEIGHT_NO_HERO, LIVE_PAGE_EPG_WIDTH,
    STATION_HORIZONTAL_SCROLL_PEEK_WIDTH, STATION_LOGO_WIDTH, STATION_ROW_HEIGHT,
};
use crate::metrics::linear_reporter::{self};
use crate::station_details::details_pointer_controls::StationDetailsPointerControlsContext;
use crate::station_details::epg::details_epg_utils::build_details_epg_context;
use crate::station_details::station_details_page::MAX_TIMELINE_LENGTH;
use crate::station_details::types::StationDetailsEpgContext;
use crate::types::{EpgContext, StoredReactionSignals};
use crate::ui::epg::epg_util::build_epg_context;
use crate::{metrics::page_load_tracker::PageLoadTracker, types::LinearTTSContext};
use chrono::Utc;
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use current_time::CurrentTimeContext;
use fableous::toasts::toast_context::ToastContext;
use ignx_compositron::pointer_control::PointerControlActiveContext;
use ignx_compositron::reactive::store_value;
use ignx_compositron::{prelude::*, tts::TTSEnabledContext};
use location::PageType;
use navigation_menu::context::nav_context::{NavControl, TopNavMode};
use std::collections::HashMap;
use std::rc::Rc;

pub fn get_nav_control(scope: Scope) -> NavControl {
    match use_context::<NavControl>(scope) {
        Some(nav_control) => nav_control,
        None => {
            linear_reporter::base_reporter::on_context_missing_error("NavControl");

            //TODO: add metric if nav control is missing
            let show_top_nav_intent = create_rw_signal(scope, false);
            let (_, set_report_top_nav_visibility) = create_signal(scope, None);
            let show_utility_nav = create_rw_signal(scope, true);
            let focused_nav = create_focus_value_signal(scope);
            let (top_nav_mode, _) = create_signal(scope, TopNavMode::TopNav);
            let disable_top_nav_focus_trap = create_rw_signal(scope, false);
            let disable_utility_nav_focus_trap = create_rw_signal(scope, false);
            let enable_focus = create_rw_signal(scope, true);

            NavControl {
                disable_top_nav_focus_trap,
                disable_utility_nav_focus_trap,
                show_top_nav_intent,
                show_utility_nav,
                set_report_top_nav_visibility,
                focused_nav,
                top_nav_mode,
                enable_focus,
            }
        }
    }
}

// Returns LinearTTSContext from context, and provides it if it is missing
pub fn get_linear_tts_context(scope: Scope) -> LinearTTSContext {
    use_context::<LinearTTSContext>(scope).unwrap_or_else(|| {
        let context = LinearTTSContext {
            is_new_row: create_rw_signal(scope, true),
            focused_metadata_messages: create_rw_signal(scope, vec![]),
        };
        provide_context(scope, context);
        context
    })
}

// Returns `is_tts_enabled` from `TTSEnabledContext`
// Defaults to false if context is missing
pub fn get_tts_enabled(scope: Scope) -> ReadSignal<bool> {
    if let Some(tts_context) = use_context::<TTSEnabledContext>(scope) {
        return tts_context.0;
    }

    linear_reporter::base_reporter::on_context_missing_error("TTSEnabledContext");

    create_signal(scope, false).0
}

pub fn get_pointer_control_active(scope: Scope) -> ReadSignal<bool> {
    if let Some(pointer_context) = use_context::<PointerControlActiveContext>(scope) {
        return pointer_context.0;
    }

    linear_reporter::base_reporter::on_context_missing_error("PointerControlActiveContext");

    create_signal(scope, false).0
}

pub fn get_details_pointer_control_context(scope: Scope) -> StationDetailsPointerControlsContext {
    if let Some(pointer_control_ctx) = use_context::<StationDetailsPointerControlsContext>(scope) {
        pointer_control_ctx
    } else {
        linear_reporter::base_reporter::on_context_missing_error(
            "StationDetailsPointerControlsContext",
        );

        let (pointer_select_direction, _) = create_signal(scope, None);
        StationDetailsPointerControlsContext {
            pointer_select_direction,
        }
    }
}

//  Warning: Fallback returns a static timestamp that will not update
pub fn get_current_time(scope: Scope) -> ReadSignal<i64> {
    if let Some(context) = use_context::<CurrentTimeContext>(scope) {
        return context.current_time;
    }

    linear_reporter::base_reporter::on_context_missing_error("CurrentTimeContext");

    create_signal(scope, Utc::now().timestamp_millis()).0
}

pub fn get_time_to_interactive_registry_context(
    scope: Scope,
    page_type: PageType,
) -> Rc<TimeToInteractiveRegistry> {
    use_context::<Rc<TimeToInteractiveRegistry>>(scope).unwrap_or_else(|| {
        let registry = Rc::new(TimeToInteractiveRegistry::new(
            scope,
            vec![],
            page_type,
            None,
        ));

        provide_context(scope, registry.clone());
        registry
    })
}

// Returns `PageLoadTracker` from context, and provides it if missing
pub fn get_page_load_tracker(scope: Scope) -> PageLoadTracker {
    use_context::<PageLoadTracker>(scope).unwrap_or_else(|| {
        let tracker = PageLoadTracker::new();
        provide_context(scope, tracker.clone());
        tracker
    })
}

// Returns `ToastContext` from context, and provides it if missing
pub fn get_toast_context(scope: Scope) -> ToastContext {
    use_context::<ToastContext>(scope).unwrap_or_else(|| {
        let toast_context = ToastContext::new(scope);
        provide_context(scope, toast_context);
        toast_context
    })
}

// Returns `StoredReactionSignals` from context, and provides it if missing
pub fn get_stored_reaction_signals(scope: Scope) -> StoredReactionSignals {
    use_context::<StoredReactionSignals>(scope).unwrap_or_else(|| {
        let new_map: StoredReactionSignals = store_value(scope, HashMap::new());
        provide_context::<StoredReactionSignals>(scope, new_map);
        new_map
    })
}

pub fn get_station_details_epg_context(scope: Scope) -> StationDetailsEpgContext {
    if let Some(context) = use_context::<StationDetailsEpgContext>(scope) {
        return context;
    }

    linear_reporter::base_reporter::on_context_missing_error("StationDetailsEpgContext");

    let current_time = get_current_time(scope);

    let epg_context =
        build_details_epg_context(scope, current_time.get_untracked() + MAX_TIMELINE_LENGTH);

    epg_context
}

pub fn get_epg_context(scope: Scope) -> EpgContext {
    if let Some(context) = use_context::<EpgContext>(scope) {
        return context;
    }

    linear_reporter::base_reporter::on_context_missing_error("EpgContext");

    let current_time = get_current_time(scope);

    let twelve_hours_in_ms = 12 * 60 * 60 * 1000;

    let schedule_data_window = create_rw_signal(
        scope,
        (current_time.get(), current_time.get() + twelve_hours_in_ms),
    );

    let carousel_width = Signal::derive(scope, move || LIVE_PAGE_EPG_WIDTH - STATION_LOGO_WIDTH);

    let epg_context = build_epg_context(
        scope,
        current_time,
        schedule_data_window.into(),
        HOUR_WIDTH,
        STATION_HORIZONTAL_SCROLL_PEEK_WIDTH,
        carousel_width.into(),
        LIVE_PAGE_CONTENT_HEIGHT_NO_HERO,
        STATION_ROW_HEIGHT,
    );

    epg_context
}

#[cfg(test)]
mod test {
    use core::assert_eq;

    use crate::{
        station_details::epg::details_epg_utils::scroll_to_index,
        types::{EpgFocusState, EpgScrollState, EpgTimelineState},
    };

    use super::*;
    use ignx_compositron::app::launch_only_scope;
    use location::RustPage;

    mod nav_control {
        use super::*;

        #[test]
        fn returns_provided_context() {
            launch_only_scope(|scope| {
                let (_, set_report_top_nav_visibility) = create_signal(scope, None);
                let (top_nav_mode, _) = create_signal(scope, TopNavMode::QuickNav);
                let nav_control_context = NavControl {
                    disable_top_nav_focus_trap: create_rw_signal(scope, false),
                    disable_utility_nav_focus_trap: create_rw_signal(scope, false),
                    show_top_nav_intent: create_rw_signal(scope, false),
                    set_report_top_nav_visibility,
                    show_utility_nav: create_rw_signal(scope, true),
                    focused_nav: create_focus_value_signal(scope),
                    top_nav_mode,
                    enable_focus: create_rw_signal(scope, true),
                };
                provide_context(scope, nav_control_context);

                let nav_control = get_nav_control(scope);
                assert!(matches!(
                    nav_control.top_nav_mode.get_untracked(),
                    TopNavMode::QuickNav
                ));
            })
        }

        #[test]
        fn returns_default_context() {
            launch_only_scope(|scope| {
                let nav_control = get_nav_control(scope);
                assert!(matches!(
                    nav_control.top_nav_mode.get_untracked(),
                    TopNavMode::TopNav
                ));
            })
        }
    }

    #[test]
    fn returns_default_signal_for_get_pointer_control_active() {
        launch_only_scope(|scope| {
            let context = get_pointer_control_active(scope);

            assert_eq!(context.get(), false);
        });
    }

    #[test]
    fn returns_context_for_get_pointer_control_active() {
        launch_only_scope(|scope| {
            let expected_val = true;
            let signal = create_signal(scope, expected_val).0;
            provide_context(scope, PointerControlActiveContext(signal));

            let context = get_pointer_control_active(scope);

            assert_eq!(context.get(), expected_val);
        });
    }

    #[test]
    fn returns_default_signal_for_get_tts_enabled() {
        launch_only_scope(|scope| {
            let context = get_tts_enabled(scope);

            assert_eq!(context.get(), false);
        });
    }

    #[test]
    fn returns_context_for_get_tts_enabled() {
        launch_only_scope(|scope| {
            let expected_val = true;
            let signal = create_signal(scope, expected_val).0;
            provide_context(scope, TTSEnabledContext(signal));

            let context = get_tts_enabled(scope);

            assert_eq!(context.get(), expected_val);
        });
    }

    #[test]
    fn returns_default_signal_for_get_current_time() {
        launch_only_scope(|scope| {
            let current_time = get_current_time(scope);

            assert!(current_time.get() > 0);
        });
    }

    #[test]
    fn returns_context_for_get_current_time() {
        launch_only_scope(|scope| {
            let expected_val = 50;
            let signal = create_signal(scope, expected_val);
            provide_context(
                scope,
                CurrentTimeContext {
                    current_time: signal.0,
                },
            );

            let context = get_current_time(scope);

            assert_eq!(context.get(), expected_val);
        });
    }

    #[test]
    fn returns_default_signal_for_get_time_to_interactive_registry_context() {
        launch_only_scope(|scope| {
            let context: Rc<TimeToInteractiveRegistry> = get_time_to_interactive_registry_context(
                scope,
                PageType::Rust(RustPage::RUST_LIVE_TV),
            );

            assert!(Rc::strong_count(&context) > 0);
        });
    }

    #[test]
    fn returns_context_for_get_time_to_interactive_registry_context() {
        launch_only_scope(|scope| {
            let registry = Rc::new(TimeToInteractiveRegistry::new(
                scope,
                vec![],
                PageType::Rust(RustPage::RUST_LIVE_TV),
                None,
            ));

            provide_context(scope, registry.clone());

            let context: Rc<TimeToInteractiveRegistry> = get_time_to_interactive_registry_context(
                scope,
                PageType::Rust(RustPage::RUST_LIVE_TV),
            );

            assert!(Rc::strong_count(&context) > 0);
        });
    }

    #[test]
    fn returns_default_epg_context() {
        launch_only_scope(|scope| {
            let signal = create_signal(scope, 100);
            provide_context(
                scope,
                CurrentTimeContext {
                    current_time: signal.0,
                },
            );
            let context = get_epg_context(scope);

            assert_eq!(
                context.timeline_state.get_untracked(),
                EpgTimelineState {
                    start_time: 0,
                    end_time: 43200000,
                    max_scroll_index: 22,
                    horizontal_scroll_peek_ms: 1035000,
                    last_visible_timestamp: 45195000
                }
            );

            assert_eq!(
                context.scroll_state.get_untracked(),
                EpgScrollState {
                    vertical_scroll_index: 0,
                    timeblock_focus_index: 0,
                    timeblock_scroll_index: 0
                }
            );
            assert_eq!(context.focus_state.get_untracked(), (0, 0, false));
            assert_eq!(context.focus_state_id.get_untracked(), "".to_string());
            assert_eq!(context.focus_signal.get_untracked(), None);
        });
    }

    #[test]
    fn returns_provided_epg_context() {
        launch_only_scope(|scope| {
            let my_epg_context = EpgContext {
                timeline_state: create_memo(scope, |_| EpgTimelineState {
                    start_time: 100,
                    end_time: 200,
                    max_scroll_index: 2,
                    horizontal_scroll_peek_ms: 5,
                    last_visible_timestamp: 1000,
                })
                .into(),
                scroll_state: create_rw_signal(
                    scope,
                    EpgScrollState {
                        vertical_scroll_index: 1,
                        timeblock_focus_index: 2,
                        timeblock_scroll_index: 3,
                    },
                ),
                focus_state: create_rw_signal::<EpgFocusState>(scope, (1, 2, true)),
                focus_state_id: create_rw_signal(scope, String::from("tester")),
                focus_signal: create_focus_value_signal::<String>(scope),
                visible_row_range: create_memo(scope, |_| (0, 0)).into(),
                visible_time_range: create_memo(scope, |_| (0, 0)).into(),
            };

            provide_context(scope, my_epg_context);

            let context = get_epg_context(scope);

            assert_eq!(
                context.timeline_state.get_untracked(),
                EpgTimelineState {
                    start_time: 100,
                    end_time: 200,
                    max_scroll_index: 2,
                    horizontal_scroll_peek_ms: 5,
                    last_visible_timestamp: 1000
                }
            );

            assert_eq!(
                context.scroll_state.get_untracked(),
                EpgScrollState {
                    vertical_scroll_index: 1,
                    timeblock_focus_index: 2,
                    timeblock_scroll_index: 3
                }
            );
            assert_eq!(context.focus_state.get_untracked(), (1, 2, true));
            assert_eq!(context.focus_state_id.get_untracked(), "tester".to_string());
            assert_eq!(context.focus_signal.get_untracked(), None);
        });
    }

    #[test]
    fn returns_default_station_details_epg_context() {
        launch_only_scope(|scope| {
            let signal = create_signal(scope, 100);
            provide_context(
                scope,
                CurrentTimeContext {
                    current_time: signal.0,
                },
            );

            let context = get_station_details_epg_context(scope);

            assert_eq!(context.focus_index.get_untracked(), 0);

            assert_eq!(context.focus_state_id.get_untracked(), String::default());

            assert_eq!(context.timeblock_with_date.get_untracked(), None);

            assert_eq!(context.max_airing_end_time.get_untracked(), 691200100);
        });
    }

    #[test]
    fn returns_provided_station_details_epg_context() {
        launch_only_scope(|scope| {
            let my_epg_context = StationDetailsEpgContext {
                focus_index: create_rw_signal(scope, 2),
                focus_state_id: create_rw_signal(scope, String::from("tester")),
                timeblock_with_date: create_rw_signal(scope, None),
                max_airing_end_time: create_rw_signal(scope, 202),
                focus_signal: create_focus_value_signal::<String>(scope),
                manual_scroll: create_rw_signal(scope, scroll_to_index(0, false)),
            };

            provide_context(scope, my_epg_context);

            let context = get_station_details_epg_context(scope);

            assert_eq!(context.focus_index.get_untracked(), 2);
            assert_eq!(context.focus_state_id.get_untracked(), "tester".to_string());
            assert_eq!(context.timeblock_with_date.get_untracked(), None);
            assert_eq!(context.max_airing_end_time.get_untracked(), 202);
            assert_eq!(context.focus_signal.get_untracked(), None);
        })
    }
}
