use linear_common::constants::LIVE_TV_LAST_PLAYBACK_TITLE_PARAM;
use location::{Location, PageType, RustPage};
use router::RoutingContext;
use serde_json::Value;

pub fn is_seamless_egress(log_source: &str, location: &Location) -> bool {
    location
        .pageParams
        .get("seamlessEgress")
        .is_some_and(|value| match value {
            Value::String(v) => v == "true",
            Value::Bool(v) => *v,
            _ => {
                log::error!(
                    "[{}] Unexpected value for pageParam seamlessEgress: {}",
                    log_source,
                    value
                );
                false
            }
        })
}

pub fn get_initial_service_token(location: &Location) -> Option<String> {
    let token = location
        .pageParams
        .get("serviceToken")
        .and_then(|token| token.as_str())
        .map(|token| token.to_owned());

    token
}

pub fn get_last_playback_title_id(location: &Location) -> Option<String> {
    let token = location
        .pageParams
        .get(LIVE_TV_LAST_PLAYBACK_TITLE_PARAM)
        .and_then(|token| token.as_str())
        .map(|token| token.to_owned());

    token
}

pub fn update_last_playback_title_param(router: RoutingContext, title_id: Option<String>) {
    router.mutate_history(Box::new(move |history| {
        let index = history.len().saturating_sub(1);
        if let Some(last_item) = history.get_mut(index) {
            if last_item.pageType == PageType::Rust(RustPage::RUST_LIVE_TV) {
                if let Some(title_id) = title_id {
                    last_item.pageParams.insert(
                        LIVE_TV_LAST_PLAYBACK_TITLE_PARAM.to_string(),
                        Value::String(title_id),
                    );
                } else {
                    last_item
                        .pageParams
                        .remove(LIVE_TV_LAST_PLAYBACK_TITLE_PARAM);
                }
            }
        }
    }));
}

pub fn is_forward_navigation(log_source: &str, location: &Location) -> bool {
    !location.is_back(log_source) && !is_back_from_playback(location)
}

fn is_back_from_playback(location: &Location) -> bool {
    // If `seamlessEgress` is set, regardless of true of false, it is coming back from playback
    location.pageParams.get("seamlessEgress").is_some()
}

#[cfg(test)]
mod tests {
    use super::*;
    use location::Location;
    use router::{rust_location, MockRouting};
    use rstest::*;
    use std::rc::Rc;

    #[rstest]
    #[case::missing_value(rust_location!(RUST_LIVE_TV), false)]
    #[case::string_value(rust_location!(RUST_LIVE_TV, {"seamlessEgress" => "true"}), true)]
    #[case::bool_value(rust_location!(RUST_LIVE_TV, {"seamlessEgress" => true}), true)]
    #[case::invalid_value(rust_location!(RUST_LIVE_TV, {"seamlessEgress" => "invalid"}), false)]
    fn is_seamless_egress_returns_correct_value(
        #[case] location: Location,
        #[case] expected_value: bool,
    ) {
        assert_eq!(is_seamless_egress("TestPage", &location), expected_value);
    }

    #[rstest]
    #[case(rust_location!(RUST_LIVE_TV), None)]
    #[case(rust_location!(RUST_LIVE_TV, {"serviceToken" => "some_token"}), Some("some_token".to_string()))]
    fn get_initial_service_token_returns_correct_value(
        #[case] location: Location,
        #[case] expected_value: Option<String>,
    ) {
        assert_eq!(get_initial_service_token(&location), expected_value);
    }

    #[rstest]
    #[case(rust_location!(RUST_LIVE_TV), true)]
    #[case(rust_location!(RUST_LIVE_TV, {"back" => "true"}), false)]
    #[case(rust_location!(RUST_LIVE_TV, {"seamlessEgress" => "false"}), false)]
    #[case(rust_location!(RUST_LIVE_TV, {"seamlessEgress" => "true"}), false)]
    fn is_forward_navigation_returns_correct_value(
        #[case] location: Location,
        #[case] expected_value: bool,
    ) {
        assert_eq!(is_forward_navigation("TestPage", &location), expected_value);
    }

    #[rstest]
    #[case(rust_location!(RUST_LIVE_TV), None)]
    #[case(rust_location!(RUST_LIVE_TV, {"lastPlaybackTitleId" => "gti"}), Some("gti".to_string()))]
    fn get_last_playback_title_id_returns_correct_value(
        #[case] location: Location,
        #[case] expected_value: Option<String>,
    ) {
        assert_eq!(get_last_playback_title_id(&location), expected_value);
    }

    mod update_last_playback_title_param {
        use super::*;

        #[rstest]
        #[case(None, rust_location!(RUST_LIVE_TV))]
        #[case(Some("gti".to_string()), rust_location!(RUST_LIVE_TV, {"lastPlaybackTitleId" => "gti"}))]
        fn replaces_existing_page_params(
            #[case] title_id: Option<String>,
            #[case] expected_location: Location,
        ) {
            let mut mock_router = MockRouting::default();
            mock_router
                .expect_mutate_history()
                .returning(move |callback| {
                    let mut locations = vec![
                        rust_location!(RUST_COLLECTIONS),
                        rust_location!(RUST_LIVE_TV, { "lastPlaybackTitleId" => "old_gti" }),
                    ];

                    callback(&mut locations);

                    assert_eq!(
                        locations,
                        vec![rust_location!(RUST_COLLECTIONS), expected_location.clone()]
                    );
                });
            update_last_playback_title_param(Rc::new(mock_router), title_id);
        }

        #[test]
        fn does_not_update_non_live_tv_page() {
            let mut mock_router = MockRouting::default();
            mock_router
                .expect_mutate_history()
                .returning(move |callback| {
                    let mut locations = vec![
                        rust_location!(RUST_COLLECTIONS),
                        rust_location!(RUST_STATION_DETAILS),
                    ];

                    callback(&mut locations);

                    assert_eq!(
                        locations,
                        vec![
                            rust_location!(RUST_COLLECTIONS),
                            rust_location!(RUST_STATION_DETAILS)
                        ]
                    );
                });
            update_last_playback_title_param(Rc::new(mock_router), Some("gti".to_string()));
        }

        #[test]
        fn does_not_update_when_history_is_empty() {
            let mut mock_router = MockRouting::default();
            mock_router
                .expect_mutate_history()
                .returning(move |callback| {
                    let mut locations = vec![];

                    callback(&mut locations);

                    assert_eq!(locations, vec![]);
                });
            update_last_playback_title_param(Rc::new(mock_router), Some("gti".to_string()));
        }
    }
}
