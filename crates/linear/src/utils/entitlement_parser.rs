use common_transform_types::container_items::{EntitlementMessage, EntitlementMessageIcons};
use title_details::components::entitlement::*;

pub fn transform_entitlement_data(
    message: &Option<EntitlementMessage>,
) -> Option<EntitlementLabelData> {
    message.as_ref().map(|message| {
        let icon_type = message.icon.as_ref().and_then(|icon| match icon {
            EntitlementMessageIcons::ENTITLED_ICON => Some(EntitlementIconType::ENTITLED),
            EntitlementMessageIcons::OFFER_ICON => Some(EntitlementIconType::OFFER),
            // Ads icon in the entitlement label is removed since the Remaster redesign.
            EntitlementMessageIcons::ADS_ICON => None,
        });

        EntitlementLabelData {
            content: message.message.clone(),
            icon_type,
            size: EntitlementLabelSize::Small,
            max_lines: 1,
        }
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(
        EntitlementMessageIcons::ENTITLED_ICON,
        Some(EntitlementIconType::ENTITLED)
    )]
    #[case(EntitlementMessageIcons::OFFER_ICON, Some(EntitlementIconType::OFFER))]
    #[case(EntitlementMessageIcons::ADS_ICON, None)]
    fn entitlement_transformation_icon_is_correct(
        #[case] input_icon: EntitlementMessageIcons,
        #[case] expected_icon: Option<EntitlementIconType>,
    ) {
        let result = transform_entitlement_data(&Some(EntitlementMessage {
            message: Some("Included with Prime".to_string()),
            icon: Some(input_icon),
        }));

        assert_eq!(
            result.clone().unwrap().content,
            Some("Included with Prime".to_string())
        );
        assert_eq!(result.unwrap().icon_type, expected_icon);
    }

    #[test]
    fn entitlement_transformation_with_none_inputs_works() {
        let result = transform_entitlement_data(&Some(EntitlementMessage {
            message: None,
            icon: None,
        }));

        assert_eq!(result.clone().unwrap().content, None);
        assert_eq!(result.unwrap().icon_type, None);
    }
}
