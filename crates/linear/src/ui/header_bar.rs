use crate::constants::{
    HEADER_CURRENT_TIME_MARKER_CHEVRON_WIDTH, HEADER_CURRENT_TIME_MARKER_START_OFFSET,
    HEADER_CURRENT_TIME_MARKER_TRACK_WIDTH, HEADER_TIMELINE_HEIGHT, HEADER_TITLE_WIDTH,
};
use crate::ui::epg::epg_util::{calculate_time_marker_progress, round_down_to_epg_timeblock};
use crate::ui::time_header::current_time_marker::*;
use crate::ui::time_header::time_segments_list::*;
use crate::utils::context_utils::get_current_time;
use amzn_fable_tokens::FableColor;
use fableous::animations::fable_motion_linear_medium;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

use super::epg::constants::TIME_BLOCK_DURATION_MS;

fn get_timeline_segments(current_timestamp: i64) -> Vec<Segment> {
    let rounded_timestamp = round_down_to_epg_timeblock(current_timestamp);

    // add 16 segments, representing future half hour blocks
    let mut v = Vec::new();
    for n in 1..17 {
        let time = rounded_timestamp + (n * TIME_BLOCK_DURATION_MS);
        v.push(Segment::new(time, false));
    }
    v
}

fn get_time_marker_offset_and_alignment(progress: f32) -> (f32, CrossAxisAlignment) {
    // the pixel distance of start edge of chevron along the track
    let offset = HEADER_CURRENT_TIME_MARKER_START_OFFSET
        + progress * HEADER_CURRENT_TIME_MARKER_TRACK_WIDTH
        - (HEADER_CURRENT_TIME_MARKER_CHEVRON_WIDTH / 2.0);

    // (offset, trans_perc)
    match (progress * 100.0).floor() as u32 {
        u32::MIN..=9 => (offset, CrossAxisAlignment::Start),
        10..=87 => (offset, CrossAxisAlignment::Center),
        88..=u32::MAX => (offset, CrossAxisAlignment::End),
    }
}

#[Composer]
pub fn HeaderBar(
    ctx: &AppContext,
    #[into] title: MaybeSignal<String>,
    #[into] horizontal_scroll: MaybeSignal<ScrollTo>,
    #[into] show_timeline: MaybeSignal<bool>,
    #[into] width: MaybeSignal<f32>,
) -> RowComposable {
    let frame_width = Signal::derive(ctx.scope(), move || width.get() - HEADER_TITLE_WIDTH);
    let current_timestamp = get_current_time(ctx.scope());

    let current_time_marker_offset_and_alignment = Signal::derive(ctx.scope(), move || {
        get_time_marker_offset_and_alignment(calculate_time_marker_progress(
            current_timestamp.get(),
        ))
    });

    let segments = create_memo(ctx.scope(), move |_| {
        get_timeline_segments(current_timestamp.get())
    });

    // TODO: Use scroll_to which requires time marker to be in RowList and handle update/remove segments
    let (translate_x, set_translate_x) = create_signal(ctx.scope(), 0.0);
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            ctx.with_animation(fable_motion_linear_medium(), || {
                set_translate_x.set(match horizontal_scroll.get() {
                    ScrollTo::Offset(offset, _, _) => offset,
                    ScrollTo::Index(_, _, _) => 0.0,
                });
            });
        }
    });

    let title_width = Signal::derive(ctx.scope(), move || {
        if show_timeline.get() {
            HEADER_TITLE_WIDTH
        } else {
            width.get()
        }
    });

    compose! {
        Row() {
            TypographyHeading100(
                content: title,
                color: get_ignx_color(FableColor::PRIMARY)
            )
            .max_lines(Some(1))
            .width(title_width)
            .z_index(1)
            .test_id("HeaderBar::Title")
            if show_timeline.get() {
                Row() { // overflow hiding frame
                    Row() { // container for current marker and time line segments, scrolls with horizontal focus
                        Row() {
                            CurrentTimeMarker(
                                current_timestamp,
                                current_time_marker_offset_and_alignment,
                            )
                        }
                        .width(HEADER_CURRENT_TIME_MARKER_START_OFFSET + HEADER_CURRENT_TIME_MARKER_TRACK_WIDTH)
                        TimeSegmentList(segments, segment_color: FableColor::PRIMARY)
                    }
                    .translate_x(translate_x)
                }
                .overflow_behavior(OverflowBehavior::Hidden)
                .width(frame_width)
            }
        }
        .width(width)
        .height(HEADER_TIMELINE_HEIGHT)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::constants::LIVE_PAGE_EPG_WIDTH;
    use current_time::CurrentTimeContext;
    use fableous::animations::fable_motion_linear_medium;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_does_not_exist, assert_node_exists},
    };

    #[test]
    fn header_renders_without_timeline() {
        launch_test(
            |ctx| {
                let (current_time, _) = create_signal(ctx.scope(), 1709760330000); // March 6, 2024 9:25:30 PM UTC
                provide_context(ctx.scope(), CurrentTimeContext { current_time });
                let (show_timeline, _) = create_signal(ctx.scope(), false);

                compose! {
                    HeaderBar(
                        show_timeline,
                        title: "Entitlement Title",
                        horizontal_scroll: ScrollTo::Offset(0.0, Pivot::Start, fable_motion_linear_medium()),
                        width: LIVE_PAGE_EPG_WIDTH
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let entitlement_title = tree
                    .find_by_props()
                    .test_id("HeaderBar::Title")
                    .find_first();
                assert_eq!(
                    Some("Entitlement Title".to_string()),
                    entitlement_title.borrow_props().text
                );
                assert_eq!(
                    LIVE_PAGE_EPG_WIDTH,
                    entitlement_title.borrow_props().layout.size.width
                );

                let current_time_marker = tree
                    .find_by_props()
                    .test_id("HeaderBar::CurrentTimeMarker")
                    .find_first();
                assert_node_does_not_exist!(current_time_marker);
            },
        )
    }

    #[test]
    fn header_renders_with_timeline() {
        launch_test(
            |ctx| {
                let (current_time, _) = create_signal(ctx.scope(), 1709760330000); // March 6, 2024 9:25:30 PM UTC
                provide_context(ctx.scope(), CurrentTimeContext { current_time });
                let (show_timeline, _) = create_signal(ctx.scope(), true);

                compose! {
                    HeaderBar(
                        show_timeline,
                        title: "Entitlement Title",
                        horizontal_scroll: ScrollTo::Offset(0.0, Pivot::Start, fable_motion_linear_medium()),
                        width: LIVE_PAGE_EPG_WIDTH
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let entitlement_title = tree
                    .find_by_props()
                    .test_id("HeaderBar::Title")
                    .find_first();
                assert_eq!(
                    Some("Entitlement Title".to_string()),
                    entitlement_title.borrow_props().text
                );
                assert_eq!(
                    HEADER_TITLE_WIDTH,
                    entitlement_title.borrow_props().layout.size.width
                );

                let current_time_marker = tree
                    .find_by_props()
                    .test_id("HeaderBar::CurrentTimeMarker")
                    .find_first();
                assert_node_exists!(current_time_marker);
            },
        )
    }
}
