use amzn_fable_tokens::*;
use fableous::animations::fable_motion_persistent_medium;
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

use crate::constants::{
    STANDARD_CAROUSEL_FOCUS_RING_BORDER_RADIUS, STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH,
    STANDARD_CAROUSEL_FOCUS_RING_HEIGHT, STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET,
    STANDARD_CAROUSEL_FOCUS_RING_VERTICAL_OFFSET, STANDARD_CAROUSEL_FOCUS_RING_WIDTH,
};

#[Composer]
pub fn FocusRing(
    ctx: &AppContext,
    #[into] x_offset: MaybeSignal<f32>,
) -> impl VisualComposable<'static> {
    let translate_x = create_rw_signal(
        ctx.scope(),
        x_offset.get() + STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET,
    );
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let new_x = x_offset.get() + STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET;
            ctx.with_animation(fable_motion_persistent_medium(), move || {
                translate_x.set(new_x);
            });
        }
    });
    compose! {
        Rectangle()
            .width(STANDARD_CAROUSEL_FOCUS_RING_WIDTH)
            .height(STANDARD_CAROUSEL_FOCUS_RING_HEIGHT)
            .border_color(get_ignx_color(FableColor::PRIMARY))
            .border_width(STANDARD_CAROUSEL_FOCUS_RING_BORDER_WIDTH)
            .border_radius(STANDARD_CAROUSEL_FOCUS_RING_BORDER_RADIUS)
            .translate_x(translate_x)
            .translate_y(STANDARD_CAROUSEL_FOCUS_RING_VERTICAL_OFFSET)
            .test_id("standard_carousel_focus_ring")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;

    #[test]
    fn add_offset_to_translate_x() {
        launch_test(
            move |ctx| {
                let x_offset = create_rw_signal(ctx.scope(), 0.0);
                provide_context(ctx.scope(), x_offset);

                compose! {
                    FocusRing(x_offset)
                }
            },
            |scope, mut test_loop| {
                let x_offset = expect_context::<RwSignal<f32>>(scope);

                let tree = test_loop.tick_until_done();
                let focus_ring_x = tree
                    .find_by_test_id("standard_carousel_focus_ring")
                    .borrow_props()
                    .layout
                    .border_box
                    .left;
                assert_eq!(focus_ring_x, STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET);

                x_offset.set(100.0);
                let tree = test_loop.tick_until_done();
                let focus_ring_x = tree
                    .find_by_test_id("standard_carousel_focus_ring")
                    .borrow_props()
                    .layout
                    .border_box
                    .left;
                assert_eq!(
                    focus_ring_x,
                    STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET + 100.0
                );
            },
        )
    }
}
