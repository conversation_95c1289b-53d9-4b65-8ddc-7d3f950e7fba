use crate::{
    constants::{
        STANDARD_CAROUSEL_CARD_BORDER_RADIUS, STANDARD_CAROUSEL_CARD_HEIGHT,
        STANDARD_CAROUSEL_CARD_WIDTH,
    },
    metrics::csm_reporter::ViewSize,
    types::{Liveliness, TitleCard},
    ui::standard_carousel::standard_carousel_utils::{
        title_card_data_to_badge_data, title_card_data_to_text_content, BadgeData,
    },
    utils::{
        common_string_ids::{
            AV_LRC_LIVE_TV_TTS_ITEM_INDEX, AV_LRC_LIVE_TV_TTS_SELECT_FOR_MORE_INFORMATION,
            AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_FIRST_ITEM,
            AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_SUBSEQUENT_ITEM,
        },
        context_utils::get_time_to_interactive_registry_context,
        tti_utils::TTI_PART_STANDARD_CAROUSEL_CARD,
    },
};
use amzn_fable_tokens::FableColor;
use common_transform_types::container_items::LiveEventDateTime;
use cross_app_events::{IMPRESSION_DURATION, IMPRESSION_STRIPE};
use fableous::{badges::label_badge::*, typography::typography::*, utils::get_ignx_color};
use firetv::use_firetv_context;
use ignx_compositron::input::KeyCode;
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{
    image::ImageLoadResultEvent,
    impression::ViewImpressionData,
    prelude::*,
    text::{LocalizedText, SubstitutionParameters, TextContent},
};
use location::{PageType, RustPage};
use lrc_image::{
    lrc_image::*,
    types::{ImageData, ImageFormat, ImageTag, ScalingStrategy},
};
use router::hooks::try_use_location;
use std::{collections::HashMap, rc::Rc};
use title_details::components::entitlement::*;

const PROGRESS_BAR_WIDTH: f32 = 360.0;
const SPACING: f32 = 12.0;
const GRADIENT_HEIGHT: f32 = 115.0;
const CONTENT_HEIGHT: f32 = 45.0;
const CONTENT_OFFSET: f32 = 159.0;
const BADGE_OFFSET: f32 = 9.0;
const CARD_SIZE: ViewSize = ViewSize {
    width: STANDARD_CAROUSEL_CARD_WIDTH,
    height: STANDARD_CAROUSEL_CARD_HEIGHT,
};

#[Composer]
pub fn StandardCarouselCard(
    ctx: &AppContext,
    data: TitleCard,
    on_select: Rc<dyn Fn(&TitleCard, ViewSize)>,
    on_highlight: Rc<dyn Fn(&TitleCard, ViewSize)>,
    on_view: Rc<dyn Fn(&TitleCard, ViewSize, ViewImpressionData)>,
    row_index: usize,
    item_index: usize,
    total_items: MaybeSignal<usize>,
) -> StackComposable {
    let data = Rc::new(data);
    let badge_data: Option<BadgeData> = title_card_data_to_badge_data(&data);
    let page = match try_use_location(ctx.scope()) {
        Some(location) => location.get().pageType,
        None => PageType::Rust(RustPage::RUST_LIVE_TV),
    };

    let entitlement_icon_data = DataSignal::<EntitlementIconDataSignal>::from_data(
        ctx.scope(),
        EntitlementIconData {
            icon_type: EntitlementIconType::OFFER,
            size: EntitlementLabelSize::Small,
        },
    );
    let image_url = data.cover_image.clone();
    let data_highlight = Rc::clone(&data);
    let data_view = Rc::clone(&data);
    let is_entitled = data.is_entitled;
    let is_live = matches!(data.liveliness, Some(Liveliness::LIVE));
    let has_text_content = !is_live
        && matches!(
            &data.live_event_date_badge,
            Some(LiveEventDateTime::LOCALIZED_BADGE(_))
        );
    let text_content = TextContent::String(
        if let Some(LiveEventDateTime::LOCALIZED_BADGE(badge)) = &data.live_event_date_badge {
            badge.text.clone()
        } else {
            "".to_string()
        },
    );
    let show_bottom_gradient = !is_live && has_text_content;
    let show_corner_gradient = !show_bottom_gradient && !is_entitled;

    let accessibility_hints = Signal::derive(ctx.scope(), move || {
        get_accessibility_hints(item_index, total_items.get())
    });
    let tts_data = data.clone();
    let accessibility_descriptions = Signal::derive(ctx.scope(), move || {
        get_accessibility_descriptions(&tts_data)
    });

    let wrapped_on_select = move || on_select(&data, CARD_SIZE);
    let firetv_context = use_firetv_context(ctx);
    let on_menu_key_down = {
        let wrapped_on_select = wrapped_on_select.clone();
        move || {
            if firetv_context.is_firetv() {
                wrapped_on_select();
            }
        }
    };
    let registry = get_time_to_interactive_registry_context(ctx.scope(), page);

    compose! {
        Stack() {
            // Card image
            Memo(item_builder: Box::new(move |ctx| {
                if let Some(url) = &image_url {
                    compose_option! {
                        LRCImage(data: ImageData {
                            url: url.clone(),
                            width: STANDARD_CAROUSEL_CARD_WIDTH,
                            height: STANDARD_CAROUSEL_CARD_HEIGHT,
                            tags: vec![]
                        })
                        .border_radius(STANDARD_CAROUSEL_CARD_BORDER_RADIUS)
                        .test_id("card_image")
                        .on_image_result({
                            let registry = Rc::clone(&registry);
                            move |res: &ImageLoadResultEvent| {
                            if matches!(res, ImageLoadResultEvent::Success) {
                                registry.set_part_ready(format!("{TTI_PART_STANDARD_CAROUSEL_CARD}_{row_index}_{item_index}"))
                            }
                        } })
                    }
                } else {
                    None
                }
            }));

            if show_bottom_gradient {
                LRCImage(data: ImageData {
                    url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/standard-card-bottom-gradient.png".to_string(),
                    width: STANDARD_CAROUSEL_CARD_WIDTH,
                    height: GRADIENT_HEIGHT,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::ScaleToHeight),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .border_radius(STANDARD_CAROUSEL_CARD_BORDER_RADIUS)
                .translate_y(STANDARD_CAROUSEL_CARD_HEIGHT - GRADIENT_HEIGHT)
                .test_id("bottom_gradient")
            }

            if show_corner_gradient {
                LRCImage(data: ImageData {
                    url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/standard-card-corner-gradient.png".to_string(),
                    width: STANDARD_CAROUSEL_CARD_WIDTH,
                    height: GRADIENT_HEIGHT,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::ScaleToHeight),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .border_radius(STANDARD_CAROUSEL_CARD_BORDER_RADIUS)
                .translate_y(STANDARD_CAROUSEL_CARD_HEIGHT - GRADIENT_HEIGHT)
                .test_id("corner_gradient")
            }

            Column() {
                Row() {
                    if !is_entitled {
                        EntitlementIcon(data: entitlement_icon_data)
                        .test_id("entitlement_icon")
                    }

                    if has_text_content {
                        TypographyUtility100(content: text_content.clone())
                        .test_id("text_content")
                    }
                }
                .main_axis_alignment(MainAxisAlignment::SpacedBy(SPACING))
                .cross_axis_alignment(CrossAxisAlignment::End)
            }
            .width(PROGRESS_BAR_WIDTH)
            .height(CONTENT_HEIGHT)
            .main_axis_alignment(MainAxisAlignment::End)
            .translate_y(CONTENT_OFFSET)
            .translate_x(SPACING)

            Memo(item_builder: Box::new(move |ctx| {
                if let Some(badge_data) = badge_data.clone() {
                    compose_option! {
                        // Row used to position localized badges of unknown width
                        Row() {
                            LabelBadge(
                                text: badge_data.text,
                                size: LabelBadgeSize::SIZE400,
                                color_scheme: badge_data.color_scheme
                            )
                            .translate_x(-BADGE_OFFSET)
                            .translate_y(BADGE_OFFSET)
                            .test_id("badge")
                        }
                        .width(STANDARD_CAROUSEL_CARD_WIDTH)
                        .main_axis_alignment(MainAxisAlignment::End)
                    }
                } else {
                    None
                }
            }))
        }
        .width(STANDARD_CAROUSEL_CARD_WIDTH)
        .height(STANDARD_CAROUSEL_CARD_HEIGHT)
        .border_radius(STANDARD_CAROUSEL_CARD_BORDER_RADIUS)
        .background_color(get_ignx_color(FableColor::COOL800))
        .focusable()
        .on_select(wrapped_on_select.clone())
        .on_long_press_start(KeyCode::Enter, wrapped_on_select.clone())
        .on_long_press_start(KeyCode::Select, wrapped_on_select)
        .on_key_down(KeyCode::Menu, on_menu_key_down)
        .on_highlight_impression(IMPRESSION_DURATION, move || on_highlight(data_highlight.as_ref(), CARD_SIZE))
        .on_view_impression(IMPRESSION_DURATION, IMPRESSION_STRIPE, move |view_data| on_view(data_view.as_ref(), CARD_SIZE, view_data))
        .accessibility_metadata_descriptions(accessibility_descriptions)
        .accessibility_hints(accessibility_hints)
    }
}

fn get_accessibility_descriptions(tts_data: &TitleCard) -> Vec<TextContent> {
    let mut messages = vec![];

    // Airing title
    if let Some(title) = &tts_data.title {
        messages.push(TextContent::String(title.clone()));
    }

    // Liveliness badge
    if let Some(liveliness) = title_card_data_to_text_content(tts_data) {
        messages.push(liveliness);
    }

    let is_live = matches!(tts_data.liveliness, Some(Liveliness::LIVE));
    if !is_live {
        // Live at...
        if let Some(LiveEventDateTime::LOCALIZED_BADGE(badge)) = &tts_data.live_event_date_badge {
            messages.push(TextContent::String(badge.text.clone()));
        }
    }

    if !tts_data.is_entitled {
        // Entitlement message
        if let Some(entitlement) = &tts_data.entitlement_message {
            if let Some(message) = &entitlement.message {
                messages.push(TextContent::String(message.to_owned()));
            }
        }
    } else {
        // Synopsis
        if let Some(synopsis) = &tts_data.synopsis {
            messages.push(TextContent::String(synopsis.clone()));
        }
    }

    // maturity rating
    if let Some(rating) = &tts_data.maturity_rating {
        messages.push(TextContent::String(rating.clone()));
    }
    // date & time
    if let Some(LiveEventDateTime::LOCALIZED_HEADER(localized_header)) =
        &tts_data.live_event_date_header
    {
        if let Some(date) = &localized_header.date {
            messages.push(TextContent::String(date.clone()));
        }
        if let Some(time) = &localized_header.time {
            messages.push(TextContent::String(time.clone()));
        }
    }
    // venue
    if let Some(venue) = &tts_data.venue {
        messages.push(TextContent::String(venue.clone()));
    }

    // Select for more information
    messages.push(TextContent::LocalizedText(LocalizedText::new(
        AV_LRC_LIVE_TV_TTS_SELECT_FOR_MORE_INFORMATION,
    )));

    messages
}

fn get_accessibility_hints(index: usize, total_items: usize) -> Vec<TextContent> {
    let position_message = TextContent::LocalizedText(LocalizedText {
        string_id: AV_LRC_LIVE_TV_TTS_ITEM_INDEX.into(),
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([
            (
                "currentIndex".to_owned(),
                TextContent::String((index + 1).to_string()),
            ),
            (
                "totalNumber".to_owned(),
                TextContent::String(total_items.to_string()),
            ),
        ]))),
    });

    let nav_message = TextContent::LocalizedText(LocalizedText::new(if index == 0 {
        AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_FIRST_ITEM
    } else {
        AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_SUBSEQUENT_ITEM
    }));

    vec![position_message, nav_message]
}
#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data_util::mock_title_card;
    use crate::types::Liveliness;
    use crate::ui::epg::constants::HOUR_IN_MS;
    use common_transform_types::container_items::{EntitlementMessage, EntitlementMessageIcons};
    use cross_app_events::tti_registry::TimeToInteractiveRegistry;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::test_utils::*;
    use ignx_compositron::text::{LocalizedText, SubstitutionParameters};
    use location::{PageType, RustPage};
    use rstest::*;

    mod tts {
        use super::*;
        use common_transform_types::container_items::{
            TitleMetadataBadge, TitleMetadataBadgeLevel,
        };
        use fableous::tiles::common_string_ids::AIV_BLAST_UPCOMING_BADGE_TEXT;

        #[test]
        fn accessibility_descriptions_for_live_card() {
            let time_range = (1000, 1000 + HOUR_IN_MS);
            let messages = get_accessibility_descriptions(&mock_title_card("card_id", time_range));

            assert_eq!(
                messages,
                vec![
                    TextContent::String("Title card_id".to_string()),
                    TextContent::String("LIVE".to_string()),
                    TextContent::String("Synopsis text".to_string()),
                    TextContent::String("18+".to_string()),
                    TextContent::String("March 6, 2024".to_string()),
                    TextContent::String("9:25 PM".to_string()),
                    TextContent::String("Venue text".to_string()),
                    TextContent::LocalizedText(LocalizedText::new(
                        AV_LRC_LIVE_TV_TTS_SELECT_FOR_MORE_INFORMATION,
                    )),
                ],
            );
        }

        #[test]
        fn accessibility_descriptions_for_upcoming_card() {
            let time_range = (1000, 1000 + HOUR_IN_MS);
            let mut title_card = mock_title_card("card_id", time_range);
            // If we have both liveliness data and title metadata badge data we consider the later one
            title_card.liveliness = Some(Liveliness::UPCOMING);
            title_card.title_metadata_badge = Some(TitleMetadataBadge {
                message: Some(String::from("UPCOMING")),
                level: Some(TitleMetadataBadgeLevel::INFO_INACTIVE),
            });
            let messages = get_accessibility_descriptions(&title_card);

            assert_eq!(
                messages,
                vec![
                    TextContent::String("Title card_id".to_string()),
                    TextContent::String("UPCOMING".to_string()),
                    TextContent::String("Live at 9:25 PM EDT".to_string()),
                    TextContent::String("Synopsis text".to_string()),
                    TextContent::String("18+".to_string()),
                    TextContent::String("March 6, 2024".to_string()),
                    TextContent::String("9:25 PM".to_string()),
                    TextContent::String("Venue text".to_string()),
                    TextContent::LocalizedText(LocalizedText::new(
                        AV_LRC_LIVE_TV_TTS_SELECT_FOR_MORE_INFORMATION,
                    )),
                ],
            );
        }

        #[test]
        fn accessibility_descriptions_for_unentitled_card() {
            let time_range = (1000, 1000 + HOUR_IN_MS);
            let mut tts_data = mock_title_card("card_id", time_range);
            tts_data.is_entitled = false;
            tts_data.entitlement_message = Some(EntitlementMessage {
                message: Some(String::from("Watch with Max")),
                icon: Some(EntitlementMessageIcons::OFFER_ICON),
            });
            // If we have liveliness data and no title metadata badge data we consider the former one
            tts_data.title_metadata_badge = None;
            tts_data.liveliness = Some(Liveliness::UPCOMING);

            let messages = get_accessibility_descriptions(&tts_data);

            assert_eq!(
                messages,
                vec![
                    TextContent::String("Title card_id".to_string()),
                    TextContent::LocalizedText(LocalizedText::new(AIV_BLAST_UPCOMING_BADGE_TEXT)),
                    TextContent::String("Live at 9:25 PM EDT".to_string()),
                    TextContent::String("Watch with Max".to_string()),
                    TextContent::String("18+".to_string()),
                    TextContent::String("March 6, 2024".to_string()),
                    TextContent::String("9:25 PM".to_string()),
                    TextContent::String("Venue text".to_string()),
                    TextContent::LocalizedText(LocalizedText::new(
                        AV_LRC_LIVE_TV_TTS_SELECT_FOR_MORE_INFORMATION,
                    )),
                ],
            );
        }

        #[test]
        fn accessibility_hints_for_first_card() {
            let index = 0;
            let total_items = 10;

            assert_eq!(
                get_accessibility_hints(index, total_items),
                vec![
                    TextContent::LocalizedText(LocalizedText {
                        string_id: AV_LRC_LIVE_TV_TTS_ITEM_INDEX.into(),
                        substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                            (
                                "currentIndex".to_owned(),
                                TextContent::String("1".to_string())
                            ),
                            (
                                "totalNumber".to_owned(),
                                TextContent::String("10".to_string())
                            ),
                        ]))),
                    }),
                    TextContent::LocalizedText(LocalizedText::new(
                        AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_FIRST_ITEM
                    )),
                ],
            );
        }

        #[test]
        fn accessibility_hints_for_subsequent_card() {
            let index = 3;
            let total_items = 10;

            assert_eq!(
                get_accessibility_hints(index, total_items),
                vec![
                    TextContent::LocalizedText(LocalizedText {
                        string_id: AV_LRC_LIVE_TV_TTS_ITEM_INDEX.into(),
                        substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                            (
                                "currentIndex".to_owned(),
                                TextContent::String("4".to_string())
                            ),
                            (
                                "totalNumber".to_owned(),
                                TextContent::String("10".to_string())
                            ),
                        ]))),
                    }),
                    TextContent::LocalizedText(LocalizedText::new(
                        AV_LRC_LIVE_TV_TTS_STANDARD_CAROUSEL_HINT_SUBSEQUENT_ITEM
                    )),
                ],
            );
        }
    }

    #[rstest]
    #[case::entitled(true)]
    #[case::unentitled(false)]
    fn live_card_renders_expected_elements(#[case] is_entitled: bool) {
        launch_test(
            move |ctx| {
                setup_contexts(&ctx, false);
                let mut data = mock_title_card("card_id", (500, 1500));
                data.is_entitled = is_entitled;
                compose! {
                    StandardCarouselCard(
                        data,
                        on_select: Rc::new(|_, _| {}),
                        on_highlight: Rc::new(|_, _| {}),
                        on_view: Rc::new(|_, _, _| {}),
                        row_index: 0,
                        item_index: 0,
                        total_items: 1.into()
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Image
                assert_node_exists!(tree.find_by_test_id("card_image"));
                // Live badge
                assert_node_exists!(tree.find_by_test_id("badge"));
                // Entitlement Icon & Gradient
                assert_node_does_not_exist!(tree.find_by_test_id("bottom_gradient"));
                if is_entitled {
                    assert_node_does_not_exist!(tree.find_by_test_id("corner_gradient"));
                    assert_node_does_not_exist!(tree.find_by_test_id("entitlement_icon"));
                } else {
                    assert_node_exists!(tree.find_by_test_id("corner_gradient"));
                    assert_node_exists!(tree.find_by_test_id("entitlement_icon"));
                }
            },
        )
    }

    #[rstest]
    #[case::entitled_with_date(true, true)]
    #[case::entitled_with_no_date(true, false)]
    #[case::unentitled_with_date(false, true)]
    #[case::unentitled_with_no_date(false, false)]
    fn upcoming_card_renders_expected_elements(#[case] is_entitled: bool, #[case] has_date: bool) {
        let mut data = mock_title_card("card_id", (2000, 3000));
        data.liveliness = Some(Liveliness::UPCOMING);
        data.is_entitled = is_entitled;
        if !has_date {
            data.live_event_date_badge = None;
        }

        launch_test(
            move |ctx| {
                setup_contexts(&ctx, false);
                compose! {
                    StandardCarouselCard(
                        data,
                        on_select: Rc::new(|_, _| {}),
                        on_highlight: Rc::new(|_, _| {}),
                        on_view: Rc::new(|_, _, _| {}),
                        row_index: 0,
                        item_index: 0,
                        total_items: 1.into()
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Image
                assert_node_exists!(tree.find_by_test_id("card_image"));

                if has_date {
                    // Gradient
                    assert_node_does_not_exist!(tree.find_by_test_id("corner_gradient"));
                    assert_node_exists!(tree.find_by_test_id("bottom_gradient"));

                    // Live at...
                    assert_node_exists!(tree.find_by_test_id("text_content"));
                } else {
                    // Gradient
                    if is_entitled {
                        // entitled with no text -> no bottom content -> no gradients
                        assert_node_does_not_exist!(tree.find_by_test_id("bottom_gradient"));
                        assert_node_does_not_exist!(tree.find_by_test_id("corner_gradient"));
                    } else {
                        // unentitled with no text -> offer icon only -> corner gradient
                        assert_node_does_not_exist!(tree.find_by_test_id("bottom_gradient"));
                        assert_node_exists!(tree.find_by_test_id("corner_gradient"));
                    }

                    // No text content
                    assert_node_does_not_exist!(tree.find_by_test_id("text_content"));
                }

                // Liveliness badge
                assert_node_exists!(tree.find_by_test_id("badge"));

                // Entitlement Icon
                if is_entitled {
                    assert_node_does_not_exist!(tree.find_by_test_id("entitlement_icon"));
                } else {
                    assert_node_exists!(tree.find_by_test_id("entitlement_icon"));
                }
            },
        )
    }

    #[rstest]
    #[case(KeyCode::Enter)]
    #[case(KeyCode::Select)]
    fn long_press_calls_on_select(#[case] key_code: KeyCode) {
        launch_test(
            |ctx| {
                setup_contexts(&ctx, false);
                let data = mock_title_card("card_id", (500, 1500));

                let on_select_called = create_rw_signal(ctx.scope(), None);
                let on_select = Rc::new({
                    move |title_card: &TitleCard, _view_size: ViewSize| {
                        on_select_called.set(Some((title_card.id.clone())));
                    }
                });

                provide_context(ctx.scope(), on_select_called);

                compose! {
                    StandardCarouselCard(
                        data,
                        on_select,
                        on_highlight: Rc::new(|_, _| {}),
                        on_view: Rc::new(|_, _, _| {}),
                        row_index: 0,
                        item_index: 0,
                        total_items: 1.into()
                    )
                }
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let card_stack = tree
                    .find_by_props()
                    .composable_type(ComposableType::Stack)
                    .is_focusable()
                    .find_first();
                assert_node_exists!(&card_stack);

                let node_id = card_stack.borrow_props().node_id;

                // Send long press event
                test_loop.send_long_key_press_event_to_node(node_id, key_code);
                test_loop.tick_until_done();

                let on_select_called = use_context::<RwSignal<Option<(String)>>>(scope)
                    .expect("Expected to find on_select_called context");

                let call_result = on_select_called
                    .get_untracked()
                    .expect("Expected on_select to have been called");

                assert_eq!(call_result, "card_id".to_string());
            },
        )
    }

    #[test]
    fn menu_press_calls_on_select_on_firetv() {
        launch_test(
            |ctx| {
                setup_contexts(&ctx, true);
                let data = mock_title_card("card_id", (500, 1500));

                let on_select_called = create_rw_signal(ctx.scope(), None);
                let on_select = Rc::new({
                    move |title_card: &TitleCard, _view_size: ViewSize| {
                        on_select_called.set(Some((title_card.id.clone())));
                    }
                });

                provide_context(ctx.scope(), on_select_called);

                compose! {
                    StandardCarouselCard(
                        data,
                        on_select,
                        on_highlight: Rc::new(|_, _| {}),
                        on_view: Rc::new(|_, _, _| {}),
                        row_index: 0,
                        item_index: 0,
                        total_items: 1.into()
                    )
                }
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let card_stack = tree
                    .find_by_props()
                    .composable_type(ComposableType::Stack)
                    .is_focusable()
                    .find_first();
                assert_node_exists!(&card_stack);

                let node_id = card_stack.borrow_props().node_id;

                // Send key press event
                test_loop.send_key_down_up_event_to_node(node_id, KeyCode::Menu);
                test_loop.tick_until_done();

                let on_select_called = use_context::<RwSignal<Option<(String)>>>(scope)
                    .expect("Expected to find on_select_called context");

                let call_result = on_select_called
                    .get_untracked()
                    .expect("Expected on_select to have been called");

                assert_eq!(call_result, "card_id".to_string());
            },
        )
    }

    #[test]
    fn menu_press_does_not_call_on_select_if_not_firetv() {
        launch_test(
            |ctx| {
                setup_contexts(&ctx, false);
                let data = mock_title_card("card_id", (500, 1500));

                let on_select_called = create_rw_signal(ctx.scope(), None);
                let on_select = Rc::new({
                    move |title_card: &TitleCard, _view_size: ViewSize| {
                        on_select_called.set(Some((title_card.id.clone())));
                    }
                });

                provide_context(ctx.scope(), on_select_called);

                compose! {
                    StandardCarouselCard(
                        data,
                        on_select,
                        on_highlight: Rc::new(|_, _| {}),
                        on_view: Rc::new(|_, _, _| {}),
                        row_index: 0,
                        item_index: 0,
                        total_items: 1.into()
                    )
                }
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let card_stack = tree
                    .find_by_props()
                    .composable_type(ComposableType::Stack)
                    .is_focusable()
                    .find_first();
                assert_node_exists!(&card_stack);

                let node_id = card_stack.borrow_props().node_id;

                // Send key press event
                test_loop.send_key_down_up_event_to_node(node_id, KeyCode::Menu);
                test_loop.tick_until_done();

                let on_select_called = use_context::<RwSignal<Option<(String)>>>(scope)
                    .expect("Expected to find on_select_called context");

                assert!(on_select_called.get_untracked().is_none());
            },
        )
    }

    fn provide_firetv_context(ctx: &AppContext, is_firetv: bool) {
        let mut firetv = MockFireTV::default();
        firetv.expect_is_firetv().return_const(is_firetv);
        firetv.provide_mock(ctx.scope());
    }

    fn setup_contexts(
        ctx: &AppContext, // Menu button exists on fire tv remote and opens/closes the contextual menu
        has_menu_button: bool,
    ) {
        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            ctx.scope(),
            vec![],
            PageType::Rust(RustPage::RUST_LIVE_TV),
            None,
        ));
        provide_context(ctx.scope(), tti_registry);
        provide_firetv_context(ctx, has_menu_button);
    }
}
