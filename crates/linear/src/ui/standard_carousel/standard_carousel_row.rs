use crate::constants::{
    STANDARD_CAROUSEL_CARD_AND_DETAILS_SPACING, STANDARD_CAROUSEL_CARD_SPACING,
    STANDARD_CAROUSEL_CARD_WIDTH, STANDARD_CAROUSEL_MINI_DETAILS_HEIGHT,
    STANDARD_CAROUSEL_ROW_PADDING,
};
use crate::live_page::data_provider::network_data_provider::paginate_container;
use crate::metrics::csm_reporter::{self, ImpressionDataType, ViewSize};
use crate::network::parser::parse_standard_carousel_item;
use crate::network::types::PaginationLink;
#[double]
use crate::network::NetworkClient;
use crate::types::{EpgContext, StandardCarouselModelReactive, TitleCard, TransitionSource};
use crate::ui::standard_carousel::{focus_ring::*, mini_details::*, standard_carousel_card::*};
use crate::utils::common_string_ids::AV_LRC_LIVE_TV_NAVIGATION_ROW_END;
use crate::utils::context_utils::get_tts_enabled;
use crate::utils::pagination_tracker::PaginationTracker;
use crate::utils::watch_modal::create_watch_modal_data;
use common_transform_types::actions::TransitionAction;
use container_types::ui_signals::CarouselIndexData;
use containers::basic_carousel::*;
use containers::utils::list_fvs::create_list_focus_value_signal;
use contextual_menu_types::prelude::*;
use fableous::animations::fable_motion_persistent_standard;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::text::{LocalizedText, TextContent};
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{focus::FocusEdges, input::KeyCode, prelude::*};
use mockall_double::double;
use std::rc::Rc;
use watch_modal::data_provider::should_show_watch_modal;

const DEFAULT_END_REACHED_BUFFER: usize = 4;

fn paginate(
    network_client: Rc<NetworkClient>,
    pagination_link_signal: RwSignal<Option<PaginationLink>>,
    items_signal: RwSignal<Vec<TitleCard>>,
    pagination_tracker: Rc<PaginationTracker>,
    row_id: String,
) {
    if let Some(pagination_link) = pagination_link_signal.get_untracked() {
        if let Some(release) = pagination_tracker.try_lock(row_id.clone()) {
            let start_index = pagination_link.startIndex.max(0) as usize;
            paginate_container(
                network_client,
                pagination_link,
                move |new_items, new_pagination_link| {
                    items_signal.try_update(|items| {
                        items.extend(parse_standard_carousel_item(
                            new_items,
                            &row_id,
                            start_index,
                        ))
                    });
                    pagination_link_signal.set(new_pagination_link);
                    release();
                },
            );
        }
    }
}

#[Composer]
pub fn StandardCarouselRow(
    ctx: &AppContext,
    data: StandardCarouselModelReactive,
    index: usize,
    default_focus_index: usize,
    #[into] reset_scroll: Signal<()>,
    transition_with_action: Rc<dyn Fn(TransitionAction, TransitionSource, String)>,
    on_key_down: Rc<dyn Fn(KeyCode) -> bool>,
    on_item_focused: Rc<dyn Fn(usize)>,
    contextual_menu_data_setter: WriteSignal<Option<ContextualMenuData>>,
    network_client: Rc<NetworkClient>,
    pagination_tracker: Rc<PaginationTracker>,
    #[into] is_pointer_control_enabled: MaybeSignal<bool>,
) -> ColumnComposable {
    let is_tts_enabled = get_tts_enabled(ctx.scope);
    let EpgContext {
        focus_signal,
        focus_state_id,
        scroll_state,
        ..
    } = expect_context(ctx.scope());

    let carousel_data = Rc::new(data.carousel_data);
    let id = carousel_data.id.clone();
    let title = carousel_data.title.clone();

    let id_clone = id.clone();
    let is_preferred_focus = Signal::derive(ctx.scope(), move || focus_state_id.get() == id_clone);

    let focus_value_signal = create_list_focus_value_signal(ctx.scope(), data.items_signal.into());
    create_effect(ctx.scope(), {
        let focus_value_signal = focus_value_signal.clone();
        move |prev| {
            reset_scroll.track();
            if prev.is_some() {
                focus_value_signal.focus_first();
            }
        }
    });

    let carousel_focused = create_focus_signal(ctx.scope());
    let mini_details_height = create_rw_signal(ctx.scope(), 0.0);
    let focused_item_data = create_rw_signal(ctx.scope(), None::<TitleCard>);

    // Animate the mini details showing / hiding
    create_effect(ctx.scope(), {
        let animation_ctx = ctx.clone();
        move |_| {
            animation_ctx.with_animation(fable_motion_persistent_standard(), || {
                if carousel_focused.get() {
                    mini_details_height.set(STANDARD_CAROUSEL_MINI_DETAILS_HEIGHT);
                } else {
                    mini_details_height.set(0.0);
                }
            });
        }
    });

    let on_card_select: Rc<dyn Fn(&TitleCard, ViewSize)> = Rc::new({
        let ctx = ctx.clone();
        let carousel = Rc::clone(&carousel_data);

        move |item: &TitleCard, size: ViewSize| {
            csm_reporter::send_select_impression(
                ctx.scope(),
                ImpressionDataType::StandardCarousel(carousel.as_ref(), item, size),
            );

            if item
                .content_type
                .as_ref()
                .is_some_and(|contentType| contentType.as_str() == "EVENT")
                && should_show_watch_modal(ctx.scope(), None)
            {
                contextual_menu_data_setter.set(create_watch_modal_data(&ctx, item));
                return;
            }

            transition_with_action(
                item.action.clone(),
                TransitionSource::StandardCarouselCard,
                item.gti.clone(),
            )
        }
    });

    let on_item_highlight: Rc<dyn Fn(&TitleCard, ViewSize)> = Rc::new({
        let ctx = ctx.clone();
        let carousel = Rc::clone(&carousel_data);
        move |card, size| {
            csm_reporter::send_highlight_impression(
                ctx.scope(),
                ImpressionDataType::StandardCarousel(carousel.as_ref(), card, size),
                None,
            )
        }
    });

    let on_item_view: Rc<dyn Fn(&TitleCard, ViewSize, ViewImpressionData)> = Rc::new({
        let ctx = ctx.clone();
        let carousel = Rc::clone(&carousel_data);
        move |card, size, view_data| {
            csm_reporter::send_view_impression(
                ctx.scope(),
                ImpressionDataType::StandardCarousel(carousel.as_ref(), card, size),
                view_data,
            )
        }
    });

    let item_count = Signal::derive(ctx.scope(), move || data.items_signal.get().len());
    let item_builder = Rc::new(
        move |ctx: &AppContext, item: &TitleCard, item_index: Signal<usize>| {
            // This is assuming that no item ever gets deleted from this carousel.
            let item_index = item_index.get_untracked();
            let item_focused = create_focus_signal(ctx.scope());
            let on_item_focused = on_item_focused.clone();

            create_effect(ctx.scope(), {
                let item = item.clone();
                move |_| {
                    if item_focused.get() {
                        focused_item_data.set(Some(item.clone()));
                        on_item_focused(item_index);
                    }
                }
            });

            compose! {
                StandardCarouselCard(
                        data: item.clone(),
                        on_select: Rc::clone(&on_card_select),
                        on_highlight: Rc::clone(&on_item_highlight),
                        on_view: Rc::clone(&on_item_view),
                        row_index: index,
                        item_index,
                        total_items: item_count.into()
                    )
                    .focused(item_focused)
                    .test_id(format!("standard_carousel_card_{}_{}", index, item_index))
                    .accessibility_navigation_not_possible_message(LocalizedText::new(AV_LRC_LIVE_TV_NAVIGATION_ROW_END))
            }
        },
    );

    // Only enable pointer control when this row is the vertical scroll target
    let focus_pointer_control = Signal::derive(ctx.scope(), move || {
        if is_pointer_control_enabled.get() && scroll_state.get().vertical_scroll_index == index {
            FocusPointerControl::Enabled
        } else {
            FocusPointerControl::Disabled
        }
    });
    let carousel_index_data = create_rw_signal(ctx.scope(), CarouselIndexData::default());
    let focus_ring_x_offset = Signal::derive(ctx.scope(), move || {
        let CarouselIndexData {
            focus_index,
            scroll_index,
            sub_index: _,
        } = carousel_index_data.get();
        let offset_index = focus_index as f32 - scroll_index as f32;
        offset_index * (STANDARD_CAROUSEL_CARD_WIDTH + STANDARD_CAROUSEL_CARD_SPACING)
    });

    let on_up_press = Rc::clone(&on_key_down);
    let on_down_press = Rc::clone(&on_key_down);
    let on_left_press = Rc::clone(&on_key_down);
    let on_right_press = Rc::clone(&on_key_down);
    let on_back_pressed = Rc::new(move || {
        on_key_down(KeyCode::Backspace);
    });

    let tts_title = title.clone();
    let tts_context_message = Signal::derive(ctx.scope(), move || {
        TextContent::String(if let Some(title) = &tts_title {
            title.to_owned()
        } else {
            "".to_string()
        })
    });

    let id_clone = id.clone();

    compose! {
        Column() {
            Stack() {
                BasicCarouselUI(
                    items: data.items_signal,
                    item_builder,
                    header: HeaderContent::PlainText { title, facet: None },
                    spacing: STANDARD_CAROUSEL_CARD_SPACING,
                    on_end_reached: Rc::new(move || paginate(Rc::clone(&network_client), data.pagination_link, data.items_signal, Rc::clone(&pagination_tracker), id_clone.clone())),
                    on_back_pressed,
                    default_focus_index,
                    focus_value_signal: Some(focus_value_signal.clone()),
                    set_index_data: Some(carousel_index_data.write_only())
                )
                .preferred_focus(is_preferred_focus)

                if carousel_focused.get() && !is_tts_enabled.get() {
                    FocusRing(x_offset: focus_ring_x_offset)
                }
            }

            Stack() {
                if carousel_focused.get() {
                    Memo(item_builder: Box::new(move |ctx| {
                        if let Some(data) = focused_item_data.get() {
                            compose_option! {
                                MiniDetails(data)
                            }
                        } else { None }}
                    ))}
                }
                .height(mini_details_height)
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(STANDARD_CAROUSEL_CARD_AND_DETAILS_SPACING))
        .padding(STANDARD_CAROUSEL_ROW_PADDING)
        .focused(carousel_focused)
        .focused_value(focus_signal, id)
        .blocked_focus_edges(FocusEdges::all())
        .on_key_down(KeyCode::Up, move || { on_up_press(KeyCode::Up); })
        .on_key_down(KeyCode::Down, move || { on_down_press(KeyCode::Down); })
        .on_key_down(KeyCode::Left, move || { on_left_press(KeyCode::Left); })
        .on_key_down(KeyCode::Right, move || { on_right_press(KeyCode::Right); })
        .focus_hierarchical_container(NavigationStrategy::Horizontal)
        .test_id(format!("standard_carousel_row_{}", index))
        .accessibility_context_message(tts_context_message)
        .focus_pointer_control(focus_pointer_control)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::constants::STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET;
    use crate::mocks::mock_data_util::{
        mock_live_page_standard_carousel_reactive, mock_network_title_card, mock_pagination_link,
        mock_title_card,
    };
    use crate::network::traits::mutex::MTX;
    use crate::network::types::{ContainerPaginationResponseResource, Item};
    use crate::ui::epg::epg_util::build_epg_context;
    use crate::utils::test_utils::{assert_node_focused, press_key};
    use common_transform_types::actions::SwiftAction;
    use common_transform_types::resiliency::WithResiliency::Ok;
    use cross_app_events::tti_registry::TimeToInteractiveRegistry;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::pointer_control::PointerControlActiveContext;
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use ignx_compositron::tts::TTSEnabledContext;
    use location::{PageType, RustPage};
    use rstest::rstest;
    use rust_features::{provide_context_test_rust_features, MockRustFeaturesBuilder};
    use std::{cell::Cell, collections::HashMap};

    const NOW: i64 = 1000;

    struct ContextsConfig {
        is_tts_enabled: bool,
        is_watch_modal_enabled: bool,
    }

    fn provide_firetv_context(ctx: &AppContext) {
        let mut firetv = MockFireTV::default();
        // is_firetv to return false
        firetv.expect_is_firetv().return_const(false);
        firetv.provide_mock(ctx.scope());
    }

    fn setup_contexts(
        ctx: &AppContext,
        config: ContextsConfig,
    ) -> (RwSignal<()>, Rc<dyn Fn(KeyCode) -> bool>) {
        let (current_time, _) = create_signal(ctx.scope(), NOW);
        let (schedule_data_window, _) = create_signal(ctx.scope(), (1000, 1500));
        let epg_context = build_epg_context(
            ctx.scope(),
            current_time,
            schedule_data_window.into(),
            0.0,
            0.0,
            (0.0).into(),
            0.0,
            0.0,
        );
        provide_context(ctx.scope(), epg_context);
        provide_context_test_rust_features(ctx.scope());

        let reset_scroll = create_rw_signal(ctx.scope(), ());
        provide_context(ctx.scope(), reset_scroll);

        let last_key_down = create_rw_signal(ctx.scope(), None);
        provide_context(ctx.scope(), last_key_down);
        let on_key_down = Rc::new(move |key| {
            last_key_down.set(Some(key));
            true
        });

        let ContextsConfig {
            is_tts_enabled,
            is_watch_modal_enabled,
        } = config;

        provide_context(
            ctx.scope(),
            TTSEnabledContext(create_signal(ctx.scope(), is_tts_enabled).0),
        );

        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            ctx.scope(),
            vec![],
            PageType::Rust(RustPage::RUST_LIVE_TV),
            None,
        ));
        provide_context(ctx.scope(), tti_registry);

        // Enable/disable watch modal
        let mut rust_features_builder = MockRustFeaturesBuilder::new();
        rust_features_builder =
            rust_features_builder.set_is_watch_modal_enabled(is_watch_modal_enabled);
        provide_context(ctx.scope(), rust_features_builder.build());

        provide_firetv_context(ctx);
        (reset_scroll, on_key_down)
    }

    fn setup(ctx: &AppContext, is_tts_enabled: bool) -> impl Composable<'static> {
        let (reset_scroll, on_key_down) = setup_contexts(
            ctx,
            ContextsConfig {
                is_tts_enabled,
                is_watch_modal_enabled: true,
            },
        );
        let contextual_menu_data_setter = create_rw_signal(ctx.scope(), None).write_only();
        let item_focused = create_rw_signal(ctx.scope(), None);
        provide_context(ctx.scope(), item_focused);

        compose! {
            Column() {
                StandardCarouselRow(
                    data: mock_live_page_standard_carousel_reactive(ctx.scope(), "Live & Upcoming", 10, NOW),
                    index: 0,
                    default_focus_index: 0,
                    reset_scroll,
                    on_key_down,
                    on_item_focused: Rc::new(move |idx| item_focused.set(Some(idx))),
                    contextual_menu_data_setter,
                    network_client: Rc::new(NetworkClient::default()),
                    pagination_tracker: PaginationTracker::new().into(),
                    transition_with_action: Rc::new(move |_, _, _| {}),
                    is_pointer_control_enabled: false,
                )

                Button(text: "Focus me").test_id("button")
            }
        }
    }

    #[rstest]
    #[case::focused_tts_enabled(true, true, false)]
    #[case::focused_tts_disabled(true, false, true)]
    #[case::not_focused_tts_enabled(false, true, false)]
    #[case::not_focused_tts_disabled(false, false, false)]
    fn test_focus_ring_visibility(
        #[case] is_focused: bool,
        #[case] is_tts_enabled: bool,
        #[case] should_be_visible: bool,
    ) {
        launch_test(
            move |ctx| setup(&ctx, is_tts_enabled),
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                if is_focused {
                    let carousel = tree.find_by_test_id("standard_carousel_row_0");
                    test_loop.send_on_focus_event(carousel.get_props().node_id);
                } else {
                    let button = tree.find_by_test_id("button");
                    test_loop.send_on_focus_event(button.get_props().node_id);
                }
                let tree = test_loop.tick_until_done();

                if should_be_visible {
                    assert_node_exists!(tree.find_by_test_id("standard_carousel_focus_ring"));
                } else {
                    assert_node_does_not_exist!(
                        tree.find_by_test_id("standard_carousel_focus_ring")
                    );
                }
            },
        )
    }

    #[test]
    fn sets_focus_ring_offset_on_scroll_with_pointer_control_active() {
        launch_test(
            move |ctx| {
                let active_signal = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), active_signal);

                let pointer_control_active_binding =
                    PointerControlActiveContext(active_signal.read_only());
                provide_context(ctx.scope(), pointer_control_active_binding);

                MockRustFeaturesBuilder::new()
                    .build_as_mock_and_real_into_context(true, ctx.scope());

                setup(&ctx, false)
            },
            move |scope, mut test_loop| {
                let set_pointer_control_active = expect_context::<RwSignal<bool>>(scope);

                let get_focus_ring_x = |tree: &SceneNodeTree| -> f32 {
                    tree.find_by_test_id("standard_carousel_focus_ring")
                        .borrow_props()
                        .layout
                        .border_box
                        .left
                };

                // Initial focus ring position
                let tree = test_loop.tick_until_done();
                assert_eq!(
                    get_focus_ring_x(&tree),
                    STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET
                );

                // Focus on second item with pointer control inactive
                test_loop.send_on_focus_event(
                    tree.find_by_test_id("standard_carousel_card_0_1")
                        .borrow_props()
                        .node_id,
                );

                // Focus ring position does not change
                let tree = test_loop.tick_until_done();
                assert_eq!(
                    get_focus_ring_x(&tree),
                    STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET
                );

                // Focus on third item with pointer control active
                set_pointer_control_active.set(true);
                test_loop.send_on_focus_event(
                    tree.find_by_test_id("standard_carousel_card_0_2")
                        .borrow_props()
                        .node_id,
                );

                // Focus ring position change position
                let tree = test_loop.tick_until_done();
                assert_eq!(
                    get_focus_ring_x(&tree),
                    STANDARD_CAROUSEL_CARD_WIDTH
                        + STANDARD_CAROUSEL_CARD_SPACING
                        + STANDARD_CAROUSEL_FOCUS_RING_HORIZONTAL_OFFSET
                );
            },
        )
    }

    #[test]
    fn mini_details_is_only_visible_when_carousel_has_focus() {
        launch_test(
            |ctx| setup(&ctx, false),
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                let button = tree.find_by_test_id("button");

                // Remove focus from the carousel
                test_loop.send_on_focus_event(button.get_props().node_id);
                let tree = test_loop.tick_until_done();

                // Mini details should not be visible
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details"));

                // Place focus on the carousel
                let carousel = tree.find_by_test_id("standard_carousel_row_0");
                test_loop.send_on_focus_event(carousel.get_props().node_id);
                let tree = test_loop.tick_until_done();

                // Mini details should be visible
                assert_node_exists!(tree.find_by_test_id("mini_details"));
            },
        )
    }

    #[test]
    fn mini_details_updates_with_focused_card() {
        launch_test(
            |ctx| setup(&ctx, false),
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Find the mini details and check the title
                let title = tree.find_by_test_id("mini_details_title");
                assert_eq!(
                    title.get_props().text,
                    Some("Title item_Live & Upcoming_0".to_string())
                );

                // Focus on next card
                let card_2 = tree.find_by_test_id("standard_carousel_card_0_1");
                test_loop.send_on_focus_event(card_2.get_props().node_id);
                let tree = test_loop.tick_until_done();

                // Check the title again
                let title = tree.find_by_test_id("mini_details_title");
                assert_eq!(
                    title.get_props().text,
                    Some("Title item_Live & Upcoming_1".to_string())
                );
            },
        )
    }

    #[test]
    fn it_requests_focus_when_focus_signal_value_set() {
        launch_test(
            |ctx| setup(&ctx, false),
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Remove focus from the carousel
                test_loop.send_on_focus_event(tree.find_by_test_id("button").get_props().node_id);

                // Verify item is not focused
                let tree = test_loop.tick_until_done();
                assert!(
                    !tree
                        .find_by_test_id("standard_carousel_card_0_0")
                        .borrow_props()
                        .is_focused
                );

                // Request focus to standard carousel
                let focus_signal = expect_context::<EpgContext>(scope).focus_signal;
                focus_signal.set("Live & Upcoming".to_string());

                // Request item is focused
                let tree = test_loop.tick_until_done();
                assert!(
                    tree.find_by_test_id("standard_carousel_card_0_0")
                        .borrow_props()
                        .is_focused
                );
            },
        )
    }

    #[test]
    fn sets_preferred_focus_when_id_match() {
        launch_test(
            |ctx| {
                let (current_time, _) = create_signal(ctx.scope(), NOW);
                let (schedule_data_window, _) = create_signal(ctx.scope(), (1000, 1500));
                let epg_context = build_epg_context(
                    ctx.scope(),
                    current_time,
                    schedule_data_window.into(),
                    0.0,
                    0.0,
                    (0.0).into(),
                    0.0,
                    0.0,
                );
                provide_context(ctx.scope(), epg_context);
                epg_context.focus_state_id.set("standard2".to_string());

                provide_context_test_rust_features(ctx.scope());
                provide_firetv_context(&ctx);
                let pagination_tracker = Rc::new(PaginationTracker::new());

                let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
                    ctx.scope(),
                    vec![],
                    PageType::Rust(RustPage::RUST_LIVE_TV),
                    None,
                ));
                provide_context(ctx.scope(), tti_registry);

                compose! {
                    Column() {
                        StandardCarouselRow(
                            data: mock_live_page_standard_carousel_reactive(ctx.scope(), "standard1", 10, NOW),
                            index: 0,
                            default_focus_index: 0,
                            reset_scroll: create_rw_signal(ctx.scope(), ()),
                            on_key_down :Rc::new(|_| true),
                            on_item_focused: Rc::new(|_| {}),
                            contextual_menu_data_setter: create_rw_signal(ctx.scope(), None).write_only(),
                            network_client: Rc::new(NetworkClient::default()),
                            pagination_tracker: Rc::clone(&pagination_tracker),
                            transition_with_action: Rc::new(move |_, _, _| {}),
                            is_pointer_control_enabled: false,
                        )
                        StandardCarouselRow(
                            data: mock_live_page_standard_carousel_reactive(ctx.scope(), "standard2", 10, NOW),
                            index: 1,
                            default_focus_index: 0,
                            reset_scroll: create_rw_signal(ctx.scope(), ()),
                            on_key_down :Rc::new(|_| true),
                            on_item_focused: Rc::new(|_| {}),
                            contextual_menu_data_setter: create_rw_signal(ctx.scope(), None).write_only(),
                            network_client: Rc::new(NetworkClient::default()),
                            pagination_tracker,
                            transition_with_action: Rc::new(move |_, _, _| {}),
                            is_pointer_control_enabled: false,
                        )
                    }
                }
            },
            |_scope, mut test_loop| {
                // Verify first item of second row is focused
                assert_node_focused(&mut test_loop, "standard_carousel_card_1_0");
            },
        )
    }

    #[test]
    fn scrolls_to_first_item_when_reset_scroll_triggered() {
        launch_test(
            |ctx| setup(&ctx, false),
            |scope, mut test_loop| {
                // Scroll right on the carousel
                press_key(&mut test_loop, "standard_carousel_card_0_0", KeyCode::Right);
                assert_node_focused(&mut test_loop, "standard_carousel_card_0_1");

                // Reset scroll and move focus to button
                expect_context::<RwSignal<()>>(scope).set(());
                let tree = test_loop.tick_until_done();
                test_loop
                    .send_on_focus_event(tree.find_by_test_id("button").borrow_props().node_id);

                // Navigate to carousel and verify first card is focused
                press_key(&mut test_loop, "button", KeyCode::Up);
                assert_node_focused(&mut test_loop, "standard_carousel_card_0_0");
            },
        )
    }

    #[test]
    fn bubble_up_back_press_when_first_item_already_focused() {
        launch_test(
            |ctx| setup(&ctx, false),
            |scope, mut test_loop| {
                let last_key_down = expect_context::<RwSignal<Option<KeyCode>>>(scope);

                // Scroll right on the carousel
                press_key(&mut test_loop, "standard_carousel_card_0_0", KeyCode::Right);
                assert_node_focused(&mut test_loop, "standard_carousel_card_0_1");

                // Press back and verify first card focused without bubble up
                press_key(
                    &mut test_loop,
                    "standard_carousel_card_0_1",
                    KeyCode::Backspace,
                );
                assert_node_focused(&mut test_loop, "standard_carousel_card_0_0");
                assert_eq!(last_key_down.get_untracked(), None);

                // Press back and verify first card focused and bubble up
                press_key(
                    &mut test_loop,
                    "standard_carousel_card_0_0",
                    KeyCode::Backspace,
                );
                assert_node_focused(&mut test_loop, "standard_carousel_card_0_0");
                assert_eq!(last_key_down.get_untracked(), Some(KeyCode::Backspace));
            },
        )
    }

    #[test]
    fn calls_on_item_focused_when_item_focused() {
        launch_test(
            |ctx| setup(&ctx, false),
            |scope, mut test_loop| {
                let _ = test_loop.tick_until_done();
                let item_focused = expect_context::<RwSignal<Option<usize>>>(scope);
                assert_eq!(item_focused.get_untracked(), Some(0));

                // Scroll right
                press_key(&mut test_loop, "standard_carousel_card_0_0", KeyCode::Right);
                assert_eq!(item_focused.get_untracked(), Some(1));

                // Press back
                press_key(
                    &mut test_loop,
                    "standard_carousel_card_0_1",
                    KeyCode::Backspace,
                );
                assert_eq!(item_focused.get_untracked(), Some(0));
            },
        )
    }

    mod select_action {
        use super::*;

        #[test]
        fn select_opens_watch_modal_for_event_cards_when_watch_modal_enabled() {
            launch_test(
                |ctx| {
                    let (reset_scroll, on_key_down) = setup_contexts(
                        &ctx,
                        ContextsConfig {
                            is_tts_enabled: false,
                            is_watch_modal_enabled: true,
                        },
                    );

                    let data = mock_live_page_standard_carousel_reactive(
                        ctx.scope(),
                        "Live & Upcoming",
                        10,
                        NOW,
                    );

                    let (contextual_menu_read, contextual_menu_write) =
                        create_signal::<Option<ContextualMenuData>>(ctx.scope(), None);
                    provide_context::<ReadSignal<Option<ContextualMenuData>>>(
                        ctx.scope(),
                        contextual_menu_read,
                    );

                    compose! {
                        Column() {
                            StandardCarouselRow(
                                data,
                                index: 0,
                                default_focus_index: 0,
                                reset_scroll,
                                on_key_down,
                                on_item_focused: Rc::new(|_| {}),
                                contextual_menu_data_setter: contextual_menu_write,
                                network_client: Rc::new(NetworkClient::default()),
                                pagination_tracker: PaginationTracker::new().into(),
                                transition_with_action: Rc::new(|_, _, _| {}),
                                is_pointer_control_enabled: false,
                            )
                        }
                    }
                },
                move |scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();

                    test_loop.send_on_select_event(
                        tree.find_by_test_id("standard_carousel_card_0_0")
                            .get_props()
                            .node_id,
                    );
                    let _tree = test_loop.tick_until_done();

                    let cm_data_signal =
                        use_context::<ReadSignal<Option<ContextualMenuData>>>(scope)
                            .expect("Contextual Menu Signal not found in scope");
                    let cm_data = cm_data_signal.get_untracked();
                    assert!(matches!(cm_data, Some(ContextualMenuData::WatchModal(_))))
                },
            )
        }

        #[test]
        fn select_calls_transition_with_action_for_event_cards_when_watch_modal_disabled() {
            let action_text = Rc::new(Cell::new(None));
            let source_text = Rc::new(Cell::new(None));

            let transition_with_action: Rc<dyn Fn(TransitionAction, TransitionSource, String)> = {
                let action_text = Rc::clone(&action_text);
                let source_text = Rc::clone(&source_text);
                Rc::new(
                    move |action: TransitionAction,
                          source: TransitionSource,
                          _station_id: String| {
                        action_text.replace(match action {
                            TransitionAction::detail(action) => Some(action.refMarker),
                            _ => Some(String::from("Unexpected")),
                        });

                        source_text.replace(match source {
                            TransitionSource::StandardCarouselCard => {
                                Some(String::from("StandardCarouselCard"))
                            }
                            _ => Some(String::from("Unexpected")),
                        });
                    },
                )
            };

            launch_test(
                |ctx| {
                    let (reset_scroll, on_key_down) = setup_contexts(
                        &ctx,
                        ContextsConfig {
                            is_tts_enabled: false,
                            is_watch_modal_enabled: false,
                        },
                    );

                    let data = mock_live_page_standard_carousel_reactive(
                        ctx.scope(),
                        "Live & Upcoming",
                        10,
                        NOW,
                    );
                    let mut items = data.items_signal.get_untracked();
                    items[0].action = TransitionAction::detail(SwiftAction {
                        text: None,
                        analytics: HashMap::new(),
                        refMarker: "Card 1".to_string(),
                        pageId: "pageId".to_string(),
                        pageType: "pageType".to_string(),
                        serviceToken: None,
                        journeyIngressContext: None,
                    });
                    items[1].action = TransitionAction::detail(SwiftAction {
                        text: None,
                        analytics: HashMap::new(),
                        refMarker: "Card 2".to_string(),
                        pageId: "pageId".to_string(),
                        pageType: "pageType".to_string(),
                        serviceToken: None,
                        journeyIngressContext: None,
                    });
                    data.items_signal.set(items);

                    compose! {
                        Column() {
                            StandardCarouselRow(
                                data,
                                index: 0,
                                default_focus_index: 0,
                                reset_scroll,
                                on_key_down,
                                on_item_focused: Rc::new(|_| {}),
                                contextual_menu_data_setter: create_signal(ctx.scope(), None).1,
                                network_client: Rc::new(NetworkClient::default()),
                                pagination_tracker: PaginationTracker::new().into(),
                                transition_with_action,
                                is_pointer_control_enabled: false,
                            )
                        }
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();
                    assert_eq!(action_text.take(), None);
                    assert_eq!(source_text.take(), None);

                    test_loop.send_on_select_event(
                        tree.find_by_test_id("standard_carousel_card_0_0")
                            .get_props()
                            .node_id,
                    );
                    let _tree = test_loop.tick_until_done();

                    assert_eq!(action_text.take(), Some("Card 1".to_string()));
                    assert_eq!(source_text.take(), Some("StandardCarouselCard".to_string()));

                    test_loop.send_on_select_event(
                        tree.find_by_test_id("standard_carousel_card_0_1")
                            .get_props()
                            .node_id,
                    );

                    let _tree = test_loop.tick_until_done();
                    assert_eq!(action_text.take(), Some("Card 2".to_string()));
                    assert_eq!(source_text.take(), Some("StandardCarouselCard".to_string()));
                },
            )
        }

        #[test]
        fn select_calls_transition_with_action_for_non_event_cards() {
            let action_text = Rc::new(Cell::new(None));
            let source_text = Rc::new(Cell::new(None));

            let transition_with_action: Rc<dyn Fn(TransitionAction, TransitionSource, String)> = {
                let action_text = Rc::clone(&action_text);
                let source_text = Rc::clone(&source_text);
                Rc::new(
                    move |action: TransitionAction,
                          source: TransitionSource,
                          _station_id: String| {
                        action_text.replace(match action {
                            TransitionAction::detail(action) => Some(action.refMarker),
                            _ => Some(String::from("Unexpected")),
                        });

                        source_text.replace(match source {
                            TransitionSource::StandardCarouselCard => {
                                Some(String::from("StandardCarouselCard"))
                            }
                            _ => Some(String::from("Unexpected")),
                        });
                    },
                )
            };

            launch_test(
                |ctx| {
                    let (reset_scroll, on_key_down) = setup_contexts(
                        &ctx,
                        ContextsConfig {
                            is_tts_enabled: false,
                            is_watch_modal_enabled: true,
                        },
                    );

                    let data = mock_live_page_standard_carousel_reactive(
                        ctx.scope(),
                        "Live & Upcoming",
                        10,
                        NOW,
                    );
                    let mut items = data.items_signal.get_untracked();
                    items[0].content_type = Some("LINEAR_STATION".to_string());
                    items[0].action = TransitionAction::detail(SwiftAction {
                        text: None,
                        analytics: HashMap::new(),
                        refMarker: "Card 1".to_string(),
                        pageId: "pageId".to_string(),
                        pageType: "pageType".to_string(),
                        serviceToken: None,
                        journeyIngressContext: None,
                    });
                    items[1].content_type = Some("LINEAR_STATION".to_string());
                    items[1].action = TransitionAction::detail(SwiftAction {
                        text: None,
                        analytics: HashMap::new(),
                        refMarker: "Card 2".to_string(),
                        pageId: "pageId".to_string(),
                        pageType: "pageType".to_string(),
                        serviceToken: None,
                        journeyIngressContext: None,
                    });
                    data.items_signal.set(items);

                    compose! {
                        Column() {
                            StandardCarouselRow(
                                data,
                                index: 0,
                                default_focus_index: 0,
                                reset_scroll,
                                on_key_down,
                                on_item_focused: Rc::new(|_| {}),
                                contextual_menu_data_setter: create_signal(ctx.scope(), None).1,
                                network_client: Rc::new(NetworkClient::default()),
                                pagination_tracker: PaginationTracker::new().into(),
                                transition_with_action,
                                is_pointer_control_enabled: false,
                            )
                        }
                    }
                },
                move |_scope, mut test_loop| {
                    let tree = test_loop.tick_until_done();
                    assert_eq!(action_text.take(), None);
                    assert_eq!(source_text.take(), None);

                    test_loop.send_on_select_event(
                        tree.find_by_test_id("standard_carousel_card_0_0")
                            .get_props()
                            .node_id,
                    );
                    let _tree = test_loop.tick_until_done();

                    assert_eq!(action_text.take(), Some("Card 1".to_string()));
                    assert_eq!(source_text.take(), Some("StandardCarouselCard".to_string()));

                    test_loop.send_on_select_event(
                        tree.find_by_test_id("standard_carousel_card_0_1")
                            .get_props()
                            .node_id,
                    );

                    let _tree = test_loop.tick_until_done();
                    assert_eq!(action_text.take(), Some("Card 2".to_string()));
                    assert_eq!(source_text.take(), Some("StandardCarouselCard".to_string()));
                },
            )
        }

        #[test]
        #[ignore]
        fn long_press_opens_watch_modal() {
            // TODO: implement test when test_game_loop supports sending long press events
        }
    }

    mod paginate {
        use super::*;

        #[test]
        fn should_paginate_when_pagination_link_is_present() {
            let _lock = MTX.lock();
            launch_only_scope(|scope| {
                let mut client = NetworkClient::default();

                client.expect_paginate_container().once().returning(
                    |_params, success_callback, _failure_callback| {
                        success_callback(ContainerPaginationResponseResource {
                            items: vec![Ok(Item::TITLE_CARD(mock_network_title_card(
                                "EVENT".to_string(),
                            )))],
                            paginationLink: Ok(
                                Some(mock_pagination_link("new pagination link")).into()
                            ),
                        })
                    },
                );

                let mut pagination_link = mock_pagination_link("original pagination link");
                pagination_link.startIndex = 1;
                let pagination_link_signal = create_rw_signal(scope, Some(pagination_link));
                let items_signal: RwSignal<Vec<TitleCard>> =
                    create_rw_signal(scope, vec![mock_title_card("card0", (1000, 2000))]);
                let pagination_tracker = Rc::new(PaginationTracker::new());

                assert_eq!(items_signal.get_untracked().len(), 1);

                paginate(
                    Rc::new(client),
                    pagination_link_signal,
                    items_signal,
                    Rc::clone(&pagination_tracker),
                    "test_row".to_string(),
                );

                // New items appended and pagination link updated
                assert_eq!(items_signal.get_untracked().len(), 2);
                assert_eq!(items_signal.get_untracked()[1].index, 1);
                assert_eq!(
                    pagination_link_signal.get_untracked(),
                    Some(mock_pagination_link("new pagination link"))
                );
            });
        }

        #[test]
        fn does_nothing_when_pagination_link_is_not_present() {
            let _lock = MTX.lock();
            launch_only_scope(|scope| {
                let mut client = NetworkClient::default();
                client.expect_paginate_container().never();

                let pagination_link_signal = create_rw_signal(scope, None);
                let items = vec![mock_title_card("card0", (1000, 2000))];
                let items_signal: RwSignal<Vec<TitleCard>> = create_rw_signal(scope, items.clone());
                let pagination_tracker = Rc::new(PaginationTracker::new());

                paginate(
                    Rc::new(client),
                    pagination_link_signal,
                    items_signal,
                    Rc::clone(&pagination_tracker),
                    "test_row".to_string(),
                );

                // Items remain the same
                assert_eq!(items_signal.get_untracked(), items);
            });
        }

        #[test]
        fn does_nothing_if_pagination_is_in_progress() {
            let _lock = MTX.lock();
            launch_only_scope(|scope| {
                let mut client = NetworkClient::default();
                client.expect_paginate_container().never();

                let pagination_link_signal =
                    create_rw_signal(scope, Some(mock_pagination_link("pagination link")));
                let items = vec![mock_title_card("card0", (1000, 2000))];
                let items_signal: RwSignal<Vec<TitleCard>> = create_rw_signal(scope, items.clone());
                let pagination_tracker = Rc::new(PaginationTracker::new());
                let row_id = "test_row".to_string();
                pagination_tracker.try_lock(row_id.clone());

                paginate(
                    Rc::new(client),
                    pagination_link_signal,
                    items_signal,
                    Rc::clone(&pagination_tracker),
                    row_id,
                );

                // Items remain the same
                assert_eq!(items_signal.get_untracked(), items);
            });
        }
    }
}
