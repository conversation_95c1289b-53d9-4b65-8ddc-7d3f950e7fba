use crate::{
    constants::{
        MINI_DETAILS_DESCRIPTION_SPACING, MINI_DETAILS_RATING_AND_TIME_SPACING,
        STANDARD_CAROUSEL_MINI_DETAILS_MAX_WIDTH,
    },
    types::TitleCard,
    utils::entitlement_parser::transform_entitlement_data,
};
use amzn_fable_tokens::FableColor;
use common_transform_types::container_items::LiveEventDateTime;
use fableous::{badges::label_badge::*, typography::typography::*, utils::get_ignx_color};
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{prelude::*, text::TextContent};
use title_details::components::entitlement::*;

#[Composer]
pub fn MiniDetails(ctx: &AppContext, data: TitleCard) -> ColumnComposable {
    let TitleCard {
        maturity_rating,
        title,
        synopsis,
        venue,
        live_event_date_header,
        is_entitled,
        entitlement_message,
        ..
    } = data;

    let (date, time) =
        if let Some(LiveEventDateTime::LOCALIZED_HEADER(header)) = live_event_date_header {
            (header.date, header.time)
        } else {
            (None, None)
        };

    let entitlement_label_data = transform_entitlement_data(&entitlement_message);

    compose! {
        Column() {
            TypographyLabel800(content: title.unwrap_or_default())
            .color(get_ignx_color(FableColor::PRIMARY))
            .max_lines(Some(1))
            .test_id("mini_details_title")
            if is_entitled {
                TypographyLabel400(content: synopsis.clone().unwrap_or_default())
                .color(get_ignx_color(FableColor::SECONDARY))
                .max_lines(Some(2))
                .test_id("mini_details_synopsis")
            }
            Row(){
                Memo(item_builder: Box::new(move |ctx| {
                    if let Some(rating) = &maturity_rating {
                        let rating_text = TextContent::from(rating.to_owned());
                        compose_option! {
                            LabelBadge(text: rating_text, size: LabelBadgeSize::SIZE200, color_scheme: LabelBadgeColorScheme::SECONDARY)
                                .test_id("mini_details_maturity_rating")
                        }
                    } else { None }
                }))
                Memo(item_builder: Box::new(move |ctx| {
                    if let Some(date) = &date {
                        compose_option! {
                            TypographyUtility100(content: TextContent::from(date.to_owned()))
                            .color(get_ignx_color(FableColor::SECONDARY))
                            .test_id("mini_details_event_date")
                        }
                    } else { None }
                }))
                Memo(item_builder: Box::new(move |ctx| {
                    if let Some(time) = &time {
                        compose_option! {
                            TypographyUtility100(content: TextContent::from(time.to_owned()))
                            .color(get_ignx_color(FableColor::SECONDARY))
                            .test_id("mini_details_event_time")
                        }
                    } else { None }
                }))
                Memo(item_builder: Box::new(move |ctx| {
                    if let Some(venue) = &venue {
                        compose_option! {
                            TypographyUtility100(content: TextContent::from(venue.to_owned()))
                            .color(get_ignx_color(FableColor::SECONDARY))
                            .test_id("mini_details_venue")
                        }
                    } else { None }
                }))}
                .main_axis_alignment(MainAxisAlignment::SpacedBy(MINI_DETAILS_RATING_AND_TIME_SPACING))
                Memo(item_builder: Box::new(move |ctx| {
                    if !is_entitled {
                        let entitlement_label_data_clone = entitlement_label_data.clone();
                        if let Some(entitlement) = entitlement_label_data_clone {
                            let entitlement:DataSignal<EntitlementLabelDataSignal> = DataSignal::from_data(ctx.scope(), entitlement);
                            compose_option! {
                                Stack() {
                                    EntitlementLabel(data: entitlement)
                                }
                                .test_id("mini_details_entitlement")
                            }
                        }else { None }
                    }else { None }
                }))
            }
            .max_width(STANDARD_CAROUSEL_MINI_DETAILS_MAX_WIDTH)
            .main_axis_alignment(MainAxisAlignment::SpacedBy(MINI_DETAILS_DESCRIPTION_SPACING))
            .test_id("mini_details")
    }
}

#[cfg(test)]
mod tests {
    use crate::mocks::mock_data_util::mock_title_card;

    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::*;

    #[test]
    fn should_render_all_components() {
        launch_test(
            |ctx| {
                let data = mock_title_card("Unit test", (500, 1500));

                compose! {
                    Stack() {
                        MiniDetails(data)
                    }
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                assert_node_exists!(tree.find_by_test_id("mini_details"));
                assert_node_exists!(tree.find_by_test_id("mini_details_title"));
                assert_node_exists!(tree.find_by_test_id("mini_details_synopsis"));
                assert_node_exists!(tree.find_by_test_id("mini_details_maturity_rating"));
                assert_node_exists!(tree.find_by_test_id("mini_details_event_date"));
                assert_node_exists!(tree.find_by_test_id("mini_details_event_time"));
                assert_node_exists!(tree.find_by_test_id("mini_details_venue"));
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details_entitlement"));
            },
        )
    }

    #[test]
    fn should_not_render_components_if_data_is_none() {
        launch_test(
            |ctx| {
                let mut data = mock_title_card("Unit test", (500, 1500));
                data.maturity_rating = None;
                data.live_event_date_header = None;
                data.venue = None;
                data.entitlement_message = None;

                compose! {
                    Stack() {
                        MiniDetails(data)
                    }
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                assert_node_exists!(tree.find_by_test_id("mini_details"));
                assert_node_exists!(tree.find_by_test_id("mini_details_title"));
                assert_node_exists!(tree.find_by_test_id("mini_details_synopsis"));
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details_maturity_rating"));
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details_event_date"));
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details_event_time"));
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details_venue"));
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details_entitlement"));
            },
        )
    }

    #[test]
    fn should_not_render_synopsis_if_not_entitled() {
        launch_test(
            |ctx| {
                let mut data = mock_title_card("Unit test", (500, 1500));
                data.is_entitled = false;

                compose! {
                    Stack() {
                        MiniDetails(data)
                    }
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                assert_node_exists!(tree.find_by_test_id("mini_details"));
                assert_node_exists!(tree.find_by_test_id("mini_details_title"));
                assert_node_does_not_exist!(tree.find_by_test_id("mini_details_synopsis"));
                assert_node_exists!(tree.find_by_test_id("mini_details_maturity_rating"));
                assert_node_exists!(tree.find_by_test_id("mini_details_event_date"));
                assert_node_exists!(tree.find_by_test_id("mini_details_event_time"));
                assert_node_exists!(tree.find_by_test_id("mini_details_venue"));
                assert_node_exists!(tree.find_by_test_id("mini_details_entitlement"));
            },
        )
    }
}
