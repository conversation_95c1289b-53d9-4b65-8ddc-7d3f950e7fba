use ignx_compositron::prelude::*;
use ignx_compositron::Composer;

// Wrapper to include a composable variable inside compose! macro
#[allow(unused_variables, reason = "AppContext required by Composer macro")]
#[Composer]
pub fn Wrapper<C>(ctx: &AppContext, component: C) -> impl Composable<'static>
where
    C: Composable<'static> + 'static,
{
    component
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::{compose, test_utils::*};

    #[test]
    fn it_renders() {
        launch_test(
            |ctx| {
                let render_item = |ctx: &AppContext| {
                    compose! {
                        Rectangle()
                    }
                };

                let component = render_item(&ctx);

                compose! {
                    Wrapper(component)
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let component = tree
                    .find_by_props()
                    .composable_type(ComposableType::Rectangle)
                    .find_first();

                assert_node_exists!(&component);
            },
        )
    }
}
