use amzn_fable_tokens::*;
use fableous::font_icon::FontIcon;
use fableous::font_icon::FontIconProps;
use fableous::pointer_control::Direction;
use fableous::utils::get_ignx_color;
use fableous::FontIconCaller;
use ignx_compositron::app::wasm_app::HoverEvent;
use ignx_compositron::label::LabelComposable;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::SignalSet;
use ignx_compositron::{compose, Composer};
use std::rc::Rc;

#[Composer]
pub fn PointerControlButton(
    ctx: &AppContext,
    direction: Direction,
    on_select: Rc<dyn Fn(&Direction)>,
) -> LabelComposable {
    let (icon, test_id) = match direction {
        Direction::Up => (FableIcon::CARET_UP, "pointer-control-button-up"),
        Direction::Left => (FableIcon::CARET_LEFT, "pointer-control-button-left"),
        Direction::Down => (FableIcon::CARET_DOWN, "pointer-control-button-down"),
        Direction::Right => (FableIcon::CARET_RIGHT, "pointer-control-button-right"),
    };

    let (icon_color, set_icon_color) =
        create_signal(ctx.scope(), get_ignx_color(FableColor::PRIMARY_SUBTLE));

    compose! {
        FontIcon(icon, color: icon_color, size: FontSize(150))
        .test_id(test_id)
        .pointer_control_target(None)
        .on_hover(move |hover_event| {
            if hover_event == &HoverEvent::Enter {
                    set_icon_color.set(get_ignx_color(FableColor::WHITE));
                } else {
                    set_icon_color.set(get_ignx_color(FableColor::PRIMARY_SUBTLE));
                }
            }
        )
        .on_select(move || {
            on_select(&direction);
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::*;
    use rstest::*;

    #[rstest]
    #[case(Direction::Up, FableIcon::CARET_UP)]
    #[case(Direction::Down, FableIcon::CARET_DOWN)]
    #[case(Direction::Left, FableIcon::CARET_LEFT)]
    #[case(Direction::Right, FableIcon::CARET_RIGHT)]
    fn test_pointer_control_button_renders(
        #[case] direction: Direction,
        #[case] expected_icon: &'static str,
    ) {
        launch_test(
            move |ctx| {
                compose! {
                    PointerControlButton(direction, on_select:Rc::new(|_| ()))
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .find_first();

                assert_eq!(node.borrow_props().text.as_ref().unwrap(), expected_icon);
            },
        );
    }
}
