use crate::constants::{HALF_HOUR_WIDTH, HEADER_TIMELINE_LABEL_SPACING};
use crate::utils::datetime_utils::{get_formatted_date, get_formatted_time};
use amzn_fable_tokens::FableColor;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::id::Id;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, compose_option, Composer};
use std::rc::Rc;

#[derive(Clone, Eq, PartialEq, Debug)]
pub struct Segment {
    pub date_label: Option<TextContent>,
    pub time_label: TextContent,
    id: String,
}

impl Id for Segment {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

impl Segment {
    pub fn new(timestamp: i64, show_date: bool) -> Segment {
        let id = format! {"{}_{}", timestamp, show_date};

        let date_label = if show_date {
            get_formatted_date(timestamp)
        } else {
            None
        };

        let time_label = if date_label.is_some() {
            TextContent::String(format!(", {}", get_formatted_time(timestamp)))
        } else {
            TextContent::String(get_formatted_time(timestamp))
        };
        Segment {
            date_label,
            time_label,
            id,
        }
    }
}

#[Composer]
pub fn TimeSegmentList(
    ctx: &AppContext,
    segments: Memo<Vec<Segment>>,
    segment_color: FableColor,
) -> RowListComposable {
    compose! {
        RowList(
            items: segments,
            item_builder: Rc::new({
                let color = get_ignx_color(segment_color);
                move |ctx: &AppContext, item: &Segment, item_index: usize| {
                    let date_label = item.date_label.clone();
                    compose! {
                        Row() {
                            Rectangle()
                            .width(4.0)
                            .height(29.0)
                            .border_radius(100.0)
                            .background_color(color)
                            Row() {
                                Memo(item_builder: Box::new(move |ctx| {
                                    if let Some(date_label) = date_label.clone() {
                                        compose_option! {
                                            TypographyHeading100(
                                                content: date_label,
                                                color
                                            )
                                        }
                                    } else {
                                        None
                                    }
                                }))
                                TypographyHeading100(
                                    content: item.time_label.clone(),
                                    color
                                )
                            }
                            .test_id(format!("HeaderBar::Timeline::Segment_{}", item_index))
                        }
                        .width(HALF_HOUR_WIDTH)
                        .cross_axis_alignment(CrossAxisAlignment::Center)
                        .main_axis_alignment(MainAxisAlignment::SpacedBy(HEADER_TIMELINE_LABEL_SPACING))
                    }
                }
            })
        )
        .test_id("HeaderBar::Timeline")
    }
}

#[cfg(test)]
mod test {
    use std::collections::HashMap;

    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::assert_node_exists,
        text::{LocalizedText, SubstitutionParameters},
    };
    use rstest::rstest;

    #[test]
    fn time_segment_row_renders() {
        launch_test(
            |ctx| {
                let segments = create_memo(ctx.scope(), move |_| {
                    let mut v = Vec::new();
                    for n in 1..10 {
                        v.push(Segment {
                            id: format!("id_{}", n),
                            time_label: TextContent::String("Segment text".to_string()),
                            date_label: None,
                        });
                    }
                    v
                });

                compose! {
                    TimeSegmentList(segments, segment_color: FableColor::PRIMARY)
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let first_time_marker = tree
                    .find_by_props()
                    .test_id("HeaderBar::Timeline::Segment_0")
                    .find_first();
                assert_node_exists!(first_time_marker);
            },
        )
    }

    #[rstest]
    #[case(false, Segment {
        id: "1735119000000_false".to_string(),
        time_label: TextContent::String("9:30 AM".to_string()),
        date_label: None,
    })]
    #[case(true, Segment {
        id: "1735119000000_true".to_string(),
        time_label: TextContent::String(", 9:30 AM".to_string()),
        date_label: Some(TextContent::LocalizedText(LocalizedText {
            string_id: "AV_LRC_SINGLE_DAY_DATE_NO_YEAR".into(), 
            substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                ("shortMonth".to_string(), TextContent::LocalizedText(LocalizedText::new("AV_LRC_SHORT_MONTH_DEC"))),
                ("dateNumber".to_string(), TextContent::String("25".to_string()))
            ])))
        })),
    })]
    fn create_time_segment(#[case] show_date: bool, #[case] expected_segment: Segment) {
        // Wednesday, December 25, 2024 9:30:00 AM
        let current_time = 1735119000000;
        assert_eq!(Segment::new(current_time, show_date), expected_segment);
    }
}
