use crate::constants::{
    EPG_GAP, HEADER_CURRENT_TIME_MARKER_CHEVRON_HEIGHT, HEADER_CURRENT_TIME_MARKER_CHEVRON_WIDTH,
    HEADER_CURRENT_TIME_MARKER_WIDTH,
};
use crate::utils::datetime_utils::get_formatted_time;
use amzn_fable_tokens::FableColor;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextVerticalAlignment;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::{ImageData, ImageFormat, ImageTag};

#[Composer]
pub fn CurrentTimeMarker(
    ctx: &AppContext,
    #[into] current_timestamp: MaybeSignal<i64>,
    #[into] current_time_marker_offset_and_alignment: MaybeSignal<(f32, CrossAxisAlignment)>,
) -> ColumnComposable {
    let current_time_marker_label = Signal::derive(ctx.scope(), move || {
        get_formatted_time(current_timestamp.get())
    });

    let current_time_marker_offset = Signal::derive(ctx.scope(), move || {
        current_time_marker_offset_and_alignment.get().0
    });
    let current_time_marker_alignment = Signal::derive(ctx.scope(), move || {
        current_time_marker_offset_and_alignment.get().1
    });

    compose! {
        Column() {
            TypographyHeading100(
                content: current_time_marker_label,
                color: get_ignx_color(FableColor::PRIMARY)
            )
            .test_id("HeaderBar::CurrentTimeMarker::Label")
            .width(HEADER_CURRENT_TIME_MARKER_WIDTH)
            .vertical_alignment(TextVerticalAlignment::Center)
            LRCImage(data: ImageData {
                url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/pages/livetv/epg-current-time-indicator.png".to_string(),
                width: HEADER_CURRENT_TIME_MARKER_CHEVRON_WIDTH,
                height: HEADER_CURRENT_TIME_MARKER_CHEVRON_HEIGHT,
                tags: vec![ImageTag::Format(ImageFormat::PNG)]
            })
        }
        .width(HEADER_CURRENT_TIME_MARKER_CHEVRON_WIDTH)
        .main_axis_alignment(MainAxisAlignment::SpacedBy(EPG_GAP))
        .cross_axis_alignment(current_time_marker_alignment)
        .translate_x(current_time_marker_offset)
        .test_id("HeaderBar::CurrentTimeMarker")
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::{app::launch_test, test_utils::assert_node_exists};

    #[test]
    fn current_marker_renders() {
        launch_test(
            |ctx| {
                let (current_timestamp, _) = create_signal(ctx.scope(), 1709760330000); // March 6, 2024 9:25:30 PM UTC

                compose! {
                    CurrentTimeMarker(
                        current_timestamp,
                        current_time_marker_offset_and_alignment: (50.0, CrossAxisAlignment::Center),
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                assert_node_exists!(tree
                    .find_by_props()
                    .test_id("HeaderBar::CurrentTimeMarker::Label")
                    .find_first());

                assert_node_exists!(tree
                    .find_by_props()
                    .test_id("HeaderBar::CurrentTimeMarker")
                    .find_first());
            },
        )
    }
}
