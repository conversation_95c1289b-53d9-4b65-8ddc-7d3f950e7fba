use crate::base_airing_card::*;
use crate::constants::*;
use crate::thumbnail::*;
use crate::types::*;
use crate::ui::epg::epg_util::calculate_time_marker_progress;
use crate::utils::common_string_ids::*;
use crate::utils::datetime_utils::*;
use crate::utils::progress_calculator::calculate_progress;
use crate::utils::tts_utils::*;
use amzn_fable_tokens::FableColor;
use common_transform_types::container_items::EntitlementMessage;
use common_transform_types::string_builder::build_prefix_string;
use common_transform_types::string_builder::format_episodic_info;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::context::*;
use ignx_compositron::prelude::*;
use ignx_compositron::text::{LocalizedText, TextContent};
use ignx_compositron::{compose, compose_option, Composer};
use linear_common::types::{Schedule, TimeRange};
use linear_common::util::schedule_util::is_on_air_at_time;
use title_details::components::entitlement::*;

// Equivilant to Label 400
const SYNOPSIS_FONT_SIZE: FontSize = FontSize(24);
const SYNOPSIS_LINE_HEIGHT: f32 = 30.0;
const SYNOPSIS_LETTER_SPACING: f32 = 2.0;
const SYNOPSIS_FONT_WEIGHT: FontWeight = FontWeight::Medium;

/**
* This component renders an Airing Card.
* It accepts the AiringCard data.
*/
#[Composer]
pub fn AiringCard(
    ctx: &AppContext,
    airing_card: AiringModel,
    is_entitled: bool,
    entitlement_message: Option<EntitlementMessage>,
    #[into] current_time: MaybeSignal<i64>,
    #[into] is_row_focused: MaybeSignal<bool>,
    #[into] is_focused: MaybeSignal<bool>,
    #[into] width: MaybeSignal<f32>,
    #[into] height: MaybeSignal<f32>,
    #[into] visible_offset: MaybeSignal<f32>,
    #[into] max_width: MaybeSignal<f32>,
    #[into] left_navigation_hint: MaybeSignal<TextContent>,
    is_last_card: bool,
    accessibility_messages_setter: WriteSignal<Vec<TextContent>>,
    accessibility_hints_setter: WriteSignal<Vec<TextContent>>,
    station_name: String,
    row_index: usize,
    is_favorites_enabled: bool,
) -> StackComposable {
    let card_width = create_rw_signal(ctx.scope(), width.get_untracked());

    let time_range = airing_card.get_time_range();
    let AiringModel {
        rating,
        content_descriptors,
        ..
    } = airing_card.clone();

    let is_on_air = create_memo(ctx.scope(), move |_| {
        is_on_air_at_time(time_range, current_time.get())
    });

    let progress = Signal::derive(ctx.scope(), move || {
        calculate_progress(time_range, current_time.get())
    });

    let display_time = Signal::derive(ctx.scope(), move || {
        get_display_time(current_time.get(), time_range)
    });

    let entitlement_message = entitlement_message
        .and_then(|message| message.message)
        .filter(|message| !message.is_empty());
    let entitlement_message_clone = entitlement_message.clone();
    let entitlement_label_data = Signal::derive(ctx.scope(), move || {
        if is_focused.get() && !is_entitled {
            entitlement_message
                .as_ref()
                .map(|message| EntitlementLabelData {
                    content: Some(message.clone()),
                    icon_type: Some(EntitlementIconType::OFFER),
                    size: EntitlementLabelSize::Small,
                    max_lines: 1,
                })
        } else {
            None
        }
    });

    let progress_scrim_width = Signal::derive(ctx.scope(), move || {
        // Width of progress scrim starting at the end of unfocused image
        calculate_time_marker_progress(current_time.get()) * HEADER_CURRENT_TIME_MARKER_TRACK_WIDTH
            + PROGRAM_IMAGE_WIDTH_FOCUSED
    });

    let text_offset = Signal::derive(ctx.scope(), move || {
        let text_offset = visible_offset.get() + AIRING_CARD_DESCRIPTION_START_PADDING;

        if text_offset > AIRING_CARD_DESCRIPTION_START_PADDING && is_on_air.get() {
            if is_row_focused.get() {
                return text_offset - PROGRAM_IMAGE_WIDTH_FOCUSED;
            } else {
                return text_offset - PROGRAM_IMAGE_WIDTH;
            };
        }

        text_offset
    });

    let text_width = Signal::derive(ctx.scope(), move || {
        let mut text_width = card_width.get() - text_offset.get();
        if is_on_air.get() {
            text_width -= if is_row_focused.get() {
                PROGRAM_IMAGE_WIDTH_FOCUSED
            } else {
                PROGRAM_IMAGE_WIDTH
            };
        }
        text_width.max(0.0)
    });

    let content_visible = Signal::derive(ctx.scope(), move || {
        text_width.with(|width| *width >= AIRING_CARD_DESCRIPTION_MIN_WIDTH)
    });

    let content_opacity =
        Signal::derive(
            ctx.scope(),
            move || if content_visible.get() { 1.0 } else { 0.0 },
        );

    let rating = Signal::derive(ctx.scope(), move || rating.clone().unwrap_or_default());

    let content_descriptors = Signal::derive(ctx.scope(), move || content_descriptors.join(", "));

    let thumbnail_should_animate = Signal::derive(ctx.scope(), move || {
        // The thumbnail should only animate its size changes if it is visible
        visible_offset.get() < PROGRAM_IMAGE_WIDTH_FOCUSED
    });

    let end_gradient_opacity = Signal::derive(ctx.scope(), move || {
        if content_visible.get() {
            AIRING_CARD_END_GRADIENT_OPACITY
        } else {
            0.0
        }
    });

    create_accessibility_messages_effect(
        ctx.scope(),
        &airing_card,
        entitlement_message_clone,
        station_name,
        accessibility_messages_setter,
    );
    create_accessibility_hints_effect(
        ctx.scope(),
        current_time,
        time_range,
        left_navigation_hint,
        is_favorites_enabled,
        accessibility_hints_setter,
    );

    compose! {
        BaseAiringCard(
            is_on_air,
            is_focused,
            width,
            card_width,
            height,
            max_width,
            progress_scrim_width,
            end_gradient_opacity,
            is_last_card,
            content: move |ctx| {
                let AiringModel {
                    title,
                    synopsis,
                    image_url,
                    context,
                    ..
                } = airing_card.clone();
                let episodic_info = build_prefix_string(&context);
                compose! {
                    // Airing Card Content
                    Row() {
                        // Airing image
                        if is_on_air.get() {
                            Thumbnail(
                                url: image_url.clone(),
                                progress,
                                show_progress_bar: is_row_focused,
                                is_entitled,
                                should_animate: thumbnail_should_animate,
                                index: row_index,
                                dimensions: LIVE_PAGE_THUMBNAIL_DIMENSIONS
                            )
                        }

                        Column() {
                            // Airing title
                            TypographyLabel800(content: title)
                            .color(get_ignx_color(FableColor::PRIMARY))
                            .max_lines(Some(1))
                            .test_id("airing_card_title")

                            // Airing synopsis is displayed only when card is focused
                            Memo(item_builder: Box::new(move |ctx| {
                                let episodic_info = episodic_info.clone();
                                let synopsis = synopsis.clone();
                                if is_focused.get() {
                                    compose_option! {
                                        RichTextLabel(text: format_episodic_info(episodic_info)) {
                                            Span(text: synopsis.unwrap_or_default())
                                                .color(get_ignx_color(FableColor::SECONDARY))
                                                .test_id("airing_card_synopsis")
                                        }
                                        .width(text_width)
                                        .font_weight(SYNOPSIS_FONT_WEIGHT)
                                        .font_size(SYNOPSIS_FONT_SIZE)
                                        .letter_spacing(SYNOPSIS_LETTER_SPACING)
                                        .line_height(SYNOPSIS_LINE_HEIGHT)
                                        .max_lines(Some(2))
                                        .color(get_ignx_color(FableColor::PRIMARY))
                                        .test_id("airing_card_episodic_info")
                                    }
                                } else {
                                    None
                                }
                            }));

                            // Time Display
                            Row(){
                                if is_row_focused.get(){
                                    TypographyLabel200(content: display_time)
                                    .color(get_ignx_color(FableColor::SECONDARY))
                                    .test_id("airing_card_display_time")
                                }
                                if is_on_air.get() && is_focused.get() && !rating.get().is_empty() {
                                    TypographyLabel200(content: rating)
                                    .color(get_ignx_color(FableColor::SECONDARY))
                                    .max_lines(Some(1))
                                    .test_id("airing_card_rating")
                                }
                                if is_on_air.get() && is_focused.get() && !content_descriptors.get().is_empty() {
                                    TypographyLabel200(content: content_descriptors)
                                    .color(get_ignx_color(FableColor::SECONDARY))
                                    .max_lines(Some(1))
                                    .test_id("airing_card_content_descriptors")
                                }
                            }
                            .main_axis_alignment(MainAxisAlignment::SpacedBy(AIRING_CARD_RATING_AND_DESCRIPTOR_SPACING))

                            Memo(item_builder: Box::new(move |ctx| {
                                if let Some(message) = entitlement_label_data.get() {
                                    let message:DataSignal<EntitlementLabelDataSignal> = DataSignal::from_data(ctx.scope(), message);
                                    compose_option! {
                                        EntitlementLabel(data: message)
                                    }
                                } else {
                                    None
                                }
                            }));
                        }
                        .main_axis_alignment(MainAxisAlignment::SpacedBy(AIRING_CARD_DESCRIPTION_SPACING))
                        .translate_x(text_offset)
                        .width(text_width)
                        .test_id("airing_card_text_content")
                    }
                    .width(card_width)
                    .height(height)
                    .main_axis_alignment(MainAxisAlignment::Start)
                    .cross_axis_alignment(CrossAxisAlignment::Center)
                    .test_id("airing_card_content")
                    .opacity(content_opacity)
                }
            }
        )
    }
}

fn create_accessibility_hints_effect(
    scope: Scope,
    current_time: MaybeSignal<i64>,
    time_range: TimeRange,
    left_navigation_hint: MaybeSignal<TextContent>,
    is_favorites_enabled: bool,
    accessibility_hints_setter: WriteSignal<Vec<TextContent>>,
) {
    create_effect(scope, move |prev| {
        let is_on_air = is_on_air_at_time(time_range, current_time.get());
        let left_navigation_hint = left_navigation_hint.get();
        let state = (is_on_air, left_navigation_hint.clone());
        if prev.is_some_and(|p| p == state) {
            return state;
        }

        let mut hints = vec![];
        // Selection message
        if is_on_air {
            hints.push(LocalizedText::new(AV_LRC_LIVE_TV_ON_NOW_SELECT_MESSAGE).into());
        } else {
            hints.push(LocalizedText::new(AV_LRC_LIVE_TV_UPCOMING_SELECT_MESSAGE).into());
        }

        // Navigation message
        if is_on_air {
            hints.push(LocalizedText::new(AV_LRC_MOVE_RIGHT_TO_BROWSE_UPCOMING).into());
            if is_favorites_enabled {
                hints.push(LocalizedText::new(AV_LRC_MOVE_LEFT_FOR_FAVORITING).into());
            } else {
                hints.push(left_navigation_hint);
            }
            hints.push(LocalizedText::new(AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS).into());
        } else {
            hints.push(
                LocalizedText::new(AV_LRC_LIVE_TV_NAVIGATION_FROM_EPG_SECONDARY_FOCUS).into(),
            );
        }
        accessibility_hints_setter.set(hints);

        state
    });
}

#[cfg(test)]
mod test {
    use crate::airing_card::*;
    use crate::mocks::mock_data_util::{mock_airing_card_with_context, mock_airing_item};
    use crate::ui::epg::constants::TEN_MIN_IN_MILLIS;
    use chrono::Utc;
    use common_transform_types::container_items::EntitlementMessageIcons;
    use common_transform_types::container_items::ShowContext;
    use cross_app_events::tti_registry::TimeToInteractiveRegistry;
    use current_time::CurrentTimeContext;
    use ignx_compositron::app::{launch_only_scope, launch_test};
    use ignx_compositron::test_utils::*;
    use ignx_compositron::tts::TTSEnabledContext;
    use location::{PageType, RustPage};
    use rstest::*;
    use std::rc::Rc;

    const TEST_CARD_WIDTH: f32 = 744.0;
    const BACKGROUND_COLOR: Color = get_ignx_color(FableColor::INVERSE_EMPHASIS);

    fn setup_contexts(ctx: &AppContext, is_tts_enabled: bool, is_new_row: bool, current_time: i64) {
        provide_context(
            ctx.scope(),
            TTSEnabledContext(create_signal(ctx.scope(), is_tts_enabled).0),
        );
        provide_context(
            ctx.scope(),
            LinearTTSContext {
                is_new_row: create_rw_signal(ctx.scope(), is_new_row),
                focused_metadata_messages: create_rw_signal(ctx.scope(), vec![]),
            },
        );
        provide_context(
            ctx.scope(),
            CurrentTimeContext {
                current_time: create_signal(ctx.scope(), current_time).0,
            },
        );
        let tti_registry = Rc::new(TimeToInteractiveRegistry::new(
            ctx.scope(),
            vec![],
            PageType::Rust(RustPage::RUST_LIVE_TV),
            None,
        ));
        provide_context(ctx.scope(), tti_registry);
    }

    fn setup_airing_card(
        ctx: &AppContext,
        current_time: i64,
        is_entitled: bool,
        is_row_focused: bool,
        is_focused: bool,
        height: f32,
        visible_offset: f32,
        is_last_card: bool,
    ) -> impl Composable<'static> {
        setup_contexts(ctx, false, false, current_time);
        let (_, accessibility_messages_setter) = create_signal(ctx.scope(), vec![]);
        let (_, accessibility_hints_setter) = create_signal(ctx.scope(), vec![]);

        compose! {
            AiringCard(
                airing_card: mock_airing_card_with_context(
                    Some(ShowContext {
                        episode: Some(5),
                        season: Some(6),
                        episodeTitle: Some(String::from("test title")),
                    }),
                    Some(String::from("test synopsis"))
                ),
                is_entitled,
                entitlement_message: Some(EntitlementMessage {
                    message: Some("Subscribe with A".to_string()),
                    icon: Some(EntitlementMessageIcons::OFFER_ICON)
                }),
                current_time,
                is_focused,
                is_row_focused,
                width: TEST_CARD_WIDTH,
                height: height,
                visible_offset,
                is_last_card,
                max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                left_navigation_hint: TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()),
                accessibility_messages_setter,
                accessibility_hints_setter,
                station_name: "station name".to_string(),
                row_index: 0,
                is_favorites_enabled: true,
            )
        }
    }

    #[rstest]
    //Test case 1: is on now and is focused
    #[case(true, true, true, "airing_card_title, airing_card_episodic_info, airing_card_synopsis, airing_card_display_time, airing_card_rating, airing_card_content_descriptors")]
    //Test case 2: is on now and row is focused but card is not focused
    #[case(true, true, false, "airing_card_title, airing_card_display_time")]
    //Test case 3: is on now and row is not focused
    #[case(true, false, false, "airing_card_title")]
    //Test case 4: is upcoming and is focused
    #[case(false, true, true, "airing_card_title, airing_card_episodic_info, airing_card_synopsis, airing_card_display_time")]
    //Test case 5: is upcoming and row is focused but card is not focused
    #[case(false, true, false, "airing_card_title, airing_card_display_time")]
    //Test case 6: is upcoming and row is not focused
    #[case(false, false, false, "airing_card_title")]
    fn it_renders_airing_card(
        #[case] is_on_air: bool,
        #[case] is_row_focused: bool,
        #[case] is_focused: bool,
        #[case] expected_visible_text_ids: &str,
    ) {
        let height = if is_row_focused {
            STATION_ROW_HEIGHT_FOCUSED
        } else {
            STATION_ROW_HEIGHT
        };

        let expected_visible_text_ids: Vec<String> = expected_visible_text_ids
            .split(", ")
            .map(|id| id.to_string())
            .collect();

        launch_test(
            move |ctx| {
                let mut current_time = Utc::now().timestamp_millis() - TEN_MIN_IN_MILLIS;
                if is_on_air {
                    current_time = Utc::now().timestamp_millis() + TEN_MIN_IN_MILLIS;
                }
                setup_airing_card(
                    &ctx,
                    current_time,
                    true,
                    is_row_focused,
                    is_focused,
                    height,
                    0.0,
                    false,
                )
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let background = tree.find_by_test_id("airing_card_background");
                let content = tree.find_by_test_id("airing_card_content");
                let end_gradient = tree.find_by_test_id("airing_card_end_gradient");
                let focus_ring = tree.find_by_test_id("airing_card_focus_ring");

                assert_node_exists!(&content);
                assert_node_exists!(&end_gradient);
                assert_node_exists!(&background);
                assert_eq!(
                    background.borrow_props().layout.size.width,
                    TEST_CARD_WIDTH - AIRING_CARD_MARGIN
                );
                assert_eq!(background.borrow_props().layout.size.height, height);
                let background_color = background
                    .borrow_props()
                    .base_styles
                    .background_color
                    .unwrap();
                assert_eq!(background_color, BACKGROUND_COLOR.apply_opacity(0.8));

                if is_focused {
                    assert_node_exists!(&focus_ring);
                    assert_eq!(focus_ring.borrow_props().base_styles.opacity.unwrap(), 1.0);
                    assert_eq!(
                        focus_ring.borrow_props().layout.size.width,
                        TEST_CARD_WIDTH - AIRING_CARD_MARGIN + AIRING_CARD_BORDER_WIDTH * 2.0,
                    );
                    assert_eq!(
                        focus_ring.borrow_props().layout.size.height,
                        height + AIRING_CARD_BORDER_WIDTH * 2.0,
                    );
                } else {
                    assert_eq!(focus_ring.borrow_props().base_styles.opacity.unwrap(), 0.0);
                }

                for id in expected_visible_text_ids {
                    let text = tree.find_by_test_id(id).get_props().text;
                    assert!(text.is_some());
                }

                if is_on_air {
                    assert_node_exists!(tree.find_by_test_id("airing_card_progress"));
                } else {
                    assert_node_does_not_exist!(tree.find_by_test_id("airing_card_progress"));
                }
            },
        )
    }

    #[test]
    fn it_renders_text_content() {
        let current_time = 1709759900000;
        // March 6, 2024 9:00:00 PM - 9:30:00 PM
        let airing = mock_airing_item("airing_id".to_string(), 1709758800000, 1709760600000);

        launch_test(
            move |ctx| {
                setup_contexts(&ctx, false, false, current_time);
                let (_, accessibility_messages_setter) = create_signal(ctx.scope(), vec![]);
                let (_, accessibility_hints_setter) = create_signal(ctx.scope(), vec![]);

                compose! {
                    AiringCard(
                        airing_card: airing,
                        current_time,
                        is_entitled: true,
                        entitlement_message: None,
                        is_focused: true,
                        is_row_focused: true,
                        width: TEST_CARD_WIDTH,
                        height: STATION_ROW_HEIGHT,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let title = tree.find_by_test_id("airing_card_title").get_props().text;
                assert_eq!(title, Some("airing_id".to_string()));

                let episodic_info = tree
                    .find_by_test_id("airing_card_episodic_info")
                    .get_props()
                    .text;
                assert_eq!(episodic_info, Some("S1 E5 Some info ".to_string()));

                let synopsis = tree
                    .find_by_test_id("airing_card_synopsis")
                    .get_props()
                    .text;
                assert_eq!(synopsis, Some("Synopsis text".to_string()));

                let rating = tree.find_by_test_id("airing_card_rating").get_props().text;
                assert_eq!(rating, Some("18+".to_string()));

                let content_descriptors = tree
                    .find_by_test_id("airing_card_content_descriptors")
                    .get_props()
                    .text;
                assert_eq!(content_descriptors, Some("Language, Violence".to_string()));
            },
        )
    }

    #[rstest]
    //Test case 1: is focused
    #[case(true, true, true)]
    //Test case 2: row is focused but card is not focused
    #[case(true, false, false)]
    //Test case 3: row is not focused
    #[case(false, false, false)]
    fn it_renders_unentitled_message(
        #[case] is_row_focused: bool,
        #[case] is_focused: bool,
        #[case] is_message_expected: bool,
    ) {
        launch_test(
            move |ctx| {
                setup_airing_card(
                    &ctx,
                    Utc::now().timestamp(),
                    false,
                    is_row_focused,
                    is_focused,
                    STATION_ROW_HEIGHT_FOCUSED,
                    0.0,
                    false,
                )
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                assert_eq!(
                    tree.find_where(|node| node.props.text == Some("Subscribe with A".to_string()))
                        .unwrap_or_default()
                        .len()
                        == 1,
                    is_message_expected
                );
            },
        )
    }

    #[test]
    fn it_renders_progress_scrim_at_correct_width() {
        // March 6, 2024 9:15:00 PM
        let current_time = 1709759700000;
        // March 6, 2024 9:00:00 PM - 9:30:00 PM
        let airing = mock_airing_item("airing_id".to_string(), 1709758800000, 1709760600000);

        launch_test(
            move |ctx| {
                setup_contexts(&ctx, false, false, current_time);
                let (_, accessibility_messages_setter) = create_signal(ctx.scope(), vec![]);
                let (_, accessibility_hints_setter) = create_signal(ctx.scope(), vec![]);

                compose! {
                    AiringCard(
                        airing_card: airing,
                        current_time,
                        is_entitled: true,
                        entitlement_message: None,
                        is_focused: false,
                        is_row_focused: false,
                        width: TEST_CARD_WIDTH,
                        height: STATION_ROW_HEIGHT,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let progress = tree.find_by_test_id("airing_card_progress");

                assert_eq!(
                    progress.borrow_props().layout.size.width,
                    0.5 * HEADER_CURRENT_TIME_MARKER_TRACK_WIDTH + PROGRAM_IMAGE_WIDTH_FOCUSED
                );
            },
        );
    }

    #[rstest]
    #[case(AIRING_CARD_DESCRIPTION_MIN_WIDTH, true)]
    #[case(AIRING_CARD_DESCRIPTION_MIN_WIDTH + 1.0, true)]
    #[case(AIRING_CARD_DESCRIPTION_MIN_WIDTH - 1.0, false)]
    fn it_renders_content_conditionally_by_width(
        #[case] text_width: f32,
        #[case] expect_content_to_be_visible: bool,
    ) {
        let current_time = Utc::now().timestamp_millis() - TEN_MIN_IN_MILLIS;
        let card_width = text_width + AIRING_CARD_DESCRIPTION_START_PADDING + AIRING_CARD_MARGIN;
        launch_test(
            move |ctx| {
                setup_contexts(&ctx, false, false, current_time);
                let (_, accessibility_messages_setter) = create_signal(ctx.scope(), vec![]);
                let (_, accessibility_hints_setter) = create_signal(ctx.scope(), vec![]);

                compose! {
                    AiringCard(
                        airing_card: mock_airing_card_with_context(
                            Some(ShowContext {
                                episode: Some(5),
                                season: Some(6),
                                episodeTitle: Some(String::from("test title")),
                            }),
                            Some(String::from("test synopsis"))
                        ),
                        is_entitled: true,
                        entitlement_message: Some(EntitlementMessage {
                            message: Some("Subscribe with A".to_string()),
                            icon: Some(EntitlementMessageIcons::OFFER_ICON)
                        }),
                        current_time,
                        is_focused: false,
                        is_row_focused: false,
                        width: card_width,
                        height: STATION_ROW_HEIGHT,
                        is_last_card: false,
                        visible_offset: 0.0,
                        max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                        left_navigation_hint: TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()),
                        accessibility_messages_setter,
                        accessibility_hints_setter,
                        station_name: "station name".to_string(),
                        row_index: 0,
                        is_favorites_enabled: true,
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let content_opacity = tree
                    .find_by_test_id("airing_card_content")
                    .borrow_props()
                    .base_styles
                    .opacity;
                let gradient_opacity = tree
                    .find_by_test_id("airing_card_end_gradient")
                    .borrow_props()
                    .base_styles
                    .opacity;

                if expect_content_to_be_visible {
                    assert_eq!(content_opacity, Some(1.0));
                    assert_eq!(gradient_opacity, Some(AIRING_CARD_END_GRADIENT_OPACITY));
                } else {
                    assert_eq!(content_opacity, Some(0.0));
                    assert_eq!(gradient_opacity, Some(0.0));
                }
            },
        )
    }

    #[test]
    fn text_width_is_shortened_by_visible_offset() {
        const VISIBLE_OFFSET: f32 = 100.0;
        launch_test(
            move |ctx| {
                setup_airing_card(
                    &ctx,
                    Utc::now().timestamp(),
                    true,
                    true,
                    true,
                    STATION_ROW_HEIGHT_FOCUSED,
                    VISIBLE_OFFSET,
                    false,
                )
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let content_bounding_box = tree
                    .find_by_test_id("airing_card_content")
                    .borrow_props()
                    .layout
                    .border_box;
                let text_bounding_box = tree
                    .find_by_test_id("airing_card_text_content")
                    .borrow_props()
                    .layout
                    .border_box;

                assert_eq!(content_bounding_box.left, 0.0);
                assert_eq!(
                    text_bounding_box.left,
                    VISIBLE_OFFSET + AIRING_CARD_DESCRIPTION_START_PADDING
                );

                let width_difference = content_bounding_box.width - text_bounding_box.width;
                assert_eq!(
                    width_difference,
                    VISIBLE_OFFSET + AIRING_CARD_DESCRIPTION_START_PADDING
                );
            },
        )
    }

    #[rstest]
    // 1: No offset, focused row, on air => text is obscured
    #[case(0.0, true, true, false)]
    // 2: No offset, focused row, upcoming => text is obscured
    #[case(0.0, true, false, false)]
    // 3: No offset, focused row, on air => text is obscured
    #[case(0.0, false, true, false)]
    // 4: No offset, unfocused row, upcoming => text is obscured
    #[case(0.0, false, false, false)]
    // 5: With offset, focused row, on air => text is fully visible
    #[case(500.0, true, true, true)]
    // 6: With offset, focused row, upcoming => text is fully visible
    #[case(500.0, true, false, true)]
    // 7: With offset, focused row, on air => text is fully visible
    #[case(500.0, false, true, true)]
    // 8: With offset, unfocused row, upcoming => text is fully visible
    #[case(500.0, false, false, true)]
    fn it_translates_text_content_with_visible_offset(
        #[case] visible_offset: f32,
        #[case] is_row_focused: bool,
        #[case] is_on_air: bool,
        #[case] expect_to_be_fully_visible: bool,
    ) {
        launch_test(
            move |ctx| {
                let current_time = if is_on_air {
                    Utc::now().timestamp_millis() + TEN_MIN_IN_MILLIS
                } else {
                    Utc::now().timestamp_millis() - TEN_MIN_IN_MILLIS
                };
                let height = if is_row_focused {
                    STATION_ROW_HEIGHT_FOCUSED
                } else {
                    STATION_ROW_HEIGHT
                };
                setup_contexts(&ctx, false, false, current_time);
                let (_, accessibility_messages_setter) = create_signal(ctx.scope(), vec![]);
                let (_, accessibility_hints_setter) = create_signal(ctx.scope(), vec![]);

                compose! {
                        AiringCard(
                            airing_card: mock_airing_card_with_context(
                                Some(ShowContext {
                                    episode: Some(5),
                                    season: Some(6),
                                    episodeTitle: Some(String::from("test title")),
                                }),
                                Some(String::from("test synopsis"))
                            ),
                            is_entitled: true,
                            entitlement_message: Some(EntitlementMessage {
                                message: Some("Subscribe with A".to_string()),
                                icon: Some(EntitlementMessageIcons::OFFER_ICON)
                            }),
                            current_time,
                            is_focused: false,
                            is_row_focused,
                            width: (1000.0),
                            height,
                            visible_offset,
                            is_last_card: false,
                            max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                            left_navigation_hint: TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()),
                            accessibility_messages_setter,
                            accessibility_hints_setter,
                            station_name: "station name".to_string(),
                            row_index: 0,
                            is_favorites_enabled: true,
                        )
                        .translate_x(-500.0) // move card halfway off-screen
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let text_content = tree.find_by_test_id("airing_card_text_content");
                let on_screen_amount = text_content.borrow_props().on_screen_amount;

                if expect_to_be_fully_visible {
                    assert_eq!(on_screen_amount, 1.0);
                } else {
                    assert!(on_screen_amount < 1.0);
                }
            },
        )
    }

    mod tts {
        use super::*;

        #[test]
        fn it_sets_up_tts_effects() {
            let current_time = 1709759900000;
            // March 6, 2024 9:00:00 PM - 9:30:00 PM
            let airing = mock_airing_item("airing_id".to_string(), 1709758800000, 1709760600000);

            launch_test(
                move |ctx| {
                    setup_contexts(&ctx, true, false, current_time);
                    let accessibility_messages = create_rw_signal(ctx.scope(), vec![]);
                    let accessibility_hints = create_rw_signal(ctx.scope(), vec![]);
                    provide_context(ctx.scope(), (accessibility_messages, accessibility_hints));

                    compose! {
                        AiringCard(
                            airing_card: airing,
                            current_time,
                            is_entitled: true,
                            entitlement_message: None,
                            is_focused: true,
                            is_row_focused: true,
                            width: TEST_CARD_WIDTH,
                            height: STATION_ROW_HEIGHT,
                            is_last_card: false,
                            visible_offset: 0.0,
                            max_width: AIRING_CARD_FOCUSED_MIN_WIDTH,
                            left_navigation_hint: TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()),
                            accessibility_messages_setter: accessibility_messages.write_only(),
                            accessibility_hints_setter: accessibility_hints.write_only(),
                            station_name: "station name".to_string(),
                            row_index: 0,
                            is_favorites_enabled: true,
                        )
                    }
                },
                |scope, _test_loop| {
                    let (accessibility_messages, accessibility_hints) =
                        expect_context::<(RwSignal<Vec<TextContent>>, RwSignal<Vec<TextContent>>)>(
                            scope,
                        );

                    assert!(accessibility_messages.get_untracked().len() > 0);
                    assert!(accessibility_hints.get_untracked().len() > 0);
                },
            )
        }

        #[rstest]
        #[case::on_air_favorite_enabled(true, true, vec![
            AV_LRC_LIVE_TV_ON_NOW_SELECT_MESSAGE,
            AV_LRC_MOVE_RIGHT_TO_BROWSE_UPCOMING,
            AV_LRC_MOVE_LEFT_FOR_FAVORITING,
            AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS,
        ])]
        #[case::on_air_favorite_disabled(true, false, vec![
            AV_LRC_LIVE_TV_ON_NOW_SELECT_MESSAGE,
            AV_LRC_MOVE_RIGHT_TO_BROWSE_UPCOMING,
            "LEFT_NAVIGATION_HINT",
            AV_LRC_MOVE_UP_DOWN_TO_BROWSE_STATIONS
        ])]
        #[case::upcoming_favorite_enabled(false, true, vec![
            AV_LRC_LIVE_TV_UPCOMING_SELECT_MESSAGE,
            AV_LRC_LIVE_TV_NAVIGATION_FROM_EPG_SECONDARY_FOCUS
        ])]
        #[case::upcoming_favorite_disabled(false, false, vec![
            AV_LRC_LIVE_TV_UPCOMING_SELECT_MESSAGE,
            AV_LRC_LIVE_TV_NAVIGATION_FROM_EPG_SECONDARY_FOCUS
        ])]
        fn it_sets_accessibility_hints(
            #[case] is_on_air: bool,
            #[case] is_favorites_enabled: bool,
            #[case] expected_messages: Vec<&str>,
        ) {
            let expected_messages: Vec<_> = expected_messages
                .iter()
                .map(|s| LocalizedText::new(s.to_string()).into())
                .collect();

            launch_only_scope(move |scope| {
                let time_range = (1000, 2000);
                let current_time = create_rw_signal(
                    scope,
                    if is_on_air {
                        time_range.0 + 10
                    } else {
                        time_range.0 - 10
                    },
                );
                let accessibility_hints = create_rw_signal(scope, vec![]);

                create_accessibility_hints_effect(
                    scope,
                    current_time.into(),
                    time_range,
                    TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()).into(),
                    is_favorites_enabled,
                    accessibility_hints.write_only(),
                );

                assert_eq!(accessibility_hints.get_untracked(), expected_messages)
            });
        }

        #[test]
        fn it_updates_accessibility_hints_when_airing_become_on_air() {
            launch_only_scope(move |scope| {
                let time_range = (1000, 2000);
                let current_time = create_rw_signal(scope, time_range.0 - 10);
                let accessibility_hints = create_rw_signal(scope, vec![]);

                create_accessibility_hints_effect(
                    scope,
                    current_time.into(),
                    time_range,
                    TextContent::LocalizedText("LEFT_NAVIGATION_HINT".into()).into(),
                    true,
                    accessibility_hints.write_only(),
                );

                let update_count = create_rw_signal(scope, 0);
                create_effect(scope, move |_| {
                    accessibility_hints.track();
                    update_count.set(update_count.get_untracked() + 1);
                });

                // Verify initial hints set
                assert_eq!(update_count.get_untracked(), 1);

                // Verify message does not update after time change
                current_time.set(current_time.get_untracked() + 1);
                assert_eq!(update_count.get_untracked(), 1);

                // Verify message updates after airing is on now
                current_time.set(current_time.get_untracked() + 10);
                assert_eq!(update_count.get_untracked(), 2);
            });
        }
    }
}
