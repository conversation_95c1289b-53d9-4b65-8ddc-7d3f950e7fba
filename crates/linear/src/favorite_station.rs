use std::cell::RefCell;
use std::collections::HashMap;
use std::rc::Rc;

#[double]
use crate::network::NetworkClient;
use crate::utils::{
    common_string_ids::{
        AV_LRC_ADDED_TO_FAVORITES, AV_LRC_ADDED_TO_FAVORITES_OUTSIDE_LIVE_PAGE,
        AV_LRC_REMOVED_FROM_FAVORITES,
    },
    toast_utils::update_toast_state,
};
#[cfg(not(test))]
use explicit_signals_service::prelude::ExplicitSignalsServiceRequest;
use explicit_signals_service::prelude::{
    ExplicitSignalsServiceEntityType, ExplicitSignalsServiceFeedbackType,
    ExplicitSignalsServiceOperation, ExplicitSignalsServiceRequestBody,
};
use ignx_compositron::{
    prelude::Scope,
    text::{LocalizedText, SubstitutionParameters, TextContent},
};
use location::RustPage;
use mockall_double::double;
use network::RequestError;

#[derive(<PERSON><PERSON>, PartialEq, Debug)]
pub enum StationFavoriteAction {
    AddFavorite,
    RemoveFavorite,
}

impl StationFavoriteAction {
    fn to_operation(&self) -> ExplicitSignalsServiceOperation {
        match self {
            Self::AddFavorite => ExplicitSignalsServiceOperation::AddFavorite,
            Self::RemoveFavorite => ExplicitSignalsServiceOperation::RemoveFavorite,
        }
    }
}

pub struct StationFavoriteTracker {
    in_flight_actions: Rc<RefCell<HashMap<String, StationFavoriteAction>>>,
    pending_actions:
        Rc<RefCell<HashMap<String, (StationFavoriteAction, Rc<dyn Fn()>, Rc<dyn Fn()>)>>>,
    network_client: Rc<NetworkClient>,
}

impl StationFavoriteTracker {
    pub fn new(network_client: Rc<NetworkClient>) -> Self {
        Self {
            in_flight_actions: Rc::new(RefCell::new(HashMap::new())),
            pending_actions: Rc::new(RefCell::new(HashMap::new())),
            network_client,
        }
    }

    pub fn update_favorite(
        &self,
        gti: String,
        action: StationFavoriteAction,
        success_callback: impl Fn() + 'static,
        failure_callback: impl Fn() + 'static,
    ) {
        // Check if there's already a pending action for this GTI
        // If we have a pending request of the same type, just keep it
        // If we have a pending request of the opposite type, they cancel out
        let mut pending_actions = self.pending_actions.borrow_mut();
        if let Some((pending_action, _, _)) = pending_actions.get(&gti) {
            if *pending_action != action {
                pending_actions.remove(&gti);
            }
            return;
        }

        // Ignore the request if it matches an in-flight action
        let in_flight_actions = self.in_flight_actions.borrow();
        if let Some(in_flight_action) = in_flight_actions.get(&gti) {
            if *in_flight_action == action {
                return;
            }
        }
        drop(in_flight_actions);

        // Store the new pending action, then process it immediatly
        pending_actions.insert(
            gti.clone(),
            (action, Rc::new(success_callback), Rc::new(failure_callback)),
        );
        drop(pending_actions); // Drop the borrow before processing
        self.process_pending_request(&gti);
    }

    fn process_pending_request(&self, gti: &str) {
        // Do nothing if there's an in-flight request for this GTI
        if self.in_flight_actions.borrow().contains_key(gti) {
            return;
        }

        let Some((action, success_callback, failure_callback)) = ({
            let mut pending_actions = self.pending_actions.borrow_mut();
            pending_actions.remove(gti)
        }) else {
            return; // If there's no pending action, nothing to do
        };

        self.in_flight_actions
            .borrow_mut()
            .insert(gti.to_string(), action.clone());

        let success_wrapper = {
            let self_clone = self.clone();
            let gti = gti.to_owned();

            move || {
                success_callback();

                {
                    self_clone.in_flight_actions.borrow_mut().remove(&gti);
                } // Block used to drop borrow before recursive call

                // Process any other pending request for this GTI
                self_clone.process_pending_request(&gti);
            }
        };

        let failure_wrapper = {
            let self_clone = self.clone();
            let gti = gti.to_owned();

            move |_err| {
                failure_callback();

                // TODO: Metrics (https://taskei.amazon.dev/tasks/LEX3PLR-1285)

                // Clean up both in-flight and pending state for this GTI
                self_clone.in_flight_actions.borrow_mut().remove(&gti);
                self_clone.pending_actions.borrow_mut().remove(&gti);
            }
        };

        self.send_request(gti.to_string(), action, success_wrapper, failure_wrapper);
    }

    fn send_request(
        &self,
        gti: String,
        action: StationFavoriteAction,
        success_callback: impl FnOnce() + 'static,
        failure_callback: impl FnOnce(RequestError) + 'static,
    ) {
        self.network_client.call_explicit_signals_service(
            ExplicitSignalsServiceRequestBody {
                entity_id: gti,
                entity_type: ExplicitSignalsServiceEntityType::Station,
                feedback_type: ExplicitSignalsServiceFeedbackType::FAVORITE,
            },
            action.to_operation(),
            Box::new(success_callback),
            Box::new(failure_callback),
        );
    }
}

impl Clone for StationFavoriteTracker {
    fn clone(&self) -> Self {
        Self {
            in_flight_actions: Rc::clone(&self.in_flight_actions),
            pending_actions: Rc::clone(&self.pending_actions),
            network_client: Rc::clone(&self.network_client),
        }
    }
}

pub fn show_toast(
    scope: Scope,
    action: StationFavoriteAction,
    station_name: String,
    page: RustPage,
) {
    let string_id = match action {
        StationFavoriteAction::AddFavorite => match page {
            RustPage::RUST_LIVE_TV => AV_LRC_ADDED_TO_FAVORITES,
            _ => AV_LRC_ADDED_TO_FAVORITES_OUTSIDE_LIVE_PAGE,
        },
        StationFavoriteAction::RemoveFavorite => AV_LRC_REMOVED_FROM_FAVORITES,
    };

    let localized_message = TextContent::LocalizedText(LocalizedText {
        string_id: string_id.into(),
        substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
            "title".to_string(),
            TextContent::String(station_name),
        )]))),
    });

    update_toast_state(scope, Some(localized_message));
}

#[cfg(test)]
mod tests {
    use super::*;
    use fableous::toasts::toast_context::ToastContext;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::prelude::provide_context;
    use ignx_compositron::reactive::SignalGetUntracked;
    use mockall::predicate::{always, eq};
    use network::common::{DeviceProxyError, DeviceProxyErrorMessage, DeviceProxyErrorMessageBody};
    use rstest::rstest;
    use serial_test::serial;
    use std::cell::Cell;

    #[rstest]
    #[serial]
    fn calls_service_with_correct_params(
        #[values(
            StationFavoriteAction::AddFavorite,
            StationFavoriteAction::RemoveFavorite
        )]
        action: StationFavoriteAction,
    ) {
        let action_clone = action.clone();
        let mut network_client = NetworkClient::default();
        network_client
            .expect_call_explicit_signals_service()
            .with(
                eq(ExplicitSignalsServiceRequestBody {
                    entity_id: "gti".to_string(),
                    entity_type: ExplicitSignalsServiceEntityType::Station,
                    feedback_type: ExplicitSignalsServiceFeedbackType::FAVORITE,
                }),
                eq(action.to_operation()),
                always(), // success callback
                always(), // failure callback
            )
            .returning(
                move |_body, operation, _success_callback, _failure_callback| {
                    assert_eq!(action_clone.to_operation(), operation);
                },
            );

        let tracker = StationFavoriteTracker::new(Rc::new(network_client));
        tracker.update_favorite("gti".to_string(), action, || {}, || {});
    }

    #[rstest]
    #[serial]
    fn success_callback_called_on_network_success(
        #[values(
            StationFavoriteAction::AddFavorite,
            StationFavoriteAction::RemoveFavorite
        )]
        action: StationFavoriteAction,
    ) {
        let success_called = Rc::new(Cell::new(false));
        let success_called_clone = Rc::clone(&success_called);

        let mut network_client = NetworkClient::default();
        network_client
            .expect_call_explicit_signals_service()
            .returning(|_body, _operation, success_callback, _failure_callback| success_callback());

        let success_callback = move || {
            success_called_clone.set(true);
        };
        let failure_callback = || {
            panic!("expected success");
        };

        let tracker = StationFavoriteTracker::new(Rc::new(network_client));
        tracker.update_favorite(
            "gti".to_string(),
            action,
            success_callback,
            failure_callback,
        );

        assert!(success_called.take());
    }

    #[rstest]
    #[serial]
    fn failure_callback_called_on_network_error(
        #[values(
            StationFavoriteAction::AddFavorite,
            StationFavoriteAction::RemoveFavorite
        )]
        action: StationFavoriteAction,
    ) {
        let failure_called = Rc::new(Cell::new(false));
        let failure_called_clone = Rc::clone(&failure_called);

        let mut network_client = NetworkClient::default();
        network_client
            .expect_call_explicit_signals_service()
            .returning(|_body, _operation, _success_callback, failure_callback| {
                failure_callback(network::RequestError::DeviceProxyError(DeviceProxyError {
                    message: DeviceProxyErrorMessage {
                        status_code: "status code".to_string(),
                        timestamp: 1,
                        body: DeviceProxyErrorMessageBody {
                            code: "error code".to_string(),
                            message: "error message".to_string(),
                        },
                    },
                }))
            });

        let success_callback = || {
            panic!("expected failure");
        };

        let failure_callback = move || {
            failure_called_clone.set(true);
        };

        let tracker = StationFavoriteTracker::new(Rc::new(network_client));
        tracker.update_favorite(
            "gti".to_string(),
            action,
            success_callback,
            failure_callback,
        );

        assert!(failure_called.take());
    }

    #[test]
    #[serial]
    fn ignores_duplicate_requests_for_same_action() {
        let mut network_client = NetworkClient::default();

        network_client
            .expect_call_explicit_signals_service()
            .times(1) // The network call should only be made once
            .return_const(());

        let tracker = StationFavoriteTracker::new(Rc::new(network_client));

        // Make two AddFavorite requests for the same GTI
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Verify no pending request for this GTI
        assert!(tracker.pending_actions.borrow().get("gti").is_none());
    }

    #[test]
    #[serial]
    fn queues_different_action_when_one_in_flight() {
        let mut network_client = NetworkClient::default();

        // First call does not complete
        network_client
            .expect_call_explicit_signals_service()
            .times(1)
            .return_const(());

        let tracker = StationFavoriteTracker::new(Rc::new(network_client));

        // Make first request
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Make second request with different action
        // This should be pending, since the original request did not complete
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::RemoveFavorite,
            || {},
            || {},
        );

        // Verify in-flight state
        let in_flight_actions = tracker.in_flight_actions.borrow();
        let action = in_flight_actions
            .get("gti")
            .expect("Should have in-flight action for GTI");
        assert_eq!(*action, StationFavoriteAction::AddFavorite);

        // Verify pending state
        let pending_actions = tracker.pending_actions.borrow();
        let (action, _, _) = pending_actions
            .get("gti")
            .expect("Should have pending action for GTI");
        assert_eq!(*action, StationFavoriteAction::RemoveFavorite);
    }

    #[test]
    #[serial]
    fn executes_pending_action_after_success() {
        let mut network_client = NetworkClient::default();
        let call_count = Rc::new(RefCell::new(0));
        let call_count_clone = Rc::clone(&call_count);

        // First call executes the success callback
        network_client
            .expect_call_explicit_signals_service()
            .times(2) // Expect two calls
            .returning_st(move |_, operation, success_cb, _| {
                let count = *call_count_clone.borrow();
                *call_count_clone.borrow_mut() += 1;

                if count == 0 {
                    // First call should be AddFavorite
                    assert_eq!(operation, ExplicitSignalsServiceOperation::AddFavorite);
                } else {
                    // Second call should be RemoveFavorite
                    assert_eq!(operation, ExplicitSignalsServiceOperation::RemoveFavorite);
                }

                // Execute success callback to trigger the next request
                success_cb();
            });

        let network_client = Rc::new(network_client);
        let tracker = StationFavoriteTracker::new(Rc::clone(&network_client));

        // Make first request
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Queue second request
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::RemoveFavorite,
            || {},
            || {},
        );

        // Verify that both requests were processed
        assert_eq!(*call_count.borrow(), 2);
    }

    #[test]
    #[serial]
    fn cancels_pending_action_when_opposite_action_requested() {
        let mut network_client = NetworkClient::default();

        // The first network call won't complete
        network_client
            .expect_call_explicit_signals_service()
            .times(1)
            .return_const(());

        let network_client = Rc::new(network_client);
        let tracker = StationFavoriteTracker::new(Rc::clone(&network_client));

        // Make first request
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Queue second request
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::RemoveFavorite,
            || {},
            || {},
        );

        // Make third request that should cancel the second
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Verify in-flight state
        let in_flight_actions = tracker.in_flight_actions.borrow();
        let action = in_flight_actions
            .get("gti")
            .expect("Should have in-flight action for GTI");
        assert_eq!(*action, StationFavoriteAction::AddFavorite);

        // Verify pending actions should be cleared
        let pending_actions = tracker.pending_actions.borrow();
        assert!(
            !pending_actions.contains_key("gti"),
            "Pending action should be canceled"
        );
    }

    #[test]
    #[serial]
    fn clears_state_on_failure() {
        let mut network_client = NetworkClient::default();

        // Request will fail when this is called
        let failure_callback_store = Rc::new(RefCell::new(None));
        network_client
            .expect_call_explicit_signals_service()
            .times(1)
            .returning_st({
                let failure_callback_store = Rc::clone(&failure_callback_store);
                move |_, _, _, failure_cb| {
                    // Store the callback but don't call it yet
                    *failure_callback_store.borrow_mut() = Some(failure_cb);
                }
            });

        let tracker = StationFavoriteTracker::new(Rc::new(network_client));

        // Make the initial request that will become in-flight
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {
                panic!("Expected failure!");
            },
            || {},
        );

        // Make a second request (will be pending)
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::RemoveFavorite,
            || {},
            || {},
        );

        // Verify both in-flight and pending states exist before failure
        {
            let in_flight_actions = tracker.in_flight_actions.borrow();
            assert!(
                in_flight_actions.contains_key("gti"),
                "Should have in-flight action before failure"
            );

            let pending_actions = tracker.pending_actions.borrow();
            assert!(
                pending_actions.contains_key("gti"),
                "Should have pending action before failure"
            );
        }

        // Now call the failure callback to trigger the cleanup
        if let Some(cb) = failure_callback_store.borrow_mut().take() {
            cb(network::RequestError::DeviceProxyError(DeviceProxyError {
                message: DeviceProxyErrorMessage {
                    status_code: "status code".to_string(),
                    timestamp: 1,
                    body: DeviceProxyErrorMessageBody {
                        code: "error code".to_string(),
                        message: "error message".to_string(),
                    },
                },
            }));
        }

        // Verify state is cleared after failure
        let in_flight_actions = tracker.in_flight_actions.borrow();
        assert!(
            !in_flight_actions.contains_key("gti"),
            "In-flight state should be cleared after failure"
        );

        let pending_actions = tracker.pending_actions.borrow();
        assert!(
            !pending_actions.contains_key("gti"),
            "Pending state should be cleared after failure"
        );
    }

    #[test]
    #[serial]
    fn clears_state_after_successful_completion_with_no_pending_requests() {
        let mut network_client = NetworkClient::default();

        network_client
            .expect_call_explicit_signals_service()
            .times(1)
            .returning(|_, _, success_cb, _| {
                success_cb();
            });

        let network_client = Rc::new(network_client);
        let tracker = StationFavoriteTracker::new(Rc::clone(&network_client));

        // Make request
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Verify state is cleared after successful completion
        let in_flight_actions = tracker.in_flight_actions.borrow();
        assert!(
            !in_flight_actions.contains_key("gti"),
            "In-flight state should be cleared after successful completion"
        );

        let pending_actions = tracker.pending_actions.borrow();
        assert!(
            !pending_actions.contains_key("gti"),
            "Pending state should be cleared after successful completion"
        );
    }

    #[test]
    #[serial]
    fn handles_different_gtis_independently() {
        let mut network_client = NetworkClient::default();
        let call_count = Rc::new(RefCell::new(0));
        let call_count_clone = Rc::clone(&call_count);

        // Expect two calls for different GTIs. Neither request will complete.
        network_client
            .expect_call_explicit_signals_service()
            .times(2)
            .returning_st(move |body, _, _, _| {
                let current_count = *call_count_clone.borrow();
                if current_count == 0 {
                    assert_eq!(body.entity_id, "gti1");
                } else {
                    assert_eq!(body.entity_id, "gti2");
                }
                *call_count_clone.borrow_mut() += 1;
            });

        let network_client = Rc::new(network_client);
        let tracker = StationFavoriteTracker::new(Rc::clone(&network_client));

        // Make request for first GTI
        tracker.update_favorite(
            "gti1".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Make request for second GTI
        tracker.update_favorite(
            "gti2".to_string(),
            StationFavoriteAction::AddFavorite,
            || {},
            || {},
        );

        // Verify both requests were made
        assert_eq!(*call_count.borrow(), 2);
    }

    #[test]
    #[serial]
    fn success_callback_executed_for_pending_requests() {
        let network_success_cb = Rc::new(RefCell::new(None));
        let network_success_cb_clone = network_success_cb.clone();

        let mut network_client = NetworkClient::default();

        network_client
            .expect_call_explicit_signals_service()
            .times(2)
            .returning_st(
                move |_body, operation, success_callback, _failure_callback| {
                    match operation {
                        ExplicitSignalsServiceOperation::AddFavorite => {
                            // Do not complete the first network call immediately
                            // Store the callback to complete the request after the second is queued
                            *network_success_cb_clone.borrow_mut() = Some(success_callback);
                        }
                        ExplicitSignalsServiceOperation::RemoveFavorite => {
                            // Complete the second network call immediately
                            success_callback();
                        }
                    }
                },
            );

        let tracker = StationFavoriteTracker::new(Rc::new(network_client));

        let first_callback_executed = Rc::new(RefCell::new(false));
        let second_callback_executed = Rc::new(RefCell::new(false));

        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::AddFavorite,
            {
                let first_callback_executed = Rc::clone(&first_callback_executed);
                move || {
                    *first_callback_executed.borrow_mut() = true;
                }
            },
            || {
                panic!("First request should not fail");
            },
        );

        // Queue second request (RemoveFavorite)
        tracker.update_favorite(
            "gti".to_string(),
            StationFavoriteAction::RemoveFavorite,
            {
                let second_callback_executed = Rc::clone(&second_callback_executed);
                move || {
                    *second_callback_executed.borrow_mut() = true;
                }
            },
            || {
                panic!("Second request should not fail");
            },
        );

        // Verify the state before completing the first request
        {
            // Verify in-flight state
            let in_flight_actions = tracker.in_flight_actions.borrow();
            let action = in_flight_actions
                .get("gti")
                .expect("Should have in-flight action for GTI");
            assert_eq!(*action, StationFavoriteAction::AddFavorite);

            // Verify pending state
            let pending_actions = tracker.pending_actions.borrow();
            let (action, _, _) = pending_actions
                .get("gti")
                .expect("Should have pending action for GTI");
            assert_eq!(*action, StationFavoriteAction::RemoveFavorite);
        }

        // Now complete the first network request - this should trigger processing the pending action
        if let Some(callback) = network_success_cb.borrow_mut().take() {
            callback();
        } else {
            panic!("First callback wasn't properly stored");
        }

        // Verify both callbacks were executed
        assert!(
            *first_callback_executed.borrow(),
            "First success callback should be called"
        );
        assert!(
            *second_callback_executed.borrow(),
            "Second success callback should be called"
        );

        // Verify final state - all requests should be processed
        {
            let in_flight_actions = tracker.in_flight_actions.borrow();
            assert!(
                !in_flight_actions.contains_key("gti"),
                "In-flight state should be cleared after all requests complete"
            );

            let pending_actions = tracker.pending_actions.borrow();
            assert!(
                !pending_actions.contains_key("gti"),
                "Pending state should be cleared after all requests complete"
            );
        }
    }

    #[rstest]
    #[case(
        StationFavoriteAction::AddFavorite,
        RustPage::RUST_LIVE_TV,
        AV_LRC_ADDED_TO_FAVORITES
    )]
    #[case(
        StationFavoriteAction::AddFavorite,
        RustPage::RUST_STATION_DETAILS,
        AV_LRC_ADDED_TO_FAVORITES_OUTSIDE_LIVE_PAGE
    )]
    #[case(
        StationFavoriteAction::RemoveFavorite,
        RustPage::RUST_LIVE_TV,
        AV_LRC_REMOVED_FROM_FAVORITES
    )]
    #[case(
        StationFavoriteAction::RemoveFavorite,
        RustPage::RUST_STATION_DETAILS,
        AV_LRC_REMOVED_FROM_FAVORITES
    )]
    fn test_show_toast(
        #[case] action: StationFavoriteAction,
        #[case] page: RustPage,
        #[case] expected_string_id: &'static str,
    ) {
        launch_only_scope(move |scope| {
            let toast_ctx = ToastContext::new(scope);
            provide_context(scope, toast_ctx);

            show_toast(scope, action, "ESPN".to_string(), page);

            assert!(toast_ctx.visible.get_untracked());
            if let Some(TextContent::LocalizedText(text)) = toast_ctx.message.get_untracked() {
                assert_eq!(text.string_id.as_str(), expected_string_id);

                if let Some(params) = &text.substitution_parameters {
                    if let Some(TextContent::String(title)) = params.0.get("title") {
                        assert_eq!(title, "ESPN");
                    } else {
                        panic!("Title substitution parameter not found or not a string");
                    }
                } else {
                    panic!("Substitution parameters not found");
                }
            } else {
                panic!("Toast message is not a localized text");
            }
        });
    }
}
