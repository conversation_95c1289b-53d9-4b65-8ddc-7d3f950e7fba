use crate::live_page::data_provider::types::{DataProviderState, LivePageData, LivePageMetadata};
use crate::network::types::LivePageResponseResourceLrc;
use crate::station_details::details_page_load::StationDetailsLoadResult;
use crate::types::StationHeroModel;
use cache::cache_invalidation::emitter::CacheInvalidationEventEmitterRc;
use cache::cache_invalidation::events::CacheInvalidationEvent;
use cache::expirable_lru_cache::ExpirableLruCache;
use cache::ExpirableLruCacheRc;
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::reactive::provide_context;
use ignx_compositron::reactive::Scope;
use serde::Deserialize;
use std::cell::BorrowMutError;
use std::rc::Rc;
use std::time::Duration;

/// Cache to store full Live page data to restore page when user navigate
/// back to Live page such as from playback, details page, etc.
/// This cache will only have an entry while Live page is in the app navigation stack.
pub type LivePageCache = ExpirableLruCacheRc<String, LivePageCacheData>;

/// Cache to maintain Live page data across different Live page sessions.
/// This stores last watched station even when user navigate away from Live page via Top Nav.
/// This data is used for Live page Hero component.
pub type LivePageSessionCache = ExpirableLruCacheRc<String, StationHeroModel>;

/// Cache to store prefetched initial Live page load network response.
pub type LivePagePrefetchCache = ExpirableLruCacheRc<String, LivePageResponseResourceLrc>;

// Cache to store station details page data and focus restoration
pub type StationDetailsPageCache = ExpirableLruCacheRc<String, StationDetailsLoadResult>;

#[derive_test_only(Clone, Debug, PartialEq)]
pub struct LivePageCacheData {
    pub page_data: LivePageData,
    pub metadata: LivePageMetadata,
    pub data_provider_state: DataProviderState,
}
pub fn create_cache(
    scope: Scope,
    event_emitter: CacheInvalidationEventEmitterRc,
    rpc_manager: RPCManager,
    prefetch_cache: LivePagePrefetchCache,
) {
    let live_page_cache = ExpirableLruCache::<String, LivePageCacheData>::new_rc(
        1,
        Box::new(|_| Duration::from_secs(4 * 60 * 60)),
    );
    let live_page_session_cache = ExpirableLruCache::<String, StationHeroModel>::new_rc(
        1,
        Box::new(|_| Duration::from_secs(4 * 60 * 60)),
    );
    let station_details_cache = ExpirableLruCache::<String, StationDetailsLoadResult>::new_rc(
        1,
        Box::new(|_| Duration::from_secs(4 * 60 * 60)),
    );

    subscribe_to_cache_invalidation_events(
        scope,
        Rc::clone(&live_page_cache),
        Rc::clone(&live_page_session_cache),
        Rc::clone(&station_details_cache),
        event_emitter,
    );
    register_cross_app_cache_invalidation(
        rpc_manager,
        Rc::clone(&live_page_cache),
        Rc::clone(&live_page_session_cache),
        prefetch_cache,
        Rc::clone(&station_details_cache),
    );

    provide_context(scope, live_page_cache);
    provide_context(scope, live_page_session_cache);
    provide_context(scope, station_details_cache);
}

fn subscribe_to_cache_invalidation_events(
    scope: Scope,
    live_page_cache: LivePageCache,
    live_page_session_cache: LivePageSessionCache,
    station_details_cache: StationDetailsPageCache,
    event_emitter: CacheInvalidationEventEmitterRc,
) {
    let on_event_emitted = move |event: CacheInvalidationEvent| match event {
        CacheInvalidationEvent::AuthChange
        | CacheInvalidationEvent::LocaleChange
        | CacheInvalidationEvent::ProfileChange => {
            let _ = try_clear_cache("LivePageCache", &live_page_cache);
            let _ = try_clear_cache("LivePageSessionCache", &live_page_session_cache);
            let _ = try_clear_cache("StationDetailsPageCache", &station_details_cache);
        }
        _ => {}
    };

    event_emitter
        .borrow_mut()
        .subscribe(scope, Box::new(on_event_emitted));
}

#[derive(Deserialize)]
pub struct InvalidateCacheRPCArgs {
    pub reason: String,
}

fn register_cross_app_cache_invalidation(
    rpc_manager: RPCManager,
    live_page_cache: LivePageCache,
    live_page_session_cache: LivePageSessionCache,
    live_page_prefetch_cache: LivePagePrefetchCache,
    station_details_cache: StationDetailsPageCache,
) {
    rpc_manager.register_cross_app_function(
        "invalidate_linear_cache".into(),
        move |_args: InvalidateCacheRPCArgs| {
            let res1 = try_clear_cache("LivePageCache", &live_page_cache);
            let res2 = try_clear_cache("LivePageSessionCache", &live_page_session_cache);
            let res3 = try_clear_cache("LivePagePrefetchCache", &live_page_prefetch_cache);
            let res4 = try_clear_cache("StationDetailsPageCache", &station_details_cache);
            match res1.and(res2).and(res3).and(res4) {
                Ok(_) => Ok(true),
                Err(_) => Err("Unable to clear caches.".into()),
            }
        },
    );
}

fn try_clear_cache<T>(
    name: &str,
    cache: &ExpirableLruCacheRc<String, T>,
) -> Result<(), BorrowMutError> {
    cache
        .try_borrow_mut()
        .map(|mut cache| cache.clear())
        .inspect_err(|e| log::error!("Unable to clear {}: {}", name, e))
}
