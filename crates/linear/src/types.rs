#![allow(nonstandard_style, reason = "Follows JSON pattern")]
use crate::network::types::{PaginationLink, Reactions as NetworkReactions, SubNavAction};
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::TransitionAction;
use common_transform_types::container_items::{
    EntitlementMessage, LiveEventDateTime, ShowContext, TitleMetadataBadge,
};
use ignx_compositron::context::*;
use ignx_compositron::id::Id;
use ignx_compositron::reactive::*;
use ignx_compositron::text::TextContent;
use linear_common::types::{Schedule, TimeRange};
use liveliness_types::Liveliness as CommonLiveliness;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;
use std::rc::Rc;
use strum::Display;

#[derive(<PERSON>splay, <PERSON><PERSON>, PartialEq)]
#[derive_test_only(Debug)]
pub enum RequestError {
    BadRequest,
    NoContent,
    ParseError,
    TravelingCustomerException { heading: String, message: String },
    UnknownError,
}

/// View-model for `HeroDetails`, `MediaBackground` and to initiate full-screen playback.
#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct StationHeroModel {
    /// Station GTI
    pub station_gti: String,

    /// Station logo
    pub station_logo: Option<String>,

    /// Station name
    pub station_name: String,

    /// Airing items for this station
    pub airings: Vec<AiringModel>,

    /// Station entitlement message and icon
    pub entitlement_message: Option<EntitlementMessage>,

    /// Label for the interactive button
    pub button_label: Option<String>,

    /// Button action
    pub action: TransitionAction,

    /// Widget type
    pub widget_type: String,

    /// Container analytics
    pub analytics: Option<HashMap<String, String>>,

    /// Explicit customer reactions to this station (e.g. Favorite)
    pub reactions: Reactions,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct StandardCarouselModel {
    /// Container analytics
    pub analytics: Option<HashMap<String, String>>,

    /// Whether customer is entitled to this carousel
    pub is_entitled: bool,

    pub id: String,

    /// List of title cards
    pub items: Vec<TitleCard>,

    /// Standard Carousel's title like `Live and Upcoming`
    pub title: Option<String>,

    /// Index of the container on the page
    pub container_index: usize,

    /// Pagination link
    pub pagination_link: Option<PaginationLink>,
}

impl Id for StandardCarouselModel {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct StandardCarouselModelReactive {
    pub carousel_data: StandardCarouselModel,
    pub items_signal: RwSignal<Vec<TitleCard>>,
    pub pagination_link: RwSignal<Option<PaginationLink>>,
}

impl Id for StandardCarouselModelReactive {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.carousel_data.id
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct StationRowModel {
    /// Unique station row identifier. Combination of container id and station id. A station may appear multiple times on a page.
    pub id: String,

    /// Station GTI
    pub station_gti: String,

    /// Station name text
    pub station_name: String,

    /// Station logo image url
    pub logo_url: Option<String>,

    /// Airing items for this station
    pub airings: Vec<AiringModel>,

    /// Whether customer is entitled to this station
    pub is_entitled: bool,

    /// Explicit customer reactions to this station (e.g. Favorite)
    pub reactions: Reactions,

    /// Station entitlement message and icon
    pub entitlement_message: Option<EntitlementMessage>,

    /// Interaction action for this station
    pub action: TransitionAction,

    /// Title text for a group of station rows. This is the same for all stations in a group.
    pub epg_group_title: String,

    /// Index of this station in a group of station rows. Since a station may be dropped, this does not map to index in EPG group response.
    pub index_in_epg_group: usize,

    /// Index of the container on the page
    pub container_index: usize,

    /// Whether this station row is part of watch history group
    pub is_watch_history: bool,

    /// Card widget type
    pub widget_type: String,

    /// Container analytics
    pub analytics: Option<HashMap<String, String>>,
}

impl From<StationRowModel> for StationHeroModel {
    fn from(station: StationRowModel) -> Self {
        StationHeroModel {
            station_gti: station.station_gti,
            station_logo: station.logo_url,
            station_name: station.station_name,
            airings: station.airings,
            entitlement_message: station.entitlement_message,
            button_label: None,
            action: station.action,
            widget_type: station.widget_type,
            analytics: station.analytics,
            reactions: station.reactions,
        }
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct Reactions {
    pub is_favorite: bool,
}

impl From<NetworkReactions> for Reactions {
    fn from(reactions: NetworkReactions) -> Self {
        Reactions {
            is_favorite: reactions.isFavorite,
        }
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum Liveliness {
    LIVE,
    ON_NOW,
    UPCOMING,
    ENDED,
    CANCELLED,
    POSTPONED,
    SUSPENDED,
}

impl From<Liveliness> for CommonLiveliness {
    fn from(value: Liveliness) -> Self {
        match value {
            Liveliness::LIVE => CommonLiveliness::Live,
            Liveliness::ON_NOW => CommonLiveliness::OnNow,
            Liveliness::UPCOMING => CommonLiveliness::Upcoming,
            Liveliness::ENDED => CommonLiveliness::Ended,
            Liveliness::CANCELLED => CommonLiveliness::Cancelled,
            Liveliness::POSTPONED => CommonLiveliness::Postponed,
            Liveliness::SUSPENDED => CommonLiveliness::Suspended,
        }
    }
}

impl TryFrom<String> for Liveliness {
    type Error = ();

    fn try_from(value: String) -> Result<Self, Self::Error> {
        match value.as_str() {
            "LIVE" => Ok(Liveliness::LIVE),
            "ON NOW" => Ok(Liveliness::ON_NOW),
            "UPCOMING" => Ok(Liveliness::UPCOMING),
            "ENDED" => Ok(Liveliness::ENDED),
            "CANCELLED" => Ok(Liveliness::CANCELLED),
            "POSTPONED" => Ok(Liveliness::POSTPONED),
            "SUSPENDED" => Ok(Liveliness::SUSPENDED),
            _ => Err(()),
        }
    }
}

impl From<Liveliness> for String {
    fn from(value: Liveliness) -> Self {
        match value {
            Liveliness::LIVE => "LIVE".to_string(),
            Liveliness::ON_NOW => "ON NOW".to_string(),
            Liveliness::UPCOMING => "UPCOMING".to_string(),
            Liveliness::ENDED => "ENDED".to_string(),
            Liveliness::CANCELLED => "CANCELLED".to_string(),
            Liveliness::POSTPONED => "POSTPONED".to_string(),
            Liveliness::SUSPENDED => "SUSPENDED".to_string(),
        }
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct TitleCard {
    pub id: String,

    /// Index of this card in a container
    pub index: usize,

    /// Interaction action for this carousel
    pub action: TransitionAction,

    pub content_type: Option<String>,

    /// Whether customer is entitled to this title card
    pub is_entitled: bool,

    /// Title card entitlement message and icon
    pub entitlement_message: Option<EntitlementMessage>,

    /// Title metadata badge message and level
    pub title_metadata_badge: Option<TitleMetadataBadge>,

    pub genres: Vec<String>,

    pub gti: String,

    pub cover_image: Option<String>,

    pub liveliness: Option<Liveliness>,

    pub live_event_date_badge: Option<LiveEventDateTime>,

    pub live_event_date_header: Option<LiveEventDateTime>,

    pub title: Option<String>,

    pub synopsis: Option<String>,

    pub venue: Option<String>,

    pub widget_type: String,

    pub maturity_rating: Option<String>,

    pub start_time: Option<i64>,

    pub end_time: Option<i64>,

    pub is_in_watchlist: bool,
}

impl Id for TitleCard {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

pub type StoredReactionSignals = StoredValue<HashMap<String, ReactionSignals>>;

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct ReactionSignals {
    pub is_favorite: RwSignal<bool>,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct StationRowModelReactive {
    pub station: StationRowModel,
    pub airings_signal: RwSignal<Vec<AiringModel>>,
}

impl EpgRowModel for StationRowModelReactive {
    type Item = AiringModel;

    fn get_schedule(&self) -> Vec<TimeRange> {
        self.airings_signal.with_untracked(|airings| {
            airings
                .iter()
                .map(|airing| airing.get_time_range())
                .collect()
        })
    }

    fn has_header(&self) -> bool {
        self.station.index_in_epg_group == 0
    }
}

impl Id for StationRowModelReactive {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.station.id
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct AiringModel {
    /// Unique airing identifier. Combination of container id, station id and airing id.
    pub id: String,

    /// Airing title text
    pub title: String,

    /// Airing start time
    pub start_time: i64,

    /// Airing end time
    pub end_time: i64,

    /// Image url for EPG item
    pub image_url: Option<String>,

    /// Image url for background
    pub background_image_url: Option<String>,

    /// Airing episodic information
    pub context: Option<ShowContext>,

    /// Airing synopsis
    pub synopsis: Option<String>,

    /// Airing rating text. e.g. R, PG-13, 16+
    pub rating: Option<String>,

    /// Airing content descriptor. e.g. "Violence, Strong Language, Drug Use"
    pub content_descriptors: Vec<String>,

    /// Localized episodic title. Only available on Station Details page. e.g. S4 E13 - Face Off
    pub localized_hierarchy_context: Option<String>,

    /// Localized time range. Only available on Station Details page. e.g. 11:15 PM - 12:05 AM EDT
    pub localized_time_range: Option<String>,

    // Airing badges. Only available on Station Details page
    pub badges: Option<LinearBadges>,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct LinearBadges {
    pub apply_live: bool,
}

impl Schedule for AiringModel {
    fn get_time_range(&self) -> TimeRange {
        (self.start_time, self.end_time)
    }
}

impl Id for AiringModel {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

/// Types of renderable row on Live page.
#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum LivePageRow {
    /// Epg Station row
    Station(StationRowModel),

    /// Standard carousel row
    StandardCarousel(StandardCarouselModel),
}

#[allow(
    clippy::large_enum_variant,
    reason = "https://issues.amazon.com/issues/LR-Rust-628"
)]
#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum LivePageRowReactive {
    Station(StationRowModelReactive),
    StandardCarousel(StandardCarouselModelReactive),
}

impl Id for LivePageRowReactive {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            LivePageRowReactive::Station(station) => station.id(),
            LivePageRowReactive::StandardCarousel(carousel) => carousel.id(),
        }
    }
}

/// Enum to represent EPG station row and standard row in [`EpgModel`].
/// `StationRow` references station model to allow row model usage without cloning.
#[derive_test_only(Debug, PartialEq)]
pub enum RowType<'a, T: EpgRowModel + Clone> {
    /// `StationRow` is a horizontal list of airing schedule.
    /// `StandardRow` is any other non EPG row such as Standard Carousel or Hero Carousel.
    /// Each row set its total height and scroll offset from the top of the row
    StationRow(&'a T, f32, f32),
    StandardRow(f32, f32),
}

/// Trait to be implemented by model to represent a vertical list with station rows and standard rows.
pub trait EpgModel<'a> {
    type EpgRow: EpgRowModel + Clone;

    fn get_rows(&'a self) -> Vec<RowType<'a, Self::EpgRow>>;
}

/// Trait to be implemented by model for an EPG row component.
/// This trait provide a list of intervals for EPG row to render.
pub trait EpgRowModel {
    type Item: Schedule + Clone + Id;

    fn get_schedule(&self) -> Vec<TimeRange>;

    fn has_header(&self) -> bool;
}

#[derive(NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct LinearFilter {
    pub id: String,
    pub text: String,
    pub action: SubNavAction,
    pub isSelected: bool,
    pub tag: Option<String>,
}

impl Id for LinearFilter {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

pub type RenderItemFn<D, C, P> = Rc<dyn Fn(&AppContext, &D, P) -> C>;

#[derive(Clone, Copy, PartialEq)]
#[derive_test_only(Debug)]
pub struct EpgTimelineState {
    /// Start timestamp of EPG timeline
    pub start_time: i64,

    /// End timestamp of EPG timeline
    pub end_time: i64,

    /// Furthest timeblock index timeline allowed to scroll to
    pub max_scroll_index: usize,

    /// Duration in ms of visible portion of previous time block when horizontally scrolled.
    pub horizontal_scroll_peek_ms: i64,

    /// Timestamp of the EPG's end edge when horizontally scrolled to the end.
    pub last_visible_timestamp: i64,
}

#[derive(Clone, Copy, PartialEq)]
#[derive_test_only(Debug)]
pub struct EpgScrollState {
    /// Row index Epg currently vertically scrolled to.
    /// This value is set even when focus is not on the Epg.
    pub vertical_scroll_index: usize,

    /// Time block index where EPG currently horizontally scrolled to.
    /// E.g. 0 when EPG scrolled to start of timeline.
    pub timeblock_scroll_index: usize,

    /// Time block index to calculate airing to focus when navigating vertically.
    /// In most cases, this is the same as `timeblock_scroll_index`.
    /// At the right end of EPG, this value may be higher than `timeblock_scroll_index`.
    pub timeblock_focus_index: usize,
}

/// Tuple representing focus state of EPG.
/// (row index, item index, logo focused) for EPG row.
/// (row index, 0, false) for non-EPG row.
pub type EpgFocusState = (usize, usize, bool);

#[derive(Clone, Copy)]
pub struct EpgContext {
    pub timeline_state: Signal<EpgTimelineState>,
    pub scroll_state: RwSignal<EpgScrollState>,
    /// Positional focus state of Epg used for navigation calculation.
    /// This value is always set and is the initial `EpgItem` to receive focus.
    pub focus_state: RwSignal<EpgFocusState>,
    /// Each Epg item has unique focus key string. This is the id of the current focus state.
    /// This is used as preferred focus.
    pub focus_state_id: RwSignal<String>,
    /// Set this signal to an Epg item focus key to request focus.
    /// This signal is None when focus is not on an `EpgItem`.
    pub focus_signal: FocusValueSignal<String>,
    /// Inclusive vertical index range of visible rows
    pub visible_row_range: Signal<(usize, usize)>,
    /// Inclusive horizontal timestamp range of visible EPG timeline
    pub visible_time_range: Signal<(i64, i64)>,
}

#[derive(Clone, Copy)]
pub struct LinearTTSContext {
    // Signal to indicate a new row is focused and row-specific metadata should be spoken
    pub is_new_row: RwSignal<bool>,
    // Signal containing TTS messages to speak for currently focused content
    pub focused_metadata_messages: RwSignal<Vec<TextContent>>,
}

#[derive(Clone, Hash, PartialEq)]
#[derive_test_only(Debug)]
pub enum FocusedSection {
    Hero,
    Epg,
    FilterList,
}

#[derive(Clone, Hash)]
pub enum HoveredSection {
    Hero,
    Epg,
    FilterList,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum TransitionSource {
    HeroDetails,
    AiringCard,
    MoreInfoModal,
    StandardCarouselCard,
    StationDetails,
}

#[cfg(test)]
mod test {

    use super::*;
    use common_transform_types::{
        actions::PlaybackAction, container_items::EntitlementMessageIcons,
    };
    use ignx_compositron::app::launch_only_scope;

    fn build_airings() -> Vec<AiringModel> {
        vec![AiringModel {
            id: String::from("123"),
            title: String::from("Show One"),
            start_time: 1234567890,
            end_time: 2345678901,
            image_url: Some(String::from("https://image.com")),
            background_image_url: Some(String::from("https://image.com")),
            context: Some(ShowContext {
                episode: Some(1),
                season: Some(1),
                episodeTitle: Some(String::from("The Episode")),
            }),
            synopsis: Some(String::from("In this episode...")),
            rating: Some(String::from("R")),
            content_descriptors: vec![String::from("Violence, Strong Language, Drug Use")],
            localized_hierarchy_context: None,
            localized_time_range: None,
            badges: None,
        }]
    }

    fn build_station_row(airings: Vec<AiringModel>) -> StationRowModel {
        StationRowModel {
            id: String::from("srm_id"),
            station_gti: String::from("srm_gti"),
            station_name: String::from("Test Station"),
            logo_url: Some(String::from("https://image.com")),
            airings,
            is_entitled: false,
            entitlement_message: Some(EntitlementMessage {
                message: Some(String::from("watch with TS sub")),
                icon: Some(EntitlementMessageIcons::OFFER_ICON),
            }),
            action: TransitionAction::player(PlaybackAction::create_populated_action("gti")),
            epg_group_title: String::from("Test Stations"),
            index_in_epg_group: 1,
            container_index: 0,
            is_watch_history: false,
            analytics: None,
            widget_type: "linearStationCard".to_string(),
            reactions: Reactions { is_favorite: false },
        }
    }

    #[test]
    fn implements_station_row_reactive_as_epg_row() {
        launch_only_scope(|scope| {
            let station_row_reactive = StationRowModelReactive {
                station: build_station_row(vec![]),
                airings_signal: create_rw_signal(scope, build_airings()),
            };

            let schedule = station_row_reactive.get_schedule();

            assert_eq!(schedule, vec![(1234567890, 2345678901)]);
        });
    }

    #[test]
    fn converts_station_row_model_to_station_hero_model() {
        let station_row_model = build_station_row(build_airings());

        assert_eq!(
            StationHeroModel {
                station_gti: "srm_gti".to_string(),
                station_logo: Some("https://image.com".to_string()),
                station_name: "Test Station".to_string(),
                airings: build_airings(),
                entitlement_message: Some(EntitlementMessage {
                    message: Some(String::from("watch with TS sub")),
                    icon: Some(EntitlementMessageIcons::OFFER_ICON),
                }),
                button_label: None,
                action: TransitionAction::player(PlaybackAction::create_populated_action("gti")),
                widget_type: "linearStationCard".to_string(),
                analytics: None,
                reactions: Reactions { is_favorite: false }
            },
            station_row_model.into(),
        );
    }

    #[test]
    fn converts_string_to_liveliness() {
        assert_eq!(
            Liveliness::try_from("LIVE".to_string()),
            Ok(Liveliness::LIVE)
        );
        assert_eq!(
            Liveliness::try_from("ON NOW".to_string()),
            Ok(Liveliness::ON_NOW)
        );
        assert_eq!(
            Liveliness::try_from("UPCOMING".to_string()),
            Ok(Liveliness::UPCOMING)
        );
        assert_eq!(
            Liveliness::try_from("ENDED".to_string()),
            Ok(Liveliness::ENDED)
        );
        assert_eq!(
            Liveliness::try_from("CANCELLED".to_string()),
            Ok(Liveliness::CANCELLED)
        );
        assert_eq!(
            Liveliness::try_from("POSTPONED".to_string()),
            Ok(Liveliness::POSTPONED)
        );
        assert_eq!(
            Liveliness::try_from("SUSPENDED".to_string()),
            Ok(Liveliness::SUSPENDED)
        );
        assert_eq!(Liveliness::try_from("UNKNOWN".to_string()), Err(()));
    }

    #[test]
    fn converts_liveliness_to_string() {
        assert_eq!(String::from(Liveliness::LIVE), "LIVE");
        assert_eq!(String::from(Liveliness::ON_NOW), "ON NOW");
        assert_eq!(String::from(Liveliness::UPCOMING), "UPCOMING");
        assert_eq!(String::from(Liveliness::ENDED), "ENDED");
        assert_eq!(String::from(Liveliness::CANCELLED), "CANCELLED");
        assert_eq!(String::from(Liveliness::POSTPONED), "POSTPONED");
        assert_eq!(String::from(Liveliness::SUSPENDED), "SUSPENDED");
    }
}
