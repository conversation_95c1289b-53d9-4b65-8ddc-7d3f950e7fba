use crate::constants::*;
use crate::metrics::csm_reporter::send_filter_selected_event;
use crate::types::*;
use crate::utils::common_string_ids::{
    AV_LRC_LIVE_TV_FILTERS_MENU, AV_LRC_LIVE_TV_NAVIGATION_FROM_FILTERS,
    AV_LRC_TTS_BUTTON_IN_COLLECTION_INDEX_MESSAGE, AV_LRC_TTS_NAVIGATION_NOT_POSSIBLE,
};
use fableous::animations::fable_motion_persistent_standard;
use fableous::buttons::toggle_button::*;
use ignx_compositron::input::KeyCode;
use ignx_compositron::prelude::*;
use ignx_compositron::text::LocalizedText;
use ignx_compositron::text::SubstitutionParameters;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, Composer};
use std::collections::HashMap;
use std::rc::Rc;
use std::time::Duration;

/**
* This component renders the filter list.
* It accepts the filterList data and renders the filterListComponent.
*/
#[Composer]
pub fn FilterList(
    ctx: &AppContext,
    #[into] filters: MaybeSignal<Vec<LinearFilter>>,
    #[into] height: MaybeSignal<f32>,
    selected_filter_id: RwSignal<Option<String>>,
    on_filter_select: Rc<dyn Fn(Option<String>, Option<String>)>,
    bubble_up: Rc<dyn Fn(KeyCode)>,
    focused_index: FocusValueSignal<usize>,
) -> ColumnComposable {
    let (scope_guard, _) = create_signal(ctx.scope(), ());

    let list_length = filters.with(|f| f.len()) as f32;
    // TODO: Add test to verify initial selected filter
    let (selected_index, set_selected_index) = create_signal(ctx.scope(), {
        if let Some(id) = selected_filter_id.get_untracked() {
            filters
                .with_untracked(|filters| filters.iter().position(|filter| filter.id == id))
                .unwrap_or(0)
        } else {
            0
        }
    });

    // Extract needed info from the first filter so we can select it on backstack
    let (first_filter_id, first_service_token, first_filter_tag) =
        if let Some(filter) = filters.get().first() {
            (
                Some(filter.id.clone()),
                filter.action.serviceToken.clone(),
                filter.tag.clone(),
            )
        } else {
            (None, None, None)
        };

    // Set initial scroll position of the filter list
    let scroll_to = ScrollTo::Index(
        selected_index.get_untracked() as u32,
        Pivot::Start,
        Animation::default().with_duration(Duration::ZERO),
    );

    // Setup closure to perform backstack action
    let on_filter_select_clone = on_filter_select.clone();
    let bubble_up_clone = bubble_up.clone();
    let perform_backstack = Rc::new(move |keyCode: KeyCode| {
        if scope_guard.try_get_untracked().is_none() {
            // scope has been disposed
            return;
        }

        // Select the All filter if not already selected
        if selected_index.get_untracked() != 0 {
            set_selected_index.set(0);
            selected_filter_id.set(first_filter_id.clone());
            on_filter_select_clone(first_service_token.clone(), first_filter_tag.clone());
        }
        // Reset focus index and bubble up
        focused_index.set(0);
        bubble_up_clone(keyCode)
    });

    compose! {
        Column() {
            ColumnList(items: filters, item_builder: Rc::new(move |ctx: &AppContext, item: &LinearFilter, index| {
                let scope = ctx.scope();
                let on_filter_select = on_filter_select.clone();
                let perform_backstack_backspace = perform_backstack.clone();
                let perform_backstack_escape = perform_backstack.clone();
                let filter_id = item.id.clone();
                let service_token = item.action.serviceToken.clone();
                let filter_tag = item.tag.clone();
                let action = item.action.clone();
                let is_filter_selected = Signal::derive(scope, move || selected_index.get() == index);
                let bubble_up = Rc::clone(&bubble_up);

                compose! {
                    ToggleButton(variant: ToggleButtonVariant::TextSize400(TextContent::String(item.text.clone())), selected: is_filter_selected)
                    .accessibility_description(item.text.clone())
                    .accessibility_role(LocalizedText {
                        string_id: AV_LRC_TTS_BUTTON_IN_COLLECTION_INDEX_MESSAGE.into(),
                        substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                            ("currentIndex".to_owned(), TextContent::String((index + 1).to_string())),
                            ("totalNumber".to_owned(), TextContent::String(list_length.to_string())),
                        ]))),
                    })
                    .focused_value(focused_index, index)
                    .preferred_focus(is_filter_selected)
                    .on_select(move || {
                        if selected_index.get_untracked() != index {
                            set_selected_index.set(index);
                            selected_filter_id.set(Some(filter_id.clone()));
                            on_filter_select(service_token.clone(), filter_tag.clone());
                            send_filter_selected_event(scope, action.pageId.clone(), action.pageType.clone(), action.refMarker.clone());
                        }
                    })
                    .on_key_down(KeyCode::Up, move || {
                        if index == 0 {
                            bubble_up(KeyCode::Up)
                        }
                    })
                    .on_key_down(KeyCode::Backspace, move || {
                        perform_backstack_backspace(KeyCode::Backspace)
                    })
                    .on_key_down(KeyCode::Escape, move || {
                        perform_backstack_escape(KeyCode::Escape)
                    })
                    .test_id(format!("filter_item_{}", index))
                }
            }))
            .scroll_to(scroll_to)
            .auto_scroll_lock_edges(AutoScroll::Pivot(Pivot::Start, fable_motion_persistent_standard()))
            .padding(FILTER_LIST_PADDING)
            .main_axis_alignment(FILTER_LIST_BUTTONS_SPACING)
            .width(FILTER_LIST_CONTAINER_WIDTH)
            .height(height)
            .overflow_behavior(OverflowBehavior::Hidden)
            .focus_hierarchical_container(NavigationStrategy::Vertical)
            .focus_scope()
            .accessibility_hint(LocalizedText::new(AV_LRC_LIVE_TV_NAVIGATION_FROM_FILTERS))
            .accessibility_context_message(LocalizedText::new(AV_LRC_LIVE_TV_FILTERS_MENU))
            .accessibility_navigation_not_possible_message(LocalizedText::new(AV_LRC_TTS_NAVIGATION_NOT_POSSIBLE))
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::mocks::mock_data_util::build_static_filter_list;
    use crate::network::types::SubNavAction;
    use crate::utils::test_utils::{assert_node_focused, press_key, press_select};
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::*;
    use rstest::*;
    use std::cell::Cell;

    #[test]
    fn it_renders() {
        launch_test(
            |ctx| {
                compose! {
                    FilterList(
                        filters:MaybeSignal::Static(build_static_filter_list()),
                        height: 800.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), None),
                        on_filter_select: Rc::new(|_, _| ()),
                        bubble_up: Rc::new(|_| ()),
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let list = tree
                    .find_by_props()
                    .composable_type(ComposableType::ColumnList)
                    .find_first();

                assert_node_exists!(&list);
            },
        )
    }

    #[test]
    fn it_calls_select_callback_on_press() {
        let select_called = Rc::new(Cell::new(None));

        let on_filter_select = {
            let select_called = select_called.clone();
            Rc::new(move |service_token: Option<String>, tag: Option<String>| {
                select_called.replace(Some((service_token, tag)));
            })
        };

        launch_test(
            |ctx| {
                compose! {
                    FilterList(
                        filters: MaybeSignal::Static(build_static_filter_list()),
                        height: 800.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), None),
                        on_filter_select,
                        bubble_up: Rc::new(|_| ()),
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            move |_scope, mut test_loop| {
                // Simulate second filter selection event
                press_key(&mut test_loop, "filter_item_0", KeyCode::Down);
                press_select(&mut test_loop, "filter_item_1");

                // Asserting that the correct service token is captured
                assert_eq!(
                    select_called.take(),
                    Some((Some("Movies_service_token".to_string()), None))
                );

                // Simulate first filter selection event
                press_key(&mut test_loop, "filter_item_1", KeyCode::Down);
                press_key(&mut test_loop, "filter_item_2", KeyCode::Down);
                press_select(&mut test_loop, "filter_item_3");

                // Asserting that the correct service token and tag are captured
                assert_eq!(
                    select_called.take(),
                    Some((
                        Some("Favorites_service_token".to_string()),
                        Some("linear-live-tv-favorites-filter".to_string())
                    ))
                );
            },
        );
    }

    #[test]
    fn it_does_not_call_select_callback_on_selecting_same_filter() {
        let select_called = Rc::new(Cell::new(None));

        let on_filter_select = {
            let select_called = select_called.clone();
            Rc::new(move |service_token: Option<String>, _tag: Option<String>| {
                select_called.replace(service_token);
            })
        };

        launch_test(
            |ctx| {
                compose! {
                    FilterList(
                        filters: MaybeSignal::Static(build_static_filter_list()),
                        height: 800.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), None),
                        on_filter_select,
                        bubble_up: Rc::new(|_| ()),
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            move |_scope, mut test_loop| {
                // Simulate first filter selection event
                press_select(&mut test_loop, "filter_item_0");

                //Asseting that select call back was not called.
                assert_eq!(select_called.take(), None);

                // Simulate second filter selection event
                press_key(&mut test_loop, "filter_item_0", KeyCode::Down);
                press_select(&mut test_loop, "filter_item_1");

                // Asserting that the correct service token is captured
                assert_eq!(
                    select_called.take(),
                    Some("Movies_service_token".to_string())
                );

                // Emptying the select_called so that we can assert that the select callback was not called.
                select_called.replace(None);
                press_select(&mut test_loop, "filter_item_1");

                // Asseting that select call back was not called since the same filter was slected again.
                assert_eq!(select_called.take(), None);
            },
        );
    }

    #[test]
    fn it_calls_bubble_up_when_press_up_on_first_button() {
        let bubbled_key = Rc::new(Cell::new(None));
        let bubbled_key_clone = Rc::clone(&bubbled_key);

        let bubble_up = Rc::new(move |key: KeyCode| {
            bubbled_key_clone.replace(Some(key));
        });

        launch_test(
            |ctx| {
                compose! {
                    FilterList(
                        filters: MaybeSignal::Static(build_static_filter_list()),
                        height: 800.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), None),
                        on_filter_select: Rc::new(|_, _| ()),
                        bubble_up,
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            move |_scope, mut test_loop| {
                // Press Up on second button does not bubble
                press_key(&mut test_loop, "filter_item_0", KeyCode::Down);
                assert_eq!(bubbled_key.take(), None);
                press_key(&mut test_loop, "filter_item_1", KeyCode::Up);
                assert_eq!(bubbled_key.take(), None);

                // Press Up on first button does bubble
                press_key(&mut test_loop, "filter_item_0", KeyCode::Up);
                assert_eq!(bubbled_key.take(), Some(KeyCode::Up));
            },
        );
    }

    #[rstest]
    #[case(KeyCode::Backspace)]
    #[case(KeyCode::Escape)]
    fn handles_back_stack(#[case] back_key: KeyCode) {
        let select_called = Rc::new(Cell::new(None));

        let on_filter_select = {
            let select_called = select_called.clone();
            Rc::new(move |service_token: Option<String>, tag: Option<String>| {
                select_called.replace(Some((service_token, tag)));
            })
        };

        let bubbled_key = Rc::new(Cell::new(None));
        let bubbled_key_clone = Rc::clone(&bubbled_key);

        let bubble_up = Rc::new(move |key: KeyCode| {
            bubbled_key_clone.replace(Some(key));
        });

        let back_key_clone_1 = back_key.clone();
        let back_key_clone_2 = back_key.clone();
        let back_key_clone_3 = back_key.clone();
        let back_key_clone_4 = back_key;

        launch_test(
            |ctx| {
                compose! {
                    FilterList(
                        filters: MaybeSignal::Static(build_static_filter_list()),
                        height: 800.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), None),
                        on_filter_select,
                        bubble_up,
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            move |_scope, mut test_loop| {
                // Focus and select the third filter
                press_key(&mut test_loop, "filter_item_0", KeyCode::Down);
                press_key(&mut test_loop, "filter_item_1", KeyCode::Down);
                press_select(&mut test_loop, "filter_item_2");

                // Press Back on third button
                press_key(&mut test_loop, "filter_item_2", back_key_clone_1);

                // Assert first filter is focused, selected, and bubble up is called
                assert_node_focused(&mut test_loop, "filter_item_0");
                let captured_params = select_called.take();
                assert_eq!(
                    captured_params,
                    Some((Some("All_service_token".to_string()), None))
                );
                assert_eq!(bubbled_key.take(), Some(back_key_clone_2));

                // Press Back on first button bubbles up
                press_key(&mut test_loop, "filter_item_0", back_key_clone_3);
                assert_eq!(bubbled_key.take(), Some(back_key_clone_4));
            },
        );
    }

    #[test]
    fn it_renders_button() {
        launch_test(
            |ctx| {
                compose! {
                    FilterList(
                        filters:MaybeSignal::Static(build_static_filter_list()),
                        height: 800.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), None),
                        on_filter_select: Rc::new(|_, _| ()),
                        bubble_up: Rc::new(|_| ()),
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let button = tree.find_by_test_id("filter_item_0");

                assert_node_exists!(&button);
            },
        )
    }

    #[test]
    fn it_renders_label_and_contents() {
        launch_test(
            |ctx| {
                compose! {
                    FilterList(
                        filters:MaybeSignal::Static(build_static_filter_list()),
                        height: 800.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), None),
                        on_filter_select: Rc::new(|_, _| ()),
                        bubble_up: Rc::new(|_| ()),
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let contents: Vec<Option<String>> = tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .find_all()
                    .drain(..)
                    .map(|node| node.get_props().text)
                    .collect();
                assert_eq!(
                    contents,
                    vec![
                        Some(String::from("All")),
                        Some(String::from("Movies")),
                        Some(String::from("Subscribe")),
                        Some(String::from("Favorites"))
                    ]
                );
            },
        )
    }

    #[test]
    fn it_scrolls_when_initial_filter_provided() {
        launch_test(
            |ctx| {
                let mut filter_list = vec![];
                for n in 1..25 {
                    filter_list.push(LinearFilter {
                        text: format!("text_{}", n),
                        id: format!("id_{}", n),
                        action: SubNavAction {
                            analytics: HashMap::new(),
                            pageId: "All_live".to_string(),
                            pageType: "home".to_string(),
                            refMarker: "hm_liv_LRd40aee_slct".to_string(),
                            serviceToken: Some("All_service_token".to_string()),
                            target: "live".to_string(),
                            text: None,
                        },
                        isSelected: false,
                        tag: None,
                    })
                }

                compose! {
                    FilterList(
                        filters:MaybeSignal::Static(filter_list),
                        height: 400.0,
                        selected_filter_id: create_rw_signal(ctx.scope(), Some("id_15".to_string())), // initial filter set
                        on_filter_select: Rc::new(|_, _| ()),
                        bubble_up: Rc::new(|_| ()),
                        focused_index: create_focus_value_signal::<usize>(ctx.scope())
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Verify first filter is missing and initial filter visible
                let visible_contents: Vec<Option<String>> = tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .is_visible()
                    .find_all()
                    .drain(..)
                    .map(|node| node.get_props().text)
                    .collect();
                assert!(visible_contents.contains(&Some(String::from("text_15"))));
                assert!(!visible_contents.contains(&Some(String::from("text_1"))));
            },
        )
    }
}
