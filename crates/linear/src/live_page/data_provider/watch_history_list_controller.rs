use crate::metrics::live_page_reporter;
use crate::types::{
    EpgFocusState, LivePageRow, Reactions, StationHeroModel, StationRowModel,
    StoredReactionSignals, TransitionSource,
};
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::TransitionAction;
use ignx_compositron::prelude::SignalGetUntracked;
use std::collections::{HashMap, HashSet};

/// This controller is responsible for modifying watch history EPG group based on local user action
/// without network call. This EPG group is commonly known as "Recently watched".
/// Watch history EPG group is identified by `watchHistory` tag.
/// This controller modifies content list before displaying on Live page by replacing contiguous "watch history" rows
/// with local state of watch history.
///
/// This controller modifies watch history rows following these rules:
/// 1) On initial load, watch history is deduped with hero station to reduce content duplicate. It is still tracked in
///    the watch history state and will display once hero changes to a different station.
/// 2) On entering playback from <PERSON>, the station is added to watch history.
///    If it is already in watch history, it is reordered to the top as most recent.
/// 3) On entering playback from EPG from watch history row, the station recency rank is adjusted, but the list is not yet reorderd.
///    This is to avoid confusion from reordering content while customer is focused on it.
/// 4) On entering playback from EPG from non-watch history row, the station recency rank is adjusted, and the list is sorted.
///    This will also sort any previous ranking that has not been sorted.
///
/// This logic is implemented by maintain local watch history in `WatchHistoryState`.
/// This state is updated on entering playback with `update_on_playback`
/// and the controller uses the state to modify content list with `modify_content_list`.
const WATCH_HISTORY_LIST_MIN_ITEM: usize = 3;
const WATCH_HISTORY_CONTAINER_ID_PREFIX: &str = "WATCH_HISTORY_";

#[derive_test_only(Clone, Debug, PartialEq)]
pub struct WatchHistoryState {
    /// Whether watch history list is initialized with watch history from network call
    is_watch_history_list_initialized: bool,

    /// Watch history EPG group title
    title: Option<String>,

    /// Watch history EPG group analytics
    analytics: Option<HashMap<String, String>>,

    /// Current hero station gti
    hero_station_gti: Option<String>,

    /// Tracks watch history station row and recency ranking.
    /// These station models are duplicate of live page content list, but impact is neglectable
    /// compared to station rows on live page.
    watch_history: Vec<(StationRowModel, usize)>,

    /// Recency rank number to use for the next station to add to watch history
    next_wh_rank: usize,

    /// In some cases, a station in watch history is hidden but still tracked in watch history.
    /// When this is true, the first station in `watch_history` will not be displayed on live page.
    omit_most_recent: bool,

    /// Maximum item count of the watch history container. This will be initialized with initial item count of the container.
    max_item_count: Option<usize>,
}

pub fn initialize_state(hero: Option<&StationHeroModel>) -> WatchHistoryState {
    WatchHistoryState {
        is_watch_history_list_initialized: false,
        title: None,
        analytics: None,
        hero_station_gti: hero.map(|hero| hero.station_gti.clone()),
        watch_history: vec![],
        next_wh_rank: 1,
        omit_most_recent: false,
        max_item_count: None,
    }
}

/// Update watch history rows in the given content list
/// This only replaces contiguous watch history rows with new watch history. Other rows remain unchanged.
// # Arguments
//   `content_list` - List of live page rows to update
//   `epg_focus_state` - Focus state to adjust from additional watch history row
//   `state` - watch history state
pub fn modify_content_list(
    content_list: &mut Vec<LivePageRow>,
    epg_focus_state: &mut Option<EpgFocusState>,
    state: &mut WatchHistoryState,
) {
    // Initialize focus state to modify
    let (mut new_focused_row, ..) = epg_focus_state.unwrap_or((0, 0, false));

    // Track watch history rows not yet added to result list
    let mut current_wh_rows: Vec<StationRowModel> = vec![];
    // Process the watch history rows and add to the result list
    let process_current_wh_rows = |result: &mut Vec<LivePageRow>,
                                   state: &mut WatchHistoryState,
                                   current_wh_rows: &mut Vec<StationRowModel>,
                                   focused_row: &mut usize| {
        if !current_wh_rows.is_empty() {
            let rows_to_replace_length = current_wh_rows.len();
            // Consume watch history rows so far to get new watch history rows
            let new_wh_rows = get_watch_history_to_display(std::mem::take(current_wh_rows), state);
            let new_length = new_wh_rows.len();
            // If focused row is after the current result length, then it needs to move down by additional rows.
            if *focused_row >= result.len() {
                *focused_row += new_length.saturating_sub(rows_to_replace_length);
            }
            // Append watch history list to result
            result.extend(new_wh_rows);
            live_page_reporter::on_watch_history_rows_modified(new_length);
        }
    };

    let original_content: Vec<LivePageRow> = std::mem::take(content_list);
    let original_length = original_content.len();
    // Drain content list and iterate through. Add rows to the input content list.
    for row in original_content {
        // If current row is a watch history row, add to tracking list.
        // Otherwise, process watch history rows so far, then add current row.
        match row {
            LivePageRow::Station(station) => {
                if station.is_watch_history {
                    current_wh_rows.push(station);
                } else {
                    process_current_wh_rows(
                        content_list,
                        state,
                        &mut current_wh_rows,
                        &mut new_focused_row,
                    );
                    content_list.push(LivePageRow::Station(station));
                }
            }
            LivePageRow::StandardCarousel(_) => {
                process_current_wh_rows(
                    content_list,
                    state,
                    &mut current_wh_rows,
                    &mut new_focused_row,
                );
                content_list.push(row);
            }
        };
    }
    process_current_wh_rows(
        content_list,
        state,
        &mut current_wh_rows,
        &mut new_focused_row,
    );
    live_page_reporter::on_watch_history_modify_content_list(
        content_list.len().abs_diff(original_length),
    );

    // Update given focus state
    if let Some((focus_row_index, ..)) = epg_focus_state.as_mut() {
        *focus_row_index = new_focused_row;
    }
}

/// Initialize watch history list if needed. Compute and return watch history rows to display
fn get_watch_history_to_display(
    input_wh_list: Vec<StationRowModel>,
    state: &mut WatchHistoryState,
) -> Vec<LivePageRow> {
    initialize_watch_history_list(input_wh_list, state);

    let mut watch_history_to_display = state.watch_history.iter();

    // Hide the most recent station, but still keep it in the state's watch history.
    // Do not hide if watch history only has one station. Otherwise, the list will disappear.
    // This can happen if user only has one station in history and it is also the hero station.
    if state.omit_most_recent && state.watch_history.len() > 1 {
        watch_history_to_display.next();
    }

    let container_ref_marker = state
        .analytics
        .as_ref()
        .and_then(|analytics| analytics.get("refMarker"));
    // Clone station from state, then modify title, analytics, and index
    let mut watch_history_to_display: Vec<LivePageRow> = watch_history_to_display
        .enumerate()
        .map(|(index, (station, _))| {
            let mut station = station.clone();
            station.epg_group_title = state.title.clone().unwrap_or_default();
            station.analytics = state.analytics.clone();
            station.index_in_epg_group = index;
            // Replace/adjust action refMarker if container has refMarker
            if let Some(container_ref_marker) = container_ref_marker {
                if let TransitionAction::player(action) = &mut station.action {
                    action.refMarker = format!("{}_{}", container_ref_marker, index + 1)
                }
            }
            LivePageRow::Station(station)
        })
        .collect();
    if let Some(max_item_count) = state.max_item_count {
        watch_history_to_display.truncate(max_item_count);
    }

    watch_history_to_display
}

/// Initialize `WatchHistoryState` `watch_history` list.
/// It is possible for user to enter playback before the state is initialized with watch history from content list.
/// E.g. User enter live page to specific filter which does not have watch history container, then user enter playback.
/// This function reconcile input watch history list from the content list and
/// watch history in the state from user playback action.
fn initialize_watch_history_list(
    input_wh_list: Vec<StationRowModel>,
    state: &mut WatchHistoryState,
) {
    if state.is_watch_history_list_initialized {
        return;
    }
    // Get EPG group title and analytics from content list input
    let title = input_wh_list
        .first()
        .map(|station| station.epg_group_title.clone());
    let analytics = input_wh_list
        .first()
        .and_then(|station| station.analytics.clone());
    let max_item_count = input_wh_list.len().max(WATCH_HISTORY_LIST_MIN_ITEM);

    // Collect and dedupe watch history list
    let mut watch_history_set: HashSet<String> = HashSet::new();
    let mut new_watch_history: Vec<(StationRowModel, usize)> = vec![];
    // This is the first time user will see watch history list.
    // The hero station in watch history get special treatment.
    let mut hero_dedupe_station = None;

    // Join state and input watch history
    // State's watch history first since it is from recent user activity
    let station_list_to_process = state
        .watch_history
        .drain(..)
        .map(|(station, _)| station)
        .chain(input_wh_list);
    for station in station_list_to_process {
        // If a station is the same as hero, handle separately
        if state
            .hero_station_gti
            .as_ref()
            .is_some_and(|hero_gti| hero_gti == &station.station_gti)
        {
            if hero_dedupe_station.is_none() {
                hero_dedupe_station = Some(station);
            }
            continue;
        }

        if watch_history_set.insert(station.station_gti.clone()) {
            new_watch_history.push((station, 0));
        }
    }
    // Add hero station as most recent but omit it for now to reduce duplicate content.
    // It will display in the list once hero is switched to another station.
    if let Some(hero_station) = hero_dedupe_station {
        new_watch_history.insert(0, (hero_station, 0));
        state.omit_most_recent = true;
    }
    // Truncate with max size + 1 since a station may be omitted
    new_watch_history.truncate(max_item_count + if state.omit_most_recent { 1 } else { 0 });

    state.title = title;
    state.analytics = analytics;
    state.watch_history = new_watch_history;
    state.is_watch_history_list_initialized = true;
    state.max_item_count = Some(max_item_count);
}

/// Update watch history state when entering playback
// # Arguments
//   `state` - Watch history state to update
//   `hero` - Current hero
//   `content_list` - Content list displayed on live page
//   `epg_focus_state` - Current focus state of the EPG
//   `transition_source` - Where user enter playback from
fn update_on_playback(
    state: &mut WatchHistoryState,
    hero: &Option<StationHeroModel>,
    content_list: &[LivePageRow],
    epg_focus_state: EpgFocusState,
    transition_source: &TransitionSource,
) {
    // Get playback station from hero or content list
    let playback_station = if matches!(transition_source, TransitionSource::HeroDetails) {
        hero.as_ref().map(get_station_row_from_hero)
    } else {
        let (focused_row_index, ..) = epg_focus_state;
        content_list
            .get(focused_row_index)
            .and_then(|row| match row {
                LivePageRow::Station(station) => Some(station.clone()),
                LivePageRow::StandardCarousel(_) => None,
            })
    };
    let Some(mut playback_station) = playback_station else {
        return;
    };

    let station_gti = playback_station.station_gti.clone();
    let is_playback_from_watch_history = playback_station.is_watch_history;
    live_page_reporter::on_playback(is_playback_from_watch_history);

    let mut watch_history = state.watch_history.drain(..).collect::<Vec<_>>();
    // Find the playback station in watch history
    let station_in_wh = watch_history
        .iter_mut()
        .find(|(station, _)| station.station_gti == station_gti);

    // If already in the list, then update order rank
    // Otherwise, add to the list
    if let Some((_, rank)) = station_in_wh {
        *rank = state.next_wh_rank;
    } else {
        modify_station_row_for_watch_history(&mut playback_station);
        watch_history.push((playback_station, state.next_wh_rank));
    }

    // If user enter playback from watch history row, do not reorder since user is still engaging with it.
    // Otherwise sort entire list with highest rank first.
    // This will also sort any previous playback action that has not been sorted.
    if !is_playback_from_watch_history {
        watch_history.sort_by(|a, b| b.1.cmp(&a.1))
    }
    // Truncate with max size + 1 since a station may be omitted
    if let Some(max_item_count) = state.max_item_count {
        watch_history.truncate(max_item_count);
    }

    state.hero_station_gti = Some(station_gti);
    state.watch_history = watch_history;
    state.omit_most_recent = false;
    state.next_wh_rank += 1;
}

/// Update watch history state when caching page
// # Arguments
//   `state` - Watch history state to update
//   `hero` - Current hero
//   `content_list` - Content list displayed on live page
//   `epg_focus_state` - Current focus state of the EPG
//   `transition_source` - Where user enter new page from
//   `reaction_signals` - Map of station reactions
//   `to_playback`  - Update watch history list if going to playback
pub fn update_on_cache_page(
    state: &mut WatchHistoryState,
    hero: &Option<StationHeroModel>,
    content_list: &[LivePageRow],
    epg_focus_state: EpgFocusState,
    transition_source: &TransitionSource,
    reaction_signals: StoredReactionSignals,
    to_playback: bool,
) {
    // Update for playback if necessary
    if to_playback {
        update_on_playback(
            state,
            hero,
            content_list,
            epg_focus_state,
            transition_source,
        );
    }

    // Update the reaction state of watch history stations
    reaction_signals.with_value(|reactions_map| {
        state.watch_history = state
            .watch_history
            .iter()
            .map(|(station, index)| {
                let mut updated_station = station.clone();
                updated_station.reactions = Reactions {
                    is_favorite: reactions_map
                        .get(&station.station_gti)
                        .is_some_and(|signals| signals.is_favorite.get_untracked()),
                };
                (updated_station, *index)
            })
            .collect();
    })
}

fn get_station_row_from_hero(hero: &StationHeroModel) -> StationRowModel {
    StationRowModel {
        id: String::default(),
        station_gti: hero.station_gti.clone(),
        station_name: hero.station_name.clone(),
        logo_url: hero.station_logo.clone(),
        airings: hero.airings.clone(),
        is_entitled: true,
        reactions: hero.reactions.clone(),
        entitlement_message: hero.entitlement_message.clone(),
        action: hero.action.clone(),
        is_watch_history: false,
        widget_type: hero.widget_type.clone(),
        // Title, index, and analytics will be set before returning station to display
        epg_group_title: String::default(),
        index_in_epg_group: 0,
        analytics: None,
        // TODO: Replace with CSM controller implementation
        container_index: 0,
    }
}

fn modify_station_row_for_watch_history(station: &mut StationRowModel) {
    // Modify row and airings id to ensure uniqueness
    station.id = format!(
        "{}{}",
        WATCH_HISTORY_CONTAINER_ID_PREFIX, station.station_gti
    );
    for (index, airing) in station.airings.iter_mut().enumerate() {
        airing.id = format!("{}_{}", station.id, index);
    }
    station.is_watch_history = true;
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::metrics::linear_reporter::mock_base_reporter::*;
    use crate::metrics::linear_reporter::mock_utils::mock_linear_metrics;
    use crate::mocks::mock_data_util::*;
    use crate::network::traits::mutex::MTX;
    use crate::types::ReactionSignals;
    use crate::utils::context_utils::get_stored_reaction_signals;
    use ignx_compositron::{
        app::launch_only_scope, prelude::create_rw_signal, reactive::store_value,
    };
    use mockall::predicate::eq;
    use rstest::rstest;

    #[test]
    fn updates_content_list_after_user_actions_when_watch_history_has_hero() {
        let _lock = MTX.lock();
        let _reporter_ctx = mock_linear_metrics();

        // Setup initial content with hero station also in watch history
        let hero = fake_hero_station("station3");
        let mut content_list = fake_content_list();
        let mut state = initialize_state(hero.as_ref());

        // Render initial page. Verify station3 is hidden in watch history
        modify_content_list(&mut content_list, &mut None, &mut state);
        assert_eq!(
            get_content_list_summary(&content_list),
            vec![
                ("standard1", "", 0),
                ("station2", "Recently watched", 0),
                ("station4", "Recently watched", 1),
                ("station5", "station5", 0),
                ("station6", "station6", 0),
                ("station3", "station3", 0),
                ("station4", "station4", 0)
            ]
        );

        // User watch station4 from watch history list
        let mut focus_state = Some((2, 0, false));
        update_on_playback(
            &mut state,
            &hero,
            &content_list,
            focus_state.unwrap(),
            &TransitionSource::AiringCard,
        );
        // Render content. Verify station3 appear and focus move down by 1 row to stay on station4
        modify_content_list(&mut content_list, &mut focus_state, &mut state);
        assert_eq!(
            get_content_list_summary(&content_list),
            vec![
                ("standard1", "", 0),
                ("station3", "Recently watched", 0),
                ("station2", "Recently watched", 1),
                ("station4", "Recently watched", 2),
                ("station5", "station5", 0),
                ("station6", "station6", 0),
                ("station3", "station3", 0),
                ("station4", "station4", 0)
            ]
        );
        assert_eq!(focus_state, Some((3, 0, false)));

        // User watch station5
        let mut focus_state = Some((4, 0, false));
        update_on_playback(
            &mut state,
            &hero,
            &content_list,
            focus_state.unwrap(),
            &TransitionSource::AiringCard,
        );
        // Render content. Verify station5 appear, list sorted
        modify_content_list(&mut content_list, &mut focus_state, &mut state);
        assert_eq!(
            get_content_list_summary(&content_list),
            vec![
                ("standard1", "", 0),
                ("station5", "Recently watched", 0),
                ("station4", "Recently watched", 1),
                ("station3", "Recently watched", 2),
                ("station5", "station5", 0),
                ("station6", "station6", 0),
                ("station3", "station3", 0),
                ("station4", "station4", 0)
            ]
        );
        assert_eq!(focus_state, Some((4, 0, false)));

        // User watch station3 from far down the EPG
        let mut focus_state = Some((6, 0, false));
        update_on_playback(
            &mut state,
            &hero,
            &content_list,
            focus_state.unwrap(),
            &TransitionSource::MoreInfoModal,
        );
        // Render content. Verify list sorted and focus does not move.
        modify_content_list(&mut content_list, &mut focus_state, &mut state);
        assert_eq!(
            get_content_list_summary(&content_list),
            vec![
                ("standard1", "", 0),
                ("station3", "Recently watched", 0),
                ("station5", "Recently watched", 1),
                ("station4", "Recently watched", 2),
                ("station5", "station5", 0),
                ("station6", "station6", 0),
                ("station3", "station3", 0),
                ("station4", "station4", 0)
            ]
        );
        assert_eq!(focus_state, Some((6, 0, false)));
    }

    #[test]
    fn updates_content_list_after_user_actions_when_watch_history_does_not_have_hero() {
        let _lock = MTX.lock();
        let _reporter_ctx = mock_linear_metrics();

        // Setup initial content with hero station not in watch history
        let hero = fake_hero_station("hero");
        let mut content_list = fake_content_list();
        let mut state = initialize_state(hero.as_ref());

        // Render initial page. Verify content has not changed
        modify_content_list(&mut content_list, &mut None, &mut state);
        assert_eq!(
            get_content_list_summary(&content_list),
            vec![
                ("standard1", "", 0),
                ("station2", "Recently watched", 0),
                ("station3", "Recently watched", 1),
                ("station4", "Recently watched", 2),
                ("station5", "station5", 0),
                ("station6", "station6", 0),
                ("station3", "station3", 0),
                ("station4", "station4", 0)
            ]
        );

        // User watch hero station
        let mut focus_state = Some((0, 0, false));
        update_on_playback(
            &mut state,
            &hero,
            &content_list,
            focus_state.unwrap(),
            &TransitionSource::HeroDetails,
        );
        // Render content. Verify hero station is added
        modify_content_list(&mut content_list, &mut focus_state, &mut state);
        assert_eq!(
            get_content_list_summary(&content_list),
            vec![
                ("standard1", "", 0),
                ("hero", "Recently watched", 0),
                ("station2", "Recently watched", 1),
                ("station3", "Recently watched", 2),
                ("station5", "station5", 0),
                ("station6", "station6", 0),
                ("station3", "station3", 0),
                ("station4", "station4", 0)
            ]
        );
        assert_eq!(focus_state, Some((0, 0, false)));
    }

    fn get_content_list_summary(content_list: &Vec<LivePageRow>) -> Vec<(&str, &str, usize)> {
        content_list
            .iter()
            .map(|row| match row {
                LivePageRow::StandardCarousel(standard) => (standard.id.as_ref(), "", 0),
                LivePageRow::Station(station) => (
                    station.station_gti.as_ref(),
                    station.epg_group_title.as_ref(),
                    station.index_in_epg_group,
                ),
            })
            .collect()
    }

    #[test]
    fn initialize_state_correctly() {
        assert_eq!(
            initialize_state(fake_hero_station("station1").as_ref()),
            WatchHistoryState {
                is_watch_history_list_initialized: false,
                title: None,
                analytics: None,
                hero_station_gti: Some("station1".to_string()),
                watch_history: vec![],
                next_wh_rank: 1,
                omit_most_recent: false,
                max_item_count: None,
            }
        );
        assert_eq!(
            initialize_state(None),
            WatchHistoryState {
                is_watch_history_list_initialized: false,
                title: None,
                analytics: None,
                hero_station_gti: None,
                watch_history: vec![],
                next_wh_rank: 1,
                omit_most_recent: false,
                max_item_count: None,
            }
        );
    }

    mod modify_content_list {
        use super::*;

        #[test]
        fn does_not_modify_list_when_no_watch_history_rows() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let original_content_list = vec![
                fake_station_row("station1", false),
                fake_station_row("station2", false),
            ];
            let original_watch_list_state = WatchHistoryState {
                is_watch_history_list_initialized: false,
                title: None,
                analytics: None,
                hero_station_gti: None,
                watch_history: vec![
                    (fake_station_model("station2", true), 0),
                    (fake_station_model("station3", true), 1),
                ],
                next_wh_rank: 2,
                omit_most_recent: false,
                max_item_count: None,
            };

            // Verify content and state unchanged when there is no watch history row
            let mut content_list = original_content_list.clone();
            let mut watch_list_state = original_watch_list_state.clone();
            modify_content_list(&mut content_list, &mut None, &mut watch_list_state);
            assert_eq!(content_list, original_content_list);
            assert_eq!(watch_list_state, original_watch_list_state);

            // Verify content and state unchanged when content list is empty
            let mut content_list = vec![];
            let mut watch_list_state = original_watch_list_state.clone();
            modify_content_list(&mut content_list, &mut None, &mut watch_list_state);
            assert_eq!(content_list, vec![]);
            assert_eq!(watch_list_state, original_watch_list_state);
        }

        #[test]
        fn replaces_watch_history_rows_with_state() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let mut state = initialize_state(None);

            let mut station4 = fake_station_model("station4", true);
            station4.index_in_epg_group = 1;
            let mut content_list = vec![
                fake_standard_carousel("standard1"),
                fake_station_row("station2", false),
                fake_station_row("station3", true),
                LivePageRow::Station(station4),
                fake_station_row("station5", false),
            ];
            let original_content_list = content_list.clone();
            // Initial rendering
            modify_content_list(&mut content_list, &mut None, &mut state);
            // Verify content did not change
            assert_eq!(content_list, original_content_list);

            // User watch station5
            update_on_playback(
                &mut state,
                &None,
                &content_list,
                (4, 0, false),
                &TransitionSource::AiringCard,
            );

            // Render content
            modify_content_list(&mut content_list, &mut None, &mut state);
            // Verify station5 is added to watch history
            let mut station5 = fake_station_model("station5", false);
            modify_station_row_for_watch_history(&mut station5);
            station5.epg_group_title = "Recently watched".to_string();
            let mut station3 = fake_station_model("station3", true);
            station3.index_in_epg_group = 1;
            let mut station4 = fake_station_model("station4", true);
            station4.index_in_epg_group = 2;

            assert_eq!(
                content_list,
                vec![
                    fake_standard_carousel("standard1"),
                    fake_station_row("station2", false),
                    LivePageRow::Station(station5),
                    LivePageRow::Station(station3),
                    LivePageRow::Station(station4),
                    fake_station_row("station5", false),
                ]
            );
        }

        #[test]
        fn adjust_epg_focus_state() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            // Setup initial state
            let mut state = initialize_state(None);
            let mut content_list = vec![
                fake_standard_carousel("standard1"),
                fake_station_row("station1", true),
                fake_station_row("station2", false),
                fake_station_row("station3", false),
                fake_station_row("station4", false),
                fake_station_row("station5", false),
            ];
            modify_content_list(&mut content_list, &mut None, &mut state);
            // User watch station4 and station5 which will be added to the list
            update_on_playback(
                &mut state,
                &None,
                &content_list,
                (4, 0, false),
                &TransitionSource::AiringCard,
            );
            update_on_playback(
                &mut state,
                &None,
                &content_list,
                (5, 0, false),
                &TransitionSource::AiringCard,
            );

            // Perform tests on cloned state and content with different focus state
            // Focus state is before the list. No change in focus
            let mut focus_state = Some((0, 0, false));
            modify_content_list(
                &mut content_list.clone(),
                &mut focus_state,
                &mut state.clone(),
            );
            assert_eq!(focus_state, Some((0, 0, false)));

            // Focus state is in the list. Focus move down 2 rows
            let mut focus_state = Some((1, 0, false));
            modify_content_list(
                &mut content_list.clone(),
                &mut focus_state,
                &mut state.clone(),
            );
            assert_eq!(focus_state, Some((3, 0, false)));

            // Focus state is after the list. Focus move down 2 rows
            let mut focus_state = Some((6, 0, false));
            modify_content_list(
                &mut content_list.clone(),
                &mut focus_state,
                &mut state.clone(),
            );
            assert_eq!(focus_state, Some((8, 0, false)));
        }

        #[test]
        fn does_not_omit_when_only_one_station_in_watch_history() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let mut state = initialize_state(fake_hero_station("station1").as_ref());
            let mut content_list = vec![
                fake_station_row("station1", true),
                fake_station_row("station2", false),
            ];
            let original_content_list = content_list.clone();
            modify_content_list(&mut content_list, &mut None, &mut state);

            assert_eq!(content_list, original_content_list);
        }

        #[test]
        fn updates_station_ref_marker_with_container_ref_marker() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();
            let fake_station = |gti: String,
                                ref_marker: String,
                                analytics: HashMap<String, String>,
                                is_watch_history: bool|
             -> LivePageRow {
                let mut station = fake_station_model(&gti, is_watch_history);
                if let TransitionAction::player(action) = &mut station.action {
                    action.refMarker = ref_marker;
                }
                station.analytics = Some(analytics);
                LivePageRow::Station(station)
            };

            let get_station_ref_markers = |content_list: &Vec<LivePageRow>| -> Vec<String> {
                content_list
                    .iter()
                    .filter_map(|row| {
                        if let LivePageRow::Station(station) = row {
                            if let TransitionAction::player(action) = &station.action {
                                return Some(action.refMarker.clone());
                            }
                        }
                        None
                    })
                    .collect()
            };

            let mut state = initialize_state(None);
            // Build content with watch history rows and non watch history rows
            let mut content_list = vec![];
            content_list.extend((1..=3).map(|i| {
                fake_station(
                    format!("whstation{}", i),
                    format!("wh_1_{}", i),
                    HashMap::from([
                        ("refMarker".to_string(), "wh_1".to_string()),
                        ("CSM".to_string(), "some_metrics".to_string()),
                    ]),
                    true,
                )
            }));
            content_list.extend((1..=3).map(|i| {
                fake_station(
                    format!("station{}", i),
                    format!("free_2_{}", i),
                    HashMap::from([
                        ("refMarker".to_string(), "free_2".to_string()),
                        ("CSM".to_string(), "some_other_metrics".to_string()),
                    ]),
                    false,
                )
            }));

            // Initial rendering
            modify_content_list(&mut content_list, &mut None, &mut state);
            // Verify refMarker did not change
            assert_eq!(
                get_station_ref_markers(&content_list),
                vec!["wh_1_1", "wh_1_2", "wh_1_3", "free_2_1", "free_2_2", "free_2_3"]
            );

            // User watch station3
            update_on_playback(
                &mut state,
                &None,
                &content_list,
                (5, 0, false),
                &TransitionSource::AiringCard,
            );
            // Render content
            modify_content_list(&mut content_list, &mut None, &mut state);

            // Verify station3 added to watch history and refMarker updated
            assert_eq!(
                get_content_list_summary(&content_list),
                vec![
                    ("station3", "Recently watched", 0),
                    ("whstation1", "Recently watched", 1),
                    ("whstation2", "Recently watched", 2),
                    ("station1", "station1", 0),
                    ("station2", "station2", 0),
                    ("station3", "station3", 0)
                ]
            );
            assert_eq!(
                get_station_ref_markers(&content_list),
                vec!["wh_1_1", "wh_1_2", "wh_1_3", "free_2_1", "free_2_2", "free_2_3"]
            );
        }

        #[test]
        fn reports_metrics_when_watch_history_modified() {
            let _lock = MTX.lock();
            // Mock reporter with 3 stations in watch history
            let c1 = on_watch_history_rows_modified_context();
            c1.expect().once().with(eq(3)).return_const(());

            // Mock reporter with 2 rows added
            let c2 = on_watch_history_modify_content_list_context();
            c2.expect().once().with(eq(2)).return_const(());

            // Create state with 3 stations in history
            let mut state = initialize_state(None);
            state.watch_history = vec![
                (fake_station_model("station1", true), 3),
                (fake_station_model("station2", true), 2),
                (fake_station_model("station3", true), 1),
            ];

            // Modify content with only 1 stations in history
            let mut content_list = vec![
                fake_station_row("station4", false),
                fake_station_row("station1", true),
                fake_station_row("station5", false),
            ];
            modify_content_list(&mut content_list, &mut None, &mut state);
        }

        #[test]
        fn reports_metrics_when_no_station_in_history() {
            let _lock = MTX.lock();
            // Mock reporter with 0 rows added
            let c2 = on_watch_history_modify_content_list_context();
            c2.expect().once().with(eq(0)).return_const(());

            // Create state empty history
            let mut state = initialize_state(None);

            // Modify content with no watch history
            let mut content_list = vec![
                fake_station_row("station1", false),
                fake_station_row("station2", false),
            ];
            modify_content_list(&mut content_list, &mut None, &mut state);
        }
    }

    mod initialize_watch_history_list {
        use super::*;

        #[test]
        fn dedupe_watch_history_with_hero() {
            let mut state = initialize_state(fake_hero_station("station2").as_ref());
            initialize_watch_history_list(
                vec![
                    fake_station_model("station1", true),
                    fake_station_model("station2", true),
                    fake_station_model("station3", true),
                ],
                &mut state,
            );
            assert_eq!(
                state,
                WatchHistoryState {
                    is_watch_history_list_initialized: true,
                    title: Some("Recently watched".to_string()),
                    analytics: None,
                    hero_station_gti: Some("station2".to_string()),
                    watch_history: vec![
                        (fake_station_model("station2", true), 0),
                        (fake_station_model("station1", true), 0),
                        (fake_station_model("station3", true), 0),
                    ],
                    next_wh_rank: 1,
                    omit_most_recent: true,
                    max_item_count: Some(3),
                }
            );
        }

        #[test]
        fn gets_title_and_analytics_from_wh() {
            let mut station = fake_station_model("station1", true);
            station.epg_group_title = "Group Title".to_string();
            station.analytics = Some(HashMap::from([(
                "refMarker".to_string(),
                "test refMarker".to_string(),
            )]));

            let mut state = initialize_state(None);
            initialize_watch_history_list(vec![station], &mut state);
            assert_eq!(state.title, Some("Group Title".to_string()));
            assert_eq!(
                state.analytics,
                Some(HashMap::from([(
                    "refMarker".to_string(),
                    "test refMarker".to_string()
                )]))
            );
        }

        #[test]
        fn does_not_reinitialize_when_already_initialized() {
            let mut state = initialize_state(None);
            initialize_watch_history_list(vec![fake_station_model("station1", true)], &mut state);
            assert_eq!(state.title, Some("Recently watched".to_string()));

            // Attempt to reinitialize with new station
            let mut new_station = fake_station_model("station2", true);
            new_station.epg_group_title = "New title".to_string();

            initialize_watch_history_list(vec![new_station], &mut state);
            assert_eq!(state.title, Some("Recently watched".to_string()));
        }

        #[test]
        fn combine_user_watch_history_with_network_content() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let mut state = initialize_state(None);
            // User watch station1 and station2
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station1", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station2", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );
            // Join with watch history with 3 stations from network
            initialize_watch_history_list(
                vec![
                    fake_station_model("station3", true),
                    fake_station_model("station4", true),
                    fake_station_model("station2", true),
                ],
                &mut state,
            );
            // Verify watch history is combined. Station 1 and 2 are modified since they are copied from other EPG
            let mut station1 = fake_station_model("station1", false);
            modify_station_row_for_watch_history(&mut station1);
            let mut station2 = fake_station_model("station2", false);
            modify_station_row_for_watch_history(&mut station2);
            assert_eq!(
                state.watch_history,
                vec![
                    (station2, 0),
                    (station1, 0),
                    (fake_station_model("station3", true), 0),
                    (fake_station_model("station4", true), 0),
                ]
            );
        }

        #[test]
        fn sets_max_item_count_with_input_container_item_count() {
            // Set max item count with input container item count
            let mut state = initialize_state(None);
            initialize_watch_history_list(
                vec![
                    fake_station_model("station1", true),
                    fake_station_model("station2", true),
                    fake_station_model("station3", true),
                    fake_station_model("station4", true),
                ],
                &mut state,
            );
            assert_eq!(state.max_item_count, Some(4));

            // Set max item count with minimum max item count
            let mut state = initialize_state(None);
            initialize_watch_history_list(vec![fake_station_model("station1", true)], &mut state);
            assert_eq!(state.max_item_count, Some(3));
        }

        #[test]
        fn truncate_with_max_item() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let mut state = initialize_state(None);
            // User watch station1 and station2
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station1", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station2", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );
            // Clear hero station
            state.hero_station_gti = None;

            // Join with watch history already with 5 stations from network
            initialize_watch_history_list(
                vec![
                    fake_station_model("station3", true),
                    fake_station_model("station4", true),
                    fake_station_model("station5", true),
                    fake_station_model("station6", true),
                    fake_station_model("station7", true),
                ],
                &mut state,
            );
            // Verify watch history is combined and list truncated
            assert_eq!(
                state
                    .watch_history
                    .into_iter()
                    .map(|(station, _)| station.station_gti)
                    .collect::<Vec<_>>(),
                vec![
                    "station2".to_string(),
                    "station1".to_string(),
                    "station3".to_string(),
                    "station4".to_string(),
                    "station5".to_string(),
                ],
            );
            assert!(!state.omit_most_recent);
        }

        #[test]
        fn truncate_with_max_item_with_station_omitted() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let mut state = initialize_state(None);
            // User watch station1 and station2
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station1", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station2", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );
            // Join with watch history already with 5 stations from network
            initialize_watch_history_list(
                vec![
                    fake_station_model("station3", true),
                    fake_station_model("station4", true),
                    fake_station_model("station5", true),
                    fake_station_model("station6", true),
                    fake_station_model("station7", true),
                ],
                &mut state,
            );
            // Verify watch history is combined and list truncated
            assert_eq!(
                state
                    .watch_history
                    .into_iter()
                    .map(|(station, _)| station.station_gti)
                    .collect::<Vec<_>>(),
                vec![
                    "station2".to_string(),
                    "station1".to_string(),
                    "station3".to_string(),
                    "station4".to_string(),
                    "station5".to_string(),
                    "station6".to_string(),
                ],
            );
            assert!(state.omit_most_recent);
        }
    }

    mod update_on_playback {
        use super::*;

        fn setup_initial_state() -> WatchHistoryState {
            // Create initial state with three stations in history
            WatchHistoryState {
                is_watch_history_list_initialized: false,
                title: None,
                analytics: None,
                hero_station_gti: None,
                watch_history: vec![
                    (fake_station_model("station1", true), 0),
                    (fake_station_model("station2", true), 0),
                    (fake_station_model("station3", true), 1),
                ],
                next_wh_rank: 2,
                omit_most_recent: false,
                max_item_count: None,
            }
        }

        #[test]
        fn does_not_update_state_when_playback_station_invalid() {
            let mut state = initialize_state(None);
            // Enter playback and focus state is invalid
            let epg_focus_state = (100, 0, false);
            update_on_playback(
                &mut state,
                &None,
                &fake_content_list(),
                epg_focus_state,
                &TransitionSource::AiringCard,
            );

            assert_eq!(state, initialize_state(None));
        }

        #[test]
        fn updates_station_row_id_and_airing_id_when_adding_to_watch_list() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let mut state = initialize_state(None);
            // Enter playback on station not in watch history
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station1", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );

            let mut expected_station = fake_station_model("station1", false);
            expected_station.id = format!("{}{}", WATCH_HISTORY_CONTAINER_ID_PREFIX, "station1");
            expected_station.airings[0].id = format!("{}_0", expected_station.id);
            expected_station.airings[1].id = format!("{}_1", expected_station.id);
            expected_station.is_watch_history = true;

            assert_eq!(state.watch_history, vec![(expected_station, 1)]);
        }

        #[test]
        fn truncate_watch_history_with_max_item() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            let mut state = initialize_state(None);
            state.watch_history = vec![
                (fake_station_model("station1", true), 0),
                (fake_station_model("station2", true), 0),
                (fake_station_model("station3", true), 0),
                (fake_station_model("station4", true), 0),
                (fake_station_model("station5", true), 0),
            ];
            state.next_wh_rank = 1;
            state.max_item_count = Some(5);

            // Enter playback from station not in history
            update_on_playback(
                &mut state,
                &None,
                &vec![fake_station_row("station6", false)],
                (0, 0, false),
                &TransitionSource::AiringCard,
            );

            // Verify new station is added and list truncated
            assert_eq!(
                state
                    .watch_history
                    .into_iter()
                    .map(|(station, _)| station.station_gti)
                    .collect::<Vec<_>>(),
                vec![
                    "station6".to_string(),
                    "station1".to_string(),
                    "station2".to_string(),
                    "station3".to_string(),
                    "station4".to_string(),
                ],
            );
            assert!(!state.omit_most_recent);
        }

        mod playback_from_epg {
            use super::*;

            #[test]
            fn updates_rank_without_sorting_when_start_playback_from_watch_history() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut state = setup_initial_state();

                // Enter playback on second station from watch history list
                update_on_playback(
                    &mut state,
                    &None,
                    &vec![fake_station_row("station2", true)],
                    (0, 0, false),
                    &TransitionSource::AiringCard,
                );

                // Verify list is not sorted
                assert_eq!(
                    state,
                    WatchHistoryState {
                        is_watch_history_list_initialized: false,
                        title: None,
                        analytics: None,
                        hero_station_gti: Some("station2".to_string()),
                        watch_history: vec![
                            (fake_station_model("station1", true), 0),
                            (fake_station_model("station2", true), 2),
                            (fake_station_model("station3", true), 1),
                        ],
                        next_wh_rank: 3,
                        omit_most_recent: false,
                        max_item_count: None,
                    }
                )
            }

            #[test]
            fn updates_rank_and_sort_when_start_playback_from_outside_watch_history() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut state = setup_initial_state();

                // Enter playback on second station not from watch history list
                update_on_playback(
                    &mut state,
                    &None,
                    &vec![fake_station_row("station2", false)],
                    (0, 0, false),
                    &TransitionSource::AiringCard,
                );

                // Verify list is sorted
                assert_eq!(
                    state,
                    WatchHistoryState {
                        is_watch_history_list_initialized: false,
                        title: None,
                        analytics: None,
                        hero_station_gti: Some("station2".to_string()),
                        watch_history: vec![
                            (fake_station_model("station2", true), 2),
                            (fake_station_model("station3", true), 1),
                            (fake_station_model("station1", true), 0),
                        ],
                        next_wh_rank: 3,
                        omit_most_recent: false,
                        max_item_count: None,
                    }
                )
            }

            #[test]
            fn adds_station_and_sort_when_not_already_in_watch_list() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut state = setup_initial_state();

                // Enter playback on fourth station not in watch history
                update_on_playback(
                    &mut state,
                    &None,
                    &vec![fake_station_row("station4", false)],
                    (0, 0, false),
                    &TransitionSource::AiringCard,
                );

                // Verify station is added and list is sorted
                let mut expected_new_station = fake_station_model("station4", false);
                modify_station_row_for_watch_history(&mut expected_new_station);

                assert_eq!(
                    state,
                    WatchHistoryState {
                        is_watch_history_list_initialized: false,
                        title: None,
                        analytics: None,
                        hero_station_gti: Some("station4".to_string()),
                        watch_history: vec![
                            (expected_new_station, 2),
                            (fake_station_model("station3", true), 1),
                            (fake_station_model("station1", true), 0),
                            (fake_station_model("station2", true), 0),
                        ],
                        next_wh_rank: 3,
                        omit_most_recent: false,
                        max_item_count: None,
                    }
                )
            }

            #[rstest]
            #[case(true)]
            #[case(false)]
            fn reports_playback_metric(#[case] is_from_watch_history: bool) {
                let _lock = MTX.lock();
                let ctx = on_playback_context();
                ctx.expect()
                    .once()
                    .with(eq(is_from_watch_history))
                    .return_const(());

                let mut state = setup_initial_state();
                update_on_playback(
                    &mut state,
                    &None,
                    &vec![fake_station_row("station1", is_from_watch_history)],
                    (0, 0, false),
                    &TransitionSource::AiringCard,
                );
            }
        }

        mod playback_from_hero {
            use super::*;
            use crate::types::Reactions;

            #[test]
            fn moves_station_to_most_recent_when_station_already_in_watch_history() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut state = setup_initial_state();

                // Enter playback on hero station
                update_on_playback(
                    &mut state,
                    &fake_hero_station("station2"),
                    &[],
                    (0, 0, false),
                    &TransitionSource::HeroDetails,
                );

                // Verify list is sorted
                assert_eq!(
                    state,
                    WatchHistoryState {
                        is_watch_history_list_initialized: false,
                        title: None,
                        analytics: None,
                        hero_station_gti: Some("station2".to_string()),
                        watch_history: vec![
                            (fake_station_model("station2", true), 2),
                            (fake_station_model("station3", true), 1),
                            (fake_station_model("station1", true), 0),
                        ],
                        next_wh_rank: 3,
                        omit_most_recent: false,
                        max_item_count: None,
                    }
                )
            }

            #[test]
            fn adds_station_when_station_not_already_in_watch_history() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut state = setup_initial_state();

                // Enter playback on hero station not in watch history
                let playback_station = fake_hero_station("station4").unwrap();
                update_on_playback(
                    &mut state,
                    &Some(playback_station.clone()),
                    &[],
                    (0, 0, false),
                    &TransitionSource::HeroDetails,
                );

                // Verify new station is added
                let mut expected_new_station = StationRowModel {
                    id: String::default(),
                    station_gti: playback_station.station_gti,
                    station_name: playback_station.station_name,
                    logo_url: playback_station.station_logo,
                    airings: playback_station.airings,
                    is_entitled: true,
                    entitlement_message: playback_station.entitlement_message,
                    action: playback_station.action,
                    is_watch_history: false,
                    widget_type: playback_station.widget_type,
                    epg_group_title: String::default(),
                    index_in_epg_group: 0,
                    analytics: None,
                    container_index: 0,
                    reactions: Reactions { is_favorite: false },
                };
                modify_station_row_for_watch_history(&mut expected_new_station);
                assert_eq!(
                    state,
                    WatchHistoryState {
                        is_watch_history_list_initialized: false,
                        title: None,
                        analytics: None,
                        hero_station_gti: Some("station4".to_string()),
                        watch_history: vec![
                            (expected_new_station, 2),
                            (fake_station_model("station3", true), 1),
                            (fake_station_model("station1", true), 0),
                            (fake_station_model("station2", true), 0),
                        ],
                        next_wh_rank: 3,
                        omit_most_recent: false,
                        max_item_count: None,
                    }
                )
            }

            #[test]
            fn reports_playback_metric() {
                let _lock = MTX.lock();
                let ctx = on_playback_context();
                ctx.expect().once().with(eq(false)).return_const(());

                let mut state = setup_initial_state();
                update_on_playback(
                    &mut state,
                    &fake_hero_station("station4"),
                    &[],
                    (0, 0, false),
                    &TransitionSource::HeroDetails,
                );
            }
        }
    }

    mod update_on_cache_page {
        use super::*;

        #[test]
        fn updates_reactions_from_signals() {
            launch_only_scope(|scope| {
                // Initialize with 3 station
                let mut state = initialize_state(None);
                initialize_watch_history_list(
                    vec![
                        fake_station_model("station1", true),
                        fake_station_model("station2", true),
                        fake_station_model("station3", true),
                    ],
                    &mut state,
                );

                // station2 has a stored is_favorite value of `false`
                assert_eq!(
                    state
                        .watch_history
                        .get(1)
                        .is_some_and(|station| station.0.reactions.is_favorite),
                    false
                );

                // Init reaction signals so station2 has `is_favorite = true`
                let mut reactions_map: HashMap<String, ReactionSignals> = HashMap::new();
                reactions_map.insert(
                    "station2".to_string(),
                    ReactionSignals {
                        is_favorite: create_rw_signal(scope, true),
                    },
                );
                let stored_reactions = store_value(scope, reactions_map);

                update_on_cache_page(
                    &mut state,
                    &None,
                    &[],
                    (0, 0, false),
                    &TransitionSource::HeroDetails,
                    stored_reactions,
                    false,
                );

                // station2 has a stored is_favorite value of `false`
                assert_eq!(
                    state
                        .watch_history
                        .get(1)
                        .is_some_and(|station| station.0.reactions.is_favorite),
                    true
                );
            });
        }

        #[rstest]
        fn updates_for_playback_when_necessary(#[values(true, false)] to_playback: bool) {
            let _lock = MTX.lock();
            launch_only_scope(move |scope| {
                let _reporter_ctx = mock_linear_metrics();
                // Initialize with 0 stations
                let mut state = initialize_state(None);

                // update on cache_page with a valid hero
                update_on_cache_page(
                    &mut state,
                    &fake_hero_station("station4"),
                    &[],
                    (0, 0, false),
                    &TransitionSource::HeroDetails,
                    get_stored_reaction_signals(scope),
                    to_playback,
                );

                // hero is added to wh if `to_playback` is true
                assert_eq!(state.watch_history.len() > 0, to_playback);
            });
        }
    }

    fn fake_hero_station(station_gti: &str) -> Option<StationHeroModel> {
        let mut hero = static_hero_model();
        hero.station_gti = station_gti.to_string();
        Some(hero)
    }

    fn fake_station_model(station_gti: &str, is_watch_history: bool) -> StationRowModel {
        let mut station = mock_station_row_model(station_gti, 2, 60);
        station.is_watch_history = is_watch_history;
        if is_watch_history {
            station.epg_group_title = "Recently watched".to_string()
        }
        station
    }

    fn fake_station_row(station_gti: &str, is_watch_history: bool) -> LivePageRow {
        LivePageRow::Station(fake_station_model(station_gti, is_watch_history))
    }

    fn fake_standard_carousel(id: &str) -> LivePageRow {
        LivePageRow::StandardCarousel(mock_live_page_standard_carousel(id, 2, 1000))
    }

    fn fake_content_list() -> Vec<LivePageRow> {
        // Setup content list with standard carousel, 3 stations in watch history, and 4 stations below watch history
        vec![
            fake_standard_carousel("standard1"),
            fake_station_row("station2", true),
            fake_station_row("station3", true),
            fake_station_row("station4", true),
            fake_station_row("station5", false),
            fake_station_row("station6", false),
            fake_station_row("station3", false),
            fake_station_row("station4", false),
        ]
    }
}
