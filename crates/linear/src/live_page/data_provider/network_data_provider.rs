use crate::metrics::linear_reporter::LinearPageType;
#[cfg(test)]
use crate::mocks::mock_utc::{Utc, MOCK_NOW_MS};
use crate::network::parser::{
    parse_epg_container, parse_exception_body_into_enum, parse_filter_list, parse_hero,
    parse_standard_carousel, parse_station_detail_to_hero,
};
use crate::network::traits::LinearEdgeRequest;
use crate::network::types::{
    Container, EpgGroup, Item, LivePagePaginationLink, LivePageResponseResourceLrc,
    PaginateLivePageRequest, PaginationLink, StandardCarousel, StationDetailRequest,
    LIVE_PAGE_SOURCE,
};
use crate::network::types::{InitialLivePageRequest, PaginateContainerRequest};
#[double]
use crate::network::NetworkClient;
use crate::types::{LinearFilter, LivePageRow, RequestError, StationHeroModel};
use cache::ExpirableLruCacheRc;
use cfg_test_attr_derive::derive_test_only;
#[cfg(not(test))]
use chrono::Utc;
use common_transform_types::resiliency::WithResiliency;
use linear_common::types::TimeRange;
use mockall_double::double;
use std::cell::Cell;
use std::rc::Rc;

const DEFAULT_SCHEDULE_QUERY_WINDOW_MS: i64 = 8 * 3_600_000;

#[derive_test_only(Clone, Debug, PartialEq)]
pub enum ContentListSource {
    /// EPG group container type which parse into a vertical list of stations.
    /// Holds `EpgGroup` container, item index offset, and container index.
    EpgContainer(EpgGroup, usize, usize),

    /// Horizontal container type which parse into a single horizontal carousel
    /// such as Standard Carousel and container index.
    HorizontalContainer(StandardCarousel, usize),

    /// EPG container pagination link to fetch more stations in an EPG container.
    /// Holds `EpgGroup` container, item index offset, pagination link, and container index..
    EpgPagination(EpgGroup, usize, PaginationLink, usize),

    /// Page level pagination link to fetch more containers.
    /// Holds pagination link and container index of the next container
    PageLevelPagination(PaginationLink, usize),
}

#[derive_test_only(Clone, Debug, PartialEq)]
pub struct InitialLoadResult {
    pub filter_list: Vec<LinearFilter>,
    pub hero: Option<StationHeroModel>,
    pub content_list: Vec<LivePageRow>,
    pub source_queue: Vec<ContentListSource>,
    pub query_window: TimeRange,
    pub is_cached: bool,
}

/// Call initial live page API to fetch and parse live page content.
/// Return a list of live page data and queue of [`ContentListSource`] to use during pagination.
pub fn load_initial_page(
    client: Rc<NetworkClient>,
    service_token: Option<String>,
    success_callback: impl FnOnce(InitialLoadResult) + 'static,
    failure_callback: impl FnOnce(RequestError) + 'static,
    cache: ExpirableLruCacheRc<String, LivePageResponseResourceLrc>,
    use_cache: bool,
    is_favorites_enabled: bool,
) {
    client.initial_live_page(
        InitialLivePageRequest {
            service_token: service_token.clone(),
            is_favorites_enabled,
        },
        move |data, is_cached| {
            let hero = data
                .hero
                .parse_resiliency(LIVE_PAGE_SOURCE.to_string(), "PlayerHero")
                .and_then(parse_hero);
            let first_container_index = if hero.is_some() { 1 } else { 0 };
            let source_queue = build_source_queue(
                data.containerList,
                data.paginationLink,
                first_container_index,
            );
            let (content_list, source_queue) = parse_source_queue(source_queue, &service_token);
            let now = Utc::now().timestamp_millis();
            success_callback(InitialLoadResult {
                filter_list: parse_filter_list(data.subNav),
                hero,
                content_list,
                source_queue,
                // TODO: use schedule query window from Linear Edge
                query_window: (now, now + DEFAULT_SCHEDULE_QUERY_WINDOW_MS),
                is_cached,
            });
        },
        move |e| {
            let linear_error = parse_exception_body_into_enum(&e);
            failure_callback(linear_error);
        },
        cache,
        use_cache,
    );
}

#[derive_test_only(Debug, PartialEq)]
pub struct PaginateListResult {
    pub content_list: Vec<LivePageRow>,
    pub source_queue: Vec<ContentListSource>,
    pub query_window: TimeRange,
}

/// Paginate live page content list using the given queue of [`ContentListSource`].
/// When it failed to paginate using a source, it continues with the rest of sources.
/// Return a list of [`LivePageRow`] and new queue of [`ContentListSource`].
pub fn paginate_list(
    client: Rc<NetworkClient>,
    source_queue: Vec<ContentListSource>,
    callback: impl FnOnce(PaginateListResult) + 'static,
) {
    let mut remaining = source_queue.into_iter();
    let next_source = remaining.next();

    // Callback and remaining sources must only be used once after network success or failure.
    // Wrap both in Optionl Cell to consume.
    let callback_and_remaining_sources = Cell::new(Some((callback, remaining)));

    // Callback to process the result after network call success/failure
    let process_result = Rc::new(move |mut new_sources: Vec<ContentListSource>| {
        // Consume the callback and remaining sources
        if let Some((callback, remaining)) = callback_and_remaining_sources.take() {
            // Add remaining sources to the end and parse
            new_sources.extend(remaining);

            let (content_list, source_queue) = parse_source_queue(new_sources, &None);
            let now = Utc::now().timestamp_millis();
            callback(PaginateListResult {
                content_list,
                source_queue,
                // TODO: use schedule query window from Linear Edge
                query_window: (now, now + DEFAULT_SCHEDULE_QUERY_WINDOW_MS),
            });
        }
    });

    match next_source {
        Some(ContentListSource::EpgPagination(mut epg_group, offset, pagination_link, index)) => {
            let process_result_failure = process_result.clone();
            client.paginate_container(
                PaginateContainerRequest {
                    pagination_link,
                    journey_ingress_context: None,
                },
                move |result| {
                    // Insert pagination link and station items into EpgGroup
                    epg_group.paginationLink = result.paginationLink;
                    epg_group.items = result
                        .items
                        .into_iter()
                        .filter_map(|item| {
                            let item = item.parse_resiliency_into_opt(
                                LIVE_PAGE_SOURCE.to_string(),
                                "EpgItem",
                            )?;
                            match item {
                                Item::LINEAR_STATION(i) => Some(WithResiliency::Ok(i.to_owned())),
                                Item::TITLE_CARD(_) => None,
                            }
                        })
                        .collect();
                    // Build sources from the EpgGroup and parse into content
                    process_result(build_sources_for_epg_container(epg_group, offset, index));
                },
                move |_| process_result_failure(vec![]),
            );
        }
        Some(ContentListSource::PageLevelPagination(pagination_link, index)) => {
            let process_result_failure = process_result.clone();
            client.paginate_live_page(
                PaginateLivePageRequest { pagination_link },
                move |result| {
                    process_result(build_source_queue(
                        result.containerList,
                        result.paginationLink,
                        index,
                    ));
                },
                move |_| process_result_failure(vec![]),
            );
        }
        Some(v) => {
            // This source does not require network call. Add back to queue and parse.
            process_result(vec![v]);
        }
        None => process_result(vec![]),
    }
}

/// Horizontal pagination to paginate [`Item`] for Standard Carousel.
/// The function takes network client, pagination link and a callback  
/// Return a list of [`Item`] and new [`PaginationLink`] on success
/// And empty list and None on failure
pub fn paginate_container(
    client: Rc<NetworkClient>,
    pagination_link: PaginationLink,
    callback: impl Fn(Vec<WithResiliency<Item>>, Option<PaginationLink>) + 'static,
) {
    let success_callback = Rc::new(callback);
    let failure_callback = success_callback.clone();

    client.paginate_container(
        PaginateContainerRequest {
            pagination_link,
            journey_ingress_context: None,
        },
        move |result| {
            let pagination_link = result
                .paginationLink
                .parse_resiliency(LIVE_PAGE_SOURCE.to_string(), "PaginationLink");
            success_callback(result.items, pagination_link);
        },
        move |_| failure_callback(vec![], None),
    );
}

/// Build a list of [`ContentListSource`] from a list of [`Container`]
/// in initial page and page pagination response.
fn build_source_queue(
    containers: Vec<WithResiliency<Container>>,
    pagination_link: LivePagePaginationLink,
    container_start_index: usize,
) -> Vec<ContentListSource> {
    let mut sources = vec![];
    let mut container_index = container_start_index;

    containers.into_iter().for_each(|container| {
        let Some(container) =
            container.parse_resiliency_into_opt(LIVE_PAGE_SOURCE.to_string(), "Container")
        else {
            return;
        };

        match container.to_owned() {
            Container::STANDARD_CAROUSEL(carousel) => sources.push(
                ContentListSource::HorizontalContainer(carousel, container_index),
            ),
            Container::EPG(epg_group) => {
                sources.extend(build_sources_for_epg_container(
                    epg_group,
                    0,
                    container_index,
                ));
            }
        }
        container_index += 1
    });

    if let Some(pagination_link) =
        pagination_link.parse_resiliency(LIVE_PAGE_SOURCE.to_string(), "PaginationLink")
    {
        sources.push(ContentListSource::PageLevelPagination(
            pagination_link,
            container_index,
        ))
    }
    sources
}

/// Build a list of [`ContentListSource`] from [`EpgGroup`]
/// where offset is item index offset starting at 0 for initial [`EpgGroup`] and increase from EPG pagination.
fn build_sources_for_epg_container(
    epg_group: EpgGroup,
    offset: usize,
    container_index: usize,
) -> Vec<ContentListSource> {
    // If there is pagination link, build EPG pagination source
    let epg_pagination = epg_group
        .paginationLink
        .clone()
        .parse_resiliency(LIVE_PAGE_SOURCE.to_string(), "EpgPaginationLink")
        .map(|link| {
            ContentListSource::EpgPagination(
                epg_group.clone_without_items(),
                epg_group.items.len() + offset,
                link,
                container_index,
            )
        });

    // Push sources in order
    let mut sources = vec![ContentListSource::EpgContainer(
        epg_group,
        offset,
        container_index,
    )];
    if let Some(epg_pagination) = epg_pagination {
        sources.push(epg_pagination);
    }
    sources
}

/// Parse the given list of [`ContentListSource`] into [`LivePageRow`]
/// until the next source which requires network call.
/// Return resulting list of [`LivePageRow`] and remaining queue of [`ContentListSource`]
fn parse_source_queue(
    source_queue: Vec<ContentListSource>,
    discriminator: &Option<String>,
) -> (Vec<LivePageRow>, Vec<ContentListSource>) {
    let mut content_list = vec![];
    let mut remaining = vec![];

    let mut require_pagination = false;
    for source in source_queue {
        if require_pagination {
            remaining.push(source);
            continue;
        }
        match source {
            ContentListSource::EpgContainer(epg_group, offset, container_index) => {
                content_list.extend(parse_epg_container(epg_group, offset, container_index));
            }
            ContentListSource::HorizontalContainer(carousel, container_index) => {
                if let Some(container) =
                    parse_standard_carousel(carousel, container_index, discriminator)
                {
                    content_list.push(container);
                }
            }
            _ => {
                require_pagination = true;
                remaining.push(source);
            }
        }
    }
    (content_list, remaining)
}

/// Calls station detail API and parse into hero station model
pub fn load_hero_for_station(
    client: Rc<NetworkClient>,
    station_id: String,
    success_callback: impl FnOnce(StationHeroModel) + 'static,
    failure_callback: impl FnOnce(RequestError) + 'static,
) {
    // Wrap failure_callback to allow calling once from success and failure callback
    let failure_callback = Rc::new({
        let callback_cell = Cell::new(Some(failure_callback));
        move |result: RequestError| {
            if let Some(callback) = callback_cell.take() {
                callback(result);
            }
        }
    });
    let failure_callback_clone = failure_callback.clone();
    client.station_detail(
        StationDetailRequest {
            station_id,
            start_time: None,
            duration_ms: None,
            journey_ingress_context: None,
            page_type: LinearPageType::LivePage,
        },
        move |response| {
            if let Some(station_details) = parse_station_detail_to_hero(response) {
                success_callback(station_details);
            } else {
                failure_callback(RequestError::ParseError)
            }
        },
        move |_| failure_callback_clone(RequestError::UnknownError),
    );
}

#[cfg(test)]
use mockall::automock;

#[cfg(test)]
#[cfg_attr(test, automock)]
pub mod mocks {
    use super::*;

    pub fn load_initial_page<S, F>(
        _client: Rc<NetworkClient>,
        _service_token: Option<String>,
        _success_callback: S,
        _failure_callback: F,
        _cache: ExpirableLruCacheRc<String, LivePageResponseResourceLrc>,
        _use_cache: bool,
        _is_favorites_enabled: bool,
    ) where
        S: FnOnce(InitialLoadResult) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
    }

    pub fn paginate_list<S>(
        _client: Rc<NetworkClient>,
        _source_queue: Vec<ContentListSource>,
        _success_callback: S,
    ) where
        S: FnOnce(PaginateListResult) + 'static,
    {
    }

    pub fn paginate_container<S>(
        _client: Rc<NetworkClient>,
        _pagination_link: PaginationLink,
        _success_callback: S,
    ) where
        S: Fn(Vec<Item>, Option<PaginationLink>) + 'static,
    {
    }

    pub fn load_hero_for_station<S, F>(
        _client: Rc<NetworkClient>,
        _station_gti: String,
        _success_callback: S,
        _failure_callback: F,
    ) where
        S: FnOnce(StationHeroModel) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::metrics::linear_reporter::mock_base_reporter::on_dropped_component_context;
    use crate::mocks::mock_data_util::{mock_pagination_link, mock_playback_action};
    use crate::network::cache_config::{live_page_ttl_resolver, LIVE_PAGE_CACHE_SIZE};
    use crate::network::traits::mutex::MTX;
    use crate::network::types::*;
    use cache::expirable_lru_cache::ExpirableLruCache;
    use common_transform_types::container_items::Badges;
    use common_transform_types::resiliency::UnexpectedValue;
    use common_transform_types::resiliency::WithResiliency::*;
    use common_transform_types::{
        actions::{Action, PlaybackAction, TransitionAction},
        container_items::EntitlementMessaging,
    };
    use network_parser::prelude::NetworkOptional;
    use std::cell::RefCell;

    mod load_initial_page {
        use super::*;

        #[test]
        fn it_return_initial_page_result_on_network_success() {
            let mut network_client = NetworkClient::default();
            network_client.expect_initial_live_page().once().returning(
                |param, success_cb, _, _, _| {
                    assert_eq!(
                        param,
                        InitialLivePageRequest {
                            service_token: Some("test token".to_string()),
                            is_favorites_enabled: true
                        }
                    );

                    // EpgGroup with 2 items and a pagination link
                    let mut epg_group = mock_epg_group("epg_1", 2);
                    epg_group.paginationLink = Ok(Some(mock_pagination_link("link_1")).into());

                    success_cb(
                        LivePageResponseResourceLrc {
                            containerList: vec![
                                Ok(Container::STANDARD_CAROUSEL(mock_standard_carousel())),
                                Ok(Container::EPG(epg_group)),
                            ],
                            hero: Ok(NetworkOptional::None),
                            paginationLink: Ok(Some(mock_pagination_link("link_2")).into()),
                            subNav: vec![],
                        },
                        true,
                    )
                },
            );

            load_initial_page(
                Rc::new(network_client),
                Some("test token".to_string()),
                |result| {
                    // Return 1 standard carousel and 2 station rows.
                    // Source queue has epg pagination and page pagination.
                    let mut expected_content_list = vec![parse_standard_carousel(
                        mock_standard_carousel(),
                        0,
                        &Some("test token".to_string()),
                    )
                    .unwrap()];
                    expected_content_list.extend(parse_epg_container(
                        mock_epg_group("epg_1", 2),
                        0,
                        1,
                    ));

                    assert_eq!(
                        result,
                        InitialLoadResult {
                            filter_list: vec![],
                            hero: None,
                            content_list: expected_content_list,
                            source_queue: vec![
                                ContentListSource::EpgPagination(
                                    mock_epg_group("epg_1", 0),
                                    2,
                                    mock_pagination_link("link_1"),
                                    1,
                                ),
                                ContentListSource::PageLevelPagination(
                                    mock_pagination_link("link_2"),
                                    2
                                ),
                            ],
                            query_window: (
                                MOCK_NOW_MS,
                                MOCK_NOW_MS + DEFAULT_SCHEDULE_QUERY_WINDOW_MS
                            ),
                            is_cached: true,
                        }
                    );
                },
                |_| panic!("Expected success"),
                mock_cache(),
                true,
                true,
            );
        }

        #[test]
        fn it_return_error_on_network_failure() {
            let mut network_client = NetworkClient::default();
            network_client
                .expect_initial_live_page()
                .once()
                .returning(|_, _, failure_cb, _, _| {
                    failure_cb(network::RequestError::Authorization(
                        "Auth error".to_string(),
                    ))
                });

            load_initial_page(
                Rc::new(network_client),
                None,
                |_| panic!("Expected failure"),
                |e| assert_eq!(e, RequestError::UnknownError),
                mock_cache(),
                true,
                true,
            );
        }

        #[test]
        fn it_parses_traveling_customer_error() {
            let exception = r#"
            {
                "exceptionType": "TravelingCustomerException",
                "heading": "TCEheading",
                "message": "TCEmessage"
            }
            "#;
            let mut network_client = NetworkClient::default();
            network_client
                .expect_initial_live_page()
                .once()
                .returning(|_, _, failure_cb, _, _| {
                    failure_cb(network::RequestError::Http {
                        code: 400,
                        body: Some(exception.to_string()),
                        headers: vec![],
                    })
                });

            load_initial_page(
                Rc::new(network_client),
                None,
                |_| panic!("Expected failure"),
                |e| {
                    assert_eq!(
                        e,
                        RequestError::TravelingCustomerException {
                            heading: "TCEheading".to_string(),
                            message: "TCEmessage".to_string()
                        }
                    )
                },
                mock_cache(),
                true,
                true,
            );
        }
    }

    mod paginate_list {
        use super::*;

        #[test]
        fn it_return_empty_pagination_result_when_source_queue_empty() {
            paginate_list(Rc::new(NetworkClient::default()), vec![], |result| {
                assert_eq!(
                    result,
                    PaginateListResult {
                        content_list: vec![],
                        source_queue: vec![],
                        query_window: (MOCK_NOW_MS, MOCK_NOW_MS + DEFAULT_SCHEDULE_QUERY_WINDOW_MS)
                    }
                );
            });
        }

        #[test]
        fn it_return_pagination_result_when_next_source_does_not_require_network() {
            let source_queue = vec![
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 0),
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 1),
                ContentListSource::EpgContainer(mock_epg_group("epg_1", 2), 0, 2),
                ContentListSource::EpgPagination(
                    mock_epg_group("epg_1", 0),
                    2,
                    mock_pagination_link("link_1"),
                    2,
                ),
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 3),
                ContentListSource::PageLevelPagination(mock_pagination_link("link_2"), 4),
            ];

            paginate_list(Rc::new(NetworkClient::default()), source_queue, |result| {
                // Return 2 standard carousel and 2 station rows.
                // Source queue has sources which require network call.
                let mut expected_content_list = vec![
                    parse_standard_carousel(mock_standard_carousel(), 0, &None).unwrap(),
                    parse_standard_carousel(mock_standard_carousel(), 1, &None).unwrap(),
                ];
                expected_content_list.extend(parse_epg_container(mock_epg_group("epg_1", 2), 0, 2));
                assert_eq!(
                    result,
                    PaginateListResult {
                        content_list: expected_content_list,
                        source_queue: vec![
                            ContentListSource::EpgPagination(
                                mock_epg_group("epg_1", 0),
                                2,
                                mock_pagination_link("link_1"),
                                2,
                            ),
                            ContentListSource::HorizontalContainer(mock_standard_carousel(), 3),
                            ContentListSource::PageLevelPagination(
                                mock_pagination_link("link_2"),
                                4
                            ),
                        ],
                        query_window: (MOCK_NOW_MS, MOCK_NOW_MS + DEFAULT_SCHEDULE_QUERY_WINDOW_MS)
                    }
                );
            });
        }

        #[test]
        fn it_return_pagination_result_on_epg_pagination_success() {
            let mut network_client = NetworkClient::default();
            network_client
                .expect_paginate_container()
                .once()
                .returning(|param, success_cb, _| {
                    assert_eq!(
                        param,
                        PaginateContainerRequest {
                            journey_ingress_context: None,
                            pagination_link: mock_pagination_link("link_1")
                        }
                    );

                    // Pagination return 3 stations and a pagination link
                    // One item that failed to parse should be dropped
                    success_cb(ContainerPaginationResponseResource {
                        items: vec![
                            Ok(Item::LINEAR_STATION(mock_linear_station_card("epg_1_0"))),
                            Ok(Item::LINEAR_STATION(mock_linear_station_card("epg_1_1"))),
                            Ok(Item::LINEAR_STATION(mock_linear_station_card("epg_1_2"))),
                            Unexpected(UnexpectedValue::Value("invalid".to_string())),
                        ],
                        paginationLink: Ok(Some(mock_pagination_link("link_2")).into()),
                    })
                });

            let source_queue = vec![
                ContentListSource::EpgPagination(
                    mock_epg_group("epg_1", 0),
                    2,
                    mock_pagination_link("link_1"),
                    1,
                ),
                ContentListSource::PageLevelPagination(mock_pagination_link("link_100"), 2),
            ];

            paginate_list(Rc::new(network_client), source_queue, |result| {
                // Return 3 station rows with item offset 2.
                // Source queue has epg pagination and page pagination.
                assert_eq!(
                    result,
                    PaginateListResult {
                        content_list: parse_epg_container(mock_epg_group("epg_1", 3), 2, 1),
                        source_queue: vec![
                            ContentListSource::EpgPagination(
                                mock_epg_group("epg_1", 0),
                                5,
                                mock_pagination_link("link_2"),
                                1,
                            ),
                            ContentListSource::PageLevelPagination(
                                mock_pagination_link("link_100"),
                                2
                            ),
                        ],
                        query_window: (MOCK_NOW_MS, MOCK_NOW_MS + DEFAULT_SCHEDULE_QUERY_WINDOW_MS)
                    }
                );
            });
        }

        #[test]
        fn it_continue_pagination_when_epg_pagination_failure() {
            let mut network_client = NetworkClient::default();
            network_client
                .expect_paginate_container()
                .once()
                .returning(|_, _, failure_cb| {
                    failure_cb(network::RequestError::Authorization(
                        "Auth error".to_string(),
                    ))
                });

            let source_queue = vec![
                ContentListSource::EpgPagination(
                    mock_epg_group("epg_1", 0),
                    2,
                    mock_pagination_link("link_1"),
                    1,
                ),
                ContentListSource::EpgContainer(mock_epg_group("epg_2", 3), 0, 2),
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 3),
                ContentListSource::PageLevelPagination(mock_pagination_link("link_2"), 4),
            ];

            paginate_list(Rc::new(network_client), source_queue, |result| {
                // Return 3 station rows and 1 standard carousel.
                // Source queue only has page pagination.
                let mut expected_content_list =
                    parse_epg_container(mock_epg_group("epg_2", 3), 0, 2);
                expected_content_list
                    .push(parse_standard_carousel(mock_standard_carousel(), 3, &None).unwrap());
                assert_eq!(
                    result,
                    PaginateListResult {
                        content_list: expected_content_list,
                        source_queue: vec![ContentListSource::PageLevelPagination(
                            mock_pagination_link("link_2"),
                            4
                        )],
                        query_window: (MOCK_NOW_MS, MOCK_NOW_MS + DEFAULT_SCHEDULE_QUERY_WINDOW_MS)
                    }
                );
            });
        }

        #[test]
        fn it_return_pagination_result_on_live_page_pagination_success() {
            let mut network_client = NetworkClient::default();
            network_client
                .expect_paginate_live_page()
                .once()
                .returning(|param, success_cb, _| {
                    assert_eq!(
                        param,
                        PaginateLivePageRequest {
                            pagination_link: mock_pagination_link("link_1")
                        }
                    );

                    // Return a standard carousel, EpgGroup with 3 stations, and pagination link.
                    success_cb(PaginateLivePageResourceLrc {
                        containerList: vec![
                            Ok(Container::STANDARD_CAROUSEL(mock_standard_carousel())),
                            Ok(Container::EPG(mock_epg_group("epg_1", 3))),
                        ],
                        paginationLink: Ok(Some(mock_pagination_link("link_2")).into()),
                    })
                });

            let source_queue = vec![ContentListSource::PageLevelPagination(
                mock_pagination_link("link_1"),
                2,
            )];

            paginate_list(Rc::new(network_client), source_queue, |result| {
                // Return a standard carousel and 3 station rows.
                // Source queue only has page pagination.
                let mut expected_content_list =
                    vec![parse_standard_carousel(mock_standard_carousel(), 2, &None).unwrap()];
                expected_content_list.extend(parse_epg_container(mock_epg_group("epg_1", 3), 0, 3));

                assert_eq!(
                    result,
                    PaginateListResult {
                        content_list: expected_content_list,
                        source_queue: vec![ContentListSource::PageLevelPagination(
                            mock_pagination_link("link_2"),
                            4
                        )],
                        query_window: (MOCK_NOW_MS, MOCK_NOW_MS + DEFAULT_SCHEDULE_QUERY_WINDOW_MS)
                    }
                );
            });
        }

        #[test]
        fn it_continue_pagination_when_live_page_pagination_failure() {
            let mut network_client = NetworkClient::default();
            network_client
                .expect_paginate_live_page()
                .once()
                .returning(|_, _, failure_cb| {
                    failure_cb(network::RequestError::Authorization(
                        "Auth error".to_string(),
                    ))
                });

            let source_queue = vec![
                ContentListSource::PageLevelPagination(mock_pagination_link("link_2"), 0),
                ContentListSource::EpgContainer(mock_epg_group("epg_1", 3), 0, 1),
            ];

            paginate_list(Rc::new(network_client), source_queue, |result| {
                // Return 3 station rows. Source queue is empty.
                assert_eq!(
                    result,
                    PaginateListResult {
                        content_list: parse_epg_container(mock_epg_group("epg_1", 3), 0, 1),
                        source_queue: vec![],
                        query_window: (MOCK_NOW_MS, MOCK_NOW_MS + DEFAULT_SCHEDULE_QUERY_WINDOW_MS)
                    }
                );
            });
        }
    }

    mod build_source_queue {
        use super::*;

        #[test]
        fn it_builds_sources_in_order() {
            let standard_carousel_1 = mock_standard_carousel();
            let standard_carousel_2 = mock_standard_carousel();
            let epg_group = mock_epg_group("epg_1", 10);

            let source_queue = build_source_queue(
                vec![
                    Ok(Container::STANDARD_CAROUSEL(standard_carousel_1)),
                    Ok(Container::STANDARD_CAROUSEL(standard_carousel_2)),
                    Ok(Container::EPG(epg_group)),
                ],
                Ok(NetworkOptional::None),
                0,
            );

            assert_eq!(
                source_queue,
                vec![
                    ContentListSource::HorizontalContainer(mock_standard_carousel(), 0),
                    ContentListSource::HorizontalContainer(mock_standard_carousel(), 1),
                    ContentListSource::EpgContainer(mock_epg_group("epg_1", 10), 0, 2),
                ],
            )
        }

        #[test]
        fn it_builds_sources_with_unexpected_results() {
            let standard_carousel_1 = mock_standard_carousel();

            let source_queue = build_source_queue(
                vec![
                    Ok(Container::STANDARD_CAROUSEL(standard_carousel_1)),
                    Unexpected(UnexpectedValue::Value("invalid".to_string())),
                ],
                Unexpected(UnexpectedValue::Value("invalid".to_string())),
                0,
            );

            assert_eq!(
                source_queue,
                vec![ContentListSource::HorizontalContainer(
                    mock_standard_carousel(),
                    0
                ),],
            )
        }

        #[test]
        fn it_builds_source_for_epg_group_with_pagination_link() {
            let item_count = 15;
            let mut epg_group_with_link = mock_epg_group("epg_1", item_count);
            epg_group_with_link.paginationLink = Ok(Some(mock_pagination_link("link_1")).into());

            let epg_group = mock_epg_group("epg_2", 10);

            let source_queue = build_source_queue(
                vec![
                    Ok(Container::EPG(epg_group_with_link)),
                    Ok(Container::EPG(epg_group)),
                ],
                Ok(NetworkOptional::None),
                1,
            );

            assert_eq!(
                source_queue,
                vec![
                    ContentListSource::EpgContainer(mock_epg_group("epg_1", item_count), 0, 1),
                    ContentListSource::EpgPagination(
                        mock_epg_group("epg_1", 0),
                        item_count,
                        mock_pagination_link("link_1"),
                        1
                    ),
                    ContentListSource::EpgContainer(mock_epg_group("epg_2", 10), 0, 2),
                ],
            )
        }

        #[test]
        fn it_builds_source_for_page_pagination_at_the_end() {
            let standard_carousel = mock_standard_carousel();
            let epg_group = mock_epg_group("epg_1", 10);

            let source_queue = build_source_queue(
                vec![
                    Ok(Container::STANDARD_CAROUSEL(standard_carousel)),
                    Ok(Container::EPG(epg_group)),
                ],
                Ok(Some(mock_pagination_link("link_1")).into()),
                0,
            );

            assert_eq!(
                source_queue,
                vec![
                    ContentListSource::HorizontalContainer(mock_standard_carousel(), 0),
                    ContentListSource::EpgContainer(mock_epg_group("epg_1", 10), 0, 1),
                    ContentListSource::PageLevelPagination(mock_pagination_link("link_1"), 2),
                ],
            )
        }
    }

    mod parse_source_queue {
        use super::*;

        #[test]
        fn it_parse_source_queue_until_next_epg_pagination() {
            let source_queue = vec![
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 0),
                ContentListSource::EpgContainer(mock_epg_group("epg_1", 2), 0, 1),
                ContentListSource::EpgPagination(
                    mock_epg_group("epg_1", 0),
                    2,
                    mock_pagination_link("link_1"),
                    1,
                ),
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 2),
                ContentListSource::PageLevelPagination(mock_pagination_link("link_2"), 3),
            ];
            let (content_list, source_queue) = parse_source_queue(source_queue, &None);

            assert_eq!(content_list.len(), 3);
            assert!(matches!(content_list[0], LivePageRow::StandardCarousel(_)));
            assert!(matches!(content_list[1], LivePageRow::Station(_)));
            assert!(matches!(content_list[2], LivePageRow::Station(_)));

            assert_eq!(
                source_queue,
                vec![
                    ContentListSource::EpgPagination(
                        mock_epg_group("epg_1", 0),
                        2,
                        mock_pagination_link("link_1"),
                        1
                    ),
                    ContentListSource::HorizontalContainer(mock_standard_carousel(), 2),
                    ContentListSource::PageLevelPagination(mock_pagination_link("link_2"), 3),
                ],
            );
        }

        #[test]
        fn it_parse_source_queue_until_next_container_list_pagination() {
            let source_queue = vec![
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 0),
                ContentListSource::EpgContainer(mock_epg_group("epg_1", 2), 0, 1),
                ContentListSource::PageLevelPagination(mock_pagination_link("link_1"), 2),
            ];
            let (content_list, source_queue) = parse_source_queue(source_queue, &None);

            assert_eq!(content_list.len(), 3);
            assert!(matches!(content_list[0], LivePageRow::StandardCarousel(_)));
            assert!(matches!(content_list[1], LivePageRow::Station(_)));
            assert!(matches!(content_list[2], LivePageRow::Station(_)));

            assert_eq!(
                source_queue,
                vec![ContentListSource::PageLevelPagination(
                    mock_pagination_link("link_1"),
                    2
                )],
            );
        }

        #[test]
        fn it_parse_all_items_in_source_queue() {
            let source_queue = vec![
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 0),
                ContentListSource::EpgContainer(mock_epg_group("epg_1", 2), 0, 1),
                ContentListSource::HorizontalContainer(mock_standard_carousel(), 2),
            ];
            let (content_list, source_queue) = parse_source_queue(source_queue, &None);

            assert_eq!(content_list.len(), 4);
            assert!(matches!(content_list[0], LivePageRow::StandardCarousel(_)));
            assert!(matches!(content_list[1], LivePageRow::Station(_)));
            assert!(matches!(content_list[2], LivePageRow::Station(_)));
            assert!(matches!(content_list[3], LivePageRow::StandardCarousel(_)));

            assert_eq!(source_queue, vec![]);
        }

        #[test]
        fn it_adds_discriminator_to_standard_carousel_ids() {
            let input_carousel = mock_standard_carousel();
            let source_queue = vec![ContentListSource::HorizontalContainer(
                input_carousel.clone(),
                0,
            )];
            let discriminator = Some("test_service_token".to_string());
            let (content_list, _) = parse_source_queue(source_queue, &discriminator);

            if let LivePageRow::StandardCarousel(carousel) = &content_list[0] {
                assert_eq!(
                    carousel.id,
                    format!("{}_{}", input_carousel.id, "test_service_token")
                );
            } else {
                panic!("Expected LivePageRow::StandardCarousel");
            }
        }
    }

    mod paginate_container {
        use super::*;

        #[test]
        fn test_paginate_container_success() {
            let mut network_client = NetworkClient::default();
            network_client.expect_paginate_container().once().returning(
                move |param, success_cb, _| {
                    assert_eq!(
                        param,
                        PaginateContainerRequest {
                            journey_ingress_context: None,
                            pagination_link: mock_pagination_link("link_1")
                        }
                    );

                    // Pagination return 3 title cards and a pagination link
                    success_cb(ContainerPaginationResponseResource {
                        items: vec![
                            Ok(Item::TITLE_CARD(mock_title_card("title_1"))),
                            Ok(Item::TITLE_CARD(mock_title_card("title_2"))),
                            Ok(Item::TITLE_CARD(mock_title_card("title_3"))),
                        ],
                        paginationLink: Ok(Some(mock_pagination_link("link_2")).into()),
                    })
                },
            );

            paginate_container(
                Rc::new(network_client),
                mock_pagination_link("link_1"),
                |items, result_pagination_link| {
                    assert_eq!(items.len(), 3);
                    assert!(matches!(items[0], Ok(Item::TITLE_CARD(_))));
                    assert_eq!(
                        result_pagination_link,
                        Some(mock_pagination_link("link_2")).into()
                    )
                },
            );
        }

        #[test]
        fn test_paginate_container_failure() {
            let mut network_client = NetworkClient::default();
            network_client
                .expect_paginate_container()
                .once()
                .returning(|_, _, failure_cb| {
                    failure_cb(network::RequestError::Authorization(
                        "Auth error".to_string(),
                    ))
                });

            paginate_container(
                Rc::new(network_client),
                mock_pagination_link("link_1"),
                |items, result_pagination_link| {
                    assert_eq!(items.len(), 0);
                    assert_eq!(result_pagination_link, None)
                },
            );
        }
    }

    mod load_hero_for_station {
        use super::*;

        #[test]
        fn returns_hero_station_on_network_success() {
            let mut network_client = NetworkClient::default();
            network_client.expect_station_detail().once().returning(
                |param, success_callback, _| {
                    assert_eq!(
                        param,
                        StationDetailRequest {
                            station_id: "station_gti".to_string(),
                            start_time: None,
                            duration_ms: None,
                            journey_ingress_context: None,
                            page_type: LinearPageType::LivePage,
                        }
                    );

                    success_callback(StationDetailLrcResource {
                        buttons: vec![Ok(ActionButton {
                            button_type: crate::network::types::ButtonType::PLAYBACK,
                            action: Some(Action::TransitionAction(mock_playback_action())),
                            label: None,
                        })],
                        containerList: vec![Ok(UnionContainer {
                            stationDetailEpg: Some(StationDetailEpg {
                                items: vec![Ok(StationDetailEpgItem {
                                    gti: "station_gti".to_string(),
                                    name: "station1".to_string(),
                                    logo: Some("logo".to_string()),
                                    entitlementMessage: EntitlementMessaging {
                                        GLANCE_MESSAGE_SLOT: None,
                                        ENTITLEMENT_MESSAGE_SLOT: None,
                                        HIGH_VALUE_MESSAGE_SLOT: None,
                                        HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                                        TITLE_METADATA_BADGE_SLOT: None,
                                        INFORMATIONAL_MESSAGE_SLOT: None,
                                        BUYBOX_MESSAGE_SLOT: None,
                                        PRODUCT_SUMMARY_SLOT: None,
                                        PRODUCT_PROMOTION_SLOT: None,
                                    },
                                    schedule: vec![LinearAiring {
                                        airingId: Some("airing1".to_string()),
                                        title: None,
                                        startTime: 1000,
                                        endTime: 2000,
                                        heroImage: None,
                                        coverImage: None,
                                        synopsis: None,
                                        contentDescriptors: vec![],
                                        hierarchyContext: None,
                                        rating: None,
                                        localizedHierarchyContext: None,
                                        localizedTimeRange: None,
                                        badges: vec![],
                                    }],
                                    reactions: Reactions { isFavorite: true },
                                })],
                            }),
                        })],
                    })
                },
            );

            load_hero_for_station(
                Rc::new(network_client),
                "station_gti".to_string(),
                |result| {
                    assert_eq!(result.station_gti, "station_gti".to_string());
                },
                |_| panic!("Expected success"),
            );
        }

        #[test]
        fn returns_error_on_network_fail() {
            let mut network_client = NetworkClient::default();
            network_client
                .expect_station_detail()
                .once()
                .returning(|_, _, failure_callback| {
                    failure_callback(network::RequestError::Authorization(
                        "Auth error".to_string(),
                    ))
                });

            load_hero_for_station(
                Rc::new(network_client),
                "station_gti".to_string(),
                |_| panic!("Expected failure"),
                |e| assert_eq!(e, RequestError::UnknownError),
            );
        }

        #[test]
        fn returns_error_on_station_parse_failure() {
            let _lock = MTX.lock();
            let mut network_client = NetworkClient::default();
            network_client
                .expect_station_detail()
                .once()
                .returning(|_, success_callback, _| {
                    success_callback(StationDetailLrcResource {
                        buttons: vec![],
                        containerList: vec![Ok(UnionContainer {
                            stationDetailEpg: Some(StationDetailEpg { items: vec![] }),
                        })],
                    })
                });

            let drop_ctx = on_dropped_component_context();
            drop_ctx.expect().once().return_const(());

            load_hero_for_station(
                Rc::new(network_client),
                "station_gti".to_string(),
                |_| panic!("Expected failure"),
                |e| assert_eq!(e, RequestError::ParseError),
            );
        }
    }

    fn mock_epg_group(id: &str, item_count: usize) -> EpgGroup {
        EpgGroup {
            analytics: None,
            entitlement: Entitlement::Entitled,
            id: id.to_string(),
            items: (0..item_count)
                .map(|i| Ok(mock_linear_station_card(&format!("{}_{}", id, i))))
                .collect(),
            paginationLink: Ok(NetworkOptional::None),
            tags: vec![],
            title: Some("Freevee".to_string()),
        }
    }

    fn mock_linear_station_card(id: &str) -> LinearStationCard {
        LinearStationCard {
            action: Action::TransitionAction(TransitionAction::player(
                PlaybackAction::create_populated_action("gti"),
            )),
            deferredAction: None,
            entitlementMessaging: EntitlementMessaging {
                GLANCE_MESSAGE_SLOT: None,
                ENTITLEMENT_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                TITLE_METADATA_BADGE_SLOT: None,
                INFORMATIONAL_MESSAGE_SLOT: None,
                BUYBOX_MESSAGE_SLOT: None,
                PRODUCT_SUMMARY_SLOT: None,
                PRODUCT_PROMOTION_SLOT: None,
            },
            gti: format!("station_{}", id),
            image: None,
            name: None,
            rating: None,
            schedule: vec![LinearAiringCard {
                airingId: None,
                title: None,
                startTime: "2024-03-06T09:00:00Z".to_string(),
                endTime: "2024-03-06T09:30:00Z".to_string(),
                rating: None,
                synopsis: None,
                contentDescriptors: vec![],
                context: None,
                heroImage: None,
                image: None,
            }],
            widgetType: "widget_type".to_string(),
            reactions: Reactions { isFavorite: false },
            contextualActions: None,
        }
    }

    fn mock_standard_carousel() -> StandardCarousel {
        StandardCarousel {
            analytics: None,
            entitlement: Entitlement::Entitled,
            items: vec![Ok(Item::TITLE_CARD(mock_title_card("id")))],
            paginationLink: Ok(NetworkOptional::None),
            id: "standard_carousel".to_string(),
            title: Some("Live & Upcoming".to_string()),
            offerType: None,
        }
    }

    fn mock_title_card(id: &str) -> TitleCard {
        TitleCard {
            action: Action::TransitionAction(TransitionAction::player(
                PlaybackAction::create_populated_action("gti"),
            )),
            badges: Some(Badges {
                applyAudioDescription: false,
                applyCC: false,
                applyDolby: false,
                applyDolbyVision: false,
                applyDolbyAtmos: false,
                applyHdr10: false,
                applyPrime: false,
                applyUhd: false,
                regulatoryRating: Some("all".to_string()),
                showPSE: false,
            }),
            entitlementMessaging: EntitlementMessaging {
                GLANCE_MESSAGE_SLOT: None,
                ENTITLEMENT_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                TITLE_METADATA_BADGE_SLOT: None,
                INFORMATIONAL_MESSAGE_SLOT: None,
                BUYBOX_MESSAGE_SLOT: None,
                PRODUCT_SUMMARY_SLOT: None,
                PRODUCT_PROMOTION_SLOT: None,
            },
            gti: id.to_string(),
            widgetType: "title_card".to_string(),
            contentType: None,
            coverImage: None,
            endTime: Some(1719996600000),
            entitlementStatus: EntitlementStatus::ENTITLED,
            genres: vec!["Sports".to_string()],
            isInWatchList: Some(false),
            liveEventDateBadge: None,
            liveEventDateHeader: None,
            liveliness: None,
            startTime: Some(1719963604000),
            synopsis: None,
            title: None,
            venue: None,
        }
    }
    fn mock_cache() -> Rc<RefCell<ExpirableLruCache<String, LivePageResponseResourceLrc>>> {
        ExpirableLruCache::new_rc(LIVE_PAGE_CACHE_SIZE, Box::new(live_page_ttl_resolver))
    }
}
