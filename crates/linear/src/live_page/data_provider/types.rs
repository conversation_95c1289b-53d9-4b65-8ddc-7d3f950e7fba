use super::watch_history_list_controller::WatchHistoryState;
use crate::live_page::data_provider::network_data_provider::ContentListSource;
use crate::types::{EpgFocusState, FocusedSection, LinearFilter, LivePageRow, StationHeroModel};
use crate::utils::types::LazyLoad;
use cfg_test_attr_derive::derive_test_only;
use linear_common::types::TimeRange;

/// Essential data to render content on Live page
#[derive_test_only(Clone, Debug, PartialEq)]
pub struct LivePageData {
    /// Hero content at the top of page can be no hero, loaded hero or lazy loading hero
    pub hero: Option<LazyLoad<StationHeroModel>>,

    /// Main content list
    pub content_list: Vec<LivePageRow>,

    /// Filter list
    pub filter_list: Vec<LinearFilter>,

    /// Time range where schedule data for all stations are valid
    pub schedule_data_window: TimeRange,
}

/// Optional data to set Live page initial state
#[derive(Default)]
#[derive_test_only(<PERSON><PERSON>, Debug, PartialEq)]
pub struct LivePageMetadata {
    /// Initial focused component/section
    pub focused_section: Option<FocusedSection>,

    /// Initial EPG scroll state
    pub epg_focus_state: Option<EpgFocusState>,

    /// Initial selected filter
    pub selected_filter_id: Option<String>,
}

/// Opaque data structure for data provider to fetch and decorate page page data
#[derive_test_only(Clone, Debug, PartialEq)]
pub struct DataProviderState {
    /// Initial service token used to load the current page data
    pub page_service_token: Option<String>,

    /// Content list queue to continue vertical pagination
    pub source_queue: Vec<ContentListSource>,

    /// Time range where schedule data for all stations are valid
    pub schedule_data_window: TimeRange,

    /// State for watch history list modification
    pub watch_history_state: WatchHistoryState,
}
