use super::watch_history_list_controller;
use crate::cache::*;
#[double]
use crate::live_page::data_provider::network_data_provider;
use crate::live_page::data_provider::types::{DataProviderState, LivePageData, LivePageMetadata};
use crate::metrics::live_page_reporter;
#[double]
use crate::network::NetworkClient;
use crate::types::{
    EpgFocusState, FocusedSection, LinearFilter, LivePageRow, RequestError, StationHeroModel,
    StoredReactionSignals, TransitionSource,
};
use crate::utils::location_utils::update_last_playback_title_param;
use crate::utils::types::LazyLoad;
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::TransitionAction;
use ignx_compositron::reactive::{use_context, Scope};
use linear_common::types::TimeRange;
use mockall_double::double;
use router::hooks::use_router;
use std::rc::Rc;

const LIVE_PAGE_CACHE_KEY: &str = "LivePageCache";

#[derive_test_only(<PERSON><PERSON>, Debug, PartialEq)]
pub struct LivePageLoadResult {
    pub page_data: LivePageData,
    pub metadata: LivePageMetadata,
    pub data_provider_state: DataProviderState,
    pub cached: bool,
}

/// Get Live page data for initial render for the given `service_token`.
/// This will attempt to use cached data if applicable.
/// The resulting page data may not match fresh page load and may be modified
/// using client logic for watch history or CSM.
// # Arguments
//   `scope` - Live page scope
//   `network_client` - network client
//   `service_token` - page service token to load content for
//   `last_playback_station_gti` - station to use as page hero
//   `before_fetch_callback` - callback to call before making network call
//   `success_callback` - callback for successful page load
//   `hero_load_success_callback` - callback for successful hero lazy load
//   `failure_callback` - callback for unsuccessful page load
//   `prefetch_cache` - cache containing Live page prefetched response
//   `use_cache` - whether cached data should be used.
//                 If false, all parts of page data will be from fresh network call
//   `is_favorites_enabled` - whether the favorites filter should be requested.
pub fn load_live_page(
    scope: Scope,
    network_client: Rc<NetworkClient>,
    service_token: Option<String>,
    last_playback_station_gti: Option<String>,
    before_fetch_callback: impl FnOnce() + 'static,
    success_callback: impl FnOnce(LivePageLoadResult) + 'static,
    failure_callback: impl FnOnce(RequestError, bool) + 'static,
    hero_load_success_callback: impl FnOnce(StationHeroModel) + 'static,
    prefetch_cache: LivePagePrefetchCache,
    use_cache: bool,
    is_favorites_enabled: bool,
) {
    let page_cache = use_context::<LivePageCache>(scope);
    let session_cache = use_context::<LivePageSessionCache>(scope);

    let wrapped_success_callback = {
        let network_client = network_client.clone();
        let session_cache = session_cache.clone();
        let should_replace_session_hero = last_playback_station_gti.is_some();

        move |result: LivePageLoadResult| {
            process_page_hero(
                &result.page_data.hero,
                network_client,
                hero_load_success_callback,
                session_cache,
                should_replace_session_hero,
            );
            success_callback(result);
        }
    };

    // Get and remove cached page data
    let page_data =
        page_cache.and_then(|c| c.borrow_mut().remove(&LIVE_PAGE_CACHE_KEY.to_string()));

    // If use cache, get cached session hero. Otherwise, remove from cache
    let session_hero = if use_cache {
        session_cache.and_then(|c| {
            c.borrow_mut()
                .get(&LIVE_PAGE_CACHE_KEY.to_string())
                .cloned()
        })
    } else {
        session_cache.and_then(|c| c.borrow_mut().remove(&LIVE_PAGE_CACHE_KEY.to_string()));
        None
    };

    if use_cache {
        if let Some(page_data) = page_data {
            // Cached data would only be available on back navigation
            // Use cache regardless of service token on initial load
            live_page_reporter::on_page_cache_hit();
            wrapped_success_callback(decorate_live_page_data(
                page_data.page_data,
                session_hero,
                page_data.metadata,
                page_data.data_provider_state,
                last_playback_station_gti,
                true,
            ));
            return;
        } else {
            live_page_reporter::on_page_cache_miss();
        }
    } else {
        live_page_reporter::on_page_cache_skipped();
    }

    before_fetch_callback();

    network_data_provider::load_initial_page(
        network_client,
        service_token.clone(),
        move |result| {
            let watch_history_state = watch_history_list_controller::initialize_state(
                session_hero.as_ref().or(result.hero.as_ref()),
            );
            wrapped_success_callback(decorate_live_page_data(
                LivePageData {
                    hero: result.hero.map(LazyLoad::Loaded),
                    content_list: result.content_list,
                    filter_list: result.filter_list,
                    schedule_data_window: result.query_window,
                },
                session_hero,
                LivePageMetadata::default(),
                DataProviderState {
                    schedule_data_window: result.query_window,
                    page_service_token: service_token,
                    source_queue: result.source_queue,
                    watch_history_state,
                },
                last_playback_station_gti,
                result.is_cached,
            ));
        },
        move |e| failure_callback(e, false),
        prefetch_cache,
        use_cache,
        is_favorites_enabled,
    );
}

/// Modify `LivePageData` before returning result
fn decorate_live_page_data(
    mut page_data: LivePageData,
    session_hero: Option<StationHeroModel>,
    mut metadata: LivePageMetadata,
    mut data_provider_state: DataProviderState,
    last_playback_station_gti: Option<String>,
    cached: bool,
) -> LivePageLoadResult {
    // If there is expected hero gti, find hero for the gti
    if let Some(expected_hero_gti) = last_playback_station_gti {
        let page_data_hero_gti = page_data.hero.as_ref().and_then(|h| match h {
            LazyLoad::Loaded(h) => Some(h.station_gti.clone()),
            LazyLoad::Waiting(_) => None,
        });
        // If page does not have hero or gti does not match
        if page_data_hero_gti.is_none_or(|gti| gti != expected_hero_gti) {
            // Replace with session hero or find in content list
            // Otherwise set to lazy loading
            page_data.hero = session_hero
                .filter(|h| h.station_gti == expected_hero_gti)
                .or_else(|| {
                    page_data
                        .content_list
                        .iter()
                        .filter_map(|row| match row {
                            LivePageRow::Station(station) => Some(station),
                            LivePageRow::StandardCarousel(_) => None,
                        })
                        .find(|station| station.station_gti == expected_hero_gti)
                        .map(|station| station.clone().into())
                })
                .map(LazyLoad::Loaded)
                .or(Some(LazyLoad::Waiting(expected_hero_gti)));
        }
    } else {
        // Otherwise, use session hero or page hero
        page_data.hero = session_hero.map(LazyLoad::Loaded).or(page_data.hero);
    }

    watch_history_list_controller::modify_content_list(
        &mut page_data.content_list,
        &mut metadata.epg_focus_state,
        &mut data_provider_state.watch_history_state,
    );

    // TODO: CSM

    LivePageLoadResult {
        page_data,
        metadata,
        data_provider_state,
        cached,
    }
}

fn process_page_hero(
    page_hero: &Option<LazyLoad<StationHeroModel>>,
    network_client: Rc<NetworkClient>,
    hero_load_success_callback: impl FnOnce(StationHeroModel) + 'static,
    session_cache: Option<LivePageSessionCache>,
    should_replace_session_hero: bool,
) {
    let replace_session_hero = move |hero: &StationHeroModel| {
        if should_replace_session_hero {
            session_cache.inspect(|c| {
                c.borrow_mut()
                    .put(LIVE_PAGE_CACHE_KEY.to_string(), hero.clone())
            });
        }
    };
    match page_hero {
        // Replace session hero when back from playback
        Some(LazyLoad::Loaded(hero)) => {
            replace_session_hero(hero);
        }
        // Trigger hero lazy load if needed
        Some(LazyLoad::Waiting(station_gti)) => {
            let on_hero_load_complete = live_page_reporter::on_lazy_load_hero_started(station_gti);
            let on_hero_load_complete_clone = Rc::clone(&on_hero_load_complete);
            network_data_provider::load_hero_for_station(
                network_client,
                station_gti.clone(),
                move |hero| {
                    replace_session_hero(&hero);
                    hero_load_success_callback(hero);
                    on_hero_load_complete(true, None);
                },
                move |error| {
                    on_hero_load_complete_clone(false, Some(&error));
                },
            );
        }
        None => (),
    }
}

#[derive_test_only(Clone, Debug, PartialEq)]
pub struct FilterLoadResult {
    pub content_list: Vec<LivePageRow>,
    pub schedule_data_window: TimeRange,
    pub data_provider_state: DataProviderState,
}

/// Load content list for the given filter service token.
// # Arguments
//   `network_client` - network client
//   `service_token` - page service token to load content for
//   `data_provider_state` - data provider state containing watch history
//   `success_callback` - callback for successful filter load
//   `failure_callback` - callback for unsuccessful filter load
//   `prefetch_cache` - cache containing Live page prefetched response
pub fn filter_page_load(
    network_client: Rc<NetworkClient>,
    service_token: Option<String>,
    data_provider_state: Option<DataProviderState>,
    success_callback: impl FnOnce(FilterLoadResult) + 'static,
    failure_callback: impl FnOnce(RequestError) + 'static,
    prefetch_cache: LivePagePrefetchCache,
    is_favorites_enabled: bool,
) {
    network_data_provider::load_initial_page(
        network_client,
        service_token.clone(),
        move |mut result| {
            // If state is missing, local watch history is lost. This will initialize watch history with fresh data from network.
            let mut watch_history_state = data_provider_state.map_or_else(
                || watch_history_list_controller::initialize_state(None),
                |d| d.watch_history_state,
            );

            watch_history_list_controller::modify_content_list(
                &mut result.content_list,
                &mut None,
                &mut watch_history_state,
            );

            success_callback(FilterLoadResult {
                content_list: result.content_list,
                schedule_data_window: result.query_window,
                data_provider_state: DataProviderState {
                    schedule_data_window: result.query_window,
                    page_service_token: service_token,
                    source_queue: result.source_queue,
                    watch_history_state,
                },
            })
        },
        failure_callback,
        prefetch_cache,
        true,
        is_favorites_enabled,
    )
}

#[derive_test_only(Clone, Debug, PartialEq)]
pub struct PaginateListResult {
    pub content_list: Vec<LivePageRow>,
    pub schedule_data_window: TimeRange,
    pub data_provider_state: DataProviderState,
}

/// Vertically paginate page content list
// # Arguments
//   `network_client` - network client
//   `data_provider_state` - data provider state which contain pagination link
//   `callback` - callback for pagination result
pub fn paginate_list(
    network_client: Rc<NetworkClient>,
    data_provider_state: DataProviderState,
    callback: impl FnOnce(PaginateListResult) + 'static,
) {
    network_data_provider::paginate_list(
        network_client,
        data_provider_state.source_queue,
        move |result| {
            let new_data_window = (
                result.query_window.0,
                data_provider_state.schedule_data_window.1,
            );
            let new_data_provider_state = DataProviderState {
                schedule_data_window: new_data_window,
                page_service_token: data_provider_state.page_service_token,
                source_queue: result.source_queue,
                watch_history_state: data_provider_state.watch_history_state,
            };

            callback(PaginateListResult {
                content_list: result.content_list,
                schedule_data_window: new_data_window,
                data_provider_state: new_data_provider_state,
            });
        },
    )
}

pub fn cache_live_page(
    scope: Scope,
    action: Option<&TransitionAction>,
    content_list: Vec<LivePageRow>,
    filter_list: Vec<LinearFilter>,
    hero: Option<LazyLoad<StationHeroModel>>,
    schedule_data_window: TimeRange,
    epg_focus_state: EpgFocusState,
    selected_filter_id: Option<String>,
    data_provider_state: Option<DataProviderState>,
    transition_source: &TransitionSource,
    reaction_signals: StoredReactionSignals,
) {
    // Cache hero only if there's a loaded hero. Lazy loading hero is replaced new station
    // when going to playback or cleared when going to non-playback page
    let hero = match hero {
        Some(LazyLoad::Loaded(hero)) => Some(hero),
        _ => None,
    };

    // Replace previous page params before leaving page
    let playback_gti = if let Some(TransitionAction::player(playback_action)) = action {
        Some(playback_action.uri.clone())
    } else {
        None
    };
    update_last_playback_title_param(use_router(scope), playback_gti.clone());

    // Cannot cache page if data provider state is invalid. Next page load will be fresh page load.
    let Some(mut data_provider_state) = data_provider_state else {
        live_page_reporter::on_cache_linear_page_fail("Missing data provider state");
        return;
    };

    // Update watch history
    watch_history_list_controller::update_on_cache_page(
        &mut data_provider_state.watch_history_state,
        &hero,
        &content_list,
        epg_focus_state,
        transition_source,
        reaction_signals,
        playback_gti.is_some(),
    );

    let Some(cache) = use_context::<LivePageCache>(scope) else {
        live_page_reporter::on_cache_linear_page_fail("Missing Live page cache");
        return;
    };

    // Cache current state of Live Page
    // If focused on EPG row, set focus to first item.
    // TODO: Maintain focus on specific EPG item.
    let (focus_row_index, mut focus_item_index, _) = epg_focus_state;
    if matches!(
        transition_source,
        TransitionSource::AiringCard | TransitionSource::MoreInfoModal
    ) {
        focus_item_index = 0;
    }
    let focused_section = if matches!(transition_source, TransitionSource::HeroDetails) {
        FocusedSection::Hero
    } else {
        FocusedSection::Epg
    };
    let data = LivePageCacheData {
        page_data: LivePageData {
            content_list,
            filter_list,
            hero: hero.map(LazyLoad::Loaded),
            schedule_data_window,
        },
        metadata: LivePageMetadata {
            epg_focus_state: Some((focus_row_index, focus_item_index, false)),
            selected_filter_id,
            focused_section: Some(focused_section),
        },
        data_provider_state,
    };
    cache
        .borrow_mut()
        .put(LIVE_PAGE_CACHE_KEY.to_string(), data);
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::live_page::data_provider::mock_network_data_provider::__load_initial_page::Context as LoadInitialPageContext;
    use crate::live_page::data_provider::network_data_provider::{
        ContentListSource, InitialLoadResult, PaginateListResult as NetworkPaginateListResult,
    };
    use crate::metrics::linear_reporter::{self, mock_base_reporter::*};
    use crate::mocks::mock_data_util::*;
    use crate::mocks::mock_data_util::{mock_live_page_station_row, static_hero_model};
    use crate::network::cache_config::{live_page_ttl_resolver, LIVE_PAGE_CACHE_SIZE};
    use crate::network::traits::__mock_MockNetworkClient::__new::Context as NetworkClientContext;
    use crate::network::traits::mutex::MTX;
    use crate::utils::context_utils::get_stored_reaction_signals;
    use cache::expirable_lru_cache::ExpirableLruCache;
    use common_transform_types::actions::PlaybackAction;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::reactive::*;
    use linear_reporter::{mock_utils::mock_linear_metrics, LinearPageType};
    use mockall::predicate::eq;
    use router::rust_location;
    use router::MockRouting;
    use router::RoutingContext;
    use std::cell::Cell;
    use std::rc::Rc;
    use std::time::Duration;

    // Friday, July 26, 2024 12:00 PM - 1:00 PM
    const DEFAULT_SCHEDULE_DATA_WINDOW: (i64, i64) = (*************, *************);

    fn setup_caches(
        scope: Scope,
        page_data: Option<LivePageCacheData>,
        session_hero: Option<StationHeroModel>,
    ) {
        let page_cache = ExpirableLruCache::<String, LivePageCacheData>::new_rc(
            1,
            Box::new(|_| Duration::from_secs(4 * 60 * 60)),
        );
        if let Some(data) = page_data {
            page_cache
                .borrow_mut()
                .put(LIVE_PAGE_CACHE_KEY.to_string(), data);
        }
        provide_context(scope, page_cache);

        let session_cache = ExpirableLruCache::<String, StationHeroModel>::new_rc(
            1,
            Box::new(|_| Duration::from_secs(4 * 60 * 60)),
        );
        if let Some(data) = session_hero {
            session_cache
                .borrow_mut()
                .put(LIVE_PAGE_CACHE_KEY.to_string(), data);
        }
        provide_context(scope, session_cache);
    }

    fn mock_mutate_history(scope: Scope) {
        let mut mock_routing_context = MockRouting::new();
        mock_routing_context
            .expect_mutate_history()
            .once()
            .return_const({});
        provide_context::<RoutingContext>(scope, Rc::new(mock_routing_context));
    }

    mod load_live_page {
        use super::*;

        #[test]
        fn returns_result_when_cache_empty_and_network_call_success() {
            let _lock = MTX.lock();
            launch_only_scope(move |scope| {
                setup_caches(scope, None, None);

                let _c1 = mock_network_client();
                let _c2 = mock_load_initial_page_success();

                let before_fetch_called = Rc::new(Cell::new(0));
                let before_fetch_called_clone = Rc::clone(&before_fetch_called);
                let before_fetch_callback = move || {
                    before_fetch_called_clone.replace(1);
                };

                let cache_miss_ctx = on_page_cache_miss_context();
                cache_miss_ctx
                    .expect()
                    .once()
                    .with(eq(LinearPageType::LivePage))
                    .returning(|_| ());
                let _reporter_ctx = mock_linear_metrics();

                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    Some("service_token".to_string()),
                    None,
                    before_fetch_callback,
                    |result| {
                        assert_eq!(
                            result,
                            LivePageLoadResult {
                                page_data: LivePageData {
                                    hero: Some(LazyLoad::Loaded(static_hero_model())),
                                    content_list: build_static_content_list(),
                                    filter_list: build_static_filter_list(),
                                    schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW
                                },
                                metadata: LivePageMetadata::default(),
                                data_provider_state: DataProviderState {
                                    page_service_token: Some("service_token".to_string()),
                                    source_queue: build_static_source_queue(),
                                    schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                                    watch_history_state:
                                        watch_history_list_controller::initialize_state(Some(
                                            &static_hero_model()
                                        ))
                                },
                                cached: false
                            }
                        );
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );

                assert_eq!(before_fetch_called.take(), 1);
            });
        }

        #[test]
        fn returns_error_when_cache_empty_and_network_call_fail() {
            let _lock = MTX.lock();
            launch_only_scope(move |scope| {
                setup_caches(scope, None, None);

                let _c1 = mock_network_client();
                let load_initial_page_ctx = network_data_provider::load_initial_page_context();
                load_initial_page_ctx.expect().once().returning(
                    move |_, _, _, failure_callback, _, _, _| {
                        failure_callback(RequestError::ParseError);
                    },
                );

                let cache_miss_ctx = on_page_cache_miss_context();
                cache_miss_ctx
                    .expect()
                    .once()
                    .with(eq(LinearPageType::LivePage))
                    .returning(|_| ());
                let _reporter_ctx = mock_linear_metrics();

                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    Some("service_token".to_string()),
                    None,
                    || {},
                    |_| panic!("Expect failure"),
                    |error, cached| {
                        assert_eq!(error, RequestError::ParseError);
                        assert!(!cached);
                    },
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
            });
        }

        #[test]
        fn returns_cached_page_load_when_use_cache_true() {
            let _lock = MTX.lock();
            launch_only_scope(move |scope| {
                // Setup cache with data
                let cached_page_data = LivePageCacheData {
                    page_data: LivePageData {
                        hero: None,
                        content_list: vec![mock_live_page_station_row("cached_row")],
                        filter_list: vec![],
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                    },
                    metadata: LivePageMetadata {
                        focused_section: Some(FocusedSection::Epg),
                        epg_focus_state: Some((0, 1, false)),
                        selected_filter_id: None,
                    },
                    data_provider_state: DataProviderState {
                        page_service_token: None,
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    },
                };
                setup_caches(scope, Some(cached_page_data), None);

                let cache_hit_ctx = on_page_cache_hit_context();
                cache_hit_ctx.expect().once().returning(|_| ());
                let _reporter_ctx = mock_linear_metrics();

                // Load live page and verify page data
                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    Some("service_token".to_string()),
                    None,
                    || {},
                    |result| {
                        assert_eq!(result.page_data.content_list.len(), 1);
                        assert_eq!(
                            result.metadata,
                            LivePageMetadata {
                                focused_section: Some(FocusedSection::Epg),
                                epg_focus_state: Some((0, 1, false)),
                                selected_filter_id: None,
                            }
                        );
                        assert!(result.cached);
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
            });
        }

        #[test]
        fn returns_fresh_page_load_when_use_cache_false() {
            let _lock = MTX.lock();
            launch_only_scope(move |scope| {
                // Setup cache with data
                let cached_page_data = LivePageCacheData {
                    page_data: LivePageData {
                        hero: None,
                        content_list: vec![mock_live_page_station_row("cached_row")],
                        filter_list: vec![],
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                    },
                    metadata: LivePageMetadata {
                        focused_section: None,
                        epg_focus_state: Some((0, 1, false)),
                        selected_filter_id: None,
                    },
                    data_provider_state: DataProviderState {
                        page_service_token: None,
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    },
                };
                let mut cached_session_hero = static_hero_model();
                cached_session_hero.station_gti = "session hero".to_string();
                setup_caches(scope, Some(cached_page_data), Some(cached_session_hero));

                // Mock network success
                let _c1 = mock_network_client();
                let _c2 = mock_load_initial_page_success();

                let cache_skip_ctx = on_page_cache_skipped_context();
                cache_skip_ctx
                    .expect()
                    .once()
                    .with(eq(LinearPageType::LivePage))
                    .returning(|_| ());
                let _reporter_ctx = mock_linear_metrics();

                // Load live page with use_cache false
                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    Some("service_token".to_string()),
                    None,
                    || {},
                    |result| {
                        assert_eq!(
                            result.page_data.hero,
                            Some(LazyLoad::Loaded(static_hero_model()))
                        );
                        assert_eq!(result.page_data.content_list.len(), 10);
                        assert_eq!(result.metadata, LivePageMetadata::default());
                        assert!(!result.cached);
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    false,
                    true,
                );
            });
        }

        #[test]
        fn initializes_watch_history_with_hero() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            launch_only_scope(move |scope| {
                setup_caches(scope, None, None);

                let _c1 = mock_network_client();
                let _c2 = mock_load_initial_page_success();

                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    Some("service_token".to_string()),
                    None,
                    || {},
                    |result| {
                        assert_eq!(
                            result.data_provider_state.watch_history_state,
                            watch_history_list_controller::initialize_state(Some(
                                &static_hero_model()
                            ))
                        );
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
            });
        }

        #[test]
        fn initializes_watch_history_with_session_hero() {
            let _lock = MTX.lock();
            launch_only_scope(move |scope| {
                // Setup cache with only session hero data
                let mut cached_session_hero = static_hero_model();
                cached_session_hero.station_gti = "session hero".to_string();
                setup_caches(scope, None, Some(cached_session_hero.clone()));

                let _c1 = mock_network_client();
                let _c2 = mock_load_initial_page_success();

                let cache_miss_ctx = on_page_cache_miss_context();
                cache_miss_ctx
                    .expect()
                    .once()
                    .with(eq(LinearPageType::LivePage))
                    .returning(|_| ());
                let _reporter_ctx = mock_linear_metrics();

                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    Some("service_token".to_string()),
                    None,
                    || {},
                    move |result| {
                        assert_eq!(
                            result.data_provider_state.watch_history_state,
                            watch_history_list_controller::initialize_state(Some(
                                &cached_session_hero
                            ))
                        );
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
            });
        }

        #[test]
        fn calls_station_detail_and_return_hero_when_lazy_load_hero_needed() {
            let _lock = MTX.lock();

            launch_only_scope(move |scope| {
                // Setup empty cache
                setup_caches(scope, None, None);
                // Mock initial page load
                let _c1 = mock_network_client();
                let _c2 = mock_load_initial_page_success();

                // Mock hero load
                let load_hero_ctx = network_data_provider::load_hero_for_station_context();
                load_hero_ctx.expect().once().returning(
                    move |_, station_gti, success_callback, _| {
                        assert_eq!("last_playback_gti", station_gti);

                        let mut hero = static_hero_model();
                        hero.station_gti = "last_playback_gti".to_string();
                        success_callback(hero);
                    },
                );

                let page_load_result = store_value(scope, None);
                let hero_load_result = store_value(scope, None);
                let lazy_load_hero_metric_result = store_value(scope, None);

                let lazy_load_ctx = on_lazy_load_hero_started_context();
                lazy_load_ctx
                    .expect()
                    .once()
                    .returning_st(move |station_gti| {
                        assert_eq!("last_playback_gti", station_gti);
                        Rc::new(move |success, error| {
                            lazy_load_hero_metric_result.set_value(Some((success, error.cloned())));
                        })
                    });
                let _reporter_ctx = mock_linear_metrics();

                // Load live page with hero station gti
                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    None,
                    Some("last_playback_gti".to_string()),
                    || {},
                    move |result| {
                        page_load_result.set_value(Some(result));
                    },
                    |_, _| panic!("Expect success"),
                    move |result| {
                        hero_load_result.set_value(Some(result));
                    },
                    build_prefetch_cache(),
                    true,
                    true,
                );
                // Verify page waiting hero and hero result
                assert_eq!(
                    page_load_result.get_value().unwrap().page_data.hero,
                    Some(LazyLoad::Waiting("last_playback_gti".to_string()))
                );
                assert_eq!(
                    hero_load_result.get_value().unwrap().station_gti,
                    "last_playback_gti".to_string()
                );
                // Verify metric
                assert_eq!(lazy_load_hero_metric_result.get_value(), Some((true, None)));
            });
        }

        #[test]
        fn report_metric_when_lazy_load_hero_fail() {
            let _lock = MTX.lock();

            launch_only_scope(move |scope| {
                // Setup empty cache
                setup_caches(scope, None, None);
                // Mock initial page load
                let _c1 = mock_network_client();
                let _c2 = mock_load_initial_page_success();

                // Mock hero load failure
                let load_hero_ctx = network_data_provider::load_hero_for_station_context();
                load_hero_ctx
                    .expect()
                    .once()
                    .returning(|_, _, _, failure_callback| {
                        failure_callback(RequestError::ParseError);
                    });

                let lazy_load_hero_metric_result = store_value(scope, None);

                let lazy_load_ctx = on_lazy_load_hero_started_context();
                lazy_load_ctx.expect().once().returning_st(move |_| {
                    Rc::new(move |success, error| {
                        lazy_load_hero_metric_result.set_value(Some((success, error.cloned())));
                    })
                });
                let _reporter_ctx = mock_linear_metrics();

                // Load live page with hero station gti
                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    None,
                    Some("last_playback_gti".to_string()),
                    || {},
                    move |_| {},
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
                // Verify metric
                assert_eq!(
                    lazy_load_hero_metric_result.get_value(),
                    Some((false, Some(RequestError::ParseError)))
                );
            });
        }

        mod cache_session_hero {
            use super::*;

            #[test]
            fn does_not_cache_hero_when_not_back_from_playback() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                launch_only_scope(move |scope| {
                    // Setup empty cache
                    setup_caches(scope, None, None);
                    // Mock initial page load
                    let _c1 = mock_network_client();
                    let _c2 = mock_load_initial_page_success();

                    // Load live page not from playback
                    let last_playback_station_gti = None;
                    load_live_page(
                        scope,
                        Rc::new(NetworkClient::default()),
                        None,
                        last_playback_station_gti,
                        || {},
                        |_| {},
                        |_, _| panic!("Expect success"),
                        |_| panic!("Unexpected hero lazy load"),
                        build_prefetch_cache(),
                        true,
                        true,
                    );
                    // Verify session cache is empty
                    let session_cache = expect_context::<LivePageSessionCache>(scope);
                    assert!(session_cache
                        .borrow_mut()
                        .remove(&LIVE_PAGE_CACHE_KEY.to_string())
                        .is_none());
                });
            }

            #[test]
            fn cache_session_hero_when_back_from_playback_with_loaded_hero() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                launch_only_scope(move |scope| {
                    // Setup empty cache
                    setup_caches(scope, None, None);
                    // Mock initial page load
                    let _c1 = mock_network_client();
                    let _c2 = mock_load_initial_page_success();

                    // Load live page not from playback
                    let last_playback_station_gti = Some("station_0".to_string());
                    load_live_page(
                        scope,
                        Rc::new(NetworkClient::default()),
                        None,
                        last_playback_station_gti,
                        || {},
                        |_| {},
                        |_, _| panic!("Expect success"),
                        |_| panic!("Unexpected hero lazy load"),
                        build_prefetch_cache(),
                        true,
                        true,
                    );
                    // Verify session cache is set with playback station
                    assert_eq!(
                        get_cached_session_hero(scope).station_gti,
                        "station_0".to_string()
                    );
                });
            }

            #[test]
            fn cache_session_hero_when_back_from_playback_and_lazy_load_hero_complete() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                launch_only_scope(move |scope| {
                    // Setup empty cache
                    setup_caches(scope, None, None);
                    // Mock initial page load
                    let _c1 = mock_network_client();
                    let _c2 = mock_load_initial_page_success();

                    // Mock hero load
                    let load_hero_ctx = network_data_provider::load_hero_for_station_context();
                    load_hero_ctx.expect().once().returning(
                        move |_, station_gti, success_callback, _| {
                            assert_eq!("last_playback_gti", station_gti);

                            let mut hero = static_hero_model();
                            hero.station_gti = "last_playback_gti".to_string();
                            success_callback(hero);
                        },
                    );

                    // Load live page with hero station gti
                    let last_playback_station_gti = Some("last_playback_gti".to_string());
                    load_live_page(
                        scope,
                        Rc::new(NetworkClient::default()),
                        None,
                        last_playback_station_gti,
                        || {},
                        |_| {},
                        |_, _| panic!("Expect success"),
                        |_| {},
                        build_prefetch_cache(),
                        true,
                        true,
                    );
                    // Verify session cache is set with playback station
                    assert_eq!(
                        get_cached_session_hero(scope).station_gti,
                        "last_playback_gti".to_string()
                    );
                });
            }
        }
    }

    mod decorate_live_page_data {
        use super::*;

        #[test]
        fn replace_cached_page_hero_with_session_hero() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();
            launch_only_scope(move |scope| {
                // Setup cache with page data and session hero
                let cached_page_data = LivePageCacheData {
                    page_data: LivePageData {
                        hero: Some(LazyLoad::Loaded(static_hero_model())),
                        content_list: vec![mock_live_page_station_row("cached_row")],
                        filter_list: vec![],
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                    },
                    metadata: LivePageMetadata {
                        focused_section: None,
                        epg_focus_state: Some((0, 1, false)),
                        selected_filter_id: None,
                    },
                    data_provider_state: DataProviderState {
                        page_service_token: None,
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(Some(
                            &static_hero_model(),
                        )),
                    },
                };
                let mut cached_session_hero = static_hero_model();
                cached_session_hero.station_gti = "session hero".to_string();
                setup_caches(
                    scope,
                    Some(cached_page_data),
                    Some(cached_session_hero.clone()),
                );

                let _c1 = mock_network_client();

                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    None,
                    None,
                    || {},
                    move |result| {
                        // Verify cached data is returned with session hero
                        assert_eq!(
                            result.page_data.hero,
                            Some(LazyLoad::Loaded(cached_session_hero))
                        );
                        assert_eq!(result.page_data.content_list.len(), 1);
                        assert!(result.cached);
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
            });
        }

        #[test]
        fn replace_fresh_network_page_hero_with_session_hero() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();
            launch_only_scope(move |scope| {
                // Setup cache with only session hero data
                let mut cached_session_hero = static_hero_model();
                cached_session_hero.station_gti = "session hero".to_string();
                setup_caches(scope, None, Some(cached_session_hero.clone()));

                // Mock network success
                let _c1 = mock_network_client();
                let _c2 = mock_load_initial_page_success();

                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    None,
                    None,
                    || {},
                    |result| {
                        // Verify fresh data is returned with session hero
                        assert_eq!(
                            result.page_data.hero,
                            Some(LazyLoad::Loaded(cached_session_hero))
                        );
                        assert_eq!(result.page_data.content_list.len(), 10);
                        assert_eq!(result.metadata, LivePageMetadata::default());
                        assert!(!result.cached);
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
            });
        }

        #[test]
        fn update_content_list_with_watch_history_controller() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();

            launch_only_scope(|scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);
                let stored_reactions = get_stored_reaction_signals(scope);

                // Setup content with one station in watch history and two stations not in history list
                let mut station1 = mock_station_row_model("station1", 20, 60);
                station1.is_watch_history = true;
                let station2 = mock_station_row_model("station2", 20, 60);
                let station3 = mock_station_row_model("station3", 20, 60);
                let content_list = vec![
                    LivePageRow::Station(station1),
                    LivePageRow::Station(station2),
                    LivePageRow::Station(station3),
                ];

                // Build watch history state with playback on station2
                let mut watch_history_state = watch_history_list_controller::initialize_state(None);
                watch_history_list_controller::update_on_cache_page(
                    &mut watch_history_state,
                    &None,
                    &content_list,
                    (1, 0, false),
                    &TransitionSource::AiringCard,
                    stored_reactions,
                    true,
                );

                // Cache live page from entering playback on station3
                let epg_focus_state = (2, 0, false);
                cache_live_page(
                    scope,
                    Some(&mock_playback_action()),
                    content_list,
                    vec![],
                    None,
                    DEFAULT_SCHEDULE_DATA_WINDOW,
                    epg_focus_state,
                    None,
                    Some(DataProviderState {
                        page_service_token: None,
                        source_queue: vec![],
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        watch_history_state,
                    }),
                    &TransitionSource::AiringCard,
                    stored_reactions,
                );

                // Load live page from cache
                load_live_page(
                    scope,
                    Rc::new(NetworkClient::default()),
                    None,
                    None,
                    || {},
                    |result| {
                        // Verify station2 added to history before station1.
                        // station3 is omitted from history due to watch history hero dedupe logic.
                        assert_eq!(result.page_data.content_list.len(), 4);
                        let station_rows = result
                            .page_data
                            .content_list
                            .into_iter()
                            .filter_map(|row| match row {
                                LivePageRow::Station(station) => Some(station),
                                _ => None,
                            })
                            .collect::<Vec<_>>();
                        assert_eq!(station_rows[0].station_gti, "station2");
                        assert_eq!(station_rows[1].station_gti, "station1");
                        assert_eq!(station_rows[2].station_gti, "station2");
                        assert_eq!(station_rows[3].station_gti, "station3");
                    },
                    |_, _| panic!("Expect success"),
                    |_| panic!("Unexpected hero lazy load"),
                    build_prefetch_cache(),
                    true,
                    true,
                );
            })
        }

        mod set_expected_page_hero {
            use super::*;

            #[test]
            fn uses_page_data_hero_when_expected_hero_gti_match() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut page_hero = static_hero_model();
                page_hero.station_gti = "page_hero_gti".to_string();
                let mut session_hero = static_hero_model();
                session_hero.station_gti = "session_hero_gti".to_string();
                let content_list = vec![mock_live_page_station_row("station1")];

                let expected_hero_gti = Some("page_hero_gti".to_string());

                let result = decorate_live_page_data(
                    build_page_data(page_hero.clone(), content_list.clone()),
                    Some(session_hero),
                    LivePageMetadata::default(),
                    build_data_provider_state(),
                    expected_hero_gti,
                    false,
                );
                assert_eq!(result.page_data.hero, Some(LazyLoad::Loaded(page_hero)));
            }

            #[test]
            fn replace_page_hero_with_session_hero_when_expected_hero_gti_match() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut page_hero = static_hero_model();
                page_hero.station_gti = "page_hero_gti".to_string();
                let mut session_hero = static_hero_model();
                session_hero.station_gti = "session_hero_gti".to_string();
                let content_list = vec![mock_live_page_station_row("station1")];

                let expected_hero_gti = Some("session_hero_gti".to_string());

                let result = decorate_live_page_data(
                    build_page_data(page_hero.clone(), content_list.clone()),
                    Some(session_hero.clone()),
                    LivePageMetadata::default(),
                    build_data_provider_state(),
                    expected_hero_gti,
                    false,
                );
                assert_eq!(result.page_data.hero, Some(LazyLoad::Loaded(session_hero)));
            }

            #[test]
            fn replace_hero_with_epg_station_when_expected_hero_gti_match() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut page_hero = static_hero_model();
                page_hero.station_gti = "page_hero_gti".to_string();
                let mut session_hero = static_hero_model();
                session_hero.station_gti = "session_hero_gti".to_string();
                let content_list = vec![mock_live_page_station_row("station1")];

                let expected_hero_gti = Some("station1".to_string());

                let result = decorate_live_page_data(
                    build_page_data(page_hero.clone(), content_list.clone()),
                    Some(session_hero.clone()),
                    LivePageMetadata::default(),
                    build_data_provider_state(),
                    expected_hero_gti,
                    false,
                );
                assert_eq!(
                    result.page_data.hero,
                    Some(LazyLoad::Loaded(
                        mock_station_row_model("station1", 20, 60).into()
                    ))
                );
            }

            #[test]
            fn lazy_load_hero_when_expected_hero_gti_not_found() {
                let _lock = MTX.lock();
                let _reporter_ctx = mock_linear_metrics();

                let mut page_hero = static_hero_model();
                page_hero.station_gti = "page_hero_gti".to_string();
                let mut session_hero = static_hero_model();
                session_hero.station_gti = "session_hero_gti".to_string();
                let content_list = vec![mock_live_page_station_row("station1")];

                let expected_hero_gti = Some("station2".to_string());

                let result = decorate_live_page_data(
                    build_page_data(page_hero.clone(), content_list.clone()),
                    Some(session_hero.clone()),
                    LivePageMetadata::default(),
                    build_data_provider_state(),
                    expected_hero_gti,
                    false,
                );
                assert_eq!(
                    result.page_data.hero,
                    Some(LazyLoad::Waiting("station2".to_string()))
                );
            }

            fn build_page_data(
                page_hero: StationHeroModel,
                content_list: Vec<LivePageRow>,
            ) -> LivePageData {
                LivePageData {
                    hero: Some(LazyLoad::Loaded(page_hero)),
                    content_list,
                    filter_list: vec![],
                    schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                }
            }

            fn build_data_provider_state() -> DataProviderState {
                DataProviderState {
                    page_service_token: None,
                    schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                    source_queue: vec![],
                    watch_history_state: watch_history_list_controller::initialize_state(None),
                }
            }
        }
    }

    mod filter_page_load {
        use super::*;

        #[test]
        fn return_result_on_network_success() {
            let _lock = MTX.lock();
            let _c1 = mock_network_client();
            let _c2 = mock_load_initial_page_success();
            let _reporter_ctx = mock_linear_metrics();

            filter_page_load(
                Rc::new(NetworkClient::default()),
                Some("service_token".to_string()),
                Some(DataProviderState {
                    page_service_token: None,
                    source_queue: vec![],
                    schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                    watch_history_state: watch_history_list_controller::initialize_state(None),
                }),
                |result| {
                    assert_eq!(
                        result,
                        FilterLoadResult {
                            content_list: build_static_content_list(),
                            data_provider_state: DataProviderState {
                                page_service_token: Some("service_token".to_string()),
                                schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                                source_queue: build_static_source_queue(),
                                watch_history_state:
                                    watch_history_list_controller::initialize_state(None)
                            },
                            schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW
                        }
                    )
                },
                |_| panic!("Expected success"),
                build_prefetch_cache(),
                true,
            );
        }

        #[test]
        fn return_failure_on_network_fail() {
            let _lock = MTX.lock();
            let _c1 = mock_network_client();
            let load_initial_page_ctx = network_data_provider::load_initial_page_context();
            load_initial_page_ctx.expect().once().returning(
                move |_, _, _, failure_callback, _, _, _| {
                    failure_callback(RequestError::ParseError);
                },
            );

            filter_page_load(
                Rc::new(NetworkClient::default()),
                Some("service_token".to_string()),
                Some(DataProviderState {
                    page_service_token: None,
                    source_queue: vec![],
                    schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                    watch_history_state: watch_history_list_controller::initialize_state(None),
                }),
                |_| panic!("Expected failure"),
                |error| assert_eq!(error, RequestError::ParseError),
                build_prefetch_cache(),
                true,
            );
        }

        #[test]
        fn update_content_list_with_watch_history_controller() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();
            launch_only_scope(|scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                let reaction_signals = get_stored_reaction_signals(scope);

                // Build watch history state with playback on station1 and station2
                let content_list = vec![
                    mock_live_page_station_row("station1"),
                    mock_live_page_station_row("station2"),
                ];
                let mut watch_history_state = watch_history_list_controller::initialize_state(None);
                watch_history_list_controller::update_on_cache_page(
                    &mut watch_history_state,
                    &None,
                    &content_list,
                    (0, 0, false),
                    &TransitionSource::AiringCard,
                    reaction_signals,
                    true,
                );
                watch_history_list_controller::update_on_cache_page(
                    &mut watch_history_state,
                    &None,
                    &content_list,
                    (1, 0, false),
                    &TransitionSource::AiringCard,
                    reaction_signals,
                    true,
                );

                // Mock filter load with one station in watch history
                let load_initial_page_ctx = network_data_provider::load_initial_page_context();
                load_initial_page_ctx.expect().once().returning(
                    move |_, _, success_callback, _, _, _, _| {
                        let mut station_3 = mock_station_row_model("station3", 20, 60);
                        station_3.is_watch_history = true;
                        let content_list = vec![LivePageRow::Station(station_3)];

                        success_callback(InitialLoadResult {
                            filter_list: vec![],
                            hero: None,
                            content_list,
                            source_queue: vec![],
                            query_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                            is_cached: false,
                        });
                    },
                );

                // Load filter page with the watch history state
                filter_page_load(
                    Rc::new(NetworkClient::default()),
                    Some("service_token".to_string()),
                    Some(DataProviderState {
                        page_service_token: None,
                        source_queue: vec![],
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        watch_history_state,
                    }),
                    |result| {
                        // Verify station1 added to history before station3.
                        // station2 is omitted from history due to watch history hero dedupe logic.
                        assert_eq!(result.content_list.len(), 2);
                        let station_rows = result
                            .content_list
                            .into_iter()
                            .filter_map(|row| match row {
                                LivePageRow::Station(station) => Some(station),
                                _ => None,
                            })
                            .collect::<Vec<_>>();
                        assert_eq!(station_rows[0].station_gti, "station1");
                        assert_eq!(station_rows[1].station_gti, "station3");
                    },
                    |_| panic!("Expected success"),
                    build_prefetch_cache(),
                    true,
                );
            })
        }
    }

    mod paginate_list {
        use super::*;

        #[test]
        fn return_result_on_network_success() {
            let _lock = MTX.lock();
            let _c1 = mock_network_client();
            let paginate_list_ctx = network_data_provider::paginate_list_context();

            // Mock pagination and return result with new query window
            let (start, end) = DEFAULT_SCHEDULE_DATA_WINDOW;
            let resut_query_window = (start + 1000, end + 1000);
            paginate_list_ctx.expect().once().returning(
                move |_, source_queue, success_callback| {
                    // Verify input source queue is used
                    assert_eq!(source_queue, build_static_source_queue());

                    success_callback(NetworkPaginateListResult {
                        content_list: build_static_content_list(),
                        query_window: resut_query_window,
                        source_queue: vec![ContentListSource::PageLevelPagination(
                            mock_pagination_link("link_2"),
                            2,
                        )],
                    })
                },
            );

            let input_data_provider_state = DataProviderState {
                page_service_token: Some("service_token".to_string()),
                schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                source_queue: build_static_source_queue(),
                watch_history_state: watch_history_list_controller::initialize_state(None),
            };
            paginate_list(
                Rc::new(NetworkClient::default()),
                input_data_provider_state,
                move |result| {
                    let expected_data_window =
                        (resut_query_window.0, DEFAULT_SCHEDULE_DATA_WINDOW.1);
                    assert_eq!(
                        result,
                        PaginateListResult {
                            content_list: build_static_content_list(),
                            data_provider_state: DataProviderState {
                                page_service_token: Some("service_token".to_string()),
                                schedule_data_window: expected_data_window,
                                source_queue: vec![ContentListSource::PageLevelPagination(
                                    mock_pagination_link("link_2"),
                                    2,
                                )],
                                watch_history_state:
                                    watch_history_list_controller::initialize_state(None)
                            },
                            schedule_data_window: expected_data_window
                        }
                    )
                },
            );
        }
    }

    mod cache_live_page {
        use super::*;

        #[test]
        fn cache_given_live_page_data() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache all live page data
                let action = mock_signup_action();
                let content_list = vec![mock_live_page_station_row("station1")];
                let filter_list = build_static_filter_list();
                let hero = static_hero_model();
                let schedule_data_window = (1000, 2000);
                let data_provider_state = DataProviderState {
                    page_service_token: Some("page_service_token".to_string()),
                    schedule_data_window,
                    source_queue: vec![ContentListSource::PageLevelPagination(
                        mock_pagination_link("link1"),
                        10,
                    )],
                    watch_history_state: watch_history_list_controller::initialize_state(None),
                };
                let focus_state = (1, 2, false);
                let selected_filter_id = Some("selected_filter".to_string());
                let transition_source = TransitionSource::HeroDetails;
                cache_live_page(
                    scope,
                    Some(&action),
                    content_list.clone(),
                    filter_list.clone(),
                    Some(LazyLoad::Loaded(hero.clone())),
                    schedule_data_window,
                    focus_state,
                    selected_filter_id.clone(),
                    Some(data_provider_state.clone()),
                    &transition_source,
                    get_stored_reaction_signals(scope),
                );

                // Verify all live page data is in cache
                let data = get_cached_page_data(scope);
                assert_eq!(
                    data,
                    LivePageCacheData {
                        page_data: LivePageData {
                            hero: Some(LazyLoad::Loaded(hero)),
                            content_list,
                            filter_list,
                            schedule_data_window,
                        },
                        metadata: LivePageMetadata {
                            epg_focus_state: Some(focus_state),
                            selected_filter_id,
                            focused_section: Some(FocusedSection::Hero),
                        },
                        data_provider_state,
                    }
                )
            });
        }

        #[test]
        fn reset_horizontal_focus_position_when_transition_from_epg() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache live page data when transition from Epg
                let content_list = vec![mock_live_page_station_row("station1")];
                cache_live_page(
                    scope,
                    Some(&mock_playback_action()),
                    content_list,
                    vec![],
                    None,
                    (1000, 2000),
                    (1, 2, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: Some("page_service_token".to_string()),
                        schedule_data_window: (1000, 2000),
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::AiringCard,
                    get_stored_reaction_signals(scope),
                );

                // Verify cached focus state
                let data = get_cached_page_data(scope);
                assert_eq!(data.metadata.epg_focus_state, Some((1, 0, false)));
                assert_eq!(data.metadata.focused_section, Some(FocusedSection::Epg));
            });
        }

        #[test]
        fn reset_horizontal_focus_position_when_transition_from_more_info_modal() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache live page data when transition from info modal
                let content_list = vec![mock_live_page_station_row("station1")];
                cache_live_page(
                    scope,
                    Some(&mock_playback_action()),
                    content_list,
                    vec![],
                    None,
                    (1000, 2000),
                    (1, 2, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: Some("page_service_token".to_string()),
                        schedule_data_window: (1000, 2000),
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::MoreInfoModal,
                    get_stored_reaction_signals(scope),
                );

                // Verify cached focus state
                let data = get_cached_page_data(scope);
                assert_eq!(data.metadata.epg_focus_state, Some((1, 0, false)));
                assert_eq!(data.metadata.focused_section, Some(FocusedSection::Epg));
            });
        }

        #[test]
        fn cache_standard_carousel_focus_position() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache live page data when transition from standard carousel
                let content_list = vec![
                    mock_live_page_station_row("station1"),
                    LivePageRow::StandardCarousel(mock_live_page_standard_carousel(
                        "standard1",
                        10,
                        1000,
                    )),
                ];
                cache_live_page(
                    scope,
                    Some(&mock_playback_action()),
                    content_list,
                    vec![],
                    None,
                    (1000, 2000),
                    (2, 2, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: Some("page_service_token".to_string()),
                        schedule_data_window: (1000, 2000),
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::StandardCarouselCard,
                    get_stored_reaction_signals(scope),
                );

                // Verify cached focus state
                let data = get_cached_page_data(scope);
                assert_eq!(data.metadata.epg_focus_state, Some((2, 2, false)));
                assert_eq!(data.metadata.focused_section, Some(FocusedSection::Epg));
            });
        }

        #[test]
        fn update_watch_history_on_playback() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache live page data when entering playback
                cache_live_page(
                    scope,
                    Some(&mock_playback_action()),
                    vec![mock_live_page_station_row("station1")],
                    vec![],
                    None,
                    DEFAULT_SCHEDULE_DATA_WINDOW,
                    (0, 0, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: None,
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::AiringCard,
                    get_stored_reaction_signals(scope),
                );

                // Verify watch history changed
                let watch_history_state = get_cached_page_data(scope)
                    .data_provider_state
                    .watch_history_state;
                assert_ne!(
                    watch_history_state,
                    watch_history_list_controller::initialize_state(None)
                );
            })
        }

        #[test]
        fn does_not_update_watch_history_on_non_playback() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache live page data when entering sign up flow
                cache_live_page(
                    scope,
                    Some(&mock_signup_action()),
                    vec![mock_live_page_station_row("station1")],
                    vec![],
                    None,
                    DEFAULT_SCHEDULE_DATA_WINDOW,
                    (0, 0, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: None,
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::AiringCard,
                    get_stored_reaction_signals(scope),
                );

                // Verify watch history unchanged
                let watch_history_state = get_cached_page_data(scope)
                    .data_provider_state
                    .watch_history_state;
                assert_eq!(
                    watch_history_state,
                    watch_history_list_controller::initialize_state(None)
                );
            })
        }

        #[test]
        fn does_not_update_watch_history_when_action_is_none() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache live page data when entering sign up flow
                cache_live_page(
                    scope,
                    None,
                    vec![mock_live_page_station_row("station1")],
                    vec![],
                    None,
                    DEFAULT_SCHEDULE_DATA_WINDOW,
                    (0, 0, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: None,
                        schedule_data_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::AiringCard,
                    get_stored_reaction_signals(scope),
                );

                // Verify watch history unchanged
                let watch_history_state = get_cached_page_data(scope)
                    .data_provider_state
                    .watch_history_state;
                assert_eq!(
                    watch_history_state,
                    watch_history_list_controller::initialize_state(None)
                );
            })
        }

        #[test]
        fn report_missing_data_provider_state() {
            let _lock = MTX.lock();
            launch_only_scope(move |scope| {
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                let cache_live_page_fail_ctx = on_cache_linear_page_fail_context();
                cache_live_page_fail_ctx
                    .expect()
                    .once()
                    .returning(|page_type, cause| {
                        assert_eq!("Missing data provider state", cause);
                        assert!(matches!(page_type, LinearPageType::LivePage))
                    });

                // Cache live page data with missing DataProviderState
                cache_live_page(
                    scope,
                    Some(&mock_playback_action()),
                    vec![mock_live_page_station_row("station1")],
                    vec![],
                    None,
                    (1000, 2000),
                    (1, 2, false),
                    None,
                    None,
                    &TransitionSource::HeroDetails,
                    get_stored_reaction_signals(scope),
                );
            })
        }

        #[test]
        fn updates_page_param_when_entering_playback() {
            let _lock = MTX.lock();
            let _reporter_ctx = mock_linear_metrics();
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                // Mock mutate Live TV page param
                let mut mock_router = MockRouting::default();
                mock_router
                    .expect_mutate_history()
                    .returning(move |callback| {
                        let mut locations = vec![
                            rust_location!(RUST_LIVE_TV, { "lastPlaybackTitleId" => "old_gti" }),
                        ];
                        callback(&mut locations);
                        assert_eq!(
                            locations,
                            vec![rust_location!(RUST_LIVE_TV, { "lastPlaybackTitleId" => "station1" })]
                        );
                    });
                provide_context::<RoutingContext>(scope, Rc::new(mock_router));

                // Cache all live page data from playback action
                let action =
                    TransitionAction::player(PlaybackAction::create_populated_action("station1"));
                cache_live_page(
                    scope,
                    Some(&action),
                    vec![mock_live_page_station_row("station1")],
                    vec![],
                    None,
                    (1000, 2000),
                    (0, 0, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: None,
                        schedule_data_window: (1000, 2000),
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::AiringCard,
                    get_stored_reaction_signals(scope),
                );
            });
        }

        #[test]
        fn clears_page_param_when_not_entering_playback() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                // Mock mutate Live TV page param
                let mut mock_router = MockRouting::default();
                mock_router
                    .expect_mutate_history()
                    .returning(move |callback| {
                        let mut locations = vec![
                            rust_location!(RUST_LIVE_TV, { "lastPlaybackTitleId" => "old_gti" }),
                        ];
                        callback(&mut locations);
                        assert_eq!(locations, vec![rust_location!(RUST_LIVE_TV)]);
                    });
                provide_context::<RoutingContext>(scope, Rc::new(mock_router));

                // Cache all live page data from sign up action
                let action = mock_signup_action();
                cache_live_page(
                    scope,
                    Some(&action),
                    vec![mock_live_page_station_row("station1")],
                    vec![],
                    None,
                    (1000, 2000),
                    (0, 0, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: None,
                        schedule_data_window: (1000, 2000),
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::AiringCard,
                    get_stored_reaction_signals(scope),
                );
            });
        }

        #[test]
        fn does_not_cache_lazy_loading_hero() {
            launch_only_scope(move |scope| {
                // Setup empty caches
                setup_caches(scope, None, None);
                mock_mutate_history(scope);

                // Cache live page with waiting hero
                cache_live_page(
                    scope,
                    Some(&mock_signup_action()),
                    vec![mock_live_page_station_row("station1")],
                    vec![],
                    Some(LazyLoad::Waiting("hero_gti".to_string())),
                    (1000, 2000),
                    (0, 0, false),
                    None,
                    Some(DataProviderState {
                        page_service_token: None,
                        schedule_data_window: (1000, 2000),
                        source_queue: vec![],
                        watch_history_state: watch_history_list_controller::initialize_state(None),
                    }),
                    &TransitionSource::AiringCard,
                    get_stored_reaction_signals(scope),
                );
                // Verify no hero in page cache
                assert!(get_cached_page_data(scope).page_data.hero.is_none());
            });
        }
    }

    fn get_cached_page_data(scope: Scope) -> LivePageCacheData {
        expect_context::<LivePageCache>(scope)
            .borrow_mut()
            .remove(&LIVE_PAGE_CACHE_KEY.to_string())
            .unwrap()
    }

    fn get_cached_session_hero(scope: Scope) -> StationHeroModel {
        expect_context::<LivePageSessionCache>(scope)
            .borrow_mut()
            .remove(&LIVE_PAGE_CACHE_KEY.to_string())
            .unwrap()
    }

    fn mock_network_client() -> NetworkClientContext {
        let network_context = NetworkClient::new_context();
        network_context
            .expect()
            .returning(|_| NetworkClient::default());
        network_context
    }

    fn mock_load_initial_page_success() -> LoadInitialPageContext {
        let load_initial_page_ctx = network_data_provider::load_initial_page_context();
        load_initial_page_ctx.expect().once().returning(
            move |_, _, success_callback, _, _, _, _| {
                success_callback(InitialLoadResult {
                    filter_list: build_static_filter_list(),
                    hero: Some(static_hero_model()),
                    content_list: build_static_content_list(),
                    source_queue: build_static_source_queue(),
                    query_window: DEFAULT_SCHEDULE_DATA_WINDOW,
                    is_cached: false,
                });
            },
        );
        load_initial_page_ctx
    }

    fn build_static_content_list() -> Vec<LivePageRow> {
        (0..10)
            .map(|idx| {
                if idx < 5 {
                    mock_live_page_station_row(&format!("station_{}", idx))
                } else if idx == 5 {
                    LivePageRow::StandardCarousel(mock_live_page_standard_carousel(
                        &format!("carousel_{}", idx),
                        10,
                        1000,
                    ))
                } else {
                    mock_live_page_unentitled_station_row(&format!("station_{}", idx))
                }
            })
            .collect()
    }

    fn build_static_source_queue() -> Vec<ContentListSource> {
        vec![ContentListSource::PageLevelPagination(
            mock_pagination_link("link_1"),
            2,
        )]
    }

    fn build_prefetch_cache() -> LivePagePrefetchCache {
        ExpirableLruCache::new_rc(LIVE_PAGE_CACHE_SIZE, Box::new(live_page_ttl_resolver))
    }
}
