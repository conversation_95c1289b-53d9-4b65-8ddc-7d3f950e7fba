[package]
name = "linear"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
network-parser.workspace = true
network-parser-derive.workspace = true
mockall.workspace = true
amzn-fable-tokens.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
chrono.workspace = true
collections-ui.workspace = true
common-transform-types.workspace = true
fableous.workspace = true
lrc-image.workspace = true
media-background.workspace = true
navigation-menu.workspace = true
serde_json.workspace = true
serde.workspace = true
title-details.workspace = true
network.workspace = true
app-config.workspace = true
mockall_double.workspace = true
transition-executor.workspace = true
router.workspace = true
auth.workspace = true
location.workspace = true
log.workspace = true
cache.workspace = true
strum.workspace = true
strum_macros.workspace = true
rust-features.workspace = true
settings-manager.workspace = true
cross-app-events.workspace = true
app-reporting.workspace = true
containers.workspace = true
container-types.workspace = true
contextual-menu.workspace = true
contextual-menu-types.workspace = true
cfg-test-attr-derive.workspace = true
watch-modal.workspace = true
app-events.workspace = true
linear-ui-signals.workspace = true
current-time.workspace = true
explicit-signals-service.workspace = true
linear-common.workspace = true
container-item-types.workspace = true
liveliness-types.workspace = true
playback-navigation.workspace = true
firetv.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
network = { workspace = true, features = ["test_utils", "mock_network"] }
rstest.workspace = true
auth.workspace = true
router.workspace = true
synchronized-state-store = { workspace = true, features = ["test-utils"] }
serial_test.workspace = true
watch-modal = { workspace = true, features = ["test_utils"] }
contextual-menu = { workspace = true, features = ["test_utils"] }
current-time = { workspace = true, features = ["test_utils"] }
explicit-signals-service = { workspace = true, features = [
    "test_utils",
] }
common-transform-types = { workspace = true, features = ["test_utils"] }
firetv = { workspace = true, features = ["test_utils"] }

[features]
default = []
example_data = []
debug_impl = []

[lints]
workspace = true

[[example]]
name = "station_logo"
crate-type = ["cdylib"]
path = "examples/station_logo.rs"


[[example]]
name = "live_page_carousel_list"
crate-type = ["cdylib"]
path = "examples/live_page_carousel_list.rs"

[[example]]
name = "airing_card"
crate-type = ["cdylib"]
path = "examples/airing_card.rs"

[[example]]
name = "skeleton"
crate-type = ["cdylib"]
path = "examples/skeleton.rs"

[[example]]
name = "station_details_airing_card_example"
crate-type = ["cdylib"]
path = "examples/station_details_airing_card_example.rs"

[[example]]
name = "station_details_epg_row_example"
crate-type = ["cdylib"]
path = "examples/station_details_epg_row_example.rs"
