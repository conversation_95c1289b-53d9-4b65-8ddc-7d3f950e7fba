// Hello_world is not used by the app, so it's safe
#![allow(clippy::disallowed_macros, reason = "example crate")]

mod tests;

use crate::prelude::*;
use ignx_compositron::reactive::*;

#[Composer]
pub fn HelloWorld(ctx: &AppContext, name: String) -> impl Composable<'static> {
    let name = create_rw_signal(ctx.scope(), name);

    let hello_msg = create_memo(ctx.scope(), move |_| format!("Hello {}", name.get()));

    #[allow(clippy::print_stdout, reason = "example crate")]
    create_effect(ctx.scope(), move |_| {
        println!("{}", hello_msg.get());
    });

    compose! {
        Row() {
            Label(text: hello_msg)
                .test_id("label")
                .color(Color::white())
            Label(text: "!!!")
                .color(Color::white())

            Button(text: "Click")
                .test_id("button")
                .on_select(move || {
                    name.set("New name".to_string());
                })
        }
        .background_color(Color::blue())
    }
}

#[cfg(test)]
mod local_tests {
    // Example of test defined in component file.

    use crate::hello_world::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;

    #[test]
    pub fn renders_correct_label_initially() {
        launch_test(
            move |ctx| {
                compose! {
                    HelloWorld(name: "World".to_string())
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Access Label component with "label" test ID
                let label_node = tree.find_by_test_id("label");

                // Final assertion
                assert_eq!(
                    label_node.borrow_props().text,
                    Some("Hello World".to_string())
                );
            },
        );
    }
}
