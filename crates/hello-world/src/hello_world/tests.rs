#[allow(clippy::module_inception, reason = "example crate")]
#[cfg(test)]
mod tests {
    // Example of test defined as separate file.
    // It can be considered when number of tests leads to bloating component file.
    use crate::hello_world::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;

    #[test]
    pub fn updates_label_on_button_press() {
        launch_test(
            move |ctx| {
                compose! {
                    HelloWorld(name: "World".to_string())
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                // Access button component with "button" test ID
                let button_node = tree.find_by_test_id("button");

                // Simulates press on the button
                test_loop.send_on_select_event(button_node.get_props().node_id);

                // Wait until app re-renders
                let tree = test_loop.tick_until_done();

                // Access Label component with "label" test ID
                let label_node = tree.find_by_test_id("label");

                // Final assertion
                assert_eq!(
                    label_node.borrow_props().text,
                    Some("Hello New name".to_string())
                );
            },
        );
    }
}
