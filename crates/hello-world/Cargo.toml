[package]
name = "hello-world"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }

log.workspace = true
[[example]]
name = "hello_world"
crate-type = ["cdylib"]
path = "examples/hello_world.rs"
