[package]
name = "ads-playback"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[features]
debug_impl = []

[dependencies]
app-config.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
cfg-test-attr-derive.workspace = true
log.workspace = true
mockall_double.workspace = true
mockall.workspace = true
cross-app-events.workspace = true
network.workspace = true
serde_json.workspace = true
serde.workspace = true
playback-core.workspace = true
playback-metadata-provider.workspace = true
playback-feature-manager.workspace = true
playback-ui-kit.workspace = true
playback-src.workspace = true
playback-machine.workspace = true
player.workspace = true
rust-features.workspace = true
fableous.workspace = true
amzn-fable-tokens.workspace = true
lrc-image.workspace = true
ads-playback-types.workspace = true
lru.workspace = true
uuid.workspace = true

[dev-dependencies]
rstest.workspace = true
amzn-ignx-compositron = { workspace = true, features = [
  "lifetime_apis",
  "test_utils",
  "mock_timer",
] }
auth.workspace = true
router = { workspace = true, features = ["test_utils"] }
app-config = { workspace = true, features = ["test_utils"] }
playback-core = { workspace = true, features = ["test_utils"] }
playback-ui-kit = { workspace = true, features = ["test_utils"] }
playback-src = { workspace = true, features = ["test_utils"] }
playback-machine = { workspace = true, features = ["test_utils"] }
playback-feature-manager = { workspace = true, features = ["test_utils"] }
player = { workspace = true, features = ["test_utils"] }
asserted-context = { workspace = true, features = ["test"] }
ads-playback-types = { workspace = true, features = ["test_utils"] }

[[example]]
name = "surface_x_example"
crate-type = ["cdylib"]
path = "examples/surface_x_example.rs"

[[example]]
name = "iva_baseline_example"
crate-type = ["cdylib"]
path = "examples/iva_baseline_example.rs"

[lints]
workspace = true
