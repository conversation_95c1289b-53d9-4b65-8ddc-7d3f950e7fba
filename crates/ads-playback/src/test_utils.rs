use crate::network::cta_service::CTAServiceRequest;
use ads_playback_types::cta_service::*;
use app_config::test_utils::MockAppConfigBuilder;
use auth::MockAuth;
use ignx_compositron::context::AppContext;
use ignx_compositron::reactive::store_value;
use mockall::mock;
use network::RequestError;
use playback_feature_manager::FeatureControllers;
use playback_machine::PlaybackMachineContext;
use playback_metadata_provider::{provide_metadata_controller, MetadataProvider};
use playback_src::NetworkClient;
use router::hooks::setup_mock_routing_ctx;
use rust_features::provide_context_test_rust_features;

mock! {
    pub NetworkClient {
        pub fn new(ctx: &AppContext) -> Self;
    }
    impl CTAServiceRequest for NetworkClient {
        fn call_to_action_request<S, F>(&self, params: CTAServiceRequestParams, success_callback: S, failure_callback: F)
        where
            S: FnOnce(CTAServiceResponse) + 'static,
            F: FnOnce(RequestError) + 'static;
    }
}

pub fn setup_mock_contexts(ctx: &AppContext) {
    PlaybackMachineContext::test_builder(ctx.scope())
        .build()
        .provide_on(ctx.scope());
    let network_context = NetworkClient::new_context();
    network_context
        .expect()
        .returning(|_| NetworkClient::default());
    store_value(ctx.scope(), network_context);
    MockAuth::new_without_params(ctx.scope()).provide(ctx.scope());
    MockAppConfigBuilder::new().build_into_context(ctx.scope());
    setup_mock_routing_ctx(ctx.scope, |_| {});
    provide_metadata_controller(
        ctx.scope(),
        store_value(ctx.scope(), Box::new(MetadataProvider::new(ctx))),
    );
    provide_context_test_rust_features(ctx.scope());
}

pub fn setup_feature_controllers_with_mock_contexts(ctx: &AppContext) -> FeatureControllers {
    setup_mock_contexts(ctx);
    FeatureControllers::new(ctx, "test".to_string())
}
