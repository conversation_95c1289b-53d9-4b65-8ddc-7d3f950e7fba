#![allow(dead_code, reason = "not used yet")]

use super::cta_service_compatibility::*;
use crate::network::cta_service::CTAServiceRequest;
use ads_playback_types::cta_service::*;
use ads_playback_types::interactive_video_ad::{AdExtensionData, InteractiveVideoAd};
use ads_playback_types::iva_v4_extension::{
    IvaV4ExtensionAdParametersActionCode, IvaV4ExtensionAdParametersActionPayload,
    IvaV4ExtensionAdParametersIdentifiers,
};
use ignx_compositron::device_information::DeviceInformation;

const LOG_PREFIX: &str = "[call_cta_handler]";
const APP_NAME: &str = "AVLRC";

pub fn call_cta_handler<S, F>(
    network_client: NetworkClient,
    device_info: &DeviceInformation,
    interactive_video_ad: &InteractiveVideoAd,
    success_cb: S,
    failure_cb: F,
) where
    S: FnOnce() + 'static,
    F: FnOnce() + 'static,
{
    let (action, identifiers) = match &interactive_video_ad.extension.data {
        AdExtensionData::Live { parameters, .. } => {
            (parameters.action.first(), &parameters.identifiers)
        }
        AdExtensionData::VodOrLinear { parameters, .. } => {
            (parameters.base.action.first(), &parameters.base.identifiers)
        }
    };

    let Some(action) = action else {
        log::warn!(
            "{} Unable to invoke CTA handler. No CTA's present in actionPayload.",
            LOG_PREFIX
        );
        // TODO: errorTrackingUrlHandler
        return failure_cb();
    };

    log::info!(
        "{} Sending request with creativeCfId: {}, bidId: {}, adCfId: {}, impressionId: {}",
        LOG_PREFIX,
        identifiers.creative,
        identifiers.bid,
        identifiers.ad,
        identifiers.impression
    );

    let Some(runtime_info) = get_runtime_info(identifiers, device_info) else {
        log::error!("{} Failed to construct runtime info", LOG_PREFIX);
        // TODO: errorTrackingUrlHandler, CtaAdInvalidPayload, CtaAdIvaError
        return failure_cb();
    };

    let payload = match &action.code {
        IvaV4ExtensionAdParametersActionCode::AddToCart => match &action.payload {
            IvaV4ExtensionAdParametersActionPayload::AddToCart(atc) => {
                Some(CTAServiceRequestPayload::AddToCart(AddToCartPayload {
                    product_asin: atc.asin.clone(),
                    offer_listing_id: None,
                }))
            }
            IvaV4ExtensionAdParametersActionPayload::SendMeMore(_) => {
                // TODO: CtaAdInvalidPayload, CtaAdIvaError
                log::error!("{} Add to cart invalid props", LOG_PREFIX);
                None
            }
        },
        IvaV4ExtensionAdParametersActionCode::SendToPhone => match &action.payload {
            IvaV4ExtensionAdParametersActionPayload::SendMeMore(smm) => {
                Some(CTAServiceRequestPayload::SendToPhone(SendToPhonePayload {
                    email_setting_id: smm.esi.clone(),
                    notification_setting_id: smm.nsi.clone(),
                }))
            }
            IvaV4ExtensionAdParametersActionPayload::AddToCart(_) => {
                // TODO: CtaAdInvalidPayload, CtaAdIvaError
                log::error!("{} Send to phone invalid props", LOG_PREFIX);
                None
            }
        },
        IvaV4ExtensionAdParametersActionCode::SendMeMore => match &action.payload {
            IvaV4ExtensionAdParametersActionPayload::SendMeMore(smm) => {
                Some(CTAServiceRequestPayload::SendMeMore(SendMeMorePayload {
                    email_setting_id: smm.esi.clone(),
                }))
            }
            IvaV4ExtensionAdParametersActionPayload::AddToCart(_) => {
                // TODO: CtaAdInvalidPayload, CtaAdIvaError
                log::error!("{} Send me more invalid props", LOG_PREFIX);
                None
            }
        },
    };

    let Some(cta_payload) = payload else {
        // TODO: errorTrackingUrlHandler
        return failure_cb();
    };

    let parameters = CTAServiceRequestParams {
        runtime_info,
        cta_payload,
    };

    // TODO: CTA_AD_BUTTON_CLICK

    network_client.call_to_action_request(
        parameters,
        move |_response| {
            // TODO: CtaAdInvokeSuccess
            success_cb();
        },
        move |error| {
            // TODO: errorTrackingUrlHandler, CtaAdInvokeFailure, CtaAdIvaError, onCTAError
            log::error!("{} Request failed: {}", LOG_PREFIX, error);
            failure_cb();
        },
    )
}

fn get_runtime_info(
    identifiers: &IvaV4ExtensionAdParametersIdentifiers,
    device_info: &DeviceInformation,
) -> Option<RuntimeInfo> {
    let Ok(creative_id) = identifiers.creative.parse() else {
        log::error!(
            "{} failed to parse creative_id '{}' as u64",
            LOG_PREFIX,
            identifiers.creative
        );
        return None;
    };

    let Ok(ad_id) = identifiers.ad.parse() else {
        log::error!("{} ad id is not a number {}", LOG_PREFIX, identifiers.ad);
        return None;
    };

    Some(RuntimeInfo {
        app_name: APP_NAME.to_string(),
        device_name: device_info.model_name.clone(),
        impression_id: identifiers.impression.clone(),
        bid_id: identifiers.bid.clone(),
        creative_id,
        ad_id,
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_data::{create_test_atc_interactive_video_ad, TestAdType};
    use crate::test_utils::*;
    use ads_playback_types::iva_v4_extension::{
        IvaV4ExtensionAdParameters, IvaV4ExtensionAdParametersAction,
        IvaV4ExtensionAdParametersActionCode, IvaV4ExtensionAdParametersActionPayload,
        IvaV4ExtensionAdParametersActionPayloadAddToCart,
        IvaV4ExtensionAdParametersActionPayloadSendMeMore,
    };
    use ads_playback_types::test_data::*;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::device_information::DeviceInformation;
    use ignx_compositron::prelude::*;
    use network::RequestError;
    use network_parser::core::network_parse_from_str;
    use rstest::*;

    #[rstest]
    fn test_get_runtime_info_success() {
        let identifiers = IvaV4ExtensionAdParametersIdentifiers {
            creative: "123".to_string(),
            bid: "bid_123".to_string(),
            ad: "456".to_string(),
            impression: "imp_789".to_string(),
        };

        let device_info = DeviceInformation::new();

        let result = get_runtime_info(&identifiers, &device_info);

        let runtime_info = result.unwrap();
        assert_eq!(runtime_info.app_name, APP_NAME);
        assert_eq!(runtime_info.device_name, device_info.model_name);
        assert_eq!(runtime_info.impression_id, identifiers.impression);
        assert_eq!(runtime_info.bid_id, identifiers.bid);
        assert_eq!(runtime_info.creative_id, 123);
        assert_eq!(runtime_info.ad_id, 456);
    }

    #[rstest]
    fn test_get_runtime_info_invalid_creative_id() {
        let identifiers = IvaV4ExtensionAdParametersIdentifiers {
            creative: "not_a_number".to_string(),
            bid: "bid_123".to_string(),
            ad: "456".to_string(),
            impression: "imp_789".to_string(),
        };

        let device_info = DeviceInformation::new();

        let result = get_runtime_info(&identifiers, &device_info);

        assert!(result.is_none());
    }

    #[rstest]
    fn test_get_runtime_info_invalid_ad_id() {
        let identifiers = IvaV4ExtensionAdParametersIdentifiers {
            creative: "123".to_string(),
            bid: "bid_123".to_string(),
            ad: "not_a_number".to_string(),
            impression: "imp_789".to_string(),
        };

        let device_info = DeviceInformation::new();

        let result = get_runtime_info(&identifiers, &device_info);

        assert!(result.is_none());
    }

    pub fn create_test_atc_interactive_video_ad_with_action(
        ad_type: TestAdType,
        ad_instance_id: impl Into<String>,
        action: Option<IvaV4ExtensionAdParametersAction>,
    ) -> InteractiveVideoAd {
        let mut ad = create_test_atc_interactive_video_ad(ad_type, ad_instance_id);

        match (&mut ad.extension.data, action) {
            (AdExtensionData::Live { parameters, .. }, action) => {
                parameters.action = action.into_iter().collect();
            }
            (AdExtensionData::VodOrLinear { parameters, .. }, action) => {
                parameters.base.action = action.into_iter().collect();
            }
        }

        ad
    }

    fn deserialize_ad_parameters(payload: &str) -> IvaV4ExtensionAdParametersAction {
        network_parse_from_str::<IvaV4ExtensionAdParameters>(payload)
            .expect("Failed to deserialize payload")
            .action
            .pop()
            .expect("Expected to have an action")
    }

    pub fn get_sample_add_to_cart_action() -> IvaV4ExtensionAdParametersAction {
        deserialize_ad_parameters(ADD_TO_CART_AD_PARAMETERS)
    }

    pub fn get_sample_send_to_phone_action() -> IvaV4ExtensionAdParametersAction {
        deserialize_ad_parameters(SEND_TO_PHONE_AD_PARAMETERS)
    }

    pub fn get_sample_send_me_more_action() -> IvaV4ExtensionAdParametersAction {
        deserialize_ad_parameters(SEND_ME_MORE_AD_PARAMETERS)
    }

    #[rstest]
    #[case(
        CTAServiceRequestPayload::AddToCart(AddToCartPayload {
            product_asin: DEFAULT_ATC_ASIN.to_string(),
            offer_listing_id: None
        }),
        get_sample_add_to_cart_action(),
    )]
    #[case(
        CTAServiceRequestPayload::SendToPhone(SendToPhonePayload {
            email_setting_id: DEFAULT_STP_ESI.to_string(),
            notification_setting_id: DEFAULT_STP_NSI.to_string()
        }),
        get_sample_send_to_phone_action(),
    )]
    #[case(
        CTAServiceRequestPayload::SendMeMore(SendMeMorePayload {
            email_setting_id: DEFAULT_SMM_ESI.to_string(),
        }),
        get_sample_send_me_more_action(),
    )]
    fn test_cta_handler_success(
        #[case] expected_payload: CTAServiceRequestPayload,
        #[case] action: IvaV4ExtensionAdParametersAction,
    ) {
        launch_only_app_context(|ctx| {
            let interactive_video_ad = create_test_atc_interactive_video_ad_with_action(
                TestAdType::Live,
                "test_ad",
                Some(action),
            );

            let mut network_client = MockNetworkClient::default();
            network_client
                .expect_call_to_action_request()
                .withf(move |params: &CTAServiceRequestParams, _, _| {
                    params.cta_payload == expected_payload
                })
                .times(1)
                .returning(|_, success_cb, _| {
                    success_cb(CTAServiceResponse {
                        message: "Success".to_string(),
                    });
                });

            let device_information = DeviceInformation::new();
            let (read, write) = create_signal(ctx.scope(), false);

            call_cta_handler(
                network_client,
                &device_information,
                &interactive_video_ad,
                move || {
                    write.set(true);
                },
                || assert!(false, "Failure callback should not be called"),
            );

            assert!(read.get());
        });
    }

    #[test]
    fn test_cta_handler_no_action() {
        launch_only_app_context(|ctx| {
            let interactive_video_ad =
                create_test_atc_interactive_video_ad_with_action(TestAdType::Live, "test_ad", None);

            let network_client = MockNetworkClient::default();
            let device_information = DeviceInformation::new();
            let (read, write) = create_signal(ctx.scope(), false);

            call_cta_handler(
                network_client,
                &device_information,
                &interactive_video_ad,
                || assert!(false, "Success callback should not be called"),
                move || {
                    write.set(true);
                },
            );

            assert!(read.get());
        });
    }

    #[test]
    fn test_cta_handler_invalid_runtime_info() {
        launch_only_app_context(|ctx| {
            let mut interactive_video_ad = create_test_atc_interactive_video_ad_with_action(
                TestAdType::Live,
                "test_ad",
                Some(get_sample_add_to_cart_action()),
            );

            match &mut interactive_video_ad.extension.data {
                AdExtensionData::Live { parameters, .. } => {
                    parameters.identifiers.creative = "not_a_number".to_string();
                }
                AdExtensionData::VodOrLinear { parameters, .. } => {
                    parameters.base.identifiers.creative = "not_a_number".to_string();
                }
            };

            let network_client = MockNetworkClient::default();
            let device_information = DeviceInformation::new();
            let (read, write) = create_signal(ctx.scope(), false);

            call_cta_handler(
                network_client,
                &device_information,
                &interactive_video_ad,
                || assert!(false, "Success callback should not be called"),
                move || {
                    write.set(true);
                },
            );

            assert!(read.get());
        });
    }

    #[rstest]
    #[case::add_to_cart_with_send_me_more_payload(
        IvaV4ExtensionAdParametersActionCode::AddToCart,
        IvaV4ExtensionAdParametersActionPayload::SendMeMore(
            IvaV4ExtensionAdParametersActionPayloadSendMeMore {
                esi: DEFAULT_SMM_ESI.to_string(),
                nsi: DEFAULT_SMM_NSI.to_string(),
            }
        )
    )]
    #[case::send_to_phone_with_add_to_cart_payload(
        IvaV4ExtensionAdParametersActionCode::SendToPhone,
        IvaV4ExtensionAdParametersActionPayload::AddToCart(
            IvaV4ExtensionAdParametersActionPayloadAddToCart {
                asin: DEFAULT_ATC_ASIN.to_string(),
            }
        )
    )]
    #[case::send_me_more_with_add_to_cart_payload(
        IvaV4ExtensionAdParametersActionCode::SendMeMore,
        IvaV4ExtensionAdParametersActionPayload::AddToCart(
            IvaV4ExtensionAdParametersActionPayloadAddToCart {
                asin: DEFAULT_ATC_ASIN.to_string(),
            }
        )
    )]
    fn test_cta_handler_invalid_payload(
        #[case] code: IvaV4ExtensionAdParametersActionCode,
        #[case] payload: IvaV4ExtensionAdParametersActionPayload,
    ) {
        launch_only_app_context(|ctx| {
            let mismatched_action = IvaV4ExtensionAdParametersAction {
                code,
                payload,
                token: "fake".to_string(),
                version: "v1".to_string(),
            };

            let interactive_video_ad = create_test_atc_interactive_video_ad_with_action(
                TestAdType::Live,
                "test_ad",
                Some(mismatched_action),
            );

            let network_client = MockNetworkClient::default();
            let device_information = DeviceInformation::new();
            let (read, write) = create_signal(ctx.scope(), false);

            call_cta_handler(
                network_client,
                &device_information,
                &interactive_video_ad,
                || assert!(false, "Success callback should not be called"),
                move || {
                    write.set(true);
                },
            );

            assert!(read.get());
        });
    }

    #[test]
    fn test_cta_handler_network_failure() {
        launch_only_app_context(|ctx| {
            let interactive_video_ad = create_test_atc_interactive_video_ad_with_action(
                TestAdType::Live,
                "test_ad",
                Some(get_sample_add_to_cart_action()),
            );

            let mut network_client = MockNetworkClient::default();
            network_client
                .expect_call_to_action_request()
                .times(1)
                .returning(|_, _, failure_cb| {
                    failure_cb(RequestError::Http {
                        code: 401,
                        body: None,
                        headers: vec![],
                    });
                });

            let device_information = DeviceInformation::new();
            let (read, write) = create_signal(ctx.scope(), false);

            call_cta_handler(
                network_client,
                &device_information,
                &interactive_video_ad,
                || assert!(false, "Success callback should not be called"),
                move || {
                    write.set(true);
                },
            );

            assert!(read.get());
        });
    }

    #[test]
    fn test_cta_handler_vod_or_linear() {
        launch_only_app_context(|ctx| {
            let interactive_video_ad = create_test_atc_interactive_video_ad_with_action(
                TestAdType::VodLinear,
                "test_ad",
                Some(get_sample_add_to_cart_action()),
            );

            let expected_payload = CTAServiceRequestPayload::AddToCart(AddToCartPayload {
                product_asin: DEFAULT_ATC_ASIN.to_string(),
                offer_listing_id: None,
            });

            let mut network_client = MockNetworkClient::default();
            network_client
                .expect_call_to_action_request()
                .withf(move |params: &CTAServiceRequestParams, _, _| {
                    params.cta_payload == expected_payload
                })
                .times(1)
                .returning(|_, success_cb, _| {
                    success_cb(CTAServiceResponse {
                        message: "Success".to_string(),
                    });
                });

            let device_information = DeviceInformation::new();
            let (read, write) = create_signal(ctx.scope(), false);

            call_cta_handler(
                network_client,
                &device_information,
                &interactive_video_ad,
                move || {
                    write.set(true);
                },
                || assert!(false, "Failure callback should not be called"),
            );

            assert!(read.get());
        });
    }
}
