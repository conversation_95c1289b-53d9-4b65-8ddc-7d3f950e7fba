#![allow(clippy::unused_self, dead_code)]

use crate::metrics::error_code::ErrorCode;
use crate::metrics::event_type::EventType;
use crate::metrics::metric_name::MetricName;
use mockall::automock;

#[cfg_attr(test, double)]
pub use cross_app_events::metric_emitter::MetricEmitter;
#[cfg(test)]
use mockall_double::double;

use cross_app_events::metric_emitter::MetricEmitterFns;

pub struct Reporter {
    default_dimensions: Vec<(String, String)>,
    metric_emitter: MetricEmitter,
}

#[automock]
impl Reporter {
    pub fn new(metric_emitter: MetricEmitter, default_dimensions: Vec<(String, String)>) -> Self {
        Self {
            default_dimensions,
            metric_emitter,
        }
    }

    pub fn metric(
        &self,
        metric_name: MetricName,
        metric_value: u128,
        mut custom_dimensions: Vec<(String, String)>,
    ) {
        custom_dimensions.extend_from_slice(self.default_dimensions.as_slice());
        self.metric_emitter
            .emit(metric_name.as_str(), metric_value, custom_dimensions);
    }

    pub fn player_event(
        &self,
        _ad_instance_id: String,
        _error_code: ErrorCode,
        _event_type: EventType,
    ) {
        // TODO: https://taskei.amazon.dev/tasks/D228950721
    }

    pub fn ad_tracking(
        &self,
        _tracking_url_template: String,
        _event_type: String,
        _error_code: Option<ErrorCode>,
        _elapsed_time: Option<f64>,
        _creative_version: Option<String>,
    ) {
        // TODO: https://taskei.amazon.dev/tasks/D228950721
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use cross_app_events::metric_emitter::MockMetricEmitter;
    use mockall::predicate;
    use rstest::*;

    const SOURCE_PAGE: &str = "PlaybackPage";

    #[rstest]
    fn test_metric_with_no_dimensions() {
        let mut mock_emitter = MockMetricEmitter::default();

        let metric_name = MetricName::VastValidationFailure;
        let metric_value = 42;

        mock_emitter
            .expect_emit()
            .with(
                predicate::eq(metric_name.as_str()),
                predicate::eq(metric_value),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());

        let reporter = Reporter::new(mock_emitter, vec![]);

        reporter.metric(metric_name, metric_value, vec![]);
    }

    #[rstest]
    fn test_metric_with_custom_dimensions() {
        let mut mock_emitter = MockMetricEmitter::default();

        let metric_name = MetricName::VastValidationFailure;
        let metric_value = 42;
        let custom_dimensions = vec![("custom_key1".to_string(), "custom_value1".to_string())];

        mock_emitter
            .expect_emit()
            .with(
                predicate::eq(metric_name.as_str()),
                predicate::eq(metric_value),
                predicate::eq(custom_dimensions.clone()),
            )
            .times(1)
            .return_const(());

        let reporter = Reporter::new(mock_emitter, vec![]);

        reporter.metric(metric_name, metric_value, custom_dimensions);
    }

    #[rstest]
    fn test_metric_with_default_dimensions() {
        let mut mock_emitter = MockMetricEmitter::default();

        let metric_name = MetricName::VastValidationFailure;
        let metric_value = 42;
        let default_dimensions = vec![("custom_key2".to_string(), "custom_value2".to_string())];

        mock_emitter
            .expect_emit()
            .with(
                predicate::eq(metric_name.as_str()),
                predicate::eq(metric_value),
                predicate::eq(default_dimensions.clone()),
            )
            .times(1)
            .return_const(());

        let reporter = Reporter::new(mock_emitter, default_dimensions);

        reporter.metric(metric_name, metric_value, vec![]);
    }

    #[rstest]
    fn test_metric_with_both_default_and_custom_dimensions() {
        let mut mock_emitter = MockMetricEmitter::default();

        let metric_name = MetricName::VastValidationFailure;
        let metric_value = 42;
        let custom_dimensions = vec![("custom_key1".to_string(), "custom_value1".to_string())];
        let default_dimensions = vec![("custom_key2".to_string(), "custom_value2".to_string())];

        let mut combined_dimensions = custom_dimensions.clone();
        combined_dimensions.extend_from_slice(default_dimensions.as_slice());

        mock_emitter
            .expect_emit()
            .with(
                predicate::eq(metric_name.as_str()),
                predicate::eq(metric_value),
                predicate::eq(combined_dimensions),
            )
            .times(1)
            .return_const(());

        let reporter = Reporter::new(mock_emitter, default_dimensions);

        reporter.metric(metric_name, metric_value, custom_dimensions);
    }

    #[rstest]
    fn test_player_event() {
        let reporter = Reporter::new(MetricEmitter::default(), vec![]);
        reporter.player_event(
            "todo".to_string(),
            ErrorCode::GenericError,
            EventType::PlaybackFailure,
        );
    }

    #[rstest]
    fn test_ad_tracking() {
        let reporter = Reporter::new(MetricEmitter::default(), vec![]);
        reporter.ad_tracking(
            "fake-url".to_string(),
            "fake-event-type".to_string(),
            None,
            None,
            None,
        );
    }
}
