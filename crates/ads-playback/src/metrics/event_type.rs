#![allow(dead_code, reason = "not used yet")]

use cfg_test_attr_derive::derive_test_only;
use std::fmt;

#[derive_test_only(Clone, Debug, PartialEq)]
pub enum EventType {
    PlaybackFailure,
    VastParsingError,
}

impl EventType {
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::PlaybackFailure => "PlaybackFailure",
            Self::VastParsingError => "VastParsingError",
        }
    }
}

impl fmt::Display for EventType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(EventType::PlaybackFailure, "PlaybackFailure")]
    #[case(EventType::VastParsingError, "VastParsingError")]
    fn test_event_type_conversions(#[case] event_type: EventType, #[case] expected: &str) {
        assert_eq!(event_type.as_str(), expected);
        assert_eq!(event_type.to_string(), expected);
    }
}
