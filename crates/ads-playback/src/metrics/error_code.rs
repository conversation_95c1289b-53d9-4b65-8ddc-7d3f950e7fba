#![allow(dead_code, reason = "not used yet")]

use cfg_test_attr_derive::derive_test_only;
use std::fmt;

#[derive_test_only(<PERSON><PERSON>, Debug, PartialEq)]
pub enum ErrorCode {
    None,
    InvalidActionableAd,
    NoAdParameters,
    InvalidAdParameters,
    TooManyIvaExtensions,
    GenericError,
    CtaHandlerNetworkError,
    CtaHandlerBuildError,
    CtaHandlerServerError,
    CtaHandlerClientError,
    CtaHandlerInvokeError,
    UnexpectedError,
    CtaHandlerTimeoutError,
    NoInteractiveCreativeFile,
    InvalidInteractiveCreativeFile,
}

impl ErrorCode {
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::None => "none",
            Self::InvalidActionableAd => "RTH-IVA_1001",
            Self::NoAdParameters => "RTH-IVA_1005",
            Self::InvalidAdParameters => "RTH-IVA_1007",
            Self::TooManyIvaExtensions => "RTH-IVA_1010",
            Self::GenericError => "RTH-IVA_1300",
            Self::CtaHandlerNetworkError => "RTH-IVA_2000",
            Self::CtaHandlerBuildError => "RTH-IVA_2001",
            Self::CtaHandlerServerError => "RTH-IVA_2002",
            Self::CtaHandlerClientError => "RTH-IVA_2003",
            Self::CtaHandlerInvokeError => "RTH-IVA_2004",
            Self::UnexpectedError => "RTH-IVA_2006",
            Self::CtaHandlerTimeoutError => "RTH-IVA_2007",
            Self::NoInteractiveCreativeFile => "RTH-IVA_1002",
            Self::InvalidInteractiveCreativeFile => "RTH-IVA_1003",
        }
    }
}

impl fmt::Display for ErrorCode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(ErrorCode::None, "none")]
    #[case(ErrorCode::InvalidActionableAd, "RTH-IVA_1001")]
    #[case(ErrorCode::NoAdParameters, "RTH-IVA_1005")]
    #[case(ErrorCode::InvalidAdParameters, "RTH-IVA_1007")]
    #[case(ErrorCode::TooManyIvaExtensions, "RTH-IVA_1010")]
    #[case(ErrorCode::GenericError, "RTH-IVA_1300")]
    #[case(ErrorCode::CtaHandlerNetworkError, "RTH-IVA_2000")]
    #[case(ErrorCode::CtaHandlerBuildError, "RTH-IVA_2001")]
    #[case(ErrorCode::CtaHandlerServerError, "RTH-IVA_2002")]
    #[case(ErrorCode::CtaHandlerClientError, "RTH-IVA_2003")]
    #[case(ErrorCode::CtaHandlerInvokeError, "RTH-IVA_2004")]
    #[case(ErrorCode::UnexpectedError, "RTH-IVA_2006")]
    #[case(ErrorCode::CtaHandlerTimeoutError, "RTH-IVA_2007")]
    #[case(ErrorCode::NoInteractiveCreativeFile, "RTH-IVA_1002")]
    #[case(ErrorCode::InvalidInteractiveCreativeFile, "RTH-IVA_1003")]
    fn test_error_code_conversions(#[case] error_code: ErrorCode, #[case] expected: &str) {
        assert_eq!(error_code.as_str(), expected);
        assert_eq!(error_code.to_string(), expected);
    }
}
