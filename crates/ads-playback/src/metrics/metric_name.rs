#![allow(dead_code, reason = "not used yet")]

use cfg_test_attr_derive::derive_test_only;
use std::fmt;

#[derive_test_only(<PERSON><PERSON>, Debug, PartialEq)]
pub enum MetricName {
    VastValidationFailure,
    VastExtensionInvalidVersion,
    IvaTrackingMissingTracking,
    PauseAdsCancelBeforeShown,
}

impl MetricName {
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::VastValidationFailure => "Playback.Iva.vastValidationFailure",
            Self::VastExtensionInvalidVersion => "Playback.Iva.vastExtensionInvalidVersion",
            Self::IvaTrackingMissingTracking => "Playback.Iva.IvaTracking.missingTracking",
            Self::PauseAdsCancelBeforeShown => "Playback.PauseAds.CancelBeforeShown",
        }
    }
}

impl fmt::Display for MetricName {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(
        MetricName::VastValidationFailure,
        "Playback.Iva.vastValidationFailure"
    )]
    #[case(
        MetricName::VastExtensionInvalidVersion,
        "Playback.Iva.vastExtensionInvalidVersion"
    )]
    #[case(
        MetricName::IvaTrackingMissingTracking,
        "Playback.Iva.IvaTracking.missingTracking"
    )]
    #[case(
        MetricName::PauseAdsCancelBeforeShown,
        "Playback.PauseAds.CancelBeforeShown"
    )]
    fn test_metric_name_conversions(#[case] metric_name: MetricName, #[case] expected: &str) {
        assert_eq!(metric_name.as_str(), expected);
        assert_eq!(metric_name.to_string(), expected);
    }
}
