use ads_playback_types::interactive_video_ad::InteractiveVideoAd;
use ads_playback_types::surface_x::base_template::{
    BaseSurfaceXTemplateProps, SurfaceXTemplateType,
};
use ads_playback_types::surface_x::widget_templates::{
    IconWidgetPropsTemplate, IconWidgetTemplate, SurfaceXWidgetTemplates, SurfaceXWidgetTypes,
    TextWidgetPropsTemplate, TextWidgetTemplate,
};
use ads_playback_types::test_data::{
    ADD_TO_CART_AD_PARAMETERS, DEFAULT_SURFACE_X_TEMPLATE_ID,
    DEFAULT_SURFACE_X_TEMPLATE_MAJOR_VERSION, DEFAULT_SURFACE_X_TEMPLATE_MINOR_VERSION,
    SEND_ME_MORE_AD_PARAMETERS, SEND_TO_PHONE_AD_PARAMETERS,
};
use ads_playback_types::test_utils::{
    ActionableAdExtensionBuilder, AdExtensionDataBuilder, InteractiveVideoAdBuilder,
};
use ignx_compositron::color::Color;
use ignx_compositron::player_data::AdTimeInfo;
use player::test_utils::{
    AdMetadataBuilder, ContentInfoBuilder, TimelineBuilder, TimelineItemBuilder,
};
use player::{AdMetadata, ElementNode, LiveAds, Node, TextNode, Timeline};
use std::collections::HashMap;

use crate::surface_x::template_hydration::IvaBaselineTemplateData;
use crate::surface_x::widgets::types::SurfaceXWidgetTemplateData;
use crate::surface_x::widgets::*;
use ads_playback_types::surface_x::ad_formats::baseline_iva::{
    BaselineIVATemplate, BaselineIVATemplateProps, CardPosition,
};
use amzn_fable_tokens::{FableIcon, FableText};
use ignx_compositron::shared::Shared;

pub fn create_ad_metadata(ad_instance_id: &str, ad_parameters_payload: &str) -> AdMetadata {
    let mut ad_metadata = AdMetadataBuilder::default()
        .ad_instance_id(ad_instance_id)
        .build();

    ad_metadata.extensions = Some(vec![ElementNode {
        name: "Extension".to_string(),
        attributes: {
            let mut map = HashMap::new();
            map.insert("type".to_string(), "ava.amazon.com/actionable".to_string());
            map
        },
        children: vec![Node::ElementNode(create_actionable_ad(
            ad_instance_id,
            ad_parameters_payload,
        ))],
    }]);

    ad_metadata
}

pub fn create_actionable_ad(ad_instance_id: &str, ad_parameters_payload: &str) -> ElementNode {
    ElementNode {
        name: "ActionableAd".to_string(),
        attributes: {
            let mut map = HashMap::new();
            map.insert("version".to_string(), "4.0".to_string());
            map.insert("id".to_string(), ad_instance_id.to_string());
            map
        },
        children: vec![
            Node::ElementNode(ElementNode {
                name: "AdParameters".to_string(),
                attributes: HashMap::new(),
                children: vec![Node::TextNode(TextNode {
                    text: ad_parameters_payload.to_string(),
                })],
            }),
            Node::ElementNode(ElementNode {
                name: "InteractiveCreativeFile".to_string(),
                attributes: {
                    let mut map = HashMap::new();
                    map.insert("apiFramework".to_string(), "SFX".to_string());
                    map
                },
                children: vec![Node::TextNode(TextNode {
                    // Create the contents of the InteractiveCreativeFile node using default constants
                    // Looks something like {"tid": "IVA", "m": 1, "mq": 1}
                    text: format!(
                        "{{\"tid\": \"{}\", \"m\": {}, \"mq\": {}}}",
                        DEFAULT_SURFACE_X_TEMPLATE_ID,
                        DEFAULT_SURFACE_X_TEMPLATE_MAJOR_VERSION,
                        DEFAULT_SURFACE_X_TEMPLATE_MINOR_VERSION
                    ),
                })],
            }),
        ],
    }
}

pub fn create_live_ad(ad_instance_id: &str, ad_parameters_payload: &str) -> LiveAds {
    LiveAds {
        ad_id: ad_instance_id.to_owned(),
        extensions: Some(vec![create_actionable_ad(
            ad_instance_id,
            ad_parameters_payload,
        )]),
        time_info: Some(AdTimeInfo {
            start_ms: 0.0,
            end_ms: 10_000.0,
        }),
        tracking_url: Some("www.tracking_url.com".to_owned()),
    }
}

pub fn create_timeline_with_pre_roll_and_mid_roll_all_interactive_ads() -> Timeline {
    let content_id_1 = "interactive_pre_roll_ad";
    let content_id_2 = "feature_1";
    let content_id_3 = "interactive_mid_roll_ad";
    let content_id_4 = "feature_2";
    TimelineBuilder::default()
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_1)
                .start_time(0)
                .end_time(30)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_2)
                .start_time(30)
                .end_time(60)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_3)
                .start_time(60)
                .end_time(75)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_4)
                .start_time(75)
                .end_time(105)
                .build(),
        )
        .add_content_info(
            content_id_1,
            ContentInfoBuilder::default()
                .ad_metadata(create_ad_metadata(content_id_1, ADD_TO_CART_AD_PARAMETERS))
                .build(),
        )
        .add_content_info(
            content_id_2,
            ContentInfoBuilder::default()
                .feature_metadata(Some(content_id_2.to_string()))
                .build(),
        )
        .add_content_info(
            content_id_3,
            ContentInfoBuilder::default()
                .ad_metadata(create_ad_metadata(
                    content_id_3,
                    SEND_TO_PHONE_AD_PARAMETERS,
                ))
                .build(),
        )
        .add_content_info(
            content_id_4,
            ContentInfoBuilder::default()
                .feature_metadata(Some(content_id_4.to_string()))
                .build(),
        )
        .build()
}

pub fn create_timeline_with_pre_roll_and_mid_roll_with_mixed_ads() -> Timeline {
    let content_id_1 = "interactive_pre_roll_ad";
    let content_id_2 = "feature_1";
    let content_id_3 = "non_interactive_mid_roll_ad";
    let content_id_4 = "interactive_mid_roll_ad";
    let content_id_5 = "another_interactive_pre_roll_ad";
    let content_id_6 = "feature_2";
    TimelineBuilder::default()
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_1)
                .start_time(0)
                .end_time(30)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_2)
                .start_time(30)
                .end_time(90)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_3)
                .start_time(90)
                .end_time(105)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_4)
                .start_time(105)
                .end_time(120)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_5)
                .start_time(120)
                .end_time(150)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_6)
                .start_time(150)
                .end_time(210)
                .build(),
        )
        .add_content_info(
            content_id_1,
            ContentInfoBuilder::default()
                .ad_metadata(create_ad_metadata(content_id_1, ADD_TO_CART_AD_PARAMETERS))
                .build(),
        )
        .add_content_info(
            content_id_2,
            ContentInfoBuilder::default()
                .feature_metadata(Some(content_id_2.to_string()))
                .build(),
        )
        .add_content_info(
            content_id_3,
            ContentInfoBuilder::default()
                .ad_metadata(AdMetadataBuilder::default().ad_instance_id(content_id_3))
                .build(),
        )
        .add_content_info(
            content_id_4,
            ContentInfoBuilder::default()
                .ad_metadata(create_ad_metadata(content_id_4, SEND_ME_MORE_AD_PARAMETERS))
                .build(),
        )
        .add_content_info(
            content_id_5,
            ContentInfoBuilder::default()
                .ad_metadata(AdMetadataBuilder::default().ad_instance_id(content_id_5))
                .build(),
        )
        .add_content_info(
            content_id_6,
            ContentInfoBuilder::default()
                .feature_metadata(Some(content_id_6.to_string()))
                .build(),
        )
        .build()
}

pub fn create_timeline_with_no_ads() -> Timeline {
    let content_id_1 = "feature-1";
    let content_id_2 = "feature-2";
    TimelineBuilder::default()
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_1)
                .start_time(0)
                .end_time(60)
                .build(),
        )
        .add_item(
            TimelineItemBuilder::default()
                .content_id(content_id_2)
                .start_time(60)
                .end_time(120)
                .build(),
        )
        .add_content_info(
            content_id_1,
            ContentInfoBuilder::default()
                .feature_metadata(Some(content_id_1.to_string()))
                .build(),
        )
        .add_content_info(
            content_id_2,
            ContentInfoBuilder::default()
                .feature_metadata(Some(content_id_2.to_string()))
                .build(),
        )
        .build()
}

pub enum TestAdType {
    Live,
    VodLinear,
}

pub fn create_test_atc_interactive_video_ad(
    ad_type: TestAdType,
    ad_instance_id: impl Into<String>,
) -> InteractiveVideoAd {
    let builder = AdExtensionDataBuilder::default().with_atc_payload();

    let extension_data = match ad_type {
        TestAdType::Live => builder.build_live(),
        TestAdType::VodLinear => builder.build_vod_linear(),
    };

    let extension = ActionableAdExtensionBuilder::default()
        .with_data(extension_data)
        .build();

    InteractiveVideoAdBuilder::default()
        .with_id(ad_instance_id)
        .with_extension(extension)
        .build()
}

pub fn create_test_iva_baseline_template_data() -> IvaBaselineTemplateData {
    IvaBaselineTemplateData {
        main_icon: SurfaceXWidgetTemplateData::Icon(Shared::new(SurfaceXIconWidgetTemplateData {
            iter_id: "test_icon".to_string(),
            icon_name: FableIcon::AD_FREE.to_string(),
            color: Some(Color::white()),
            font_size: None,
            container_style: None,
        })),
        main_content: vec![Shared::new(SurfaceXWidgetTemplateData::Text(Shared::new(
            SurfaceXTextWidgetTemplateData {
                iter_id: "test_text".to_string(),
                text: "Test Content".to_string(),
                type_ramp: FableText::TYPE_BODY200.into(),
                color: None,
                max_lines: None,
                text_alignment: None,
                line_height: None,
                container_style: None,
                truncation_mode: None,
                text_decoration_line: None,
            },
        )))],
        cta_button: Some(Shared::new(SurfaceXWidgetTemplateData::PrimaryButton(
            Shared::new(SurfaceXPrimaryButtonWidgetTemplateData {
                text: "CTA Button text".to_string(),
                icon_name: None,
                iter_id: "iter_id".to_string(),
                container_style: None,
            }),
        ))),
        hint_text: None,
        card_position: None,
        cta_background_color: None,
    }
}

pub fn create_test_iva_baseline_template() -> BaselineIVATemplate {
    let main_icon = IconWidgetTemplate {
        type_field: SurfaceXWidgetTypes::IconWidget,
        props: IconWidgetPropsTemplate {
            icon_name: "star".to_string(),
            color: Some("#FF0000".to_string()),
            font_size: Some(24),
            container_style: None,
        },
    };

    let main_content = TextWidgetTemplate {
        type_field: SurfaceXWidgetTypes::TextWidget,
        props: TextWidgetPropsTemplate {
            text: "Test Content".to_string(),
            type_ramp: "body".to_string(),
            color: None,
            text_align: None,
            line_height: None,
            ellipsize_mode: None,
            number_of_lines: None,
            text_decoration_line: None,
            container_style: None,
        },
    };

    let hint_text = TextWidgetTemplate {
        type_field: SurfaceXWidgetTypes::TextWidget,
        props: TextWidgetPropsTemplate {
            text: "Press OK to continue".to_string(),
            type_ramp: "body".to_string(),
            color: Some("#CCCCCC".to_string()),
            text_align: None,
            line_height: None,
            ellipsize_mode: None,
            number_of_lines: None,
            text_decoration_line: None,
            container_style: None,
        },
    };

    let template_props = BaselineIVATemplateProps {
        base: BaseSurfaceXTemplateProps {
            mandatory_fields: vec![],
            animation: None,
            accessibility_messages: None,
        },
        main_icon: SurfaceXWidgetTemplates::IconWidget(main_icon),
        main_content: vec![SurfaceXWidgetTemplates::TextWidget(main_content)],
        cta_button: None,
        hint_text: Some(SurfaceXWidgetTemplates::TextWidget(hint_text)),
        card_position: Some(CardPosition::Right),
        cta_background_color: Some("#000000".to_string()),
    };

    BaselineIVATemplate {
        template_type: SurfaceXTemplateType::BaselineIVA,
        template_props,
    }
}
