use crate::surface_x::widgets::*;
use ignx_compositron::prelude::safe::*;
use ignx_compositron::{compose, compose_option, Composer};

use super::widgets::types::SurfaceXWidgetTemplateData;

#[Composer]
pub fn WidgetRenderer<'s>(
    ctx: &AppContext<'s>,
    widget_data: RefSignal<'s, SurfaceXWidgetTemplateData>,
) -> MemoComposable<'s> {
    compose! {
         Memo(item_builder: Box::new(move |ctx| {
                match widget_data.get() {
                    SurfaceXWidgetTemplateData::Text(data) => {
                        compose_option! {
                            SurfaceXTextWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::Image(data) => {
                        compose_option! {
                            SurfaceXImageWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::Badge(data) => {
                        compose_option! {
                            SurfaceXBadgeWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::Icon(data) => {
                        compose_option! {
                            SurfaceXIconWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::Divider(data) => {
                        compose_option! {
                            SurfaceXDividerWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::PrimaryButton(data) => {
                        compose_option! {
                            SurfaceXPrimaryButtonWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::PrimeLogo(data) => {
                        compose_option! {
                            SurfaceXPrimeLogoWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::StarRating(data) => {
                        compose_option! {
                            SurfaceXStarRatingWidget(template_data: data)
                            .into_any()
                        }
                    },
                    SurfaceXWidgetTemplateData::Row(data) => {
                        compose_option! {
                            SurfaceXRowWidget(template_data: data)
                            .into_any()
                        }
                    }
                }
        }))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::surface_x::widgets::{
        badge_widget::SurfaceXBadgeWidgetTemplateData,
        divider_widget::{SurfaceXDividerOrientation, SurfaceXDividerWidgetTemplateData},
        icon_widget::SurfaceXIconWidgetTemplateData,
        image_widget::SurfaceXImageWidgetTemplateData,
        primary_button_widget::SurfaceXPrimaryButtonWidgetTemplateData,
        prime_logo_widget::SurfaceXPrimeLogoWidgetTemplateData,
        row_widget::SurfaceXRowWidgetTemplateData,
        star_rating_widget::SurfaceXStarRatingWidgetTemplateData,
        text_widget::SurfaceXTextWidgetTemplateData,
        types::SurfaceXRowChildWidgetTemplateData,
    };
    use amzn_fable_tokens::FableText;
    use fableous::typography::type_ramp::TypeRamp;
    use ignx_compositron::{
        app::launch_test,
        shared::Shared,
        test_utils::{node_properties::SceneNodeTree, *},
    };

    // Helper function to run a test with a specific widget data type
    fn test_widget_renderer_with<F>(widget_data: SurfaceXWidgetTemplateData, assert_fn: F)
    where
        F: FnOnce(&SceneNodeTree) + 'static,
    {
        launch_test(
            |ctx| {
                let widget_data =
                    create_ref_signal(ctx.scope(), SharedSignal::from_data(widget_data));

                compose! {
                    WidgetRenderer(widget_data)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                assert_fn(&node_tree);
            },
        );
    }

    #[test]
    fn test_widget_renderer_text() {
        let text_data = SurfaceXTextWidgetTemplateData {
            text: "Test Text".to_string(),
            type_ramp: TypeRamp::from(FableText::TYPE_BODY200),
            color: Some(Color::red()),
            text_alignment: None,
            line_height: None,
            container_style: None,
            truncation_mode: None,
            max_lines: None,
            text_decoration_line: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::Text(Shared::new(text_data)),
            |node_tree| {
                let text_widget = node_tree.find_by_test_id("surface_x_text_widget");
                assert_node_exists!(&text_widget);

                // Find RichTextLabel anywhere in the tree
                let rich_text = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();
                assert_node_exists!(&rich_text);
                assert_eq!(rich_text.get_props().text.unwrap(), "Test Text");
            },
        );
    }

    #[test]
    fn test_widget_renderer_image() {
        let image_data = SurfaceXImageWidgetTemplateData {
            image_url: "https://example.com/image.jpg".to_string(),
            width: 100.0,
            height: 100.0,
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::Image(Shared::new(image_data)),
            |node_tree| {
                let image_widget = node_tree.find_by_test_id("surface_x_image_widget");
                assert_node_exists!(&image_widget);
            },
        );
    }

    #[test]
    fn test_widget_renderer_badge() {
        let badge_data = SurfaceXBadgeWidgetTemplateData {
            text: "Test Badge".to_string(),
            background_color: Color::yellow(),
            type_ramp: None,
            text_color: None,
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::Badge(Shared::new(badge_data)),
            |node_tree| {
                let badge_widget = node_tree.find_by_test_id("surface_x_badge_widget");
                assert_node_exists!(&badge_widget);

                // Verify badge text using the root node tree
                let text_node = node_tree.find_by_props().text("Test Badge").find_first();
                assert_node_exists!(&text_node);
            },
        );
    }

    #[test]
    fn test_widget_renderer_icon() {
        let icon_data = SurfaceXIconWidgetTemplateData {
            icon_name: "test_icon".to_string(),
            color: None,
            font_size: None,
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::Icon(Shared::new(icon_data)),
            |node_tree| {
                let icon_widget = node_tree.find_by_test_id("surface_x_icon_widget");
                assert_node_exists!(&icon_widget);
            },
        );
    }

    #[test]
    fn test_widget_renderer_divider() {
        let divider_data = SurfaceXDividerWidgetTemplateData {
            orientation: SurfaceXDividerOrientation::Horizontal,
            length: 100.0,
            color: None,
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::Divider(Shared::new(divider_data)),
            |node_tree| {
                let divider_widget = node_tree.find_by_test_id("surface_x_divider_widget");
                assert_node_exists!(&divider_widget);
            },
        );
    }

    #[test]
    fn test_widget_renderer_primary_button() {
        let button_data = SurfaceXPrimaryButtonWidgetTemplateData {
            text: "Click Me".to_string(),
            icon_name: None,
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::PrimaryButton(Shared::new(button_data)),
            |node_tree| {
                let button_widget = node_tree.find_by_test_id("surface_x_primary_button_widget");
                assert_node_exists!(&button_widget);

                // Verify button text
                let text_node = node_tree.find_by_props().text("Click Me").find_first();
                assert_node_exists!(&text_node);
            },
        );
    }

    #[test]
    fn test_widget_renderer_prime_logo() {
        let logo_data = SurfaceXPrimeLogoWidgetTemplateData {
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::PrimeLogo(Shared::new(logo_data)),
            |node_tree| {
                let logo_widget = node_tree.find_by_test_id("surface_x_prime_logo_widget");
                assert_node_exists!(&logo_widget);
            },
        );
    }

    #[test]
    fn test_widget_renderer_star_rating() {
        let rating_data = SurfaceXStarRatingWidgetTemplateData {
            rating: 4.5,
            rating_count: "1,234".to_string(),
            star_size: FontSize(24),
            full_star_color: Color::red(),
            empty_star_color: Color::gray(),
            star_separator_width: 4.0,
            show_rating_number: None,
            rating_text_type_ramp: None,
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::StarRating(Shared::new(rating_data)),
            |node_tree| {
                let rating_widget = node_tree.find_by_test_id("surface_x_star_rating_widget");
                assert_node_exists!(&rating_widget);

                // Verify rating count text
                let count_text = node_tree.find_by_props().text("1,234").find_first();
                assert_node_exists!(&count_text);
            },
        );
    }

    #[test]
    fn test_widget_renderer_row() {
        let text_widget_data = Shared::new(SurfaceXTextWidgetTemplateData {
            text: "Test Text".to_string(),
            type_ramp: TypeRamp::from(FableText::TYPE_BODY200),
            color: Some(Color::red()),
            text_alignment: None,
            line_height: None,
            container_style: None,
            truncation_mode: None,
            max_lines: None,
            text_decoration_line: None,
            iter_id: String::new(),
        });
        let row_data = SurfaceXRowWidgetTemplateData {
            row_components: vec![
                SurfaceXRowChildWidgetTemplateData::Text(text_widget_data.clone()),
                SurfaceXRowChildWidgetTemplateData::Text(text_widget_data),
            ],
            container_style: None,
            iter_id: String::new(),
        };

        test_widget_renderer_with(
            SurfaceXWidgetTemplateData::Row(Shared::new(row_data)),
            |node_tree| {
                let row_widget = node_tree.find_by_test_id("surface_x_row_widget");
                assert_node_exists!(&row_widget);

                // Verify there are two text items
                let text_nodes = node_tree.find_by_props().text("Test Text").find_all();
                assert_eq!(
                    text_nodes.len(),
                    2,
                    "Should have two text nodes with 'Test Text'"
                );
            },
        );
    }
}
