use crate::surface_x::widgets::container_styles::SurfaceXContainerStyles;
use amzn_fable_tokens::FableColor;
use cfg_test_attr_derive::derive_test_only;
use fableous::utils::get_ignx_color;
use ignx_compositron::{compose, id::Id, prelude::safe::*, shared::Shared, Composer, Signify};

#[derive(Clone, Signify)]
#[derive_test_only(PartialEq, Debug)]
pub enum SurfaceXDividerOrientation {
    Horizontal,
    Vertical,
}

const DIVIDER_WIDTH: f32 = 3.0;

#[derive(Signify)]
pub struct SurfaceXDividerWidgetTemplateData {
    pub orientation: SurfaceXDividerOrientation,
    pub length: f32,
    pub color: Option<Color>,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXDividerWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXDividerWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXDividerWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let is_vertical = matches!(
        template_data.orientation().get(),
        SurfaceXDividerOrientation::Vertical
    );
    let color = template_data
        .color()
        .get()
        .unwrap_or(get_ignx_color(FableColor::PRIMARY040));
    let length = template_data.length().get();

    let mut container = compose! {
        Column(){
          Rectangle()
            .height(if is_vertical { length } else { DIVIDER_WIDTH })
            .width(if is_vertical { DIVIDER_WIDTH } else { length })
            .border_radius(1000.0)
            .background_color(color)
          }
          .test_id("surface_x_divider_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{app::launch_test, test_utils::ComposableType};

    fn create_default_template_data() -> SurfaceXDividerWidgetTemplateData {
        SurfaceXDividerWidgetTemplateData {
            orientation: SurfaceXDividerOrientation::Vertical,
            length: 100.0,
            color: None,
            container_style: None,
            iter_id: "iter_id".to_string(),
        }
    }

    #[test]
    fn test_divider_with_default_props() {
        let template_data = create_default_template_data();

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXDividerWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let divider = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Rectangle)
                    .find_first();

                let props = divider.get_props();

                // Check default dimensions (vertical)
                assert_eq!(props.layout.size.height, 100.0);
                assert_eq!(props.layout.size.width, DIVIDER_WIDTH);

                // Check default color
                assert_eq!(
                    props.base_styles.background_color,
                    Some(get_ignx_color(FableColor::PRIMARY040))
                );

                // Check border radius
                assert_eq!(props.base_styles.border_radius, Some(1000.0));
            },
        );
    }

    #[test]
    fn test_divider_with_custom_props() {
        let template_data = SurfaceXDividerWidgetTemplateData {
            orientation: SurfaceXDividerOrientation::Horizontal,
            length: 200.0,
            color: Some(Color::red()),
            container_style: None,
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXDividerWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let divider = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Rectangle)
                    .find_first();

                let props = divider.get_props();

                // Check custom dimensions (horizontal)
                assert_eq!(props.layout.size.height, DIVIDER_WIDTH);
                assert_eq!(props.layout.size.width, 200.0);

                // Check custom color
                assert_eq!(props.base_styles.background_color, Some(Color::red()));
            },
        );
    }

    #[test]
    fn test_id_trait() {
        let template_data = create_default_template_data();
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_divider_with_container_style() {
        let template_data = SurfaceXDividerWidgetTemplateData {
            orientation: SurfaceXDividerOrientation::Vertical,
            length: 100.0,
            color: Some(Color::red()),
            container_style: Some(SurfaceXContainerStyles {
                width: Some(100.0),
                height: Some(100.0),
                background_color: Some(Color::blue()),
                ..Default::default()
            }),
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXDividerWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check container styles
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                assert_eq!(container_props.layout.size.width, 100.0);
                assert_eq!(container_props.layout.size.height, 100.0);
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::blue())
                );
            },
        );
    }
}
