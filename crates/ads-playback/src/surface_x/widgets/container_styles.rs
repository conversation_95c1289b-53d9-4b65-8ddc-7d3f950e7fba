use ignx_compositron::{
    color::Color,
    layout::{CrossAxisAlignment, MainAxisAlignment, Padding},
    prelude::{SingleAxisLayoutComposable, VisualComposable},
    Signify,
};

// TODO: Remove <PERSON>lone to avoid cloning data when accessing it in widgets
// https://taskei.amazon.dev/tasks/P265158298
#[derive(Clone, Default, Signify)]
pub struct SurfaceXContainerStyles {
    pub background_color: Option<Color>,
    pub border_color: Option<Color>,
    pub border_width: Option<f32>,
    pub opacity: Option<f32>,
    pub border_radius: Option<f32>,
    pub max_width: Option<f32>,
    pub width: Option<f32>,
    pub min_width: Option<f32>,
    pub height: Option<f32>,
    pub min_height: Option<f32>,
    pub max_height: Option<f32>,
    pub padding_bottom: Option<f32>,
    pub padding_top: Option<f32>,
    pub padding_start: Option<f32>,
    pub padding_end: Option<f32>,
    pub margin_bottom: Option<f32>,
    pub margin_top: Option<f32>,
    pub margin_start: Option<f32>,
    pub margin_end: Option<f32>,
    pub main_axis_alignment: Option<MainAxisAlignment>,
    pub cross_axis_alignment: Option<CrossAxisAlignment>,
}

impl SurfaceXContainerStyles {
    pub fn apply_container_style<'s, T>(&self, mut component: T) -> T
    where
        T: VisualComposable<'s> + SingleAxisLayoutComposable<'s>,
    {
        // Dimensions
        if let Some(width) = self.width {
            component = component.width(width);
        }
        if let Some(height) = self.height {
            component = component.height(height);
        }
        if let Some(max_width) = self.max_width {
            component = component.max_width(max_width);
        }
        if let Some(max_height) = self.max_height {
            component = component.max_height(max_height);
        }
        if let Some(min_width) = self.min_width {
            component = component.min_width(min_width);
        }
        if let Some(min_height) = self.min_height {
            component = component.min_height(min_height);
        }

        // Combined padding and margins
        // Until SDK supports margins - SIM: https://issues.amazon.com/issues/LRCP-4260
        let total_start = self.padding_start.unwrap_or(0.0) + self.margin_start.unwrap_or(0.0);
        let total_end = self.padding_end.unwrap_or(0.0) + self.margin_end.unwrap_or(0.0);
        let total_top = self.padding_top.unwrap_or(0.0) + self.margin_top.unwrap_or(0.0);
        let total_bottom = self.padding_bottom.unwrap_or(0.0) + self.margin_bottom.unwrap_or(0.0);

        let padding = Padding {
            start: total_start,
            end: total_end,
            top: total_top,
            bottom: total_bottom,
        };
        component = component.padding(padding);

        // Visual styling
        if let Some(background_color) = self.background_color {
            component = component.background_color(background_color);
        }
        if let Some(border_color) = self.border_color {
            component = component.border_color(border_color);
        }
        if let Some(border_width) = self.border_width {
            component = component.border_width(border_width);
        }
        if let Some(border_radius) = self.border_radius {
            component = component.border_radius(border_radius);
        }
        if let Some(opacity) = self.opacity {
            component = component.opacity(opacity);
        }

        // Layout alignment
        if let Some(alignment) = self.main_axis_alignment {
            component = component.main_axis_alignment(alignment);
        }
        if let Some(alignment) = self.cross_axis_alignment {
            component = component.cross_axis_alignment(alignment);
        }

        component
    }
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test, compose, prelude::safe::*, test_utils::ComposableType,
    };

    pub fn create_mock_container_style() -> SurfaceXContainerStyles {
        SurfaceXContainerStyles {
            width: Some(800.0),
            height: Some(800.0),
            background_color: Some(Color::yellow()),
            border_color: Some(Color::red()),
            border_width: Some(4.0),
            border_radius: Some(12.0),
            opacity: Some(0.9),
            max_width: Some(800.0),
            max_height: Some(800.0),
            min_width: Some(300.0),
            min_height: Some(300.0),
            padding_bottom: Some(5.0),
            padding_start: Some(5.0),
            padding_end: Some(5.0),
            padding_top: Some(5.0),
            margin_bottom: Some(5.0),
            margin_start: Some(5.0),
            margin_end: Some(5.0),
            margin_top: Some(5.0),
            main_axis_alignment: Some(MainAxisAlignment::Center),
            cross_axis_alignment: Some(CrossAxisAlignment::End),
        }
    }

    #[test]
    fn test_get_default() {
        let style = create_mock_container_style();

        assert_eq!(style.width, Some(800.0));
        assert_eq!(style.height, Some(800.0));
        assert_eq!(style.background_color, Some(Color::yellow()));
        assert_eq!(style.border_color, Some(Color::red()));
        assert_eq!(style.border_width, Some(4.0));
        assert_eq!(style.border_radius, Some(12.0));
        assert_eq!(style.opacity, Some(0.9));
        assert_eq!(style.max_width, Some(800.0));
        assert_eq!(style.max_height, Some(800.0));
        assert_eq!(style.min_width, Some(300.0));
        assert_eq!(style.min_height, Some(300.0));
        assert_eq!(style.padding_bottom, Some(5.0));
        assert_eq!(style.padding_start, Some(5.0));
        assert_eq!(style.padding_end, Some(5.0));
        assert_eq!(style.padding_top, Some(5.0));
        assert_eq!(style.margin_bottom, Some(5.0));
        assert_eq!(style.margin_start, Some(5.0));
        assert_eq!(style.margin_end, Some(5.0));
        assert_eq!(style.margin_top, Some(5.0));
        assert_eq!(style.main_axis_alignment, Some(MainAxisAlignment::Center));
        assert_eq!(style.cross_axis_alignment, Some(CrossAxisAlignment::End));
    }

    #[test]
    fn test_apply_container_style() {
        launch_test(
            |ctx| {
                let style = create_mock_container_style();

                let mut container = compose! {
                  Column(){}.test_id("test_id")
                };

                container = style.apply_container_style(container);

                container
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id("test_id");
                let props = node.get_props();

                assert_eq!(props.composable_type, ComposableType::Column);

                // Visual styling assertions
                assert_eq!(props.base_styles.background_color, Some(Color::yellow()));
                assert_eq!(props.base_styles.border_color, Some(Color::red()));
                assert_eq!(props.base_styles.border_width, Some(4.0));
                assert_eq!(props.base_styles.border_radius, Some(12.0));
                assert_eq!(props.base_styles.opacity, Some(0.9));

                // Dimension assertions
                assert_eq!(props.layout.size.width, 800.0);
                assert_eq!(props.layout.size.height, 800.0);

                // Padding assertions
                assert_eq!(props.layout.padding.start, 10.0); // 5.0 + 5.0
                assert_eq!(props.layout.padding.end, 10.0); // 5.0 + 5.0
                assert_eq!(props.layout.padding.top, 10.0); // 5.0 + 5.0
                assert_eq!(props.layout.padding.bottom, 10.0); // 5.0 + 5.0
            },
        );
    }

    #[test]
    fn test_max_constraints() {
        launch_test(
            |ctx| {
                let mut style = create_mock_container_style();

                // Set width and height larger than max_width and max_height to verify constraint
                style.width = Some(1000.0);
                style.height = Some(1000.0);
                style.max_width = Some(800.0);
                style.max_height = Some(800.0);

                let mut container = compose! {
                    Column(){}.test_id("test_max_constraints")
                };

                container = style.apply_container_style(container);
                container
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id("test_max_constraints");
                let props = node.get_props();

                assert_eq!(props.layout.size.width, 800.0);
                assert_eq!(props.layout.size.height, 800.0);
            },
        );
    }

    #[test]
    fn test_min_constraints() {
        launch_test(
            |ctx| {
                let mut style = create_mock_container_style();

                // Set width and height smaller than min_width and min_height to verify constraint
                style.width = Some(100.0);
                style.height = Some(100.0);
                style.min_width = Some(800.0);
                style.min_height = Some(800.0);

                let mut container = compose! {
                    Column(){}.test_id("test_max_constraints")
                };

                container = style.apply_container_style(container);
                container
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let node = node_tree.find_by_test_id("test_max_constraints");
                let props = node.get_props();

                assert_eq!(props.layout.size.width, 800.0);
                assert_eq!(props.layout.size.height, 800.0);
            },
        );
    }

    #[test]
    fn test_default() {
        let default_style = SurfaceXContainerStyles::default();

        assert_eq!(default_style.background_color, None);
        assert_eq!(default_style.border_color, None);
        assert_eq!(default_style.border_width, None);
        assert_eq!(default_style.opacity, None);
        assert_eq!(default_style.border_radius, None);
        assert_eq!(default_style.max_width, None);
        assert_eq!(default_style.width, None);
        assert_eq!(default_style.min_width, None);
        assert_eq!(default_style.height, None);
        assert_eq!(default_style.min_height, None);
        assert_eq!(default_style.max_height, None);
        assert_eq!(default_style.padding_bottom, None);
        assert_eq!(default_style.padding_top, None);
        assert_eq!(default_style.padding_start, None);
        assert_eq!(default_style.padding_end, None);
        assert_eq!(default_style.margin_bottom, None);
        assert_eq!(default_style.margin_top, None);
        assert_eq!(default_style.margin_start, None);
        assert_eq!(default_style.margin_end, None);
        assert_eq!(default_style.main_axis_alignment, None);
        assert_eq!(default_style.cross_axis_alignment, None);
    }
}
