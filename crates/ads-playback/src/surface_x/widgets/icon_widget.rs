use fableous::font_icon::*;
use fableous::typography::typography::*;
use ignx_compositron::{compose, id::Id, prelude::safe::*, shared::Shared, Composer, Signify};

use crate::surface_x::widgets::container_styles::SurfaceXContainerStyles;

#[derive(Signify)]
pub struct SurfaceXIconWidgetTemplateData {
    pub icon_name: String,
    pub color: Option<Color>,
    pub font_size: Option<FontSize>,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXIconWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXIconWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXIconWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let icon = template_data.icon_name().get();
    let color = template_data.color().get().unwrap_or(IGNX_PRIMARY_COLOR);
    let default_font = FontSize::default();
    let size = template_data.font_size().get().unwrap_or(default_font);

    let mut container = compose! {
        Column(){
            FontIcon(
                icon,
                color,
                size
            )
        }
        .test_id("surface_x_icon_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{node_properties::NodeTypeProperties, ComposableType},
    };

    fn create_default_template_data() -> SurfaceXIconWidgetTemplateData {
        SurfaceXIconWidgetTemplateData {
            icon_name: "default_icon".to_string(),
            color: None,
            font_size: None,
            container_style: None,
            iter_id: "iter_id".to_string(),
        }
    }

    #[test]
    fn test_icon_with_default_props() {
        let template_data = create_default_template_data();
        launch_test(
            |ctx| {
                compose! {
                    SurfaceXIconWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let icon_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .find_first();

                let props = icon_node.get_props();

                // Test icon properties
                assert_eq!(props.composable_type, ComposableType::Label);
                assert_eq!(props.text, Some("default_icon".to_string()));

                if let NodeTypeProperties::Text(text_props) = &props.node_type_props {
                    assert_eq!(text_props.color, IGNX_PRIMARY_COLOR);
                    assert_eq!(text_props.font.size, FontSize(48));
                } else {
                    panic!("Expected Text node type properties");
                }

                // Check container has no styles
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                assert_eq!(container_props.base_styles.background_color, None);
                assert_eq!(container_props.base_styles.border_color, None);
                assert_eq!(container_props.base_styles.border_width, None);
                assert_eq!(container_props.base_styles.border_radius, None);
                assert_eq!(container_props.base_styles.opacity, None);
                assert_eq!(container_props.layout.padding.start, 0.0);
                assert_eq!(container_props.layout.padding.end, 0.0);
                assert_eq!(container_props.layout.padding.top, 0.0);
                assert_eq!(container_props.layout.padding.bottom, 0.0);
            },
        );
    }

    #[test]
    fn test_icon_with_container_style() {
        let mut template_data = create_default_template_data();
        template_data.container_style = Some(SurfaceXContainerStyles {
            width: Some(800.0),
            height: Some(800.0),
            background_color: Some(Color::red()),
            ..Default::default()
        });

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXIconWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                assert_eq!(container_props.layout.size.width, 800.0);
                assert_eq!(container_props.layout.size.height, 800.0);
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::red())
                );
            },
        );
    }

    #[test]
    fn test_id_trait() {
        let template_data = create_default_template_data();
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_icon_with_custom_props() {
        let template_data = SurfaceXIconWidgetTemplateData {
            icon_name: "custom_icon".to_string(),
            color: Some(Color::green()),
            font_size: Some(FontSize(32)),
            container_style: None,
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXIconWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let icon_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .find_first();

                let props = icon_node.get_props();

                // Test icon properties
                assert_eq!(props.composable_type, ComposableType::Label);
                assert_eq!(props.text, Some("custom_icon".to_string()));

                if let NodeTypeProperties::Text(text_props) = &props.node_type_props {
                    assert_eq!(text_props.color, Color::green());
                    assert_eq!(text_props.font.size, FontSize(32));
                } else {
                    panic!("Expected Text node type properties");
                }
            },
        );
    }
}
