use amzn_fable_tokens::FableText;
use fableous::star_rating::*;
use fableous::typography::type_ramp::TypeRamp;
use ignx_compositron::shared::Shared;
use ignx_compositron::Signify;
use ignx_compositron::{compose, id::Id, prelude::safe::*, Composer};

use crate::surface_x::widgets::container_styles::SurfaceXContainerStyles;
use crate::surface_x::widgets::text_widget::*;

const RATING_TEXT_DEFAULT_TYPERAM: FableText = FableText::TYPE_LABEL200;
const TEXT_MARGIN_TOP: f32 = 3.0;
const TEXT_MARGIN_HORIZONTAL: f32 = 8.0;

#[derive(Signify)]
pub struct SurfaceXStarRatingWidgetTemplateData {
    pub rating: f32,
    pub rating_count: String,
    pub star_size: FontSize,
    pub full_star_color: Color,
    pub empty_star_color: Color,
    pub star_separator_width: f32,
    pub show_rating_number: Option<bool>,
    pub rating_text_type_ramp: Option<TypeRamp>,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXStarRatingWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXStarRatingWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXStarRatingWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let rating = template_data.rating().get();
    let rating_count = template_data.rating_count().get();
    let star_size = template_data.star_size().get();
    let full_star_color = template_data.full_star_color().get();
    let empty_star_color = template_data.empty_star_color().get();
    let star_separator_width = template_data.star_separator_width().get();
    let type_ramp = template_data
        .rating_text_type_ramp()
        .get()
        .unwrap_or_else(|| TypeRamp::from(RATING_TEXT_DEFAULT_TYPERAM));
    let show_rating_number = template_data.show_rating_number().get().unwrap_or(false);
    let type_ramp_for_votes = type_ramp.clone();

    let mut container = compose! {
        Row() {
            // Rating number
            if show_rating_number {
              SurfaceXTextWidget(
                  template_data: Shared::new(SurfaceXTextWidgetTemplateData {
                      text: format!("{:.1}", rating),
                      type_ramp: type_ramp.clone() ,
                      color: None,
                      text_alignment: None,
                      line_height: None,
                      container_style: Some(SurfaceXContainerStyles {
                          margin_end: Some(TEXT_MARGIN_HORIZONTAL),
                          margin_top: Some(TEXT_MARGIN_TOP),
                          ..Default::default()
                      }),
                      truncation_mode: None,
                      max_lines: None,
                      text_decoration_line: None,
                      iter_id:String::new()
                  })
              )
            }

            StarRating(
              rating,
              star_size,
              full_star_color,
              empty_star_color,
              gap: star_separator_width
            )

            // Votes
            SurfaceXTextWidget(
                template_data: Shared::new(SurfaceXTextWidgetTemplateData {
                    text: rating_count,
                    type_ramp: type_ramp_for_votes,
                    color: None,
                    text_alignment: None,
                    line_height: None,
                    container_style: Some(SurfaceXContainerStyles {
                        margin_start: Some(TEXT_MARGIN_HORIZONTAL),
                        margin_top: Some(TEXT_MARGIN_TOP),
                        ..Default::default()
                    }),
                    truncation_mode: None,
                    max_lines: None,
                    text_decoration_line: None,
                    iter_id:String::new()
                })
            )
        }
        .cross_axis_alignment(CrossAxisAlignment::Center)
        .test_id("surface_x_star_rating_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{node_properties::NodeTypeProperties, ComposableType},
    };

    fn create_minimal_template_data() -> SurfaceXStarRatingWidgetTemplateData {
        SurfaceXStarRatingWidgetTemplateData {
            rating: 4.5,
            rating_count: "1,234".to_string(),
            star_size: FontSize(24),
            full_star_color: Color::red(),
            empty_star_color: Color::gray(),
            star_separator_width: 4.0,
            show_rating_number: None,
            rating_text_type_ramp: None,
            container_style: None,
            iter_id: "iter_id".to_string(),
        }
    }

    #[test]
    fn test_star_rating_widget_with_minimal_props() {
        let template_data = create_minimal_template_data();

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXStarRatingWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check StarRating
                let star_icons = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .find_all();

                // Should have 5 stars
                assert_eq!(star_icons.len(), 5, "Should have 5 star icons");

                // Check rating count text
                let text_nodes = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_all();

                assert_eq!(text_nodes.len(), 1, "Should only have rating count text");

                let first_text_node = text_nodes[0].clone();

                let rating_count_text = first_text_node.get_props();
                assert_eq!(rating_count_text.text.as_ref().unwrap(), "1,234");

                if let NodeTypeProperties::RichText(text_props) = &rating_count_text.node_type_props
                {
                    assert_eq!(text_props.font_size, Some(FontSize(20)));
                }
            },
        );
    }

    #[test]
    fn test_id_trait() {
        let template_data = create_minimal_template_data();
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_star_rating_widget_with_custom_props() {
        let template_data = SurfaceXStarRatingWidgetTemplateData {
            rating: 3.7,
            rating_count: "987".to_string(),
            star_size: FontSize(32),
            full_star_color: Color::blue(),
            empty_star_color: Color::yellow(),
            star_separator_width: 8.0,
            show_rating_number: Some(true),
            rating_text_type_ramp: Some(TypeRamp::from(FableText::TYPE_LABEL100)),
            container_style: None,
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXStarRatingWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check text nodes (rating and count)
                let text_nodes = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_all();

                assert_eq!(
                    text_nodes.len(),
                    2,
                    "Should have both rating and count text"
                );

                // Check rating text
                let rating_text = text_nodes[0].clone().get_props();
                assert_eq!(rating_text.text.as_ref().unwrap(), "3.7");

                // Check rating count text
                let count_text = text_nodes[1].clone().get_props();
                assert_eq!(count_text.text.as_ref().unwrap(), "987");

                if let NodeTypeProperties::RichText(text_props) = &count_text.node_type_props {
                    assert_eq!(text_props.font_size, Some(FontSize(20)));
                }
            },
        );
    }

    #[test]
    fn test_star_rating_widget_with_container_style() {
        let container_style = SurfaceXContainerStyles {
            width: Some(300.0),
            height: Some(60.0),
            background_color: Some(Color::green()),
            ..Default::default()
        };

        let template_data = SurfaceXStarRatingWidgetTemplateData {
            container_style: Some(container_style),
            ..create_minimal_template_data()
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXStarRatingWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check container
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Row)
                    .find_first();

                let container_props = container.get_props();
                assert_eq!(container_props.layout.size.width, 300.0);
                assert_eq!(container_props.layout.size.height, 60.0);
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::green())
                );
            },
        );
    }
}
