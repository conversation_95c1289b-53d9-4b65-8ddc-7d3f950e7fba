use ignx_compositron::{id::Id, shared::Shared, Signify};

use super::{
    SurfaceXBadgeWidgetTemplateData, SurfaceXDividerWidgetTemplateData,
    SurfaceXIconWidgetTemplateData, SurfaceXImageWidgetTemplateData,
    SurfaceXPrimaryButtonWidgetTemplateData, SurfaceXPrimeLogoWidgetTemplateData,
    SurfaceXRowWidgetTemplateData, SurfaceXStarRatingWidgetTemplateData,
    SurfaceXTextWidgetTemplateData,
};

#[derive(Clone, Signify)]
pub enum SurfaceXWidgetTemplateData {
    Text(Shared<SurfaceXTextWidgetTemplateData>),
    Image(Shared<SurfaceXImageWidgetTemplateData>),
    Badge(Shared<SurfaceXBadgeWidgetTemplateData>),
    Icon(Shared<SurfaceXIconWidgetTemplateData>),
    Divider(Shared<SurfaceXDividerWidgetTemplateData>),
    PrimaryButton(Shared<SurfaceXPrimaryButtonWidgetTemplateData>),
    PrimeLogo(Shared<SurfaceXPrimeLogoWidgetTemplateData>),
    StarRating(Shared<SurfaceXStarRatingWidgetTemplateData>),
    Row(Shared<SurfaceXRowWidgetTemplateData>),
}

impl Id for SurfaceXWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            SurfaceXWidgetTemplateData::Text(data) => data.id(),
            SurfaceXWidgetTemplateData::Image(data) => data.id(),
            SurfaceXWidgetTemplateData::Badge(data) => data.id(),
            SurfaceXWidgetTemplateData::Icon(data) => data.id(),
            SurfaceXWidgetTemplateData::Divider(data) => data.id(),
            SurfaceXWidgetTemplateData::PrimaryButton(data) => data.id(),
            SurfaceXWidgetTemplateData::PrimeLogo(data) => data.id(),
            SurfaceXWidgetTemplateData::StarRating(data) => data.id(),
            SurfaceXWidgetTemplateData::Row(data) => data.id(),
        }
    }
}

/// Widget template data that can be used as children of a row widget
/// (excludes row widgets to prevent nesting)
#[derive(Clone, Signify)]
pub enum SurfaceXRowChildWidgetTemplateData {
    Text(Shared<SurfaceXTextWidgetTemplateData>),
    Image(Shared<SurfaceXImageWidgetTemplateData>),
    Badge(Shared<SurfaceXBadgeWidgetTemplateData>),
    Icon(Shared<SurfaceXIconWidgetTemplateData>),
    Divider(Shared<SurfaceXDividerWidgetTemplateData>),
    PrimaryButton(Shared<SurfaceXPrimaryButtonWidgetTemplateData>),
    PrimeLogo(Shared<SurfaceXPrimeLogoWidgetTemplateData>),
    StarRating(Shared<SurfaceXStarRatingWidgetTemplateData>),
}

impl Id for SurfaceXRowChildWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            SurfaceXRowChildWidgetTemplateData::Text(data) => data.id(),
            SurfaceXRowChildWidgetTemplateData::Image(data) => data.id(),
            SurfaceXRowChildWidgetTemplateData::Badge(data) => data.id(),
            SurfaceXRowChildWidgetTemplateData::Icon(data) => data.id(),
            SurfaceXRowChildWidgetTemplateData::Divider(data) => data.id(),
            SurfaceXRowChildWidgetTemplateData::PrimaryButton(data) => data.id(),
            SurfaceXRowChildWidgetTemplateData::PrimeLogo(data) => data.id(),
            SurfaceXRowChildWidgetTemplateData::StarRating(data) => data.id(),
        }
    }
}

impl From<SurfaceXRowChildWidgetTemplateData> for SurfaceXWidgetTemplateData {
    fn from(child: SurfaceXRowChildWidgetTemplateData) -> Self {
        match child {
            SurfaceXRowChildWidgetTemplateData::Text(data) => {
                SurfaceXWidgetTemplateData::Text(data)
            }
            SurfaceXRowChildWidgetTemplateData::Image(data) => {
                SurfaceXWidgetTemplateData::Image(data)
            }
            SurfaceXRowChildWidgetTemplateData::Badge(data) => {
                SurfaceXWidgetTemplateData::Badge(data)
            }
            SurfaceXRowChildWidgetTemplateData::Icon(data) => {
                SurfaceXWidgetTemplateData::Icon(data)
            }
            SurfaceXRowChildWidgetTemplateData::Divider(data) => {
                SurfaceXWidgetTemplateData::Divider(data)
            }
            SurfaceXRowChildWidgetTemplateData::PrimaryButton(data) => {
                SurfaceXWidgetTemplateData::PrimaryButton(data)
            }
            SurfaceXRowChildWidgetTemplateData::PrimeLogo(data) => {
                SurfaceXWidgetTemplateData::PrimeLogo(data)
            }
            SurfaceXRowChildWidgetTemplateData::StarRating(data) => {
                SurfaceXWidgetTemplateData::StarRating(data)
            }
        }
    }
}
