use ignx_compositron::{compose, id::Id, prelude::safe::*, shared::Shared, Composer, Signify};
use lrc_image::{lrc_image::*, types::*};

use crate::surface_x::widgets::container_styles::SurfaceXContainerStyles;

const PRIME_WITH_CHECK_LOGO_URL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/common/prime_with_check.png";

#[derive(Signify)]
pub struct SurfaceXPrimeLogoWidgetTemplateData {
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXPrimeLogoWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXPrimeLogoWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXPrimeLogoWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let width = template_data
        .container_style()
        .get()
        .as_ref()
        .and_then(|style| style.width)
        .unwrap_or(105.0);

    let height = template_data
        .container_style()
        .get()
        .as_ref()
        .and_then(|style| style.height)
        .unwrap_or(33.0);

    let mut container = compose! {
        Column(){
            LRCImage(
              data: ImageData {
                  url: PRIME_WITH_CHECK_LOGO_URL.to_string(),
                  width,
                  height,
                  tags: vec![
                    ImageTag::Scaling(
                        ScalingStrategy::ScaleToRectangle(
                            ScaleToRectangleOptions {
                                hide_canvas: true,
                                alignment: lrc_image::types::ImageAlignment::Centered
                            }
                        )
                    ),
                    ImageTag::Format(ImageFormat::PNG)
                  ]
             }
           )
        }
        .test_id("surface_x_prime_logo_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        color::Color,
        test_utils::{node_properties::NodeTypeProperties, ComposableType},
    };

    #[test]
    fn test_prime_logo_without_container_style() {
        let template_data = SurfaceXPrimeLogoWidgetTemplateData {
            container_style: None,
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXPrimeLogoWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check Image properties
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();
                let props = image_node.get_props();

                // Check composable type
                assert_eq!(props.composable_type, ComposableType::Image);

                // Check image source
                if let NodeTypeProperties::Image(image_source) = &props.node_type_props {
                    assert!(
                        image_source.uri.as_ref().is_some_and(|uri| uri
                            .starts_with("https://m.media-amazon.com/images/G/01/AVLRC/images/default/common/prime_with_check")),
                        "Unexpected image URI"
                    );
                }

                // Check dimensions
                assert_eq!(props.layout.size.width, 105.0);
                assert_eq!(props.layout.size.height, 33.0);

                // Check base styles
                assert_eq!(props.base_styles.background_color, None);
                assert_eq!(props.base_styles.opacity, None);
                assert_eq!(props.base_styles.border_color, None);
                assert_eq!(props.base_styles.border_width, None);
                assert_eq!(props.base_styles.border_radius, None);

                // Check padding
                assert_eq!(props.layout.padding.start, 0.0);
                assert_eq!(props.layout.padding.end, 0.0);
                assert_eq!(props.layout.padding.top, 0.0);
                assert_eq!(props.layout.padding.bottom, 0.0);
            },
        );
    }

    #[test]
    fn test_id_trait() {
        let template_data = SurfaceXPrimeLogoWidgetTemplateData {
            container_style: None,
            iter_id: "iter_id".to_string(),
        };
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_prime_logo_with_container_style() {
        let template_data = SurfaceXPrimeLogoWidgetTemplateData {
            container_style: Some(SurfaceXContainerStyles {
                width: Some(200.0),
                height: Some(100.0),
                background_color: Some(Color::blue()),
                ..Default::default()
            }),
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXPrimeLogoWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check container styles
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                // Test styling
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::blue())
                );
            },
        );
    }
}
