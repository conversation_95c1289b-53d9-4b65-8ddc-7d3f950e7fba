#![allow(unused)]
use fableous::buttons::primary_button::*;
use ignx_compositron::{compose, id::Id, prelude::safe::*, shared::Shared, Composer, Signify};

use super::container_styles::SurfaceXContainerStyles;

#[derive(Signify)]
pub struct SurfaceXPrimaryButtonWidgetTemplateData {
    pub text: String,
    pub icon_name: Option<String>,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXPrimaryButtonWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXPrimaryButtonWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXPrimaryButtonWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let variant = if let Some(icon_name) = template_data.icon_name().get() {
        PrimaryButtonVariant::IconAndTextSize400(
            icon_name,
            TextContent::String(template_data.text().get()),
        )
    } else {
        PrimaryButtonVariant::TextSize400(TextContent::String(template_data.text().get()))
    };

    let mut container = compose! {
        Column(){
            PrimaryButton(
                variant: variant,
                disabled: false,
                animation: None,
                center_content: true
            )
        }
        .cross_axis_stretch(true)
        .test_id("surface_x_primary_button_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use amzn_fable_tokens::FableIcon;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{
            assert_node_does_not_exist, node_properties::NodeTypeProperties, ComposableType,
        },
    };

    #[test]
    fn test_minimal_props() {
        let template_data = SurfaceXPrimaryButtonWidgetTemplateData {
            text: "Minimal Button".to_string(),
            icon_name: None,
            container_style: None,
            iter_id: String::new(),
        };

        launch_test(
            |ctx| compose! { SurfaceXPrimaryButtonWidget(template_data: Shared::new(template_data)) },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check container has no styles
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                assert_eq!(container_props.base_styles.background_color, None);
                assert_eq!(container_props.base_styles.border_color, None);
                assert_eq!(container_props.base_styles.border_width, None);
                assert_eq!(container_props.base_styles.border_radius, None);
                assert_eq!(container_props.base_styles.opacity, None);
                assert_eq!(container_props.layout.padding.start, 0.0);

                // Check button has only text
                let button = node_tree
                    .find_by_props()
                    .test_id(PRIMARY_BUTTON_TEST_ID)
                    .find_first();

                let icon = button
                    .find_any_child_with()
                    .composable_type(ComposableType::Label)
                    .find_first();

                assert_node_does_not_exist!(icon);

                let text = button
                    .find_any_child_with()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();
                assert_eq!(text.get_props().text, Some("Minimal Button".to_string()));
            },
        );
    }

    #[test]
    fn test_button_icon() {
        let template_data = SurfaceXPrimaryButtonWidgetTemplateData {
            text: "Minimal Button".to_string(),
            icon_name: Some(FableIcon::AD_FREE.into()),
            container_style: None,
            iter_id: String::new(),
        };

        launch_test(
            |ctx| compose! { SurfaceXPrimaryButtonWidget(template_data: Shared::new(template_data)) },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let icon = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Label)
                    .find_first();

                assert_eq!(icon.get_props().text, Some(FableIcon::AD_FREE.to_string()));
            },
        );
    }

    #[test]
    fn test_id_trait() {
        let template_data = SurfaceXPrimaryButtonWidgetTemplateData {
            text: "Test".to_string(),
            icon_name: None,
            container_style: None,
            iter_id: "iter_id".to_string(),
        };
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_container_styles() {
        let template_data = SurfaceXPrimaryButtonWidgetTemplateData {
            text: "Hello".to_string(),
            icon_name: None,
            container_style: Some(SurfaceXContainerStyles {
                width: Some(200.0),
                height: Some(100.0),
                background_color: Some(Color::blue()),
                ..Default::default()
            }),
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXPrimaryButtonWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                // Verify container styles are applied correctly
                assert_eq!(container_props.layout.size.width, 200.0);
                assert_eq!(container_props.layout.size.height, 100.0);
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::blue())
                );
            },
        );
    }
}
