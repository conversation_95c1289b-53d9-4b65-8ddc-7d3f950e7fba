use ignx_compositron::{compose, id::Id, prelude::safe::*, shared::Shared, Composer, Signify};
use lrc_image::{lrc_image::*, types::*};

use crate::{
    surface_x::widgets::container_styles::SurfaceXContainerStyles,
    utils::get_file_extension_from_url,
};

#[derive(Signify)]
pub struct SurfaceXImageWidgetTemplateData {
    pub image_url: String,
    pub width: f32,
    pub height: f32,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXImageWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXImageWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXImageWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let url = template_data.image_url().get();
    let width = template_data.width().get();
    let height = template_data.height().get();

    let format_tag = match get_file_extension_from_url(&url) {
        Some(ext) if ext == ".png" => Some(ImageTag::Format(ImageFormat::PNG)),
        _ => None,
    };

    let mut container = compose! {
        Column(){
           LRCImage(
            data: ImageData{
                url,
                width,
                height,
                tags: {
                   let mut tags = vec![
                        ImageTag::Scaling(
                            ScalingStrategy::ScaleToRectangle(
                                ScaleToRectangleOptions {
                                    hide_canvas: true,
                                    alignment: lrc_image::types::ImageAlignment::Centered
                                }
                            )
                        ),
                    ];

                    if let Some(tag) = format_tag {
                        tags.push(tag);
                    }

                    tags
                  }
                },
           )
        }
        .test_id("surface_x_image_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        color::Color,
        test_utils::{node_properties::NodeTypeProperties, ComposableType},
    };

    fn create_default_template_data() -> SurfaceXImageWidgetTemplateData {
        SurfaceXImageWidgetTemplateData {
            image_url: "https://example.com/image.jpg".to_string(),
            width: 100.0,
            height: 100.0,
            container_style: None,
            iter_id: "iter_id".to_string(),
        }
    }

    #[test]
    fn test_image_with_default_props() {
        let template_data = create_default_template_data();
        launch_test(
            |ctx| {
                compose! {
                    SurfaceXImageWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();

                let props = image_node.get_props();

                // Test image properties
                if let NodeTypeProperties::Image(image_source) = &props.node_type_props {
                    assert!(
                        image_source
                            .uri
                            .as_ref()
                            .is_some_and(|uri| uri.starts_with("https://example.com/image")),
                        "Image URI should start with 'https://example.com/image'"
                    );
                } else {
                    panic!("Expected Image node type properties");
                }

                // Test layout properties
                assert_eq!(props.layout.size.width, 100.0);
                assert_eq!(props.layout.size.height, 100.0);

                // Check container has no styles
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();
                assert_eq!(container_props.base_styles.background_color, None);
                assert_eq!(container_props.base_styles.border_color, None);
                assert_eq!(container_props.base_styles.border_width, None);
                assert_eq!(container_props.base_styles.border_radius, None);
                assert_eq!(container_props.base_styles.opacity, None);
                assert_eq!(container_props.layout.padding.start, 0.0);
                assert_eq!(container_props.layout.padding.end, 0.0);
                assert_eq!(container_props.layout.padding.top, 0.0);
                assert_eq!(container_props.layout.padding.bottom, 0.0);
            },
        );
    }
    #[test]
    fn test_id_trait() {
        let template_data = create_default_template_data();
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_image_with_container_style() {
        let mut template_data = create_default_template_data();
        template_data.container_style = Some(SurfaceXContainerStyles {
            width: Some(200.0),
            height: Some(200.0),
            background_color: Some(Color::red()),
            ..Default::default()
        });

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXImageWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Test container styles
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                assert_eq!(container_props.layout.size.width, 200.0);
                assert_eq!(container_props.layout.size.height, 200.0);
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::red())
                );
            },
        );
    }

    #[test]
    fn test_image_format_tag_based_on_extension() {
        // Test with PNG extension
        let mut png_template_data = create_default_template_data();
        png_template_data.image_url = "https://example.com/image.png".to_string();

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXImageWidget(template_data: Shared::new(png_template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();

                let props = image_node.get_props();

                if let NodeTypeProperties::Image(image_source) = &props.node_type_props {
                    assert!(
                        image_source
                            .uri
                            .as_ref()
                            .is_some_and(|uri| uri.contains(".png")),
                        "PNG image should have PNG format tag"
                    );
                } else {
                    panic!("Expected Image format tag");
                }
            },
        );

        // Test with JPG extension
        let jpg_template_data = create_default_template_data();
        // Using the default jpg URL from create_default_template_data

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXImageWidget(template_data: Shared::new(jpg_template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();

                let props = image_node.get_props();

                if let NodeTypeProperties::Image(image_source) = &props.node_type_props {
                    assert!(
                        image_source
                            .uri
                            .as_ref()
                            .is_some_and(|uri| uri.contains(".jpg")),
                        "JPG image should have JPG format tag"
                    );
                } else {
                    panic!("Expected Image node type properties");
                }
            },
        );
    }
}
