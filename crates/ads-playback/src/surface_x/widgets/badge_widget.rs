use amzn_fable_tokens::FableText;
use fableous::*;
use ignx_compositron::{compose, id::Id, prelude::safe::*, shared::Shared, Composer, Signify};
use typography::type_ramp::TypeRamp;

use crate::surface_x::widgets::{container_styles::SurfaceXContainerStyles, text_widget::*};

#[derive(Signify)]
pub struct SurfaceXBadgeWidgetTemplateData {
    pub text: String,
    pub background_color: Color,
    pub type_ramp: Option<TypeRamp>,
    pub text_color: Option<Color>,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXBadgeWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXBadgeWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXBadgeWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let text = template_data.text().get();
    let type_ramp = template_data
        .type_ramp()
        .get()
        .unwrap_or_else(|| FableText::TYPE_LABEL200.into());
    let text_color = template_data.text_color().get().unwrap_or(Color::white());
    let background_color = template_data.background_color().get();

    let mut container = compose! {
        Column() {
            Column(){
               SurfaceXTextWidget(
                  template_data: Shared::new(SurfaceXTextWidgetTemplateData {
                      text,
                      type_ramp,
                      color: Some(text_color),
                      max_lines: Some(1),
                      text_alignment: None,
                      line_height: None,
                      container_style: None,
                      truncation_mode: None,
                      text_decoration_line: None,
                      iter_id: String::new(),
                  })
              )
            }
            .background_color(background_color)
            .padding(Padding::new(6.0, 6.0, 6.0, 6.0))
            .border_radius(9.0)
            .main_axis_alignment(MainAxisAlignment::Center)
            .cross_axis_alignment(CrossAxisAlignment::Center)
            .test_id("surface_x_badge_widget")
        }

    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{node_properties::NodeTypeProperties, ComposableType},
    };

    fn create_default_template_data() -> SurfaceXBadgeWidgetTemplateData {
        SurfaceXBadgeWidgetTemplateData {
            text: "Test Badge".to_string(),
            background_color: Color::yellow(),
            type_ramp: None,
            text_color: None,
            container_style: None,
            iter_id: "iter_id".to_string(),
        }
    }

    #[test]
    fn test_badge_with_required_props_only() {
        let template_data = create_default_template_data();

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXBadgeWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Find inner Column (badge itself)
                let badge = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_by_index(1);
                let badge_props = badge.get_props();

                // Test default badge styling
                assert_eq!(
                    badge_props.base_styles.background_color,
                    Some(Color::yellow())
                );
                assert_eq!(badge_props.base_styles.border_radius, Some(9.0));
                assert_eq!(badge_props.layout.padding.start, 6.0);
                assert_eq!(badge_props.layout.padding.end, 6.0);
                assert_eq!(badge_props.layout.padding.top, 6.0);
                assert_eq!(badge_props.layout.padding.bottom, 6.0);

                // Find and test text with default props
                let text = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();
                let text_props = text.get_props();

                assert_eq!(text_props.text, Some("Test Badge".to_string()));

                // Check default text color (white) and type ramp (TYPE_LABEL200)
                if let NodeTypeProperties::RichText(rich_text_props) = &text_props.node_type_props {
                    assert_eq!(
                        rich_text_props.color.as_ref().map(|c| c.0),
                        Some(Color::white())
                    );
                    assert_eq!(rich_text_props.font_size, Some(FontSize(20)));
                    assert_eq!(rich_text_props.font_weight, Some(FontWeight::Bold));
                    assert_eq!(rich_text_props.line_height, Some(24.0));
                    assert_eq!(rich_text_props.letter_spacing, Some(2.0));
                }
            },
        );
    }

    #[test]
    fn test_badge_with_custom_props() {
        let template_data = SurfaceXBadgeWidgetTemplateData {
            type_ramp: Some(FableText::TYPE_LABEL200.into()),
            text_color: Some(Color::red()),
            ..create_default_template_data()
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXBadgeWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Find and test text
                let text = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();
                let text_props = text.get_props();

                if let NodeTypeProperties::RichText(rich_text_props) = &text_props.node_type_props {
                    assert_eq!(
                        rich_text_props.color.as_ref().map(|c| c.0),
                        Some(Color::red())
                    );
                    assert_eq!(rich_text_props.font_size, Some(FontSize(20)));
                }
            },
        );
    }

    #[test]
    fn test_id_trait() {
        let template_data = create_default_template_data();
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_badge_with_container_style() {
        let mut template_data = create_default_template_data();
        template_data.container_style = Some(SurfaceXContainerStyles {
            width: Some(200.0),
            height: Some(50.0),
            background_color: Some(Color::blue()),
            ..Default::default()
        });

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXBadgeWidget(template_data: Shared::new(template_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Test outer container (with container_style)
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                // Test container dimensions and styling
                assert_eq!(container_props.layout.size.width, 200.0);
                assert_eq!(container_props.layout.size.height, 50.0);
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::blue())
                );
            },
        );
    }
}
