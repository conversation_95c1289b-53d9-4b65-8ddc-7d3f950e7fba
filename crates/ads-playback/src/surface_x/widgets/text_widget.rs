use fableous::typography::typography::*;
use fableous::*;
use ignx_compositron::{
    compose, id::Id, prelude::safe::*, shared::Shared, text::TextVerticalAlignment, Composer,
    Signify,
};
use typography::type_ramp::TypeRamp;

use crate::surface_x::widgets::container_styles::SurfaceXContainerStyles;

#[derive(Signify)]
pub struct SurfaceXTextWidgetTemplateData {
    pub text: String,
    pub color: Option<Color>,
    pub type_ramp: TypeRamp,
    pub text_alignment: Option<TextVerticalAlignment>,
    pub line_height: Option<f32>,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub truncation_mode: Option<TruncationMode>,
    pub max_lines: Option<u32>,
    pub text_decoration_line: Option<DecorationLine>,
    pub iter_id: String,
}

impl Id for SurfaceXTextWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXTextWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXTextWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let mut container = compose! {
        Column(){
            Memo(item_builder: Box::new(move |ctx| {
                let type_ramp = template_data.type_ramp().get();
                let color = template_data.color().get().unwrap_or(IGNX_PRIMARY_COLOR);
                let text = template_data.text().get();


                let  mut typography = compose! {
                   // Using RichTextLabel because Typography component doesn't support decoration_line
                   RichTextLabel(
                    text,
                   ){}
                   .font_size(type_ramp.font_size)
                   .font_weight(type_ramp.font_weight)
                   .line_height(type_ramp.line_height)
                   .letter_spacing(type_ramp.letter_spacing as f32)
                   .font_families(type_ramp.font_family)
                   .color(color)
                };


                if let Some(decoration_line) = template_data.text_decoration_line().get() {
                    typography = typography.decoration_line(Some(DecorationLineSettings::new(
                        decoration_line,
                        color,
                    )));
                }

                if let Some(line_height) = template_data.line_height().get() {
                    typography = typography.line_height(line_height);
                }

                if let Some(alignment) = template_data.text_alignment().get() {
                    typography = typography.vertical_alignment(alignment);
                }

                if let Some(truncation_mode) = template_data.truncation_mode().get() {
                  typography = typography.truncation_mode(truncation_mode);
                }

                if let Some(max_lines) = template_data.max_lines().get() {
                  typography = typography.max_lines(Some(max_lines));
                }

                Some(typography)
            }))
        }
        .test_id("surface_x_text_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use crate::surface_x::widgets::container_styles::tests::create_mock_container_style;

    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{node_properties::NodeTypeProperties, ComposableType},
    };

    #[test]
    fn test_container_styles() {
        let template_data = create_default_template_data();

        launch_test(
            |ctx| {
                compose! {
                      SurfaceXTextWidget(template_data)
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                assert_eq!(container_props.layout.size.width, 800.0);
                assert_eq!(container_props.layout.size.height, 800.0);
                assert_eq!(
                    container_props.base_styles.background_color,
                    Some(Color::yellow())
                );
                assert_eq!(container_props.base_styles.border_color, Some(Color::red()));
                assert_eq!(container_props.base_styles.border_width, Some(4.0));
                assert_eq!(container_props.base_styles.border_radius, Some(12.0));
                assert_eq!(container_props.base_styles.opacity, Some(0.9));
                assert_eq!(container_props.layout.padding.start, 10.0);
                assert_eq!(container_props.layout.padding.end, 10.0);
                assert_eq!(container_props.layout.padding.top, 10.0);
                assert_eq!(container_props.layout.padding.bottom, 10.0);
            },
        );
    }

    #[test]
    fn test_id_trait() {
        let template_data = create_default_template_data();
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_text_and_layout_properties() {
        let template_data = create_default_template_data();

        launch_test(
            |ctx| compose! { SurfaceXTextWidget(template_data) },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let text_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();
                let props = text_node.get_props();

                assert_eq!(props.text, Some("Test Text".to_string()));

                let text_layout = props
                    .text_layout
                    .as_ref()
                    .expect("Expected text layout properties");
                assert!(!text_layout.is_text_truncated);
                assert_eq!(text_layout.num_lines, 1);
                assert_eq!(text_layout.is_rich_text_parent, Some(true));
            },
        );
    }

    #[test]
    fn test_rich_text_attributes() {
        let template_data = create_default_template_data();

        launch_test(
            |ctx| compose! { SurfaceXTextWidget(template_data) },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let text_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();

                let rich_text_props = match text_node.get_props().node_type_props {
                    NodeTypeProperties::RichText(props) => props,
                    _ => panic!("Expected RichText properties"),
                };

                assert_eq!(
                    rich_text_props.color.as_ref().map(|c| c.0),
                    Some(Color::red())
                );
                // TODO: turn on when pipeline changes goes through https://t.corp.amazon.com/V1741586974/communication
                /* assert_eq!(
                    rich_text_props.font_family,
                    Some(FontFamilies(vec![
                        "Amazon Ember".to_string(),
                        "Default-Font".to_string()
                    ]))
                ); */
                assert_eq!(rich_text_props.font_size, Some(FontSize(14)));
                assert_eq!(rich_text_props.font_weight, Some(FontWeight::Normal));
                assert_eq!(rich_text_props.text_align, Some(ClientTextAlign::End));
                assert_eq!(rich_text_props.line_height, Some(10.0));
                assert_eq!(rich_text_props.letter_spacing, Some(1.0));
                assert_eq!(
                    rich_text_props.text_decoration_line,
                    Some(DecorationLine::STRIKETHROUGH)
                );
                assert_eq!(rich_text_props.text_decoration_color, Some(Color::red()));
            },
        );
    }

    #[test]
    fn test_minimal_props() {
        let template_data = Shared::new(SurfaceXTextWidgetTemplateData {
            text: "Minimal Text".to_string(),
            type_ramp: TypeRamp {
                font_size: FontSize(14),
                font_weight: FontWeight::Normal,
                line_height: 1.2,
                letter_spacing: 0,
                font_family: FontFamilies(vec!["Default-Font".to_string()]),
            },
            color: None,
            text_alignment: None,
            line_height: None,
            container_style: None,
            truncation_mode: None,
            max_lines: None,
            text_decoration_line: None,
            iter_id: String::new(),
        });

        launch_test(
            |ctx| compose! { SurfaceXTextWidget(template_data) },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check container has no styles
                let container = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Column)
                    .find_first();
                let container_props = container.get_props();

                assert_eq!(container_props.base_styles.background_color, None);
                assert_eq!(container_props.base_styles.border_color, None);
                assert_eq!(container_props.base_styles.border_width, None);
                assert_eq!(container_props.base_styles.border_radius, None);
                assert_eq!(container_props.base_styles.opacity, None);
                assert_eq!(container_props.layout.padding.start, 0.0);
                assert_eq!(container_props.layout.padding.end, 0.0);
                assert_eq!(container_props.layout.padding.top, 0.0);
                assert_eq!(container_props.layout.padding.bottom, 0.0);

                // Check text node
                let text_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_first();
                let props = text_node.get_props();

                assert_eq!(props.text, Some("Minimal Text".to_string()));

                // Check rich text properties
                let rich_text_props = match &props.node_type_props {
                    NodeTypeProperties::RichText(props) => props,
                    _ => panic!("Expected RichText properties"),
                };

                assert_eq!(
                    rich_text_props.color.as_ref().map(|c| c.0),
                    Some(IGNX_PRIMARY_COLOR)
                );
                assert_eq!(rich_text_props.text_align, None);
                assert_eq!(rich_text_props.line_height, Some(1.2));
                assert_eq!(rich_text_props.text_decoration_line, None);
                assert_eq!(rich_text_props.text_decoration_color, None);
                assert_eq!(rich_text_props.letter_spacing, Some(0.0));
                assert_eq!(rich_text_props.font_weight, Some(FontWeight::Normal));
            },
        );
    }

    fn create_default_template_data() -> Shared<SurfaceXTextWidgetTemplateData> {
        Shared::new(SurfaceXTextWidgetTemplateData {
            text: "Test Text".to_string(),
            color: Some(Color::red()),
            type_ramp: TypeRamp {
                font_size: FontSize(14),
                font_weight: FontWeight::Normal,
                line_height: 1.2,
                letter_spacing: 1,
                font_family: FontFamilies(vec!["Default-Font".to_string()]),
            },
            text_alignment: Some(TextVerticalAlignment::End),
            line_height: Some(10.0),
            container_style: Some(create_mock_container_style()),
            truncation_mode: Some(TruncationMode::Clip),
            max_lines: Some(2),
            text_decoration_line: Some(DecorationLine::STRIKETHROUGH),
            iter_id: "iter_id".to_string(),
        })
    }
}
