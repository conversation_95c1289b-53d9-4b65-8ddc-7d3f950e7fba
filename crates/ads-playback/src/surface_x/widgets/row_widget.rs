use crate::surface_x::widget_renderer::*;
use crate::surface_x::widgets::container_styles::SurfaceXContainerStyles;
use crate::surface_x::widgets::types::{
    SurfaceXRowChildWidgetTemplateData, SurfaceXWidgetTemplateData,
};
use ignx_compositron::prelude::safe::*;
use ignx_compositron::shared::Shared;
use ignx_compositron::{compose, id::Id, Composer, Signify};
use std::rc::Rc;

#[derive(Signify)]
pub struct SurfaceXRowWidgetTemplateData {
    pub row_components: Vec<SurfaceXRowChildWidgetTemplateData>,
    pub container_style: Option<SurfaceXContainerStyles>,
    pub iter_id: String,
}

impl Id for SurfaceXRowWidgetTemplateData {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.iter_id
    }
}

#[Composer]
pub fn SurfaceXRowWidget<'s>(
    ctx: &AppContext<'s>,
    template_data: Shared<SurfaceXRowWidgetTemplateData>,
) -> impl VisualComposable<'s> {
    let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(template_data));

    let mut container = compose! {
        RowForEach(
            items: template_data.row_components().get(),
            item_builder: Rc::new(move |ctx, item, _idx| {
                // Convert from SurfaceXRowChildWidgetTemplateData to SurfaceXWidgetTemplateData
                let widget_data: SurfaceXWidgetTemplateData = item.clone().into();
                let widget_data = create_ref_signal(ctx.scope(), SharedSignal::from_data(widget_data));
                compose! {
                    WidgetRenderer(widget_data)
                }
            })
        )
        .test_id("surface_x_row_widget")
    };

    if let Some(style) = &template_data.container_style().get() {
        container = style.apply_container_style(container);
    }

    container
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::surface_x::widgets::text_widget::SurfaceXTextWidgetTemplateData;
    use amzn_fable_tokens::FableText;
    use fableous::typography::type_ramp::TypeRamp;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::color::Color;
    use ignx_compositron::test_utils::{assert_node_exists, ComposableType};

    #[test]
    fn test_id_trait() {
        let template_data = SurfaceXRowWidgetTemplateData {
            row_components: vec![],
            container_style: None,
            iter_id: "iter_id".to_string(),
        };
        assert_eq!(template_data.id(), &"iter_id".to_string());
    }

    #[test]
    fn test_row_widget_renders_components() {
        let text_widget_data = Shared::new(SurfaceXTextWidgetTemplateData {
            text: "Test Text".to_string(),
            type_ramp: TypeRamp::from(FableText::TYPE_BODY200),
            color: Some(Color::red()),
            text_alignment: None,
            line_height: None,
            container_style: None,
            truncation_mode: None,
            max_lines: None,
            text_decoration_line: None,
            iter_id: String::new(),
        });

        let row_widget_data = SurfaceXRowWidgetTemplateData {
            row_components: vec![
                SurfaceXRowChildWidgetTemplateData::Text(text_widget_data.clone()),
                SurfaceXRowChildWidgetTemplateData::Text(text_widget_data),
            ],
            container_style: Some(SurfaceXContainerStyles {
                background_color: Some(Color::blue()),
                width: Some(300.0),
                height: Some(100.0),
                ..Default::default()
            }),
            iter_id: String::new(),
        };

        launch_test(
            |ctx| {
                compose! {
                    SurfaceXRowWidget(template_data: Shared::new(row_widget_data))
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check that we have a RowForEach
                let row = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RowForEach)
                    .find_first();
                assert_node_exists!(&row);

                // Check container styles
                let row_props = row.get_props();
                assert_eq!(row_props.base_styles.background_color, Some(Color::blue()));
                assert_eq!(row_props.layout.size.width, 300.0);
                assert_eq!(row_props.layout.size.height, 100.0);

                // Check that we have two text components
                let text_nodes = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::RichTextLabel)
                    .find_all();
                assert_eq!(text_nodes.len(), 2);
            },
        );
    }
}
