use crate::surface_x::widgets::{
    divider_widget::SurfaceXDividerOrientation,
    types::{SurfaceXRowChildWidgetTemplateData, SurfaceXWidgetTemplateData},
    SurfaceXBadgeWidgetTemplateData, SurfaceXDividerWidgetTemplateData,
    SurfaceXIconWidgetTemplateData, SurfaceXImageWidgetTemplateData,
    SurfaceXPrimaryButtonWidgetTemplateData, SurfaceXPrimeLogoWidgetTemplateData,
    SurfaceXRowWidgetTemplateData, SurfaceXStarRatingWidgetTemplateData,
    SurfaceXTextWidgetTemplateData,
};
use ads_playback_types::surface_x::widget_templates::{
    BadgeWidgetTemplate, DividerWidgetTemplate, IconWidgetTemplate, ImageWidgetTemplate,
    PrimaryButtonWidgetTemplate, PrimeLogoWidgetTemplate, PrimitiveWidgetTemplates,
    RowWidgetTemplate, StarRatingWidgetTemplate, SurfaceXWidgetTemplates, TextWidgetTemplate,
};
use ignx_compositron::{font::FontSize, shared::Shared};
use uuid::Uuid;

use crate::surface_x::template_hydration::parsers::{
    parse_color_string, parse_container_style, parse_decoration_line, parse_text_alignment,
    parse_truncation_mode, parse_type_ramp, WidgetOrientation,
};

/// Populates a `SurfaceX` widget template received from SOS (`SurfaceX` Orchestration Service) with data and converts values
/// into their corresponding Rust types.
///
/// This function handles conversion of raw template values into strongly-typed Rust objects. For example:
/// - `{font_size: 10}` becomes `{font_size: FontSize::from(10)}`
/// - `{color: "#FF0000"}` becomes `{color: Color::from_hex("#FF0000")}`
///
/// The function also validates that all required fields are present and contain valid values.
/// If validation fails or type conversion fails, it returns an error message explaining what went wrong.
///
/// # Arguments
/// * `template` - The `SurfaceX` widget template to hydrate
///
/// # Returns
/// * `Ok(SurfaceXWidgetTemplateData)` - Successfully hydrated widget data
/// * `Err(String)` - Error message if hydration/validation fails
///
pub fn hydrate_widget_template(
    template: &SurfaceXWidgetTemplates,
) -> Result<SurfaceXWidgetTemplateData, String> {
    match template {
        SurfaceXWidgetTemplates::TextWidget(text_template) => hydrate_text_widget(text_template)
            .map(|data| SurfaceXWidgetTemplateData::Text(Shared::new(data))),

        SurfaceXWidgetTemplates::ImageWidget(image_template) => {
            hydrate_image_widget(image_template)
                .map(|data| SurfaceXWidgetTemplateData::Image(Shared::new(data)))
        }

        SurfaceXWidgetTemplates::BadgeWidget(badge_template) => {
            hydrate_badge_widget(badge_template)
                .map(|data| SurfaceXWidgetTemplateData::Badge(Shared::new(data)))
        }

        SurfaceXWidgetTemplates::IconWidget(icon_template) => hydrate_icon_widget(icon_template)
            .map(|data| SurfaceXWidgetTemplateData::Icon(Shared::new(data))),

        SurfaceXWidgetTemplates::DividerWidget(divider_template) => {
            hydrate_divider_widget(divider_template)
                .map(|data| SurfaceXWidgetTemplateData::Divider(Shared::new(data)))
        }

        SurfaceXWidgetTemplates::PrimaryButtonWidget(primary_button_template) => {
            hydrate_primary_button_widget(primary_button_template)
                .map(|data| SurfaceXWidgetTemplateData::PrimaryButton(Shared::new(data)))
        }

        SurfaceXWidgetTemplates::PrimeLogoWidget(prime_logo_template) => {
            hydrate_prime_logo_widget(prime_logo_template)
                .map(|data| SurfaceXWidgetTemplateData::PrimeLogo(Shared::new(data)))
        }

        SurfaceXWidgetTemplates::StarRatingWidget(star_rating_template) => {
            hydrate_star_rating_widget(star_rating_template)
                .map(|data| SurfaceXWidgetTemplateData::StarRating(Shared::new(data)))
        }

        SurfaceXWidgetTemplates::RowWidget(row_widget_template) => {
            hydrate_row_widget(row_widget_template)
                .map(|data| SurfaceXWidgetTemplateData::Row(Shared::new(data)))
        }
    }
}

fn hydrate_text_widget(
    text_template: &TextWidgetTemplate,
) -> Result<SurfaceXTextWidgetTemplateData, String> {
    let props = &text_template.props;

    // Convert type_ramp from String to TypeRamp
    let type_ramp = parse_type_ramp(&props.type_ramp)?;

    // Convert color from Option<String> to Option<Color>
    let color = match &props.color {
        Some(color_str) => Some(parse_color_string(color_str)?),
        None => None,
    };

    // Convert text_align from Option<String> to Option<TextVerticalAlignment>
    // NOTE: It uses the wrong name. It is actually horizontal alingnment
    let text_alignment = match &props.text_align {
        Some(align_str) => Some(parse_text_alignment(align_str)?),
        None => None,
    };

    // Convert ellipsize_mode from Option<String> to Option<TruncationMode>
    let truncation_mode = match &props.ellipsize_mode {
        Some(mode_str) => Some(parse_truncation_mode(mode_str)?),
        None => None,
    };

    // Convert text_decoration_line from Option<String> to Option<DecorationLine>
    let text_decoration_line = match &props.text_decoration_line {
        Some(line_str) => parse_decoration_line(line_str)?,
        None => None,
    };

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXTextWidgetTemplateData {
        text: props.text.clone(),
        color,
        type_ramp,
        text_alignment,
        line_height: props.line_height,
        container_style,
        truncation_mode,
        max_lines: props.number_of_lines,
        text_decoration_line,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_image_widget(
    image_template: &ImageWidgetTemplate,
) -> Result<SurfaceXImageWidgetTemplateData, String> {
    let props = &image_template.props;

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXImageWidgetTemplateData {
        width: props.width,
        height: props.height,
        image_url: props.image_url.clone(),
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_badge_widget(
    badge_template: &BadgeWidgetTemplate,
) -> Result<SurfaceXBadgeWidgetTemplateData, String> {
    let props = &badge_template.props;

    // Convert background_color from String to Color
    let background_color = parse_color_string(&props.background_color)?;

    // Convert type_ramp from Option<String> to Option<TypeRamp>
    let type_ramp = match &props.type_ramp {
        Some(type_ramp_str) => Some(parse_type_ramp(type_ramp_str)?),
        None => None,
    };

    // Convert text_color from Option<String> to Option<Color>
    let text_color = match &props.text_color {
        Some(color_str) => Some(parse_color_string(color_str)?),
        None => None,
    };

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXBadgeWidgetTemplateData {
        text: props.text.clone(),
        background_color,
        type_ramp,
        text_color,
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_icon_widget(
    icon_template: &IconWidgetTemplate,
) -> Result<SurfaceXIconWidgetTemplateData, String> {
    let props = &icon_template.props;

    // Convert color from Option<String> to Option<Color>
    let color = match &props.color {
        Some(color_str) => Some(parse_color_string(color_str)?),
        None => None,
    };

    // Convert font_size from Option<u32> to Option<FontSize>
    let font_size = props.font_size.map(FontSize::from);

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXIconWidgetTemplateData {
        icon_name: props.icon_name.clone(),
        color,
        font_size,
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_divider_widget(
    divider_template: &DividerWidgetTemplate,
) -> Result<SurfaceXDividerWidgetTemplateData, String> {
    let props = &divider_template.props;

    let orientation = match props.orientation.to_lowercase().as_str() {
        "vertical" => SurfaceXDividerOrientation::Vertical,
        "horizontal" => SurfaceXDividerOrientation::Horizontal,
        _ => return Err(format!("Invalid orientation: {}", props.orientation)),
    };

    // Convert color from Option<String> to Option<Color>
    let color = match &props.color {
        Some(color_str) => Some(parse_color_string(color_str)?),
        None => None,
    };

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXDividerWidgetTemplateData {
        orientation,
        length: props.length,
        color,
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_primary_button_widget(
    primary_button_template: &PrimaryButtonWidgetTemplate,
) -> Result<SurfaceXPrimaryButtonWidgetTemplateData, String> {
    let props = &primary_button_template.props;

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXPrimaryButtonWidgetTemplateData {
        text: props.text.clone(),
        icon_name: props.icon_name.clone(),
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_prime_logo_widget(
    prime_logo_template: &PrimeLogoWidgetTemplate,
) -> Result<SurfaceXPrimeLogoWidgetTemplateData, String> {
    let props = &prime_logo_template.props;

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXPrimeLogoWidgetTemplateData {
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_row_widget(
    row_widget_template: &RowWidgetTemplate,
) -> Result<SurfaceXRowWidgetTemplateData, String> {
    let props = &row_widget_template.props;

    let mut row_components = Vec::new();

    for component in props.row_components.iter() {
        let child_widget_data = match component {
            PrimitiveWidgetTemplates::TextWidget(template) => {
                let data = hydrate_text_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::Text(Shared::new(data))
            }
            PrimitiveWidgetTemplates::ImageWidget(template) => {
                let data = hydrate_image_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::Image(Shared::new(data))
            }
            PrimitiveWidgetTemplates::BadgeWidget(template) => {
                let data = hydrate_badge_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::Badge(Shared::new(data))
            }
            PrimitiveWidgetTemplates::IconWidget(template) => {
                let data = hydrate_icon_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::Icon(Shared::new(data))
            }
            PrimitiveWidgetTemplates::DividerWidget(template) => {
                let data = hydrate_divider_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::Divider(Shared::new(data))
            }
            PrimitiveWidgetTemplates::PrimaryButtonWidget(template) => {
                let data = hydrate_primary_button_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::PrimaryButton(Shared::new(data))
            }
            PrimitiveWidgetTemplates::PrimeLogoWidget(template) => {
                let data = hydrate_prime_logo_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::PrimeLogo(Shared::new(data))
            }
            PrimitiveWidgetTemplates::StarRatingWidget(template) => {
                let data = hydrate_star_rating_widget(template)?;
                SurfaceXRowChildWidgetTemplateData::StarRating(Shared::new(data))
            }
        };

        // Use the index as the ID for the row component
        row_components.push(child_widget_data);
    }

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Horizontal))
        .transpose()?;

    Ok(SurfaceXRowWidgetTemplateData {
        row_components,
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

fn hydrate_star_rating_widget(
    star_rating_template: &StarRatingWidgetTemplate,
) -> Result<SurfaceXStarRatingWidgetTemplateData, String> {
    let props = &star_rating_template.props;

    // Convert rating from String to f32
    let rating = props
        .rating
        .parse::<f32>()
        .map_err(|e| format!("Invalid rating: {}", e))?;

    // Convert star_size from u32 to FontSize
    let star_size = FontSize::from(props.star_size);

    // Convert full_star_color from String to Color
    let full_star_color = parse_color_string(&props.full_star_color)?;

    // Convert empty_star_color from String to Color
    let empty_star_color = parse_color_string(&props.empty_star_color)?;

    // Convert rating_text_type_ramp from Option<String> to Option<TypeRamp>
    let rating_text_type_ramp = match &props.rating_text_type_ramp {
        Some(type_ramp_str) => Some(parse_type_ramp(type_ramp_str)?),
        None => None,
    };

    let container_style = props
        .container_style
        .as_ref()
        .map(|style| parse_container_style(style, WidgetOrientation::Vertical))
        .transpose()?;

    Ok(SurfaceXStarRatingWidgetTemplateData {
        rating,
        rating_count: props.rating_count.clone(),
        star_size,
        full_star_color,
        empty_star_color,
        star_separator_width: props.star_separator_width,
        show_rating_number: props.show_text,
        rating_text_type_ramp,
        container_style,
        iter_id: Uuid::new_v4().to_string(),
    })
}

#[cfg(test)]
mod tests {
    use std::str::FromStr;

    use ads_playback_types::surface_x::widget_templates::{
        BadgeWidgetPropsTemplate, DividerWidgetPropsTemplate, IconWidgetPropsTemplate,
        ImageWidgetPropsTemplate, PrimaryButtonWidgetPropsTemplate, PrimeLogoWidgetPropsTemplate,
        RowWidgetPropsTemplate, StarRatingWidgetPropsTemplate, SurfaceXContainerStylesTemplate,
        SurfaceXWidgetTypes, TextWidgetPropsTemplate,
    };
    use fableous::typography::type_ramp::TypeRamp;
    use ignx_compositron::{
        app::launch_only_scope,
        color::Color,
        prelude::{safe::SharedSignal, SignalGet},
        text::{TextVerticalAlignment, TruncationMode},
        DecorationLine,
    };

    use crate::surface_x::widgets::{
        SurfaceXBadgeWidgetTemplateDataProperties, SurfaceXDividerWidgetTemplateDataProperties,
        SurfaceXIconWidgetTemplateDataProperties, SurfaceXImageWidgetTemplateDataProperties,
        SurfaceXPrimaryButtonWidgetTemplateDataProperties,
        SurfaceXPrimeLogoWidgetTemplateDataProperties, SurfaceXRowWidgetTemplateDataProperties,
        SurfaceXStarRatingWidgetTemplateDataProperties, SurfaceXTextWidgetTemplateDataProperties,
    };

    use super::*;

    #[test]
    fn test_hydrate_text_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.width = Some(100.0);

            let text_template = TextWidgetTemplate {
                type_field: SurfaceXWidgetTypes::TextWidget,
                props: TextWidgetPropsTemplate {
                    text: "Hello World".to_string(),
                    type_ramp: "body".to_string(),
                    color: Some("#FF0000".to_string()),
                    text_align: Some("center".to_string()),
                    line_height: None,
                    ellipsize_mode: Some("tail".to_string()),
                    number_of_lines: Some(2),
                    text_decoration_line: Some("underline".to_string()),
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::TextWidget(text_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::Text(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert_eq!(data_ref.text().get(), "Hello World");
                assert_eq!(data_ref.type_ramp().get(), TypeRamp::from("body"));
                assert_eq!(
                    data_ref.color().get(),
                    Some(Color::from_str("#FF0000").unwrap())
                );
                assert_eq!(
                    data_ref.text_alignment().get(),
                    Some(TextVerticalAlignment::Center)
                );
                assert_eq!(data_ref.truncation_mode().get(), Some(TruncationMode::Tail));
                assert_eq!(data_ref.max_lines().get(), Some(2));
                assert_eq!(
                    data_ref.text_decoration_line().get(),
                    Some(DecorationLine::UNDERLINE)
                );
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref.container_style().get().as_ref().unwrap().width,
                    Some(100.0)
                );
            }
        })
    }

    #[test]
    fn test_hydrate_text_widget_invalid_color() {
        launch_only_scope(|_| {
            let text_template = TextWidgetTemplate {
                type_field: SurfaceXWidgetTypes::TextWidget,
                props: TextWidgetPropsTemplate {
                    text: "Hello World".to_string(),
                    type_ramp: "body".to_string(),
                    color: Some("invalid-color".to_string()),
                    text_align: None,
                    line_height: None,
                    ellipsize_mode: None,
                    number_of_lines: None,
                    text_decoration_line: None,
                    container_style: None,
                },
            };

            let widget_template = SurfaceXWidgetTemplates::TextWidget(text_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Invalid color"));
        })
    }

    // IMAGE WIDGET TESTS
    #[test]
    fn test_hydrate_image_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.height = Some(200.0);

            let image_template = ImageWidgetTemplate {
                type_field: SurfaceXWidgetTypes::ImageWidget,
                props: ImageWidgetPropsTemplate {
                    width: 100.0,
                    height: 200.0,
                    image_url: "https://example.com/image.jpg".to_string(),
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::ImageWidget(image_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::Image(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert_eq!(data_ref.width().get(), 100.0);
                assert_eq!(data_ref.height().get(), 200.0);
                assert_eq!(data_ref.image_url().get(), "https://example.com/image.jpg");
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref.container_style().get().as_ref().unwrap().height,
                    Some(200.0)
                );
            }
        })
    }

    #[test]
    fn test_hydrate_image_widget_invalid_url() {
        launch_only_scope(|_| {
            let image_template = ImageWidgetTemplate {
                type_field: SurfaceXWidgetTypes::ImageWidget,
                props: ImageWidgetPropsTemplate {
                    width: 100.0,
                    height: 200.0,
                    image_url: "".to_string(), // Empty URL
                    container_style: None,
                },
            };

            let widget_template = SurfaceXWidgetTemplates::ImageWidget(image_template);
            let result = hydrate_widget_template(&widget_template);
            // Note: Currently the implementation doesn't validate URLs, so this test would pass
            // This test is a placeholder for when URL validation is added
            assert!(result.is_ok());
        })
    }

    // BADGE WIDGET TESTS
    #[test]
    fn test_hydrate_badge_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.background_color = Some("#00FF00".to_string());

            let badge_template = BadgeWidgetTemplate {
                type_field: SurfaceXWidgetTypes::BadgeWidget,
                props: BadgeWidgetPropsTemplate {
                    text: "New".to_string(),
                    background_color: "#FF0000".to_string(),
                    type_ramp: Some("body".to_string()),
                    text_color: Some("#FFFFFF".to_string()),
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::BadgeWidget(badge_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::Badge(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert_eq!(data_ref.text().get(), "New");
                assert_eq!(
                    data_ref.background_color().get(),
                    Color::from_str("#FF0000").unwrap()
                );
                assert_eq!(data_ref.type_ramp().get(), Some(TypeRamp::from("body")));
                assert_eq!(
                    data_ref.text_color().get(),
                    Some(Color::from_str("#FFFFFF").unwrap())
                );
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref
                        .container_style()
                        .get()
                        .as_ref()
                        .unwrap()
                        .background_color,
                    Some(Color::from_str("#00FF00").unwrap())
                );
            }
        })
    }

    #[test]
    fn test_hydrate_badge_widget_invalid_color() {
        launch_only_scope(|_| {
            let badge_template = BadgeWidgetTemplate {
                type_field: SurfaceXWidgetTypes::BadgeWidget,
                props: BadgeWidgetPropsTemplate {
                    text: "New".to_string(),
                    background_color: "invalid-color".to_string(),
                    type_ramp: None,
                    text_color: None,
                    container_style: None,
                },
            };

            let widget_template = SurfaceXWidgetTemplates::BadgeWidget(badge_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Invalid color"));
        })
    }

    // ICON WIDGET TESTS
    #[test]
    fn test_hydrate_icon_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.opacity = Some(0.8);

            let icon_template = IconWidgetTemplate {
                type_field: SurfaceXWidgetTypes::IconWidget,
                props: IconWidgetPropsTemplate {
                    icon_name: "star".to_string(),
                    color: Some("#FF0000".to_string()),
                    font_size: Some(24),
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::IconWidget(icon_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::Icon(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert_eq!(data_ref.icon_name().get(), "star");
                assert_eq!(
                    data_ref.color().get(),
                    Some(Color::from_str("#FF0000").unwrap())
                );
                assert_eq!(data_ref.font_size().get(), Some(FontSize::from(24)));
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref.container_style().get().as_ref().unwrap().opacity,
                    Some(0.8)
                );
            }
        })
    }

    #[test]
    fn test_hydrate_icon_widget_invalid_color() {
        launch_only_scope(|_| {
            let icon_template = IconWidgetTemplate {
                type_field: SurfaceXWidgetTypes::IconWidget,
                props: IconWidgetPropsTemplate {
                    icon_name: "star".to_string(),
                    color: Some("invalid-color".to_string()),
                    font_size: Some(24),
                    container_style: None,
                },
            };

            let widget_template = SurfaceXWidgetTemplates::IconWidget(icon_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Invalid color"));
        })
    }

    // DIVIDER WIDGET TESTS
    #[test]
    fn test_hydrate_divider_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.border_radius = Some(5.0);

            let divider_template = DividerWidgetTemplate {
                type_field: SurfaceXWidgetTypes::DividerWidget,
                props: DividerWidgetPropsTemplate {
                    orientation: "horizontal".to_string(),
                    length: 100.0,
                    color: Some("#FF0000".to_string()),
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::DividerWidget(divider_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::Divider(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert_eq!(
                    data_ref.orientation().get(),
                    SurfaceXDividerOrientation::Horizontal
                );
                assert_eq!(
                    data_ref.color().get(),
                    Some(Color::from_str("#FF0000").unwrap())
                );
                assert_eq!(data_ref.length().get(), 100.0);
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref
                        .container_style()
                        .get()
                        .as_ref()
                        .unwrap()
                        .border_radius,
                    Some(5.0)
                );
            }
        })
    }

    #[test]
    fn test_hydrate_divider_widget_invalid_orientation() {
        launch_only_scope(|_| {
            let divider_template = DividerWidgetTemplate {
                type_field: SurfaceXWidgetTypes::DividerWidget,
                props: DividerWidgetPropsTemplate {
                    orientation: "diagonal".to_string(),
                    length: 100.0,
                    color: None,
                    container_style: None,
                },
            };

            let widget_template = SurfaceXWidgetTemplates::DividerWidget(divider_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Invalid orientation"));
        })
    }

    // PRIMARY BUTTON WIDGET TESTS
    #[test]
    fn test_hydrate_primary_button_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.margin_top = Some(10.0);

            let primary_button_template = PrimaryButtonWidgetTemplate {
                type_field: SurfaceXWidgetTypes::PrimaryButtonWidget,
                props: PrimaryButtonWidgetPropsTemplate {
                    text: "Click Me".to_string(),
                    icon_name: Some("arrow".to_string()),
                    container_style: Some(container_style),
                },
            };

            let widget_template =
                SurfaceXWidgetTemplates::PrimaryButtonWidget(primary_button_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::PrimaryButton(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert_eq!(data_ref.text().get(), "Click Me");
                assert_eq!(data_ref.icon_name().get(), Some("arrow".to_string()));
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref
                        .container_style()
                        .get()
                        .as_ref()
                        .unwrap()
                        .margin_top,
                    Some(10.0)
                );
            }
        })
    }

    #[test]
    fn test_hydrate_primary_button_widget_empty_text() {
        launch_only_scope(|_| {
            let primary_button_template = PrimaryButtonWidgetTemplate {
                type_field: SurfaceXWidgetTypes::PrimaryButtonWidget,
                props: PrimaryButtonWidgetPropsTemplate {
                    text: "".to_string(), // Empty text
                    icon_name: None,
                    container_style: None,
                },
            };

            let widget_template =
                SurfaceXWidgetTemplates::PrimaryButtonWidget(primary_button_template);
            let result = hydrate_widget_template(&widget_template);
            // Note: Currently the implementation doesn't validate empty text, so this test would pass
            // This test is a placeholder for when text validation is added
            assert!(result.is_ok());
        })
    }

    // PRIME LOGO WIDGET TESTS
    #[test]
    fn test_hydrate_prime_logo_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.padding_top = Some(5.0);

            let prime_logo_template = PrimeLogoWidgetTemplate {
                type_field: SurfaceXWidgetTypes::PrimeLogoWidget,
                props: PrimeLogoWidgetPropsTemplate {
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::PrimeLogoWidget(prime_logo_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::PrimeLogo(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref
                        .container_style()
                        .get()
                        .as_ref()
                        .unwrap()
                        .padding_top,
                    Some(5.0)
                );
            }
        })
    }

    #[test]
    fn test_hydrate_prime_logo_widget_invalid_container_style() {
        launch_only_scope(|_| {
            let container_style = SurfaceXContainerStylesTemplate {
                vertical_alignment: Some("invalid".to_string()),
                ..Default::default()
            };

            let prime_logo_template = PrimeLogoWidgetTemplate {
                type_field: SurfaceXWidgetTypes::PrimeLogoWidget,
                props: PrimeLogoWidgetPropsTemplate {
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::PrimeLogoWidget(prime_logo_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Unknown main axis alignment"));
        })
    }

    // STAR RATING WIDGET TESTS
    #[test]
    fn test_hydrate_star_rating_widget() {
        launch_only_scope(|_| {
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.padding_bottom = Some(15.0);

            let star_rating_template = StarRatingWidgetTemplate {
                type_field: SurfaceXWidgetTypes::StarRatingWidget,
                props: StarRatingWidgetPropsTemplate {
                    rating: "4.5".to_string(),
                    rating_count: "1234".to_string(),
                    star_size: 24,
                    full_star_color: "#FFD700".to_string(),
                    empty_star_color: "#CCCCCC".to_string(),
                    star_separator_width: 2.0,
                    show_text: Some(true),
                    rating_text_type_ramp: Some("body".to_string()),
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::StarRatingWidget(star_rating_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::StarRating(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert_eq!(data_ref.rating().get(), 4.5);
                assert_eq!(data_ref.rating_count().get(), "1234".to_string());
                assert_eq!(data_ref.star_size().get(), FontSize::from(24));
                assert_eq!(
                    data_ref.full_star_color().get(),
                    Color::from_str("#FFD700").unwrap()
                );
                assert_eq!(
                    data_ref.empty_star_color().get(),
                    Color::from_str("#CCCCCC").unwrap()
                );
                assert_eq!(data_ref.star_separator_width().get(), 2.0);
                assert_eq!(data_ref.show_rating_number().get(), Some(true));
                assert_eq!(
                    data_ref.rating_text_type_ramp().get(),
                    Some(TypeRamp::from("body"))
                );
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref
                        .container_style()
                        .get()
                        .as_ref()
                        .unwrap()
                        .padding_bottom,
                    Some(15.0)
                );
            }
        })
    }

    #[test]
    fn test_hydrate_star_rating_widget_invalid_rating() {
        launch_only_scope(|_| {
            let star_rating_template = StarRatingWidgetTemplate {
                type_field: SurfaceXWidgetTypes::StarRatingWidget,
                props: StarRatingWidgetPropsTemplate {
                    rating: "not-a-number".to_string(),
                    rating_count: "1234".to_string(),
                    star_size: 24,
                    full_star_color: "#FFD700".to_string(),
                    empty_star_color: "#CCCCCC".to_string(),
                    star_separator_width: 2.0,
                    show_text: None,
                    rating_text_type_ramp: None,
                    container_style: None,
                },
            };

            let widget_template = SurfaceXWidgetTemplates::StarRatingWidget(star_rating_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Invalid rating"));
        })
    }

    // CONTAINER STYLE TESTS
    #[test]
    fn test_hydrate_container_style_with_all_properties() {
        launch_only_scope(|_| {
            let container_style = SurfaceXContainerStylesTemplate {
                vertical_alignment: Some("center".to_string()),
                horizontal_alignment: Some("start".to_string()),
                width: Some(100.0),
                height: Some(200.0),
                max_height: Some(300.0),
                min_height: Some(50.0),
                max_width: Some(400.0),
                min_width: Some(50.0),
                padding_top: Some(10.0),
                padding_end: Some(20.0),
                padding_bottom: Some(30.0),
                padding_start: Some(40.0),
                margin_top: Some(5.0),
                margin_end: Some(10.0),
                margin_bottom: Some(15.0),
                margin_start: Some(20.0),
                background_color: Some("#00FF00".to_string()),
                border_color: Some("#FF0000".to_string()),
                border_width: Some(1.0),
                border_radius: Some(5.0),
                opacity: Some(0.5),
            };

            let text_template = TextWidgetTemplate {
                type_field: SurfaceXWidgetTypes::TextWidget,
                props: TextWidgetPropsTemplate {
                    text: "Hello World".to_string(),
                    type_ramp: "body".to_string(),
                    color: None,
                    text_align: None,
                    line_height: None,
                    ellipsize_mode: None,
                    number_of_lines: None,
                    text_decoration_line: None,
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::TextWidget(text_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::Text(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                assert!(data_ref.container_style().get().is_some());
                let style = data_ref.container_style().get().unwrap();
                assert_eq!(
                    style.background_color,
                    Some(Color::from_str("#00FF00").unwrap())
                );
                assert_eq!(
                    style.border_color,
                    Some(Color::from_str("#FF0000").unwrap())
                );
                assert_eq!(style.border_width, Some(1.0));
                assert_eq!(style.border_radius, Some(5.0));
                assert_eq!(style.opacity, Some(0.5));
                assert_eq!(style.width, Some(100.0));
                assert_eq!(style.height, Some(200.0));
                assert_eq!(style.max_height, Some(300.0));
                assert_eq!(style.min_height, Some(50.0));
                assert_eq!(style.max_width, Some(400.0));
                assert_eq!(style.min_width, Some(50.0));
                assert_eq!(style.padding_top, Some(10.0));
                assert_eq!(style.padding_end, Some(20.0));
                assert_eq!(style.padding_bottom, Some(30.0));
                assert_eq!(style.padding_start, Some(40.0));
                assert_eq!(style.margin_top, Some(5.0));
                assert_eq!(style.margin_end, Some(10.0));
                assert_eq!(style.margin_bottom, Some(15.0));
                assert_eq!(style.margin_start, Some(20.0));
            }
        })
    }

    #[test]
    fn test_hydrate_container_style_invalid_alignment() {
        launch_only_scope(|_| {
            let container_style = SurfaceXContainerStylesTemplate {
                vertical_alignment: Some("invalid".to_string()),
                ..Default::default()
            };

            let text_template = TextWidgetTemplate {
                type_field: SurfaceXWidgetTypes::TextWidget,
                props: TextWidgetPropsTemplate {
                    text: "Hello World".to_string(),
                    type_ramp: "body".to_string(),
                    color: None,
                    text_align: None,
                    line_height: None,
                    ellipsize_mode: None,
                    number_of_lines: None,
                    text_decoration_line: None,
                    container_style: Some(container_style),
                },
            };

            let widget_template = SurfaceXWidgetTemplates::TextWidget(text_template);
            let result = hydrate_widget_template(&widget_template);
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Unknown main axis alignment"));
        })
    }

    // ROW WIDGET TESTS
    #[test]
    fn test_hydrate_row_widget_with_image() {
        launch_only_scope(|_| {
            // Create an image widget for the row
            let image_props = ImageWidgetPropsTemplate {
                width: 100.0,
                height: 200.0,
                image_url: "https://example.com/image.jpg".to_string(),
                container_style: None,
            };

            let image_template = ImageWidgetTemplate {
                type_field: SurfaceXWidgetTypes::ImageWidget,
                props: image_props,
            };

            // Create a row widget with the image as a component
            let row_props = RowWidgetPropsTemplate {
                row_components: vec![PrimitiveWidgetTemplates::ImageWidget(image_template)],
                container_style: Some(SurfaceXContainerStylesTemplate {
                    width: Some(300.0),
                    ..Default::default()
                }),
            };

            let row_template = RowWidgetTemplate {
                type_field: SurfaceXWidgetTypes::RowWidget,
                props: row_props,
            };

            let widget_template = SurfaceXWidgetTemplates::RowWidget(row_template);
            let result = hydrate_widget_template(&widget_template);

            assert!(result.is_ok());

            if let Ok(SurfaceXWidgetTemplateData::Row(data)) = result {
                let data = SharedSignal::from_shared(data);
                let data_ref = data.as_ref();

                // Check that we have one component
                assert_eq!(data_ref.row_components().get().len(), 1);

                // Check that the component is an image with the right properties
                let component = &data_ref.row_components().get()[0];

                if let SurfaceXRowChildWidgetTemplateData::Image(image_data) = component {
                    let image_data = SharedSignal::from_shared(image_data.to_owned());
                    let image_data_ref = image_data.as_ref();

                    assert_eq!(image_data_ref.width().get(), 100.0);
                    assert_eq!(image_data_ref.height().get(), 200.0);
                    assert_eq!(
                        image_data_ref.image_url().get(),
                        "https://example.com/image.jpg"
                    );
                } else {
                    panic!("Expected Image component");
                }

                // Check that container style was processed with horizontal orientation
                assert!(data_ref.container_style().get().is_some());
                assert_eq!(
                    data_ref.container_style().get().as_ref().unwrap().width,
                    Some(300.0)
                );
            } else {
                panic!("Expected Row widget data");
            }
        })
    }

    #[test]
    fn test_hydrate_row_widget_with_invalid_image() {
        launch_only_scope(|_| {
            // Create an image widget with invalid color in container style
            let mut container_style = SurfaceXContainerStylesTemplate::default();
            container_style.background_color = Some("invalid-color".to_string());

            let image_props = ImageWidgetPropsTemplate {
                width: 100.0,
                height: 200.0,
                image_url: "https://example.com/image.jpg".to_string(),
                container_style: Some(container_style),
            };

            let image_template = ImageWidgetTemplate {
                type_field: SurfaceXWidgetTypes::ImageWidget,
                props: image_props,
            };

            // Create a row widget with the invalid image as a component
            let row_props = RowWidgetPropsTemplate {
                row_components: vec![PrimitiveWidgetTemplates::ImageWidget(image_template)],
                container_style: None,
            };

            let row_template = RowWidgetTemplate {
                type_field: SurfaceXWidgetTypes::RowWidget,
                props: row_props,
            };

            let widget_template = SurfaceXWidgetTemplates::RowWidget(row_template);
            let result = hydrate_widget_template(&widget_template);

            // The error from the image should propagate up
            assert!(result.is_err());
            let err = result.err().unwrap();
            assert!(err.contains("Invalid color"));
        })
    }
}
