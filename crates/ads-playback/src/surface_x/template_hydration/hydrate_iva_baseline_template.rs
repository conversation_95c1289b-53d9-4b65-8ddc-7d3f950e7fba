use crate::surface_x::{
    template_hydration::hydrate_widget_template, widgets::types::SurfaceXWidgetTemplateData,
};
use ads_playback_types::surface_x::ad_formats::baseline_iva::{BaselineIVATemplate, CardPosition};
use ignx_compositron::{shared::Shared, Signify};

#[derive(Signify)]
pub struct IvaBaselineTemplateData {
    pub main_icon: SurfaceXWidgetTemplateData,
    pub main_content: Vec<Shared<SurfaceXWidgetTemplateData>>,
    pub cta_button: Option<Shared<SurfaceXWidgetTemplateData>>,
    pub hint_text: Option<Shared<SurfaceXWidgetTemplateData>>,
    pub card_position: Option<CardPosition>,
    pub cta_background_color: Option<String>,
}

pub fn hydrate_iva_baseline_template(
    template: &BaselineIVATemplate,
) -> Result<IvaBaselineTemplateData, String> {
    //TODO: Add template validation using mandatory fields
    //TODO: Parse accessibility and animation fields
    //TODO: Replace string placeholders with values from ad metadata

    let props = &template.template_props;

    let hydrate_optional = |widget: &Option<_>| -> Result<Option<Shared<_>>, String> {
        widget
            .as_ref()
            .map(|w| hydrate_widget_template(w).map(Shared::new))
            .transpose()
    };

    let template_data = IvaBaselineTemplateData {
        main_icon: hydrate_widget_template(&props.main_icon)?,
        main_content: props
            .main_content
            .iter()
            .map(|w| hydrate_widget_template(w).map(Shared::new))
            .collect::<Result<Vec<_>, _>>()?,
        cta_button: hydrate_optional(&props.cta_button)?,
        hint_text: hydrate_optional(&props.hint_text)?,
        card_position: props.card_position.clone(),
        cta_background_color: props.cta_background_color.clone(),
    };

    Ok(template_data)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_data::create_test_iva_baseline_template;

    #[test]
    fn test_hydrate_iva_baseline_template_success() {
        let template = create_test_iva_baseline_template();
        let result = hydrate_iva_baseline_template(&template);

        assert!(result.is_ok());
        let data = result.unwrap();
        assert_eq!(data.card_position, Some(CardPosition::Right));
        assert_eq!(data.cta_background_color, Some("#000000".to_string()));
        assert_eq!(data.main_content.len(), 1);
        assert!(data.cta_button.is_none());
        assert!(data.hint_text.is_some());
    }
}
