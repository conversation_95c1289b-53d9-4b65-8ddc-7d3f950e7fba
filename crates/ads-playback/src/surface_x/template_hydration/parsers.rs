use std::str::FromStr;

use ads_playback_types::surface_x::widget_templates::SurfaceXContainerStylesTemplate;
use fableous::typography::type_ramp::TypeRamp;
use ignx_compositron::{
    color::Color,
    layout::{CrossAxisAlignment, MainAxisAlignment},
    text::{TextVerticalAlignment, TruncationMode},
    DecorationLine,
};

use crate::surface_x::widgets::container_styles::SurfaceXContainerStyles;

pub fn parse_type_ramp(type_str: &str) -> Result<TypeRamp, String> {
    // Get the default value that would be used for invalid inputs
    let default_type_ramp = TypeRamp::from("body");

    // Convert the input string
    let converted_type_ramp = TypeRamp::from(type_str);

    // If they're identical, it might be because the input was invalid
    // and got converted to the default (unless the input actually was "body")
    if converted_type_ramp == default_type_ramp && type_str != "body" {
        Err(format!("Unknown type_ramp: {}", type_str))
    } else {
        Ok(converted_type_ramp)
    }
}

pub fn parse_color_string(color_str: &str) -> Result<Color, String> {
    Color::from_str(color_str).map_err(|e| format!("Invalid color '{}': {}", color_str, e))
}

pub fn parse_text_alignment(alignment_str: &str) -> Result<TextVerticalAlignment, String> {
    match alignment_str {
        "start" => Ok(TextVerticalAlignment::Start),
        "center" => Ok(TextVerticalAlignment::Center),
        "end" => Ok(TextVerticalAlignment::End),
        _ => {
            let error_msg = format!("Unknown text alignment: {}", alignment_str);
            Err(error_msg)
        }
    }
}

pub fn parse_truncation_mode(mode_str: &str) -> Result<TruncationMode, String> {
    match mode_str {
        "head" => Ok(TruncationMode::Head),
        "tail" => Ok(TruncationMode::Tail),
        "clip" => Ok(TruncationMode::Clip),
        _ => {
            let error_msg = format!("Unknown truncation mode: {}", mode_str);
            Err(error_msg)
        }
    }
}

pub fn parse_decoration_line(decoration_str: &str) -> Result<Option<DecorationLine>, String> {
    match decoration_str {
        "none" => Ok(None), // Function returns Option because there's no DecorationLine::NONE
        "underline" => Ok(Some(DecorationLine::UNDERLINE)),
        "line-through" => Ok(Some(DecorationLine::STRIKETHROUGH)),
        _ => {
            let error_msg = format!("Unknown text decoration line: {}", decoration_str);
            Err(error_msg)
        }
    }
}

pub enum WidgetOrientation {
    Horizontal, // For widgets where main axis is horizontal (Like Row)
    Vertical,   // For widgets wrapped in Column where main axis is vertical
}

pub fn parse_container_style(
    style_template: &SurfaceXContainerStylesTemplate,
    widget_orientation: WidgetOrientation,
) -> Result<SurfaceXContainerStyles, String> {
    // A wrapper function that will return the Result
    let parse_style_and_add_error_context = || {
        let background_color = match &style_template.background_color {
            Some(color_str) => Some(parse_color_string(color_str)?),
            None => None,
        };

        let border_color = match &style_template.border_color {
            Some(color_str) => Some(parse_color_string(color_str)?),
            None => None,
        };

        // Determine if widget is horizontally oriented (e.g. Row widget)
        let is_horizontal = matches!(widget_orientation, WidgetOrientation::Horizontal);

        // For horizontal widgets: horizontal → main, vertical → cross
        // For vertical widgets: vertical → main, horizontal → cross
        let (main_alignment_str, cross_alignment_str) = if is_horizontal {
            (
                &style_template.horizontal_alignment,
                &style_template.vertical_alignment,
            )
        } else {
            (
                &style_template.vertical_alignment,
                &style_template.horizontal_alignment,
            )
        };

        // Parse the alignments
        let main_axis_alignment = main_alignment_str
            .as_ref()
            .map(|a| string_to_main_axis_alignment(a))
            .transpose()?;

        let cross_axis_alignment = cross_alignment_str
            .as_ref()
            .map(|a| string_to_cross_axis_alignment(a))
            .transpose()?;

        Ok(SurfaceXContainerStyles {
            background_color,
            border_color,
            border_width: style_template.border_width,
            border_radius: style_template.border_radius,
            opacity: style_template.opacity,

            width: style_template.width,
            height: style_template.height,
            max_width: style_template.max_width,
            min_width: style_template.min_width,
            max_height: style_template.max_height,
            min_height: style_template.min_height,

            // Map start/end to left/right
            padding_top: style_template.padding_top,
            padding_end: style_template.padding_end,
            padding_bottom: style_template.padding_bottom,
            padding_start: style_template.padding_start,

            margin_top: style_template.margin_top,
            margin_end: style_template.margin_end,
            margin_bottom: style_template.margin_bottom,
            margin_start: style_template.margin_start,

            main_axis_alignment,
            cross_axis_alignment,
        })
    };

    parse_style_and_add_error_context().map_err(|e: String| {
        let err_msg = format!("Failed to convert container_style: {}", e);
        err_msg
    })
}

pub fn string_to_main_axis_alignment(alignment: &str) -> Result<MainAxisAlignment, String> {
    match alignment {
        "start" => Ok(MainAxisAlignment::Start),
        "center" => Ok(MainAxisAlignment::Center),
        "end" => Ok(MainAxisAlignment::End),
        _ => {
            let error_msg = format!("Unknown main axis alignment: {}", alignment);
            Err(error_msg)
        }
    }
}

pub fn string_to_cross_axis_alignment(alignment: &str) -> Result<CrossAxisAlignment, String> {
    match alignment {
        "start" => Ok(CrossAxisAlignment::Start),
        "center" => Ok(CrossAxisAlignment::Center),
        "end" => Ok(CrossAxisAlignment::End),
        _ => {
            let error_msg = format!("Unknown cross axis alignment: {}", alignment);
            Err(error_msg)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case("body", Ok(TypeRamp::from("body")))]
    #[case("heading-800", Ok(TypeRamp::from("heading-800")))]
    #[case("label-400", Ok(TypeRamp::from("label-400")))]
    #[case("invalid_type", Err("Unknown type_ramp: invalid_type".to_string()))]
    fn test_parse_type_ramp(#[case] input: &str, #[case] expected: Result<TypeRamp, String>) {
        let result = parse_type_ramp(input);
        match (result.clone(), expected.clone()) {
            (Ok(actual), Ok(expected)) => assert_eq!(actual, expected),
            (Err(actual_err), Err(expected_err)) => assert_eq!(actual_err, expected_err),
            _ => panic!("Result types don't match: {:?} vs {:?}", result, expected),
        }
    }

    #[rstest]
    #[case("#FF0000", Ok(Color::from_str("#FF0000").unwrap()))]
    #[case("#00FF00", Ok(Color::from_str("#00FF00").unwrap()))]
    #[case("rgb(255, 0, 0)", Ok(Color::from_str("rgb(255, 0, 0)").unwrap()))]
    #[case("invalid_color", Err("Invalid color 'invalid_color': failed to parse color".to_string()))]
    fn test_parse_color_string(#[case] input: &str, #[case] expected: Result<Color, String>) {
        let result = parse_color_string(input);
        match (result, expected) {
            (Ok(actual), Ok(expected)) => assert_eq!(actual, expected),
            (Err(actual_err), Err(_)) => {
                // Just check that it's an error, the exact message might vary
                assert!(actual_err.contains("Invalid color"));
            }
            _ => panic!("Result types don't match"),
        }
    }

    #[rstest]
    #[case("start", Ok(TextVerticalAlignment::Start))]
    #[case("center", Ok(TextVerticalAlignment::Center))]
    #[case("end", Ok(TextVerticalAlignment::End))]
    #[case("invalid", Err("Unknown text alignment: invalid".to_string()))]
    fn test_parse_text_alignment(
        #[case] input: &str,
        #[case] expected: Result<TextVerticalAlignment, String>,
    ) {
        let result = parse_text_alignment(input);
        assert_eq!(result, expected);
    }

    #[rstest]
    #[case("head", Ok(TruncationMode::Head))]
    #[case("tail", Ok(TruncationMode::Tail))]
    #[case("clip", Ok(TruncationMode::Clip))]
    #[case("invalid", Err("Unknown truncation mode: invalid".to_string()))]
    fn test_parse_truncation_mode(
        #[case] input: &str,
        #[case] expected: Result<TruncationMode, String>,
    ) {
        let result = parse_truncation_mode(input);
        assert_eq!(result, expected);
    }

    #[test]
    fn test_parse_container_style_horizontal_orientation() {
        let style_template = SurfaceXContainerStylesTemplate {
            horizontal_alignment: Some("start".to_string()),
            vertical_alignment: Some("center".to_string()),
            background_color: Some("#FF0000".to_string()),
            ..Default::default()
        };

        // Test with horizontal orientation
        let result = parse_container_style(&style_template, WidgetOrientation::Horizontal);

        assert!(result.is_ok());
        let styles = result.unwrap();

        assert_eq!(styles.main_axis_alignment, Some(MainAxisAlignment::Start));
        assert_eq!(
            styles.cross_axis_alignment,
            Some(CrossAxisAlignment::Center)
        );
    }

    #[rstest]
    #[case("none", Ok(None))]
    #[case("underline", Ok(Some(DecorationLine::UNDERLINE)))]
    #[case("line-through", Ok(Some(DecorationLine::STRIKETHROUGH)))]
    #[case("invalid", Err("Unknown text decoration line: invalid".to_string()))]
    fn test_parse_decoration_line(
        #[case] input: &str,
        #[case] expected: Result<Option<DecorationLine>, String>,
    ) {
        let result = parse_decoration_line(input);
        assert_eq!(result, expected);
    }

    #[rstest]
    #[case("start", Ok(MainAxisAlignment::Start))]
    #[case("center", Ok(MainAxisAlignment::Center))]
    #[case("end", Ok(MainAxisAlignment::End))]
    #[case("invalid", Err("Unknown main axis alignment: invalid".to_string()))]
    fn test_parse_vertical_alignment(
        #[case] input: &str,
        #[case] expected: Result<MainAxisAlignment, String>,
    ) {
        let result = string_to_main_axis_alignment(input);
        assert_eq!(result, expected);
    }

    #[rstest]
    #[case("start", Ok(CrossAxisAlignment::Start))]
    #[case("center", Ok(CrossAxisAlignment::Center))]
    #[case("end", Ok(CrossAxisAlignment::End))]
    #[case("invalid", Err("Unknown cross axis alignment: invalid".to_string()))]
    fn test_parse_horizontal_alignment(
        #[case] input: &str,
        #[case] expected: Result<CrossAxisAlignment, String>,
    ) {
        let result = string_to_cross_axis_alignment(input);
        assert_eq!(result, expected);
    }

    #[test]
    fn test_parse_container_style_success() {
        let template = SurfaceXContainerStylesTemplate {
            background_color: Some("#FF0000".to_string()),
            border_color: Some("#00FF00".to_string()),
            border_width: Some(1.0),
            border_radius: Some(5.0),
            opacity: Some(0.5),
            width: Some(100.0),
            height: Some(200.0),
            max_width: Some(300.0),
            min_width: Some(50.0),
            max_height: Some(400.0),
            min_height: Some(100.0),
            padding_top: Some(10.0),
            padding_end: Some(20.0),
            padding_bottom: Some(30.0),
            padding_start: Some(40.0),
            margin_top: Some(5.0),
            margin_end: Some(10.0),
            margin_bottom: Some(15.0),
            margin_start: Some(20.0),
            vertical_alignment: Some("center".to_string()),
            horizontal_alignment: Some("start".to_string()),
        };

        let result = parse_container_style(&template, WidgetOrientation::Vertical);
        assert!(result.is_ok());

        let styles = result.unwrap();
        assert_eq!(
            styles.background_color,
            Some(Color::from_str("#FF0000").unwrap())
        );
        assert_eq!(
            styles.border_color,
            Some(Color::from_str("#00FF00").unwrap())
        );
        assert_eq!(styles.border_width, Some(1.0));
        assert_eq!(styles.border_radius, Some(5.0));
        assert_eq!(styles.opacity, Some(0.5));
        assert_eq!(styles.width, Some(100.0));
        assert_eq!(styles.height, Some(200.0));
        assert_eq!(styles.main_axis_alignment, Some(MainAxisAlignment::Center));
        assert_eq!(styles.cross_axis_alignment, Some(CrossAxisAlignment::Start));
    }

    #[test]
    fn test_parse_container_style_invalid_color() {
        let template = SurfaceXContainerStylesTemplate {
            background_color: Some("invalid_color".to_string()),
            border_color: None,
            border_width: None,
            border_radius: None,
            opacity: None,
            width: None,
            height: None,
            max_width: None,
            min_width: None,
            max_height: None,
            min_height: None,
            padding_top: None,
            padding_end: None,
            padding_bottom: None,
            padding_start: None,
            margin_top: None,
            margin_end: None,
            margin_bottom: None,
            margin_start: None,
            vertical_alignment: None,
            horizontal_alignment: None,
        };

        let result = parse_container_style(&template, WidgetOrientation::Vertical);
        assert!(result.is_err());
        let err = result.err().unwrap();
        assert!(err.contains("Failed to convert container_style"));
    }

    #[test]
    fn test_parse_container_style_invalid_alignment() {
        let template = SurfaceXContainerStylesTemplate {
            background_color: None,
            border_color: None,
            border_width: None,
            border_radius: None,
            opacity: None,
            width: None,
            height: None,
            max_width: None,
            min_width: None,
            max_height: None,
            min_height: None,
            padding_top: None,
            padding_end: None,
            padding_bottom: None,
            padding_start: None,
            margin_top: None,
            margin_end: None,
            margin_bottom: None,
            margin_start: None,
            vertical_alignment: Some("invalid".to_string()),
            horizontal_alignment: None,
        };

        let result = parse_container_style(&template, WidgetOrientation::Vertical);
        assert!(result.is_err());
        let err = result.err().unwrap();
        assert!(err.contains("Failed to convert container_style"));
    }
}
