use ads_playback_types::interactive_video_ad::{
    ActionableAdExtension, AdInstanceId, InteractiveVideoAd, TemplateId,
};
use ads_playback_types::sos_request::{
    AdItemRequest, BaseTemplateRequest, EndpointSource, GetSurfaceTemplatesParams,
    IvaTemplateRequest, TemplateIdRequest,
};
use ads_playback_types::surface_x::SurfaceXTemplate;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::{store_value, StoredValue};
use network_parser::core::network_parse_from_str;
use playback_core::PlaybackObserver;
use playback_metadata_provider::types::{ResourceName, ResourceParams, ResourceResponse};
use playback_metadata_provider::{
    use_metadata_controller, MetadataControllerTrait, RequiresMetadataResources,
};
use playback_src::IntrinsicContentConfig;
use playback_ui_kit::ads::{use_ads_store, Ad};
use player::ContentInfo;
use std::cell::RefCell;
use std::collections::{HashMap, HashSet};
use std::num::NonZeroUsize;
use std::rc::Rc;

const LOG_PREFIX: &str = "IvaService";
const DEFAULT_TEMPLATE_CACHE_SIZE: NonZeroUsize = match NonZeroUsize::new(10) {
    Some(n) => n,
    None => panic!("10 should be non-zero"),
};

pub type InteractiveVideoAdsServiceRc = Rc<RefCell<InteractiveVideoAdsService>>;

pub struct InteractiveVideoAdsService {
    metadata_controller: Rc<dyn MetadataControllerTrait>,
    active_ad: Signal<Option<Ad>>,
    set_active_iva: WriteSignal<Option<AdInstanceId>>,
    pub active_iva: ReadSignal<Option<AdInstanceId>>,
    pub template_cache: StoredValue<lru::LruCache<TemplateId, SurfaceXTemplate>>,
    pub timeline_ads: StoredValue<HashSet<InteractiveVideoAd>>,
}

impl InteractiveVideoAdsService {
    fn new(ctx: &AppContext) -> Self {
        let metadata_controller = use_metadata_controller(ctx);
        let active_ad = use_ads_store(ctx.scope()).active_ad;
        let (active_iva, set_active_iva) = create_rw_signal(ctx.scope(), None).split();
        Self {
            metadata_controller,
            active_ad,
            set_active_iva,
            active_iva,
            template_cache: store_value(
                ctx.scope(),
                lru::LruCache::new(DEFAULT_TEMPLATE_CACHE_SIZE),
            ),
            timeline_ads: store_value(ctx.scope(), HashSet::new()),
        }
    }

    pub fn new_rc(ctx: &AppContext) -> InteractiveVideoAdsServiceRc {
        Rc::new(RefCell::new(Self::new(ctx)))
    }

    pub fn request_single_template(
        &self,
        ad_instance_id: &str,
        actionable_ad_extension: &ActionableAdExtension,
    ) {
        let template_id = &actionable_ad_extension.template_id;

        let template_request_parameters = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::ADS_BEHAVIOR,
            adItems: HashMap::from([(
                ad_instance_id.to_string(),
                AdItemRequest {
                    iva: Some(IvaTemplateRequest {
                        base: BaseTemplateRequest { enabled: true },
                        templateId: Some(TemplateIdRequest {
                            id: template_id.id.clone(),
                            majorVersion: template_id.major_version,
                            minorVersion: template_id.minor_version,
                        }),
                    }),
                    adFeedback: None,
                },
            )]),
        };

        self.metadata_controller.request_resources(HashSet::from([(
            ResourceName::SurfaceXTemplates,
            Some(ResourceParams::SurfaceXTemplates(
                template_request_parameters,
            )),
        )]));
    }

    fn handle_resource_response(&mut self, response: &ResourceResponse) {
        let ResourceResponse::SurfaceXTemplates(response) = response else {
            return;
        };

        let templates_response = match response {
            Ok(templates_response) => templates_response,
            Err(error) => {
                log::error!(
                    "{} Failed to fetch templates. cause: {}, message: {}",
                    LOG_PREFIX,
                    error.error_cause,
                    error.message
                );
                return;
            }
        };

        let Some(commons) = templates_response.commons.as_ref() else {
            log::error!("{} Response did not contain a commons section", LOG_PREFIX);
            return;
        };

        let Some(templates_map) = commons.templates.as_ref() else {
            log::error!(
                "{} Commons section did not contain a templates map",
                LOG_PREFIX
            );
            return;
        };

        let number_of_templates = templates_map.len();
        if number_of_templates != 1 {
            log::error!(
                "{} Unexpected number of templates ({}). We only request a single template for now",
                LOG_PREFIX,
                number_of_templates
            );
            return;
        }

        self.template_cache.update_value(|cache| {
            templates_response
                .adItems
                .iter()
                .filter_map(|(ad_instance_id, ad_item_response)| {
                    let ad_template_id = self
                        .timeline_ads
                        .with_value(|ads| ads.get(ad_instance_id).map(|ad| ad.template_id()))?;

                    let iva_template_response = ad_item_response.iva.as_ref()?;
                    let response_template_id =
                        iva_template_response
                            .templateId.as_ref()?
                            .to_identifier_string();
                    let template_contents = templates_map.get(&response_template_id)?;
                    match network_parse_from_str::<SurfaceXTemplate>(template_contents) {
                        Ok(template) => Some((ad_template_id, template)),
                        Err(error) => {
                            log::error!(
                                "{} Failed to deserialize surface x template. ad_instance_id: {}, Template Id: {} Error: {}",
                                LOG_PREFIX,
                                ad_instance_id,
                                response_template_id,
                                error
                            );
                            None
                        }
                    }
                })
                .for_each(|(id, template)| {
                    cache.put(id, template);
                });
        });

        // If the template has arrived after an IVA ad has started, signal that we are now ready
        self.active_ad.with_untracked(|ad| {
            let Some(ad) = ad else {
                return;
            };

            if !self
                .timeline_ads
                .with_value(|ads| ads.contains(&ad.ad_instance_id))
            {
                return;
            }

            self.set_active_iva.set(Some(ad.ad_instance_id.clone()));
        });
    }

    fn reset_ads_state(&mut self) {
        self.timeline_ads.update_value(|ads| ads.clear());
        self.set_active_iva.set(None);
    }
}

impl PlaybackObserver for InteractiveVideoAdsService {
    fn on_timeline_ended(&mut self) {
        self.reset_ads_state();
    }

    fn on_content_info(&mut self, content_info: &ContentInfo) {
        let ContentInfo::Advertisement(ad_metadata, _) = content_info else {
            self.set_active_iva.set(None);
            return;
        };

        let ad_instance_id = &ad_metadata.ad_instance_id;

        self.timeline_ads.with_value(|ads| {
            let Some(ad) = ads.get(ad_instance_id) else {
                self.set_active_iva.set(None);
                return;
            };

            // Signal that the IVA is ready if we have the template, otherwise request it and do it async
            if self
                .template_cache
                .with_value(|cache| cache.contains(&ad.template_id()))
            {
                self.set_active_iva.set(Some(ad_instance_id.clone()));
            } else {
                self.request_single_template(ad_instance_id, &ad.extension);
            }
        });
    }
}

impl RequiresMetadataResources for InteractiveVideoAdsService {
    fn get_required_resources_on_demand(&self, _: IntrinsicContentConfig) -> HashSet<ResourceName> {
        HashSet::from([ResourceName::SurfaceXTemplates])
    }

    fn on_resource(&mut self, resource_response: &ResourceResponse) {
        self.handle_resource_response(resource_response);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_data::{create_test_atc_interactive_video_ad, TestAdType};
    use crate::test_utils::setup_mock_contexts;
    use ads_playback_types::sos_response::{
        AdItemResponseCommons, GetSurfaceTemplatesData, LayoutPosition,
    };
    use ads_playback_types::test_data::MINIMAL_BASELINE_IVA_TEMPLATE;
    use ads_playback_types::test_utils::{
        AdItemResponseBuilder, SosTemplatesResponseBuilder, TemplateIdResponseBuilder,
    };
    use ignx_compositron::app::launch_only_app_context;
    use playback_core::{
        provide_observable, try_update_observable, PlaybackEvent, PlaybackObservable,
    };
    use playback_metadata_provider::types::ResourceError;
    use playback_ui_kit::ads::provide_ads_store;
    use player::test_utils::{
        AdMetadataBuilder, ContentInfoBuilder, TimeDataBuildable, TimelineTestUtils,
    };
    use player::{PlayerEvent, TimeData, Timeline};
    use rust_features::MockRustFeaturesBuilder;

    fn setup_iva_service(ctx: &AppContext) -> InteractiveVideoAdsService {
        setup_mock_contexts(ctx);
        InteractiveVideoAdsService::new(ctx)
    }

    #[test]
    fn test_handle_resource_response_successful_with_valid_templates() {
        launch_only_app_context(|ctx| {
            let ad_instance_id = "test_ad_1";

            let interactive_video_ad =
                create_test_atc_interactive_video_ad(TestAdType::VodLinear, ad_instance_id);

            let mut iva_service = setup_iva_service(&ctx);
            iva_service.timeline_ads.update_value(|ads| {
                ads.insert(interactive_video_ad);
            });

            let template_id = TemplateIdResponseBuilder::default().build();

            let sos_response = SosTemplatesResponseBuilder::default()
                .add_ad_item(
                    ad_instance_id,
                    AdItemResponseBuilder::default()
                        .with_template_id(template_id.clone())
                        .with_enabled_iva(LayoutPosition::LEFT)
                        .build(),
                )
                .add_template(
                    template_id.to_identifier_string(),
                    MINIMAL_BASELINE_IVA_TEMPLATE,
                )
                .build();

            iva_service.on_resource(&ResourceResponse::SurfaceXTemplates(Ok(sos_response)));

            assert_eq!(
                iva_service.template_cache.with_value(|cache| cache.len()),
                1
            );
        });
    }

    #[test]
    fn test_handle_resource_response_with_missing_ad() {
        launch_only_app_context(|ctx| {
            let mut iva_service = setup_iva_service(&ctx);

            let template_id = TemplateIdResponseBuilder::default().build();

            let sos_response = SosTemplatesResponseBuilder::default()
                .add_ad_item(
                    "nonexistent_ad",
                    AdItemResponseBuilder::default()
                        .with_template_id(template_id.clone())
                        .with_enabled_iva(LayoutPosition::LEFT)
                        .build(),
                )
                .add_template(
                    template_id.to_identifier_string(),
                    MINIMAL_BASELINE_IVA_TEMPLATE,
                )
                .build();

            iva_service.on_resource(&ResourceResponse::SurfaceXTemplates(Ok(sos_response)));

            assert!(iva_service
                .template_cache
                .with_value(|cache| cache.is_empty()));
        });
    }

    #[test]
    fn test_required_resources() {
        launch_only_app_context(|ctx| {
            let iva_service = setup_iva_service(&ctx);
            let required_resources = iva_service
                .get_required_resources_on_demand(IntrinsicContentConfig::builder().build());
            assert_eq!(required_resources.len(), 1);
            assert!(required_resources.contains(&ResourceName::SurfaceXTemplates));
        });
    }

    #[test]
    fn test_handle_resource_response_non_surface_x_templates_response() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new().build_into_context(ctx.scope());
            let mut iva_service = setup_iva_service(&ctx);
            iva_service.on_resource(&ResourceResponse::CatalogMetadata(Ok(None)));
            assert!(iva_service
                .template_cache
                .with_value(|cache| cache.is_empty()));
        });
    }

    #[test]
    fn test_handle_resource_response_with_error() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new().build_into_context(ctx.scope());
            let mut iva_service = setup_iva_service(&ctx);
            let response = ResourceResponse::SurfaceXTemplates(Err(ResourceError {
                error_cause: "NETWORK_ERROR".to_string(),
                message: "Failed to fetch templates".to_string(),
            }));
            iva_service.on_resource(&response);
            assert!(iva_service
                .template_cache
                .with_value(|cache| cache.is_empty()));
        });
    }

    #[test]
    fn test_handle_resource_response_missing_commons() {
        launch_only_app_context(|ctx| {
            let mut iva_service = setup_iva_service(&ctx);

            let sos_response = GetSurfaceTemplatesData {
                commons: None,
                adItems: HashMap::new(),
            };

            iva_service.on_resource(&ResourceResponse::SurfaceXTemplates(Ok(sos_response)));

            assert!(iva_service
                .template_cache
                .with_value(|cache| cache.is_empty()));
        });
    }

    #[test]
    fn test_handle_resource_response_missing_templates_map() {
        launch_only_app_context(|ctx| {
            let mut iva_service = setup_iva_service(&ctx);

            let sos_response = GetSurfaceTemplatesData {
                commons: Some(AdItemResponseCommons {
                    templates: None,
                    localizedStrings: Some(HashMap::new()),
                    ext: Some(HashMap::new()),
                }),
                adItems: HashMap::new(),
            };

            iva_service.on_resource(&ResourceResponse::SurfaceXTemplates(Ok(sos_response)));

            assert!(iva_service
                .template_cache
                .with_value(|cache| cache.is_empty()));
        });
    }

    #[test]
    fn test_handle_resource_response_multiple_templates() {
        launch_only_app_context(|ctx| {
            let mut iva_service = setup_iva_service(&ctx);

            let template_id1 = TemplateIdResponseBuilder::default().build();
            let template_id2 = TemplateIdResponseBuilder::default()
                .with_id("template2")
                .build();

            let sos_response = SosTemplatesResponseBuilder::default()
                .add_template(
                    template_id1.to_identifier_string(),
                    MINIMAL_BASELINE_IVA_TEMPLATE,
                )
                .add_template(
                    template_id2.to_identifier_string(),
                    MINIMAL_BASELINE_IVA_TEMPLATE,
                )
                .build();

            iva_service.on_resource(&ResourceResponse::SurfaceXTemplates(Ok(sos_response)));

            assert!(iva_service
                .template_cache
                .with_value(|cache| cache.is_empty()));
        });
    }

    #[test]
    fn test_handle_resource_response_invalid_template_content() {
        launch_only_app_context(|ctx| {
            let ad_instance_id = "test_ad_1";
            let interactive_video_ad =
                create_test_atc_interactive_video_ad(TestAdType::VodLinear, ad_instance_id);

            let mut iva_service = setup_iva_service(&ctx);
            iva_service.timeline_ads.update_value(|ads| {
                ads.insert(interactive_video_ad);
            });

            let template_id = TemplateIdResponseBuilder::default().build();

            let sos_response = SosTemplatesResponseBuilder::default()
                .add_ad_item(
                    ad_instance_id,
                    AdItemResponseBuilder::default()
                        .with_template_id(template_id.clone())
                        .with_enabled_iva(LayoutPosition::LEFT)
                        .build(),
                )
                .add_template(template_id.to_identifier_string(), "invalid json content")
                .build();

            iva_service.on_resource(&ResourceResponse::SurfaceXTemplates(Ok(sos_response)));

            assert!(iva_service
                .template_cache
                .with_value(|cache| cache.is_empty()));
        });
    }

    #[test]
    fn test_handle_resource_response_sets_active_iva_when_template_arrives_after_ad_start() {
        launch_only_app_context(|ctx| {
            provide_observable(ctx.scope(), PlaybackObservable::new());
            provide_ads_store(ctx.scope());

            // Load the timeline with ads
            let timeline = Timeline::samples().ads_and_aux();
            try_update_observable(ctx.scope(), |v| {
                v.dispatch(&PlaybackEvent::Player(&PlayerEvent::TimelineChange(
                    timeline.clone(),
                )));
            });

            // Set current timeline item to 0 (first ad in the first ad pod)
            try_update_observable(ctx.scope(), |v| {
                v.dispatch(&PlaybackEvent::Player(&PlayerEvent::TimeDataChange(
                    TimeData::builder().current_timeline_item_index(0).build(),
                )));
            });

            let ad_instance_id = "default_id";
            let interactive_video_ad =
                create_test_atc_interactive_video_ad(TestAdType::VodLinear, ad_instance_id);

            let mut iva_service = setup_iva_service(&ctx);

            iva_service.timeline_ads.update_value(|ads| {
                ads.insert(interactive_video_ad);
            });

            let template_id = TemplateIdResponseBuilder::default().build();

            let sos_response = SosTemplatesResponseBuilder::default()
                .add_ad_item(
                    ad_instance_id,
                    AdItemResponseBuilder::default()
                        .with_template_id(template_id.clone())
                        .with_enabled_iva(LayoutPosition::LEFT)
                        .build(),
                )
                .add_template(
                    template_id.to_identifier_string(),
                    MINIMAL_BASELINE_IVA_TEMPLATE,
                )
                .build();

            iva_service.on_resource(&ResourceResponse::SurfaceXTemplates(Ok(sos_response)));

            assert_eq!(
                iva_service.active_iva.get_untracked(),
                Some(ad_instance_id.to_string())
            );
        });
    }

    #[test]
    fn test_on_content_info_with_known_ad_sets_iva_if_template_already_exists() {
        launch_only_app_context(|ctx| {
            let ad_instance_id = "test_ad_1";

            let interactive_video_ad =
                create_test_atc_interactive_video_ad(TestAdType::VodLinear, ad_instance_id);

            let ad_extension_template_id = interactive_video_ad.template_id();

            let mut iva_service = setup_iva_service(&ctx);

            iva_service.timeline_ads.update_value(|ads| {
                ads.insert(interactive_video_ad);
            });

            iva_service.template_cache.update_value(|cache| {
                cache.put(
                    ad_extension_template_id,
                    network_parse_from_str::<SurfaceXTemplate>(MINIMAL_BASELINE_IVA_TEMPLATE)
                        .expect("Failed to deserialize payload"),
                );
            });

            let content_info = ContentInfoBuilder::default()
                .ad_metadata(
                    AdMetadataBuilder::default()
                        .ad_instance_id(ad_instance_id)
                        .build(),
                )
                .build();

            iva_service.on_content_info(&content_info);

            assert_eq!(
                iva_service.active_iva.get_untracked(),
                Some(ad_instance_id.to_string())
            );
        });
    }

    #[test]
    fn test_on_content_info_with_known_ad_does_not_set_iva_if_template_does_not_exist() {
        launch_only_app_context(|ctx| {
            let ad_instance_id = "test_ad_1";

            let interactive_video_ad =
                create_test_atc_interactive_video_ad(TestAdType::VodLinear, ad_instance_id);

            let mut iva_service = setup_iva_service(&ctx);

            iva_service.timeline_ads.update_value(|ads| {
                ads.insert(interactive_video_ad);
            });

            let content_info = ContentInfoBuilder::default()
                .ad_metadata(
                    AdMetadataBuilder::default()
                        .ad_instance_id(ad_instance_id)
                        .build(),
                )
                .build();

            iva_service.on_content_info(&content_info);

            assert_eq!(iva_service.active_iva.get_untracked(), None);
        });
    }

    #[test]

    fn test_on_content_info_with_non_iva_ad_reset_active_iva() {
        launch_only_app_context(|ctx| {
            let mut iva_service = setup_iva_service(&ctx);

            let content_info = ContentInfoBuilder::default()
                .ad_metadata(
                    AdMetadataBuilder::default()
                        .ad_instance_id("unknown_ad")
                        .build(),
                )
                .build();

            iva_service
                .set_active_iva
                .set(Some("some_iva_ad".to_string()));

            iva_service.on_content_info(&content_info);

            assert_eq!(iva_service.active_iva.get_untracked(), None);
        });
    }

    #[test]
    fn test_on_content_info_non_ad_content_reset_active_iva() {
        launch_only_app_context(|ctx| {
            let mut iva_service = setup_iva_service(&ctx);

            iva_service.set_active_iva.set(Some("iva_ad_1".to_string()));

            let content_info = ContentInfoBuilder::default()
                .feature_metadata(Some("test_title".to_string()))
                .build();

            iva_service.on_content_info(&content_info);

            assert_eq!(iva_service.active_iva.get_untracked(), None);
        });
    }

    #[test]
    fn test_on_timeline_ended() {
        launch_only_app_context(|ctx| {
            let ad_instance_id = "test_ad_1";
            let interactive_video_ad =
                create_test_atc_interactive_video_ad(TestAdType::VodLinear, ad_instance_id);

            let mut iva_service = setup_iva_service(&ctx);

            // Set up initial state with ads and active IVA
            iva_service.timeline_ads.update_value(|ads| {
                ads.insert(interactive_video_ad);
            });
            iva_service
                .set_active_iva
                .set(Some(ad_instance_id.to_string()));

            // Verify initial state
            assert!(!iva_service.timeline_ads.with_value(|ads| ads.is_empty()));
            assert_eq!(
                iva_service.active_iva.get_untracked(),
                Some(ad_instance_id.to_string())
            );

            // Call on_timeline_ended
            iva_service.on_timeline_ended();

            // Verify state is reset
            assert!(iva_service.timeline_ads.with_value(|ads| ads.is_empty()));
            assert_eq!(iva_service.active_iva.get_untracked(), None);
        });
    }
}
