use ignx_compositron::compose;
use ignx_compositron::prelude::*;
use playback_core::PlaybackObserver;
use playback_feature_manager::{
    Feature, FeatureControllers, FeatureName, OverlayPanelPosition, Panel, PanelGroup, PanelName,
};
use playback_metadata_provider::types::ResourceResponse;
use playback_metadata_provider::RequiresMetadataResources;
use playback_ui_kit::playback_controller::PlaybackProxy;

#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;

use crate::features::interactive_video_ads_service::InteractiveVideoAdsServiceRc;
#[cfg_attr(test, double)]
use crate::metrics::reporter::Reporter;
#[cfg(test)]
use mockall_double::double;

const LOG_PREFIX: &str = "[InteractiveVideoAdsLiveFeature]";

pub struct InteractiveVideoAdsLiveFeature {
    ctx: AppContext,
    controllers: FeatureControllers,
}

impl InteractiveVideoAdsLiveFeature {
    pub fn new(ctx: AppContext, controllers: FeatureControllers) -> Self {
        Self { ctx, controllers }
    }
}

impl Feature for InteractiveVideoAdsLiveFeature {
    fn feature_name(&self) -> FeatureName {
        FeatureName::InteractiveVideoAdsLive
    }

    fn initialize(&mut self) {
        if let Err(error) = self.controllers.panels_controller.register(Panel {
            panel_name: PanelName::InteractiveVideoAdsLive,
            group: PanelGroup::Overlay(OverlayPanelPosition::Full),
            feature_name: FeatureName::InteractiveVideoAdsLive,
            auto_hide_timeout: None,
            component: Box::new({
                move |ctx| {
                    compose! {
                        Label(text: "IVA Live").into_any()
                    }
                }
            }),
            tab: None,
            on_hidden: None,
            on_shown: None,
        }) {
            log::error!("{} failed to register panel: {:?}", LOG_PREFIX, error);
        }
    }

    fn is_feature_enabled(&self) -> bool {
        let rust_features = try_use_rust_features(self.ctx.scope());
        if let Some(rust_features) = rust_features {
            rust_features.is_rust_playback_iva_live_enabled()
        } else {
            log::error!("Could not find rust features in context. IVA Live feature is disabled");
            false
        }
    }

    fn reset(&mut self) {
        self.controllers
            .panels_controller
            .deregister(&PanelName::InteractiveVideoAdsLive);
    }
}

impl PlaybackObserver for InteractiveVideoAdsLiveFeature {}

impl PlaybackProxy for InteractiveVideoAdsLiveFeature {}

impl RequiresMetadataResources for InteractiveVideoAdsLiveFeature {
    fn on_resource(&mut self, _: &ResourceResponse) {}
}

pub fn interactive_video_ads_live_feature_factory(
    ctx: AppContext,
    controllers: FeatureControllers,
    _reporter: Reporter,
    _interactive_video_ads_service: InteractiveVideoAdsServiceRc,
) -> InteractiveVideoAdsLiveFeature {
    InteractiveVideoAdsLiveFeature::new(ctx, controllers)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::features::interactive_video_ads_service::InteractiveVideoAdsService;
    use crate::test_utils::*;
    use ignx_compositron::app::launch_only_app_context;
    use playback_src::IntrinsicContentConfig;
    use rust_features::MockRustFeaturesBuilder;

    fn setup_feature(ctx: &AppContext) -> InteractiveVideoAdsLiveFeature {
        let controllers = setup_feature_controllers_with_mock_contexts(ctx);
        let reporter = Reporter::default();
        let interactive_video_ads_service = InteractiveVideoAdsService::new_rc(ctx);
        interactive_video_ads_live_feature_factory(
            ctx.clone(),
            controllers,
            reporter,
            interactive_video_ads_service,
        )
    }

    #[test]
    fn test_feature_factory_creates_feature() {
        launch_only_app_context(|ctx| {
            let feature = setup_feature(&ctx);
            assert_eq!(feature.feature_name(), FeatureName::InteractiveVideoAdsLive);
        });
    }

    #[test]
    fn test_feature_name_returns_iva_live() {
        launch_only_app_context(|ctx| {
            let feature = setup_feature(&ctx);
            assert_eq!(feature.feature_name(), FeatureName::InteractiveVideoAdsLive);
        });
    }

    #[test]
    fn test_is_feature_enabled_returns_true_when_rust_feature_enabled() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new()
                .set_rust_playback_iva_live_enabled(true)
                .build_into_context(ctx.scope());
            let feature = setup_feature(&ctx);
            assert!(feature.is_feature_enabled());
        });
    }

    #[test]
    fn test_is_feature_enabled_returns_false_when_rust_feature_disabled() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new()
                .set_rust_playback_iva_live_enabled(false)
                .build_into_context(ctx.scope());
            let feature = setup_feature(&ctx);
            assert!(!feature.is_feature_enabled());
        });
    }

    #[test]
    fn test_lifecycle_methods_execute_without_error() {
        launch_only_app_context(|ctx| {
            // No explicit assert - we're verifying the functions execute without error
            let mut feature = setup_feature(&ctx);
            feature.initialize();
            feature.reset();
        });
    }

    #[test]
    fn test_required_resources() {
        launch_only_app_context(|ctx| {
            let feature = setup_feature(&ctx);
            let required_resources =
                feature.get_required_resources_on_demand(IntrinsicContentConfig::builder().build());
            assert!(required_resources.is_empty());
        });
    }
}
