#![allow(clippy::unused_self)]

use ignx_compositron::{prelude::*, reactive::StoredValue};
use mockall::automock;
use mockall_double::double;

use playback_feature_manager::{Feature, FeatureControllers, FeatureName};

#[double]
use playback_feature_manager::FeatureManager;

#[double]
use playback_metadata_provider::MetadataProvider;

#[cfg_attr(test, double)]
use crate::metrics::reporter::Reporter;

use super::interactive_video_ads_live::interactive_video_ads_live_feature_factory;
use super::interactive_video_ads_vod::interactive_video_ads_vod_feature_factory;
use super::pause_ads::pause_ads_feature_factory;

use crate::features::interactive_video_ads_service::{
    InteractiveVideoAdsService, InteractiveVideoAdsServiceRc,
};
#[cfg_attr(test, double)]
use cross_app_events::metric_emitter::MetricEmitter;
use playback_core::try_update_observable;

pub struct AdvertisingFeaturesCreator {}

#[automock]
impl AdvertisingFeaturesCreator {
    pub fn new(_ctx: &AppContext) -> Self {
        Self {}
    }

    pub fn register_features(
        &self,
        ctx: &AppContext,
        feature_manager: &mut FeatureManager,
        metadata_provider: StoredValue<Box<MetadataProvider>>,
    ) {
        let interactive_video_ads_service = InteractiveVideoAdsService::new_rc(ctx);

        create_advertising_feature(
            FeatureName::PauseAds,
            pause_ads_feature_factory,
            feature_manager,
            metadata_provider,
            &interactive_video_ads_service,
        );

        create_advertising_feature(
            FeatureName::InteractiveVideoAdsVod,
            interactive_video_ads_vod_feature_factory,
            feature_manager,
            metadata_provider,
            &interactive_video_ads_service,
        );

        create_advertising_feature(
            FeatureName::InteractiveVideoAdsLive,
            interactive_video_ads_live_feature_factory,
            feature_manager,
            metadata_provider,
            &interactive_video_ads_service,
        );

        metadata_provider.try_update_value(|m| m.attach(interactive_video_ads_service.clone()));

        try_update_observable(ctx.scope(), |observable| {
            observable.attach_rc(interactive_video_ads_service.clone())
        });
    }
}

fn create_advertising_feature<F, FF>(
    name: FeatureName,
    feature_factory: FF,
    feature_manager: &mut FeatureManager,
    metadata_provider: StoredValue<Box<MetadataProvider>>,
    interactive_video_ads_service: &InteractiveVideoAdsServiceRc,
) where
    F: Feature + 'static,
    FF: Fn(AppContext, FeatureControllers, Reporter, InteractiveVideoAdsServiceRc) -> F + 'static,
{
    let interactive_video_ads_service = interactive_video_ads_service.clone();
    if let Err(error) =
        feature_manager.register(metadata_provider, name, move |ctx, controllers| {
            feature_factory(
                ctx.clone(),
                controllers,
                Reporter::new(
                    MetricEmitter::new(ctx.scope(), "Playback Page".to_string()),
                    vec![],
                ),
                interactive_video_ads_service.clone(),
            )
        })
    {
        log::error!(
            "[AdvertisingFeaturesCreator] register_features: Failed to register feature {:?}: {:?}",
            name,
            error
        );
    }
}

#[cfg(test)]
mod tests {
    use crate::features::interactive_video_ads_live::InteractiveVideoAdsLiveFeature;
    use crate::features::interactive_video_ads_vod::InteractiveVideoAdsVodFeature;
    use crate::features::pause_ads::PauseAdsFeature;

    use super::*;
    use crate::test_utils::setup_mock_contexts;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::reactive::store_value;
    use playback_feature_manager::FeatureManagerError;

    fn setup(ctx: &AppContext) -> (FeatureManager, StoredValue<Box<MetadataProvider>>) {
        setup_mock_contexts(ctx);

        let mut metadata_provider = MetadataProvider::default();

        metadata_provider.expect_attach().times(1).return_const(());

        (
            FeatureManager::default(),
            store_value(ctx.scope(), Box::new(metadata_provider)),
        )
    }

    #[test]
    fn test_register_features() {
        launch_only_app_context(|ctx| {
            let (mut feature_manager, metadata_provider) = setup(&ctx);

            // Expect PauseAds feature registration
            feature_manager
                .expect_register::<PauseAdsFeature>()
                .withf(|_, name, _| *name == FeatureName::PauseAds)
                .times(1)
                .return_const(Ok(()));

            // Expect IVA VOD feature registration
            feature_manager
                .expect_register::<InteractiveVideoAdsVodFeature>()
                .withf(|_, name, _| *name == FeatureName::InteractiveVideoAdsVod)
                .times(1)
                .return_const(Ok(()));

            // Expect IVA Live feature registration
            feature_manager
                .expect_register::<InteractiveVideoAdsLiveFeature>()
                .withf(|_, name, _| *name == FeatureName::InteractiveVideoAdsLive)
                .times(1)
                .return_const(Ok(()));

            let creator = AdvertisingFeaturesCreator::new(&ctx);

            creator.register_features(&ctx, &mut feature_manager, metadata_provider);
        });
    }

    #[test]
    fn test_register_features_handles_errors() {
        launch_only_app_context(|ctx| {
            let (mut feature_manager, metadata_provider) = setup(&ctx);

            // All features should handle errors gracefully
            feature_manager
                .expect_register::<PauseAdsFeature>()
                .times(1)
                .return_const(Err(FeatureManagerError::DuplicateFeatureName));

            feature_manager
                .expect_register::<InteractiveVideoAdsVodFeature>()
                .times(1)
                .return_const(Err(FeatureManagerError::DuplicateFeatureName));

            feature_manager
                .expect_register::<InteractiveVideoAdsLiveFeature>()
                .times(1)
                .return_const(Err(FeatureManagerError::DuplicateFeatureName));

            let creator = AdvertisingFeaturesCreator::new(&ctx);

            creator.register_features(&ctx, &mut feature_manager, metadata_provider);

            // No explicit assert - we're verifying the function handles errors gracefully
        });
    }
}
