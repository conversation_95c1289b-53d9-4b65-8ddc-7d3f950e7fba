use crate::metrics::metric_name::MetricName;
use ignx_compositron::compose;
use ignx_compositron::prelude::*;
use ignx_compositron::task::TaskID;
use ignx_compositron::time::Instant;
use playback_core::PlaybackObserver;
use playback_feature_manager::{
    Feature, FeatureControllers, FeatureName, OverlayPanelPosition, Panel, PanelGroup, PanelName,
};
use playback_metadata_provider::types::ResourceName;
use playback_metadata_provider::types::ResourceResponse;
use playback_metadata_provider::RequiresMetadataResources;
use playback_src::IntrinsicContentConfig;
use playback_ui_kit::playback_controller::PlaybackProxy;
use player::{ContentInfo, PlaybackState};

#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;

#[cfg(test)]
use mockall_double::double;

#[cfg_attr(test, double)]
use crate::metrics::reporter::Reporter;

use crate::features::interactive_video_ads_service::InteractiveVideoAdsServiceRc;
use std::collections::HashSet;
use std::time::Duration;

const DEFAULT_PAUSE_ADS_FETCH_DELAY: Duration = Duration::from_millis(5000);
const DEFAULT_PAUSE_ADS_SHOW_DELAY: Duration = Duration::from_millis(5000);
const LOG_PREFIX: &str = "[PauseAdsFeature]";

pub struct PauseAdsFeature {
    ctx: AppContext,
    controllers: FeatureControllers,
    regolith_url: Option<String>,
    fetch_ad_delay: Duration,
    fetch_ad_task_id: Option<TaskID>,
    show_ad_delay: Duration,
    show_ad_task_id: Option<TaskID>,
    min_duration_between_fetch_in_milliseconds: Option<Duration>,
    last_shown_ad_time: Option<Instant>,
}

impl PauseAdsFeature {
    // Todo: get config from ACM and set fetch_ad_delay + show_ad_delay properly
    pub fn new(ctx: AppContext, controllers: FeatureControllers) -> Self {
        Self {
            ctx,
            controllers,
            regolith_url: None,
            fetch_ad_delay: DEFAULT_PAUSE_ADS_FETCH_DELAY,
            fetch_ad_task_id: None,
            show_ad_delay: DEFAULT_PAUSE_ADS_SHOW_DELAY,
            show_ad_task_id: None,
            min_duration_between_fetch_in_milliseconds: None,
            last_shown_ad_time: None,
        }
    }

    fn schedule_fetching_ad(&mut self) {
        self.cancel_timers();
        self.fetch_ad_task_id = Some(self.ctx.schedule_task(
            Instant::now() + self.fetch_ad_delay,
            move || {
                // Todo: Once we make changes to the Metadata Provider
            },
        ))
    }

    fn clear_ad(&mut self) {
        self.cancel_timers();
    }

    // Todo: Reset the fetch / show durations from ACM as well once we able to do so.
    fn reset_state(&mut self) {
        self.cancel_timers();
        self.regolith_url = None;
        self.fetch_ad_task_id = None;
        self.show_ad_task_id = None;
        self.min_duration_between_fetch_in_milliseconds = None;
        self.last_shown_ad_time = None;
    }

    fn cancel_timers(&mut self) {
        if let Some(task_id) = self.fetch_ad_task_id {
            metric!(MetricName::PauseAdsCancelBeforeShown.as_str(), 1);
            self.ctx.cancel_task(task_id);
            self.fetch_ad_task_id = None;
        }
        if let Some(task_id) = self.show_ad_task_id {
            self.ctx.cancel_task(task_id);
            self.show_ad_task_id = None;
        }
    }

    fn has_enough_time_passed_since_last_pause_ad(&self) -> bool {
        let Some(last_shown) = self.last_shown_ad_time else {
            return true;
        };

        let Some(min_duration_between_fetch_in_milliseconds) =
            self.min_duration_between_fetch_in_milliseconds
        else {
            return true;
        };

        let elapsed_time = Instant::now() - last_shown;

        elapsed_time > min_duration_between_fetch_in_milliseconds
    }
}

impl Feature for PauseAdsFeature {
    fn feature_name(&self) -> FeatureName {
        FeatureName::PauseAds
    }

    fn initialize(&mut self) {
        self.reset_state();
        if let Err(error) = self.controllers.panels_controller.register(Panel {
            panel_name: PanelName::PauseAds,
            group: PanelGroup::Overlay(OverlayPanelPosition::Full),
            feature_name: FeatureName::PauseAds,
            auto_hide_timeout: None,
            component: Box::new({
                move |ctx| {
                    compose! {
                        Label(text: "Empty").into_any()
                    }
                }
            }),
            tab: None,
            on_hidden: None,
            on_shown: None,
        }) {
            log::error!("{} failed to register panel: {:?}", LOG_PREFIX, error);
        }
    }

    fn is_feature_enabled(&self) -> bool {
        let rust_features = try_use_rust_features(self.ctx.scope());
        if let Some(rust_features) = rust_features {
            rust_features.is_rust_playback_pause_ads_enabled()
        } else {
            log::error!("Could not find rust features in context. Pause ads feature is disabled");
            false
        }
    }

    fn reset(&mut self) {
        self.controllers
            .panels_controller
            .deregister(&PanelName::PauseAds);
        self.reset_state();
    }
}

impl PlaybackObserver for PauseAdsFeature {
    // Todo: Discuss with LRPX about the alternative to using this
    fn on_playback_state_change(&mut self, playback_state: &PlaybackState) {
        match playback_state {
            PlaybackState::Playing => {
                self.clear_ad();
            }
            PlaybackState::Paused => {
                // Todo: Add more unit tests when pause ads rust feature is added.
                // We are not able to test the code in this block because is_feature_enabled is
                // hardcoded to false for now.
                if !self.is_feature_enabled() {
                    log::info!(
                        "{} Not fetching pause ad because pause ads has been disabled",
                        LOG_PREFIX
                    );
                    return;
                }

                if self.regolith_url.is_none() {
                    log::info!(
                        "{} not fetching pause ad because regolith endpoint is none",
                        LOG_PREFIX
                    );
                    return;
                }

                if !self.has_enough_time_passed_since_last_pause_ad() {
                    log::info!("{} not fetching pause ad because not enough time has passed since last time a pause ad was shown", LOG_PREFIX);
                    return;
                }

                self.schedule_fetching_ad();
            }
        }
    }

    fn on_content_info(&mut self, content_info: &ContentInfo) {
        let content_info_regolith_url = match content_info {
            ContentInfo::Feature(_, content_info_data)
            | ContentInfo::Advertisement(_, content_info_data)
            | ContentInfo::Recap(_, content_info_data)
            | ContentInfo::Auxiliary(_, content_info_data)
            | ContentInfo::AdTransition(_, content_info_data) => content_info_data
                .pause_behavior
                .as_ref()
                .map(|pause_behaviour| pause_behaviour.ad_request_url.clone()),
        };

        let is_both_regolith_urls_none =
            self.regolith_url.is_none() && content_info_regolith_url.is_none();
        let is_both_regolith_urls_some =
            self.regolith_url.is_some() && content_info_regolith_url.is_some();

        let should_not_replace_regolith_url =
            is_both_regolith_urls_none || is_both_regolith_urls_some;
        if should_not_replace_regolith_url {
            return;
        }

        self.regolith_url = content_info_regolith_url;
    }
}

impl PlaybackProxy for PauseAdsFeature {}

impl RequiresMetadataResources for PauseAdsFeature {
    fn get_required_resources_on_demand(&self, _: IntrinsicContentConfig) -> HashSet<ResourceName> {
        HashSet::from([ResourceName::PauseAds])
    }

    fn on_resource(&mut self, _: &ResourceResponse) {}
}

pub fn pause_ads_feature_factory(
    ctx: AppContext,
    controllers: FeatureControllers,
    _reporter: Reporter,
    _interactive_video_ads_service: InteractiveVideoAdsServiceRc,
) -> PauseAdsFeature {
    PauseAdsFeature::new(ctx, controllers)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::features::interactive_video_ads_service::InteractiveVideoAdsService;
    use crate::test_utils::*;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::time::MockClock;
    use playback_core::PlaybackPersistedEvents;
    use player::test_utils::*;
    use player::PauseBehavior;
    use rstest::*;
    use rust_features::MockRustFeaturesBuilder;

    fn setup_feature(ctx: &AppContext) -> PauseAdsFeature {
        let controllers = setup_feature_controllers_with_mock_contexts(ctx);
        let reporter = Reporter::default();
        let interactive_video_ads_service = InteractiveVideoAdsService::new_rc(ctx);
        pause_ads_feature_factory(
            ctx.clone(),
            controllers,
            reporter,
            interactive_video_ads_service,
        )
    }

    #[test]
    fn test_feature_factory_creates_feature() {
        launch_only_app_context(|ctx| {
            let feature = setup_feature(&ctx);
            assert_eq!(feature.feature_name(), FeatureName::PauseAds);
        });
    }

    #[test]
    fn test_feature_name_returns_pause_ads() {
        launch_only_app_context(|ctx| {
            let feature = setup_feature(&ctx);
            assert_eq!(feature.feature_name(), FeatureName::PauseAds);
        });
    }

    #[test]
    fn test_is_feature_enabled_returns_true_when_rust_feature_enabled() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_rust_playback_pause_ads_enabled(true)
                .build_into_context(ctx.scope());
            let feature = setup_feature(&ctx);
            assert!(feature.is_feature_enabled());
        });
    }

    #[test]
    fn test_is_feature_enabled_returns_false_when_rust_feature_disabled() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_rust_playback_pause_ads_enabled(false)
                .build_into_context(ctx.scope());
            let feature = setup_feature(&ctx);
            assert!(!feature.is_feature_enabled());
        });
    }

    #[test]
    fn test_lifecycle_methods_execute_without_error() {
        launch_only_app_context(|ctx| {
            // No explicit assert - we're verifying the functions handles executes without error
            let mut feature = setup_feature(&ctx);
            feature.initialize();
            feature.reset();
        });
    }

    #[test]
    fn test_playback_observer_methods_execute_without_error() {
        launch_only_app_context(|ctx| {
            // No explicit assert - we're verifying the functions handles executes without error
            let mut feature = setup_feature(&ctx);
            let events = PlaybackPersistedEvents::default();
            feature.on_attach(&events);
        });
    }

    #[test]
    fn test_schedule_fetching_ad() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);
            feature.schedule_fetching_ad();
            assert!(feature.fetch_ad_task_id.is_some());
        });
    }

    #[test]
    fn test_clear_ad() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);
            feature.schedule_fetching_ad();
            feature.clear_ad();
            assert!(feature.fetch_ad_task_id.is_none());
            assert!(feature.show_ad_task_id.is_none());
        });
    }

    #[test]
    fn test_reset_state() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.regolith_url = Some("https://example.com".to_string());
            feature.schedule_fetching_ad();
            feature.min_duration_between_fetch_in_milliseconds = Some(Duration::from_secs(10));
            feature.last_shown_ad_time = Some(Instant::now());

            feature.reset_state();

            assert!(feature.regolith_url.is_none());
            assert!(feature.fetch_ad_task_id.is_none());
            assert!(feature.show_ad_task_id.is_none());
            assert!(feature.min_duration_between_fetch_in_milliseconds.is_none());
            assert!(feature.last_shown_ad_time.is_none());
        });
    }

    #[test]
    fn test_cancel_timers() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.fetch_ad_task_id = Some(ctx.schedule_task(Instant::now(), move || {}));

            feature.show_ad_task_id = Some(ctx.schedule_task(Instant::now(), move || {}));

            feature.cancel_timers();

            assert!(feature.fetch_ad_task_id.is_none());
            assert!(feature.show_ad_task_id.is_none());
        });
    }

    #[rstest]
    #[case(None, Some(Duration::from_secs(10)), true)]
    #[case(Some(Instant::now()), None, true)]
    #[case(Some(Instant::now()), Some(Duration::from_secs(100)), false)]
    fn test_exceeds_min_time_between_pause_ads(
        #[case] last_shown: Option<Instant>,
        #[case] min_duration: Option<Duration>,
        #[case] expected: bool,
    ) {
        launch_only_app_context(move |ctx| {
            let mut feature = setup_feature(&ctx);
            feature.last_shown_ad_time = last_shown;
            feature.min_duration_between_fetch_in_milliseconds = min_duration;
            assert_eq!(
                feature.has_enough_time_passed_since_last_pause_ad(),
                expected
            );
        });
    }

    #[test]
    fn test_on_playback_state_change_when_playing() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.schedule_fetching_ad();
            assert!(feature.fetch_ad_task_id.is_some());

            feature.on_playback_state_change(&PlaybackState::Playing);

            assert!(feature.fetch_ad_task_id.is_none());
            assert!(feature.show_ad_task_id.is_none());
        });
    }

    #[test]
    fn test_on_playback_state_change_when_paused_feature_disabled() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.on_playback_state_change(&PlaybackState::Paused);

            assert!(feature.fetch_ad_task_id.is_none());
            assert!(feature.show_ad_task_id.is_none());
        });
    }

    #[test]
    fn test_on_playback_state_change_when_paused_no_regolith_url() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_rust_playback_pause_ads_enabled(false)
                .build_into_context(ctx.scope());

            let mut feature = setup_feature(&ctx);

            feature.regolith_url = None;

            feature.on_playback_state_change(&PlaybackState::Paused);

            assert!(feature.fetch_ad_task_id.is_none());
            assert!(feature.show_ad_task_id.is_none());
        });
    }

    #[test]
    fn test_on_content_info_updates_regolith_url() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            let test_url = "https://test.com/ads";
            let content_info = ContentInfoBuilder::default()
                .feature_metadata(Some("fake".to_string()))
                .pause_behavior(PauseBehavior {
                    ad_request_url: test_url.to_string(),
                    ux_request_url: None,
                })
                .build();

            feature.on_content_info(&content_info);

            assert_eq!(feature.regolith_url, Some(test_url.to_string()));
        });
    }

    #[test]
    fn test_on_content_info_content_info_url_none() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            let original_url = "https://original.url";
            feature.regolith_url = Some(original_url.to_string());

            let content_info = ContentInfoBuilder::default().feature_metadata(None).build();

            feature.on_content_info(&content_info);

            assert_eq!(feature.regolith_url, None);
        });
    }

    #[test]
    fn test_exceeds_min_time_between_pause_ads_with_recent_ad() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.min_duration_between_fetch_in_milliseconds = Some(Duration::from_secs(100));
            feature.last_shown_ad_time = Some(Instant::now());

            assert!(!feature.has_enough_time_passed_since_last_pause_ad());
        });
    }

    #[test]
    fn test_on_playback_state_change_when_paused_with_min_duration_not_exceeded() {
        launch_only_app_context(|ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_rust_playback_pause_ads_enabled(false)
                .build_into_context(ctx.scope());

            let mut feature = setup_feature(&ctx);

            feature.regolith_url = Some("https://test.url".to_string());
            feature.min_duration_between_fetch_in_milliseconds = Some(Duration::from_millis(10000));
            feature.last_shown_ad_time = Some(Instant::now());

            feature.on_playback_state_change(&PlaybackState::Paused);

            assert!(feature.show_ad_task_id.is_none());
        });
    }

    #[test]
    fn test_feature_can_reset_and_initialize_multiple_times() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.reset();
            feature.initialize();
            feature.reset();
            feature.initialize();
        });
    }

    #[test]
    fn test_exceeds_min_time_between_pause_ads_with_old_ad() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            // Advance the clock well ahead because when we do Instant::now() - Duration::from_secs(1)
            // it goes negative overflow which is a panic
            MockClock::set_time(Duration::from_secs(6000));

            feature.min_duration_between_fetch_in_milliseconds = Some(Duration::from_millis(100));
            feature.last_shown_ad_time = Some(Instant::now() - Duration::from_secs(1));

            assert!(feature.has_enough_time_passed_since_last_pause_ad());
        });
    }

    #[test]
    fn test_on_content_info_both_regolith_urls_none() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.regolith_url = None;

            let content_info = ContentInfoBuilder::default().feature_metadata(None).build();

            feature.on_content_info(&content_info);

            assert_eq!(feature.regolith_url, None);
        });
    }

    #[test]
    fn test_on_content_info_both_regolith_urls_some() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            let url = "https://same.url";
            feature.regolith_url = Some(url.to_string());

            let content_info = ContentInfoBuilder::default()
                .feature_metadata(None)
                .pause_behavior(PauseBehavior {
                    ad_request_url: url.to_string(),
                    ux_request_url: None,
                })
                .build();

            feature.on_content_info(&content_info);

            assert_eq!(feature.regolith_url, Some(url.to_string()));
        });
    }

    #[test]
    fn test_on_content_info_transition_from_none_to_some() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            feature.regolith_url = None;

            let new_url = "https://new.url";
            let content_info = ContentInfoBuilder::default()
                .feature_metadata(None)
                .pause_behavior(PauseBehavior {
                    ad_request_url: new_url.to_string(),
                    ux_request_url: None,
                })
                .build();

            feature.on_content_info(&content_info);

            assert_eq!(feature.regolith_url, Some(new_url.to_string()));
        });
    }

    #[test]
    fn test_on_content_info_transition_from_some_to_none() {
        launch_only_app_context(|ctx| {
            let mut feature = setup_feature(&ctx);

            let initial_url = "https://initial.url";
            feature.regolith_url = Some(initial_url.to_string());

            let content_info = ContentInfoBuilder::default().feature_metadata(None).build();

            feature.on_content_info(&content_info);

            assert_eq!(feature.regolith_url, None);
        });
    }

    #[test]
    fn test_required_resources() {
        launch_only_app_context(|ctx| {
            let feature = setup_feature(&ctx);
            let required_resources =
                feature.get_required_resources_on_demand(IntrinsicContentConfig::builder().build());
            assert_eq!(required_resources.len(), 1);
            assert!(required_resources.contains(&ResourceName::PauseAds));
        });
    }
}
