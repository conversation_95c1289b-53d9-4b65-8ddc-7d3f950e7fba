use crate::features::interactive_video_ads_service::InteractiveVideoAdsServiceRc;
#[cfg_attr(test, double)]
use crate::metrics::reporter::Reporter;
use crate::parsers::vast_extensions::{IvaVastExtensionParser, VastExtensionParser};
use crate::utils::iva_ad_panel::{create_iva_ad_panel, IvaAdPanel};
use ads_playback_types::interactive_video_ad::{AdInstanceId, InteractiveVideoAd, TemplateId};
use ads_playback_types::surface_x::SurfaceXTemplate;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::StoredValue;
#[cfg(test)]
use mockall_double::double;
use playback_core::{PlaybackObserver, TimelineInfo};
use playback_feature_manager::{Feature, FeatureControllers, FeatureName, PanelName};
use playback_machine::use_streaming_type;
use playback_metadata_provider::types::ResourceResponse;
use playback_metadata_provider::RequiresMetadataResources;
use playback_src::StreamingType;
use playback_ui_kit::ads::{use_ads_store, Ad};
use playback_ui_kit::playback_controller::PlaybackProxy;
use player::{ContentInfo, Timeline};
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use rust_features::WeblabTreatmentString;
use std::collections::HashSet;

const LOG_PREFIX: &str = "[InteractiveVideoAdsVodFeature]";

pub struct InteractiveVideoAdsVodFeature {
    interactive_video_ads_service: InteractiveVideoAdsServiceRc,
    ctx: AppContext,
    controllers: FeatureControllers,
    extension_parser: IvaVastExtensionParser,
    streaming_type: Signal<Option<StreamingType>>,
    active_iva: ReadSignal<Option<AdInstanceId>>,
    timeline_ads: StoredValue<HashSet<InteractiveVideoAd>>,
    template_cache: StoredValue<lru::LruCache<TemplateId, SurfaceXTemplate>>,
    ad_panel: RwSignal<Option<IvaAdPanel>>,
}

impl InteractiveVideoAdsVodFeature {
    pub fn new(
        ctx: AppContext,
        controllers: FeatureControllers,
        reporter: Reporter,
        interactive_video_ads_service: InteractiveVideoAdsServiceRc,
    ) -> Self {
        let streaming_type = use_streaming_type(ctx.scope());

        let ad_panel = create_rw_signal(ctx.scope(), None);

        let (active_iva, timeline_ads, template_cache) = {
            let interactive_video_ads_service = interactive_video_ads_service.borrow();
            (
                interactive_video_ads_service.active_iva,
                interactive_video_ads_service.timeline_ads,
                interactive_video_ads_service.template_cache,
            )
        };

        Self {
            interactive_video_ads_service,
            ctx,
            controllers,
            extension_parser: IvaVastExtensionParser::new(reporter),
            streaming_type,
            active_iva,
            timeline_ads,
            template_cache,
            ad_panel,
        }
    }

    pub fn setup_effects(&self) {
        self.setup_create_ad_panel_effect();
        self.setup_ad_panel_lifecycle_effect();
    }

    fn setup_create_ad_panel_effect(&self) {
        let ad_panel = self.ad_panel;
        let active_iva = self.active_iva;
        let panels_controller = self.controllers.panels_controller.clone();
        let timeline_ads = self.timeline_ads;
        let template_cache = self.template_cache;

        create_effect(self.ctx.scope(), move |_| {
            active_iva.with(|active_iva| match active_iva.as_ref() {
                Some(id) => {
                    let should_create_new_panel = ad_panel.with_untracked(|existing_panel| {
                        existing_panel
                            .as_ref()
                            .is_none_or(|existing_panel| existing_panel.ad_instance_id() != id)
                    });

                    if !should_create_new_panel {
                        return;
                    }

                    ad_panel.set(None);

                    if let Some(panel) = create_iva_ad_panel(
                        FeatureName::InteractiveVideoAdsVod,
                        PanelName::InteractiveVideoAdsVod,
                        panels_controller.clone(),
                        timeline_ads,
                        template_cache,
                        id,
                    ) {
                        ad_panel.set(Some(panel))
                    }
                }
                None => {
                    ad_panel.set(None);
                }
            });
        });
    }

    fn setup_ad_panel_lifecycle_effect(&self) {
        let active_ad = use_ads_store(self.ctx.scope()).active_ad;
        let ad_panel = self.ad_panel;
        create_effect(self.ctx.scope(), move |_| {
            active_ad.with(|active_ad| {
                active_ad.as_ref().map(|active_ad| {
                    ad_panel.update(|ad_panel| {
                        ad_panel.as_mut().map(|ad_panel| {
                            if Self::should_show_panel(active_ad) {
                                ad_panel.show();
                            } else {
                                ad_panel.hide();
                            }
                        });
                    })
                })
            });
        });
    }

    fn handle_on_timeline_change(&mut self, timeline: &Timeline, _: &TimelineInfo) {
        log::info!(
            "{} Received new timeline, looking for interactive ads",
            LOG_PREFIX
        );

        self.timeline_ads.update_value(|ads| {
            let new_ads: HashSet<InteractiveVideoAd> = timeline
                .items
                .iter()
                // Get all the content info that correspond to the current items
                .filter_map(|item| {
                    let content_info = timeline.content_infos.get(&item.content_id)?;
                    Some((item, content_info))
                })
                // Filter only ads and drop the ad if we have parsed it before
                .filter_map(|(item, content_info)| match content_info {
                    ContentInfo::Advertisement(ad_metadata, _)
                        if !ads.contains(&ad_metadata.ad_instance_id) =>
                    {
                        Some((item, ad_metadata))
                    }
                    _ => None,
                })
                // Parse ad extensions and create an interactive video ad
                .filter_map(|(item, content_info)| {
                    self.extension_parser
                        .create_vod_ads(item, content_info)
                        .map(|actionable_ad_extension| InteractiveVideoAd {
                            id: content_info.ad_instance_id.clone(),
                            extension: actionable_ad_extension,
                        })
                })
                .collect();

            log::info!("{} Found {} new interactive ads", LOG_PREFIX, new_ads.len());

            ads.extend(new_ads);
        });
    }

    // TODO: https://taskei.amazon.dev/tasks/D264754673
    fn should_show_panel(_active_ad: &Ad) -> bool {
        true
    }
}

impl Feature for InteractiveVideoAdsVodFeature {
    fn feature_name(&self) -> FeatureName {
        FeatureName::InteractiveVideoAdsVod
    }

    fn initialize(&mut self) {}

    fn is_feature_enabled(&self) -> bool {
        try_use_rust_features(self.ctx.scope()).map_or_else(
            || {
                log::error!(
                    "{} Could not find rust features in context. IVA Vod feature is disabled",
                    LOG_PREFIX
                );
                false
            },
            |rust_features| {
                let treatment = rust_features.get_rust_playback_iva_vod_treatment_string();
                let is_enabled = treatment != WeblabTreatmentString::C;

                let streaming_type = self.streaming_type.get_untracked();
                let is_vod_stream = streaming_type == Some(StreamingType::Vod)
                    || streaming_type == Some(StreamingType::VodEventItem);

                log::info!(
                    "{} Weblab treatment: {:?}, is_enabled: {}, is_vod_stream: {}",
                    LOG_PREFIX,
                    treatment,
                    is_enabled,
                    is_vod_stream
                );

                is_enabled && is_vod_stream
            },
        )
    }

    fn reset(&mut self) {}
}

impl PlaybackObserver for InteractiveVideoAdsVodFeature {
    fn on_timeline_change(&mut self, timeline: &Timeline, timeline_info: &TimelineInfo) {
        if !self.is_feature_enabled() {
            return;
        }
        self.handle_on_timeline_change(timeline, timeline_info);
    }
}

impl PlaybackProxy for InteractiveVideoAdsVodFeature {}

impl RequiresMetadataResources for InteractiveVideoAdsVodFeature {
    fn on_resource(&mut self, _: &ResourceResponse) {}
}

pub fn interactive_video_ads_vod_feature_factory(
    ctx: AppContext,
    controllers: FeatureControllers,
    reporter: Reporter,
    interactive_video_ads_service: InteractiveVideoAdsServiceRc,
) -> InteractiveVideoAdsVodFeature {
    let feature = InteractiveVideoAdsVodFeature::new(
        ctx,
        controllers,
        reporter,
        interactive_video_ads_service,
    );
    feature.setup_effects();
    feature
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::features::interactive_video_ads_service::InteractiveVideoAdsService;
    use crate::test_data::{
        create_test_atc_interactive_video_ad, create_timeline_with_no_ads,
        create_timeline_with_pre_roll_and_mid_roll_all_interactive_ads,
        create_timeline_with_pre_roll_and_mid_roll_with_mixed_ads, TestAdType,
    };
    use crate::test_utils::*;
    use ads_playback_types::test_data::MINIMAL_BASELINE_IVA_TEMPLATE;
    use ignx_compositron::app::launch_only_app_context;
    use network_parser::prelude::network_parse_from_str;
    use playback_core::{
        provide_observable, try_update_observable, PlaybackEvent, PlaybackObservable,
    };
    use playback_machine::PlaybackMachineContext;
    use playback_src::{
        IntrinsicContentConfig, LinearStartMode, LiveEventStartMode, StreamingConfig,
    };
    use playback_ui_kit::ads::provide_ads_store;
    use player::test_utils::{
        AdMetadataBuilder, ContentInfoBuilder, TimeDataBuildable, TimelineTestUtils,
    };
    use player::{PlayerEvent, TimeData};
    use rstest::rstest;
    use rust_features::{MockRustFeaturesBuilder, WeblabTreatmentString};

    fn setup_feature(
        ctx: &AppContext,
    ) -> (InteractiveVideoAdsVodFeature, InteractiveVideoAdsServiceRc) {
        PlaybackMachineContext::test_builder(ctx.scope())
            .build()
            .provide_on(ctx.scope());
        let controllers = setup_feature_controllers_with_mock_contexts(ctx);
        let reporter = Reporter::default();
        let interactive_video_ads_service = InteractiveVideoAdsService::new_rc(ctx);
        (
            interactive_video_ads_vod_feature_factory(
                ctx.clone(),
                controllers,
                reporter,
                interactive_video_ads_service.clone(),
            ),
            interactive_video_ads_service,
        )
    }

    fn setup_feature_with_playback_loaded_and_playing(
        ctx: &AppContext,
        streaming_config: StreamingConfig,
    ) -> (InteractiveVideoAdsVodFeature, InteractiveVideoAdsServiceRc) {
        let controllers = setup_feature_controllers_with_mock_contexts(ctx);
        PlaybackMachineContext::test_builder(ctx.scope())
            .build_loaded_and_playing(
                IntrinsicContentConfig::builder()
                    .streaming_config(streaming_config)
                    .build()
                    .into(),
            )
            .provide_on(ctx.scope());
        let mut reporter = Reporter::default();
        reporter.expect_metric().return_const(());
        let interactive_video_ads_service = InteractiveVideoAdsService::new_rc(ctx);
        (
            interactive_video_ads_vod_feature_factory(
                ctx.clone(),
                controllers,
                reporter,
                interactive_video_ads_service.clone(),
            ),
            interactive_video_ads_service,
        )
    }

    #[test]
    fn test_feature_factory_creates_feature() {
        launch_only_app_context(|ctx| {
            let (feature, _) = setup_feature(&ctx);
            assert_eq!(feature.feature_name(), FeatureName::InteractiveVideoAdsVod);
        });
    }

    #[test]
    fn test_feature_name_returns_iva_vod() {
        launch_only_app_context(|ctx| {
            let (feature, _) = setup_feature(&ctx);
            assert_eq!(feature.feature_name(), FeatureName::InteractiveVideoAdsVod);
        });
    }

    #[rstest]
    #[case(
        StreamingConfig::Linear { start_mode: LinearStartMode::AtLive },
        false
    )]
    #[case(
        StreamingConfig::LiveEvent { start_mode: LiveEventStartMode::AtLive },
        false
    )]
    #[case(StreamingConfig::Trailer, false)]
    #[case(StreamingConfig::Promo, false)]
    #[case(
        StreamingConfig::Vod { position: 0 },
        true
    )]
    #[case(
        StreamingConfig::VodEventItem { position: 0 },
        true
    )]
    fn test_is_feature_enabled_returns_true_when_rust_feature_enabled_for_vod_only(
        #[case] streaming_config: StreamingConfig,
        #[case] expected: bool,
    ) {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_rust_playback_iva_vod_treatment(WeblabTreatmentString::T1)
                .build_into_context(ctx.scope());
            let (feature, _) =
                setup_feature_with_playback_loaded_and_playing(&ctx, streaming_config);
            assert_eq!(feature.is_feature_enabled(), expected);
        });
    }

    #[rstest]
    #[case(
        StreamingConfig::Linear { start_mode: LinearStartMode::AtLive },
    )]
    #[case(
        StreamingConfig::LiveEvent { start_mode: LiveEventStartMode::AtLive },
    )]
    #[case(StreamingConfig::Trailer)]
    #[case(StreamingConfig::Promo)]
    #[case(
        StreamingConfig::Vod { position: 0 },
    )]
    #[case(
        StreamingConfig::VodEventItem { position: 0 },
    )]
    fn test_is_feature_enabled_returns_false_when_rust_feature_disabled_for_any_streaming_type(
        #[case] streaming_config: StreamingConfig,
    ) {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_rust_playback_iva_vod_treatment(WeblabTreatmentString::C)
                .build_into_context(ctx.scope());
            let (feature, _) =
                setup_feature_with_playback_loaded_and_playing(&ctx, streaming_config);
            assert!(!feature.is_feature_enabled());
        });
    }

    #[test]
    fn test_lifecycle_methods_execute_without_error() {
        launch_only_app_context(|ctx| {
            // No explicit assert - we're verifying the functions execute without error
            let (mut feature, _) = setup_feature(&ctx);
            feature.initialize();
            feature.reset();
        });
    }

    #[rstest]
    #[case(create_timeline_with_no_ads(), 0, WeblabTreatmentString::T1)]
    #[case(
        create_timeline_with_pre_roll_and_mid_roll_all_interactive_ads(),
        2,
        WeblabTreatmentString::T1
    )]
    #[case(
        create_timeline_with_pre_roll_and_mid_roll_with_mixed_ads(),
        2,
        WeblabTreatmentString::T1
    )]
    #[case(create_timeline_with_no_ads(), 0, WeblabTreatmentString::C)]
    #[case(
        create_timeline_with_pre_roll_and_mid_roll_all_interactive_ads(),
        0,
        WeblabTreatmentString::C
    )]
    #[case(
        create_timeline_with_pre_roll_and_mid_roll_with_mixed_ads(),
        0,
        WeblabTreatmentString::C
    )]
    fn test_on_timeline_change_with_ads(
        #[case] timeline: Timeline,
        #[case] expected_number_of_interactive_video_ads: usize,
        #[case] treatment: WeblabTreatmentString,
    ) {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_rust_playback_iva_vod_treatment(treatment)
                .build_into_context(ctx.scope());

            let (mut feature, interactive_video_ads_service) =
                setup_feature_with_playback_loaded_and_playing(
                    &ctx,
                    StreamingConfig::Vod { position: 0 },
                );

            let timeline_info = TimelineInfo {
                item_offsets: vec![],
            };

            feature.on_timeline_change(&timeline, &timeline_info);

            let mut total_iva_ads = 0;

            timeline.content_infos.iter().for_each(|(_, content_info)| {
                if let ContentInfo::Advertisement(ad_metadata, _) = content_info {
                    if interactive_video_ads_service
                        .borrow()
                        .timeline_ads
                        .with_value(|ads| ads.contains(&ad_metadata.ad_instance_id))
                    {
                        total_iva_ads += 1;
                    }
                }
            });

            assert_eq!(total_iva_ads, expected_number_of_interactive_video_ads);
        });
    }

    fn setup_interactive_video_ad(
        interactive_video_ads_service: &mut InteractiveVideoAdsService,
        ad_instance_id: &str,
    ) {
        let interactive_video_ad =
            create_test_atc_interactive_video_ad(TestAdType::VodLinear, ad_instance_id);
        let template_id = interactive_video_ad.template_id();

        interactive_video_ads_service
            .timeline_ads
            .update_value(|ads| {
                ads.insert(interactive_video_ad);
            });

        interactive_video_ads_service
            .template_cache
            .update_value(|cache| {
                cache.put(
                    template_id,
                    network_parse_from_str::<SurfaceXTemplate>(MINIMAL_BASELINE_IVA_TEMPLATE)
                        .expect("Failed to deserialize payload"),
                );
            });
    }

    fn trigger_content_info(
        interactive_video_ads_service: &mut InteractiveVideoAdsService,
        ad_instance_id: Option<&str>,
    ) {
        let content_info = match ad_instance_id {
            Some(id) => ContentInfoBuilder::default()
                .ad_metadata(AdMetadataBuilder::default().ad_instance_id(id).build())
                .build(),
            None => ContentInfoBuilder::default()
                .feature_metadata(Some("test".to_string()))
                .build(),
        };

        interactive_video_ads_service.on_content_info(&content_info);
    }

    #[test]
    fn test_setup_create_ad_panel_effect_creates_panel_when_active_iva_changes() {
        launch_only_app_context(|ctx| {
            let (feature, interactive_video_ads_service) = setup_feature(&ctx);

            // Initially no panel
            assert!(feature.ad_panel.with_untracked(|panel| panel.is_none()));

            let ad_instance_id = "test_id".to_string();
            let mut interactive_video_ads_service = interactive_video_ads_service.borrow_mut();

            setup_interactive_video_ad(&mut interactive_video_ads_service, &ad_instance_id);
            trigger_content_info(&mut interactive_video_ads_service, Some(&ad_instance_id));

            // Panel should be created
            assert!(feature.ad_panel.with_untracked(|panel| panel.is_some()));
            assert_eq!(
                feature.ad_panel.with_untracked(|panel| panel
                    .as_ref()
                    .map(|panel| panel.ad_instance_id().to_string())),
                Some(ad_instance_id)
            );

            feature.ad_panel.set_untracked(None);
        });
    }

    #[test]
    fn test_setup_create_ad_panel_effect_removes_panel_when_active_iva_is_none() {
        launch_only_app_context(|ctx| {
            let (feature, interactive_video_ads_service) = setup_feature(&ctx);

            let ad_instance_id = "test_id".to_string();
            let mut interactive_video_ads_service = interactive_video_ads_service.borrow_mut();

            setup_interactive_video_ad(&mut interactive_video_ads_service, &ad_instance_id);
            trigger_content_info(&mut interactive_video_ads_service, Some(&ad_instance_id));

            // Verify panel was created
            assert!(feature.ad_panel.with_untracked(|panel| panel.is_some()));

            // Clear active IVA
            trigger_content_info(&mut interactive_video_ads_service, None);

            // Panel should be removed
            assert!(feature.ad_panel.with_untracked(|panel| panel.is_none()));
        });
    }

    #[test]
    fn test_setup_ad_panel_lifecycle_effect_shows_panel_for_active_ad() {
        launch_only_app_context(|ctx| {
            provide_observable(ctx.scope(), PlaybackObservable::new());
            provide_ads_store(ctx.scope());

            let (feature, interactive_video_ads_service) = setup_feature(&ctx);

            let ad_instance_id = "test_id".to_string();
            let mut interactive_video_ads_service = interactive_video_ads_service.borrow_mut();

            setup_interactive_video_ad(&mut interactive_video_ads_service, &ad_instance_id);
            trigger_content_info(&mut interactive_video_ads_service, Some(&ad_instance_id));

            let timeline = Timeline::samples().ads_and_aux();
            try_update_observable(ctx.scope(), |v| {
                v.dispatch(&PlaybackEvent::Player(&PlayerEvent::TimelineChange(
                    timeline.clone(),
                )));
            });

            try_update_observable(ctx.scope(), |v| {
                v.dispatch(&PlaybackEvent::Player(&PlayerEvent::TimeDataChange(
                    TimeData::builder().current_timeline_item_index(0).build(),
                )));
            });

            feature.ad_panel.with_untracked(|panel| {
                panel.as_ref().expect("Panel should exist");
            });

            feature.ad_panel.set_untracked(None);
        });
    }
}
