use std::rc::Rc;

use super::iva_baseline_success_details::*;
use crate::surface_x::template_hydration::{
    IvaBaselineTemplateData, IvaBaselineTemplateDataProperties,
};
use crate::surface_x::widget_renderer::*;
use crate::surface_x::widgets::types::SurfaceXWidgetTemplateData;
use crate::surface_x::widgets::{container_styles::SurfaceXContainerStyles, *};
use amzn_fable_tokens::{FableBorder, FableColor, FableIcon, FableSpacing};
use fableous::utils::get_ignx_color;
use ignx_compositron::shared::Shared;
use ignx_compositron::{compose, compose_option, prelude::safe::*, Composer};

const CONTENT_WIDTH: f32 = 429.0;
const ICON_WIDTH: f32 = 81.0;
const ICON_PADDING_END: f32 = FableSpacing::SPACING088;
const MAIN_CONTENT_WIDTH: f32 = CONTENT_WIDTH - ICON_WIDTH - ICON_PADDING_END;
const INNER_PADDING: f32 = FableSpacing::SPACING133;

#[Composer]
pub fn IVABaseline<'s>(
    ctx: &AppContext<'s>,
    cta_was_clicked: RwSignal<'s, bool>,
    template_data: RefSignal<'static, IvaBaselineTemplateData>,
) -> ColumnComposable<'s> {
    let focus_signal = create_focus_signal(ctx.scope());

    let success_icon_widget_data =
        SurfaceXWidgetTemplateData::Icon(Shared::new(SurfaceXIconWidgetTemplateData {
            iter_id: "icon_id".to_string(),
            icon_name: FableIcon::CHECK.to_string(),
            color: Some(get_ignx_color(FableColor::SUCCESS)),
            font_size: Some(FontSize(48)),
            container_style: Some(SurfaceXContainerStyles {
                width: Some(ICON_WIDTH),
                height: Some(ICON_WIDTH),
                border_radius: Some(FableBorder::RADIUS100),
                main_axis_alignment: Some(MainAxisAlignment::Center),
                cross_axis_alignment: Some(CrossAxisAlignment::Center),
                background_color: Some(get_ignx_color(FableColor::CHANNEL)),
                ..Default::default()
            }),
        }));
    let success_icon_widget_data_ref = create_ref_signal(
        ctx.scope(),
        SharedSignal::from_data(success_icon_widget_data),
    );

    let icon_widget_from_template = template_data.main_icon();
    let main_content_from_template = template_data.main_content();
    let cta_button_from_template = template_data.cta_button();

    let main_icon_builder = Box::new(move |ctx: &AppContext<'s>| {
        compose_option! {
           if cta_was_clicked.get() {
            Stack() {
              WidgetRenderer(widget_data:success_icon_widget_data_ref)
            }
            .test_id("success_icon")
           } else {
             Stack() {
                WidgetRenderer(widget_data: icon_widget_from_template)
             }
             .test_id("initial_icon")
           }
        }
    });

    let cta_button_builder = Box::new(move |ctx: &AppContext<'s>| {
        if !cta_was_clicked.get() {
            let cta_button_data = cta_button_from_template.get()?;
            let widget_data =
                create_ref_signal(ctx.scope(), SharedSignal::from_shared(cta_button_data));

            compose_option! {
                Column() {
                  WidgetRenderer(widget_data)
                }
                .width(CONTENT_WIDTH)
                .cross_axis_stretch(true)
                .test_id("cta_button")
            }
        } else {
            None
        }
    });

    let main_content_builder = Box::new(move |ctx: &AppContext<'s>| {
        if cta_was_clicked.get() {
            compose_option! {
                  IVABaselineSuccessDetails(cta_type: ActionType::ATC, atc_headline:"ATC Headline" )
                  .test_id("success_content")
            }
        } else {
            compose_option! {
                ColumnForEach(
                    items: main_content_from_template,
                    item_builder: Rc::new(move |ctx, item, _index| {
                         let widget_data = create_ref_signal(ctx.scope(), SharedSignal::from_shared(item.clone()));

                        compose! {
                            WidgetRenderer(widget_data)
                        }
                    })
                )
                .width(MAIN_CONTENT_WIDTH)
                .test_id("initial_content")
            }
        }
    });

    let border_color = create_memo(ctx.scope(), move |_| {
        focus_signal
            .get()
            .then_some(get_ignx_color(FableColor::PRIMARY))
            .unwrap_or(Color::transparent())
    });

    compose! {
        Column() {
            Column() {
                Row() {
                  Column() {
                    Memo(item_builder: main_icon_builder)
                  }
                  .padding(Padding::new(0.0, ICON_PADDING_END, 0.0, 0.0))

                  Memo(item_builder: main_content_builder)
                }

                Memo(item_builder: cta_button_builder)
            }
            .width(CONTENT_WIDTH + INNER_PADDING * 2.0)
            .padding(Padding::all(INNER_PADDING))
            .border_radius(FableBorder::RADIUS150)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
            .overflow_behavior(OverflowBehavior::Hidden)
        }
        .padding(Padding::all(FableSpacing::FOUNDATION_SPACING050))
        .border_width(4.0)
        .border_color(border_color)
        .focused(focus_signal)
        .focusable()
        .border_radius(FableBorder::RADIUS200)
        .test_id("iva_baseline")
    }
}

#[cfg(test)]
mod tests {
    use crate::test_data::create_test_iva_baseline_template_data;

    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_does_not_exist, assert_node_exists},
    };
    use rstest::rstest;

    fn create_mock_template_data(
        ctx: &AppContext<'static>,
    ) -> RefSignal<'static, IvaBaselineTemplateData> {
        let template_data = create_test_iva_baseline_template_data();
        create_ref_signal(ctx.scope(), SharedSignal::from_data(template_data))
    }

    #[rstest]
    fn test_initial_state() {
        launch_test(
            |ctx| {
                let cta_was_clicked = create_rw_signal(ctx.scope(), false);
                let template_data = create_mock_template_data(&ctx);

                compose! {
                    IVABaseline(
                        cta_was_clicked,
                        template_data,
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let main_container = node_tree.find_by_test_id("iva_baseline");
                assert_node_exists!(&main_container);

                let initial_icon = node_tree.find_by_test_id("initial_icon");
                assert_node_exists!(&initial_icon);

                let initial_content = node_tree.find_by_test_id("initial_content");
                assert_node_exists!(&initial_content);

                let cta_button = node_tree.find_by_test_id("cta_button");
                assert_node_exists!(&cta_button);

                // Check success state elements don't exist
                let success_icon = node_tree.find_by_test_id("success_icon");
                assert_node_does_not_exist!(&success_icon);

                let success_content = node_tree.find_by_test_id("success_content");
                assert_node_does_not_exist!(&success_content);
            },
        );
    }

    #[rstest]
    fn test_success_state_and_content_details() {
        launch_test(
            |ctx| {
                let cta_was_clicked = create_rw_signal(ctx.scope(), true);
                let template_data = create_mock_template_data(&ctx);

                compose! {
                    IVABaseline(
                        cta_was_clicked,
                        template_data,
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let main_container = node_tree.find_by_test_id("iva_baseline");
                assert_node_exists!(&main_container);

                let success_icon = node_tree.find_by_test_id("success_icon");
                assert_node_exists!(&success_icon);

                let success_content = node_tree.find_by_test_id("success_content");
                assert_node_exists!(&success_content);

                // Check initial state elements don't exist
                let initial_icon = node_tree.find_by_test_id("initial_icon");
                assert_node_does_not_exist!(&initial_icon);

                let initial_content = node_tree.find_by_test_id("initial_content");
                assert_node_does_not_exist!(&initial_content);

                let cta_button = node_tree.find_by_test_id("cta_button");
                assert_node_does_not_exist!(&cta_button);

                // Check IVABaselineSuccessDetails components exist within success_content
                let headline_text = node_tree.find_by_test_id("headline-text");
                assert_node_exists!(&headline_text);
                let headline_props = headline_text.get_props();
                assert_eq!(headline_props.text, Some("ATC Headline".to_string()));
            },
        );
    }

    #[rstest]
    fn test_state_transition() {
        launch_test(
            |ctx| {
                let cta_was_clicked = create_rw_signal(ctx.scope(), false);
                let template_data = create_mock_template_data(&ctx);
                provide_context(ctx.scope(), cta_was_clicked);

                compose! {
                    IVABaseline(
                        cta_was_clicked,
                        template_data,
                    )
                }
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let initial_content = node_tree.find_by_test_id("initial_content");
                assert_node_exists!(&initial_content);

                let success_content = node_tree.find_by_test_id("success_content");
                assert_node_does_not_exist!(&success_content);

                // Trigger state change
                let cta_was_clicked = use_context::<RwSignal<'_, bool>>(scope).unwrap();
                cta_was_clicked.set(true);

                // Verify success state
                let node_tree = test_game_loop.tick_until_done();
                let success_content = node_tree.find_by_test_id("success_content");
                assert_node_exists!(&success_content);

                let initial_content = node_tree.find_by_test_id("initial_content");
                assert_node_does_not_exist!(&initial_content);
            },
        );
    }

    #[rstest]
    fn test_container_properties() {
        launch_test(
            |ctx| {
                let cta_was_clicked = create_rw_signal(ctx.scope(), false);
                let template_data = create_mock_template_data(&ctx);

                compose! {
                    IVABaseline(
                        cta_was_clicked,
                        template_data,
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let main_container = node_tree.find_by_test_id("iva_baseline");
                assert_node_exists!(&main_container);

                let container_props = main_container.get_props();

                assert_eq!(container_props.base_styles.border_width, Some(4.0));

                // Check padding - using FableSpacing::FOUNDATION_SPACING050 value
                assert_eq!(
                    container_props.layout.padding.start,
                    FableSpacing::FOUNDATION_SPACING050
                );
                assert_eq!(
                    container_props.layout.padding.end,
                    FableSpacing::FOUNDATION_SPACING050
                );
                assert_eq!(
                    container_props.layout.padding.top,
                    FableSpacing::FOUNDATION_SPACING050
                );
                assert_eq!(
                    container_props.layout.padding.bottom,
                    FableSpacing::FOUNDATION_SPACING050
                );

                // Check that border color exists (dynamic based on focus)
                assert!(container_props.base_styles.border_color.is_some());
            },
        );
    }

    #[rstest]
    fn test_border_color_when_unfocused() {
        launch_test(
            |ctx| {
                let cta_was_clicked = create_rw_signal(ctx.scope(), false);
                let template_data = create_mock_template_data(&ctx);

                compose! {
                    Stack() {
                      Row(){}
                        .focusable()
                      IVABaseline(
                          cta_was_clicked,
                          template_data,
                      )
                    }
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let main_container = node_tree.find_by_test_id("iva_baseline");
                let container_props = main_container.get_props();

                // When unfocused, border should be transparent
                assert_eq!(
                    container_props.base_styles.border_color,
                    Some(Color::transparent())
                );
            },
        );
    }

    #[rstest]
    fn test_border_color_when_focused() {
        launch_test(
            |ctx| {
                let cta_was_clicked = create_rw_signal(ctx.scope(), false);
                let template_data = create_mock_template_data(&ctx);

                compose! {
                    IVABaseline(
                        cta_was_clicked,
                        template_data,
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Focus the component by sending a focus event
                let main_container = node_tree.find_by_test_id("iva_baseline");
                test_game_loop.send_on_focus_event(main_container.get_props().node_id);

                let node_tree = test_game_loop.tick_until_done();
                let main_container = node_tree.find_by_test_id("iva_baseline");
                let container_props = main_container.get_props();

                // When focused, border should be primary color
                assert_eq!(
                    container_props.base_styles.border_color,
                    Some(get_ignx_color(FableColor::PRIMARY))
                );
            },
        );
    }
}
