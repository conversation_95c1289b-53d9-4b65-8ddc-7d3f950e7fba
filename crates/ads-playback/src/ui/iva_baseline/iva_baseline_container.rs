use crate::surface_x::template_hydration::IvaBaselineTemplateData;

use super::*;
use ignx_compositron::app::wasm_app::EventData;
use ignx_compositron::input::KeyCode;
use ignx_compositron::prelude::safe::RefSignal;
use ignx_compositron::time::*;
use ignx_compositron::{compose, prelude::*, Composer};
use playback_ui_kit::background_gradient::{
    use_background_gradient_context, BottomBackgroundGradient,
};
use playback_ui_kit::chrome_visibility_mode::{
    use_chrome_visibility_mode, ChromeModeFocus, ChromeModeParameters, ChromeVisibilityMode,
};
use playback_ui_kit::focus_manager::{use_focus_manager, FocusLocation};
use playback_ui_kit::playback_mode::{use_playback_mode, PlaybackMode};

pub const IVA_BASELINE_CONTAINER_TEST_ID: &str = "iva_baseline_container";

#[Composer]
pub fn IVABaselineContainer(
    ctx: &AppContext,
    template_data: RefSignal<'static, IvaBaselineTemplateData>,
) -> AnyComposable {
    let playback_mode = use_playback_mode(ctx.scope());
    let chrome_visibility_mode = use_chrome_visibility_mode(ctx.scope());
    let background_gradient = use_background_gradient_context(ctx.scope());
    let focus_manager = use_focus_manager(ctx.scope());
    let focused_value_signal = focus_manager.focus_value_signal;
    let cta_was_clicked = create_rw_signal(ctx.scope(), false);
    let is_watch_mode = playback_mode.is(PlaybackMode::Watch);

    background_gradient.set_bottom_gradient(BottomBackgroundGradient::Nudge, None);

    // Use task as a workaround to move focus
    // See https://sim.amazon.com/issues/LRNP-5189
    ctx.schedule_task(Instant::now(), {
        let focus_manager = focus_manager.clone();
        move || {
            if is_watch_mode.get() {
                focus_manager.set_focus(FocusLocation::IVABaseline);
            }
        }
    });

    let on_key_down = move || {
        playback_mode.switch_to(PlaybackMode::Chrome(Some(
            ChromeModeParameters::default().with_initial_focus(ChromeModeFocus::ProgressBar),
        )));
    };

    let on_key_up = move || {
        chrome_visibility_mode.set(ChromeVisibilityMode::Compressed);
        playback_mode.switch_to(PlaybackMode::Chrome(None));
    };

    let on_select = move |event: &mut EventData| {
        event.stop_propagation();
        cta_was_clicked.try_set(true);
    };

    let on_backspace = move |event: &mut EventData| {
        event.stop_propagation();

        match playback_mode.get() {
            PlaybackMode::Chrome(_) => {
                playback_mode.switch_to(PlaybackMode::Watch);
            }
            PlaybackMode::AudioOptions
            | PlaybackMode::SubtitleOptions
            | PlaybackMode::InfoPanel
            | PlaybackMode::Watch
            | PlaybackMode::TrickPlay(_) => {
                // TODO: add dismiss logic
            }
        }
    };

    let on_pause = move || {
        playback_mode.switch_to(PlaybackMode::Chrome(Some(
            ChromeModeParameters::default().with_initial_focus(ChromeModeFocus::ProgressBar),
        )));
    };

    // To compress chrome mode when iva is focused
    create_effect(ctx.scope(), move |_| {
        let is_chrome_mode = matches!(playback_mode.playback_mode.get(), PlaybackMode::Chrome(_));
        let is_iva_focused = matches!(
            focus_manager.focus_value_signal.get(),
            Some(FocusLocation::IVABaseline)
        );
        let is_compressed_mode = matches!(
            chrome_visibility_mode.current_mode.get(),
            ChromeVisibilityMode::Compressed
        );

        if is_iva_focused && is_chrome_mode && !is_compressed_mode {
            chrome_visibility_mode.set(ChromeVisibilityMode::Compressed);
        }
    });

    compose! {
         Stack() {
           Row() {
            IVABaseline(cta_was_clicked, template_data)
            .test_id("iva_baseline")
           }
        }
        .focused_value(focused_value_signal, FocusLocation::IVABaseline)
        .test_id(IVA_BASELINE_CONTAINER_TEST_ID)
        .on_key_down(KeyCode::Down, on_key_down)
        .on_key_down(KeyCode::Up, on_key_up)
        .on_key_down(KeyCode::Space, on_pause)
        .on_select_event(on_select)
        .on_key_down_event(KeyCode::Backspace, on_backspace)
        .into_any()
    }
}

#[cfg(test)]
mod tests {
    use crate::test_data::create_test_iva_baseline_template_data;

    use super::*;
    use app_config::test_utils::MockAppConfigBuilder;
    use auth::MockAuth;
    use ignx_compositron::prelude::safe::{create_ref_signal, SharedSignal};
    use ignx_compositron::test_utils::assert_node_exists;
    use playback_ui_kit::background_gradient::{
        provide_background_gradient_context, BackgroundGradientSignals, BottomBackgroundGradient,
    };
    use playback_ui_kit::chrome_visibility_mode::{
        provide_chrome_visibility_mode_context, ChromeVisibilityModeContext,
    };
    use playback_ui_kit::focus_manager::provide_focus_manager;
    use playback_ui_kit::playback_mode::{provide_playback_mode, PlaybackMode};
    use router::hooks::setup_mock_routing_ctx;

    fn setup(ctx: AppContext) -> impl Composable<'static> {
        MockAuth::new_without_params(ctx.scope()).provide(ctx.scope());
        MockAppConfigBuilder::new().build_into_context(ctx.scope());
        setup_mock_routing_ctx(ctx.scope(), |_| {});

        provide_playback_mode(ctx.scope(), PlaybackMode::Watch);
        let bg_signals = provide_background_gradient_context(ctx.scope());
        provide_context(ctx.scope(), bg_signals);
        provide_chrome_visibility_mode_context(ctx.scope());

        let focus_manager_signals = provide_focus_manager(ctx.scope());
        provide_context(ctx.scope(), focus_manager_signals);

        let template_data = create_test_iva_baseline_template_data();
        let template_data = create_ref_signal(ctx.scope(), SharedSignal::from_data(template_data));

        compose! {
            IVABaselineContainer(template_data)
        }
    }

    #[test]
    fn renders_iva_baseline_container() {
        ignx_compositron::app::launch_test(setup, |scope, mut test_renderer_game_loop| {
            let tree = test_renderer_game_loop.tick_until_done();
            let bg_signals = expect_context::<BackgroundGradientSignals>(scope);

            assert_node_exists!(tree.find_by_test_id(IVA_BASELINE_CONTAINER_TEST_ID));

            assert_eq!(
                bg_signals.bottom_gradient.get(),
                Some(BottomBackgroundGradient::Nudge)
            );

            assert_node_exists!(tree.find_by_test_id("iva_baseline"));
        });
    }

    #[test]
    fn initial_focus_is_on_iva_baseline() {
        ignx_compositron::app::launch_test(setup, |scope, mut test_renderer_game_loop| {
            let _ = test_renderer_game_loop.tick_until_done();
            let focus_manager = use_focus_manager(scope);

            assert_eq!(
                focus_manager.focus_value_signal.get(),
                Some(FocusLocation::IVABaseline)
            );
        });
    }

    #[test]
    fn handles_down_key() {
        ignx_compositron::app::launch_test(setup, |scope, mut test_game_loop| {
            let tree = test_game_loop.tick_until_done();
            let playback_mode = use_playback_mode(scope);
            let container = tree.find_by_test_id(IVA_BASELINE_CONTAINER_TEST_ID);

            test_game_loop
                .send_key_down_up_event_to_node(container.borrow_props().node_id, KeyCode::Down);
            let _ = test_game_loop.tick_until_done();

            match playback_mode.get() {
                PlaybackMode::Chrome(Some(params)) => {
                    assert_eq!(params.initial_focus, ChromeModeFocus::ProgressBar);
                }
                _ => panic!("Expected Chrome mode with progress bar focus"),
            }
        });
    }

    #[test]
    fn handles_up_key() {
        ignx_compositron::app::launch_test(setup, |scope, mut test_game_loop| {
            let tree = test_game_loop.tick_until_done();
            let playback_mode = use_playback_mode(scope);
            let chrome_visibility_mode = use_chrome_visibility_mode(scope);
            let container = tree.find_by_test_id(IVA_BASELINE_CONTAINER_TEST_ID);

            test_game_loop
                .send_key_down_up_event_to_node(container.borrow_props().node_id, KeyCode::Up);
            let _ = test_game_loop.tick_until_done();

            assert_eq!(playback_mode.get(), PlaybackMode::Chrome(None));
            assert_eq!(
                chrome_visibility_mode.current_mode.get(),
                ChromeVisibilityMode::Compressed
            );
        });
    }

    #[test]
    fn handles_space_key() {
        ignx_compositron::app::launch_test(setup, |scope, mut test_game_loop| {
            let tree = test_game_loop.tick_until_done();
            let playback_mode = use_playback_mode(scope);
            let container = tree.find_by_test_id(IVA_BASELINE_CONTAINER_TEST_ID);

            test_game_loop
                .send_key_down_up_event_to_node(container.borrow_props().node_id, KeyCode::Space);
            let _ = test_game_loop.tick_until_done();

            match playback_mode.get() {
                PlaybackMode::Chrome(Some(params)) => {
                    assert_eq!(params.initial_focus, ChromeModeFocus::ProgressBar);
                }
                _ => panic!("Expected Chrome mode with progress bar focus after pause"),
            }
        });
    }

    #[test]
    fn handles_backspace_in_chrome_mode() {
        ignx_compositron::app::launch_test(setup, |scope, mut test_game_loop| {
            let tree = test_game_loop.tick_until_done();
            let playback_mode = use_playback_mode(scope);
            let container = tree.find_by_test_id(IVA_BASELINE_CONTAINER_TEST_ID);

            playback_mode.switch_to(PlaybackMode::Chrome(None));
            let _ = test_game_loop.tick_until_done();

            test_game_loop.send_key_down_up_event_to_node(
                container.borrow_props().node_id,
                KeyCode::Backspace,
            );
            let _ = test_game_loop.tick_until_done();

            assert_eq!(playback_mode.get(), PlaybackMode::Watch);
        });
    }

    #[test]
    fn compresses_chrome_when_iva_focused_in_chrome_mode() {
        ignx_compositron::app::launch_test(setup, |scope, mut test_game_loop| {
            let _ = test_game_loop.tick_until_done();
            let playback_mode = use_playback_mode(scope);
            let chrome_visibility_mode_context =
                use_context::<ChromeVisibilityModeContext>(scope).unwrap();
            let focus_manager = use_focus_manager(scope);

            // Set up the scenario: Chrome mode + IVA focused + not compressed
            chrome_visibility_mode_context.set(ChromeVisibilityMode::Full);
            playback_mode.switch_to(PlaybackMode::Chrome(None));
            focus_manager.set_focus(FocusLocation::IVABaseline);

            let _ = test_game_loop.tick_until_done();

            // The effect should compress the chrome mode
            assert_eq!(
                chrome_visibility_mode_context.current_mode.get(),
                ChromeVisibilityMode::Compressed
            );
        });
    }
}
