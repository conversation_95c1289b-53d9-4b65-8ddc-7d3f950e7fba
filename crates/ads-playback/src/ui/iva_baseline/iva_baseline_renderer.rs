use crate::surface_x::template_hydration::IvaBaselineTemplateData;

use super::*;
use ignx_compositron::{
    compose,
    prelude::{
        safe::{create_ref_signal, SharedSignal},
        *,
    },
    Composer,
};

#[Composer]
pub fn IVABaselineRenderer(
    ctx: &AppContext,
    template_data: SharedSignal<IvaBaselineTemplateData>,
) -> AnyComposable {
    let template_data_ref = create_ref_signal(ctx.scope(), template_data);

    compose! {
      IVABaselineContainer(template_data: template_data_ref)
    }
    .into_any()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_data::create_test_iva_baseline_template_data;
    use ignx_compositron::{
        app::launch_test, prelude::safe::SharedSignal, test_utils::assert_node_exists,
    };

    #[test]
    fn test_iva_baseline_renderer_renders_container() {
        launch_test(
            |ctx| {
                let template_data = create_test_iva_baseline_template_data();
                let template_data_signal = SharedSignal::from_data(template_data);
                compose! {
                    IVABaselineRenderer(template_data: template_data_signal)
                }
            },
            |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                assert_node_exists!(tree.find_by_test_id(IVA_BASELINE_CONTAINER_TEST_ID));
            },
        );
    }
}
