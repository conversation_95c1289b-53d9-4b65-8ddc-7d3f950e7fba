use amzn_fable_tokens::{FableColor, FableSpacing};
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::text::{LocalizedText, TextContent};
use ignx_compositron::{compose, prelude::safe::*, Composer};

// TODO: use actual enum from template
pub enum ActionType {
    ATC,
    SMM,
    STP,
}

#[Composer]
pub fn IVABaselineSuccessDetails<'s>(
    ctx: &AppContext<'s>,
    atc_headline: &str,
    cta_type: ActionType,
) -> ColumnComposable<'s> {
    match cta_type {
        ActionType::ATC => {
            let padding_top = Padding::new(0.0, 0.0, FableSpacing::FOUNDATION_SPACING035, 0.0);

            compose! {
                Column() {
                    TypographyBody200(content: TextContent::LocalizedText(LocalizedText::new(
                        "AV_LRC_IVA_CTA_CARD_SUCCESS_CONFIRMATION",
                    )))
                    .test_id("confirmation-text")

                    Row() {
                        TypographyLabel600(content: TextContent::String(atc_headline.to_string()))
                        .max_lines(Some(1))
                        .test_id("headline-text")
                    }
                    .padding(padding_top.clone())
                    .cross_axis_alignment(CrossAxisAlignment::Center)

                    Row() {
                        TypographyLabel100(content: TextContent::LocalizedText(LocalizedText::new(
                            "AV_LRC_IVA_CTA_CARD_SUCCESS_LEGAL"
                        )))
                        .color(get_ignx_color(FableColor::SECONDARY))
                        .test_id("legal-text")
                    }
                    .padding(padding_top)
                    .cross_axis_alignment(CrossAxisAlignment::Center)
                }
                .test_id("atc-container")
            }
        }
        ActionType::SMM => {
            let smm_text =
                TextContent::LocalizedText(LocalizedText::new("AV_LRC_IVA_CTA_SMM_SUCCESS_TEXT"));

            compose! {
                Column() {
                   TypographyBody200(content: smm_text)
                   .test_id("smm-text")
                }
            }
        }
        ActionType::STP => {
            let stp_text =
                TextContent::LocalizedText(LocalizedText::new("AV_LRC_IVA_CTA_STP_SUCCESS_TEXT"));

            compose! {
                Column() {
                   TypographyBody200(content: stp_text)
                   .test_id("stp-text")
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_does_not_exist, assert_node_exists},
    };
    use rstest::rstest;

    #[rstest]
    fn test_atc_action_type_renders_successfully() {
        let atc_headline = "Test ATC Headline";

        launch_test(
            |ctx| {
                compose! {
                    IVABaselineSuccessDetails(
                        atc_headline,
                        cta_type: ActionType::ATC,
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check container exists
                let container = node_tree.find_by_test_id("atc-container");
                assert_node_exists!(&container);

                // Check all text elements exist
                let confirmation_text = node_tree.find_by_test_id("confirmation-text");
                assert_node_exists!(&confirmation_text);

                let headline_text = node_tree.find_by_test_id("headline-text");
                assert_node_exists!(&headline_text);
                let headline_props = headline_text.get_props();
                assert_eq!(headline_props.text, Some("Test ATC Headline".to_string()));

                let legal_text = node_tree.find_by_test_id("legal-text");
                assert_node_exists!(&legal_text);

                // Verify other action types don't exist
                let smm_text = node_tree.find_by_test_id("smm-text");
                assert_node_does_not_exist!(&smm_text);

                let stp_text = node_tree.find_by_test_id("stp-text");
                assert_node_does_not_exist!(&stp_text);
            },
        );
    }

    #[rstest]
    fn test_smm_action_type_renders_successfully() {
        launch_test(
            |ctx| {
                compose! {
                    IVABaselineSuccessDetails(
                        atc_headline: "Test SMM Headline",
                        cta_type: ActionType::SMM,
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check SMM text exists
                let smm_text = node_tree.find_by_test_id("smm-text");
                assert_node_exists!(&smm_text);

                // Verify other action types don't exist
                let atc_container = node_tree.find_by_test_id("atc-container");
                assert_node_does_not_exist!(&atc_container);

                let stp_text = node_tree.find_by_test_id("stp-text");
                assert_node_does_not_exist!(&stp_text);
            },
        );
    }

    #[rstest]
    fn test_stp_action_type_renders_successfully() {
        launch_test(
            |ctx| {
                compose! {
                    IVABaselineSuccessDetails(
                        atc_headline: "Test STP Headline",
                        cta_type: ActionType::STP,
                    )
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                // Check STP text exists
                let stp_text = node_tree.find_by_test_id("stp-text");
                assert_node_exists!(&stp_text);

                // Verify other action types don't exist
                let atc_container = node_tree.find_by_test_id("atc-container");
                assert_node_does_not_exist!(&atc_container);

                let smm_text = node_tree.find_by_test_id("smm-text");
                assert_node_does_not_exist!(&smm_text);
            },
        );
    }
}
