use crate::metrics::error_code::ErrorCode;
use crate::metrics::event_type::EventType;
use crate::metrics::metric_name::MetricName;
#[cfg_attr(test, double)]
use crate::metrics::reporter::Reporter;
use ads_playback_types::interactive_video_ad::{
    ActionableAdExtension, AdExtensionData, SurfaceXTemplateId,
};
use ads_playback_types::iva_v4_extension::{
    IvaV4ExtensionAdParameters, IvaV4ExtensionAdParametersTempletizedTrackersOnly,
    IvaV4VodLinearExtensionAdParameters,
};
use mockall::automock;
#[cfg(test)]
use mockall_double::double;
use network_parser::prelude::*;
use player::{AdMetadata, ElementNode, LiveAds, Node, TextNode, TimelineItem};

const LOG_PREFIX: &str = "[Advertising - Parse Extensions]";
const TYPE_ATTRIBUTE_NAME: &str = "type";
const ID_ATTRIBUTE_NAME: &str = "id";
const VERSION_ATTRIBUTE_NAME: &str = "version";
const IVA_EXTENSION_TYPE: &str = "ava.amazon.com/actionable";
const IVA_ACTIONABLE_AD_NAME: &str = "ActionableAd";
const IVA_ACTIONABLE_AD_VERSION: &str = "4.0";
const IVA_AD_PARAMETERS_NAME: &str = "AdParameters";
const INTERACTIVE_CREATIVE_FILE_NAME: &str = "InteractiveCreativeFile";
const API_FRAMEWORK_ATTRIBUTE_NAME: &str = "apiFramework";
const SFX_ID: &str = "SFX";
const INVALID_AD_PARAMETERS_EVENT_TYPE_FOR_MACRO: &str = "INVALID_AD_PARAMETERS";

#[automock]
pub trait VastExtensionParser {
    fn create_vod_ads(
        &self,
        timeline_item: &TimelineItem,
        ad_metadata: &AdMetadata,
    ) -> Option<ActionableAdExtension>;
    #[allow(dead_code, reason = "not used yet")]
    fn create_live_ads(&self, live_ad: &LiveAds) -> Option<ActionableAdExtension>;
}

pub struct IvaVastExtensionParser {
    reporter: Reporter,
}

impl VastExtensionParser for IvaVastExtensionParser {
    fn create_vod_ads(
        &self,
        timeline_item: &TimelineItem,
        ad_metadata: &AdMetadata,
    ) -> Option<ActionableAdExtension> {
        if timeline_item.start_time.is_none() || timeline_item.end_time.is_none() {
            log::debug!(
                "{} Did not parse extensions for ad: {} because it did not have a valid duration",
                LOG_PREFIX,
                ad_metadata.ad_instance_id
            );
            return None;
        }
        ad_metadata
            .extensions
            .as_ref()?
            .iter()
            .find_map(|extension| {
                if !is_vod_iva_extension(extension) {
                    return None;
                }

                let actionable_ads = extension
                    .children
                    .iter()
                    .filter_map(|extension| match extension {
                        Node::ElementNode(node) if node.name == IVA_ACTIONABLE_AD_NAME => {
                            Some(node)
                        }
                        _ => None,
                    })
                    .collect();
                let actionable_ad_node = self.find_actionable_ad_extension_node(&actionable_ads)?;
                let (ad_parameters_node, template_id) =
                    self.find_necessary_nodes_from_actionable_ad(actionable_ad_node)?;

                let parameters = self.deserialise_vod_linear_ad_parameters(ad_parameters_node)?;

                let ad_extension_data = AdExtensionData::VodOrLinear { parameters };

                self.validate_and_create_actionable_ad_extension(
                    actionable_ad_node,
                    ad_extension_data,
                    template_id,
                )
            })
    }

    fn create_live_ads(&self, live_ad: &LiveAds) -> Option<ActionableAdExtension> {
        let actionable_ads: Vec<&ElementNode> = live_ad
            .extensions
            .as_ref()?
            .iter()
            .filter(|actionable_ad_node| actionable_ad_node.name == IVA_ACTIONABLE_AD_NAME)
            .collect();

        let actionable_ad_node = self.find_actionable_ad_extension_node(&actionable_ads)?;
        let (ad_parameters_node, template_id) =
            self.find_necessary_nodes_from_actionable_ad(actionable_ad_node)?;

        let parameters = self.deserialise_live_ad_parameters(ad_parameters_node)?;
        let tracking_url = live_ad.tracking_url.clone();
        let start_ms = live_ad.time_info.as_ref()?.start_ms;
        let end_ms = live_ad.time_info.as_ref()?.end_ms;

        let ad_extension_data = AdExtensionData::Live {
            parameters,
            tracking_url,
            start_ms,
            end_ms,
        };

        self.validate_and_create_actionable_ad_extension(
            actionable_ad_node,
            ad_extension_data,
            template_id,
        )
    }
}

impl IvaVastExtensionParser {
    pub fn new(reporter: Reporter) -> Self {
        Self { reporter }
    }

    fn find_actionable_ad_extension_node<'a>(
        &self,
        actionable_ads: &'a Vec<&ElementNode>,
    ) -> Option<&'a ElementNode> {
        let actionable_ads_count = actionable_ads.len();

        if actionable_ads_count == 0 {
            self.report_validation_error(ErrorCode::InvalidActionableAd);
            log::error!("{} No IVA extensions found", LOG_PREFIX);
            return None;
        }

        if actionable_ads_count > 1 {
            self.report_validation_error(ErrorCode::TooManyIvaExtensions);
            log::error!("{} Too many IVA extensions found", LOG_PREFIX);
            return None;
        }

        let actionable_ad = actionable_ads.first()?;

        let attributes = &actionable_ad.attributes;

        if !attributes.contains_key(ID_ATTRIBUTE_NAME)
            || !attributes.contains_key(VERSION_ATTRIBUTE_NAME)
        {
            self.report_validation_error(ErrorCode::InvalidActionableAd);
            log::error!("{} Missing required attributes for extension", LOG_PREFIX);
            return None;
        }

        let version = attributes.get(VERSION_ATTRIBUTE_NAME)?;

        let is_valid_version = version == IVA_ACTIONABLE_AD_VERSION;

        self.reporter.metric(
            MetricName::VastExtensionInvalidVersion,
            if is_valid_version { 0 } else { 1 },
            vec![],
        );

        if !is_valid_version {
            log::error!("{} Invalid extension version: {}", LOG_PREFIX, version);
            self.report_validation_error(ErrorCode::InvalidActionableAd);
            return None;
        }

        Some(*actionable_ad)
    }

    fn parse_interactive_creative_file(
        &self,
        actionable_ad: &ElementNode,
    ) -> Option<SurfaceXTemplateId> {
        let creative_files: Vec<_> = actionable_ad
            .children
            .iter()
            .filter_map(|child| match child {
                Node::ElementNode(node) if node.name == INTERACTIVE_CREATIVE_FILE_NAME => {
                    Some(node)
                }
                _ => None,
            })
            .collect();

        let creative_files_count = creative_files.len();

        if creative_files_count == 0 {
            self.report_validation_error(ErrorCode::NoInteractiveCreativeFile);
            log::error!("{} No InteractiveCreativeFile found", LOG_PREFIX);
            return None;
        }

        if creative_files_count > 1 {
            self.report_validation_error(ErrorCode::InvalidActionableAd);
            log::error!("{} Too many InteractiveCreativeFile found", LOG_PREFIX);
            return None;
        }

        let creative_file = *creative_files.first()?;

        let is_sfx_api_framework = creative_file
            .attributes
            .get(API_FRAMEWORK_ATTRIBUTE_NAME)
            .is_some_and(|api_framework| api_framework == SFX_ID);

        if !is_sfx_api_framework {
            return None;
        }

        let text_child = creative_file
            .children
            .first()
            .and_then(|child| match child {
                Node::TextNode(text) => Some(text),
                Node::ElementNode(_) => None,
            })?;

        match network_parse_from_str::<SurfaceXTemplateId>(&text_child.text) {
            Ok(template_id) => Some(template_id),
            Err(error) => {
                log::error!(
                    "{} Missing or invalid InteractiveCreativeFile. JSON: {}. Parsing Error: {}",
                    LOG_PREFIX,
                    text_child.text,
                    error
                );
                self.report_validation_error(ErrorCode::InvalidInteractiveCreativeFile);
                None
            }
        }
    }

    fn find_ad_parameters_node<'a>(&self, actionable_ad: &'a ElementNode) -> Option<&'a TextNode> {
        let ad_parameter_nodes: Vec<&ElementNode> = actionable_ad
            .children
            .iter()
            .filter_map(|child| match child {
                Node::ElementNode(node)
                    if node.name == IVA_AD_PARAMETERS_NAME && !node.children.is_empty() =>
                {
                    Some(node)
                }
                _ => None,
            })
            .collect();

        let ad_parameter_nodes_count = ad_parameter_nodes.len();

        if ad_parameter_nodes_count == 0 {
            self.report_validation_error(ErrorCode::NoAdParameters);
            log::error!("{} No Ad Parameters found", LOG_PREFIX);
            return None;
        }

        if ad_parameter_nodes_count > 1 {
            log::error!("{} Too many AdParameters found", LOG_PREFIX);
            return None;
        }

        ad_parameter_nodes.first().and_then(|ad_parameter_node| {
            match ad_parameter_node.children.first() {
                Some(node) => match node {
                    Node::ElementNode(_) => {
                        log::error!("{} Invalid AdParameters child type", LOG_PREFIX);
                        None
                    }
                    Node::TextNode(text_node) => Some(text_node),
                },
                None => None,
            }
        })
    }

    fn deserialise_vod_linear_ad_parameters(
        &self,
        ad_parameters: &TextNode,
    ) -> Option<IvaV4VodLinearExtensionAdParameters> {
        let result =
            network_parse_from_str::<IvaV4VodLinearExtensionAdParameters>(&ad_parameters.text);

        if let Err(error) = &result {
            log::error!("{} Deserialisation failed: {}", LOG_PREFIX, error);
            self.report_ad_parameters_parsing_failure(ad_parameters);
        }

        result.ok()
    }

    fn deserialise_live_ad_parameters(
        &self,
        ad_parameters: &TextNode,
    ) -> Option<IvaV4ExtensionAdParameters> {
        let result = network_parse_from_str::<IvaV4ExtensionAdParameters>(&ad_parameters.text);

        if let Err(error) = &result {
            log::error!("{} Deserialisation failed: {}", LOG_PREFIX, error);
            self.report_ad_parameters_parsing_failure(ad_parameters);
        }

        result.ok()
    }

    fn validate_and_create_actionable_ad_extension(
        &self,
        actionable_ad_node: &ElementNode,
        ad_extension_data: AdExtensionData,
        template_id: SurfaceXTemplateId,
    ) -> Option<ActionableAdExtension> {
        if !self.are_tracker_urls_valid(&ad_extension_data) {
            return None;
        }

        let id = actionable_ad_node
            .attributes
            .get(ID_ATTRIBUTE_NAME)?
            .clone();

        let version = actionable_ad_node
            .attributes
            .get(VERSION_ATTRIBUTE_NAME)?
            .clone();

        self.reporter
            .metric(MetricName::VastValidationFailure, 0, vec![]);

        Some(ActionableAdExtension {
            id,
            version,
            template_id,
            data: ad_extension_data,
        })
    }

    fn report_validation_error(&self, error_code: ErrorCode) {
        self.reporter
            .metric(MetricName::VastValidationFailure, 1, vec![]);
        self.reporter
            .player_event("todo".to_string(), error_code, EventType::VastParsingError);
    }

    fn report_ad_parameters_parsing_failure(&self, ad_parameters: &TextNode) {
        self.reporter
            .metric(MetricName::VastValidationFailure, 1, vec![]);

        self.reporter.player_event(
            "todo".to_string(),
            ErrorCode::InvalidAdParameters,
            EventType::VastParsingError,
        );

        let result = network_parse_from_str::<IvaV4ExtensionAdParametersTempletizedTrackersOnly>(
            &ad_parameters.text,
        );

        match result {
            Ok(params) => {
                self.reporter.ad_tracking(
                    params.trackers.template,
                    INVALID_AD_PARAMETERS_EVENT_TYPE_FOR_MACRO.to_string(),
                    Some(ErrorCode::InvalidAdParameters),
                    None,
                    None,
                );
            }
            Err(error) => {
                log::warn!(
                    "{} Failed to parse templated trackers: {}",
                    LOG_PREFIX,
                    error
                );
            }
        }
    }

    fn are_tracker_urls_valid(&self, ad_extension_data: &AdExtensionData) -> bool {
        let (tt, t) = match ad_extension_data {
            AdExtensionData::Live { parameters, .. } => {
                (&parameters.trackers, &parameters.tracker_parameters)
            }
            AdExtensionData::VodOrLinear { parameters, .. } => (
                &parameters.base.trackers,
                &parameters.base.tracker_parameters,
            ),
        };

        let is_tracker_event_valid = !tt.events.is_empty();
        let is_tracker_template_valid = !tt.template.is_empty();
        let is_iva_first_click_valid = t
            .get("ivaFirstClick")
            .and_then(|trackers| trackers.first())
            .map(|tracker| !tracker.is_empty())
            .unwrap_or_default();

        let is_valid =
            is_tracker_event_valid && is_tracker_template_valid && is_iva_first_click_valid;

        if !is_valid {
            log::error!("{} Missing tracker url", LOG_PREFIX);
            self.reporter
                .metric(MetricName::IvaTrackingMissingTracking, 1, vec![]);
        }

        is_valid
    }

    fn find_necessary_nodes_from_actionable_ad<'a>(
        &self,
        actionable_ad_node: &'a ElementNode,
    ) -> Option<(&'a TextNode, SurfaceXTemplateId)> {
        let ad_parameters_node = self.find_ad_parameters_node(actionable_ad_node)?;
        let interactive_creative_file = self.parse_interactive_creative_file(actionable_ad_node)?;
        Some((ad_parameters_node, interactive_creative_file))
    }
}

fn is_vod_iva_extension(extension: &ElementNode) -> bool {
    let is_iva_extension = extension
        .attributes
        .get(TYPE_ATTRIBUTE_NAME)
        .map(|extension_type| extension_type == IVA_EXTENSION_TYPE)
        .unwrap_or_default();

    let has_children = !extension.children.is_empty();

    is_iva_extension && has_children
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::metrics::reporter::MockReporter;
    use crate::test_data::create_ad_metadata;
    use ads_playback_types::test_data::{
        ADD_TO_CART_AD_PARAMETERS, SEND_ME_MORE_AD_PARAMETERS, SEND_TO_PHONE_AD_PARAMETERS,
    };
    use mockall::predicate;
    use player::test_utils::TimelineItemBuilder;
    use rstest::*;
    use std::collections::HashMap;

    const INVALID_JSON: &str = "{invalid json}";

    fn create_sample_valid_extension() -> ElementNode {
        ElementNode {
            name: "Extension".to_string(),
            attributes: {
                let mut map = HashMap::new();
                map.insert(
                    TYPE_ATTRIBUTE_NAME.to_string(),
                    IVA_EXTENSION_TYPE.to_string(),
                );
                map
            },
            children: vec![Node::ElementNode(ElementNode {
                name: IVA_ACTIONABLE_AD_NAME.to_string(),
                attributes: {
                    let mut map = HashMap::new();
                    map.insert(
                        VERSION_ATTRIBUTE_NAME.to_string(),
                        IVA_ACTIONABLE_AD_VERSION.to_string(),
                    );
                    map.insert(
                        ID_ATTRIBUTE_NAME.to_string(),
                        "Eau1gb2VuqsqJkXfuI9oWQ".to_string(),
                    );
                    map
                },
                children: vec![
                    Node::ElementNode(ElementNode {
                        name: "InteractiveCreativeFile".to_string(),
                        attributes: {
                            let mut map = HashMap::new();
                            map.insert("apiFramework".to_string(), SFX_ID.to_string());
                            map
                        },
                        children: vec![Node::TextNode(TextNode {
                            text: r#"{"tid": "IVA", "m": "1", "mq": "0"}"#.to_string(),
                        })],
                    }),
                    Node::ElementNode(ElementNode {
                        name: IVA_AD_PARAMETERS_NAME.to_string(),
                        attributes: HashMap::new(),
                        children: vec![Node::TextNode(TextNode {
                            text: ADD_TO_CART_AD_PARAMETERS.to_string(),
                        })],
                    }),
                ],
            })],
        }
    }

    fn create_actionable_ad(version: &str) -> ElementNode {
        ElementNode {
            name: IVA_ACTIONABLE_AD_NAME.to_string(),
            attributes: {
                let mut map = HashMap::new();
                map.insert(ID_ATTRIBUTE_NAME.to_string(), "test-id".to_string());
                map.insert(VERSION_ATTRIBUTE_NAME.to_string(), version.to_string());
                map
            },
            children: vec![],
        }
    }

    fn create_ad_parameters(parameters: &str) -> ElementNode {
        ElementNode {
            name: IVA_AD_PARAMETERS_NAME.to_string(),
            children: vec![Node::TextNode(TextNode {
                text: parameters.to_string(),
            })],
            attributes: HashMap::new(),
        }
    }

    #[rstest]
    fn test_is_iva_extension_valid() {
        let extension = create_sample_valid_extension();
        assert!(is_vod_iva_extension(&extension));
    }

    #[rstest]
    fn test_is_iva_extension_invalid_type() {
        let mut extension = create_sample_valid_extension();
        extension.attributes.insert(
            TYPE_ATTRIBUTE_NAME.to_string(),
            "invalid.extension.type".to_string(),
        );
        assert!(!is_vod_iva_extension(&extension));
    }

    #[rstest]
    fn test_is_iva_extension_missing_type() {
        let extension = ElementNode {
            name: "Extension".to_string(),
            attributes: HashMap::new(),
            children: vec![],
        };
        assert!(!is_vod_iva_extension(&extension));
    }

    #[rstest]
    fn test_find_actionable_ad_extension_node_version_success() {
        let actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let actionable_ads = vec![&actionable_ad];

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastExtensionInvalidVersion),
                predicate::eq(0),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_actionable_ad_extension_node(&actionable_ads);
        assert!(result.is_some());
    }

    #[rstest]
    #[case("3.0")]
    #[case("5.0")]
    fn test_find_actionable_ad_extension_node_version_failure(#[case] version: &str) {
        let actionable_ad = create_actionable_ad(version);
        let actionable_ads = vec![&actionable_ad];

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastExtensionInvalidVersion),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());

        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());

        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::InvalidActionableAd),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_actionable_ad_extension_node(&actionable_ads);
        assert!(result.is_none());
    }

    #[rstest]
    fn test_find_actionable_ad_extension_node_multiple_ads() {
        let actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let actionable_ads = vec![&actionable_ad, &actionable_ad];

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::TooManyIvaExtensions),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_actionable_ad_extension_node(&actionable_ads);
        assert!(result.is_none());
    }

    #[rstest]
    fn test_find_actionable_ad_extension_node_missing_attributes() {
        let actionable_ad = ElementNode {
            name: IVA_ACTIONABLE_AD_NAME.to_string(),
            attributes: HashMap::new(),
            children: vec![],
        };
        let actionable_ads = vec![&actionable_ad];

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::InvalidActionableAd),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_actionable_ad_extension_node(&actionable_ads);
        assert!(result.is_none());
    }

    #[rstest]
    fn test_find_ad_parameters_node_valid() {
        let mut actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let ad_parameters = create_ad_parameters(ADD_TO_CART_AD_PARAMETERS);
        actionable_ad.children = vec![Node::ElementNode(ad_parameters)];

        let mock_reporter = MockReporter::default();
        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_ad_parameters_node(&actionable_ad);
        assert!(result.is_some());
    }

    #[rstest]
    fn test_find_ad_parameters_node_multiple_parameters() {
        let mut actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let ad_parameters = create_ad_parameters(ADD_TO_CART_AD_PARAMETERS);
        actionable_ad.children = vec![
            Node::ElementNode(ad_parameters.clone()),
            Node::ElementNode(ad_parameters),
        ];

        let mock_reporter = MockReporter::default();
        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_ad_parameters_node(&actionable_ad);
        assert!(result.is_none());
    }

    #[rstest]
    fn test_find_ad_parameters_node_empty_parameters() {
        let mut actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let ad_parameters = ElementNode {
            name: IVA_AD_PARAMETERS_NAME.to_string(),
            children: vec![],
            attributes: HashMap::new(),
        };
        actionable_ad.children = vec![Node::ElementNode(ad_parameters)];

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::NoAdParameters),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_ad_parameters_node(&actionable_ad);
        assert!(result.is_none());
    }

    #[rstest]
    #[case(ADD_TO_CART_AD_PARAMETERS)]
    #[case(SEND_ME_MORE_AD_PARAMETERS)]
    #[case(SEND_TO_PHONE_AD_PARAMETERS)]
    fn test_deserialise_vod_linear_ad_parameters(#[case] parameters: &str) {
        let text_node = TextNode {
            text: parameters.to_string(),
        };

        let mock_reporter = MockReporter::default();
        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.deserialise_vod_linear_ad_parameters(&text_node);
        assert!(result.is_some());
    }

    #[test]
    fn test_deserialise_vod_linear_ad_parameters_invalid() {
        let text_node = TextNode {
            text: INVALID_JSON.to_string(),
        };

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::InvalidAdParameters),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.deserialise_vod_linear_ad_parameters(&text_node);
        assert!(result.is_none());
    }

    #[rstest]
    #[case(ADD_TO_CART_AD_PARAMETERS)]
    #[case(SEND_ME_MORE_AD_PARAMETERS)]
    #[case(SEND_TO_PHONE_AD_PARAMETERS)]
    fn test_deserialise_live_ad_parameters(#[case] parameters: &str) {
        let text_node = TextNode {
            text: parameters.to_string(),
        };

        let mock_reporter = MockReporter::default();
        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.deserialise_live_ad_parameters(&text_node);
        assert!(result.is_some());
    }

    #[test]
    fn test_deserialise_live_ad_parameters_failure() {
        let text_node = TextNode {
            text: INVALID_JSON.to_string(),
        };

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::InvalidAdParameters),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.deserialise_live_ad_parameters(&text_node);
        assert!(result.is_none());
    }

    #[rstest]
    #[case(ADD_TO_CART_AD_PARAMETERS)]
    #[case(SEND_ME_MORE_AD_PARAMETERS)]
    #[case(SEND_TO_PHONE_AD_PARAMETERS)]
    fn test_create_vod_linear_ads_success(#[case] ad_parameters: &str) {
        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(0),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastExtensionInvalidVersion),
                predicate::eq(0),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let ad_metadata = create_ad_metadata("ad-instance-1", ad_parameters);

        let timeline_item = TimelineItemBuilder::default()
            .start_time(0)
            .end_time(30)
            .build();

        let result = parser.create_vod_ads(&timeline_item, &ad_metadata);

        assert!(result.is_some());
    }

    #[rstest]
    #[case(ADD_TO_CART_AD_PARAMETERS)]
    #[case(SEND_ME_MORE_AD_PARAMETERS)]
    #[case(SEND_TO_PHONE_AD_PARAMETERS)]
    fn test_create_live_ads_success(#[case] ad_parameters: &str) {
        use crate::test_data::create_live_ad;

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(0),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastExtensionInvalidVersion),
                predicate::eq(0),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);
        let live_ad = create_live_ad("ad-instance-1", ad_parameters);
        let result = parser.create_live_ads(&live_ad);
        assert!(result.is_some());
    }

    #[rstest]
    fn test_parse_interactive_creative_file_success() {
        let mut actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let creative_file = ElementNode {
            name: INTERACTIVE_CREATIVE_FILE_NAME.to_string(),
            attributes: {
                let mut map = HashMap::new();
                map.insert(API_FRAMEWORK_ATTRIBUTE_NAME.to_string(), SFX_ID.to_string());
                map
            },
            children: vec![Node::TextNode(TextNode {
                text: r#"{"tid": "IVA", "m": 1, "mq": 1}"#.to_string(),
            })],
        };
        actionable_ad.children = vec![Node::ElementNode(creative_file)];

        let mock_reporter = MockReporter::default();
        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.parse_interactive_creative_file(&actionable_ad);
        assert!(result.is_some());

        let template_id = result.expect("Expected template ID");
        assert_eq!(template_id.id, "IVA");
        assert_eq!(template_id.major_version, 1);
        assert_eq!(template_id.minor_version, 1);
    }

    #[rstest]
    fn test_find_actionable_ad_extension_node_no_actionable_ads() {
        let actionable_ads = vec![];

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastExtensionInvalidVersion),
                predicate::eq(0),
                predicate::eq(vec![]),
            )
            .times(0..=1)
            .return_const(());
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::InvalidActionableAd),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_actionable_ad_extension_node(&actionable_ads);
        assert!(result.is_none());
    }

    #[rstest]
    fn test_find_ad_parameters_node_no_children() {
        let mut actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let mut ad_parameters = create_ad_parameters(ADD_TO_CART_AD_PARAMETERS);
        ad_parameters.children = vec![];
        actionable_ad.children = vec![Node::ElementNode(ad_parameters)];

        let mut mock_reporter = MockReporter::default();
        mock_reporter
            .expect_metric()
            .with(
                predicate::eq(MetricName::VastValidationFailure),
                predicate::eq(1),
                predicate::eq(vec![]),
            )
            .times(1)
            .return_const(());
        mock_reporter
            .expect_player_event()
            .with(
                predicate::eq("todo".to_string()),
                predicate::eq(ErrorCode::NoAdParameters),
                predicate::eq(EventType::VastParsingError),
            )
            .times(1)
            .return_const(());

        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_ad_parameters_node(&actionable_ad);
        assert!(result.is_none());
    }

    #[rstest]
    fn test_find_ad_parameters_node_element_child() {
        let mut actionable_ad = create_actionable_ad(IVA_ACTIONABLE_AD_VERSION);
        let mut ad_parameters = create_ad_parameters(ADD_TO_CART_AD_PARAMETERS);
        ad_parameters.children = vec![Node::ElementNode(ElementNode {
            name: "SomeElement".to_string(),
            attributes: HashMap::new(),
            children: vec![],
        })];
        actionable_ad.children = vec![Node::ElementNode(ad_parameters)];

        let mock_reporter = MockReporter::default();
        let parser = IvaVastExtensionParser::new(mock_reporter);

        let result = parser.find_ad_parameters_node(&actionable_ad);
        assert!(result.is_none());
    }
}
