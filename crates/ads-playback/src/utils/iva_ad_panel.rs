use crate::utils::panel_buildable::{PanelBuildable, PanelState};
use ads_playback_types::interactive_video_ad::{AdInstanceId, InteractiveVideoAd, TemplateId};
use ads_playback_types::surface_x::SurfaceXTemplate;
use ignx_compositron::reactive::StoredValue;
use playback_feature_manager::{FeatureName, Panel, PanelName, PanelsController};
use std::collections::HashSet;

pub struct IvaAdPanel {
    panels_controller: PanelsController,
    panel_state: PanelState,
    panel_name: PanelName,
    ad_instance_id: AdInstanceId,
    is_visible: bool,
}

const AD_PANEL_LOG_PREFIX: &str = "[IvaAdPanel]";

impl IvaAdPanel {
    pub fn new(
        panels_controller: PanelsController,
        panel_state: PanelState,
        panel_name: PanelName,
        ad_instance_id: AdInstanceId,
    ) -> Self {
        Self {
            panels_controller,
            panel_state,
            panel_name,
            ad_instance_id,
            is_visible: false,
        }
    }

    pub fn show(&mut self) {
        if self.is_visible {
            return;
        }
        match self.panels_controller.show_panel_by_name(&self.panel_name) {
            Some(_) => {
                log::info!("{} Successfully shown panel", AD_PANEL_LOG_PREFIX);
                self.is_visible = true;
            }
            None => {
                log::error!("{} Failed to show panel", AD_PANEL_LOG_PREFIX);
            }
        }
    }

    pub fn hide(&mut self) {
        if !self.is_visible {
            return;
        }
        match self.panels_controller.hide_panel_by_name(&self.panel_name) {
            Some(_) => {
                log::info!("{} Successfully hidden panel", AD_PANEL_LOG_PREFIX);
                self.is_visible = false;
            }
            None => {
                log::error!("{} Failed to hide panel", AD_PANEL_LOG_PREFIX);
            }
        }
    }

    pub fn ad_instance_id(&self) -> &str {
        self.ad_instance_id.as_str()
    }
}

impl Drop for IvaAdPanel {
    fn drop(&mut self) {
        if self
            .panels_controller
            .deregister(&self.panel_name)
            .is_none()
        {
            log::error!("{} Failed to deregister panel", AD_PANEL_LOG_PREFIX);
        }
    }
}

const CREATE_AD_PANEL_LOG_PREFIX: &str = "[create_iva_ad_panel]";

pub fn create_iva_ad_panel(
    feature_name: FeatureName,
    panel_name: PanelName,
    panels_controller: PanelsController,
    timeline_ads: StoredValue<HashSet<InteractiveVideoAd>>,
    template_cache: StoredValue<lru::LruCache<TemplateId, SurfaceXTemplate>>,
    ad_instance_id: &AdInstanceId,
) -> Option<IvaAdPanel> {
    timeline_ads.with_value(|ads| {
        let Some(interactive_video_ad) = ads.get(ad_instance_id) else {
            log::error!(
                "{} Ad instance id must correspond to a valid iva",
                CREATE_AD_PANEL_LOG_PREFIX
            );
            return None;
        };

        template_cache.try_update_value(|cache| {
            let Some(template) = cache.get(&interactive_video_ad.template_id()) else {
                log::error!(
                    "{} A template does not exist for this iva",
                    CREATE_AD_PANEL_LOG_PREFIX
                );
                return None;
            };

            let Some(panel_state) = template.panel_state(interactive_video_ad) else {
                log::error!(
                    "{} Failed to create panel render state",
                    CREATE_AD_PANEL_LOG_PREFIX
                );
                return None;
            };

            panels_controller
                .register(Panel {
                    panel_name,
                    group: template.panel_group(),
                    feature_name,
                    auto_hide_timeout: None,
                    component: template.panel_component(&panel_state),
                    tab: None,
                    on_hidden: None,
                    on_shown: None,
                })
                .map_err(|error| {
                    log::error!(
                        "{} Failed to register panel: {:?}",
                        CREATE_AD_PANEL_LOG_PREFIX,
                        error
                    )
                })
                .ok()?;

            Some(IvaAdPanel::new(
                panels_controller,
                panel_state,
                panel_name,
                ad_instance_id.clone(),
            ))
        })
    })?
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_data::{
        create_test_atc_interactive_video_ad, create_test_iva_baseline_template_data, TestAdType,
    };
    use crate::test_utils::setup_feature_controllers_with_mock_contexts;
    use ads_playback_types::test_data::MINIMAL_BASELINE_IVA_TEMPLATE;
    use ignx_compositron::app::launch_only_app_context;

    use ignx_compositron::prelude::AppContext;
    use ignx_compositron::reactive::store_value;
    use ignx_compositron::shared::Shared;
    use network_parser::core::network_parse_from_str;
    use playback_feature_manager::test_utils::PanelBuildable;
    use playback_feature_manager::{FeatureName, PanelsController};
    use std::collections::HashSet;

    fn create_test_template() -> SurfaceXTemplate {
        network_parse_from_str(MINIMAL_BASELINE_IVA_TEMPLATE)
            .expect("Should be able to deserialize")
    }

    fn create_test_panels_controller(ctx: &AppContext) -> PanelsController {
        setup_feature_controllers_with_mock_contexts(ctx).panels_controller
    }

    fn create_test_interactive_video_ad(id: &str) -> InteractiveVideoAd {
        create_test_atc_interactive_video_ad(TestAdType::VodLinear, id)
    }

    fn create_test_lru_cache() -> lru::LruCache<TemplateId, SurfaceXTemplate> {
        lru::LruCache::new(std::num::NonZeroUsize::new(10).unwrap())
    }

    fn create_fake_panel_state() -> PanelState {
        PanelState::BaselineIVA(Shared::new(create_test_iva_baseline_template_data()))
    }

    #[test]
    fn test_ad_panel_new() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_123".to_string();
            let fake_panel_state = create_fake_panel_state();

            let ad_panel = IvaAdPanel::new(
                panels_controller,
                fake_panel_state,
                panel_name,
                ad_instance_id.clone(),
            );

            assert_eq!(ad_panel.panel_name, panel_name);
            assert_eq!(ad_panel.ad_instance_id, ad_instance_id);
            assert!(!ad_panel.is_visible);
        });
    }

    #[test]
    fn test_ad_panel_ad_instance_id() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_456".to_string();
            let fake_panel_state = create_fake_panel_state();

            let ad_panel = IvaAdPanel::new(
                panels_controller,
                fake_panel_state,
                panel_name,
                ad_instance_id.clone(),
            );

            assert_eq!(ad_panel.ad_instance_id(), &ad_instance_id);
        });
    }

    #[test]
    fn test_ad_panel_show_when_not_visible() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_show".to_string();
            let fake_panel_state = create_fake_panel_state();

            let panel = Panel::builder()
                .panel_name(panel_name)
                .feature_name(FeatureName::InteractiveVideoAdsVod)
                .build();
            let _ = panels_controller.register(panel);

            let mut ad_panel = IvaAdPanel::new(
                panels_controller,
                fake_panel_state,
                panel_name,
                ad_instance_id,
            );

            assert!(!ad_panel.is_visible);
            ad_panel.show();
            assert!(ad_panel.is_visible);
        });
    }

    #[test]
    fn test_ad_panel_show_when_already_visible() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_show_visible".to_string();
            let fake_panel_state = create_fake_panel_state();

            let panel = Panel::builder()
                .panel_name(panel_name)
                .feature_name(FeatureName::InteractiveVideoAdsVod)
                .build();
            let _ = panels_controller.register(panel);

            let mut ad_panel = IvaAdPanel::new(
                panels_controller,
                fake_panel_state,
                panel_name,
                ad_instance_id,
            );
            ad_panel.is_visible = true;

            ad_panel.show();
            assert!(ad_panel.is_visible);
        });
    }

    #[test]
    fn test_ad_panel_show_panel_not_found() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_not_found".to_string();
            let fake_panel_state = create_fake_panel_state();

            let mut ad_panel = IvaAdPanel::new(
                panels_controller,
                fake_panel_state,
                panel_name,
                ad_instance_id,
            );

            assert!(!ad_panel.is_visible);
            ad_panel.show();
            assert!(!ad_panel.is_visible);
        });
    }

    #[test]
    fn test_ad_panel_hide_when_visible() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_hide".to_string();
            let fake_panel_state = create_fake_panel_state();

            let panel = Panel::builder()
                .panel_name(panel_name)
                .feature_name(FeatureName::InteractiveVideoAdsVod)
                .build();
            let _ = panels_controller.register(panel);

            let mut ad_panel = IvaAdPanel::new(
                panels_controller,
                fake_panel_state,
                panel_name,
                ad_instance_id,
            );
            ad_panel.is_visible = true;

            ad_panel.hide();
            assert!(!ad_panel.is_visible);
        });
    }

    #[test]
    fn test_ad_panel_hide_when_not_visible() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_hide_not_visible".to_string();
            let fake_panel_state = create_fake_panel_state();

            let mut ad_panel = IvaAdPanel::new(
                panels_controller,
                fake_panel_state,
                panel_name,
                ad_instance_id,
            );

            assert!(!ad_panel.is_visible);
            ad_panel.hide();
            assert!(!ad_panel.is_visible);
        });
    }

    #[test]
    fn test_ad_panel_drop() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_drop".to_string();
            let fake_panel_state = create_fake_panel_state();

            let panel = Panel::builder()
                .panel_name(panel_name)
                .feature_name(FeatureName::InteractiveVideoAdsVod)
                .build();
            let _ = panels_controller.register(panel);

            {
                let _ad_panel = IvaAdPanel::new(
                    panels_controller,
                    fake_panel_state,
                    panel_name,
                    ad_instance_id,
                );
            }
        });
    }

    #[test]
    fn test_ad_panel_drop_deregister_fails() {
        launch_only_app_context(move |ctx| {
            let panels_controller = create_test_panels_controller(&ctx);
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let ad_instance_id = "test_ad_drop_fail".to_string();
            let fake_panel_state = create_fake_panel_state();
            {
                let _ad_panel = IvaAdPanel::new(
                    panels_controller,
                    fake_panel_state,
                    panel_name,
                    ad_instance_id,
                );
            }
        });
    }

    #[test]
    fn test_create_ad_panel_ad_not_found() {
        launch_only_app_context(move |ctx| {
            let feature_name = FeatureName::InteractiveVideoAdsVod;
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let panels_controller = create_test_panels_controller(&ctx);
            let timeline_ads = store_value(ctx.scope(), HashSet::new());
            let template_cache = store_value(ctx.scope(), create_test_lru_cache());
            let ad_instance_id = "non_existent_ad".to_string();

            let result = create_iva_ad_panel(
                feature_name,
                panel_name,
                panels_controller,
                timeline_ads,
                template_cache,
                &ad_instance_id,
            );

            assert!(result.is_none());
        });
    }

    #[test]
    fn test_create_ad_panel_template_not_found() {
        launch_only_app_context(move |ctx| {
            let feature_name = FeatureName::InteractiveVideoAdsVod;
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let panels_controller = create_test_panels_controller(&ctx);
            let ad_instance_id = "test_ad_template_missing".to_string();

            let mut ads_set = HashSet::new();
            let test_ad = create_test_interactive_video_ad(&ad_instance_id);
            ads_set.insert(test_ad);
            let timeline_ads = store_value(ctx.scope(), ads_set);

            let template_cache = store_value(ctx.scope(), create_test_lru_cache());

            let result = create_iva_ad_panel(
                feature_name,
                panel_name,
                panels_controller,
                timeline_ads,
                template_cache,
                &ad_instance_id,
            );

            assert!(result.is_none());
        });
    }

    #[test]
    fn test_create_ad_panel_success() {
        launch_only_app_context(move |ctx| {
            let feature_name = FeatureName::InteractiveVideoAdsVod;
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let panels_controller = create_test_panels_controller(&ctx);
            let ad_instance_id = "test_ad_success".to_string();

            let mut ads_set = HashSet::new();
            let test_ad = create_test_interactive_video_ad(&ad_instance_id);
            ads_set.insert(test_ad);
            let timeline_ads = store_value(ctx.scope(), ads_set);

            let mut cache = create_test_lru_cache();
            let template = create_test_template();
            let template_id = "IVA#1.0".to_string();
            cache.put(template_id, template);
            let template_cache = store_value(ctx.scope(), cache);

            let result = create_iva_ad_panel(
                feature_name,
                panel_name,
                panels_controller,
                timeline_ads,
                template_cache,
                &ad_instance_id,
            );

            assert!(result.is_some());

            let ad_panel = result.unwrap();
            assert_eq!(ad_panel.ad_instance_id(), &ad_instance_id);
            assert!(!ad_panel.is_visible);
        });
    }

    #[test]
    fn test_create_ad_panel_component_creation_success() {
        launch_only_app_context(move |ctx| {
            let feature_name = FeatureName::InteractiveVideoAdsVod;
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let panels_controller = create_test_panels_controller(&ctx);
            let ad_instance_id = "test_ad_component_success".to_string();

            let mut ads_set = HashSet::new();
            let test_ad = create_test_interactive_video_ad(&ad_instance_id);
            ads_set.insert(test_ad);
            let timeline_ads = store_value(ctx.scope(), ads_set);

            let mut cache = create_test_lru_cache();
            let template = create_test_template();
            let template_id = "IVA#1.0".to_string();
            cache.put(template_id, template);
            let template_cache = store_value(ctx.scope(), cache);

            let result = create_iva_ad_panel(
                feature_name,
                panel_name,
                panels_controller,
                timeline_ads,
                template_cache,
                &ad_instance_id,
            );

            assert!(result.is_some());
        });
    }

    #[test]
    fn test_create_ad_panel_registration_fails() {
        launch_only_app_context(move |ctx| {
            let feature_name = FeatureName::InteractiveVideoAdsVod;
            let panel_name = PanelName::InteractiveVideoAdsVod;
            let panels_controller = create_test_panels_controller(&ctx);
            let ad_instance_id = "test_ad_registration_fail".to_string();

            let conflicting_panel = Panel::builder()
                .panel_name(panel_name)
                .feature_name(feature_name)
                .build();
            let _ = panels_controller.register(conflicting_panel);

            let mut ads_set = HashSet::new();
            let test_ad = create_test_interactive_video_ad(&ad_instance_id);
            ads_set.insert(test_ad);
            let timeline_ads = store_value(ctx.scope(), ads_set);

            let mut cache = create_test_lru_cache();
            let template = create_test_template();
            let template_id = "IVA#1.0".to_string();
            cache.put(template_id, template);
            let template_cache = store_value(ctx.scope(), cache);

            let result = create_iva_ad_panel(
                feature_name,
                panel_name,
                panels_controller,
                timeline_ads,
                template_cache,
                &ad_instance_id,
            );

            assert!(result.is_none());
        });
    }
}
