use crate::surface_x::template_hydration::{
    hydrate_iva_baseline_template, IvaBaselineTemplateData,
};

use crate::ui::iva_baseline::*;
use ads_playback_types::interactive_video_ad::InteractiveVideoAd;
use ads_playback_types::surface_x::ad_feedback::AdFeedbackTemplate;
use ads_playback_types::surface_x::ad_formats::baseline_iva::{BaselineIVATemplate, CardPosition};
use ads_playback_types::surface_x::SurfaceXTemplate;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::safe::SharedSignal;
use ignx_compositron::prelude::*;
use ignx_compositron::shared::Shared;
use ignx_compositron::{compose, compose_option};
use playback_feature_manager::{OverlayPanelPosition, PanelGroup};

#[derive(Clone)]
pub enum PanelState {
    BaselineIVA(Shared<IvaBaselineTemplateData>),
    AdFeedback(Shared<IvaBaselineTemplateData>), // Todo: Ad feedback to add their state
}

pub trait PanelBuildable {
    fn panel_group(&self) -> PanelGroup;

    fn panel_state(&self, interactive_video_ad: &InteractiveVideoAd) -> Option<PanelState>;

    fn panel_component(
        &self,
        panel_render_state: &PanelState,
    ) -> Box<dyn Fn(&AppContext) -> AnyComposable + 'static>;
}

impl PanelBuildable for BaselineIVATemplate {
    fn panel_group(&self) -> PanelGroup {
        const DEFAULT_PANEL_GROUP: PanelGroup = PanelGroup::Overlay(OverlayPanelPosition::Start);
        self.template_props
            .card_position
            .as_ref()
            .map_or(DEFAULT_PANEL_GROUP, |card_position| {
                let overlay_panel_position = match card_position {
                    CardPosition::Left => OverlayPanelPosition::Start,
                    CardPosition::Right => OverlayPanelPosition::End,
                };
                PanelGroup::Overlay(overlay_panel_position)
            })
    }

    fn panel_state(&self, _interactive_video_ad: &InteractiveVideoAd) -> Option<PanelState> {
        let template_data = hydrate_iva_baseline_template(self).ok()?;
        Some(PanelState::BaselineIVA(Shared::new(template_data)))
    }

    fn panel_component(
        &self,
        panel_render_state: &PanelState,
    ) -> Box<dyn Fn(&AppContext) -> AnyComposable + 'static> {
        Box::new({
            let panel_render_state = panel_render_state.clone();
            move |ctx: &AppContext| {
                let panel_render_state = panel_render_state.clone();
                compose! {
                    Memo(item_builder: Box::new(move |ctx| {
                        if let PanelState::BaselineIVA(template_data) = panel_render_state.clone() {
                            compose_option! {
                                IVABaselineRenderer(template_data: SharedSignal::from_shared(template_data))
                            }
                        } else {
                            None
                        }
                    }))
                    .into_any()
                }
            }
        })
    }
}

impl PanelBuildable for AdFeedbackTemplate {
    fn panel_group(&self) -> PanelGroup {
        PanelGroup::Overlay(OverlayPanelPosition::Full)
    }

    fn panel_state(&self, _interactive_video_ad: &InteractiveVideoAd) -> Option<PanelState> {
        None
    }

    fn panel_component(
        &self,
        _panel_render_state: &PanelState,
    ) -> Box<dyn Fn(&AppContext) -> AnyComposable + 'static> {
        Box::new({
            move |ctx| {
                compose! {
                    Stack(){}.into_any()
                }
            }
        })
    }
}

impl PanelBuildable for SurfaceXTemplate {
    fn panel_group(&self) -> PanelGroup {
        match self {
            Self::BaselineIVA(template) => template.panel_group(),
            Self::AdFeedback(template) => template.panel_group(),
        }
    }

    fn panel_state(&self, interactive_video_ad: &InteractiveVideoAd) -> Option<PanelState> {
        match self {
            Self::BaselineIVA(template) => template.panel_state(interactive_video_ad),
            Self::AdFeedback(template) => template.panel_state(interactive_video_ad),
        }
    }

    fn panel_component(
        &self,
        panel_render_state: &PanelState,
    ) -> Box<dyn Fn(&AppContext) -> AnyComposable + 'static> {
        match self {
            Self::BaselineIVA(template) => template.panel_component(panel_render_state),
            Self::AdFeedback(template) => template.panel_component(panel_render_state),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_data::{create_test_atc_interactive_video_ad, TestAdType};
    use ads_playback_types::test_data::{
        MINIMAL_AD_FEEDBACK_TEMPLATE, MINIMAL_BASELINE_IVA_TEMPLATE,
    };

    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::assert_node_exists;
    use network_parser::core::network_parse_from_str;
    use playback_feature_manager::OverlayPanelPosition::Full;
    use rstest::rstest;

    fn baseline_iva_template() -> BaselineIVATemplate {
        network_parse_from_str::<SurfaceXTemplate>(MINIMAL_BASELINE_IVA_TEMPLATE)
            .ok()
            .and_then(|template| match template {
                SurfaceXTemplate::BaselineIVA(template) => Some(template),
                _ => None,
            })
            .expect("Failed to deserialize payload to BaselineIVATemplate")
    }

    fn ad_feedback_template() -> AdFeedbackTemplate {
        network_parse_from_str::<SurfaceXTemplate>(MINIMAL_AD_FEEDBACK_TEMPLATE)
            .ok()
            .and_then(|template| match template {
                SurfaceXTemplate::AdFeedback(template) => Some(template),
                _ => None,
            })
            .expect("Failed to deserialize payload to AdFeedbackTemplate")
    }

    #[rstest]
    #[case(None, OverlayPanelPosition::Start)]
    #[case(Some(CardPosition::Left), OverlayPanelPosition::Start)]
    #[case(Some(CardPosition::Right), OverlayPanelPosition::End)]
    fn test_baseline_iva_panel_group(
        #[case] card_position: Option<CardPosition>,
        #[case] expected: OverlayPanelPosition,
    ) {
        let mut template = baseline_iva_template();
        template.template_props.card_position = card_position;
        assert_eq!(template.panel_group(), PanelGroup::Overlay(expected));
    }

    #[test]
    fn test_baseline_iva_panel_state() {
        let template = baseline_iva_template();
        let iva = create_test_atc_interactive_video_ad(TestAdType::VodLinear, "test_ad");
        assert!(template.panel_state(&iva).is_some());
    }

    #[test]
    fn test_baseline_iva_panel_component() {
        launch_test(
            move |ctx| {
                let template = baseline_iva_template();
                let iva = create_test_atc_interactive_video_ad(TestAdType::VodLinear, "test_ad");
                let state = template
                    .panel_state(&iva)
                    .expect("Panel state creation should succeed");
                template.panel_component(&state)(&ctx)
            },
            |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let container = tree.find_by_test_id(IVA_BASELINE_CONTAINER_TEST_ID);
                assert_node_exists!(&container);
            },
        );
    }

    #[test]

    fn test_ad_feedback_panel_group() {
        let template = ad_feedback_template();
        assert_eq!(template.panel_group(), PanelGroup::Overlay(Full));
    }

    #[test]
    fn test_ad_feedback_panel_state() {
        let template = ad_feedback_template();
        let iva = create_test_atc_interactive_video_ad(TestAdType::VodLinear, "test_ad");
        assert!(template.panel_state(&iva).is_none());
    }
}
