use std::collections::HashMap;

/// Replaces all instances of all macros in a string based on the macro replacement map provided in `replace_map`.
///
/// # Arguments
/// * `string` - The original string/URL
/// * `replace_map` - Map from macro to its replacement value
///
/// # Returns
/// The string with all macros replaced
///
pub fn replace_all_macros(string: &str, replace_map: &HashMap<&str, &str>) -> String {
    let mut result = string.to_string();

    for (key, replacement) in replace_map {
        let macro_key = format!("[{}]", key);
        result = result.replace(&macro_key, replacement);
    }

    result
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_replace_all_macros() {
        let mut replace_map = HashMap::new();
        replace_map.insert("EVENTTYPE", "ivaError");
        replace_map.insert("ERRORCODE", "2005");

        let original = "Error: [EVENTTYPE] with code [ERRORCODE] and [EMPTY]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "Error: ivaError with code 2005 and [EMPTY]");
    }

    #[test]
    fn test_replace_all_macros_empty_map() {
        let replace_map = HashMap::new();
        let original = "No macros here [EVENTTYPE]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, original);
    }

    #[test]
    fn test_multiple_macro_replacements() {
        let mut replace_map = HashMap::new();
        replace_map.insert("USER", "Client");
        replace_map.insert("ACTION", "clicked");
        replace_map.insert("ITEM", "button");

        let original = "[USER] [ACTION] the [ITEM] at [USER]'s request";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "Client clicked the button at Client's request");
    }

    #[test]
    fn test_special_characters_in_replacement() {
        let mut replace_map = HashMap::new();
        replace_map.insert("SPECIAL", "$1 & [test]");

        let original = "Value: [SPECIAL]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "Value: $1 & [test]");
    }

    #[test]
    fn test_should_correctly_replace_all_instances() {
        let original_string = "[AAX_DOMAIN_PLACEHOLDER]/x/px?c={\"e1\":\"[EVENTTYPE]\",\"vat\":[ATTIME],\"vd\":15.01,\"e2\":\"%5BEVENTTYPE%5D\",\"vec\":[ERRORCODE]}";
        let mut replacement_map = HashMap::new();
        replacement_map.insert("EVENTTYPE", "ivaCtaDisplayed");
        replacement_map.insert("ERRORCODE", "2005");

        let result = replace_all_macros(original_string, &replacement_map);
        let expected = "[AAX_DOMAIN_PLACEHOLDER]/x/px?c={\"e1\":\"ivaCtaDisplayed\",\"vat\":[ATTIME],\"vd\":15.01,\"e2\":\"%5BEVENTTYPE%5D\",\"vec\":2005}";
        assert_eq!(result, expected);
    }

    #[test]
    fn test_should_return_original_string_if_no_matches_found() {
        let original_string = "[AAX_DOMAIN_PLACEHOLDER]/x/px?c={\"e1\":\"[EVENTTYPE1]\",\"vat\":[ATTIME],\"vd\":15.01,\"e2\":\"%5BEVENTTYPE2%5D\",\"vec\":[ERRORCODE]}";
        let mut replacement_map = HashMap::new();
        replacement_map.insert("EVENTTYPE", "ivaCtaDisplayed");

        let result = replace_all_macros(original_string, &replacement_map);
        assert_eq!(result, original_string);
    }

    #[test]
    fn test_empty_string_input() {
        let mut replace_map = HashMap::new();
        replace_map.insert("TEST", "value");

        let result = replace_all_macros("", &replace_map);
        assert_eq!(result, "");
    }

    #[test]
    fn test_empty_replacement_value() {
        let mut replace_map = HashMap::new();
        replace_map.insert("EMPTY_VALUE", "");

        let original = "Before [EMPTY_VALUE] after";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "Before  after");
    }

    #[test]
    fn test_macro_at_string_boundaries() {
        let mut replace_map = HashMap::new();
        replace_map.insert("START", "Beginning");
        replace_map.insert("END", "Finish");

        let original = "[START] middle [END]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "Beginning middle Finish");
    }

    #[test]
    fn test_nested_brackets_in_replacement() {
        let mut replace_map = HashMap::new();
        replace_map.insert("NESTED", "[INNER]");

        let original = "Value: [NESTED]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "Value: [INNER]");
    }

    #[test]
    fn test_consecutive_macros() {
        let mut replace_map = HashMap::new();
        replace_map.insert("A", "Alpha");
        replace_map.insert("B", "Beta");

        let original = "[A][B]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "AlphaBeta");
    }

    #[test]
    fn test_macro_with_numbers_and_underscores() {
        let mut replace_map = HashMap::new();
        replace_map.insert("TEST_123", "value123");
        replace_map.insert("VAR_2_TEST", "another_value");

        let original = "[TEST_123] and [VAR_2_TEST]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "value123 and another_value");
    }

    #[test]
    fn test_case_sensitive_macros() {
        let mut replace_map = HashMap::new();
        replace_map.insert("test", "lowercase");
        replace_map.insert("TEST", "uppercase");

        let original = "[test] vs [TEST]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "lowercase vs uppercase");
    }

    #[test]
    fn test_partial_macro_matches() {
        let mut replace_map = HashMap::new();
        replace_map.insert("TEST", "replaced");

        // Should not replace partial matches
        let original = "[TESTING] and [TEST] and TEST";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "[TESTING] and replaced and TEST");
    }

    #[test]
    fn test_unicode_in_macros_and_replacements() {
        let mut replace_map = HashMap::new();
        replace_map.insert("EMOJI", "🚀");
        replace_map.insert("UNICODE", "café");

        let original = "Launch [EMOJI] at the [UNICODE]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "Launch 🚀 at the café");
    }

    #[test]
    fn test_very_long_string() {
        let mut replace_map = HashMap::new();
        replace_map.insert("REPEAT", "X");

        let original = "[REPEAT]".repeat(1000);
        let result = replace_all_macros(&original, &replace_map);
        assert_eq!(result, "X".repeat(1000));
    }

    #[test]
    fn test_macro_replacement_order_independence() {
        let mut replace_map = HashMap::new();
        replace_map.insert("FIRST", "1st");
        replace_map.insert("SECOND", "2nd");

        let original = "[SECOND] comes after [FIRST]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "2nd comes after 1st");
    }

    #[test]
    fn test_json_like_string_with_macros() {
        let mut replace_map = HashMap::new();
        replace_map.insert("USER_ID", "12345");
        replace_map.insert("ACTION", "click");
        replace_map.insert("TIMESTAMP", "1640995200");

        let original = r#"{"userId": "[USER_ID]", "action": "[ACTION]", "timestamp": [TIMESTAMP]}"#;
        let result = replace_all_macros(original, &replace_map);
        let expected = r#"{"userId": "12345", "action": "click", "timestamp": 1640995200}"#;
        assert_eq!(result, expected);
    }

    #[test]
    fn test_url_with_macros() {
        let mut replace_map = HashMap::new();
        replace_map.insert("DOMAIN", "example.com");
        replace_map.insert("PATH", "api/v1");
        replace_map.insert("PARAM", "test_value");

        let original = "https://[DOMAIN]/[PATH]?param=[PARAM]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "https://example.com/api/v1?param=test_value");
    }

    #[test]
    fn test_macros_not_in_map() {
        let mut replace_map = HashMap::new();
        replace_map.insert("REPLACE_ME", "replaced");
        replace_map.insert("ALSO_REPLACE", "also_replaced");

        let original = "[REPLACE_ME] [KEEP_ME] [ALSO_REPLACE]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "replaced [KEEP_ME] also_replaced");
    }

    #[test]
    fn test_whitespace_in_replacements() {
        let mut replace_map = HashMap::new();
        replace_map.insert("SPACES", "  multiple   spaces  ");
        replace_map.insert("TABS", "\t\ttabs\t");
        replace_map.insert("NEWLINES", "line1\nline2");

        let original = "[SPACES] [TABS] [NEWLINES]";
        let result = replace_all_macros(original, &replace_map);
        assert_eq!(result, "  multiple   spaces   \t\ttabs\t line1\nline2");
    }
}
