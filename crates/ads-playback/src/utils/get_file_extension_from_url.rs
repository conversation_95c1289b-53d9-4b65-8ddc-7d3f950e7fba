use ignx_compositron::regex_lite::Regex;

const LOG_PREFIX: &str = "[get_file_extension_from_url]";

/// Extracts the file extension from a URL (including the dot)
///
/// Returns the lowercase extension if present (including the dot), or None if no extension is found.
/// This function mimics the behavior of <https://code.amazon.com/packages/AVLivingRoomClient/blobs/mainline/--/packages/avlrc-images/urlParser.ts>
pub fn get_file_extension_from_url(url: &str) -> Option<String> {
    let valid_url_regex =
        match Regex::new(r"^(https?):\/\/([^/]+)((?:\/[^/]+)*)\/([^/^?]+)(?:\?.*)?$") {
            Ok(regex) => regex,
            Err(err) => {
                log::error!(
                    "{} Failed to compile URL regex pattern: {}",
                    LOG_PREFIX,
                    err
                );
                return None;
            }
        };

    // Check if the URL matches the pattern
    if let Some(captures) = valid_url_regex.captures(url) {
        // Extract the filename (4th capture group)
        if let Some(filename) = captures.get(4) {
            let filename = filename.as_str();

            // Find the last dot in the filename
            filename
                .rfind('.')
                .map(|pos| filename[pos..].to_lowercase())
        } else {
            None
        }
    } else {
        // URL doesn't match the pattern
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_file_extension_from_url_valid() {
        // Long dir
        assert_eq!(
            get_file_extension_from_url("https://some.domain/path1/path2/path3/file.ext"),
            Some(".ext".to_string())
        );

        // Short dir
        assert_eq!(
            get_file_extension_from_url("http://some.domain.com/path1/file.name.jpeg"),
            Some(".jpeg".to_string())
        );

        // No dir
        assert_eq!(
            get_file_extension_from_url("https://domain/file._name_something_.png"),
            Some(".png".to_string())
        );

        // No ext
        assert_eq!(
            get_file_extension_from_url("https://some.domain/path1/file"),
            None
        );

        // Ignore query string
        assert_eq!(
            get_file_extension_from_url("https://some.domain/path1/file.ext?a=b&c=d"),
            Some(".ext".to_string())
        );
    }

    #[test]
    fn test_get_file_extension_from_url_invalid() {
        // Non-HTTP protocol
        assert_eq!(
            get_file_extension_from_url("ftp://some.domain/path1/path2/path3/file.ext"),
            None
        );

        // Characters before HTTP
        assert_eq!(
            get_file_extension_from_url("xhttps://some.domain/path1/path2/path3/file.ext"),
            None
        );

        // No protocol
        assert_eq!(
            get_file_extension_from_url("some.domain/path1/path2/path3/file.ext"),
            None
        );

        // No file name
        assert_eq!(
            get_file_extension_from_url("https://some.domain/path1/path2/path3/"),
            None
        );

        // No hostname
        assert_eq!(
            get_file_extension_from_url("https:///path1/path2/path3/file.ext"),
            None
        );

        // Non-URL
        assert_eq!(get_file_extension_from_url("this-is-not-url"), None);
    }

    #[test]
    fn test_get_file_extension_additional_cases() {
        // Additional tests for edge cases

        // Empty string
        assert_eq!(get_file_extension_from_url(""), None);

        // URLs with empty extensions (ending with a dot)
        assert_eq!(
            get_file_extension_from_url("https://some.domain/path1/file."),
            Some(".".to_string())
        );

        // Case conversion
        assert_eq!(
            get_file_extension_from_url("https://example.com/image.PNG"),
            Some(".png".to_string())
        );
        assert_eq!(
            get_file_extension_from_url("https://example.com/document.PDF"),
            Some(".pdf".to_string())
        );

        // Spaces in filename (assuming already decoded)
        assert_eq!(
            get_file_extension_from_url("https://example.com/image file.png"),
            Some(".png".to_string())
        );

        // Different extensions
        assert_eq!(
            get_file_extension_from_url("https://example.com/document.docx"),
            Some(".docx".to_string())
        );
        assert_eq!(
            get_file_extension_from_url("https://example.com/archive.tar.gz"),
            Some(".gz".to_string())
        );
        assert_eq!(
            get_file_extension_from_url("https://example.com/script.min.js"),
            Some(".js".to_string())
        );

        // Query parameters and fragment
        assert_eq!(
            get_file_extension_from_url("https://example.com/page.php?id=1#section2"),
            Some(".php".to_string())
        );
    }
}
