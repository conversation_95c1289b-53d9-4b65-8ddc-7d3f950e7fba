This crate provides the playback decoupled feature for interactive ads during playback.

## Tech Approach
https://quip-amazon.com/aIMEAjAeLNTv/Enabling-PVA-Playback-Features-Development-in-Rust

## Overview
We implement a unified Advertising feature to encapsulate all ads functionality
during playback. This centralised approach:

* Consolidates business logic for ads (such as player SDK and panel integration, logic around player chrome interactions, etc)
* Streamlines data fetching through a single interface
* Simplifies render operations, render state persistence and contextual metrics collection


## SurfaceX widgets
### Why using Memo in our widgets?
> The Memo composer tracks signals used in its builder function and automatically re-executes it when those signals change, returning a new composable instance (or None), so it's crucial that signals are used efficiently to avoid unnecessary UI rebuilds.
- We use Memo because it solves a fundamental Rust ownership challenge when styling components. When you modify a component multiple times with different styles, each modification would normally consume the component due to Rust's move semantics. <PERSON><PERSON> creates a closure where we can freely modify our component without fighting the ownership system.
- Unlike React where you can save a component to a variable (const myText = <Text>Hello</Text>) and reuse it inside other components, our compose system requires components to be defined directly within the compose! macro. You cannot store the result of compose! in a variable and use it later inside another compose! block.
```
// This won't work ❌
let my_text = compose! { RichTextLabel(text: "Hello"){} };
compose! {
    Column(){
        my_text  // Error: Cannot use variable here
    }
}

// This will work ✅
compose! {
    Column(){
        Memo(item_builder: Box::new( move |ctx| {
            let mut my_text = compose! { RichTextLabel(text: "Hello"){} };
            
            my_text = my_text.color(Color::white());
            
            Some(my_text)
        }))
    }
}
```