use ads_playback::surface_x::template_hydration::IvaBaselineTemplateData;
use ads_playback::surface_x::widgets::types::*;
use ads_playback::surface_x::widgets::*;
use ads_playback::ui::iva_baseline::*;
use amzn_fable_tokens::FableText;
use fableous::SCREEN_HEIGHT;
use fableous::SCREEN_WIDTH;
use ignx_compositron::color::Color;
use ignx_compositron::shared::Shared;
use ignx_compositron::{compose, prelude::safe::*};
use std::str::FromStr;

// IVA Baseline example

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let iva_baseline_template_data = IvaBaselineTemplateData {
            main_icon: SurfaceXWidgetTemplateData::Badge(Shared::new(
                SurfaceXBadgeWidgetTemplateData {
                    text: "Icon".to_string(),
                    background_color: Color::purple(),
                    type_ramp: None,
                    text_color: None,
                    container_style: None,
                    iter_id: "1".to_string(),
                },
            )),
            main_content: vec![Shared::new(SurfaceXWidgetTemplateData::Row(Shared::new(
                SurfaceXRowWidgetTemplateData {
                    row_components: vec![SurfaceXRowChildWidgetTemplateData::Text(Shared::new(
                        SurfaceXTextWidgetTemplateData {
                            text: "Some".to_string(),
                            color: None,
                            type_ramp: FableText::TYPE_BODY200.into(),
                            text_alignment: None,
                            line_height: None,
                            container_style: None,
                            truncation_mode: None,
                            max_lines: None,
                            text_decoration_line: None,
                            iter_id: "2".to_string(),
                        },
                    ))],
                    iter_id: String::new(),
                    container_style: None,
                },
            )))],
            cta_button: Some(Shared::new(SurfaceXWidgetTemplateData::PrimaryButton(
                Shared::new(SurfaceXPrimaryButtonWidgetTemplateData {
                    text: "Button text".to_string(),
                    icon_name: None,
                    container_style: None,
                    iter_id: "3".to_string(),
                }),
            ))),
            hint_text: None,
            card_position: None,
            cta_background_color: None,
        };

        let iva_baseline_template_data = SharedSignal::from_data(iva_baseline_template_data);

        compose! {
            Stack() {
                IVABaselineRenderer(template_data: iva_baseline_template_data)
            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .alignment(Alignment::StartBottom)
            .background_color(Color::from_str("#525252").unwrap_or_default())
        }
    });
}
