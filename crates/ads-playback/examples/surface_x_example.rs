use ads_playback::surface_x::template_hydration::hydrate_widget_template;
use ads_playback::surface_x::widget_renderer::*;
use ads_playback::surface_x::widgets::container_styles::SurfaceXContainerStyles;
use ads_playback::surface_x::widgets::primary_button_widget::SurfaceXPrimaryButtonWidgetTemplateData;
use ads_playback::surface_x::widgets::types::{
    SurfaceXRowChildWidgetTemplateData, SurfaceXWidgetTemplateData,
};
use ads_playback::surface_x::widgets::*;
use ads_playback_types::surface_x::widget_templates::SurfaceXWidgetTemplates;
use amzn_fable_tokens::*;
use fableous::typography::type_ramp::TypeRamp;
use fableous::typography::typography::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::color::Color;
use ignx_compositron::shared::Shared;
use ignx_compositron::Composer;
use ignx_compositron::{compose, prelude::safe::*};
use network_parser::core::network_parse_from_str;
use std::str::FromStr;
use uuid::Uuid;

// SurfaceX widgets example

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let text_widget_template_data = SharedSignal::from_data(SurfaceXWidgetTemplateData::Text(
            Shared::new(SurfaceXTextWidgetTemplateData {
                text: "Hello TextWidget, Hello TextWidget, Hello TextWidget, Hello TextWidget"
                    .to_string(),
                type_ramp: FableText::TYPE_BODY200.into(),
                color: Color::from_str("#f171ff").ok(),
                text_alignment: Some(TextVerticalAlignment::Start),
                line_height: None,
                container_style: Some(SurfaceXContainerStyles {
                    width: Some(200.0),
                    height: Some(80.0),
                    background_color: Some(Color::white()),
                    border_color: Some(Color::red()),
                    border_width: Some(4.0),
                    border_radius: Some(12.0),
                    opacity: Some(0.9),
                    max_width: Some(200.0),
                    max_height: Some(200.0),
                    min_width: Some(0.0),
                    min_height: Some(0.0),
                    padding_bottom: Some(5.0),
                    padding_start: Some(5.0),
                    padding_end: Some(5.0),
                    padding_top: Some(5.0),
                    margin_bottom: Some(5.0),
                    margin_start: Some(5.0),
                    margin_end: Some(5.0),
                    margin_top: Some(5.0),
                    main_axis_alignment: Some(MainAxisAlignment::Center),
                    cross_axis_alignment: Some(CrossAxisAlignment::End),
                }),
                truncation_mode: Some(TruncationMode::Head),
                max_lines: Some(2),
                text_decoration_line: None,
                iter_id: Uuid::new_v4().to_string(),
            }),
        ));

        let divider_widget_template_data = SharedSignal::from_data(
            SurfaceXWidgetTemplateData::Divider(Shared::new(SurfaceXDividerWidgetTemplateData {
                color: Some(Color::white()),
                length: 32.0,
                orientation: SurfaceXDividerOrientation::Vertical,
                container_style: None,
                iter_id: Uuid::new_v4().to_string(),
            })),
        );

        let badge_widget_template_data = SharedSignal::from_data(
            SurfaceXWidgetTemplateData::Badge(Shared::new(SurfaceXBadgeWidgetTemplateData {
                text: "Hello BadgeWidget".to_string(),
                type_ramp: Some(FableText::TYPE_LABEL400.into()),
                text_color: Some(Color::green()),
                background_color: Color::purple(),
                container_style: None,
                iter_id: Uuid::new_v4().to_string(),
            })),
        );

        let icon_widget_template_data = SharedSignal::from_data(SurfaceXWidgetTemplateData::Icon(
            Shared::new(SurfaceXIconWidgetTemplateData {
                icon_name: FableIcon::DELETE.to_string(),
                color: Some(Color::red()),
                font_size: Some(FontSize(24)),
                container_style: Some(SurfaceXContainerStyles {
                    width: Some(100.0),
                    height: Some(50.0),
                    background_color: Some(Color::white()),
                    ..Default::default()
                }),
                iter_id: Uuid::new_v4().to_string(),
            }),
        ));

        let primary_button_widget_data =
            SharedSignal::from_data(SurfaceXWidgetTemplateData::PrimaryButton(Shared::new(
                SurfaceXPrimaryButtonWidgetTemplateData {
                    text: "Hello PrimaryButtonWidget".to_string(),
                    icon_name: None,
                    container_style: None,
                    iter_id: Uuid::new_v4().to_string(),
                },
            )));

        let prime_logo_widget_template_data =
            SharedSignal::from_data(SurfaceXWidgetTemplateData::PrimeLogo(Shared::new(
                SurfaceXPrimeLogoWidgetTemplateData {
                    container_style: None,
                    iter_id: Uuid::new_v4().to_string(),
                },
            )));

        let star_rating_widget_template_data =
            SharedSignal::from_data(SurfaceXWidgetTemplateData::StarRating(Shared::new(
                SurfaceXStarRatingWidgetTemplateData {
                    rating: 3.4,
                    rating_count: "1000".to_string(),
                    star_size: FontSize(34),
                    full_star_color: Color::red(),
                    empty_star_color: Color::white(),
                    star_separator_width: 10.0,
                    show_rating_number: Some(true),
                    rating_text_type_ramp: Some(TypeRamp::from(FableText::TYPE_LABEL800)),
                    container_style: None,
                    iter_id: Uuid::new_v4().to_string(),
                },
            )));

        let image_widget_template_data = SharedSignal::from_data(SurfaceXWidgetTemplateData::Image(Shared::new(SurfaceXImageWidgetTemplateData {
            image_url:
                "https://m.media-amazon.com/images/S/sonata-images-prod/UK_Batman_Caped_Crusader_S1_CS_UI/25c323f6-8638-4dd8-b947-11765ec9f48a.jpeg"
                    .to_string(),
            width: 200.0,
            height: 100.0,
            container_style: None,
            iter_id: Uuid::new_v4().to_string(),
        })));

        // Create raw widget data for row components
        let text_for_row = Shared::new(SurfaceXTextWidgetTemplateData {
            text: "Row Text".to_string(),
            type_ramp: FableText::TYPE_BODY200.into(),
            color: Color::from_str("#ffffff").ok(),
            text_alignment: Some(TextVerticalAlignment::Start),
            line_height: None,
            container_style: None,
            truncation_mode: None,
            max_lines: None,
            text_decoration_line: None,
            iter_id: Uuid::new_v4().to_string(),
        });

        let divider_for_row = Shared::new(SurfaceXDividerWidgetTemplateData {
            color: Some(Color::white()),
            length: 16.0,
            orientation: SurfaceXDividerOrientation::Vertical,
            container_style: None,
            iter_id: Uuid::new_v4().to_string(),
        });

        // Create a row widget with multiple components
        let row_widget_template_data = Shared::new(SurfaceXRowWidgetTemplateData {
            row_components: vec![
                SurfaceXRowChildWidgetTemplateData::Text(text_for_row.clone()),
                SurfaceXRowChildWidgetTemplateData::Divider(divider_for_row),
                SurfaceXRowChildWidgetTemplateData::Text(text_for_row),
            ],
            container_style: Some(SurfaceXContainerStyles {
                background_color: Some(Color::from_str("#333333").unwrap_or_default()),
                padding_top: Some(10.0),
                padding_bottom: Some(10.0),
                padding_start: Some(10.0),
                padding_end: Some(10.0),
                border_radius: Some(8.0),
                cross_axis_alignment: Some(CrossAxisAlignment::Center),
                ..Default::default()
            }),
            iter_id: Uuid::new_v4().to_string(),
        });

        let row_widget_template_data =
            SharedSignal::from_data(SurfaceXWidgetTemplateData::Row(row_widget_template_data));
        let row_widget_template_data = create_ref_signal(ctx.scope(), row_widget_template_data);
        let text_widget_template_data = create_ref_signal(ctx.scope(), text_widget_template_data);
        let divider_widget_template_data =
            create_ref_signal(ctx.scope(), divider_widget_template_data);
        let badge_widget_template_data = create_ref_signal(ctx.scope(), badge_widget_template_data);
        let icon_widget_template_data = create_ref_signal(ctx.scope(), icon_widget_template_data);
        let primary_button_widget_data = create_ref_signal(ctx.scope(), primary_button_widget_data);
        let prime_logo_widget_template_data =
            create_ref_signal(ctx.scope(), prime_logo_widget_template_data);
        let star_rating_widget_template_data =
            create_ref_signal(ctx.scope(), star_rating_widget_template_data);
        let image_widget_template_data = create_ref_signal(ctx.scope(), image_widget_template_data);

        let parsed_widget_data =
            SharedSignal::from_data(parse_and_hydrate(ROW_WIDGET_JSON_STR, "row widget").unwrap());
        let parsed_widget_data = create_ref_signal(ctx.scope(), parsed_widget_data);

        compose! {

            Row() {
              Column() {
                ExampleLabel(text: "WIDGETS", type_ramp: FableText::TYPE_LABEL200.into())

                ExampleLabel(text: "Row widget");
                WidgetRenderer(widget_data: row_widget_template_data)

                ExampleLabel(text: "Icon widget");
                WidgetRenderer(widget_data: icon_widget_template_data) // Icons can only be seen when building the whole app.

                ExampleLabel(text: "Badge widget");
                WidgetRenderer(widget_data: badge_widget_template_data)

                ExampleLabel(text: "Prime Logo widget");
                WidgetRenderer(widget_data: prime_logo_widget_template_data)

                ExampleLabel(text: "Text widget");
                WidgetRenderer(widget_data: text_widget_template_data)

                ExampleLabel(text: "Divider widget");
                WidgetRenderer(widget_data: divider_widget_template_data)

                ExampleLabel(text: "Primary Button widget");
                WidgetRenderer(widget_data: primary_button_widget_data)

                ExampleLabel(text: "Star Rating widget");
                WidgetRenderer(widget_data: star_rating_widget_template_data)

                ExampleLabel(text: "Image widget");
                WidgetRenderer(widget_data: image_widget_template_data)
              }
              .width(SCREEN_WIDTH / 2.0)
              .height(SCREEN_HEIGHT)


              Column() {
                ExampleLabel(text: "PARSED TEMPLATES", type_ramp: FableText::TYPE_LABEL200.into())

                ExampleLabel(text: "Row widget");
                WidgetRenderer(widget_data: parsed_widget_data)
              }
              .width(SCREEN_WIDTH / 2.0)
              .height(SCREEN_HEIGHT)

            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .background_color(Color::from_str("#525252").unwrap_or_default())
        }
    });
}

#[Composer]
fn ExampleLabel<'s>(
    ctx: &AppContext<'s>,
    text: &str,
    #[optional = FableText::TYPE_LABEL100.into()] type_ramp: TypeRamp,
) -> impl VisualComposable<'s> {
    let label_color = MaybeSignal::Static(Color::from_str("#b3b3b3").unwrap_or_default());

    compose! {
       Column(){
             Typography(content: text, type_ramp).color(label_color)
          }
          .padding(Padding {
            start: 0.0,
            end: 0.0,
            top: 10.0,
            bottom: 0.0,
        })
    }
}

fn parse_and_hydrate(
    json_str: &str,
    widget_type: &str,
) -> Result<SurfaceXWidgetTemplateData, String> {
    match network_parse_from_str::<SurfaceXWidgetTemplates>(json_str) {
        Ok(template) => hydrate_widget_template(&template).map_err(|e| {
            let error_msg = format!("Failed to convert {} template to props: {}", widget_type, e);

            log::error!("{}", error_msg);
            error_msg
        }),
        Err(e) => {
            let error_msg = format!("Failed to parse {} template: {}", widget_type, e);
            log::error!("{}", error_msg);
            Err(error_msg)
        }
    }
}

const TEXT_WIDGET_JSON_STR: &str = r#"{ 
          "type": "TextWidget",
          "props": 
            { "text": "text widget", 
              "typeRamp": "label-600", 
              "numberOfLines": 2, 
              "textDecorationLine": "none", 
              "lineHeight": 1.2,
              "textAlign": "start"
            } 
          }"#;

const IMAGE_WIDGET_JSON_STR: &str = r#"{
          "type": "ImageWidget",
          "props": {
              "imageUrl": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png",
              "width": 40,
              "height": 40,
              "containerStyle": {
                  "width": 81,
                  "height": 81,
                  "borderRadius": 16,
                  "backgroundColor": "242424"
              }
          }
        }"#;

const BADGE_WIDGET_JSON_STR: &str = r#"{ 
          "type": "BadgeWidget",
          "props": 
            { "text": "badge widget", 
              "typeRamp": "label-600",
              "background_color": "fefefe",
              "textColor": "bebebe"
            } 
          }"#;

const ICON_WIDGET_JSON_STR: &str = r#"{ 
          "type": "IconWidget",
          "props": 
            {
                "iconName": "Delete",
                "color": "d18888",
                "fontSize": 48,
                "containerStyle": {
                  "backgroundColor": "fefefe"
                }
            } 
        }"#;

const DIVIDER_WIDGET_JSON_STR: &str = r#"{ 
          "type": "DividerWidget",
          "props": 
            {
                "orientation": "vertical",
                "length": 28,
                "color": "bebebe",
                "containerStyle": {
                  "backgroundColor": "fefefe",
                  "paddingStart": 10,
                  "paddingEnd": 10
                }
            } 
        }"#;

const PRIMARY_BUTTON_WIDGET_JSON_STR: &str = r#"{ 
          "type": "PrimaryButtonWidget",
          "props": 
            {
                "text": "Button text",
                "iconName": "Delete",
                "containerStyle": {
                  "backgroundColor": "fefefe",
                  "paddingStart": 10,
                  "paddingEnd": 10
                }
            } 
        }"#;

const PRIME_LOGO_WIDGET_JSON_STR: &str = r#"{ 
          "type": "PrimeLogoWidget",
          "props": 
            {
                "containerStyle": {
                  "backgroundColor": "fefefe",
                  "paddingStart": 10,
                  "paddingEnd": 10
                }
            } 
        }"#;

const STAR_RATING_WIDGET_JSON_STR: &str = r#"{ 
          "type": "StarRatingWidget",
          "props": 
            {
                "rating": "4.5", 
                "starSize": 20,
                "fullStarColor": "FFD700",
                "emptyStarColor": "CCCCCC",
                "starSeparatorWidth": 2,
                "showText": true,
                "ratingTextTypeRamp": "label-200",
                "ratingCount": "2000",
                "containerStyle": {
                  "backgroundColor": "fefefe",
                  "paddingStart": 10,
                  "paddingEnd": 10
                }
            } 
        }"#;

const ROW_WIDGET_JSON_STR: &str = r#"{ 
          "type": "RowWidget",
          "props": {
              "rowComponents": [{
                      "type": "TextWidget",
                      "props": {
                          "text": "$100",
                          "typeRamp": "label-200",
                          "lineHeight": 1.2,
                          "textDecorationLine": "none",
                          "containerStyle": {
                              "marginRight": 8
                          }
                      }
                  },
                  {
                      "type": "DividerWidget",
                      "props": {
                          "orientation": "vertical",
                          "length": 10,
                          "color": "bebebe"
                      }
                  },
                  {
                      "type": "TextWidget",
                      "props": {
                          "text": "10%",
                          "typeRamp": "label-200",
                          "lineHeight": 1.2,
                          "textDecorationLine": "none"
                      }
                  }
              ],
              "containerStyle": {
                  "backgroundColor": "9c619a",
                  "paddingStart": 10,
                  "paddingEnd": 10,
                  "vertical_alignment": "center"
              }
          }
        }"#;
