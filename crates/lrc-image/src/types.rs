use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::color::Color;
use ignx_compositron::prelude::Scope;

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum ImageTag {
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HResizeTags>
    Scaling(ScalingStrategy),
    /// <https://w.amazon.com/index.php/MSA/HowTo/ImageStyleCodes#HScalingAlgorithmSelection>
    ScalingAlgorithm(ScalingAlgorithmStrategy),
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HOverrideExtensionTag>
    Format(ImageFormat),
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HWhitespaceAutoCrop>
    Brightness(i32),
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HRoundCorners>
    RoundedCorners(RoundedCorners),
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HBlur>
    Blur(u32),
    /// Used to strip metadata from the image.
    /// Sets the dominant color as background (will have no effect on JPEG)
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HBackground>
    DB,
    /// <https://w.amazon.com/index.php/MSA/HowTo/ImageStyleCodes#HCollaging>
    Collage(CollageConfig),
    /// Use to invalidate image cache at the CDN layer
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HCacheBustingTag>
    CacheBuster,
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HBackground>
    Background(RGBAColor),
    /// <https://w.amazon.com/index.php/MSA/HowTo/ImageStyleCodes#HPQ:ImagePerceptualQualityTag28JPG2CAVIF2CWebP29>
    Quality(u8),
    /// <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HCrop>
    Cropping(CroppingStrategy),
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum ScalingAlgorithmStrategy {
    Mipmap,
    Contrast,
    Average,
    Bilinear,
    Convolve,
    Merlin,
}

impl ScalingAlgorithmStrategy {
    pub fn to_tag(&self) -> &'static str {
        match self {
            ScalingAlgorithmStrategy::Mipmap => "mipmap",
            ScalingAlgorithmStrategy::Contrast => "contrast",
            ScalingAlgorithmStrategy::Average => "average",
            ScalingAlgorithmStrategy::Bilinear => "bilinear",
            ScalingAlgorithmStrategy::Convolve => "convolve",
            ScalingAlgorithmStrategy::Merlin => "merlin",
        }
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum ScalingStrategy {
    // ScaleAndCentre(ScaleAndCentreOptions),
    /// This strategy takes an image and scales it to a rectangular canvas, padding extra
    /// area with whitespace. If the image's dimensions aren't rectangle to begin with
    /// then it will place it upon a transparent background and center the scaled source
    /// image.
    ///
    /// Original aspect image of the source image is always preserved (the image is never
    /// stretched), instead whitespace is just added for JPEGs.
    ///
    /// Ref: <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HScaletoRectangle/>
    ScaleToRectangle(ScaleToRectangleOptions),

    /// This strategy proportionally scales an image to its width.
    /// The image can only be scaled down (i.e. if the specified width is larger than
    /// the source image's width, then the image will not be scaled), and you can only
    /// use the 'Large' image size as the base.
    ///
    /// Ref: <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HScaletoWidth/>
    ScaleToWidth,

    /// This strategy proportionally scales an image to a given width.
    /// The image can only be scaled down (i.e. if the specified width is larger than
    /// the source image's width, then the image will not be scaled), and you can only
    /// use the 'Large' image size as the base.
    ///
    /// This is similar to [`ScalingStrategy::ScaleToWidth`], except the expected width here
    /// has to be specified here as opposed to inferred from the composable's dimensions.
    ///
    /// Ref: <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HScaletoWidth/>
    ScaleToWidthCustom(f32),

    /// This strategy proportionally scales an image to its height.
    /// The image can only be scaled down (i.e. if the specified height is larger than
    /// the source image's height, then the image will not be scaled), and you can only
    /// use the 'Large' image size as the base.
    ///
    /// Ref: <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#HScaletoHeight/>
    ScaleToHeight,

    /// This strategy scales the source image's width/height to the image's dimensions.
    /// If the dimensions specified are not equivalent to the original image's aspect ratio,
    /// this strategy will change the aspect ratio of the resulting image.
    ///
    /// Ref: <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#H28Up29scaletoRectangle/>
    UpscaleToRectangle,

    /// This strategy scales the source image's width/height keeping aspect ratio intact.
    /// At least one of the sides is an exact match, prioritizing the dimensions that result in a larger image.
    ///
    /// Ref: <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#H28Up29ScaletoCover>
    UpscaleToCover,

    /// This strategy scales the source image's width/height to the specified dimensions.
    /// If the dimensions specified are not equivalent to the original image's aspect ratio,
    /// this strategy will change the aspect ratio of the resulting image.
    ///
    /// This is similar to [`ScalingStrategy::UpscaleToRectangle`], except the dimensions
    /// have to be specified here as opposed to inferred from the composable's dimensions.
    ///
    /// Ref: <https://w.amazon.com/bin/view/MSA/HowTo/ImageStyleCodes#H28Up29scaletoRectangle/>
    UpscaleToRectangleCustom(f32, f32),
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum CroppingStrategy {
    /// Auto Crop
    /// <https://w.amazon.com/index.php/MSA/HowTo/ImageStyleCodes#HWhitespaceAutoCrop/>
    AutoCrop,
    /// Crop
    /// <https://w.amazon.com/index.php/MSA/HowTo/ImageStyleCodes#HCrop/>
    ///
    /// x Starting point from left edge
    /// y Starting point from top edge
    /// width Target width
    /// height Target height
    /// return `CR<x,y,width,height>`
    Crop(CropDimensions),
    /// Crop Anchor
    /// <https://w.amazon.com/index.php/MSA/HowTo/ImageStyleCodes#HCropAnchor/>
    ///
    /// <TBC> are vertical anchors, top, bottom and center
    /// <LRC>are horizontal anchors, left, right and center
    /// width Target width
    /// height Target height
    /// return `CA<TBC><LRC>,<width>,<height>`
    CropAnchor(CropAnchorDimensions),
}

#[derive(Clone)]
pub struct ScaleAndCentreOptions {
    pub original_height: i32, // The original height of the image.
    pub original_width: i32,  // The original width of the image.
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct ScaleToRectangleOptions {
    pub alignment: ImageAlignment, // Where to align the image if empty space.
    pub hide_canvas: bool,         // Whether to hide or show the canvas if there is empty space.
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct RoundedCorners {
    pub radius: i32,
    pub border_width: i32,
    pub rgba_border_color: String,
    pub rgba_background_color: String,
    pub corner_mask: CornerMask,
}

#[derive(Clone, Copy)]
#[derive_test_only(Debug, PartialEq)]
pub enum CornerMask {
    NoCorners,
    TopLeft,
    TopRight,
    TopLeftRight,
    BottomLeft,
    TopBottomLeft,
    TopRightBottomLeft,
    TopLeftRightBottomLeft,
    BottomRight,
    TopLeftBottomRight,
    TopBottomRight,
    TopLeftRightBottomRight,
    BottomLeftRight,
    TopBottomLeftBottomRight,
    TopRightBottomLeftRight,
    AllCorners,
}

impl CornerMask {
    pub fn to_code(&self) -> i32 {
        match self {
            CornerMask::NoCorners => 0,
            CornerMask::TopLeft => 1,
            CornerMask::TopRight => 2,
            CornerMask::TopLeftRight => 3,
            CornerMask::BottomLeft => 4,
            CornerMask::TopBottomLeft => 5,
            CornerMask::TopRightBottomLeft => 6,
            CornerMask::TopLeftRightBottomLeft => 7,
            CornerMask::BottomRight => 8,
            CornerMask::TopLeftBottomRight => 9,
            CornerMask::TopBottomRight => 10,
            CornerMask::TopLeftRightBottomRight => 11,
            CornerMask::BottomLeftRight => 12,
            CornerMask::TopBottomLeftBottomRight => 13,
            CornerMask::TopRightBottomLeftRight => 14,
            CornerMask::AllCorners => 15,
        }
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct CropDimensions {
    pub x: i32,
    pub y: i32,
    pub width: i32,
    pub height: i32,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct CropAnchorDimensions {
    pub vertical: ImageAlignment,
    pub horizontal: ImageAlignment,
    pub width: i32,
    pub height: i32,
}

#[derive(Clone, Copy)]
#[derive_test_only(Debug, PartialEq)]
pub enum ImageAlignment {
    Centered,
    Top,
    Bottom,
    Left,
    Right,
}

impl ImageAlignment {
    pub fn to_code(&self) -> char {
        match self {
            ImageAlignment::Centered => 'C',
            ImageAlignment::Top => 'T',
            ImageAlignment::Bottom => 'B',
            ImageAlignment::Left => 'L',
            ImageAlignment::Right => 'R',
        }
    }
}

#[derive(Clone, Copy, PartialEq)]
#[derive_test_only(Debug)]
pub enum ImageFormat {
    PNG,
    JPG,
}

impl ImageFormat {
    pub fn to_tag(&self) -> &str {
        match &self {
            ImageFormat::JPG => "FMjpg",
            ImageFormat::PNG => "FMpng",
        }
    }
}

#[derive(Clone, Copy)]
#[derive_test_only(Debug, PartialEq)]
pub enum MediaCentralIdentifierType {
    PhysicalId,
    Path,
}

impl MediaCentralIdentifierType {
    pub fn to_tag(&self) -> &str {
        match &self {
            MediaCentralIdentifierType::PhysicalId => "CLa",
            MediaCentralIdentifierType::Path => "CLs",
        }
    }

    pub fn from_url(url: &str) -> Self {
        if url.contains("images/I/") {
            MediaCentralIdentifierType::PhysicalId
        } else {
            MediaCentralIdentifierType::Path
        }
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct ImageData {
    pub url: String,
    pub height: f32,
    pub width: f32,
    pub tags: Vec<ImageTag>,
}

#[derive(Clone, Copy)]
#[derive_test_only(Debug, PartialEq)]
pub struct Dimensions {
    pub width: i32,
    pub height: i32,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct OverlayImage {
    pub id: String,
    pub dimensions: Dimensions,
    pub x_pos: i32,
    pub y_pos: i32,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct CollageConfig {
    pub overlay_images: Vec<OverlayImage>,
    pub final_width: i32,
    pub final_height: i32,
    pub identifier_type: MediaCentralIdentifierType,
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct RGBAColor(pub u8, pub u8, pub u8, pub Option<u8>);

// Cacheable doesn't handle tuple structs so we generate it manually
#[derive(Clone)]
pub struct RGBAColorCacheable(pub u8, pub u8, pub u8, pub Option<u8>);
impl RGBAColorCacheable {
    pub fn into_sig(self, _scope: Scope) -> RGBAColor {
        RGBAColor(self.0, self.1, self.2, self.3)
    }
}
impl From<RGBAColor> for RGBAColorCacheable {
    fn from(sig: RGBAColor) -> Self {
        Self(sig.0, sig.1, sig.2, sig.3)
    }
}

impl From<Color> for RGBAColor {
    fn from(color: Color) -> Self {
        RGBAColor(color.r, color.g, color.b, Some(color.a))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    mod media_central_identifier_type {
        use super::*;

        #[test]
        fn test_to_tag() {
            assert_eq!(MediaCentralIdentifierType::PhysicalId.to_tag(), "CLa");
            assert_eq!(MediaCentralIdentifierType::Path.to_tag(), "CLs");
        }

        #[test]
        fn test_from_url() {
            assert_eq!(
                MediaCentralIdentifierType::from_url(
                    "https://m.media-amazon.com/images/I/C1rsThR+ahL.jpg"
                ),
                MediaCentralIdentifierType::PhysicalId
            );
            assert_eq!(
                MediaCentralIdentifierType::from_url("https://m.media-amazon.com/images/S/pv-target-images/eb031c21cfeeba2b0769bd8727145b5fb1a4dafb98fdadcdbe3b1352ea04a882.jpg"),
                MediaCentralIdentifierType::Path
            );
        }
    }
}
