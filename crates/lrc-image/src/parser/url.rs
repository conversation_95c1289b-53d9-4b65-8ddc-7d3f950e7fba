use crate::types::*;

#[cfg(not(test))]
use ignx_compositron::display;
#[cfg(test)]
use mock_instant::SystemTime;
#[cfg(test)]
use mock_instant::UNIX_EPOCH;
#[cfg(not(test))]
use std::time::SystemTime;
#[cfg(not(test))]
use std::time::UNIX_EPOCH;
#[cfg(test)]
mod display {
    thread_local! {
        pub static DISPLAY_SCALE: std::cell::Cell<f32> = const { std::cell::Cell::new(1.0) };
    }

    pub fn display_scale() -> f32 {
        DISPLAY_SCALE.with(|scale| scale.get())
    }
}
// GRCOV_BEGIN_COVERAGE

pub fn apply_media_central_tags(
    url: &str,
    height: f32,
    width: f32,
    tags: &Vec<ImageTag>,
    should_add_cache_buster_tag: bool,
) -> String {
    if url.is_empty() {
        return "".to_string();
    }

    let (prefix, extension) = split_extension(url);
    let prefix = add_tag_prefix_if_not_found(prefix);

    let mut has_image_format_tag: bool = false;
    let mut has_scaling_tag: bool = false;
    let mut has_bg_tag: bool = false;
    let mut has_db_tag: bool = false;

    for tag in tags {
        match tag {
            ImageTag::Format(_) => has_image_format_tag = true,
            ImageTag::Scaling(_) => has_scaling_tag = true,
            ImageTag::DB => has_db_tag = true,
            ImageTag::Background(_) => has_bg_tag = true,
            ImageTag::Cropping(_)
            | ImageTag::Brightness(_)
            | ImageTag::RoundedCorners(_)
            | ImageTag::ScalingAlgorithm(_)
            | ImageTag::Collage(_)
            | ImageTag::CacheBuster
            | ImageTag::Blur(_)
            | ImageTag::Quality(_) => {}
        }
    }

    // using a separate vector, so we don't have to clone tags or its content.
    let mut additional_tags: Vec<ImageTag> = vec![];

    if !has_scaling_tag {
        // default scaling behaviour.
        additional_tags.push(ImageTag::Scaling(ScalingStrategy::UpscaleToRectangle));
    }

    // If no other override is added, add FMjpg
    if !has_image_format_tag {
        additional_tags.push(ImageTag::Format(ImageFormat::JPG));
    }

    // Always set DB tag
    if !has_db_tag && !has_bg_tag {
        additional_tags.push(ImageTag::DB)
    }

    if should_add_cache_buster_tag {
        additional_tags.push(ImageTag::CacheBuster)
    }

    let mut new_url = prefix;
    new_url.push_str(&build_tags(height, width, tags, &additional_tags));

    if let Some(extension) = extension {
        new_url.push_str(&extension);
    }

    new_url
}

fn split_extension(url: &str) -> (String, Option<String>) {
    let total_length = url.chars().count();
    if total_length < 1 {
        return (url.to_string(), None);
    }
    let mut pos = total_length - 1;

    // find either a / or . whichever comes first.
    for c in url.chars().rev() {
        if c == '/' {
            // no extension.
            break;
        }

        if c == '.' {
            let extension: String = url.chars().skip(pos).take(total_length - pos).collect();
            let prefix: String = url.chars().take(pos).collect();

            return (prefix, Some(extension));
        }

        if pos == 0 {
            break;
        }

        pos -= 1;
    }

    // no extension.
    (url.to_string(), None)
}

fn add_tag_prefix_if_not_found(mut prefix: String) -> String {
    // find either a / or . whichever comes first.
    let mut last_was_underscore = false;
    for c in prefix.chars().rev() {
        if c == '.' && last_was_underscore {
            // found tags.
            return prefix;
        }

        if c == '/' {
            // no tags, append a ._
            prefix.push_str("._");
            return prefix;
        }

        last_was_underscore = c == '_';
    }

    prefix
}

fn build_scaling_tag(height: f32, width: f32, strategy: &ScalingStrategy) -> String {
    match strategy {
        // ScalingStrategy::ScaleAndCentre(_) => "TODO".to_string(), // Handle later.
        ScalingStrategy::ScaleToWidth => format_scaling_1d_tag("SX", width),
        ScalingStrategy::ScaleToWidthCustom(size) => format_scaling_1d_tag("SX", *size),
        ScalingStrategy::ScaleToHeight => format_scaling_1d_tag("SY", height),
        ScalingStrategy::UpscaleToRectangle => format_scaling_2d_tag("UR", height, width),
        ScalingStrategy::UpscaleToCover => format_scaling_2d_tag("UC", height, width),
        ScalingStrategy::UpscaleToRectangleCustom(width, height) => {
            format_scaling_2d_tag("UR", *height, *width)
        }
        ScalingStrategy::ScaleToRectangle(options) => {
            let mut tag = format_scaling_2d_tag("SR", height, width);

            if options.hide_canvas {
                tag.push_str(",0");
            } else {
                tag.push_str(",1");
            }

            tag.push(',');
            tag.push(options.alignment.to_code());

            tag
        }
    }
}

fn build_rounded_corners_tag(rounded_corners: &RoundedCorners) -> String {
    let display_scale = display::display_scale();

    format!(
        "RO{0},{1},{2},{3},{4}",
        (rounded_corners.radius as f32 * display_scale).round(),
        (rounded_corners.border_width as f32 * display_scale).round(),
        rounded_corners.rgba_border_color,
        rounded_corners.rgba_background_color,
        rounded_corners.corner_mask.to_code()
    )
}

fn build_cropping_tag(strategy: &CroppingStrategy) -> String {
    match strategy {
        CroppingStrategy::AutoCrop => "AC".to_string(),
        CroppingStrategy::Crop(crop_dimensions) => {
            let display_scale = display::display_scale();
            format!(
                "CR{0},{1},{2},{3}",
                (crop_dimensions.x as f32 * display_scale).round(),
                (crop_dimensions.y as f32 * display_scale).round(),
                (crop_dimensions.width as f32 * display_scale).round(),
                (crop_dimensions.height as f32 * display_scale).round()
            )
        }
        CroppingStrategy::CropAnchor(crop_anchor_dimensions) => {
            let display_scale = display::display_scale();
            format!(
                "CA{0}{1},{2},{3}",
                crop_anchor_dimensions.vertical.to_code(),
                crop_anchor_dimensions.horizontal.to_code(),
                (crop_anchor_dimensions.width as f32 * display_scale).round(),
                (crop_anchor_dimensions.height as f32 * display_scale).round()
            )
        }
    }
}

fn prepend_slash(input: &str) -> String {
    if !input.starts_with('/') {
        format!("/{}", input)
    } else {
        input.to_string()
    }
}

fn format_bounding_box(oi: &OverlayImage) -> String {
    let OverlayImage {
        dimensions,
        x_pos,
        y_pos,
        ..
    } = *oi;
    format!(
        "{},{},{},{}",
        x_pos, y_pos, dimensions.width, dimensions.height
    )
}

const CACHE_BUSTING_INTERVAL_MS: u64 = 900000;
fn build_cache_buster_tag() -> String {
    let interval_id = SystemTime::now().duration_since(UNIX_EPOCH);
    if let Ok(interval_id) = interval_id {
        format!(
            "CB{}",
            (interval_id.as_millis() as u64) / CACHE_BUSTING_INTERVAL_MS
        )
    } else {
        "".to_string()
    }
}

fn build_collation_tag(height: f32, width: f32, config: &CollageConfig) -> String {
    let CollageConfig {
        overlay_images,
        final_width,
        final_height,
        identifier_type,
    } = config;
    let base_dimensions = format!("{},{}", width, height);
    let final_dimensions = format!("{},{}", final_width, final_height);
    let overlay_filenames = overlay_images
        .iter()
        .map(|img| match identifier_type {
            MediaCentralIdentifierType::Path => prepend_slash(&img.id),
            MediaCentralIdentifierType::PhysicalId => img.id.clone(),
        })
        .collect::<Vec<_>>()
        .join(",");
    let overlay_bounding_boxes = overlay_images
        .iter()
        .map(format_bounding_box)
        .collect::<Vec<_>>()
        .join("+");

    format!(
        "{}|{}|{}|0,0,{}+{}",
        identifier_type.to_tag(),
        final_dimensions,
        overlay_filenames,
        base_dimensions,
        overlay_bounding_boxes
    )
}

fn build_background_tag(rgba_color: &RGBAColor) -> String {
    let RGBAColor(r, g, b, a) = rgba_color;
    if let Some(alpha) = a {
        format!("BG{},{},{},{}", r, g, b, alpha)
    } else {
        format!("BG{},{},{}", r, g, b)
    }
}

fn build_tag(height: f32, width: f32, tag: &ImageTag) -> String {
    match tag {
        ImageTag::Scaling(strategy) => build_scaling_tag(height, width, strategy),
        ImageTag::Format(format) => format.to_tag().to_string(),
        ImageTag::Brightness(amount) => format!("BR{amount}"),
        ImageTag::RoundedCorners(rounded_corners) => build_rounded_corners_tag(rounded_corners),
        ImageTag::DB => "DB,0".to_string(),
        ImageTag::Collage(config) => build_collation_tag(height, width, config),
        ImageTag::ScalingAlgorithm(algorithm) => format!("AG{:}", algorithm.to_tag()),
        ImageTag::CacheBuster => build_cache_buster_tag(),
        ImageTag::Blur(amount) => format!("BL{:02}", amount.clamp(&1, &99)),
        ImageTag::Background(rgba_color) => build_background_tag(rgba_color),
        ImageTag::Quality(amount) => format!("PQ{}", amount.clamp(&1, &100)),
        ImageTag::Cropping(strategy) => build_cropping_tag(strategy),
    }
}

fn build_tags(
    height: f32,
    width: f32,
    provided_tags: &Vec<ImageTag>,
    additional_tags: &Vec<ImageTag>,
) -> String {
    let mut tags: Vec<String> = Vec::new();

    for tag in provided_tags {
        tags.push(build_tag(height, width, tag));
    }

    for tag in additional_tags {
        tags.push(build_tag(height, width, tag));
    }

    let mut result = tags.join("_");
    result.push('_');
    result
}

fn format_scaling_2d_tag(prefix: &str, height: f32, width: f32) -> String {
    let mut tag = prefix.to_string();
    tag.push_str(&(width * display::display_scale()).round().to_string());
    tag.push(',');
    tag.push_str(&(height * display::display_scale()).round().to_string());
    tag
}

fn format_scaling_1d_tag(prefix: &str, value: f32) -> String {
    let mut tag = prefix.to_string();
    tag.push_str(&(value * display::display_scale()).round().to_string());
    tag
}

#[cfg(test)]
mod test {
    use std::time::Duration;

    use super::*;
    use mock_instant::MockClock;
    use rstest::*;

    #[rstest]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![],
        false,
        [
            "https://amazon.com/test/testing123._UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Format(ImageFormat::JPG)],
        false,
        [
            "https://amazon.com/test/testing123._FMjpg_UR200,333_DB,0_.png",
            "https://amazon.com/test/testing123._FMjpg_UR300,500_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Format(ImageFormat::PNG)],
        false,
        [
            "https://amazon.com/test/testing123._FMpng_UR200,333_DB,0_.png",
            "https://amazon.com/test/testing123._FMpng_UR300,500_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![],
        false,
        [
            "https://amazon.com/test/testing123._UR200,333_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._UR300,500_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case::upscale_to_rectangle(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::UpscaleToRectangle)],
        false,
        [
            "https://amazon.com/test/testing123._UR200,333_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._UR300,500_FMjpg_DB,0_.jpg",
        ]
    )]
    #[case::upscale_to_rectangle_1pixel(
        "https://amazon.com/test/testing123.jpg",
        1.0,
        1.0,
        vec![ImageTag::Scaling(ScalingStrategy::UpscaleToRectangle)],
        false,
        [
            "https://amazon.com/test/testing123._UR1,1_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._UR1,1_FMjpg_DB,0_.jpg",
        ]
    )]
    #[case::upscale_to_rectangle_custom(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(100.0, 150.0))],
        false,
        [
            "https://amazon.com/test/testing123._UR67,100_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._UR100,150_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case::upscale_to_rectangle_custom_1pixel(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(1.0, 1.0))],
        false,
        [
            "https://amazon.com/test/testing123._UR1,1_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._UR1,1_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case::scale_to_width_1pixel(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToWidth)],
        false,
        [
            "https://amazon.com/test/testing123._SX200_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SX300_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case::scale_to_width_1pixel(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        1.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToWidth)],
        false,
        [
            "https://amazon.com/test/testing123._SX1_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SX1_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case::scale_to_height(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToHeight)],
        false,
        [
            "https://amazon.com/test/testing123._SY333_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SY500_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case::scale_to_height_1pixel(
        "https://amazon.com/test/testing123.jpg",
        1.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToHeight)],
        false,
        [
            "https://amazon.com/test/testing123._SY1_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SY1_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions { alignment: ImageAlignment::Left, hide_canvas: true }))],
        false,
        [
            "https://amazon.com/test/testing123._SR200,333,0,L_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SR300,500,0,L_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions { alignment: ImageAlignment::Right, hide_canvas: false }))],
        false,
        [
            "https://amazon.com/test/testing123._SR200,333,1,R_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SR300,500,1,R_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions { alignment: ImageAlignment::Bottom, hide_canvas: false }))],
        false,
        [
            "https://amazon.com/test/testing123._SR200,333,1,B_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SR300,500,1,B_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions { alignment: ImageAlignment::Top, hide_canvas: false }))],
        false,
        [
            "https://amazon.com/test/testing123._SR200,333,1,T_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SR300,500,1,T_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.jpg",
        500.0,
        300.0,
        vec![ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions { alignment: ImageAlignment::Centered, hide_canvas: false }))],
        false,
        [
            "https://amazon.com/test/testing123._SR200,333,1,C_FMjpg_DB,0_.jpg",
            "https://amazon.com/test/testing123._SR300,500,1,C_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123",
        500.0,
        300.0,
        vec![],
        false,
        [
            "https://amazon.com/test/testing123._UR200,333_FMjpg_DB,0_",
            "https://amazon.com/test/testing123._UR300,500_FMjpg_DB,0_"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123._EXISTING_TAG_.png",
        500.0,
        300.0,
        vec![],
        false,
        [
            "https://amazon.com/test/testing123._EXISTING_TAG_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._EXISTING_TAG_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://m.media-amazon.com/sub.folder/image.jpg",
        500.0,
        300.0,
        vec![],
        false,
        [
            "https://m.media-amazon.com/sub.folder/image._UR200,333_FMjpg_DB,0_.jpg",
            "https://m.media-amazon.com/sub.folder/image._UR300,500_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "",
        500.0,
        300.0,
        vec![],
        false,
        ["", ""]
    )]
    #[case(
        "https://m.media-amazon.com/sub.folder/image.jpg",
        500.0,
        300.0,
        vec![ImageTag::Cropping(CroppingStrategy::AutoCrop), ImageTag::Blur(6)],
        false,
        [
        "https://m.media-amazon.com/sub.folder/image._AC_BL06_UR200,333_FMjpg_DB,0_.jpg",
        "https://m.media-amazon.com/sub.folder/image._AC_BL06_UR300,500_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://m.media-amazon.com/sub.folder/image.jpg",
        500.0,
        300.0,
        vec![ImageTag::Cropping(CroppingStrategy::AutoCrop), ImageTag::Brightness(-6)],
        false,
        [
            "https://m.media-amazon.com/sub.folder/image._AC_BR-6_UR200,333_FMjpg_DB,0_.jpg",
            "https://m.media-amazon.com/sub.folder/image._AC_BR-6_UR300,500_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://m.media-amazon.com/sub.folder/image.jpg",
        500.0,
        300.0,
        vec![ImageTag::Cropping(CroppingStrategy::AutoCrop), ImageTag::Brightness(20)],
        false,
        [
            "https://m.media-amazon.com/sub.folder/image._AC_BR20_UR200,333_FMjpg_DB,0_.jpg",
            "https://m.media-amazon.com/sub.folder/image._AC_BR20_UR300,500_FMjpg_DB,0_.jpg"
        ]
    )]
    #[case(
        "https://m.media-amazon.com/sub.folder/image.jpg",
        500.0,
        300.0,
        vec![ImageTag::Cropping(CroppingStrategy::AutoCrop), ImageTag::Brightness(20)],
        true,
        [
            "https://m.media-amazon.com/sub.folder/image._AC_BR20_UR200,333_FMjpg_DB,0_CB1_.jpg",
            "https://m.media-amazon.com/sub.folder/image._AC_BR20_UR300,500_FMjpg_DB,0_CB1_.jpg"
        ]
    )]
    #[case(
        "https://m.media-amazon.com/sub.folder/image.jpg",
        500.0,
        300.0,
        vec![],
        true,
        [
            "https://m.media-amazon.com/sub.folder/image._UR200,333_FMjpg_DB,0_CB1_.jpg",
            "https://m.media-amazon.com/sub.folder/image._UR300,500_FMjpg_DB,0_CB1_.jpg"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::AllCorners })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,15_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,15_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::NoCorners })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,0_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,0_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopLeft })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,1_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,1_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,2_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,2_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopLeftRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,3_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,3_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::BottomLeft })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,4_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,4_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopBottomLeft })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,5_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,5_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopRightBottomLeft })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,6_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,6_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopLeftRightBottomLeft })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,7_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,7_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::BottomRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,8_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,8_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopLeftBottomRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,9_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,9_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopBottomRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,10_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,10_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopLeftRightBottomRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,11_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,11_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::BottomLeftRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,12_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,12_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopBottomLeftBottomRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,13_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,13_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::RoundedCorners(RoundedCorners { radius: 20, border_width: 1, rgba_border_color: "45,95,195,250".to_string(), rgba_background_color: "50,100,200,255".to_string(), corner_mask: CornerMask::TopRightBottomLeftRight })],
        false,
        [
            "https://amazon.com/test/testing123._RO13,1,45,95,195,250,50,100,200,255,14_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._RO20,1,45,95,195,250,50,100,200,255,14_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Cropping(CroppingStrategy::Crop(CropDimensions { x: 2, y: 2, width: 300, height: 500 }))],
        false,
        [
            "https://amazon.com/test/testing123._CR1,1,200,333_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._CR2,2,300,500_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Collage(CollageConfig{
            overlay_images: vec![
                OverlayImage{
                    id: "overlay-image-path.png".to_string(),
                    dimensions: Dimensions{
                        width: 100,
                        height: 80,
                    },
                    x_pos: 0,
                    y_pos: 0
                }
            ],
            final_width: 500,
            final_height: 300,
            identifier_type: MediaCentralIdentifierType::Path
        })],
        false,
        [
            "https://amazon.com/test/testing123._CLs|500,300|/overlay-image-path.png|0,0,300,500+0,0,100,80_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._CLs|500,300|/overlay-image-path.png|0,0,300,500+0,0,100,80_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Collage(CollageConfig{
            overlay_images: vec![
                OverlayImage{
                    id: "overlay-image-path.png".to_string(),
                    dimensions: Dimensions{
                        width: 100,
                        height: 80,
                    },
                    x_pos: 0,
                    y_pos: 0
                },
                OverlayImage{
                    id: "/another-image-path.png".to_string(),
                    dimensions: Dimensions{
                        width: 20,
                        height: 40,
                    },
                    x_pos: 10,
                    y_pos: 11
                }
            ],
            final_width: 384,
            final_height: 216,
            identifier_type: MediaCentralIdentifierType::Path
        })],
        false,
        [
            "https://amazon.com/test/testing123._CLs|384,216|/overlay-image-path.png,/another-image-path.png|0,0,300,500+0,0,100,80+10,11,20,40_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._CLs|384,216|/overlay-image-path.png,/another-image-path.png|0,0,300,500+0,0,100,80+10,11,20,40_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case::scaling_algorithm(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::ScalingAlgorithm(ScalingAlgorithmStrategy::Average)],
        false,
        [
            "https://amazon.com/test/testing123._AGaverage_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._AGaverage_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    #[case::background_tag(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Background(RGBAColor(0,0,0,None))],
        false,
        [
            "https://amazon.com/test/testing123._BG0,0,0_UR200,333_FMjpg_.png",
            "https://amazon.com/test/testing123._BG0,0,0_UR300,500_FMjpg_.png"
        ]
    )]
    #[case::background_tag_with_alpha(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Background(RGBAColor(0,0,0,Some(40)))],
        false,
        [
            "https://amazon.com/test/testing123._BG0,0,0,40_UR200,333_FMjpg_.png",
            "https://amazon.com/test/testing123._BG0,0,0,40_UR300,500_FMjpg_.png"
        ]
    )]
    #[case::quality_tag(
        "https://amazon.com/test/testing123.png",
        500.0,
        300.0,
        vec![ImageTag::Quality(50u8)],
        false,
        [
            "https://amazon.com/test/testing123._PQ50_UR200,333_FMjpg_DB,0_.png",
            "https://amazon.com/test/testing123._PQ50_UR300,500_FMjpg_DB,0_.png"
        ]
    )]
    pub fn should_parse_url(
        #[case] url: &str,
        #[case] height: f32,
        #[case] width: f32,
        #[case] tags: Vec<ImageTag>,
        #[case] should_add_cache_buster_tag: bool,
        #[case] expected_urls: [&str; 2],
        #[values(0, 1)] scale_index: usize,
    ) {
        MockClock::set_system_time(Duration::from_secs(1000));
        const SCALES: [f32; 2] = [0.666667, 1.0];

        let scale = SCALES[scale_index];
        let expected_url = expected_urls[scale_index];

        super::display::DISPLAY_SCALE.with(|s| s.set(scale));

        assert_eq!(
            apply_media_central_tags(url, height, width, &tags, should_add_cache_buster_tag),
            expected_url.to_string(),
            "Images don't match at resolution {}p",
            (1080.0 * scale).round()
        );
    }

    #[rstest]
    #[case::empty_url(String::from(""), String::from(""), None)]
    #[case::not_url(String::from("error"), String::from("error"), None)]
    #[case::url_no_extension(
        String::from("http://what/the/url"),
        String::from("http://what/the/url"),
        None
    )]
    #[case::url_extension_broken(
        String::from("http://what/the/url..aaa"),
        String::from("http://what/the/url."),
        Some(String::from(".aaa"))
    )]
    #[case::dot(String::from("."), String::from(""), Some(String::from(".")))]
    pub fn split_extension_with_dodgy_http_string(
        #[case] initial_url: String,
        #[case] expected_url: String,
        #[case] expected_extension: Option<String>,
    ) {
        let (url, extension) = split_extension(&initial_url);
        assert_eq!(&url, &expected_url);
        assert_eq!(extension, expected_extension);
    }
}
