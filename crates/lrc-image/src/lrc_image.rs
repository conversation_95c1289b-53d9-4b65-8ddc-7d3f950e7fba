use ignx_compositron::composables::composable::*;
use ignx_compositron::composables::image::*;
use ignx_compositron::context::safe::*;
use ignx_compositron::reactive::safe::*;
use ignx_compositron::{compose, Composer};
use mockall_double::double;
#[double]
use resiliency_store::ResiliencyStore;

use crate::parser::url::apply_media_central_tags;
use crate::types::ImageData;

pub const LRC_IMAGE_TEST_ID: &str = "lrc-image-test-id";

#[derive(Clone, PartialEq)]
struct ParsedData {
    url: String,
    width: f32,
    height: f32,
}

#[derive(Default)]
pub enum DimensionsType {
    #[default]
    /// `.width` and `.height` will be applied to the `ImageComposable`
    EXACT,
    /// `.max_width` and `.max_height` will be applied to the `ImageComposable`
    MAX,
    /// Use this type, if you need manual control over the `ImageComposable` dimensions.
    CUSTOM,
}

#[Composer]
pub fn LRCImage<'s>(
    ctx: &AppContext<'s>,
    #[into] data: MaybeSignal<'s, ImageData>,
    #[optional] dimensions_type: DimensionsType,
) -> ImageComposable<'s> {
    let should_add_cache_buster_tag = use_context::<ResiliencyStore>(ctx.scope)
        .is_some_and(|rs| rs.get_enable_image_cache_buster());
    let parsed_data: Memo<'s, ParsedData> = create_memo(ctx.scope(), move |_| {
        data.with(|data: &ImageData| ParsedData {
            url: apply_media_central_tags(
                &data.url,
                data.height,
                data.width,
                &data.tags,
                should_add_cache_buster_tag,
            ),
            height: data.height,
            width: data.width,
        })
    });

    let url = Signal::derive(ctx.scope(), move || {
        parsed_data.with(|data| data.url.clone())
    });
    let height = Signal::derive(ctx.scope(), move || parsed_data.with(|data| data.height));
    let width = Signal::derive(ctx.scope(), move || parsed_data.with(|data| data.width));

    #[allow(clippy::disallowed_methods, reason = "core component")]
    let image_composable = compose! {
        Image(url)
            .test_id(LRC_IMAGE_TEST_ID)
    };

    match dimensions_type {
        DimensionsType::EXACT => image_composable.height(height).width(width),
        DimensionsType::MAX => image_composable.max_height(height).max_width(width),
        DimensionsType::CUSTOM => image_composable,
    }
}

#[cfg(test)]
mod tests {
    use ignx_compositron::app::launch_test;
    use ignx_compositron::composables::row::*;
    use ignx_compositron::test_utils::node_properties::NodeTypeProperties;
    use ignx_compositron::test_utils::*;
    use resiliency_store::MockResiliencyStore;
    use rstest::rstest;

    use super::*;

    #[test]
    pub fn should_render_image() {
        launch_test(
            |ctx| {
                compose! { Row() { LRCImage(data: ImageData { url: "https://amazon.com/myimage.png".to_string(), width: 500.0, height: 200.0, tags: vec![] }) } }
            },
            |_scope, mut game_loop| {
                let node_tree = game_loop.tick_until_done();
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();

                assert_node_exists!(&image_node);

                if let NodeTypeProperties::Image(image_props) =
                    image_node.get_props().node_type_props
                {
                    assert_eq!(
                        image_props.uri,
                        Some("https://amazon.com/myimage._UR500,200_FMjpg_DB,0_.png".to_string()) // JPG and DB tags set by default
                    );
                } else {
                    panic!("node is not an image node");
                }
            },
        )
    }

    #[test]
    pub fn should_use_width_height_by_default() {
        launch_test(
            |ctx| {
                compose! {
                    Row() {
                        LRCImage(
                            data: ImageData { url: "https://amazon.com/myimage.png".to_string(), width: 500.0, height: 200.0, tags: vec![] }
                        )
                    }
                }
            },
            |_scope, mut game_loop| {
                let node_tree = game_loop.tick_until_done();
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();

                assert_node_exists!(&image_node);

                let props = image_node.borrow_props();
                let layout = &props.layout;

                // Even though the image is not loaded, the layout of this Composable should still be 500x200
                assert_eq!(layout.size.width, 500.0);
                assert_eq!(layout.size.height, 200.0);
            },
        )
    }

    #[rstest]
    #[case(
        true,
        true,
        "https://amazon.com/myimage._UR500,200_FMjpg_DB,0_CB0_.png"
    )]
    #[case(true, false, "https://amazon.com/myimage._UR500,200_FMjpg_DB,0_.png")]
    #[case(false, false, "https://amazon.com/myimage._UR500,200_FMjpg_DB,0_.png")]
    pub fn should_apply_cache_buster_tag_if_grogu_config_is_enabled(
        #[case] resiliency_config_available: bool,
        #[case] cache_buster_config_enabled: bool,
        #[case] expected_url: &'static str,
    ) {
        launch_test(
            move |ctx| {
                setup_resiliency_store(
                    ctx.scope,
                    resiliency_config_available,
                    cache_buster_config_enabled,
                );
                compose! { Row() { LRCImage(data: ImageData { url: "https://amazon.com/myimage.png".to_string(), width: 500.0, height: 200.0, tags: vec![] }) } }
            },
            |_scope, mut game_loop| {
                let node_tree = game_loop.tick_until_done();
                let image_node = node_tree
                    .find_by_props()
                    .composable_type(ComposableType::Image)
                    .find_first();

                assert_node_exists!(&image_node);

                if let NodeTypeProperties::Image(image_props) =
                    image_node.get_props().node_type_props
                {
                    assert_eq!(image_props.uri, Some(expected_url.to_string()));
                } else {
                    panic!("node is not an image node");
                }
            },
        )
    }

    fn setup_resiliency_store<'s>(
        scope: Scope<'s>,
        resiliency_config_available: bool,
        enabled: bool,
    ) {
        if resiliency_config_available {
            let mut mock_resiliency_store = MockResiliencyStore::default();

            mock_resiliency_store.expect_clone().returning(move || {
                let mut clone = MockResiliencyStore::default();
                clone
                    .expect_get_enable_image_cache_buster()
                    .return_const(enabled);
                clone
            });

            provide_context::<MockResiliencyStore>(scope, mock_resiliency_store);
        }
    }
}
