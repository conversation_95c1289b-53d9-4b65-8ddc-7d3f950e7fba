[package]
name = "lrc-image"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
resiliency-store.workspace = true
mockall_double.workspace = true
cfg-test-attr-derive.workspace = true

[lints]
workspace = true

[dev-dependencies]
rstest.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }
mock_instant.workspace = true

[features]
debug_impl = []
