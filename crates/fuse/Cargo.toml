[package]
name = "fuse"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
serde_json.workspace = true
serde.workspace = true
common-transform-types.workspace = true
collections-ui-signals.workspace = true
cache.workspace = true
fableous.workspace = true
router.workspace = true
network.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
mockall.workspace = true
log.workspace = true
cfg-test-attr-derive.workspace = true
app-config.workspace = true
strum.workspace = true
strum_macros.workspace = true
mockall_double.workspace = true
uuid.workspace = true
auth.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
    "mock_timer",
] }
network = { workspace = true, features = ["test_utils", "mock_network"] }
rstest.workspace = true
serial_test.workspace = true
mockall.workspace = true
cache = { workspace = true, features = ["test-utils"] }

[features]
debug_impl = []
