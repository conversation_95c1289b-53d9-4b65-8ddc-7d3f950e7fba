use crate::common_string_ids::*;
use crate::network::traits::FuseCustomerAcquisition;

#[cfg(test)]
use crate::network::traits::MockNetworkClient as NetworkClient;
use crate::network::types::*;
use cache::cache_invalidation::emitter::CacheInvalidationEventEmitterRc;
use cache::cache_invalidation::events::CacheInvalidationEvent;
use collections_ui_signals::CollectionsPageStateOverrides;
use fableous::toasts::toast_context::ToastContext;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::metric;
use ignx_compositron::prelude::{use_context, Scope, SignalSet};
use ignx_compositron::text::{LocalizedText, TextContent};
#[cfg(not(test))]
use network::NetworkClient;
use std::rc::Rc;
use std::str::FromStr;

const FUSE_OFFER_ACTIVATION_SUCCESS_METRIC: &str = "FuseOfferActivation.Success";
const FUSE_OFFER_ACTIVATION_FAILURE_METRIC: &str = "FuseOfferActivation.Failed";
const COMPLETED: &str = "COMPLETED";
const ERROR: &str = "ERROR";

/// Activates the eligible fuse offer for customer using data_share_eligibility_acquisition_workflow API
///
/// # Arguments
///
/// * `ctx` - Application context containing network client and scope information
/// * `on_finally` - Callback function to execute after the activation attempt completes
///
/// # Effects
///
/// - Makes network request to activate fuse offer
/// - Shows success/failure toast messages
/// - Tracks metrics for success/failure
/// - Executes provided callback when complete
pub fn activate_fuse_offer(ctx: &AppContext, on_finally: Rc<dyn Fn()>) {
    let client = NetworkClient::new(ctx);
    let scope = ctx.scope();

    let success_callback = {
        let on_finally: Rc<dyn Fn()> = on_finally.clone();
        move |response: DSEAResponse| {
            on_finally();

            if response.statusCode.as_deref() == Some(COMPLETED) {
                invalidate_cache_and_reload_page(scope);
                log::info!("[fuse::fuse_offer_activation] Fuse offer activation successful");
                metric!(FUSE_OFFER_ACTIVATION_SUCCESS_METRIC, 1);
            } else {
                log::info!(
                    "[fuse::fuse_offer_activation] Fuse offer activation failed. {:?}",
                    response
                );
                metric!(FUSE_OFFER_ACTIVATION_FAILURE_METRIC, 1);
            }

            set_toast_context(scope, get_toast_message(&response));
        }
    };

    let failure_callback = move |err| {
        on_finally();
        set_toast_context(
            scope,
            TextContent::LocalizedText(LocalizedText::new(AV_LRC_FUSE_OFFER_ACTIVATION_FAILURE)),
        );
        log::error!(
            "[fuse::fuse_offer_activation] Fuse offer activation failed. {:?}",
            err
        );
        metric!(FUSE_OFFER_ACTIVATION_FAILURE_METRIC, 1);
    };

    client.data_share_eligibility_acquisition_workflow(success_callback, failure_callback);
}

/// Invalidates cache and then reloads the current page
///
/// # Arguments
///
/// * `scope` - The compositron scope containing navigation and cache contexts
///
/// # Effects
///
/// - Invalidates the page cache by emitting an AcquisitionStart event
/// - Sets `should_reload` signal to true which reloads the collections page
///
/// # Notes
///
/// - Requires CollectionsPageStateOverrides and CacheInvalidationEventEmitter contexts to be present in scope
/// - Logs errors if required contexts are missing
fn invalidate_cache_and_reload_page(scope: Scope) {
    use_context::<CacheInvalidationEventEmitterRc>(scope)
    .map_or_else(
        || log::error!("[fuse::fuse_offer_activation] CacheInvalidationEventEmitter not found in scope"),
        |event_emitter| {
            event_emitter
                .try_borrow()
                .map_or_else(
                    |_| log::error!("[fuse::fuse_offer_activation] CacheInvalidationEventEmitter couldn't be borrowed"),
                    |emitter| {
                        emitter.emit(CacheInvalidationEvent::AcquisitionStart);
                    },
                )
        }
    );
    use_context::<CollectionsPageStateOverrides>(scope).map_or_else(
        || {
            log::error!(
                "[fuse::fuse_offer_activation] CollectionsPageStateOverrides not found in scope"
            )
        },
        |collections_page_state_overrides| {
            collections_page_state_overrides.should_reload.set(true);
        },
    );
}

fn set_toast_context(scope: Scope, message: TextContent) {
    match use_context::<ToastContext>(scope) {
        Some(toast_context) => {
            toast_context.message.set(Some(message));
            toast_context.visible.set(true);
        }
        None => log::error!("[fuse::fuse_offer_activation] ToastContext not found in scope"),
    }
}

fn get_toast_message(response: &DSEAResponse) -> TextContent {
    match response.statusCode.as_deref() {
        Some(COMPLETED) => {
            TextContent::LocalizedText(LocalizedText::new(AV_LRC_FUSE_OFFER_ACTIVATION_SUCCESS))
        }
        Some(ERROR) => {
            if response
                .errorCode
                .as_ref()
                .map(|code| IneligibilityErrorCode::from_str(code).is_ok())
                .unwrap_or(false)
            {
                TextContent::LocalizedText(LocalizedText::new(AV_LRC_FUSE_OFFER_INELIGIBLE))
            } else {
                TextContent::LocalizedText(LocalizedText::new(AV_LRC_FUSE_OFFER_ACTIVATION_FAILURE))
            }
        }
        _ => TextContent::LocalizedText(LocalizedText::new(AV_LRC_FUSE_OFFER_ACTIVATION_FAILURE)),
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use std::cell::RefCell;
    use std::sync::Arc;

    use crate::network::traits::MockNetworkClient;
    use crate::network::traits::__mock_MockNetworkClient::__new::Context;
    use cache::cache_invalidation::emitter::CacheInvalidationEventEmitter;
    use ignx_compositron::prelude::{use_context, SignalGet};
    use ignx_compositron::{
        app::launch_only_app_context, context::AppContext, reactive::provide_context,
    };
    use network::RequestError;
    use serial_test::serial;

    fn mock_context(ctx: &AppContext) {
        provide_context::<ToastContext>(ctx.scope(), ToastContext::new(ctx.scope()));
        provide_context::<CollectionsPageStateOverrides>(
            ctx.scope(),
            CollectionsPageStateOverrides::new(ctx.scope()),
        );
    }

    fn mock_event_emitter(scope: Scope) -> Rc<RefCell<CacheInvalidationEventEmitter>> {
        let invalidation_event_emitter = CacheInvalidationEventEmitter::new_rc_without_rpc();
        provide_context::<CacheInvalidationEventEmitterRc>(
            scope,
            invalidation_event_emitter.clone(),
        );
        invalidation_event_emitter
    }

    fn mock_listener_callback() -> (Rc<RefCell<i32>>, Box<dyn Fn(CacheInvalidationEvent)>) {
        let times_called = Rc::new(RefCell::new(0));
        let callback = {
            let times_called = times_called.clone();
            Box::new(move |_event| {
                times_called.replace_with(|&mut old| old + 1);
            })
        };
        (times_called, callback)
    }

    fn mock_on_finally() -> (Rc<RefCell<i32>>, Rc<dyn Fn()>) {
        let times_called = Rc::new(RefCell::new(0));
        let on_finally: Rc<dyn Fn()> = {
            let times_called = times_called.clone();
            Rc::new(move || {
                times_called.replace_with(|&mut old| old + 1);
            }) as Rc<dyn Fn()>
        };
        (times_called, on_finally)
    }

    fn mock_network_client(response: DSEAResponse) -> Context {
        let response = Arc::new(response);
        let mock_client_context: Context = MockNetworkClient::new_context();
        mock_client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();
            let response = Arc::clone(&response);
            mock_client
                .expect_data_share_eligibility_acquisition_workflow()
                .times(1)
                .returning(move |success_cb, _| success_cb((*response).clone()));
            mock_client
        });
        mock_client_context
    }

    #[test]
    #[serial]
    fn should_show_failure_toast_when_request_failed() {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let mock_client_context: Context = MockNetworkClient::new_context();
            mock_client_context.expect().returning(move |_| {
                let mut mock_client = MockNetworkClient::default();
                mock_client
                    .expect_data_share_eligibility_acquisition_workflow()
                    .times(1)
                    .returning(|_, failure_cb| {
                        failure_cb(RequestError::Builder("Request Error".to_string()))
                    });
                mock_client
            });

            // Act
            activate_fuse_offer(&ctx, on_finally);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();
            let expected_message = TextContent::LocalizedText(LocalizedText::new(
                "AV_LRC_FUSE_OFFER_ACTIVATION_FAILURE",
            ));

            assert_eq!(*times_called.borrow(), 1);
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }

    #[test]
    #[serial]
    fn should_show_success_toast_when_status_completed() {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let response = DSEAResponse {
                requestId: "test_id".to_string(),
                workflowExecutionId: "test_workflow".to_string(),
                isFlowComplete: true,
                errorMessage: None,
                errorCode: None,
                statusCode: Some(COMPLETED.to_string()),
                clickStreamMetrics: ClickStreamMetrics {
                    pageType: "test".to_string(),
                    subPageType: None,
                    subSubPageType: None,
                    pageAction: None,
                    pageTypeId: None,
                    pageTypeIdSource: None,
                },
                viewData: None,
                customerOptions: None,
            };
            let _mock_client = mock_network_client(response);
            let event_emitter = mock_event_emitter(ctx.scope());
            let (listner_called, listener) = mock_listener_callback();
            event_emitter.borrow_mut().subscribe(ctx.scope(), listener);

            // Act
            activate_fuse_offer(&ctx, on_finally);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();
            let expected_message = TextContent::LocalizedText(LocalizedText::new(
                "AV_LRC_FUSE_OFFER_ACTIVATION_SUCCESS",
            ));
            let reload_page_signal = use_context::<CollectionsPageStateOverrides>(ctx.scope())
                .unwrap()
                .should_reload;

            assert!(reload_page_signal.get()); // asserts that reload_page signal was set to true
            assert_eq!(*listner_called.borrow(), 1); // asserts that CacheInvalidationEvent was emitted once
            assert_eq!(*times_called.borrow(), 1); // asserts on_finally callback was called once
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }

    #[test]
    #[serial]
    fn should_show_failure_toast_when_status_invalid() {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let response = DSEAResponse {
                requestId: "test_id".to_string(),
                workflowExecutionId: "test_workflow".to_string(),
                isFlowComplete: true,
                errorMessage: None,
                errorCode: None,
                statusCode: Some("INVALID".to_string()),
                clickStreamMetrics: ClickStreamMetrics {
                    pageType: "test".to_string(),
                    subPageType: None,
                    subSubPageType: None,
                    pageAction: None,
                    pageTypeId: None,
                    pageTypeIdSource: None,
                },
                viewData: None,
                customerOptions: None,
            };
            let _mock_client = mock_network_client(response);

            // Act
            activate_fuse_offer(&ctx, on_finally);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();
            let expected_message = TextContent::LocalizedText(LocalizedText::new(
                "AV_LRC_FUSE_OFFER_ACTIVATION_FAILURE",
            ));

            assert_eq!(*times_called.borrow(), 1);
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }

    mod when_status_error {
        use super::*;

        #[test]
        #[serial]
        fn should_show_failure_toast_when_error_code_is_customer_ineligible() {
            let (times_called, on_finally) = mock_on_finally();
            launch_only_app_context(move |ctx| {
                // Setup
                mock_context(&ctx);
                let response = DSEAResponse {
                    requestId: "test_id".to_string(),
                    workflowExecutionId: "test_workflow".to_string(),
                    isFlowComplete: true,
                    errorMessage: None,
                    errorCode: Some("CUSTOMER_INELIGIBLE".to_string()),
                    statusCode: Some(ERROR.to_string()),
                    clickStreamMetrics: ClickStreamMetrics {
                        pageType: "test".to_string(),
                        subPageType: None,
                        subSubPageType: None,
                        pageAction: None,
                        pageTypeId: None,
                        pageTypeIdSource: None,
                    },
                    viewData: None,
                    customerOptions: None,
                };
                let _mock_client = mock_network_client(response);

                // Act
                activate_fuse_offer(&ctx, on_finally);

                // Assertions
                let visible = use_context::<ToastContext>(ctx.scope())
                    .unwrap()
                    .visible
                    .get();
                let message = use_context::<ToastContext>(ctx.scope())
                    .unwrap()
                    .message
                    .get();
                let expected_message =
                    TextContent::LocalizedText(LocalizedText::new("AV_LRC_FUSE_OFFER_INELIGIBLE"));

                assert_eq!(*times_called.borrow(), 1);
                assert_eq!(visible, true);
                assert_eq!(message.unwrap(), expected_message);
            })
        }

        #[test]
        #[serial]
        fn should_show_failure_toast_when_error_code_is_invalid() {
            let (times_called, on_finally) = mock_on_finally();
            launch_only_app_context(move |ctx| {
                // Setup
                mock_context(&ctx);
                let response = DSEAResponse {
                    requestId: "test_id".to_string(),
                    workflowExecutionId: "test_workflow".to_string(),
                    isFlowComplete: true,
                    errorMessage: None,
                    errorCode: Some("INVALID_ERROR_CODE".to_string()),
                    statusCode: Some(ERROR.to_string()),
                    clickStreamMetrics: ClickStreamMetrics {
                        pageType: "test".to_string(),
                        subPageType: None,
                        subSubPageType: None,
                        pageAction: None,
                        pageTypeId: None,
                        pageTypeIdSource: None,
                    },
                    viewData: None,
                    customerOptions: None,
                };
                let _mock_client = mock_network_client(response);

                // Act
                activate_fuse_offer(&ctx, on_finally);

                // Assertions
                let visible = use_context::<ToastContext>(ctx.scope())
                    .unwrap()
                    .visible
                    .get();
                let message = use_context::<ToastContext>(ctx.scope())
                    .unwrap()
                    .message
                    .get();
                let expected_message = TextContent::LocalizedText(LocalizedText::new(
                    "AV_LRC_FUSE_OFFER_ACTIVATION_FAILURE",
                ));

                assert_eq!(*times_called.borrow(), 1);
                assert_eq!(visible, true);
                assert_eq!(message.unwrap(), expected_message);
            })
        }
    }
}
