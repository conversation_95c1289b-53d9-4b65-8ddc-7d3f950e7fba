#![allow(nonstandard_style, reason = "Follows JSON pattern")]
use std::collections::HashMap;

use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::*;
use serde::{Deserialize, Serialize};
use strum_macros::EnumString;

#[derive(Serialize)]
pub struct DSEARequest {
    pub workflowName: String,
    pub requestId: String,
    pub clientData: ClientData,
}

#[derive(Serialize)]
pub struct ClientData {
    pub marketplaceId: String,
    pub ipAddress: String,
    pub geoLocation: String,
    pub activationMetadata: ActivationMetadata,
}

#[derive(Serialize)]
pub struct ActivationMetadata {
    pub activationMode: ActivationMode,
    pub autoActivationTriggerSource: AutoActivationTriggerSource,
}

#[derive(Serialize)]
pub enum ActivationMode {
    CUSTOMER_INITIATED_ACTIVATION,
}

#[derive(Serialize)]
#[allow(clippy::upper_case_acronyms)]
pub enum AutoActivationTriggerSource {
    AVLRC,
}

#[derive(Deserialize, Clone, NetworkParsed, Debug)]
#[derive_test_only(PartialEq)]
pub struct DSEAResponse {
    pub requestId: String,
    pub workflowExecutionId: String,
    pub isFlowComplete: bool,
    pub errorMessage: Option<String>,
    pub errorCode: Option<String>,
    pub statusCode: Option<String>,
    pub clickStreamMetrics: ClickStreamMetrics,
    pub viewData: Option<HashMap<String, String>>,
    pub customerOptions: Option<Vec<String>>,
}

#[derive(Deserialize, Clone, NetworkParsed, Debug)]
#[derive_test_only(PartialEq)]
pub struct ClickStreamMetrics {
    pub pageType: String,
    pub subPageType: Option<String>,
    pub subSubPageType: Option<String>,
    pub pageAction: Option<String>,
    pub pageTypeId: Option<String>,
    pub pageTypeIdSource: Option<String>,
}

#[derive(Debug, PartialEq, EnumString)]
pub enum IneligibilityErrorCode {
    CUSTOMER_INELIGIBLE,
    ALREADY_SUBSCRIBED,
}
