use std::rc::Rc;

use crate::network::types::*;
use ignx_compositron::network::http::HttpMethod;
use ignx_compositron::prelude::metric;
use mockall::mock;
use network::common::{DeviceProxyResponse, LRCEdgeResponse};
use network::NetworkClient;
use network::{RequestError, URLBuilder};
use network_parser::core::network_parse_from_str;
use uuid::Uuid;

const FUSE_DSEA_URL: &str = "/cdp/fuse/fcas/DataShareEligibilityAcquisitionWorkflow";
const DUMMY_IP_ADDRESS: &str = "127.0.0.1";
const WORKFLOW_NAME: &str = "DSEAFlow";
const FUSE_OFFER_ACTIVATION_LATENCY_METRIC: &str = "FuseOfferActivation.Latency";

pub trait FuseCustomerAcquisition {
    fn data_share_eligibility_acquisition_workflow<S, F>(
        &self,
        success_callback: S,
        failure_callback: F,
    ) where
        S: FnOnce(DSEAResponse) + 'static,
        F: FnOnce(RequestError) + 'static;
}

/// A trait for handling Fuse customer acquisition workflows.
///
/// This trait provides functionality for initiating and managing data share eligibility
/// acquisition workflows for customer acquisition through Fuse.
///
/// # Methods
/// - `data_share_eligibility_acquisition_workflow`: Initiates a data share eligibility check workflow
///   with success and failure callback handlers
impl FuseCustomerAcquisition for NetworkClient {
    fn data_share_eligibility_acquisition_workflow<S, F>(
        &self,
        success_callback: S,
        failure_callback: F,
    ) where
        S: FnOnce(DSEAResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
        let url = match URLBuilder::for_device_proxy(
            FUSE_DSEA_URL,
            vec![],
            false,
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
        ) {
            Ok(url) => url,
            Err(e) => {
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let marketplace_id = self.app_config.get_marketplace_id().unwrap_or_default();
        let geo_location = self.app_config.get_geo_location().unwrap_or_default();
        let uuid = Uuid::new_v4();

        let request = DSEARequest {
            workflowName: WORKFLOW_NAME.to_string(),
            requestId: uuid.to_string(),
            clientData: ClientData {
                marketplaceId: marketplace_id,
                ipAddress: DUMMY_IP_ADDRESS.to_string(),
                geoLocation: geo_location,
                activationMetadata: ActivationMetadata {
                    activationMode: ActivationMode::CUSTOMER_INITIATED_ACTIVATION,
                    autoActivationTriggerSource: AutoActivationTriggerSource::AVLRC,
                },
            },
        };

        let body: String = match serde_json::to_string(&request) {
            Ok(body) => body,
            Err(err) => {
                return failure_callback(RequestError::Builder(err.to_string()));
            }
        };

        let success_cb = move |v: DSEAResponse, _| {
            success_callback(v);
        };

        let failure_cb = move |e: RequestError, _| {
            failure_callback(e);
        };

        let parser = |_: &AppContext,
                      response: String,
                      _status_code: i32,
                      cb: Box<
            dyn FnOnce(Result<DeviceProxyResponse<DSEAResponse>, RequestError>),
        >| {
            cb(network_parse_from_str::<DSEAResponse>(&response)
                .map(LRCEdgeResponse::with_resource)
                .map(DeviceProxyResponse::LRCEdgeResponse)
                .map_err(RequestError::DeserializationFailed))
        };

        #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-785")]
        let builder = self
            .builder(url, HttpMethod::Post, "FuseCustomerAcquisition")
            .data(body)
            .with_device_proxy_headers()
            .with_network_latency(Rc::new(move |_start_time, duration| {
                metric!(
                    FUSE_OFFER_ACTIVATION_LATENCY_METRIC,
                    duration.as_millis() as f64
                )
            }))
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb))
            .with_parser(parser);

        builder.execute();
    }
}

#[cfg(test)]
use crate::network::types::DSEAResponse;
use ignx_compositron::context::AppContext;
mock! {

    pub NetworkClient {
        pub fn new(ctx: &AppContext) -> Self;
    }
    impl FuseCustomerAcquisition for NetworkClient {
        fn data_share_eligibility_acquisition_workflow<S, F>(
            &self,
            success_callback: S,
            failure_callback: F,
        ) where
            S: FnOnce(DSEAResponse) + 'static,
            F: FnOnce(RequestError) + 'static;
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use app_config::{AppConfigContext, MockAppConfig};
    use auth::{AuthContext, MockAuth};
    use ignx_compositron::{
        app::launch_only_app_context, context::AppContext, network::http::MockHttpRequestContext,
        reactive::provide_context,
    };

    use router::{MockRouting, RoutingContext};
    use serial_test::*;
    use std::cell::RefCell;

    fn mock_context(ctx: &AppContext) {
        let mock_routing_context = MockRouting::new();
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
        let mut mock_app_config = MockAppConfig::default();
        mock_app_config
            .expect_get_ux_locale()
            .returning(|| "en_GB".into());
        mock_app_config
            .expect_get_geo_location()
            .returning(|| Some("IE".into()));
        mock_app_config
            .expect_get_marketplace_id()
            .returning(|| Some("IE".into()));
        mock_app_config
            .expect_get_supported_locales()
            .returning(|| vec!["de_DE".into(), "en_US".into(), "fr_FR".into()]);
        mock_app_config
            .expect_get_base_url()
            .return_once(|| Some("https://base_url.com".to_string()));
        provide_context::<AppConfigContext>(ctx.scope(), Rc::new(mock_app_config));

        let auth = Rc::new(MockAuth::new_without_params(ctx.scope()));
        provide_context::<AuthContext>(ctx.scope, auth);
    }

    fn mock_success_callback() -> (Rc<RefCell<i32>>, Box<dyn FnOnce(DSEAResponse) + 'static>) {
        let times_called = Rc::new(RefCell::new(0));
        let callback = {
            let times_called = times_called.clone();
            Box::new(move |_dsea_response: DSEAResponse| {
                times_called.replace_with(|&mut old| old + 1);
            })
        };
        (times_called, callback)
    }

    fn mock_error_callback() -> (Rc<RefCell<i32>>, Box<dyn FnOnce(RequestError) + 'static>) {
        let times_called = Rc::new(RefCell::new(0));
        let callback = {
            let times_called = times_called.clone();
            Box::new(move |_e| {
                times_called.replace_with(|&mut old| old + 1);
            })
        };
        (times_called, callback)
    }

    #[test]
    #[serial]
    fn should_call_network_client_with_right_params() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);

            let network_client = NetworkClient::new(&ctx);
            network_client.data_share_eligibility_acquisition_workflow(|_| {}, |_| {});

            let props = MockHttpRequestContext::get();

            assert_eq!(props.method, HttpMethod::Post);
            assert_eq!(
                props.url,
                "https://base_url.com/cdp/fuse/fcas/DataShareEligibilityAcquisitionWorkflow?gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB"
            );

            let mut headers = props.headers.clone();
            headers.sort();

            let mut expected_headers = vec![
                "x-client-app: avlrc",
                "accept: application/json",
                "x-client-version: unknown-version",
                "content-type: application/json",
                "x-request-priority: CRITICAL",
            ];

            expected_headers.sort();

            assert_eq!(headers, expected_headers);
            let sent_data = props.sent_data.unwrap();
            let parts: Vec<&str> = sent_data.split("requestId").collect();
            let before_uuid = parts[0];
            let after_uuid = parts[1].split_once(",").unwrap().1;
            assert_eq!(before_uuid, "{\"workflowName\":\"DSEAFlow\",\"");
            assert_eq!(
                after_uuid,
                "\"clientData\":{\"marketplaceId\":\"IE\",\"ipAddress\":\"127.0.0.1\",\"geoLocation\":\"IE\",\"activationMetadata\":{\"activationMode\":\"CUSTOMER_INITIATED_ACTIVATION\",\"autoActivationTriggerSource\":\"AVLRC\"}}}"
            );
        })
    }

    #[test]
    #[serial]
    fn should_invoke_success_callback_when_success() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);

            let network_client = NetworkClient::new(&ctx);

            let (success_callback_times_called, success_callback) = mock_success_callback();
            let (failure_callback_times_called, failure_callback) = mock_error_callback();

            network_client
                .data_share_eligibility_acquisition_workflow(success_callback, failure_callback);

            let props = MockHttpRequestContext::get();

            let input = include_str!("../../assets/valid_response.json");

            // Act
            props.invoke_callback(Some(input.into()), 200);

            // Assert
            assert_eq!(*success_callback_times_called.borrow(), 1);
            assert_eq!(*failure_callback_times_called.borrow(), 0);
        })
    }

    #[test]
    #[serial]
    fn should_invoke_failure_callback_when_error() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);
            let network_client = NetworkClient::new(&ctx);
            let (success_callback_times_called, success_callback) = mock_success_callback();
            let (failure_callback_times_called, failure_callback) = mock_error_callback();
            network_client
                .data_share_eligibility_acquisition_workflow(success_callback, failure_callback);
            let props = MockHttpRequestContext::get();

            // Act
            props.invoke_callback(Some("Error".to_string()), 502);

            // Assert
            assert_eq!(*success_callback_times_called.borrow(), 0);
            assert_eq!(*failure_callback_times_called.borrow(), 1);
        })
    }
}
