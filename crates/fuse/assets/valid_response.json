{"requestId": "e6aedb36-8f59-4dfd-8ce6-a902c1c5a54a", "workflowExecutionId": "0c3f17b1-ef06-4550-85cc-3d13c50a8e66", "isFlowComplete": true, "errorMessage": "No entitlements available for customer: A3HQP4SYD5Z5V1", "errorCode": "CUSTOMER_INELIGIBLE", "statusCode": "ERROR", "clickStreamMetrics": {"pageType": "ERROR_CUSTOMER_INELIGIBLE", "subPageType": "BOT_FILTER", "subSubPageType": "CUSTOMER_INELIGIBLE", "pageAction": null, "pageTypeId": "0c3f17b1-ef06-4550-85cc-3d13c50a8e66", "pageTypeIdSource": "FUSE_WORKFLOW_EXECUTION_ID"}, "viewData": null, "customerOptions": null}