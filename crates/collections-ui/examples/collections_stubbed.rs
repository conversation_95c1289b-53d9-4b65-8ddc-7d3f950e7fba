#![cfg(feature = "example_data")]

use amzn_fable_tokens::*;
use collections_ui::examples_utils::MockNetworkClient;
use collections_ui::examples_utils::__mock_MockNetworkClient::__new::Context;
use collections_ui::examples_utils::collections_response_parser;
use collections_ui::page::collections_page::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::app::launch_composable;
use ignx_compositron::compose;
use ignx_compositron::prelude::*;
use ignx_compositron::time::Instant;
use location::RustPage::RUST_COLLECTIONS;
use location::{Location, LocationWithEffect, PageType};
use media_background::media_background::*;
use media_background::provide_media_background_context;
use media_background::types::MediaBackgroundType;
use mockall::predicate;
use mockall::predicate::function;
use navigation_menu::model::nav_model::TopNavData;
use network::common::DeviceProxyResponse;
use router::{rust_location, MockRouting, RoutingContext};
use std::rc::Rc;
use std::time::Duration;
use title_details::core::create_title_details_signals;
use title_details::core::*;

fn mocked_collections_page_initial_response() -> &'static str {
    let value: Option<&str> = option_env!("COLLECTIONS_STUBBED");
    if let Some(example) = value {
        match example.to_uppercase().as_str() {
            // A hand-crafted response containing all container types handy for testing
            "DEFAULT" => {
                include_str!("assets/all_container_types.json")
            }

            // An example response when navigating to the "Sports" collection page
            "SPORTS" => include_str!("assets/sports_collection_page_example.json"),

            // An example response when subscribed to the "HISTORY PLAY" channel. It also contains
            // a CHANNEL_CARD in the standard hero
            "HISTORY_CHANNEL" => include_str!("assets/history_channel.json"),

            // An example that shows a single link card
            "TRAVELLING_CUSTOMER" => include_str!("assets/travelling_customer.json"),

            // LLM synopsis: Using fallback title text
            // Check out HISTORY_CHANNEL or TRAVELLING_CUSTOMER for the non-expandable cases
            "LLM_SYNOPSIS" => include_str!("assets/llm_synopsis.json"),
            _ => panic!("Unknown example {:}", example),
        }
    } else {
        include_str!("assets/all_container_types.json")
    }
}

fn mock_routing(scope: Scope) {
    let mut mock_routing_context = MockRouting::new();
    mock_routing_context
        .expect_navigate()
        .with(
            function(|loc: &Location| {
                println!("navigate called with {:?}", loc);
                true
            }),
            predicate::always(),
        )
        .return_const(());
    mock_routing_context
        .expect_location()
        .return_const(create_rw_signal(
            scope,
            Location {
                pageType: PageType::Rust(RUST_COLLECTIONS),
                ..Default::default()
            },
        ));
    mock_routing_context
        .expect_location_with_effect()
        .return_const(create_rw_signal(
            scope,
            LocationWithEffect {
                from_location: Some(Location::default()),
                to_location: rust_location!(RUST_COLLECTIONS),
                exit_effect: None,
                enter_effect: None,
            },
        ));
    provide_context::<RoutingContext>(scope, Rc::new(mock_routing_context));
}

fn mock_network_client(scope: Scope) {
    // NetworkClient
    let client_context = MockNetworkClient::new_context();
    client_context.expect().returning(|_| {
        let mut mock_client = MockNetworkClient::default();

        mock_client.expect_collection_initial().returning(
            move |success_cb, _, _, _, _, _, _, _, _, _| {
                let response = mocked_collections_page_initial_response().to_string();
                let page_data = collections_response_parser(response)
                    .map(|result| match result {
                        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                        DeviceProxyResponse::ErrorResponse(_) => {
                            panic!("unexpected error response")
                        }
                    })
                    .unwrap();
                success_cb(page_data, Instant::now());
            },
        );

        // Theoretically we could also mock pagination for all the different cases by handling all the different
        // cases with `.withf(...)`.
        mock_client
            .expect_paginate()
            .returning(move |page, pc, _, _, _| {
                println!(
                    "[{} / {}] `network_client.paginate(...)` called",
                    page,
                    pc.log_name()
                );
            });
        mock_client
    });

    // Technically this context value is not used within the collection page, but it can't be dropped while we want to use the mocked network client.
    provide_context::<Rc<Context>>(scope, Rc::new(client_context));
}

/// To run this example you need to:
/// * Pass in `example_data` as a feature
/// * Use the `COLLECTIONS_STUBBED` environment variable to select a mock response
/// * Comment out `mock_timer` in Cargo.toml
///
/// Example:
/// COLLECTIONS_STUBBED=TRAVELLING_CUSTOMER build-tools/run collections-ui --additional-features example_data --example collections_stubbed
#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        mock_network_client(ctx.scope());
        mock_routing(ctx.scope());

        let update_media_background_rw = create_rw_signal(ctx.scope(), MediaBackgroundType::None);
        let td_signals = create_title_details_signals(ctx.scope());
        let (title_details_data, update_title_details) = td_signals.data.split();

        // FIXME: Hack to fix rendering issues caused by FontManager not knowing the size of text
        let start_rendering = create_rw_signal(ctx.scope(), false);
        ctx.schedule_task(Instant::now() + Duration::from_millis(500), move || {
            start_rendering.set(true);
        });

        let top_nav_data = create_rw_signal(
            ctx.scope(),
            TopNavData::new(
                vec![],
                vec![],
                None,
                Default::default(),
                Rc::new(|_| {}),
                true,
            ),
        );

        let autoplay_enabled = Signal::derive(ctx.scope(), move || false);
        let modal_data = create_rw_signal(ctx.scope(), vec![]);
        let active_profile = create_signal(ctx.scope(), None).0;
        provide_media_background_context(ctx.scope());

        compose! {
            Stack() {
                if start_rendering.get() {
                    Stack() {
                        MediaBackground(incoming_data: update_media_background_rw.read_only(), rtl_enabled: false, autoplay_enabled)
                        TitleDetails(incoming_data: title_details_data, opacity: td_signals.opacity, x_offset: td_signals.x_offset, y_offset: td_signals.y_offset)
                        CollectionsPage(
                            update_title_details,
                            update_media_background_rw,
                            top_nav_data: Signal::from(top_nav_data),
                            modal_data: modal_data.write_only(),
                            active_profile
                        )
                    }
                    .width(SCREEN_WIDTH)
                    .height(SCREEN_HEIGHT)
                    .background_color(get_ignx_color(FableColor::BACKGROUND))
                }
            }
        }
    });
}
