use amzn_fable_tokens::FableColor;
use collections_ui::ui::page_skeleton::*;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::app::launch_composable;
use ignx_compositron::{compose, prelude::*};

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        compose! {
            Stack() {
                CollectionPageSkeleton()
            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
        }
    });
}
