#![cfg(feature = "example_data")]

use collections_ui::examples_utils::standard_carousel_parsing;
use common_transform_types::containers::StandardCarouselDeserialization;
use container_types::ui_signals::{
    CardGridMetadata, CardGridModel, CommonCarouselCardMetadata, ContainerModel,
    StandardCardContainerItemType, StandardCarouselMetadata,
};
use containers::card_grid::card_grid_ui_builder::*;
use fableous::buttons::primary_button::*;
use fableous::cards::sizing::CardDimensions;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::app::launch_composable;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::prelude::*;
use ignx_compositron::text::TextContent;
use ignx_compositron::{compose, compose_option, Composer};
use network_parser::core::network_parse_from_str;
use std::rc::Rc;

#[Composer]
pub(crate) fn GridModelToUI(
    ctx: &AppContext,
    grid: RwSignal<CardGridModel>,
    num_columns: usize,
    scroll: GridScroll,
    on_item_viewed: OnItemViewed,
    on_item_highlighted: OnItemHighlighted,
    on_item_selected: OnItemSelected,
    on_item_focused: OnItemFocused,
    on_item_long_pressed: OnItemLongPressed,
    default_focus_id: Signal<Option<String>>,
) -> StackComposable {
    CardGridUIBuilder::new(
        ctx.clone(),
        grid.into(),
        num_columns,
        scroll,
        on_item_viewed.clone(),
        on_item_highlighted.clone(),
        default_focus_id,
    )
    .with_item_selected(on_item_selected.clone())
    .with_item_focused(on_item_focused.clone())
    .with_item_long_pressed(on_item_long_pressed.clone())
    .build()
}

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let response_str = include_str!("./assets/standard_carousel.json");
        let standard_carousel: StandardCarouselDeserialization =
            network_parse_from_str(response_str).unwrap();
        let standard_carousel = standard_carousel_parsing(&standard_carousel, ctx.scope()).unwrap();
        let grid_data: CardGridModel = match standard_carousel.model.get_untracked() {
            ContainerModel::StandardCarousel(sig) => {
                let common_carousel_metadata = sig.get_untracked().common_carousel_metadata;
                let items = sig.get_untracked().items;
                let StandardCarouselMetadata {
                    tags,
                    title,
                    facet,
                    pagination_link,
                    pagination_pending,
                    see_more_link: _,
                    see_more_link_placement: _,
                } = sig
                    .get_untracked()
                    .standard_carousel_metadata
                    .get_untracked();
                let card_grid_metadata: RwSignal<CardGridMetadata> = create_rw_signal(
                    ctx.scope(),
                    CardGridMetadata {
                        tags,
                        title,
                        facet,
                        pagination_link,
                        pagination_pending,
                    },
                );
                CardGridModel {
                    common_carousel_metadata,
                    card_grid_metadata,
                    items,
                }
            }
            _ => panic!("should be a standard carousel"),
        };
        let grid_data = create_rw_signal(ctx.scope(), grid_data);

        let num_columns = create_rw_signal(ctx.scope(), 4);
        let columns_button_variant: PrimaryButtonVariant = PrimaryButtonVariant::TextSize200(
            TextContent::String("click me to increment grid columns 2 - 5".to_string()),
        );

        let scroll = create_rw_signal(ctx.scope(), GridScroll::None);
        let scroll_button_text = create_rw_signal(ctx.scope(), "none".to_string());
        let scroll_button_variant: Signal<PrimaryButtonVariant> =
            Signal::derive(ctx.scope(), move || {
                PrimaryButtonVariant::TextSize200(TextContent::String(format!(
                    "click me to toggle scroll. Current: {}",
                    scroll_button_text.get()
                )))
            });

        let on_item_selected = Rc::new({
            move |item: RwSignal<StandardCardContainerItemType>, row_idx, column_idx, _, _| {
                let item = item.get_untracked();
                let item_metadata_signal: RwSignal<CommonCarouselCardMetadata> = (&item).into();

                println!(
                    "on_item_selected {} {} {}",
                    item_metadata_signal.get_untracked().id,
                    row_idx,
                    column_idx,
                );
            }
        });

        let on_item_highlighted = Rc::new({
            move |item: RwSignal<StandardCardContainerItemType>,
                  row_idx,
                  column_idx,
                  dimensions: CardDimensions| {
                let item = item.get_untracked();
                let item_metadata_signal: RwSignal<CommonCarouselCardMetadata> = (&item).into();

                println!(
                    "on_item_highlighted {} {} {} {} {}",
                    item_metadata_signal.get_untracked().id,
                    row_idx,
                    column_idx,
                    dimensions.width,
                    dimensions.height
                );
            }
        });

        let on_item_viewed = Rc::new({
            move |data: ViewImpressionData,
                  item: RwSignal<StandardCardContainerItemType>,
                  row_idx,
                  column_idx,
                  dimensions: CardDimensions| {
                let item = item.get_untracked();
                let item_metadata_signal: RwSignal<CommonCarouselCardMetadata> = (&item).into();

                println!(
                    "on_item_viewed {} {} {} {} {} {}",
                    item_metadata_signal.get_untracked().id,
                    data.in_view_stripe,
                    row_idx,
                    column_idx,
                    dimensions.width,
                    dimensions.height
                );
            }
        });

        let on_item_focused = Rc::new({
            move |item: RwSignal<StandardCardContainerItemType>, row_idx, column_idx| {
                let item = item.get_untracked();
                let item_metadata_signal: RwSignal<CommonCarouselCardMetadata> = (&item).into();

                println!(
                    "on_item_focused {} {} {}",
                    item_metadata_signal.get_untracked().id,
                    row_idx,
                    column_idx
                );
            }
        });

        let on_item_long_pressed = Rc::new({
            move |item: RwSignal<StandardCardContainerItemType>, row_idx, column_idx| {
                let item = item.get_untracked();
                let item_metadata_signal: RwSignal<CommonCarouselCardMetadata> = (&item).into();

                println!(
                    "on_item_long_pressed {} {} {}",
                    item_metadata_signal.get_untracked().id,
                    row_idx,
                    column_idx
                );
            }
        });

        compose! {
            Column() {
                Row() {
                    PrimaryButton(variant: columns_button_variant)
                    .on_select(move || {
                        num_columns.update(|n| {
                            let k = (*n + 1) % 6;
                            if k == 0 {
                                *n = 2
                            } else {
                                *n = k
                            }
                        });
                    })

                    PrimaryButton(variant: scroll_button_variant)
                    .on_select(move || {
                        match scroll.get_untracked() {
                            GridScroll::None => {
                                scroll.set(GridScroll::AtFirstRow);
                                scroll_button_text.set("at first row".to_string());
                            },
                            GridScroll::AtFirstRow => {
                                scroll.set(GridScroll::AtEnds);
                                scroll_button_text.set("at ends".to_string());
                            },
                            GridScroll::AtEnds => {
                                scroll.set(GridScroll::None);
                                scroll_button_text.set("none".to_string());
                            },
                        }
                    })
                }
                .main_axis_alignment(MainAxisAlignment::SpaceBetween)
                .main_axis_size(MainAxisSize::Max)
                .padding(Padding::vertical(15.0))

                Memo(item_builder: Box::new(move |ctx| {
                    let num_columns = num_columns.get();
                    let scroll = scroll.get();
                    let on_item_selected = on_item_selected.clone();
                    let on_item_focused = on_item_focused.clone();
                    let on_item_long_pressed = on_item_long_pressed.clone();
                    let on_item_viewed = on_item_viewed.clone();
                    let on_item_highlighted = on_item_highlighted.clone();

                    compose_option! {
                        GridModelToUI(
                            grid: grid_data,
                            num_columns,
                            scroll,
                            on_item_viewed: on_item_viewed.clone(),
                            on_item_highlighted: on_item_highlighted.clone(),
                            on_item_selected: on_item_selected.clone(),
                            on_item_focused: on_item_focused.clone(),
                            on_item_long_pressed: on_item_long_pressed.clone(),
                            default_focus_id: Signal::derive(ctx.scope(), move || Some("".to_string())),
                        )
                    }
                }))
            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .padding(Padding::all(50.0))
            .focus_hierarchical_container(NavigationStrategy::Vertical)
        }
    })
}
