{"resource": {"containerList": [{"id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7IioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvboyOpjE6MTNQQ05XUTNGVVVVRUEjI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy", "offerType": null, "entitlement": null, "items": [{"id": "historyuk", "title": "HISTORY Play", "synopsis": "Awesome description of the history channel that could span over two lines, maybe even three lines if we are not careful with choosing our words wisely", "headerText": null, "transformItemId": "historyuk", "widgetType": "channel", "heroImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/heroes/channels-collection-page-LRD-web-background-image_3840x1440._CB559163940_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-collection-page-LRD-web-logo_1000x400._CB559208271_.png", "actions": [{"serviceToken": "eyJ0eXBlIjoicXVlcnkiLCJuYXYiOnRydWUsInBpIjoiZGVmYXVsdCIsInNlYyI6ImNlbnRlciIsInN0eXBlIjoic2VhcmNoIiwicXJ5Ijoic2VhcmNoLWFsaWFzPWluc3RhbnQtdmlkZW8mZmllbGQtc3Vic2NyaXB0aW9uX2lkPWhpc3Rvcnl1ayIsInR4dCI6IkhJU1RPUlkgUGxheSIsIm9mZnNldCI6MCwibnBzaSI6MCwib3JlcSI6IjhlMjNhNGYyOGZmYTk0OWFmMWUwZjYyYTc5Yzg2NzdjOjE3MTUyODg1NDQwMDAiLCJvcmVxayI6IngveHE4ZkZ6TmMrWWpkVWJudWRwK2lqekRHWFdjRENvUTU0b1VUMFcycWM9Iiwib3JlcWt2IjoxfQ==", "refMarker": "3p_his_c_wWUEuq_HS5d545d_1_1", "target": "browse", "text": "HISTORY Play", "pageType": "browse", "pageId": "default", "analytics": {"refMarker": "3p_his_c_wWUEuq_HS5d545d_1_1"}}], "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}}, "regulatoryLabel": null, "cardType": "CHANNEL_CARD", "gti": null}], "analytics": {"refMarker": "3p_his_c_wWUEuq_1", "ClientSideMetrics": "472|ClsKLVVLQ2hhbm5lbENvbGxlY3Rpb25QYWdlSGVyb0xpdmVEZWZhdWx0RGVmYXVsdBIQMToxM1BDTldRM0ZVVVVFQRoQMjpEWTU2QjlCQkE0RDY1OCIGd1dVRXVxEkkKDHN1YnNjcmlwdGlvbhIJaGlzdG9yeXVrIgZjZW50ZXIqADIkODk1NDg3ODUtN2M0My00YTkxLWEwNjItMWVlNTUxODNlNmY3GgxzdWJzY3JpcHRpb24iA2FsbCoJaGlzdG9yeXVrMgxoZXJvQ2Fyb3VzZWw6E0hlcm9Db250ZW50UHJvdmlkZXJCGUNoYW5uZWxDb2xsZWN0aW9uUGFnZUhlcm9KFHNob3dVbmRlckV2ZXJ5RmlsdGVyUghlbnRpdGxlZFoAYgxTdGFuZGFyZEhlcm9oAXIAeiA4ZTIzYTRmMjhmZmE5NDlhZjFlMGY2MmE3OWM4Njc3Y4IBBHRydWU="}, "tags": [], "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg==", "type": "STANDARD_HERO"}, {"facet": {"text": null}, "title": "Recently added", "titleImageUrl": null, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiODk1NDg3ODUtN2M0My00YTkxLWEwNjItMWVlNTUxODNlNmY3IiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6IjhlMjNhNGYyOGZmYTk0OWFmMWUwZjYyYTc5Yzg2NzdjOjE3MTUyODg1NDQwMDAiLCJhcE1heCI6MjQxLCJzdHJpZCI6IjE6MTEzU1E0TVgyS0lUNVgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsInN0S2V5Ijoie1wic2JzaW5cIjowLFwiY3Vyc2l6ZVwiOjI0MSxcInByZXNpemVcIjowfSIsIm9yZXFrIjoieC94cThmRnpOYytZamRVYm51ZHAraWp6REdYV2NEQ29RNTRvVVQwVzJxYz0iLCJvcmVxa3YiOjF9", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7MioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvboyOqjE6MTEzU1E0TVgyS0lUNVgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "pageId": "historyuk", "pageType": "subscription", "pageContext": {"pageType": "subscription", "pageId": "historyuk"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7MioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvboyOqjE6MTEzU1E0TVgyS0lUNVgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "Subscription", "entitlement": "Entitled", "items": [{"title": "I Was There Season 1", "gti": "amzn1.dv.gti.067880d9-5ddb-4dd0-a104-87288e7c5c51", "transformItemId": "amzn1.dv.gti.067880d9-5ddb-4dd0-a104-87288e7c5c51", "synopsis": "I WAS THERE host <PERSON> believes we can only truly understand history by being there-- and now, we will. In this series, <PERSON> time-travels to pivotal moments in history that come to life in stunning detail. Inside some of history's most iconic events, he unearths the fascinating and lost details of why they really happened, while reliving the moments of our past that shaped us all.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bfa907716f499477e982e5d2d9c9aee163eed596911a64e31d7ee4b7bc650cde.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/a764769cdc3074aa944b1a7696707d8938eb3db511071cf75067b8af0ecb7938.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1652054400000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.067880d9-5ddb-4dd0-a104-87288e7c5c51", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_1"}, "refMarker": "3p_his_c_WgMUZO_brws_2_1", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.067880d9-5ddb-4dd0-a104-87288e7c5c51", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_1"}, "refMarker": "3p_his_c_WgMUZO_brws_2_1", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "I Was There", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "River Hunters: Wars Of The Roses S3", "gti": "amzn1.dv.gti.c8a3c03c-b85a-43ff-899d-fd2f18b20082", "transformItemId": "amzn1.dv.gti.c8a3c03c-b85a-43ff-899d-fd2f18b20082", "synopsis": "River Hunters sees <PERSON> and river-searching expert <PERSON> searching the unexplored rivers and waterways of Britain, unearthing treasures from the past which reveal how our ancestors used to live.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/be0a64102445c8cb33ff824f0e6201fbd5cf8db02b3f316ffa9ff44bf18c504a.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/cfe343a39e56433d8a67ae05a59072ab2230b39c351e48b9b463a1a46237a31f.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": null, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 3, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c8a3c03c-b85a-43ff-899d-fd2f18b20082", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_2"}, "refMarker": "3p_his_c_WgMUZO_brws_2_2", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.c8a3c03c-b85a-43ff-899d-fd2f18b20082", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_2"}, "refMarker": "3p_his_c_WgMUZO_brws_2_2", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "River Hunters: Wars Of The Roses", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Revealed: The Hunt For Bin Laden S1", "gti": "amzn1.dv.gti.64cd5773-0821-4d33-b260-8dda4d6c01d4", "transformItemId": "amzn1.dv.gti.64cd5773-0821-4d33-b260-8dda4d6c01d4", "synopsis": "Tracks the ten-year effort to find <PERSON><PERSON><PERSON>, the man responsible for the 9/11 attacks and leader of the terrorist group al-Qaeda.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "U", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/61384748deb93735bd093bd8cf2cdff4c9fa73c3a84e4497a4e82599327271f7.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/97846440cfe0f37f03972852faa2b0c77e273a121c1948149b42caa2f6875588.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1631232000000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.64cd5773-0821-4d33-b260-8dda4d6c01d4", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_3"}, "refMarker": "3p_his_c_WgMUZO_brws_2_3", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.64cd5773-0821-4d33-b260-8dda4d6c01d4", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_3"}, "refMarker": "3p_his_c_WgMUZO_brws_2_3", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Revealed: The Hunt For Bin Laden", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Ivan The Terrible S1", "gti": "amzn1.dv.gti.8223921c-ad55-445c-b308-423335822d1d", "transformItemId": "amzn1.dv.gti.8223921c-ad55-445c-b308-423335822d1d", "synopsis": "Across centuries of human history, there have been many great kings, but <PERSON> does not hold this particular epithet. Experience his reign as the first tsar of Russia.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/dcea8545e34a684dfdee6fb26c2d2a30a174790ed04b32136ed805c80a18861d.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8a0905a110dcda130c3e3a7ff1a3273fe6b1c8cf784e86310405e3ffe632fca5.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1667174400000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.8223921c-ad55-445c-b308-423335822d1d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_5"}, "refMarker": "3p_his_c_WgMUZO_brws_2_5", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.8223921c-ad55-445c-b308-423335822d1d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_5"}, "refMarker": "3p_his_c_WgMUZO_brws_2_5", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "<PERSON>rrible", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "On A Wing And A Prayer S1", "gti": "amzn1.dv.gti.c7b7384d-45bc-4d97-a62f-a06b04066251", "transformItemId": "amzn1.dv.gti.c7b7384d-45bc-4d97-a62f-a06b04066251", "synopsis": "Presented by <PERSON>, this film portrays the story of RAF Bomber Command during the Second World War. Beginning with its birth as a fledging arm of the Force in WWI, throughout its development between the wars.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/68c7cbc66e4ea318923a5951d160dd7835a35eb2f9c05a73e2bb373f0a06b3f0.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/37d7fef47df70165c595817811a68c48949234003c1d3791d5459714f169c6cd.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1616025600000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c7b7384d-45bc-4d97-a62f-a06b04066251", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_6"}, "refMarker": "3p_his_c_WgMUZO_brws_2_6", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.c7b7384d-45bc-4d97-a62f-a06b04066251", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_6"}, "refMarker": "3p_his_c_WgMUZO_brws_2_6", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "On A Wing And A Prayer", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Black Patriots: Heroes Of The Civil War S1", "gti": "amzn1.dv.gti.329943c9-d095-4bcc-a845-922fb8ff48de", "transformItemId": "amzn1.dv.gti.329943c9-d095-4bcc-a845-922fb8ff48de", "synopsis": "This documentary takes viewers through an evolution of African American involvement over the course of the Civil War through the stories of some of the most vital and significant figures of the day and the famed 54th Massachusetts Infantry Regiment.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "13+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8f3f49795a32c736a1cf613e62cfc2f22c6d4f961b5a04a2b2bb4e7badbb736d.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/583ab340628cc36785660472ca4e5b9460c7b7aa762e90ce9fe687c105d03cfd.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1648944000000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.329943c9-d095-4bcc-a845-922fb8ff48de", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_7"}, "refMarker": "3p_his_c_WgMUZO_brws_2_7", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.329943c9-d095-4bcc-a845-922fb8ff48de", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_7"}, "refMarker": "3p_his_c_WgMUZO_brws_2_7", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Black Patriots: Heroes Of The Civil War", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>: UFO Conspiracies S1", "gti": "amzn1.dv.gti.11ef1e8a-22d7-478c-bd92-ad2659235c1f", "transformItemId": "amzn1.dv.gti.11ef1e8a-22d7-478c-bd92-ad2659235c1f", "synopsis": "In October 2017, astronomers at the Pan-STARRS observatory saw something strange in our solar system: an object they named ‘<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, the very first observed visitor from interstellar space. This programme contains some flashing images.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f28fe7f5921957d9d51649d75152d530ded202e127df84d8bec91847dd0fdcdf.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/1aa0073a8d1c7e1dfe53c9b6eba525e2d7cfb9c76eed73149b2bd2e463a31d56.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1647302400000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.11ef1e8a-22d7-478c-bd92-ad2659235c1f", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_8"}, "refMarker": "3p_his_c_WgMUZO_brws_2_8", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.11ef1e8a-22d7-478c-bd92-ad2659235c1f", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_8"}, "refMarker": "3p_his_c_WgMUZO_brws_2_8", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "<PERSON>: UFO Conspiracies", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Queens Of Ancient Egypt S1", "gti": "amzn1.dv.gti.d80b5d26-e49e-41d2-888a-919a71613b93", "transformItemId": "amzn1.dv.gti.d80b5d26-e49e-41d2-888a-919a71613b93", "synopsis": "The famous Cleopatra is only one in a long line of Egyptian queens, spanning 3,000 years of history. Featuring new archaeological discoveries, <PERSON> of Ancient Egypt explores the lives of three of ancient Egypt’s fascinating and charismatic female monarchs.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2c3aa5eec173c6ecb91d178f7d8a9061bb001c547124138aafc78493ed1461dd.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/703889583b1d2102f79676b1da66677b46a83fc8c2bc479ef6a2f8322be66e39.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1688947200000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d80b5d26-e49e-41d2-888a-919a71613b93", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_9"}, "refMarker": "3p_his_c_WgMUZO_brws_2_9", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d80b5d26-e49e-41d2-888a-919a71613b93", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_9"}, "refMarker": "3p_his_c_WgMUZO_brws_2_9", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Queens Of Ancient Egypt", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "8 Days That Made Rome S1", "gti": "amzn1.dv.gti.a9473476-4ba6-4360-95a8-f0d8db456e66", "transformItemId": "amzn1.dv.gti.a9473476-4ba6-4360-95a8-f0d8db456e66", "synopsis": "Award-winning historian and broadcaster <PERSON><PERSON> presents a new eight-part landmark history of ancient Rome. Across the series <PERSON> explores eight key days that she believes define the Roman Empire and help us to understand its remarkable success.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2fd921743f169002764e767ac2f3dc40eb47c2730e92f1f6587b9deb02907cb4.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/172a40c2b62a87da8d79a24e8fd2859ed6244627fb1913eada14130864f4f42b.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1658188800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a9473476-4ba6-4360-95a8-f0d8db456e66", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_10"}, "refMarker": "3p_his_c_WgMUZO_brws_2_10", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a9473476-4ba6-4360-95a8-f0d8db456e66", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_WgMUZO_brws_2_10"}, "refMarker": "3p_his_c_WgMUZO_brws_2_10", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "8 Days That Made Rome", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "3p_his_c_WgMUZO_2", "ClientSideMetrics": "456|Cl0KL1VLM1BSZWNlbnRseUFkZGVkQ2hhbm5lbHNFdG9JTGl2ZURlZmF1bHREZWZhdWx0EhAxOjExM1NRNE1YMktJVDVYGhAyOkRZRDkzNkRDMTRCQTY3IgZXZ01VWk8SSQoMc3Vic2NyaXB0aW9uEgloaXN0b3J5dWsiBmNlbnRlcioAMiQ4OTU0ODc4NS03YzQzLTRhOTEtYTA2Mi0xZWU1NTE4M2U2ZjcaDHN1YnNjcmlwdGlvbiIDYWxsKgloaXN0b3J5dWsyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQg5Ccm93c2VTdHJhdGVneUoWY29sbGVjdGlvblBhZ2VDYXJvdXNlbFIIZW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgCcgB6IDhlMjNhNGYyOGZmYTk0OWFmMWUwZjYyYTc5Yzg2NzdjggEEdHJ1ZQ=="}, "tags": [], "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg==", "seeMore": null, "type": "STANDARD_CAROUSEL"}, {"facet": {"text": null}, "title": "Most popular TV", "titleImageUrl": null, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiODk1NDg3ODUtN2M0My00YTkxLWEwNjItMWVlNTUxODNlNmY3IiwiZmlsdGVyIjp7fSwib2Zmc2V0IjoxMCwibnBzaSI6MTAsIm9yZXEiOiI4ZTIzYTRmMjhmZmE5NDlhZjFlMGY2MmE3OWM4Njc3YzoxNzE1Mjg4NTQ0MDAwIiwiYXBNYXgiOjI0MSwic3RyaWQiOiIxOjEyQTRWUldCR1VVUVpTIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE0iLCJzdEtleSI6IntcInNic2luXCI6MCxcImN1cnNpemVcIjoyNDEsXCJwcmVzaXplXCI6MH0iLCJvcmVxayI6IngveHE4ZkZ6TmMrWWpkVWJudWRwK2lqekRHWFdjRENvUTU0b1VUMFcycWM9Iiwib3JlcWt2IjoxfQ==", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7MioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvboyOqjE6MTJBNFZSV0JHVVVRWlMjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "pageId": "historyuk", "pageType": "subscription", "pageContext": {"pageType": "subscription", "pageId": "historyuk"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7MioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvboyOqjE6MTJBNFZSV0JHVVVRWlMjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "Subscription", "entitlement": "Entitled", "items": [{"title": "Rise And Fall: The World Trade Center S1", "gti": "amzn1.dv.gti.9334eb64-8e6a-4827-b828-724c44d72f69", "transformItemId": "amzn1.dv.gti.9334eb64-8e6a-4827-b828-724c44d72f69", "synopsis": "As told through two dozen interviews, Rise and Fall: The World Trade Center tells the story of those who helped create and construct the Twin Towers and those who worked in them.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "U", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/cb5d46e08b9ae2a8917b92b96242579f17ee32895553a7fe74e7e8cdae28f2d3.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/34e5bb574deeef6c71fcd405890bba22c75abd8897e686883db556bb73196263.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1631404800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.9334eb64-8e6a-4827-b828-724c44d72f69", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_1"}, "refMarker": "3p_his_c_HhkZyY_brws_3_1", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.9334eb64-8e6a-4827-b828-724c44d72f69", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_1"}, "refMarker": "3p_his_c_HhkZyY_brws_3_1", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Rise And Fall: The World Trade Center", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Colosseum S1", "gti": "amzn1.dv.gti.284aa0ad-1eec-425c-8ca0-0bb367500fc3", "transformItemId": "amzn1.dv.gti.284aa0ad-1eec-425c-8ca0-0bb367500fc3", "synopsis": "By the 4th century AD, the Roman Empire is devastated by invasions and religious divides. The Colosseum, which once stood as a symbol of the Empire's glory, lies empty. To restore Rome, a devout worshipper of Rome's old gods turns to the arena.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/725afb53ca1dc6020ce65b0ba3ee02adc6c9fe0d79aa5f8fd69271b12bf21846.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/3cb5893edff834b2443e1602b394463a62ecb78dd42c5c79dfcdbab146c9c1b3.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1677715200000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.284aa0ad-1eec-425c-8ca0-0bb367500fc3", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_2"}, "refMarker": "3p_his_c_HhkZyY_brws_3_2", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.284aa0ad-1eec-425c-8ca0-0bb367500fc3", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_2"}, "refMarker": "3p_his_c_HhkZyY_brws_3_2", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Colosseum", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Hitler's War S1", "gti": "amzn1.dv.gti.3a328995-b8cc-4481-baff-d3f12912848b", "transformItemId": "amzn1.dv.gti.3a328995-b8cc-4481-baff-d3f12912848b", "synopsis": "Eight one hour episodes, each focusing on a different battle, trace the trajectory of <PERSON>’s ill-advised military ventures from the early Blitzreig triumphs to the disaster at Stalingrad and on to the final battle for Berlin. Each fascinating episode reveals the changing tactics, weapons, planes and aircraft which shaped the face of battle and focuses on the changing technology.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "U", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/5917891620c9a35d7effd829689574ead744d88da9eb348051bf6bb4079e8f6a.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c66e2368d0f318b3b05421255eec31d527b71d255e5c58e969277442c9f9ee8e.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1515110400000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3a328995-b8cc-4481-baff-d3f12912848b", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_3"}, "refMarker": "3p_his_c_HhkZyY_brws_3_3", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3a328995-b8cc-4481-baff-d3f12912848b", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_3"}, "refMarker": "3p_his_c_HhkZyY_brws_3_3", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Hitler's War", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Attila's Forbidden Tomb S1", "gti": "amzn1.dv.gti.1fd85dd8-3109-431a-9f11-99b77921cba3", "transformItemId": "amzn1.dv.gti.1fd85dd8-3109-431a-9f11-99b77921cba3", "synopsis": "In the fifth century the Roman Empire suddenly lost control. Now scientists and historians are hunting for clues about the role played by a brutal warrior king – <PERSON><PERSON><PERSON>.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "U", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6a768a45edcf0b2c9082ef34889566b965d955510c628c2a1a718b5877877608.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/e54671f561fbc84b74acb68434f71879bc97a7c459c99910af02056e6deefb0d.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1642982400000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1fd85dd8-3109-431a-9f11-99b77921cba3", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_5"}, "refMarker": "3p_his_c_HhkZyY_brws_3_5", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1fd85dd8-3109-431a-9f11-99b77921cba3", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_5"}, "refMarker": "3p_his_c_HhkZyY_brws_3_5", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Attila's Forbidden Tomb", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "History's Greatest Mysteries S4", "gti": "amzn1.dv.gti.b2cf2f7b-1d43-4270-82f3-7c1947f6a9f0", "transformItemId": "amzn1.dv.gti.b2cf2f7b-1d43-4270-82f3-7c1947f6a9f0", "synopsis": "Emmy Award® winner <PERSON> investigates some of the greatest mysteries of all time: S<PERSON><PERSON>'s Ice Ship, the Titanic, <PERSON> and <PERSON><PERSON><PERSON>. These programs provides new clarity to these larger-than-life chapters in history.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d05aba0ed7dffab7434715089eee1accdd3b97c1ee7b739cc19297c7caa25936.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9c45efee5ffd1f8255419e7880872e564981d2e786af251ecbd31281ec2d77b4.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/b57cb9ec1bbce5e67770167212e8b050709042751444694efe7eb88df1e3761f.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1697500800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 4, "watchProgress": null, "numberOfSeasons": 2, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b2cf2f7b-1d43-4270-82f3-7c1947f6a9f0", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_6"}, "refMarker": "3p_his_c_HhkZyY_brws_3_6", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b2cf2f7b-1d43-4270-82f3-7c1947f6a9f0", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_6"}, "refMarker": "3p_his_c_HhkZyY_brws_3_6", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "History's Greatest Mysteries", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Battle 360 S1", "gti": "amzn1.dv.gti.74ab38df-d23a-f4e9-38d3-13ee2d8b43ea", "transformItemId": "amzn1.dv.gti.74ab38df-d23a-f4e9-38d3-13ee2d8b43ea", "synopsis": "Amazing CGI animation helps tell the story of the World War II aircraft carrier USS Enterprise", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "NR", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary", "Historical", "Military and War"], "maturityRatingString": "18+", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/62fb15afff79bf10d4e321e2cd954f627e780301bfeb8a14a0cb8aae7eac727b.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/14eeff159caa0905e1ddb68b15bbb024051569a8663324e9d5652ff493050715.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1209686400000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.74ab38df-d23a-f4e9-38d3-13ee2d8b43ea", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_7"}, "refMarker": "3p_his_c_HhkZyY_brws_3_7", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.74ab38df-d23a-f4e9-38d3-13ee2d8b43ea", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_7"}, "refMarker": "3p_his_c_HhkZyY_brws_3_7", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Battle 360", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Liberté S1", "gti": "amzn1.dv.gti.a33ce4f3-283b-4323-8c94-1b<PERSON>aca9d63d", "transformItemId": "amzn1.dv.gti.a33ce4f3-283b-4323-8c94-1b<PERSON>aca9d63d", "synopsis": "Britain's unlikeliest of spies is tested to the limit as she faces her brutal Nazi captors for the final time. Can she hold on to London's secrets as the Allied forces close in? Inspired by the true story of Muslim war heroine <PERSON><PERSON>.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c2463f3d2299c227ea5efd7506f899607f73b9a5f7d850d8f65e4f2a00a6d77f.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8e4f79fbc2f165eb4b1af710ab35a51f38aa92f23ffbc3dc2b4ae5e17e0c44c9.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": null, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a33ce4f3-283b-4323-8c94-1b<PERSON>aca9d63d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_8"}, "refMarker": "3p_his_c_HhkZyY_brws_3_8", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a33ce4f3-283b-4323-8c94-1b<PERSON>aca9d63d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_8"}, "refMarker": "3p_his_c_HhkZyY_brws_3_8", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "<PERSON><PERSON><PERSON>", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Mysteries From Above S2", "gti": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "transformItemId": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "synopsis": "Mysteries From Above sets out to investigate significant historic and contemporary sites from above with drone, satellite, and aerial photography. Each story begins with a view from above, which allows a unique perspective to understand the site below and dramatically changes the way we see these mysteries, ourselves and the world around us.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/48bae36ee5dca55d11c320718fbf44028dc39fe3ddf6360a859c727e886b7395.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/775bf56bf7b461c5282ef0fb7ff137c41ca4f8853cc8bc84a5b06053f16ef7bc.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/30b27120973ecbc9fc024da9860cf06b96dc9107fb43043b03be878368d4cc73.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1716940800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 2, "watchProgress": null, "numberOfSeasons": 2, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_9"}, "refMarker": "3p_his_c_HhkZyY_brws_3_9", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_9"}, "refMarker": "3p_his_c_HhkZyY_brws_3_9", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Mysteries From Above", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Nazi Architects Of Darkness S1", "gti": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "transformItemId": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "synopsis": "Six one hour episodes each focusing on a key individual that traces the trajectory and design of <PERSON>'s thousand year Reich.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "U", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c71e368d498ca385de6ecd10b6fa4750a375cf9019b16a0781b142851632b599.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/82067cb9fe5e7b60aba941c51aa779cd4a40179bf87a48d5c7eeb777c21a812c.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1517011200000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_10"}, "refMarker": "3p_his_c_HhkZyY_brws_3_10", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_HhkZyY_brws_3_10"}, "refMarker": "3p_his_c_HhkZyY_brws_3_10", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Nazi Architects Of Darkness", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "3p_his_c_HhkZyY_3", "ClientSideMetrics": "432|Ck4KIFVLM1BQb3B1bGFyVFYxTGl2ZURlZmF1bHREZWZhdWx0EhAxOjEyQTRWUldCR1VVUVpTGhAyOkRZNTY5QTU3MzRGODc0IgZIaGtaeVkSSQoMc3Vic2NyaXB0aW9uEgloaXN0b3J5dWsiBmNlbnRlcioAMiQ4OTU0ODc4NS03YzQzLTRhOTEtYTA2Mi0xZWU1NTE4M2U2ZjcaDHN1YnNjcmlwdGlvbiICdHYqCWhpc3Rvcnl1azIPZmFjZXRlZENhcm91c2VsOgZCcm93c2VCDkJyb3dzZVN0cmF0ZWd5ShZjb2xsZWN0aW9uUGFnZUNhcm91c2VsUghlbnRpdGxlZFoAYhBTdGFuZGFyZENhcm91c2VsaANyAHogOGUyM2E0ZjI4ZmZhOTQ5YWYxZTBmNjJhNzljODY3N2OCAQR0cnVl"}, "tags": [], "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg==", "seeMore": null, "type": "STANDARD_CAROUSEL"}, {"facet": {"text": null}, "title": "Best of British", "titleImageUrl": null, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiODk1NDg3ODUtN2M0My00YTkxLWEwNjItMWVlNTUxODNlNmY3IiwiZmlsdGVyIjp7fSwib2Zmc2V0Ijo0LCJucHNpIjoxMCwib3JlcSI6IjhlMjNhNGYyOGZmYTk0OWFmMWUwZjYyYTc5Yzg2NzdjOjE3MTUyODg1NDQwMDAiLCJhcE1heCI6MTEwLCJzdHJpZCI6IjE6MTJCMVNMQldDOFc5RE4jI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsInN0S2V5Ijoie1wic2JzaW5cIjowLFwiY3Vyc2l6ZVwiOjExMCxcInByZXNpemVcIjowfSIsIm9yZXFrIjoieC94cThmRnpOYytZamRVYm51ZHAraWp6REdYV2NEQ29RNTRvVVQwVzJxYz0iLCJvcmVxa3YiOjF9", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7MioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvboyOqjE6MTJCMVNMQldDOFc5RE4jI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "pageId": "historyuk", "pageType": "subscription", "pageContext": {"pageType": "subscription", "pageId": "historyuk"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7MioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvboyOqjE6MTJCMVNMQldDOFc5RE4jI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "Subscription", "entitlement": "Entitled", "items": [{"title": "Air Crash: Disasters Uncovered S1", "gti": "amzn1.dv.gti.e592b07f-c641-4d73-9f06-d70902f06e40", "transformItemId": "amzn1.dv.gti.e592b07f-c641-4d73-9f06-d70902f06e40", "synopsis": "This series examines some of the world's worst air disasters, using official reports, transcripts and interviews with people involved to discover what went wrong in each case.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b074b615912dda41061be8734aea5e338738ce6088afe256ea2a92d3e2884b34.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/800702becb4b547b0346217196bd4986b8004dec488aa8052379af5815b17d35.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/de686f896ff4cffcffdb64a09a5c00cc74d1debe49c3a1596def6d9e91f7d136.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1668124800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e592b07f-c641-4d73-9f06-d70902f06e40", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_1"}, "refMarker": "3p_his_c_ET4XcO_brws_4_1", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e592b07f-c641-4d73-9f06-d70902f06e40", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_1"}, "refMarker": "3p_his_c_ET4XcO_brws_4_1", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Air Crash: Disasters Uncovered", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "History's Greatest Myths S1", "gti": "amzn1.dv.gti.462b4bb5-7d71-4424-90dc-23c92fd84f0d", "transformItemId": "amzn1.dv.gti.462b4bb5-7d71-4424-90dc-23c92fd84f0d", "synopsis": "What are the myths and the unsolved mysteries of history? Fake news is all around us today but how much of what we learned in school was just well written and well-worn fake history? History's greatest Myths is an enlightening series that digs in deep to discover some good ol' home truths. The series utilises archive, graphics, and compelling interviewees to question our long-held beliefs about...", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/185aee061532b4e9e6629dd8407bfde53ba1d73adaf388a0fc1845fd1d95b53c.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/0ec30315c56e8ae00d680612ae47011de840c6c7c628dd2115d6b795b60e57da.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1686960000000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.462b4bb5-7d71-4424-90dc-23c92fd84f0d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_2"}, "refMarker": "3p_his_c_ET4XcO_brws_4_2", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.462b4bb5-7d71-4424-90dc-23c92fd84f0d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_2"}, "refMarker": "3p_his_c_ET4XcO_brws_4_2", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "History's Greatest Myths", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": true}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Mysteries From Above S2", "gti": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "transformItemId": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "synopsis": "Mysteries From Above sets out to investigate significant historic and contemporary sites from above with drone, satellite, and aerial photography. Each story begins with a view from above, which allows a unique perspective to understand the site below and dramatically changes the way we see these mysteries, ourselves and the world around us.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/48bae36ee5dca55d11c320718fbf44028dc39fe3ddf6360a859c727e886b7395.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/775bf56bf7b461c5282ef0fb7ff137c41ca4f8853cc8bc84a5b06053f16ef7bc.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/30b27120973ecbc9fc024da9860cf06b96dc9107fb43043b03be878368d4cc73.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1716940800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 2, "watchProgress": null, "numberOfSeasons": 2, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_3"}, "refMarker": "3p_his_c_ET4XcO_brws_4_3", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f05bd03b-5d3c-48a9-a60c-bb8234e85fbc", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_3"}, "refMarker": "3p_his_c_ET4XcO_brws_4_3", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Mysteries From Above", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Ancient Aliens S16", "gti": "amzn1.dv.gti.8dbb38c1-1e1c-4a23-90e5-94595953fc9f", "transformItemId": "amzn1.dv.gti.8dbb38c1-1e1c-4a23-90e5-94595953fc9f", "synopsis": "ANCIENT ALIENS examines 75 million years of the most credible alien evidence here on Earth, from the age of the dinosaurs, to ancient Egypt, to the skies over the western desert in the present day US. Ancient cave drawings of strange creatures, an asphalt-like substance in an Egyptian pyramid made from the remains of unidentified creatures, continued mass sightings in the USA -- these are...", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/0933b9ef8f55b345829b65cd32b50ed485525154f887cb4daf96c153954f4060.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6590b453da79c31d5fd7cad18c4bac17c6f9d460164faa3d5dcfd9554327679a.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/4b22c14b4881c9526b5a2d0d975beeae7f46493aa027577fe2238f768f6cf4c6.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": null, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 16, "watchProgress": null, "numberOfSeasons": 16, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.8dbb38c1-1e1c-4a23-90e5-94595953fc9f", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_4"}, "refMarker": "3p_his_c_ET4XcO_brws_4_4", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.8dbb38c1-1e1c-4a23-90e5-94595953fc9f", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_4"}, "refMarker": "3p_his_c_ET4XcO_brws_4_4", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Ancient Aliens", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Egypt's Unexplained Files S2", "gti": "amzn1.dv.gti.abee7cfa-e7fc-48d3-8fdc-6da185a13f3c", "transformItemId": "amzn1.dv.gti.abee7cfa-e7fc-48d3-8fdc-6da185a13f3c", "synopsis": "Ancient Egypt is one of the world’s greatest civilisations, spanning 3000 years of history. But, even today, it is a land of mystery. For the past two centuries, since we first brushed the sands from the Great Sphinx, archaeologists and historians have been slowly unravelling its secrets. Yet experts believe some of the most important discoveries of this ancient world lie undiscovered beneath...", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/2f19ec02c464ea2ee5eb7b74316b001eabd8a83b8cc64e9214e5580616809017.jpg", "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/36c785b12329fadb780017ed4b9f93c642c3cad1cb1dda5f94fb560e9485655e.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/262d078f62a8efe3b095d73020ae40ed307dc1a2e50bb3df09765636f6daefe7.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1696377600000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 2, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.abee7cfa-e7fc-48d3-8fdc-6da185a13f3c", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_5"}, "refMarker": "3p_his_c_ET4XcO_brws_4_5", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.abee7cfa-e7fc-48d3-8fdc-6da185a13f3c", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_5"}, "refMarker": "3p_his_c_ET4XcO_brws_4_5", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Egypt's Unexplained Files", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "heroImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Nazi Architects Of Darkness S1", "gti": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "transformItemId": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "synopsis": "Six one hour episodes each focusing on a key individual that traces the trajectory and design of <PERSON>'s thousand year Reich.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "U", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c71e368d498ca385de6ecd10b6fa4750a375cf9019b16a0781b142851632b599.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/82067cb9fe5e7b60aba941c51aa779cd4a40179bf87a48d5c7eeb777c21a812c.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1517011200000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_6"}, "refMarker": "3p_his_c_ET4XcO_brws_4_6", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5c6d4596-286b-4419-bbcd-dd65a83090e7", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_6"}, "refMarker": "3p_his_c_ET4XcO_brws_4_6", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Nazi Architects Of Darkness", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Secret History Of Comics S1", "gti": "amzn1.dv.gti.a9426e76-3c5d-427f-b463-7807d771381f", "transformItemId": "amzn1.dv.gti.a9426e76-3c5d-427f-b463-7807d771381f", "synopsis": "A deep-dive into the stories, people and events that have transformed the world of comic books.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/0bcf58e4302b5356dcfd73a41a3b6b956a6dbdc3bb300f9cc5754f0278395a7b.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/2030d594be6f544e9637ab3d1d9b11b3ccd2cbd8d6bc2966c4b9696a975671a7.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1629676800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a9426e76-3c5d-427f-b463-7807d771381f", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_7"}, "refMarker": "3p_his_c_ET4XcO_brws_4_7", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a9426e76-3c5d-427f-b463-7807d771381f", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_7"}, "refMarker": "3p_his_c_ET4XcO_brws_4_7", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Secret History Of Comics", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Swamp People S12", "gti": "amzn1.dv.gti.8a1e43e4-f56c-430b-a4fe-d3fae045a07b", "transformItemId": "amzn1.dv.gti.8a1e43e4-f56c-430b-a4fe-d3fae045a07b", "synopsis": "As a new alligator season begins, Southern Louisiana hunters face a daunting surprise: gator numbers have exploded and it’s getting worse by the day.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "U", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "U", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/u.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8c5db13212f0c372d3fa9442fca56291569449487b872ede507b60b4fd6e219f.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/366604980a46b07df33cfd56311a56c3e333c9eb061517e1733ea34a7290f7e0.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1640736000000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 12, "watchProgress": null, "numberOfSeasons": 13, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.8a1e43e4-f56c-430b-a4fe-d3fae045a07b", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_8"}, "refMarker": "3p_his_c_ET4XcO_brws_4_8", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.8a1e43e4-f56c-430b-a4fe-d3fae045a07b", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_8"}, "refMarker": "3p_his_c_ET4XcO_brws_4_8", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Swamp People", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Digging For Britain S1", "gti": "amzn1.dv.gti.15b41aff-b14c-42ec-96a1-1e5fc057d8c3", "transformItemId": "amzn1.dv.gti.15b41aff-b14c-42ec-96a1-1e5fc057d8c3", "synopsis": "Dr <PERSON> investigates British archaeology, joining up the results of digs and investigations the length and breadth of the country.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "PG", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/pg.png", "dimension": {"width": 88, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f37bee39e0110065ca625206cad157e16313c885f1b844f7dd9dc2429a2ab6ff.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/9fad12c69b7e6009e46a7183da4a2c751ae5c9e6acb5348aeae0387667223d39.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1519862400000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 7, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.15b41aff-b14c-42ec-96a1-1e5fc057d8c3", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_9"}, "refMarker": "3p_his_c_ET4XcO_brws_4_9", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.15b41aff-b14c-42ec-96a1-1e5fc057d8c3", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_9"}, "refMarker": "3p_his_c_ET4XcO_brws_4_9", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Digging For Britain", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}, {"title": "Ideas That Changed The World S1", "gti": "amzn1.dv.gti.62840cae-eb69-49d8-a7d7-ca83408fac9d", "transformItemId": "amzn1.dv.gti.62840cae-eb69-49d8-a7d7-ca83408fac9d", "synopsis": "This is the amazing story of how humans changed the world forever, told through six iconic objects that we take for granted. These are the secrets of how we got to our modern world, told in an accessible, exciting format with broad appeal.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "15", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Documentary"], "maturityRatingString": "15", "maturityRatingImage": {"url": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/15.png", "dimension": {"width": 80, "height": 80}}, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8b262b5fee93f348f7b74127d458b882bf2b52919c1decf331bdc74285aebc9a.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/46f92348622c448f36ec89b06c1fc1c4ef6261c05bfe30bb3e05231313a8ff3c.jpg", "titleLogoImage": null, "providerLogoImage": "https://m.media-amazon.com/images/G/02/digital/video/merch/subs/benefit-id/g-l/historyuk/logos/channels-logo-white._CB559208271_.png", "poster2x3Image": null, "publicReleaseDate": 1659484800000, "runtimeSeconds": null, "runtime": null, "overallRating": null, "totalReviewCount": null, "seasonNumber": 1, "watchProgress": null, "numberOfSeasons": 1, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.62840cae-eb69-49d8-a7d7-ca83408fac9d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_10"}, "refMarker": "3p_his_c_ET4XcO_brws_4_10", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your HISTORY Play subscription", "icon": "ENTITLED_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.62840cae-eb69-49d8-a7d7-ca83408fac9d", "pageType": "detail", "analytics": {"refMarker": "3p_his_c_ET4XcO_brws_4_10"}, "refMarker": "3p_his_c_ET4XcO_brws_4_10", "text": null, "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg=="}], "playableGti": null, "showName": "Ideas That Changed The World", "episodeNumber": null, "watchNextType": null, "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "boxartImage": {"height": null, "width": null, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": false}, "providerLogoImage": {"height": 530, "width": 1996, "scalarHorizontal": null, "scalarStacked": null, "safeToOverlay": null}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "3p_his_c_ET4XcO_4", "ClientSideMetrics": "440|ClMKJVVLM1BiZXN0b2Zicml0aXNodHZMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTJCMVNMQldDOFc5RE4aEDI6RFlGMDNCN0YzMTFCNjciBkVUNFhjTxJJCgxzdWJzY3JpcHRpb24SCWhpc3Rvcnl1ayIGY2VudGVyKgAyJDg5NTQ4Nzg1LTdjNDMtNGE5MS1hMDYyLTFlZTU1MTgzZTZmNxoMc3Vic2NyaXB0aW9uIgJ0dioJaGlzdG9yeXVrMg9mYWNldGVkQ2Fyb3VzZWw6BkJyb3dzZUIOQnJvd3NlU3RyYXRlZ3lKFmNvbGxlY3Rpb25QYWdlQ2Fyb3VzZWxSCGVudGl0bGVkWgBiEFN0YW5kYXJkQ2Fyb3VzZWxoBHIAeiA4ZTIzYTRmMjhmZmE5NDlhZjFlMGY2MmE3OWM4Njc3Y4IBBHRydWU="}, "tags": [], "journeyIngressContext": "36|CgloaXN0b3J5dWsSDHN1YnNjcmlwdGlvbg==", "seeMore": null, "type": "STANDARD_CAROUSEL"}], "paginationLink": {"serviceToken": "eyJ0eXBlIjoidnBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiODk1NDg3ODUtN2M0My00YTkxLWEwNjItMWVlNTUxODNlNmY3IiwicnQiOiIiLCJmaWx0ZXIiOnt9LCJvZmZzZXQiOjAsIm5wc2kiOjAsIm9yZXEiOiI4ZTIzYTRmMjhmZmE5NDlhZjFlMGY2MmE3OWM4Njc3YzoxNzE1Mjg4NTQ0MDAwIiwiYXBNYXgiOjcsIm9yZXFrIjoieC94cThmRnpOYytZamRVYm51ZHAraWp6REdYV2NEQ29RNTRvVVQwVzJxYz0iLCJvcmVxa3YiOjEsImRkcHQiOiJ2MTpBUWNBQUFCYUFBQkNJQUFBZ0FBQUNBQUFBUVFBQWdFQUFBQUlRQUJBQUFBQ0JBQ0FBQUFBZ1FBQUFBQUFBQUFBZ0JBQUFBQWdBQUNBQUF3UUFBQUFBQUFBQUFCQkFJZ0FBQUFBQUFDQUFBZ0FBTEFBQ0FBQUFBUUFBQUJBQVJBQUFEQVFBQUFBQUFBQUFJQUFBQWdRQUFBQUVBQUFnQUlBSUFBQUFBQWdnRVNBQUFBQUFBQUVRQUFBQUFFQWhBUVFDQlJBUUlCQUFJQUFBZ0FFd2dBQUFBQUFBZ0FnQ0FBQUFBZ0VBcUFBb0FvQURCQUFBQUFBQUFDQUtBQUFJQUFJRUFnQUFFQUFBQUFBRUFnQUFBQUlCQUFBQUFBQ2dBQUNnQUFBQWdBQUFBa0FBUUFBQUFBQUFDQUFBQUFBQUJBa0FBQUFBUUFBQlFBQUFRQUFBQ0FnQWtBRUFBQUFBQWdBUWdVSUFBQWdCQUNDQ0FBUUNJQUFnQUlBQ0FBQUFCQUFBUUFCSUFBa0JBQUFBQUNBSUFVU2dBQUFBQUVBZ0FnQUFBQUFRQUJBREFBQUFBQUVBSUFRQkFBQUFJQVF3QUFBQUFBQUFBUUFBQWdpQUFBQVFBRUFJUUZRd0FBRUFBa0FBQUVCQWhSSUFTQUlBQUlCQ0FBQUFCQkFFSUFBQUlBQUJJQUFBZ1lDQUFBa1FBQUFRZ0FBQVFBUVFBQUNRQkFBQ0FBQUFBSVFBQUFCQUFBQUVJd0NnSUFBQUFBQUJBQUFBQUFBRUlRQmdBQUNBQUFDQUFGQUFBQlVBQUFBQUFBQUFBQUFBb0VBQUFBSUJnQ0FCQWdDUkJCQUFBQUFFRUFBQWFCQUFFQUFBZ2dBQUFBQkFDQUFRQUFERWdRQUFBQmdBQUFBUUJnQUFFQ0FBQUFBQUJBQUFBQUFDQVFBSXdBQXdBQUFBQVFJQVFJRUFBQUNBQUFBQUFBQU1BQUFBQ0lBQUFBRUFFQUFBQWdJRWdnQUlBTUFBQ0VBQUZBQUFBQUFBQUFRQUFnQUFFUUFBRUFBSUVBQUNBQUFBRUFJQU1BQkFBUUFJRUFnQUFBQUJBQUNBQUFBQUFBQmdBQUFBQmlCQUFBQUlBQUJBQUFBQUVBQUFDQUFBQUFCQUFBQUFBQUFFQUFBQUpBQUFBQUFBQUNBUkFCQkFBZ0FBQUFDQUFBQUNBQUFBUUFBQWdBQWdBQUFBQUFBQkFBQUFBQUJKUUlDQUJBQUFBQUFBUUNBQUFBa0FBRUFFSWdnQkFBS0FBQXdnaEFBQWdBQSJ9", "startIndex": 4, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6hioloaXN0b3J5dWuLjHN1YnNjcmlwdGlvbowPjQ-OglYy", "pageId": "historyuk", "pageType": "subscription", "pageContext": {"pageType": "subscription", "pageId": "historyuk"}}, "subNav": [], "pageTitle": "HISTORY Play", "pageMetadata": {"title": "HISTORY Play", "logoImage": {"url": null}, "entitlementIntent": "Entitled", "navNode": null, "locationDependentPage": false, "showVoiceFilters": false, "persistentTitleOrLogo": true}}, "metadata": {"requestId": "8e23a4f28ffa949af1e0f62a79c8677c", "requestedTransformId": "lr/collections/collectionsPageInitial", "domain": "prod", "realm": "us-east-1", "timestamp": "2024-05-09T21:02:24.973025Z"}}