use std::rc::Rc;

use amzn_fable_tokens::*;
use ignx_compositron::app::launch_composable;
use ignx_compositron::composable::*;
use ignx_compositron::compose;
use ignx_compositron::context::AppContext;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use mockall::predicate;
use mockall::predicate::function;

use auth::{AuthContext, MockAuth};
use collections_ui::page::collections_page::*;
use common_transform_types::profile::{Profile, ProfileAvatar};
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use location::Location;
use media_background::media_background::*;
use media_background::types::MediaBackgroundType;
use navigation_menu::mock_data::mock_top_nav_data;
use navigation_menu::model::nav_model::TopNavData;
use router::{MockRouting, RoutingContext};
use title_details::core::create_title_details_signals;
use title_details::core::*;

fn setup_mocks(ctx: AppContext) -> Signal<TopNavData> {
    let mut mock_routing_context = MockRouting::new();
    mock_routing_context
        .expect_navigate()
        .with(
            function(|loc: &Location| {
                println!("navigate called with {:?}", loc);
                true
            }),
            predicate::always(),
        )
        .return_const(());
    mock_routing_context
        .expect_location()
        .return_const(create_rw_signal(ctx.scope, Location::empty()));
    provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));

    let auth = MockAuth::new_without_params(ctx.scope);
    provide_context::<AuthContext>(ctx.scope, Rc::new(auth));
    mock_top_nav_data(ctx.scope)
}

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let top_nav_data = setup_mocks(ctx.clone());

        let media_background_data = create_rw_signal(ctx.scope(), MediaBackgroundType::None);
        let title_details_data = create_title_details_signals(ctx.scope());
        let autoplay_enabled = Signal::derive(ctx.scope(), move || false);
        let modal_data = create_rw_signal(ctx.scope(), vec![]).write_only();
        let profile = Profile {
            id: "".to_string(),
            avatar: ProfileAvatar {
                avatarId: "".to_string(),
                avatarUrl: "".to_string(),
                avatarDescription: None,
            },
            name: "".to_string(),
            isActive: false,
            isAdult: false,
            profileIsImplicit: false,
            translationDetails: None,
            permissions: None,
        };
        let active_profile: Signal<Option<Profile>> =
            create_signal(ctx.scope(), Some(profile)).0.into();
        compose! {
            Stack() {
                MediaBackground(incoming_data: media_background_data.read_only(), rtl_enabled: false, autoplay_enabled)
                TitleDetails(incoming_data: title_details_data.data, x_offset: title_details_data.x_offset, y_offset: title_details_data.y_offset, opacity: title_details_data.opacity)
                CollectionsPage(update_media_background_rw: media_background_data, update_title_details: title_details_data.data.write_only(), top_nav_data, modal_data, active_profile)
            }
            .width(SCREEN_WIDTH)
            .height(SCREEN_HEIGHT)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
        }
    });
}
