use cross_app_events::impression_tracker::ImpressionTracker;
use cross_app_events::ImpressionData;
use fableous::cards::sizing::CardDimensions;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::prelude::Scope;
use mockall::automock;
use std::cell::RefCell;

#[automock]
pub trait CanSendImpressions {
    fn send_highlight_impression_event(s: Scope, d: ImpressionData);
    fn send_select_impression_event(s: Scope, d: ImpressionData);
    fn send_view_impression_event(s: Scope, v: ViewImpressionData, i: ImpressionData);
}

#[cfg(not(test))]
pub struct CrossAppEventImpressions;
#[cfg(not(test))]
impl CanSendImpressions for CrossAppEventImpressions {
    fn send_highlight_impression_event(s: Scope, d: ImpressionData) {
        cross_app_events::send_highlight_impression_event(s, d)
    }

    fn send_select_impression_event(s: Scope, d: ImpressionData) {
        cross_app_events::send_select_impression_event(s, d)
    }

    fn send_view_impression_event(s: <PERSON>ope, v: ViewImpressionData, i: ImpressionData) {
        cross_app_events::send_view_impression_event(s, v, i)
    }
}

#[cfg(not(test))]
pub type ImpressionsSender = CrossAppEventImpressions;
#[cfg(test)]
pub type ImpressionsSender = MockCanSendImpressions;

#[derive(Clone, Hash, Eq, PartialEq, Default, Debug)]
pub struct SlotRowCol(pub u32, pub u32);

pub struct OnboardingImpressionsReporter {
    tracker: RefCell<ImpressionTracker<SlotRowCol>>,
}

#[derive(Clone, Default)]
pub struct AugmentedImpressionData {
    impression_data: ImpressionData,
    slot: SlotRowCol,
}

impl AugmentedImpressionData {
    pub fn new(
        mut impression_data: ImpressionData,
        slot: SlotRowCol,
        size: Option<CardDimensions>,
    ) -> Self {
        impression_data.slot_id = Some((slot.1, slot.0));
        if let Some(size) = size {
            impression_data.size = Some((size.width, size.height));
        }
        Self {
            slot,
            impression_data,
        }
    }
    pub fn clone_inner(&self) -> ImpressionData {
        self.impression_data.clone()
    }
}

impl OnboardingImpressionsReporter {
    pub fn new() -> Self {
        Self {
            tracker: RefCell::new(ImpressionTracker::default()),
        }
    }

    pub fn send_select_impression(&self, scope: Scope, impression: &AugmentedImpressionData) {
        let mut tracker = self.tracker.borrow_mut();

        if !tracker.was_selected(&impression.slot) {
            ImpressionsSender::send_select_impression_event(scope, impression.clone_inner());
            tracker.set_selected(impression.slot.clone());
        }
    }

    pub fn send_view_impression(
        &self,
        scope: Scope,
        view: ViewImpressionData,
        impression: &AugmentedImpressionData,
    ) {
        let mut tracker = self.tracker.borrow_mut();

        if !tracker.was_viewed(&impression.slot) {
            ImpressionsSender::send_view_impression_event(scope, view, impression.clone_inner());
            tracker.set_viewed(impression.slot.clone());
        }
    }

    pub fn send_highlight_impression(&self, scope: Scope, impression: &AugmentedImpressionData) {
        let mut tracker = self.tracker.borrow_mut();

        if !tracker.was_highlighted(&impression.slot) {
            ImpressionsSender::send_highlight_impression_event(scope, impression.clone_inner());
            tracker.set_highlighted(impression.slot.clone());
        }
    }
}
