use cfg_test_attr_derive::derive_test_only;
use strum::Display;

#[derive_test_only(Debug, PartialEq)]
pub enum MetricNames {
    PageActionBegin,
    PageActionSuccessRate,
    PageActionOverallLatency,
    OnboardingPageEvent,
}

impl From<&MetricNames> for &'static str {
    fn from(value: &MetricNames) -> Self {
        match value {
            MetricNames::PageActionBegin => "PageAction.Begin",
            MetricNames::PageActionSuccessRate => "PageAction.SuccessRate",
            MetricNames::PageActionOverallLatency => "PageAction.OverallLatency",
            MetricNames::OnboardingPageEvent => "OnboardingPage.Event",
        }
    }
}

#[derive(Clone, Display)]
#[derive_test_only(Debug, PartialEq)]
pub enum MetricEvents {
    // Onboarding Events
    Render,
    Select,
    // Onboarding Service Events
    OnboardingCallSucceeded,
    OnboardingCallFailed,
    // Onboarding Reactive Events
    ReactionCallSucceeded,
    ReactionCallFailed,
}

#[derive(Clone, Display, PartialEq)]
pub enum MetricActions {
    Like,
    RemoveLike,
    Continue,
}

#[derive(Clone, Display)]
pub enum OnboardingMetricContext {
    Page,
    HeroStandard,
}
