use cfg_test_attr_derive::derive_test_only;
#[cfg(not(test))]
use cross_app_events::app_event::AppEventReporter;
#[cfg(test)]
use cross_app_events::app_event::MockAppEventReporter as AppEventReporter;
use cross_app_events::create_serde_map;
use navigation_menu::utils::{CollectionsPageParams, PageParams};
use serde_json::{Map, Value};

const BEGIN: &str = "COLLECTION_PAGE_LOAD_BEGIN";
const PAGE_ID_KEY: &str = "pageId";
const SUCCESS: &str = "COLLECTION_PAGE_LOAD_SUCCESS";
const FAILURE: &str = "COLLECTION_PAGE_LOAD_FAILED";
const FOCUS_RESTORATION: &str = "COLLECTION_PAGE_FOCUS_RESTORATION";
const FOCUS_RESTORATION_ACCURACY: &str = "COLLECTION_PAGE_FOCUS_RESTORATION_ACCURACY";
const RESPONSE_PARSED: &str = "COLLECTION_PAGE_RESPONSE_PARSED";
const RENDER_BEGIN: &str = "COLLECTION_PAGE_RENDER_BEGIN";
const RENDER_FINISH: &str = "COLLECTION_PAGE_RENDER_FINISH";
const NETWORK_SUCCESS: &str = "COLLECTION_PAGE_NETWORK_SUCCESS";
const NETWORK_FAILURE: &str = "COLLECTION_PAGE_NETWORK_FAILED";
const PROCESSING_SUCCESS: &str = "COLLECTION_PAGE_PROCESSING_SUCCESS";

const TRANSFORM_FAILURE_APP_FATAL: &str = "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_TRANSFORM";
const EMPTY_PAGE_APP_FATAL: &str = "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_EMPTY_PAGE";
const NETWORK_APP_FATAL: &str = "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_NETWORK";

const SOURCE: &str = "COLLECTION_PAGE";
const INITIAL_TRANSFORM: &str = "collectionsPageInitial";
const RESTORE_V2_TRANSFORM: &str = "restoreCollectionPageFocusV2";
const SPORTS_EDGE_INITIAL_TRANSFORM: &str = "collectionPageSportsEdgeInitial";

fn app_event(reporter: &AppEventReporter, name: &'static str) {
    reporter.send_app_event(name, SOURCE, None);
}

fn app_event_with_params(
    reporter: &AppEventReporter,
    name: &'static str,
    params: Map<String, Value>,
) {
    reporter.send_app_event(name, SOURCE, Some(params));
}

#[derive_test_only(Debug, PartialEq, Clone)]
pub(crate) enum CollectionsAppFatals {
    TransformFailure,
    EmptyPage,
    Network,
}

#[derive_test_only(Debug, PartialEq, Clone)]
pub(crate) enum CollectionsTransforms {
    CollectionsPageInitial,
    RestoreCollectionPageFocusV2,
    SportsEdgeInitial,
}

impl CollectionsTransforms {
    fn to_value(self) -> Value {
        match self {
            CollectionsTransforms::CollectionsPageInitial => INITIAL_TRANSFORM.into(),
            CollectionsTransforms::RestoreCollectionPageFocusV2 => RESTORE_V2_TRANSFORM.into(),
            CollectionsTransforms::SportsEdgeInitial => SPORTS_EDGE_INITIAL_TRANSFORM.into(),
        }
    }
}

#[derive_test_only(Debug, PartialEq, Clone)]
pub(crate) enum CollectionsAppEvents<'a> {
    Begin {
        page_params: &'a CollectionsPageParams,
        travelling_customer: bool,
        transition_source: Option<&'a Value>,
    },
    LoadSuccess {
        page_params: Option<&'a PageParams>,
    },
    LoadFailure {
        page_params: Option<&'a PageParams>,
    },
    FocusRestoration {
        transform: bool,
        v3_treatment: usize,
    },
    FocusRestorationAccuracy {
        matches: bool,
        version: &'a str,
        flow: &'a str,
    },
    ResponseParsed {
        time: u64,
    },
    RenderBegin,
    RenderFinished,
    NetworkSuccess {
        transform: CollectionsTransforms,
        time: u64,
    },
    NetworkFailure {
        transform: CollectionsTransforms,
    },
    ProcessingSuccess {
        transform: CollectionsTransforms,
        time: u64,
    },
    Fatal(CollectionsAppFatals),
}

impl CollectionsAppEvents<'_> {
    pub(crate) fn report(self, reporter: &AppEventReporter) {
        match self {
            CollectionsAppEvents::Begin {
                page_params,
                travelling_customer,
                transition_source,
            } => {
                let mut params = create_serde_map([
                    ("pageId", app_event_page_id(page_params).into()),
                    ("isTravelingCustomer", travelling_customer.into()),
                ]);
                if let Some(val) = transition_source {
                    params.insert("transitionSource".into(), val.clone());
                }
                app_event_with_params(reporter, BEGIN, params);
            }
            CollectionsAppEvents::LoadSuccess { page_params } => {
                app_event_with_params(
                    reporter,
                    SUCCESS,
                    create_serde_map([(
                        "pageId",
                        app_event_page_id_with_fallback(page_params).into(),
                    )]),
                );
            }
            CollectionsAppEvents::LoadFailure { page_params } => {
                app_event_with_params(
                    reporter,
                    FAILURE,
                    create_serde_map([(
                        "pageId",
                        app_event_page_id_with_fallback(page_params).into(),
                    )]),
                );
            }
            CollectionsAppEvents::FocusRestoration {
                transform,
                v3_treatment,
            } => {
                app_event_with_params(
                    reporter,
                    FOCUS_RESTORATION,
                    create_serde_map([
                        ("transform", transform.into()),
                        ("v3Treatment", v3_treatment.into()),
                    ]),
                );
            }
            CollectionsAppEvents::FocusRestorationAccuracy {
                matches,
                version,
                flow,
            } => {
                app_event_with_params(
                    reporter,
                    FOCUS_RESTORATION_ACCURACY,
                    create_serde_map([
                        ("match", matches.into()),
                        ("version", version.into()),
                        ("flow", flow.into()),
                    ]),
                );
            }
            CollectionsAppEvents::ResponseParsed { time } => app_event_with_params(
                reporter,
                RESPONSE_PARSED,
                create_serde_map([
                    ("endpoint", INITIAL_TRANSFORM.into()),
                    ("time", time.into()),
                ]),
            ),
            CollectionsAppEvents::RenderBegin => app_event(reporter, RENDER_BEGIN),
            CollectionsAppEvents::RenderFinished => app_event(reporter, RENDER_FINISH),
            CollectionsAppEvents::NetworkSuccess { transform, time } => app_event_with_params(
                reporter,
                NETWORK_SUCCESS,
                create_serde_map([("endpoint", transform.to_value()), ("time", time.into())]),
            ),
            CollectionsAppEvents::NetworkFailure { transform } => app_event_with_params(
                reporter,
                NETWORK_FAILURE,
                create_serde_map([("endpoint", transform.to_value())]),
            ),
            CollectionsAppEvents::ProcessingSuccess { transform, time } => app_event_with_params(
                reporter,
                PROCESSING_SUCCESS,
                create_serde_map([("endpoint", transform.to_value()), ("time", time.into())]),
            ),
            CollectionsAppEvents::Fatal(fatal) => match fatal {
                CollectionsAppFatals::TransformFailure => {
                    app_event(reporter, TRANSFORM_FAILURE_APP_FATAL);
                }
                CollectionsAppFatals::EmptyPage => {
                    app_event_with_params(
                        reporter,
                        EMPTY_PAGE_APP_FATAL,
                        create_serde_map([("endpoint", INITIAL_TRANSFORM.into())]),
                    );
                }
                CollectionsAppFatals::Network => {
                    app_event(reporter, NETWORK_APP_FATAL);
                }
            },
        }
    }
}

fn app_event_page_id_with_fallback(page_params: Option<&PageParams>) -> String {
    page_params.map_or_else(
        || "Unknown".to_string(),
        |params| {
            let PageParams::Collections(params) = params;
            app_event_page_id(params)
        },
    )
}

fn app_event_page_id(params: &CollectionsPageParams) -> String {
    format!("{}-{}", params.page_type, params.page_id)
}
