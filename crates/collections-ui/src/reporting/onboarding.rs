use crate::reporting::types::{<PERSON>ricA<PERSON>, MetricEvents, Metric<PERSON><PERSON>s, OnboardingMetricContext};
use ignx_compositron::prelude::{provide_context, use_context, Scope};
use metrics_client::client::{get_metric_client, MetricClient};
use metrics_client::common::timed_metric_reporter::TimedMetricReporter;
use std::cell::RefCell;
use std::rc::Rc;

const SOURCE: &str = "OnboardingPage";

/// Client for reporting Onboarding specific metrics
pub(crate) struct OnboardingMetricClient {
    metric_client: MetricClient,
    pub page_latency_reporter: RefCell<TimedMetricReporter>,
}

pub(crate) type OnboardingMetricClientContext = Rc<OnboardingMetricClient>;

/// Initialize the [`OnboardingMetricClient`]
pub(crate) fn provide_onboarding_client(scope: Scope) {
    provide_context(scope, Rc::new(OnboardingMetricClient::new()));
}

/// Get [`OnboardingMetricClient`] from the context
pub(crate) fn use_onboarding_client(scope: Scope) -> Option<OnboardingMetricClientContext> {
    use_context::<OnboardingMetricClientContext>(scope)
}

impl OnboardingMetricClient {
    pub(crate) fn new() -> Self {
        let client = get_metric_client();
        Self {
            metric_client: client.clone(),
            page_latency_reporter: RefCell::new(TimedMetricReporter::new(client)),
        }
    }

    /// Wrapper for reporting metrics, that restricts the metric names
    /// to the [`MetricNames`] enum
    fn report_metric(&self, metric: MetricNames, value: u128, dimensions: Vec<(String, String)>) {
        let mut dimensions = dimensions;

        dimensions.push(("pageType".to_string(), SOURCE.to_string()));
        dimensions.push(("activeLayer".to_string(), "Wasm".to_string()));

        self.metric_client.emit((&metric).into(), value, dimensions)
    }

    /// Report page loading start
    ///
    /// Also records a start time for latency reporting
    pub fn report_page_loading_started(&self) {
        let dimensions = vec![("actionName".to_string(), "Load".to_string())];

        self.page_latency_reporter
            .borrow_mut()
            .report_start((&MetricNames::PageActionBegin).into(), dimensions);
    }

    /// Report page loading finish
    ///
    /// Emits both `SuccessRate` and `OverallLatency` metrics
    pub fn report_page_loading_finished(&self) {
        let dimensions = vec![("actionName".to_string(), "Load".to_string())];

        self.page_latency_reporter.borrow_mut().report_end(
            (&MetricNames::PageActionOverallLatency).into(),
            dimensions.clone(),
        );
        self.report_metric(MetricNames::PageActionSuccessRate, 1, dimensions);
    }

    /// Report an event from [`MetricEvents`] enum
    ///
    /// Optional [`MetricActions`] enum for the action
    /// and [`OnboardingMetricContext`] enum for the context can be provided
    pub fn report_page_event(
        &self,
        event: MetricEvents,
        action: Option<MetricActions>,
        context: Option<OnboardingMetricContext>,
    ) {
        let mut dimensions: Vec<(String, String)> = vec![("event".to_string(), event.to_string())];

        if let Some(action) = action {
            dimensions.push(("action".to_string(), action.to_string()));
        }

        if let Some(context) = context {
            dimensions.push(("context".to_string(), context.to_string()));
        }

        self.report_metric(MetricNames::OnboardingPageEvent, 1, dimensions);
    }
}

#[cfg(test)]
pub mod test_utils {
    use crate::reporting::onboarding::OnboardingMetricClient;
    use ignx_compositron::prelude::{provide_context, Scope};
    use metrics_client::common::timed_metric_reporter::TimedMetricReporter;
    use metrics_client::test_utils::MetricClientEmitterSpy;
    use std::cell::RefCell;
    use std::rc::Rc;

    /// Initializes the [OnboardingMetricClient] with spy client instead of a real one
    pub fn provide_onboarding_client_with_spy(scope: Scope, spy: Rc<MetricClientEmitterSpy>) {
        provide_context(
            scope,
            Rc::new(OnboardingMetricClient {
                metric_client: spy.clone(),
                page_latency_reporter: RefCell::new(TimedMetricReporter::new(spy.clone())),
            }),
        )
    }
}
