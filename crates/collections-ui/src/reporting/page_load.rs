use firetv::{use_firetv_context, FireTVChannelIngressType, FireTVChannelTransitionEvent};
use ignx_compositron::prelude::AppContext;
use navigation_menu::utils::PageParams;

const CHANNEL_PAGE_TYPE: &str = "channel";
const SUBSCRIPTION_PAGE_TYPE: &str = "subscription";

pub(crate) fn fire_tv_page_reporting(ctx: &AppContext, page_params: Option<&PageParams>) {
    if let Some(PageParams::Collections(params)) = page_params {
        let event = match params.page_type.as_str() {
            CHANNEL_PAGE_TYPE => Some(FireTVChannelTransitionEvent::new(
                FireTVChannelIngressType::CHANNEL,
                &params.page_id,
            )),
            SUBSCRIPTION_PAGE_TYPE => Some(FireTVChannelTransitionEvent::new(
                FireTVChannelIngressType::SUBSCRIPTION,
                &params.page_id,
            )),
            _ => None,
        };
        if let Some(event) = event {
            use_firetv_context(ctx).propagate_transition_event(ctx, &event)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use firetv::{FireTVChannelIngressType, MockFireTV};
    use ignx_compositron::app::launch_only_app_context;
    use navigation_menu::utils::CollectionsPageParams;

    #[test]
    fn should_not_report_when_no_page_params() {
        launch_only_app_context(|ctx| {
            // Arrange
            let mut fire_tv = MockFireTV::default();

            // Expect no calls to propagate_transition_event
            fire_tv.expect_propagate_transition_event().times(0);
            fire_tv.provide_mock(ctx.scope());

            // Act
            fire_tv_page_reporting(&ctx, None);
        });
    }

    #[test]
    fn should_not_report_when_non_matching_page_type() {
        launch_only_app_context(|ctx| {
            // Arrange
            let mut fire_tv = MockFireTV::default();

            // Expect no calls to propagate_transition_event
            fire_tv.expect_propagate_transition_event().times(0);
            fire_tv.provide_mock(ctx.scope());

            // Act - pass Collections page params with non-matching page type
            let page_params = PageParams::Collections(CollectionsPageParams {
                page_id: "home".to_string(),
                page_type: "home".to_string(),
                service_token: None,
            });
            fire_tv_page_reporting(&ctx, Some(&page_params));
        });
    }

    #[test]
    fn should_report_channel_transition_when_channel_page_type() {
        launch_only_app_context(|ctx| {
            // Arrange
            let mut fire_tv = MockFireTV::default();

            // Set expectations for FireTV
            fire_tv
                .expect_propagate_transition_event()
                .withf(|_, event| {
                    // Verify exact parameters
                    matches!(event.ingress_type(), &FireTVChannelIngressType::CHANNEL)
                        && event.benefit_id() == "test-channel-id"
                })
                .times(1)
                .return_const(());

            fire_tv.provide_mock(ctx.scope());

            // Act - pass Collections page params with channel page type
            let page_params = PageParams::Collections(CollectionsPageParams {
                page_id: "test-channel-id".to_string(),
                page_type: "channel".to_string(),
                service_token: None,
            });
            fire_tv_page_reporting(&ctx, Some(&page_params));
        });
    }

    #[test]
    fn should_report_subscription_transition_when_subscription_page_type() {
        launch_only_app_context(|ctx| {
            // Arrange
            let mut fire_tv = MockFireTV::default();

            // Set expectations for FireTV
            fire_tv
                .expect_propagate_transition_event()
                .withf(|_, event| {
                    // Verify exact parameters
                    matches!(
                        event.ingress_type(),
                        &FireTVChannelIngressType::SUBSCRIPTION
                    ) && event.benefit_id() == "test-subscription-id"
                })
                .times(1)
                .return_const(());

            fire_tv.provide_mock(ctx.scope());

            // Act - pass Collections page params with subscription page type
            let page_params = PageParams::Collections(CollectionsPageParams {
                page_id: "test-subscription-id".to_string(),
                page_type: "subscription".to_string(),
                service_token: None,
            });
            fire_tv_page_reporting(&ctx, Some(&page_params));
        });
    }

    #[test]
    fn should_handle_empty_page_id() {
        launch_only_app_context(|ctx| {
            // Arrange
            let mut fire_tv = MockFireTV::default();

            // Set expectations for FireTV - should still call with empty string
            fire_tv
                .expect_propagate_transition_event()
                .withf(|_, event| {
                    // Verify exact parameters - empty page_id should be passed through
                    matches!(event.ingress_type(), &FireTVChannelIngressType::CHANNEL)
                        && event.benefit_id() == ""
                })
                .times(1)
                .return_const(());

            fire_tv.provide_mock(ctx.scope());

            // Act - pass Collections page params with empty page_id
            let page_params = PageParams::Collections(CollectionsPageParams {
                page_id: "".to_string(),
                page_type: "channel".to_string(),
                service_token: None,
            });
            fire_tv_page_reporting(&ctx, Some(&page_params));
        });
    }

    #[test]
    fn should_handle_malformed_page_id() {
        launch_only_app_context(|ctx| {
            // Arrange
            let mut fire_tv = MockFireTV::default();

            // Set expectations for FireTV - should still call with malformed string
            fire_tv
                .expect_propagate_transition_event()
                .withf(|_, event| {
                    // Verify exact parameters - malformed page_id should be passed through
                    matches!(
                        event.ingress_type(),
                        &FireTVChannelIngressType::SUBSCRIPTION
                    ) && event.benefit_id() == "invalid/page\\id"
                })
                .times(1)
                .return_const(());

            fire_tv.provide_mock(ctx.scope());

            // Act - pass Collections page params with malformed page_id
            let page_params = PageParams::Collections(CollectionsPageParams {
                page_id: "invalid/page\\id".to_string(),
                page_type: "subscription".to_string(),
                service_token: None,
            });
            fire_tv_page_reporting(&ctx, Some(&page_params));
        });
    }
}
