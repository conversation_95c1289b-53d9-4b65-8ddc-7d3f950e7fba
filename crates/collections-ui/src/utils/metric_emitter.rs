// GRCOV_STOP_COVERAGE
use std::time::Duration;

use ignx_compositron::prelude::metric;
use mockall::automock;

use super::freshness_metric::FreshnessAction;

pub struct MetricEmitter;

#[automock]
impl MetricEmitter {
    pub fn emit(metric: &'static str, value: u128, dimensions: Vec<(String, String)>) {
        metric!(metric, value as f64, dimensions);
    }
    pub fn emit_freshness_metric(time: Duration, page_type: String, action: FreshnessAction) {
        log::info!(
            "Displaying {} collections page with a freshness value of {}ms",
            page_type,
            time.as_millis()
        );
        metric!("PageAction.Freshness", time.as_millis() as f64, "pageType" => page_type, "actionName" => action);
    }
}
// GRCOV_BEGIN_COVERAGE
