use crate::network::types::FocusPosition;
use crate::ui::types::ContainerType;
use container_types::container_parsing::TransformId;
use container_types::ui_signals::{
    ContainerModel, DiscoveryAssistantModel, NodesCarouselModel, StandardCardContainerItemType,
    SuperCarouselItemType,
};
use ignx_compositron::reactive::*;

const DEFAULT_FOCUS: FocusPosition = FocusPosition {
    container: 0,
    item: 0,
    sub_index: None,
};
const CONTINUE_WATCHING_TAG: &str = "watchNextCarousel";

pub fn find_default_focus_after_restoration(
    container_list: ReadSignal<Vec<ContainerType>>,
    transform_container_id: &str,
    transform_item_id: &str,
) -> (FocusPosition, bool) {
    container_list.with_untracked(|container_list| {
        let continue_watching_container = find_continue_watching_container(container_list);

        if let Some((container, container_index)) = continue_watching_container {
            if let Some(item_index) = find_item_in_container(container, transform_item_id) {
                return (
                    FocusPosition {
                        container: container_index,
                        item: item_index,
                        sub_index: None,
                    },
                    true,
                );
            }
        }

        let targeted_container = find_container_by_criteria(container_list, |container| {
            container.transform_container_id() == transform_container_id
        });

        if let Some((container, container_index)) = targeted_container {
            if let Some(item_index) = find_item_in_container(container, transform_item_id) {
                // report it here.
                return (
                    FocusPosition {
                        container: container_index,
                        item: item_index,
                        sub_index: None,
                    },
                    true,
                );
            } else {
                return (
                    FocusPosition {
                        container: container_index,
                        item: 0,
                        sub_index: None,
                    },
                    false,
                );
            }
        }

        (DEFAULT_FOCUS.clone(), false)
    })
}

fn find_continue_watching_container(
    containers: &[ContainerType],
) -> Option<(&ContainerType, usize)> {
    let tag = CONTINUE_WATCHING_TAG.to_string();

    find_container_by_criteria(containers, |container| {
        container.model.with_untracked(|container| {
            if let ContainerModel::StandardCarousel(model) = container {
                model.with_untracked(|model| {
                    model
                        .standard_carousel_metadata
                        .with_untracked(|model| model.tags.contains(&tag))
                })
            } else {
                false
            }
        })
    })
}

fn find_container_by_criteria(
    containers: &[ContainerType],
    predicate: impl Fn(&ContainerType) -> bool,
) -> Option<(&ContainerType, usize)> {
    let position = containers.iter().position(predicate);

    position.and_then(|position| {
        let container = containers.get(position);
        container.map(|container| (container, position))
    })
}

fn find_item_in_container(container: &ContainerType, transform_item_id: &str) -> Option<usize> {
    let matches_transform_id = |t: &dyn TransformId| t.transform_id() == transform_item_id;

    container.model.with_untracked(|model| match model {
        ContainerModel::PromoBanner(model) => model.with_untracked(|model| {
            model
                .item
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::StandardCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::SuperCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::ChartsCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::ShortCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::EntityCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::StandardHero(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::TentpoleHero(model) => model.with_untracked(|model| {
            model
                .item
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::NodesCarousel(model) => model.with_untracked(|model| match model {
            NodesCarouselModel::EntitledOrAgnostic(model) => model.with_untracked(|model| {
                model
                    .items
                    .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
            }),
            NodesCarouselModel::Unentitled(model) => model.with_untracked(|model| {
                model
                    .items
                    .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
            }),
        }),
        ContainerModel::BeardSupportedCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::DiscoveryAssistant(model) => model.with_untracked(|model| match model {
            DiscoveryAssistantModel::Grid(model) => model.with_untracked(|model| {
                model.columns.with_untracked(|cols| {
                    cols.iter()
                        // Special case where the id combines different transform ids into a single string separated by a '_'
                        .position(|column| column.id.contains(transform_item_id))
                })
            }),
            DiscoveryAssistantModel::Pills(_) => None,
        }),
        ContainerModel::ScheduleCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::SpecialCollectionsCarousel(model) => model.with_untracked(|model| {
            model
                .items
                .with_untracked(|items| items.iter().position(|t| matches_transform_id(t)))
        }),
        ContainerModel::Grid(_) => None,
        ContainerModel::DiscoveryAssistantHeader(_) => None,
        ContainerModel::Onboarding(_) => None,
    })
}

pub fn should_skip_pagination(
    container_list: ReadSignal<Vec<ContainerType>>,
    focus_position: &FocusPosition,
) -> bool {
    container_list.with_untracked(|container_list| {
        let Some(container) = container_list.get(focus_position.container) else {
            return false;
        };

        container.model.with_untracked(|model| match model {
            ContainerModel::TentpoleHero(_) => true,
            ContainerModel::PromoBanner(_) => true,
            ContainerModel::StandardCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    let Some(item) = items.get(focus_position.item) else {
                        return false;
                    };

                    item.model.with_untracked(|item| {
                        matches!(item, StandardCardContainerItemType::Event(_))
                    })
                })
            }),
            ContainerModel::BeardSupportedCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    let Some(item) = items.get(focus_position.item) else {
                        return false;
                    };

                    item.model.with_untracked(|item| {
                        matches!(item, StandardCardContainerItemType::Event(_))
                    })
                })
            }),
            ContainerModel::SuperCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    let Some(item) = items.get(focus_position.item) else {
                        return false;
                    };

                    item.model
                        .with_untracked(|item| matches!(item, SuperCarouselItemType::Event(_)))
                })
            }),
            ContainerModel::ChartsCarousel(_) => false,
            ContainerModel::NodesCarousel(_) => false,
            ContainerModel::EntityCarousel(_) => false,
            ContainerModel::ShortCarousel(_) => false,
            ContainerModel::StandardHero(_) => false,
            ContainerModel::DiscoveryAssistant(_) => false,
            ContainerModel::DiscoveryAssistantHeader(_) => false,
            ContainerModel::Grid(_) => false,
            ContainerModel::Onboarding(_) => true,
            ContainerModel::ScheduleCarousel(_) => true,
            ContainerModel::SpecialCollectionsCarousel(_) => false,
        })
    })
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::test_assets::mocks::SuccessCallbackTestSetup;
    use crate::{
        network::parser::collections_response_parser,
        page::{
            helpers_sig::create_default_collections_page_model,
            helpers_sig::get_initial_load_success_cb,
        },
    };
    use ignx_compositron::prelude::AppContext;
    use ignx_compositron::{app::launch_only_app_context, reactive::create_rw_signal};
    use network::common::{DeviceProxyResponse, PageRequestStatus};
    use rstest::*;
    use rust_features::{provide_context_test_rust_features, MockRustFeaturesBuilder};
    use std::rc::Rc;

    fn get_mock_container_list(ctx: AppContext) -> ReadSignal<Vec<ContainerType>> {
        let scope = ctx.scope();
        let mock_features = MockRustFeaturesBuilder::new()
            .set_is_promo_banner_enabled(true)
            .build();
        provide_context(scope, mock_features);
        provide_context_test_rust_features(scope);

        let page_data = collections_response_parser(
            include_str!("../test_assets/collections_page_restored_response.json").to_string(),
        )
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .unwrap();

        let page_data_signal = create_default_collections_page_model(scope);
        let load_status_signal =
            create_rw_signal(scope, PageRequestStatus::Success("home-home".to_string()));

        let setup = SuccessCallbackTestSetup::new(ctx)
            .with_page_data_and_status(page_data_signal, load_status_signal)
            .with_parsed_page(page_data);

        let page_controller = setup.to_controller();

        let success_cb =
            get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

        let args = setup.to_success_cb_args();

        success_cb(
            args.parsed_page_from_network.clone(),
            args.transform_name,
            args.manipulate_page_data,
            args.timestamp,
        );

        page_data_signal.get_untracked().container_list.read_only()
    }

    #[test]
    fn should_restore_to_standard_carousel() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), &"amzn1.dv.gti.4613fb5b-4e16-428d-a892-24d67b81a859".to_string()), (FocusPosition { container: 3, item: 1, sub_index: None, }, true));
        });
    }

    #[test]
    fn should_restore_to_super_carousel() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxU09NMURCSzhDTUJBIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), &"amzn1.dv.gti.d35af27a-6a45-47e8-8c6c-d3fe8d904336".to_string()), (FocusPosition { container: 2, item: 3, sub_index: None, }, true));
        });
    }

    #[test]
    fn should_restore_to_short_carousel() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7mio6qc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNzaG9ydGNhcm91c2Vsi4R0ZXN0jI6qMToxMjJKNUJNQUlIUjlYNCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), &"landing:merch:kids".to_string()), (FocusPosition { container: 1, item: 4, sub_index: None }, true));
        });
    }

    #[test]
    fn should_restore_to_standard_hero() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt67ioRob21li4Rob21ljI6mMToxMjJZTU9SUTEwNkdKUiMjTkJTWEUzMkRNRlpHNjVMVE1WV0GND46CVjI=".to_string(), &"amzn1.dv.gti.9358ec09-06d8-4365-a885-c8fe38115efe".to_string()), (FocusPosition { container: 0, item: 6, sub_index: None, }, true));
        });
    }

    #[test]
    fn should_restore_to_nodes_carousel() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=X_enodes_Xu69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyT0NaNzhLUjI0VTZGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), &"landing:subscription:dailyburn".to_string()), (FocusPosition { container: 8, item: 1, sub_index: None, }, true));
        });
    }

    #[test]
    fn should_restore_to_promo_banner() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt65ioRob21li4J0doyOpjE6MTJFQ1IyRzRXWkpPUk8jI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy".to_string(), &"amzn1.dv.gti.cfc6048f-1dfc-4453-acc9-cba5e3b59f3f".to_string()), (FocusPosition { container: 11, item: 0, sub_index: None, }, true));
        });
    }

    #[test]
    fn should_restore_to_beard_supported_carousel() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRsaXZli4Rob21ljI6qMToxMVIzOFY0Tks2SFgyMiMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), &"amzn1.dv.gti.3bc8aeac-a1c8-4170-9df9-c9d5b425ac9c".to_string()), (FocusPosition { container: 9, item: 1, sub_index: None, }, true));
        });
    }

    #[test]
    #[ignore]
    fn should_restore_to_discovery_assistant() {
        todo!("[LR-Rust-399][LR-Rust-373] focus restoration")
    }

    #[test]
    fn should_restore_to_continue_watching_if_item_appears_in_both() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), &"amzn1.dv.gti.17b73916-9ce2-47be-bb33-b211c4f92ff2".to_string()), (FocusPosition { container: 4, item: 3, sub_index: None, }, true));
        });
    }

    #[test]
    fn should_restore_to_continue_watching_if_not_found_in_container_but_item_appears_in_continue_watching(
    ) {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), &"amzn1.dv.gti.16bb7228-5aeb-455e-52e8-a719d2297c29".to_string()), (FocusPosition { container: 4, item: 5, sub_index: None, }, true));
        });
    }

    #[test]
    fn should_restore_to_continue_watching_if_container_is_missing_but_item_appears_in_continue_watching(
    ) {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(
                find_default_focus_after_restoration(
                    container_list,
                    &"unknown_container".to_string(),
                    &"amzn1.dv.gti.16bb7228-5aeb-455e-52e8-a719d2297c29".to_string()
                ),
                (
                    FocusPosition {
                        container: 4,
                        item: 5,
                        sub_index: None,
                    },
                    true
                )
            );
        });
    }

    #[test]
    fn should_restore_to_continue_watching_directly() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(
                find_default_focus_after_restoration(
                    container_list,
                    &"continueWatchingId".to_string(),
                    &"amzn1.dv.gti.16bb7228-5aeb-455e-52e8-a719d2297c29".to_string()
                ),
                (
                    FocusPosition {
                        container: 4,
                        item: 5,
                        sub_index: None,
                    },
                    true
                )
            );
        });
    }

    #[test]
    fn should_return_default_focus_when_container_not_found_and_not_in_continue_watching() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(
                find_default_focus_after_restoration(
                    container_list,
                    &"unknown-container".to_string(),
                    &"amzn1.dv.gti.4613fb5b-4e16-428d-a892-24d67b81a859".to_string()
                ),
                (
                    FocusPosition {
                        container: 0,
                        item: 0,
                        sub_index: None,
                    },
                    false
                )
            );
        });
    }

    #[test]
    fn should_return_index_0_of_container_when_container_found_but_item_not_found() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), &"unknownItem".to_string()), (FocusPosition { container: 3, item: 0, sub_index: None, }, false));
        });
    }

    #[test]
    fn should_return_index_0_of_container_when_container_found_but_item_is_in_another_container() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(find_default_focus_after_restoration(container_list, &"V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), &"amzn1.dv.gti.5c2c1110-ccb6-4922-9690-1193f46fa535".to_string()), (FocusPosition { container: 3, item: 0, sub_index: None,}, false));
        });
    }

    #[rstest]
    #[case(FocusPosition { container: 0, item: 0, sub_index: None }, false)] // super hero.
    #[case(FocusPosition { container: 1, item: 4, sub_index: None }, false)] // short carousel.
    #[case(FocusPosition { container: 3, item: 2, sub_index: None }, false)] // standard carousel - non event.
    #[case(FocusPosition { container: 3, item: 8, sub_index: None }, true)] // standard carousel - event.
    #[case(FocusPosition { container: 2, item: 3, sub_index: None }, false)] // super carousel - non event.
    #[case(FocusPosition { container: 2, item: 9, sub_index: None }, true)] // super carousel - event.
    #[case(FocusPosition { container: 7, item: 1, sub_index: None }, false)] // charts carousel.
    #[case(FocusPosition { container: 8, item: 1, sub_index: None }, false)] // nodes carousel.
    #[case(FocusPosition { container: 10, item: 1, sub_index: None }, false)] // discovery assistant
    #[case(FocusPosition { container: 99, item: 1, sub_index: None }, false)] // container not found.
    #[case(FocusPosition { container: 11, item: 0, sub_index: None }, true)] // promo banner
    #[case(FocusPosition { container: 3, item: 99, sub_index: None }, false)] // item not found.
    fn should_skip_pagination_should_be_correct(
        #[case] position: FocusPosition,
        #[case] expected: bool,
    ) {
        launch_only_app_context(move |ctx| {
            let container_list = get_mock_container_list(ctx);

            assert_eq!(should_skip_pagination(container_list, &position), expected);
        });
    }
}
