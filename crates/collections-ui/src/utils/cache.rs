use crate::network::types::CachedFocusData;
use crate::types::collections_types::CollectionsPageModelCacheable;
use cache::cache_invalidation::emitter::CacheInvalidationEventEmitterRc;
use cache::cache_invalidation::events::CacheInvalidationEvent;
use cache::expirable_lru_cache::ExpirableLruCache;
use cache::ExpirableLruCacheRc;
use ignx_compositron::prelude::metric;
use ignx_compositron::reactive::Scope;
use location::{PageType, RustPage};
use std::time::Duration;

#[derive(Clone)]
pub struct CachedCollectionsPage {
    pub data: Option<CollectionsPageModelCacheable>,
    pub focus: Option<CachedFocusData>,
    pub is_resiliency_enabled: Option<bool>,
}

const MY_STUFF_KEY: &str = "home-MyStuff-None";

pub fn create_cache(
    scope: Scope,
    event_emitter: Option<CacheInvalidationEventEmitterRc>,
) -> ExpirableLruCacheRc<String, CachedCollectionsPage> {
    let cache =
        ExpirableLruCache::<String, CachedCollectionsPage>::new_rc(10, Box::new(|_| Duration::MAX));

    if let Some(event_emitter) = event_emitter {
        let cache = cache.clone();

        let on_event_emitted = move |event: CacheInvalidationEvent| match event {
            CacheInvalidationEvent::DroppedCard | CacheInvalidationEvent::WatchlistToggled => {
                if let Some(entry) = cache.borrow_mut().get_mut(&MY_STUFF_KEY.to_string()) {
                    let page_source = PageType::Rust(RustPage::RUST_COLLECTIONS).to_string();
                    let action_name = match event {
                        CacheInvalidationEvent::DroppedCard => "CollectionsCacheClear_DroppedCard",
                        CacheInvalidationEvent::WatchlistToggled => {
                            "CollectionsCacheClear_WatchlistToggled"
                        }
                        _ => "CollectionsCacheClear_Unknown",
                    };
                    metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => action_name);
                    entry.data = None;
                }
            }
            CacheInvalidationEvent::LegacyUndefinedReasonFromJS
            | CacheInvalidationEvent::TransitionedToPlayback
            | CacheInvalidationEvent::AcquisitionStart
            | CacheInvalidationEvent::TitleReaction
            | CacheInvalidationEvent::Inactivity
            | CacheInvalidationEvent::AuthChange
            | CacheInvalidationEvent::LocaleChange
            | CacheInvalidationEvent::SyncContent => {
                cache.borrow_mut().mutate_all(move |_, value| {
                    value.data = None;
                });
            }
            CacheInvalidationEvent::ProfileChange => {}
        };

        event_emitter
            .borrow_mut()
            .subscribe(scope, Box::new(on_event_emitted));
    }

    cache
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::network::types::FocusPosition;
    use crate::page::helpers_sig::create_default_collections_page_model;
    use cache::cache_invalidation::emitter::CacheInvalidationEventEmitter;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::prelude::*;
    use rstest::*;
    use std::{cell::RefCell, rc::Rc};

    use super::create_cache;

    fn add_to_cache(
        scope: Scope,
        cache: ExpirableLruCacheRc<String, CachedCollectionsPage>,
        key: String,
    ) {
        cache.borrow_mut().put(
            key,
            CachedCollectionsPage {
                data: Some(create_default_collections_page_model(scope).get().into()),
                focus: Some(CachedFocusData {
                    container_id: "container_id".to_string(),
                    item_id: "item_id".to_string(),
                    should_skip_pagination: false,
                    position: FocusPosition {
                        container: 0,
                        item: 0,
                        sub_index: None,
                    },
                }),
                is_resiliency_enabled: Some(false),
            },
        );
    }

    #[test]
    fn should_create_cache() {
        launch_test(
            |ctx| {
                compose! { Rectangle() }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let event_emitter = CacheInvalidationEventEmitter::new_rc_without_rpc();
                create_cache(scope, Some(event_emitter));
            },
        );
    }

    #[test]
    fn should_create_cache_without_event_emitter() {
        launch_test(
            |ctx| {
                compose! { Rectangle() }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                create_cache(scope, None);
            },
        );
    }

    #[rstest]
    #[case(CacheInvalidationEvent::LegacyUndefinedReasonFromJS)]
    #[case(CacheInvalidationEvent::TransitionedToPlayback)]
    #[case(CacheInvalidationEvent::AcquisitionStart)]
    #[case(CacheInvalidationEvent::TitleReaction)]
    #[case(CacheInvalidationEvent::Inactivity)]
    #[case(CacheInvalidationEvent::AuthChange)]
    #[case(CacheInvalidationEvent::LocaleChange)]
    fn should_clear_entire_cache_when_event_is_emitted(#[case] event: CacheInvalidationEvent) {
        launch_test(
            |ctx| {
                compose! { Rectangle() }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();

                let event_emitter = CacheInvalidationEventEmitter::new_rc_without_rpc();
                let cache: Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>> =
                    create_cache(scope, Some(event_emitter.clone()));

                add_to_cache(scope, cache.clone(), "home-home".to_string());
                add_to_cache(scope, cache.clone(), "home-MyStuff".to_string());

                event_emitter.borrow().emit(event);

                assert!(cache
                    .borrow_mut()
                    .get(&"home-home".to_string())
                    .unwrap()
                    .data
                    .is_none());
                assert!(cache
                    .borrow_mut()
                    .get(&"home-MyStuff".to_string())
                    .unwrap()
                    .data
                    .is_none());
            },
        );
    }

    #[rstest]
    #[case(CacheInvalidationEvent::DroppedCard)]
    #[case(CacheInvalidationEvent::WatchlistToggled)]
    fn should_clear_my_stuff_when_event_is_emitted(#[case] event: CacheInvalidationEvent) {
        launch_test(
            |ctx| {
                compose! { Rectangle() }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();

                let event_emitter = CacheInvalidationEventEmitter::new_rc_without_rpc();
                let cache: Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>> =
                    create_cache(scope, Some(event_emitter.clone()));

                // doesn't fail if no my stuff in the cache.
                event_emitter.borrow().emit(event.clone());

                add_to_cache(scope, cache.clone(), "home-home".to_string());
                add_to_cache(scope, cache.clone(), "home-MyStuff-None".to_string());

                event_emitter.borrow().emit(event);

                assert!(cache
                    .borrow_mut()
                    .get(&"home-home".to_string())
                    .unwrap()
                    .data
                    .is_some());
                assert!(cache
                    .borrow_mut()
                    .get(&"home-MyStuff-None".to_string())
                    .unwrap()
                    .data
                    .is_none());
            },
        );
    }
}
