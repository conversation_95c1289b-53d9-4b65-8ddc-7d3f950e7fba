use crate::ui::types::SeamlessPlaybackContext;
use common_transform_types::actions::TransitionAction;
use container_item_types::linear_utils;
use container_types::ui_signals::CommonCarouselCardMetadata;
use container_types::ui_signals::StandardCardContainerItemType;
use ignx_compositron::app::rpc::RPCError;
#[double]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::log;
use ignx_compositron::prelude::*;
use mockall_double::double;
use playback_navigation::types::PlaybackOrigin;

fn has_playback_action(item_type: &StandardCardContainerItemType) -> bool {
    let metadata_sig: RwSignal<CommonCarouselCardMetadata> = item_type.into();
    metadata_sig.with_untracked(|metadata| matches!(&metadata.action, TransitionAction::player(_)))
}

pub fn seamless_playback_context_for_standard_card(
    item_type: &StandardCardContainerItemType,
    scope: Scope,
) -> SeamlessPlaybackContext {
    // Seamless transition only enabled for LiveLinear cards
    let seamless = matches!(item_type, StandardCardContainerItemType::LiveLinear(_))
        && has_playback_action(item_type)
        && linear_utils::autoplay::is_linear_autoplay_feature_enabled(scope)
        && linear_utils::autoplay::is_autoplay_enabled(scope);

    // Seamless linear transition has its own playback origin
    SeamlessPlaybackContext {
        enabled: seamless,
        origin: seamless.then_some(PlaybackOrigin::LinearCarouselAutoplay),
    }
}

pub fn try_record_linear_autoplay_trigger(scope: Scope, item_type: &StandardCardContainerItemType) {
    // Only trigger for entitled, LiveLinear cards
    // No trigger if autoplay is disabled
    if !(matches!(item_type, StandardCardContainerItemType::LiveLinear(_))
        && has_playback_action(item_type)
        && linear_utils::autoplay::is_autoplay_enabled(scope))
    {
        return;
    }

    log::error!("linearAutoplayInCarouselExperimentTrigger triggered");
    if let Some(rpc_manager) = use_context::<RPCManager>(scope) {
        rpc_manager
            .call::<()>("linearAutoplayInCarouselExperimentTrigger")
            .error_callback(Box::new(|error: RPCError| {
                log::error!("linearAutoplayInCarouselExperimentTrigger failed {}", error);
            }))
            .send();
    } else {
        log::error!("[LEX] Failed to locate RPCManager in context - will not record triggers for linear autoplay experiment");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use common_transform_types::actions::{Action, PlaybackAction, SignUpAction};
    use container_item_types::linear_utils::autoplay::get_linear_autoplay_video_id;
    use container_types::ui_signals::{
        CommonCarouselCardMetadata, StandardCardContainerItemModel, StandardCardData,
        StandardCardUIData,
    };
    use core::assert_eq;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::app::rpc::{MockRPCCall, MockRPCManager};
    use media_background::types::MediaBackgroundType;
    use mockall::predicate;
    use rstest::*;
    use rust_features::MockRustFeaturesBuilder;
    use settings_manager::{ApplicationSettingsContext, MockApplicationSettings};
    use std::collections::HashMap;
    use std::rc::Rc;
    use title_details::types::common::{StandardTitleDetailsData, TitleData, TitleDetailsMetadata};

    fn expect_record_trigger_rpc(rpc_manager: &mut RPCManager) {
        let mut rpc_manager_clone = MockRPCManager::new();

        let mut mock_rpc_call: MockRPCCall<()> = MockRPCCall::new();

        let mut mock_rpc_call_2 = MockRPCCall::new();
        mock_rpc_call_2.expect_send().return_once(move || ());

        mock_rpc_call
            .expect_error_callback()
            .once()
            .return_once(move |_| mock_rpc_call_2);

        rpc_manager_clone
            .expect_call()
            .with(predicate::eq("linearAutoplayInCarouselExperimentTrigger"))
            .once()
            .return_once(move |_| mock_rpc_call);

        rpc_manager
            .expect_clone()
            .once()
            .return_once(move || rpc_manager_clone);
    }

    fn get_player_action() -> TransitionAction {
        TransitionAction::player(PlaybackAction::create_populated_action("SomeUri"))
    }

    fn get_signup_action() -> TransitionAction {
        TransitionAction::signUp(SignUpAction {
            text: None,
            analytics: HashMap::new(),
            benefitId: String::default(),
            bundleId: None,
            nonSupportedText: String::default(),
            uri: String::default(),
            benefitName: None,
            offerDetails: None,
            refMarker: String::default(),
        })
    }

    fn mock_standard_card(
        scope: Scope,
        entitled: bool,
    ) -> RwSignal<StandardCardContainerItemModel> {
        let action = if entitled {
            get_player_action()
        } else {
            get_signup_action()
        };

        create_rw_signal(
            scope,
            StandardCardContainerItemModel {
                title_details_data: create_rw_signal(
                    scope,
                    StandardTitleDetailsData {
                        title_data: TitleData {
                            title_art_url: None,
                            provider_logo_url: None,
                            title_text: String::default(),
                        },
                        metadata: TitleDetailsMetadata::None,
                        synopsis: None,
                        entitlement_data: Default::default(),
                    },
                ),
                media_background_data: create_rw_signal(scope, MediaBackgroundType::None),
                carousel_card_data: create_rw_signal(
                    scope,
                    StandardCardData {
                        metadata: create_rw_signal(
                            scope,
                            CommonCarouselCardMetadata::from_id_and_action(
                                String::default(),
                                action,
                            ),
                        ),
                        card_ui_data: create_rw_signal(
                            scope,
                            StandardCardUIData {
                                title: create_rw_signal(scope, None),
                                subtitle: create_rw_signal(scope, None),
                                progress_bar_props: create_rw_signal(scope, None),
                                badge_props: create_rw_signal(scope, None),
                                icon_props: create_rw_signal(scope, None),
                                card_image: create_rw_signal(scope, None),
                                provider_logo_props: create_rw_signal(scope, None),
                                fallback_text: create_rw_signal(scope, None),
                                text_overlay_position: create_rw_signal(scope, None),
                                tts_details_data: create_rw_signal(scope, None),
                                card_secondary_base_image: create_rw_signal(scope, None),
                                card_image_base_attributes: create_rw_signal(
                                    scope,
                                    Default::default(),
                                ),
                                item_type_test_id: create_rw_signal(scope, Default::default()),
                            },
                        ),
                        linear_schedule_data: vec![],
                    },
                ),
                contextual_menu_metadata: create_rw_signal(scope, Default::default()),
            },
        )
    }

    fn get_live_linear_item(scope: Scope, entitled: bool) -> StandardCardContainerItemType {
        StandardCardContainerItemType::LiveLinear(mock_standard_card(scope, entitled))
    }

    fn get_movie_item(scope: Scope) -> StandardCardContainerItemType {
        StandardCardContainerItemType::Movie(mock_standard_card(scope, false))
    }

    fn setup(
        scope: Scope,
        autoplay_enabled: bool,
        firetv_pcon_restrictions_enabled: bool,
        feature_enabled: bool,
    ) {
        MockRustFeaturesBuilder::new()
            .set_is_linear_station_autoplay_in_carousels_enabled(feature_enabled)
            .build_as_mock_and_real_into_context(false, scope);

        let mut mock_settings_manager = MockApplicationSettings::default();
        mock_settings_manager
            .expect_get_autoplay_enabled()
            .return_const_st(Signal::derive(scope, move || autoplay_enabled));
        mock_settings_manager
            .expect_get_firetv_pcon_viewing_restrictions_enabled()
            .return_const_st(Signal::derive(scope, move || {
                firetv_pcon_restrictions_enabled
            }));
        let settings_context: ApplicationSettingsContext = Rc::new(mock_settings_manager);
        provide_context(scope, settings_context);
    }

    const IS_LINEAR_ITEM: bool = true;
    const IS_NON_LINEAR_ITEM: bool = false;
    const IS_ENTITLED: bool = true;
    const IS_UNENTITLED: bool = false;
    const AUTOPLAY_ENABLED: bool = true;
    const AUTOPLAY_DISABLED: bool = false;
    const FEATURE_ENABLED: bool = true;
    const FEATURE_DISABLED: bool = false;
    const FIRETV_PCON_RESTRICTIONS_ENABLED: bool = true;
    const FIRETV_PCON_RESTRICTIONS_DISABLED: bool = false;
    const SHOULD_TRIGGER: bool = true;
    const SHOULD_NOT_TRIGGER: bool = false;
    const SHOULD_AUTOPLAY: bool = true;
    const SHOULD_NOT_AUTOPLAY: bool = false;

    #[rstest]
    #[case::all_criteria_true(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SHOULD_NOT_TRIGGER
    )]
    #[case::all_criteria_true_firetv_pcon_restrictions_disabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SHOULD_TRIGGER
    )]
    #[case::not_linear(
        IS_NON_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SHOULD_NOT_TRIGGER
    )]
    #[case::not_linear_firetv_pcon_restrictions_disabled(
        IS_NON_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SHOULD_NOT_TRIGGER
    )]
    #[case::unentitled(
        IS_LINEAR_ITEM,
        IS_UNENTITLED,
        AUTOPLAY_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SHOULD_NOT_TRIGGER
    )]
    #[case::unentitled_firetv_pcon_restrictions_disabled(
        IS_LINEAR_ITEM,
        IS_UNENTITLED,
        AUTOPLAY_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SHOULD_NOT_TRIGGER
    )]
    #[case::autoplay_disabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_DISABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SHOULD_NOT_TRIGGER
    )]
    #[case::autoplay_disabled_firetv_pcon_restrictions_disabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_DISABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SHOULD_NOT_TRIGGER
    )]
    fn try_record_linear_autoplay_trigger_records_trigger(
        #[case] is_linear_item: bool,
        #[case] entitled: bool,
        #[case] autoplay_enabled: bool,
        #[case] firetv_pcon_restrictions_enabled: bool,
        #[case] should_trigger: bool,
    ) {
        launch_only_scope(move |scope| {
            setup(
                scope,
                autoplay_enabled,
                firetv_pcon_restrictions_enabled,
                false,
            );

            let mut rpc_manager = MockRPCManager::new();
            // only trigger weblab if all criteria are met
            if should_trigger {
                expect_record_trigger_rpc(&mut rpc_manager);
            }
            provide_context(scope, rpc_manager);

            let item = if is_linear_item {
                get_live_linear_item(scope, entitled)
            } else {
                get_movie_item(scope)
            };

            try_record_linear_autoplay_trigger(scope, &item);
        })
    }

    #[rstest]
    #[case::enabled_entitled_firetv_pcon_restrictions_enabled(
        get_player_action(),
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SHOULD_NOT_AUTOPLAY
    )]
    #[case::enabled_entitled_firetv_pcon_restrictions_disabled(
        get_player_action(),
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SHOULD_AUTOPLAY
    )]
    #[case::enabled_unentitled_firetv_pcon_restrictions_enabled(
        get_signup_action(),
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SHOULD_NOT_AUTOPLAY
    )]
    #[case::enabled_unentitled_firetv_pcon_restrictions_disabled(
        get_signup_action(),
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SHOULD_NOT_AUTOPLAY
    )]
    #[case::disabled_entitled_firetv_pcon_restrictions_enabled(
        get_player_action(),
        FEATURE_DISABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SHOULD_NOT_AUTOPLAY
    )]
    #[case::disabled_entitled_firetv_pcon_restrictions_disabled(
        get_player_action(),
        FEATURE_DISABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SHOULD_NOT_AUTOPLAY
    )]
    fn get_linear_autoplay_video_id_returns_correct_video_id(
        #[case] action: TransitionAction,
        #[case] linear_autoplay_enabled: bool,
        #[case] firetv_pcon_restrictions_enabled: bool,
        #[case] should_autoplay: bool,
    ) {
        launch_only_scope(move |scope| {
            // For autoplay to work, both the feature and autoplay setting need to be enabled
            let autoplay_enabled = should_autoplay;
            setup(
                scope,
                autoplay_enabled,
                firetv_pcon_restrictions_enabled,
                linear_autoplay_enabled,
            );

            let result =
                get_linear_autoplay_video_id(&Some(Action::TransitionAction(action)), scope);

            let expected_video_id = if should_autoplay {
                Some("SomeUri".to_string())
            } else {
                None
            };

            assert_eq!(result, expected_video_id);
        })
    }

    #[rstest]
    #[case::all_criteria_true_firetv_pcon_restrictions_enabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::all_criteria_true_firetv_pcon_restrictions_disabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SeamlessPlaybackContext {
            enabled: true,
            origin: Some(PlaybackOrigin::LinearCarouselAutoplay)
        }
    )]
    #[case::not_linear_firetv_pcon_restrictions_enabled(
        IS_NON_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::not_linear_firetv_pcon_restrictions_disabled(
        IS_NON_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::unentitled_firetv_pcon_restrictions_enabled(
        IS_LINEAR_ITEM,
        IS_UNENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::unentitled_firetv_pcon_restrictions_disabled(
        IS_LINEAR_ITEM,
        IS_UNENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::autoplay_disabled_firetv_pcon_restrictions_enabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_DISABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::autoplay_disabled_firetv_pcon_restrictions_disabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_DISABLED,
        FEATURE_ENABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::feature_disabled_firetv_pcon_restrictions_enabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_DISABLED,
        FIRETV_PCON_RESTRICTIONS_ENABLED,
        SeamlessPlaybackContext::default()
    )]
    #[case::feature_disabled_firetv_pcon_restrictions_disabled(
        IS_LINEAR_ITEM,
        IS_ENTITLED,
        AUTOPLAY_ENABLED,
        FEATURE_DISABLED,
        FIRETV_PCON_RESTRICTIONS_DISABLED,
        SeamlessPlaybackContext::default()
    )]
    fn seamless_playback_transition_for_standard_card_enabled_builds_context(
        #[case] is_linear_item: bool,
        #[case] entitled: bool,
        #[case] autoplay_enabled: bool,
        #[case] feature_enabled: bool,
        #[case] firetv_pcon_restrictions_enabled: bool,
        #[case] expected: SeamlessPlaybackContext,
    ) {
        launch_only_scope(move |scope| {
            setup(
                scope,
                autoplay_enabled,
                firetv_pcon_restrictions_enabled,
                feature_enabled,
            );

            let item = if is_linear_item {
                get_live_linear_item(scope, entitled)
            } else {
                get_movie_item(scope)
            };

            let result = seamless_playback_context_for_standard_card(&item, scope);
            assert_eq!(result, expected);
        })
    }
}
