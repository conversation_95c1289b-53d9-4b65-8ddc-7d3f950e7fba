use crate::network::types::FocusPosition;
use crate::ui::types::ContainerType;
use container_types::container_parsing::TransformId;
use container_types::ui_signals::{
    ContainerModel, DiscoveryAssistantModel, DiscoveryCardColumn, NodesCarouselModel,
};
use ignx_compositron::reactive::*;

pub fn get_container_and_item_ids(
    container_list: ReadSignal<Vec<ContainerType>>,
    focus_pos: &FocusPosition,
) -> (String, String) {
    container_list.with_untracked(|container_list| {
        let Some(container) = container_list.get(focus_pos.container) else {
            log::warn!(
                "[get_container_and_item_ids] cannot find container at {}",
                focus_pos.container
            );
            // intentionally return "unknown" to not match any id for focus restoration when index is out of bound
            return ("unknown".to_string(), "unknown".to_string());
        };

        let clone_transform_id = |t: &dyn TransformId| t.transform_id().to_string();

        let item_id: Option<String> = container.model.with_untracked(|model| match model {
            ContainerModel::StandardCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::PromoBanner(model) => model.with_untracked(|model| {
                model.item.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::SuperCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::ChartsCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::ShortCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::EntityCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::StandardHero(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::TentpoleHero(model) => model.with_untracked(|model| {
                model.item.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::NodesCarousel(model) => model.with_untracked(|model| match model {
                NodesCarouselModel::EntitledOrAgnostic(model) => model.with_untracked(|model| {
                    let leading_carousel_button = model
                        .leading_carousel_button
                        .with_untracked(|button| button.clone());
                    if let Some(leading_carousel_button) = leading_carousel_button {
                        if focus_pos.item == 0 {
                            Some(leading_carousel_button.transform_id().to_string())
                        } else {
                            model.items.with_untracked(|items| {
                                items.get(focus_pos.item - 1).map(|t| clone_transform_id(t))
                            })
                        }
                    } else {
                        model.items.with_untracked(|items| {
                            items.get(focus_pos.item).map(|t| clone_transform_id(t))
                        })
                    }
                }),
                NodesCarouselModel::Unentitled(model) => model.with_untracked(|model| {
                    let leading_carousel_button = model
                        .leading_carousel_button
                        .with_untracked(|button| button.clone());
                    if let Some(leading_carousel_button) = leading_carousel_button {
                        if focus_pos.item == 0 {
                            Some(leading_carousel_button.transform_id().to_string())
                        } else {
                            model.items.with_untracked(|items| {
                                items.get(focus_pos.item - 1).map(|t| clone_transform_id(t))
                            })
                        }
                    } else {
                        model.items.with_untracked(|items| {
                            items.get(focus_pos.item).map(|t| clone_transform_id(t))
                        })
                    }
                }),
            }),
            ContainerModel::BeardSupportedCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::DiscoveryAssistant(model) => {
                model.with_untracked(|model| match model {
                    DiscoveryAssistantModel::Grid(model) => model.with_untracked(|model| {
                        model.columns.with_untracked(|cols| {
                            cols.get(focus_pos.item).and_then(|i| {
                                i.column.with_untracked(|c| {
                                    let item = match c {
                                        DiscoveryCardColumn::Info(_) => None,
                                        DiscoveryCardColumn::Triple(t) => {
                                            focus_pos.sub_index.and_then(|idx| match idx {
                                                0 => Some(t.top),
                                                1 => t.middle,
                                                2 => t.bottom,
                                                _ => None,
                                            })
                                        }
                                        DiscoveryCardColumn::DoubleMediumTop(d) => {
                                            focus_pos.sub_index.and_then(|idx| match idx {
                                                0 => Some(d.top),
                                                1 => d.bottom,
                                                _ => None,
                                            })
                                        }
                                        DiscoveryCardColumn::DoubleMediumBottom(d) => {
                                            focus_pos.sub_index.and_then(|idx| match idx {
                                                0 => Some(d.top),
                                                1 => d.bottom,
                                                _ => None,
                                            })
                                        }
                                    };

                                    item.map(|i| i.with_untracked(|t| clone_transform_id(t)))
                                })
                            })
                        })
                    }),
                    DiscoveryAssistantModel::Pills(_) => None,
                })
            }
            ContainerModel::Grid(_) => None,
            ContainerModel::DiscoveryAssistantHeader(_) => None,
            ContainerModel::Onboarding(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::ScheduleCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
            ContainerModel::SpecialCollectionsCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    items.get(focus_pos.item).map(|t| clone_transform_id(t))
                })
            }),
        });

        let item_id = item_id.unwrap_or_else(|| {
            log::warn!(
                "[get_container_and_item_ids] cannot find item at {}",
                focus_pos.item
            );
            // intentionally return "unknown" to not match any id for focus restoration
            "unknown".to_string()
        });

        (container.id.clone(), item_id)
    })
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::test_assets::mocks::SuccessCallbackTestSetup;
    use crate::{
        network::parser::collections_response_parser,
        page::{
            helpers_sig::create_default_collections_page_model,
            helpers_sig::get_initial_load_success_cb,
        },
    };
    use collection_types::network_types::CollectionsPage;
    use ignx_compositron::prelude::AppContext;
    use ignx_compositron::{app::launch_only_app_context, reactive::create_rw_signal};
    use network::common::{DeviceProxyResponse, LRCEdgeResponse, PageRequestStatus};
    use rust_features::{provide_context_test_rust_features, MockRustFeaturesBuilder};
    use std::rc::Rc;

    fn get_mock_container_list_from_page_response(
        ctx: AppContext,
        response: LRCEdgeResponse<CollectionsPage>,
    ) -> ReadSignal<Vec<ContainerType>> {
        let scope = ctx.scope();
        let mock_features = MockRustFeaturesBuilder::new()
            .set_is_promo_banner_enabled(true)
            .build();
        provide_context(scope, mock_features);
        provide_context_test_rust_features(scope);

        let page_data_signal = create_default_collections_page_model(scope);
        let load_status_signal =
            create_rw_signal(scope, PageRequestStatus::Success("home-home".to_string()));

        let setup = SuccessCallbackTestSetup::new(ctx)
            .with_page_data_and_status(page_data_signal, load_status_signal)
            .with_parsed_page(response.resource);

        let page_controller = setup.to_controller();

        let success_cb =
            get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

        let args = setup.to_success_cb_args();

        success_cb(
            args.parsed_page_from_network.clone(),
            args.transform_name,
            args.manipulate_page_data,
            args.timestamp,
        );
        page_data_signal.get_untracked().container_list.read_only()
    }

    fn get_mock_container_list(ctx: AppContext) -> ReadSignal<Vec<ContainerType>> {
        let page_data = collections_response_parser(
            include_str!("../test_assets/collections_page_full_response.json").to_string(),
        )
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .unwrap();

        get_mock_container_list_from_page_response(ctx, page_data)
    }

    fn get_mock_container_list_promo(ctx: AppContext) -> ReadSignal<Vec<ContainerType>> {
        let page_data = collections_response_parser(
            include_str!("../test_assets/collections_page_with_promo_banner.json").to_string(),
        )
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .unwrap();

        get_mock_container_list_from_page_response(ctx, page_data)
    }

    #[test]
    fn should_return_correct_values() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list(ctx);

            // Standard Hero
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 0, item: 0, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt67ioRob21li4Rob21ljI6mMToxMjJZTU9SUTEwNkdKUiMjTkJTWEUzMkRNRlpHNjVMVE1WV0GND46CVjI=".to_string(), "amzn1.dv.gti.64cca3fc-92d0-4f15-8a20-f2df0ea530ab".to_string()));
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 0, item: 1, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt67ioRob21li4Rob21ljI6mMToxMjJZTU9SUTEwNkdKUiMjTkJTWEUzMkRNRlpHNjVMVE1WV0GND46CVjI=".to_string(), "legacyDetail:detail:amzn1.dv.gti.af316d6d-1270-4dba-882f-33fc6a17ea4f".to_string()));
            // Short Carousel
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 1, item: 1, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7mio6qc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNzaG9ydGNhcm91c2Vsi4R0ZXN0jI6qMToxMjJKNUJNQUlIUjlYNCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), "landing:genre:av_genre_action".to_string()));
            // Super Carousel
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 2, item: 1, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxU09NMURCSzhDTUJBIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), "amzn1.dv.gti.5c2c1110-ccb6-4922-9690-1193f46fa535".to_string()));
            // Standard Carousel
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 3, item: 1, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(), "amzn1.dv.gti.4613fb5b-4e16-428d-a892-24d67b81a859".to_string()));
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 4, item: 2, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7oio6sc3RvcmVmcm9udHRlc3RzdWl0ZXdpZGdldHR5cGVzbGl2ZWxpbmVhcmNhcmSLhHRlc3SMjqoxOjExMUFFTjhNWTROVThYIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), "amzn1.dv.gti.c6a261fd-19a7-4846-8e28-d309763b62ab".to_string()));
            // Charts Carousel
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 5, item: 0, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7fio6jc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNjaGFydHOLhHRlc3SMjqoxOjEzT1ZKNkUyNkJJVzQxIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), "amzn1.dv.gti.5c2c1110-ccb6-4922-9690-1193f46fa535".to_string()));
            // Unentitled Nodes without leading button
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 6, item: 0, sub_index: None }), ("V2=4AEA6_unodes_Ye-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7eio6ic3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNub2Rlc4uEdGVzdIyOqjE6MTM3V1I4S1FTWjRXSVcjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==".to_string(), "legacyDetail:detail:amzn1.dv.gti.b27a1f8b-00d5-420f-83c4-868fd6de272c".to_string()));
            // Entitled Nodes with leading button
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 7, item: 0, sub_index: None }), ("V2=X_enodes_Xu69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyT0NaNzhLUjI0VTZGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), "V2=X_enodes_Xu69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyT0NaNzhLUjI0VTZGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=-SeeMore-Some(Start)".to_string()));
            // Entitled Nodes with leading button
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 7, item: 1, sub_index: None }), ("V2=X_enodes_Xu69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyT0NaNzhLUjI0VTZGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), "landing:subscription:101filmsuk".to_string()));
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 9, item: 1, sub_index: Some(0) }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSNkJEMzM0ODNDQTk4IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=".to_string(), "landing:home:assistant-eyJ0eXBlIjoiZmlsdGVyIiwibmF2Ijp0cnVlLCJzZWMiOiJjZW50ZXIiLCJmaWx0ZXIiOnsiYXNzaXN0YW50U3RhdGUiOlsiMjY1OF9kZW1vLTIwMjQwOTI3XzAxNWY2MTE3LWE3Y2YtNGZlOC1iMzYzLWFmZmFjM2RhM2QxMiJdfSwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoiS0x0cmhHc1V1dm5HemRmdWJSQ20tZjZzNUVrY1BSX2hlVVdLZGVlQ0xUaXhrZm9EeVp5WERnPT06MTcyODkxNTg3NjAwMCIsIm9yZXFrIjoicDNYVFIxcGVvSTVjMlVqZWEza0pxSWp5ZDBjRTlyQ3ZrVXZqbUpUc25FQT0iLCJvcmVxa3YiOjF9".to_string()));
        });
    }

    #[test]
    fn should_return_correct_id_for_promo_banner() {
        launch_only_app_context(|ctx| {
            let container_list = get_mock_container_list_promo(ctx);

            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 3, item: 0, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt65ioRob21li4J0doyOpjE6MTJFQ1IyRzRXWkpPUk8jI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy".to_string(), "amzn1.dv.gti.cfc6048f-1dfc-4453-acc9-cba5e3b59f3f".to_string()));
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 4, item: 0, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt65ioRob21li4J0doyOpjE6MTJFQ1IyRzRXWkpPUk8jI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy".to_string(), "amzn1.dv.gti.e3d03023-4e3b-45f1-9988-0c404a6eb3db".to_string()));
            assert_eq!(get_container_and_item_ids(container_list, &FocusPosition { container: 5, item: 0, sub_index: None }), ("V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt65ioRob21li4J0doyOpjE6MTJFQ1IyRzRXWkpPUk8jI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy".to_string(), "amzn1.dv.gti.627b2aae-8672-4494-a678-baf90046e136".to_string()));
        });
    }
}
