use crate::{
    ui::types::{ContainerType, SeamlessPlaybackContext},
    utils::common_string_ids::AV_LRC_ADDED_TO_FAVORITES_OUTSIDE_LIVE_PAGE,
};
use auth::is_last_known_state_signed_in;
use common_transform_types::actions::TransitionAction;
use container_types::ui_signals::{
    ChartsCarouselItemType, ContainerModel, NodesCarouselModel, ScheduleCardItemType,
    ScheduleCarouselUIItem, SpecialCollectionsCarouselItemType, StandardCardContainerItemType,
    SuperCarouselItemType,
};
use contextual_menu_types::prelude::*;
use explicit_signals_service::prelude::ExplicitSignalsServiceEntityType;
use hero::buttons::ButtonVariant;
use ignx_compositron::prelude::*;
use location::RustPage;
use mockall_double::double;
use navigation_menu::utils::{get_page_params, PageParams};
use router::hooks::use_location;
#[double]
use rust_features::RustFeatures;
use watch_modal::helpers::hero_data::create_hero_ssm_data;

#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;

fn create_hide_variant_button(
    scope: Scope,
    metadata: &ContextualMenuMetadata,
    more_details_ref_marker: Option<&String>,
    container_supports_hiding: bool,
    container_name: Option<String>,
    container_id: Option<String>,
) -> Option<ContextualMenuButton> {
    let is_hide_this_remove_from_cw_enabled = {
        use_context::<RustFeatures>(scope)
            .is_some_and(|features| features.is_hide_this_remove_from_cw_enabled())
    };
    if !is_hide_this_remove_from_cw_enabled {
        return None;
    }

    if !is_last_known_state_signed_in(scope) {
        return None;
    }

    if !container_supports_hiding {
        return None;
    }

    // Only those with a GTI can be hidden
    let Some(gti) = metadata.gti.clone() else {
        return None;
    };

    let Some(PageParams::Collections(params)) =
        use_location(scope).with_untracked(|loc| get_page_params(loc, scope))
    else {
        return None;
    };
    if params.page_id.eq("MyStuff") {
        return None;
    }

    let content_type = metadata.content_type.clone().unwrap_or_default();

    // The logic to figure out,  whether the item is in a "Continue Watching", is by checking
    // for the existence of a target = "removeFromContinueWatching" action in the contextualActions.
    let continue_watching_action = metadata
        .contextual_actions
        .iter()
        .flatten()
        .find(|action| action.target.eq("removeFromContinueWatching"));

    if let Some(action) = continue_watching_action {
        let Some(container_name) = container_name else {
            log::error!("[Contextual Menu] Container has no container name and RemoveFromCW button could not be created");
            return None;
        };
        let Some(container_id) = container_id else {
            log::error!("[Contextual Menu] Container has no container id and RemoveFromCW button could not be created");
            return None;
        };
        Some(ContextualMenuButton::RemoveFromCW(Box::new(
            RemoveFromCWButtonData {
                id: "RemoveFromCW".to_string(),
                gti,
                content_type,
                container_id,
                ref_marker: Some(action.refMarker.clone()),
                is_removed: false,
                container_name,
            },
        )))
    } else {
        // RemoveFromCW supports all content types, meanwhile IFS only supports certain content types
        static HIDEABLE_CONTENT_TYPES: &[&str] = &["MOVIE", "SEASON", "EVENT"];

        if !HIDEABLE_CONTENT_TYPES.contains(&content_type.as_str()) {
            return None;
        }
        Some(ContextualMenuButton::HideThis(Box::new(
            HideThisButtonData {
                id: "HideThis".into(),
                gti,
                content_type,
                ref_marker: more_details_ref_marker.cloned(),
                is_content_hidden: false,
            },
        )))
    }
}

pub struct CreateContextualMenuOptions {
    /// Depending on the button variant for Hero like containers, we will choose the primary
    /// action or secondary action as the transition action for "More Details". By providing None
    /// the logic will take the primary action
    pub button: Option<ButtonVariant>,

    /// The container title that can be used for buttons such as "Remove from {containerName}" button
    pub container_title: Option<String>,

    /// The container's ID, which would be useful for "Remove from Continue Watching" to target
    /// that specific container
    pub container_id: Option<String>,

    /// If the container does not support hiding, then no "Hide" variant button will be surfaced
    pub container_supports_hiding: bool,
}

impl Default for CreateContextualMenuOptions {
    fn default() -> Self {
        Self {
            button: None,
            container_title: None,
            container_id: None,
            container_supports_hiding: false,
        }
    }
}

pub fn create_contextual_menu_data(
    metadata: ContextualMenuMetadata,
    ctx: &AppContext,
    options: CreateContextualMenuOptions,
) -> Option<ContextualMenuData> {
    let CreateContextualMenuOptions {
        button,
        container_supports_hiding,
        container_title,
        container_id,
    } = options;
    if let Some(ButtonVariant::Primary) = button {
        // If transition action is of type openModal, open SSM variant of Watch Modal. Note
        // that this action is only returned for the HERO actions view.
        if let Some(TransitionAction::openModal(action)) = &metadata.primary_action {
            return Some(create_hero_ssm_data(
                metadata.to_owned(),
                action.to_owned(),
                RustPage::RUST_COLLECTIONS,
            ));
        };
    }

    let is_signed_in = is_last_known_state_signed_in(ctx.scope());

    let mut buttons = vec![];

    let action = match button {
        None => metadata.primary_action.clone(),
        Some(button) => match button {
            // Several primary button actions are TAPS transitions. However, in React
            // Re-Master page the "More Details" button always goes to the details page
            // (i.e. secondary button action). The following logic makes it so that we try
            // to mirror that behaviour. If both primary and secondary actions exist then
            // we prefer the secondary action. If there is only a primary action and no
            // secondary then we keep the primary action anyway. If in the future we decide
            // that we want to propagate the corresponding action of the button instead we
            // can do that easily here.
            ButtonVariant::Primary => {
                if metadata.secondary_action.is_some() {
                    // secondary action data available, return that
                    metadata.secondary_action.clone()
                } else {
                    // No secondary action data available, return  the primary action
                    metadata.primary_action.clone()
                }
            }
            ButtonVariant::Secondary => metadata.secondary_action.clone(),
        },
    };

    let ref_marker = action.as_ref().map(|action| action.get_ref_marker());

    if let Some(action) = action {
        buttons.push(ContextualMenuButton::MoreDetails(Box::new(
            MoreDetailsButtonData {
                id: "MoreDetails".to_string(),
                action,
                on_select_cb: None,
            },
        )));
    };

    if let Some(&is_in_watchlist) = metadata.is_in_watchlist.as_ref() {
        if is_signed_in {
            buttons.push(ContextualMenuButton::AddToWatchlist(Box::new(
                WatchlistButtonData {
                    id: "Watchlist".to_string(),
                    is_in_watchlist,
                    gti: metadata.gti.clone().unwrap_or_default(),
                    ref_marker: ref_marker.clone(),
                    on_select_cb: None,
                },
            )));
        }
    }

    if let Some(hide_button) = create_hide_variant_button(
        ctx.scope(),
        &metadata,
        ref_marker.as_ref(),
        container_supports_hiding,
        container_title,
        container_id,
    ) {
        buttons.push(hide_button);
    }

    Some(ContextualMenuData::ContextualMenu(
        ContextualMenuProperties {
            title: metadata.title.unwrap_or_default(),
            gti: metadata.gti.unwrap_or_default(),
            metrics_context: ContextualMenuMetricsContext {
                page_type: RustPage::RUST_COLLECTIONS,
                is_linear_title: metadata.is_linear_title,
            },
            ref_marker,
            maturity_rating_image: metadata.maturity_rating_image,
            maturity_rating_string: metadata.maturity_rating_string,
            buttons,
            title_actions: None,
            event_metadata: None,
            metadata_row: None,
            tnf_properties: None,
            liveliness_data: metadata.liveliness_data,
            on_dismiss_cb: None,
            entitlement_label_data: None,
            journey_ingress_context: None,
            show_skeleton: None,
            content_type: metadata.content_type,
        },
    ))
}

pub fn create_contextual_menu_data_for_bonus_schedule_cards(
    metadata: ContextualMenuMetadata,
) -> Option<ContextualMenuData> {
    let mut buttons = vec![];

    let action = metadata.primary_action;

    let ref_marker = action.as_ref().map(|action| action.get_ref_marker());

    if let Some(action) = action {
        match action {
            TransitionAction::playback(_) => {
                buttons.push(ContextualMenuButton::WatchNow(Box::new(
                    WatchNowButtonData {
                        id: "WatchNow".to_string(),
                        action,
                        gti: metadata.gti.clone(),
                        seamless_transition: false,
                        playback_origin: None,
                        variant: WatchNowVariants::WatchNow,
                    },
                )));
            }
            TransitionAction::signUp(_) => {
                buttons.push(ContextualMenuButton::Subscribe(Box::new(
                    SubscribeButtonData {
                        id: "Subscription".to_string(),
                        action,
                    },
                )));
            }
            _ => (),
        }
    };

    Some(ContextualMenuData::ContextualMenu(
        ContextualMenuProperties {
            title: metadata.title.unwrap_or_default(),
            gti: metadata.gti.unwrap_or_default(),
            metrics_context: ContextualMenuMetricsContext {
                page_type: RustPage::RUST_COLLECTIONS,
                is_linear_title: metadata.is_linear_title,
            },
            ref_marker,
            maturity_rating_image: metadata.maturity_rating_image,
            maturity_rating_string: metadata.maturity_rating_string,
            buttons,
            title_actions: None,
            event_metadata: None,
            metadata_row: None,
            tnf_properties: None,
            liveliness_data: metadata.liveliness_data,
            on_dismiss_cb: None,
            entitlement_label_data: None,
            journey_ingress_context: None,
            show_skeleton: None,
            content_type: metadata.content_type,
        },
    ))
}

fn get_button_for_live_linear_card_menu(
    action: Option<TransitionAction>,
    seamless_context: &SeamlessPlaybackContext,
    metadata: &ContextualMenuMetadata,
) -> Option<ContextualMenuButton> {
    if let Some(action) = action {
        match action {
            TransitionAction::playback(_)
            | TransitionAction::player(_)
            | TransitionAction::consent(_) => {
                let to_playback = !matches!(action, TransitionAction::consent(_));
                let SeamlessPlaybackContext {
                    enabled: seamless_enabled,
                    origin,
                } = seamless_context;

                return Some(ContextualMenuButton::WatchNow(Box::new(
                    WatchNowButtonData {
                        id: "WatchNow".to_string(),
                        action,
                        gti: metadata.gti.clone(),
                        seamless_transition: *seamless_enabled && to_playback,
                        playback_origin: to_playback.then_some(*origin).flatten(),
                        variant: WatchNowVariants::WatchNow,
                    },
                )));
            }
            TransitionAction::signUp(_) => {
                return Some(ContextualMenuButton::Subscribe(Box::new(
                    SubscribeButtonData {
                        id: "Subscription".to_string(),
                        action,
                    },
                )));
            }
            _ => {
                return Some(ContextualMenuButton::MoreDetails(Box::new(
                    MoreDetailsButtonData {
                        id: "MoreDetails".to_string(),
                        action,
                        on_select_cb: None,
                    },
                )));
            }
        }
    }

    None
}

pub fn create_contextual_menu_data_for_live_linear_cards(
    scope: Scope,
    metadata: ContextualMenuMetadata,
    seamless_context: SeamlessPlaybackContext,
    container_supports_hiding: bool,
    container_title: Option<String>,
    container_id: Option<String>,
) -> Option<ContextualMenuData> {
    let is_signed_in = is_last_known_state_signed_in(scope);

    let action = metadata.primary_action.clone();
    let secondary_action = metadata.secondary_action.clone();

    let ref_marker = action.as_ref().map(|action| action.get_ref_marker());

    let mut buttons = Vec::new();

    if let Some(primary_button) =
        get_button_for_live_linear_card_menu(action, &seamless_context, &metadata)
    {
        buttons.push(primary_button);
    }

    if try_use_rust_features(scope).is_some_and(|v| v.is_linear_favorite_stations_enabled()) {
        if is_signed_in {
            buttons.push(ContextualMenuButton::ToggleFavorite(Box::new(
                FavoriteButtonData {
                    id: "FavoriteLinearStation".to_string(),
                    gti: metadata.gti.clone().unwrap_or_default(),
                    ref_marker: ref_marker.clone(),
                    is_favorited: metadata.is_favorited.unwrap_or_default(),
                    entity_type: ExplicitSignalsServiceEntityType::Station,
                    title: metadata.title.clone().unwrap_or_default(),
                    favorited_success_toast_message_localized_str_id:
                        AV_LRC_ADDED_TO_FAVORITES_OUTSIDE_LIVE_PAGE.to_string(),
                },
            )))
        }
    }

    if let Some(secondary_button) =
        get_button_for_live_linear_card_menu(secondary_action, &seamless_context, &metadata)
    {
        // Check if buttons already contains a button with the same id
        let secondary_id = secondary_button.get_id();
        let has_matching_id = buttons.iter().any(|b| b.get_id() == secondary_id);

        if !has_matching_id {
            buttons.push(secondary_button);
        }
    };

    if let Some(hide_button) = create_hide_variant_button(
        scope,
        &metadata,
        ref_marker.as_ref(),
        container_supports_hiding,
        container_title,
        container_id,
    ) {
        buttons.push(hide_button);
    }

    Some(ContextualMenuData::ContextualMenu(
        ContextualMenuProperties {
            title: metadata.title.unwrap_or_default(),
            gti: metadata.gti.unwrap_or_default(),
            metrics_context: ContextualMenuMetricsContext {
                page_type: RustPage::RUST_COLLECTIONS,
                is_linear_title: metadata.is_linear_title,
            },
            ref_marker,
            maturity_rating_image: metadata.maturity_rating_image,
            maturity_rating_string: metadata.maturity_rating_string,
            buttons,
            title_actions: None,
            event_metadata: None,
            metadata_row: None,
            tnf_properties: None,
            liveliness_data: metadata.liveliness_data,
            on_dismiss_cb: None,
            entitlement_label_data: None,
            journey_ingress_context: None,
            show_skeleton: None,
            content_type: metadata.content_type,
        },
    ))
}

fn update_cm_metadata_if_gti_matches(
    metadata_signal: RwSignal<ContextualMenuMetadata>,
    gti: &Option<String>,
    value: bool,
    variant: MenuConfigurableItemVariant,
) {
    let should_update = metadata_signal.with_untracked(|metadata| metadata.gti == *gti);

    if !should_update {
        return;
    }

    match variant {
        MenuConfigurableItemVariant::Watchlist => metadata_signal.update(|cm_data| {
            cm_data.is_in_watchlist = Some(value);
        }),
        MenuConfigurableItemVariant::Favorite => metadata_signal.update(|cm_data| {
            cm_data.is_favorited = Some(value);
        }),
    }
}

#[derive(Copy, Clone)]
pub enum MenuConfigurableItemVariant {
    Watchlist,
    Favorite,
}

pub fn update_cm_item_state(
    containers: &Vec<ContainerType>,
    value: bool,
    gti: Option<String>,
    variant: MenuConfigurableItemVariant,
) {
    for container in containers {
        container.model.with_untracked(|model| match model {
            ContainerModel::StandardCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        item.model.with_untracked(|model| match model {
                            StandardCardContainerItemType::Series(model)
                            | StandardCardContainerItemType::Show(model)
                            | StandardCardContainerItemType::Movie(model)
                            | StandardCardContainerItemType::Event(model)
                            | StandardCardContainerItemType::GenericTitleCard(model)
                            | StandardCardContainerItemType::LiveLinear(model)
                            | StandardCardContainerItemType::Link(model)
                            | StandardCardContainerItemType::BonusSchedule(model)
                            | StandardCardContainerItemType::VodExtraContent(model)
                            | StandardCardContainerItemType::SeeMoreLink(model)
                            | StandardCardContainerItemType::Hint(model) => {
                                model.with_untracked(|model| {
                                    update_cm_metadata_if_gti_matches(
                                        model.contextual_menu_metadata,
                                        &gti,
                                        value,
                                        variant,
                                    )
                                });
                            }
                        })
                    }
                })
            }),
            ContainerModel::SuperCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        item.model.with_untracked(|model| match model {
                            SuperCarouselItemType::Series(model)
                            | SuperCarouselItemType::Show(model)
                            | SuperCarouselItemType::Movie(model)
                            | SuperCarouselItemType::Event(model)
                            | SuperCarouselItemType::GenericTitleCard(model)
                            | SuperCarouselItemType::Link(model) => {
                                model.with_untracked(|model| {
                                    update_cm_metadata_if_gti_matches(
                                        model.contextual_menu_metadata,
                                        &gti,
                                        value,
                                        variant,
                                    )
                                });
                            }
                        })
                    }
                })
            }),
            ContainerModel::ChartsCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        item.model.with_untracked(|model| match model {
                            ChartsCarouselItemType::Series(model)
                            | ChartsCarouselItemType::Show(model)
                            | ChartsCarouselItemType::Movie(model)
                            | ChartsCarouselItemType::Event(model)
                            | ChartsCarouselItemType::GenericTitleCard(model) => {
                                model.with_untracked(|model| {
                                    update_cm_metadata_if_gti_matches(
                                        model.contextual_menu_metadata,
                                        &gti,
                                        value,
                                        variant,
                                    )
                                });
                            }
                        })
                    }
                })
            }),
            ContainerModel::NodesCarousel(model) => model.with_untracked(|model| match model {
                NodesCarouselModel::EntitledOrAgnostic(model) => {
                    model.with_untracked(|model| {
                        model.items.with_untracked(|items| {
                            for item in items {
                                item.carousel_card_data.with_untracked(|model| {
                                    update_cm_metadata_if_gti_matches(
                                        model.contextual_menu_metadata,
                                        &gti,
                                        value,
                                        variant,
                                    )
                                })
                            }
                        })
                    });
                }
                NodesCarouselModel::Unentitled(model) => model.with_untracked(|model| {
                    model.items.with_untracked(|items| {
                        for item in items {
                            item.carousel_card_data.with_untracked(|model| {
                                update_cm_metadata_if_gti_matches(
                                    model.contextual_menu_metadata,
                                    &gti,
                                    value,
                                    variant,
                                )
                            })
                        }
                    })
                }),
            }),
            ContainerModel::ShortCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        item.carousel_card_data.with_untracked(|model| {
                            update_cm_metadata_if_gti_matches(
                                model.contextual_menu_metadata,
                                &gti,
                                value,
                                variant,
                            )
                        })
                    }
                })
            }),
            ContainerModel::StandardHero(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        update_cm_metadata_if_gti_matches(
                            item.contextual_menu_metadata,
                            &gti,
                            value,
                            variant,
                        )
                    }
                })
            }),
            ContainerModel::PromoBanner(model) => model.with_untracked(|model| {
                model.item.with_untracked(|items| {
                    for item in items {
                        update_cm_metadata_if_gti_matches(
                            item.contextual_menu_metadata,
                            &gti,
                            value,
                            variant,
                        )
                    }
                })
            }),
            ContainerModel::TentpoleHero(model) => model.with_untracked(|model| {
                model.item.with_untracked(|items| {
                    for item in items {
                        update_cm_metadata_if_gti_matches(
                            item.contextual_menu_metadata,
                            &gti,
                            value,
                            variant,
                        )
                    }
                })
            }),
            ContainerModel::BeardSupportedCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        item.model.with_untracked(|model| match model {
                            StandardCardContainerItemType::Series(model)
                            | StandardCardContainerItemType::Show(model)
                            | StandardCardContainerItemType::Movie(model)
                            | StandardCardContainerItemType::Event(model)
                            | StandardCardContainerItemType::GenericTitleCard(model)
                            | StandardCardContainerItemType::LiveLinear(model)
                            | StandardCardContainerItemType::Link(model)
                            | StandardCardContainerItemType::BonusSchedule(model)
                            | StandardCardContainerItemType::VodExtraContent(model)
                            | StandardCardContainerItemType::SeeMoreLink(model)
                            | StandardCardContainerItemType::Hint(model) => {
                                model.with_untracked(|model| {
                                    update_cm_metadata_if_gti_matches(
                                        model.contextual_menu_metadata,
                                        &gti,
                                        value,
                                        variant,
                                    )
                                });
                            }
                        })
                    }
                })
            }),
            ContainerModel::ScheduleCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        match item {
                            ScheduleCarouselUIItem::Card(card_item) => {
                                card_item.item.with_untracked(|card_type| match card_type {
                                    ScheduleCardItemType::Event(model)
                                    | ScheduleCardItemType::OffPlatform(model) => model
                                        .with_untracked(|model| {
                                            update_cm_metadata_if_gti_matches(
                                                model.contextual_menu_metadata,
                                                &gti,
                                                value,
                                                variant,
                                            )
                                        }),
                                })
                            }
                            ScheduleCarouselUIItem::Button(_) => {}
                        }
                    }
                })
            }),
            ContainerModel::SpecialCollectionsCarousel(model) => model.with_untracked(|model| {
                model.items.with_untracked(|items| {
                    for item in items {
                        if let SpecialCollectionsCarouselItemType::SpecialCollectionsTile(item) =
                            &item.model
                        {
                            update_cm_metadata_if_gti_matches(
                                item.with_untracked(|item| item.contextual_menu_metadata),
                                &gti,
                                value,
                                variant,
                            )
                        }
                    }
                })
            }),
            ContainerModel::DiscoveryAssistant(_) => {
                // the discovery assistant does not contain title cards
            }
            ContainerModel::DiscoveryAssistantHeader(_) => {
                // the discovery assistant header does not contain title cards
            }
            ContainerModel::Grid(_) => {
                // Implement watchlist for grid
            }
            ContainerModel::Onboarding(_) => {
                // watchlist not accessible from onboarding container
            }
            ContainerModel::EntityCarousel(_) => {
                // watchlist not accessible from Entity container
            }
        });
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use auth::{AuthContext, MockAuth};
    use common_transform_types::{
        actions::TransitionAction, playback_metadata::TitleActionMetadataType,
    };
    use container_types::container_item_parsing::{
        create_consent_action, create_playback_action, create_signup_action, create_swift_action,
        create_title_playback_action,
    };
    use ignx_compositron::app::launch_only_app_context;
    use playback_navigation::types::PlaybackOrigin;
    use rstest::*;
    use std::rc::Rc;

    const PRIMARY_REF_MARKER: &str = "primary_ref_marker";
    const SECONDARY_REF_MARKER: &str = "secondary_ref_marker";

    #[fixture]
    fn primary_action_data() -> Option<TransitionAction> {
        let mut action = create_swift_action();
        action.refMarker = PRIMARY_REF_MARKER.to_string();
        Some(TransitionAction::detail(action))
    }

    #[fixture]
    fn secondary_action_data() -> Option<TransitionAction> {
        let mut action = create_swift_action();
        action.refMarker = SECONDARY_REF_MARKER.to_string();
        Some(TransitionAction::detail(action))
    }

    type Actions = (Option<TransitionAction>, Option<TransitionAction>);

    #[fixture]
    fn none_action_data() -> Actions {
        (None, None)
    }

    #[fixture]
    fn single_action_data() -> Actions {
        (primary_action_data(), None)
    }

    #[fixture]
    fn hero_action_data() -> Actions {
        (primary_action_data(), secondary_action_data())
    }

    #[fixture]
    fn playback_transition_action() -> Option<TransitionAction> {
        let mut action =
            create_title_playback_action(false, false, TitleActionMetadataType::Playback);
        action.refMarker = PRIMARY_REF_MARKER.to_string();
        Some(TransitionAction::playback(action))
    }

    #[fixture]
    fn player_transition_action() -> Option<TransitionAction> {
        let mut action = create_playback_action();
        action.refMarker = PRIMARY_REF_MARKER.to_string();
        Some(TransitionAction::player(action))
    }

    #[fixture]
    fn consent_transition_action() -> Option<TransitionAction> {
        let mut action = create_consent_action();
        action.refMarker = PRIMARY_REF_MARKER.to_string();
        Some(TransitionAction::consent(action))
    }

    #[fixture]
    fn signup_action() -> Option<TransitionAction> {
        let mut action = create_signup_action();
        action.refMarker = PRIMARY_REF_MARKER.to_string();
        Some(TransitionAction::signUp(action))
    }

    fn station_detail_secondary_action(page_type: &str) -> Option<TransitionAction> {
        let mut action = create_swift_action();
        action.pageType = page_type.to_string();
        Some(TransitionAction::detail(action))
    }

    #[rstest]
    #[case(None, none_action_data())]
    #[case(Some(ButtonVariant::Primary), none_action_data())]
    #[case(Some(ButtonVariant::Secondary), none_action_data())]
    fn should_not_add_more_details_button_if_action_is_none(
        #[case] button: Option<ButtonVariant>,
        #[case] actions: Actions,
    ) {
        launch_only_app_context(move |ctx| {
            let (primary_action, secondary_action) = actions;
            let metadata = ContextualMenuMetadata {
                primary_action,
                secondary_action,
                ..Default::default()
            };
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();
            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.is_empty())
            );
        });
    }

    #[rstest]
    #[case(Some(ButtonVariant::Primary), hero_action_data())]
    #[case(Some(ButtonVariant::Secondary), hero_action_data())]
    #[case(None, single_action_data())]
    fn should_add_more_details_button_if_action_is_some(
        #[case] button: Option<ButtonVariant>,
        #[case] actions: Actions,
    ) {
        launch_only_app_context(move |ctx| {
            let (primary_action, secondary_action) = actions;
            let metadata = ContextualMenuMetadata {
                primary_action,
                secondary_action,
                ..Default::default()
            };
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();
            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::MoreDetails(_)))
            );
        });
    }

    #[rstest]
    #[case(Some(ButtonVariant::Primary), hero_action_data(), SECONDARY_REF_MARKER)]
    #[case(
        Some(ButtonVariant::Secondary),
        hero_action_data(),
        SECONDARY_REF_MARKER
    )]
    #[case(None, single_action_data(), PRIMARY_REF_MARKER)]
    fn should_forward_ref_marker(
        #[case] button: Option<ButtonVariant>,
        #[case] actions: Actions,
        #[case] expected_ref_marker: &'static str,
    ) {
        launch_only_app_context(move |ctx| {
            let (primary_action, secondary_action) = actions;
            let metadata = ContextualMenuMetadata {
                primary_action,
                secondary_action,
                ..Default::default()
            };
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();
            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.ref_marker == Some(expected_ref_marker.to_string()))
            );
        });
    }

    #[rstest]
    #[case(None)]
    #[case(Some(ButtonVariant::Primary))]
    #[case(Some(ButtonVariant::Secondary))]
    fn should_default_to_empty_title_if_title_is_none(#[case] button: Option<ButtonVariant>) {
        launch_only_app_context(move |ctx| {
            let metadata = ContextualMenuMetadata::default();
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();
            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.title == *"")
            );
        });
    }

    #[rstest]
    #[case(None)]
    #[case(Some(ButtonVariant::Primary))]
    #[case(Some(ButtonVariant::Secondary))]
    fn should_default_to_empty_gti_if_gti_is_none(#[case] button: Option<ButtonVariant>) {
        launch_only_app_context(move |ctx| {
            let metadata = ContextualMenuMetadata::default();
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };

            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();
            assert!(matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.gti == *""));
        });
    }

    #[rstest]
    #[case(None)]
    #[case(Some(ButtonVariant::Primary))]
    #[case(Some(ButtonVariant::Secondary))]
    fn should_create_correct_contextual_menu_metrics(#[case] button: Option<ButtonVariant>) {
        launch_only_app_context(move |ctx| {
            let metadata = ContextualMenuMetadata {
                is_linear_title: true,
                ..Default::default()
            };
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();
            let metrics_ctx = match cm_data {
                ContextualMenuData::ContextualMenu(data) => data.metrics_context,
                _ => panic!("unexpected ContextualMenu type"),
            };
            assert!(metrics_ctx.is_linear_title);
            assert_eq!(metrics_ctx.page_type, RustPage::RUST_COLLECTIONS);
        });
    }

    #[rstest]
    #[case(None)]
    #[case(Some(ButtonVariant::Primary))]
    #[case(Some(ButtonVariant::Secondary))]
    fn should_not_add_add_to_watchlist_button_if_is_in_watchlist_is_none(
        #[case] button: Option<ButtonVariant>,
    ) {
        launch_only_app_context(move |ctx| {
            let metadata = ContextualMenuMetadata::default();
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();
            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.is_empty())
            );
        });
    }

    #[rstest]
    #[case(None)]
    #[case(Some(ButtonVariant::Primary))]
    #[case(Some(ButtonVariant::Secondary))]
    fn should_not_add_add_to_watchlist_button_if_customer_is_not_signed_in(
        #[case] button: Option<ButtonVariant>,
    ) {
        launch_only_app_context(move |ctx| {
            let mock_auth = MockAuth::new_without_params(ctx.scope());
            provide_context::<AuthContext>(ctx.scope(), Rc::new(mock_auth));

            let metadata = ContextualMenuMetadata::default();
            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();

            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.is_empty())
            );
        });
    }

    #[rstest]
    #[case(None)]
    #[case(Some(ButtonVariant::Primary))]
    #[case(Some(ButtonVariant::Secondary))]
    fn should_add_add_to_watchlist_button_if_customer_is_signed_in(
        #[case] button: Option<ButtonVariant>,
    ) {
        launch_only_app_context(move |ctx| {
            let mut mock_auth = MockAuth::new_without_params(ctx.scope());
            mock_auth.set_access_token(Some("mock-token".to_string()));

            provide_context::<AuthContext>(ctx.scope(), Rc::new(mock_auth));

            let metadata = ContextualMenuMetadata {
                is_in_watchlist: Some(false),
                ..Default::default()
            };

            let options = CreateContextualMenuOptions {
                button,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data(metadata, &ctx, options).unwrap();

            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::AddToWatchlist(_)))
            );
        });
    }

    mod bonus_schedule_card {
        use super::*;

        #[test]
        fn should_add_watch_now_button_if_action_is_playback_transition() {
            let primary_action = playback_transition_action();
            let metadata = ContextualMenuMetadata {
                primary_action,
                secondary_action: None,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data_for_bonus_schedule_cards(metadata).unwrap();
            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::WatchNow(_)))
            );
        }

        #[test]
        fn should_add_subscribe_button_if_action_is_signup() {
            let primary_action = signup_action();
            let metadata = ContextualMenuMetadata {
                primary_action,
                secondary_action: None,
                ..Default::default()
            };
            let cm_data = create_contextual_menu_data_for_bonus_schedule_cards(metadata).unwrap();
            assert!(
                matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::Subscribe(_)))
            );
        }
    }

    mod live_linear_card {
        use super::*;
        use ignx_compositron::app::launch_only_scope;
        use rust_features::MockRustFeaturesBuilder;

        #[rstest]
        #[case(playback_transition_action())]
        #[case(player_transition_action())]
        #[case(consent_transition_action())]
        fn should_add_watch_now_button_based_on_action_type(
            #[case] primary_action: Option<TransitionAction>,
        ) {
            launch_only_scope(|scope| {
                let metadata = ContextualMenuMetadata {
                    primary_action,
                    secondary_action: None,
                    ..Default::default()
                };
                let cm_data = create_contextual_menu_data_for_live_linear_cards(
                    scope,
                    metadata,
                    SeamlessPlaybackContext::default(),
                    true,
                    None,
                    None,
                )
                .unwrap();
                assert!(
                    matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::WatchNow(_)))
                );
            });
        }

        #[rstest]
        #[case::autoplay_enabled_playback(
            playback_transition_action(),
            true,
            true,
            Some(PlaybackOrigin::LinearCarouselAutoplay)
        )]
        #[case::autoplay_disabled_player(
            player_transition_action(),
            false,
            false,
            Some(PlaybackOrigin::LinearCarouselAutoplay)
        )]
        #[case::autoplay_enabled_consent(consent_transition_action(), true, false, None)]
        fn should_choose_correct_seamless_param_for_watch_now_button(
            #[case] primary_action: Option<TransitionAction>,
            #[case] autoplay_enabled: bool,
            #[case] expected: bool,
            #[case] origin: Option<PlaybackOrigin>,
        ) {
            launch_only_scope(move |scope| {
                let metadata = ContextualMenuMetadata {
                    primary_action,
                    secondary_action: None,
                    ..Default::default()
                };
                let seamless_context = SeamlessPlaybackContext {
                    enabled: autoplay_enabled,
                    origin: Some(PlaybackOrigin::LinearCarouselAutoplay),
                };

                let cm_data = create_contextual_menu_data_for_live_linear_cards(
                    scope,
                    metadata,
                    seamless_context,
                    true,
                    None,
                    None,
                )
                .unwrap();
                let ContextualMenuData::ContextualMenu(data) = cm_data else {
                    panic!("Data should be ContextualMenuProperties")
                };

                let ContextualMenuButton::WatchNow(button_data) = &data.buttons[0] else {
                    panic!("Data should be WatchNowButtonData")
                };

                assert_eq!(button_data.seamless_transition, expected);
                assert_eq!(button_data.playback_origin, origin);
            });
        }

        #[test]
        fn should_add_subscribe_button_if_action_is_signup() {
            launch_only_scope(|scope| {
                let primary_action = signup_action();
                let metadata = ContextualMenuMetadata {
                    primary_action,
                    secondary_action: None,
                    ..Default::default()
                };
                let cm_data = create_contextual_menu_data_for_live_linear_cards(
                    scope,
                    metadata,
                    SeamlessPlaybackContext::default(),
                    true,
                    None,
                    None,
                )
                .unwrap();
                assert!(
                    matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::Subscribe(_)))
                );
            });
        }

        #[test]
        fn should_add_more_details_button_for_other_action_types() {
            launch_only_scope(|scope| {
                let primary_action = primary_action_data();
                let metadata = ContextualMenuMetadata {
                    primary_action,
                    secondary_action: None,
                    ..Default::default()
                };
                let cm_data = create_contextual_menu_data_for_live_linear_cards(
                    scope,
                    metadata,
                    SeamlessPlaybackContext::default(),
                    true,
                    None,
                    None,
                )
                .unwrap();
                assert!(
                    matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::MoreDetails(_)))
                );
            });
        }

        #[test]
        fn should_add_more_details_button_if_for_station_detail_page() {
            launch_only_scope(|scope| {
                let primary_action = playback_transition_action();
                let secondary_action = station_detail_secondary_action("linearStationDetail");

                let metadata = ContextualMenuMetadata {
                    primary_action,
                    secondary_action,
                    ..Default::default()
                };
                let cm_data = create_contextual_menu_data_for_live_linear_cards(
                    scope,
                    metadata,
                    SeamlessPlaybackContext::default(),
                    true,
                    None,
                    None,
                )
                .unwrap();
                assert!(
                    matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 2 && matches!(data.buttons[0], ContextualMenuButton::WatchNow(_)) && matches!(data.buttons[1], ContextualMenuButton::MoreDetails(_)))
                );
            });
        }

        #[test]
        fn should_not_add_duplicate_buttons() {
            launch_only_scope(|scope| {
                let primary_action = station_detail_secondary_action("linearStationDetail");
                let secondary_action = station_detail_secondary_action("linearStationDetail");

                let metadata = ContextualMenuMetadata {
                    primary_action,
                    secondary_action,
                    ..Default::default()
                };
                let cm_data = create_contextual_menu_data_for_live_linear_cards(
                    scope,
                    metadata,
                    SeamlessPlaybackContext::default(),
                    true,
                    None,
                    None,
                )
                .unwrap();
                assert!(
                    matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 1 && matches!(data.buttons[0], ContextualMenuButton::MoreDetails(_)))
                );
            });
        }

        #[test]
        fn should_add_favorites_button_when_authenticated() {
            launch_only_scope(|scope| {
                let primary_action = playback_transition_action();
                let secondary_action = station_detail_secondary_action("linearStationDetail");

                MockRustFeaturesBuilder::new()
                    .set_is_linear_favorite_stations_enabled(true)
                    .build_into_context(scope);

                let mut mock_auth = MockAuth::new_without_params(scope);
                mock_auth.set_access_token(Some("DUMMY AUTH".to_string()));
                provide_context::<AuthContext>(scope, Rc::new(mock_auth));

                let metadata = ContextualMenuMetadata {
                    primary_action,
                    secondary_action,
                    ..Default::default()
                };
                let cm_data = create_contextual_menu_data_for_live_linear_cards(
                    scope,
                    metadata,
                    SeamlessPlaybackContext::default(),
                    true,
                    None,
                    None,
                )
                .unwrap();

                assert!(
                    matches!(cm_data, ContextualMenuData::ContextualMenu(data) if data.buttons.len() == 3
                    && matches!(data.buttons[0], ContextualMenuButton::WatchNow(_)) 
                    && matches!(data.buttons[1], ContextualMenuButton::ToggleFavorite(_)) 
                    && matches!(data.buttons[2], ContextualMenuButton::MoreDetails(_)))
                );
            });
        }
    }

    mod watchlist_update {
        use super::*;
        use common_transform_types::actions::{
            TitleActionSegment, TitleOpenModalAction, TitlePlaybackAction, TransitionAction,
        };
        use common_transform_types::playback_metadata::{
            PlaybackMetadata, PlaybackPosition, TitleActionMetadataType, UserEntitlementMetadata,
            UserPlaybackMetadata,
        };
        use container_types::ui_signals::{
            BeardSupportedCarouselMetadata, BeardSupportedCarouselModel, ChartCardUIData,
            ChartsCarouselCardData, ChartsCarouselItemModel, ChartsCarouselItemTypeWrapper,
            ChartsCarouselMetadata, ChartsCarouselModel, CommonCarouselCardMetadata,
            CommonCarouselMetadata, EntitledOrAgnosticNodesCarouselItemModel,
            EntitledOrAgnosticNodesCarouselModel, HeroItemModel, HeroItemUIData,
            NodesCarouselEntitledOrAgnosticCardData, NodesCarouselEntitledOrAgnosticCardUIData,
            NodesCarouselMetadata, NodesCarouselUnentitledCardData,
            NodesCarouselUnentitledCardUIData, PromoBannerButtonData, PromoBannerMetadataRows,
            PromoBannerModel, PromoBannerUIData, PromoItemModel, ShortCarouselCardData,
            ShortCarouselCardUIData, ShortCarouselItemModel, ShortCarouselModel,
            StandardCardContainerItemListType, StandardCardContainerItemModel, StandardCardData,
            StandardCardUIData, StandardCarouselMetadata, StandardCarouselModel, StandardHeroModel,
            SuperCardUIData, SuperCarouselCardData, SuperCarouselItemModel,
            SuperCarouselItemTypeWrapper, SuperCarouselMetadata, SuperCarouselModel,
            TentpoleHeroModel, UnentitledNodesCarouselItemModel, UnentitledNodesCarouselModel,
        };
        use fableous::buttons::primary_button::PrimaryButtonVariant;
        use fableous::collaged_image::ImageCollageBaseAttributes;
        use media_background::types::MediaBackgroundType;
        use title_details::components::title::TitleData;
        use title_details::types::common::{StandardTitleDetailsData, TitleDetailsMetadata};
        use uuid::Uuid;

        fn create_fake_short_carousel_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> ShortCarouselItemModel {
            let common_carousel_card_metadata = CommonCarouselCardMetadata::default();
            ShortCarouselItemModel {
                carousel_card_data: create_rw_signal(
                    scope,
                    ShortCarouselCardData {
                        metadata: create_rw_signal(scope, common_carousel_card_metadata),
                        card_ui_data: create_rw_signal(
                            scope,
                            ShortCarouselCardUIData {
                                title: create_rw_signal(scope, None),
                                card_image: create_rw_signal(scope, None),
                                text_overlay_position: create_rw_signal(scope, None),
                            },
                        ),
                        contextual_menu_metadata,
                    },
                ),
                id: "".to_string(),
                uuid: Uuid::new_v4(),
            }
        }

        fn create_fake_promo_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> PromoItemModel {
            PromoItemModel {
                uuid: Uuid::new_v4(),
                ui_data: create_rw_signal(
                    scope,
                    PromoBannerUIData {
                        title_data: create_rw_signal(
                            scope,
                            TitleData {
                                title_art_url: None,
                                provider_logo_url: None,
                                title_text: "".to_string(),
                                size: None,
                                provider_logo_size: None,
                            },
                        ),
                        synopsis: create_rw_signal(scope, None),
                        primary_button_action: create_rw_signal(scope, None),
                        secondary_button_action: create_rw_signal(scope, None),
                        metadata_rows: create_rw_signal(scope, PromoBannerMetadataRows::default()),
                        primary_button_variant: create_rw_signal(
                            scope,
                            PrimaryButtonVariant::IconSize800("".to_string()),
                        ),
                        secondary_button_variant: create_rw_signal(scope, None),
                        maturity_rating_image: create_rw_signal(scope, None),
                        entitlement_label: create_rw_signal(scope, None),
                        legal_messages: create_rw_signal(scope, vec![]),
                        maturity_rating_string: create_rw_signal(scope, None),
                        regulatory_label_string: create_rw_signal(scope, None),
                        hero_image: create_rw_signal(scope, Some("hero-image".to_string())),
                        primary_button: create_rw_signal(
                            scope,
                            PromoBannerButtonData {
                                common_carousel_card_metadata: CommonCarouselCardMetadata::default(
                                ),
                                secondary_transition_action: None,
                                contextual_menu_data: contextual_menu_metadata,
                            },
                        ),
                        is_in_watchlist: create_rw_signal(scope, Some(false)),
                    },
                ),
                metadata: CommonCarouselCardMetadata::default(),
                contextual_menu_metadata,
            }
        }

        fn create_fake_hero_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> HeroItemModel {
            HeroItemModel {
                uuid: Uuid::new_v4(),
                id: "".to_string(),
                ui_data: create_rw_signal(
                    scope,
                    HeroItemUIData {
                        title_data: create_rw_signal(
                            scope,
                            TitleData {
                                title_art_url: None,
                                provider_logo_url: None,
                                title_text: "".to_string(),
                                size: None,
                                provider_logo_size: None,
                            },
                        ),
                        synopsis: create_rw_signal(scope, None),
                        primary_button_action: create_rw_signal(scope, None),
                        secondary_button_action: create_rw_signal(scope, None),
                        metadata_rows: create_rw_signal(scope, vec![]),
                        primary_button_variant: create_rw_signal(
                            scope,
                            PrimaryButtonVariant::IconSize800("".to_string()),
                        ),
                        secondary_button_variant: create_rw_signal(scope, None),
                        maturity_rating_image: create_rw_signal(scope, None),
                        entitlement_label: create_rw_signal(scope, None),
                        legal_messages: create_rw_signal(scope, vec![]),
                        maturity_rating_string: create_rw_signal(scope, None),
                        regulatory_label_string: create_rw_signal(scope, None),
                        progress_bar_percentage: create_rw_signal(scope, None),
                    },
                ),
                gti: create_rw_signal(scope, None),
                media_background_data: create_rw_signal(scope, MediaBackgroundType::None),
                contextual_menu_metadata,
                impression_data: Default::default(),
                swift_content_type: Some("MOVIE".to_string()),
                linear_schedule_data: vec![],
            }
        }

        fn create_fake_untitled_nodes_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> UnentitledNodesCarouselItemModel {
            UnentitledNodesCarouselItemModel {
                carousel_card_data: create_rw_signal(
                    scope,
                    NodesCarouselUnentitledCardData {
                        metadata: create_rw_signal(scope, CommonCarouselCardMetadata::default()),
                        card_ui_data: create_rw_signal(
                            scope,
                            NodesCarouselUnentitledCardUIData {
                                card_image: create_rw_signal(scope, None),
                                fallback_text: create_rw_signal(scope, None),
                                title: create_rw_signal(scope, None),
                                icon_props: create_rw_signal(scope, None),
                            },
                        ),
                        contextual_menu_metadata,
                    },
                ),
                uuid: Uuid::new_v4(),
                id: "".to_string(),
            }
        }

        fn create_fake_entitled_or_agnostic_nodes_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> EntitledOrAgnosticNodesCarouselItemModel {
            EntitledOrAgnosticNodesCarouselItemModel {
                carousel_card_data: create_rw_signal(
                    scope,
                    NodesCarouselEntitledOrAgnosticCardData {
                        metadata: create_rw_signal(scope, CommonCarouselCardMetadata::default()),
                        card_ui_data: create_rw_signal(
                            scope,
                            NodesCarouselEntitledOrAgnosticCardUIData {
                                card_image: create_rw_signal(scope, None),
                                fallback_text: create_rw_signal(scope, None),
                                title: create_rw_signal(scope, None),
                            },
                        ),
                        contextual_menu_metadata,
                    },
                ),
                uuid: Uuid::new_v4(),
                id: "".to_string(),
            }
        }

        fn create_fake_charts_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> ChartsCarouselItemModel {
            ChartsCarouselItemModel {
                title_details_data: create_rw_signal(
                    scope,
                    StandardTitleDetailsData {
                        title_data: Default::default(),
                        metadata: TitleDetailsMetadata::None,
                        synopsis: None,
                        entitlement_data: Default::default(),
                    },
                ),
                media_background_data: create_rw_signal(scope, MediaBackgroundType::None),
                carousel_card_data: create_rw_signal(
                    scope,
                    ChartsCarouselCardData {
                        metadata: create_rw_signal(scope, CommonCarouselCardMetadata::default()),
                        card_ui_data: create_rw_signal(
                            scope,
                            ChartCardUIData {
                                rank: create_rw_signal(scope, 1),
                                title: create_rw_signal(scope, None),
                                progress_bar_props: create_rw_signal(scope, None),
                                badge_props: create_rw_signal(scope, None),
                                icon_props: create_rw_signal(scope, None),
                                card_image: create_rw_signal(scope, None),
                                fallback_text: create_rw_signal(scope, None),
                                provider_logo_props: create_rw_signal(scope, None),
                                tts_details_data: create_rw_signal(scope, None),
                                card_image_base_attributes: create_rw_signal(
                                    scope,
                                    ImageCollageBaseAttributes::default(),
                                ),
                                card_secondary_base_image: create_rw_signal(scope, None),
                            },
                        ),
                    },
                ),
                contextual_menu_metadata,
            }
        }

        fn create_fake_super_carousel_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> SuperCarouselItemModel {
            SuperCarouselItemModel {
                carousel_card_data: create_rw_signal(
                    scope,
                    SuperCarouselCardData {
                        metadata: create_rw_signal(scope, CommonCarouselCardMetadata::default()),
                        card_ui_data: create_rw_signal(
                            scope,
                            SuperCardUIData {
                                title: create_rw_signal(scope, None),
                                fallback_text: create_rw_signal(scope, None),
                                subtitle: create_rw_signal(scope, None),
                                badge_props: create_rw_signal(scope, None),
                                icon_props: create_rw_signal(scope, None),
                                poster_2x3_image: create_rw_signal(scope, None),
                                provider_logo_props: create_rw_signal(scope, None),
                                tts_details_data: create_rw_signal(scope, None),
                            },
                        ),
                        expanded_card_ui_data: create_rw_signal(scope, None),
                    },
                ),
                contextual_menu_metadata,
                media_background_data: create_rw_signal(scope, MediaBackgroundType::None),
            }
        }

        fn create_fake_standard_carousel_card(
            scope: Scope,
            contextual_menu_metadata: RwSignal<ContextualMenuMetadata>,
        ) -> StandardCardContainerItemModel {
            StandardCardContainerItemModel {
                title_details_data: create_rw_signal(
                    scope,
                    StandardTitleDetailsData {
                        title_data: Default::default(),
                        metadata: TitleDetailsMetadata::None,
                        synopsis: None,
                        entitlement_data: Default::default(),
                    },
                ),
                media_background_data: create_rw_signal(scope, MediaBackgroundType::None),
                carousel_card_data: create_rw_signal(
                    scope,
                    StandardCardData {
                        metadata: create_rw_signal(scope, CommonCarouselCardMetadata::default()),
                        card_ui_data: create_rw_signal(
                            scope,
                            StandardCardUIData {
                                title: create_rw_signal(scope, None),
                                subtitle: create_rw_signal(scope, None),
                                provider_logo_props: create_rw_signal(scope, None),
                                progress_bar_props: create_rw_signal(scope, None),
                                badge_props: create_rw_signal(scope, None),
                                icon_props: create_rw_signal(scope, None),
                                card_image: create_rw_signal(scope, None),
                                fallback_text: create_rw_signal(scope, None),
                                text_overlay_position: create_rw_signal(scope, None),
                                tts_details_data: create_rw_signal(scope, None),
                                card_image_base_attributes: create_rw_signal(
                                    scope,
                                    ImageCollageBaseAttributes::default(),
                                ),
                                card_secondary_base_image: create_rw_signal(scope, None),
                                item_type_test_id: create_rw_signal(scope, Default::default()),
                            },
                        ),
                        linear_schedule_data: vec![],
                    },
                ),
                contextual_menu_metadata,
            }
        }

        const MOCK_GTI: &str = "mock-gti";

        fn create_container_list(
            scope: Scope,
        ) -> (Vec<ContainerType>, Vec<RwSignal<ContextualMenuMetadata>>) {
            let signals: Vec<RwSignal<ContextualMenuMetadata>> = (0..10)
                .map(|_| {
                    create_rw_signal(
                        scope,
                        ContextualMenuMetadata {
                            gti: Some(MOCK_GTI.to_string()),
                            ..Default::default()
                        },
                    )
                })
                .collect();
            let common_carousel_metadata = CommonCarouselMetadata {
                id: "".to_string(),
                analytics: create_rw_signal(scope, Default::default()),
                journey_ingress_context: create_rw_signal(scope, None),
            };
            let nodes_carousel_metadata = NodesCarouselMetadata {
                title: create_rw_signal(scope, None),
            };
            (
                vec![
                    // Standard Carousel
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::StandardCarousel(create_rw_signal(
                                scope,
                                StandardCarouselModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata.clone(),
                                    ),
                                    standard_carousel_metadata: create_rw_signal(
                                        scope,
                                        StandardCarouselMetadata {
                                            tags: vec![],
                                            title: create_rw_signal(scope, None),
                                            facet: create_rw_signal(scope, None),
                                            pagination_link: create_rw_signal(scope, None),
                                            pagination_pending: create_rw_signal(scope, false),
                                            see_more_link: create_rw_signal(scope, None),
                                            see_more_link_placement: create_rw_signal(scope, None),
                                        },
                                    ),
                                    items: create_rw_signal(
                                        scope,
                                        vec![StandardCardContainerItemListType {
                                            model: create_rw_signal(
                                                scope,
                                                StandardCardContainerItemType::GenericTitleCard(
                                                    create_rw_signal(
                                                        scope,
                                                        create_fake_standard_carousel_card(
                                                            scope, signals[0],
                                                        ),
                                                    ),
                                                ),
                                            ),
                                            id: "".to_string(),
                                            uuid: Uuid::new_v4(),
                                        }],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Super Carousel
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::SuperCarousel(create_rw_signal(
                                scope,
                                SuperCarouselModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata.clone(),
                                    ),
                                    super_carousel_metadata: create_rw_signal(
                                        scope,
                                        SuperCarouselMetadata {
                                            title: create_rw_signal(scope, None),
                                            pagination_link: create_rw_signal(scope, None),
                                            pagination_pending: create_rw_signal(scope, false),
                                            expandable: false,
                                        },
                                    ),
                                    items: create_rw_signal(
                                        scope,
                                        vec![SuperCarouselItemTypeWrapper {
                                            uuid: Uuid::new_v4(),
                                            model: create_rw_signal(
                                                scope,
                                                SuperCarouselItemType::GenericTitleCard(
                                                    create_rw_signal(
                                                        scope,
                                                        create_fake_super_carousel_card(
                                                            scope, signals[1],
                                                        ),
                                                    ),
                                                ),
                                            ),
                                            id: "".to_string(),
                                        }],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Chart Carousel
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::ChartsCarousel(create_rw_signal(
                                scope,
                                ChartsCarouselModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata.clone(),
                                    ),
                                    charts_carousel_metadata: create_rw_signal(
                                        scope,
                                        ChartsCarouselMetadata {
                                            title: create_rw_signal(scope, None),
                                        },
                                    ),
                                    items: create_rw_signal(
                                        scope,
                                        vec![ChartsCarouselItemTypeWrapper {
                                            model: create_rw_signal(
                                                scope,
                                                ChartsCarouselItemType::GenericTitleCard(
                                                    create_rw_signal(
                                                        scope,
                                                        create_fake_charts_card(scope, signals[2]),
                                                    ),
                                                ),
                                            ),
                                            uuid: Uuid::new_v4(),
                                            id: "".to_string(),
                                        }],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Nodes Carousels
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::NodesCarousel(create_rw_signal(
                                scope,
                                NodesCarouselModel::EntitledOrAgnostic(create_rw_signal(
                                    scope,
                                    EntitledOrAgnosticNodesCarouselModel {
                                        common_carousel_metadata: create_rw_signal(
                                            scope,
                                            common_carousel_metadata.clone(),
                                        ),
                                        nodes_carousel_metadata: create_rw_signal(
                                            scope,
                                            nodes_carousel_metadata.clone(),
                                        ),
                                        items: create_rw_signal(
                                            scope,
                                            vec![create_fake_entitled_or_agnostic_nodes_card(
                                                scope, signals[3],
                                            )],
                                        ),
                                        leading_carousel_button: create_rw_signal(scope, None),
                                    },
                                )),
                            )),
                        ),
                        id: "".to_string(),
                    },
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::NodesCarousel(create_rw_signal(
                                scope,
                                NodesCarouselModel::Unentitled(create_rw_signal(
                                    scope,
                                    UnentitledNodesCarouselModel {
                                        common_carousel_metadata: create_rw_signal(
                                            scope,
                                            common_carousel_metadata.clone(),
                                        ),
                                        nodes_carousel_metadata: create_rw_signal(
                                            scope,
                                            nodes_carousel_metadata,
                                        ),
                                        items: create_rw_signal(
                                            scope,
                                            vec![create_fake_untitled_nodes_card(
                                                scope, signals[4],
                                            )],
                                        ),
                                        leading_carousel_button: create_rw_signal(scope, None),
                                    },
                                )),
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Short Carousel
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::ShortCarousel(create_rw_signal(
                                scope,
                                ShortCarouselModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata.clone(),
                                    ),
                                    items: create_rw_signal(
                                        scope,
                                        vec![create_fake_short_carousel_card(scope, signals[5])],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Standard Hero Carousel
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::StandardHero(create_rw_signal(
                                scope,
                                StandardHeroModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata.clone(),
                                    ),
                                    items: create_rw_signal(
                                        scope,
                                        vec![create_fake_hero_card(scope, signals[6])],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Tentpole Hero Carousel
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::TentpoleHero(create_rw_signal(
                                scope,
                                TentpoleHeroModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata.clone(),
                                    ),
                                    item: create_rw_signal(
                                        scope,
                                        vec![create_fake_hero_card(scope, signals[7])],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Beard Supported Carousel
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::BeardSupportedCarousel(create_rw_signal(
                                scope,
                                BeardSupportedCarouselModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata.clone(),
                                    ),
                                    beard_supported_carousel_metadata: create_rw_signal(
                                        scope,
                                        BeardSupportedCarouselMetadata {
                                            tags: vec![],
                                            title: create_rw_signal(scope, None),
                                            facet: create_rw_signal(scope, None),
                                            pagination_link: create_rw_signal(scope, None),
                                            pagination_pending: create_rw_signal(scope, false),
                                        },
                                    ),
                                    items: create_rw_signal(
                                        scope,
                                        vec![StandardCardContainerItemListType {
                                            model: create_rw_signal(
                                                scope,
                                                StandardCardContainerItemType::GenericTitleCard(
                                                    create_rw_signal(
                                                        scope,
                                                        create_fake_standard_carousel_card(
                                                            scope, signals[8],
                                                        ),
                                                    ),
                                                ),
                                            ),
                                            id: "".to_string(),
                                            uuid: Uuid::new_v4(),
                                        }],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                    // Promo Banner
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::PromoBanner(create_rw_signal(
                                scope,
                                PromoBannerModel {
                                    common_carousel_metadata: create_rw_signal(
                                        scope,
                                        common_carousel_metadata,
                                    ),
                                    item: create_rw_signal(
                                        scope,
                                        vec![create_fake_promo_card(scope, signals[9])],
                                    ),
                                },
                            )),
                        ),
                        id: "".to_string(),
                    },
                ],
                signals,
            )
        }

        #[test]
        fn updates_carousels_for_watchlist() {
            launch_only_app_context(move |ctx| {
                let (containers, signals) = create_container_list(ctx.scope());
                signals
                    .iter()
                    .for_each(|signal| assert_eq!(signal.get_untracked().is_in_watchlist, None));
                update_cm_item_state(
                    &containers,
                    true,
                    Some(MOCK_GTI.to_string()),
                    MenuConfigurableItemVariant::Watchlist,
                );
                signals.iter().for_each(|signal| {
                    assert_eq!(signal.get_untracked().is_in_watchlist, Some(true))
                });
            });
        }

        #[test]
        fn updates_carousels_for_favorite() {
            launch_only_app_context(move |ctx| {
                let (containers, signals) = create_container_list(ctx.scope());
                signals
                    .iter()
                    .for_each(|signal| assert_eq!(signal.get_untracked().is_favorited, None));
                update_cm_item_state(
                    &containers,
                    true,
                    Some(MOCK_GTI.to_string()),
                    MenuConfigurableItemVariant::Favorite,
                );
                signals
                    .iter()
                    .for_each(|signal| assert_eq!(signal.get_untracked().is_favorited, Some(true)));
            });
        }

        #[test]
        fn should_handle_open_modal_primary_action() {
            launch_only_app_context(move |ctx| {
                let primary_action = TransitionAction::openModal(TitleOpenModalAction {
                    refMarker: "Test refMarker".to_string(),
                    label: "Open Modal Label".to_string(),
                    modalHeader: "Test Header".to_string(),
                    actionSegments: vec![TitleActionSegment {
                        childActions: vec![TransitionAction::playback(TitlePlaybackAction {
                            playbackMetadata: PlaybackMetadata {
                                refMarker: "ref_marker".to_string(),
                                contentDescriptors: None,
                                playbackExperienceMetadata: None,
                                position: PlaybackPosition::FeatureFinished,
                                startPositionEpochUtc: None,
                                userPlaybackMetadata: UserPlaybackMetadata {
                                    runtimeSeconds: Some(0),
                                    timecodeSeconds: Some(0),
                                    hasStreamed: Some(false),
                                    isLinear: None,
                                    linearStartTime: None,
                                    linearEndTime: None,
                                },
                                userEntitlementMetadata: UserEntitlementMetadata {
                                    entitlementType: "type".to_string(),
                                    benefitType: vec![],
                                },
                                videoMaterialType: "type".to_string(),
                                channelId: None,
                                playbackTitle: None,
                                metadataActionType: TitleActionMetadataType::Playback,
                                catalogMetadata: None,
                                isTrailer: None,
                                isUnopenedRental: false,
                            },
                            label: None,
                            refMarker: "".to_string(),
                        })],
                        entitlementMessaging: None,
                    }],
                });

                let metadata = ContextualMenuMetadata {
                    primary_action: Some(primary_action),
                    ..Default::default()
                };
                let cm_data = create_contextual_menu_data(
                    metadata,
                    &ctx,
                    CreateContextualMenuOptions {
                        button: Some(ButtonVariant::Primary),
                        ..Default::default()
                    },
                )
                .unwrap();
                assert!(
                    matches!(cm_data, ContextualMenuData::WatchModal(data) if data.buttons.is_empty())
                );
            })
        }
    }
}
