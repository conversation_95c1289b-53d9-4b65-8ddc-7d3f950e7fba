use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::{
    lifecycle::{LifecycleState, LifecycleStateContext},
    prelude::{
        create_effect, create_rw_signal, use_context, AppContext, ReadSignal, RwSignal, Scope,
        SignalGet, SignalGetUntracked, SignalWithUntracked,
    },
    time::Instant,
};
use location::Location;
use mockall_double::double;
use navigation_menu::utils::{get_page_params, PageParams};
use strum::Display;

#[double]
use crate::utils::metric_emitter::MetricEmitter;

const PAGE_ID_KEYS: [&str; 7] = ["home", "movie", "tv", "Sports", "live", "MyStuff", "kids"];

#[derive(Clone, Display)]
#[derive_test_only(PartialEq, Debug)]
pub enum FreshnessAction {
    BackCache,
    NetworkClient,
    Foreground,
}

fn get_page_type(scope: Scope, location: ReadSignal<Location>) -> String {
    let expected_page_id = location
        .with_untracked(|location| get_page_params(location, scope))
        .map(|page_params| {
            match page_params {
                PageParams::Collections(page_params) => {
                    if page_params.page_id == "home" {
                        // If page_id is home, then search by the page_type.
                        // This is the case for movies, tv etc. If it's the homepage then the page_type will still be home.
                        page_params.page_type
                    } else {
                        page_params.page_id
                    }
                }
            }
        });

    if let Some(expected_page_id) = expected_page_id {
        if PAGE_ID_KEYS
            .iter()
            .any(|page_id| page_id == &expected_page_id)
        {
            return expected_page_id;
        }
    }

    "others".to_string()
}

fn report_freshness_metric(
    scope: Scope,
    data_fetch_time: Instant,
    action: FreshnessAction,
    location: ReadSignal<Location>,
) {
    let time = Instant::now().duration_since(data_fetch_time);
    let page_type = get_page_type(scope, location);

    MetricEmitter::emit_freshness_metric(time, page_type, action);
}

pub fn setup_freshness_metric(
    ctx: &AppContext,
    location: ReadSignal<Location>,
) -> RwSignal<Option<(Instant, FreshnessAction)>> {
    let data_fetch_data = create_rw_signal(ctx.scope(), None::<(Instant, FreshnessAction)>);
    let lifecycle_state: Option<ReadSignal<LifecycleState>> =
        match use_context::<LifecycleStateContext>(ctx.scope()) {
            None => {
                log::error!("Freshness metric - Lifecycle state is not in cache");
                None
            }
            Some(val) => Some(val.0),
        };

    // Handles when the page loads with new data.
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();

        move |_| {
            let data_fetch_data: Option<(Instant, FreshnessAction)> = data_fetch_data.get();
            let lifecycle_state = lifecycle_state.map_or_else(
                || LifecycleState::Foreground,
                |lifecycle_state| lifecycle_state.get_untracked(),
            );

            if let LifecycleState::Foreground = lifecycle_state {
                if let Some((data_fetch_time, action)) = data_fetch_data {
                    report_freshness_metric(ctx.scope(), data_fetch_time, action, location);
                }
            }
        }
    });

    // Handles returning from background mode.
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();

        move |_| {
            let lifecycle_state = match lifecycle_state.map(|lifecycle_state| lifecycle_state.get())
            {
                Some(state) => state,
                None => {
                    return;
                }
            };

            if let LifecycleState::Foreground = lifecycle_state {
                if let Some((data_fetch_time, _)) = data_fetch_data.get_untracked() {
                    report_freshness_metric(
                        ctx.scope(),
                        data_fetch_time,
                        FreshnessAction::Foreground,
                        location,
                    );
                }
            }
        }
    });

    data_fetch_data
}

#[cfg(test)]
pub fn mock_setup_freshness_metric(
    ctx: &AppContext,
    _location: ReadSignal<Location>,
) -> RwSignal<Option<(Instant, FreshnessAction)>> {
    use_context(ctx.scope()).unwrap()
}

#[cfg(test)]
pub mod tests {

    use std::time::Duration;

    use crate::utils::metric_emitter::MockMetricEmitter;

    use super::*;
    use ignx_compositron::{
        app::launch_only_app_context,
        prelude::{provide_context, SignalSet},
    };
    use mock_instant::MockClock;
    use mockall::predicate::eq;
    use serde_json::{Map, Value};

    fn get_location(page_type: &str, page_id: &str) -> Location {
        let mut params = Map::new();
        params.insert("pageType".to_string(), Value::String(page_type.to_string()));
        params.insert("pageId".to_string(), Value::String(page_id.to_string()));

        Location {
            pageType: location::PageType::Rust(location::RustPage::RUST_COLLECTIONS),
            pageParams: params,
        }
    }

    #[test]
    pub fn should_update_freshness_metric_when_data_fetch_time_changes() {
        let cases = [
            (
                get_location("home", "home"),
                0,
                "home",
                FreshnessAction::BackCache,
            ),
            (
                get_location("Sports", "home"),
                200,
                "Sports",
                FreshnessAction::NetworkClient,
            ),
            (
                get_location("tv", "home"),
                100,
                "tv",
                FreshnessAction::NetworkClient,
            ),
            (
                get_location("movie", "home"),
                300,
                "movie",
                FreshnessAction::BackCache,
            ),
            (
                get_location("merch", "kids"),
                100,
                "kids",
                FreshnessAction::NetworkClient,
            ),
            (
                get_location("MyStuff", "home"),
                100,
                "MyStuff",
                FreshnessAction::NetworkClient,
            ),
            (
                get_location("home", "MyStuff"),
                400,
                "MyStuff",
                FreshnessAction::NetworkClient,
            ),
            (
                get_location("merch", "itvhub"),
                100,
                "others",
                FreshnessAction::NetworkClient,
            ),
        ];

        launch_only_app_context(move |ctx| {
            let location = create_rw_signal(ctx.scope(), get_location("home", "home"));
            let data_fetch_data = setup_freshness_metric(&ctx, location.read_only().into());

            for case in cases {
                let metric_emitter_context = MockMetricEmitter::emit_freshness_metric_context();

                let time = Instant::now();
                MockClock::advance(Duration::from_millis(case.1));

                metric_emitter_context
                    .expect()
                    .returning(|_, _, _| {})
                    .with(
                        eq(Duration::from_millis(case.1)),
                        eq(case.2.to_string()),
                        eq(case.3.clone()),
                    )
                    .once();

                location.set(case.0.clone());
                data_fetch_data.set(Some((time, case.3.clone())));
            }

            data_fetch_data.set(None); // doesn't do anything.
        });
    }

    #[test]
    pub fn should_update_freshness_metric_when_coming_to_foreground() {
        launch_only_app_context(move |ctx| {
            let location = create_rw_signal(ctx.scope(), get_location("home", "home"));
            let lifecycle_state = create_rw_signal(ctx.scope(), LifecycleState::Background);
            provide_context(
                ctx.scope(),
                LifecycleStateContext(lifecycle_state.read_only()),
            );

            let data_fetch_data = setup_freshness_metric(&ctx, location.read_only().into());

            // change a few times.
            MockClock::advance(Duration::from_millis(500));
            data_fetch_data.set(Some((Instant::now(), FreshnessAction::NetworkClient)));
            MockClock::advance(Duration::from_millis(500));
            data_fetch_data.set(Some((Instant::now(), FreshnessAction::BackCache)));
            MockClock::advance(Duration::from_millis(500));

            // doesn't update.
            lifecycle_state.set(LifecycleState::Background);
            MockClock::advance(Duration::from_millis(500));

            let metric_emitter_context = MockMetricEmitter::emit_freshness_metric_context();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq(Duration::from_millis(1000)),
                    eq("home".to_string()),
                    eq(FreshnessAction::Foreground),
                )
                .once();

            // updates.
            lifecycle_state.set(LifecycleState::Foreground);
            MockClock::advance(Duration::from_millis(500));

            // doesn't update.
            lifecycle_state.set(LifecycleState::Background);
            MockClock::advance(Duration::from_millis(500));

            let metric_emitter_context = MockMetricEmitter::emit_freshness_metric_context();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq(Duration::from_millis(2000)),
                    eq("home".to_string()),
                    eq(FreshnessAction::Foreground),
                )
                .once();

            // updates.
            lifecycle_state.set(LifecycleState::Foreground);
            MockClock::advance(Duration::from_millis(500));

            // let metric_emitter_context = MockMetricEmitter::emit_freshness_metric_context();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq(Duration::from_millis(0)),
                    eq("home".to_string()),
                    eq(FreshnessAction::NetworkClient),
                )
                .once();

            // emits as usual.
            data_fetch_data.set(Some((Instant::now(), FreshnessAction::NetworkClient)));
        });
    }
}
