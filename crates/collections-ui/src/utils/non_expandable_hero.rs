use container_types::ui_signals::StandardHeroModel;
use ignx_compositron::prelude::{RwSignal, SignalWithUntracked};

pub fn is_non_expandable_hero_sig(container: &RwSignal<StandardHeroModel>) -> bool {
    container.with_untracked(|data| {
        let len = data.items.with_untracked(|items| items.len());

        if len > 1 {
            return false;
        }

        let item = data.items.with_untracked(|items| items.first().cloned());
        let Some(item) = item else {
            return false;
        };

        let item_gti_defined = item.gti.with_untracked(|gti| gti.is_some());
        let secondary_button_defined = item.ui_data.with_untracked(|ui_data| {
            ui_data
                .secondary_button_action
                .with_untracked(|action| action.is_some())
        });

        !item_gti_defined && !secondary_button_defined
    })
}

#[cfg(test)]
mod test {
    use super::*;
    use common_transform_types::actions::{SwiftAction, TransitionAction};
    use container_types::ui_signals::{CommonCarouselMetadata, HeroItemModel, HeroItemUIData};
    use contextual_menu_types::prelude::*;
    use fableous::buttons::primary_button::PrimaryButtonVariant;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::prelude::{create_rw_signal, Scope};
    use media_background::types::MediaBackgroundType;
    use rstest::*;
    use std::collections::HashMap;
    use title_details::components::title::TitleData;
    use uuid::Uuid;

    fn inner_transition_action() -> TransitionAction {
        TransitionAction::landing(SwiftAction {
            text: Some("text".to_string()),
            analytics: HashMap::new(),
            refMarker: "refMarker".to_string(),
            pageId: "pageId".to_string(),
            pageType: "pageType".to_string(),
            serviceToken: None,
            journeyIngressContext: None,
        })
    }

    fn hero_item(
        scope: Scope,
        gti: Option<String>,
        secondary_button_action: Option<TransitionAction>,
    ) -> HeroItemModel {
        HeroItemModel {
            uuid: Uuid::new_v4(),
            id: "id".to_string(),
            gti: create_rw_signal(scope, gti),
            ui_data: create_rw_signal(
                scope,
                HeroItemUIData {
                    title_data: create_rw_signal(
                        scope,
                        TitleData {
                            title_art_url: None,
                            provider_logo_url: None,
                            title_text: "a title".to_string(),
                            size: None,
                            provider_logo_size: None,
                        },
                    ),
                    synopsis: create_rw_signal(scope, None),
                    primary_button_action: create_rw_signal(scope, Some(inner_transition_action())),
                    secondary_button_action: create_rw_signal(scope, secondary_button_action),
                    metadata_rows: create_rw_signal(scope, vec![]),
                    primary_button_variant: create_rw_signal(
                        scope,
                        PrimaryButtonVariant::IconSize400("".to_string()),
                    ),
                    secondary_button_variant: create_rw_signal(scope, None),
                    legal_messages: create_rw_signal(scope, vec![]),
                    maturity_rating_image: create_rw_signal(scope, None),
                    entitlement_label: create_rw_signal(scope, None),
                    maturity_rating_string: create_rw_signal(scope, None),
                    regulatory_label_string: create_rw_signal(scope, None),
                    progress_bar_percentage: create_rw_signal(scope, None),
                },
            ),
            media_background_data: create_rw_signal(scope, MediaBackgroundType::None),
            contextual_menu_metadata: create_rw_signal(scope, ContextualMenuMetadata::default()),
            impression_data: Default::default(),
            swift_content_type: Some("MOVIE".to_string()),
            linear_schedule_data: vec![],
        }
    }

    enum HeroOptions {
        GTI,
        SecondaryButton,
        NoGTINoSecondaryButton,
    }

    fn hero_items(scope: Scope, num: usize, options: HeroOptions) -> Vec<HeroItemModel> {
        let mut items = vec![];

        for _ in 0..num {
            let item = match options {
                HeroOptions::GTI => hero_item(scope, Some("a gti".to_string()), None),
                HeroOptions::SecondaryButton => {
                    hero_item(scope, None, Some(inner_transition_action()))
                }
                HeroOptions::NoGTINoSecondaryButton => hero_item(scope, None, None),
            };

            items.push(item);
        }

        items
    }

    // more than one item -> false
    // item has gti -> false
    // item has secondary button -> false
    // one item, no gti, no secondary button -> true
    // zero item -> false

    #[rstest]
    #[case(2, HeroOptions::NoGTINoSecondaryButton, false)]
    #[case(1, HeroOptions::GTI, false)]
    #[case(1, HeroOptions::SecondaryButton, false)]
    #[case(1, HeroOptions::NoGTINoSecondaryButton, true)]
    #[case(0, HeroOptions::NoGTINoSecondaryButton, false)]
    fn test_is_non_expandable_hero_sig(
        #[case] num_items: usize,
        #[case] options: HeroOptions,
        #[case] expected: bool,
    ) {
        launch_only_scope(move |scope| {
            let items = hero_items(scope, num_items, options);

            let hero = create_rw_signal(
                scope,
                StandardHeroModel {
                    common_carousel_metadata: create_rw_signal(
                        scope,
                        CommonCarouselMetadata {
                            id: "hero id".to_string(),
                            analytics: create_rw_signal(scope, Default::default()),
                            journey_ingress_context: create_rw_signal(scope, None),
                        },
                    ),
                    items: create_rw_signal(scope, items),
                },
            );

            assert_eq!(is_non_expandable_hero_sig(&hero), expected);
        });
    }
}
