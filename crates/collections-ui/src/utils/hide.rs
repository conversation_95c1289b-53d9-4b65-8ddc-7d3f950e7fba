use crate::types::collections_types::CollectionsPageModel;
use crate::ui::types::ContainerType;
use container_types::ui_signals::{
    BeardSupportedCarouselModel, CommonCarouselCardMetadata, ContainerModel, NodesCarouselModel,
    StandardCarouselModel, SuperCarouselModel,
};
use ignx_compositron::prelude::*;

trait Hideable {
    /// If not supported, no operation will be done.
    fn try_hide(&self, gti: &str);
}
impl Hideable for ContainerModel {
    fn try_hide(&self, gti: &str) {
        match self {
            ContainerModel::StandardCarousel(model) => model.try_hide(gti),
            ContainerModel::SuperCarousel(model) => model.try_hide(gti),
            ContainerModel::NodesCarousel(model) => model.try_hide(gti),
            ContainerModel::BeardSupportedCarousel(model) => model.try_hide(gti),
            ContainerModel::StandardHero(_) | ContainerModel::TentpoleHero(_) => {
                // Not supported (yet) because we need to deal with edge cases like what happens if the whole Hero gets removed.
                // Additionally, we need to support rotating to the next item.
                // SIM: https://issues.amazon.com/issues/LR-Rust-798
            }
            ContainerModel::ChartsCarousel(_) // Not supported as per product decision because we don't want a partial charts carousel e.g. "#1 #3 #7" which could look jarring.
            | ContainerModel::ShortCarousel(_) // Has no GTI
            | ContainerModel::PromoBanner(_)
            | ContainerModel::DiscoveryAssistant(_)
            | ContainerModel::DiscoveryAssistantHeader(_)
            | ContainerModel::Grid(_)
            | ContainerModel::Onboarding(_)
            |ContainerModel::ScheduleCarousel(_)
            | ContainerModel::EntityCarousel(_)
            | ContainerModel::SpecialCollectionsCarousel(_) => {}
        }
    }
}

impl Hideable for RwSignal<StandardCarouselModel> {
    fn try_hide(&self, gti_to_hide: &str) {
        self.with(|model| model.items.try_hide(gti_to_hide))
    }
}

impl Hideable for RwSignal<BeardSupportedCarouselModel> {
    fn try_hide(&self, gti_to_hide: &str) {
        self.with(|model| {
            model.items.try_hide(gti_to_hide);
        })
    }
}

impl<T> Hideable for RwSignal<Vec<T>>
where
    for<'a> &'a T: Into<RwSignal<CommonCarouselCardMetadata>>,
{
    fn try_hide(&self, gti_to_remove: &str) {
        self.update(|t| {
            t.retain(|item| {
                let signal: RwSignal<CommonCarouselCardMetadata> = item.into();
                signal.with_untracked(|signal| {
                    let gti = signal.gti.as_ref();
                    if let Some(gti) = gti {
                        gti != gti_to_remove
                    } else {
                        true
                    }
                })
            })
        })
    }
}

impl Hideable for RwSignal<SuperCarouselModel> {
    fn try_hide(&self, gti_to_hide: &str) {
        self.with(|model| {
            model.items.try_hide(gti_to_hide);
        })
    }
}

impl Hideable for RwSignal<NodesCarouselModel> {
    fn try_hide(&self, gti_to_hide: &str) {
        self.with(|model| match model {
            NodesCarouselModel::EntitledOrAgnostic(model) => model.with_untracked(|t| {
                t.items.try_hide(gti_to_hide);
            }),
            NodesCarouselModel::Unentitled(model) => model.with_untracked(|t| {
                t.items.try_hide(gti_to_hide);
            }),
        })
    }
}

fn is_empty_and_to_be_removed(container_type: &ContainerType) -> bool {
    container_type.model.with_untracked(|model| {
        match model {
            ContainerModel::StandardCarousel(model) => {
                model.with_untracked(|t| t.items.with_untracked(|t| t.is_empty()))
            }
            ContainerModel::SuperCarousel(model) => {
                model.with_untracked(|t| t.items.with_untracked(|t| t.is_empty()))
            }
            ContainerModel::ChartsCarousel(model) => {
                model.with_untracked(|t| t.items.with_untracked(|t| t.is_empty()))
            }
            ContainerModel::ShortCarousel(model) => {
                model.with_untracked(|t| t.items.with_untracked(|t| t.is_empty()))
            }
            ContainerModel::BeardSupportedCarousel(model) => {
                model.with_untracked(|t| t.items.with_untracked(|t| t.is_empty()))
            }
            ContainerModel::NodesCarousel(model) => model.with_untracked(|model| match model {
                NodesCarouselModel::EntitledOrAgnostic(t) => t.with_untracked(|t| {
                    t.leading_carousel_button
                        .with_untracked(|btn| btn.is_none())
                        && t.items.with_untracked(|t| t.is_empty())
                }),
                NodesCarouselModel::Unentitled(t) => t.with_untracked(|t| {
                    t.leading_carousel_button
                        .with_untracked(|btn| btn.is_none())
                        && t.items.with_untracked(|t| t.is_empty())
                }),
            }),
            ContainerModel::StandardHero(model) => {
                model.with_untracked(|model| model.items.with_untracked(|t| t.is_empty()))
            }
            // Technically speaking, both Tentpole and PromoBanner have only one item. Coincidentally, we store them into a Vec and therefore it can be removed.
            ContainerModel::TentpoleHero(model) => {
                model.with_untracked(|model| model.item.with_untracked(|t| t.is_empty()))
            }
            ContainerModel::PromoBanner(model) => {
                model.with_untracked(|model| model.item.with_untracked(|t| t.is_empty()))
            }
            // Not supported
            ContainerModel::DiscoveryAssistant(_) => false,
            ContainerModel::DiscoveryAssistantHeader(_) => false,
            ContainerModel::Grid(_) => false,
            ContainerModel::Onboarding(_) => false,
            ContainerModel::ScheduleCarousel(_) => false,
            ContainerModel::EntityCarousel(_) => false,
            ContainerModel::SpecialCollectionsCarousel(_) => false,
        }
    })
}

/// Removes the items that have the given GTI and also removes empty containers
pub fn remove_gti_from_containers(page_data: RwSignal<CollectionsPageModel>, gti: &str) {
    page_data.with_untracked(|page_data| {
        page_data.container_list.update(|containers| {
            containers.retain_mut(|container| {
                container.model.with(|model| model.try_hide(gti));
                !is_empty_and_to_be_removed(container)
            })
        });
    });
}

pub fn remove_gti_from_container(
    page_data: RwSignal<CollectionsPageModel>,
    gti: &str,
    container_id: &str,
) {
    page_data.with_untracked(|page_data| {
        page_data.container_list.update(|containers| {
            containers.retain_mut(|container| {
                // Here we use the transform container id, which makes sense in case the "Remove from Continue Watching"
                // carousel was duplicated for some reason.
                if container.transform_container_id() == container_id {
                    container.model.with(|model| model.try_hide(gti));
                }
                !is_empty_and_to_be_removed(container)
            })
        });
    });
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::page::helpers_sig::{
        create_default_collections_page_model, get_initial_load_success_cb,
    };
    use crate::test_assets::mocks::SuccessCallbackTestSetup;
    use crate::types::collections_types::CollectionsPageModel;
    use crate::utils::hide::{remove_gti_from_container, remove_gti_from_containers};
    use collection_types::network_types::CollectionsPage;
    use container_types::ui_signals::HeroItemModel;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::context::AppContext;
    use navigation_menu::utils::CollectionsPageParams;
    use network::common::{LRCEdgeResponse, PageRequestStatus};
    use network_parser::core::network_parse_from_str;
    use std::rc::Rc;

    fn page_data() -> serde_json::Result<LRCEdgeResponse<CollectionsPage>> {
        let res = include_str!("../test_assets/collections_page_hide_and_continue_watching.json");
        network_parse_from_str(&res.to_string())
    }

    fn populate_page_data_from_response(
        page_data_signal: RwSignal<CollectionsPageModel>,
        load_status_signal: RwSignal<PageRequestStatus<String>>,
        ctx: AppContext,
    ) {
        let page_data = page_data().map(|result| result.resource).unwrap();
        let page_params = CollectionsPageParams {
            page_id: "home".to_string(),
            page_type: "home".to_string(),
            service_token: None,
        };

        let setup = SuccessCallbackTestSetup::new(ctx)
            .with_page_data_and_status(page_data_signal, load_status_signal)
            .with_parsed_page(page_data)
            .with_location_signal_from_params(page_params);

        let page_controller = setup.to_controller();

        let success_cb =
            get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

        let args = setup.to_success_cb_args();

        success_cb(
            args.parsed_page_from_network.clone(),
            args.transform_name,
            args.manipulate_page_data,
            args.timestamp,
        );
    }

    fn mock_response(ctx: &AppContext) -> RwSignal<CollectionsPageModel> {
        let page_data = create_default_collections_page_model(ctx.scope);
        let load_status_signal =
            create_rw_signal(ctx.scope(), PageRequestStatus::Success("page".to_string()));
        populate_page_data_from_response(page_data, load_status_signal, ctx.clone());
        page_data
    }

    // Helper function to find a container with a specific ID
    fn find_container_by_id(
        page_model: &CollectionsPageModel,
        container_id: &str,
    ) -> Option<usize> {
        page_model.container_list.with_untracked(|containers| {
            containers
                .iter()
                .position(|container| container.transform_container_id() == container_id)
        })
    }

    fn find_gtis<T>(items: RwSignal<Vec<T>>) -> Vec<String>
    where
        for<'a> &'a T: Into<RwSignal<CommonCarouselCardMetadata>>,
    {
        items.with_untracked(|items| {
            items
                .iter()
                .filter_map(|item| {
                    let signal: RwSignal<CommonCarouselCardMetadata> = item.into();
                    signal.with_untracked(|signal| signal.gti.clone())
                })
                .collect::<Vec<String>>()
        })
    }
    fn find_gtis_hero(items: RwSignal<Vec<HeroItemModel>>) -> Vec<String> {
        items.with_untracked(|items| {
            items
                .iter()
                .flat_map(|item| item.gti.get())
                .collect::<Vec<String>>()
        })
    }

    // Helper function to check if a container has an item with specific GTI
    fn container_has_item_with_gti(container: &ContainerType, gti: &str) -> bool {
        let gti = gti.to_string();
        container.model.with_untracked(|model| {
            match model {
                ContainerModel::StandardCarousel(carousel) => {
                    carousel.with_untracked(|carousel_model| {
                        find_gtis(carousel_model.items).contains(&gti)
                    })
                }
                ContainerModel::SuperCarousel(carousel) => {
                    carousel.with_untracked(|carousel_model| {
                        find_gtis(carousel_model.items).contains(&gti)
                    })
                }
                ContainerModel::ChartsCarousel(carousel) => {
                    carousel.with_untracked(|carousel_model| {
                        find_gtis(carousel_model.items).contains(&gti)
                    })
                }
                ContainerModel::BeardSupportedCarousel(carousel) => {
                    carousel.with_untracked(|carousel_model| {
                        find_gtis(carousel_model.items).contains(&gti)
                    })
                }
                ContainerModel::NodesCarousel(carousel) => {
                    carousel.with_untracked(|carousel_model| {
                        match carousel_model {
                            // The buttons wont have a GTI and can be ignored
                            NodesCarouselModel::EntitledOrAgnostic(model) => {
                                model.with_untracked(|model| find_gtis(model.items).contains(&gti))
                            }
                            NodesCarouselModel::Unentitled(model) => {
                                model.with_untracked(|model| find_gtis(model.items).contains(&gti))
                            }
                        }
                    })
                }
                ContainerModel::StandardHero(hero) => {
                    hero.with_untracked(|hero| find_gtis_hero(hero.items).contains(&gti))
                }
                ContainerModel::TentpoleHero(hero) => {
                    hero.with_untracked(|hero| find_gtis_hero(hero.item).contains(&gti))
                }
                ContainerModel::PromoBanner(promo) => promo.with_untracked(|promo| {
                    promo.item.with_untracked(|item| {
                        item.iter()
                            .any(|item| item.metadata.gti == Some(gti.clone()))
                    })
                }),
                ContainerModel::ShortCarousel(_) => false,
                ContainerModel::Grid(_) | ContainerModel::Onboarding(_) => false,
                ContainerModel::DiscoveryAssistant(_) => false,
                ContainerModel::DiscoveryAssistantHeader(_) => false,
                ContainerModel::ScheduleCarousel(_) => false,
                ContainerModel::EntityCarousel(_) => false,
                ContainerModel::SpecialCollectionsCarousel(_) => false,
            }
        })
    }

    const CONTINUE_WATCHING_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMjdBRE45Tk81WlhJWCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy";
    const HERO_CAROUSEL_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt67ioRob21li4Rob21ljI6mMToxMjJZTU9SUTEwNkdKUiMjTkJTWEUzMkRNRlpHNjVMVE1WV0GND46CVjI=";
    const STANDARD_CAROUSEL_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy";
    const CHARTS_CAROUSEL_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7fio6jc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNjaGFydHOLhHRlc3SMjqoxOjEzT1ZKNkUyNkJJVzQxIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=";
    const BEARD_SUPPORTED_CAROUSEL_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRsaXZli4Rob21ljI6qMToxMVIzOFY0Tks2SFgyMiMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy";
    const SUPER_CAROUSEL_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxNzRERzlVTkUyQjZXIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=";
    const SHORT_CAROUSEL_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7mio6qc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNzaG9ydGNhcm91c2Vsi4R0ZXN0jI6qMToxMjJKNUJNQUlIUjlYNCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy";
    const NODES_UNENTITLED_CONTAINER_ID: &'static str = "V2=4AEA6_unodes_Ye-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7eio6ic3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNub2Rlc4uEdGVzdIyOqjE6MTM3V1I4S1FTWjRXSVcjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==";
    const NODES_ENTITLED_CONTAINER_ID: &'static str = "V2=X_enodes_Xu69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyT0NaNzhLUjI0VTZGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=";
    const PROMOTIONAL_BANNER_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt65ioRob21li4J0doyOpjE6MTJFQ1IyRzRXWkpPUk8jI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy";
    const TENTPOLE_HERO_CONTAINER_ID: &'static str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Lio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOpjE6MTNCWlpUSkxZSEtWTkMjI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy";

    const THE_BOYS_GTI: &'static str = "amzn1.dv.gti.292e4408-aade-494c-84fa-b8950d3e9418"; // The Boys

    fn is_gti_present_in_container(
        model: RwSignal<CollectionsPageModel>,
        container_id: &str,
        gti: &str,
    ) -> bool {
        model.with_untracked(|model| {
            let container_idx = find_container_by_id(model, container_id);

            if let Some(idx) = container_idx {
                let container = &model.container_list.get_untracked()[idx];
                container_has_item_with_gti(container, gti)
            } else {
                false
            }
        })
    }

    fn assert_is_gti_present_in_containers(
        model: RwSignal<CollectionsPageModel>,
        tests: &[(&str, bool)],
        gti: &str,
    ) {
        for &(container_id, expected_presence) in tests {
            let actual_presence = is_gti_present_in_container(model.clone(), container_id, gti);
            assert_eq!(
                actual_presence, expected_presence,
                "GTI {gti}, ContainerId {container_id}, actual_presence = {actual_presence}, expected_presence = {expected_presence}"
            );
        }
    }

    #[test]
    fn test_remove_gti_from_container() {
        launch_only_app_context(|ctx: AppContext| {
            let model = mock_response(&ctx);

            // We want to test that removing from one container doesn't affect others
            // The Boys appears in multiple containers
            let target_container_id = CONTINUE_WATCHING_CONTAINER_ID;
            let target_gti = THE_BOYS_GTI;

            assert_is_gti_present_in_containers(
                model,
                &[
                    (HERO_CAROUSEL_CONTAINER_ID, true),
                    (STANDARD_CAROUSEL_CONTAINER_ID, true),
                    (CONTINUE_WATCHING_CONTAINER_ID, true),
                    (CHARTS_CAROUSEL_CONTAINER_ID, true),
                    (BEARD_SUPPORTED_CAROUSEL_CONTAINER_ID, true),
                    (SUPER_CAROUSEL_CONTAINER_ID, true),
                    (SHORT_CAROUSEL_CONTAINER_ID, false),
                    (NODES_ENTITLED_CONTAINER_ID, false),
                    (NODES_UNENTITLED_CONTAINER_ID, false),
                    (PROMOTIONAL_BANNER_CONTAINER_ID, true),
                    (TENTPOLE_HERO_CONTAINER_ID, true),
                ],
                target_gti,
            );

            // Remove the GTI from the target container
            remove_gti_from_container(model, target_gti, target_container_id);

            assert_is_gti_present_in_containers(
                model,
                &[
                    (HERO_CAROUSEL_CONTAINER_ID, true),
                    (STANDARD_CAROUSEL_CONTAINER_ID, true),
                    (CONTINUE_WATCHING_CONTAINER_ID, false), // Only one changed
                    (CHARTS_CAROUSEL_CONTAINER_ID, true),
                    (BEARD_SUPPORTED_CAROUSEL_CONTAINER_ID, true),
                    (SUPER_CAROUSEL_CONTAINER_ID, true),
                    (SHORT_CAROUSEL_CONTAINER_ID, false),
                    (NODES_ENTITLED_CONTAINER_ID, false),
                    (NODES_UNENTITLED_CONTAINER_ID, false),
                    (PROMOTIONAL_BANNER_CONTAINER_ID, true),
                    (TENTPOLE_HERO_CONTAINER_ID, true),
                ],
                target_gti,
            );
        });
    }

    #[test]
    fn test_remove_gti_from_all_containers() {
        launch_only_app_context(|ctx: AppContext| {
            let model = mock_response(&ctx);

            // The Boys appears in multiple containers
            let target_gti = THE_BOYS_GTI;

            assert_is_gti_present_in_containers(
                model,
                &[
                    (HERO_CAROUSEL_CONTAINER_ID, true),
                    (STANDARD_CAROUSEL_CONTAINER_ID, true),
                    (CONTINUE_WATCHING_CONTAINER_ID, true),
                    (CHARTS_CAROUSEL_CONTAINER_ID, true),
                    (BEARD_SUPPORTED_CAROUSEL_CONTAINER_ID, true),
                    (SUPER_CAROUSEL_CONTAINER_ID, true),
                    (SHORT_CAROUSEL_CONTAINER_ID, false),
                    (NODES_ENTITLED_CONTAINER_ID, false),
                    (NODES_UNENTITLED_CONTAINER_ID, false),
                    (PROMOTIONAL_BANNER_CONTAINER_ID, true),
                    (TENTPOLE_HERO_CONTAINER_ID, true),
                ],
                target_gti,
            );

            // Remove the GTI from all containers
            remove_gti_from_containers(model, target_gti);

            assert_is_gti_present_in_containers(
                model,
                &[
                    (STANDARD_CAROUSEL_CONTAINER_ID, false),
                    (CONTINUE_WATCHING_CONTAINER_ID, false),
                    (CHARTS_CAROUSEL_CONTAINER_ID, true), // Not supported per product decision
                    (BEARD_SUPPORTED_CAROUSEL_CONTAINER_ID, false),
                    (SUPER_CAROUSEL_CONTAINER_ID, false),
                    (SHORT_CAROUSEL_CONTAINER_ID, false),
                    (NODES_ENTITLED_CONTAINER_ID, false),
                    (NODES_UNENTITLED_CONTAINER_ID, false),
                    // Not implemented yet
                    (HERO_CAROUSEL_CONTAINER_ID, true),
                    (PROMOTIONAL_BANNER_CONTAINER_ID, true),
                    (TENTPOLE_HERO_CONTAINER_ID, true),
                ],
                target_gti,
            );
        });
    }

    #[test]
    fn test_empty_container_removal() {
        launch_only_app_context(|ctx: AppContext| {
            let page_response = mock_response(&ctx);

            // Use the continue watching container directly
            let container_id = CONTINUE_WATCHING_CONTAINER_ID;

            // Get all GTIs from the container
            let gtis = page_response.with_untracked(|model| {
                let container_idx =
                    find_container_by_id(model, container_id).expect("Container should exist");
                let container = &model.container_list.get_untracked()[container_idx];

                container.model.with_untracked(|model| match model {
                    ContainerModel::StandardCarousel(carousel) => {
                        carousel.with_untracked(|model| {
                            model.items.with_untracked(|items| {
                                items
                                    .iter()
                                    .filter_map(|item| {
                                        let data: RwSignal<CommonCarouselCardMetadata> =
                                            item.into();
                                        data.with_untracked(|data| data.gti.clone())
                                    })
                                    .collect::<Vec<String>>()
                            })
                        })
                    }
                    _ => Vec::new(),
                })
            });

            assert!(!gtis.is_empty(), "Container should have items with GTIs");
            println!("Found {} items in continue watching container", gtis.len());

            // Container exists before removal
            let container_exists_before = page_response
                .with_untracked(|model| find_container_by_id(model, container_id).is_some());
            assert!(
                container_exists_before,
                "Container should exist before removal"
            );

            // Remove each item one by one
            for (i, gti) in gtis.iter().enumerate() {
                remove_gti_from_container(page_response, gti, container_id);

                // Check if container still exists
                let container_exists = page_response
                    .with_untracked(|model| find_container_by_id(model, container_id).is_some());

                // Container should exist until the last item is removed
                if i < gtis.len() - 1 {
                    assert!(
                        container_exists,
                        "Container should still exist after removing item {}/{}",
                        i + 1,
                        gtis.len()
                    );
                } else {
                    assert!(
                        !container_exists,
                        "Container should be removed after removing the last item"
                    );
                }
            }
        });
    }
}
