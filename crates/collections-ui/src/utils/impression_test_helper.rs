use crate::reporting::onboarding_impressions_reporter::ImpressionsSender;
use cross_app_events::ImpressionData;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::prelude::{provide_context, Scope};
use std::cell::RefCell;
use std::rc::Rc;

#[derive(Debug, <PERSON>lone)]
pub struct ImpressionTestHelper {
    view_impressions_sent: Rc<RefCell<Vec<(ViewImpressionData, ImpressionData)>>>,
    select_impressions_sent: Rc<RefCell<Vec<ImpressionData>>>,
    highlight_impressions_sent: Rc<RefCell<Vec<ImpressionData>>>,
}

impl ImpressionTestHelper {
    pub fn new() -> Self {
        ImpressionTestHelper {
            view_impressions_sent: Rc::new(RefCell::new(Vec::new())),
            select_impressions_sent: Rc::new(RefCell::new(Vec::new())),
            highlight_impressions_sent: Rc::new(RefCell::new(Vec::new())),
        }
    }

    pub fn setup_mocks(&self, scope: Scope) {
        let view_context = ImpressionsSender::send_view_impression_event_context();
        let view_impressions_sent = self.view_impressions_sent.clone();
        view_context.expect().times(..).returning_st(
            move |_, view_impression_data: ViewImpressionData, impression_data: ImpressionData| {
                view_impressions_sent
                    .borrow_mut()
                    .push((view_impression_data.clone(), impression_data.clone()));
            },
        );
        provide_context(scope, Rc::new(view_context));

        let select_context = ImpressionsSender::send_select_impression_event_context();
        let select_impressions_sent = self.select_impressions_sent.clone();
        select_context
            .expect()
            .times(..)
            .returning_st(move |_, data| {
                select_impressions_sent.borrow_mut().push(data.clone());
            });
        provide_context(scope, Rc::new(select_context));

        let highlight_context = ImpressionsSender::send_highlight_impression_event_context();
        let highlight_impressions_sent = self.highlight_impressions_sent.clone();
        highlight_context
            .expect()
            .times(..)
            .returning_st(move |_, data| {
                highlight_impressions_sent.borrow_mut().push(data.clone());
            });

        provide_context(scope, Rc::new(highlight_context));
    }

    pub fn assert_view_impression_was_sent_for_ref_marker(
        &self,
        ref_marker: &str,
        expected_impression_data: ImpressionData,
    ) {
        let view_impressions = self.view_impressions_sent.borrow();
        let impression_data = view_impressions
            .iter()
            .map(|(_, impression)| impression)
            .find(|impression| {
                impression
                    .ref_marker
                    .as_ref()
                    .is_some_and(|x| x == ref_marker)
            })
            .expect(
                format!(
                    "No impression with ref_marker: {ref_marker} was sent, sent: {:?}",
                    view_impressions
                )
                .as_str(),
            );

        assert_eq!(*impression_data, expected_impression_data);
    }

    pub fn get_view_impressions_count(&self) -> usize {
        let view_impressions = self.view_impressions_sent.borrow();
        view_impressions.iter().len()
    }

    pub fn assert_select_impression_was_sent(
        &self,
        ref_marker: &str,
        expected_data: ImpressionData,
    ) {
        let select_impressions = self.select_impressions_sent.borrow();
        let impression_data = select_impressions
            .iter()
            .find(|&data| data.ref_marker == Some(ref_marker.to_string()))
            .expect(format!(
                "No select impression with ref_marker: {ref_marker} was sent. Impressions sent were: {:?}", select_impressions
            ).as_str(), );
        assert_eq!(*impression_data, expected_data);
    }

    pub fn assert_highlight_impression_was_sent(
        &self,
        ref_marker: &str,
        expected_data: ImpressionData,
    ) {
        let highlight_impressions = self.highlight_impressions_sent.borrow();
        let impression_data = highlight_impressions
            .iter()
            .find(|&data| data.ref_marker == Some(ref_marker.to_string()))
            .expect(
                format!(
                    "No highlight impression with ref_marker: {ref_marker} was sent. Impressions sent were: {:?}",
                    highlight_impressions
                )
                    .as_str(),
            );
        assert_eq!(*impression_data, expected_data);
    }
}
