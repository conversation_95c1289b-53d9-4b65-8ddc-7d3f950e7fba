#[cfg(test)]
mod tests {
    use crate::ui::my_stuff_empty_page::*;
    use fableous::{
        buttons::primary_button::PRIMARY_BUTTON_TEST_ID, typography::typography::TYPOGRAPHY_TEST_ID,
    };
    use ignx_compositron::compose;
    use ignx_compositron::test_utils::assert_node_exists;
    use ignx_compositron::{app::launch_test, prelude::provide_context};
    use location::PageType;
    use router::{MockRouting, RoutingContext};
    use std::rc::Rc;

    #[test]
    fn renders() {
        launch_test(
            |ctx| {
                compose! {
                    MyStuffEmptyCollectionsPage()
                }
            },
            |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let page = tree.find_by_test_id("empty-my-stuff-collections");
                let go_home_button = tree.find_by_test_id(PRIMARY_BUTTON_TEST_ID);
                let text_typographies = tree.find_by_props().test_id(TYPOGRAPHY_TEST_ID).find_all();
                assert_node_exists!(&page);
                assert_node_exists!(&go_home_button);
                assert_eq!(text_typographies.len(), 3); // title, message, button text
            },
        )
    }

    #[test]
    fn should_navigate_to_home_collections_on_button_selection() {
        launch_test(
            |ctx| {
                let mut router = MockRouting::default();
                router.expect_navigate().times(1).returning(|loc, source| {
                    assert_eq!(
                        loc.pageType,
                        PageType::Rust(location::RustPage::RUST_COLLECTIONS)
                    );
                    assert_eq!(source, "MY_STUFF_EMPTY_PAGE");
                });
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(router));

                compose! {
                    MyStuffEmptyCollectionsPage()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let go_home_button = node_tree.find_by_test_id(PRIMARY_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(go_home_button.borrow_props().node_id);
                test_game_loop.tick_until_done();
            },
        )
    }
}
