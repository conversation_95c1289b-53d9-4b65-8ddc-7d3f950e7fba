use amzn_fable_tokens::FableColor;
use containers::standard_carousel_sig::STANDARD_CAROUSEL_SPACING;
use fableous::animations::{FableMotionDuration, MotionDuration};
use fableous::cards::sizing::{CardBorderRadius, CardDimensions, CardSize};
use fableous::utils::get_ignx_color;
use ignx_compositron::focus::FocusEdges;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::on_cleanup;
use ignx_compositron::{compose, Composer};
use std::rc::Rc;

#[Composer]
pub fn CollectionPageSkeleton(ctx: &AppContext) -> StackComposable {
    let (opacity, set_opacity) = create_signal(ctx.scope(), 0.1);
    let task_guard = ctx.schedule_repeating_task(FableMotionDuration::Slow.to_duration(), {
        let ctx = ctx.clone();
        move || {
            let opacity = if opacity.try_get_untracked() == Some(0.1) {
                0.2
            } else {
                0.1
            };
            ctx.with_animation(
                Animation::default().with_duration(FableMotionDuration::Slow.to_duration()),
                move || {
                    set_opacity.try_set(opacity);
                },
            );
        }
    });
    let task_id = task_guard.task_id;
    // Not actually used, but keep task_guard alive otherwise the repeating task will stop
    provide_context(ctx.scope, Rc::new(task_guard));

    on_cleanup(ctx.scope, {
        let ctx = ctx.clone();
        move || {
            ctx.cancel_task(task_id);
        }
    });

    compose! {
        Stack() {
            Column() {
                StandardHeroSkeleton()

                Rectangle()
                .width(10.0)
                .height(72.0)
                .opacity(0.0)

                Column() {
                    StandardCarouselSkeleton()
                    StandardCarouselSkeleton()
                }
                .main_axis_alignment(MainAxisAlignment::SpacedBy(64.0))
            }
            .opacity(opacity)
            .padding(Padding::new(144.0, 0.0, 253.0, 0.0))
            .focusable()
            .focus_hierarchical_container(NavigationStrategy::Vertical)
            .blocked_focus_edges(FocusEdges::TOP |  FocusEdges::BOTTOM | FocusEdges::START | FocusEdges::END)
            .test_id("collections-page-skeleton")
        }
    }
}

#[Composer]
pub fn StandardHeroSkeleton(ctx: &AppContext) -> impl Composable<'static> {
    compose! {
        Column() {
            Rectangle()
            .width(300.0)
            .height(33.0)
            .border_radius(12.0)
            .background_color(get_ignx_color(FableColor::PRIMARY))

            Rectangle()
            .width(10.0)
            .height(24.0)
            .opacity(0.0)

            Rectangle()
            .width(500.0)
            .height(120.0)
            .border_radius(16.0)
            .background_color(get_ignx_color(FableColor::PRIMARY))

            Rectangle()
            .width(10.0)
            .height(24.0)
            .opacity(0.0)

            Rectangle()
            .width(300.0)
            .height(33.0)
            .border_radius(12.0)
            .background_color(get_ignx_color(FableColor::PRIMARY))

            Rectangle()
            .width(10.0)
            .height(21.0)
            .opacity(0.0)

            Rectangle()
            .width(246.0)
            .height(86.0)
            .border_radius(12.0)
            .background_color(get_ignx_color(FableColor::PRIMARY))
        }
        .test_id("standard_hero-skeleton")
    }
}

#[Composer]
pub fn StandardCarouselSkeleton(ctx: &AppContext) -> impl Composable<'static> {
    compose! {
        Row() {
            CardSkeleton()
            CardSkeleton()
            CardSkeleton()
            CardSkeleton()
            CardSkeleton()
        }
        .main_axis_alignment(MainAxisAlignment::SpacedBy(STANDARD_CAROUSEL_SPACING))
    }
}

#[Composer]
pub fn CardSkeleton(ctx: &AppContext) -> impl Composable<'static> {
    let card_size = CardSize::Standard;
    let card_dimensions: CardDimensions = card_size.into();
    let border_radius: CardBorderRadius = card_size.into();
    let border_radius: f32 = border_radius.into();
    compose! {
        Rectangle()
        .width(card_dimensions.width)
        .height(card_dimensions.height)
        .border_radius(border_radius)
        .background_color(get_ignx_color(FableColor::PRIMARY))
        .test_id("standard-carousel-card-skeleton")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::test_utils::assert_node_exists;

    #[test]
    fn render_skeleton() {
        ignx_compositron::app::launch_test(
            |ctx| {
                compose! {
                    CollectionPageSkeleton()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let collections_page_skeleton =
                    node_tree.find_by_test_id("collections-page-skeleton");
                assert_node_exists!(collections_page_skeleton);

                let standard_hero_skeleton = node_tree.find_by_test_id("standard_hero-skeleton");
                assert_node_exists!(standard_hero_skeleton);

                let card_skeleton = node_tree
                    .find_by_props()
                    .test_id("standard-carousel-card-skeleton")
                    .find_all();
                assert_eq!(card_skeleton.len(), 10);
            },
        )
    }

    #[test]
    fn should_be_focusable() {
        ignx_compositron::app::launch_test(
            |ctx| {
                compose! {
                    CollectionPageSkeleton()
                }
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let collections_page_skeleton =
                    node_tree.find_by_test_id("collections-page-skeleton");
                assert_node_exists!(&collections_page_skeleton);
                assert!(collections_page_skeleton.borrow_props().is_focused);
            },
        )
    }
}
