use crate::network::types::FocusPosition;
use crate::ui::helpers::title_details_and_media_background::clear_title_details_and_media_background;
use crate::ui::page_ui_sig::{
    get_on_item_highlight, get_on_item_selected, get_on_item_view, handle_on_back_press,
    ItemSelectPageSignals,
};
use crate::ui::TTI_PART_CONTAINERS;
use container_types::ui_signals::{CarouselIndexData, EntityCarouselModel, SportsCardData};
use containers::entity_carousel_sig::*;
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use ignx_compositron::animation::Animation;
use ignx_compositron::column::ColumnComposable;
use ignx_compositron::compose_option;
use ignx_compositron::context::AppContext;
use ignx_compositron::list::{<PERSON>vo<PERSON>, ScrollTo};
use ignx_compositron::prelude::*;
use media_background::types::MediaBackgroundType;
use std::rc::Rc;
use title_details::core::TitleDetailsChangeRequest;

use common_transform_types::actions::TransitionAction;
use container_item_types::traits::toggle_favorite::{
    Domain, FavoriteItemMetadata, FavoriteItemType,
};
use explicit_signals_service::prelude::ExplicitSignalsServiceRequest;
#[cfg(test)]
use explicit_signals_service::test_utils::MockNetworkClient as NetworkClient;
use fableous::cards::sizing::CardDimensions;
#[cfg(not(test))]
use network::NetworkClient;
use sports_favorites_utils::toggle_favorite_helpers::ToggleFavorite;

pub const ENTITY_CAROUSEL_TOP_OFFSET: f32 = 388.0;
const VERTICAL_CAROUSEL_SPACING: f32 = 24.0;

struct EntityCarouselNetworkHandler {
    scope: Scope,
    client: Rc<dyn ExplicitSignalsServiceRequest>,
}

impl ToggleFavorite for EntityCarouselNetworkHandler {
    fn scope(&self) -> Scope {
        self.scope
    }

    fn network_client(&self) -> Rc<dyn ExplicitSignalsServiceRequest> {
        self.client.clone()
    }
}

fn try_handle_favorite_action(ctx: &AppContext, item: &SportsCardData) -> bool {
    let handler = EntityCarouselNetworkHandler {
        scope: ctx.scope,
        client: Rc::new(NetworkClient::new(ctx)),
    };
    let metadata = item.metadata.get_untracked();
    let card_ui_data = item.card_ui_data.get_untracked();

    let item_type = match card_ui_data.entity_type.get_untracked().as_deref() {
        Some("TEAM") => Some(FavoriteItemType::Team),
        Some("ORGANIZATION") => Some(FavoriteItemType::League),
        _ => None,
    };

    match (card_ui_data.is_favorited, metadata.action, item_type) {
        (Some(is_favorite), TransitionAction::removeFromFavorites(_), Some(item_type)) => {
            let favorite_item_metadata = Box::new(FavoriteItemMetadata {
                id: metadata.id,
                name: card_ui_data.title.get_untracked(),
                item_type,
                domain: Domain::Sports,
                is_favorite,
            });
            handler.toggle_favorite(favorite_item_metadata);
            true
        }
        _ => false,
    }
}

pub fn entity_carousel_model_to_ui(
    ctx: &AppContext,
    container: &RwSignal<EntityCarouselModel>,
    update_td: WriteSignal<TitleDetailsChangeRequest>,
    update_mb: RwSignal<MediaBackgroundType>,
    scroll_to: RwSignal<ScrollTo>,
    container_idx: u32,
    on_end_reached: Rc<dyn Fn()>,
    set_current_focus: WriteSignal<Option<FocusPosition>>,
    current_focused_container_index: FocusValueSignal<usize>,
    default_focus_index: Signal<usize>,
    page_allows_top_nav: RwSignal<bool>,
    set_focus_carousel_index_data: WriteSignal<CarouselIndexData>,
    tti_registry: Rc<TimeToInteractiveRegistry>,
    focus_ring_namespace: Namespace,
) -> Option<ColumnComposable> {
    let (scope_guard, _) = create_signal(ctx.scope(), ());

    let on_focus = move || {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        clear_title_details_and_media_background(update_td, update_mb);

        // Only set if it's a new index. This is to not override the initial scroll offset
        let curr = scroll_to.try_get_untracked();
        match curr {
            Some(ScrollTo::Index(idx, _, _)) if idx != container_idx => {
                scroll_to.set(ScrollTo::Index(
                    container_idx,
                    Pivot::Custom(ENTITY_CAROUSEL_TOP_OFFSET),
                    Animation::default(),
                ));
            }
            _ => {}
        }
    };

    let scope = ctx.scope;
    let on_back_pressed = Rc::new(move || {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        handle_on_back_press(scope, current_focused_container_index, page_allows_top_nav);
    });

    let on_item_focus = Rc::new(move |index: usize| {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        set_current_focus.set(Some(FocusPosition {
            container: container_idx as usize,
            item: index,
            sub_index: None,
        }));
    });

    let on_item_selected = {
        let ctx = ctx.clone();

        Rc::new(
            move |item: RwSignal<SportsCardData>,
                  _item_idx: usize,
                  card_dimensions: Option<CardDimensions>| {
                if scope_guard.try_get_untracked().is_none() {
                    return;
                }

                // Try to handle favorite action first
                let item_data = item.get_untracked();
                if try_handle_favorite_action(&ctx, &item_data) {
                    return;
                }

                // Handle other item selection cases...
                let signals = ItemSelectPageSignals {
                    scope_guard,
                    update_mb,
                    update_td,
                    update_cm: None,
                    page_opacity: None,
                    is_resiliency_enabled: None,
                };
                get_on_item_selected(&ctx, signals, container_idx)(
                    item,
                    _item_idx,
                    card_dimensions,
                );
            },
        )
    };

    let on_item_long_press = Rc::new(move |_item: RwSignal<SportsCardData>, _item_idx: usize| {
        if scope_guard.try_get_untracked().is_none() {}
        // No-op for entity carousel for now
    });

    let on_item_view = get_on_item_view(ctx, container_idx, None);

    let on_item_highlight = get_on_item_highlight(ctx, container_idx, None);

    let entity_carousel_tti_part = format!("{TTI_PART_CONTAINERS}_{container_idx}");

    // Simplification: Track the first few carousels for TTI calculation.
    // Ideally we should calculate the exact number of carousels that are visible on screen: https://issues.amazon.com/issues/LR-Rust-156
    if container_idx < 3 {
        tti_registry.add_part(entity_carousel_tti_part.clone());
    }

    let on_ready = Rc::new({
        let tti_registry = Rc::clone(&tti_registry);
        move || {
            tti_registry.set_part_ready(entity_carousel_tti_part.clone());
        }
    });

    compose_option! {
        SportEntityCarouselSigUI(
            carousel_data: container.to_owned(),
            on_item_selected,
            on_item_long_press,
            on_item_view,
            on_item_highlight,
            on_back_pressed,
            on_item_focus,
            on_end_reached,
            default_focus_index,
            set_index_data: Some(set_focus_carousel_index_data),
            on_ready,
            focus_ring_namespace
        )
        .on_focus(on_focus)
        .padding(Padding {
            start: 0.0,
            end: 0.0,
            top: 0.0,
            bottom: VERTICAL_CAROUSEL_SPACING
        })
    }
}

#[cfg(test)]
mod try_handle_favorite_action_tests {
    use super::*;
    use common_transform_types::actions::{ClientAction, SwiftAction, TransitionAction};
    use container_types::ui_signals::{
        CommonCarouselCardMetadata, SportsCardData, SportsCardUIData,
    };
    use explicit_signals_service::test_utils::MockNetworkClient;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::reactive::store_value;
    use std::rc::Rc;

    /// Helper function to create a preconfigured mock network client
    fn create_mock_network_client(scope: Scope) {
        let client_context = MockNetworkClient::new_context();
        client_context.expect().returning(|_| {
            let mut mock_client = MockNetworkClient::default();

            // We're not specifically testing the network calls in these tests,
            // so we just set up a default mock that allows any calls
            mock_client
                .expect_call()
                .returning(|_, _, success_callback, _| {
                    // Always succeed for these tests
                    success_callback();
                });

            mock_client
        });
        store_value(scope, client_context);

        let mut mock_client = MockNetworkClient::default();
        mock_client
            .expect_call()
            .returning(|_, _, success_callback, _| {
                success_callback();
            });
        let client = Rc::new(mock_client);
        provide_context::<Rc<dyn ExplicitSignalsServiceRequest>>(scope, client.clone());
    }

    /// Helper function to create a mock sport card for testing
    fn create_mock_sports_card(
        scope: Scope,
        action: TransitionAction,
        is_favorited: Option<bool>,
        entity_type: Option<String>,
    ) -> SportsCardData {
        SportsCardData {
            metadata: create_rw_signal(
                scope,
                CommonCarouselCardMetadata::from_id_and_action("test-id".to_string(), action),
            ),
            card_ui_data: create_rw_signal(
                scope,
                SportsCardUIData {
                    title: create_rw_signal(scope, "Test Title".to_string()),
                    background_image: create_rw_signal(scope, Some("test-image.jpg".to_string())),
                    background_color: create_rw_signal(scope, None),
                    is_favorited: is_favorited.map(|liked| create_rw_signal(scope, liked)),
                    entity_type: create_rw_signal(scope, entity_type),
                    is_see_more_item: Some(false),
                },
            ),
        }
    }

    #[derive(Clone, Debug)]
    struct TestCase {
        action: TransitionAction,
        is_favorited: Option<bool>,
        entity_type: Option<String>,
        expected_result: bool,
        should_toggle_is_favorited: bool,
        description: &'static str,
    }

    fn run_test_case(test_case: TestCase) {
        launch_test(
            move |ctx| {
                let scope = ctx.scope;
                let sports_card = create_mock_sports_card(
                    scope,
                    test_case.action.clone(),
                    test_case.is_favorited,
                    test_case.entity_type.clone(),
                );
                create_mock_network_client(scope);

                let result = try_handle_favorite_action(&ctx, &sports_card);

                let (result_signal, set_result) = create_signal(ctx.scope(), result);
                set_result.set(result);

                provide_context(ctx.scope(), result_signal);
                provide_context(ctx.scope(), sports_card);
                provide_context(ctx.scope(), test_case);

                compose! {
                    Stack() {}
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_once();

                let result_signal = use_context::<ReadSignal<bool>>(scope)
                    .expect("Result signal should be provided");
                let result = result_signal.get();
                let test_case =
                    use_context::<TestCase>(scope).expect("TestCase should be provided");

                assert_eq!(
                    result, test_case.expected_result,
                    "{}",
                    test_case.description
                );

                let sports_card =
                    use_context::<SportsCardData>(scope).expect("SportsCard should be provided");

                if let Some(is_favorited) = test_case.is_favorited {
                    if let Some(is_favorited_signal) =
                        sports_card.card_ui_data.get_untracked().is_favorited
                    {
                        if test_case.should_toggle_is_favorited {
                            assert_ne!(
                                is_favorited_signal.get_untracked(),
                                is_favorited,
                                "is_favorited should be toggled"
                            );
                        } else {
                            assert_eq!(
                                is_favorited_signal.get_untracked(),
                                is_favorited,
                                "is_favorited should remain unchanged"
                            );
                        }
                    }
                }
            },
        );
    }

    fn create_remove_favorites_action() -> TransitionAction {
        TransitionAction::removeFromFavorites(ClientAction {
            text: None,
            refMarker: "test_refmarker".to_string(),
            target: "test_target".to_string(),
        })
    }

    fn create_landing_action() -> TransitionAction {
        TransitionAction::landing(SwiftAction {
            text: None,
            analytics: Default::default(),
            refMarker: "ref_marker".to_string(),
            pageId: "".to_string(),
            pageType: "".to_string(),
            serviceToken: None,
            journeyIngressContext: Some("TestContext".to_string()),
        })
    }

    #[test]
    fn test_handle_toggle_favorite_when_match_all_conditions() {
        let test_case = TestCase {
            action: create_remove_favorites_action(),
            is_favorited: Some(true),
            expected_result: true,
            entity_type: Some("TEAM".to_string()),
            should_toggle_is_favorited: true,
            description: "Should handle removeFromFavorites action",
        };
        run_test_case(test_case);
    }

    #[test]
    fn test_with_different_entity_types() {
        let test_cases = vec![
            (
                Some("TEAM".to_string()),
                true,
                "Should handle when entity_type is TEAM",
            ),
            (
                Some("ORGANIZATION".to_string()),
                true,
                "Should handle when entity_type is ORGANIZATION",
            ),
            (
                Some("STATION".to_string()),
                false,
                "Should handle when entity_type is STATION",
            ),
            (None, false, "Should not handle when entity_type is None"),
            (
                Some("".to_string()),
                false,
                "Should not handle when entity_type is empty string",
            ),
            (
                Some("Invalid".to_string()),
                false,
                "Should not handle when entity_type is invalid",
            ),
        ];

        for (entity_type, expected_result, description) in test_cases {
            let test_case = TestCase {
                action: create_remove_favorites_action(),
                is_favorited: Some(true),
                entity_type,
                expected_result,
                should_toggle_is_favorited: expected_result,
                description,
            };
            run_test_case(test_case);
        }
    }

    #[test]
    fn test_with_non_remove_favorite_action() {
        let test_case = TestCase {
            action: create_landing_action(),
            is_favorited: Some(true),
            entity_type: Some("Team".to_string()),
            expected_result: false,
            should_toggle_is_favorited: false,
            description: "Should not handle non-favorite action",
        };
        run_test_case(test_case);
    }

    #[test]
    fn test_without_is_favorited_value() {
        let test_case = TestCase {
            action: create_remove_favorites_action(),
            is_favorited: None,
            entity_type: Some("Team".to_string()),
            expected_result: false,
            should_toggle_is_favorited: false,
            description: "Should not handle when is_favorited is None",
        };
        run_test_case(test_case);
    }
}
