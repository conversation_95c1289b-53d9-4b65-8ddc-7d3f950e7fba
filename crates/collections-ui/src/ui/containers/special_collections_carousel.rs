#![allow(
    clippy::clone_on_copy,
    reason = "Can't apply line macro inside compose, clone is required in this case"
)]

use amzn_fable_tokens::FableSpacing;
use app_events::collection::hero::report_hero_action_button_pressed;
use container_types::ui_signals::{
    CarouselIndexData, SpecialCollectionsCardModel, SpecialCollectionsCarouselModel,
    SpecialCollectionsDetailsModel,
};
use containers::special_collections_carousel::special_collections_carousel::*;
use contextual_menu_types::prelude::{ContextualMenuData, ContextualMenuMetadata};
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use fableous::cards::sizing::CardDimensions;
use ignx_compositron::{compose_option, prelude::*};
use media_background::types::MediaBackgroundType;
use std::rc::Rc;
use title_details::core::TitleDetailsChangeRequest;

use crate::{
    network::types::FocusPosition,
    ui::{
        helpers::title_details_and_media_background::clear_title_details,
        page_ui_sig::{
            get_on_item_highlight, get_on_item_selected, get_on_item_view, handle_on_back_press,
            prepare_contextual_menu_telemetry, ItemSelectPageSignals,
            SPECIAL_COLLECTIONS_CAROUSEL_TOP_OFFSET,
        },
        TTI_PART_CONTAINERS,
    },
    utils::contextual_menu::create_contextual_menu_data,
};

const SPECIAL_COLLECTIONS_CAROUSEL_VERTICAL_PADDING: f32 = FableSpacing::SPACING600;

pub(crate) fn special_collections_carousel_model_to_ui(
    ctx: &AppContext,
    container: &RwSignal<SpecialCollectionsCarouselModel>,
    update_td: WriteSignal<TitleDetailsChangeRequest>,
    update_mb: RwSignal<MediaBackgroundType>,
    update_cm: WriteSignal<Option<ContextualMenuData>>,
    scroll_to: RwSignal<ScrollTo>,
    container_idx: u32,
    current_focus: RwSignal<Option<FocusPosition>>,
    set_focus_carousel_index_data: WriteSignal<CarouselIndexData>,
    current_focused_container_index: FocusValueSignal<usize>,
    page_allows_top_nav: RwSignal<bool>,
    focus_ring_namespace: Namespace,
    default_focus_index: Signal<usize>,
    tti_registry: Rc<TimeToInteractiveRegistry>,
) -> Option<ColumnComposable> {
    let (scope_guard, _) = create_signal(ctx.scope(), ());

    let scope = ctx.scope;
    let on_back_pressed = Rc::new(move || {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        handle_on_back_press(scope, current_focused_container_index, page_allows_top_nav)
    });

    let on_item_focus = Rc::new(move |index| {
        set_focus_carousel_index_data.set(CarouselIndexData {
            scroll_index: index,
            focus_index: index,
            sub_index: None,
        });
        current_focus.set(Some(FocusPosition {
            container: container_idx as usize,
            item: index,
            sub_index: None,
        }));
    });

    let page_signals = ItemSelectPageSignals {
        scope_guard,
        update_mb,
        update_td,
        update_cm: Some(update_cm),
        page_opacity: None,
        is_resiliency_enabled: None,
    };

    let on_item_selected: Rc<
        dyn Fn(RwSignal<SpecialCollectionsCardModel>, usize, Option<CardDimensions>),
    > = get_on_item_selected(ctx, page_signals.clone(), container_idx);

    let on_cta_selected: Rc<
        dyn Fn(RwSignal<SpecialCollectionsDetailsModel>, usize, Option<CardDimensions>),
    > = get_on_item_selected(ctx, page_signals, container_idx);

    let on_cta_selected = Rc::new({
        let ctx = ctx.clone();
        let on_cta_selected = on_cta_selected.clone();
        let container = *container;
        move |item: RwSignal<SpecialCollectionsDetailsModel>,
              item_idx: usize,
              dimensions: Option<CardDimensions>| {
            // TODO: To be removed with Clickstream refactoring where Clickstream is reported in the page level select handler
            report_hero_action_button_pressed(
                ctx.scope(),
                item.with_untracked(|item| item.cta_button_action.try_get().flatten()),
                item_idx,
                container.with_untracked(|container| {
                    container
                        .common_carousel_metadata
                        .with_untracked(|metadata| metadata.analytics.get_untracked())
                }),
                container_idx as usize,
            );
            on_cta_selected(item, item_idx, dimensions);
        }
    });

    let on_item_long_press = Rc::new({
        let ctx = ctx.clone();
        move |item: RwSignal<SpecialCollectionsCardModel>, _item_idx: usize| {
            if scope_guard.try_get_untracked().is_none() {
                return;
            }
            prepare_contextual_menu_telemetry(ctx.clone());
            item.with_untracked(|item| {
                let data_signal: RwSignal<ContextualMenuMetadata> = item.into();
                update_cm.set(create_contextual_menu_data(
                    data_signal.get_untracked(),
                    &ctx,
                    Default::default(),
                ));
            });
        }
    });

    let special_collections_carousel_tti_part = format!("{TTI_PART_CONTAINERS}_{container_idx}");

    // Simplification: Track the first few carousels for TTI calculation.
    // Ideally we should calculate the exact number of carousels that are visible on screen: https://issues.amazon.com/issues/LR-Rust-156
    if container_idx < 3 {
        tti_registry.add_part(special_collections_carousel_tti_part.clone());
    }
    let on_ready = Rc::new({
        let tti_registry = Rc::clone(&tti_registry);
        move || {
            tti_registry.set_part_ready(special_collections_carousel_tti_part.clone());
        }
    });

    let mb_data = container
        .get_untracked()
        .special_collections_carousel_metadata
        .get_untracked()
        .media_background_data
        .get_untracked();

    let on_container_focus = move || {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        clear_title_details(update_td);
        update_mb.set(MediaBackgroundType::None);
        update_mb.set(mb_data.clone());

        // Only set if it's a new index. This is to not override the initial scroll offset, which is not animated
        let curr = scroll_to.try_get_untracked();
        match curr {
            Some(ScrollTo::Index(idx, _, _)) if idx != container_idx => {
                scroll_to.set(ScrollTo::Index(
                    container_idx,
                    Pivot::Custom(SPECIAL_COLLECTIONS_CAROUSEL_TOP_OFFSET),
                    Animation::default(),
                ));
            }
            _ => {}
        }
    };

    let container_focused = Signal::derive(scope, move || {
        if let ScrollTo::Index(idx, ..) = scroll_to.get() {
            idx == container_idx
        } else {
            false
        }
    });

    compose_option! {
        SpecialCollectionsCarouselUI(
            carousel_data: container.clone(),
            on_item_selected,
            on_item_long_press,
            on_item_view: get_on_item_view(ctx, container_idx, None),
            on_item_highlight: get_on_item_highlight(ctx, container_idx, None),
            on_cta_selected,
            on_back_pressed,
            on_item_focus,
            default_focus_index,
            set_index_data: Some(set_focus_carousel_index_data),
            on_ready,
            focus_ring_namespace,
            container_focused
        )
        .on_focus(on_container_focus)
        .padding(Padding::vertical(SPECIAL_COLLECTIONS_CAROUSEL_VERTICAL_PADDING))
    }
}
