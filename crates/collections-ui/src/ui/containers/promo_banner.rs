use crate::network::types::FocusPosition;
use crate::ui::helpers::title_details_and_media_background::clear_title_details_and_media_background;
use crate::ui::page_ui_sig::{
    get_on_item_highlight, get_on_item_selected, get_on_item_view, handle_on_back_press,
    prepare_contextual_menu_telemetry, ItemSelectPageSignals,
};
use crate::utils::contextual_menu::{create_contextual_menu_data, CreateContextualMenuOptions};
use app_events::collection::hero::report_hero_action_button_pressed;
use container_types::ui_signals::{PromoBannerButtonData, PromoBannerModel, PromoItemModel};
use containers::promotional_banner::*;
use contextual_menu_types::prelude::ContextualMenuData;
use fableous::cards::sizing::CardDimensions;
use hero::buttons::ButtonVariant;
use ignx_compositron::compose_option;
use ignx_compositron::prelude::*;
use media_background::types::MediaBackgroundType;
use std::rc::Rc;
use title_details::core::TitleDetailsChangeRequest;

// TODO: Refactor page_ui_sig to extract these constants
const VERTICAL_CAROUSEL_SPACING: f32 = 24.0;

const PROMOTIONAL_BANNER_TOP_OFFSET: f32 = 240.0;

pub fn promo_banner_model_to_ui(
    ctx: &AppContext,
    container: &RwSignal<PromoBannerModel>,
    update_td: WriteSignal<TitleDetailsChangeRequest>,
    update_mb: RwSignal<MediaBackgroundType>,
    update_cm: WriteSignal<Option<ContextualMenuData>>,
    scroll_to: RwSignal<ScrollTo>,
    container_idx: u32,
    set_current_focus: WriteSignal<Option<FocusPosition>>,
    current_focused_container_index: FocusValueSignal<usize>,
    page_allows_top_nav: RwSignal<bool>,
    left_padding: MaybeSignal<f32>,
    is_resiliency_enabled: ReadSignal<bool>,
) -> Option<ColumnComposable> {
    let (scope_guard, _) = create_signal(ctx.scope(), ());

    let on_item_focus = Rc::new(move |_: &PromoItemModel| {
        // Only set if it's a new index. This is to not override the initial scroll offset, which is not animated
        let curr = scroll_to.try_get_untracked();
        match curr {
            Some(ScrollTo::Index(idx, _, _)) if idx != container_idx => {
                scroll_to.set(ScrollTo::Index(
                    container_idx,
                    Pivot::Custom(PROMOTIONAL_BANNER_TOP_OFFSET),
                    Animation::default(),
                ));
                set_current_focus.set(Some(FocusPosition {
                    container: container_idx as usize,
                    item: 0,
                    sub_index: None,
                }));
                clear_title_details_and_media_background(update_td, update_mb);
            }
            _ => {}
        }
    });

    let on_item_selected = {
        let ctx = ctx.clone();
        get_on_item_selected(
            &ctx,
            ItemSelectPageSignals {
                scope_guard,
                update_mb,
                update_td,
                update_cm: Some(update_cm),
                page_opacity: None,
                is_resiliency_enabled: Some(is_resiliency_enabled),
            },
            container_idx,
        )
    };

    let on_item_selected = Rc::new({
        let ctx = ctx.clone();
        let on_item_selected = on_item_selected.clone();
        let container = *container;
        move |item: RwSignal<PromoBannerButtonData>,
              item_idx: usize,
              dimensions: Option<CardDimensions>| {
            // TODO: To be removed with Clickstream refactoring where Clickstream is reported in the page level select handler
            report_hero_action_button_pressed(
                ctx.scope(),
                Some(item.with_untracked(|item| item.common_carousel_card_metadata.action.clone())),
                item_idx,
                container.with_untracked(|container| {
                    container
                        .common_carousel_metadata
                        .with_untracked(|metadata| metadata.analytics.get_untracked())
                }),
                container_idx as usize,
            );
            on_item_selected(item, item_idx, dimensions);
        }
    });
    let on_item_view = get_on_item_view(ctx, container_idx, None);
    let on_item_highlight = get_on_item_highlight(ctx, container_idx, None);

    let scope = ctx.scope;
    let on_back_press = Rc::new(move || {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        handle_on_back_press(scope, current_focused_container_index, page_allows_top_nav);
    });

    let on_item_long_press = Rc::new({
        let ctx = ctx.clone();
        move |item: PromoItemModel| {
            if scope_guard.try_get_untracked().is_none() {
                return;
            }

            if let Some(contextual_menu_data) = item.contextual_menu_metadata.try_get_untracked() {
                prepare_contextual_menu_telemetry(ctx.clone());
                update_cm.set(create_contextual_menu_data(
                    contextual_menu_data,
                    &ctx,
                    CreateContextualMenuOptions {
                        button: Some(ButtonVariant::Primary),
                        container_supports_hiding: true,
                        ..Default::default()
                    },
                ));
            }
        }
    });

    let promo_banner = compose_option! {
        PromotionalBannerUI(
            container: container.to_owned(),
            on_item_focus: on_item_focus,
            on_item_selected,
            on_item_view,
            on_item_highlight,
            on_item_long_press,
            left_padding,
            rtl_enabled: false,
            on_back_pressed: on_back_press,
        )
        .padding(Padding{ start:0.0, end: 0.0, top: 0.0, bottom: VERTICAL_CAROUSEL_SPACING })
    };

    if promo_banner.is_some() {
        metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => "PromotionalBannerContainer", "actionName" => "Mounted");
    }
    promo_banner
}
