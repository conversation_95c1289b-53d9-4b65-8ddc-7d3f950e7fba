use crate::network::types::FocusPosition;
use crate::ui::helpers::title_details_and_media_background::{
    clear_media_background, clear_title_details, convert_to_title_details_change_request,
};
use crate::ui::page_ui_sig::{
    get_on_item_highlight, get_on_item_selected, get_on_item_view, handle_on_back_press,
    prepare_contextual_menu_telemetry, ItemSelectPageSignals, VERTICAL_CAROUSEL_SPACING,
};
use crate::ui::TTI_PART_CONTAINERS;
use crate::utils::contextual_menu::{create_contextual_menu_data, CreateContextualMenuOptions};
use container_types::ui_signals::{
    CarouselIndexData, CarouselSanitizedTitle, ScheduleCardItemContent, ScheduleCardItemType,
    ScheduleCarouselData, ScheduleCarouselModel,
};
use containers::schedule_carousel_sig::*;
use contextual_menu_types::prelude::{ContextualMenuData, ContextualMenuMetadata};
use cross_app_events::tti_registry::TimeToInteractiveRegistry;
use ignx_compositron::animation::Animation;
use ignx_compositron::column::ColumnComposable;
use ignx_compositron::compose_option;
use ignx_compositron::context::AppContext;
use ignx_compositron::focus::FocusDirection;
use ignx_compositron::list::{Pivot, ScrollTo};
use ignx_compositron::prelude::*;
use media_background::types::MediaBackgroundType;
use std::rc::Rc;
use title_details::core::TitleDetailsChangeRequest;
use title_details::types::common::StandardTitleDetailsData;
use watch_modal::data_provider::should_show_watch_modal;
use watch_modal::helpers::event_card::create_watch_modal_data_for_event_cards;

pub fn schedule_carousel_model_to_ui(
    ctx: &AppContext,
    opacity: RwSignal<f32>,
    container: &RwSignal<ScheduleCarouselModel>,
    update_td: WriteSignal<TitleDetailsChangeRequest>,
    update_mb: RwSignal<MediaBackgroundType>,
    update_cm: WriteSignal<Option<ContextualMenuData>>,
    scroll_to: RwSignal<ScrollTo>,
    container_idx: u32,
    on_end_reached: Rc<dyn Fn()>,
    current_focus: RwSignal<Option<FocusPosition>>,
    current_focused_container_index: FocusValueSignal<usize>,
    default_focus_index: Signal<usize>,
    page_allows_top_nav: RwSignal<bool>,
    set_focus_carousel_index_data: WriteSignal<CarouselIndexData>,
    tti_registry: Rc<TimeToInteractiveRegistry>,
    focus_ring_namespace: Namespace,
    is_resiliency_enabled: ReadSignal<bool>,
) -> Option<ColumnComposable> {
    let (scope_guard, _) = create_signal(ctx.scope(), ());

    let on_item_focus = Rc::new(
        move |item: ScheduleCarouselData, direction: Option<FocusDirection>, idx: usize| {
            if scope_guard.try_get_untracked().is_none() {
                return;
            }
            match item {
                ScheduleCarouselData::Card(data) => {
                    let item_content: RwSignal<ScheduleCardItemContent> =
                        data.with_untracked(|x| x.into());
                    item_content.with_untracked(|item_content| {
                        let title_details_data: RwSignal<StandardTitleDetailsData> =
                            item_content.title_details_data;
                        title_details_data.with_untracked(|data| {
                            let td_data = convert_to_title_details_change_request(data, direction);
                            update_td.set(td_data);
                        });
                        let media_background_data: RwSignal<MediaBackgroundType> =
                            item_content.media_background_data;
                        media_background_data.with_untracked(|data| {
                            update_mb.set(data.clone());
                        });
                    })
                }
                ScheduleCarouselData::Button(_) => {
                    clear_title_details(update_td);
                    clear_media_background(update_mb);
                }
            }
            current_focus.set(Some(FocusPosition {
                container: container_idx as usize,
                item: idx,
                sub_index: None,
            }));
        },
    );

    let on_focus = move || {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        let curr = scroll_to.try_get_untracked();
        match curr {
            Some(ScrollTo::Index(idx, _, _)) if idx != container_idx => {
                scroll_to.set(ScrollTo::Index(
                    container_idx,
                    Pivot::Custom(crate::ui::page_ui_sig::SCHEDULE_CAROUSEL_TOP_OFFSET),
                    Animation::default(),
                ));
            }
            _ => {}
        }
    };

    let on_item_highlight = get_on_item_highlight(ctx, container_idx, None);

    let on_item_view = get_on_item_view(ctx, container_idx, None);

    let on_item_selected = get_on_item_selected(
        ctx,
        ItemSelectPageSignals {
            scope_guard,
            update_mb,
            update_td,
            update_cm: Some(update_cm),
            page_opacity: Some(opacity),
            is_resiliency_enabled: Some(is_resiliency_enabled),
        },
        container_idx,
    );

    let on_item_long_press = Rc::new({
        let ctx = ctx.clone();
        let container_title = container.with_untracked(|model| {
            model
                .schedule_carousel_metadata
                .with_untracked(|metadata| metadata.title.get_untracked().sanitized_title_text())
        });
        move |item: ScheduleCarouselData| {
            if scope_guard.try_get_untracked().is_none() {
                return;
            }
            prepare_contextual_menu_telemetry(ctx.clone());
            let data_signal: RwSignal<ContextualMenuMetadata> = (&item).into();
            let data = data_signal.get_untracked();
            match item {
                ScheduleCarouselData::Card(card_item) => {
                    card_item.with_untracked(|item| match item {
                        ScheduleCardItemType::Event(_) => {
                            if should_show_watch_modal(
                                ctx.scope(),
                                is_resiliency_enabled.with_untracked(|val| *val).into(),
                            ) {
                                update_cm.set(create_watch_modal_data_for_event_cards(
                                    ctx.scope(),
                                    &data,
                                ))
                            } else {
                                update_cm.set(create_contextual_menu_data(
                                    data,
                                    &ctx,
                                    CreateContextualMenuOptions {
                                        container_title: container_title.clone(),
                                        container_supports_hiding: true,
                                        ..Default::default()
                                    },
                                ))
                            }
                        }
                        ScheduleCardItemType::OffPlatform(_) => {
                            // NotSupported For Phase1
                        }
                    })
                }
                ScheduleCarouselData::Button(_) => {
                    update_cm.set(create_contextual_menu_data(
                        data,
                        &ctx,
                        CreateContextualMenuOptions::default(),
                    ));
                }
            }
        }
    });

    let schedule_carousel_tti_part = format!("{TTI_PART_CONTAINERS}_{container_idx}");

    if container_idx < 3 {
        tti_registry.add_part(schedule_carousel_tti_part.clone());
    }
    let on_ready = Rc::new({
        let tti_registry = Rc::clone(&tti_registry);
        move || {
            tti_registry.set_part_ready(schedule_carousel_tti_part.clone());
        }
    });

    let scope = ctx.scope;
    let on_back_pressed = Rc::new(move || {
        if scope_guard.try_get_untracked().is_none() {
            return;
        }
        handle_on_back_press(scope, current_focused_container_index, page_allows_top_nav);
    });

    compose_option! {
        ScheduleCarouselSigUI(
            carousel_data: container.to_owned(),
            on_item_focus,
            on_item_selected,
            on_item_long_press,
            on_item_view,
            on_item_highlight,
            on_end_reached,
            on_back_pressed,
            default_focus_index,
            set_index_data: Some(set_focus_carousel_index_data),
            on_ready,
            focus_ring_namespace,
        )
        .on_focus(on_focus)
        .focus_hierarchical_container(NavigationStrategy::Horizontal)
        .padding(Padding{ start:0.0, end: 0.0, top: 0.0, bottom: VERTICAL_CAROUSEL_SPACING })
    }
}
