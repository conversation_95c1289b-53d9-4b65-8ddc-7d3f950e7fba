use crate::context::onboarding_context::use_onboarding_context;
use crate::reporting::onboarding::use_onboarding_client;
use crate::reporting::onboarding_impressions_reporter::{AugmentedImpressionData, SlotRowCol};
use crate::reporting::types::{MetricActions, MetricEvents, OnboardingMetricContext};
use crate::types::collections_types::{OnboardingPageModel, StartColumn};
use crate::ui::page_ui_sig::safeguard_containers;
use crate::ui::page_ui_sig::VERTICAL_CAROUSEL_SPACING;
use crate::ui::types::ContainerType;
use amzn_fable_tokens::FableText;
use clickstream::client::use_clickstream_client;
use clickstream::events::onboarding::{
    OnboardingPageDoneEvent, OnboardingPageLikeEvent, OnboardingPageRemoveLikeEvent,
    OnboardingPageSpeedBumpDoneEvent, OnboardingPageSpeedBumpLikeEvent,
    OnboardingPageSpeedBumpOpenEvent, OnboardingPageSpeedBumpRemoveLikeEvent,
};
use container_types::ui_signals::{
    CardGridModel, CarouselIndexData, CarouselTitle, ContainerModel, ImpressionsData,
    StandardCardContainerItemType,
};
use containers::card_grid::card_grid_ui_builder::{CardGridUIBuilder, GridScroll};
use containers::card_grid::card_grid_utils::{GRID_CARD_SIZE, GRID_SPACING_V};
use containers::card_grid::overlays::GridCardOverlay;
use containers::title::TITLE_PADDING;
use containers::utils::tts_utils::tts_onboarding_grid_card_hint;
use cross_app_events::app_event::AppEventReporter;
use fableous::cards::sizing::CardDimensions;
use fableous::toasts::toast_context::ToastContext;
use fableous::SCREEN_WIDTH;
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::text::{LocalizedText, TextContent};
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{
    prelude::*,
    reactive::Namespace,
    time::{Duration, Instant},
};
use location::PageType::Rust;
use location::{Location, RustPage::RUST_COLLECTIONS};
use network::{NetworkClient, RequestError};
use onboarding_lifecycle_service::traits::OnboardingLifecycleServiceClientRequest;
use personalization_feedback_service::prelude::{
    PersonalizationFeedbackServiceFeedbackType, PersonalizationFeedbackServiceOperation,
    PersonalizationFeedbackServiceRequest, PersonalizationFeedbackServiceRequestParams,
    PersonalizationFeedbackServiceResponse,
};
use serde_json::Map;
use std::rc::Rc;
use title_reaction_buttons::ui::like_button::{
    LIKE_BUTTON_ANIMATION_REMOVE_TOTAL_DURATION, LIKE_BUTTON_ANIMATION_SET_TOTAL_DURATION,
};
use two_column_page::{
    columns::{container_list_column::*, textual_button_column::*},
    two_column_page::*,
};

pub const START_COLUMN_WIDTH: f32 = 600.0;
pub const ONBOARDING_PAGE: &str = "onboarding-page";
pub const ONBOARDING_GRID_COLUMN_COUNT: usize = 3;
pub const SCROLL_START_ROW_IDX: usize = 3;
const ATV_SONATA_ONBOARDING_COMPLETE: &str = "atv-sonata-onboarding-complete";
const TITLE_HEIGHT: f32 =
    FableText::TYPE_LABEL500_EMPHASIS.line_height as f32 + TITLE_PADDING.bottom + TITLE_PADDING.top;

/// Creates a set of scroll handling functions for managing grid scroll positions
///
/// # Arguments
/// * `scroll_to` - Signal to control scrolling behavior
/// * `container_idx` - Index of the container being managed
///
/// # Returns a tuple of two functions:
/// * First: Main scroll position handler that takes a distance parameter
/// * Second: Handler to reset scroll position to the top of the grid
fn get_scroll_handlers(
    scroll_to: RwSignal<ScrollTo>,
    container_idx: u32,
) -> (Rc<dyn Fn(f32)>, impl Fn() + Clone) {
    let scroll_to_position = Rc::new(move |distance: f32| {
        scroll_to.set(ScrollTo::Index(
            container_idx,
            Pivot::Custom(distance),
            Animation::default(),
        ));
    });

    // Triggered on focus of container and on backpress from item idx x when
    // x > 0 AND the grid has been scrolled
    let position_top_of_grid_to_top_of_page = {
        let scroll_to_position_for_back = scroll_to_position.clone();
        move || scroll_to_position_for_back(0.0)
    };

    (scroll_to_position, position_top_of_grid_to_top_of_page)
}

fn get_item_id(item: RwSignal<StandardCardContainerItemType>) -> String {
    item.get_untracked()
        .get_card_data()
        .get_untracked()
        .metadata
        .get_untracked()
        .id
}

/// Creates a handler function that manages scrolling behavior when grid items receive focus
///
/// # Arguments
/// * `scroll_to_position` - Function to trigger scrolling to a specific position
/// * `card_height_plus_space` - Total height of a card including vertical spacing
/// * `grid_has_scrolled` - Signal tracking whether the grid is currently scrolled
/// * `focused_item_id` - `WriteSignal` tracking which grid item is currently focused
/// * `grid_has_title` - Boolean indicating whether the grid has a title
///
/// # Behavior
/// - Calculates scroll position based on the focused row index and title presence
/// - Resets scroll position to top (0.0) when focus moves upward to the row preceding `SCROLL_START_ROW_IDX` in a titled grid
/// - Otherwise, only triggers scroll when row index is >= `SCROLL_START_ROW_IDX`
/// - Scroll distance is negative (upward scroll) and proportional to rows from start
/// - Updates grid scroll state when scroll conditions change
/// - Updates the currently focused item state
fn get_on_item_focused(
    scroll_to_position: Rc<dyn Fn(f32)>,
    card_height_plus_space: f32,
    grid_has_scrolled: RwSignal<bool>,
    focused_item_id: WriteSignal<Option<String>>,
    grid_has_title: bool,
) -> Rc<dyn Fn(RwSignal<StandardCardContainerItemType>, usize, usize)> {
    Rc::new(move |item, row_idx, _sub_index| {
        // in this case we know the scroll position has been offset to account for the title
        let reset_scroll_for_titled_grid = grid_has_title
            && row_idx == SCROLL_START_ROW_IDX - 1
            && grid_has_scrolled.get_untracked();
        let row_idx_necessitates_scroll = row_idx >= SCROLL_START_ROW_IDX;

        if reset_scroll_for_titled_grid {
            // revert offset to account for title
            scroll_to_position(0.0);
        } else if row_idx_necessitates_scroll {
            let rows_to_scroll = (row_idx - SCROLL_START_ROW_IDX) as f32;
            let mut scroll_distance = -card_height_plus_space * rows_to_scroll;
            // offset scroll to account for title
            if grid_has_title {
                scroll_distance -= TITLE_HEIGHT
            }
            scroll_to_position(scroll_distance);
        }

        let current_scroll_state = grid_has_scrolled.get_untracked();
        if current_scroll_state != row_idx_necessitates_scroll {
            grid_has_scrolled.set(row_idx_necessitates_scroll);
        }

        focused_item_id.set(Some(get_item_id(item)));
    })
}

/// Creates a handler to reset focus to the first item in the first grid (container index 0)
/// Called on back button press when focused on the first item in any grid apart from the first
///
/// # Arguments
/// * `container_index_focus_value` - Signal tracking which container is currently focused
/// * `first_grid_focus_state` - Signal tracking the item focused in the first grid
///
/// # Behavior
/// - Resets focus of the first grid
/// - Moves focus to the first grid
fn reset_page_focus_to_top(
    container_index_focus_value: FocusValueSignal<usize>,
    first_grid_focus_state: RwSignal<Option<String>>,
) {
    first_grid_focus_state.set(None);
    container_index_focus_value.set(0);
}

/// Creates a handler for back button press when focused on non-first items in a grid
///
/// # Arguments
/// * `grid_has_scrolled` - Signal indicating whether the grid is currently scrolled
/// * `position_top_of_grid_to_top_of_page_on_back` - Function to reset grid scroll position
///
/// # Behavior
/// - Only performs scroll reset when grid is in a scrolled state
/// - Leaves focus handling to the `CardGrid` component
/// - Does nothing if grid is already at the top position
fn get_on_back_pressed_internal(
    grid_has_scrolled: RwSignal<bool>,
    position_top_of_grid_to_top_of_page_on_back: impl Fn() + 'static,
) -> Rc<dyn Fn()> {
    Rc::new(move || {
        if grid_has_scrolled.get_untracked() {
            position_top_of_grid_to_top_of_page_on_back()
        }
    })
}

const REACTION_ERROR_MESSAGE: &str = "AV_LRC_REACTION_ERROR_TOAST";
fn display_error_toast(scope: Scope) {
    match use_context::<ToastContext>(scope) {
        Some(toast_context) => {
            toast_context.message.set(Some(TextContent::LocalizedText(
                REACTION_ERROR_MESSAGE.to_string().into(),
            )));
            toast_context.visible.set(true);
        }
        None => log::error!("[onboarding_cx::onboarding_grid] ToastContext not found in scope"),
    }
}

/// Calls the onboarding lifecycle service with success and error callbacks
///
/// # Arguments
/// * `network_client` - Reference to the `NetworkClient` instance
/// * `onboarding_status_submission_failed` - Signal to track submission failure status
///
/// # Behavior
/// * Makes a call to the onboarding lifecycle service
/// * On success: Takes no action (empty callback)
/// * On error: Sets the submission failed flag to true, enabling retry on page exit
fn call_onboarding_lifecycle_service(
    ctx: &AppContext,
    network_client: &NetworkClient,
    onboarding_status_submission_failed: RwSignal<bool>,
) {
    let report_event = |ctx: &AppContext, event: MetricEvents| {
        if let Some(metrics_client) = use_onboarding_client(ctx.scope()) {
            metrics_client.report_page_event(event, None, None);
        }
    };

    network_client.call_onboarding_lifecycle_service(
        {
            let ctx = ctx.clone();
            move |_| {
                report_event(&ctx, MetricEvents::OnboardingCallSucceeded);
            }
        },
        {
            let ctx = ctx.clone();
            move |_| {
                report_event(&ctx, MetricEvents::OnboardingCallFailed);
                // flag to indicate we should retry submission on exit the ocx page
                onboarding_status_submission_failed.set_untracked(true);
            }
        },
    );
}

/// Creates a success callback function for personalization feedback service
///
/// # Arguments
/// * `user_has_reacted` - Signal tracking if user has already reacted
/// * `network_client` - Reference counted `NetworkClient` instance
/// * `selected` - Boolean indicating if item is selected
/// * `onboarding_status_submission_failed` - Signal to track submission failure status
///
/// # Behavior
/// * Checks if this is the user's first reaction and if the item was selected
/// * If both conditions are true:
///   - Updates the user reaction state to indicate interaction
///   - Calls the onboarding lifecycle service to mark onboarding as complete
/// * No action taken if user has already reacted or item was deselected
fn get_personalization_success_callback(
    ctx: AppContext,
    user_has_reacted: RwSignal<bool>,
    network_client: Rc<NetworkClient>,
    selected: bool,
    onboarding_status_submission_failed: RwSignal<bool>,
) -> impl FnOnce(PersonalizationFeedbackServiceResponse) {
    if let Some(metrics_client) = use_onboarding_client(ctx.scope()) {
        metrics_client.report_page_event(MetricEvents::ReactionCallSucceeded, None, None);
    }
    move |_| {
        if !user_has_reacted.get_untracked() && selected {
            user_has_reacted.set_untracked(true);
            // onboarding considered complete if at least one reaction has been successfully recorded
            call_onboarding_lifecycle_service(
                &ctx,
                network_client.as_ref(),
                onboarding_status_submission_failed,
            );
        }
    }
}

/// Creates an error callback function for personalization feedback service
///
/// # Arguments
/// * `ctx` - Application context
/// * `is_card_selected` - Signal tracking card selection state
/// * `prev_selected` - Previous selection state
///
/// # Behavior
/// * Waits for the failed reaction animation to complete using appropriate duration
/// * Schedules a task to execute after animation completion
/// * Displays an error toast to notify the user
/// * Reverts the card selection state to its previous value
fn get_personalization_error_callback(
    ctx: AppContext,
    is_card_selected: RwSignal<bool>,
    prev_selected: bool,
) -> impl FnOnce(RequestError) {
    move |_| {
        let reverting_animation_duration = if prev_selected {
            LIKE_BUTTON_ANIMATION_SET_TOTAL_DURATION
        } else {
            LIKE_BUTTON_ANIMATION_REMOVE_TOTAL_DURATION
        };

        if let Some(metrics_client) = use_onboarding_client(ctx.scope()) {
            metrics_client.report_page_event(MetricEvents::ReactionCallFailed, None, None);
        }

        // allow for the animation resulting from the failed reaction to complete before reverting it
        ctx.schedule_task(
            Instant::now() + Duration::from_millis(reverting_animation_duration),
            {
                let ctx = ctx.clone();
                move || {
                    display_error_toast(ctx.scope());
                    is_card_selected.set(prev_selected);
                }
            },
        );
    }
}

/// Creates an item selection handler function
///
/// # Arguments
/// * `ctx` - Reference to application context
/// * `network_client` - Reference counted `NetworkClient` instance
/// * `user_has_reacted` - Signal tracking if user has already reacted
/// * `onboarding_status_submission_failed` - Signal to track submission failure status
///
/// # Behavior
/// * Toggles the selection state of the card when clicked
/// * Updates the card's visual state immediately
/// * Makes a call to `PersonalizationFeedbackService` with Hide/Unhide operation
/// * On success: Updates user reaction state and calls onboarding lifecycle service if needed
/// * On error: Reverts the card selection and displays an error toast
fn get_on_item_selected(
    ctx: &AppContext,
    network_client: Rc<NetworkClient>,
    user_has_reacted: RwSignal<bool>,
    onboarding_status_submission_failed: RwSignal<bool>,
    is_speedbump: bool,
) -> impl Fn(RwSignal<StandardCardContainerItemType>, usize, usize, Option<CardDimensions>, RwSignal<bool>)
{
    let ctx_clone = ctx.clone();
    move |item, row, col, dimensions, is_card_selected: RwSignal<bool>| {
        let title_id = get_item_id(item);
        let prev_selected = is_card_selected.get_untracked();
        let selected = !prev_selected;
        is_card_selected.set(selected);

        let ctx = ctx_clone.clone();
        let network_client_clone = Rc::clone(&network_client);

        let operation = if selected {
            PersonalizationFeedbackServiceOperation::Hide
        } else {
            PersonalizationFeedbackServiceOperation::Unhide
        };

        let clickstream = use_clickstream_client(ctx.scope());

        match (selected, is_speedbump) {
            (true, true) => clickstream.track(OnboardingPageSpeedBumpLikeEvent {
                page_type_id: title_id.clone(),
            }),
            (true, false) => clickstream.track(OnboardingPageLikeEvent {
                page_type_id: title_id.clone(),
            }),
            (false, true) => clickstream.track(OnboardingPageSpeedBumpRemoveLikeEvent {
                page_type_id: title_id.clone(),
            }),
            (false, false) => clickstream.track(OnboardingPageRemoveLikeEvent {
                page_type_id: title_id.clone(),
            }),
        }

        if let Some(metrics_client) = use_onboarding_client(ctx.scope()) {
            match selected {
                true => {
                    metrics_client.report_page_event(
                        MetricEvents::Select,
                        Some(MetricActions::Like),
                        Some(OnboardingMetricContext::Page),
                    );
                }
                false => {
                    metrics_client.report_page_event(
                        MetricEvents::Select,
                        Some(MetricActions::RemoveLike),
                        Some(OnboardingMetricContext::Page),
                    );
                }
            }

            let idx = row * ONBOARDING_GRID_COLUMN_COUNT + col;
            let impressions_data = item.to_impressions_data().map(|data| {
                AugmentedImpressionData::new(data, SlotRowCol(0, idx as u32), dimensions)
            });
            if let Some(impressions_data) = impressions_data {
                if let Some(onboarding_context) = use_onboarding_context(ctx.scope()) {
                    onboarding_context
                        .borrow()
                        .impressions_reporter
                        .send_select_impression(ctx.scope(), &impressions_data);
                }
            }
        }

        let params = PersonalizationFeedbackServiceRequestParams {
            operation,
            feedbackType: PersonalizationFeedbackServiceFeedbackType::Like,
            titleId: title_id,
        };

        let success_cb = get_personalization_success_callback(
            ctx.clone(),
            user_has_reacted,
            network_client_clone,
            selected,
            onboarding_status_submission_failed,
        );

        let error_cb = get_personalization_error_callback(ctx, is_card_selected, prev_selected);

        network_client
            .as_ref()
            .call_personalization_service(params, success_cb, error_cb);
    }
}

fn get_on_item_viewed(
    scope: Scope,
) -> impl Fn(ViewImpressionData, RwSignal<StandardCardContainerItemType>, usize, usize, CardDimensions)
{
    move |view_impression_data: ViewImpressionData,
          item: RwSignal<StandardCardContainerItemType>,
          row: usize,
          col: usize,
          card_dimensions| {
        if let Some(onboarding_context) = use_onboarding_context(scope) {
            let idx = row * ONBOARDING_GRID_COLUMN_COUNT + col;
            let impression_data = item.to_impressions_data().map(|data| {
                AugmentedImpressionData::new(data, SlotRowCol(0, idx as u32), Some(card_dimensions))
            });
            if let Some(impression_data) = impression_data {
                onboarding_context
                    .borrow()
                    .impressions_reporter
                    .send_view_impression(scope, view_impression_data, &impression_data)
            }
        };
    }
}

fn get_on_item_highlighted(
    scope: Scope,
) -> impl Fn(RwSignal<StandardCardContainerItemType>, usize, usize, CardDimensions) {
    move |item: RwSignal<StandardCardContainerItemType>, row: usize, col: usize, card_dimensions| {
        if let Some(onboarding_context) = use_onboarding_context(scope) {
            let idx = row * ONBOARDING_GRID_COLUMN_COUNT + col;
            let impression_data = item.to_impressions_data().map(|data| {
                AugmentedImpressionData::new(data, SlotRowCol(0, idx as u32), Some(card_dimensions))
            });
            if let Some(impression_data) = impression_data {
                onboarding_context
                    .borrow()
                    .impressions_reporter
                    .send_highlight_impression(scope, &impression_data);
            }
        }
    }
}

/// Creates a closure that handles the exit behavior for the onboarding page
///
/// # Arguments
/// * `ctx` - The application context
/// * `user_has_reacted` - Signal indicating whether the user has interacted with the page
/// * `onboarding_status_submission_failed` - Signal indicating if there was a submission failure
/// * `on_page_exit` - Callback function to execute when navigating back
///
/// # Behavior
/// * Checks user interaction state and submission failure status
/// * If user has reacted and submission previously failed, calls onboarding lifecycle service
/// * Executes the provided page-level back action
fn get_on_exit_onboarding_page(
    ctx: &AppContext,
    user_has_reacted: ReadSignal<bool>,
    onboarding_status_submission_failed: RwSignal<bool>,
    on_page_exit: Rc<dyn Fn(Location)>,
    is_speedbump: bool,
) -> impl Fn(Location) + Clone {
    let ctx = ctx.clone();

    move |location: Location| {
        if user_has_reacted.get_untracked() && onboarding_status_submission_failed.get_untracked() {
            call_onboarding_lifecycle_service(
                &ctx,
                &NetworkClient::new(&ctx),
                onboarding_status_submission_failed,
            );
        }

        let clickstream = use_clickstream_client(ctx.scope());
        if is_speedbump {
            clickstream.track(OnboardingPageSpeedBumpDoneEvent);
        } else {
            clickstream.track(OnboardingPageDoneEvent);
        }

        on_page_exit(location)
    }
}

#[Composer]
fn OnboardingCardGridModelToUI(
    ctx: &AppContext,
    model: &RwSignal<CardGridModel>,
    scroll_to: RwSignal<ScrollTo>,
    container_idx: u32,
    on_back_pressed_external: Rc<dyn Fn()>,
    default_focus: RwSignal<Option<String>>,
    user_has_reacted: RwSignal<bool>,
    onboarding_status_submission_failed: RwSignal<bool>,
    is_speedbump: bool,
) -> StackComposable {
    let grid_has_title = model.with_untracked(|d| {
        let t: Option<CarouselTitle> = d.into();
        t.is_some()
    });
    let card_height_plus_space = CardDimensions::from(GRID_CARD_SIZE).height + GRID_SPACING_V;
    let grid_has_scrolled = create_rw_signal(ctx.scope(), false);
    let currently_focused_item_id = create_rw_signal(ctx.scope(), None::<String>);
    let default_focus_id = default_focus.into();

    let (scroll_to_position, position_top_of_grid_to_top_of_page) =
        get_scroll_handlers(scroll_to, container_idx);

    // allows for focus restoration when returning to a grid that was previously focused
    let on_un_focus = move || default_focus.set(currently_focused_item_id.get_untracked());

    let on_item_focused = get_on_item_focused(
        scroll_to_position.clone(),
        card_height_plus_space,
        grid_has_scrolled,
        currently_focused_item_id.write_only(),
        grid_has_title,
    );

    let on_back_pressed_internal = get_on_back_pressed_internal(
        grid_has_scrolled,
        position_top_of_grid_to_top_of_page.clone(),
    );

    let network_client = Rc::new(NetworkClient::new(ctx));

    let on_item_selected = Rc::new(get_on_item_selected(
        ctx,
        network_client,
        user_has_reacted,
        onboarding_status_submission_failed,
        is_speedbump,
    ));

    let on_item_viewed = Rc::new(get_on_item_viewed(ctx.scope()));

    let on_item_highlighted = Rc::new(get_on_item_highlighted(ctx.scope()));

    let model = *model;

    let onboarding_card_accessibility_hint_helper = Rc::new(
        move |row_index: usize, col_index: usize, item_count: usize, is_selected: bool| {
            tts_onboarding_grid_card_hint(
                container_idx == 0,
                row_index,
                col_index,
                item_count,
                is_selected,
                ONBOARDING_GRID_COLUMN_COUNT,
            )
        },
    );

    CardGridUIBuilder::new(
        ctx.clone(),
        model.into(),
        ONBOARDING_GRID_COLUMN_COUNT,
        GridScroll::None,
        on_item_viewed,
        on_item_highlighted,
        default_focus_id,
    )
    .with_item_selected(on_item_selected)
    .with_item_focused(on_item_focused)
    .with_item_long_pressed(Rc::new(move |_, _, _| {}))
    .with_back_pressed_external(on_back_pressed_external)
    .with_back_pressed_internal(Some(on_back_pressed_internal))
    .with_card_accessibility_hint_helper(Some(onboarding_card_accessibility_hint_helper))
    .with_overlay(GridCardOverlay::LikeButton)
    .build()
    .on_focus(position_top_of_grid_to_top_of_page)
    .on_unfocus(on_un_focus)
    .padding(Padding {
        start: 0.0,
        end: 0.0,
        top: 0.0,
        bottom: VERTICAL_CAROUSEL_SPACING,
    })
}

/// The Onboarding CX Page, rendered by the Collections Page when the parsed model
/// contains a start_column. This page renders a split-screen two column layout.
///
/// This is currently in progress and will be populated by subtasks listed here:
/// https://issues.amazon.com/issues/LR-Rust-578
#[Composer]
pub fn OnboardingPageUI(
    ctx: &AppContext,
    model: OnboardingPageModel,
    on_ready: Rc<dyn Fn()>,
    on_page_exit: Rc<dyn Fn(Location)>,
    is_speedbump: bool,
) -> StackComposable {
    // refer to reporting logic in page_ui_sig
    let app_event_reporter = AppEventReporter::new(ctx.scope());
    app_event_reporter.send_app_event("PROFILE_SELECTION_PAGE_COMPLETED", "PROFILES", None);
    let OnboardingPageModel {
        start_column,
        container_list,
    } = model;
    let focused = create_focus_signal(ctx.scope());
    let opacity = create_rw_signal(ctx.scope(), 1.0);
    let container_index_focus_value = create_focus_value_signal(ctx.scope());
    let scroll_to = create_rw_signal(
        ctx.scope(),
        ScrollTo::Index(
            0,
            Pivot::Custom(0.0),
            Animation::default().with_duration(Duration::ZERO),
        ),
    );
    let focus_enabled_container_list = Signal::derive(ctx.scope(), move || opacity.get() == 1.0);
    let (focus_carousel_index_data, _set_focus_carousel_index_data) =
        create_signal(ctx.scope(), CarouselIndexData::default());
    let user_has_reacted = create_rw_signal(ctx.scope(), false);
    let onboarding_status_submission_failed = create_rw_signal(ctx.scope(), false);

    let on_exit_onboarding_page = get_on_exit_onboarding_page(
        ctx,
        user_has_reacted.read_only(),
        onboarding_status_submission_failed,
        on_page_exit.clone(),
        is_speedbump,
    );

    let on_default_exit_onboarding_page = {
        let on_exit_onboarding_page = on_exit_onboarding_page.clone();
        move || {
            on_exit_onboarding_page(Location {
                pageType: Rust(RUST_COLLECTIONS),
                pageParams: Map::new(),
            })
        }
    };

    let on_button_press = {
        let on_default_exit_onboarding_page = on_default_exit_onboarding_page.clone();
        let metrics_client = use_onboarding_client(ctx.scope());
        move || {
            if let Some(metrics_client) = &metrics_client {
                metrics_client.report_page_event(
                    MetricEvents::Select,
                    Some(MetricActions::Continue),
                    Some(OnboardingMetricContext::Page),
                );
            }
            on_default_exit_onboarding_page()
        }
    };

    let StartColumn {
        title,
        subtitle,
        action: _,
    } = start_column;

    let accessibility_context_message = match (&title, &subtitle) {
        (Some(t), Some(s)) => format!("{t}, {s}"),
        (Some(t), None) => t.clone(),
        (None, Some(s)) => s.clone(),
        (None, None) => String::new(),
    };

    let textual_button_column = {
        TextualButtonColumnProps {
            title,
            subtitle,
            button_text: TextContent::LocalizedText(LocalizedText::new(
                ATV_SONATA_ONBOARDING_COMPLETE,
            )),
            on_button_press,
            on_page_level_back: on_default_exit_onboarding_page.clone(),
            button_secondary_message: LocalizedText::from(
                "AV_LRC_ONBOARDING_DONE_TTS_SECONDARY_CLOSING_MESSAGE",
            ),
        }
    };

    let first_grid_default_focus = create_rw_signal(ctx.scope(), None::<String>);

    let item_builder = Rc::new(
        move |ctx: &AppContext,
              (container, _focus_ring_namespace): &(ContainerType, Namespace),
              container_idx: usize| {
            let model = container.model;
            let on_ready = Rc::clone(&on_ready);
            let on_default_exit_onboarding_page = on_default_exit_onboarding_page.clone();

            let on_back_pressed_external =
                Rc::new(move || match container_index_focus_value.get_untracked() {
                    Some(0) => on_default_exit_onboarding_page(),
                    _ => reset_page_focus_to_top(
                        container_index_focus_value,
                        first_grid_default_focus,
                    ),
                });

            compose! {
                Column() {
                    Memo(item_builder: Box::new(move |ctx| {
                        let on_ready = on_ready.clone();
                        let on_ready_wrapper: Rc<dyn Fn()> = Rc::new(move || {
                            if container_idx == 0 {
                                if is_speedbump {
                                    use_clickstream_client(ctx.scope()).track(OnboardingPageSpeedBumpOpenEvent);
                                }
                                if let Some(metrics_client) = use_onboarding_client(ctx.scope()) {
                                    metrics_client.report_page_loading_finished();
                                    metrics_client.report_page_event(MetricEvents::Render, None, Some(OnboardingMetricContext::Page));
                                }
                                on_ready()
                            }
                        });
                        let on_back_pressed_external = Rc::clone(&on_back_pressed_external);
                        let composable = model.with(move |container| {
                            match container {
                                ContainerModel::Grid(onboarding_card_grid_model) => {
                                    let container_idx = container_idx as u32;
                                    let default_focus = if container_idx == 0 { first_grid_default_focus } else { create_rw_signal(ctx.scope(), None::<String>) };
                                    let ui = compose_option! { OnboardingCardGridModelToUI(model: onboarding_card_grid_model, scroll_to, container_idx, on_back_pressed_external, default_focus, user_has_reacted, onboarding_status_submission_failed, is_speedbump) };
                                    on_ready_wrapper();
                                    ui
                                },
                                _ => None
                            }
                        });

                        composable.map(|composable| composable.focused_value(container_index_focus_value, container_idx))
                    }))
                }
            }
        },
    );

    let items = safeguard_containers(ctx.scope(), container_list);

    let container_list_column = ContainerListColumnProperties {
        items,
        scroll_to,
        item_builder,
        focused,
        on_end_reached: Rc::new(|| {}),
        on_container_focused: Rc::new(|_| {}),
        focus_carousel_index_data: Some(focus_carousel_index_data),
        focus_enabled: Some(focus_enabled_container_list),
    };

    let column_ratio = Some(START_COLUMN_WIDTH / SCREEN_WIDTH);

    let props = TwoColumnPageProps {
        start_column: textual_button_column,
        end_column: container_list_column,
        column_ratio,
    };

    compose! {
        Stack() {
            TwoColumnPageComposer(props)
        }
        .accessibility_context_message(accessibility_context_message)
        .test_id(ONBOARDING_PAGE)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::context::onboarding_context::provide_onboarding_context;
    use crate::page::helpers_sig::{
        create_default_collections_page_model, get_initial_load_success_cb,
    };
    use crate::reporting::onboarding::test_utils::provide_onboarding_client_with_spy;
    use crate::test_assets::mocks::SuccessCallbackTestSetup;
    use crate::types::collections_types::CollectionsPageModel;
    use crate::utils::impression_test_helper::ImpressionTestHelper;
    use clickstream::test_utils::provide_clickstream_client_spy;
    use collection_types::network_types::CollectionsPage;
    use containers::card_grid::card_grid_ui_builder::GRID_TEST_ID;
    use containers::card_grid::card_grid_utils::GRID_CARD_TEST_ID_PREFIX;
    use ignx_compositron::app::{launch_only_app_context, launch_test};
    use ignx_compositron::input::{KeyCode, KeyEventType};
    use ignx_compositron::network::http::MockHttpRequestContext;
    use ignx_compositron::reactive::{store_value, StoredValue};
    use ignx_compositron::test_utils::node_properties::{NodeProperties, SceneNodeTree};
    use ignx_compositron::test_utils::TestRendererGameLoop;
    use ignx_compositron::time::MockClock;
    use metrics_client::test_utils::{get_spy_metric_client, MetricClientEmitterSpy};
    use network::common::{LRCEdgeResponse, PageRequestStatus};
    use network_parser::core::network_parse_from_str;
    use onboarding_lifecycle_service::test_utils::provide_ols_context::provide_ols_context;
    use rstest::rstest;
    use rust_features::provide_context_test_rust_features;
    use serial_test::*;
    use std::rc::Rc;
    use title_reaction_buttons::ui::like_button::LIKE_FILLED_ICON_TEST_ID;

    fn onboarding_page_response_parser(
        multiple_grids: bool,
        has_title: bool,
    ) -> serde_json::Result<LRCEdgeResponse<CollectionsPage>> {
        let res = match multiple_grids {
            true => include_str!(
                "../test_assets/onboarding_cx_page_with_multiple_onboarding_containers.json"
            ),
            false => {
                if has_title {
                    include_str!(
                        "../test_assets/onboarding_cx_page_with_single_titled_onboarding_container.json"
                    )
                } else {
                    include_str!(
                        "../test_assets/onboarding_cx_page_with_single_onboarding_container.json"
                    )
                }
            }
        };
        network_parse_from_str(&res.to_string())
    }

    fn populate_onboarding_page_data_from_mock_response(
        page_data_signal: RwSignal<CollectionsPageModel>,
        load_status_signal: RwSignal<PageRequestStatus<String>>,
        multiple_grids: bool,
        has_title: bool,
        ctx: AppContext,
    ) {
        let scope = ctx.scope();

        let page_data = onboarding_page_response_parser(multiple_grids, has_title)
            .map(|result| result.resource)
            .unwrap();

        let modal_data = create_rw_signal(scope, vec![]);
        let setup = SuccessCallbackTestSetup::new(ctx)
            .with_page_data_and_status(page_data_signal, load_status_signal)
            .with_parsed_page(page_data)
            .with_modal_data_signal(modal_data);

        let page_controller = setup.to_controller();

        let success_cb =
            get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

        let args = setup.to_success_cb_args();

        success_cb(
            args.parsed_page_from_network.clone(),
            args.transform_name,
            args.manipulate_page_data,
            args.timestamp,
        );
    }

    struct OnboardingPageConfig {
        multiple_grids: bool,
        is_speedbump: bool,
        start_column_override: Option<StartColumn>,
        has_title: bool,
        metric_spy: Option<Rc<MetricClientEmitterSpy>>,
    }

    impl Default for OnboardingPageConfig {
        fn default() -> Self {
            Self {
                multiple_grids: false,
                is_speedbump: true,
                start_column_override: None,
                has_title: false,
                metric_spy: None,
            }
        }
    }

    fn onboarding_page(ctx: &AppContext, config: OnboardingPageConfig) -> StackComposable {
        let OnboardingPageConfig {
            multiple_grids,
            is_speedbump,
            start_column_override,
            has_title,
            metric_spy,
        } = config;
        provide_context_test_rust_features(ctx.scope());

        let page_data = create_default_collections_page_model(ctx.scope);
        let load_status_signal = create_rw_signal(
            ctx.scope(),
            PageRequestStatus::Success("onboarding page".to_string()),
        );
        populate_onboarding_page_data_from_mock_response(
            page_data,
            load_status_signal,
            multiple_grids,
            has_title,
            ctx.clone(),
        );

        let on_ready_called = store_value(ctx.scope(), 0);
        let on_ready = {
            Rc::new(move || {
                on_ready_called.set_value(on_ready_called.get_value() + 1);
            })
        };

        let on_page_exit_called = store_value(ctx.scope(), 0);
        let on_page_exit_called_with_location = store_value::<Option<Location>>(ctx.scope(), None);
        let on_page_exit = {
            Rc::new(move |location: Location| {
                on_page_exit_called_with_location.set_value(Some(location));
                on_page_exit_called.set_value(on_page_exit_called.get_value() + 1);
            })
        };

        provide_context(ctx.scope(), on_ready_called);
        provide_context(ctx.scope(), on_page_exit_called);
        provide_context(ctx.scope(), on_page_exit_called_with_location);
        provide_ols_context(&ctx, false);
        provide_clickstream_client_spy(ctx.scope());

        let metric_spy = if let Some(metric_spy) = metric_spy {
            metric_spy
        } else {
            get_spy_metric_client()
        };
        provide_onboarding_client_with_spy(ctx.scope(), metric_spy);

        provide_onboarding_context(ctx.scope());

        let impressions = ImpressionTestHelper::new();
        impressions.setup_mocks(ctx.scope());
        provide_context(ctx.scope(), impressions);

        let start_column = if start_column_override.is_some() {
            start_column_override.unwrap()
        } else {
            page_data.with(|page_data| page_data.start_column.get_untracked().unwrap())
        };

        let container_list = page_data.with(|data| data.container_list);

        let model = OnboardingPageModel {
            container_list,
            start_column,
        };

        compose! {
            OnboardingPageUI(
                model,
                on_ready,
                on_page_exit,
                is_speedbump,
            )
        }
    }

    fn press_keys(test_game_loop: &mut TestRendererGameLoop, keys: &[KeyCode]) -> SceneNodeTree {
        for key in keys {
            test_game_loop.send_key_press_event(KeyEventType::ButtonDown, key.clone());
            test_game_loop.tick_until_done();
        }
        test_game_loop.tick_until_done()
    }

    fn get_card_in_grid_props(
        tree: SceneNodeTree,
        grid_idx: usize,
        row_idx: usize,
        col_idx: usize,
    ) -> NodeProperties {
        tree.find_by_props()
            .test_id(GRID_TEST_ID)
            .find_by_index(grid_idx)
            .find_any_child_with()
            .test_id(format!(
                "{}{}-{}",
                GRID_CARD_TEST_ID_PREFIX, row_idx, col_idx
            ))
            .find_first()
            .get_props()
    }

    fn get_action_button_props(tree: SceneNodeTree) -> NodeProperties {
        tree.find_by_test_id("textual_button_column_button")
            .get_props()
    }

    fn get_grid_has_focused_child(tree: SceneNodeTree, idx: usize) -> bool {
        let grid = tree
            .find_by_props()
            .test_id(GRID_TEST_ID)
            .find_by_index(idx);
        let grid_focused_child = grid.find_any_child_with().is_focused().find_first();
        // Decipher whether node exists without panicking, assertion is made when appropriate
        grid_focused_child.query_result().as_ref().is_ok()
    }

    fn select_card(
        test_game_loop: &mut TestRendererGameLoop,
        grid_idx: usize,
        row_idx: usize,
        col_idx: usize,
    ) {
        let NodeProperties { node_id, .. } =
            get_card_in_grid_props(test_game_loop.tick_until_done(), grid_idx, row_idx, col_idx);
        test_game_loop.send_on_select_event(node_id);
        test_game_loop.tick_until_done();
    }

    fn select_action_button(test_game_loop: &mut TestRendererGameLoop) {
        let NodeProperties { node_id, .. } =
            get_action_button_props(test_game_loop.tick_until_done());
        test_game_loop.send_on_select_event(node_id);
        test_game_loop.tick_until_done();
    }

    fn is_first_card_selected(test_game_loop: &mut TestRendererGameLoop) -> bool {
        test_game_loop
            .tick_until_done()
            .find_by_props()
            .test_id(LIKE_FILLED_ICON_TEST_ID)
            .find_first()
            .get_props()
            .base_styles
            .opacity
            == Some(1.0)
    }

    fn invoke_personalisation_feedback_service_on_success(props: MockHttpRequestContext) {
        props.invoke_callback(
            Some(
                include_str!("../../../personalization-feedback-service/src/mock_response.in")
                    .to_string(),
            ),
            200,
        );
    }

    fn invoke_personalisation_feedback_service_on_failure(props: MockHttpRequestContext) {
        props.invoke_callback(
            Some(
                include_str!("../../../personalization-feedback-service/src/mock_response.in")
                    .to_string(),
            ),
            502,
        );
    }

    fn invoke_onboarding_lifecycle_service_on_success(props: MockHttpRequestContext) {
        props.invoke_callback(
            Some(
                include_str!(
                    "../../../onboarding-lifecycle-service/src/test_utils/mock_response.in"
                )
                .to_string(),
            ),
            200,
        );
    }

    fn invoke_onboarding_lifecycle_service_on_failure(props: MockHttpRequestContext) {
        props.invoke_callback(
            Some(
                include_str!(
                    "../../../onboarding-lifecycle-service/src/test_utils/mock_response.in"
                )
                .to_string(),
            ),
            502,
        );
    }

    fn advance_clock_by_set_plus_revert_animation_duration() {
        MockClock::advance(Duration::from_millis(
            LIKE_BUTTON_ANIMATION_SET_TOTAL_DURATION + LIKE_BUTTON_ANIMATION_REMOVE_TOTAL_DURATION,
        ));
    }

    fn assert_page_exit(
        on_page_exit_call_count: StoredValue<i32>,
        page_exit_destination: StoredValue<Option<Location>>,
    ) {
        assert_eq!(on_page_exit_call_count.get_value(), 1);
        assert_eq!(
            page_exit_destination.get_value(),
            Some(Location {
                pageType: Rust(RUST_COLLECTIONS),
                pageParams: Map::new()
            })
        );
    }

    mod on_card_select {
        use super::*;

        #[test]
        #[serial]
        fn records_feedback() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    let game_loop = &mut test_game_loop;

                    assert!(!is_first_card_selected(game_loop));

                    select_card(game_loop, 0, 0, 0);

                    // Assert selection
                    assert!(is_first_card_selected(game_loop));

                    // PersonalizationFeedbackService called to record feedback
                    let MockHttpRequestContext { url, .. } = MockHttpRequestContext::get();
                    assert_eq!(url, "https://url/cdp/discovery/RecordExplicitCustomerFeedback?titleId=amzn1%2Edv%2Egti%2E31bdc438%2De953%2D4314%2D9d43%2D097d9fe746c6&feedbackType=like&gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB".to_string());
                },
            )
        }

        #[test]
        #[serial]
        fn first_successful_feedback() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    select_card(&mut test_game_loop, 0, 0, 0);

                    invoke_personalisation_feedback_service_on_success(
                        MockHttpRequestContext::get(),
                    );

                    // OnboardingLifecycleService called to mark onboarding complete
                    let MockHttpRequestContext { url, .. } = MockHttpRequestContext::get();
                    assert_eq!(url, "https://url/cdp/ols/UpdateOnboardingState?gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB".to_string());
                },
            )
        }

        #[test]
        #[serial]
        fn later_successful_feedback() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    for i in 0..2 {
                        select_card(&mut test_game_loop, 0, i, 0);

                        invoke_personalisation_feedback_service_on_success(
                            MockHttpRequestContext::get(),
                        );
                    }

                    // OnboardingLifecycleService not called as onboarding has already been marked as complete
                    assert!(MockHttpRequestContext::try_get().is_none());
                },
            )
        }

        #[test]
        #[serial]
        fn failed_feedback_auto_reverts() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    let game_loop = &mut test_game_loop;

                    select_card(game_loop, 0, 0, 0);

                    invoke_personalisation_feedback_service_on_failure(
                        MockHttpRequestContext::get(),
                    );

                    // OnboardingLifecycleService not called as feedback failed
                    assert!(MockHttpRequestContext::try_get().is_none());

                    advance_clock_by_set_plus_revert_animation_duration();

                    // Assert selection reverted
                    assert!(!is_first_card_selected(game_loop));
                },
            )
        }

        #[test]
        #[serial]
        fn manually_reverted_feedback() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    let game_loop = &mut test_game_loop;

                    for _ in 0..2 {
                        select_card(game_loop, 0, 0, 0);
                    }

                    assert!(!is_first_card_selected(game_loop));

                    // PersonalizationFeedbackService called to delete reaction
                    let MockHttpRequestContext { url, .. } = MockHttpRequestContext::get();
                    assert_eq!(url, "https://url/cdp/discovery/DeleteExplicitCustomerFeedback?titleId=amzn1%2Edv%2Egti%2E31bdc438%2De953%2D4314%2D9d43%2D097d9fe746c6&feedbackType=like&gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB".to_string());
                },
            )
        }

        #[test]
        #[serial]
        fn failed_manual_revert_auto_reverts() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    let game_loop = &mut test_game_loop;

                    for _ in 0..2 {
                        select_card(game_loop, 0, 0, 0);
                    }

                    invoke_personalisation_feedback_service_on_failure(
                        MockHttpRequestContext::get(),
                    );

                    advance_clock_by_set_plus_revert_animation_duration();

                    // Assert selection reinstated
                    assert!(is_first_card_selected(game_loop));
                },
            )
        }
    }

    mod action_button {
        use super::*;

        #[test]
        #[serial]
        fn test_action_button_press_calls_ols_if_previous_call_failed() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    let game_loop = &mut test_game_loop;

                    select_card(game_loop, 0, 0, 0);

                    invoke_personalisation_feedback_service_on_success(
                        MockHttpRequestContext::get(),
                    );

                    invoke_onboarding_lifecycle_service_on_failure(MockHttpRequestContext::get());

                    assert!(MockHttpRequestContext::try_get().is_none());

                    select_action_button(game_loop);

                    // OnboardingLifecycleService request retried
                    let MockHttpRequestContext { url, .. } = MockHttpRequestContext::get();
                    assert_eq!(url, "https://url/cdp/ols/UpdateOnboardingState?gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB".to_string());
                },
            )
        }

        #[test]
        #[serial]
        fn test_action_button_press_does_not_call_ols_if_previous_call_succeeded() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    let game_loop = &mut test_game_loop;

                    select_card(game_loop, 0, 0, 0);

                    invoke_personalisation_feedback_service_on_success(
                        MockHttpRequestContext::get(),
                    );

                    invoke_onboarding_lifecycle_service_on_success(MockHttpRequestContext::get());

                    assert!(MockHttpRequestContext::try_get().is_none());

                    select_action_button(game_loop);

                    // OnboardingLifecycleService request is not retried as it previously succeeded
                    assert!(MockHttpRequestContext::try_get().is_none());
                },
            )
        }

        #[test]
        #[serial]
        fn test_action_button_press_does_not_call_ols_if_user_has_not_reacted() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    select_action_button(&mut test_game_loop);

                    assert!(MockHttpRequestContext::try_get().is_none());
                },
            )
        }

        #[test]
        #[serial]
        fn test_action_button_press_triggers_page_exit_with_default_location() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |scope, mut test_game_loop| {
                    let on_page_exit = use_context::<StoredValue<i32>>(scope).unwrap();
                    let on_page_exit_location =
                        use_context::<StoredValue<Option<Location>>>(scope).unwrap();
                    assert_eq!(on_page_exit.get_value(), 0);
                    assert!(on_page_exit_location.get_value().is_none());

                    select_action_button(&mut test_game_loop);

                    assert_page_exit(on_page_exit, on_page_exit_location);
                },
            )
        }
    }

    mod back_behaviour {
        use super::*;

        #[test]
        #[serial]
        fn test_back_from_action_button_triggers_page_exit_with_default_location() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    let on_page_exit = use_context::<StoredValue<i32>>(scope).unwrap();
                    let on_page_exit_location =
                        use_context::<StoredValue<Option<Location>>>(scope).unwrap();

                    press_keys(&mut test_game_loop, &[KeyCode::Left, KeyCode::Backspace]);

                    assert_page_exit(on_page_exit, on_page_exit_location);
                },
            )
        }

        #[test]
        #[serial]
        fn test_back_from_first_grid_first_item_triggers_page_exit_with_default_location() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    let on_page_exit = use_context::<StoredValue<i32>>(scope).unwrap();
                    let on_page_exit_location =
                        use_context::<StoredValue<Option<Location>>>(scope).unwrap();

                    press_keys(&mut test_game_loop, &[KeyCode::Backspace]);

                    assert_page_exit(on_page_exit, on_page_exit_location);
                },
            )
        }

        #[test]
        #[serial]
        fn test_back_after_scroll_resets_scroll() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    // Navigate to row 5, at which point we know the top row will be hidden,
                    // then back, which results in the top row coming fully back into view
                    let tree = press_keys(
                        &mut test_game_loop,
                        &[
                            KeyCode::Down,
                            KeyCode::Down,
                            KeyCode::Down,
                            KeyCode::Down,
                            KeyCode::Down,
                            KeyCode::Backspace,
                        ],
                    );

                    let NodeProperties {
                        is_visible: is_first_card_visible,
                        on_screen_amount: first_card_on_screen_amount,
                        ..
                    } = get_card_in_grid_props(tree, 0, 0, 0);
                    let is_first_card_fully_visible =
                        is_first_card_visible && first_card_on_screen_amount == 1.0;
                    assert!(is_first_card_fully_visible)
                },
            )
        }

        #[test]
        #[serial]
        fn test_back_from_within_grid_focuses_first_item() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_, mut test_game_loop| {
                    test_game_loop.tick_until_done();

                    // Focus first card in second row
                    let tree = press_keys(&mut test_game_loop, &[KeyCode::Down]);
                    let NodeProperties {
                        is_focused: is_first_card_focused,
                        ..
                    } = get_card_in_grid_props(tree, 0, 0, 0);

                    assert!(!is_first_card_focused);

                    let tree = press_keys(&mut test_game_loop, &[KeyCode::Backspace]);
                    let NodeProperties {
                        is_focused: is_first_card_focused,
                        ..
                    } = get_card_in_grid_props(tree, 0, 0, 0);

                    // Back lands on first card
                    assert!(is_first_card_focused);
                },
            )
        }

        #[test]
        #[serial]
        fn test_back_from_second_grid_first_item_lands_on_first_grid_first_item() {
            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            multiple_grids: true,
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let mut tree = test_game_loop.tick_until_done();

                    assert!(get_grid_has_focused_child(tree.clone(), 0));
                    assert!(!get_grid_has_focused_child(tree.clone(), 1));

                    // Move down while focused child exists in the first grid, and no focused child exists in the second grid
                    // i.e. move down until focus moves to second grid
                    while get_grid_has_focused_child(tree.clone(), 0)
                        && !get_grid_has_focused_child(tree.clone(), 1)
                    {
                        tree = press_keys(&mut test_game_loop, &[KeyCode::Down]);
                    }

                    let NodeProperties {
                        is_focused: is_first_card_in_second_grid_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 1, 0, 0);

                    assert!(
                        get_grid_has_focused_child(tree.clone(), 1)
                            && is_first_card_in_second_grid_focused
                    );
                    assert!(!get_grid_has_focused_child(tree, 0));

                    tree = press_keys(&mut test_game_loop, &[KeyCode::Backspace]);
                    let NodeProperties {
                        is_focused: is_first_card_in_first_grid_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 0, 0, 0);

                    // Focus restored to the first item in the first grid
                    assert!(
                        get_grid_has_focused_child(tree.clone(), 0)
                            && is_first_card_in_first_grid_focused
                    );
                    assert!(!get_grid_has_focused_child(tree, 1));
                },
            )
        }
    }

    mod scroll {
        use super::*;
        #[test]
        #[serial]
        fn test_grid_scroll_behavior() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();

                    // Navigating down, we expect the top row to remain at least partially visible until we reach row 5
                    for i in 0..5 {
                        let tree = press_keys(&mut test_game_loop, &[KeyCode::Down]);

                        let NodeProperties {
                            is_visible: is_first_card_visible,
                            on_screen_amount: first_card_on_screen_amount,
                            ..
                        } = get_card_in_grid_props(tree, 0, 0, 0);
                        let expectation = match i {
                            // We have scrolled enough to completely hide the top row
                            4 => !is_first_card_visible,
                            // We have initiated scroll, resulting in the top row being partially hidden
                            3 => is_first_card_visible && first_card_on_screen_amount < 1.0,
                            // We have not yet initiated scroll, so the top row is still fully visible
                            _ => is_first_card_visible && first_card_on_screen_amount == 1.0,
                        };
                        assert!(expectation)
                    }

                    // Navigating back up results in the top row coming partially back into view
                    let tree = press_keys(&mut test_game_loop, &[KeyCode::Up]);
                    let NodeProperties {
                        is_visible: is_first_card_visible,
                        on_screen_amount: first_card_on_screen_amount,
                        ..
                    } = get_card_in_grid_props(tree, 0, 0, 0);
                    let is_first_card_partially_visible =
                        is_first_card_visible && first_card_on_screen_amount < 1.0;
                    assert!(is_first_card_partially_visible)
                },
            )
        }

        #[test]
        #[serial]
        fn test_titled_grid_scroll_behavior() {
            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            has_title: true,
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();

                    // Navigating down, we expect the list to be scrolled once the fourth row is focused
                    for i in 0..3 {
                        let tree = press_keys(&mut test_game_loop, &[KeyCode::Down]);

                        let NodeProperties {
                            is_visible: is_fifth_row_visible,
                            on_screen_amount: fifth_row_on_screen_amount,
                            ..
                        } = get_card_in_grid_props(tree, 0, 4, 0);
                        let expectation = match i {
                            // we have focused the fourth row which initiated scroll, making the fifth row partially visible
                            2 => is_fifth_row_visible && fifth_row_on_screen_amount < 1.0,
                            // We have not yet initiated scroll, so the fifth row is fully hidden
                            _ => !is_fifth_row_visible,
                        };
                        assert!(expectation)
                    }

                    // Navigating back up, we expect the fifth row to once again be fully hidden
                    let tree = press_keys(&mut test_game_loop, &[KeyCode::Up]);
                    let NodeProperties {
                        is_visible: is_fifth_row_visible,
                        ..
                    } = get_card_in_grid_props(tree, 0, 4, 0);
                    assert!(!is_fifth_row_visible);
                },
            )
        }
    }

    mod focus_restoration {
        use super::*;

        #[test]
        #[serial]
        fn test_focus_restored_from_button() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |_scope, mut test_game_loop| {
                    let mut tree = test_game_loop.tick_until_done();

                    // Navigate down to the first item in the third row
                    tree = press_keys(&mut test_game_loop, &[KeyCode::Down, KeyCode::Down]);
                    let NodeProperties {
                        is_focused: is_first_card_in_third_row_focused,
                        ..
                    } = get_card_in_grid_props(tree, 0, 2, 0);
                    assert!(is_first_card_in_third_row_focused);

                    // Navigate left from the grid to the button
                    tree = press_keys(&mut test_game_loop, &[KeyCode::Left]);

                    let NodeProperties {
                        is_focused: is_first_card_in_third_row_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 0, 2, 0);
                    let NodeProperties {
                        is_focused: is_action_button_focused,
                        ..
                    } = get_action_button_props(tree);
                    assert!(is_action_button_focused);
                    assert!(!is_first_card_in_third_row_focused);

                    // Navigate right from the button to the grid, expect focus to be restored
                    // to the first item in the third row
                    tree = press_keys(&mut test_game_loop, &[KeyCode::Right]);

                    let NodeProperties {
                        is_focused: is_first_card_in_third_row_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 0, 2, 0);
                    let NodeProperties {
                        is_focused: is_action_button_focused,
                        ..
                    } = get_action_button_props(tree);
                    assert!(is_first_card_in_third_row_focused);
                    assert!(!is_action_button_focused);
                },
            )
        }

        #[test]
        #[serial]
        fn test_focus_restored_between_grids() {
            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            multiple_grids: true,
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let mut tree = test_game_loop.tick_until_done();
                    // Navigate to the second item in the bottom (second) row of the first grid
                    tree = press_keys(&mut test_game_loop, &[KeyCode::Down, KeyCode::Right]);

                    let NodeProperties {
                        is_focused: is_second_card_in_second_row_first_grid_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 0, 1, 1);
                    assert!(is_second_card_in_second_row_first_grid_focused);

                    // Navigate down and right, to second item in the first row of the second grid
                    tree = press_keys(&mut test_game_loop, &[KeyCode::Down, KeyCode::Right]);

                    let NodeProperties {
                        is_focused: is_second_card_in_first_row_second_grid_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 1, 0, 1);
                    assert!(is_second_card_in_first_row_second_grid_focused);

                    // Navigate up, expect focus to be restored to the second item in the bottom row of the first grid
                    tree = press_keys(&mut test_game_loop, &[KeyCode::Up]);

                    let NodeProperties {
                        is_focused: is_second_card_in_second_row_first_grid_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 0, 1, 1);
                    assert!(is_second_card_in_second_row_first_grid_focused);

                    // Navigate down, expect focus to be restored to the second item in the top row of the second grid
                    tree = press_keys(&mut test_game_loop, &[KeyCode::Down]);

                    let NodeProperties {
                        is_focused: is_second_card_in_first_row_second_grid_focused,
                        ..
                    } = get_card_in_grid_props(tree.clone(), 1, 0, 1);
                    assert!(is_second_card_in_first_row_second_grid_focused);
                },
            )
        }
    }

    #[rstest]
    #[case (StartColumn { title: None, subtitle: None, action: None}, vec![(String::default(), None)])]
    #[case (StartColumn { title: Some("title".to_string()), subtitle: None, action: None}, vec![("title".to_string(), None)])]
    #[case (StartColumn { title: None, subtitle: Some("subtitle".to_string()), action: None}, vec![("subtitle".to_string(), None)])]
    #[case (StartColumn { title: Some("title".to_string()), subtitle: Some("subtitle".to_string()), action: None}, vec![("title, subtitle".to_string(), None)])]
    #[serial]
    fn defines_tts_according_to_title_and_subtitle(
        #[case] start_column: StartColumn,
        #[case] expectation: Vec<(String, Option<LocalizedText>)>,
    ) {
        launch_test(
            move |ctx| {
                onboarding_page(
                    &ctx,
                    OnboardingPageConfig {
                        start_column_override: Some(start_column),
                        ..OnboardingPageConfig::default()
                    },
                )
            },
            move |_scope, mut test_game_loop| {
                let NodeProperties { accessibility, .. } = test_game_loop
                    .tick_until_done()
                    .find_by_test_id(ONBOARDING_PAGE)
                    .get_props();
                assert_eq!(accessibility.unwrap().context_messages, expectation);
            },
        )
    }

    mod metrics_reporting {
        use super::*;
        use crate::utils::impression_test_helper::ImpressionTestHelper;
        use clickstream::test_utils::use_clickstream_emitter_spy;
        use cross_app_events::ImpressionData;
        use ignx_compositron::time::MockClock;
        use metrics_client::test_utils::{
            get_spy_metric_client, MetricClientEmitterSpy, MetricEventPayload,
        };
        use rstest::rstest;
        use std::collections::HashMap;

        #[rstest]
        #[case(true)]
        #[case(false)]
        #[serial]
        fn test_metrics_on_page_load(#[case] is_speedbump: bool) {
            let spy: Rc<MetricClientEmitterSpy> = get_spy_metric_client();
            let spy_clone = spy.clone();

            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            is_speedbump,
                            metric_spy: Some(spy.clone()),
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let spy = spy_clone.clone();

                    test_game_loop.tick_until_done();

                    let calls = spy.recorded_metrics();
                    assert_eq!(calls.len(), 2);
                    assert_eq!(
                        calls,
                        vec![
                            MetricEventPayload {
                                name: "PageAction.SuccessRate",
                                value: 1,
                                dimensions: vec![
                                    ("actionName".to_string(), "Load".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            },
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "Render".to_string()),
                                    ("context".to_string(), "Page".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            }
                        ]
                    )
                },
            )
        }

        #[test]
        #[serial]
        fn test_metrics_on_card_like() {
            let spy = get_spy_metric_client();
            let spy_clone = spy.clone();

            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            is_speedbump: true,
                            metric_spy: Some(spy.clone()),
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let spy = spy_clone.clone();

                    test_game_loop.tick_until_done();

                    // Removing the initial events reported
                    spy.flush();

                    // Select first card
                    select_card(&mut test_game_loop, 0, 0, 0);

                    let calls = spy.recorded_metrics();
                    assert_eq!(
                        calls[0],
                        MetricEventPayload {
                            name: "OnboardingPage.Event",
                            value: 1,
                            dimensions: vec![
                                ("event".to_string(), "Select".to_string()),
                                ("action".to_string(), "Like".to_string()),
                                ("context".to_string(), "Page".to_string()),
                                ("pageType".to_string(), "OnboardingPage".to_string()),
                                ("activeLayer".to_string(), "Wasm".to_string())
                            ]
                        }
                    );

                    spy.flush();

                    // Select same card again to unlike
                    select_card(&mut test_game_loop, 0, 0, 0);

                    let calls = spy.recorded_metrics();
                    assert_eq!(
                        calls[0],
                        MetricEventPayload {
                            name: "OnboardingPage.Event",
                            value: 1,
                            dimensions: vec![
                                ("event".to_string(), "Select".to_string()),
                                ("action".to_string(), "RemoveLike".to_string()),
                                ("context".to_string(), "Page".to_string()),
                                ("pageType".to_string(), "OnboardingPage".to_string()),
                                ("activeLayer".to_string(), "Wasm".to_string())
                            ]
                        }
                    );
                },
            )
        }

        #[test]
        #[serial]
        fn test_metrics_on_lifecycle_and_personalisation_service_success() {
            let spy = get_spy_metric_client();
            let spy_clone = spy.clone();

            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            metric_spy: Some(spy.clone()),
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let spy = spy_clone.clone();

                    test_game_loop.tick_until_done();

                    // Removing the initial events reported
                    spy.flush();

                    select_card(&mut test_game_loop, 0, 0, 0);

                    invoke_personalisation_feedback_service_on_success(
                        MockHttpRequestContext::get(),
                    );

                    invoke_onboarding_lifecycle_service_on_success(MockHttpRequestContext::get());

                    let calls = spy.recorded_metrics();
                    assert_eq!(calls.len(), 3);
                    assert_eq!(
                        calls,
                        vec![
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "Select".to_string()),
                                    ("action".to_string(), "Like".to_string()),
                                    ("context".to_string(), "Page".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            },
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "ReactionCallSucceeded".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            },
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "OnboardingCallSucceeded".to_string(),),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            }
                        ]
                    );
                },
            )
        }

        #[test]
        #[serial]
        fn test_metrics_on_lifecycle_service_failure() {
            let spy = get_spy_metric_client();
            let spy_clone = spy.clone();

            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            metric_spy: Some(spy.clone()),
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let spy = spy_clone.clone();

                    test_game_loop.tick_until_done();

                    // Removing the initial events reported
                    spy.flush();

                    select_card(&mut test_game_loop, 0, 0, 0);

                    invoke_personalisation_feedback_service_on_success(
                        MockHttpRequestContext::get(),
                    );

                    invoke_onboarding_lifecycle_service_on_failure(MockHttpRequestContext::get());

                    let calls = spy.recorded_metrics();

                    assert_eq!(calls.len(), 3);
                    assert_eq!(
                        calls,
                        vec![
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "Select".to_string()),
                                    ("action".to_string(), "Like".to_string()),
                                    ("context".to_string(), "Page".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            },
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "ReactionCallSucceeded".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            },
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "OnboardingCallFailed".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            }
                        ]
                    );
                },
            )
        }

        #[test]
        #[serial]
        fn test_metrics_on_personalisation_service_failure() {
            let spy = get_spy_metric_client();
            let spy_clone = spy.clone();

            launch_test(
                move |ctx| {
                    provide_onboarding_client_with_spy(ctx.scope(), spy.clone());
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            metric_spy: Some(spy.clone()),
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |_scope, mut test_game_loop| {
                    let spy = spy_clone.clone();

                    test_game_loop.tick_until_done();

                    // Removing the initial events reported
                    spy.flush();

                    select_card(&mut test_game_loop, 0, 0, 0);

                    invoke_onboarding_lifecycle_service_on_failure(MockHttpRequestContext::get());

                    let calls = spy.recorded_metrics();
                    assert_eq!(calls.len(), 3);
                    assert_eq!(
                        calls,
                        vec![
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "Select".to_string()),
                                    ("action".to_string(), "Like".to_string()),
                                    ("context".to_string(), "Page".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            },
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "ReactionCallSucceeded".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            },
                            MetricEventPayload {
                                name: "OnboardingPage.Event",
                                value: 1,
                                dimensions: vec![
                                    ("event".to_string(), "ReactionCallFailed".to_string()),
                                    ("pageType".to_string(), "OnboardingPage".to_string()),
                                    ("activeLayer".to_string(), "Wasm".to_string())
                                ]
                            }
                        ]
                    );
                },
            )
        }

        #[rstest]
        #[case(true)]
        #[case(false)]
        #[serial]
        fn test_clickstream_events(#[case] is_speedbump: bool) {
            launch_test(
                move |ctx| {
                    onboarding_page(
                        &ctx,
                        OnboardingPageConfig {
                            is_speedbump,
                            ..OnboardingPageConfig::default()
                        },
                    )
                },
                move |scope, mut test_game_loop| {
                    let spy = use_clickstream_emitter_spy(scope);

                    // Select first card
                    select_card(&mut test_game_loop, 0, 0, 0);

                    // Select same card again to unlike
                    select_card(&mut test_game_loop, 0, 0, 0);

                    // Select done button
                    select_action_button(&mut test_game_loop);

                    let calls = spy.recorded_events();

                    // When variation is not SPEEDBUMP, we don't need to report
                    // OPEN event, as it can be deduced with other clickstream events
                    assert_eq!(calls.len(), if is_speedbump { 4 } else { 3 });
                    if is_speedbump {
                        insta::assert_yaml_snapshot!(calls, @r###"
                        - source: ONBOARDING_PAGE
                          name: ONBOARDING_PAGE_SPEEDBUMP_OPEN
                          params: {}
                        - source: ONBOARDING_PAGE
                          name: ONBOARDING_PAGE_SPEEDBUMP_LIKE
                          params:
                            pageTypeId: amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6
                        - source: ONBOARDING_PAGE
                          name: ONBOARDING_PAGE_SPEEDBUMP_REMOVE_LIKE
                          params:
                            pageTypeId: amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6
                        - source: ONBOARDING_PAGE
                          name: ONBOARDING_PAGE_SPEEDBUMP_DONE
                          params: {}
                        "###)
                    } else {
                        insta::assert_yaml_snapshot!(calls, @r###"
                        - source: ONBOARDING_PAGE
                          name: ONBOARDING_PAGE_LIKE
                          params:
                            pageTypeId: amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6
                        - source: ONBOARDING_PAGE
                          name: ONBOARDING_PAGE_REMOVE_LIKE
                          params:
                            pageTypeId: amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6
                        - source: ONBOARDING_PAGE
                          name: ONBOARDING_PAGE_DONE
                          params: {}
                        "###)
                    }
                },
            )
        }

        #[test]
        #[serial]
        fn test_impressions() {
            launch_test(
                move |ctx| onboarding_page(&ctx, OnboardingPageConfig::default()),
                move |scope, mut test_game_loop| {
                    let impression_helper = expect_context::<ImpressionTestHelper>(scope);

                    // Focus first card
                    let tree = test_game_loop.tick_until_done();
                    let first_card = get_card_in_grid_props(tree, 0, 0, 0);
                    test_game_loop.send_on_focus_event(first_card.node_id);

                    // Wait for impression duration
                    MockClock::advance(Duration::from_secs(2));
                    test_game_loop.tick_until_done();

                    // Verify highlight impression
                    let expected_data = ImpressionData {
                        widget_type: Some("titleCard".to_string()),
                        ref_marker: Some("hm_hom_p_THPXkc_brws_2_1".to_string()),
                        content_type: None,
                        benefit_id: None,
                        content_id: Some("amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6".to_string()),
                        creative_id: Some("https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9b1b690399279aa4a2a43fe6154f0ac09790e11ec3b18dd219356922438eea93._UR1920,1080_RI_.png".to_string()),
                        analytics: Some(HashMap::from([(
                            "refMarker".to_string(),
                            "hm_hom_p_THPXkc_brws_2_1".to_string(),
                        )])),
                        slot_id: Some((0, 0)),
                        size: Some((384.0, 216.0)),
                        carousel_analytics: Some("472|CmIKNE9uYm9hcmRpbmdUaXRsZXNTcGVlZEJ1bXBBbHRlcm5hdGVMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTEwTTJTQjlIMDgwVDcaEDI6RFk5QjZBRTkwMUM1RDkiBlRIUFhrYxJECgRob21lEgRob21lIg5wcmlvcml0eUNlbnRlcioAMiRmZTUyNzgxZi0wOGFmLTRkYTQtODZhYi0zYzUxNmZkYTQwNmUaA2FsbCIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQg5Ccm93c2VTdHJhdGVneUoaT25ib2FyZGluZ1ZhcmlhbnRTcGVlZEJ1bXBSC25vdEVudGl0bGVkWgBiCk9uYm9hcmRpbmdoAnIAejhzc2FyODVzNVJhZUVSdEhsbjR5bFlWS0M4TE50U1dwTnF3TDJfVGpKcmFJX24xUkpRVGtZVXc9PYIBA2FsbIoBAJIBAA==".to_string()),
                    };

                    impression_helper.assert_highlight_impression_was_sent(
                        "hm_hom_p_THPXkc_brws_2_1",
                        expected_data.clone(),
                    );

                    // Select card
                    select_card(&mut test_game_loop, 0, 0, 0);

                    // Verify select impression
                    impression_helper.assert_select_impression_was_sent(
                        "hm_hom_p_THPXkc_brws_2_1",
                        expected_data.clone(),
                    );

                    // Verify view impression
                    impression_helper.assert_view_impression_was_sent_for_ref_marker(
                        "hm_hom_p_THPXkc_brws_2_1",
                        expected_data,
                    );

                    assert_eq!(impression_helper.get_view_impressions_count(), 12);
                },
            )
        }
    }

    #[derive(Clone)]
    struct MockToastContext {
        message: RwSignal<Option<TextContent>>,
        visible: RwSignal<bool>,
    }

    impl MockToastContext {
        fn new(scope: Scope) -> Self {
            Self {
                message: create_rw_signal(scope, None),
                visible: create_rw_signal(scope, false),
            }
        }
    }

    #[test]
    #[serial]
    fn test_display_error_toast() {
        launch_only_app_context(move |ctx| {
            let scope = ctx.scope();
            let mock_toast = MockToastContext::new(scope);
            let message_signal = mock_toast.message;
            let visible_signal = mock_toast.visible;

            provide_context(
                scope,
                ToastContext {
                    message: message_signal,
                    visible: visible_signal,
                },
            );

            display_error_toast(scope);

            assert!(visible_signal.get());
            if let Some(TextContent::LocalizedText(text)) = message_signal.get() {
                assert_eq!(
                    text.string_id.as_str(),
                    "AV_LRC_REACTION_ERROR_TOAST",
                    "Toast should have correct error message"
                );
            } else {
                panic!("Expected LocalizedText variant");
            }
        });
    }
}
