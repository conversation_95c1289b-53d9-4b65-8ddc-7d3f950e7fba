use std::{cell::Cell, collections::HashSet, rc::Rc};

use container_types::{
    container_item_parsing::schedule_utils::get_progress_ms,
    ui_signals::{HeroItemModel, StandardCardContainerItemModel, UpdatableContainerItemType},
};
use current_time::CurrentTimeContext;
use ignx_compositron::{prelude::*, reactive::StoredValue};
use linear_common::util::schedule_util::{find_current_item_from_schedule, get_duration_minutes};
use media_background::types::MediaBackgroundType;
use title_details::core::TitleDetailsData;

pub fn create_update_items_effect(
    updatable_items: StoredValue<HashSet<UpdatableContainerItemType>>,
    scope: Scope,
    reload_td_mb: Rc<dyn Fn()>,
) {
    let Some(current_time) =
        use_context::<CurrentTimeContext>(scope).map(|context| context.current_time)
    else {
        // Can't refresh if not aware of the time
        return;
    };

    create_effect(scope, move |prev_time| {
        // refresh each time change
        let current_time = current_time.get();

        let items = updatable_items.get_value();

        // Track whether any items backgrounds have changed
        let card_background_updated = Cell::new(false);
        // Update items
        items
            .iter()
            .for_each(|updatable_item| match updatable_item {
                UpdatableContainerItemType::StandardCard(item_model) => update_standard_card(
                    item_model,
                    current_time,
                    prev_time,
                    &card_background_updated,
                ),
                UpdatableContainerItemType::Hero(hero_model) => {
                    update_hero_card_with_current_program(
                        hero_model,
                        current_time,
                        prev_time,
                        &card_background_updated,
                    )
                }
            });

        if card_background_updated.get() {
            // Update title details and media background once items are updated
            // Title Details and Media Background components do not re-render if no changes are detected to focused item
            reload_td_mb();
        }
        current_time
    });
}

// This accepts any standard card, but will only apply updates to cards with `linear_schedule_data`
fn update_standard_card(
    item_sig: &RwSignal<StandardCardContainerItemModel>,
    current_time: i64,
    prev_time: Option<i64>,
    card_background_updated: &Cell<bool>,
) {
    let (carousel_card_data, media_background_data, title_details_data) =
        item_sig.with_untracked(|model| {
            (
                model.carousel_card_data,
                model.media_background_data,
                model.title_details_data,
            )
        });
    let ui_data =
        carousel_card_data.with_untracked(|card_data| card_data.card_ui_data.get_untracked());

    let Some(current) = carousel_card_data.with_untracked(|card_data| {
        find_current_item_from_schedule(&card_data.linear_schedule_data, current_time).cloned()
    }) else {
        // if there is no current airing, don't update anything
        return;
    };

    let (start_time, end_time) = (current.start_time, current.end_time);

    // If the current airing has changed since last update, metadata should be updated
    let is_new_airing = prev_time.is_none_or(|prev_time| prev_time < start_time);

    if is_new_airing {
        // Flag that there is a card with new background metadata
        card_background_updated.set(true);

        let TitleDetailsData::CollectionAndDetailsPageLayout(ref title_details) =
            current.title_details
        else {
            log::error!("[Collections][UpdateStandardCard]unexpected title details type in item {}! Returning early", item_sig.with_untracked(|i| i.carousel_card_data.with_untracked(|i| i.metadata.with_untracked(|i| i.id.clone()))));
            return;
        };

        title_details_data.set(title_details.clone());
        media_background_data.set(current.media_background.clone());
        ui_data
            .card_image_base_attributes
            .set(current.card_image_attributes.clone());
        ui_data.card_image.set(current.card_image.clone());
        ui_data
            .tts_details_data
            .set(Some(current.tts_details.clone()));
        ui_data.badge_props.set(current.tile_badge.clone());
    }

    // update the progress
    let current_time = u64::try_from(current_time);
    let start_time = u64::try_from(start_time);
    let end_time = u64::try_from(end_time);
    let progress = if let (Ok(c), Ok(s), Ok(e)) = (current_time, start_time, end_time) {
        get_progress_ms(c, s, e)
    } else {
        log::error!("[Collections][UpdateStandardCard] could not convert current_time {:?}, start_time {:?} or end_time {:?} from i64 to u64. Progress bar will be set to None", current_time, start_time, end_time);
        None
    };

    if let Some(progress_props) = ui_data.progress_bar_props.get_untracked() {
        if let Some(progress) = progress {
            progress_props.progress.set(progress);
        }
        if is_new_airing {
            progress_props
                .tts_total_minutes
                .set(get_duration_minutes(current.start_time, current.end_time));
        }
    }
}

// This accepts any hero card, but will only apply updates to heros with `linear_schedule_data`
fn update_hero_card_with_current_program(
    item: &HeroItemModel,
    current_time: i64,
    prev_time: Option<i64>,
    card_background_updated: &Cell<bool>,
) {
    let Some(current) = find_current_item_from_schedule(&item.linear_schedule_data, current_time)
    else {
        // if there is no current airing, don't update anything
        return;
    };

    // If the current airing has changed since last update, metadata should be updated
    let is_new_airing = prev_time.is_none_or(|prev_time| prev_time < current.start_time);

    if is_new_airing {
        // Flag that there is a card with new background metadata
        card_background_updated.set(true);

        // Linear Station Hero uses standard hero background
        item.media_background_data
            .update(|background| match background {
                MediaBackgroundType::StandardHero(ref mut data) => {
                    data.image_url = current.background_image.clone()
                }
                _ => {
                    log::error!{"Failed to update hero card background. Item media background is not StandardHero"}
                }
            });

        // wrapping with `update` triggers the hero to re-render if focused
        item.ui_data.update(|ui_data| {
            ui_data.metadata_rows.set(current.metadata_rows.clone());
            ui_data
                .maturity_rating_string
                .set(current.maturity_rating_string.clone());
            ui_data.synopsis.set(current.synopsis.clone());
            ui_data
                .legal_messages
                .set(vec![current.content_descriptors_string.clone()]);
            ui_data
                .title_data
                .update(|title_data| title_data.title_text = current.title_text.clone());
        });
    }
}

#[cfg(test)]
mod tests {
    use common_transform_types::actions::{PlaybackAction, TransitionAction};
    use container_types::ui_signals::{
        CommonCarouselCardMetadata, HeroCardLinearAiringUIData, HeroItemUIData, StandardCardData,
        StandardCardLinearAiringUIData, StandardCardUIData,
    };
    use contextual_menu_types::prelude::ContextualMenuMetadata;
    use cross_app_events::ImpressionData;
    use fableous::{
        buttons::primary_button::PrimaryButtonVariant,
        collaged_image::ImageCollageBaseAttributes,
        progress_bar::ProgressBarVariant,
        tiles::tile::{TileBadgeProps, TileProgressBarProps},
    };
    use ignx_compositron::{app::launch_only_scope, text::TextContent};
    use liveliness_types::Liveliness;
    use media_background::types::{
        AutoplayInteractionSource, MediaBackgroundType, MediaStrategy, RotationDirection,
        StandardBackgroundData, StandardHeroBackgroundData,
    };
    use rstest::rstest;
    use title_details::types::common::{
        MetadataRowType, StandardTitleDetailsData, TitleDetailsMetadata,
    };
    use title_details::{core::TitleDetailsData, types::common::TTSTitleDetailsData};
    use uuid::Uuid;

    use super::*;

    const SCHEDULE_START: i64 = 1709760330000;
    const HOUR_IN_MS: i64 = 3_600_000;

    fn mock_live_linear_card(scope: Scope) -> RwSignal<StandardCardContainerItemModel> {
        create_rw_signal(
            scope,
            StandardCardContainerItemModel {
                title_details_data: create_rw_signal(
                    scope,
                    StandardTitleDetailsData {
                        title_data: title_details::types::common::TitleData {
                            title_art_url: None,
                            provider_logo_url: None,
                            title_text: "title0".to_string(),
                        },
                        metadata: TitleDetailsMetadata::None,
                        synopsis: None,
                        entitlement_data: Default::default(),
                    },
                ),
                media_background_data: create_rw_signal(
                    scope,
                    MediaBackgroundType::Standard(StandardBackgroundData {
                        id: "id0".to_string(),
                        image_url: None,
                        video_id: None,
                        enter_immediately: false,
                        placement: "PVBrowse".to_string(),
                        media_strategy: MediaStrategy::Linear,
                        csm_data: None,
                        interaction_source_override: Some(
                            AutoplayInteractionSource::HomePageLinearCarousel,
                        ),
                    }),
                ),
                carousel_card_data: create_rw_signal(
                    scope,
                    StandardCardData {
                        metadata: create_rw_signal(
                            scope,
                            CommonCarouselCardMetadata::from_id_and_action(
                                String::default(),
                                TransitionAction::player(PlaybackAction::create_populated_action(
                                    "SomeUri",
                                )),
                            ),
                        ),
                        card_ui_data: create_rw_signal(
                            scope,
                            StandardCardUIData {
                                title: create_rw_signal(scope, None),
                                subtitle: create_rw_signal(scope, None),
                                progress_bar_props: create_rw_signal(
                                    scope,
                                    Some(TileProgressBarProps {
                                        progress: create_rw_signal(scope, 0.0),
                                        variant: ProgressBarVariant::OnAir.into(),
                                        tts_total_minutes: create_rw_signal(scope, Some(60)),
                                    }),
                                ),
                                badge_props: create_rw_signal(scope, None),
                                icon_props: create_rw_signal(scope, None),
                                card_image: create_rw_signal(scope, Some("cardImage0".to_string())),
                                provider_logo_props: create_rw_signal(scope, None),
                                fallback_text: create_rw_signal(scope, None),
                                text_overlay_position: create_rw_signal(scope, None),
                                tts_details_data: create_rw_signal(
                                    scope,
                                    Some(TTSTitleDetailsData::default()),
                                ),
                                card_secondary_base_image: create_rw_signal(scope, None),
                                card_image_base_attributes: create_rw_signal(
                                    scope,
                                    Default::default(),
                                ),
                                item_type_test_id: create_rw_signal(scope, Default::default()),
                            },
                        ),
                        linear_schedule_data: vec![
                            mock_linear_airing(0),
                            mock_linear_airing(1),
                            mock_linear_airing(2),
                        ],
                    },
                ),
                contextual_menu_metadata: create_rw_signal(scope, Default::default()),
            },
        )
    }

    fn mock_linear_airing(idx: usize) -> StandardCardLinearAiringUIData {
        let start = SCHEDULE_START + { idx as i64 * HOUR_IN_MS };
        StandardCardLinearAiringUIData {
            start_time: start,
            end_time: start + HOUR_IN_MS,
            card_image: Some(format!("cardImage{}", idx)),
            card_image_attributes: ImageCollageBaseAttributes::default(),
            media_background: MediaBackgroundType::Standard(StandardBackgroundData {
                id: format!("id{}", idx),
                image_url: None,
                video_id: None,
                enter_immediately: false,
                placement: "PVBrowse".to_string(),
                media_strategy: MediaStrategy::Linear,
                csm_data: None,
                interaction_source_override: Some(
                    AutoplayInteractionSource::HomePageLinearCarousel,
                ),
            }),
            title_details: TitleDetailsData::CollectionAndDetailsPageLayout(
                StandardTitleDetailsData {
                    title_data: title_details::types::common::TitleData {
                        title_art_url: None,
                        provider_logo_url: None,
                        title_text: format!("title{}", idx),
                    },
                    metadata: TitleDetailsMetadata::None,
                    synopsis: None,
                    entitlement_data: Default::default(),
                },
            ),
            tts_details: TTSTitleDetailsData::default(),
            tile_badge: Some(TileBadgeProps::from_liveliness(&Liveliness::OnNow)),
        }
    }

    fn mock_linear_hero_ui_data(idx: usize) -> HeroCardLinearAiringUIData {
        let start = SCHEDULE_START + { idx as i64 * HOUR_IN_MS };
        HeroCardLinearAiringUIData {
            start_time: start,
            end_time: start + HOUR_IN_MS,
            metadata_rows: vec![MetadataRowType::PrimaryLabel(TextContent::String(format!(
                "metadata_{}",
                idx
            )))],
            maturity_rating_string: Some(format!("rating_{}", idx)),
            content_descriptors_string: format!("content_descriptors_{}", idx),
            synopsis: Some(format!("synopsis_{}", idx)),
            background_image: Some(format!("background_img_{}", idx)),
            title_text: format!("title_{}", idx),
            tts_data: Default::default(),
        }
    }

    fn mock_linear_hero(scope: Scope) -> HeroItemModel {
        HeroItemModel {
            uuid: Uuid::new_v4(),
            id: "id".to_string(),
            ui_data: create_rw_signal(
                scope,
                HeroItemUIData {
                    title_data: create_rw_signal(
                        scope,
                        title_details::components::title::TitleData {
                            title_art_url: None,
                            provider_logo_url: None,
                            title_text: "title_0".to_string(),
                            size: None,
                            provider_logo_size: None,
                        },
                    ),
                    synopsis: create_rw_signal(scope, Some("synopsis_0".to_string())),
                    progress_bar_percentage: create_rw_signal(scope, None),
                    primary_button_action: create_rw_signal(scope, None),
                    secondary_button_action: create_rw_signal(scope, None),
                    metadata_rows: create_rw_signal(
                        scope,
                        vec![MetadataRowType::PrimaryLabel(TextContent::String(
                            "metadata_0".to_string(),
                        ))],
                    ),
                    primary_button_variant: create_rw_signal(
                        scope,
                        PrimaryButtonVariant::TextSize100(TextContent::String("text".to_string())),
                    ),
                    secondary_button_variant: create_rw_signal(scope, None),
                    legal_messages: create_rw_signal(
                        scope,
                        vec!["content_descriptors_0".to_string()],
                    ),
                    maturity_rating_image: create_rw_signal(scope, None),
                    entitlement_label: create_rw_signal(scope, None),
                    maturity_rating_string: create_rw_signal(scope, Some("rating_0".to_string())),
                    regulatory_label_string: create_rw_signal(scope, None),
                },
            ),
            media_background_data: create_rw_signal(
                scope,
                MediaBackgroundType::StandardHero(StandardHeroBackgroundData {
                    id: "id_0".to_string(),
                    image_url: Some("background_img_0".to_string()),
                    video_id: Some("gti".to_string()),
                    enter_immediately: false,
                    rotation_direction: RotationDirection::NONE,
                    is_expanded: create_rw_signal(scope, false),
                    placement: "StorefrontHero".to_string(),
                    media_strategy: MediaStrategy::Linear,
                    csm_data: None,
                }),
            ),
            gti: create_rw_signal(scope, None),
            contextual_menu_metadata: create_rw_signal(scope, ContextualMenuMetadata::default()),
            impression_data: ImpressionData::default(),
            swift_content_type: None,
            linear_schedule_data: vec![mock_linear_hero_ui_data(0), mock_linear_hero_ui_data(1)],
        }
    }

    #[rstest]
    fn it_updates_linear_card_on_time_change(#[values(true, false)] new_item: bool) {
        launch_only_scope(move |scope| {
            let mock_card = mock_live_linear_card(scope);
            let time_jump = if new_item {
                1.5 * HOUR_IN_MS as f32
            } else {
                0.5 * HOUR_IN_MS as f32
            } as i64;

            let updated = Cell::new(false);
            // Update to middle of next program
            update_standard_card(
                &mock_card,
                SCHEDULE_START + time_jump,
                Some(SCHEDULE_START),
                &updated,
            );

            assert_eq!(updated.get(), new_item);
            let result = mock_card.get_untracked();

            // updated card should match this LinearAiringModel
            let mock_new_airing = mock_linear_airing(new_item.into());

            let TitleDetailsData::CollectionAndDetailsPageLayout(expected_title_details_data) =
                mock_new_airing.title_details
            else {
                panic!("should have correct title details data");
            };

            // validate title details
            assert_eq!(
                result.title_details_data.get_untracked(),
                expected_title_details_data
            );

            // validate media background
            let mb = result.media_background_data.get_untracked();
            assert_eq!(mb, mock_new_airing.media_background);

            // validate ui model
            let ui_data = result
                .carousel_card_data
                .get_untracked()
                .card_ui_data
                .get_untracked();

            let progress_props = ui_data.progress_bar_props.get_untracked().unwrap();
            assert_eq!(progress_props.progress.get_untracked(), 0.5);
            assert_eq!(
                progress_props.tts_total_minutes.get_untracked().unwrap(),
                60
            );

            assert_eq!(
                ui_data.card_image_base_attributes.get_untracked(),
                mock_new_airing.card_image_attributes
            );
            assert_eq!(
                ui_data.card_image.get_untracked(),
                mock_new_airing.card_image
            );
            assert_eq!(
                ui_data.tts_details_data.get_untracked(),
                Some(mock_new_airing.tts_details)
            );
        });
    }

    #[rstest]
    fn it_updates_hero_card_on_time_change(#[values(true, false)] new_item: bool) {
        launch_only_scope(move |scope| {
            let mock_card = mock_linear_hero(scope);
            let time_jump = if new_item {
                1.5 * HOUR_IN_MS as f32
            } else {
                0.5 * HOUR_IN_MS as f32
            } as i64;

            let updated = Cell::new(false);
            // Update to middle of next program
            update_hero_card_with_current_program(
                &mock_card,
                SCHEDULE_START + time_jump,
                Some(SCHEDULE_START),
                &updated,
            );

            assert_eq!(updated.get(), new_item);

            // updated card should match this LinearAiringModel
            let mock_new_hero_ui_data = mock_linear_hero_ui_data(new_item.into());

            // validate media background
            match mock_card.media_background_data.get_untracked() {
                MediaBackgroundType::StandardHero(hero_bg) => {
                    assert_eq!(hero_bg.image_url, mock_new_hero_ui_data.background_image)
                }
                _ => panic!("Media background should be StandardHero type"),
            }

            // validate ui model
            let ui_data = mock_card.ui_data.get_untracked();

            assert_eq!(
                ui_data.maturity_rating_string.get_untracked(),
                mock_new_hero_ui_data.maturity_rating_string
            );
            assert_eq!(
                ui_data.metadata_rows.get_untracked(),
                mock_new_hero_ui_data.metadata_rows
            );
            assert_eq!(
                ui_data.synopsis.get_untracked(),
                mock_new_hero_ui_data.synopsis
            );
            assert_eq!(
                ui_data.legal_messages.get_untracked(),
                vec![mock_new_hero_ui_data.content_descriptors_string]
            );
            assert_eq!(
                ui_data.title_data.get_untracked().title_text,
                mock_new_hero_ui_data.title_text
            );
        });
    }
}
