use container_types::ui_signals::{StandardCardContainerItemType, SuperCarouselItemType};
use ignx_compositron::focus::FocusDirection;
use ignx_compositron::prelude::*;
use media_background::types::MediaBackgroundType;
use title_details::core::{NavigationDirection, TitleDetailsChangeRequest, TitleDetailsData};
use title_details::types::common::StandardTitleDetailsData;

pub fn convert_to_title_details_change_request(
    data: &StandardTitleDetailsData,
    focus_direction: Option<FocusDirection>,
) -> TitleDetailsChangeRequest {
    let navigation_direction = match focus_direction {
        None => NavigationDirection::NONE,
        Some(FocusDirection::Left) => NavigationDirection::LEFT,
        Some(FocusDirection::Right) => NavigationDirection::RIGHT,
        Some(FocusDirection::Up) => NavigationDirection::UP,
        Some(FocusDirection::Down) => NavigationDirection::DOWN,
    };
    TitleDetailsChangeRequest {
        data: TitleDetailsData::CollectionAndDetailsPageLayout(data.clone()),
        navigation_direction,
    }
}

pub fn clear_title_details(update_td: WriteSignal<TitleDetailsChangeRequest>) {
    update_td.set(TitleDetailsChangeRequest {
        data: TitleDetailsData::Empty,
        navigation_direction: NavigationDirection::NONE,
    });
}

pub fn clear_media_background(update_mb: RwSignal<MediaBackgroundType>) {
    update_mb.set(MediaBackgroundType::None);
}

pub fn clear_title_details_and_media_background(
    update_td: WriteSignal<TitleDetailsChangeRequest>,
    update_mb: RwSignal<MediaBackgroundType>,
) {
    clear_title_details(update_td);
    clear_media_background(update_mb);
}

pub fn resizable_super_media_background_is_already_correct(
    item: &SuperCarouselItemType,
    update_mb: RwSignal<MediaBackgroundType>,
) -> bool {
    let media_background_data: RwSignal<MediaBackgroundType> = item.into();
    let prev_mb = update_mb.get_untracked();
    let next_mb = media_background_data.get_untracked();

    if let (
        MediaBackgroundType::ResizableSuperCarousel(prev),
        MediaBackgroundType::ResizableSuperCarousel(next),
    ) = (prev_mb, next_mb)
    {
        return prev.id == next.id && prev.video_id == next.video_id;
    }

    false
}

pub fn update_title_details_media_background_super_carousel_item(
    item: &SuperCarouselItemType,
    update_mb: RwSignal<MediaBackgroundType>,
    update_td: WriteSignal<TitleDetailsChangeRequest>,
) {
    // Preserve media background if possible (Allows for CBDP like behaviour with details page)
    if resizable_super_media_background_is_already_correct(item, update_mb) {
        return;
    }

    clear_title_details_and_media_background(update_td, update_mb);
}

pub fn update_title_details_media_background_standard_carousel_item(
    item: &StandardCardContainerItemType,
    update_td: WriteSignal<TitleDetailsChangeRequest>,
    update_mb: RwSignal<MediaBackgroundType>,
    direction: Option<FocusDirection>,
) {
    let title_details_data: RwSignal<StandardTitleDetailsData> = item.into();
    title_details_data.with_untracked(|data| {
        let td_data: TitleDetailsChangeRequest =
            convert_to_title_details_change_request(data, direction);
        update_td.set(td_data);
    });

    // Preserve media background if possible (Allows for CBDP like behaviour with details page)
    let media_background_data: RwSignal<MediaBackgroundType> = item.into();
    let should_update = match (
        update_mb.get_untracked(),
        media_background_data.get_untracked(),
    ) {
        (MediaBackgroundType::Standard(prev), MediaBackgroundType::Standard(update)) => {
            prev.id != update.id
                || prev.image_url != update.image_url
                || prev.video_id != update.video_id
        }
        (
            MediaBackgroundType::ResizableStandard(prev),
            MediaBackgroundType::ResizableStandard(update),
        ) => {
            prev.id != update.id
                || prev.image_url != update.image_url
                || prev.video_id != update.video_id
        }
        _ => true,
    };

    if should_update {
        media_background_data.with_untracked(|data| {
            update_mb.set(data.clone());
        });
    }
}
