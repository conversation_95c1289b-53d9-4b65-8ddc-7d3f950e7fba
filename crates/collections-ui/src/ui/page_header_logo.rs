use cross_app_events::tti_tracker::TimeToInteractiveTracker;
use fableous::gradients::gradient::*;
use fableous::SCREEN_WIDTH;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::layout::Alignment;
use ignx_compositron::layout::Padding;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageAlignment;
use lrc_image::types::ImageData;
use lrc_image::types::ImageTag;
use lrc_image::types::ScaleToRectangleOptions;
use lrc_image::types::ScalingStrategy;
use lrc_image::types::*;
use std::rc::Rc;

const PAGE_HEADER_LOGO_HEIGHT: f32 = 60.0;
const PAGE_HEADER_LOGO_WIDTH: f32 = 417.0;
const PAGE_HEADER_LOGO_TOP_MARGIN: f32 = 54.0;
const PAGE_HEADER_LOGO_RIGHT_MARGIN: f32 = 96.0;

const TTI_PART_GRADIENT: &str = "PageHeaderLogoGradient";
const TTI_PART_IMAGE: &str = "PageHeaderLogoImage";

#[Composer]
pub fn PageHeaderLogo(
    ctx: &AppContext,
    #[into] url: MaybeSignal<String>,
    on_ready: Rc<dyn Fn()>,
) -> StackComposable {
    let image_data = Signal::derive(ctx.scope(), move || ImageData {
        url: url.get(),
        height: PAGE_HEADER_LOGO_HEIGHT,
        width: PAGE_HEADER_LOGO_WIDTH,
        tags: vec![
            ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions {
                alignment: ImageAlignment::Right,
                hide_canvas: true,
            })),
            ImageTag::Format(ImageFormat::PNG),
        ],
    });

    let tti_tracker = TimeToInteractiveTracker::new(
        vec![TTI_PART_GRADIENT.into(), TTI_PART_IMAGE.into()],
        move || on_ready(),
    );

    let set_part_ready = Rc::new({
        move |res: &ImageLoadResultEvent, part: String| {
            if res == &ImageLoadResultEvent::Success {
                tti_tracker.set_part_ready(part)
            }
        }
    });

    let on_gradient_result = {
        let gradient_ready = Rc::clone(&set_part_ready);
        move |res: &ImageLoadResultEvent| gradient_ready(res, TTI_PART_GRADIENT.into())
    };

    let on_image_result = {
        let image_ready = Rc::clone(&set_part_ready);
        move |res: &ImageLoadResultEvent| image_ready(res, TTI_PART_IMAGE.into())
    };

    compose! {
        Stack() {
            Gradient(gradient: GradientName::HeroScrimAttribution, width: 723.0, height: 407.0)
                .on_image_result(on_gradient_result)
            Stack() {
                LRCImage(data: image_data)
                .on_image_result(on_image_result)
            }
            .padding(Padding::new(0.0, PAGE_HEADER_LOGO_RIGHT_MARGIN, PAGE_HEADER_LOGO_TOP_MARGIN, 0.0))
        }
        .alignment(Alignment::EndTop)
        .width(SCREEN_WIDTH)
    }
}
