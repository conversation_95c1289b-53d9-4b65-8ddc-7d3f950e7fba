use amzn_fable_tokens::{FableColor, FableSpacing};
use fableous::buttons::primary_button::*;
use fableous::{
    buttons::primary_button::PrimaryButtonVariant, typography::typography::*, utils::get_ignx_color,
};
use ignx_compositron::time::{Duration, Instant};
use ignx_compositron::{compose, Composer};
use ignx_compositron::{
    prelude::*,
    text::{LocalizedText, TextContent},
};
use navigation_menu::ui::utility_nav::UTILITY_NAV_COLLAPSED_WIDTH;
use router::hooks::use_navigation;
use router::rust_location;

// GRCOV_BEGIN_COVERAGE
const AV_LRC_MY_STUFF_EMPTY_STATE_DETAILS: &str = "AV_LRC_MY_STUFF_EMPTY_STATE_DETAILS";
const AV_LRC_MY_STUFF_TITLE: &str = "AV_LRC_MY_STUFF_TITLE";
const AV_LRC_MY_STUFF_EMPTY_STATE_BUTTON: &str = "AV_LRC_MY_STUFF_EMPTY_STATE_BUTTON";
#[Composer]
pub fn MyStuffEmptyCollectionsPage(ctx: &AppContext) -> StackComposable {
    let title = TextContent::LocalizedText(LocalizedText::new(AV_LRC_MY_STUFF_TITLE));

    let message =
        TextContent::LocalizedText(LocalizedText::new(AV_LRC_MY_STUFF_EMPTY_STATE_DETAILS));

    let scope = ctx.scope();
    let on_select = move || {
        let navigate = use_navigation(scope);
        navigate(
            rust_location!(RUST_COLLECTIONS, {"pageType" => "home", "pageId" => "home"}),
            "MY_STUFF_EMPTY_PAGE",
        );
    };

    let browse_home_text = PrimaryButtonVariant::TextSize400(TextContent::LocalizedText(
        LocalizedText::new(AV_LRC_MY_STUFF_EMPTY_STATE_BUTTON),
    ));
    let button_focused = create_focus_signal(ctx.scope());

    // We follow the same hack as in page_ui_sig to force the focus to be on the page.
    // Check comment with "SCHEDULING A TASK HERE IS A HACK" there.
    ctx.schedule_task(Instant::now() + Duration::from_millis(100), move || {
        button_focused.try_set(true);
    });

    compose! {
        Stack() {
            Column() {
                TypographyHeading600(content: title)
                TypographyBody400(content: message)
                    .width(798.0)
                Row() {
                    PrimaryButton(variant: browse_home_text)
                    .focused(button_focused)
                    .on_select(on_select)
                }.padding(Padding::vertical(30.0))
            }
            .width(fableous::SCREEN_WIDTH)
            .height(fableous::SCREEN_HEIGHT)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
            .padding(Padding::new(UTILITY_NAV_COLLAPSED_WIDTH, 90.0, 172.0, 0.0))
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
            .test_id("empty-my-stuff-collections")
        }
    }
}
