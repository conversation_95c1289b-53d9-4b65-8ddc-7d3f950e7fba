pub mod containers;
pub mod helpers;
pub mod my_stuff_empty_page;
pub mod notifications;
pub mod onboarding_page_ui;
pub mod page_header_logo;
pub mod page_skeleton;
pub mod page_title;
pub mod page_ui_sig;
pub mod traits;
pub mod types;

mod my_stuff_empty_page_test;

pub(crate) const TTI_PART_PAGE_HEADER_OR_TITLE: &str = "PageHeaderOrTitle";
pub(crate) const TTI_PART_DEFAULT_FOCUS: &str = "DefaultFocus";
pub(crate) const TTI_PART_MEDIA_BACKGROUND: &str = "MediaBackground";
pub(crate) const TTI_PART_TITLE_DETAILS: &str = "TitleDetails";
pub(crate) const TTI_PART_CONTAINERS: &str = "Containers";

#[cfg(test)]
mod tests {
    use crate::network::parser::collections_response_parser;
    use common_transform_types::containers::Container;
    use common_transform_types::resiliency::WithResiliency;
    use network::common::DeviceProxyResponse;
    use std::collections::HashSet;
    use strum::IntoEnumIterator;

    #[test]
    fn verify_all_containers_present() {
        /*
            ==== READ ME ====
            If this test is failing, you have likely added a new container type that is
            not included in the mocked collections initial response located in:
            `collections-ui/src/test_assets/collections_page_all_container_types.json`
            To fix this, you'll need to have access to mock data for your container type
            that can currently be parsed and add it to the JSON file.

            Run the example and check that all containers look okay with:
            build-tools/run collections-ui --additional-features example_data --example collections_all_containers --wasm
            or the following (does not rebuild)
            build-tools/launch_vscode_target.sh -c "\u00A0\u00A0Collections All Containers (Debug)"

            If you have a valid reason to exclude the container type you can add the
            container type to the list of types being filtered out in 'expected_container_types`
        */
        fn expected_container_types() -> Vec<Container> {
            Container::iter()
                .filter(|c| {
                    !matches!(
                        c,
                        // Filter out containers that have not yet been implemented or are not part of
                        // the storefront collections page
                        Container::NEXT_BEST_ACTION_HINT(_)
                            | Container::CHANNEL_NAVIGATION(_)
                            | Container::DETAILS_ACTIONS(_)
                            | Container::VOD_EXTRA_CONTENT(_)
                            | Container::ENTITY_CAROUSEL(_)
                            | Container::TEXT_WIDGET(_)
                            | Container::ONBOARDING(_)
                            | Container::ACTION_BUTTON_COLLECTION(_)
                            | Container::FULL_SCREEN_HERO(_)
                            | Container::GRID(_)
                            | Container::EPG(_)
                            | Container::COVER(_)
                            | Container::SCHEDULE_CAROUSEL(_)
                    )
                })
                .collect()
        }

        let mocked_response =
            include_str!("../test_assets/collections_page_all_container_types.json").to_string();
        let expected_containers: HashSet<String> = expected_container_types()
            .into_iter()
            .map(|c| c.type_id())
            .collect();
        let page_data = collections_response_parser(mocked_response)
            .map(|result| match result {
                DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                DeviceProxyResponse::ErrorResponse(_) => {
                    panic!("unexpected error response")
                }
            })
            .unwrap();

        let mut seen = HashSet::new();
        for container in page_data.containerList {
            if let WithResiliency::Ok(c) = container {
                seen.insert(c.type_id());
            }
        }
        assert_eq!(
            seen,
            expected_containers,
            "Missing containers in mocked response: {:?}. Please add missing containers to the response data in {:?}",
            expected_containers.difference(&seen).cloned().collect::<Vec<_>>(),
            "crates/collections-ui/examples/assets/all_container_types.json".to_string()
        );
    }
}
