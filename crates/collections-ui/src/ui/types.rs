use cacheable_derive::Cacheable;
use cfg_test_attr_derive::derive_test_only;
use container_types::ui_signals::{
    CardGridModel, ContainerModel, ContainerModelCacheable, DiscoveryAssistantModel,
    EntityCarouselModel, NodesCarouselModel, PromoBannerModel,
};
use core::option::Option;
use ignx_compositron::id::Id;
use ignx_compositron::prelude::*;
use playback_navigation::types::PlaybackOrigin;

#[derive(Clone, Cacheable)]
pub struct ContainerType {
    pub model: RwSignal<ContainerModel>,
    pub id: String,
}

pub trait ToContainerType {
    fn to_container_type(self, id: String, scope: Scope) -> ContainerType;
}

impl ContainerType {
    pub(crate) fn transform_container_id(&self) -> &'_ str {
        &self.id
    }
}

impl Id for ContainerType {
    type Id = String;

    fn id(&self) -> &Self::Id {
        /// This is unique enough for now list composables that require unique ids, but prone
        /// to break if containers get duplicated
        &self.id
    }
}

impl PartialEq for ContainerType {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

macro_rules! impl_to_container_types {
    ($(($type_name:ident, $variant_name:ident)),* $(,)?) => {
        $(
            impl ToContainerType for $type_name {
                fn to_container_type(self, id: String, scope: Scope) -> ContainerType {
                    ContainerType {
                        model: create_rw_signal(
                            scope,
                            ContainerModel::$variant_name(create_rw_signal(scope, self)),
                        ),
                        id,
                    }
                }
            }
        )*
    };
}

impl_to_container_types!(
    (DiscoveryAssistantModel, DiscoveryAssistant),
    (PromoBannerModel, PromoBanner),
    (NodesCarouselModel, NodesCarousel),
    (CardGridModel, Grid),
    (EntityCarouselModel, EntityCarousel),
);

#[derive_test_only(Debug, Default, PartialEq)]
pub struct SeamlessPlaybackContext {
    pub enabled: bool,
    pub origin: Option<PlaybackOrigin>,
}
