use amzn_fable_tokens::FableColor;
use fableous::gradients::gradient::*;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::SCREEN_WIDTH;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::layout::Alignment;
use ignx_compositron::layout::Padding;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::text::TextVerticalAlignment;
use ignx_compositron::{compose, Composer};
use std::rc::Rc;

const PAGE_TITLE_LOGO_HEIGHT: f32 = 70.0;
const PAGE_TITLE_LOGO_WIDTH: f32 = 546.0;
const PAGE_TITLE_LOGO_TOP_MARGIN: f32 = 54.0;
const PAGE_TITLE_LOGO_RIGHT_MARGIN: f32 = 96.0;

#[Composer]
pub fn PageTitle(
    ctx: &AppContext,
    #[into] page_title: MaybeSignal<String>,
    on_ready: Rc<dyn Fn()>,
) -> StackComposable {
    compose! {
        Stack () {
            Gradient(gradient: GradientName::HeroScrimAttribution, width: 723.0, height: 407.0)
                .on_image_result({
                    move |res: &ImageLoadResultEvent| if res == &ImageLoadResultEvent::Success { on_ready() }
                })
            Stack() {
                TypographyHeading100(content: page_title.clone())
                .color(get_ignx_color(FableColor::PRIMARY))
                .max_width(PAGE_TITLE_LOGO_WIDTH)
                .max_height(PAGE_TITLE_LOGO_HEIGHT)
                .vertical_alignment(TextVerticalAlignment::Start)
            }
            .padding(Padding::new(0.0, PAGE_TITLE_LOGO_RIGHT_MARGIN, PAGE_TITLE_LOGO_TOP_MARGIN, 0.0))
            .height(PAGE_TITLE_LOGO_HEIGHT)
        }
        .alignment(Alignment::EndTop)
        .width(SCREEN_WIDTH)
    }
}
