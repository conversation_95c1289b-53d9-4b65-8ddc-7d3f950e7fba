use crate::ui::types::ContainerType;
use crate::ui::types::SeamlessPlaybackContext;
use crate::utils::linear_autoplay_util::seamless_playback_context_for_standard_card;
use container_types::ui_signals::SpecialCollectionsCardModel;
use container_types::ui_signals::SpecialCollectionsDetailsModel;
use container_types::ui_signals::{
    CarouselIndexData, ChartsCarouselItemType, ClickstreamData, CommonCarouselCardMetadata,
    ContainerItem, ContainerModel, ContainerModelDiscriminants, DiscoveryAssistantGridItemModel,
    DiscoveryInfoColumn, FocusRingProperties, HeroItemModel, ImpressionsData,
    NodesCarouselCardData, PromoBannerButtonData, ScheduleCardItemType, ScheduleCarouselData,
    ShortCarouselCardData, SportsCardData, StandardCardContainerItemListType,
    StandardCardContainerItemType, SuperCarouselItemType, Transitionable,
    UpdatableContainerItemType,
};
use containers::focus_ring::get_focus_ring_properties_for_container_sig;
use contextual_menu_types::prelude::ContextualMenuMetadata;
use ignx_compositron::prelude::{metric, RwSignal, Scope, SignalGetUntracked, SignalWithUntracked};
use ignx_compositron::reactive::StoredValue;
use location::{PageType, RustPage};

fn get_opacity_for_index(container_modal: ContainerModelDiscriminants, index_offset: i32) -> f32 {
    match container_modal {
        ContainerModelDiscriminants::StandardCarousel
        | ContainerModelDiscriminants::BeardSupportedCarousel
        | ContainerModelDiscriminants::ChartsCarousel
        | ContainerModelDiscriminants::ScheduleCarousel => match index_offset {
            0 => 1.0,
            1 => 0.5,
            _ => 0.0,
        },
        ContainerModelDiscriminants::PromoBanner
        | ContainerModelDiscriminants::SuperCarousel
        | ContainerModelDiscriminants::NodesCarousel
        | ContainerModelDiscriminants::EntityCarousel
        | ContainerModelDiscriminants::Grid
        | ContainerModelDiscriminants::DiscoveryAssistant
        | ContainerModelDiscriminants::DiscoveryAssistantHeader => match index_offset {
            0 => 1.0,
            -1 | 1 => 0.5,
            _ => 0.0,
        },
        ContainerModelDiscriminants::ShortCarousel => match index_offset {
            0 => 1.0,
            -1 | 1 | 2 => 0.5,
            _ => 0.0,
        },
        ContainerModelDiscriminants::TentpoleHero
        | ContainerModelDiscriminants::StandardHero
        | ContainerModelDiscriminants::Onboarding => match index_offset {
            0 | 1 => 1.0,
            2 => 0.5,
            _ => 0.0,
        },
        ContainerModelDiscriminants::SpecialCollectionsCarousel => 1.0,
    }
}

impl ContainerItem for ContainerType {
    fn get_opacity_for_index(&self, index_offset: i32) -> f32 {
        let model_ref: ContainerModelDiscriminants =
            self.model.with_untracked(|model_ref| model_ref.into());
        get_opacity_for_index(model_ref, index_offset)
    }

    fn get_focus_ring_properties(
        &self,
        data: CarouselIndexData,
        is_tts_enabled: bool,
    ) -> Option<FocusRingProperties> {
        get_focus_ring_properties_for_container_sig(
            self.model.get_untracked(),
            data,
            is_tts_enabled,
        )
    }
}

pub(crate) trait WatchModalLaunch {
    fn get_watch_modal_launch_data(&self) -> Option<ContextualMenuMetadata>;
}

macro_rules! impl_watch_modal_none_for {
    ($($item_type:ty),*) => {
        $(
            impl WatchModalLaunch for $item_type {
                fn get_watch_modal_launch_data(&self) -> Option<ContextualMenuMetadata> {
                    None
                }
            }
        )*
    }
}

impl_watch_modal_none_for!(
    RwSignal<DiscoveryAssistantGridItemModel>,
    RwSignal<DiscoveryInfoColumn>,
    StoredValue<CommonCarouselCardMetadata>,
    RwSignal<ChartsCarouselItemType>,
    RwSignal<ShortCarouselCardData>,
    RwSignal<SportsCardData>,
    NodesCarouselCardData,
    RwSignal<HeroItemModel>, // although the hero *can* launch the watch modal, it's handled inside the hero component
    RwSignal<SpecialCollectionsCardModel>,
    RwSignal<SpecialCollectionsDetailsModel>
);

impl WatchModalLaunch for RwSignal<StandardCardContainerItemType> {
    fn get_watch_modal_launch_data(&self) -> Option<ContextualMenuMetadata> {
        self.with_untracked(|a| match a {
            StandardCardContainerItemType::Series(_)
            | StandardCardContainerItemType::GenericTitleCard(_)
            | StandardCardContainerItemType::SeeMoreLink(_)
            | StandardCardContainerItemType::Hint(_)
            | StandardCardContainerItemType::Show(_)
            | StandardCardContainerItemType::Movie(_)
            | StandardCardContainerItemType::LiveLinear(_)
            | StandardCardContainerItemType::Link(_)
            | StandardCardContainerItemType::BonusSchedule(_)
            | StandardCardContainerItemType::VodExtraContent(_) => None,
            StandardCardContainerItemType::Event(_) => {
                let cm_metadata: RwSignal<ContextualMenuMetadata> = a.into();
                Some(cm_metadata.get_untracked())
            }
        })
    }
}

impl WatchModalLaunch for RwSignal<SuperCarouselItemType> {
    fn get_watch_modal_launch_data(&self) -> Option<ContextualMenuMetadata> {
        self.with_untracked(|a| match a {
            SuperCarouselItemType::Series(_)
            | SuperCarouselItemType::Show(_)
            | SuperCarouselItemType::Movie(_)
            | SuperCarouselItemType::GenericTitleCard(_)
            | SuperCarouselItemType::Link(_) => None,
            SuperCarouselItemType::Event(_) => {
                let cm_metadata: RwSignal<ContextualMenuMetadata> = a.into();
                Some(cm_metadata.get_untracked())
            }
        })
    }
}

impl WatchModalLaunch for ScheduleCarouselData {
    fn get_watch_modal_launch_data(&self) -> Option<ContextualMenuMetadata> {
        match self {
            ScheduleCarouselData::Card(card_item) => card_item.with_untracked(|item| match item {
                ScheduleCardItemType::Event(model) => {
                    model.with_untracked(|item| Some(item.contextual_menu_metadata.get_untracked()))
                }
                ScheduleCardItemType::OffPlatform(_) => None,
            }),
            ScheduleCarouselData::Button(_) => None,
        }
    }
}

impl WatchModalLaunch for RwSignal<PromoBannerButtonData> {
    fn get_watch_modal_launch_data(&self) -> Option<ContextualMenuMetadata> {
        None
    }
}

pub(crate) trait SelectMetrics {
    fn report_metrics_on_select(&self);
}

macro_rules! impl_no_select_metrics_for {
    ($($item_type:ty),*) => {
        $(
            impl SelectMetrics for $item_type {
                fn report_metrics_on_select(&self) {}
            }
        )*
    }
}

impl_no_select_metrics_for!(
    RwSignal<DiscoveryAssistantGridItemModel>,
    RwSignal<DiscoveryInfoColumn>,
    StoredValue<CommonCarouselCardMetadata>,
    RwSignal<SuperCarouselItemType>,
    RwSignal<ChartsCarouselItemType>,
    RwSignal<ShortCarouselCardData>,
    RwSignal<SportsCardData>,
    NodesCarouselCardData,
    RwSignal<HeroItemModel>,
    ScheduleCarouselData,
    RwSignal<SpecialCollectionsCardModel>,
    RwSignal<SpecialCollectionsDetailsModel>
);

impl SelectMetrics for RwSignal<StandardCardContainerItemType> {
    fn report_metrics_on_select(&self) {
        self.with_untracked(|a| match a {
            StandardCardContainerItemType::Series(_)
            | StandardCardContainerItemType::GenericTitleCard(_)
            | StandardCardContainerItemType::SeeMoreLink(_)
            | StandardCardContainerItemType::Show(_)
            | StandardCardContainerItemType::Movie(_)
            | StandardCardContainerItemType::LiveLinear(_)
            | StandardCardContainerItemType::Link(_)
            | StandardCardContainerItemType::VodExtraContent(_)
            | StandardCardContainerItemType::Event(_) => {},
            StandardCardContainerItemType::BonusSchedule(_) => {
                let page_source = PageType::Rust(RustPage::RUST_COLLECTIONS).to_string();
                // Emit SportsVOD metric on card click
                metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => "SportsVODCardPressed", "activeLayer" => "Wasm");
            },
            StandardCardContainerItemType::Hint(_) => {}
        })
    }
}
impl SelectMetrics for RwSignal<PromoBannerButtonData> {
    fn report_metrics_on_select(&self) {
        metric!("PageAction.Count", 1, "pageType" => "Rust", "actionName" => "PromoBannerButtonPressed", "activeLayer" => "Wasm")
        // TODO: We could probably emit more granular metrics (i.e. which button was pressed): https://issues.amazon.com/issues/LR-Rust-660
    }
}

pub(crate) trait SeamlessTransition {
    // Determines if a transition to JS playback page should happen seamlessly
    fn seamless_transition_to_playback(&self, scope: Scope) -> SeamlessPlaybackContext;
}

impl SeamlessTransition for RwSignal<StandardCardContainerItemType> {
    fn seamless_transition_to_playback(&self, scope: Scope) -> SeamlessPlaybackContext {
        self.with_untracked(|item_type| {
            seamless_playback_context_for_standard_card(item_type, scope)
        })
    }
}

macro_rules! impl_seamless_transition_other {
    ($($item_type:ty),*) => {
        $(
            impl SeamlessTransition for $item_type {
                fn seamless_transition_to_playback(&self, _scope: Scope) -> SeamlessPlaybackContext {
                    SeamlessPlaybackContext {enabled: false, origin: None}
                }
            }
        )*
    }
}

impl_seamless_transition_other!(
    RwSignal<DiscoveryAssistantGridItemModel>,
    RwSignal<DiscoveryInfoColumn>,
    StoredValue<CommonCarouselCardMetadata>,
    RwSignal<SuperCarouselItemType>,
    RwSignal<ChartsCarouselItemType>,
    RwSignal<ShortCarouselCardData>,
    RwSignal<SportsCardData>,
    NodesCarouselCardData,
    RwSignal<HeroItemModel>,
    RwSignal<PromoBannerButtonData>,
    ScheduleCarouselData,
    RwSignal<SpecialCollectionsCardModel>,
    RwSignal<SpecialCollectionsDetailsModel>
);

pub(crate) trait Selectable:
    Transitionable
    + ImpressionsData
    + WatchModalLaunch
    + SelectMetrics
    + ClickstreamData
    + SeamlessTransition
{
}

macro_rules! impl_selectable_for {
    ($($item_type:ty),*) => {
        $(
            impl Selectable for $item_type {}
        )*
    }
}

impl_selectable_for!(
    RwSignal<DiscoveryAssistantGridItemModel>,
    RwSignal<DiscoveryInfoColumn>,
    StoredValue<CommonCarouselCardMetadata>,
    RwSignal<StandardCardContainerItemType>,
    RwSignal<SuperCarouselItemType>,
    RwSignal<ChartsCarouselItemType>,
    RwSignal<ShortCarouselCardData>,
    RwSignal<SportsCardData>,
    NodesCarouselCardData,
    RwSignal<HeroItemModel>,
    RwSignal<PromoBannerButtonData>,
    ScheduleCarouselData,
    RwSignal<SpecialCollectionsCardModel>,
    RwSignal<SpecialCollectionsDetailsModel>
);

pub(crate) trait UpdatableItems {
    fn get_updatable_items(&self) -> Vec<UpdatableContainerItemType>;
}

impl UpdatableItems for ContainerModel {
    fn get_updatable_items(&self) -> Vec<UpdatableContainerItemType> {
        match self {
            ContainerModel::StandardCarousel(model_sig) => model_sig
                .with_untracked(|model| get_updatable_items_for_standard_cards(model.items)),
            ContainerModel::Grid(model_sig) => model_sig
                .with_untracked(|model| get_updatable_items_for_standard_cards(model.items)),
            ContainerModel::Onboarding(model_sig) => model_sig
                .with_untracked(|model| get_updatable_items_for_standard_cards(model.items)),
            ContainerModel::BeardSupportedCarousel(model_sig) => model_sig
                .with_untracked(|model| get_updatable_items_for_standard_cards(model.items)),
            ContainerModel::StandardHero(model_sig) => {
                model_sig.with_untracked(|model| get_updatable_items_for_hero_items(model.items))
            }
            ContainerModel::SuperCarousel(_)
            | ContainerModel::ChartsCarousel(_)
            | ContainerModel::NodesCarousel(_)
            | ContainerModel::EntityCarousel(_)
            | ContainerModel::ShortCarousel(_)
            | ContainerModel::TentpoleHero(_)
            | ContainerModel::DiscoveryAssistant(_)
            | ContainerModel::DiscoveryAssistantHeader(_)
            | ContainerModel::PromoBanner(_)
            | ContainerModel::ScheduleCarousel(_)
            | ContainerModel::SpecialCollectionsCarousel(_) => vec![],
        }
    }
}

fn get_updatable_items_for_standard_cards(
    items: RwSignal<Vec<StandardCardContainerItemListType>>,
) -> Vec<UpdatableContainerItemType> {
    items.with_untracked(|items| {
        items
            .iter()
            .filter_map(|item| {
                item.model.with_untracked(|item| {
                    if item.has_schedule_data() {
                        Some(UpdatableContainerItemType::StandardCard(
                            item.get_item_model(),
                        ))
                    } else {
                        None
                    }
                })
            })
            .collect()
    })
}

fn get_updatable_items_for_hero_items(
    items: RwSignal<Vec<HeroItemModel>>,
) -> Vec<UpdatableContainerItemType> {
    items.with_untracked(|items| {
        items
            .iter()
            .filter_map(|item| {
                // only items with schedule data are updatable
                (item.linear_schedule_data.len() > 0)
                    .then_some(UpdatableContainerItemType::Hero(item.clone()))
            })
            .collect()
    })
}

#[cfg(test)]
mod test {
    use crate::ui::traits::{get_opacity_for_index, WatchModalLaunch};
    use container_types::ui_signals::{
        CommonCarouselCardMetadata, ContainerModelDiscriminants, PromoBannerButtonData,
    };
    use ignx_compositron::{app::launch_only_scope, prelude::create_rw_signal};
    use rstest::rstest;

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.5)]
    #[case(1, 0.5)]
    #[case(2, 0.5)]
    #[case(123, 0.0)]
    pub fn adjacent_opacities_when_focused_short_carousel(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::ShortCarousel, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.5)]
    // The container at offset=-2 should be 0.0.
    // Reason: When we scroll down to the focused super carousel, we fade out the title details + media background. This makes the container at -2 shortly visible and that looks off.
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_opacities_when_focused_super_or_nodes_carousel(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::NodesCarousel, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.5)]
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_container_opacities_when_focused_entity_carousel(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::EntityCarousel, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.0)]
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_opacities_when_focused_standard_or_charts_carousel(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::ChartsCarousel, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.0)]
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 1.0)]
    #[case(2, 0.5)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_opacities_when_focused_tentpole_hero(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::TentpoleHero, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.0)]
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 1.0)]
    #[case(2, 0.5)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_opacities_when_focused_standard_hero(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::StandardHero, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.5)]
    // The container at offset=-2 should be 0.0.
    // Reason: When we scroll down to the focused super carousel, we fade out the title details + media background. This makes the container at -2 shortly visible and that looks off.
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_container_opacities_when_focused_super_carousel(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::SuperCarousel, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.0)]
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_opacities_when_focused_standard_carousel(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::StandardCarousel, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.0)]
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_opacities_when_focused_beard_supported_carousel(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::BeardSupportedCarousel, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.5)]
    // The container at offset=-2 should be 0.0.
    // Reason: When we scroll down to the focused discovery assistant, we fade out the title details + media background. This makes the container at -2 shortly visible and that looks off.
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_container_opacities_when_focused_discovery_assistant(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::DiscoveryAssistant, offset),
            expected_opacity
        );
    }

    #[rstest]
    #[case(0, 1.0)]
    #[case(-1, 0.5)]
    // The container at offset=-2 should be 0.0.
    // Reason: When we scroll down to the focused grid, we fade out the title details + media background. This makes the container at -2 shortly visible and that looks off.
    #[case(-2, 0.0)]
    #[case(-3, 0.0)]
    #[case(-42, 0.0)]
    #[case(1, 0.5)]
    #[case(2, 0.0)]
    #[case(3, 0.0)]
    #[case(42, 0.0)]
    pub fn adjacent_container_opacities_when_focused_grid(
        #[case] offset: i32,
        #[case] expected_opacity: f32,
    ) {
        assert_eq!(
            get_opacity_for_index(ContainerModelDiscriminants::Grid, offset),
            expected_opacity
        );
    }

    #[test]
    pub fn get_watch_modal_launch_data_returns_none_for_promo_banner() {
        launch_only_scope(|scope| {
            let model = create_rw_signal(
                scope,
                PromoBannerButtonData {
                    common_carousel_card_metadata: CommonCarouselCardMetadata::default(),
                    secondary_transition_action: None,
                    contextual_menu_data: create_rw_signal(scope, Default::default()),
                },
            );
            assert_eq!(model.get_watch_modal_launch_data(), None);
        });
    }
}
