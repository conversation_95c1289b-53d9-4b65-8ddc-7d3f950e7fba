use amzn_fable_tokens::FableColor;
use amzn_fable_tokens::FableIcon;
use fableous::font_icon::*;
use fableous::typography::typography::*;
use fableous::utils::get_ignx_color;
use fableous::SCREEN_WIDTH;
use ignx_compositron::composable::VisualComposable;
use ignx_compositron::context::*;
use ignx_compositron::font::FontSize;
use ignx_compositron::layout::MainAxisAlignment;
use ignx_compositron::layout::Padding;
use ignx_compositron::prelude::*;
use ignx_compositron::text::LocalizedText;
use ignx_compositron::text::TextContent;
use ignx_compositron::text::TextVerticalAlignment;
use ignx_compositron::{compose, Composer};
use navigation_menu::ui::utility_nav::UTILITY_NAV_COLLAPSED_WIDTH;

const AVLRC_EPA_FALLBACK_HOME_NOTIFICATION_MESSAGE: &str =
    "AVLRC_EPA_FALLBACK_HOMEPAGE_NOTIFICATION_MESSAGE";
const FALLBACK_HOME_NOTIFICATION_HEIGHT: f32 = 144.0;

pub const FALLBACK_HOME_NOTIFICATION_TEST_ID: &str = "fallback-homepage-notification";

#[Composer]
pub fn FallbackHomepageNotification(ctx: &AppContext) -> StackComposable {
    let text = TextContent::LocalizedText(LocalizedText::new(
        AVLRC_EPA_FALLBACK_HOME_NOTIFICATION_MESSAGE,
    ));

    compose! {
            Stack() {
                Row() {
                    Column() {
                        FontIcon(icon: FableIcon::ERROR, // 0xe93b
                            color: get_ignx_color(FableColor::PRIMARY),
                            size: FontSize(FableIcon::SIZE150 as u32),
                        )
                    }
                    .background_color(get_ignx_color(FableColor::INFO))
                    .height(FALLBACK_HOME_NOTIFICATION_HEIGHT)
                    .padding(Padding{start: 15.0, end: 15.0, top: 0.0, bottom: 0.0})
                    .main_axis_alignment(MainAxisAlignment::Center)
                    Column() {
                        TypographyLabel500(content: text, color: get_ignx_color(FableColor::PRIMARY))
                            .vertical_alignment(TextVerticalAlignment::Start)
                        }
                        .main_axis_alignment(MainAxisAlignment::Center)
                        .height(144.0)
                        .padding(Padding{start: 9.0, end: 15.0, top: 0.0, bottom: 0.0})
                }
                .height(FALLBACK_HOME_NOTIFICATION_HEIGHT)
                .background_color(get_ignx_color(FableColor::COOL600))
                // This is the calculation done on JS
                .width(SCREEN_WIDTH - UTILITY_NAV_COLLAPSED_WIDTH * 2.0 - 68.0)
            }
            .flex(1.0)
            .alignment(Alignment::CenterBottom)
            .padding(Padding{start: 0.0, end: 0.0, top: 0.0, bottom: 20.0})
            .test_id(FALLBACK_HOME_NOTIFICATION_TEST_ID)
    }
}
