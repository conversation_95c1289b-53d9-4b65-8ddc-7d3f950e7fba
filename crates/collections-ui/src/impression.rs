#[cfg(not(test))]
pub mod impressions {
    pub use cross_app_events::send_highlight_impression_event;
    pub use cross_app_events::send_select_impression_event;
    pub use cross_app_events::send_view_impression_event;
}

#[cfg(test)]
use mockall::automock;

#[cfg(test)]
#[automock]
pub mod impressions {
    use cross_app_events::ImpressionData;
    use ignx_compositron::{impression::ViewImpressionData, reactive::Scope};

    pub fn send_highlight_impression_event(_s: Scope, _d: ImpressionData) {}
    pub fn send_select_impression_event(_s: Scope, _d: ImpressionData) {}
    pub fn send_view_impression_event(_s: Scope, _v: ViewImpressionData, _i: ImpressionData) {}
}
