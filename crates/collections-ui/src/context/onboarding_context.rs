use crate::reporting::onboarding_impressions_reporter::OnboardingImpressionsReporter;
use ignx_compositron::prelude::Scope;
use ignx_compositron::reactive::{provide_context, use_context};
use std::cell::RefCell;
use std::rc::Rc;

pub struct OnboardingContextStore {
    pub impressions_reporter: OnboardingImpressionsReporter,
}

pub type OnboardingContext = Rc<RefCell<OnboardingContextStore>>;

pub fn provide_onboarding_context(scope: Scope) -> OnboardingContext {
    let existing_context = use_onboarding_context(scope);

    if let Some(context) = existing_context {
        return context;
    }

    let onboarding_context = OnboardingContextStore {
        impressions_reporter: OnboardingImpressionsReporter::new(),
    };

    let wrapped_context = Rc::new(RefCell::new(onboarding_context));

    provide_context::<OnboardingContext>(scope, wrapped_context.clone());
    wrapped_context
}

pub fn use_onboarding_context(scope: Scope) -> Option<OnboardingContext> {
    let data = use_context::<OnboardingContext>(scope);
    if data.is_none() {
        log::error!("Onboarding context was not provided, returning None");
    }
    data
}
