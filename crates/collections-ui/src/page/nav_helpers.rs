use ignx_compositron::prelude::*;
use ignx_compositron::prelude::{use_context, Scope};
use location::{PageType, RustPage};
use navigation_menu::context::nav_context::NavControl;
use router::hooks::use_last_navigation_action;
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use rust_features::WeblabTreatmentString;

#[derive(Debug)]
struct NavSettings {
    disable_top_nav_focus_trap: bool,
    enable_focus: bool,
    show_top_nav_intent: bool,
    show_utility_nav: bool,
}

fn apply_nav_settings(nav_control: NavControl, settings: NavSettings) {
    nav_control
        .disable_top_nav_focus_trap
        .try_set(settings.disable_top_nav_focus_trap);
    nav_control.enable_focus.try_set(settings.enable_focus);
    nav_control
        .show_top_nav_intent
        .try_set(settings.show_top_nav_intent);
    nav_control
        .show_utility_nav
        .try_set(settings.show_utility_nav);
}

/*
   show_navs will be true for standard CollectionPages, false for the OnboardingCX case
*/
pub fn configure_navs(scope: Scope, show_navs: bool) {
    let nav_control = use_context::<NavControl>(scope);

    if let Some(ref nav_control) = nav_control {
        let is_default_focus_experiment_enabled = try_use_rust_features(scope).is_some_and(|t| {
            !matches!(
                t.get_home_default_focus_experiment_treatment_string(),
                WeblabTreatmentString::C
            )
        });

        let mut nav_settings = NavSettings {
            disable_top_nav_focus_trap: !show_navs,
            enable_focus: show_navs,
            show_top_nav_intent: show_navs,
            show_utility_nav: show_navs,
        };

        /*
           default focus experiment irrelevant to OnboardingCX case
        */
        if show_navs && is_default_focus_experiment_enabled {
            let last_nav = use_last_navigation_action(scope);
            if last_nav.from != PageType::Rust(RustPage::RUST_LIVE_TV) {
                nav_settings.enable_focus = false
            }
        }

        let nav_control = nav_control.clone();
        apply_nav_settings(nav_control, nav_settings);
    }
}
