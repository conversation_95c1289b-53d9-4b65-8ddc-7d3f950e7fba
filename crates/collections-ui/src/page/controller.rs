use crate::page::helpers_sig::EMPTY_CONTAINER_LIST_ERROR;
use crate::reporting::app_events::{CollectionsAppEvents, CollectionsAppFatals};
use crate::reporting::page_load::fire_tv_page_reporting;
use crate::types::collections_types::CollectionsPageModel;
use crate::utils::freshness_metric::FreshnessAction;
use cfg_test_attr_derive::derive_test_only;
#[cfg(not(test))]
use cross_app_events::app_event::AppEventReporter;
#[cfg(test)]
use cross_app_events::app_event::MockAppEventReporter as AppEventReporter;
use ignx_compositron::prelude::safe::{AppContext, RwSignal, Signal, StoredValue, WriteSignal};
#[cfg(not(test))]
use ignx_compositron::time::Instant;
use location::{Location, RustPage};
#[cfg(test)]
use mock_instant::Instant;
use modal_manager::modal_manager::AppModal;
use navigation_menu::utils::PageParams;
use network::common::PageRequestStatus;
use network::RequestError;

#[derive_test_only(Default, Clone)]
pub(crate) struct CollectionsPageFeatures {
    pub(crate) quickplay_v2: bool,
}

pub trait CollectionsPageControlling {
    fn page_source(&self) -> &RustPage;
    fn ctx(&self) -> &AppContext<'static>;
    fn signals(&self) -> &PageControllerSignals;
    fn page_features(&self) -> &CollectionsPageFeatures;
    fn report_app_event(&self, event: CollectionsAppEvents<'_>);
    fn page_load_success(&self, page_params: Option<&PageParams>);
    fn page_load_failure(&self, page_params: Option<&PageParams>, error: &RequestError);
}

#[derive_test_only(Clone)]
pub(crate) struct PageControllerSignals {
    pub(crate) initial_load_status: RwSignal<'static, PageRequestStatus<String>>,
    pub(crate) page_data: RwSignal<'static, CollectionsPageModel>,
    pub(crate) modal_data: WriteSignal<'static, Vec<AppModal>>,
    pub(crate) internal_location: Signal<'static, Location>,
    pub(crate) last_request_counter: StoredValue<'static, usize>,
    pub(crate) data_fetch_data: RwSignal<'static, Option<(Instant, FreshnessAction)>>,
    pub(crate) should_reload: RwSignal<'static, bool>,
}

pub(crate) struct CollectionsPageController {
    ctx: AppContext<'static>,
    app_event_reporter: AppEventReporter,
    page_source: RustPage,
    signals: PageControllerSignals,
    features: CollectionsPageFeatures,
}

impl CollectionsPageController {
    pub(crate) fn new(
        ctx: &AppContext<'static>,
        page_signals: PageControllerSignals,
        page_features: CollectionsPageFeatures,
    ) -> Self {
        let scope = ctx.scope;
        CollectionsPageController::new_from(
            ctx.clone(),
            page_signals,
            page_features,
            AppEventReporter::new(scope),
        )
    }

    fn new_from(
        ctx: AppContext<'static>,
        page_signals: PageControllerSignals,
        features: CollectionsPageFeatures,
        app_event_reporter: AppEventReporter,
    ) -> Self {
        CollectionsPageController {
            ctx,
            app_event_reporter,
            page_source: RustPage::RUST_COLLECTIONS,
            signals: page_signals,
            features,
        }
    }
}

impl CollectionsPageControlling for CollectionsPageController {
    fn page_source(&self) -> &RustPage {
        &self.page_source
    }

    fn ctx(&self) -> &AppContext<'static> {
        &self.ctx
    }

    fn signals(&self) -> &PageControllerSignals {
        &self.signals
    }

    fn page_features(&self) -> &CollectionsPageFeatures {
        &self.features
    }
    fn report_app_event(&self, event: CollectionsAppEvents<'_>) {
        event.report(&self.app_event_reporter)
    }

    fn page_load_success(&self, page_params: Option<&PageParams>) {
        // report app event
        self.report_app_event(CollectionsAppEvents::LoadSuccess { page_params });
        // report to fire tv
        fire_tv_page_reporting(&self.ctx, page_params);
    }

    fn page_load_failure(&self, page_params: Option<&PageParams>, error: &RequestError) {
        // report app event
        if let RequestError::Http { .. } = error {
            self.report_app_event(CollectionsAppEvents::Fatal(
                CollectionsAppFatals::TransformFailure,
            ));
        } else if let RequestError::ResolverFailed(error) = error {
            if error == EMPTY_CONTAINER_LIST_ERROR {
                self.report_app_event(CollectionsAppEvents::Fatal(CollectionsAppFatals::EmptyPage));
            } else {
                // Considering all remaining failures to be generic network errors
                self.report_app_event(CollectionsAppEvents::Fatal(CollectionsAppFatals::Network));
            }
        } else {
            self.report_app_event(CollectionsAppEvents::Fatal(CollectionsAppFatals::Network));
        }
        self.report_app_event(CollectionsAppEvents::LoadFailure { page_params });
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::reporting::app_events::CollectionsTransforms;
    use crate::test_assets::mocks::MockData;
    use cross_app_events::app_event::MockAppEventReporter;
    use firetv::{FireTVChannelIngressType, MockFireTV};
    use ignx_compositron::{app::launch_only_app_context, reactive::store_value};
    use navigation_menu::utils::{CollectionsPageParams, PageParams};
    use network::RequestError;
    use rstest::*;
    use serde_json::{Map, Value};

    type AppEventCall = (String, String, Option<Map<String, Value>>);

    struct TestSetup {
        controller: CollectionsPageController,
        calls: StoredValue<'static, Vec<AppEventCall>>,
        ctx: AppContext<'static>,
    }

    impl TestSetup {
        fn new(ctx: &AppContext<'static>) -> Self {
            let scope = ctx.scope;
            let calls = store_value(scope, Vec::new());
            let calls_clone = calls;

            let mut mock_reporter = MockAppEventReporter::default();
            mock_reporter
                .expect_send_app_event()
                .returning(move |name, source, params| {
                    calls_clone.update_value(|v| {
                        v.push((name.to_string(), source.to_string(), params));
                    });
                });

            let signals = PageControllerSignals::mock_data(scope);

            let controller = CollectionsPageController::new_from(
                ctx.clone(),
                signals,
                CollectionsPageFeatures::default(),
                mock_reporter,
            );

            TestSetup {
                controller,
                calls,
                ctx: ctx.clone(),
            }
        }

        fn with_fire_tv(self, fire_tv: MockFireTV) -> Self {
            fire_tv.provide_mock(self.ctx.scope);
            self
        }

        fn assert_single_call(
            &self,
            expected_name: &str,
            expected_source: &str,
            expected_params: Option<Map<String, Value>>,
        ) {
            let calls_vec = self.calls.get_value();
            assert_eq!(calls_vec.len(), 1, "Expected exactly one app event call");
            assert_eq!(calls_vec[0].0, expected_name);
            assert_eq!(calls_vec[0].1, expected_source);
            assert_eq!(calls_vec[0].2, expected_params);
        }

        fn assert_calls(&self, expected_calls: Vec<(String, String, Option<Map<String, Value>>)>) {
            let calls_vec = self.calls.get_value();
            assert_eq!(
                calls_vec.len(),
                expected_calls.len(),
                "Unexpected number of app event calls"
            );
            for (i, expected) in expected_calls.iter().enumerate() {
                assert_eq!(calls_vec[i].0, expected.0, "Call {} name mismatch", i);
                assert_eq!(calls_vec[i].1, expected.1, "Call {} source mismatch", i);
                assert_eq!(calls_vec[i].2, expected.2, "Call {} params mismatch", i);
            }
        }
    }

    #[test]
    fn test_report_app_event_load_success() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            let page_params = PageParams::Collections(CollectionsPageParams {
                page_type: "test".to_string(),
                page_id: "123".to_string(),
                service_token: Some("token".to_string()),
            });

            setup
                .controller
                .report_app_event(CollectionsAppEvents::LoadSuccess {
                    page_params: Some(&page_params),
                });

            let mut expected_params = Map::new();
            expected_params.insert("pageId".to_string(), Value::String("test-123".to_string()));

            setup.assert_single_call(
                "COLLECTION_PAGE_LOAD_SUCCESS",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[test]
    fn test_report_app_event_load_failure() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            let page_params = PageParams::Collections(CollectionsPageParams {
                page_type: "test".to_string(),
                page_id: "456".to_string(),
                service_token: Some("token".to_string()),
            });

            setup
                .controller
                .report_app_event(CollectionsAppEvents::LoadFailure {
                    page_params: Some(&page_params),
                });

            let mut expected_params = Map::new();
            expected_params.insert("pageId".to_string(), Value::String("test-456".to_string()));

            setup.assert_single_call(
                "COLLECTION_PAGE_LOAD_FAILED",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[test]
    fn test_report_app_event_fatal_transform_failure() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::Fatal(
                    CollectionsAppFatals::TransformFailure,
                ));

            setup.assert_single_call(
                "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_TRANSFORM",
                "COLLECTION_PAGE",
                None,
            );
        });
    }

    #[test]
    fn test_report_app_event_fatal_empty_page() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::Fatal(CollectionsAppFatals::EmptyPage));

            let mut expected_params = Map::new();
            expected_params.insert(
                "endpoint".to_string(),
                Value::String("collectionsPageInitial".to_string()),
            );

            setup.assert_single_call(
                "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_EMPTY_PAGE",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[test]
    fn test_report_app_event_fatal_network() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::Fatal(CollectionsAppFatals::Network));

            setup.assert_single_call(
                "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_NETWORK",
                "COLLECTION_PAGE",
                None,
            );
        });
    }

    #[test]
    fn test_report_app_event_render_begin() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::RenderBegin);

            setup.assert_single_call("COLLECTION_PAGE_RENDER_BEGIN", "COLLECTION_PAGE", None);
        });
    }

    #[test]
    fn test_report_app_event_network_success() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::NetworkSuccess {
                    transform: CollectionsTransforms::CollectionsPageInitial,
                    time: 1500,
                });

            let mut expected_params = Map::new();
            expected_params.insert(
                "endpoint".to_string(),
                Value::String("collectionsPageInitial".to_string()),
            );
            expected_params.insert("time".to_string(), Value::Number(1500.into()));

            setup.assert_single_call(
                "COLLECTION_PAGE_NETWORK_SUCCESS",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[test]
    fn test_page_load_success_reports_correct_event() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            let page_params = PageParams::Collections(CollectionsPageParams {
                page_type: "home".to_string(),
                page_id: "main".to_string(),
                service_token: Some("token".to_string()),
            });

            setup.controller.page_load_success(Some(&page_params));

            let mut expected_params = Map::new();
            expected_params.insert("pageId".to_string(), Value::String("home-main".to_string()));

            setup.assert_single_call(
                "COLLECTION_PAGE_LOAD_SUCCESS",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[test]
    fn test_page_load_success_reports_to_fire_tv_when_expected() {
        launch_only_app_context(move |ctx| {
            let mut fire_tv = MockFireTV::default();
            fire_tv
                .expect_propagate_transition_event()
                .withf(|_, event| {
                    matches!(event.ingress_type(), &FireTVChannelIngressType::CHANNEL)
                        && event.benefit_id() == "hbo"
                })
                .times(1)
                .return_const(());

            fire_tv
                .expect_propagate_transition_event()
                .withf(|_, event| {
                    matches!(
                        event.ingress_type(),
                        &FireTVChannelIngressType::SUBSCRIPTION
                    ) && event.benefit_id() == "hbo"
                })
                .times(1)
                .return_const(());

            let setup = TestSetup::new(&ctx).with_fire_tv(fire_tv);

            let page_params = PageParams::Collections(CollectionsPageParams {
                page_type: "channel".to_string(),
                page_id: "hbo".to_string(),
                service_token: Some("token".to_string()),
            });

            setup.controller.page_load_success(Some(&page_params));

            let page_params = PageParams::Collections(CollectionsPageParams {
                page_type: "subscription".to_string(),
                page_id: "hbo".to_string(),
                service_token: Some("token".to_string()),
            });

            setup.controller.page_load_success(Some(&page_params));
        });
    }

    #[rstest]
    #[case::no_params(None)]
    #[case::not_channel_or_subscription_params(Some(PageParams::Collections(CollectionsPageParams {
        page_type: "home".to_string(),
        page_id: "home".to_string(),
        service_token: Some("token".to_string()),
    })))]
    fn test_page_load_success_doesnt_report_to_fire_tv_when_not_expected(
        #[case] params: Option<PageParams>,
    ) {
        launch_only_app_context(move |ctx| {
            let mut fire_tv = MockFireTV::default();
            fire_tv
                .expect_propagate_transition_event()
                .times(0)
                .return_const(());

            let setup = TestSetup::new(&ctx).with_fire_tv(fire_tv);
            setup.controller.page_load_success(params.as_ref());
        });
    }

    #[test]
    fn test_page_load_failure_http_error() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            let page_params = PageParams::Collections(CollectionsPageParams {
                page_type: "test".to_string(),
                page_id: "error".to_string(),
                service_token: Some("token".to_string()),
            });

            let http_error = RequestError::Http {
                code: 500,
                body: Some("Internal Server Error".to_string()),
                headers: vec![],
            };

            setup
                .controller
                .page_load_failure(Some(&page_params), &http_error);

            let mut load_failure_params = Map::new();
            load_failure_params.insert(
                "pageId".to_string(),
                Value::String("test-error".to_string()),
            );

            setup.assert_calls(vec![
                (
                    "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_TRANSFORM".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    None,
                ),
                (
                    "COLLECTION_PAGE_LOAD_FAILED".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    Some(load_failure_params),
                ),
            ]);
        });
    }

    #[test]
    fn test_page_load_failure_empty_container_error() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            let page_params = PageParams::Collections(CollectionsPageParams {
                page_type: "empty".to_string(),
                page_id: "test".to_string(),
                service_token: Some("token".to_string()),
            });

            let empty_error = RequestError::ResolverFailed(EMPTY_CONTAINER_LIST_ERROR.to_string());

            setup
                .controller
                .page_load_failure(Some(&page_params), &empty_error);

            let mut empty_page_params = Map::new();
            empty_page_params.insert(
                "endpoint".to_string(),
                Value::String("collectionsPageInitial".to_string()),
            );

            let mut load_failure_params = Map::new();
            load_failure_params.insert(
                "pageId".to_string(),
                Value::String("empty-test".to_string()),
            );

            setup.assert_calls(vec![
                (
                    "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_EMPTY_PAGE".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    Some(empty_page_params),
                ),
                (
                    "COLLECTION_PAGE_LOAD_FAILED".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    Some(load_failure_params),
                ),
            ]);
        });
    }

    #[test]
    fn test_page_load_failure_generic_resolver_error() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            let generic_error = RequestError::ResolverFailed("Generic resolver error".to_string());

            setup.controller.page_load_failure(None, &generic_error);

            let mut load_failure_params = Map::new();
            load_failure_params.insert("pageId".to_string(), Value::String("Unknown".to_string()));

            setup.assert_calls(vec![
                (
                    "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_NETWORK".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    None,
                ),
                (
                    "COLLECTION_PAGE_LOAD_FAILED".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    Some(load_failure_params),
                ),
            ]);
        });
    }

    #[test]
    fn test_page_load_failure_network_timeout_error() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            let timeout_error = RequestError::ResolverFailed("timeout".into());

            setup.controller.page_load_failure(None, &timeout_error);

            let mut load_failure_params = Map::new();
            load_failure_params.insert("pageId".to_string(), Value::String("Unknown".to_string()));

            setup.assert_calls(vec![
                (
                    "COLLECTION_PAGE_LOAD_FAILED_APP_FATAL_NETWORK".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    None,
                ),
                (
                    "COLLECTION_PAGE_LOAD_FAILED".to_string(),
                    "COLLECTION_PAGE".to_string(),
                    Some(load_failure_params),
                ),
            ]);
        });
    }

    #[rstest]
    #[case(true, Some(Value::String("navigation".to_string())), "home".into(), "featured".into())]
    #[case(false, None, "sports".into(), "basketball".into())]
    fn test_report_app_event_begin(
        #[case] travelling_customer: bool,
        #[case] transition_source: Option<Value>,
        #[case] page_type: String,
        #[case] page_id: String,
    ) {
        launch_only_app_context(move |ctx| {
            let setup = TestSetup::new(&ctx);

            let page_params = CollectionsPageParams {
                page_type: page_type.clone(),
                page_id: page_id.clone(),
                service_token: None,
            };

            setup
                .controller
                .report_app_event(CollectionsAppEvents::Begin {
                    page_params: &page_params,
                    travelling_customer,
                    transition_source: transition_source.as_ref(),
                });

            let mut expected_params = Map::new();
            expected_params.insert(
                "pageId".to_string(),
                Value::String(format!("{}-{}", page_type, page_id)),
            );
            expected_params.insert(
                "isTravelingCustomer".to_string(),
                Value::Bool(travelling_customer),
            );
            if let Some(source) = transition_source {
                expected_params.insert("transitionSource".to_string(), source);
            }

            setup.assert_single_call(
                "COLLECTION_PAGE_LOAD_BEGIN",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[rstest]
    #[case(true, 2)]
    #[case(false, 0)]
    fn test_report_app_event_focus_restoration(
        #[case] transform: bool,
        #[case] v3_treatment: usize,
    ) {
        launch_only_app_context(move |ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::FocusRestoration {
                    transform,
                    v3_treatment,
                });

            let mut expected_params = Map::new();
            expected_params.insert("transform".to_string(), Value::Bool(transform));
            expected_params.insert(
                "v3Treatment".to_string(),
                Value::Number(v3_treatment.into()),
            );

            setup.assert_single_call(
                "COLLECTION_PAGE_FOCUS_RESTORATION",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[rstest]
    #[case(true, "v3.2".into(), "standard".into())]
    #[case(false, "v2.1".into(), "fallback".into())]
    fn test_report_app_event_focus_restoration_accuracy(
        #[case] matches: bool,
        #[case] version: String,
        #[case] flow: String,
    ) {
        launch_only_app_context(move |ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::FocusRestorationAccuracy {
                    matches,
                    version: &version,
                    flow: &flow,
                });

            let mut expected_params = Map::new();
            expected_params.insert("match".to_string(), Value::Bool(matches));
            expected_params.insert("version".to_string(), Value::String(version));
            expected_params.insert("flow".to_string(), Value::String(flow));

            setup.assert_single_call(
                "COLLECTION_PAGE_FOCUS_RESTORATION_ACCURACY",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[rstest]
    #[case(250)]
    #[case(1500)]
    fn test_report_app_event_response_parsed(#[case] time: u64) {
        launch_only_app_context(move |ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::ResponseParsed { time });

            let mut expected_params = Map::new();
            expected_params.insert(
                "endpoint".to_string(),
                Value::String("collectionsPageInitial".to_string()),
            );
            expected_params.insert("time".to_string(), Value::Number(time.into()));

            setup.assert_single_call(
                "COLLECTION_PAGE_RESPONSE_PARSED",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[test]
    fn test_report_app_event_render_finished() {
        launch_only_app_context(|ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::RenderFinished);

            setup.assert_single_call("COLLECTION_PAGE_RENDER_FINISH", "COLLECTION_PAGE", None);
        });
    }

    #[rstest]
    #[case(
        CollectionsTransforms::CollectionsPageInitial,
        "collectionsPageInitial".into()
    )]
    #[case(
        CollectionsTransforms::RestoreCollectionPageFocusV2,
        "restoreCollectionPageFocusV2".into()
    )]
    #[case(
        CollectionsTransforms::SportsEdgeInitial,
        "collectionPageSportsEdgeInitial".into()
    )]
    fn test_report_app_event_network_failure(
        #[case] transform: CollectionsTransforms,
        #[case] expected_endpoint: String,
    ) {
        launch_only_app_context(move |ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::NetworkFailure { transform });

            let mut expected_params = Map::new();
            expected_params.insert("endpoint".to_string(), Value::String(expected_endpoint));

            setup.assert_single_call(
                "COLLECTION_PAGE_NETWORK_FAILED",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }

    #[rstest]
    #[case(
        CollectionsTransforms::CollectionsPageInitial,
        "collectionsPageInitial",
        180
    )]
    #[case(
        CollectionsTransforms::RestoreCollectionPageFocusV2,
        "restoreCollectionPageFocusV2",
        75
    )]
    #[case(
        CollectionsTransforms::SportsEdgeInitial,
        "collectionPageSportsEdgeInitial",
        320
    )]
    fn test_report_app_event_processing_success(
        #[case] transform: CollectionsTransforms,
        #[case] expected_endpoint: String,
        #[case] time: u64,
    ) {
        launch_only_app_context(move |ctx| {
            let setup = TestSetup::new(&ctx);

            setup
                .controller
                .report_app_event(CollectionsAppEvents::ProcessingSuccess { transform, time });

            let mut expected_params = Map::new();
            expected_params.insert("endpoint".to_string(), Value::String(expected_endpoint));
            expected_params.insert("time".to_string(), Value::Number(time.into()));

            setup.assert_single_call(
                "COLLECTION_PAGE_PROCESSING_SUCCESS",
                "COLLECTION_PAGE",
                Some(expected_params),
            );
        });
    }
}
