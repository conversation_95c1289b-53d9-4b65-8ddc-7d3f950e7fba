use crate::cache::CachedCollectionsPage;
use crate::context::onboarding_context::provide_onboarding_context;
use crate::network::collections_request_wrapper::get_collection_page;
#[cfg(not(any(test, feature = "example_data")))]
#[cfg(not(any(test, feature = "example_data")))]
use crate::network::restore_collection_page_focus::RestoreCollectionPageFocusRequests;
use crate::network::types::{CachedFocusData, FocusPosition};
use crate::page::collections_page_error_modal::*;
#[cfg(not(test))]
use crate::page::controller::CollectionsPageController;
use crate::page::controller::PageControllerSignals;
use crate::page::controller::{CollectionsPageControlling, CollectionsPageFeatures};
use crate::page::helpers_sig::{
    create_default_collections_page_model, create_default_home_page_request,
    create_restore_collection_page_focus_request, extract_resiliency_page_params_from_location,
    get_initial_load_failure_cb, get_initial_load_success_cb, get_paginate_component_closure,
    get_paginate_page_closure, is_onboarding_page_hero_ingress, page_params_to_identifier,
    EMPTY_CONTAINER_LIST_ERROR,
};
use crate::page::nav_helpers::configure_navs;
use crate::reporting::app_events::CollectionsAppEvents;
use crate::reporting::onboarding::{provide_onboarding_client, use_onboarding_client};
#[cfg(test)]
use crate::test_assets::mocks::MockCollectionsPageController as CollectionsPageController;
#[cfg(any(test, feature = "example_data"))]
use crate::test_assets::mocks::MockNetworkClient as NetworkClient;
use crate::types::cache_config::{collection_page_ttl_resolver, COLLECTION_PAGE_CACHE_SIZE};
use crate::types::collections_types::{CollectionsPageModel, OnboardingPageModel};
use crate::ui::my_stuff_empty_page::*;
use crate::ui::onboarding_page_ui::{
    OnboardingPageUI, OnboardingPageUICaller, OnboardingPageUIProps,
};
use crate::ui::page_skeleton::*;
use crate::ui::page_ui_sig::*;
use crate::ui::traits::UpdatableItems;
#[double]
use crate::utils::freshness_metric::setup_freshness_metric;
use crate::utils::freshness_metric::FreshnessAction;
use crate::utils::item_id_extracter::get_container_and_item_ids;
use crate::utils::restore_focus::{find_default_focus_after_restoration, should_skip_pagination};
use app_config::AppConfigContext;
use app_reporting::kpi_reporting::report_cx_error;
use beekeeper::types::StorefrontFallbackPageFulfilledAction;
#[double]
use beekeeper::BeekeeperContext;
use cache::cache_control::CacheControl;
use cache::expirable_lru_cache::ExpirableLruCache;
use cache::ExpirableLruCacheRc;
use collection_types::network_types::CollectionsPage;
use collections_ui_signals::CollectionsPageStateOverrides;
use common_transform_types::profile::Profile;
use current_time::{create_current_time_signal, CurrentTimeContext};
use details_integration::collections::use_details_collections_integration;
use fableous::animations::{FableMotionDuration, FableMotionEasing, MotionDuration, MotionEasing};
use fableous::pointer_control::use_focus_pointer_control;
use ignx_compositron::input::KeyCode;
use ignx_compositron::memo::*;
use ignx_compositron::metrics::metric;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::{on_cleanup, store_value};
use ignx_compositron::time::Instant;
use ignx_compositron::{compose, compose_option, Composer};
use location::{Location, PageType, RustPage};
use log::info;
use media_background::types::MediaBackgroundType;
use mockall_double::double;
use modal_manager::modal_manager::AppModal;
use navigation_menu::context::nav_context::NavControl;
use navigation_menu::model::nav_model::TopNavData;
#[double]
use navigation_menu::state::top_nav_metrics_reporter::TopNavMetricsReporter;
use navigation_menu::utils::{get_page_params, CollectionsPageParams, PageParams};
use network::common::lrc_edge_constants::CollectionInitialTypes;
use network::common::PageRequestStatus;
#[cfg(all(not(test), not(feature = "example_data")))]
use network::NetworkClient;
use network::RequestError;
use network_parser::core::NetworkBool;
use popups::exit_popup::exit_modal_back_handler;
#[double]
use reduce_steps_to_mlp::upsell_mlp;
#[double]
use resiliency_store::ResiliencyStore;
use router::hooks::{use_location_with_effect, use_router};
use rust_features::try_use_rust_features;
use std::cell::RefCell;
use std::collections::HashSet;
use std::ops::{Add, Deref};
use std::rc::Rc;
use std::time::Duration;
use storage_events::StorageManager;
use title_details::core::{NavigationDirection, TitleDetailsChangeRequest, TitleDetailsData};

#[derive(Clone, PartialEq, Debug)]
enum SkeletonState {
    Visible,
    Hidden,
}

#[derive(Clone, Debug)]
struct ScheduledPageLoadInfo {
    page_load_time: Instant,
}

// GRCOV_BEGIN_COVERAGE
fn is_top_nav_prefetch_disabled(scope: Scope) -> bool {
    // Do not prefetch if `is_tentpole` BK4C config is `true`
    let beekeeper = use_context::<BeekeeperContext>(scope);
    if let Some(beekeeper) = beekeeper {
        if beekeeper.as_ref().is_tentpole() {
            metric!("Rust.Cache.TentpoleActive", 1);
            return true;
        }
    } else {
        log::warn!(
            "Beekeeper config not found in context while checking for top nav prefetch disabling."
        );
    }
    // Do not prefetch if client resiliency config has it disabled
    let resiliency_store = use_context::<ResiliencyStore>(scope);
    if let Some(resiliency_store) = resiliency_store {
        return resiliency_store.get_disable_top_nav_pages_prefetching();
    }

    log::warn!(
        "Resiliency store not found in context while checking for top nav prefetch disabling."
    );

    false
}

const MY_STUFF_PAGE_ID: &str = "MyStuff";

const SKELETON_TIMEOUT_DURATION: Duration = Duration::from_millis(5000);

#[cfg(test)]
#[derive(Clone)]
struct TestContext {
    pub set_content_ready: Rc<dyn Fn()>,
}
// GRCOV_BEGIN_COVERAGE

fn handle_onboarding_cx(
    scope: Scope,
    cache: Rc<RefCell<ExpirableLruCache<String, CollectionsPage>>>,
    onboarding_page_model: WriteSignal<Option<OnboardingPageModel>>,
) -> Box<dyn FnOnce(&mut CollectionsPageModel)> {
    Box::new(move |page_data: &mut CollectionsPageModel| {
        if let Some(start_column) = page_data.start_column.get_untracked() {
            onboarding_page_model.set(Some(OnboardingPageModel {
                container_list: page_data.container_list,
                start_column,
            }));
            // Init reporting
            provide_onboarding_context(scope);
            provide_onboarding_client(scope);

            if let Some(onboarding_metrics_client) = use_onboarding_client(scope) {
                onboarding_metrics_client.report_page_loading_started();
            }

            /*
               Hide navs as soon as we know we are navigating to OCX page
            */
            configure_navs(scope, false);

            /*
               Clear cache to ensure subsequent pages are fetched fresh.
               OCX influences recommendations via title likes, so cached pages would
               be stale.
            */
            cache.borrow_mut().clear();
        } else {
            onboarding_page_model.set(None)
        }
    })
}

#[Composer]
pub fn CollectionsPage(
    ctx: &AppContext,
    update_media_background_rw: RwSignal<MediaBackgroundType>,
    update_title_details: WriteSignal<TitleDetailsChangeRequest>,
    top_nav_data: Signal<TopNavData>,
    modal_data: WriteSignal<Vec<AppModal>>,
    #[into] active_profile: Signal<Option<Profile>>,
) -> impl VisualComposable<'static> {
    let scope = ctx.scope();
    let details_integration = use_details_collections_integration(scope);
    let client = Rc::new(NetworkClient::new(ctx));
    // this signal is hoisted on app_container and is used for reloading collections page
    let should_reload = use_context::<CollectionsPageStateOverrides>(ctx.scope()).map_or_else(
        || {
            log::error!("[collections_page] CollectionsPageStateOverrides not found in scope");
            create_rw_signal(ctx.scope(), false)
        },
        |collections_page_state_overrides| {
            collections_page_state_overrides
                .should_reload
                .set_untracked(false);
            collections_page_state_overrides.should_reload
        },
    );
    let initial_load_status = create_rw_signal(scope, PageRequestStatus::Waiting);
    let is_restoring_focus = create_rw_signal(scope, false);
    let page_data = create_default_collections_page_model(scope);
    let (default_focus, set_default_focus) = create_signal(ctx.scope(), None);
    let current_focus = create_rw_signal(ctx.scope(), None);
    let reset_page_focus = create_rw_signal(ctx.scope(), false);
    let onboarding_page_model = create_rw_signal::<Option<OnboardingPageModel>>(ctx.scope(), None);

    let is_rust_quickplay_v2_enabled = try_use_rust_features(ctx.scope())
        .map_or(false, |features| features.is_rust_quickplay_v2_enabled());

    // set up signal to update current time to be used to refresh linear cards on the page
    let current_time = create_current_time_signal(ctx).0;
    provide_context(scope, CurrentTimeContext { current_time });

    // This internal location signal is different from the one from `use_location`. This internal
    // signal considers debounce and enter effect before changing the location to actually start
    // loading the page with data.
    let (internal_location, set_internal_location) =
        create_signal(ctx.scope(), Location::default());
    let data_fetch_data = setup_freshness_metric(ctx, internal_location);
    let location_with_effect = use_location_with_effect(scope);
    let page_translate_x = create_rw_signal(ctx.scope(), 0.0);
    let page_opacity = create_rw_signal(ctx.scope(), 1.0);

    let skeleton_translate_x = create_rw_signal(ctx.scope(), 0.0);

    let skeleton_anim_finished = create_rw_signal(ctx.scope(), false);
    let scheduled_page_info = create_rw_signal(ctx.scope(), None);
    let location_to_load = store_value(ctx.scope(), Location::default());
    let delay_task_id = store_value(ctx.scope(), None);

    let page_signals = PageControllerSignals {
        initial_load_status,
        page_data,
        modal_data,
        internal_location: internal_location.into(),
        last_request_counter: store_value(ctx.scope(), 0),
        data_fetch_data,
        should_reload,
    };

    let page_features = CollectionsPageFeatures {
        quickplay_v2: is_rust_quickplay_v2_enabled,
    };

    let page_controller: Rc<dyn CollectionsPageControlling> = Rc::new(
        CollectionsPageController::new(ctx, page_signals, page_features),
    );

    // Track a map of container items that will update when time changes
    let updatable_items = store_value(scope, HashSet::new());
    // Recalculate updatable items if the page data changes
    create_effect(scope, move |_| {
        // Initialize updatable items with containers on page load
        updatable_items.set_value(page_data.with(|data| {
            data.container_list.with_untracked(|containers| {
                containers
                    .iter()
                    .flat_map(|container| {
                        container
                            .model
                            .with_untracked(|model| model.get_updatable_items())
                    })
                    .collect()
            })
        }));
    });

    create_effect(scope, {
        let ctx = ctx.clone();
        move |_| {
            let is_skeleton_finished = skeleton_anim_finished.get();
            let schedule_info: Option<ScheduledPageLoadInfo> = scheduled_page_info.get();

            if let Some(info) = schedule_info {
                let Some(delay_task) = delay_task_id.try_get_value() else {
                    // scope has been disposed, return early
                    return;
                };
                if let Some(delay_task) = delay_task {
                    ctx.cancel_task(delay_task);
                }

                if !is_skeleton_finished {
                    return;
                }

                let now = Instant::now();
                if now >= info.page_load_time {
                    if let Some(location_to_load) = location_to_load.try_get_value() {
                        set_internal_location.set(location_to_load);
                    }
                } else {
                    let remaining_time = info.page_load_time - now;
                    let delay_task =
                        ctx.schedule_task(Instant::now().add(remaining_time), move || {
                            if let Some(is_skeleton_finished) =
                                skeleton_anim_finished.try_get_untracked()
                            {
                                if is_skeleton_finished {
                                    if let Some(location_to_load) = location_to_load.try_get_value()
                                    {
                                        set_internal_location.try_set(location_to_load);
                                    }
                                }
                            }
                            // Do nothing if skeleton hasn't finished the transition will happen when it does.
                        });
                    delay_task_id.set_value(Some(delay_task));
                }
            }
        }
    });

    create_effect(scope, {
        let ctx = ctx.clone();
        move |_| {
            location_with_effect.with(|location_with_effect| {
                let is_leaving_collections_page = matches!(&location_with_effect.from_location, Some(location) if location.pageType == PageType::Rust(RustPage::RUST_COLLECTIONS));
                let is_entering_collections_page = location_with_effect.to_location.pageType == PageType::Rust(RustPage::RUST_COLLECTIONS);

                let mut exit_effect_end_time = None;

                if let Some(previously_scheduled_page_load_info) = scheduled_page_info.try_get_untracked().flatten() {
                    if is_entering_collections_page && previously_scheduled_page_load_info.page_load_time > Instant::now() {
                        // Still running exit or enter effects, so only wait for a debounce period
                        // and load the page
                        location_to_load.try_set_value(location_with_effect.to_location.clone());

                        let Some(enter_effect) = &location_with_effect.enter_effect else {
                            // there is no enter_effect, so we allow the ongoing effect to finish
                            // and load the new page type
                            return;
                        };

                        let time_after_debounce = Instant::now() + enter_effect.duration().to_owned();
                        if time_after_debounce > previously_scheduled_page_load_info.page_load_time {
                            // current time + debounce is beyond the originally scheduled page
                            // load time, so we need to cancel the page load task and schedule
                            // with the new debounce time to load the new page location

                            scheduled_page_info.set(Some(ScheduledPageLoadInfo {
                                page_load_time: time_after_debounce,
                            }))
                        }
                        return;
                    }
                }

                if is_leaving_collections_page {
                    if let Some(exit_effect) = &location_with_effect.exit_effect {
                        if exit_effect.fade_media_background() {
                            update_media_background_rw.set(MediaBackgroundType::None);
                        }

                        ctx.with_animation(
                            exit_effect.to_animation(),
                            move || {
                                page_translate_x.set(exit_effect.to_translate_x());
                                page_opacity.set(0.0);
                            }
                        );

                        let effect_end_time = Instant::now() + exit_effect.duration().to_owned();
                        ctx.schedule_task(effect_end_time, move || {
                            page_translate_x.try_set(0.0);
                            page_opacity.try_set(1.0);
                        });
                        exit_effect_end_time = Some(effect_end_time);
                    }
                }

                if is_entering_collections_page {
                    skeleton_anim_finished.set(false);
                    location_to_load.try_set_value(location_with_effect.to_location.clone());

                    if let Some(enter_effect) = &location_with_effect.enter_effect {
                        let enter_effect_start_time = exit_effect_end_time.unwrap_or_else(Instant::now);

                        let effect = enter_effect.clone();
                        ctx.schedule_task(enter_effect_start_time, {
                            let ctx = ctx.clone();
                            move || {
                                let animation = effect.to_animation();

                                page_translate_x.try_set(0.0);
                                page_opacity.try_set(1.0);
                                skeleton_translate_x.try_set(effect.from_translate_x());
                                initial_load_status.try_set(PageRequestStatus::Waiting);

                                // need to wrap the animation in a schedule task likely because
                                // above is also setting skeleton_translate_x
                                ctx.schedule_task(Instant::now(), {
                                    let ctx = ctx.clone();
                                    move || {
                                        ctx.with_animation_completion(
                                            animation,
                                            move || {
                                                skeleton_translate_x.set(0.0);
                                            },
                                            move || {
                                                skeleton_anim_finished.try_set(true);
                                            }
                                        );
                                    }
                                });
                            }
                        });

                        let enter_effect_end_time = enter_effect_start_time + enter_effect.duration().to_owned();
                        scheduled_page_info.set(Some(ScheduledPageLoadInfo {
                            page_load_time: enter_effect_end_time
                        }));
                    } else if let Some(location_to_load) = location_to_load.try_get_value() {
                        set_internal_location.set(location_to_load);
                    }
                };
            });
        }
    });

    // Eligible customers are shown the Prime MLP after profile selection. If the MLP fails to load, we need to
    // clear it's flag in storage so they will be eligible to see the MLP again.
    // SIM to clean up this tech debt: https://issues.amazon.com/issues/LEX-13150
    upsell_mlp::clear_mlp_shown_flag_if_needed(scope, &StorageManager {});

    let cache: ExpirableLruCacheRc<String, CollectionsPage> =
        use_context(scope).unwrap_or_else(|| {
            log::error!("Collection Page cache is not available");
            ExpirableLruCache::new_rc(
                COLLECTION_PAGE_CACHE_SIZE,
                Box::new(collection_page_ttl_resolver),
            )
        });
    let page_allows_top_nav = create_rw_signal(ctx.scope(), true);

    let collections_cache =
        use_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(ctx.scope());

    let (resiliency, set_resiliency) = create_signal(ctx.scope(), false);
    let cache_page = Rc::new({
        let collections_cache = collections_cache.clone();

        move || {
            // Scope guard with unrelated but cheap to copy `reset_page_focus` signal
            // onboarding cx should not be cached
            if reset_page_focus.try_get_untracked().is_some()
                && onboarding_page_model.get_untracked().is_none()
            {
                // clear default focus, so it's not left over on the next page
                set_default_focus.set(None);

                if let Some(collections_cache) = &collections_cache {
                    initial_load_status.with_untracked(|status| {
                        if let PageRequestStatus::Success(_) = status {
                            let page_data = page_data.get_untracked();
                            let page_id = page_data.unique_id.get_untracked();

                            let focus = {
                                let focus_pos = current_focus.get_untracked();

                                if let Some(focus_pos) = focus_pos {
                                    let (container_id, item_id) = get_container_and_item_ids(
                                        page_data.container_list.read_only(),
                                        &focus_pos,
                                    );

                                    let should_skip_pagination = should_skip_pagination(
                                        page_data.container_list.read_only(),
                                        &focus_pos,
                                    );

                                    Some(CachedFocusData {
                                        should_skip_pagination,
                                        position: focus_pos,
                                        container_id,
                                        item_id,
                                    })
                                } else {
                                    None
                                }
                            };

                            let cache_entry_time = data_fetch_data
                                .get()
                                .map_or_else(Instant::now, |data_fetch_data| data_fetch_data.0);

                            collections_cache.borrow_mut().put_with_entry_time(
                                page_id,
                                CachedCollectionsPage {
                                    data: Some(page_data.into()),
                                    focus,
                                    is_resiliency_enabled: resiliency
                                        .with_untracked(|val| *val)
                                        .into(),
                                },
                                cache_entry_time,
                            );
                        }
                    });
                }
            }
        }
    });

    on_cleanup(ctx.scope(), {
        let cache_page = cache_page.clone();
        move || cache_page()
    });

    create_effect(scope, {
        let client = Rc::clone(&client);
        let ctx = ctx.clone();
        let page_controller = Rc::clone(&page_controller);

        move |prev_page_params| {
            let loc = internal_location.get();
            let page_params = get_page_params(&loc, scope);
            let prev_page_params = prev_page_params.flatten();
            // if loading with the same params again and we are waiting, such as navigate back to
            // the exact same page before enter effect finishes, then show what was loaded
            let is_waiting =
                initial_load_status.with_untracked(|s| matches!(s, PageRequestStatus::Waiting));
            let is_reload = should_reload.get();

            if is_reload && is_waiting {
                should_reload.set_untracked(false);
            } else if is_reload || page_params != prev_page_params || is_waiting {
                should_reload.set_untracked(false);
                let original_page_params = page_params.clone();
                if let Some(PageParams::Collections(ref page_params)) = page_params {
                    let is_traveling_customer = use_context::<AppConfigContext>(scope)
                        .is_some_and(|c| c.get_is_traveling_customer());
                    page_controller.report_app_event(CollectionsAppEvents::Begin {
                        page_params,
                        travelling_customer: is_traveling_customer,
                        transition_source: loc.pageParams.get("transitionSource"),
                    });

                    let unique_page_data_identifier = page_params_to_identifier(page_params);

                    let back: bool = loc.is_back("collections_page");

                    let request_index =
                        page_controller.signals().last_request_counter.get_value() + 1;
                    page_controller
                        .signals()
                        .last_request_counter
                        .try_set_value(request_index);

                    let on_network_success = {
                        let page_controller = Rc::clone(&page_controller);
                        get_initial_load_success_cb(
                            page_controller,
                            request_index,
                            unique_page_data_identifier,
                        )
                    };

                    if !back {
                        cache_page();
                    } else if let Some(collections_cache) = &collections_cache {
                        let key = page_params_to_identifier(page_params);
                        let mut collections_cache = collections_cache.borrow_mut();
                        let data: Option<(&CachedCollectionsPage, &Instant)> =
                            collections_cache.get_with_insert_time(&key);

                        if let Some((data, time)) = data {
                            update_top_nav_allowed(top_nav_data, &loc, page_allows_top_nav, scope);

                            if let Some(is_resiliency_enabled) = data.is_resiliency_enabled {
                                set_resiliency.set(is_resiliency_enabled);
                            }

                            if let Some(page) = &data.data {
                                page_data.set(page.clone().into_sig(scope));

                                if let Some(focus) = &data.focus {
                                    set_default_focus.set(Some(focus.position.clone()));
                                } else {
                                    set_default_focus.set(None);
                                }

                                initial_load_status.set(PageRequestStatus::Success(key));
                                data_fetch_data
                                    .set(Some((time.to_owned(), FreshnessAction::BackCache)));

                                page_controller.report_app_event(
                                    CollectionsAppEvents::FocusRestoration {
                                        transform: false,
                                        v3_treatment: 0,
                                    },
                                );
                                page_controller.report_app_event(
                                    CollectionsAppEvents::FocusRestorationAccuracy {
                                        matches: true,
                                        version: "V2",
                                        flow: "client",
                                    },
                                );

                                return original_page_params;
                            } else if let Some(focus) = &data.focus {
                                initial_load_status.set(PageRequestStatus::Waiting);
                                is_restoring_focus.set(true);

                                let container_id = focus.container_id.clone();
                                let item_id = focus.item_id.clone();

                                page_controller.report_app_event(
                                    CollectionsAppEvents::FocusRestoration {
                                        transform: true,
                                        v3_treatment: 0,
                                    },
                                );

                                client.restore_collection_page_focus(
                                    {
                                        let page_controller = Rc::clone(&page_controller);
                                        move |collections_page, data_fetch_time| {
                                            let matchFound: Option<NetworkBool> = collections_page
                                                .matchFound
                                                .to_owned()
                                                .parse_resiliency(
                                                    PageType::Rust(RustPage::RUST_COLLECTIONS)
                                                        .to_string(),
                                                    "MatchFound",
                                                );
                                            let matchFound: Option<bool> = matchFound.map(|b| b.into());
                                            let matchFound: bool = matchFound.unwrap_or_default();

                                            let restore_focus = {
                                                let page_controller = Rc::clone(&page_controller);
                                                move |page_data: &mut CollectionsPageModel| {
                                                    if matchFound {
                                                        let (new_position, match_found) =
                                                            find_default_focus_after_restoration(
                                                                page_data.container_list.read_only(),
                                                                &container_id,
                                                                &item_id,
                                                            );
                                                        set_default_focus.set(Some(new_position));

                                                        page_controller.report_app_event(
                                                            CollectionsAppEvents::FocusRestorationAccuracy {
                                                                matches: match_found,
                                                                version: "V2",
                                                                flow: "transform"
                                                            });
                                                    } else {
                                                        set_default_focus.set(Some(FocusPosition {
                                                            container: 0,
                                                            item: 0,
                                                            sub_index: None,
                                                        }));
                                                    }
                                                }
                                            };
                                        is_restoring_focus.set(false);
                                            on_network_success(
                                                collections_page,
                                                "restoreCollectionPageFocus",
                                                Some(Box::new(restore_focus)),
                                                data_fetch_time,
                                            );
                                        }
                                    },
                                    get_initial_load_failure_cb(initial_load_status.write_only()),
                                    Rc::clone(&page_controller),
                                    CollectionInitialTypes::NavItem,
                                    &create_restore_collection_page_focus_request(
                                        page_params.page_id.clone(),
                                        page_params.page_type.clone(),
                                        page_params.service_token.clone(),
                                        focus.container_id.clone(),
                                        focus.item_id.clone(),
                                        focus.should_skip_pagination,
                                        scope,
                                    ),
                                );

                                return original_page_params;
                            }
                        }
                    }

                    update_top_nav_allowed(top_nav_data, &loc, page_allows_top_nav, scope);

                    initial_load_status.set(PageRequestStatus::Waiting);
                    let resiliency_page_from_navigation_params =
                        extract_resiliency_page_params_from_location(internal_location.into());

                    let cache_clone = cache.clone();
                    let scope_clone = ctx.scope();

                    get_collection_page(
                        Rc::clone(&client),
                        scope,
                        {
                            let on_network_success = on_network_success.clone();
                            move |collections_page, data_fetch_time| {
                                let callback = handle_onboarding_cx(
                                    scope_clone,
                                    cache_clone,
                                    onboarding_page_model.write_only(),
                                );
                                on_network_success(
                                    collections_page,
                                    "get_collection_page_data",
                                    Some(callback),
                                    data_fetch_time,
                                );
                                set_resiliency.set(false);
                            }
                        },
                        get_initial_load_failure_cb(initial_load_status.write_only()),
                        move |action: StorefrontFallbackPageFulfilledAction<CollectionsPage>| {
                            report_cx_error(RustPage::RUST_COLLECTIONS);
                            // TODO: implement isReengagementQuickplayEnabled for feature config to emit app event
                            //AppEvents.log(CollectionPageNetworkFailed("collectionsPageInitial", this.isReengagementQuickplayEnabled));
                            if let Some(resiliency_state) = action.resiliencyState {
                                set_resiliency.set(resiliency_state.isResiliencyEnabled);
                            }
                            if let Some(page_data) = action.response {
                                // Always treat beekeeper responses as fresh.
                                on_network_success(
                                    page_data,
                                    "get_collection_page_data",
                                    None,
                                    Instant::now(),
                                );
                            } else {
                                log::error!("(FATAL): [collections-ui#collection_initial]: StorefrontFallbackPageFulfilledAction's response is None. Expected CollectionsPage.");
                                let failure_callback =
                                    get_initial_load_failure_cb(initial_load_status.write_only());
                                failure_callback(network::RequestError::ResolverFailed(
                                    "Resiliency action were called but response returned None"
                                        .into(),
                                ));
                            }
                        },
                        Rc::clone(&page_controller),
                        CollectionInitialTypes::NavItem,
                        &create_default_home_page_request(
                            page_params.page_id.clone(),
                            page_params.page_type.clone(),
                            page_params.service_token.clone(),
                            ctx.scope(),
                        ),
                        Rc::clone(&cache),
                        page_params.page_type.clone(),
                        page_params.page_id.clone(),
                        resiliency_page_from_navigation_params,
                        is_reload,
                    );
                }
            } else if page_params == prev_page_params
                && !is_waiting
                && loc.pageParams.contains_key("isRemasterTopNavPage")
                && page_allows_top_nav.try_get().unwrap_or_default()
            {
                // If the user selects an item using the quick-nav while we are already on the page, we will perform a second navigation to the same page
                // and need to reset focus
                reset_page_focus.set(true);
            }

            page_params
        }
    });

    // Prefetch top nav pages after first successful call.
    // Going to a non-Collections page and back will trigger prefetch again.
    create_effect(scope, move |has_prefetched| {
        if has_prefetched.is_some_and(|p| p) {
            return true;
        }

        /*
           Disable prefetching post-OCX to prevent stale cache entries from being used
           for subsequent page loads, ensuring liked titles are reflected in recommendations
        */
        if is_top_nav_prefetch_disabled(scope) || onboarding_page_model.get().is_some() {
            return false;
        }

        let has_prefetched =
            initial_load_status.try_with(|status: &PageRequestStatus<String>| match status {
                PageRequestStatus::Success(_) => {
                    let cache_control = use_context::<CacheControl>(scope);
                    if let Some(cache_control) = cache_control {
                        let prefetch_top_nav_pages_fn =
                            cache_control.prefetch_top_nav_pages.deref();
                        let active_profile_is_adult =
                            active_profile.get_untracked().is_none_or(|p| p.isAdult);
                        prefetch_top_nav_pages_fn(active_profile_is_adult);
                    } else {
                        log::warn!("CacheControl not found in context. Skip prefetching.");
                    }
                    true
                }
                _ => false,
            });

        has_prefetched.unwrap_or(false)
    });

    let client = Rc::clone(&client);
    let in_top_nav_page_closure = move || {
        internal_location
            .try_get()
            .is_some_and(|ref loc| location_is_in_top_nav(top_nav_data, loc, scope))
    };
    let in_top_nav_page = Rc::new(in_top_nav_page_closure);
    let paginate_component_closure = get_paginate_component_closure(
        Rc::clone(&client),
        scope,
        Some(updatable_items),
        Rc::clone(&in_top_nav_page),
        is_rust_quickplay_v2_enabled,
    );
    let paginate_component = Rc::new(paginate_component_closure);

    let paginate_page_closure = get_paginate_page_closure(
        Rc::clone(&client),
        scope,
        updatable_items,
        is_rust_quickplay_v2_enabled,
    );
    let paginate_page = Rc::new(paginate_page_closure);

    configure_navs(ctx.scope(), true);

    let skeleton_state = create_rw_signal(ctx.scope(), SkeletonState::Hidden);
    let skeleton_opacity = create_rw_signal(ctx.scope(), 0.0);
    let content_opacity = create_rw_signal(ctx.scope(), 0.0);
    let should_render_skeleton = create_rw_signal(ctx.scope(), false);
    let skeleton_timeout_task_id = store_value(ctx.scope(), None);
    let is_skeleton_hiding = store_value(ctx.scope(), false);

    // skeleton state
    create_effect(scope, {
        let ctx = ctx.clone();
        move |_| {
            initial_load_status.with(|status| match status {
                PageRequestStatus::Waiting => {
                    skeleton_state.try_set(SkeletonState::Visible);
                }
                _ => {
                    let task_id =
                        ctx.schedule_task(Instant::now() + SKELETON_TIMEOUT_DURATION, move || {
                            skeleton_state.try_set(SkeletonState::Hidden);
                        });
                    skeleton_timeout_task_id.set_value(Some(task_id));
                }
            });
        }
    });

    // react to skeleton state
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |prev_state| {
            let new_state = skeleton_state.try_get();
            let Some(new_state) = new_state else {
                // Likely the scope is disposed at this point. Return a more sensible default to
                // avoid edge case bug where skeleton is shown forever
                return SkeletonState::Hidden;
            };

            if let Some(task_id) = skeleton_timeout_task_id.try_get_value().flatten() {
                ctx.cancel_task(task_id);
                skeleton_timeout_task_id.set_value(None);
            }

            if let Some(prev_state) = prev_state.clone() {
                if prev_state == new_state {
                    // No state change. Do nothing
                    return new_state;
                }
            }

            match new_state {
                SkeletonState::Visible => {
                    // skeleton appears immediately on enter
                    skeleton_opacity.set(1.0);
                    should_render_skeleton.try_set(true);
                    // content disappears immediately on exit
                    content_opacity.set(0.0);

                    // Temporary log message to help track incorrect scenarios where the skeleton state does not get set back to hidden
                    // Tracking TT: https://t.corp.amazon.com/V1649544416
                    info!("{}", &format!(
                        "[COLLECTION_SKELETON_STATE_LOGGER] Setting should_render_skeleton to true, prevState: {:?}, newState {:?}",
                        prev_state, new_state
                    ));

                    if !details_integration.is_seamless_return_from_details_enabled() {
                        update_media_background_rw.set(MediaBackgroundType::None);
                        update_title_details.set(TitleDetailsChangeRequest {
                            navigation_direction: NavigationDirection::NONE,
                            data: TitleDetailsData::Empty,
                        });
                    }

                    is_skeleton_hiding.set_value(false);
                }
                SkeletonState::Hidden => {
                    let new_state_copy = new_state.clone();
                    ctx.with_animation(
                        Animation::default()
                            .with_interpolation(FableMotionEasing::Linear.to_interpolation())
                            .with_duration(FableMotionDuration::Standard.to_duration()),
                        move || {
                            // skeleton disappears with effect on exit
                            skeleton_opacity.set(0.0);
                            // content appears with effect on enter
                            content_opacity.set(1.0);
                        },
                    );
                    ctx.schedule_task(
                        Instant::now() + FableMotionDuration::Standard.to_duration(),
                        move || {
                            if let Some(is_skeleton_hiding) = is_skeleton_hiding.try_get_value() {
                                // The hide animation can be interrupted by another collections page navigation
                                // Only stop rendering the skeleton if the hide animation was not interrupted
                                if is_skeleton_hiding {
                                    should_render_skeleton.try_set(false);
                                    // Temporary log message to help track incorrect scenarios where the skeleton state does not get set back to hidden
                                    // Tracking TT: https://t.corp.amazon.com/V1649544416
                                    info!("{}", &format!(
                                        "[COLLECTION_SKELETON_STATE_LOGGER] Setting should_render_skeleton to false, prevState: {:?}, newState {:?}",
                                        prev_state, new_state_copy
                                    ));
                                }
                            }
                        },
                    );

                    is_skeleton_hiding.set_value(true);
                }
            }

            new_state
        }
    });

    let nav_control = use_context::<NavControl>(ctx.scope());
    let set_content_ready = Rc::new(move || {
        skeleton_state.try_set(SkeletonState::Hidden);
        report_top_nav_metrics(scope);

        if let Some(ref nav_control) = nav_control {
            if onboarding_page_model.get_untracked().is_none() {
                nav_control.enable_focus.try_set(true);
            }
        }
    });

    #[cfg(test)]
    {
        let set_content_ready = Rc::clone(&set_content_ready);
        provide_context(ctx.scope(), TestContext { set_content_ready });
    }
    // GRCOV_BEGIN_COVERAGE

    compose! {
        Stack() {
            Column() {
                Memo(item_builder: Box::new(move |ctx| {
                    initial_load_status.with(|status| {
                        let loc = internal_location.get_untracked();
                        let page_params = get_page_params(&loc, scope);
                        let set_content_ready = Rc::clone(&set_content_ready);
                        match status {
                            PageRequestStatus::Success(_) => {
                                let paginate_component = Rc::clone(&paginate_component);
                                let paginate_page = Rc::clone(&paginate_page);
                                let on_ready = Rc::new({
                                    move || {
                                        set_content_ready();
                                    }
                                });
                                let collections_page_ui = if let Some(model) = onboarding_page_model.get_untracked() {
                                    let should_reload = should_reload.write_only();
                                    let on_page_exit = Rc::new(move |next_location: Location| onboarding_page_exit_handler(scope, should_reload, internal_location, next_location));
                                    // See the explanation of possible variants of [`is_onboarding_page`]
                                    let is_speedbump = !is_onboarding_page_hero_ingress(&loc);
                                    compose_option! {
                                        OnboardingPageUI(
                                            model,
                                            on_ready,
                                            on_page_exit,
                                            is_speedbump,
                                        )
                                        .opacity(page_opacity)
                                        .translate_x(page_translate_x)
                                    }
                                } else {
                                    compose_option! {
                                        CollectionsPageUISig(
                                            page_controller: Rc::clone(&page_controller),
                                            page_data,
                                            update_media_background_rw,
                                            update_title_details,
                                            paginate_component,
                                            paginate_page,
                                            page_allows_top_nav,
                                            default_focus,
                                            current_focus,
                                            on_ready,
                                            reset_page_focus: reset_page_focus.read_only(),
                                            resiliency,
                                            updatable_items
                                        )
                                        .opacity(page_opacity)
                                        .translate_x(page_translate_x)
                                    }
                                };
                                page_controller.page_load_success(page_params.as_ref());
                                collections_page_ui
                            },
                            PageRequestStatus::RequestError(error) => {
                                let page_ui;
                                if should_show_my_stuff_empty_page_error(error, &page_params){
                                    log::warn!(
                                        "Showing empty My Stuff page."
                                    );
                                    page_ui = compose_option! {
                                        MyStuffEmptyCollectionsPage()
                                        .opacity(page_opacity)
                                        .translate_x(page_translate_x)
                                    };
                                page_controller.page_load_success(page_params.as_ref());
                                } else {
                                    log::error!(
                                        "Showing collections page error modal."
                                    );
                                    page_controller.page_load_failure(page_params.as_ref(), error);
                                    page_ui = compose_option! {
                                        CollectionsPageErrorModal(
                                            on_retry: Rc::new(move || should_reload.set(true))
                                        )
                                    };
                                }
                                page_ui
                            },
                            PageRequestStatus::Waiting => {
                                None
                            }
                        }
                    })
                }))
            }
            .opacity(content_opacity)

            if should_render_skeleton.get() {
                CollectionPageSkeleton()
                .opacity(skeleton_opacity)
                .translate_x(skeleton_translate_x)
            }
        }
        .on_key_down(KeyCode::Backspace, {
            let scope = ctx.scope();
            move || page_level_back_handler(scope, should_render_skeleton.into(), initial_load_status.into(), onboarding_page_model, is_restoring_focus)
        })
        .on_key_down(KeyCode::Escape, {
            let scope = ctx.scope();
            move || page_level_back_handler(scope, should_render_skeleton.into(), initial_load_status.into(), onboarding_page_model, is_restoring_focus)
        })
    }
    .focus_pointer_control(use_focus_pointer_control(ctx.scope))
}

fn should_show_my_stuff_empty_page_error(
    error: &RequestError,
    page_params: &Option<PageParams>,
) -> bool {
    let Some(PageParams::Collections(CollectionsPageParams {
        page_id,
        page_type: _,
        service_token: _,
    })) = page_params
    else {
        return false;
    };
    if page_id == MY_STUFF_PAGE_ID {
        if let RequestError::ResolverFailed(error) = error {
            if error == EMPTY_CONTAINER_LIST_ERROR {
                return true;
            }
        }
    }
    false
}

fn location_is_in_top_nav(
    top_nav_data: Signal<TopNavData>,
    location: &Location,
    scope: Scope,
) -> bool {
    top_nav_data.with_untracked(|d| d.location_is_in_top_nav(location, scope))
}

fn update_top_nav_allowed(
    top_nav_data: Signal<TopNavData>,
    location: &Location,
    allowed_signal: RwSignal<bool>,
    scope: Scope,
) {
    if location_is_in_top_nav(top_nav_data, location, scope) {
        allowed_signal.set(true);
    } else {
        allowed_signal.set(false);
    }
}

fn onboarding_page_exit_handler(
    scope: Scope,
    should_reload: WriteSignal<bool>,
    current_location: ReadSignal<Location>,
    next_location: Location,
) {
    /*
        Unhide navs prior to navigating back to home
    */
    configure_navs(scope, true);

    if is_onboarding_page_hero_ingress(&current_location.get_untracked()) {
        /*
           Case 1: current location is merch/onboarding (hero ingress) -> Navigate to next_location
               - next_location is (currently) always home:home
               - Future iterations of OCX may want to support dynamic actions, passing the next_location
                 param better sets us up for this
        */
        let router = use_router(scope);
        router.navigate(next_location, "ONBOARDING_PAGE")
    } else {
        /*
           Case 2: current location is home/home (speed bump) -> Reload
        */
        should_reload.set(true);
    }
}

fn page_level_back_handler(
    scope: Scope,
    should_render_skeleton: Signal<bool>,
    page_state: Signal<PageRequestStatus<String>>,
    onboarding_page_model: RwSignal<Option<OnboardingPageModel>>,
    is_restoring_focus: RwSignal<bool>,
) {
    let page_state = page_state.with_untracked(|s| matches!(s, PageRequestStatus::Success(_)));
    let is_restoring = is_restoring_focus.get_untracked();

    if (!page_state || should_render_skeleton.get_untracked())
        && onboarding_page_model.get_untracked().is_none()
        && !is_restoring
    {
        exit_modal_back_handler(scope);
    }
}

fn report_top_nav_metrics(scope: Scope) {
    if let Some(top_nav_metrics_reporter) = use_context::<Rc<RefCell<TopNavMetricsReporter>>>(scope)
    {
        // Top nav metrics reporter takes care to report metrics only once
        // per session, irrespective of how many collection loads happen.
        top_nav_metrics_reporter.borrow_mut().report_metrics(scope);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::network::dynamic_params::DEFAULT_PRESENTATION_SCHEME;
    use crate::network::parser::collections_response_parser;
    use crate::network::types::FocusPosition;
    use crate::test_assets::mocks::MockNetworkClient;
    use crate::test_assets::mocks::__mock_MockNetworkClient::__new::Context;
    use crate::test_assets::mocks::{MockCollectionsPageController, SuccessCallbackTestSetup};
    use crate::types::collections_types::CollectionsPageModel;
    use crate::utils::cache::create_cache;
    use app_config::test_utils::MockAppConfigBuilder;
    use beekeeper::types::{
        BeekeeperFallbackPageParams, LrcEdgeBeekeeperPageParams, ResiliencyPage, ResiliencyState,
    };
    use beekeeper::MockBeekeeper;
    use common_transform_types::profile::ProfileAvatar;
    use common_transform_types::resiliency::WithResiliency;
    use cross_app_events::app_event::MockAppEventReporter;
    use details_integration::collections::{
        DetailsCollectionIntegrationContext, MockDetailsCollectionIntegrations,
    };
    use fableous::buttons::primary_button::PRIMARY_BUTTON_TEST_ID;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use ignx_compositron::tts::TTSEnabledContext;
    use location::NavigationDirection as NavigationDirectionLocation;
    use location::RustPage::RUST_COLLECTIONS;
    use location::{EnterEffect, ExitEffect, LocationWithEffect, NavigationAction};
    use media_background::types::{MediaStrategy, StandardBackgroundData};
    use mock_instant::MockClock;
    use mockall::Sequence;
    use navigation_menu::context::nav_context::TopNavMode;
    use navigation_menu::mock_data::{mock_addon_item, mock_dynamic_items, mock_static_items};
    use navigation_menu::state::top_nav_metrics_reporter::MockTopNavMetricsReporter;
    use network::common::lrc_edge_constants::DynamicFeature;
    use network::common::DeviceProxyResponse;
    use network_parser::core::{NetworkBool, NetworkOptional};
    use onboarding_lifecycle_service::test_utils::provide_ols_context::provide_ols_context;
    use resiliency_store::MockResiliencyStore;
    use router::{rust_location, MockRouting, RoutingContext};
    use rstest::*;
    use rust_features::{MockRustFeaturesBuilder, WeblabTreatmentString};
    use serde_json::Map;
    use serial_test::serial;
    use std::cell::RefCell;
    use std::collections::HashSet;
    use title_details::core::TitleDetailsData;
    use RequestError::Http;

    #[derive(Default, Clone)]
    struct ReportingExpectations {
        begin_event: Option<(String, String, Option<String>)>, // page_id, page_type, service_token
        focus_restoration: Option<(bool, usize)>,              // transform, v3_treatment
        focus_restoration_accuracy: Option<(bool, String, String)>, // matches, version, flow
        page_load_success: Option<Option<(String, String, Option<String>)>>,
        page_load_failure: Option<Option<(String, String, Option<String>)>>,
        allow_other_events: bool,
    }

    impl ReportingExpectations {
        fn expect_begin_event(
            mut self,
            page_id: &str,
            page_type: &str,
            service_token: Option<&str>,
        ) -> Self {
            self.begin_event = Some((
                page_id.to_string(),
                page_type.to_string(),
                service_token.map(|s| s.to_string()),
            ));
            self
        }

        fn expect_focus_restoration(mut self, transform: bool, v3_treatment: usize) -> Self {
            self.focus_restoration = Some((transform, v3_treatment));
            self
        }

        fn expect_focus_restoration_accuracy(
            mut self,
            matches: bool,
            version: &str,
            flow: &str,
        ) -> Self {
            self.focus_restoration_accuracy =
                Some((matches, version.to_string(), flow.to_string()));
            self
        }

        fn expect_page_load_success(mut self, params: Option<(&str, &str, Option<&str>)>) -> Self {
            self.page_load_success =
                Some(params.map(|p| (p.0.into(), p.1.into(), p.2.map(|t| t.into()))));
            self
        }

        fn expect_page_load_failure(
            mut self,
            page_id: &str,
            page_type: &str,
            service_token: Option<&str>,
        ) -> Self {
            self.page_load_failure = Some(Some((
                page_id.to_string(),
                page_type.to_string(),
                service_token.map(|s| s.to_string()),
            )));
            self
        }

        fn allow_other_events(mut self) -> Self {
            self.allow_other_events = true;
            self
        }

        // Method to apply all expectations to a mock controller
        fn apply_to_mock(&self, mock_controller: &mut MockCollectionsPageController) {
            if let Some((page_id, page_type, service_token)) = &self.begin_event {
                let page_id = page_id.clone();
                let page_type = page_type.clone();
                let service_token = service_token.clone();

                mock_controller
                    .expect_report_app_event()
                    .times(1)
                    .withf(move |event| {
                        event
                            == &CollectionsAppEvents::Begin {
                                page_params: &CollectionsPageParams {
                                    page_id: page_id.clone(),
                                    page_type: page_type.clone(),
                                    service_token: service_token.clone(),
                                },
                                travelling_customer: false,
                                transition_source: None,
                            }
                    })
                    .return_const(());
            }

            if let Some((transform, v3_treatment)) = self.focus_restoration {
                mock_controller
                    .expect_report_app_event()
                    .times(1)
                    .withf(move |event| {
                        event
                            == &CollectionsAppEvents::FocusRestoration {
                                transform,
                                v3_treatment,
                            }
                    })
                    .return_const(());
            }

            if let Some((matches, version, flow)) = &self.focus_restoration_accuracy {
                let matches = *matches;
                let version = version.clone();
                let flow = flow.clone();

                mock_controller
                    .expect_report_app_event()
                    .times(1)
                    .withf(move |event| {
                        event
                            == &CollectionsAppEvents::FocusRestorationAccuracy {
                                matches,
                                version: version.as_str(),
                                flow: flow.as_str(),
                            }
                    })
                    .return_const(());
            }

            if let Some(expected_params) = &self.page_load_success {
                let expected_params = expected_params.clone();
                mock_controller
                    .expect_page_load_success()
                    .withf(
                        move |received_params| match (&expected_params, received_params) {
                            (
                                Some((
                                    expected_page_id,
                                    expected_page_type,
                                    expected_service_token,
                                )),
                                Some(PageParams::Collections(CollectionsPageParams {
                                    page_id,
                                    page_type,
                                    service_token,
                                })),
                            ) => {
                                page_id == expected_page_id
                                    && page_type == expected_page_type
                                    && service_token == expected_service_token
                            }
                            (None, None) => true,
                            _ => false,
                        },
                    )
                    .times(1)
                    .return_const(());
            }

            if let Some(expected_params) = &self.page_load_failure {
                let expected_params = expected_params.clone();
                mock_controller
                    .expect_page_load_failure()
                    .times(1)
                    .withf(move |received_params, _error| {
                        match (&expected_params, received_params) {
                            (
                                Some((
                                    expected_page_id,
                                    expected_page_type,
                                    expected_service_token,
                                )),
                                Some(PageParams::Collections(CollectionsPageParams {
                                    page_id,
                                    page_type,
                                    service_token,
                                })),
                            ) => {
                                page_id == expected_page_id
                                    && page_type == expected_page_type
                                    && service_token == expected_service_token
                            }
                            (None, None) => true,
                            _ => false,
                        }
                    })
                    .return_const(());
            }

            if self.allow_other_events {
                mock_controller.expect_report_app_event().return_const(());
                mock_controller.expect_page_load_success().return_const(());
                mock_controller.expect_page_load_failure().return_const(());
            }
        }
    }

    /// TestSetup provides a reusable way to configure the test environment for collections page tests.
    /// It uses the builder pattern to allow flexible and readable test configuration.
    ///
    /// # Basic Usage
    /// ```ignore
    /// TestSetup::default().setup(scope);
    /// ```
    ///
    /// # Customizing the Setup
    /// To extend TestSetup with new configuration options:
    /// 1. Add the new field to the TestSetup struct
    /// 2. Add a builder method that sets the field value
    /// 3. Update the setup() method to use the field during test initialization
    ///
    /// Example extending with locale support:
    /// ```ignore
    /// pub struct TestSetup {
    ///     locale: Option<String>,
    ///     // existing fields...
    /// }
    ///
    /// impl TestSetup {
    ///     pub fn with_locale(mut self, locale: String) -> Self {
    ///         self.locale = Some(locale);
    ///         self
    ///     }
    /// }
    ///
    /// // Usage:
    /// TestSetup::default()
    ///     .with_locale("en_US".to_string())
    ///     .setup(scope);
    ///
    /// ```
    struct TestSetup {
        location: Location,
        exit_effect: Option<ExitEffect>,
        enter_effect: Option<EnterEffect>,
        previous_page: PageType,
        feature_builder: MockRustFeaturesBuilder,
        disable_top_nav_prefetch: bool,
        client: TestClient,
        fire_tv: MockFireTV,
        expected_navigation: Option<(PageType, &'static str)>,
        reporting_expectations: ReportingExpectations,
    }

    impl Default for TestSetup {
        fn default() -> Self {
            let mut fire_tv = MockFireTV::default();
            fire_tv.expect_is_firetv().return_const(false);
            TestSetup {
                location: TestLocation::TV.into(),
                exit_effect: None,
                enter_effect: None,
                previous_page: PageType::Rust(RUST_COLLECTIONS),
                feature_builder: get_mock_feature_builder(),
                disable_top_nav_prefetch: false,
                client: TestClient::Default(TestClientResponse::None),
                fire_tv,
                expected_navigation: None,
                reporting_expectations: ReportingExpectations::default().allow_other_events(),
            }
        }
    }

    impl TestSetup {
        fn with_additional_features(
            self,
            feature_closure: impl Fn(MockRustFeaturesBuilder) -> MockRustFeaturesBuilder,
        ) -> Self {
            let feature_builder = self.feature_builder;
            let updated_feature_builder = feature_closure(feature_builder);
            Self {
                feature_builder: updated_feature_builder,
                ..self
            }
        }

        fn with_location(self, location: impl Into<Location>) -> Self {
            Self {
                location: location.into(),
                ..self
            }
        }

        fn with_previous_page(self, previous_page: PageType) -> Self {
            Self {
                previous_page,
                ..self
            }
        }

        fn with_enter_effect(self, enter_effect: EnterEffect) -> Self {
            Self {
                enter_effect: Some(enter_effect),
                ..self
            }
        }

        fn with_exit_effect(self, exit_effect: ExitEffect) -> Self {
            Self {
                exit_effect: Some(exit_effect),
                ..self
            }
        }

        fn with_top_nav_prefetch_disabled(self) -> Self {
            Self {
                disable_top_nav_prefetch: true,
                ..self
            }
        }

        fn with_client(self, client: TestClient) -> Self {
            Self { client, ..self }
        }

        fn with_fire_tv(self, fire_tv: MockFireTV) -> Self {
            Self { fire_tv, ..self }
        }

        fn with_expected_navigation(mut self, page_type: PageType, source: &'static str) -> Self {
            self.expected_navigation = Some((page_type, source));
            self
        }

        fn with_reporting_expectations(mut self, expectations: ReportingExpectations) -> Self {
            self.reporting_expectations = expectations;
            self
        }

        fn setup(self, scope: Scope) {
            self.setup_client(scope);
            let TestSetup {
                location,
                exit_effect,
                enter_effect,
                previous_page,
                feature_builder,
                disable_top_nav_prefetch,
                client: _,
                fire_tv,
                expected_navigation,
                reporting_expectations,
            } = self;

            setup_resiliency_store(scope, disable_top_nav_prefetch);
            feature_builder.build_as_mock_and_real_into_context(false, scope);
            let mock_app_config = MockAppConfigBuilder::new()
                .set_ux_locale("en_US".to_string())
                .build();
            provide_context::<AppConfigContext>(scope, Rc::new(mock_app_config));
            let mut mock_routing_context = MockRouting::new();
            let location_with_effect = create_rw_signal(
                scope,
                LocationWithEffect {
                    from_location: Some(Location::default()),
                    to_location: location.clone(),
                    exit_effect,
                    enter_effect,
                },
            );
            let location_sig = create_rw_signal(scope, location.clone());
            mock_routing_context
                .expect_get_previous_page()
                .returning(move || {
                    Some(Location {
                        pageType: previous_page,
                        pageParams: Map::default(),
                    })
                });
            mock_routing_context
                .expect_get_last_navigation_action()
                .returning(move || NavigationAction {
                    direction: NavigationDirectionLocation::Forward,
                    from: previous_page,
                    to: PageType::Rust(RUST_COLLECTIONS),
                });
            mock_routing_context
                .expect_location_with_effect()
                .return_const_st(location_with_effect.read_only());
            mock_routing_context
                .expect_location()
                .return_const_st(location_sig.read_only());

            mock_routing_context.expect_back().return_const_st(());
            if let Some(expected_navigation) = expected_navigation {
                mock_routing_context
                    .expect_navigate()
                    .times(1)
                    .withf(move |loc, source| {
                        loc.pageType == expected_navigation.0 && source == expected_navigation.1
                    })
                    .return_const(());
            };
            provide_context::<RoutingContext>(scope, Rc::new(mock_routing_context));
            provide_context::<WriteSignal<LocationWithEffect>>(
                scope,
                location_with_effect.write_only(),
            );
            provide_context::<RwSignal<Option<(Instant, FreshnessAction)>>>(
                scope,
                create_rw_signal(scope, None),
            );
            fire_tv.provide_mock(scope);
            let app_event_reporter_context = MockAppEventReporter::new_context();
            app_event_reporter_context.expect().returning(|_| {
                let mut mock_reporting_context = MockAppEventReporter::default();
                mock_reporting_context
                    .expect_send_app_event()
                    .return_const(());
                mock_reporting_context
            });
            store_value(scope, app_event_reporter_context);
            let controller_context = MockCollectionsPageController::new_context();
            controller_context
                .expect()
                .returning_st(move |ctx, signals, features| {
                    let mut mock_controller_context = MockCollectionsPageController::default();
                    mock_controller_context
                        .expect_signals()
                        .return_const(signals);
                    mock_controller_context
                        .expect_ctx()
                        .return_const(ctx.clone());
                    mock_controller_context
                        .expect_page_features()
                        .times(..)
                        .return_const(features);
                    mock_controller_context
                        .expect_page_source()
                        .return_const(RUST_COLLECTIONS);

                    // Apply app event expectations
                    reporting_expectations.apply_to_mock(&mut mock_controller_context);

                    mock_controller_context
                });
            store_value(scope, controller_context);
        }

        fn setup_client(&self, scope: Scope) {
            let context = match &self.client {
                TestClient::None => mock_client_no_call(),
                TestClient::Default(response) => {
                    let expected_page_id = self
                        .location
                        .pageParams
                        .get("pageId")
                        .and_then(|v| v.as_str())
                        .unwrap_or("home");
                    let expected_page_type = self
                        .location
                        .pageParams
                        .get("pageType")
                        .and_then(|v| v.as_str())
                        .unwrap_or("home");

                    mock_client(
                        expected_page_id.into(),
                        expected_page_type.into(),
                        DEFAULT_PRESENTATION_SCHEME,
                        vec![],
                        get_default_expected_additional_dynamic_features(),
                        response.clone(),
                    )
                }
                TestClient::DefaultWithPage(page_id, page_type) => mock_client(
                    (*page_id).into(),
                    (*page_type).into(),
                    DEFAULT_PRESENTATION_SCHEME,
                    vec![],
                    get_default_expected_additional_dynamic_features(),
                    TestClientResponse::None,
                ),
                TestClient::WithSuccessResponses(responses) => {
                    mock_client_with_multi_success_response(responses.clone())
                }
                TestClient::RestoreClient(params) => {
                    let expected_page_id = self
                        .location
                        .pageParams
                        .get("pageId")
                        .and_then(|v| v.as_str())
                        .unwrap_or("home");
                    let expected_page_type = self
                        .location
                        .pageParams
                        .get("pageType")
                        .and_then(|v| v.as_str())
                        .unwrap_or("home");
                    mock_restore_client(
                        expected_page_id.into(),
                        expected_page_type.into(),
                        params.clone(),
                    )
                }
                TestClient::Onboarding(with_back_press_response, speed_bump) => {
                    mock_client_with_onboarding_page_response(
                        *with_back_press_response,
                        *speed_bump,
                    )
                }
                TestClient::WithResiliencyAction(
                    expected_beekeeper_params,
                    expected_redirect_params,
                    action_payload,
                ) => mock_client_with_resiliency_action(
                    expected_beekeeper_params.clone(),
                    expected_redirect_params.clone(),
                    action_payload.clone(),
                ),
                TestClient::SportsEdge(response) => {
                    let expected_page_id = self
                        .location
                        .pageParams
                        .get("pageId")
                        .and_then(|v| v.as_str())
                        .unwrap_or("pageId");
                    let expected_page_type = self
                        .location
                        .pageParams
                        .get("pageType")
                        .and_then(|v| v.as_str())
                        .unwrap_or("tournament");

                    mock_client_for_sports_edge(
                        expected_page_id.into(),
                        expected_page_type.into(),
                        DEFAULT_PRESENTATION_SCHEME,
                        vec![],
                        get_default_expected_additional_dynamic_features(),
                        response.clone(),
                    )
                }
            };
            store_value(scope, context); // prevents it being dropped
        }
    }

    fn get_default_expected_additional_dynamic_features() -> Vec<DynamicFeature> {
        vec![
            DynamicFeature::HERO_IMAGE_OPTIONAL,
            DynamicFeature::ENABLE_CSIR,
            DynamicFeature::PromotionalBannerSupported,
            DynamicFeature::DiscoveryAssistantSupported,
            DynamicFeature::LinearStationInAllCarousels,
            DynamicFeature::LinearStationsInHero,
        ]
    }

    // co-locating with the expected dynamic features so that we can easily adjust both when
    // feature flags change
    fn get_mock_feature_builder() -> MockRustFeaturesBuilder {
        MockRustFeaturesBuilder::new()
            .set_is_rust_collection_enabled(true)
            .set_is_promo_banner_widget_scheme_enabled(true)
            .set_is_promo_banner_channel_decor_widget_scheme_enabled(true)
            .set_home_default_focus_experiment_treatment(WeblabTreatmentString::C)
            .set_is_sterling_profiles_enabled(true)
            .set_is_linear_stations_in_hero_enabled(true)
    }

    enum TestLocation {
        Home,
        HomeWithBack,
        TV,
        TVWithBack,
        MyStuff,
        OnboardingHeroIngress,
        Tournament,
    }

    impl From<TestLocation> for Location {
        fn from(value: TestLocation) -> Self {
            match value {
                TestLocation::Home => {
                    rust_location!(RUST_COLLECTIONS, {"pageType" => "home", "pageId" => "home", "serviceToken" => "token", "back" => "false"})
                }
                TestLocation::HomeWithBack => {
                    rust_location!(RUST_COLLECTIONS, {"pageType" => "home", "pageId" => "home", "serviceToken" => "token", "back" => "true"})
                }
                TestLocation::TV => {
                    rust_location!(RUST_COLLECTIONS, {"pageType" => "tv", "pageId" => "home", "serviceToken" => "token", "back" => "false"})
                }
                TestLocation::TVWithBack => {
                    rust_location!(RUST_COLLECTIONS, {"pageType" => "tv", "pageId" => "home", "serviceToken" => "token", "back" => "true"})
                }
                TestLocation::MyStuff => {
                    rust_location!(RUST_COLLECTIONS, {"pageType" => "home", "pageId" => "MyStuff", "serviceToken" => "token", "back" => "false"})
                }
                TestLocation::OnboardingHeroIngress => {
                    rust_location!(RUST_COLLECTIONS, {"pageType" => "merch", "pageId" => "onboarding", "serviceToken" => "token", "back" => "false"})
                }
                TestLocation::Tournament => {
                    rust_location!(RUST_COLLECTIONS, {"pageType" => "tournament", "pageId" => "pageId", "serviceToken" => "token", "back" => "false"})
                }
            }
        }
    }

    enum TestClient {
        None,
        Default(TestClientResponse),
        DefaultWithPage(&'static str, &'static str),
        WithSuccessResponses(Vec<(&'static str, &'static str)>),
        RestoreClient(RestoreClientParams),
        Onboarding(bool, bool),
        WithResiliencyAction(
            BeekeeperFallbackPageParams,
            Option<ResiliencyPage>,
            StorefrontFallbackPageFulfilledAction<CollectionsPage>,
        ),
        SportsEdge(TestClientResponse),
    }

    #[derive(Clone)]
    enum TestClientResponse {
        None,
        SuccessDefault,
        Error,
        SuccessResponse(CollectionsPage),
    }

    #[derive(Clone)]
    struct RestoreClientParams {
        container_id: &'static str,
        item_id: &'static str,
        skip_pagination: &'static bool,
        presentation_scheme: &'static str,
        roles: Vec<String>,
        dynamic_features: Vec<DynamicFeature>,
        response: TestClientResponse,
    }

    impl Default for RestoreClientParams {
        fn default() -> Self {
            Self {
                container_id: "containerToRestore",
                item_id: "itemToRestore",
                skip_pagination: &false,
                presentation_scheme: DEFAULT_PRESENTATION_SCHEME,
                roles: vec![],
                dynamic_features: get_default_expected_additional_dynamic_features(),
                response: TestClientResponse::None,
            }
        }
    }

    #[test]
    #[serial]
    fn render_page_with_correct_page_id_and_page_type() {
        launch_test(
            |ctx| {
                TestSetup::default().setup(ctx.scope);
                setup_page(&ctx)
            },
            |_scope, mut test_game_loop| {
                let _node_tree = test_game_loop.tick_until_done();
            },
        )
    }

    #[test]
    #[serial]
    fn render_with_skeleton_while_waiting_data() {
        launch_test(
            |ctx| {
                TestSetup::default().setup(ctx.scope);

                let initial_td = TitleDetailsChangeRequest {
                    data: TitleDetailsData::Empty,
                    navigation_direction: NavigationDirection::DOWN,
                };
                let initial_mb = MediaBackgroundType::Standard(StandardBackgroundData {
                    id: "".to_string(),
                    image_url: Some("".to_string()),
                    video_id: None,
                    enter_immediately: false,
                    placement: "".to_string(),
                    media_strategy: MediaStrategy::Promo,
                    csm_data: None,
                    interaction_source_override: None,
                });

                setup_page_with_existing_mb_and_td(&ctx, initial_mb, initial_td)
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let title_details =
                    use_context::<RwSignal<TitleDetailsChangeRequest>>(scope).unwrap();
                let media_background = use_context::<RwSignal<MediaBackgroundType>>(scope).unwrap();

                assert_skeleton_is_shown(&node_tree);
                assert_eq!(media_background.get(), MediaBackgroundType::None);
                assert_eq!(
                    title_details.get(),
                    TitleDetailsChangeRequest {
                        data: TitleDetailsData::Empty,
                        navigation_direction: NavigationDirection::NONE
                    }
                );
                assert!(
                    use_context::<RwSignal<Option<(Instant, FreshnessAction)>>>(scope)
                        .unwrap()
                        .get_untracked()
                        .is_none()
                );
            },
        )
    }

    #[test]
    #[serial]
    fn should_not_clear_data_when_details_seamless_is_enabled() {
        launch_test(
            |ctx| {
                TestSetup::default().setup(ctx.scope);

                let mut details_integration = MockDetailsCollectionIntegrations::default();
                details_integration
                    .expect_is_seamless_return_from_details_enabled()
                    .returning(|| true)
                    .times(1);
                provide_context::<DetailsCollectionIntegrationContext>(
                    ctx.scope(),
                    Rc::new(details_integration),
                );

                let initial_td = TitleDetailsChangeRequest {
                    data: TitleDetailsData::Empty,
                    navigation_direction: NavigationDirection::DOWN,
                };
                let initial_mb = MediaBackgroundType::Standard(StandardBackgroundData {
                    id: "".to_string(),
                    image_url: Some("".to_string()),
                    video_id: None,
                    enter_immediately: false,
                    placement: "".to_string(),
                    media_strategy: MediaStrategy::Promo,
                    csm_data: None,
                    interaction_source_override: None,
                });

                setup_page_with_existing_mb_and_td(&ctx, initial_mb, initial_td)
            },
            |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let title_details =
                    use_context::<RwSignal<TitleDetailsChangeRequest>>(scope).unwrap();
                let media_background = use_context::<RwSignal<MediaBackgroundType>>(scope).unwrap();

                assert_skeleton_is_shown(&node_tree);
                assert_ne!(media_background.get(), MediaBackgroundType::None);
                assert_ne!(
                    title_details.get(),
                    TitleDetailsChangeRequest {
                        data: TitleDetailsData::Empty,
                        navigation_direction: NavigationDirection::NONE
                    }
                );
            },
        )
    }

    #[test]
    #[serial]
    fn render_page_when_data_is_available() {
        MockClock::advance(Duration::from_millis(300));
        let expected_time = Instant::now();

        launch_test(
            |ctx| {
                TestSetup::default()
                    .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                    .setup(ctx.scope);

                setup_page(&ctx)
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();

                MockClock::advance(Duration::from_millis(300));

                assert_eq!(
                    use_context::<RwSignal<Option<(Instant, FreshnessAction)>>>(scope)
                        .unwrap()
                        .get_untracked()
                        .expect("Missing data_fetch_data"),
                    (expected_time, FreshnessAction::NetworkClient)
                );
                assert_collections_page_is_shown(&node_tree);
            },
        )
    }

    #[test]
    #[serial]
    fn render_page_with_sports_edge_when_data_is_available() {
        MockClock::advance(Duration::from_millis(300));
        let expected_time = Instant::now();

        launch_test(
            |ctx| {
                TestSetup::default()
                    .with_client(TestClient::SportsEdge(TestClientResponse::SuccessDefault))
                    .with_location(TestLocation::Tournament)
                    .with_additional_features(|builder| {
                        builder.set_enable_sports_edge_enabled(WeblabTreatmentString::T2)
                    })
                    .setup(ctx.scope);

                setup_page(&ctx)
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();

                MockClock::advance(Duration::from_millis(300));

                assert_eq!(
                    use_context::<RwSignal<Option<(Instant, FreshnessAction)>>>(scope)
                        .unwrap()
                        .get_untracked()
                        .expect("Missing data_fetch_data"),
                    (expected_time, FreshnessAction::NetworkClient)
                );
                assert_collections_page_is_shown(&node_tree);
            },
        )
    }

    #[test]
    #[serial]
    fn report_top_nav_metrics_when_page_is_rendered() {
        launch_test(
            |ctx| {
                TestSetup::default()
                    .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                    .setup(ctx.scope);
                let mut mock_top_nav_metrics_reporter = MockTopNavMetricsReporter::default();
                mock_top_nav_metrics_reporter
                    .expect_report_metrics()
                    .times(1)
                    .return_const(());
                provide_context(
                    ctx.scope(),
                    Rc::new(RefCell::new(mock_top_nav_metrics_reporter)),
                );
                setup_page(&ctx)
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();
                assert_collections_page_is_shown(&node_tree);
            },
        )
    }

    #[test]
    #[serial]
    fn render_empty_my_stuff_page() {
        launch_test(
            |ctx| {
                TestSetup::default()
                    .with_location(TestLocation::MyStuff)
                    .with_client(TestClient::Default(TestClientResponse::SuccessResponse(
                        get_unpopulated_my_stuff_collections_page(),
                    )))
                    .setup(ctx.scope);
                setup_page(&ctx)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let skeleton = node_tree.find_by_test_id("empty-my-stuff-collections");
                assert_node_exists!(&skeleton);
            },
        )
    }

    #[test]
    #[serial]
    fn render_onboarding_page() {
        launch_test(
            |ctx| {
                provide_ols_context(&ctx, false);

                TestSetup::default()
                    .with_location(TestLocation::Home)
                    .with_client(TestClient::Onboarding(false, true))
                    .setup(ctx.scope);

                setup_page(&ctx)
            },
            |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                let onboarding_page = node_tree.find_by_test_id("onboarding-page");
                assert_node_exists!(&onboarding_page);
            },
        )
    }

    #[test]
    #[serial]
    fn render_page_with_collectionspagestateoverrides_context_set_to_true() {
        launch_test(
            |ctx| {
                TestSetup::default()
                    .with_client(TestClient::WithSuccessResponses(vec![("home", "tv")]))
                    .setup(ctx.scope);
                provide_context::<CollectionsPageStateOverrides>(
                    ctx.scope(),
                    CollectionsPageStateOverrides {
                        should_reload: create_rw_signal(ctx.scope(), true),
                    },
                );

                setup_page(&ctx)
            },
            |scope, mut test_game_loop| {
                let _node_tree = test_game_loop.tick_until_done();
                let reload_page_signal = use_context::<CollectionsPageStateOverrides>(scope)
                    .unwrap()
                    .should_reload;
                assert!(!reload_page_signal.get());
            },
        )
    }

    #[test]
    #[serial]
    fn call_prefetch_when_data_is_available_the_first_time() {
        #[derive(Clone)]
        struct CacheControlStat {
            prefetch_home_page: u32,
            prefetch_top_nav_pages: u32,
            clear: u32,
        }

        launch_test(
            |ctx| {
                TestSetup::default()
                    .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                    .setup(ctx.scope);
                let cache_control_stat = create_rw_signal(
                    ctx.scope(),
                    CacheControlStat {
                        prefetch_home_page: 0,
                        prefetch_top_nav_pages: 0,
                        clear: 0,
                    },
                );

                let prefetch_home_page = Rc::new(move || {
                    cache_control_stat.update_untracked(|stat| {
                        stat.prefetch_home_page += 1;
                    });
                });
                let prefetch_top_nav_pages = Rc::new(move |_| {
                    cache_control_stat.update_untracked(|stat| {
                        stat.prefetch_top_nav_pages += 1;
                    });
                });
                let clear = Rc::new(move || {
                    cache_control_stat.update_untracked(|stat| {
                        stat.clear += 1;
                    });
                });

                provide_context(
                    ctx.scope(),
                    CacheControl {
                        prefetch_home_page,
                        prefetch_top_nav_pages,
                        clear,
                    },
                );
                provide_context(ctx.scope(), cache_control_stat);

                setup_page(&ctx)
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();

                let cache_control_stat = use_context::<RwSignal<CacheControlStat>>(scope)
                    .unwrap()
                    .get_untracked();
                assert_eq!(cache_control_stat.prefetch_home_page, 0);
                assert_eq!(cache_control_stat.prefetch_top_nav_pages, 1);
                assert_eq!(cache_control_stat.clear, 0);
            },
        )
    }

    #[test]
    #[serial]
    fn should_load_fallback_page_when_circuit_breaker_is_enabled() {
        launch_test(
            |ctx| {
                let beekeeper_params =
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".into(),
                        pageId: "home".into(),
                        pageSection: None,
                    });
                let redirect_param = StorefrontFallbackPageFulfilledAction {
                    resiliencyState: Some(ResiliencyState {
                        isResiliencyEnabled: true,
                        targetPage: None,
                    }),
                    response: Some(get_beekeeper_fallback_collections_page()),
                };
                TestSetup::default()
                    .with_location(TestLocation::Home)
                    .with_client(TestClient::WithResiliencyAction(
                        beekeeper_params,
                        None,
                        redirect_param,
                    ))
                    .setup(ctx.scope);

                setup_page(&ctx)
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();

                let collections_page_skeleton =
                    node_tree.find_by_test_id("collections-page-skeleton");
                assert_node_does_not_exist!(collections_page_skeleton);

                let collections_page = node_tree.find_by_test_id("collections-page");
                assert_node_exists!(collections_page);
            },
        )
    }

    #[test]
    #[serial]
    fn should_set_cache_with_resiliency_flag_when_circuit_breaker_is_enabled() {
        launch_test(
            |ctx| {
                let beekeeper_params =
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".into(),
                        pageId: "home".into(),
                        pageSection: None,
                    });
                let redirect_param = StorefrontFallbackPageFulfilledAction {
                    resiliencyState: Some(ResiliencyState {
                        isResiliencyEnabled: true,
                        targetPage: None,
                    }),
                    response: Some(get_beekeeper_fallback_collections_page()),
                };
                TestSetup::default()
                    .with_location(TestLocation::Home)
                    .with_client(TestClient::WithResiliencyAction(
                        beekeeper_params,
                        None,
                        redirect_param,
                    ))
                    .setup(ctx.scope());

                let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                    create_cache(ctx.scope(), None);

                provide_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(
                    ctx.scope(),
                    cache,
                );

                setup_page(&ctx)
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();

                let collections_page_skeleton =
                    node_tree.find_by_test_id("collections-page-skeleton");
                assert_node_does_not_exist!(collections_page_skeleton);

                let collections_page = node_tree.find_by_test_id("collections-page");
                assert_node_exists!(collections_page);

                // ensure page is cached
                scope.dispose();

                let cache =
                    use_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(scope)
                        .expect("Cache not found in scope");
                let expected_key = String::from("home-home-Some(\"token\")");
                let mut collections_cache = cache.borrow_mut();
                let cached_page: Option<&CachedCollectionsPage> =
                    collections_cache.get(&expected_key);

                assert!(cached_page
                    .is_some_and(|page| page.is_resiliency_enabled.is_some_and(|val| val)))
            },
        )
    }

    #[test]
    #[serial]
    fn should_persist_resiliency_flag_for_restored_beekeeper_pages() {
        launch_test(
            |ctx| {
                TestSetup::default()
                    .with_location(TestLocation::HomeWithBack)
                    .with_client(TestClient::None)
                    .setup(ctx.scope());

                let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                    create_cache(ctx.scope(), None);

                cache.borrow_mut().put(
                    "home-home-Some(\"token\")".to_string(),
                    CachedCollectionsPage {
                        data: Some(get_mock_collections_page(ctx.clone()).into()),
                        focus: Some(CachedFocusData {
                            container_id: "container_id".to_string(),
                            item_id: "item_id".to_string(),
                            should_skip_pagination: false,
                            position: FocusPosition {
                                container: 0,
                                item: 0,
                                sub_index: None,
                            },
                        }),
                        is_resiliency_enabled: Some(true),
                    },
                );

                provide_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(
                    ctx.scope(),
                    cache,
                );

                setup_page(&ctx)
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();

                let collections_page_skeleton =
                    node_tree.find_by_test_id("collections-page-skeleton");
                assert_node_does_not_exist!(collections_page_skeleton);

                let collections_page = node_tree.find_by_test_id("collections-page");
                assert_node_exists!(collections_page);

                // re-cache page to validate that flag has persisted
                scope.dispose();

                let cache =
                    use_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(scope)
                        .expect("Cache not found in scope");
                let expected_key = String::from("home-home-Some(\"token\")");
                let mut collections_cache = cache.borrow_mut();
                let cached_page: Option<&CachedCollectionsPage> =
                    collections_cache.get(&expected_key);

                assert!(cached_page
                    .is_some_and(|page| page.is_resiliency_enabled.is_some_and(|val| val)))
            },
        )
    }

    #[rstest]
    #[case(true, true, 0)]
    #[case(true, false, 1)]
    #[case(false, false, 1)]
    #[serial]
    fn should_prefetch_topnav_based_on_beekeeper_istentpole_flag(
        #[case] beekeeper_in_context: bool,
        #[case] is_tentpole: bool,
        #[case] expected_top_nav_prefetch_calls: usize,
    ) {
        let prefetch_home_page_count: Rc<RefCell<usize>> = Rc::new(RefCell::new(0));
        let prefetch_top_nav_page_count: Rc<RefCell<usize>> = Rc::new(RefCell::new(0));
        let prefetch_top_nav_page_count_clone: Rc<RefCell<usize>> =
            prefetch_top_nav_page_count.clone();

        launch_test(
            move |ctx| {
                let cache_control: CacheControl = CacheControl {
                    prefetch_home_page: Rc::new(move || {
                        *prefetch_home_page_count.borrow_mut() += 1;
                    }),
                    prefetch_top_nav_pages: Rc::new(move |_| {
                        *prefetch_top_nav_page_count.borrow_mut() += 1;
                    }),
                    clear: Rc::new(|| {}),
                };

                provide_context::<CacheControl>(ctx.scope, cache_control);

                if beekeeper_in_context {
                    let mut mock_beekeeper = MockBeekeeper::default();
                    mock_beekeeper
                        .expect_is_tentpole()
                        .return_const(is_tentpole);
                    let mock_beekeeper_context = Rc::new(mock_beekeeper);
                    provide_context::<BeekeeperContext>(ctx.scope, mock_beekeeper_context);
                }

                TestSetup::default()
                    .with_location(TestLocation::TVWithBack)
                    .with_client(TestClient::None)
                    .setup(ctx.scope());

                let cache: Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>> =
                    create_cache(ctx.scope(), None);

                cache.borrow_mut().put(
                    "tv-home-Some(\"token\")".to_string(),
                    CachedCollectionsPage {
                        data: Some(get_mock_collections_page(ctx.clone()).into()),
                        focus: Some(CachedFocusData {
                            position: FocusPosition {
                                container: 0,
                                item: 0,
                                sub_index: None,
                            },
                            container_id: "container_id".to_string(),
                            item_id: "item_id".to_string(),
                            should_skip_pagination: false,
                        }),
                        is_resiliency_enabled: Some(false),
                    },
                );

                provide_context::<Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>>>(
                    ctx.scope(),
                    cache,
                );

                setup_page(&ctx)
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();

                assert_collections_page_is_shown(&node_tree);

                assert_eq!(
                    *prefetch_top_nav_page_count_clone.borrow(),
                    expected_top_nav_prefetch_calls
                );
            },
        )
    }

    #[test]
    #[serial]
    fn should_not_prefetch_topnav_if_client_resliency_config_has_it_disabled() {
        let prefetch_home_page_count: Rc<RefCell<usize>> = Rc::new(RefCell::new(0));
        let prefetch_top_nav_page_count: Rc<RefCell<usize>> = Rc::new(RefCell::new(0));
        let prefetch_top_nav_page_count_clone: Rc<RefCell<usize>> =
            prefetch_top_nav_page_count.clone();

        launch_test(
            move |ctx| {
                let cache_control: CacheControl = CacheControl {
                    prefetch_home_page: Rc::new(move || {
                        *prefetch_home_page_count.borrow_mut() += 1;
                    }),
                    prefetch_top_nav_pages: Rc::new(move |_| {
                        *prefetch_top_nav_page_count.borrow_mut() += 1;
                    }),
                    clear: Rc::new(|| {}),
                };

                provide_context::<CacheControl>(ctx.scope, cache_control);

                TestSetup::default()
                    .with_location(TestLocation::TVWithBack)
                    .with_top_nav_prefetch_disabled()
                    .with_client(TestClient::None)
                    .setup(ctx.scope());

                let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                    create_cache(ctx.scope(), None);

                cache.borrow_mut().put(
                    "tv-home-Some(\"token\")".to_string(),
                    CachedCollectionsPage {
                        data: Some(get_mock_collections_page(ctx.clone()).into()),
                        focus: Some(CachedFocusData {
                            container_id: "container_id".to_string(),
                            item_id: "item_id".to_string(),
                            should_skip_pagination: false,
                            position: FocusPosition {
                                container: 0,
                                item: 0,
                                sub_index: None,
                            },
                        }),
                        is_resiliency_enabled: Some(false),
                    },
                );

                provide_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(
                    ctx.scope(),
                    cache,
                );

                setup_page(&ctx)
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                set_content_ready(scope);
                let node_tree = test_game_loop.tick_until_done();

                let collections_page_skeleton =
                    node_tree.find_by_test_id("collections-page-skeleton");
                assert_node_does_not_exist!(collections_page_skeleton);

                let collections_page = node_tree.find_by_test_id("collections-page");
                assert_node_exists!(collections_page);
                assert_eq!(*prefetch_top_nav_page_count_clone.borrow(), 0);
            },
        )
    }

    #[test]
    #[serial]
    fn should_not_prefetch_topnav_or_cache_page_if_rendering_onboarding_cx() {
        let prefetch_top_nav_page_count: Rc<RefCell<usize>> = Rc::new(RefCell::new(0));
        let prefetch_top_nav_page_count_clone: Rc<RefCell<usize>> =
            prefetch_top_nav_page_count.clone();

        launch_test(
            move |ctx| {
                let cache_control: CacheControl = CacheControl {
                    prefetch_home_page: Rc::new(|| {}),
                    prefetch_top_nav_pages: Rc::new(move |_| {
                        *prefetch_top_nav_page_count.borrow_mut() += 1;
                    }),
                    clear: Rc::new(|| {}),
                };
                let cache: ExpirableLruCacheRc<String, CollectionsPage> = ExpirableLruCache::new_rc(
                    COLLECTION_PAGE_CACHE_SIZE,
                    Box::new(collection_page_ttl_resolver),
                );
                cache
                    .borrow_mut()
                    .put("home-home".to_string(), get_mock_page_response());

                provide_context::<CacheControl>(ctx.scope, cache_control);
                provide_context::<ExpirableLruCacheRc<String, CollectionsPage>>(ctx.scope(), cache);
                provide_ols_context(&ctx, false);

                TestSetup::default()
                    .with_location(TestLocation::Home)
                    .with_client(TestClient::Onboarding(false, true))
                    .setup(ctx.scope());

                setup_page(&ctx)
            },
            move |scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let onboarding_page = node_tree.find_by_test_id("onboarding-page");

                assert_node_exists!(onboarding_page);
                assert_eq!(*prefetch_top_nav_page_count_clone.borrow(), 0);

                let cache =
                    use_context::<ExpirableLruCacheRc<String, CollectionsPage>>(scope).unwrap();
                let mut cache = cache.borrow_mut();
                let cache_value = cache.get(&"home-home".to_string());

                assert!(cache_value.is_none());
            },
        )
    }

    #[rstest]
    #[case(WeblabTreatmentString::C, false, true)]
    #[case(WeblabTreatmentString::T1, false, false)]
    #[case(WeblabTreatmentString::T2, false, false)]
    #[case(WeblabTreatmentString::T1, true, true)]
    #[serial]
    fn should_disable_initial_nav_focus_when_default_focus_experiment_enabled(
        #[case] experiment_treatment: WeblabTreatmentString,
        #[case] previous_page_is_live_tv: bool,
        #[case] expected_initial_focus: bool,
    ) {
        launch_test(
            move |ctx| {
                let previous_page = if previous_page_is_live_tv {
                    RustPage::RUST_LIVE_TV
                } else {
                    RustPage::RUST_COLLECTIONS
                };
                TestSetup::default()
                    .with_previous_page(PageType::Rust(previous_page))
                    .with_additional_features(|builder| {
                        builder.set_home_default_focus_experiment_treatment(
                            experiment_treatment.clone(),
                        )
                    })
                    .setup(ctx.scope());

                let nav_control = NavControl {
                    disable_top_nav_focus_trap: create_rw_signal(ctx.scope(), false),
                    disable_utility_nav_focus_trap: create_rw_signal(ctx.scope(), false),
                    show_utility_nav: create_rw_signal(ctx.scope(), true),
                    show_top_nav_intent: create_rw_signal(ctx.scope(), true),
                    set_report_top_nav_visibility: create_signal(ctx.scope(), None).1,
                    focused_nav: create_focus_value_signal(ctx.scope()),
                    top_nav_mode: create_signal(ctx.scope(), TopNavMode::TopNav).0,
                    enable_focus: create_rw_signal(ctx.scope(), true),
                };
                provide_context(ctx.scope(), nav_control);

                setup_page(&ctx)
            },
            move |scope, mut test_game_loop| {
                let _node_tree = test_game_loop.tick_until_done();
                let nav_control = use_context::<NavControl>(scope).unwrap();
                assert_eq!(nav_control.enable_focus.get(), expected_initial_focus);
            },
        )
    }

    mod focus_restoration {
        use crate::network::types::{CachedFocusData, FocusPosition};
        use common_transform_types::resiliency::WithResiliency;
        use mock_instant::MockClock;
        use navigation_menu::context::nav_context::{FocusedNav, TopNavMode};
        use network_parser::core::NetworkOptional;
        use taps_parameters::{MockTapsParameterStore, TapsParameterStore};

        use super::*;

        #[test]
        #[serial]
        fn should_load_page_from_cache_when_back_is_provided() {
            MockClock::advance(Duration::from_millis(300));
            let cache_entry_time = Instant::now();
            MockClock::advance(Duration::from_millis(300));

            launch_test(
                move |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::None)
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        create_cache(ctx.scope(), None);

                    cache.borrow_mut().put_with_entry_time(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: Some(get_mock_collections_page(ctx.clone()).into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                        cache_entry_time,
                    );

                    provide_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(
                        ctx.scope(),
                        cache,
                    );

                    setup_page(&ctx)
                },
                move |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    set_content_ready(scope);
                    let node_tree = test_game_loop.tick_until_done();

                    assert_eq!(
                        use_context::<RwSignal<Option<(Instant, FreshnessAction)>>>(scope)
                            .unwrap()
                            .get_untracked(),
                        Some((cache_entry_time, FreshnessAction::BackCache))
                    );
                    assert_collections_page_is_shown(&node_tree);
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_load_page_from_cache_when_back_is_not_provided() {
            launch_test(
                |ctx| {
                    TestSetup::default().setup(ctx.scope());
                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "other-type".to_string(),
                        CachedCollectionsPage {
                            data: Some(get_mock_collections_page(ctx.clone()).into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    assert_skeleton_is_shown(&node_tree);
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_load_page_from_cache_when_no_match_found() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .setup(ctx.scope());
                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "other-type".to_string(),
                        CachedCollectionsPage {
                            data: Some(get_mock_collections_page(ctx.clone()).into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    assert_skeleton_is_shown(&node_tree);
                },
            )
        }

        #[rstest]
        #[serial]
        #[case(&true)]
        #[serial]
        #[case(&false)]
        fn should_not_load_page_from_cache_when_match_found_but_invalidated(
            #[case] skip_pagination: &'static bool,
        ) {
            launch_test(
                move |ctx| {
                    let restore_client_params = RestoreClientParams {
                        skip_pagination,
                        ..RestoreClientParams::default()
                    };
                    TestSetup::default()
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .with_location(TestLocation::TVWithBack)
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: Some(CachedFocusData {
                                container_id: "containerToRestore".to_string(),
                                item_id: "itemToRestore".to_string(),
                                should_skip_pagination: *skip_pagination,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    assert_skeleton_is_shown(&node_tree);
                },
            )
        }

        #[test]
        #[serial]
        fn provide_taps_roles_to_restore_focus_transform_when_provided() {
            launch_test(
                move |ctx| {
                    let restore_client_params = RestoreClientParams {
                        presentation_scheme: "retrieved-scheme",
                        roles: vec!["role1".to_string(), "role2".to_string()],
                        ..RestoreClientParams::default()
                    };
                    TestSetup::default()
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .with_location(TestLocation::TVWithBack)
                        .setup(ctx.scope());

                    let mut mock_parameters = MockTapsParameterStore::new();
                    mock_parameters
                        .expect_get_collections_roles()
                        .times(1)
                        .returning(|| vec!["role1".to_string(), "role2".to_string()]);
                    mock_parameters
                        .expect_get_presentation_scheme()
                        .times(1)
                        .returning(|_| "retrieved-scheme".to_string());

                    provide_context::<Rc<dyn TapsParameterStore>>(
                        ctx.scope(),
                        Rc::new(mock_parameters),
                    );

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: Some(CachedFocusData {
                                container_id: "containerToRestore".to_string(),
                                item_id: "itemToRestore".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    assert_skeleton_is_shown(&node_tree);
                },
            )
        }

        #[test]
        #[serial]
        fn provide_additional_dynamic_features_to_restore_focus_transform_when_provided() {
            launch_test(
                move |ctx| {
                    let mut expected_additional_dynamic_features =
                        get_default_expected_additional_dynamic_features();
                    expected_additional_dynamic_features.push(DynamicFeature::GLOBAL_TRAINING);

                    let restore_client_params = RestoreClientParams {
                        dynamic_features: expected_additional_dynamic_features,
                        presentation_scheme: "retrieved-scheme",
                        ..RestoreClientParams::default()
                    };

                    TestSetup::default()
                        .with_additional_features(|builder| {
                            builder
                                .set_is_global_training_enabled(true)
                                .set_get_beard_supported_carousel_on_rust_enabled_string(
                                    WeblabTreatmentString::C,
                                )
                        })
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .setup(ctx.scope());

                    let mut mock_parameters = MockTapsParameterStore::new();
                    mock_parameters
                        .expect_get_collections_roles()
                        .times(1)
                        .returning(std::vec::Vec::new);
                    mock_parameters
                        .expect_get_presentation_scheme()
                        .times(1)
                        .returning(|_| "retrieved-scheme".to_string());

                    provide_context::<Rc<dyn TapsParameterStore>>(
                        ctx.scope(),
                        Rc::new(mock_parameters),
                    );

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: Some(CachedFocusData {
                                container_id: "containerToRestore".to_string(),
                                item_id: "itemToRestore".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    assert_skeleton_is_shown(&node_tree);
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_load_page_from_cache_or_restore_from_network_when_match_found_but_invalidated_and_focus_not_set(
        ) {
            launch_test(
                move |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: None,
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    assert_skeleton_is_shown(&node_tree);
                },
            )
        }

        #[test]
        #[serial]
        fn should_apply_default_focus_to_page_after_restoring_from_network_if_match_found() {
            MockClock::advance(Duration::from_millis(300));
            let expected_time = Instant::now();

            launch_test(
                |ctx| {
                    let mut mock_response = get_mock_page_response();
                    mock_response.matchFound =
                        WithResiliency::Ok(NetworkOptional::Some(NetworkBool::Item(true)));

                    let container_id: &str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy";
                    let item_id: &str = "amzn1.dv.gti.17b73916-9ce2-47be-bb33-b211c4f92ff2";

                    let restore_client_params = RestoreClientParams {
                        container_id,
                        item_id,
                        response: TestClientResponse::SuccessResponse(mock_response),
                        ..RestoreClientParams::default()
                    };

                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .setup(ctx.scope());

                    let cache: Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: Some(CachedFocusData {
                                container_id: container_id.to_string(),
                                item_id: item_id.to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 1, // these are purposefully wrong to ensure we're restoring using container_id and item_id for restoring.
                                    item: 2,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                move |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    set_content_ready(scope);
                    let node_tree = test_game_loop.tick_until_done();

                    assert_collections_page_is_shown(&node_tree);

                    // now dispose of it and see if correct focus was remembered.
                    let cache = use_context::<
                        Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>>,
                    >(scope)
                    .unwrap();
                    cache.borrow_mut().clear();

                    MockClock::advance(Duration::from_millis(300));

                    assert_eq!(
                        use_context::<RwSignal<Option<(Instant, FreshnessAction)>>>(scope)
                            .unwrap()
                            .get_untracked()
                            .expect("Missing data_fetch_data"),
                        (expected_time, FreshnessAction::NetworkClient)
                    );

                    // clean up page so it's cached again.
                    scope.dispose();

                    let mut borrowed_cache = cache.borrow_mut();
                    let (position, time) = &borrowed_cache
                        .get_with_insert_time(&"tv-home-Some(\"token\")".to_string())
                        .unwrap();

                    let position = position.focus.clone().unwrap().position;

                    // assert focus was restored and then resaved to the cache.
                    assert_eq!(position.container, 3);
                    assert_eq!(position.item, 4);
                    assert_eq!(**time, expected_time);
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_apply_default_focus_to_page_after_restoring_from_network_if_match_not_found()
        {
            launch_test(
                |ctx| {
                    let mut mock_response = get_mock_page_response();
                    mock_response.matchFound =
                        WithResiliency::Ok(NetworkOptional::Some(NetworkBool::Item(false)));

                    let container_id: &str = "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy";
                    let item_id: &str = "amzn1.dv.gti.17b73916-9ce2-47be-bb33-b211c4f92ff2";

                    let restore_client_params = RestoreClientParams {
                        container_id,
                        item_id,
                        response: TestClientResponse::SuccessResponse(mock_response),
                        ..RestoreClientParams::default()
                    };

                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .setup(ctx.scope());
                    let cache: Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: Some(CachedFocusData {
                                container_id: container_id.to_string(),
                                item_id: item_id.to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 1, // these are purposefully wrong to ensure we're restoring using container_id and item_id for restoring.
                                    item: 2,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    set_content_ready(scope);
                    let node_tree = test_game_loop.tick_until_done();

                    assert_collections_page_is_shown(&node_tree);

                    // now dispose of it and see if correct focus was remembered.
                    let cache = use_context::<
                        Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>>,
                    >(scope)
                    .unwrap();
                    cache.borrow_mut().clear();

                    // clean up page so it's cached again.
                    scope.dispose();

                    let mut borrowed_cache = cache.borrow_mut();
                    let position = &borrowed_cache
                        .get(&"tv-home-Some(\"token\")".to_string())
                        .unwrap()
                        .focus
                        .as_ref()
                        .unwrap()
                        .position;

                    // assert focus was set to the top of the page and then resaved to the cache.
                    assert_eq!(position.container, 0);
                    assert_eq!(position.item, 0);
                },
            )
        }

        #[test]
        #[serial]
        fn should_store_to_cache_when_switching_collection_pages() {
            let cache_time = Instant::now();

            launch_test(
                move |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::DefaultWithPage("page", "new"))
                        .setup(ctx.scope());
                    // only the next page should be called.
                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);
                    let page_response = get_mock_collections_page(ctx.clone());
                    page_response.unique_id.set("tv-home".to_string());

                    // using cache to populate original data, will remove once loaded.
                    cache.borrow_mut().put_with_entry_time(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: Some(page_response.into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 3,
                                    item: 2,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                        cache_time,
                    );

                    setup_page(&ctx)
                },
                move |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    set_content_ready(scope);
                    let node_tree = test_game_loop.tick_until_done();
                    let cache = use_context::<
                        Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>>,
                    >(scope)
                    .unwrap();
                    cache.borrow_mut().clear();

                    let key = "tv-home".to_string();
                    assert!(cache.borrow_mut().get(&key).is_none());

                    assert_collections_page_is_shown(&node_tree);

                    let set_location_with_effect =
                        use_context::<WriteSignal<LocationWithEffect>>(scope).unwrap();
                    set_location_with_effect.set(
                        LocationWithEffect {
                            from_location: Some(Location::default()),
                            to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "new", "pageId" => "page" }),
                            exit_effect: None,
                            enter_effect: None,
                        }
                    );

                    let node_tree = test_game_loop.tick_until_done();
                    assert_skeleton_is_shown(&node_tree);

                    let mut cache = cache.borrow_mut();
                    let (cache_value, time) = cache
                        .get_with_insert_time(&key)
                        .expect("cache value not found");
                    assert!(cache_value.data.is_some());
                    assert_eq!(cache_value.focus, Some(CachedFocusData {
                        position: FocusPosition { container: 3, item: 2, sub_index: None},
                        should_skip_pagination: false,
                        container_id: "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(),
                        item_id: "amzn1.dv.gti.c4a9f692-8cdd-bafd-867f-0603a11a2fde".to_string()
                    }));
                    assert_eq!(&cache_time, time);
                },
            )
        }

        #[test]
        #[serial]
        fn should_store_to_cache_when_switching_to_another_page_type() {
            let cache_time = Instant::now();

            launch_test(
                move |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::None)
                        .setup(ctx.scope());
                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);
                    let page_response = get_mock_collections_page(ctx.clone());
                    page_response.unique_id.set("tv-home".to_string());

                    // using cache to populate original data, will remove once loaded.
                    cache.borrow_mut().put_with_entry_time(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: Some(page_response.into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 3,
                                    item: 2,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                        cache_time,
                    );

                    setup_page(&ctx)
                },
                move |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    set_content_ready(scope);
                    let node_tree = test_game_loop.tick_until_done();
                    let cache = use_context::<
                        Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>>,
                    >(scope)
                    .unwrap();
                    cache.borrow_mut().clear();

                    let key = "tv-home".to_string();
                    assert!(cache.borrow_mut().get(&key).is_none());

                    assert_collections_page_is_shown(&node_tree);

                    scope.dispose();

                    let mut cache = cache.borrow_mut();
                    let cache_value = cache.get(&key);

                    assert!(cache_value.is_some());
                    let (cache_value, time) = cache
                        .get_with_insert_time(&key)
                        .expect("cache value not found");
                    assert!(cache_value.data.is_some());
                    assert_eq!(cache_value.focus, Some(CachedFocusData {
                        position: FocusPosition { container: 3, item: 2, sub_index: None},
                        should_skip_pagination: false,
                        container_id: "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(),
                        item_id: "amzn1.dv.gti.c4a9f692-8cdd-bafd-867f-0603a11a2fde".to_string()
                    }));
                    assert_eq!(&cache_time, time);
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_store_focus_in_cache_when_focused_on_top_nav() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::None)
                        .setup(ctx.scope());
                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);
                    let page_response = get_mock_collections_page(ctx.clone());
                    page_response.unique_id.set("tv-home".to_string());

                    // using cache to populate original data, will remove once loaded.
                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: Some(page_response.into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    let nav_control = NavControl {
                        disable_top_nav_focus_trap: create_rw_signal(ctx.scope(), false),
                        disable_utility_nav_focus_trap: create_rw_signal(ctx.scope(), false),
                        show_utility_nav: create_rw_signal(ctx.scope(), true),
                        show_top_nav_intent: create_rw_signal(ctx.scope(), true),
                        set_report_top_nav_visibility: create_signal(ctx.scope(), None).1,
                        focused_nav: create_focus_value_signal(ctx.scope()),
                        top_nav_mode: create_signal(ctx.scope(), TopNavMode::TopNav).0,
                        enable_focus: create_rw_signal(ctx.scope(), true),
                    };
                    provide_context(ctx.scope(), nav_control);

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    test_game_loop.tick_until_done();
                    set_content_ready(scope);
                    let node_tree = test_game_loop.tick_until_done();
                    let cache = use_context::<
                        Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>>,
                    >(scope)
                    .unwrap();
                    cache.borrow_mut().clear();

                    let key = "tv-home".to_string();
                    assert!(cache.borrow_mut().get(&key).is_none());

                    assert_collections_page_is_shown(&node_tree);

                    let nav_control = use_context::<NavControl>(scope).unwrap();
                    nav_control.focused_nav.mock_set(Some(FocusedNav::TopNav));

                    test_game_loop.tick_until_done();

                    scope.dispose();

                    let mut cache = cache.borrow_mut();
                    let cache_value = cache.get(&key);

                    assert!(cache_value.is_some());
                    let cache_value = cache_value.expect("cache value not found");
                    assert!(cache_value.data.is_some());
                    assert!(cache_value.focus.is_none());
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_store_to_cache_when_switching_collection_pages_if_not_loaded_yet() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .setup(ctx.scope());
                    provide_cache(&ctx);

                    let (show_page, set_show_page) = create_signal(ctx.scope(), true);
                    provide_context::<WriteSignal<bool>>(ctx.scope(), set_show_page);

                    let update_media_background_rw =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    let update_title_details =
                        create_signal(ctx.scope(), title_details_request()).1;
                    let top_nav_data = Signal::derive(ctx.scope(), move || {
                        TopNavData::new(
                            mock_static_items(),
                            mock_dynamic_items(),
                            mock_addon_item(),
                            Default::default(),
                            Rc::new(|_| {}),
                            true,
                        )
                    });
                    let modal_data = create_rw_signal(ctx.scope(), vec![]).write_only();
                    let active_profile: Signal<Option<Profile>> =
                        create_signal(ctx.scope(), Some(create_mock_profile()))
                            .0
                            .into();

                    let upsell_mlp_context = upsell_mlp::clear_mlp_shown_flag_if_needed_context();
                    upsell_mlp_context.expect().once().return_const(());

                    compose! {
                        Memo(item_builder: Box::new(move |ctx| {
                            if show_page.get() {
                                compose_option! { CollectionsPage(update_media_background_rw, update_title_details, top_nav_data, modal_data, active_profile) }
                            } else {
                                None
                            }
                        }))
                    }
                },
                |scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();
                    let cache = use_context::<
                        Rc<RefCell<ExpirableLruCache<String, CachedCollectionsPage>>>,
                    >(scope)
                    .unwrap();

                    assert_skeleton_is_shown(&node_tree);

                    // dismantles the page.
                    let set_page_shown = use_context::<WriteSignal<bool>>(scope).unwrap();
                    set_page_shown.set(false);

                    let mut cache = cache.borrow_mut();
                    let cache_value = cache.get(&"tv-home".to_string());

                    assert!(cache_value.is_none());
                },
            )
        }
    }

    /// Test suite for exit and enter effects during navigation
    /// Basic timing of events are
    ///
    ///   exit effect    enter effect    load page
    /// |------------->|-------------->|----------->|
    mod navigation_effect {
        use super::*;
        use fableous::animations::{FableMotionDuration, MotionDuration};

        #[test]
        #[serial]
        fn enter_from_another_page() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    // tick twice to set skeleton translate x but not the enter effect
                    // otherwise the effect will finish in the next tick
                    test_game_loop.tick_once();
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(
                        &node_tree,
                        enter_effect().from_translate_x(),
                    );

                    // run until end of enter effect
                    MockClock::advance(enter_effect().duration().to_owned());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_skeleton_is_shown(&node_tree);

                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();

                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                },
            )
        }

        #[test]
        #[serial]
        fn exit_to_another_page() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    let media_background =
                        use_context::<RwSignal<MediaBackgroundType>>(scope).unwrap();

                    // load and show page
                    test_game_loop.tick_until_done();
                    MockClock::advance(enter_effect().duration().to_owned());
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);

                    // switch to another page
                    let location_with_effect =
                        use_context::<WriteSignal<LocationWithEffect>>(scope).unwrap();
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS)),
                        to_location: rust_location!(RUST_DETAILS),
                        exit_effect: Some(exit_effect()),
                        enter_effect: Some(enter_effect()),
                    });

                    // run exit effect
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_faded_away(
                        &node_tree,
                        exit_effect().to_translate_x(),
                    );
                    // media background is hidden
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);
                },
            )
        }

        #[test]
        #[serial]
        fn exit_and_enter_to_collections_page() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::WithSuccessResponses(vec![
                            ("home", "tv"),
                            ("another_id", "some_type"),
                        ]))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    let media_background =
                        use_context::<RwSignal<MediaBackgroundType>>(scope).unwrap();

                    // load and show page
                    test_game_loop.tick_until_done();
                    MockClock::advance(enter_effect().duration().to_owned());
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);

                    // switch to another collections page
                    let location_with_effect =
                        use_context::<WriteSignal<LocationWithEffect>>(scope).unwrap();
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "some_id"})),
                        to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "another_id"}),
                        exit_effect: Some(exit_effect()),
                        enter_effect: Some(enter_effect()),
                    });
                    // run exit effect
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_faded_away(
                        &node_tree,
                        exit_effect().to_translate_x(),
                    );
                    // media background is hidden
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);

                    // finish exit effect and timer, start enter effect
                    MockClock::advance(exit_effect().duration().to_owned());
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(
                        &node_tree,
                        enter_effect().from_translate_x(),
                    );

                    // run until end of enter effect, page should be loaded and shown
                    MockClock::advance(enter_effect().duration().to_owned());
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);
                },
            )
        }

        /// Receives a navigation event during exit effect. This can happen with top nav focus move.
        /// With the second event, it only waits for the new enter effect duration as debounce. In
        /// this case, it is shorter than the original effect, so no timing change, but loads the
        /// page with new destination page params.
        ///
        ///     Original
        ///   exit effect    enter effect    load page
        /// |------------->|-------------->|----------->|
        ///          |-------------->|
        ///            new event
        ///            debounce
        #[test]
        #[serial]
        fn trigger_page_load_during_exit_effect() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::WithSuccessResponses(vec![
                            ("home", "tv"),
                            ("id3", "some_type"),
                        ]))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());
                    // expect to load the last navigation params

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    let media_background =
                        use_context::<RwSignal<MediaBackgroundType>>(scope).unwrap();
                    let location_with_effect =
                        use_context::<WriteSignal<LocationWithEffect>>(scope).unwrap();

                    // load and show page
                    test_game_loop.tick_until_done();
                    MockClock::advance(enter_effect().duration().to_owned());
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);

                    // switch to another collections page
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id1"})),
                        to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id2"}),
                        exit_effect: Some(exit_effect()),
                        enter_effect: Some(enter_effect()),
                    });
                    test_game_loop.tick_until_done();
                    // media background is hidden
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);

                    // halfway through exit effect, load collections page again
                    MockClock::advance(exit_effect().duration().to_owned() / 2);
                    test_game_loop.tick_until_done();
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id2"})),
                        to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id3"}),
                        exit_effect: Some(exit_effect()),
                        enter_effect: Some(enter_effect()),
                    });
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_faded_away(
                        &node_tree,
                        exit_effect().to_translate_x(),
                    );

                    // end of original exit effect, expect to start enter effect
                    MockClock::advance(exit_effect().duration().to_owned() / 2);
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(
                        &node_tree,
                        enter_effect().from_translate_x(),
                    );
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);

                    // run until end of enter effect, page should be loaded and shown
                    MockClock::advance(enter_effect().duration().to_owned());
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);
                },
            )
        }

        #[test]
        #[serial]
        #[ignore]
        fn trigger_page_load_with_no_enter_effect_after_exit_effect() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::WithSuccessResponses(vec![
                            ("home", "tv"),
                            ("id2", "some_type"),
                            ("id3", "some_type"),
                        ]))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());

                    // expect to load the last navigation params

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    let media_background =
                        use_context::<RwSignal<MediaBackgroundType>>(scope).unwrap();
                    let location_with_effect =
                        use_context::<WriteSignal<LocationWithEffect>>(scope).unwrap();

                    // load and show page
                    test_game_loop.tick_until_done();
                    MockClock::advance(enter_effect().duration().to_owned());
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    test_game_loop.tick_until_done();
                    let node_tree = test_game_loop.tick_until_done();

                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);

                    // switch to another collections page (with no enter effect)
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id1"})),
                        to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id2"}),
                        exit_effect: Some(exit_effect()),
                        enter_effect: None,
                    });
                    // setup effects
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    MockClock::advance(exit_effect().duration().to_owned());
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_collections_page_is_shown(&node_tree);

                    // switch to new collections page (with no exit effect, only enter)
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id2"})),
                        to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id3"}),
                        exit_effect: None,
                        enter_effect: Some(enter_effect()),
                    });

                    // setup enter effect scheduled for next tick
                    test_game_loop.tick_once();
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(&node_tree, 200.0);
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);

                    MockClock::advance(enter_effect().duration().to_owned());

                    // Get skeleton effect to finish
                    test_game_loop.tick_until_done();

                    // Invoke delay function that was scheduled in previous tick
                    test_game_loop.tick_until_done();

                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // Skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree)
                },
            )
        }

        /// Receives a navigation event during enter effect. This can happen with top nav focus move.
        /// With the second event, it waits for the new enter effect duration as debounce. In this
        /// case, it is later than the original effect. The enter effect is not affected, but it
        /// stays on the skeleton longer until the end of new event debounce period before it loads
        /// the page.
        ///
        ///     Original
        ///   exit effect    enter effect
        /// |------------->|-------------->|
        ///                          |-------------->|----------->|
        ///                            new event        load page
        ///                            debounce
        #[test]
        #[serial]
        fn trigger_page_load_during_enter_event() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::WithSuccessResponses(vec![
                            ("home", "tv"),
                            ("id3", "some_type"),
                        ]))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());
                    // expect to load the last navigation params

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    let media_background =
                        use_context::<RwSignal<MediaBackgroundType>>(scope).unwrap();
                    let location_with_effect =
                        use_context::<WriteSignal<LocationWithEffect>>(scope).unwrap();

                    // load and show page
                    test_game_loop.tick_until_done();
                    MockClock::advance(enter_effect().duration().to_owned());
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);

                    // switch to another collections page
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id1"})),
                        to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id2"}),
                        exit_effect: Some(exit_effect()),
                        enter_effect: Some(enter_effect()),
                    });
                    // run exit effect
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_faded_away(
                        &node_tree,
                        exit_effect().to_translate_x(),
                    );
                    // media background is hidden
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);

                    // finish exit effect and timer, start enter effect
                    MockClock::advance(exit_effect().duration().to_owned());
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(
                        &node_tree,
                        enter_effect().from_translate_x(),
                    );

                    // halfway through enter effect, load collections page again
                    MockClock::advance(enter_effect().duration().to_owned() / 2);
                    test_game_loop.tick_until_done();
                    location_with_effect.set(LocationWithEffect {
                        from_location: Some(rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id2"})),
                        to_location: rust_location!(RUST_COLLECTIONS, {"pageType" => "some_type", "pageId" => "id3"}),
                        exit_effect: Some(exit_effect()),
                        enter_effect: Some(enter_effect()),
                    });
                    test_game_loop.tick_until_done();

                    // end of original enter effect, should not load page yet, skeleton enter effect is done
                    MockClock::advance(enter_effect().duration().to_owned() / 2);
                    let node_tree = test_game_loop.tick_until_done();
                    assert_skeleton_is_shown_with_translate_x(&node_tree, 0.0);
                    assert_eq!(media_background.get_untracked(), MediaBackgroundType::None);

                    // run until end of new event debounce, page should be loaded and shown
                    MockClock::advance(enter_effect().duration().to_owned() / 2);
                    test_game_loop.tick_until_done();
                    // load page
                    set_content_ready(scope);
                    test_game_loop.tick_until_done();
                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                    // media background is shown
                    assert_ne!(media_background.get_untracked(), MediaBackgroundType::None);
                },
            )
        }

        #[test]
        #[serial]
        fn skeleton_dismisses_after_timeout() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    // tick twice to set skeleton translate x but not the enter effect
                    // otherwise the effect will finish in the next tick
                    test_game_loop.tick_once();
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(
                        &node_tree,
                        enter_effect().from_translate_x(),
                    );

                    // run until end of enter effect
                    MockClock::advance(enter_effect().duration().to_owned());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_skeleton_is_shown(&node_tree);

                    // `set_content_ready` is never called
                    // skeleton should dismiss after 5 seconds
                    MockClock::advance(SKELETON_TIMEOUT_DURATION);
                    test_game_loop.tick_until_done();

                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);
                },
            )
        }

        fn exit_effect() -> ExitEffect {
            ExitEffect::FadeToStart {
                duration: Duration::from_millis(300),
                fade_media_background: true,
            }
        }

        fn enter_effect() -> EnterEffect {
            EnterEffect::FadeFromEnd {
                duration: Duration::from_millis(200),
            }
        }
    }

    mod back_press {
        use super::*;
        use crate::ui::onboarding_page_ui::ONBOARDING_PAGE;
        use ignx_compositron::input::KeyEventType;
        use ignx_compositron::time::MockClock;

        #[rstest]
        #[serial]
        fn should_trigger_app_exit_modal_via_context_signal_when_back_pressed_on_error(
            #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
        ) {
            launch_test(
                |ctx| {
                    let back_press_signal = create_rw_signal(ctx.scope(), false);
                    provide_context(ctx.scope(), back_press_signal);

                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::Error))
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    // load page
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_error_is_shown(&node_tree);

                    // back press
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, button);
                    test_game_loop.tick_until_done();

                    let back_press_signal = use_context::<RwSignal<bool>>(scope).unwrap();
                    assert!(back_press_signal.get_untracked());
                },
            )
        }

        #[rstest]
        #[serial]
        fn should_trigger_app_exit_modal_via_context_signal_when_back_pressed_on_loading(
            #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
        ) {
            launch_test(
                |ctx| {
                    let back_press_signal = create_rw_signal(ctx.scope(), false);
                    provide_context(ctx.scope(), back_press_signal);

                    TestSetup::default().setup(ctx.scope());

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    // load page
                    let node_tree = test_game_loop.tick_until_done();
                    assert_skeleton_is_shown(&node_tree);

                    // back press
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, button);
                    test_game_loop.tick_until_done();

                    let back_press_signal = use_context::<RwSignal<bool>>(scope).unwrap();
                    assert!(back_press_signal.get_untracked());
                },
            )
        }

        #[rstest]
        #[serial]
        fn should_trigger_app_exit_modal_via_context_signal_when_back_pressed_on_loaded_if_skeleton_visible(
            #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
        ) {
            let exit_effect = || ExitEffect::FadeToStart {
                duration: Duration::from_millis(300),
                fade_media_background: true,
            };
            let enter_effect = || EnterEffect::FadeFromEnd {
                duration: Duration::from_millis(200),
            };
            launch_test(
                move |ctx| {
                    let back_press_signal = create_rw_signal(ctx.scope(), false);

                    provide_context(ctx.scope(), back_press_signal);

                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                move |scope, mut test_game_loop| {
                    // tick twice to set skeleton translate x but not the enter effect
                    // otherwise the effect will finish in the next tick
                    test_game_loop.tick_once();
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(
                        &node_tree,
                        enter_effect().from_translate_x(),
                    );

                    // run until end of enter effect
                    MockClock::advance(enter_effect().duration().to_owned());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_skeleton_is_shown(&node_tree);

                    // back press
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, button);
                    test_game_loop.tick_until_done();

                    let back_press_signal = use_context::<RwSignal<bool>>(scope).unwrap();
                    assert!(back_press_signal.get_untracked());
                },
            )
        }

        #[rstest]
        #[serial]
        fn should_not_trigger_app_exit_modal_via_context_signal_when_back_pressed_on_loaded_if_skeleton_not_visible(
            #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
        ) {
            let exit_effect = || ExitEffect::FadeToStart {
                duration: Duration::from_millis(300),
                fade_media_background: true,
            };
            let enter_effect = || EnterEffect::FadeFromEnd {
                duration: Duration::from_millis(200),
            };
            launch_test(
                move |ctx| {
                    let back_press_signal = create_rw_signal(ctx.scope(), false);
                    provide_context(ctx.scope(), back_press_signal);

                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                        .with_exit_effect(exit_effect())
                        .with_enter_effect(enter_effect())
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                move |scope, mut test_game_loop| {
                    // tick twice to set skeleton translate x but not the enter effect
                    // otherwise the effect will finish in the next tick
                    test_game_loop.tick_once();
                    let node_tree = test_game_loop.tick_once().node_tree;
                    assert_skeleton_is_shown_with_translate_x(
                        &node_tree,
                        enter_effect().from_translate_x(),
                    );

                    // run until end of enter effect
                    MockClock::advance(enter_effect().duration().to_owned());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_skeleton_is_shown(&node_tree);

                    // `set_content_ready` is never called
                    // skeleton should dismiss after 5 seconds
                    MockClock::advance(SKELETON_TIMEOUT_DURATION);
                    test_game_loop.tick_until_done();

                    // end of skeleton fade out
                    MockClock::advance(FableMotionDuration::Standard.to_duration());
                    let node_tree = test_game_loop.tick_until_done();
                    assert_collections_page_is_shown(&node_tree);

                    // back press
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, button);
                    test_game_loop.tick_until_done();

                    let back_press_signal = use_context::<RwSignal<bool>>(scope).unwrap();
                    assert!(!back_press_signal.get_untracked());
                },
            )
        }

        #[rstest]
        #[serial]
        fn should_not_trigger_app_exit_modal_via_context_signal_when_back_pressed_while_restoring_focus(
            #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
        ) {
            launch_test(
                move |ctx| {
                    let back_press_signal = create_rw_signal(ctx.scope(), false);
                    provide_context(ctx.scope(), back_press_signal);

                    let restore_client_params = RestoreClientParams {
                        response: TestClientResponse::None,
                        ..RestoreClientParams::default()
                    };
                    TestSetup::default()
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .with_location(TestLocation::HomeWithBack)
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "home-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: Some(CachedFocusData {
                                container_id: "containerToRestore".to_string(),
                                item_id: "itemToRestore".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                move |scope, mut test_game_loop| {
                    // tick twice to set skeleton translate x but not the enter effect
                    // otherwise the effect will finish in the next tick
                    test_game_loop.tick_once();

                    // back press
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, button);
                    test_game_loop.tick_until_done();

                    let back_press_signal = use_context::<RwSignal<bool>>(scope).unwrap();
                    assert!(!back_press_signal.get_untracked());
                },
            )
        }

        #[rstest]
        #[serial]
        fn should_reload_home_from_onboarding_speedbump(
            #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
        ) {
            launch_test(
                |ctx| {
                    provide_ols_context(&ctx, false);

                    TestSetup::default()
                        .with_location(TestLocation::Home)
                        .with_client(TestClient::Onboarding(true, true))
                        /*
                            asserts second call to home:home resulting from setting should_reload to true
                        */
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |_, mut test_game_loop| {
                    let mut tree = test_game_loop.tick_until_done();
                    assert_node_exists!(tree.find_by_test_id(ONBOARDING_PAGE));
                    assert_node_does_not_exist!(tree.find_by_test_id("collections-page"));

                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, button);
                    tree = test_game_loop.tick_until_done();

                    assert_node_exists!(tree.find_by_test_id("collections-page"));
                    assert_node_does_not_exist!(tree.find_by_test_id(ONBOARDING_PAGE));
                },
            )
        }

        #[rstest]
        #[serial]
        fn should_navigate_to_home_from_onboarding_hero_ingress(
            #[values(KeyCode::Backspace, KeyCode::Escape)] button: KeyCode,
        ) {
            launch_test(
                |ctx| {
                    provide_ols_context(&ctx, false);

                    TestSetup::default()
                        .with_location(TestLocation::OnboardingHeroIngress)
                        .with_client(TestClient::Onboarding(false, false))
                        .with_expected_navigation(
                            PageType::Rust(RUST_COLLECTIONS),
                            "ONBOARDING_PAGE",
                        )
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |_, mut test_game_loop| {
                    let mut tree = test_game_loop.tick_until_done();
                    assert_node_exists!(tree.find_by_test_id(ONBOARDING_PAGE));
                    assert_node_does_not_exist!(tree.find_by_test_id("collections-page"));

                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, button);
                    tree = test_game_loop.tick_until_done();
                },
            )
        }
    }

    mod reporting {
        use super::*;

        #[test]
        #[serial]
        fn should_report_begin_event_before_initial_load_call() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                        .with_location(TestLocation::Home)
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_begin_event("home", "home", Some("token"))
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    // The Begin event should be reported during the initial page load setup
                    let _ = test_game_loop.tick_until_done();
                },
            );
        }

        #[test]
        #[serial]
        fn should_report_begin_event_before_attempting_cache_retrieval_on_back() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::HomeWithBack)
                        .with_client(TestClient::None)
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_begin_event("home", "home", Some("token"))
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "home-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: Some(get_mock_collections_page(ctx.clone()).into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    // The Begin event should be reported before attempting cache retrieval
                    let _ = test_game_loop.tick_until_done();
                },
            );
        }

        #[test]
        #[serial]
        fn should_report_focus_restoration_events_when_data_retrieved_from_cache() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::HomeWithBack)
                        .with_client(TestClient::None)
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_focus_restoration(false, 0) // transform: false, v3_treatment: 0
                                .expect_focus_restoration_accuracy(true, "V2", "client")
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "home-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: Some(get_mock_collections_page(ctx.clone()).into()),
                            focus: Some(CachedFocusData {
                                container_id: "container_id".to_string(),
                                item_id: "item_id".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    // Focus restoration events should be reported when loading from cache
                    let _ = test_game_loop.tick_until_done();
                },
            );
        }

        #[test]
        #[serial]
        fn should_report_focus_restoration_event_when_attempting_transform_call() {
            launch_test(
                |ctx| {
                    let restore_client_params = RestoreClientParams {
                        response: TestClientResponse::SuccessDefault,
                        ..RestoreClientParams::default()
                    };

                    TestSetup::default()
                        .with_location(TestLocation::HomeWithBack)
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_focus_restoration(true, 0) // transform: true, v3_treatment: 0
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "home-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None, // Data is invalidated, but focus exists
                            focus: Some(CachedFocusData {
                                container_id: "containerToRestore".to_string(),
                                item_id: "itemToRestore".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 0,
                                    item: 0,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    // Focus restoration event should be reported when making transform call
                    let _ = test_game_loop.tick_until_done();
                },
            );
        }

        #[test]
        #[serial]
        fn should_report_focus_restoration_accuracy_event_when_focus_restoration_transform_restores_correctly(
        ) {
            launch_test(
                |ctx| {
                    let mut mock_response = get_mock_page_response();
                    mock_response.matchFound =
                        WithResiliency::Ok(NetworkOptional::Some(NetworkBool::Item(true)));

                    let restore_client_params = RestoreClientParams {
                        container_id: "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy",
                        item_id: "amzn1.dv.gti.17b73916-9ce2-47be-bb33-b211c4f92ff2",
                        response: TestClientResponse::SuccessResponse(mock_response),
                        ..RestoreClientParams::default()
                    };

                    TestSetup::default()
                        .with_location(TestLocation::TVWithBack)
                        .with_client(TestClient::RestoreClient(restore_client_params))
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_focus_restoration_accuracy(true, "V2", "transform") // matches: true
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
                        provide_cache(&ctx);

                    cache.borrow_mut().put(
                        "tv-home-Some(\"token\")".to_string(),
                        CachedCollectionsPage {
                            data: None,
                            focus: Some(CachedFocusData {
                                container_id: "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy".to_string(),
                                item_id: "amzn1.dv.gti.17b73916-9ce2-47be-bb33-b211c4f92ff2".to_string(),
                                should_skip_pagination: false,
                                position: FocusPosition {
                                    container: 1,
                                    item: 2,
                                    sub_index: None,
                                },
                            }),
                            is_resiliency_enabled: Some(false),
                        },
                    );

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    // Focus restoration accuracy event should be reported when transform succeeds
                    let _ = test_game_loop.tick_until_done();
                },
            );
        }

        #[test]
        #[serial]
        fn should_report_page_load_success_when_ui_rendered() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::SuccessDefault))
                        .with_location(TestLocation::Home)
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_page_load_success(Some(("home", "home", Some("token"))))
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |scope, mut test_game_loop| {
                    let _ = test_game_loop.tick_until_done();
                    set_content_ready(scope);
                    let node_tree = test_game_loop.tick_until_done();

                    // Verify the page is actually rendered
                    assert_collections_page_is_shown(&node_tree);
                },
            );
        }

        #[test]
        #[serial]
        fn should_report_page_load_success_when_empty_my_stuff() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_location(TestLocation::MyStuff)
                        .with_client(TestClient::Default(TestClientResponse::SuccessResponse(
                            get_unpopulated_my_stuff_collections_page(),
                        )))
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_page_load_success(Some(("MyStuff", "home", Some("token"))))
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    // Verify the empty my stuff page is shown
                    let empty_my_stuff = node_tree.find_by_test_id("empty-my-stuff-collections");
                    assert_node_exists!(&empty_my_stuff);
                },
            );
        }

        #[test]
        #[serial]
        fn should_report_page_load_failure_when_error_modal_shown() {
            launch_test(
                |ctx| {
                    TestSetup::default()
                        .with_client(TestClient::Default(TestClientResponse::Error))
                        .with_location(TestLocation::Home)
                        .with_reporting_expectations(
                            ReportingExpectations::default()
                                .expect_begin_event("home", "home", Some("token"))
                                .expect_page_load_failure("home", "home", Some("token"))
                                .allow_other_events(),
                        )
                        .setup(ctx.scope());

                    setup_page(&ctx)
                },
                |_scope, mut test_game_loop| {
                    let node_tree = test_game_loop.tick_until_done();

                    // Verify the error modal is shown
                    assert_collections_page_error_is_shown(&node_tree);
                },
            );
        }
    }

    fn provide_cache(ctx: &AppContext) -> ExpirableLruCacheRc<String, CachedCollectionsPage> {
        let cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
            create_cache(ctx.scope(), None);
        provide_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(
            ctx.scope(),
            cache.clone(),
        );
        cache
    }

    fn mock_client(
        expected_page_id: String,
        expected_page_type: String,
        expected_presentation_scheme: &'static str,
        expected_taps_roles: Vec<String>,
        expected_additional_dynamic_features: Vec<DynamicFeature>,
        return_response: TestClientResponse,
    ) -> Context {
        let client_context = MockNetworkClient::new_context();
        client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();

            let expected_taps_roles = expected_taps_roles.clone();
            let expected_additional_dynamic_features = expected_additional_dynamic_features.clone();
            let expected_page_id = expected_page_id.clone();
            let expected_page_type = expected_page_type.clone();
            let return_response = return_response.clone();
            mock_client
                .expect_collection_initial()
                .withf(move |_, _, _, _, _, request, _, _, _, _| {
                    let expected_additional_dynamic_features_set =
                        expected_additional_dynamic_features
                            .iter()
                            .collect::<HashSet<_>>();
                    let dynamic_feature_set = request
                        .container_request_base
                        .additional_dynamic_features
                        .iter()
                        .collect::<HashSet<_>>();
                    request.container_request_base.page_id == expected_page_id
                        && request.container_request_base.page_type == expected_page_type
                        && request.container_request_base.presentation_scheme
                            == *expected_presentation_scheme
                        && request.container_request_base.taps_roles == expected_taps_roles
                        && dynamic_feature_set == expected_additional_dynamic_features_set
                })
                .returning(
                    move |success_cb, err_cb, _, _, _, _, _, _, _, _| match &return_response {
                        TestClientResponse::SuccessResponse(r) => {
                            success_cb(r.clone(), Instant::now())
                        }
                        TestClientResponse::SuccessDefault => {
                            success_cb(get_populated_collections_page(), Instant::now())
                        }
                        TestClientResponse::Error => {
                            let error = Http {
                                code: 500,
                                body: Some("error".to_string()),
                                headers: vec![],
                            };
                            err_cb(error);
                        }
                        TestClientResponse::None => {}
                    },
                );
            mock_client.expect_restore_collection_page_focus().never();
            mock_client.expect_get_collection_page_sports_edge().never();
            mock_client
        });

        client_context
    }

    fn mock_restore_client(
        expected_page_id: String,
        expected_page_type: String,
        params: RestoreClientParams,
    ) -> Context {
        let client_context = MockNetworkClient::new_context();
        client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();
            let expected_taps_roles = params.roles.clone();
            let expected_presentation_scheme = params.presentation_scheme;
            let container_id = params.container_id;
            let item_id = params.item_id;
            let skip_pagination = params.skip_pagination.clone();
            let expected_additional_dynamic_features = params.dynamic_features.clone();
            let expected_page_id = expected_page_id.clone();
            let expected_page_type = expected_page_type.clone();
            mock_client
                .expect_restore_collection_page_focus()
                .withf(move |_, _, _, _, request| {
                    let dynamic_feature_set = request
                        .collection_initial_base
                        .container_request_base
                        .additional_dynamic_features
                        .iter()
                        .collect::<HashSet<_>>();
                    let expected_dynamic_feature_set = expected_additional_dynamic_features
                        .iter()
                        .collect::<HashSet<_>>();
                    request
                        .collection_initial_base
                        .container_request_base
                        .page_id
                        == expected_page_id
                        && request
                            .collection_initial_base
                            .container_request_base
                            .page_type
                            == expected_page_type
                        && request
                            .collection_initial_base
                            .container_request_base
                            .taps_roles
                            == expected_taps_roles
                        && request
                            .collection_initial_base
                            .container_request_base
                            .presentation_scheme
                            == expected_presentation_scheme
                        && dynamic_feature_set == expected_dynamic_feature_set
                        && request.container_id == container_id
                        && request.transform_item_id == item_id
                        && request.skip_pagination == skip_pagination
                })
                .return_once({
                    let response = params.response.clone();
                    move |success_callback, err_cb, _, _, _| match response {
                        TestClientResponse::None => {}
                        TestClientResponse::SuccessResponse(r) => {
                            success_callback(r, Instant::now())
                        }
                        TestClientResponse::SuccessDefault => {
                            success_callback(get_populated_collections_page(), Instant::now())
                        }
                        TestClientResponse::Error => {
                            let error = Http {
                                code: 500,
                                body: Some("error".to_string()),
                                headers: vec![],
                            };
                            err_cb(error);
                        }
                    }
                });
            mock_client.expect_collection_initial().never();
            mock_client.expect_get_collection_page_sports_edge().never();
            mock_client
        });

        client_context
    }

    fn mock_client_with_multi_success_response(
        expected_params: Vec<(&'static str, &'static str)>,
    ) -> Context {
        let client_context = MockNetworkClient::new_context();
        let mut seq = Sequence::new();

        client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();

            for (expected_page_id, expected_page_type) in &expected_params {
                let expected_page_id = expected_page_id.clone();
                let expected_page_type = expected_page_type.clone();
                mock_client
                    .expect_collection_initial()
                    .once()
                    .in_sequence(&mut seq)
                    .withf(move |_, _, _, _, _, request, _, _, _, _| {
                        request.container_request_base.page_id == *expected_page_id
                            && request.container_request_base.page_type == *expected_page_type
                    })
                    .returning(move |success_cb, _, _, _, _, _, _, _, _, _| {
                        success_cb(get_populated_collections_page(), Instant::now())
                    });
            }
            mock_client
        });

        client_context
    }

    fn mock_client_with_resiliency_action(
        expected_beekeeper_params: BeekeeperFallbackPageParams,
        expected_redirect_param: Option<ResiliencyPage>,
        action_payload: StorefrontFallbackPageFulfilledAction<CollectionsPage>,
    ) -> Context {
        let client_context = MockNetworkClient::new_context();
        let mut seq = Sequence::new();

        client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();
            let expected_beekeeper_params = expected_beekeeper_params.clone();
            let expected_redirect_param = expected_redirect_param.clone();
            let action_payload = action_payload.clone();
            mock_client
                .expect_collection_initial()
                .once()
                .in_sequence(&mut seq)
                .withf(move |_, _, _, _, _, _, _, beekeeper_params, redirect, _| {
                    expected_beekeeper_params == *beekeeper_params
                        && expected_redirect_param == *redirect
                })
                .returning(move |_, _, resiliency_cb, _, _, _, _, _, _, _| {
                    let action_payload = action_payload.clone();
                    resiliency_cb(action_payload);
                });

            mock_client
        });

        client_context
    }

    fn mock_client_for_sports_edge(
        expected_page_id: String,
        expected_page_type: String,
        expected_presentation_scheme: &'static str,
        expected_taps_roles: Vec<String>,
        expected_additional_dynamic_features: Vec<DynamicFeature>,
        return_response: TestClientResponse,
    ) -> Context {
        let client_context = MockNetworkClient::new_context();
        client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();

            let expected_taps_roles = expected_taps_roles.clone();
            let expected_additional_dynamic_features = expected_additional_dynamic_features.clone();
            let expected_page_id = expected_page_id.clone();
            let expected_page_type = expected_page_type.clone();
            let return_response = return_response.clone();
            mock_client
                .expect_get_collection_page_sports_edge()
                .withf(move |_, _, _, _, _, request, _, _, _, _| {
                    let expected_additional_dynamic_features_set =
                        expected_additional_dynamic_features
                            .iter()
                            .collect::<HashSet<_>>();
                    let dynamic_feature_set = request
                        .container_request_base
                        .additional_dynamic_features
                        .iter()
                        .collect::<HashSet<_>>();
                    request.container_request_base.page_id == expected_page_id
                        && request.container_request_base.page_type == expected_page_type
                        && request.container_request_base.presentation_scheme
                            == *expected_presentation_scheme
                        && request.container_request_base.taps_roles == expected_taps_roles
                        && dynamic_feature_set == expected_additional_dynamic_features_set
                })
                .returning(
                    move |success_cb, err_cb, _, _, _, _, _, _, _, _| match &return_response {
                        TestClientResponse::SuccessResponse(r) => {
                            success_cb(r.clone(), Instant::now())
                        }
                        TestClientResponse::SuccessDefault => {
                            success_cb(get_populated_collections_page(), Instant::now())
                        }
                        TestClientResponse::Error => {
                            let error = Http {
                                code: 500,
                                body: Some("error".to_string()),
                                headers: vec![],
                            };
                            err_cb(error);
                        }
                        TestClientResponse::None => {}
                    },
                );
            mock_client.expect_restore_collection_page_focus().never();
            mock_client.expect_collection_initial().never();
            mock_client
        });

        client_context
    }

    fn get_beekeeper_fallback_collections_page() -> CollectionsPage {
        collections_response_parser(
            include_str!("../test_assets/collections_page_beekeeper_fallback_response.json")
                .to_string(),
        )
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .unwrap()
    }

    fn get_populated_collections_page() -> CollectionsPage {
        collections_response_parser(
            include_str!("../test_assets/collections_page_full_response.json").to_string(),
        )
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .unwrap()
    }

    fn get_unpopulated_my_stuff_collections_page() -> CollectionsPage {
        collections_response_parser(
            include_str!("../test_assets/collections_page_without_container_list.json").to_string(),
        )
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .unwrap()
    }

    fn get_onboarding_cx_page() -> CollectionsPage {
        collections_response_parser(
            include_str!("../test_assets/onboarding_cx_page_with_single_onboarding_container.json")
                .to_string(),
        )
        .map(|result| match result {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        })
        .unwrap()
    }

    fn mock_client_with_onboarding_page_response(
        mock_response_on_backpress: bool,
        speed_bump: bool,
    ) -> Context {
        let client_context = MockNetworkClient::new_context();
        let mut seq = Sequence::new();
        let expected_page_id = if speed_bump { "home" } else { "onboarding" };
        let expected_page_type = if speed_bump { "home" } else { "merch" };
        client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();

            mock_client
                .expect_collection_initial()
                .once()
                .in_sequence(&mut seq)
                .withf(move |_, _, _, _, _, request, _, _, _, _| {
                    request.container_request_base.page_id == *expected_page_id
                        && request.container_request_base.page_type == *expected_page_type
                })
                .returning(move |success_cb, _, _, _, _, _, _, _, _, _| {
                    success_cb(get_onboarding_cx_page(), Instant::now())
                });

            if mock_response_on_backpress {
                mock_client
                    .expect_collection_initial()
                    .once()
                    .in_sequence(&mut seq)
                    .withf(move |_, _, _, _, _, request, _, _, _, _| {
                        request.container_request_base.page_id == *expected_page_id
                            && request.container_request_base.page_type == *expected_page_type
                    })
                    .returning(move |success_cb, _, _, _, _, _, _, _, _, _| {
                        success_cb(get_populated_collections_page(), Instant::now())
                    });
            }

            mock_client
        });

        client_context
    }

    fn mock_client_no_call() -> Context {
        let client_context = MockNetworkClient::new_context();
        client_context.expect().returning(|_| {
            let mut mock_client = MockNetworkClient::default();
            mock_client.expect_collection_initial().never();
            mock_client.expect_restore_collection_page_focus().never();
            mock_client
        });

        client_context
    }

    fn title_details_request() -> TitleDetailsChangeRequest {
        TitleDetailsChangeRequest {
            data: TitleDetailsData::Empty,
            navigation_direction: NavigationDirection::NONE,
        }
    }

    fn setup_resiliency_store(scope: Scope, should_disable_top_nav_prefetch: bool) {
        let mut mock_resiliency_store = MockResiliencyStore::default();

        mock_resiliency_store.expect_clone().returning(move || {
            let mut clone = MockResiliencyStore::default();
            clone
                .expect_get_disable_top_nav_pages_prefetching()
                .return_const(should_disable_top_nav_prefetch);
            clone
        });

        provide_context::<MockResiliencyStore>(scope, mock_resiliency_store);
    }

    fn top_nav_signal(scope: Scope) -> Signal<TopNavData> {
        Signal::derive(scope, move || {
            TopNavData::new(
                mock_static_items(),
                mock_dynamic_items(),
                mock_addon_item(),
                Default::default(),
                Rc::new(|_| {}),
                true,
            )
        })
    }

    fn get_mock_page_response() -> CollectionsPage {
        match collections_response_parser(
            include_str!("../test_assets/collections_page_full_response.json").to_string(),
        )
        .unwrap()
        {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        }
    }

    fn get_mock_collections_page(ctx: AppContext) -> CollectionsPageModel {
        let scope = ctx.scope();
        let page_data = get_mock_page_response();

        let page_data_signal = create_default_collections_page_model(scope);
        let load_status_signal =
            create_rw_signal(scope, PageRequestStatus::Success("home-home".to_string()));

        let setup = SuccessCallbackTestSetup::new(ctx)
            .with_page_data_and_status(page_data_signal, load_status_signal)
            .with_parsed_page(page_data);

        let page_controller = setup.to_controller();

        let mock_page_id = "home-home".to_string();

        let success_cb = get_initial_load_success_cb(Rc::new(page_controller), 1, mock_page_id);

        let args = setup.to_success_cb_args();

        success_cb(
            args.parsed_page_from_network.clone(),
            args.transform_name,
            args.manipulate_page_data,
            args.timestamp,
        );

        page_data_signal.get_untracked()
    }

    fn setup_page(ctx: &AppContext) -> impl Composable<'static> {
        setup_page_with_existing_mb_and_td(ctx, MediaBackgroundType::None, title_details_request())
    }

    fn setup_page_with_existing_mb_and_td(
        ctx: &AppContext,
        mb: MediaBackgroundType,
        td: TitleDetailsChangeRequest,
    ) -> impl Composable<'static> {
        let media_background = create_rw_signal(ctx.scope(), mb);
        let title_details = create_rw_signal(ctx.scope(), td);
        let top_nav_data = top_nav_signal(ctx.scope());
        let modal_data = create_rw_signal(ctx.scope(), vec![]).write_only();
        let active_profile: Signal<Option<Profile>> =
            create_signal(ctx.scope(), Some(create_mock_profile()))
                .0
                .into();

        provide_context(ctx.scope(), media_background);
        provide_context(ctx.scope(), title_details);
        provide_context(
            ctx.scope(),
            TTSEnabledContext(create_signal(ctx.scope(), false).0),
        );

        let upsell_mlp_context = upsell_mlp::clear_mlp_shown_flag_if_needed_context();
        upsell_mlp_context.expect().once().return_const(());

        compose! {
            CollectionsPage(update_media_background_rw: media_background, update_title_details: title_details.write_only(), top_nav_data, modal_data, active_profile)
        }
    }

    fn assert_skeleton_is_shown(node_tree: &SceneNodeTree) {
        assert_skeleton_is_shown_with_translate_x(node_tree, 0.0);
    }

    fn assert_skeleton_is_shown_with_translate_x(node_tree: &SceneNodeTree, expected_x: f32) {
        let skeleton = node_tree.find_by_test_id("collections-page-skeleton");
        assert_node_exists!(&skeleton);
        let parent = skeleton.find_parent();
        let skeleton_props = parent.borrow_props();
        assert_eq!(skeleton_props.layout.position.x, expected_x);
        assert_eq!(skeleton_props.base_styles.opacity, Some(1.0));
    }

    fn assert_collections_page_is_shown(node_tree: &SceneNodeTree) {
        assert_collections_page_is_shown_with_props(node_tree, 0.0, 1.0)
    }

    fn assert_collections_page_is_faded_away(node_tree: &SceneNodeTree, expected_x: f32) {
        assert_collections_page_is_shown_with_props(node_tree, expected_x, 0.0);
    }

    fn assert_collections_page_is_shown_with_props(
        node_tree: &SceneNodeTree,
        expected_x: f32,
        expected_opacity: f32,
    ) {
        let skeleton = node_tree.find_by_test_id("collections-page-skeleton");
        assert_node_does_not_exist!(&skeleton);

        let collections_page = node_tree.find_by_test_id("collections-page");
        assert_node_exists!(&collections_page);
        let parent = collections_page.find_parent();
        let collections_page_props = parent.borrow_props();
        assert_eq!(collections_page_props.layout.position.x, expected_x);
        assert_eq!(
            collections_page_props.base_styles.opacity,
            Some(expected_opacity)
        );
    }

    fn assert_collections_page_is_fading_in(node_tree: &SceneNodeTree) {
        let skeleton = node_tree.find_by_test_id("collections-page-skeleton");
        assert_node_exists!(&skeleton);
        let parent = skeleton.find_parent();
        let skeleton_props = parent.borrow_props();
        assert_eq!(skeleton_props.layout.position.x, 0.0);
        assert_eq!(skeleton_props.base_styles.opacity, Some(1.0));

        let collections_page = node_tree.find_by_test_id("collections-page");
        assert_node_exists!(&collections_page);
        let parent = collections_page.find_parent();
        let collections_page_props = parent.borrow_props();
        assert_eq!(collections_page_props.layout.position.x, 0.0);
        assert_eq!(collections_page_props.base_styles.opacity, Some(0.0));
    }

    fn assert_collections_page_error_is_shown(node_tree: &SceneNodeTree) {
        let error = node_tree.find_by_test_id(COLLECTIONS_PAGE_ERROR_MODAL_TEST_ID);
        assert_node_exists!(&error);

        let button = error
            .find_any_child_with()
            .test_id(PRIMARY_BUTTON_TEST_ID)
            .find_first();
        assert_node_exists!(&button);
        assert!(&button.borrow_props().is_focused);
    }

    fn set_content_ready(scope: Scope) {
        let test_context: TestContext = use_context(scope).unwrap();
        (test_context.set_content_ready)();
    }

    fn create_mock_profile() -> Profile {
        Profile {
            id: "profile id".to_string(),
            avatar: ProfileAvatar {
                avatarId: "avatar id".to_string(),
                avatarUrl: "avatar url".to_string(),
                avatarDescription: None,
            },
            name: "jeremy bearimy".to_string(),
            isActive: false,
            isAdult: false,
            profileIsImplicit: false,
            translationDetails: None,
            permissions: None,
        }
    }
}
