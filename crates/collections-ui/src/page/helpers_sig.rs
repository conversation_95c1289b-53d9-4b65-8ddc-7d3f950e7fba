use crate::network::dynamic_params::CollectionsDynamicNetworkParameters;
use crate::network::types::RestoreCollectionPageFocusRequest;
use crate::page::controller::CollectionsPageControlling;
use crate::parser::title_details::{
    parse_metadata_event, parse_see_more_link_card_item, parse_standard_carousel_item,
    parse_vod_metadata_series, parse_vod_metadata_show,
};
use crate::reporting::app_events::CollectionsAppEvents;
#[cfg(test)]
use crate::test_assets::mocks::mock_display as display;
#[cfg(any(test, feature = "example_data"))]
use crate::test_assets::mocks::MockNetworkClient as NetworkClient;
use crate::types::collections_types::{CollectionsPageModel, StartColumn};
use crate::ui::traits::UpdatableItems;
use crate::ui::types::{ContainerType, ToContainerType};
use crate::utils::freshness_metric::FreshnessAction;
#[double]
use crate::utils::metric_emitter::MetricEmitter;
use crate::{
    network::types::CollectionInitialRequest, parser::title_details::parse_charts_carousel_item,
};
use beekeeper::types::ResiliencyPage;
use chrono::Utc;
use clickstream::events::cards::CardClickedEvent;
use collection_types::network_types::CollectionsPage;
use common_transform_types::actions::{Action, TransitionAction};
use common_transform_types::containers::{
    BeardSupportedCarouselDeserialization, CommonCarouselTitleCardItem, ComponentItems, Container,
    ContainerWithResiliency, DisplayPlacement, EntityCarouselDeserialization, NodesDeserialization,
    PaginatableComponent, PaginationLink, ScheduleCarouselDeserialization, ScheduleCarouselItem,
    SpecialCarouselContainerDeserialization, SpecialCarouselItem, StandardCarouselDeserialization,
    StandardCarouselItem, StandardHeroItem, SuperCarouselDeserialization, SuperCarouselItem,
    TentpoleHeroItem,
};
use common_transform_types::containers::{ChartsCarouselDeserialization, ChartsCarouselItem};
use common_transform_types::impressions::get_carousel_analytics;
use common_transform_types::modals::MarketingModalData;
use common_transform_types::page_metadata::PageMetadata;
use common_transform_types::resiliency::WithResiliency;
use container_item_types::linear_utils::updatable_ui::{
    UpdatableLinearAiringData, UpdatableLinearHeroAiringData,
};
use container_item_types::media_background_parsing::collections::{
    generate_charts_carousel_media_background_data,
    generate_schedule_carousel_media_background_data, generate_see_more_link_media_background_data,
    generate_standard_carousel_media_background_data, generate_standard_hero_media_background_data,
    generate_super_carousel_media_background_data, generate_tentpole_hero_media_background_data,
};
use container_item_types::title_details_parsing::standard_carousel::common_carousel_title_card::{
    parse_common_vod_metadata, parse_vod_metadata_movies,
};
use container_item_types::title_details_parsing::standard_carousel::parse_schedule_carousel_item;
use container_item_types::title_details_parsing::utils::entitlement::parse_entitlement_label_data;
use container_types::container_item_parsing::hero_item::hero_linear_station_card;
use container_types::container_item_parsing::parsing_options::{
    ContainerItemParsingOptions, GetParsingOptions,
};
use container_types::container_item_parsing::special_carousel_item::{
    special_carousel_channel_card, special_carousel_details, special_carousel_title_card,
};
use container_types::container_item_parsing::standard_card::{
    standard_carousel_event_card, standard_carousel_see_more_link, standard_carousel_title_card,
};
use container_types::container_parsing::TransformModelContainerUIParse;
#[cfg(all(not(test), not(feature = "example_data")))]
use container_types::network_traits::ContainerRequests;
use container_types::ui_signals::{
    standard_card_container_item_model_for_hint, CommonCarouselCardMetadata,
    ScheduleCardGroupContext, ScheduleCardItemContent, ScheduleCardItemType,
    ScheduleCarouselButtonItem, ScheduleCarouselButtonItemContent, ScheduleCarouselButtonUIData,
    ScheduleCarouselCardItem, ScheduleCarouselMetadata, ScheduleCarouselModel,
    ScheduleCarouselUIItem, SpecialCollectionsCarouselItemTypeWrapper,
    SpecialCollectionsCarouselMetadata, SpecialCollectionsCarouselModel,
    StandardCardContainerItemType, UpdatableContainerItemType,
};
use container_types::{
    container_item_parsing::{
        charts_card::{
            charts_carousel_event_card, charts_carousel_generic_card, charts_carousel_movie_card,
            charts_carousel_series_card, charts_carousel_show_card,
        },
        entity_carousel_item::entity_carousel_item_to_model,
        get_see_more_link_contextual_menu_metadata,
        hero_item::{hero_channel_card, hero_hero_card, hero_link_card},
        short_carousel_item::short_carousel_item_to_model,
        standard_card::{
            standard_carousel_bonus_schedule_card, standard_carousel_linear_card,
            standard_carousel_linear_station_card, standard_carousel_link_card,
            standard_carousel_vod_extra_content,
        },
        super_carousel_item::*,
        CarouselItem,
    },
    network::{ContainerRequestBase, PaginationResponse},
    ui_signals::{
        BeardSupportedCarouselMetadata, BeardSupportedCarouselModel, CardGridModel, CarouselTitle,
        ChartsCarouselItemTypeWrapper, ChartsCarouselMetadata, ChartsCarouselModel,
        CommonCarouselMetadata, ContainerModel, DiscoveryAssistantModel, EntityCarouselModel,
        HeroItemModel, NodesCarouselModel, PaginationPendingSignal, PaginationSignals,
        PromoBannerModel, ShortCarouselItemModel, ShortCarouselModel,
        StandardCardContainerItemListType, StandardCarouselMetadata, StandardCarouselModel,
        StandardHeroModel, SuperCarouselItemTypeWrapper, SuperCarouselMetadata, SuperCarouselModel,
        TentpoleHeroModel,
    },
};
use contextual_menu_types::prelude::ContextualMenuMetadata;
use cross_app_events::ImpressionData;
use cross_benefit_discovery::generate_xbd_modal;
use educational_cx::generate_edcx_modal;
use ignx_compositron::context::AppContext;
#[cfg(not(test))]
use ignx_compositron::display;
use ignx_compositron::id::Id;
use ignx_compositron::metrics::metric;
use ignx_compositron::reactive::*;
#[cfg(not(test))]
use ignx_compositron::time::Instant;
use liveliness_types::Liveliness;
use location::{Location, PageType, RustPage};
use media_background::types::{
    MediaBackgroundType, MediaStrategy, SpecialCollectionsBackgroundData,
};
#[cfg(test)]
use mock_instant::Instant;
use mockall_double::double;
use modal_manager::modal_manager::AppModal;
use navigation_menu::utils::CollectionsPageParams;
use network::common::lrc_edge_constants::DynamicNetworkParameters;
use network::common::PageRequestStatus;
#[cfg(all(not(test), not(feature = "example_data")))]
use network::NetworkClient;
use network::RequestError;
use payment_risk_message::generate_payment_risk_message_modal;
use payment_risk_message::PaymentRiskContext;
use router::hooks::try_use_location;
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use rust_features::WeblabTreatmentString;
use serde_json::Value;
use std::collections::HashSet;
use std::rc::Rc;
use title_details::types::common::{
    BadgeLayouts, BadgeOptions, MetadataOptions, StarRatingOptions,
};
use uuid::Uuid;

pub(crate) const EMPTY_CONTAINER_LIST_ERROR: &str = "Empty container list";
const RECYCLING_ITEM_LIMIT: u32 = 200;

// C -> Uncapped standard carousel
// T1 -> Standard carousel capped at 45 items, infinite* scrolling by re-cycling earlier items
// T2 -> Standard carousel capped at 60 items, infinite* scrolling by re-cycling earlier items
// T3 -> Standard carousel capped at 45 items, hint at the end of the list, pressing right scrolls to beginning
// T4 -> Standard carousel capped at 60 items, hint at the end of the list, pressing right scrolls to beginning
// * - hard capped at RECYCLING_ITEM_LIMIT for performance
fn get_east_west_carousel_truncation_treatment(scope: Scope) -> Option<WeblabTreatmentString> {
    let features = try_use_rust_features(scope);
    features.map(|f| f.get_east_west_carousel_truncation_treatment_string())
}

fn is_single_item_edcx_modal_allowed(scope: Scope) -> bool {
    try_use_rust_features(scope)
        .is_some_and(|f| f.get_home_region_setting_treatment_string() == WeblabTreatmentString::T2)
}

pub(crate) fn get_standard_carousel_cap(scope: Scope) -> Option<u32> {
    let treatment = get_east_west_carousel_truncation_treatment(scope);

    #[cfg(not(test))]
    match treatment {
        Some(WeblabTreatmentString::T1 | WeblabTreatmentString::T3) => Some(45),
        Some(WeblabTreatmentString::T2 | WeblabTreatmentString::T4) => Some(60),
        _ => None,
    }

    #[cfg(test)]
    match treatment {
        Some(WeblabTreatmentString::T1 | WeblabTreatmentString::T3) => Some(10),
        Some(WeblabTreatmentString::T2 | WeblabTreatmentString::T4) => Some(15),
        _ => None,
    }
}

pub(crate) fn create_default_collections_page_model(
    scope: Scope,
) -> RwSignal<CollectionsPageModel> {
    create_rw_signal(
        scope,
        CollectionsPageModel {
            container_list: create_rw_signal(scope, vec![]),
            pagination_link: create_rw_signal(scope, None),
            page_title: create_rw_signal(scope, None),
            page_logo_image: create_rw_signal(scope, None),
            pagination_pending: create_rw_signal(scope, false),
            unique_id: create_rw_signal(scope, "".to_string()),
            start_column: create_rw_signal(scope, None),
        },
    )
}

pub(crate) fn create_default_home_page_request(
    page_id: String,
    page_type: String,
    service_token: Option<String>,
    scope: Scope,
) -> CollectionInitialRequest {
    let DynamicNetworkParameters {
        widget_scheme,
        presentation_scheme,
        taps_roles,
        dynamic_features,
        client_features,
    } = <DynamicNetworkParameters as CollectionsDynamicNetworkParameters>::get_all(scope);
    CollectionInitialRequest {
        container_request_base: ContainerRequestBase {
            page_id,
            page_type,
            widget_scheme,
            presentation_scheme,
            taps_roles,
            additional_dynamic_features: dynamic_features,
            additional_client_features: client_features,
        },
        service_token,
        is_category_page_redesign_enabled: None,
        include_voice_hints: None,
    }
}

pub(crate) fn create_restore_collection_page_focus_request(
    page_id: String,
    page_type: String,
    service_token: Option<String>,
    container_id: String,
    transform_item_id: String,
    skip_pagination: bool,
    scope: Scope,
) -> RestoreCollectionPageFocusRequest {
    RestoreCollectionPageFocusRequest {
        collection_initial_base: create_default_home_page_request(
            page_id,
            page_type,
            service_token,
            scope,
        ),
        container_id,
        transform_item_id,
        skip_pagination,
        fallback_to_container: true,
    }
}

pub(crate) fn page_params_to_identifier(params: &CollectionsPageParams) -> String {
    format!(
        "{}-{}-{:?}",
        params.page_type, params.page_id, params.service_token
    )
}

fn get_super_carousel_has_title(super_carousel_model: &SuperCarouselModel) -> bool {
    let Some(carousel_title_signal) = super_carousel_model
        .super_carousel_metadata
        .try_with_untracked(|metadata| metadata.title)
    else {
        return false;
    };
    let title_text = carousel_title_signal
        .try_get_untracked()
        .flatten()
        .and_then(|carousel_title| carousel_title.title_text);
    title_text.is_some_and(|title_text| !title_text.is_empty())
}

pub(crate) fn update_standard_container_with_recycling(
    items: &mut Vec<StandardCardContainerItemListType>,
    update_size: usize,
    item_cap: u32,
    recycling_limit: u32,
) {
    let start_count = items.len() as u32;
    if start_count >= recycling_limit {
        return;
    }
    if item_cap > start_count {
        return;
    }
    let start_index = items.len();
    for i in start_index..(start_index + update_size) {
        let new_item_index = i - item_cap as usize;
        if let Some(item) = items.get(new_item_index) {
            // The implementation of the `Id` trait must return a unique ID because it is required
            // by List composables
            items.push(item.clone_with_new_uuid())
        }
    }

    if item_cap == 0 {
        return;
    }
    // Subtracting 2 to account for initial starting state of start_count == item_cap and SeeMoreLink
    let start_cycle_count = (start_count - 2) / item_cap;
    let end_cycle_count = items.len() as u32 / item_cap;
    if end_cycle_count > start_cycle_count {
        let action_name = match start_cycle_count {
            0 => "CarouselRecycled_1",
            1 => "CarouselRecycled_2",
            2 => "CarouselRecycled_3",
            3 => "CarouselRecycled_4",
            4 => "CarouselRecycled_5",
            _ => "CarouselRecycled_Unexpected",
        };
        MetricEmitter::emit(
            "ComponentAction.Count",
            1,
            vec![
                ("pageType".to_string(), "Collection".to_string()),
                ("componentName".to_string(), "StandardCarousel".to_string()),
                ("actionName".to_string(), action_name.to_string()),
            ],
        );
    }

    if items.len() as u32 >= recycling_limit {
        MetricEmitter::emit(
            "ComponentAction.Count",
            1,
            vec![
                ("pageType".to_string(), "Collection".to_string()),
                ("componentName".to_string(), "StandardCarousel".to_string()),
                (
                    "actionName".to_string(),
                    "RecyclingLimitReached".to_string(),
                ),
            ],
        );
    }
}

pub(crate) fn update_container_with_pagination(
    component_to_update: &ContainerModel,
    pagination_response: &PaginationResponse,
    scope: Scope,
    ignore_cap: bool,
    is_rust_quickplay_v2_enabled: bool,
) {
    let new_link = &pagination_response.paginationLink;
    let new_items = &pagination_response.items;

    let parsing_options = component_to_update
        .get_item_parsing_options(try_use_location(scope).map(|l| l.get_untracked()));
    match component_to_update {
        ContainerModel::StandardCarousel(standard_carousel_signal) => {
            let ComponentItems::StandardCarousel(standard_carousel_items) = new_items else {
                return;
            };
            standard_carousel_signal.with_untracked(|standard_carousel_model| {
                let carousel_analytics = standard_carousel_model
                    .common_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.analytics.with_untracked(get_carousel_analytics)
                    });
                let mut converted_items = standard_carousel_items
                    .iter()
                    .filter_map(|item| {
                        standard_carousel_item_to_item_type_wrapper(
                            item,
                            scope,
                            carousel_analytics.clone(),
                            parsing_options,
                            is_rust_quickplay_v2_enabled,
                        )
                    })
                    .collect::<Vec<StandardCardContainerItemListType>>();

                let end_see_more_item = standard_carousel_model
                    .standard_carousel_metadata
                    .with_untracked(|metadata| {
                        match metadata
                            .see_more_link_placement
                            .try_get_untracked()
                            .unwrap_or(None)
                        {
                            Some(DisplayPlacement::End) => {
                                metadata.see_more_link.try_get_untracked().unwrap_or(None)
                            }
                            _ => None,
                        }
                    });

                if new_link.is_none() {
                    if let Some(see_more_item) = end_see_more_item {
                        converted_items.push(see_more_item);
                    }
                }

                standard_carousel_model.items.update(|items| {
                    let item_cap = if !ignore_cap {
                        get_standard_carousel_cap(scope)
                    } else {
                        None
                    };

                    if let Some(item_cap) = item_cap {
                        let amount_to_add = (item_cap as i32 - items.len() as i32).max(0) as usize;
                        converted_items.truncate(amount_to_add);
                    }

                    items.extend(converted_items);
                });

                standard_carousel_model
                    .standard_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.pagination_link.set(new_link.to_owned());
                    });
            });
        }
        ContainerModel::SuperCarousel(super_carousel_signal) => {
            let ComponentItems::SuperCarousel(super_carousel_items) = new_items else {
                return;
            };
            super_carousel_signal.with_untracked(|super_carousel_model| {
                let carousel_analytics = super_carousel_model
                    .common_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.analytics.with_untracked(get_carousel_analytics)
                    });
                let carousel_has_title = get_super_carousel_has_title(super_carousel_model);

                let converted_items = super_carousel_items
                    .iter()
                    .filter_map(|item| {
                        super_carousel_item_to_item_type_wrapper(
                            item,
                            carousel_analytics.clone(),
                            carousel_has_title,
                            scope,
                            super_carousel_model
                                .super_carousel_metadata
                                .get_untracked()
                                .expandable,
                            is_rust_quickplay_v2_enabled,
                        )
                    })
                    .collect::<Vec<SuperCarouselItemTypeWrapper>>();

                super_carousel_model.items.update(|items| {
                    items.extend(converted_items);
                });

                super_carousel_model
                    .super_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.pagination_link.set(new_link.to_owned());
                    });
            });
        }
        ContainerModel::BeardSupportedCarousel(beard_supported_carousel_signal) => {
            // Bearded Carousel shares StandardCarouselItem with StandardCarousel
            let ComponentItems::StandardCarousel(beard_supported_carousel_items) = new_items else {
                return;
            };
            beard_supported_carousel_signal.with_untracked(|beard_supported_carousel_model| {
                let carousel_analytics = beard_supported_carousel_model
                    .common_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.analytics.with_untracked(get_carousel_analytics)
                    });
                let converted_items = beard_supported_carousel_items
                    .iter()
                    .filter_map(|item| {
                        standard_carousel_item_to_item_type_wrapper(
                            item,
                            scope,
                            carousel_analytics.clone(),
                            parsing_options,
                            is_rust_quickplay_v2_enabled,
                        )
                    })
                    .collect::<Vec<StandardCardContainerItemListType>>();
                beard_supported_carousel_model.items.update(|items| {
                    items.extend(converted_items);
                });
                beard_supported_carousel_model
                    .beard_supported_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.pagination_link.set(new_link.to_owned());
                    });
            });
        }
        ContainerModel::EntityCarousel(entity_carousel_signal) => {
            let ComponentItems::EntityCarousel(entity_carousel_items) = new_items else {
                return;
            };
            entity_carousel_signal.with_untracked(|entity_carousel_model| {
                let carousel_analytics = entity_carousel_model
                    .common_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.analytics.with_untracked(get_carousel_analytics)
                    });

                let mut converted_items = entity_carousel_items
                    .iter()
                    .filter_map(|item| {
                        entity_carousel_item_to_model(item, scope, carousel_analytics.clone())
                    })
                    .collect::<Vec<_>>();

                let end_see_more_item = entity_carousel_model
                    .entity_carousel_metadata
                    .with_untracked(|metadata| {
                        match metadata
                            .see_more_link_placement
                            .try_get_untracked()
                            .unwrap_or(None)
                        {
                            Some(DisplayPlacement::End) => {
                                metadata.see_more_link.try_get_untracked().unwrap_or(None)
                            }
                            _ => None,
                        }
                    });

                if new_link.is_none() {
                    if let Some(see_more_item) = end_see_more_item {
                        converted_items.push(see_more_item);
                    }
                }

                entity_carousel_model.items.update(|items| {
                    items.extend(converted_items);
                });

                entity_carousel_model
                    .entity_carousel_metadata
                    .with_untracked(|metadata| {
                        metadata.pagination_link.set(new_link.to_owned());
                    });
            });
        }

        ContainerModel::ChartsCarousel(_) => {
            // charts carousel shouldn't paginate
        }
        ContainerModel::NodesCarousel(_) => {
            // nodes carousel shouldn't paginate
        }
        ContainerModel::ShortCarousel(_) => {
            // short carousel shouldn't paginate
        }
        ContainerModel::StandardHero(_) => {
            // standard hero shouldn't paginate
        }
        ContainerModel::TentpoleHero(_) => {
            // tentpole hero shouldn't paginate
        }
        ContainerModel::DiscoveryAssistant(_) => {
            // discovery assistant shouldn't paginate
        }
        ContainerModel::DiscoveryAssistantHeader(_) => {
            // discovery assistant header shouldn't paginate
        }
        ContainerModel::PromoBanner(_) => {
            // promo banner shouldn't paginate
        }
        ContainerModel::Grid(_) => {
            // grid shouldn't paginate
        }
        ContainerModel::Onboarding(_) => {
            // onboarding shouldn't paginate
        }
        ContainerModel::ScheduleCarousel(_) => {
            // schedule carousel shouldn't paginate
        }
        ContainerModel::SpecialCollectionsCarousel(_) => {
            // special collections carousel shouldn't paginate
        }
    };
}

pub(crate) fn update_page_with_pagination(
    page_data: &CollectionsPageModel,
    pagination_response: &PaginationResponse,
    scope: Scope,
    updatable_items: StoredValue<HashSet<UpdatableContainerItemType>>,
    is_rust_quickplay_v2_enabled: bool,
) {
    let new_link = &pagination_response.paginationLink;
    let new_items = &pagination_response.items;

    let ComponentItems::ContainerList(new_containers) = new_items else {
        return;
    };
    let result = page_data.container_list.try_update(|containers| {
        let converted_containers = new_containers
            .iter()
            .filter_map(|container| {
                container_to_container_type(container, scope, is_rust_quickplay_v2_enabled)
            })
            .inspect(|container| {
                updatable_items.update_value(|items| {
                    items.extend(
                        container
                            .model
                            .with_untracked(|model| model.get_updatable_items()),
                    );
                });
            })
            .collect::<Vec<ContainerType>>();
        containers.extend(converted_containers);
    });
    if result.is_some() {
        page_data.pagination_link.set(new_link.to_owned());
    }
}

fn update_modal_data(
    scope: Scope,
    ctx: &AppContext,
    modal_data: &WriteSignal<Vec<AppModal>>,
    page_data: &CollectionsPage,
) {
    if let Some(payment_risk_state) = use_context::<PaymentRiskContext>(scope) {
        if let Some(payment_risk_message) = payment_risk_state.borrow().get_payment_risk_message() {
            let payment_risk_message_modal_data =
                generate_payment_risk_message_modal(ctx.clone(), payment_risk_message);

            modal_data.update(|app_modals| {
                app_modals.push(AppModal::PaymentRiskMessage(
                    payment_risk_message_modal_data,
                ))
            });
        }
    }

    let marketing_modal_data: Option<MarketingModalData> =
        page_data.marketingModalData.to_owned().parse_resiliency(
            PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
            "MarketingModalData",
        );

    if let Some(ref marketing_modal_data) = marketing_modal_data {
        // Temporary mitigation disabling the EdCX modal to fix the UI shifting issue https://t.corp.amazon.com/P143322146/communication
        let display_scale = display::display_scale();
        let device_is_1080 = display_scale == 1.0;

        if let Some(edcx_modal_data) = generate_edcx_modal(ctx.clone(), marketing_modal_data) {
            log::info!("CollectionsPage: EdCX modal available");
            if device_is_1080
                || (edcx_modal_data.tiles.len() < 2 && is_single_item_edcx_modal_allowed(scope))
            {
                modal_data.update(|app_modals| app_modals.push(AppModal::EdCX(edcx_modal_data)));
            } else {
                log::warn!(
                    "CollectionsPage: Not displaying available EdCX modal as device is not 1080"
                );
            }
        } else if let Some(xbd_modal_data) = generate_xbd_modal(ctx.clone(), marketing_modal_data) {
            log::info!("CollectionsPage: XBD modal available");
            modal_data.update(|app_modals| app_modals.push(xbd_modal_data));
        }
    }

    if let Some(survey) = &page_data.surveyData {
        modal_data.update(|app_modals| app_modals.push(AppModal::Survey(survey.clone())));
    }
}

pub(crate) fn get_initial_load_success_cb(
    page_controller: Rc<dyn CollectionsPageControlling>,
    expected_request_index: usize,
    unique_page_data_id: String,
) -> impl FnOnce(CollectionsPage, &str, Option<Box<dyn FnOnce(&mut CollectionsPageModel)>>, Instant)
       + Clone {
    let signals = page_controller.signals();
    let initial_load_status = signals.initial_load_status;
    let page_data_signal = signals.page_data;
    let modal_data = signals.modal_data;
    let internal_location = signals.internal_location;
    let last_request_counter = signals.last_request_counter;
    let data_fetch_data = signals.data_fetch_data;
    let ctx = page_controller.ctx().clone();

    move |page_data: CollectionsPage,
          transform_name: &str,
          manipulate_page_data: Option<Box<dyn FnOnce(&mut CollectionsPageModel)>>,
          timestamp: Instant| {
        let scope = ctx.scope();
        if last_request_counter.try_get_value() != Some(expected_request_index) {
            return;
        }

        let location = internal_location.try_get_untracked();
        let Some(location) = location else {
            return;
        };

        /*
           No modals should be displayed on the OnboardingCX page.
        */
        let is_onboarding_page = is_onboarding_page(&page_data, &location);
        if is_onboarding_page {
            modal_data.set(Vec::new());
        } else {
            update_modal_data(scope, &ctx, &modal_data, &page_data);
        }

        let quickplay_v2_enabled = page_controller.page_features().quickplay_v2;
        let request_success_time = Instant::now();
        let updated = update_signals_from_page_data(
            page_data_signal,
            scope,
            &page_data,
            unique_page_data_id.clone(),
            transform_name,
            manipulate_page_data,
            is_onboarding_page,
            quickplay_v2_enabled,
        );

        initial_load_status.try_set(PageRequestStatus::Success(unique_page_data_id));

        match updated {
            Ok(_) => {
                let response_destructure_duration = Instant::now() - request_success_time;
                log::info!(
                    "[Parsing] Updated signals from page data in {} ms",
                    response_destructure_duration.as_millis() as u64
                );
                page_controller.report_app_event(CollectionsAppEvents::ResponseParsed {
                    time: response_destructure_duration.as_millis() as u64,
                });
            }
            Err(e) => {
                if let Some(e) = e {
                    get_initial_load_failure_cb(initial_load_status.write_only())(e);
                }
            }
        }

        data_fetch_data.set(Some((timestamp, FreshnessAction::NetworkClient)));
    }
}

fn log_request_error(request_error: &RequestError) {
    let page_source = PageType::Rust(RustPage::RUST_COLLECTIONS).to_string();
    match request_error {
        RequestError::Platform(error) => {
            log::error!("[{page_source}] {error}");
        }
        RequestError::DeserializationFailed(error) => {
            log::error!("[{page_source}] Deserialization failed without resiliency (error page will be shown): {error}");
            metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => "DeserializationFailedError");
        }
        RequestError::Builder(error) => {
            log::error!("[{page_source}] Request failed to build: {error}");
        }
        RequestError::Authorization(error) => {
            log::error!("[{page_source}] Request was not authorized: {error}");
        }
        RequestError::SerializationFailed(error) => {
            log::error!(
                "[{page_source}] Request Serialization failed (this is unexpected!): {error}"
            );
        }
        RequestError::Http { .. } => {
            // TODO: Write a test to output the {request_error}. Do not manually output _body which can potentially cause crashes if it's large.
            // See Sev2: https://t.corp.amazon.com/D160146991/communication
            log::error!("[{page_source}] {request_error}");
        }
        RequestError::ResolverFailed(error) => {
            log::error!("[{page_source}] Request failed to resolve: {error}");
        }
        RequestError::DeviceProxyError(error) => {
            log::error!("[{page_source}] Device proxy error: {error:?}");
        }
    }
}

pub(crate) fn get_initial_load_failure_cb(
    initial_load_status: WriteSignal<PageRequestStatus<String>>,
) -> impl Fn(RequestError) {
    move |request_error: RequestError| {
        log_request_error(&request_error);
        initial_load_status.try_set(PageRequestStatus::RequestError(request_error));
    }
}

pub(crate) fn get_paginate_container_success_cb(
    component_to_update: RwSignal<ContainerModel>,
    scope: Scope,
    pending_signal: PaginationPendingSignal,
    updatable_items: Option<StoredValue<HashSet<UpdatableContainerItemType>>>,
    ignore_cap: bool,
    is_rust_quickplay_v2_enabled: bool,
) -> impl FnOnce(PaginationResponse, String) {
    move |response: PaginationResponse, _: String| {
        if let Some(_scope_guard) = pending_signal.try_get_untracked() {
            component_to_update.with_untracked(|component| {
                update_container_with_pagination(
                    component,
                    &response,
                    scope,
                    ignore_cap,
                    is_rust_quickplay_v2_enabled,
                );
                // Updatable items should be updated with any new component items
                if let Some(updatable_items) = updatable_items {
                    updatable_items.update_value(|items| {
                        items.extend(component.get_updatable_items());
                    })
                }
            });
            pending_signal.set(false);
        }
    }
}

pub(crate) fn get_paginate_page_success_cb(
    page_signal: RwSignal<CollectionsPageModel>,
    scope: Scope,
    pending_signal: PaginationPendingSignal,
    updatable_items: StoredValue<HashSet<UpdatableContainerItemType>>,
    is_rust_quickplay_v2_enabled: bool,
) -> impl FnOnce(PaginationResponse, String) {
    move |response: PaginationResponse, _: String| {
        if let Some(_scope_guard) = pending_signal.try_get_untracked() {
            page_signal.with_untracked(|page| {
                update_page_with_pagination(
                    page,
                    &response,
                    scope,
                    updatable_items,
                    is_rust_quickplay_v2_enabled,
                );
            });
            pending_signal.try_set(false);
        }
    }
}

pub(crate) fn get_pagination_failure_cb(
    pending_signal: PaginationPendingSignal,
) -> impl Fn(RequestError) {
    move |request_error: RequestError| {
        log_request_error(&request_error);
        pending_signal.try_set(false);
    }
}

pub fn get_paginate_component_closure(
    client: Rc<NetworkClient>,
    scope: Scope,
    updatable_items: Option<StoredValue<HashSet<UpdatableContainerItemType>>>,
    in_top_nav_page_closure: Rc<impl Fn() -> bool>,
    is_rust_quickplay_v2_enabled: bool,
) -> impl Fn(RwSignal<ContainerModel>) {
    move |component_signal: RwSignal<ContainerModel>| {
        component_signal.try_with_untracked(|component| {
            let Some(link_signals): Option<PaginationSignals> = component.into() else {
                // in this case the component is not paginatable
                return;
            };
            let (link_signal, jic_signal, pending_signal) = link_signals;
            link_signal.with_untracked(|link| {
                let Some(link) = link else {
                    // in this case the component is paginatable but has no pagination link
                    return;
                };
                if pending_signal.get_untracked() {
                    // in this case the component is paginatable and has a pagination pending
                    return;
                }
                let success_cb = get_paginate_container_success_cb(
                    component_signal,
                    scope,
                    pending_signal,
                    updatable_items,
                    !in_top_nav_page_closure(),
                    is_rust_quickplay_v2_enabled,
                );
                let failure_cb = get_pagination_failure_cb(pending_signal);

                let pagination: Option<PaginatableComponent> = match component {
                    ContainerModel::StandardCarousel(model_signal) => {
                        let item_cap = if in_top_nav_page_closure() {
                            get_standard_carousel_cap(scope)
                        } else {
                            None
                        };
                        let model = model_signal.try_get_untracked();
                        let item_count = model.as_ref().and_then(|m| {
                            m.items.try_get_untracked().map(|items| items.len() as u32)
                        });

                        match (item_count, item_cap) {
                            (Some(count), Some(cap)) if count >= cap => {
                                match get_east_west_carousel_truncation_treatment(scope) {
                                    Some(WeblabTreatmentString::T1 | WeblabTreatmentString::T2)
                                        if count < RECYCLING_ITEM_LIMIT =>
                                    {
                                        if let Some(model) = model {
                                            model.items.try_update(|items| {
                                                update_standard_container_with_recycling(
                                                    items,
                                                    10,
                                                    cap,
                                                    RECYCLING_ITEM_LIMIT,
                                                );
                                            });
                                        }
                                    }
                                    Some(WeblabTreatmentString::T3 | WeblabTreatmentString::T4) => {
                                        let last_item_type = model.as_ref().and_then(|m| {
                                            m.items
                                                .try_get_untracked()
                                                .and_then(|items| items.last().cloned())
                                        });

                                        // Check if the hint has not already been added
                                        if last_item_type.is_some_and(|t| {
                                            t.model.try_get_untracked().is_some_and(|model| {
                                                !matches!(
                                                    model,
                                                    StandardCardContainerItemType::Hint(_)
                                                )
                                            })
                                        }) {
                                            let item_model =
                                                standard_card_container_item_model_for_hint(scope);
                                            let hint_item = StandardCardContainerItemListType {
                                                model: create_rw_signal(
                                                    scope,
                                                    StandardCardContainerItemType::Hint(item_model),
                                                ),
                                                id: "hint-id".to_string(),
                                                uuid: Uuid::new_v4(),
                                            };

                                            if let Some(model) = model {
                                                model.items.try_update(|items| {
                                                    items.push(hint_item);
                                                });
                                            }
                                        }
                                    }
                                    _ => {}
                                }
                                None
                            }
                            _ => Some(PaginatableComponent::StandardCarousel(
                                Some(link.to_owned()),
                                jic_signal.and_then(|signal| signal.get_untracked()),
                            )),
                        }
                    }
                    ContainerModel::SuperCarousel(_) => Some(PaginatableComponent::SuperCarousel(
                        Some(link.to_owned()),
                        jic_signal.and_then(|signal| signal.get_untracked()),
                    )),
                    ContainerModel::BeardSupportedCarousel(_) => {
                        Some(PaginatableComponent::BeardSupportedCarousel(
                            Some(link.to_owned()),
                            jic_signal.and_then(|signal| signal.get_untracked()),
                        ))
                    }
                    ContainerModel::EntityCarousel(_) => {
                        Some(PaginatableComponent::EntityCarousel(
                            Some(link.to_owned()),
                            jic_signal.and_then(|signal| signal.get_untracked()),
                        ))
                    }
                    ContainerModel::ChartsCarousel(_) => None,
                    ContainerModel::NodesCarousel(_) => None,
                    ContainerModel::ShortCarousel(_) => None,
                    ContainerModel::StandardHero(_) => None,
                    ContainerModel::TentpoleHero(_) => None,
                    ContainerModel::DiscoveryAssistant(_) => None,
                    ContainerModel::DiscoveryAssistantHeader(_) => None,
                    ContainerModel::PromoBanner(_) => None,
                    ContainerModel::Grid(_) => None,
                    ContainerModel::Onboarding(_) => None,
                    ContainerModel::ScheduleCarousel(_) => None,
                    ContainerModel::SpecialCollectionsCarousel(_) => None,
                };

                let Some(pagination) = pagination else {
                    return;
                };

                let dynamic_params: DynamicNetworkParameters =
                    <DynamicNetworkParameters as CollectionsDynamicNetworkParameters>::get_all(
                        scope,
                    );

                client.paginate(
                    RustPage::RUST_COLLECTIONS,
                    &pagination,
                    dynamic_params,
                    success_cb,
                    failure_cb,
                );

                pending_signal.set(true);
            });
        });
    }
}

pub(crate) fn get_paginate_page_closure(
    client: Rc<NetworkClient>,
    scope: Scope,
    updatable_items: StoredValue<HashSet<UpdatableContainerItemType>>,
    is_rust_quickplay_v2_enabled: bool,
) -> impl Fn(RwSignal<CollectionsPageModel>) {
    move |page_signal: RwSignal<CollectionsPageModel>| {
        page_signal.try_with_untracked(|page| {
            let link_signals: PaginationSignals = page.into();
            let (link_signal, jic_signal, pending_signal) = link_signals;
            link_signal.with_untracked(|link| {
                let Some(link) = link else {
                    // in this case the page has no pagination link available
                    return;
                };
                if pending_signal.get_untracked() {
                    // in this case there is a pagination pending
                    return;
                }
                let success_cb = get_paginate_page_success_cb(
                    page_signal,
                    scope,
                    pending_signal,
                    updatable_items,
                    is_rust_quickplay_v2_enabled,
                );
                let failure_cb = get_pagination_failure_cb(pending_signal);

                let pagination: PaginatableComponent = PaginatableComponent::ContainerList(
                    Some(link.to_owned()),
                    jic_signal.and_then(|signal| signal.get_untracked()),
                );

                let dynamic_params: DynamicNetworkParameters =
                    <DynamicNetworkParameters as CollectionsDynamicNetworkParameters>::get_all(
                        scope,
                    );

                client.paginate(
                    RustPage::RUST_COLLECTIONS,
                    &pagination,
                    dynamic_params,
                    success_cb,
                    failure_cb,
                );

                pending_signal.set(true);
            });
        });
    }
}

/// Extracts See More Link item from the Standard Carousel network response, if present
fn standard_carousel_see_more_link_type_wrapper(
    container: &StandardCarouselDeserialization,
    scope: Scope,
    carousel_analytics: Option<String>,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<StandardCardContainerItemListType> {
    let see_more = container.seeMore.as_ref()?;
    let container_id = container.containerMetadata.id.clone();
    let id = format!("{}_SEE_MORE_LINK", container_id);
    let see_more = see_more.parse_resiliency_into_opt(
        PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
        "SeeMore",
    )?;
    let td_data = parse_see_more_link_card_item(see_more);
    let mb_data = generate_see_more_link_media_background_data(
        see_more,
        id.clone(),
        carousel_analytics.clone(),
        is_rust_quickplay_v2_enabled,
    );
    let Ok(td_data) = td_data else {
        return None;
    };
    let cm_data = get_see_more_link_contextual_menu_metadata(see_more);

    standard_carousel_see_more_link(
        see_more,
        scope,
        td_data,
        mb_data,
        cm_data,
        carousel_analytics,
        id,
    )
}

/// Takes a Standard Carousel Item from the network response and converts it into a nested-signal
/// structure based on the UI components that will use the data.
/// There are many Content Types that can be used in the Standard Carousel, but as in React we
/// treat most of them as "Generic"
// TODO: move this to a trait in container-types for reusability
fn standard_carousel_item_to_item_type_wrapper(
    item: &StandardCarouselItem,
    scope: Scope,
    carousel_analytics: Option<String>,
    parsing_options: ContainerItemParsingOptions,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<StandardCardContainerItemListType> {
    let td_data = parse_standard_carousel_item(item);
    let mb_data = generate_standard_carousel_media_background_data(
        item,
        carousel_analytics.clone(),
        scope,
        is_rust_quickplay_v2_enabled,
    );
    let Ok(td_data) = td_data else {
        return None;
    };
    let cm_data = item.get_contextual_menu_metadata();
    match item {
        StandardCarouselItem::TITLE_CARD(item) => {
            standard_carousel_title_card(item, scope, td_data, mb_data, cm_data, carousel_analytics)
        }
        StandardCarouselItem::VOD_EXTRA_CONTENT(item) => standard_carousel_vod_extra_content(
            item,
            scope,
            td_data,
            mb_data,
            cm_data,
            carousel_analytics,
        ),
        StandardCarouselItem::LIVE_LINEAR_CARD(item) => standard_carousel_linear_card(
            item,
            scope,
            td_data,
            mb_data,
            cm_data,
            carousel_analytics,
            Utc::now(),
            item.to_updatable_linear_airing_data(
                scope,
                parsing_options,
                is_rust_quickplay_v2_enabled,
            ),
        ),
        StandardCarouselItem::LINEAR_STATION(item) => standard_carousel_linear_station_card(
            item,
            scope,
            td_data,
            mb_data,
            cm_data,
            carousel_analytics,
            Utc::now(),
            item.to_updatable_linear_airing_data(
                scope,
                parsing_options,
                is_rust_quickplay_v2_enabled,
            ),
            parsing_options,
        ),
        StandardCarouselItem::LINK_CARD(item) => {
            standard_carousel_link_card(item, scope, td_data, mb_data, cm_data, carousel_analytics)
        }
        StandardCarouselItem::BONUS_SCHEDULE_CARD(item) => standard_carousel_bonus_schedule_card(
            item,
            scope,
            td_data,
            mb_data,
            cm_data,
            carousel_analytics,
        ),
        StandardCarouselItem::HERO_CARD(_) => {
            log::error!("[collections-ui::helpers_sig] received hero card with id {:?} in standard carousel. dropping.", item.id());
            None
        }
        StandardCarouselItem::LINEAR_AIRING(_) => {
            log::error!("[collections-ui::helpers_sig] received linear airing card with id {:?} in standard carousel in unexpected context. dropping.", item.id());
            None
        }
        StandardCarouselItem::CHANNEL_CARD(_) => {
            log::error!("[collections-ui::helpers_sig] received channel card with id {:?} in standard carousel in unexpected context. dropping.", item.id());
            None
        }
    }
}

// TODO: move this to a trait in container-types for reusability
fn charts_carousel_item_to_item_type_wrapper(
    item: &ChartsCarouselItem,
    scope: Scope,
    carousel_analytics: Option<String>,
    rank: u32,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<ChartsCarouselItemTypeWrapper> {
    let td_data = parse_charts_carousel_item(item);
    let mb_data = generate_charts_carousel_media_background_data(
        item,
        carousel_analytics.clone(),
        is_rust_quickplay_v2_enabled,
    );
    let Ok(td_data) = td_data else {
        return None;
    };
    let cm_data = item.get_contextual_menu_metadata();
    match item {
        ChartsCarouselItem::TITLE_CARD(item) => match item {
            CommonCarouselTitleCardItem::SEASON(card) => charts_carousel_series_card(
                card,
                scope,
                td_data,
                mb_data,
                cm_data,
                carousel_analytics,
                rank,
            ),
            CommonCarouselTitleCardItem::SHOW(card) => charts_carousel_show_card(
                card,
                scope,
                td_data,
                mb_data,
                cm_data,
                carousel_analytics,
                rank,
            ),
            CommonCarouselTitleCardItem::MOVIE(card) => charts_carousel_movie_card(
                card,
                scope,
                td_data,
                mb_data,
                cm_data,
                carousel_analytics,
                rank,
            ),
            CommonCarouselTitleCardItem::EVENT(card)
            | CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => charts_carousel_event_card(
                card,
                scope,
                td_data,
                mb_data,
                cm_data,
                carousel_analytics,
                rank,
            ),
            CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card)
            | CommonCarouselTitleCardItem::EPISODE(card)
            | CommonCarouselTitleCardItem::SERIES(card)
            | CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card)
            | CommonCarouselTitleCardItem::SPORT(card)
            | CommonCarouselTitleCardItem::LEAGUE(card)
            | CommonCarouselTitleCardItem::TOURNAMENT(card)
            | CommonCarouselTitleCardItem::TEAM(card)
            | CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(card)
            | CommonCarouselTitleCardItem::PLAYER(card) => charts_carousel_generic_card(
                card,
                scope,
                td_data,
                mb_data,
                cm_data,
                carousel_analytics,
                rank,
            ),
        },
    }
}

// TODO: move this to a trait in container-types for reusability
fn super_carousel_item_to_item_type_wrapper(
    item: &SuperCarouselItem,
    carousel_analytics: Option<String>,
    carousel_has_title: bool,
    scope: Scope,
    is_expandable: bool,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<SuperCarouselItemTypeWrapper> {
    let cm_data = item.get_contextual_menu_metadata();
    let mb_data = generate_super_carousel_media_background_data(
        item,
        carousel_has_title,
        carousel_analytics.clone(),
        is_rust_quickplay_v2_enabled,
    );
    let td_metadata_options = MetadataOptions {
        badge_options: BadgeOptions::new(&BadgeLayouts::None).build(),
        star_rating_options: StarRatingOptions::DisplayFull,
    };
    match item {
        SuperCarouselItem::TITLE_CARD(item) => match item {
            CommonCarouselTitleCardItem::SEASON(card) => {
                let (entitlement_data, metadata) = (
                    parse_entitlement_label_data(
                        card.titleCardBaseMetadata.entitlementMessaging.as_ref(),
                    ),
                    parse_vod_metadata_series(card, &td_metadata_options),
                );
                super_carousel_series_card(
                    card,
                    cm_data,
                    carousel_analytics,
                    mb_data,
                    scope,
                    is_expandable,
                    entitlement_data,
                    metadata,
                )
            }
            CommonCarouselTitleCardItem::SHOW(card) => {
                let (entitlement_data, metadata) = (
                    parse_entitlement_label_data(
                        card.titleCardBaseMetadata.entitlementMessaging.as_ref(),
                    ),
                    parse_vod_metadata_show(card, &td_metadata_options),
                );
                super_carousel_show_card(
                    card,
                    cm_data,
                    carousel_analytics,
                    mb_data,
                    scope,
                    is_expandable,
                    entitlement_data,
                    metadata,
                )
            }
            CommonCarouselTitleCardItem::MOVIE(card) => {
                let (entitlement_data, metadata) = (
                    parse_entitlement_label_data(
                        card.titleCardBaseMetadata.entitlementMessaging.as_ref(),
                    ),
                    parse_vod_metadata_movies(card, &td_metadata_options),
                );
                super_carousel_movie_card(
                    card,
                    cm_data,
                    carousel_analytics,
                    mb_data,
                    scope,
                    is_expandable,
                    entitlement_data,
                    metadata,
                )
            }
            CommonCarouselTitleCardItem::EVENT(card)
            | CommonCarouselTitleCardItem::LIVE_EVENT_ITEM(card) => {
                let (entitlement_data, metadata) = (
                    parse_entitlement_label_data(
                        card.titleCardBaseMetadata.entitlementMessaging.as_ref(),
                    ),
                    parse_metadata_event(card),
                );
                super_carousel_event_card(
                    card,
                    cm_data,
                    carousel_analytics,
                    mb_data,
                    scope,
                    is_expandable,
                    entitlement_data,
                    metadata,
                )
            }
            CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(card)
            | CommonCarouselTitleCardItem::EPISODE(card)
            | CommonCarouselTitleCardItem::SERIES(card)
            | CommonCarouselTitleCardItem::VOD_EVENT_ITEM(card)
            | CommonCarouselTitleCardItem::SPORT(card)
            | CommonCarouselTitleCardItem::LEAGUE(card)
            | CommonCarouselTitleCardItem::TOURNAMENT(card)
            | CommonCarouselTitleCardItem::TEAM(card)
            | CommonCarouselTitleCardItem::VOD_EXTRA_CONTENT(card)
            | CommonCarouselTitleCardItem::PLAYER(card) => {
                let (entitlement_data, metadata) = (
                    parse_entitlement_label_data(
                        card.titleCardBaseMetadata.entitlementMessaging.as_ref(),
                    ),
                    parse_common_vod_metadata(&card.titleCardBaseMetadata, &td_metadata_options),
                );
                super_carousel_generic_card(
                    card,
                    cm_data,
                    carousel_analytics,
                    mb_data,
                    scope,
                    is_expandable,
                    entitlement_data,
                    metadata,
                )
            }
        },
        SuperCarouselItem::LINK_CARD(card) => {
            if is_expandable {
                None
            } else {
                super_carousel_link_card(card, cm_data, carousel_analytics, mb_data, scope)
            }
        }
    }
}

fn schedule_carousel_item_to_item_type_wrapper(
    item: &ScheduleCarouselItem,
    scope: Scope,
    carousel_analytics: Option<String>,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<(ScheduleCardItemType, String)> {
    let td_data = parse_schedule_carousel_item(item);
    let mb_data = generate_schedule_carousel_media_background_data(
        item,
        carousel_analytics.clone(),
        is_rust_quickplay_v2_enabled,
    );
    let cm_data = item.get_contextual_menu_metadata();

    let Ok(td_data) = td_data else {
        if let Err(err) = &td_data {
            log::warn!("Failed to parse schedule carousel item: {:?}", err);
        }
        return None;
    };

    let card_data = standard_carousel_event_card(
        &item.eventCard,
        scope,
        td_data,
        mb_data,
        cm_data,
        carousel_analytics,
    )?;

    let is_off_platform = item.isOffPlatformTitle.unwrap_or(false);

    let content = ScheduleCardItemContent {
        carousel_card_data: card_data.model.get_untracked().get_card_data(),
        contextual_menu_metadata: card_data.model.get_untracked().get_contexual_menu_data(),
        title_details_data: card_data.model.get_untracked().get_title_details(),
        media_background_data: card_data.model.get_untracked().get_media_background(),
        is_geo_restricted: item.isCustomerGeoRestrictedToLiveEvent,
    };

    let item_type = if is_off_platform {
        ScheduleCardItemType::OffPlatform(create_rw_signal(scope, content))
    } else {
        ScheduleCardItemType::Event(create_rw_signal(scope, content))
    };
    Some((item_type, card_data.id))
}

// TODO: move this to a trait in container-types for reusability
pub fn super_carousel_to_container_type(
    container: &SuperCarouselDeserialization,
    scope: Scope,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<ContainerType> {
    let id = &container.containerMetadata.id;
    let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);
    let not_expandable = container.notExpandable;
    let has_title = container.title.as_ref().is_some_and(|t| !t.is_empty());
    let mapped_items = container
        .items
        .iter()
        .filter_map(|sc_item| {
            let item = sc_item.parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "SuperCarousel",
            );
            item.and_then(|item| {
                super_carousel_item_to_item_type_wrapper(
                    item,
                    carousel_analytics.clone(),
                    has_title,
                    scope,
                    !not_expandable,
                    is_rust_quickplay_v2_enabled,
                )
            })
        })
        .collect::<Vec<SuperCarouselItemTypeWrapper>>();

    if mapped_items.is_empty() {
        log::warn!("Super Carousel with id {id} has no items after parsing, dropping");
        return None;
    }

    Some(ContainerType {
        id: id.to_owned(),
        model: create_rw_signal(
            scope,
            ContainerModel::SuperCarousel(create_rw_signal(
                scope,
                SuperCarouselModel {
                    common_carousel_metadata: create_rw_signal(
                        scope,
                        CommonCarouselMetadata {
                            id: id.to_owned(),
                            analytics: create_rw_signal(
                                scope,
                                container.containerMetadata.analytics.to_owned(),
                            ),
                            journey_ingress_context: create_rw_signal(
                                scope,
                                container.journeyIngressContext.to_owned(),
                            ),
                        },
                    ),
                    super_carousel_metadata: create_rw_signal(
                        scope,
                        SuperCarouselMetadata {
                            title: create_rw_signal(
                                scope,
                                Some(CarouselTitle {
                                    title_text: container.title.clone(),
                                    title_image_url: None,
                                }),
                            ),
                            pagination_link: create_rw_signal(
                                scope,
                                container.paginationLink.to_owned(),
                            ),
                            pagination_pending: create_rw_signal(scope, false),
                            expandable: !not_expandable,
                        },
                    ),
                    items: create_rw_signal(scope, mapped_items),
                },
            )),
        ),
    })
}

/// Takes a Nodes Carousel network response and converts it into a nested-signal
/// structure based on the UI components that will use the data.
// TODO: move this to a trait in container-types for reusability
fn nodes_carousel_container_to_ui_model(
    container: &NodesDeserialization,
    scope: Scope,
) -> Option<ContainerType> {
    let model = NodesCarouselModel::from_transform_model(container, scope);
    model.map(|(m, id)| m.to_container_type(id, scope))
}

/// Takes an Entity Carousel network response and converts it into a nested-signal
/// structure based on the UI components that will use the data.
fn entity_carousel_to_container_type(
    container: &EntityCarouselDeserialization,
    scope: Scope,
) -> Option<ContainerType> {
    let rust_features = try_use_rust_features(scope);
    let is_sports_favorites_enabled =
        rust_features.map_or(false, |features| features.is_sports_favorites_enabled());

    if is_sports_favorites_enabled {
        let model = EntityCarouselModel::from_transform_model(container, scope);
        model.map(|(m, id)| m.to_container_type(id, scope))
    } else {
        None
    }
}

// TODO: move this to a trait in container-types for reusability
fn standard_hero_item_to_item_model(
    item: &StandardHeroItem,
    carousel_analytics: Option<String>,
    scope: Scope,
    enable_optimise_hero_fields_on_rust_treatment: WeblabTreatmentString,
    parsing_options: ContainerItemParsingOptions,
) -> Option<HeroItemModel> {
    let mb_data = generate_standard_hero_media_background_data(
        item,
        carousel_analytics.clone(),
        scope,
        parsing_options,
    );
    match item {
        StandardHeroItem::HERO_CARD(card) => hero_hero_card(
            card,
            scope,
            mb_data,
            carousel_analytics,
            false,
            enable_optimise_hero_fields_on_rust_treatment,
        ),
        StandardHeroItem::LINK_CARD(card) => hero_link_card(
            card,
            scope,
            mb_data,
            carousel_analytics,
            enable_optimise_hero_fields_on_rust_treatment,
        ),
        StandardHeroItem::CHANNEL_CARD(card) => hero_channel_card(
            card,
            scope,
            mb_data,
            carousel_analytics,
            enable_optimise_hero_fields_on_rust_treatment,
        ),
        StandardHeroItem::LINEAR_STATION(card) => hero_linear_station_card(
            card,
            scope,
            mb_data,
            carousel_analytics,
            card.to_updatable_linear_hero_airing_data(),
        ),
    }
}

// TODO: move this to a trait in container-types for reusability
fn tentpole_hero_item_to_item_model(
    item: &TentpoleHeroItem,
    carousel_analytics: Option<String>,
    scope: Scope,
    enable_optimise_hero_fields_on_rust_treatment: WeblabTreatmentString,
) -> Option<HeroItemModel> {
    let mb_data =
        generate_tentpole_hero_media_background_data(item, carousel_analytics.clone(), scope);
    match item {
        TentpoleHeroItem::HERO_CARD(card) => hero_hero_card(
            card,
            scope,
            mb_data,
            carousel_analytics,
            true,
            enable_optimise_hero_fields_on_rust_treatment,
        ),
    }
}

// TODO: move this to a trait in container-types for reusability
fn special_carousel_item_to_item_type_wrapper(
    item: &SpecialCarouselItem,
    scope: Scope,
    carousel_analytics: Option<String>,
) -> Option<SpecialCollectionsCarouselItemTypeWrapper> {
    let cm_data = item.get_contextual_menu_metadata();
    match item {
        SpecialCarouselItem::CHANNEL_CARD(item) => {
            special_carousel_channel_card(scope, item, cm_data, carousel_analytics)
        }
        SpecialCarouselItem::TITLE_CARD(item) => {
            special_carousel_title_card(scope, item, cm_data, carousel_analytics)
        }
    }
}

#[cfg(feature = "example_data")]
pub fn standard_carousel_parsing(
    container: &StandardCarouselDeserialization,
    scope: Scope,
) -> Option<ContainerType> {
    standard_carousel_to_container_type(
        container,
        scope,
        ContainerItemParsingOptions::default(),
        false,
    )
}

// TODO: move this to a trait in container-types for reusability
fn standard_carousel_to_container_type(
    container: &StandardCarouselDeserialization,
    scope: Scope,
    parsing_options: ContainerItemParsingOptions,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<ContainerType> {
    let id = container.containerMetadata.id.to_owned();
    let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);
    let mut mapped_items = container
        .items
        .iter()
        .filter_map(|sc_item| {
            let item = sc_item.parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "StandardCarousel",
            );
            item.and_then(|item| {
                standard_carousel_item_to_item_type_wrapper(
                    item,
                    scope,
                    carousel_analytics.clone(),
                    parsing_options,
                    is_rust_quickplay_v2_enabled,
                )
            })
        })
        .collect::<Vec<StandardCardContainerItemListType>>();

    if mapped_items.is_empty() {
        log::warn!("Standard Carousel with id {id} has no items after parsing, dropping");
        return None;
    }

    let see_more_link_item = standard_carousel_see_more_link_type_wrapper(
        container,
        scope,
        carousel_analytics,
        is_rust_quickplay_v2_enabled,
    );
    let see_more_link_placement = container
        .seeMore
        .as_ref()
        .and_then(|sm| {
            sm.parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "SeeMore",
            )
        })
        .and_then(|link| link.displayPlacement.clone());

    if let Some(ref see_more_link_item) = see_more_link_item {
        match see_more_link_placement {
            Some(DisplayPlacement::Start) => {
                mapped_items.insert(0, see_more_link_item.clone());
            }
            Some(DisplayPlacement::End) => {
                if container.paginationLink.is_none() {
                    mapped_items.push(see_more_link_item.clone());
                }
            }
            None => {
                log::warn!(
                    "Standard Carousel with id {id} has a see more link but no placement, dropping"
                );
            }
        }
    }

    Some(ContainerType {
        model: create_rw_signal(
            scope,
            ContainerModel::StandardCarousel(create_rw_signal(
                scope,
                StandardCarouselModel {
                    common_carousel_metadata: create_rw_signal(
                        scope,
                        CommonCarouselMetadata {
                            id: id.clone(),
                            analytics: create_rw_signal(
                                scope,
                                container.containerMetadata.analytics.to_owned(),
                            ),
                            journey_ingress_context: create_rw_signal(
                                scope,
                                container.journeyIngressContext.to_owned(),
                            ),
                        },
                    ),
                    standard_carousel_metadata: create_rw_signal(
                        scope,
                        StandardCarouselMetadata {
                            tags: container.containerMetadata.tags.to_owned(),
                            title: create_rw_signal(
                                scope,
                                Some(CarouselTitle {
                                    title_text: container.title.to_owned(),
                                    title_image_url: container.titleImageUrl.to_owned(),
                                }),
                            ),
                            facet: create_rw_signal(
                                scope,
                                container
                                    .facet
                                    .as_ref()
                                    .and_then(|facet| facet.text.to_owned()),
                            ),
                            pagination_link: create_rw_signal(
                                scope,
                                container.paginationLink.to_owned(),
                            ),
                            pagination_pending: create_rw_signal(scope, false),
                            see_more_link: create_rw_signal(scope, see_more_link_item),
                            see_more_link_placement: create_rw_signal(
                                scope,
                                see_more_link_placement,
                            ),
                        },
                    ),
                    items: create_rw_signal(scope, mapped_items),
                },
            )),
        ),
        id,
    })
}

// TODO: move this to a trait in container-types for reusability
fn charts_carousel_to_container_type(
    container: &ChartsCarouselDeserialization,
    scope: Scope,
    carousel_analytics: Option<String>,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<ContainerType> {
    let id = container.containerMetadata.id.to_owned();
    let mut mapped_items = vec![];
    let mut rank = 1;
    for item in &container.items {
        if let Some(item) = item
            .parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "ChartsCarousel",
            )
            .and_then(|item| {
                charts_carousel_item_to_item_type_wrapper(
                    item,
                    scope,
                    carousel_analytics.clone(),
                    rank,
                    is_rust_quickplay_v2_enabled,
                )
            })
        {
            mapped_items.push(item);
            rank += 1;
        }
    }

    if mapped_items.is_empty() {
        log::warn!("Standard Carousel with id {id} has no items after parsing, dropping");
        return None;
    }

    Some(ContainerType {
        model: create_rw_signal(
            scope,
            ContainerModel::ChartsCarousel(create_rw_signal(
                scope,
                ChartsCarouselModel {
                    common_carousel_metadata: create_rw_signal(
                        scope,
                        CommonCarouselMetadata {
                            id: id.clone(),
                            analytics: create_rw_signal(
                                scope,
                                container.containerMetadata.analytics.to_owned(),
                            ),
                            journey_ingress_context: create_rw_signal(
                                scope,
                                container.journeyIngressContext.to_owned(),
                            ),
                        },
                    ),
                    charts_carousel_metadata: create_rw_signal(
                        scope,
                        ChartsCarouselMetadata {
                            title: create_rw_signal(
                                scope,
                                Some(CarouselTitle {
                                    title_text: container.title.to_owned(),
                                    title_image_url: None,
                                }),
                            ),
                        },
                    ),
                    items: create_rw_signal(scope, mapped_items),
                },
            )),
        ),
        id,
    })
}

// TODO: move this to a trait in container-types for reusability
fn beard_supported_carousel_to_container_type(
    container: &BeardSupportedCarouselDeserialization,
    scope: Scope,
    parsing_options: ContainerItemParsingOptions,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<ContainerType> {
    let id = container.containerMetadata.id.to_owned();
    let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);
    let mapped_items = container
        .items
        .iter()
        .filter_map(|sc_item| {
            let item = sc_item.parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "BeardSupportedCarousel",
            );
            item.and_then(|item| {
                standard_carousel_item_to_item_type_wrapper(
                    item,
                    scope,
                    carousel_analytics.clone(),
                    parsing_options,
                    is_rust_quickplay_v2_enabled,
                )
            })
        })
        .collect::<Vec<StandardCardContainerItemListType>>();

    if mapped_items.is_empty() {
        log::warn!("Beard Supported Carousel with id {id} has no items after parsing, dropping");
        return None;
    }

    Some(ContainerType {
        model: create_rw_signal(
            scope,
            ContainerModel::BeardSupportedCarousel(create_rw_signal(
                scope,
                BeardSupportedCarouselModel {
                    common_carousel_metadata: create_rw_signal(
                        scope,
                        CommonCarouselMetadata {
                            id: id.clone(),
                            analytics: create_rw_signal(
                                scope,
                                container.containerMetadata.analytics.to_owned(),
                            ),
                            journey_ingress_context: create_rw_signal(
                                scope,
                                container.journeyIngressContext.to_owned(),
                            ),
                        },
                    ),
                    beard_supported_carousel_metadata: create_rw_signal(
                        scope,
                        BeardSupportedCarouselMetadata {
                            tags: container.containerMetadata.tags.to_owned(),
                            title: create_rw_signal(
                                scope,
                                Some(CarouselTitle {
                                    title_text: container.title.to_owned(),
                                    title_image_url: container.titleImageUrl.to_owned(),
                                }),
                            ),
                            facet: create_rw_signal(
                                scope,
                                container
                                    .facet
                                    .as_ref()
                                    .and_then(|facet| facet.text.to_owned()),
                            ),
                            pagination_link: create_rw_signal(
                                scope,
                                container.paginationLink.to_owned(),
                            ),
                            pagination_pending: create_rw_signal(scope, false),
                        },
                    ),
                    items: create_rw_signal(scope, mapped_items),
                },
            )),
        ),
        id,
    })
}

fn determine_initial_focus_index(items: &[ScheduleCarouselUIItem]) -> usize {
    for (index, item) in items.iter().enumerate() {
        if let ScheduleCarouselUIItem::Card(card_item) = item {
            if let ScheduleCardItemType::Event(event_signal) = card_item.item.get_untracked() {
                if let Some(content) = event_signal.try_get_untracked() {
                    if let Some(contextual_menu) =
                        content.contextual_menu_metadata.try_get_untracked()
                    {
                        if let Some(liveliness_data) = contextual_menu.liveliness_data {
                            if let Some(liveliness) = liveliness_data.liveliness {
                                if matches!(liveliness, Liveliness::Live | Liveliness::Upcoming) {
                                    return index;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    // If no LIVE or UPCOMING events found, return the last index (most recent ENDED event)
    // If current_index is 0, it means the schedule is empty
    items.len().saturating_sub(1)
}

fn schedule_carousel_to_container_type(
    container: &ScheduleCarouselDeserialization,
    scope: Scope,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<ContainerType> {
    let id = container.containerMetadata.id.to_owned();
    let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);

    // Process schedule items
    let mut mapped_items: Vec<ScheduleCarouselUIItem> = container
        .items
        .iter()
        .enumerate()
        .filter_map(|(group_index, group)| {
            let schedule_group = group.parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "ScheduleCarousel",
            )?;

            // Collect and process valid items
            let valid_items: Vec<_> = schedule_group
                .items
                .iter()
                .filter_map(|item| {
                    schedule_carousel_item_to_item_type_wrapper(
                        item,
                        scope,
                        carousel_analytics.clone(),
                        is_rust_quickplay_v2_enabled,
                    )
                })
                .collect();

            // Create group items with correct context
            let group_items = valid_items
                .iter()
                .enumerate()
                .map(|(index, (card, id))| {
                    let group_context = ScheduleCardGroupContext {
                        is_first_in_group: index == 0,
                        is_last_in_group: index == valid_items.len() - 1,
                        group_index: group_index as u32,
                        date_image_url: schedule_group.date_image_url.clone(),
                    };

                    ScheduleCarouselUIItem::Card(ScheduleCarouselCardItem {
                        item: create_rw_signal(scope, card.clone()),
                        schedule_card_group_context: create_rw_signal(scope, group_context),
                        uuid: Uuid::new_v4(),
                        id: id.clone(),
                    })
                })
                .collect::<Vec<_>>();
            Some(group_items)
        })
        .flatten()
        .collect();

    if mapped_items.is_empty() {
        log::warn!("Schedule Carousel with id {id} has no items after parsing, dropping");
        return None;
    }

    // Add schedule page buttons if action exists
    if let Some(Action::TransitionAction(TransitionAction::sportsSchedule(action))) =
        &container.fullScheduleAction
    {
        let button = |button_id: &str| {
            ScheduleCarouselUIItem::Button(ScheduleCarouselButtonItem {
                content: create_rw_signal(
                    scope,
                    ScheduleCarouselButtonItemContent {
                        metadata: create_rw_signal(
                            scope,
                            CommonCarouselCardMetadata {
                                id: format!("{}-FullSchedulePageButton-{}", id, button_id),
                                action: TransitionAction::sportsSchedule(action.clone()),
                                impression_data: ImpressionData::default(), //todo update to non-default
                                swift_content_type: None,
                                gti: None,
                                clickstream_data: CardClickedEvent {
                                    analytics: None,
                                    ref_marker: None,
                                    clickstream_page_params: None,
                                }, //todo update to non-default
                            },
                        ),
                        button_ui_data: create_rw_signal(
                            scope,
                            ScheduleCarouselButtonUIData {
                                button_text: create_rw_signal(scope, "Full schedule".to_string()),
                                description: create_rw_signal(scope, None),
                                title: create_rw_signal(scope, None),
                            },
                        ),
                        contextual_menu_metadata: create_rw_signal(
                            scope,
                            ContextualMenuMetadata {
                                primary_action: Some(TransitionAction::sportsSchedule(
                                    action.clone(),
                                )),
                                ..Default::default()
                            },
                        ),
                    },
                ),
                uuid: Uuid::new_v4(),
                id: format!("{}-FullSchedulePageButton-{}", id, button_id),
            })
        };

        // Add buttons at start and end
        mapped_items.insert(0, button("FIRST"));
        mapped_items.push(button("LAST"));
    }
    let initial_focus = determine_initial_focus_index(&mapped_items);

    Some(ContainerType {
        model: create_rw_signal(
            scope,
            ContainerModel::ScheduleCarousel(create_rw_signal(
                scope,
                ScheduleCarouselModel {
                    common_carousel_metadata: create_rw_signal(
                        scope,
                        CommonCarouselMetadata {
                            id: id.clone(),
                            analytics: create_rw_signal(
                                scope,
                                container.containerMetadata.analytics.to_owned(),
                            ),
                            journey_ingress_context: create_rw_signal(
                                scope,
                                container.journeyIngressContext.to_owned(),
                            ),
                        },
                    ),
                    schedule_carousel_metadata: create_rw_signal(
                        scope,
                        ScheduleCarouselMetadata {
                            title: create_rw_signal(
                                scope,
                                Some(CarouselTitle {
                                    title_text: container.title.to_owned(),
                                    title_image_url: None,
                                }),
                            ),
                            initial_focus_index: create_rw_signal(scope, initial_focus),
                        },
                    ),
                    items: create_rw_signal(scope, mapped_items),
                },
            )),
        ),
        id,
    })
}

fn special_carousel_to_container_type(
    container: &SpecialCarouselContainerDeserialization,
    scope: Scope,
) -> Option<ContainerType> {
    let id = container.containerMetadata.id.to_owned();
    let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);

    let mut mapped_items: Vec<SpecialCollectionsCarouselItemTypeWrapper> = container
        .items
        .iter()
        .filter_map(|item_ref| {
            let item = item_ref.parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "SpecialCollectionsCarousel",
            );
            item.and_then(|item| {
                special_carousel_item_to_item_type_wrapper(item, scope, carousel_analytics.clone())
            })
        })
        .collect();

    if let Some(details) = special_carousel_details(container, scope) {
        mapped_items.insert(0, details)
    }

    let mb_data = MediaBackgroundType::SpecialCollections(SpecialCollectionsBackgroundData {
        id: id.clone(),
        image_url: container.backgroundImageUrl.clone(),
        csm_data: None,
        video_id: None,
        enter_immediately: true,
        placement: "".into(),
        media_strategy: MediaStrategy::Promo,
        interaction_source_override: None,
    });

    Some(ContainerType {
        model: create_rw_signal(
            scope,
            ContainerModel::SpecialCollectionsCarousel(create_rw_signal(
                scope,
                SpecialCollectionsCarouselModel {
                    common_carousel_metadata: create_rw_signal(
                        scope,
                        CommonCarouselMetadata {
                            id: id.clone(),
                            analytics: create_rw_signal(
                                scope,
                                container.containerMetadata.analytics.to_owned(),
                            ),
                            journey_ingress_context: create_rw_signal(
                                scope,
                                container.journeyIngressContext.to_owned(),
                            ),
                        },
                    ),
                    special_collections_carousel_metadata: create_rw_signal(
                        scope,
                        SpecialCollectionsCarouselMetadata {
                            media_background_data: create_rw_signal(scope, mb_data),
                        },
                    ),
                    items: create_rw_signal(scope, mapped_items),
                },
            )),
        ),
        id,
    })
}

// TODO: consider moving this to a trait in container-types for reusability
//      however this does define the supported containers on the page so maybe should remain in
//      collections-ui
pub fn container_to_container_type(
    container: &Container,
    scope: Scope,
    is_rust_quickplay_v2_enabled: bool,
) -> Option<ContainerType> {
    let rust_features = try_use_rust_features(scope);
    let optimise_hero_fields_treatment_string = rust_features.map_or_else(
        || WeblabTreatmentString::default(),
        |features| features.get_optimise_hero_fields_treatment_string(),
    );

    let parsing_options =
        container.get_item_parsing_options(try_use_location(scope).map(|l| l.get_untracked()));

    match container {
        Container::STANDARD_CAROUSEL(container) => standard_carousel_to_container_type(
            container,
            scope,
            parsing_options,
            is_rust_quickplay_v2_enabled,
        ),
        Container::NODES(container) => nodes_carousel_container_to_ui_model(container, scope),
        Container::STANDARD_HERO(container) => {
            let id = container.containerMetadata.id.to_owned();
            let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);
            let mapped_items = container
                .items
                .iter()
                .filter_map(|hero_item| {
                    let item = hero_item.parse_resiliency_into_opt(
                        PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                        "StandardHero",
                    );
                    item.and_then(|item| {
                        standard_hero_item_to_item_model(
                            item,
                            carousel_analytics.clone(),
                            scope,
                            optimise_hero_fields_treatment_string.clone(),
                            parsing_options,
                        )
                    })
                })
                .collect::<Vec<HeroItemModel>>();

            if mapped_items.is_empty() {
                log::warn!("Standard Hero with id {id} has no items after parsing, dropping");
                return None;
            }

            Some(ContainerType {
                id: id.clone(),
                model: create_rw_signal(
                    scope,
                    ContainerModel::StandardHero(create_rw_signal(
                        scope,
                        StandardHeroModel {
                            common_carousel_metadata: create_rw_signal(
                                scope,
                                CommonCarouselMetadata {
                                    id,
                                    analytics: create_rw_signal(
                                        scope,
                                        container.containerMetadata.analytics.to_owned(),
                                    ),
                                    journey_ingress_context: create_rw_signal(
                                        scope,
                                        container.journeyIngressContext.to_owned(),
                                    ),
                                },
                            ),
                            items: create_rw_signal(scope, mapped_items),
                        },
                    )),
                ),
            })
        }
        Container::TENTPOLE_HERO(container) => {
            let id = container.containerMetadata.id.to_owned();
            let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);
            let Some(item) = &container
                .item
                .parse_resiliency_into_opt(
                    PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                    "TentpoleHero",
                )
                .and_then(|item| {
                    tentpole_hero_item_to_item_model(
                        item,
                        carousel_analytics,
                        scope,
                        optimise_hero_fields_treatment_string.clone(),
                    )
                })
            else {
                log::warn!("Tentpole Hero with id {id} could not parse item, dropping");
                return None;
            };

            Some(ContainerType {
                id: id.clone(),
                model: create_rw_signal(
                    scope,
                    ContainerModel::TentpoleHero(create_rw_signal(
                        scope,
                        TentpoleHeroModel {
                            common_carousel_metadata: create_rw_signal(
                                scope,
                                CommonCarouselMetadata {
                                    id,
                                    analytics: create_rw_signal(
                                        scope,
                                        container.containerMetadata.analytics.to_owned(),
                                    ),
                                    journey_ingress_context: create_rw_signal(
                                        scope,
                                        container.journeyIngressContext.to_owned(),
                                    ),
                                },
                            ),
                            item: create_rw_signal(scope, vec![item.clone()]),
                        },
                    )),
                ),
            })
        }
        Container::SHORT_CAROUSEL(container) => {
            let id = container.containerMetadata.id.to_owned();
            let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);
            let mapped_items = container
                .items
                .iter()
                .filter_map(|sc_item| {
                    let item = sc_item.parse_resiliency_into_opt(
                        PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                        "ShortCarousel",
                    );
                    item.and_then(|item| {
                        short_carousel_item_to_model(item, scope, carousel_analytics.clone())
                    })
                })
                .collect::<Vec<ShortCarouselItemModel>>();

            if mapped_items.is_empty() {
                log::warn!("Short Carousel with id {id} has no items after parsing, dropping");
                return None;
            }

            Some(ContainerType {
                model: create_rw_signal(
                    scope,
                    ContainerModel::ShortCarousel(create_rw_signal(
                        scope,
                        ShortCarouselModel {
                            common_carousel_metadata: create_rw_signal(
                                scope,
                                CommonCarouselMetadata {
                                    id: id.clone(),
                                    analytics: create_rw_signal(
                                        scope,
                                        container.containerMetadata.analytics.to_owned(),
                                    ),
                                    journey_ingress_context: create_rw_signal(
                                        scope,
                                        container.journeyIngressContext.to_owned(),
                                    ),
                                },
                            ),
                            items: create_rw_signal(scope, mapped_items),
                        },
                    )),
                ),
                id,
            })
        }
        Container::CHARTS(container) => {
            let carousel_analytics = get_carousel_analytics(&container.containerMetadata.analytics);
            charts_carousel_to_container_type(
                container,
                scope,
                carousel_analytics,
                is_rust_quickplay_v2_enabled,
            )
        }
        Container::BEARD_SUPPORTED_CAROUSEL(container) => {
            beard_supported_carousel_to_container_type(
                container,
                scope,
                parsing_options,
                is_rust_quickplay_v2_enabled,
            )
        }
        Container::COVER(_) => None,
        Container::SUPER_CAROUSEL(container) => {
            super_carousel_to_container_type(container, scope, is_rust_quickplay_v2_enabled)
        }
        Container::EPG(_) => None,
        Container::GRID(_) => None,
        Container::DETAILS_ACTIONS(_) => None,
        Container::VOD_EXTRA_CONTENT(container) => standard_carousel_to_container_type(
            container,
            scope,
            parsing_options,
            is_rust_quickplay_v2_enabled,
        ),
        Container::ACTION_BUTTON_COLLECTION(_) => None,
        Container::NEXT_BEST_ACTION_HINT(_) => None,
        Container::ONBOARDING(container) => {
            let model = CardGridModel::from_transform_model(container, scope);
            model.map(|(m, id)| m.to_container_type(id, scope))
        }
        Container::TEXT_WIDGET(_) => None,
        Container::CHANNEL_NAVIGATION(_) => None,
        Container::FULL_SCREEN_HERO(_) => None,
        Container::ENTITY_CAROUSEL(container) => {
            entity_carousel_to_container_type(container, scope)
        }
        Container::DISCOVERY_ASSISTANT(container) => {
            let model = DiscoveryAssistantModel::from_transform_model(container, scope);
            model.map(|(m, id)| m.to_container_type(id, scope))
        }
        Container::PROMOTIONAL_BANNER(container) => {
            let model = PromoBannerModel::from_transform_model(container, scope);
            model.map(|(m, id)| m.to_container_type(id, scope))
        }
        Container::SCHEDULE_CAROUSEL(container) => {
            schedule_carousel_to_container_type(container, scope, is_rust_quickplay_v2_enabled)
        }
        Container::SPECIAL_CAROUSEL(container) => {
            special_carousel_to_container_type(container, scope)
        }
    }
}

fn is_onboarding_page_speedbump(page_data: &CollectionsPage) -> bool {
    let priority_center_received = page_data
        .isPriorityCenter
        .to_owned()
        .parse_resiliency(
            PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
            "IsPriorityCenter",
        )
        .is_some_and(|b| b.into());
    if priority_center_received {
        // temp. metric ahead of OCX dialup
        metric!("Debug", 1, "metric" => "CollectionsPage.PriorityCenterReceived", "activeLayer" => "Wasm" );
    };
    priority_center_received
}

pub fn is_onboarding_page_hero_ingress(location: &Location) -> bool {
    location.contains_page_params(serde_json::Map::from_iter(vec![
        ("pageType".to_string(), Value::String("merch".to_string())),
        (
            "pageId".to_string(),
            Value::String("onboarding".to_string()),
        ),
    ]))
}

/// In Rust, there are two ways to reach the `OnboardingCX` page
/// 1 - the Speed Bump variant; OCX data is returned by request for home:home, with content
/// within the `priorityCentre` section of the Swift response
/// 2 - the Hero Ingress variant; OCX data is returned by request for merch:onboarding, with content
// within the `standardContent` section of the Swift response
pub fn is_onboarding_page(page_data: &CollectionsPage, location: &Location) -> bool {
    is_onboarding_page_speedbump(page_data) || is_onboarding_page_hero_ingress(location)
}

/// Parses OCX page data to extract title and subtitle information from `TEXT_WIDGET` container.
///
/// Current Implementation:
/// - Extracts title and subtitle from `TEXT_WIDGET` if present
/// - Returns None for action, allowing OCX page to handle default action assignment
/// - Ignores `ACTION_BUTTON_COLLECTION` data
///
/// Future Considerations:
/// - May want to support dynamic button text and navigation based on `ACTION_BUTTON_COLLECTION`
fn parse_page_with_start_column(
    start_column_data: Vec<ContainerWithResiliency>,
) -> (
    Option<StartColumn>,
    Option<String>,
    Option<String>,
    Option<PaginationLink>,
) {
    let (title, subtitle) = start_column_data
        .iter()
        .find_map(|container| {
            if let WithResiliency::Ok(Container::TEXT_WIDGET(widget)) = container {
                Some((widget.title.clone(), widget.subtitle.clone()))
            } else {
                None
            }
        })
        .unwrap_or((None, None));

    (
        Some(StartColumn {
            title,
            subtitle,
            action: None, // OCX page will handle default action assignment
        }),
        None, // title not supported in OCX page
        None, // logo not supported in OCX page
        None, // pagination not supported in OCX page
    )
}

fn parse_page_without_start_column(
    page_data: &CollectionsPage,
) -> (
    Option<StartColumn>,
    Option<String>,
    Option<String>,
    Option<PaginationLink>,
) {
    let page_metadata: Option<PageMetadata> = page_data.pageMetadata.to_owned().parse_resiliency(
        PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
        "PageMetadata",
    );
    let page_title = page_metadata.as_ref().and_then(|metadata| {
        metadata
            .title
            .as_ref()
            .filter(|title| !title.is_empty())
            .cloned()
    });
    let page_logo_image = page_metadata
        .and_then(|metadata| metadata.logoImage)
        .and_then(|logo_image| logo_image.url);

    let pagination_link: Option<PaginationLink> =
        page_data.paginationLink.to_owned().parse_resiliency(
            PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
            "PaginationLink",
        );

    (None, page_title, page_logo_image, pagination_link)
}

fn parse_page_data(
    scope: Scope,
    page_data: &CollectionsPage,
    unique_id: String,
    transform_name: &str,
    is_onboarding_page: bool,
    is_rust_quickplay_v2_enabled: bool,
) -> Result<CollectionsPageModel, RequestError> {
    let mut container_list = page_data.to_owned().containerList;
    let mut start_column_data = Vec::new();

    if is_onboarding_page {
        // Remove and collect the ACTION_BUTTON_COLLECTION and TEXT_WIDGET from container_list
        container_list.retain(|container| {
            if matches!(
                container,
                WithResiliency::Ok(
                    Container::ACTION_BUTTON_COLLECTION(_) | Container::TEXT_WIDGET(_)
                )
            ) {
                start_column_data.push(container.clone());
                false
            } else {
                true
            }
        });
    }

    let new_container_list = container_list
        .iter()
        .filter_map(|container| {
            let container = container.parse_resiliency_into_opt(
                PageType::Rust(RustPage::RUST_COLLECTIONS).to_string(),
                "ContainerList",
            );
            container.and_then(|container| {
                container_to_container_type(container, scope, is_rust_quickplay_v2_enabled)
            })
        })
        .collect::<Vec<ContainerType>>();

    if new_container_list.is_empty() {
        log::error!("{} returned an empty containerList after parsing (either empty, all containers dropped due to lack of support or all items dropped leading to all containers dropped)", transform_name);
        Err(RequestError::ResolverFailed(
            EMPTY_CONTAINER_LIST_ERROR.to_string(),
        ))
    } else {
        let (start_column, page_title, page_logo_image, pagination_link) = if is_onboarding_page {
            parse_page_with_start_column(start_column_data)
        } else {
            parse_page_without_start_column(page_data)
        };

        let parsed_data = CollectionsPageModel {
            container_list: create_rw_signal(scope, new_container_list),
            pagination_link: create_rw_signal(scope, pagination_link),
            page_title: create_rw_signal(scope, page_title),
            page_logo_image: create_rw_signal(scope, page_logo_image),
            pagination_pending: create_rw_signal(scope, false),
            unique_id: create_rw_signal(scope, unique_id),
            start_column: create_rw_signal(scope, start_column),
        };
        Ok(parsed_data)
    }
}

fn update_signals_from_page_data(
    signal: RwSignal<CollectionsPageModel>,
    scope: Scope,
    page_data: &CollectionsPage,
    unique_id: String,
    transform_name: &str,
    manipulate_page_data: Option<Box<dyn FnOnce(&mut CollectionsPageModel)>>,
    is_onboarding_page: bool,
    is_rust_quickplay_v2_enabled: bool,
) -> Result<bool, Option<RequestError>> {
    let new_data = parse_page_data(
        scope,
        page_data,
        unique_id,
        transform_name,
        is_onboarding_page,
        is_rust_quickplay_v2_enabled,
    );

    match new_data {
        Ok(mut new_data) => {
            if let Some(manipulate_page_data) = manipulate_page_data {
                manipulate_page_data(&mut new_data);
            }

            if signal.try_set(new_data).is_some() {
                Ok(true)
            } else {
                Err(None)
            }
        }
        Err(err) => Err(Some(err)),
    }
}

/// Extracts (pageType, pageId) from location pageParams if they are available
pub(crate) fn extract_resiliency_page_params_from_location(
    location: Signal<Location>,
) -> Option<ResiliencyPage> {
    let page_params = location.get_untracked().pageParams;

    let resiliency_source_page_type = page_params.get("resiliencySourcePageType");
    let resiliency_source_page_id = page_params.get("resiliencySourcePageId");

    let resiliency_page_type =
        resiliency_source_page_type.map_or_else(|| None, |v| v.as_str().map(|s| s.to_string()));
    let resiliency_page_id =
        resiliency_source_page_id.map_or_else(|| None, |v| v.as_str().map(|s| s.to_string()));

    match (resiliency_page_type, resiliency_page_id) {
        (Some(t), Some(i)) => Some(ResiliencyPage {
            pageType: t,
            pageId: i,
        }),
        _ => None,
    }
}

pub(crate) fn validate_collections_page_from_value(value: &Value) -> bool {
    match value.get("containerList") {
        Some(v) => v.as_array().is_some_and(|arr| !arr.is_empty()),
        None => false,
    }
}

#[cfg(test)]
mod tests {
    use consent::types::{ConsentModalData, CriticalNotificationId};
    use in_app_survey::mocks::mock_survey::get_mock_survey;
    use mockall::predicate::eq;
    use network::common::DeviceProxyResponse;
    use payment_risk_message::PaymentRiskState;
    use rust_features::MockRustFeaturesBuilder;
    use title_details::types::common::{
        EntitlementIconType, EntitlementLabelData, EventMetadata, HighValueMessageData,
        LiveLinearMetadata, LivelinessData, MaturityRatingData, StandardTitleDetailsData,
        StarRatingData, TitleData, TitleDetailsMetadata, VodMetadata,
    };

    use super::*;
    use crate::network::parser::collections_response_sports_edge_parser;
    use crate::test_assets::mocks::{
        get_populated_collections_page, get_populated_collections_page_with_beard_support,
        get_populated_collections_page_with_edcx_modal_multiple_items,
        get_populated_collections_page_with_edcx_modal_single_item,
        get_populated_collections_page_with_linear_station_hero,
        get_populated_collections_page_with_mixed_usable_unusable_content,
        get_populated_collections_page_with_onboarding_cx_content,
        get_populated_collections_page_with_only_unusable_content,
        get_populated_collections_page_with_promo_banner,
        get_populated_collections_page_with_see_more_links, MockNetworkClient,
        SuccessCallbackTestSetup,
    };
    use crate::utils::metric_emitter::MockMetricEmitter;
    use common_transform_types::actions::Action;
    use common_transform_types::container_items::{CarouselItemData, SportsCard};
    use common_transform_types::containers::{
        ContainerMetadata, EntityCarouselDeserialization, EntityCarouselItem, PresentationCue,
    };
    use common_transform_types::{
        payment_risk_message::PaymentRiskMessage, resiliency::WithResiliency,
    };
    use container_types::{
        test_utils::{
            get_paginated_beard_supported_carousel, get_paginated_container_list,
            get_paginated_container_list_with_unsupported_see_more_link,
            get_paginated_standard_carousel, get_paginated_standard_carousel_with_see_more,
            get_paginated_super_carousel,
        },
        ui_signals::StandardCardContainerItemType,
    };
    use core::default::Default;
    use educational_cx::test_utils::setup_edcx_mocks;
    use ignx_compositron::app::{launch_only_app_context, launch_only_scope};
    use liveliness_types::Liveliness;
    use network_parser::core::NetworkOptional;
    use router::rust_location;
    use rstest::rstest;
    use serial_test::serial;

    enum PageSetupState {
        Empty,
        Populated,
        PopulatedWithBeardSupport,
        SeeMore,
        OnboardingCX,
    }

    #[derive(PartialEq)]
    enum Features {
        PromoBanner,
        EastWestTruncationExperimentT1,
        EastWestTruncationExperimentT3,
    }

    fn get_east_west_carousel_experiment_treatment(
        features: Vec<Features>,
    ) -> WeblabTreatmentString {
        if features.contains(&Features::EastWestTruncationExperimentT1) {
            WeblabTreatmentString::T1
        } else if features.contains(&Features::EastWestTruncationExperimentT3) {
            WeblabTreatmentString::T3
        } else {
            WeblabTreatmentString::C
        }
    }

    fn setup_page(
        scope: Scope,
        state: PageSetupState,
        features: Vec<Features>,
    ) -> (
        RwSignal<CollectionsPageModel>,
        RwSignal<PageRequestStatus<String>>,
    ) {
        let feature_builder = MockRustFeaturesBuilder::new()
            .set_is_linear_stations_in_hero_enabled(true)
            .set_east_west_carousel_truncation_treatment(
                get_east_west_carousel_experiment_treatment(features),
            );

        provide_context(scope, feature_builder.build());

        let page_data = create_default_collections_page_model(scope);

        match state {
            PageSetupState::Empty => (
                page_data,
                create_rw_signal(scope, PageRequestStatus::Waiting),
            ),
            _ => {
                let new_data = match state {
                    PageSetupState::Populated => get_populated_collections_page(),
                    PageSetupState::PopulatedWithBeardSupport => {
                        get_populated_collections_page_with_beard_support()
                    }
                    PageSetupState::SeeMore => get_populated_collections_page_with_see_more_links(),
                    PageSetupState::OnboardingCX => {
                        get_populated_collections_page_with_onboarding_cx_content()
                    }
                    _ => unreachable!(),
                };

                update_signals_from_page_data(
                    page_data,
                    scope,
                    &new_data,
                    "home-home-None".to_string(),
                    "a transform",
                    None,
                    false,
                    false,
                );
                (
                    page_data,
                    create_rw_signal(
                        scope,
                        PageRequestStatus::Success("home-home-None".to_string()),
                    ),
                )
            }
        }
    }

    fn assert_page_data(
        page_data: RwSignal<CollectionsPageModel>,
        assertions: impl FnOnce(&CollectionsPageModel),
    ) {
        page_data.with_untracked(assertions);
    }

    fn assert_initial_load_status_success(
        initial_load_status: RwSignal<PageRequestStatus<String>>,
        expected_string: &str,
    ) {
        initial_load_status.with_untracked(|status| {
            let PageRequestStatus::Success(a_string) = status else {
                panic!("Expected initial load status to be success!");
            };
            assert_eq!(a_string, expected_string);
        });
    }

    fn assert_initial_load_status_failure(
        initial_load_status: RwSignal<PageRequestStatus<String>>,
    ) {
        initial_load_status.with_untracked(|status| {
            let PageRequestStatus::RequestError(_) = status else {
                panic!("Expected initial load status to be request error!");
            };
        });
    }

    fn assert_recycled_items(
        item_list: Vec<StandardCardContainerItemListType>,
        start_index: usize,
        recycled_start_index: usize,
        number_of_cards: usize,
        has_see_more_item: bool,
    ) {
        for i in start_index..number_of_cards {
            let original_item_index = if has_see_more_item { i + 1 } else { i };
            let recycled_item_index = if has_see_more_item {
                i + 1 + recycled_start_index
            } else {
                i + recycled_start_index
            };
            let original_item = item_list.get(original_item_index).unwrap();
            let recycled_item = item_list.get(recycled_item_index).unwrap();
            assert_eq!(
                original_item
                    .model
                    .get()
                    .get_item_model()
                    .get()
                    .title_details_data
                    .get(),
                recycled_item
                    .model
                    .get()
                    .get_item_model()
                    .get()
                    .title_details_data
                    .get()
            );
        }
    }

    fn create_title_data(title_text: &str) -> TitleData {
        TitleData {
            title_text: title_text.to_string(),
            title_art_url: Some("some_url".to_string()),
            provider_logo_url: Some("provider_logo_url".to_string()),
        }
    }

    fn create_vod_metadata(
        maturity_rating: Option<MaturityRatingData>,
        high_value_message: Option<String>,
    ) -> TitleDetailsMetadata {
        TitleDetailsMetadata::Vod(VodMetadata {
            high_value_message: high_value_message.map(|high_value_message| HighValueMessageData {
                message: high_value_message,
                color: ignx_compositron::color::Color::red(),
            }),
            star_rating: Some(StarRatingData {
                rating: 3.0,
                votes: Some(650),
            }),
            metadata_badge_message: None,
            duration: None,
            number_of_seasons: Some(3),
            release_year: Some(2012),
            maturity_rating,
            badges: Vec::new(),
            genres: vec!["Drama".to_string(), "Action".to_string()],
            is_prerelease: None,
            card_badge: None,
            upcoming_message: None,
            condensed_page: false,
        })
    }

    fn create_event_metadata(maturity_rating: Option<MaturityRatingData>) -> TitleDetailsMetadata {
        TitleDetailsMetadata::Event(EventMetadata {
            maturity_rating,
            liveliness_data: Some(LivelinessData {
                message: None,
                level: None,
                liveliness: Some(Liveliness::Live),
            }),
            event_date: Some("2023-01-01".to_string()),
            event_time: Some("12:00".to_string()),
            event_venue: Some("Some Venue".to_string()),
            event_gti: Some("amzn1.dv.gti.00000000-0000-0000-0000-000000000000".to_string()),
            service_announcement: None,
        })
    }

    fn create_live_linear_metadata() -> TitleDetailsMetadata {
        TitleDetailsMetadata::LiveLinear(LiveLinearMetadata {
            schedule_time: Some("2023-01-01 12:00".to_string()),
            content_descriptors: Some("content, descriptors".to_string()),
            station_name: Some("Station Name".to_string()),
            maturity_rating_image: Some("maturity_image_url".to_string()),
            up_next: Some("Up Next Show".to_string()),
            liveliness: Liveliness::OnNow,
        })
    }

    fn create_standard_title_details(
        title_text: &str,
        metadata: TitleDetailsMetadata,
        synopsis: Option<String>,
        entitlement_label: Option<String>,
        entitlement_icon: Option<EntitlementIconType>,
    ) -> StandardTitleDetailsData {
        StandardTitleDetailsData {
            title_data: create_title_data(title_text),
            metadata,
            synopsis,
            entitlement_data: EntitlementLabelData {
                entitlement_label,
                entitlement_icon,
            },
        }
    }

    fn get_mock_consent_modal() -> AppModal {
        AppModal::Consent(
            ConsentModalData {
                title: String::from("Privacy Policy Update"),
                modal_type: CriticalNotificationId::CookieConsent, // Assuming this enum exists
                content: Some(String::from("We've updated our privacy policy. Please review and accept the changes to continue using our services.")),
                accept_button_text: String::from("Accept"),
                reject_button_text: String::from("Decline"),
                navigate_hint_text: Some(String::from("Please scroll to read the full policy")),
                on_accept: Rc::new(|| {
                    println!("Mock: Consent accepted");
                }),
                on_reject: Rc::new(|| {
                    println!("Mock: Consent rejected");
                }),
                render_ref_marker: Some(String::from("consent-modal-render")),
                accept_ref_marker: Some(String::from("consent-accept-button")),
                reject_ref_marker: Some(String::from("consent-reject-button")),
                scroll_ref_marker: Some(String::from("consent-scroll-marker")),
            }
        )
    }

    #[test]
    fn initial_load_success_cb_does_nothing_if_request_id_doesnt_match() {
        launch_only_app_context(|ctx| {
            let modal_data_signal = create_rw_signal(ctx.scope, vec![]);
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);

            // assert before
            assert_eq!(
                page_data_and_status
                    .0
                    .with_untracked(|m| m.container_list.get_untracked().len()),
                0
            );
            assert!(page_data_and_status
                .1
                .with_untracked(|s| matches!(s, PageRequestStatus::Waiting)));

            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_modal_data_signal(modal_data_signal)
                .with_location_signal_from_default_params();

            let page_controller = setup.to_controller();

            // request id in setup is 1, passing 3 so the cb will bail out early.
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 3, "home-home-None".into());

            let args = setup.to_success_cb_args();

            success_cb(
                args.parsed_page_from_network.clone(), // by default this is the populated collections page
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            // assert after
            assert_eq!(modal_data_signal.get().len(), 0);
            assert_page_data(setup.controller_signals.page_data, |data| {
                let CollectionsPageModel { container_list, .. } = data;
                assert_eq!(container_list.get_untracked().len(), 0);
            });
            assert!(setup
                .controller_signals
                .initial_load_status
                .with_untracked(|s| matches!(s, PageRequestStatus::Waiting)));
        });
    }

    #[test]
    fn initial_load_success_cb_does_nothing_if_scope_disposed_so_location_is_not_present() {
        launch_only_app_context(|ctx| {
            let modal_data_signal = create_rw_signal(ctx.scope, vec![]);
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);

            // assert before
            assert_eq!(
                page_data_and_status
                    .0
                    .with_untracked(|m| m.container_list.get_untracked().len()),
                0
            );
            assert!(page_data_and_status
                .1
                .with_untracked(|s| matches!(s, PageRequestStatus::Waiting)));

            ctx.scope.child_scope(move |child_scope| {
                let location = create_signal(child_scope, Location::default()).0;

                let setup = SuccessCallbackTestSetup::new(ctx)
                    .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                    .with_modal_data_signal(modal_data_signal)
                    .with_location_signal(location.into());

                let page_controller = setup.to_controller();

                let success_cb = get_initial_load_success_cb(
                    Rc::new(page_controller),
                    1,
                    "home-home-None".into(),
                );

                let args = setup.to_success_cb_args();

                child_scope.dispose();

                success_cb(
                    args.parsed_page_from_network.clone(), // by default this is the populated collections page
                    args.transform_name,
                    args.manipulate_page_data,
                    args.timestamp,
                );

                // assert after
                assert_eq!(modal_data_signal.get().len(), 0);
                assert_page_data(setup.controller_signals.page_data, |data| {
                    let CollectionsPageModel { container_list, .. } = data;
                    assert_eq!(container_list.get_untracked().len(), 0);
                });
                assert!(setup
                    .controller_signals
                    .initial_load_status
                    .with_untracked(|s| matches!(s, PageRequestStatus::Waiting)));
            });
        });
    }

    #[test]
    fn initial_load_success_cb_updates_signals() {
        launch_only_app_context(|ctx| {
            let modal_data_signal = create_rw_signal(ctx.scope, vec![]);
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_manipulate_page_data_fn()
                .with_modal_data_signal(modal_data_signal)
                .with_location_signal_from_default_params();

            let page_controller = setup.to_controller();

            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home-None".into());

            let args = setup.to_success_cb_args();

            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp.clone(),
            );

            assert_eq!(modal_data_signal.get().len(), 0);
            assert_eq!(*args.manipulate_page_data_calls.borrow(), 1);
            assert_page_data(setup.controller_signals.page_data, |data| {
                let CollectionsPageModel {
                    container_list,
                    pagination_link,
                    page_title,
                    page_logo_image,
                    pagination_pending,
                    unique_id,
                    start_column,
                } = data;

                let WithResiliency::Ok(NetworkOptional::Some(metadata)) =
                    &args.parsed_page_from_network.pageMetadata
                else {
                    panic!("page metadata should be ok and some");
                };
                let WithResiliency::Ok(NetworkOptional::Some(page_link)) =
                    &args.parsed_page_from_network.paginationLink
                else {
                    panic!("pagination link should be ok and some");
                };

                assert!(!pagination_pending.get_untracked());
                assert_eq!(
                    page_logo_image.get_untracked(),
                    metadata.logoImage.as_ref().unwrap().url
                );
                assert_eq!(page_title.get_untracked(), None);
                assert_eq!(
                    pagination_link.get_untracked().unwrap(),
                    page_link.to_owned()
                );
                // container specific assertions are done in their parsing tests
                // 10 because the only ones allowed currently are standard carousel(3), super_carousel, standard hero, tentpole hero (ignored), short carousel, nodes carousel(2), charts carousel, discovery assistant
                assert_eq!(container_list.get_untracked().len(), 10);
                assert_eq!(container_list.get_untracked()[0].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt67ioRob21li4Rob21ljI6mMToxMjJZTU9SUTEwNkdKUiMjTkJTWEUzMkRNRlpHNjVMVE1WV0GND46CVjI=");
                assert_eq!(container_list.get_untracked()[1].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7mio6qc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNzaG9ydGNhcm91c2Vsi4R0ZXN0jI6qMToxMjJKNUJNQUlIUjlYNCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy");
                assert_eq!(container_list.get_untracked()[2].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxU09NMURCSzhDTUJBIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(container_list.get_untracked()[3].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM0pFME9aU1FBVUoyRCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy");
                assert_eq!(container_list.get_untracked()[4].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7oio6sc3RvcmVmcm9udHRlc3RzdWl0ZXdpZGdldHR5cGVzbGl2ZWxpbmVhcmNhcmSLhHRlc3SMjqoxOjExMUFFTjhNWTROVThYIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(container_list.get_untracked()[5].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7fio6jc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNjaGFydHOLhHRlc3SMjqoxOjEzT1ZKNkUyNkJJVzQxIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(container_list.get_untracked()[6].id, "V2=4AEA6_unodes_Ye-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7eio6ic3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNub2Rlc4uEdGVzdIyOqjE6MTM3V1I4S1FTWjRXSVcjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==");
                assert_eq!(container_list.get_untracked()[7].id, "V2=X_enodes_Xu69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyT0NaNzhLUjI0VTZGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(container_list.get_untracked()[8].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZTcG9ydHOLhGhvbWWMjqoxOjEyQVFXNk9CMENaRFhOIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(container_list.get_untracked()[9].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Eiolhc3Npc3RhbnSLhGhvbWWMjqoyOlNSNkJEMzM0ODNDQTk4IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(unique_id.get_untracked(), "home-home-None");
                assert_eq!(start_column.get_untracked(), None)
            });
            assert_initial_load_status_success(
                setup.controller_signals.initial_load_status,
                "home-home-None",
            );
            assert_eq!(
                setup.controller_signals.data_fetch_data.get_untracked(),
                Some((args.timestamp, FreshnessAction::NetworkClient))
            );
        });
    }

    #[test]
    #[ignore]
    fn initial_load_success_cb_reports_response_parsed_event() {
        launch_only_app_context(|ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_location_signal_from_default_params();

            let mut page_controller = setup.to_controller();

            // Set up expectation for ResponseParsed event
            page_controller
                .expect_report_app_event()
                .times(1)
                .withf(|event| matches!(event, CollectionsAppEvents::ResponseParsed { .. }))
                .return_const(());
            page_controller.expect_report_app_event().return_const(());

            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home-None".into());

            let args = setup.to_success_cb_args();

            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );
        });
    }

    #[test]
    fn initial_load_success_cb_does_not_report_when_empty_container_list_after_parsing() {
        launch_only_app_context(|ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_location_signal_from_default_params()
                .with_parsed_page(get_populated_collections_page_with_only_unusable_content());

            let mut page_controller = setup.to_controller();

            // Should NOT expect any app events to be reported
            page_controller.expect_report_app_event().times(0);

            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home-None".into());

            let args = setup.to_success_cb_args();

            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );
        });
    }

    #[test]
    fn initial_load_success_cb_shows_in_app_survey_if_present() {
        launch_only_app_context(|ctx| {
            let modal_data_signal = create_rw_signal(ctx.scope, vec![]);
            let mut populated_collections_page = get_populated_collections_page();
            populated_collections_page.surveyData = Some(get_mock_survey());
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);

            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_modal_data_signal(modal_data_signal)
                .with_parsed_page(populated_collections_page)
                .with_location_signal_from_default_params();

            let page_controller = setup.to_controller();

            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

            let args = setup.to_success_cb_args();

            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert!(matches!(modal_data_signal.get()[0], AppModal::Survey(_)));
        });
    }

    #[test]
    fn initial_load_success_cb_shows_payment_risk_message_if_present() {
        launch_only_app_context(|ctx| {
            let scope = ctx.scope;
            let modal_data_signal = create_rw_signal(scope, vec![]);
            let page_data_and_status = setup_page(scope, PageSetupState::Empty, vec![]);

            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_modal_data_signal(modal_data_signal)
                .with_location_signal_from_default_params();

            let page_controller = setup.to_controller();

            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

            let payment_risk_state = PaymentRiskState::new(
                Some(PaymentRiskMessage {
                    action: "test".into(),
                    body: "test".into(),
                    dismiss: "test".into(),
                    header: "test".into(),
                    offer_type: "OfferType".into(),
                }),
                None,
            );
            provide_context::<PaymentRiskContext>(scope, Rc::new(payment_risk_state.into()));

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert_eq!(modal_data_signal.get().len(), 1);
        });
    }

    #[test]
    fn initial_load_success_cb_does_not_show_payment_risk_message_if_not_present() {
        launch_only_app_context(|ctx| {
            let scope = ctx.scope;
            let modal_data_signal = create_rw_signal(scope, vec![]);
            let page_data_and_status = setup_page(scope, PageSetupState::Empty, vec![]);

            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_modal_data_signal(modal_data_signal)
                .with_location_signal_from_default_params();

            let page_controller = setup.to_controller();

            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

            let payment_risk_state = PaymentRiskState::new(None, None);
            provide_context::<PaymentRiskContext>(scope, Rc::new(payment_risk_state.into()));

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert_eq!(modal_data_signal.get().len(), 0);
        });
    }

    #[test]
    fn initial_load_success_cb_clears_existing_modals_and_avoids_populating_more_modals_for_onboarding_cx(
    ) {
        launch_only_app_context(|ctx| {
            let scope = ctx.scope;
            let modal_data_signal = create_rw_signal(ctx.scope, vec![get_mock_consent_modal()]);
            let mut populated_collections_page =
                get_populated_collections_page_with_onboarding_cx_content();
            populated_collections_page.surveyData = Some(get_mock_survey());

            let page_data_and_status = setup_page(ctx.scope, PageSetupState::OnboardingCX, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_modal_data_signal(modal_data_signal)
                .with_parsed_page(populated_collections_page)
                .with_location_signal_from_default_params();

            let page_controller = setup.to_controller();
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

            let payment_risk_state = PaymentRiskState::new(
                Some(PaymentRiskMessage {
                    action: "test".into(),
                    body: "test".into(),
                    dismiss: "test".into(),
                    header: "test".into(),
                    offer_type: "OfferType".into(),
                }),
                None,
            );
            provide_context::<PaymentRiskContext>(scope, Rc::new(payment_risk_state.into()));

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert_eq!(modal_data_signal.get().len(), 0);
        });
    }

    #[test]
    fn initial_load_success_cb_updates_signals_with_resililency() {
        launch_only_app_context(|ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_parsed_page(
                    get_populated_collections_page_with_mixed_usable_unusable_content(),
                )
                .with_location_signal_from_default_params();
            let page_controller = setup.to_controller();
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home-None".into());

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert_page_data(setup.controller_signals.page_data, |data| {
                let CollectionsPageModel {
                    container_list,
                    pagination_link,
                    page_title,
                    page_logo_image,
                    pagination_pending,
                    unique_id,
                    start_column,
                } = data;

                let WithResiliency::Unexpected(_) = &args.parsed_page_from_network.pageMetadata
                else {
                    panic!("page metadata should be unexpected");
                };
                let WithResiliency::Ok(NetworkOptional::Some(page_link)) =
                    &args.parsed_page_from_network.paginationLink
                else {
                    panic!("pagination link should be ok and some");
                };

                assert!(!pagination_pending.get_untracked());
                // Since page metadata response is bad, we should be resilient and just drop the page logo
                assert_eq!(page_logo_image.get_untracked(), None);
                assert_eq!(page_title.get_untracked(), None);
                assert_eq!(
                    pagination_link.get_untracked().unwrap(),
                    page_link.to_owned()
                );
                // ------ Container List in Response: ------
                // STANDARD HERO        | 10 items  | 5 items invalid | All other metadata valid
                // SHORT CAROUSEL       | 5 items   | 0 items invalid | Other metadata invalid
                // SUPER CAROUSEL       | 10 items  | 0 items invalid | Other metadata invalid
                // SUPER CAROUSEL       | 10 items  | 4 items invalid | All other metadata valid
                // STANDARD CAROUSEL    | 10 items  | 0 items invalid | Other metadata invalid
                // STANDARD CAROUSEL    | 20 items  | 3 items invalid | All other metadata valid
                // COVER CAROUSEL       | 10 items  | 0 items invalid | All other metadata valid
                // CHARTS CAROUSEL      | 10 items  | 2 items invalid | All other metadata valid
                // NODES CAROUSEL (ent) | 5 items   | 0 items invalid | Other metadata invalid
                // NODES CAROUSEL (un)  | 5 items   | 4 items invalid | All other metadata valid
                // NODES CAROUSEL (mix) | 3 items   | 0 items invalid | All other metadata valid
                // TEXT WIDGET          | 0 items   | 0 items invalid | Other metadata invalid
                // ------ Expected Container List in Page: ------
                // STANDARD HERO        | 5 items   |
                // SUPER CAROUSEL       | 6 items   |
                // STANDARD CAROUSEL    | 17 items  |
                // CHARTS CAROUSEL      | 8 items   |
                // NODES CAROUSEL (un)  | 1 items   |
                let list = container_list.get_untracked();

                let container0 = &list[0];
                let ContainerModel::StandardHero(hero) = container0.model.get_untracked() else {
                    panic!("Expected Standard Hero");
                };
                assert_eq!(container0.id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt67ioRob21li4Rob21ljI6mMToxMjJZTU9SUTEwNkdKUiMjTkJTWEUzMkRNRlpHNjVMVE1WV0GND46CVjI=");
                assert_eq!(hero.get_untracked().items.get_untracked().len(), 5);

                let container1 = &list[1];
                let ContainerModel::SuperCarousel(superc) = container1.model.get_untracked() else {
                    panic!("Expected Super Carousel");
                };
                assert_eq!(container1.id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxU09NMURCSzhDTUJBIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(superc.get_untracked().items.get_untracked().len(), 6);

                let container2 = &list[2];
                let ContainerModel::StandardCarousel(standard) = container2.model.get_untracked()
                else {
                    panic!("Expected Standard Carousel");
                };
                assert_eq!(container2.id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7oio6sc3RvcmVmcm9udHRlc3RzdWl0ZXdpZGdldHR5cGVzbGl2ZWxpbmVhcmNhcmSLhHRlc3SMjqoxOjExMUFFTjhNWTROVThYIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(standard.get_untracked().items.get_untracked().len(), 16);
                assert!(standard
                    .get_untracked()
                    .standard_carousel_metadata
                    .get_untracked()
                    .see_more_link
                    .get_untracked()
                    .is_none());

                let container3 = &list[3];
                let ContainerModel::ChartsCarousel(charts) = container3.model.get_untracked()
                else {
                    panic!("Expected Charts Carousel");
                };
                assert_eq!(container3.id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7fio6jc3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNjaGFydHOLhHRlc3SMjqoxOjEzT1ZKNkUyNkJJVzQxIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=");
                assert_eq!(charts.get_untracked().items.get_untracked().len(), 8);

                let container4 = &list[4];
                let ContainerModel::NodesCarousel(nodes) = container4.model.get_untracked() else {
                    panic!("Expected Nodes Carousel");
                };
                let NodesCarouselModel::Unentitled(nodes) = nodes.get_untracked() else {
                    panic!("Expected Nodes Carousel to be Unentitled");
                };
                assert_eq!(container4.id, "V2=4AEA6_unodes_Ye-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7eio6ic3RvcmVmcm9udHRlc3RzdWl0ZWNvbnRhaW5lcnNub2Rlc4uEdGVzdIyOqjE6MTM3V1I4S1FTWjRXSVcjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==");
                assert_eq!(nodes.get_untracked().items.get_untracked().len(), 1);

                assert_eq!(container_list.get_untracked().len(), 5);

                assert_eq!(unique_id.get_untracked(), "home-home-None");
                assert_eq!(start_column.get_untracked(), None)
            });
            assert_initial_load_status_success(
                setup.controller_signals.initial_load_status,
                "home-home-None",
            );
        });
    }

    #[test]
    fn initial_load_success_cb_only_updates_load_status_if_no_containers() {
        launch_only_app_context(|ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_parsed_page(get_populated_collections_page_with_only_unusable_content())
                .with_location_signal_from_default_params();
            let page_controller = setup.to_controller();
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert_page_data(setup.controller_signals.page_data, |data| {
                let CollectionsPageModel {
                    container_list,
                    pagination_link,
                    page_title,
                    page_logo_image,
                    pagination_pending,
                    unique_id,
                    start_column,
                } = data;
                assert_eq!(container_list.get_untracked().len(), 0);
                assert_eq!(pagination_link.get_untracked(), None);
                assert_eq!(page_title.get_untracked(), None);
                assert_eq!(page_logo_image.get_untracked(), None);
                assert!(!pagination_pending.get_untracked());
                assert_eq!(start_column.get_untracked(), None);
                assert_eq!(unique_id.get_untracked(), "".to_string());
            });
            assert_initial_load_status_failure(setup.controller_signals.initial_load_status);
        });
    }

    #[test]
    fn initial_load_success_cb_for_onboarding_cx() {
        launch_only_app_context(|ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_parsed_page(get_populated_collections_page_with_onboarding_cx_content())
                .with_location_signal_from_default_params();
            let page_controller = setup.to_controller();
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home-None".into());

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert_page_data(setup.controller_signals.page_data, |data| {
                let CollectionsPageModel {
                    container_list,
                    pagination_link,
                    page_title,
                    page_logo_image,
                    pagination_pending,
                    unique_id,
                    start_column,
                } = data;

                assert!(!pagination_pending.get_untracked());
                assert_eq!(page_logo_image.get_untracked(), None);
                assert_eq!(page_title.get_untracked(), None);
                assert_eq!(pagination_link.get_untracked(), None);

                //only onboarding container present in container_list
                assert_eq!(container_list.get_untracked().len(), 1);
                assert_eq!(container_list.get_untracked()[0].id, "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMTBNMlNCOUgwODBUNyMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy");
                assert_eq!(unique_id.get_untracked(), "home-home-None");
                assert_eq!(
                    start_column.get_untracked().unwrap().title,
                    Some("What do you like to watch?".to_string())
                );
                assert_eq!(start_column.get_untracked().unwrap().subtitle, Some("Select movies and TV shows and we’ll use those to find videos we think you’ll like.".to_string()));
                assert!(start_column.get_untracked().unwrap().action.is_none());
            });
            assert_initial_load_status_success(
                setup.controller_signals.initial_load_status,
                "home-home-None",
            );
        });
    }

    #[test]
    fn initial_load_success_cb_shows_see_more_link() {
        launch_only_app_context(|ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_parsed_page(get_populated_collections_page_with_see_more_links())
                .with_location_signal_from_default_params();
            let page_controller = setup.to_controller();
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home-None".into());

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            assert_page_data(setup.controller_signals.page_data, |data| {
                let CollectionsPageModel {
                    container_list,
                    pagination_link,
                    page_title,
                    page_logo_image,
                    pagination_pending,
                    unique_id,
                    start_column,
                } = data;
                assert_eq!(container_list.get_untracked().len(), 5);
                assert!(pagination_link.get_untracked().is_some());
                assert_eq!(page_title.get_untracked(), None);
                assert_eq!(page_logo_image.get_untracked(), None);
                assert!(!pagination_pending.get_untracked());
                assert_eq!(unique_id.get_untracked(), "home-home-None".to_string());

                // Container 0 - See More link at the start of the carousel
                // Container 1 - See More link at the end of the carousel without pagination link
                // Container 2 - See More link at the end of the carousel with pagination link
                // Container 3 - No see more link
                // All containers are identical prior See More link insertion
                let container0 = match container_list.get_untracked().get(0).unwrap().model.get() {
                    ContainerModel::StandardCarousel(standard) => standard.get(),
                    _ => panic!("Expected Standard Carousel"),
                };
                let container1 = match container_list.get_untracked().get(1).unwrap().model.get() {
                    ContainerModel::StandardCarousel(standard) => standard.get(),
                    _ => panic!("Expected Standard Carousel"),
                };
                let container2 = match container_list.get_untracked().get(2).unwrap().model.get() {
                    ContainerModel::StandardCarousel(standard) => standard.get(),
                    _ => panic!("Expected Standard Carousel"),
                };
                let container3 = match container_list.get_untracked().get(3).unwrap().model.get() {
                    ContainerModel::StandardCarousel(standard) => standard.get(),
                    _ => panic!("Expected Standard Carousel"),
                };

                assert!(container0
                    .standard_carousel_metadata
                    .get()
                    .see_more_link
                    .get()
                    .is_some());
                assert!(container1
                    .standard_carousel_metadata
                    .get()
                    .see_more_link
                    .get()
                    .is_some());
                assert!(container2
                    .standard_carousel_metadata
                    .get()
                    .see_more_link
                    .get()
                    .is_some());
                assert!(container3
                    .standard_carousel_metadata
                    .get()
                    .see_more_link
                    .get()
                    .is_none());

                assert_eq!(
                    container0
                        .standard_carousel_metadata
                        .get()
                        .see_more_link_placement
                        .get(),
                    Some(DisplayPlacement::Start)
                );
                assert_eq!(
                    container1
                        .standard_carousel_metadata
                        .get()
                        .see_more_link_placement
                        .get(),
                    Some(DisplayPlacement::End)
                );
                assert_eq!(
                    container2
                        .standard_carousel_metadata
                        .get()
                        .see_more_link_placement
                        .get(),
                    Some(DisplayPlacement::End)
                );
                assert_eq!(
                    container3
                        .standard_carousel_metadata
                        .get()
                        .see_more_link_placement
                        .get(),
                    None
                );

                // Check if see more link is visible as an extra item
                assert_eq!(container0.items.get().len(), 3);
                assert_eq!(container1.items.get().len(), 3);
                assert_eq!(container2.items.get().len(), 2);
                assert_eq!(container3.items.get().len(), 2);

                // Check if first item is see more link
                assert!(matches!(
                    container0.items.get().get(0).unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));
                assert!(!matches!(
                    container1.items.get().get(0).unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));
                assert!(!matches!(
                    container2.items.get().get(0).unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));
                assert!(!matches!(
                    container3.items.get().get(0).unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));

                // Check if last item is see more link
                assert!(!matches!(
                    container0.items.get().last().unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));
                assert!(matches!(
                    container1.items.get().last().unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));
                assert!(!matches!(
                    container2.items.get().last().unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));
                assert!(!matches!(
                    container3.items.get().last().unwrap().model.get(),
                    StandardCardContainerItemType::SeeMoreLink(_)
                ));
                assert_eq!(start_column.get_untracked(), None)
            });
            assert_initial_load_status_success(
                setup.controller_signals.initial_load_status,
                "home-home-None",
            );
        });
    }

    #[test]
    fn initial_load_failure_cb_updates_signals() {
        launch_only_scope(|scope| {
            let (page_data, initial_load_status) = setup_page(scope, PageSetupState::Empty, vec![]);
            let failure_cb = get_initial_load_failure_cb(initial_load_status.write_only());

            let error = RequestError::ResolverFailed("Error".to_string());
            failure_cb(error);

            assert_page_data(page_data, |data| {
                assert_eq!(data.pagination_link.get_untracked(), None);
                assert_eq!(data.container_list.get_untracked().len(), 0);
            });
            assert_initial_load_status_failure(initial_load_status);
        });
    }

    #[rstest]
    #[case(WeblabTreatmentString::C, false)]
    #[case(WeblabTreatmentString::T1, true)]
    #[case(WeblabTreatmentString::T2, true)]
    #[case(WeblabTreatmentString::T3, true)]
    #[case(WeblabTreatmentString::T4, true)]
    fn standard_carousel_is_capped(
        #[case] treatment: WeblabTreatmentString,
        #[case] expected_capped: bool,
    ) {
        launch_only_scope(move |scope| {
            let mock_rust_features = MockRustFeaturesBuilder::new()
                .set_east_west_carousel_truncation_treatment(treatment);
            mock_rust_features.build_into_context(scope);

            let standard_carousel_cap = get_standard_carousel_cap(scope);
            assert_eq!(
                standard_carousel_cap.is_some_and(|cap| cap > 0),
                expected_capped
            );
        })
    }

    #[test]
    fn paginate_page_success_cb_updates_signals_and_pending() {
        launch_only_scope(|scope| {
            let (page_data, initial_load_status) =
                setup_page(scope, PageSetupState::Populated, vec![]);
            let pending_signal = page_data.with(|data| data.pagination_pending.to_owned());
            let updatable_signal = store_value(scope, HashSet::new());
            let success_cb = get_paginate_page_success_cb(
                page_data,
                scope,
                pending_signal,
                updatable_signal,
                false,
            );

            pending_signal.set(true);
            let pagination_response = get_paginated_container_list();
            success_cb(pagination_response.clone(), "".to_string());

            assert_page_data(page_data, |data| {
                // initial page load: 10 after filtering
                // pagination: 10 more after filtering
                assert_eq!(data.container_list.get_untracked().len(), 20);
                assert_eq!(
                    data.pagination_link.get_untracked(),
                    pagination_response.paginationLink
                );
            });
            assert_initial_load_status_success(initial_load_status, "home-home-None");
            assert!(!pending_signal.get_untracked());
        });
    }

    #[test]
    fn paginate_page_handles_unsupported_see_more_links() {
        launch_only_scope(|scope| {
            let (page_data, initial_load_status) =
                setup_page(scope, PageSetupState::Populated, vec![]);
            let pending_signal = page_data.with(|data| data.pagination_pending.to_owned());
            let updatable_signal = store_value(scope, HashSet::new());
            let success_cb = get_paginate_page_success_cb(
                page_data,
                scope,
                pending_signal,
                updatable_signal,
                false,
            );

            pending_signal.set(true);
            let pagination_response = get_paginated_container_list_with_unsupported_see_more_link();
            success_cb(pagination_response.clone(), "".to_string());

            assert_page_data(page_data, |data| {
                // initial page load: 10 after filtering
                // pagination: 10 more after filtering
                assert_eq!(data.container_list.get_untracked().len(), 20);
                assert_eq!(
                    data.pagination_link.get_untracked(),
                    pagination_response.paginationLink
                );

                let carousel = data.container_list.get_untracked()[15]
                    .model
                    .get_untracked();

                let standard_carousel = match carousel {
                    ContainerModel::StandardCarousel(c) => c,
                    _ => panic!("Expected standard carousel"),
                }
                .get_untracked();

                let title = standard_carousel
                    .standard_carousel_metadata
                    .get_untracked()
                    .title
                    .get_untracked()
                    .expect("Expected title")
                    .title_text
                    .expect("Expected title");

                assert!(title.starts_with("Explore: Movies to rent"));
                assert!(standard_carousel
                    .standard_carousel_metadata
                    .get_untracked()
                    .see_more_link
                    .get_untracked()
                    .is_none());
            });
            assert_initial_load_status_success(initial_load_status, "home-home-None");
            assert!(!pending_signal.get_untracked());
        });
    }

    #[test]
    fn paginate_page_closure_does_nothing_if_cannot_paginate_or_is_pending() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);

            let page_pagination_link = page_data.with_untracked(|data| data.pagination_link);
            let page_pagination_pending = page_data.with_untracked(|data| data.pagination_pending);

            let mut mock_client = MockNetworkClient::default();
            mock_client
                .expect_paginate()
                .times(0)
                .withf(|_, _, _, _, _| true);
            let client = Rc::new(mock_client);

            let updatable_signal = store_value(scope, HashSet::new());
            let pagination_closure =
                get_paginate_page_closure(client, scope, updatable_signal, false);

            let link = page_pagination_link
                .get_untracked()
                .expect("expected defined link");

            page_pagination_link.set(None);
            pagination_closure(page_data);

            page_pagination_link.set(Some(link));
            page_pagination_pending.set(true);
            pagination_closure(page_data);
        });
    }

    #[test]
    fn paginate_page_closure_calls_paginate_and_updates_pending_if_can_paginate() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);

            let page_pagination_link = page_data.with_untracked(|data| data.pagination_link);
            let page_pagination_pending = page_data.with_untracked(|data| data.pagination_pending);

            let expected_paginatable_component = PaginatableComponent::ContainerList(
                Some(
                    page_pagination_link
                        .get_untracked()
                        .expect("expected defined link"),
                ),
                None,
            );

            let mut mock_client = MockNetworkClient::default();
            mock_client
                .expect_paginate()
                .times(1)
                .withf(move |page, pagination, _, _, _| {
                    page == &RustPage::RUST_COLLECTIONS
                        && pagination == &expected_paginatable_component
                })
                .return_once(|_, _, _, _, _| {});
            let client = Rc::new(mock_client);

            setup_mock_rust_features(scope);
            let updatable_signal = store_value(scope, HashSet::new());
            let pagination_closure =
                get_paginate_page_closure(client, scope, updatable_signal, false);

            pagination_closure(page_data);
            assert!(page_pagination_pending.get_untracked());
        });
    }

    #[test]
    fn paginate_page_success_cb_updates_updatable_items() {
        launch_only_scope(|scope| {
            let (page_data, _initial_load_status) =
                setup_page(scope, PageSetupState::Populated, vec![]);
            let pending_signal = page_data.with(|data| data.pagination_pending.to_owned());
            let updatable_signal = store_value(scope, HashSet::new());
            let success_cb = get_paginate_page_success_cb(
                page_data,
                scope,
                pending_signal,
                updatable_signal.clone(),
                false,
            );

            pending_signal.set(true);
            // Containes 1 LiveLinear card
            let pagination_response = get_paginated_container_list();
            success_cb(pagination_response.clone(), "".to_string());
            assert_eq!(updatable_signal.get_value().len(), 1);
        });
    }

    #[test]
    fn paginate_container_success_cb_updates_signals_and_pending_super_carousel() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                let first_sc = list
                    .iter()
                    .find(|c| matches!(c.model.get_untracked(), ContainerModel::SuperCarousel(_)))
                    .expect("expected a super carousel in the container list");
                first_sc.model
            });
            let ContainerModel::SuperCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a super carousel in the container list");
            };
            let pending_signal = sc_signal
                .get_untracked()
                .super_carousel_metadata
                .get_untracked()
                .pagination_pending;
            let success_cb = get_paginate_container_success_cb(
                carousel_to_paginate_signal,
                scope,
                pending_signal,
                None,
                false,
                false,
            );

            pending_signal.set(true);

            let pagination_response = get_paginated_super_carousel();
            success_cb(pagination_response.clone(), "".to_string());

            let items_signal = sc_signal.get_untracked().items;
            let pagination_link_signal = sc_signal
                .get_untracked()
                .super_carousel_metadata
                .get_untracked()
                .pagination_link;

            // initial page load: 10
            // pagination: 10 more
            assert_eq!(items_signal.get_untracked().len(), 20);
            assert_eq!(
                pagination_link_signal.get_untracked(),
                pagination_response.paginationLink
            );
            assert!(!pending_signal.get_untracked());
        });
    }

    #[test]
    fn paginate_container_success_cb_updates_signals_and_pending_standard_carousel() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                let first_sc = list
                    .iter()
                    .find(|c| {
                        if let ContainerModel::StandardCarousel(_) = c.model.get_untracked() {
                            true
                        } else {
                            false
                        }
                    })
                    .expect("expected a standard carousel in the container list");
                first_sc.model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };
            let pending_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_pending;
            let success_cb = get_paginate_container_success_cb(
                carousel_to_paginate_signal,
                scope,
                pending_signal,
                None,
                false,
                false,
            );

            pending_signal.set(true);

            let pagination_response = get_paginated_standard_carousel();
            success_cb(pagination_response.clone(), "".to_string());

            let items_signal = sc_signal.get_untracked().items;
            let pagination_link_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_link;

            // initial page load: 10
            // pagination: 10 more
            assert_eq!(items_signal.get_untracked().len(), 20);
            assert_eq!(
                pagination_link_signal.get_untracked(),
                pagination_response.paginationLink
            );
            assert!(!pending_signal.get_untracked());
        });
    }

    #[test]
    fn paginate_container_success_cb_updates_signals_and_pending_beard_supported_carousel() {
        launch_only_scope(|scope| {
            let (page_data, _) =
                setup_page(scope, PageSetupState::PopulatedWithBeardSupport, vec![]);
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                let first_bc = list
                    .iter()
                    .find(|c| {
                        if let ContainerModel::BeardSupportedCarousel(_) = c.model.get_untracked() {
                            true
                        } else {
                            false
                        }
                    })
                    .expect("expected a beard supported carousel in the container list");
                first_bc.model
            });
            let ContainerModel::BeardSupportedCarousel(bc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a beard supported carousel in the container list");
            };
            let pending_signal = bc_signal
                .get_untracked()
                .beard_supported_carousel_metadata
                .get_untracked()
                .pagination_pending;
            let success_cb = get_paginate_container_success_cb(
                carousel_to_paginate_signal,
                scope,
                pending_signal,
                None,
                false,
                false,
            );

            pending_signal.set(true);

            let pagination_response = get_paginated_beard_supported_carousel();
            success_cb(pagination_response.clone(), "".to_string());

            let items_signal = bc_signal.get_untracked().items;
            let pagination_link_signal = bc_signal
                .get_untracked()
                .beard_supported_carousel_metadata
                .get_untracked()
                .pagination_link;

            // initial page load: 10
            // pagination: 10 more
            assert_eq!(items_signal.get_untracked().len(), 20);
            assert_eq!(
                pagination_link_signal.get_untracked(),
                pagination_response.paginationLink
            );
            assert!(!pending_signal.get_untracked());
        });
    }

    #[test]
    fn paginate_container_closure_does_nothing_if_cannot_paginate_or_is_pending() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);

            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                let first_sc = list
                    .iter()
                    .find(|c| {
                        if let ContainerModel::StandardCarousel(_) = c.model.get_untracked() {
                            true
                        } else {
                            false
                        }
                    })
                    .expect("expected a standard carousel in the container list");
                first_sc.model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };
            let pending_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_pending;

            let mut mock_client = MockNetworkClient::default();
            mock_client
                .expect_paginate()
                .times(0)
                .withf(|_, _, _, _, _| true);
            let client = Rc::new(mock_client);
            let pagination_closure =
                get_paginate_component_closure(client, scope, None, (|| true).into(), false);

            let pagination_link_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_link;
            let link = pagination_link_signal
                .get_untracked()
                .expect("expected defined link");

            pagination_link_signal.set(None);
            pagination_closure(carousel_to_paginate_signal);

            pagination_link_signal.set(Some(link));
            pending_signal.set(true);
            pagination_closure(carousel_to_paginate_signal);
        });
    }

    #[test]
    fn paginate_container_closure_calls_paginate_and_updates_pending_if_can_paginate() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);

            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                let first_sc = list
                    .iter()
                    .find(|c| {
                        matches!(c.model.get_untracked(), ContainerModel::StandardCarousel(_))
                    })
                    .expect("expected a standard carousel in the container list");
                first_sc.model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };
            let pending_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_pending;

            let pagination_link_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_link;
            let link = pagination_link_signal
                .get_untracked()
                .expect("expected defined link");
            let jic = sc_signal
                .get_untracked()
                .common_carousel_metadata
                .get_untracked()
                .journey_ingress_context
                .get_untracked();
            let expected_paginatable_component =
                PaginatableComponent::StandardCarousel(Some(link), jic);
            let mut mock_client = MockNetworkClient::default();
            mock_client
                .expect_paginate()
                .times(1)
                .withf(move |page, pagination, _, _, _| {
                    page == &RustPage::RUST_COLLECTIONS
                        && pagination == &expected_paginatable_component
                })
                .return_once(|_, _, _, _, _| {});
            let client = Rc::new(mock_client);

            let pagination_closure =
                get_paginate_component_closure(client, scope, None, (|| true).into(), false);

            pagination_closure(carousel_to_paginate_signal);
            assert!(pending_signal.get_untracked());
        });
    }

    #[test]
    fn paginate_container_standard_carousel_shows_see_more_link() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::SeeMore, vec![]);
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                list.get(2).unwrap().model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };

            assert_eq!(sc_signal.get_untracked().items.get_untracked().len(), 2);
            assert!(!matches!(
                sc_signal
                    .get_untracked()
                    .items
                    .get()
                    .get(1)
                    .unwrap()
                    .model
                    .get(),
                StandardCardContainerItemType::SeeMoreLink(_)
            ));

            let pending_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_pending;
            let success_cb = get_paginate_container_success_cb(
                carousel_to_paginate_signal,
                scope,
                pending_signal,
                None,
                false,
                false,
            );

            pending_signal.set(true);

            let pagination_response = get_paginated_standard_carousel_with_see_more();
            success_cb(pagination_response.clone(), "".to_string());

            let pagination_link_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_link;

            assert_eq!(sc_signal.get_untracked().items.get_untracked().len(), 3);
            assert!(matches!(
                sc_signal
                    .get_untracked()
                    .items
                    .get()
                    .get(2)
                    .unwrap()
                    .model
                    .get(),
                StandardCardContainerItemType::SeeMoreLink(_)
            ));
            assert_eq!(
                pagination_link_signal.get_untracked(),
                pagination_response.paginationLink
            );
            assert!(!pending_signal.get_untracked());
        });
    }

    #[test]
    #[serial]
    fn paginate_container_standard_carousel_recycles() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(
                scope,
                PageSetupState::Populated,
                vec![Features::EastWestTruncationExperimentT1],
            );
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                list.get(3).unwrap().model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };

            let initial_items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(
                initial_items.len(),
                10,
                "Expected to have initially have exactly 10 items"
            );
            let client = Rc::new(MockNetworkClient::default());
            let updatable_items = store_value(scope, HashSet::new());

            let paginate_component = get_paginate_component_closure(
                client,
                scope,
                Some(updatable_items),
                (|| true).into(),
                false,
            );

            // 1st Pagination
            let metric_emitter_context = MockMetricEmitter::emit_context();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        ("actionName".to_string(), "CarouselRecycled_1".to_string()),
                    ]),
                )
                .once();
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 20, "Expected to have exactly 20 items");
            assert_recycled_items(items, 0, 10, 10, false);

            // 2nd Pagination
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        ("actionName".to_string(), "CarouselRecycled_2".to_string()),
                    ]),
                )
                .once();
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 30, "Expected to have exactly 30 items");
            assert_recycled_items(items, 0, 20, 10, false);

            // check if we hit upper cap of 200
            for n in 0..16 {
                let expected_action_name = match n {
                    0 => "CarouselRecycled_3",
                    1 => "CarouselRecycled_4",
                    2 => "CarouselRecycled_5",
                    _ => "CarouselRecycled_Unexpected",
                };

                metric_emitter_context
                    .expect()
                    .returning(|_, _, _| {})
                    .with(
                        eq("ComponentAction.Count"),
                        eq(1),
                        eq(vec![
                            ("pageType".to_string(), "Collection".to_string()),
                            ("componentName".to_string(), "StandardCarousel".to_string()),
                            ("actionName".to_string(), expected_action_name.to_string()),
                        ]),
                    )
                    .once();

                paginate_component(carousel_to_paginate_signal);
            }
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 190, "Expected to have exactly 190 items");

            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        (
                            "actionName".to_string(),
                            "CarouselRecycled_Unexpected".to_string(),
                        ),
                    ]),
                )
                .once();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        (
                            "actionName".to_string(),
                            "RecyclingLimitReached".to_string(),
                        ),
                    ]),
                )
                .once();
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 200, "Expected to have exactly 200 items");

            // No further items are added
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        (
                            "actionName".to_string(),
                            "RecyclingLimitReached".to_string(),
                        ),
                    ]),
                )
                .never();
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 200, "Expected to have exactly 200 items");
            assert_recycled_items(items, 0, 190, 10, false);
        });
    }

    #[test]
    #[serial]
    fn paginate_container_standard_carousel_does_not_recycle_if_not_in_top_nav_location() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(
                scope,
                PageSetupState::Populated,
                vec![Features::EastWestTruncationExperimentT1],
            );
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                list.get(3).unwrap().model
            });

            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };
            let pending_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_pending;

            let initial_items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(
                initial_items.len(),
                10,
                "Expected to have initially have exactly 10 items"
            );

            let client = Rc::new(MockNetworkClient::default());
            let updatable_items = store_value(scope, HashSet::new());
            pending_signal.set(true);

            let paginate_component = get_paginate_component_closure(
                client,
                scope,
                Some(updatable_items),
                (|| false).into(),
                false,
            );

            let metric_emitter_context = MockMetricEmitter::emit_context();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        ("actionName".to_string(), "CarouselRecycled_1".to_string()),
                    ]),
                )
                .never();

            // Verify metric for recycling is never emitted
            paginate_component(carousel_to_paginate_signal);
            paginate_component(carousel_to_paginate_signal);
            paginate_component(carousel_to_paginate_signal);
            paginate_component(carousel_to_paginate_signal);
        });
    }

    #[test]
    #[serial]
    fn paginate_container_standard_carousel_recycles_with_see_more_link() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(
                scope,
                PageSetupState::SeeMore,
                vec![Features::EastWestTruncationExperimentT1],
            );
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                list.get(4).unwrap().model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };

            let initial_items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(
                initial_items.len(),
                11,
                "Expected to have initially have exactly 11 items"
            );
            let client = Rc::new(MockNetworkClient::default());
            let updatable_items = store_value(scope, HashSet::new());

            let paginate_component = get_paginate_component_closure(
                client,
                scope,
                Some(updatable_items),
                (|| true).into(),
                false,
            );

            let metric_emitter_context = MockMetricEmitter::emit_context();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        ("actionName".to_string(), "CarouselRecycled_1".to_string()),
                    ]),
                )
                .once();
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 21, "Expected to have exactly 21 items");
            assert_recycled_items(items, 0, 10, 10, true);

            let metric_emitter_context = MockMetricEmitter::emit_context();
            metric_emitter_context
                .expect()
                .returning(|_, _, _| {})
                .with(
                    eq("ComponentAction.Count"),
                    eq(1),
                    eq(vec![
                        ("pageType".to_string(), "Collection".to_string()),
                        ("componentName".to_string(), "StandardCarousel".to_string()),
                        ("actionName".to_string(), "CarouselRecycled_2".to_string()),
                    ]),
                )
                .once();
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 31, "Expected to have exactly 31 items");
            assert_recycled_items(items, 0, 20, 10, true);
        });
    }

    #[test]
    fn paginate_container_standard_carousel_adds_hint_component_once() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(
                scope,
                PageSetupState::Populated,
                vec![Features::EastWestTruncationExperimentT3],
            );
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                list.get(3).unwrap().model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };

            let initial_items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(
                initial_items.len(),
                10,
                "Expected to have initially have exactly 10 items"
            );
            let client = Rc::new(MockNetworkClient::default());
            let updatable_items = store_value(scope, HashSet::new());

            let paginate_component = get_paginate_component_closure(
                client,
                scope,
                Some(updatable_items),
                (|| true).into(),
                false,
            );

            // Hint component is added
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 11, "Expected to have exactly 11 items");
            for i in 0..10 {
                assert!(!matches!(
                    items[i].model.get(),
                    StandardCardContainerItemType::Hint(_)
                ));
            }
            assert!(matches!(
                items[10].model.get(),
                StandardCardContainerItemType::Hint(_)
            ));

            // Hint component is not added again
            paginate_component(carousel_to_paginate_signal);
            let items = sc_signal.get_untracked().items.get_untracked();
            assert_eq!(items.len(), 11, "Expected to have exactly 11 items");
        });
    }

    #[test]
    fn paginate_container_standard_carousel_updates_updatable_items() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                list.get(4).unwrap().model
            });
            let ContainerModel::StandardCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a standard carousel in the container list");
            };

            let pending_signal = sc_signal
                .get_untracked()
                .standard_carousel_metadata
                .get_untracked()
                .pagination_pending;
            let updatable_items = store_value(scope, HashSet::new());
            let success_cb = get_paginate_container_success_cb(
                carousel_to_paginate_signal,
                scope,
                pending_signal,
                Some(updatable_items),
                false,
                false,
            );

            pending_signal.set(true);

            let pagination_response = get_paginated_standard_carousel();
            success_cb(pagination_response.clone(), "".to_string());

            // all linear items should be added to `updatable_items`
            assert_eq!(updatable_items.get_value().len(), 20);
        });
    }

    #[test]
    fn pagination_failure_cb_updates_pending() {
        launch_only_scope(|scope| {
            let (page_data, initial_load_status) =
                setup_page(scope, PageSetupState::Populated, vec![]);

            let pending_signal = page_data.with(|data| data.pagination_pending.to_owned());
            let failure_cb = get_pagination_failure_cb(pending_signal);

            pending_signal.set(true);
            let error = RequestError::ResolverFailed("Error".to_string());
            failure_cb(error);

            assert_page_data(page_data, |data| {
                // from initial page load
                assert_eq!(data.container_list.get_untracked().len(), 10);
            });
            assert_initial_load_status_success(initial_load_status, "home-home-None");
            assert!(!pending_signal.get_untracked());
        });
    }

    #[test]
    fn should_extract_resiliency_page_params_from_location() {
        launch_only_scope(|scope| {
            let location = create_rw_signal(
                scope,
                rust_location!(RUST_COLLECTIONS, {"resiliencySourcePageType" => "home", "resiliencySourcePageId" => "movies"}),
            );

            let page = extract_resiliency_page_params_from_location(location.into());

            assert_eq!(
                page.unwrap(),
                ResiliencyPage {
                    pageType: "home".into(),
                    pageId: "movies".into()
                }
            )
        });
    }

    #[test]
    fn should_not_extract_resiliency_page_params_from_location_when_not_supplied() {
        launch_only_scope(|scope| {
            let location = create_rw_signal(
                scope,
                rust_location!(RUST_COLLECTIONS, {"some-other-param" => "yes"}),
            );

            let page = extract_resiliency_page_params_from_location(location.into());

            assert_eq!(page, None::<ResiliencyPage>)
        });
    }

    #[test]
    fn should_drop_link_cards_for_expandable_super_carousels() {
        use common_transform_types::container_items::{
            CarouselItemData, EntitlementMessage, EntitlementMessageIcons, EntitlementMessaging,
            LinkCard,
        };

        launch_only_scope(|scope| {
            let link_card = LinkCard {
                carouselCardMetadata: CarouselItemData {
                    transformItemId: Some("a mock id".to_string()),
                    title: Some("a mock title".to_string()),
                    synopsis: Some("a mock synopsis".to_string()),
                    action: None,
                    deferredAction: None,
                    actions: vec![],
                    widgetType: Some("a mock widget type".to_string()),
                },
                imageAlternateText: Some("image alternate text".to_string()),
                imageUrl: Some("image url".to_string()),
                backgroundImageUrl: None,
                headerText: Some("header text".to_string()),
                isEntitled: Some(false),
                offerText: Some("offer text".to_string()),
                logoImageUrl: Some("logo image url".to_string()),
                entitlementMessaging: Some(EntitlementMessaging {
                    ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                        message: Some("a mock entitlement message slot message".to_string()),
                        icon: Some(EntitlementMessageIcons::OFFER_ICON),
                    }),
                    TITLE_METADATA_BADGE_SLOT: None,
                    GLANCE_MESSAGE_SLOT: None,
                    HIGH_VALUE_MESSAGE_SLOT: None,
                    HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                    INFORMATIONAL_MESSAGE_SLOT: None,
                    BUYBOX_MESSAGE_SLOT: None,
                    PRODUCT_SUMMARY_SLOT: None,
                    PRODUCT_PROMOTION_SLOT: None,
                }),
                overlayTextPosition: None,
                regulatoryLabel: Some("regulatory label".to_string()),
                description: Some("a mock description".to_string()),
            };

            let converted_card = super_carousel_item_to_item_type_wrapper(
                &SuperCarouselItem::LINK_CARD(link_card),
                None,
                true,
                scope,
                true,
                false,
            );

            assert!(converted_card.is_none());
        });
    }

    #[test]
    fn test_get_super_carousel_has_title_for_pagination() {
        launch_only_scope(|scope| {
            let (page_data, _) = setup_page(scope, PageSetupState::Populated, vec![]);
            let carousel_to_paginate_signal = page_data.with_untracked(|data| {
                let list = data.container_list.get_untracked();
                let first_sc = list
                    .iter()
                    .find(|c| matches!(c.model.get_untracked(), ContainerModel::SuperCarousel(_)))
                    .expect("expected a super carousel in the container list");
                first_sc.model
            });
            let ContainerModel::SuperCarousel(sc_signal) =
                carousel_to_paginate_signal.get_untracked()
            else {
                panic!("expected a super carousel in the container list");
            };
            assert!(get_super_carousel_has_title(&sc_signal.get()));
        });
    }

    fn setup_mock_rust_features(scope: Scope) {
        MockRustFeaturesBuilder::new()
            .set_is_prime_student_free_trial_enabled(false)
            .set_is_prime_student_free_trial_enabled(false)
            .set_is_multiview_discovery_cx_enabled(false)
            .set_is_linear_stations_in_hero_enabled(true)
            .build_into_context(scope);
    }

    #[test]
    fn initial_load_success_with_promo_banner() {
        launch_only_app_context(move |ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_parsed_page(get_populated_collections_page_with_promo_banner())
                .with_location_signal_from_default_params();
            let page_controller = setup.to_controller();
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            let modal = setup
                .controller_signals
                .page_data
                .get_untracked()
                .container_list
                .get_untracked()[3]
                .model
                .get_untracked();
            let promo_banner_is_present = matches!(modal, ContainerModel::PromoBanner(_));
            assert_eq!(promo_banner_is_present, true);
        });
    }

    #[test]
    fn initial_load_success_with_linear_station_hero() {
        launch_only_app_context(move |ctx| {
            let page_data_and_status = setup_page(ctx.scope, PageSetupState::Empty, vec![]);
            let setup = SuccessCallbackTestSetup::new(ctx)
                .with_page_data_and_status(page_data_and_status.0, page_data_and_status.1)
                .with_parsed_page(get_populated_collections_page_with_linear_station_hero())
                .with_location_signal_from_default_params();
            let page_controller = setup.to_controller();
            let success_cb =
                get_initial_load_success_cb(Rc::new(page_controller), 1, "home-home".into());

            let args = setup.to_success_cb_args();
            success_cb(
                args.parsed_page_from_network.clone(),
                args.transform_name,
                args.manipulate_page_data,
                args.timestamp,
            );

            let ContainerModel::StandardHero(standard_hero) = setup
                .controller_signals
                .page_data
                .get_untracked()
                .container_list
                .get_untracked()[0]
                .model
                .get_untracked()
            else {
                panic!("Expected a standard hero");
            };

            let hero_items = standard_hero.get_untracked().items.get_untracked();
            assert_eq!(hero_items.len(), 1);
            assert_eq!(
                hero_items[0].swift_content_type.clone().unwrap(),
                "linearStationCard"
            );
        });
    }

    #[rstest]
    #[serial]
    #[case::single_item_treatment_not_set(false, WeblabTreatmentString::C, false)]
    #[serial]
    #[case::multiple_items_treatment_not_set(true, WeblabTreatmentString::C, false)]
    #[serial]
    #[case::single_item_treatment_set(false, WeblabTreatmentString::T2, true)]
    #[serial]
    #[case::multiple_items_treatment_set(true, WeblabTreatmentString::T2, false)]
    fn test_update_modal_with_edcx_low_res_display(
        #[case] use_multiple_items: bool,
        #[case] treatment: WeblabTreatmentString,
        #[case] is_modal_populated: bool,
    ) {
        launch_only_app_context(move |ctx| {
            let scope = ctx.scope();
            let mock_rust_features =
                MockRustFeaturesBuilder::new().set_home_region_setting_treatment(treatment);
            mock_rust_features.build_into_context(scope);

            let modal_data = create_rw_signal(scope, vec![]);

            let _mock_network_client_edcx_context = setup_edcx_mocks(&ctx);

            let populated_collections_page = if use_multiple_items {
                get_populated_collections_page_with_edcx_modal_multiple_items()
            } else {
                get_populated_collections_page_with_edcx_modal_single_item()
            };

            update_modal_data(
                scope,
                &ctx,
                &modal_data.write_only(),
                &populated_collections_page,
            );

            assert_eq!(modal_data.get().len() > 0, is_modal_populated);
        });
    }

    #[test]
    fn entity_carousel_to_container_type_respects_sports_favorites_flag() {
        launch_only_scope(|scope| {
            // Create a basic EntityCarousel for testing
            let landing_action =
                Action::create_transition_landing(Some("test_landing_page".to_string()));
            let entity_carousel = EntityCarouselDeserialization {
                containerMetadata: ContainerMetadata {
                    id: "test_container".to_string(),
                    offerType: None,
                    entitlement: None,
                    analytics: Default::default(),
                    tags: vec!["test_tags_0".to_string()],
                    badges: None,
                },
                facet: None,
                seeMore: None,
                title: Some("Test Carousel".to_string()),
                items: vec![common_transform_types::resiliency::WithResiliency::Ok(
                    EntityCarouselItem::SPORTS_CARD(SportsCard {
                        carouselCardMetadata: CarouselItemData {
                            transformItemId: Some("test_id".to_string()),
                            title: Some("Test Title".to_string()),
                            synopsis: None,
                            action: Some(landing_action),
                            deferredAction: None,
                            actions: vec![],
                            widgetType: None,
                        },
                        backgroundImage: Some("test_image.jpg".to_string()),
                        backgroundColor: None,
                        isFavorited: Some(true),
                        entityType: Some("TEAM".to_string()),
                    }),
                )],
                presentationCue: PresentationCue::Circle,
                journeyIngressContext: None,
                paginationLink: None,
            };

            // Test with sports favorites enabled
            {
                let mock_rust_features =
                    MockRustFeaturesBuilder::new().set_is_sports_favorites_enabled(true);
                mock_rust_features.build_into_context(scope);

                let container_type = entity_carousel_to_container_type(&entity_carousel, scope);
                assert!(
                    container_type.is_some(),
                    "Should return Some when sports favorites is enabled"
                );

                let container = container_type.unwrap();
                assert_eq!(container.id, "test_container");

                match container.model.get_untracked() {
                    ContainerModel::EntityCarousel(_) => (),
                    _ => panic!("Expected EntityCarousel container model"),
                }
            }

            // Test with sports favorites disabled
            {
                let mock_rust_features =
                    MockRustFeaturesBuilder::new().set_is_sports_favorites_enabled(false);
                mock_rust_features.build_into_context(scope);

                let container_type = entity_carousel_to_container_type(&entity_carousel, scope);
                assert!(
                    container_type.is_none(),
                    "Should return None when sports favorites is disabled"
                );
            }
        });
    }

    #[test]
    fn test_schedule_carousel_parsing() {
        launch_only_app_context(|ctx| {
            let scope = ctx.scope();

            // Load and parse the full sports collections page
            let populated_collections_page = collections_response_sports_edge_parser(
                include_str!("../test_assets/sports_edge_collections_page_valid_response.json")
                    .to_string(),
            )
            .map(|result| match result {
                DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
            })
            .unwrap();

            // Find the schedule carousel container
            let schedule_container = populated_collections_page
                .containerList
                .iter()
                .find_map(|container| {
                    if let WithResiliency::Ok(Container::SCHEDULE_CAROUSEL(schedule)) = container {
                        Some(schedule)
                    } else {
                        None
                    }
                })
                .expect("No schedule carousel found in test data");

            // Test the conversion
            let result = schedule_carousel_to_container_type(schedule_container, scope, false);

            assert!(result.is_some());
            if let Some(ContainerType { model, .. }) = result {
                if let ContainerModel::ScheduleCarousel(carousel_model) = model.get_untracked() {
                    let model = carousel_model.get_untracked();

                    // Test common metadata
                    let common_metadata = model.common_carousel_metadata.get_untracked();
                    assert_eq!(common_metadata.id, "SomeSwiftId");

                    // Test schedule metadata
                    let schedule_metadata = model.schedule_carousel_metadata.get_untracked();
                    assert_eq!(
                        schedule_metadata
                            .title
                            .get_untracked()
                            .map(|t| t.title_text),
                        Some(Some("Schedule".to_string()))
                    );

                    // Test items
                    let items = model.items.get_untracked();
                    assert!(!items.is_empty());
                    assert_eq!(items.len(), 3);

                    // Verify buttons
                    assert!(matches!(
                        items.first(),
                        Some(ScheduleCarouselUIItem::Button(_))
                    ));
                    assert!(matches!(
                        items.last(),
                        Some(ScheduleCarouselUIItem::Button(_))
                    ));

                    // Verify initial focus
                    let initial_focus = schedule_metadata.initial_focus_index.get_untracked();
                    assert_eq!(initial_focus, 2);
                } else {
                    panic!("Expected ScheduleCarousel model");
                }
            }
        });
    }
}
