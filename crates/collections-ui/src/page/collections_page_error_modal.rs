use amzn_fable_tokens::{FableColor, FableSpacing};
use app_reporting::kpi_reporting::report_cx_fatal;
use fableous::{
    buttons::primary_button::PrimaryButtonVariant, typography::typography::*, utils::get_ignx_color,
};
use fableous::{
    buttons::primary_button::*,
    common_string_ids::{
        AV_LRC_GENERIC_ERROR_TITLE, AV_LRC_LANDING_PAGE_EMPTY_CAROUSEL_LIST_ERROR_MESSAGE,
        AV_LRC_RETRY_BUTTON_TEXT,
    },
};
use ignx_compositron::{compose, Composer};
use ignx_compositron::{
    prelude::*,
    text::{LocalizedText, TextContent},
};
use location::RustPage;
use navigation_menu::ui::utility_nav::UTILITY_NAV_COLLAPSED_WIDTH;
use std::rc::Rc;

pub(crate) const COLLECTIONS_PAGE_ERROR_MODAL_TEST_ID: &str = "collections-page-error";

#[Composer]
pub fn CollectionsPageErrorModal(ctx: &AppContext, on_retry: Rc<dyn Fn()>) -> StackComposable {
    let error_title = TextContent::LocalizedText(LocalizedText::new(AV_LRC_GENERIC_ERROR_TITLE));

    let error_message = TextContent::LocalizedText(LocalizedText::new(
        AV_LRC_LANDING_PAGE_EMPTY_CAROUSEL_LIST_ERROR_MESSAGE,
    ));

    let retry_button = PrimaryButtonVariant::TextSize400(TextContent::LocalizedText(
        LocalizedText::new(AV_LRC_RETRY_BUTTON_TEXT),
    ));

    report_cx_fatal(RustPage::RUST_COLLECTIONS);

    compose! {
        Stack() {
            Column() {
                TypographyHeading600(content: error_title)

                TypographyBody400(content: error_message)
                .width(798.0)

                Row() {
                    PrimaryButton(variant: retry_button)
                    .padding(Padding::all(12.0))
                    .on_select(move || on_retry())
                }
                .padding(Padding::vertical(14.0)) // until SDK supports margin - SIM: https://issues.amazon.com/issues/LRCP-4260
            }
            .width(fableous::SCREEN_WIDTH)
            .height(fableous::SCREEN_HEIGHT)
            .background_color(get_ignx_color(FableColor::BACKGROUND))
            .padding(Padding::new(UTILITY_NAV_COLLAPSED_WIDTH, 0.0, 172.0, 0.0))
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
            .test_id(COLLECTIONS_PAGE_ERROR_MODAL_TEST_ID)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::assert_node_exists;
    use std::rc::Rc;

    #[test]
    fn renders() {
        launch_test(
            |ctx| {
                compose! {
                    CollectionsPageErrorModal(on_retry: Rc::new(|| {})).test_id("collections-page-error-modal")
                }
            },
            |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let modal = tree.find_by_test_id("collections-page-error-modal");
                assert_node_exists!(&modal);
            },
        )
    }
}
