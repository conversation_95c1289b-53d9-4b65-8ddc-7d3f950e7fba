use super::{
    dynamic_params::{
        get_message_presentation_params_for_sports_edge,
        get_title_actions_view_params_for_sports_edge, MessagePresentationParams,
        TitleActionsViewParams,
    },
    types::RestoreCollectionPageFocusRequest,
};
use crate::network::dynamic_params::{
    get_additional_dynamic_features_for_sports_edge, get_widget_scheme_for_sports_edge,
};
use crate::network::types::CollectionInitialRequest;
use app_config::AppConfigContext;
use container_types::network::container_query_params_constructor;
use ignx_compositron::device_information::DeviceInformation;
use ignx_compositron::reactive::Scope;
#[cfg(not(any(test, feature = "mock_network")))]
use ignx_compositron::timezone::get_timezone_id;
use location::RustPage;
use network::common::lrc_edge_constants::{
    CollectionInitialTypes, LRCEdgeTransforms, FEATURE_SCHEME,
};
#[cfg(any(test, feature = "mock_network"))]
use network::test_assets::mocks::get_timezone_id;
use network::{URLBuilder, URLBuilderError, URLBuilderResult};
use serde_json::json;
use std::rc::Rc;

// GRCOV_BEGIN_COVERAGE
fn create_params_for_base_request<'a>(
    page_source: &'a RustPage,
    params: &'a CollectionInitialRequest,
) -> Result<Vec<(&'a str, String)>, URLBuilderError> {
    let mut query_params = container_query_params_constructor(
        page_source,
        params.container_request_base.page_id.as_str(),
        params.container_request_base.page_type.as_str(),
        &params.container_request_base.widget_scheme,
        &params.container_request_base.presentation_scheme,
        &params.container_request_base.taps_roles,
        &params.container_request_base.additional_dynamic_features,
        &params.container_request_base.additional_client_features,
    )?;
    if let Some(token) = params.service_token.clone() {
        query_params.extend([("serviceToken", token)])
    }
    if let Some(category_redesign) = params.is_category_page_redesign_enabled {
        let val = if category_redesign { "true" } else { "false" };
        query_params.extend([("isCategoryPageRedesignEnabled", val.to_string())])
    }
    if let Some(voice_hints) = params.include_voice_hints {
        let val = if voice_hints { "true" } else { "false" };
        query_params.extend([("includeVoiceHints", val.to_string())])
    }

    Ok(query_params)
}

pub(crate) fn collection_initial_url_constructor(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
    page_source: &RustPage,
    collection_initial_type: CollectionInitialTypes,
    params: &CollectionInitialRequest,
) -> URLBuilderResult {
    let query_params = create_params_for_base_request(page_source, params)?;
    URLBuilder::for_lrc_edge(
        &LRCEdgeTransforms::CollectionInitial(collection_initial_type),
        query_params,
        false,
        device_info,
        app_config,
    )
}

pub(crate) fn restore_collection_page_focus_url_constructor(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
    page_source: &RustPage,
    collection_initial_type: CollectionInitialTypes,
    params: &RestoreCollectionPageFocusRequest,
) -> URLBuilderResult {
    let mut query_params =
        create_params_for_base_request(page_source, &params.collection_initial_base)?;

    query_params.extend([("containerId", params.container_id.clone())]);
    query_params.extend([("transformItemId", params.transform_item_id.clone())]);
    query_params.extend([(
        "fallbackToContainer",
        params.fallback_to_container.to_string(),
    )]);
    query_params.extend([("skipPagination", params.skip_pagination.to_string())]);

    URLBuilder::for_lrc_edge(
        &LRCEdgeTransforms::RestoreCollectionPageFocus(collection_initial_type),
        query_params,
        false,
        device_info,
        app_config,
    )
}

pub(crate) fn collection_page_sports_edge_url_constructor(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
    params: &CollectionInitialRequest,
    query_hash: &str,
    scope: Scope,
) -> URLBuilderResult {
    let dynamic_features: Vec<String> = params
        .container_request_base
        .additional_dynamic_features
        .iter()
        .chain(get_additional_dynamic_features_for_sports_edge(scope).iter())
        .map(|feature| Into::<&'static str>::into(feature.clone()).to_string())
        .collect();

    let timezone_id = match get_timezone_id() {
        None => serde_json::Value::Null,
        Some(value) => serde_json::Value::String(value),
    };
    let os_locale = match device_info.os_locale.to_owned() {
        None => serde_json::Value::Null,
        Some(value) => serde_json::Value::String(value),
    };
    let title_actions_view_params: TitleActionsViewParams =
        get_title_actions_view_params_for_sports_edge(scope, params);
    let message_presentation_params: MessagePresentationParams =
        get_message_presentation_params_for_sports_edge(scope);

    let extensions = json!(
        {
            "persistedQuery": {
                "version": 1,
                "sha256Hash": String::from(query_hash)
            }
        }
    );

    let variables = json!({
        "input": {
            "adsToken": serde_json::Value::Null,
            "deviceContext": {
                "deviceTypeId": device_info.device_type_id,
                "osLocale": os_locale,
                "timeZoneId": timezone_id,
                "variant": "matrix"
            },
            "journeyIngressContext": serde_json::Value::Null,
            "pageContext": {
                "pageType": params.container_request_base.page_type.clone(),
                "pageId": params.container_request_base.page_id.clone()
            },
            "priorityLevel": "critical",
            "requestConfiguration": {
                "dynamicFeatures": dynamic_features,
                "featureScheme": FEATURE_SCHEME.to_string(),
                "widgetScheme": String::from(get_widget_scheme_for_sports_edge(scope,params.container_request_base.widget_scheme.clone()))
            },
            "traceToken": serde_json::Value::Null
        },
        "time_zone": timezone_id,
        "presentation_scheme": params.container_request_base.presentation_scheme.clone(),
        "title_actions_view_params_roles": title_actions_view_params.roles,
        "message_presentation_params_roles": message_presentation_params.roles
    });

    let query_params: Vec<(&str, String)> = vec![
        serde_json::to_string(&extensions)
            .ok()
            .map(|ext| ("extensions", ext)),
        serde_json::to_string(&variables)
            .ok()
            .map(|vars| ("variables", vars)),
        Some(("operationName", "LRCGetCollectionPage".to_string())),
    ]
    .into_iter()
    .flatten()
    .collect();

    URLBuilder::for_sports_edge(
        query_params,
        false,
        Rc::clone(&device_info),
        Rc::clone(&app_config),
    )
}

#[cfg(test)]
mod tests {
    use super::*;
    use app_config::MockAppConfig;
    use container_types::network::ContainerRequestBase;
    use ignx_compositron::app::launch_only_scope;
    use network::common::lrc_edge_constants::{DynamicFeature, WidgetSchemes};
    use network::common::test_utils::{
        assert_query_params, assert_required_device_proxy_query_params,
        assert_required_lrc_edge_query_params,
    };
    use network::URLBuilderError;

    fn create_collections_request_params() -> CollectionInitialRequest {
        CollectionInitialRequest {
            container_request_base: ContainerRequestBase {
                page_id: "AN ID".to_string(),
                page_type: "A TYPE".to_string(),
                widget_scheme: WidgetSchemes::LRCRustCollectionsV1,
                presentation_scheme: "living-room-react-focus".to_string(),
                taps_roles: vec![],
                additional_dynamic_features: vec![
                    DynamicFeature::GLOBAL_TRAINING,
                    DynamicFeature::LinearStationInAllCarousels,
                ],
                additional_client_features: vec![],
            },
            service_token: None,
            is_category_page_redesign_enabled: None,
            include_voice_hints: None,
        }
    }

    fn setup() -> (Rc<DeviceInformation>, Rc<MockAppConfig>) {
        let device_info = DeviceInformation::new();
        let mut app_config = MockAppConfig::default();
        app_config
            .expect_get_ux_locale()
            .returning(|| "en_GB".into());
        app_config
            .expect_get_geo_location()
            .returning(|| Some("IE".into()));
        app_config
            .expect_get_supported_locales()
            .returning(|| vec!["de_DE".into(), "en_US".into(), "fr_FR".into()]);
        app_config
            .expect_get_base_url()
            .return_once(|| Some("https://base_url.com".to_string()));

        (Rc::new(device_info), Rc::new(app_config))
    }

    #[test]
    fn collection_initial_url_errors_when_page_unsupported() {
        let params = create_collections_request_params();
        let (device_info, app_config) = setup();
        let res = collection_initial_url_constructor(
            device_info,
            app_config,
            &RustPage::RUST_EMPTY,
            CollectionInitialTypes::Landing,
            &params,
        );
        match res {
            Ok(_) => panic!("should have errored, but didn't"),
            Err(e) => match e {
                URLBuilderError::UnsupportedPage => {}
                _ => panic!("errored, but returned incorrect error. Expected AppHttpRequestError::UnsupportedPage, received {:?}", e)
            }
        }
    }

    #[test]
    fn collection_initial_url_required_params() {
        let params = create_collections_request_params();
        let (device_info, app_config) = setup();
        let res = collection_initial_url_constructor(
            device_info,
            app_config,
            &RustPage::RUST_COLLECTIONS,
            CollectionInitialTypes::Landing,
            &params,
        );
        match res {
            Ok(url) => {
                let params = vec![
                    ("pageId", "AN ID"),
                    ("pageType", "A TYPE"),
                    ("widgetScheme", "lrc-rust-collections-v1"),
                    ("featureScheme", "react-v5"),
                    ("decorationScheme", "lr-decoration-gen4-v4"),
                    ("presentationScheme", "living-room-react-focus"),
                    ("clientPage", "CollectionsPage"),
                    ("clientFeatures", "DynamicBadging,Remaster,UseDynamicDatumParameters,UseV12TitleActionsView"),
                    ("tentpoleSSMEnabled", "true"),
                    ("isBrandingEnabled", "true"),
                    ("regulatoryLabel", "true"),
                    ("useEpisodicSynopsis", "true"),
                    ("dynamicFeatures", "AppleTVODEnabled,CLIENT_DECORATION_ENABLE_DAAPI,DigitalBundlePvcPvc,GLOBAL_TRAINING,LinearStationInAllCarousels,LinearTitles,LiveTab,RustLRSupported,SupportsImageTextLinkTextInStandardHero"),
                    ("timeZoneId", "MockTimeZoneId"),
                ];
                assert_query_params(&url.to_url_string(), params, true);
                assert_required_lrc_edge_query_params(&url.to_url_string(), true);
                assert_required_device_proxy_query_params(&url.to_url_string(), true);
                let expected_url_path = format!(
                    "{}/lrcedge/getDataByJavaTransform/v1/lr/collections/collectionsPageInitial",
                    "https://base_url.com"
                );
                assert!(
                    url.to_url_string().contains(expected_url_path.as_str()),
                    "url is {}",
                    url.to_url_string()
                );
            }
            Err(e) => panic!("shouldn't have errored, but did with {:?}", e),
        }
    }

    #[test]
    fn collection_initial_url_optional_params() {
        let mut params = create_collections_request_params();
        params.include_voice_hints = Some(true);
        params.service_token = Some("A SERVICE TOKEN".to_string());
        params.is_category_page_redesign_enabled = Some(true);
        params.container_request_base.taps_roles = vec!["role1".to_string(), "role2".to_string()];
        let (device_info, app_config) = setup();
        let res = collection_initial_url_constructor(
            device_info,
            app_config,
            &RustPage::RUST_COLLECTIONS,
            CollectionInitialTypes::Landing,
            &params,
        );
        match res {
            Ok(url) => {
                let params = vec![
                    ("serviceToken", "A SERVICE TOKEN"),
                    ("isCategoryPageRedesignEnabled", "true"),
                    ("includeVoiceHints", "true"),
                    ("roles", "role1,role2"),
                ];
                assert_query_params(&url.to_url_string(), params, true);
            }
            Err(e) => panic!("shouldn't have errored, but did with {:?}", e),
        }
    }

    #[test]
    fn collection_page_sports_edge_with_required_params() {
        launch_only_scope(|scope| {
            let params = create_collections_request_params();
            let (device_info, app_config) = setup();
            let res = collection_page_sports_edge_url_constructor(
                device_info,
                app_config,
                &params,
                "mock-query-hash",
                scope,
            );

            match res {
                Ok(url) => {
                    let variables: String = json!({
                        "input": {
                            "adsToken": null,
                            "deviceContext": {
                                "deviceTypeId": "A71I8788P1ZV8",
                                "osLocale": "GB",
                                "timeZoneId": "MockTimeZoneId",
                                "variant": "matrix"
                            },
                            "journeyIngressContext": null,
                            "pageContext": {
                                "pageId": "AN ID",
                                "pageType": "A TYPE"
                            },
                            "priorityLevel": "critical",
                            "requestConfiguration": {
                                "dynamicFeatures": [
                                    "GLOBAL_TRAINING",
                                    "LinearStationInAllCarousels"
                                ],
                                "featureScheme": "react-v5",
                                "widgetScheme": "lrc-rust-collections-v1"
                            },
                            "traceToken": null
                        },
                        "message_presentation_params_roles": "new-title-badge-supported,coming-soon-title-badge-supported,av-liveliness-with-unavailable-message-supported",
                        "presentation_scheme": "living-room-react-focus",
                        "title_actions_view_params_roles": "new-title-badge-supported,coming-soon-title-badge-supported",
                        "time_zone": "MockTimeZoneId",
                    }).to_string();

                    let params = vec![
                        (
                            "extensions",
                            "{\"persistedQuery\":{\"sha256Hash\":\"mock-query-hash\",\"version\":1}}",
                        ),
                        ("variables", &variables),
                        ("operationName", "LRCGetCollectionPage"),
                    ];
                    assert_query_params(&url.to_url_string(), params, true);
                    assert_required_device_proxy_query_params(&url.to_url_string(), true);
                    let expected_url_path = format!(
                        "{}/cdp/sportsgraphqlgateway/graphql",
                        "https://base_url.com"
                    );
                    assert!(
                        url.to_url_string().contains(expected_url_path.as_str()),
                        "url is {}",
                        url.to_url_string()
                    );
                }
                Err(e) => panic!("shouldn't have errored, but did with {:?}", e),
            }
        })
    }

    #[test]
    fn collection_page_sports_edge_with_empty_dynamic_params() {
        launch_only_scope(|scope| {
            let mut params = create_collections_request_params();
            params.container_request_base.additional_dynamic_features = Vec::new();
            let (device_info, app_config) = setup();
            let res = collection_page_sports_edge_url_constructor(
                device_info,
                app_config,
                &params,
                "mock-query-hash",
                scope,
            );

            match res {
                Ok(url) => {
                    let variable: String = json!({
                        "input": {
                            "adsToken": null,
                            "deviceContext": {
                                "deviceTypeId": "A71I8788P1ZV8",
                                "osLocale": "GB",
                                "timeZoneId": "MockTimeZoneId",
                                "variant": "matrix"
                            },
                            "journeyIngressContext": null,
                            "pageContext": {
                                "pageId": "AN ID",
                                "pageType": "A TYPE"
                            },
                            "priorityLevel": "critical",
                            "requestConfiguration": {
                                "dynamicFeatures": [],
                                "featureScheme": "react-v5",
                                "widgetScheme": "lrc-rust-collections-v1"
                            },
                            "traceToken": null
                        },
                        "message_presentation_params_roles": "new-title-badge-supported,coming-soon-title-badge-supported,av-liveliness-with-unavailable-message-supported",
                        "presentation_scheme": "living-room-react-focus",
                        "title_actions_view_params_roles": "new-title-badge-supported,coming-soon-title-badge-supported",
                        "time_zone": "MockTimeZoneId",
                        }).to_string();

                    let params = vec![
                        (
                            "extensions",
                            "{\"persistedQuery\":{\"sha256Hash\":\"mock-query-hash\",\"version\":1}}",
                        ),
                        ("variables", &variable),
                        ("operationName", "LRCGetCollectionPage"),
                    ];
                    assert_query_params(&url.to_url_string(), params, true);
                    assert_required_device_proxy_query_params(&url.to_url_string(), true);
                    let expected_url_path = format!(
                        "{}/cdp/sportsgraphqlgateway/graphql",
                        "https://base_url.com"
                    );
                    assert!(
                        url.to_url_string().contains(expected_url_path.as_str()),
                        "url is {}",
                        url.to_url_string()
                    );
                }
                Err(e) => panic!("shouldn't have errored, but did with {:?}", e),
            }
        });
    }
}
