#[cfg(all(not(test), not(feature = "example_data")))]
use crate::network::collection_initial::CollectionsRequests;
use crate::network::types::CollectionInitialRequest;
use crate::page::controller::CollectionsPageControlling;
#[cfg(any(test, feature = "example_data"))]
use crate::test_assets::mocks::MockNetworkClient as NetworkClient;
use beekeeper::types::{
    BeekeeperFallbackPageParams, LrcEdgeBeekeeperPageParams, ResiliencyPage,
    SportEdgeBeekeeperPageParams, StorefrontFallbackPageFulfilledAction,
};
use cache::ExpirableLruCacheRc;
use collection_types::network_types::CollectionsPage;
use ignx_compositron::prelude::Scope;
use ignx_compositron::time::Instant;
use network::common::lrc_edge_constants::CollectionInitialTypes;
#[cfg(all(not(test), not(feature = "example_data")))]
use network::NetworkClient;
use network::RequestError;
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use rust_features::WeblabTreatmentString;
use sports_edge_types::network::get_collection_page_types::lrc_get_collection_page::{
    OPERATION_NAME, SPORTS_EDGE_QUERY_HASH,
};
use std::rc::Rc;

// TODO: Replace this with action target based
macro_rules!  static_set {
    ($fn_name:ident, [$($item:expr),* $(,)?]) => {
        fn $fn_name(value: &str) -> bool {
            match value {
                $( $item => true, )*
                _ => false,
            }
        }
    };
}
static_set!(
    is_page_id_allowlisted,
    [
        // NBA
        "amzn1.dv.icid.cf75b42a-68cd-4a1c-993a-a0b3f0575c5f",
        "amzn1.dv.icid.bb89b408-68d6-4d51-9584-82b5ccec4ae9",
        "amzn1.dv.icid.ea93477e-dbe2-491a-887c-664c21cc931b",
        "amzn1.dv.icid.2bd075e5-44cd-420f-a3b0-f4fe05bff87a",
        "amzn1.dv.icid.c0086968-725f-4866-b234-f3c9dae90547",
        "amzn1.dv.icid.af6203c0-a376-4215-a271-68e88de37683",
        "amzn1.dv.icid.78ee5f7b-20a0-442e-94a1-49c937fa5dce",
        "amzn1.dv.icid.ebb4f137-deb5-4e99-a4fd-60d835d54f4a",
        "amzn1.dv.icid.db75c759-d842-4dd6-a378-947527d0e44a",
        "amzn1.dv.icid.2027cbc0-6647-4424-8333-07a48d55ef2a",
        "amzn1.dv.icid.108a4afd-633c-42ac-b9c8-c17b9be32d7e",
        "amzn1.dv.icid.b2563605-b823-4d6e-afa0-f88084674639",
        "amzn1.dv.icid.5c7a2c0b-b476-4bf9-90ae-b1cef8491916",
        "amzn1.dv.icid.8006fb81-f8c5-4b94-945b-6e4b0ef58391",
        "amzn1.dv.icid.e0a8697e-3149-40ec-ad6b-a1e2c1c3e528",
        "amzn1.dv.icid.37ffa6cb-83b6-41ec-9914-c10a6cf4d039",
        "amzn1.dv.icid.249b15e7-32b4-45e1-bd1c-65e879f09760",
        "amzn1.dv.icid.9371daa7-2eb7-4879-9677-8681fe37e6a6",
        "amzn1.dv.icid.aa73ac76-56b2-4631-bc52-696c07537f05",
        "amzn1.dv.icid.6964779e-0f3d-4596-9489-65151de1c46e",
        "amzn1.dv.icid.8ba2302e-05ba-448d-a0c3-5295fc96ec83",
        "amzn1.dv.icid.cc05255b-5e1e-454a-8a5f-b999af7fd1f1",
        "amzn1.dv.icid.0a271086-a0d7-413d-8708-9851842c0c0e",
        "amzn1.dv.icid.16dfc34e-4497-48dc-afa7-3e54c563bd96",
        "amzn1.dv.icid.0c977950-dbd6-4bc3-bfdb-03687423d94a",
        "amzn1.dv.icid.26502f2a-cd10-4046-a56d-298511d9c0fa",
        "amzn1.dv.icid.b91cf83d-7cdd-4cca-8641-8738c965ca2e",
        "amzn1.dv.icid.6569eb43-f12a-4a84-9d1c-f3efb943ddd6",
        "amzn1.dv.icid.0eaf2d7c-ec65-4db8-a0b6-b707152f30fc",
        "amzn1.dv.icid.e56e8088-f013-45d2-a4db-215ef04f75fa",
        "amzn1.dv.icid.3b735de5-9f19-4f7e-9493-02394d932b6d",
        // WNBA
        "amzn1.dv.icid.9128d05b-2c57-491d-89da-c49d779c5db4",
        "amzn1.dv.icid.33a86492-d1d0-4ebf-84da-85d2a9cd0849",
        "amzn1.dv.icid.2aafddb0-daaa-42b0-b15b-6919a0d10b68",
        "amzn1.dv.icid.6a49c4eb-98ad-48c8-b0b6-ac6375c9577f",
        "amzn1.dv.icid.07919776-8004-4068-b21b-ef2d2b527273",
        "amzn1.dv.icid.6e19fdea-3ae8-4964-8a4b-87a1bfbc7107",
        "amzn1.dv.icid.86949198-c110-45df-839f-1867fe4a96b9",
        "amzn1.dv.icid.90bbb1aa-48ff-4ff5-a13d-0b7ae8fa47ae",
        "amzn1.dv.icid.9b006456-a1ae-4904-b5af-a1d35f3bcb89",
        "amzn1.dv.icid.1cfb5da5-a31e-458c-a767-0d5d7a0ff4b4",
        "amzn1.dv.icid.5357c832-c64d-4e29-8b72-b60a34640321",
        "amzn1.dv.icid.8dff2f0b-0c2c-4ab7-856b-ff912102d337",
        "amzn1.dv.icid.de708c75-3500-42f3-9027-b175acbdb4a6",
        "amzn1.dv.icid.acea19e2-5f0a-409d-b47f-b6cdf91a79e4"
    ]
);

const TOURNAMENT_PAGE_TYPE: &str = "tournament";

pub fn get_collection_page<S, F, R>(
    client: Rc<NetworkClient>,
    scope: Scope,
    success_callback: S,
    failure_callback: F,
    resiliency_callback: R,
    page_controller: Rc<dyn CollectionsPageControlling>,
    collection_initial_type: CollectionInitialTypes,
    params: &CollectionInitialRequest,
    cache: ExpirableLruCacheRc<String, CollectionsPage>,
    page_type: String,
    page_id: String,
    redirect: Option<ResiliencyPage>,
    is_reload: bool,
) where
    S: FnOnce(CollectionsPage, Instant) + 'static,
    F: FnOnce(RequestError) + 'static,
    R: FnOnce(StorefrontFallbackPageFulfilledAction<CollectionsPage>) + 'static,
{
    if should_use_sports_edge(scope, params) {
        client.get_collection_page_sports_edge(
            success_callback,
            failure_callback,
            resiliency_callback,
            page_controller,
            collection_initial_type,
            params,
            cache,
            BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                pageType: page_type,
                pageId: page_id,
                query_hash: SPORTS_EDGE_QUERY_HASH.to_string(),
                operation_name: OPERATION_NAME.to_string(),
            }),
            redirect,
            is_reload,
        )
    } else {
        client.collection_initial(
            success_callback,
            failure_callback,
            resiliency_callback,
            page_controller,
            collection_initial_type,
            params,
            cache,
            BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                pageType: page_type,
                pageId: page_id,
                pageSection: None,
            }),
            redirect,
            is_reload,
        )
    }
}

fn should_use_sports_edge(scope: Scope, params: &CollectionInitialRequest) -> bool {
    let enable_sports_edge_weblab_treatment = {
        try_use_rust_features(scope).map_or_else(WeblabTreatmentString::default, |features| {
            features.get_enable_sports_edge_treatment_string()
        })
    };
    if enable_sports_edge_weblab_treatment == WeblabTreatmentString::T1
        && is_page_id_allowlisted(params.container_request_base.page_id.as_str())
    {
        true
    } else if enable_sports_edge_weblab_treatment == WeblabTreatmentString::T2
        && params.container_request_base.page_type == TOURNAMENT_PAGE_TYPE
    {
        true
    } else {
        false
    }
}

#[cfg(test)]
mod test {
    use super::*;

    #[test]
    fn test_is_page_id_allowlisted() {
        // Test all allowlisted NBA page IDs
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.cf75b42a-68cd-4a1c-993a-a0b3f0575c5f"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.bb89b408-68d6-4d51-9584-82b5ccec4ae9"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.ea93477e-dbe2-491a-887c-664c21cc931b"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.2bd075e5-44cd-420f-a3b0-f4fe05bff87a"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.c0086968-725f-4866-b234-f3c9dae90547"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.af6203c0-a376-4215-a271-68e88de37683"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.78ee5f7b-20a0-442e-94a1-49c937fa5dce"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.ebb4f137-deb5-4e99-a4fd-60d835d54f4a"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.db75c759-d842-4dd6-a378-947527d0e44a"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.2027cbc0-6647-4424-8333-07a48d55ef2a"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.108a4afd-633c-42ac-b9c8-c17b9be32d7e"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.b2563605-b823-4d6e-afa0-f88084674639"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.5c7a2c0b-b476-4bf9-90ae-b1cef8491916"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.8006fb81-f8c5-4b94-945b-6e4b0ef58391"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.e0a8697e-3149-40ec-ad6b-a1e2c1c3e528"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.37ffa6cb-83b6-41ec-9914-c10a6cf4d039"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.249b15e7-32b4-45e1-bd1c-65e879f09760"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.9371daa7-2eb7-4879-9677-8681fe37e6a6"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.aa73ac76-56b2-4631-bc52-696c07537f05"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.6964779e-0f3d-4596-9489-65151de1c46e"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.8ba2302e-05ba-448d-a0c3-5295fc96ec83"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.cc05255b-5e1e-454a-8a5f-b999af7fd1f1"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.0a271086-a0d7-413d-8708-9851842c0c0e"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.16dfc34e-4497-48dc-afa7-3e54c563bd96"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.0c977950-dbd6-4bc3-bfdb-03687423d94a"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.26502f2a-cd10-4046-a56d-298511d9c0fa"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.b91cf83d-7cdd-4cca-8641-8738c965ca2e"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.6569eb43-f12a-4a84-9d1c-f3efb943ddd6"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.0eaf2d7c-ec65-4db8-a0b6-b707152f30fc"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.e56e8088-f013-45d2-a4db-215ef04f75fa"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.3b735de5-9f19-4f7e-9493-02394d932b6d"
        ));

        // Test all allowlisted WNBA page IDs
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.9128d05b-2c57-491d-89da-c49d779c5db4"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.33a86492-d1d0-4ebf-84da-85d2a9cd0849"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.2aafddb0-daaa-42b0-b15b-6919a0d10b68"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.6a49c4eb-98ad-48c8-b0b6-ac6375c9577f"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.07919776-8004-4068-b21b-ef2d2b527273"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.6e19fdea-3ae8-4964-8a4b-87a1bfbc7107"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.86949198-c110-45df-839f-1867fe4a96b9"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.90bbb1aa-48ff-4ff5-a13d-0b7ae8fa47ae"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.9b006456-a1ae-4904-b5af-a1d35f3bcb89"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.1cfb5da5-a31e-458c-a767-0d5d7a0ff4b4"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.5357c832-c64d-4e29-8b72-b60a34640321"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.8dff2f0b-0c2c-4ab7-856b-ff912102d337"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.de708c75-3500-42f3-9027-b175acbdb4a6"
        ));
        assert!(is_page_id_allowlisted(
            "amzn1.dv.icid.acea19e2-5f0a-409d-b47f-b6cdf91a79e4"
        ));

        // Test non-allowlisted page IDs
        assert!(!is_page_id_allowlisted("amzn1.dv.icid.not-in-the-list"));
        assert!(!is_page_id_allowlisted(""));
        assert!(!is_page_id_allowlisted("random-string"));
    }

    #[test]
    fn test_static_set_macro() {
        // Test the macro with empty set
        static_set!(is_empty_set, []);
        assert!(!is_empty_set("anything"));
        assert!(!is_empty_set(""));

        // Test the macro with a single item
        static_set!(is_single_item, ["only_item"]);
        assert!(is_single_item("only_item"));
        assert!(!is_single_item("other_item"));

        // Test the macro with multiple items
        static_set!(is_in_set, ["item1", "item2", "item3"]);
        assert!(is_in_set("item1"));
        assert!(is_in_set("item2"));
        assert!(is_in_set("item3"));
        assert!(!is_in_set("item4"));
    }
}
