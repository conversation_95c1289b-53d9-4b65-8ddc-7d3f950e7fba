use consumption_only::ConsumptionOnlyStore;
#[cfg(not(test))]
use ignx_compositron::device_information::DeviceInformation;
use ignx_compositron::prelude::use_context;
use ignx_compositron::reactive::Scope;
use network::common::lrc_edge_constants::{
    ClientFeature, DynamicFeature, DynamicNetworkParameters, WidgetSchemes,
};
#[cfg(test)]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(test))]
use rust_features::try_use_rust_features;
use rust_features::WeblabTreatmentString;
use std::rc::Rc;
use taps_parameters::TapsParameterStore;

use crate::network::types::CollectionInitialRequest;

pub(crate) const DEFAULT_PRESENTATION_SCHEME: &str = "living-room-react-focus";

pub(crate) trait CollectionsDynamicNetworkParameters {
    fn get_all(scope: Scope) -> DynamicNetworkParameters {
        DynamicNetworkParameters {
            presentation_scheme: Self::get_presentation_scheme(scope),
            widget_scheme: Self::get_widget_scheme(scope),
            taps_roles: Self::get_taps_roles(scope),
            dynamic_features: Self::get_dynamic_features(scope),
            client_features: Self::get_client_features(scope),
        }
    }

    /// Has to have different code for test in order to be mocked out properly
    fn get_os_name(_scope: Scope) -> String {
        #[cfg(not(test))]
        {
            let device_information = DeviceInformation::new();
            device_information.os_name
        }
        #[cfg(test)]
        {
            use_context::<String>(_scope).unwrap_or("Unknown".to_string())
        }
    }

    fn get_dynamic_features(scope: Scope) -> Vec<DynamicFeature> {
        let mut features = vec![
            DynamicFeature::HERO_IMAGE_OPTIONAL,
            DynamicFeature::ENABLE_CSIR,
            DynamicFeature::PromotionalBannerSupported,
            DynamicFeature::LinearStationInAllCarousels,
        ];

        if Self::get_os_name(scope) == "Android" {
            if let Some(consumption_only_store) = use_context::<Rc<dyn ConsumptionOnlyStore>>(scope)
            {
                if consumption_only_store.get_prime_add_on() {
                    features.push(DynamicFeature::SuppressPVAdFreePrice)
                }
            }
        }

        if let Some(rust_features) = try_use_rust_features(scope) {
            if rust_features.is_global_training_enabled() {
                features.push(DynamicFeature::GLOBAL_TRAINING);
            }
            if let Some(f) = DynamicFeature::discovery_assistant_supported(scope) {
                features.push(f);
            }
            if rust_features.is_hide_this_remove_from_cw_enabled() {
                features.push(DynamicFeature::RemoveFromContinueWatching);
            }
            if rust_features.is_sports_favorites_enabled() {
                features.push(DynamicFeature::SportsFavoritesEnabled);
            }
            if rust_features.is_enable_syml_carousel_and_widget_scheme_enabled() {
                features.push(DynamicFeature::SupportChannelItemDecoration);
            }
            if rust_features.is_linear_stations_in_hero_enabled() {
                features.push(DynamicFeature::LinearStationsInHero);
            }
            if rust_features.is_enable_api_link_action_support_enabled() {
                features.push(DynamicFeature::API_LINK_ACTION_SUPPORTED);
            }
        }
        features
    }

    fn get_client_features(scope: Scope) -> Vec<ClientFeature> {
        let mut client_features = vec![];

        if let Some(rust_features) = try_use_rust_features(scope) {
            if rust_features.is_prime_student_free_trial_enabled() {
                client_features.push(ClientFeature::PrimeStudentFreeTrial)
            }
            if rust_features.is_multiview_discovery_cx_enabled() {
                client_features.push(ClientFeature::MultiView)
            }
            if rust_features.is_enable_syml_carousel_and_widget_scheme_enabled() {
                client_features.push(ClientFeature::EnableFixedChannelBundle);
            }
        }

        client_features
    }

    fn get_taps_roles(scope: Scope) -> Vec<String> {
        if let Some(taps_params_store) = use_context::<Rc<dyn TapsParameterStore>>(scope) {
            taps_params_store.get_collections_roles()
        } else {
            vec![]
        }
    }

    fn get_presentation_scheme(scope: Scope) -> String {
        if let Some(taps_params_store) = use_context::<Rc<dyn TapsParameterStore>>(scope) {
            taps_params_store.get_presentation_scheme(true)
        } else {
            DEFAULT_PRESENTATION_SCHEME.to_string()
        }
    }

    fn get_widget_scheme(scope: Scope) -> WidgetSchemes {
        match try_use_rust_features(scope) {
            Some(features)
                if features
                    .is_linear_station_favoriting_and_airing_upgrade_widget_scheme_enabled() =>
            {
                WidgetSchemes::LRCRustCollectionsV3_4
            }
            Some(features) if features.is_enable_syml_carousel_and_widget_scheme_enabled() => {
                WidgetSchemes::LRCRustCollectionsV3_3
            }
            Some(features) if features.is_sports_favorites_enabled() => {
                // Sports Favorites takes precedence, which contains all contents from V3_1 plus entityCarousel related content
                WidgetSchemes::LRCRustCollectionsV3_2
            }
            Some(features) if features.is_promo_banner_channel_decor_widget_scheme_enabled() => {
                WidgetSchemes::LRCRustCollectionsV3_1
            }
            _ => WidgetSchemes::LRCRustCollectionsV3,
        }
    }
}

impl CollectionsDynamicNetworkParameters for DynamicNetworkParameters {}

/// Dynamic (determined during runtime) parameters for the `TitleActionsView` datum.
/// This struct is only consumed by pages powered by the graphql edge service. For other pages,
/// these parameters are set in the LRC Edge serivce.
pub(crate) struct TitleActionsViewParams {
    pub(crate) roles: String,
}

pub(crate) fn get_title_actions_view_params_for_sports_edge(
    scope: Scope,
    params: &CollectionInitialRequest,
) -> TitleActionsViewParams {
    let mut roles: Vec<String> = params.container_request_base.taps_roles.clone();

    roles.push(String::from("new-title-badge-supported"));
    roles.push(String::from("coming-soon-title-badge-supported"));

    if let Some(rust_features) = try_use_rust_features(scope) {
        if rust_features.is_prime_student_free_trial_enabled() {
            roles.push(String::from("psft-supported"))
        }
        if rust_features.is_multiview_discovery_cx_enabled() {
            roles.push(String::from("supports-multi-view"))
        }
    }

    TitleActionsViewParams {
        roles: roles.join(","),
    }
}

/// Dynamic (determined during runtime) parameters for the `MessagePresentation` datum.
/// This struct is only consumed by pages powered by the graphql edge service. For other pages,
/// these parameters are set in the LRC Edge serivce.
pub(crate) struct MessagePresentationParams {
    pub(crate) roles: String,
}

pub(crate) fn get_message_presentation_params_for_sports_edge(
    scope: Scope,
) -> MessagePresentationParams {
    let mut roles: Vec<String> = vec![];

    roles.push(String::from("new-title-badge-supported"));
    roles.push(String::from("coming-soon-title-badge-supported"));
    roles.push(String::from(
        "av-liveliness-with-unavailable-message-supported",
    ));

    if let Some(rust_features) = try_use_rust_features(scope) {
        if rust_features.is_prime_student_free_trial_enabled() {
            roles.push(String::from("psft-supported"))
        }
    }

    MessagePresentationParams {
        roles: roles.join(","),
    }
}

pub(crate) fn get_widget_scheme_for_sports_edge(
    scope: Scope,
    current_widget_schemes: WidgetSchemes,
) -> WidgetSchemes {
    match try_use_rust_features(scope) {
        Some(features)
            if features.get_enable_sports_edge_treatment_string() != WeblabTreatmentString::C =>
        {
            WidgetSchemes::LRCRustCollectionsSportsV1
        }
        _ => current_widget_schemes,
    }
}

pub(crate) fn get_additional_dynamic_features_for_sports_edge(scope: Scope) -> Vec<DynamicFeature> {
    let mut features = Vec::new();
    if let Some(rust_features) = try_use_rust_features(scope) {
        // todo: use enableSportsEdge Weblab to unblock QA testing, will create a new weblab to for schedule carousel only
        if rust_features.get_enable_sports_edge_treatment_string() != WeblabTreatmentString::C {
            features.push(DynamicFeature::Maverick);
        }
    }
    features
}

#[cfg(test)]
pub mod test {
    use super::*;
    use app_config::test_utils::MockAppConfigBuilder;
    use consumption_only::MockConsumptionOnlyStore;
    use container_types::network::ContainerRequestBase;
    use ignx_compositron::{app::launch_only_scope, reactive::provide_context};
    use rstest::*;
    use rust_features::{MockRustFeaturesBuilder, WeblabTreatmentString};
    use taps_parameters::MockTapsParameterStore;

    #[rstest]
    #[case(true, true, true, true, WidgetSchemes::LRCRustCollectionsV3_4)]
    #[case(true, true, true, false, WidgetSchemes::LRCRustCollectionsV3_3)]
    #[case(true, true, false, false, WidgetSchemes::LRCRustCollectionsV3_2)]
    #[case(true, false, false, false, WidgetSchemes::LRCRustCollectionsV3_2)]
    #[case(false, true, false, false, WidgetSchemes::LRCRustCollectionsV3_1)]
    #[case(false, false, false, false, WidgetSchemes::LRCRustCollectionsV3)]
    pub fn returns_correct_widget_scheme(
        #[case] sports_favorites_enabled: bool,
        #[case] promo_banner_channel_decor_widget_enabled: bool,
        #[case] syml_enabled: bool,
        #[case] linear_station_favoriting_and_airing_upgrade_enabled: bool,
        #[case] expected_scheme: WidgetSchemes,
    ) {
        launch_only_scope(move |scope| {
            MockRustFeaturesBuilder::new()
                .set_is_sports_favorites_enabled(sports_favorites_enabled)
                .set_is_promo_banner_channel_decor_widget_scheme_enabled(
                    promo_banner_channel_decor_widget_enabled,
                )
                .set_is_enable_syml_carousel_and_widget_scheme_enabled(syml_enabled)
                .set_is_linear_station_favoriting_and_airing_upgrade_widget_scheme_enabled(
                    linear_station_favoriting_and_airing_upgrade_enabled,
                )
                .build_into_context(scope);
            assert_eq!(
                DynamicNetworkParameters::get_widget_scheme(scope),
                expected_scheme
            );
        });
    }

    #[test]
    fn provides_taps_roles_from_store() {
        launch_only_scope(|scope| {
            let mut mock_parameters = MockTapsParameterStore::new();
            mock_parameters
                .expect_get_collections_roles()
                .times(1)
                .returning(|| vec!["role1".to_string(), "role2".to_string()]);
            provide_context::<Rc<dyn TapsParameterStore>>(scope, Rc::new(mock_parameters));

            let taps_roles = DynamicNetworkParameters::get_taps_roles(scope);
            assert_eq!(taps_roles, vec!["role1".to_string(), "role2".to_string()]);
        })
    }

    #[test]
    fn provides_default_taps_roles_if_store_is_not_available() {
        launch_only_scope(|scope| {
            let taps_roles = DynamicNetworkParameters::get_taps_roles(scope);
            let expected_roles: Vec<String> = vec![];
            assert_eq!(taps_roles, expected_roles);
        })
    }

    #[test]
    fn provides_presentation_scheme_from_store() {
        launch_only_scope(|scope| {
            let mut mock_parameters = MockTapsParameterStore::new();
            mock_parameters
                .expect_get_presentation_scheme()
                .times(1)
                .return_const("presentation_scheme".to_string());
            provide_context::<Rc<dyn TapsParameterStore>>(scope, Rc::new(mock_parameters));

            let presentation_scheme = DynamicNetworkParameters::get_presentation_scheme(scope);
            assert_eq!(presentation_scheme, "presentation_scheme".to_string());
        })
    }

    #[test]
    fn provides_default_presentation_scheme_if_store_is_not_available() {
        launch_only_scope(|scope| {
            let presentation_scheme = DynamicNetworkParameters::get_presentation_scheme(scope);
            assert_eq!(presentation_scheme, DEFAULT_PRESENTATION_SCHEME.to_string());
        })
    }

    enum DynamicFeatureOptions {
        GlobalTraining,
        UXLocaleEnUs,
        Android,
        ConsumptionOnlyPrimeAddOn,
        HideThisRemoveFromCW,
        SportsFavorites,
        SupportChannelItemDecoration,
        LinearStationsInHero,
        ApiLinkActionSupported,
    }

    impl DynamicFeatureOptions {
        fn setup_with_features(features: &[DynamicFeatureOptions], scope: Scope) {
            let (
                mut global_training,
                mut ux_locale,
                mut android_tv_device,
                mut consumption_only_prime_add_on,
                mut hide_this_remove_from_cw,
                mut sports_favorites,
                mut enable_syml,
                mut linear_stations_in_hero,
                mut enable_api_link,
            ) = (
                false,
                "en-GB".to_string(),
                false,
                false,
                false,
                false,
                false,
                false,
                false,
            );

            for feature in features {
                match feature {
                    DynamicFeatureOptions::GlobalTraining => global_training = true,
                    DynamicFeatureOptions::UXLocaleEnUs => ux_locale = "en_US".to_string(),
                    DynamicFeatureOptions::Android => android_tv_device = true,
                    DynamicFeatureOptions::ConsumptionOnlyPrimeAddOn => {
                        consumption_only_prime_add_on = true
                    }
                    DynamicFeatureOptions::HideThisRemoveFromCW => hide_this_remove_from_cw = true,
                    DynamicFeatureOptions::SportsFavorites => sports_favorites = true,
                    DynamicFeatureOptions::SupportChannelItemDecoration => enable_syml = true,
                    DynamicFeatureOptions::LinearStationsInHero => linear_stations_in_hero = true,
                    DynamicFeatureOptions::ApiLinkActionSupported => enable_api_link = true,
                }
            }

            if android_tv_device {
                provide_context::<String>(scope, "Android".to_string());
            }

            let mut mock_consumption_only = MockConsumptionOnlyStore::default();
            mock_consumption_only
                .expect_get_prime_add_on()
                .return_const(consumption_only_prime_add_on);
            provide_context::<Rc<dyn ConsumptionOnlyStore>>(scope, Rc::new(mock_consumption_only));

            let add_features = |mock_feature_builder: MockRustFeaturesBuilder| {
                mock_feature_builder
                    .set_is_global_training_enabled(global_training)
                    .set_is_rust_collection_enabled(true)
                    .set_home_default_focus_experiment_treatment(WeblabTreatmentString::C)
                    .set_is_hide_this_remove_from_cw_enabled(hide_this_remove_from_cw)
                    .set_is_sports_favorites_enabled(sports_favorites)
                    .set_is_enable_syml_carousel_and_widget_scheme_enabled(enable_syml)
                    .set_is_linear_stations_in_hero_enabled(linear_stations_in_hero)
                    .set_is_enable_api_link_action_support_enabled(enable_api_link)
            };

            mock_rust_feature(scope, add_features);

            MockAppConfigBuilder::new()
                .set_ux_locale(ux_locale)
                .build_into_context(scope);
        }
    }

    enum ClientFeatureOptions {
        MultiView,
        EnableFixedChannelBundle,
    }

    impl ClientFeatureOptions {
        fn setup_with_features(features: &[ClientFeatureOptions], scope: Scope) {
            let mut multiview = false;
            let mut fixed_channel_bundle = false;

            for feature in features {
                match feature {
                    ClientFeatureOptions::MultiView => multiview = true,
                    ClientFeatureOptions::EnableFixedChannelBundle => fixed_channel_bundle = true,
                }
            }

            let add_features = |mock_feature_builder: MockRustFeaturesBuilder| {
                mock_feature_builder
                    .set_is_multiview_discovery_cx_enabled(multiview)
                    .set_is_enable_syml_carousel_and_widget_scheme_enabled(fixed_channel_bundle)
            };

            mock_rust_feature(scope, add_features);
        }
    }

    #[rstest]
    #[case(vec![], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels])]
    #[case(vec![DynamicFeatureOptions::GlobalTraining], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::GLOBAL_TRAINING])]
    #[case(vec![DynamicFeatureOptions::UXLocaleEnUs], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::DiscoveryAssistantSupported])]
    #[case(vec![DynamicFeatureOptions::GlobalTraining, DynamicFeatureOptions::UXLocaleEnUs], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::GLOBAL_TRAINING, DynamicFeature::DiscoveryAssistantSupported])]
    #[case(vec![DynamicFeatureOptions::Android], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels])]
    #[case(vec![DynamicFeatureOptions::Android, DynamicFeatureOptions::ConsumptionOnlyPrimeAddOn], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR,  DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::SuppressPVAdFreePrice])]
    #[case(vec![DynamicFeatureOptions::HideThisRemoveFromCW], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::RemoveFromContinueWatching])]
    #[case(vec![DynamicFeatureOptions::SportsFavorites], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::SportsFavoritesEnabled])]
    #[case(vec![DynamicFeatureOptions::SupportChannelItemDecoration], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::SupportChannelItemDecoration])]
    #[case(vec![DynamicFeatureOptions::LinearStationsInHero], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::LinearStationsInHero])]
    #[case(vec![DynamicFeatureOptions::ApiLinkActionSupported], vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::API_LINK_ACTION_SUPPORTED])]
    fn provides_additional_dynamic_features(
        #[case] options: Vec<DynamicFeatureOptions>,
        #[case] expected_additional_dynamic_features: Vec<DynamicFeature>,
    ) {
        launch_only_scope(move |scope| {
            DynamicFeatureOptions::setup_with_features(&options, scope);
            let features = DynamicNetworkParameters::get_dynamic_features(scope);

            assert_eq!(features, expected_additional_dynamic_features);
        })
    }

    #[rstest]
    #[case(vec![], vec![])]
    #[case(vec![ClientFeatureOptions::MultiView], vec![ClientFeature::MultiView])]
    #[case(vec![ClientFeatureOptions::EnableFixedChannelBundle], vec![ClientFeature::EnableFixedChannelBundle])]
    fn provides_additional_client_features(
        #[case] options: Vec<ClientFeatureOptions>,
        #[case] expected_additional_client_features: Vec<ClientFeature>,
    ) {
        launch_only_scope(move |scope| {
            ClientFeatureOptions::setup_with_features(&options, scope);
            let features = DynamicNetworkParameters::get_client_features(scope);

            assert_eq!(features, expected_additional_client_features);
        })
    }

    fn mock_rust_feature(
        scope: Scope,
        mut add_features: impl FnMut(MockRustFeaturesBuilder) -> MockRustFeaturesBuilder,
    ) {
        let mock_rust_features = add_features(MockRustFeaturesBuilder::new());
        mock_rust_features.build_into_context(scope);
    }

    fn make_collections_request_params(taps_roles: Vec<String>) -> CollectionInitialRequest {
        CollectionInitialRequest {
            container_request_base: ContainerRequestBase {
                page_id: "test-page-id".to_string(),
                page_type: "test-page-type".to_string(),
                widget_scheme: WidgetSchemes::LRCRustCollectionsV1,
                presentation_scheme: "living-room-react-focus".to_string(),
                taps_roles,
                additional_dynamic_features: vec![],
                additional_client_features: vec![],
            },
            service_token: None,
            is_category_page_redesign_enabled: None,
            include_voice_hints: None,
        }
    }

    #[derive(PartialEq)]
    enum RustFeatureOptions {
        PrimeStudentFreeTrial,
        MultiView,
    }

    const DEFAULT_TITLE_ACTIONS_VIEW_PARAMS_ROLES: &str =
        "new-title-badge-supported,coming-soon-title-badge-supported";

    #[rstest]
    #[case(vec![], vec![], DEFAULT_TITLE_ACTIONS_VIEW_PARAMS_ROLES.to_string())]
    #[case(vec!["test-taps-roles".to_string()], vec![], format!("test-taps-roles,{DEFAULT_TITLE_ACTIONS_VIEW_PARAMS_ROLES}"))]
    #[case(vec![], vec![RustFeatureOptions::PrimeStudentFreeTrial, RustFeatureOptions::MultiView], format!("{DEFAULT_TITLE_ACTIONS_VIEW_PARAMS_ROLES},psft-supported,supports-multi-view"))]
    fn provide_title_actions_view_params_for_sports_edge(
        #[case] taps_roles: Vec<String>,
        #[case] rust_feature_enabled: Vec<RustFeatureOptions>,
        #[case] expected_roles: String,
    ) {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, |mock_feature_builder| {
                mock_feature_builder
                    .set_is_prime_student_free_trial_enabled(
                        rust_feature_enabled.contains(&RustFeatureOptions::PrimeStudentFreeTrial),
                    )
                    .set_is_multiview_discovery_cx_enabled(
                        rust_feature_enabled.contains(&RustFeatureOptions::MultiView),
                    )
            });
            let params = make_collections_request_params(taps_roles);
            let TitleActionsViewParams { roles } =
                get_title_actions_view_params_for_sports_edge(scope, &params);
            assert_eq!(roles, expected_roles);
        });
    }

    const DEFAULT_MESSAGE_PRESENTATION_PARAMS_ROLES: &str = "new-title-badge-supported,coming-soon-title-badge-supported,av-liveliness-with-unavailable-message-supported";

    #[rstest]
    #[case(vec![], DEFAULT_MESSAGE_PRESENTATION_PARAMS_ROLES.to_string())]
    #[case(vec![RustFeatureOptions::PrimeStudentFreeTrial], format!("{DEFAULT_MESSAGE_PRESENTATION_PARAMS_ROLES},psft-supported"))]
    fn provide_message_presentation_params_for_sports_edge(
        #[case] rust_feature_enabled: Vec<RustFeatureOptions>,
        #[case] expected_roles: String,
    ) {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, |mock_feature_builder| {
                mock_feature_builder.set_is_prime_student_free_trial_enabled(
                    rust_feature_enabled.contains(&RustFeatureOptions::PrimeStudentFreeTrial),
                )
            });
            let MessagePresentationParams { roles } =
                get_message_presentation_params_for_sports_edge(scope);
            assert_eq!(roles, expected_roles);
        })
    }

    #[test]
    fn get_additional_dynamic_features_for_sports_edge_test() {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, |mock_feature_builder| {
                mock_feature_builder.set_enable_sports_edge_enabled(WeblabTreatmentString::T1)
            });
            let dynamic_feature = get_additional_dynamic_features_for_sports_edge(scope);
            assert_eq!(dynamic_feature, vec![DynamicFeature::Maverick])
        })
    }

    #[test]
    fn get_widget_scheme_for_sports_edge_test() {
        launch_only_scope(move |scope| {
            mock_rust_feature(scope, |mock_feature_builder| {
                mock_feature_builder.set_enable_sports_edge_enabled(WeblabTreatmentString::T1)
            });
            let widget_schemes =
                get_widget_scheme_for_sports_edge(scope, WidgetSchemes::LRCProfilesV1);
            assert_eq!(widget_schemes, WidgetSchemes::LRCRustCollectionsSportsV1)
        })
    }
}
