#![allow(
    nonstandard_style,
    missing_docs,
    clippy::upper_case_acronyms,
    reason = "compatibility"
)]

use cfg_test_attr_derive::derive_test_only;
use container_types::network::ContainerRequestBase;

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct FocusPosition {
    pub container: usize,
    pub item: usize,
    pub sub_index: Option<usize>,
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct CachedFocusData {
    pub position: FocusPosition,
    pub container_id: String,
    pub item_id: String,
    pub should_skip_pagination: bool,
}

/// The parameters needed for a collection initial request
#[derive_test_only(Debug)]
pub struct CollectionInitialRequest {
    /// Common Collection
    pub container_request_base: ContainerRequestBase,
    /// Swift service token
    pub service_token: Option<String>,
    /// LRC Edge param to allow category page responses in collections page
    pub is_category_page_redesign_enabled: Option<bool>,
    /// LRC Edge param to allow voice hints
    pub include_voice_hints: Option<bool>,
}

#[derive_test_only(Debug)]
pub struct RestoreCollectionPageFocusRequest {
    pub collection_initial_base: CollectionInitialRequest,
    // Focused container ID.
    pub container_id: String,
    // Focused item ID.
    pub transform_item_id: String,
    // Should restore to container even if item not found.
    pub fallback_to_container: bool,
    // Should skip pagination.
    pub skip_pagination: bool,
}
