use collection_types::network_types::CollectionsPage;
use ignx_compositron::prelude::AppContext;
use network::common::LRCEdgeResponse;
use network::{common::DeviceProxyResponse, RequestError};
use network_parser::core::network_parse_from_str;
use sports_edge_parser::parser::collections_response_sports_edge_parser as sports_edge_parser;

// TODO: implement parser with fallbacks/error handling
pub fn collections_response_parser_callback(
    _ctx: &AppContext,
    response: String,
    _status_code: i32,
    cb: Box<dyn FnOnce(Result<DeviceProxyResponse<CollectionsPage>, RequestError>)>,
) {
    cb(collections_response_parser(response))
}

pub fn collections_response_parser(
    response: String,
) -> Result<DeviceProxyResponse<CollectionsPage>, RequestError> {
    network_parse_from_str(&response).map_err(RequestError::DeserializationFailed)
}

pub fn collections_response_sports_edge_parser_callback(
    _ctx: &AppContext,
    response: String,
    _status_code: i32,
    cb: Box<dyn FnOnce(Result<DeviceProxyResponse<CollectionsPage>, RequestError>)>,
) {
    cb(collections_response_sports_edge_parser(response))
}

pub fn collections_response_sports_edge_parser(
    response: String,
) -> Result<DeviceProxyResponse<CollectionsPage>, RequestError> {
    sports_edge_parser(response)
        .map(LRCEdgeResponse::with_resource)
        .map(DeviceProxyResponse::LRCEdgeResponse)
}

/// We don't have a way to get a real response with *all* possible options populated right now, so
/// it is possible that some of these types are mistaken.
/// To aid debugging if there is a problem, use this test with the response that will not parse.
/// You may need to narrow the scope of the parsing as the error message is not always super
/// verbose.
/// For example, if there is an issue in a card type, it will likely just point you to the index
/// of the container in the container list. You can pull the container out of the response, change
/// the type of the parsing result and narrow down where the issue is further. You can repeat this
/// process until the error message stops becoming more granular, at which point you'll need to
/// compare that subset of the response to the types defined and see where the mismatch is.
#[cfg(test)]
mod test {
    use super::*;
    use rstest::*;

    #[rstest]
    #[case(include_str!("../test_assets/collections_page_full_response.json"))]
    #[case(include_str!("../test_assets/collections_page_with_survey.json"))]
    fn test_deserialize_collections_page(#[case] path: &str) {
        let test_page = path.to_string();

        let resource = match collections_response_parser(test_page).unwrap() {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        };

        println!("{:#?}", resource);
    }

    #[rstest]
    #[case(include_str!("../test_assets/sports_edge_collections_page_valid_response.json"))]
    fn test_deserialize_sports_edge_collections_page(#[case] path: &str) {
        let test_page = path.to_string();

        let resource = match collections_response_sports_edge_parser(test_page).unwrap() {
            DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
            DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
        };

        println!("{:#?}", resource);
    }
}
