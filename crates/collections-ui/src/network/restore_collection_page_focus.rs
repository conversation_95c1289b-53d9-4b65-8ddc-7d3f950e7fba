use std::rc::Rc;

use super::{
    parser::collections_response_parser_callback, types::RestoreCollectionPageFocusRequest,
    url_constructors::restore_collection_page_focus_url_constructor,
};
use crate::page::controller::CollectionsPageControlling;
use crate::reporting::app_events::{CollectionsAppEvents, CollectionsTransforms};
use collection_types::network_types::CollectionsPage;
use ignx_compositron::{network::http::HttpMethod, time::Instant};
use network::{
    common::lrc_edge_constants::{CollectionInitialTypes, LRCEdgeTransforms},
    NetworkClient, RequestError,
};

pub trait RestoreCollectionPageFocusRequests {
    #[allow(dead_code, reason = "trait")]
    fn restore_collection_page_focus<S, F>(
        &self,
        success_callback: S,
        failure_callback: F,
        page_controller: Rc<dyn CollectionsPageControlling>,
        collection_initial_type: CollectionInitialTypes,
        params: &RestoreCollectionPageFocusRequest,
    ) where
        S: FnOnce(CollectionsPage, Instant) + 'static,
        F: FnOnce(RequestError) + 'static;
}

impl RestoreCollectionPageFocusRequests for NetworkClient {
    fn restore_collection_page_focus<S, F>(
        &self,
        success_callback: S,
        failure_callback: F,
        page_controller: Rc<dyn CollectionsPageControlling>,
        collection_initial_type: CollectionInitialTypes,
        params: &RestoreCollectionPageFocusRequest,
    ) where
        S: FnOnce(CollectionsPage, Instant) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
        let url = match restore_collection_page_focus_url_constructor(
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
            page_controller.page_source(),
            collection_initial_type.clone(),
            params,
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!("CollectionsRequests#restore_collection_page_focus: Cannot create the request url. Error: {:?}", e);
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let request_start_time = Instant::now();

        let success_cb = {
            let page_controller = Rc::clone(&page_controller);
            move |v: CollectionsPage, data_fetch_time| {
                let network_call_duration = Instant::now() - request_start_time;
                page_controller.report_app_event(CollectionsAppEvents::NetworkSuccess {
                    transform: CollectionsTransforms::RestoreCollectionPageFocusV2,
                    time: network_call_duration.as_millis() as u64,
                });
                success_callback(v, data_fetch_time);
            }
        };

        let failure_cb = {
            let page_controller = Rc::clone(&page_controller);
            move |e: RequestError, _| {
                page_controller.report_app_event(CollectionsAppEvents::NetworkFailure {
                    transform: CollectionsTransforms::RestoreCollectionPageFocusV2,
                });
                failure_callback(e);
            }
        };

        let transform = LRCEdgeTransforms::RestoreCollectionPageFocus(collection_initial_type);
        let crc_page_id = Some(
            params
                .collection_initial_base
                .container_request_base
                .page_id
                .clone(),
        );

        let builder = self
            .builder(url, HttpMethod::Get, "restoreCollectionPageFocusV2")
            .with_lrc_edge_headers(&transform, crc_page_id)
            .with_parser(collections_response_parser_callback)
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb));

        builder.execute();
    }
}

#[cfg(test)]
mod tests {
    use crate::network::types::CollectionInitialRequest;

    use super::*;
    use crate::test_assets::mocks::MockControllerBuilder;
    use app_config::{AppConfigContext, MockAppConfig};
    use auth::{AuthContext, MockAuth};
    use container_types::network::ContainerRequestBase;
    use ignx_compositron::network::http::MockHttpRequestContext;
    use ignx_compositron::reactive::*;
    use ignx_compositron::{app::launch_only_app_context, context::AppContext};
    use network::common::lrc_edge_constants::{DynamicFeature, WidgetSchemes};
    use router::{MockRouting, RoutingContext};
    use serial_test::*;

    #[test]
    #[serial]
    fn should_call_network_client_with_right_params() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);

            let network_client = NetworkClient::new(&ctx);
            let controller = MockControllerBuilder::new(ctx).setup();
            network_client.restore_collection_page_focus(
                |_, _| {},
                |_| {},
                Rc::new(controller),
                CollectionInitialTypes::NavItem,
                &restore_collection_page_request(
                    "home",
                    "movies",
                    Some("serviceToken"),
                    "containerToRestore",
                    "itemToRestore",
                    true,
                    true,
                ),
            );

            let props = MockHttpRequestContext::get();
            assert_eq!(props.method, HttpMethod::Get);
            assert_eq!(
                props.url,
                expected_url(
                    "home",
                    "movies",
                    Some("serviceToken"),
                    "containerToRestore",
                    "itemToRestore",
                    true,
                    true
                )
            );
            let mut sorted_headers = props.headers.clone();
            sorted_headers.sort();

            let mut expected_headers = [
                "x-atv-page-id: movies",
                "x-client-app: avlrc",
                "accept: application/json",
                "x-client-version: unknown-version",
                "content-type: application/json",
                "x-atv-page-type: ATVHome",
                "x-request-priority: CRITICAL",
            ];
            expected_headers.sort();

            assert_eq!(sorted_headers, expected_headers);
            assert_eq!(props.timeout, None);
        })
    }

    fn restore_collection_page_request(
        page_type: &str,
        page_id: &str,
        service_token: Option<&str>,
        container_id: &str,
        transform_item_id: &str,
        fallback_to_container: bool,
        skip_pagination: bool,
    ) -> RestoreCollectionPageFocusRequest {
        RestoreCollectionPageFocusRequest {
            collection_initial_base: CollectionInitialRequest {
                container_request_base: ContainerRequestBase {
                    page_id: page_id.to_string(),
                    page_type: page_type.to_string(),
                    widget_scheme: WidgetSchemes::LRCRustCollectionsV1,
                    presentation_scheme: "living-room-react-focus".to_string(),
                    taps_roles: vec![],
                    additional_dynamic_features: vec![
                        DynamicFeature::GLOBAL_TRAINING,
                        DynamicFeature::LinearStationInAllCarousels,
                    ],
                    additional_client_features: vec![],
                },
                service_token: service_token.map(|t| t.to_string()),
                is_category_page_redesign_enabled: None,
                include_voice_hints: None,
            },
            container_id: container_id.to_string(),
            transform_item_id: transform_item_id.to_string(),
            fallback_to_container,
            skip_pagination,
        }
    }

    fn mock_context(ctx: &AppContext) {
        let mock_routing_context = MockRouting::new();
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
        let mut mock_app_config = MockAppConfig::default();
        mock_app_config
            .expect_get_ux_locale()
            .returning(|| "en_GB".into());
        mock_app_config
            .expect_get_geo_location()
            .returning(|| Some("IE".into()));
        mock_app_config
            .expect_get_supported_locales()
            .returning(|| vec!["de_DE".into(), "en_US".into(), "fr_FR".into()]);
        mock_app_config
            .expect_get_base_url()
            .return_once(|| Some("https://base_url.com".to_string()));
        provide_context::<AppConfigContext>(ctx.scope(), Rc::new(mock_app_config));

        let auth = Rc::new(MockAuth::new_without_params(ctx.scope()));
        provide_context::<AuthContext>(ctx.scope, auth);
    }

    fn expected_url(
        page_type: &str,
        page_id: &str,
        service_token: Option<&str>,
        container_id: &str,
        transform_item_id: &str,
        fallback_to_container: bool,
        skip_pagination: bool,
    ) -> String {
        let service_token = match service_token {
            Some(service_token) => format!("serviceToken={service_token}&"),
            None => "".to_string(),
        };

        format!(
            "https://base_url.com/lrcedge/getDataByJavaTransform/v1/lr/collections/restoreCollectionPageFocusV2?pageId={page_id}&pageType={page_type}&widgetScheme=lrc%2Drust%2Dcollections%2Dv1&featureScheme=react%2Dv5&decorationScheme=lr%2Ddecoration%2Dgen4%2Dv4&clientPage=CollectionsPage&clientFeatures=DynamicBadging%2CRemaster%2CUseDynamicDatumParameters%2CUseV12TitleActionsView&dynamicFeatures=AppleTVODEnabled%2CCLIENT%5FDECORATION%5FENABLE%5FDAAPI%2CDigitalBundlePvcPvc%2CGLOBAL%5FTRAINING%2CLinearStationInAllCarousels%2CLinearTitles%2CLiveTab%2CRustLRSupported%2CSupportsImageTextLinkTextInStandardHero&presentationScheme=living%2Droom%2Dreact%2Dfocus&isBrandingEnabled=true&regulatoryLabel=true&tentpoleSSMEnabled=true&useEpisodicSynopsis=true&useMiniSynopsis=true&{service_token}containerId={container_id}&transformItemId={transform_item_id}&fallbackToContainer={fallback_to_container}&skipPagination={skip_pagination}&clientId=pv%2Dlrc%2Drust&transformStore=local&transformStage=prod&javaTransformTimeout=5000&timeZoneId=MockTimeZoneId&gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB"
        )
    }
}
