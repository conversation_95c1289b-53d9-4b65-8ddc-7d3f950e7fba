use crate::network::parser::{
    collections_response_parser_callback, collections_response_sports_edge_parser_callback,
};
use crate::network::types::CollectionInitialRequest;
use crate::network::url_constructors::{
    collection_initial_url_constructor, collection_page_sports_edge_url_constructor,
};
use crate::page::controller::CollectionsPageControlling;
use crate::page::helpers_sig::validate_collections_page_from_value;
use crate::reporting::app_events::{CollectionsAppEvents, CollectionsTransforms};
use beekeeper::types::{
    BeekeeperFallbackPageParams, ResiliencyPage, StorefrontFallbackPageFulfilledAction,
};
#[double]
use beekeeper::{with_beekeeper_fallback, BeekeeperContext};
use cache::ExpirableLruCacheRc;
use collection_types::network_types::CollectionsPage;
use ignx_compositron::network::http::HttpMethod;
use ignx_compositron::prelude::{Scope, SignalGetUntracked};
use ignx_compositron::reactive::use_context;
use ignx_compositron::time::Instant;
use location::RustPage;
use mockall_double::double;
use network::common::lrc_edge_constants::{CollectionInitialTypes, LRCEdgeTransforms};
use network::types::cache_types::CacheType;
use network::{NetworkClient, RequestError};
#[double]
use profile_manager::try_use_profile_manager;
use sports_edge_types::network::get_collection_page_types::lrc_get_collection_page::SPORTS_EDGE_QUERY_HASH;
use std::rc::Rc;

pub trait CollectionsRequests {
    #[allow(dead_code, reason = "trait used with mock")]
    fn collection_initial<S, F, R>(
        &self,
        success_callback: S,
        failure_callback: F,
        resiliency_callback: R,
        page_controller: Rc<dyn CollectionsPageControlling>,
        collection_initial_type: CollectionInitialTypes,
        params: &CollectionInitialRequest,
        cache: ExpirableLruCacheRc<String, CollectionsPage>,
        beekeeper_page_params: BeekeeperFallbackPageParams,
        redirect: Option<ResiliencyPage>,
        is_reload: bool,
    ) where
        S: FnOnce(CollectionsPage, Instant) + 'static,
        F: FnOnce(RequestError) + 'static,
        R: FnOnce(StorefrontFallbackPageFulfilledAction<CollectionsPage>) + 'static;

    fn get_collection_page_sports_edge<S, F, R>(
        &self,
        success_callback: S,
        failure_callback: F,
        resiliency_callback: R,
        page_controller: Rc<dyn CollectionsPageControlling>,
        collection_initial_type: CollectionInitialTypes,
        params: &CollectionInitialRequest,
        cache: ExpirableLruCacheRc<String, CollectionsPage>,
        beekeeper_page_params: BeekeeperFallbackPageParams,
        redirect: Option<ResiliencyPage>,
        is_reload: bool,
    ) where
        S: FnOnce(CollectionsPage, Instant) + 'static,
        F: FnOnce(RequestError) + 'static,
        R: FnOnce(StorefrontFallbackPageFulfilledAction<CollectionsPage>) + 'static;

    fn prefetch_collection_initial(
        &self,
        page_source: RustPage,
        collection_initial_type: CollectionInitialTypes,
        params: &CollectionInitialRequest,
        cache: ExpirableLruCacheRc<String, CollectionsPage>,
    );
}

impl CollectionsRequests for NetworkClient {
    fn collection_initial<S, F, R>(
        &self,
        success_callback: S,
        failure_callback: F,
        resiliency_callback: R,
        page_controller: Rc<dyn CollectionsPageControlling>,
        collection_initial_type: CollectionInitialTypes,
        params: &CollectionInitialRequest,
        cache: ExpirableLruCacheRc<String, CollectionsPage>,
        beekeeper_page_params: BeekeeperFallbackPageParams,
        redirect: Option<ResiliencyPage>,
        is_reload: bool,
    ) where
        S: FnOnce(CollectionsPage, Instant) + 'static,
        F: FnOnce(RequestError) + 'static,
        R: FnOnce(StorefrontFallbackPageFulfilledAction<CollectionsPage>) + 'static,
    {
        let url = match collection_initial_url_constructor(
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
            page_controller.page_source(),
            collection_initial_type.clone(),
            params,
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!("CollectionsRequests#collection_initial: Cannot create the request url. Error: {:?}", e);
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let success_cb = move |v: CollectionsPage, data_fetch_time| {
            success_callback(v, data_fetch_time);
        };

        let failure_cb = {
            let page_controller = Rc::clone(&page_controller);
            move |e: RequestError, _| {
                page_controller.report_app_event(CollectionsAppEvents::NetworkFailure {
                    transform: CollectionsTransforms::CollectionsPageInitial,
                });
                failure_callback(e);
            }
        };

        let transform = LRCEdgeTransforms::CollectionInitial(collection_initial_type);
        let crc_page_id = Some(params.container_request_base.page_id.clone());

        #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-785")]
        let mut builder = self
            .builder(url, HttpMethod::Get, "collectionsPageInitial")
            .with_lrc_edge_headers(&transform, crc_page_id)
            .with_parser(collections_response_parser_callback)
            .with_network_latency(Rc::new({
                let page_controller = Rc::clone(&page_controller);
                move |_, latency| {
                    page_controller.report_app_event(CollectionsAppEvents::NetworkSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: latency.as_millis() as u64,
                    });
                }
            }))
            .with_processing_latency(Rc::new({
                let page_controller = Rc::clone(&page_controller);
                move |_, latency| {
                    page_controller.report_app_event(CollectionsAppEvents::ProcessingSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: latency.as_millis() as u64,
                    });
                }
            }));

        if should_cache(params) {
            let active_profile_id = get_active_profile_id(self.ctx.scope);
            let cache_key = cache_key(params, self.app_config.get_ux_locale(), active_profile_id);

            if !is_reload {
                let cache_type = cache_type(params);
                builder =
                    builder.use_cached_response(Rc::clone(&cache), cache_key.clone(), cache_type)
            }

            builder = builder.cache_response(cache, cache_key);
        }

        let Some(beekeeper) = use_context::<BeekeeperContext>(self.ctx.scope()) else {
            log::error!("[collections#network] Cannot find BeekeeperContext in given scope. Continue without beekeeper");
            return builder
                .on_success(Box::new(success_cb))
                .on_failure(Box::new(failure_cb))
                .execute();
        };

        with_beekeeper_fallback(
            beekeeper,
            beekeeper_page_params,
            redirect,
            Some(Rc::new(validate_collections_page_from_value)),
            builder,
            Box::new(success_cb),
            Box::new(failure_cb),
            Box::new(resiliency_callback),
        );
    }

    fn get_collection_page_sports_edge<S, F, R>(
        &self,
        success_callback: S,
        failure_callback: F,
        resiliency_callback: R,
        page_controller: Rc<dyn CollectionsPageControlling>,
        _collection_initial_type: CollectionInitialTypes,
        params: &CollectionInitialRequest,
        cache: ExpirableLruCacheRc<String, CollectionsPage>,
        beekeeper_page_params: BeekeeperFallbackPageParams,
        redirect: Option<ResiliencyPage>,
        is_reload: bool,
    ) where
        S: FnOnce(CollectionsPage, Instant) + 'static,
        F: FnOnce(RequestError) + 'static,
        R: FnOnce(StorefrontFallbackPageFulfilledAction<CollectionsPage>) + 'static,
    {
        let scope = self.ctx.scope();
        let url = match collection_page_sports_edge_url_constructor(
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
            params,
            SPORTS_EDGE_QUERY_HASH,
            scope,
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!("CollectionsRequests#get_collection_page_sports_edge: Cannot create the request url. Error: {:?}", e);
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let success_cb = move |v: CollectionsPage, data_fetch_time| {
            success_callback(v, data_fetch_time);
        };

        let failure_cb = {
            let page_controller = Rc::clone(&page_controller);
            move |e: RequestError, _| {
                page_controller.report_app_event(CollectionsAppEvents::NetworkFailure {
                    transform: CollectionsTransforms::SportsEdgeInitial,
                });
                failure_callback(e);
            }
        };

        #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-785")]
        let mut builder = self
            .builder(url, HttpMethod::Get, "SportsEdgeRequest")
            .with_parser(collections_response_sports_edge_parser_callback)
            .with_auth(Rc::clone(&self.auth))
            .with_network_latency({
                let page_controller = Rc::clone(&page_controller);
                Rc::new(move |_, latency| {
                    page_controller.report_app_event(CollectionsAppEvents::NetworkSuccess {
                        transform: CollectionsTransforms::SportsEdgeInitial,
                        time: latency.as_millis() as u64,
                    });
                })
            })
            .with_processing_latency({
                let page_controller = Rc::clone(&page_controller);
                Rc::new(move |_, latency| {
                    page_controller.report_app_event(CollectionsAppEvents::ProcessingSuccess {
                        transform: CollectionsTransforms::SportsEdgeInitial,
                        time: latency.as_millis() as u64,
                    });
                })
            });

        if should_cache(params) {
            let active_profile_id = get_active_profile_id(self.ctx.scope);
            let cache_key = cache_key(params, self.app_config.get_ux_locale(), active_profile_id);

            if !is_reload {
                let cache_type = cache_type(params);
                builder =
                    builder.use_cached_response(Rc::clone(&cache), cache_key.clone(), cache_type)
            }

            builder = builder.cache_response(cache, cache_key);
        }

        let Some(beekeeper) = use_context::<BeekeeperContext>(self.ctx.scope()) else {
            log::error!("[collections#network] Cannot find BeekeeperContext in given scope. Continue without beekeeper");
            return builder
                .on_success(Box::new(success_cb))
                .on_failure(Box::new(failure_cb))
                .execute();
        };

        with_beekeeper_fallback(
            beekeeper,
            beekeeper_page_params,
            redirect,
            Some(Rc::new(validate_collections_page_from_value)),
            builder,
            Box::new(success_cb),
            Box::new(failure_cb),
            Box::new(resiliency_callback),
        );
    }

    fn prefetch_collection_initial(
        &self,
        page_source: RustPage,
        collection_initial_type: CollectionInitialTypes,
        params: &CollectionInitialRequest,
        cache: ExpirableLruCacheRc<String, CollectionsPage>,
    ) {
        if !should_cache(params) {
            return;
        }
        let active_profile_id = get_active_profile_id(self.ctx.scope);
        let cache_key = cache_key(params, self.app_config.get_ux_locale(), active_profile_id);
        let cache_type = cache_type(params);
        let url = match collection_initial_url_constructor(
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
            &page_source,
            collection_initial_type.clone(),
            params,
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!("CollectionsRequests#prefetch_collection_initial: Cannot create the request url. Error: {:?}", e);
                return;
            }
        };

        let transform = LRCEdgeTransforms::CollectionInitial(collection_initial_type);
        let crc_page_id = Some(params.container_request_base.page_id.clone());

        self.builder(url, HttpMethod::Get, "collectionsPageInitial")
            .with_lrc_edge_headers(&transform, crc_page_id)
            .with_parser(collections_response_parser_callback)
            .prefetch(cache, cache_key, cache_type)
            .execute();
    }
}

fn cache_key(
    params: &CollectionInitialRequest,
    ux_locale: String,
    actor_id: Option<String>,
) -> String {
    format!(
        "{}-{}-{:?}-{:?}-{:?}",
        params.container_request_base.page_id,
        params.container_request_base.page_type,
        params.service_token,
        ux_locale,
        actor_id
    )
}

fn cache_type(params: &CollectionInitialRequest) -> CacheType {
    match params.container_request_base.page_id.as_str() {
        "home" => match params.container_request_base.page_type.as_str() {
            "home" => CacheType::Home,
            "movie" => CacheType::Movie,
            "tv" => CacheType::Tv,
            _ => CacheType::Others,
        },
        "kids" => match params.container_request_base.page_type.as_str() {
            "merch" => CacheType::KidsMerch,
            _ => CacheType::Others,
        },
        "addons" => match params.container_request_base.page_type.as_str() {
            "home" => CacheType::Addons,
            _ => CacheType::Others,
        },
        "MyStuff" => match params.container_request_base.page_type.as_str() {
            "home" => CacheType::MyStuff,
            _ => CacheType::Others,
        },
        "Sports" => match params.container_request_base.page_type.as_str() {
            "home" => CacheType::Sports,
            _ => CacheType::Others,
        },
        "news" => match params.container_request_base.page_type.as_str() {
            "home" => CacheType::News,
            _ => CacheType::Others,
        },
        _ => match params.container_request_base.page_type.as_str() {
            "subscription" => CacheType::Subscription,
            _ => CacheType::Others,
        },
    }
}

fn should_cache(params: &CollectionInitialRequest) -> bool {
    !(params.container_request_base.page_type == "home"
        && params.container_request_base.page_id == "MyStuff")
}

fn get_active_profile_id(scope: Scope) -> Option<String> {
    try_use_profile_manager(scope).and_then(|profile_manager| {
        profile_manager
            .get_active_profile_id()
            .try_get_untracked()
            .flatten()
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_assets::mocks::MockControllerBuilder;
    use app_config::{AppConfigContext, MockAppConfig};
    use auth::{AuthContext, MockAuth};
    use beekeeper::MockBeekeeper;
    use common_transform_types::profile::{Profile, ProfileAvatar};
    use container_types::network::ContainerRequestBase;
    use ignx_compositron::context::AppContext;
    use ignx_compositron::reactive::*;
    use network::common::lrc_edge_constants::{DynamicFeature, WidgetSchemes};
    use profile_manager::MockProfileManager;
    use router::{MockRouting, RoutingContext};

    mod collection_initial {
        use super::*;
        use beekeeper::types::LrcEdgeBeekeeperPageParams;
        use cache::expirable_lru_cache::ExpirableLruCache;
        use ignx_compositron::app::launch_only_app_context;
        use ignx_compositron::network::http::MockHttpRequestContext;
        use serial_test::serial;
        use std::cell::RefCell;
        use std::time::Duration;

        #[test]
        #[serial]
        fn should_call_network_client_with_right_params() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx).setup();
                network_client.collection_initial(
                    |_, _| {},
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "movies", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".to_string(),
                        pageId: "movies".to_string(),
                        pageSection: None,
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                assert_eq!(props.method, HttpMethod::Get);
                assert_eq!(
                    props.url,
                    expected_url("home", "movies", Some("serviceToken"))
                );
                let mut headers = props.headers.clone();
                headers.sort();
                let mut expected_headers = vec![
                    "x-atv-page-id: movies",
                    "x-client-app: avlrc",
                    "accept: application/json",
                    "x-client-version: unknown-version",
                    "content-type: application/json",
                    "x-atv-page-type: ATVHome",
                    "x-request-priority: CRITICAL",
                ];
                expected_headers.sort();
                assert_eq!(headers, expected_headers);
                assert_eq!(props.timeout, None);
                assert_eq!(props.sent_data, Some("".into()));
            })
        }

        #[test]
        #[serial]
        fn should_call_network_client_with_right_params_when_beekeeper_enabled() {
            launch_only_app_context(|ctx| {
                mock_context_with_beekeeper(&ctx);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx).setup();
                network_client.collection_initial(
                    |_, _| {},
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "movies", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".to_string(),
                        pageId: "movies".to_string(),
                        pageSection: None,
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::try_get();
                // Request is built when we call `execute`. When Beekeeper is available, we don't construct the request right away.
                assert!(props.is_none());
            })
        }

        #[test]
        fn should_invoke_success_cb_and_metrics_cbs_on_success_without_beekeeper() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx)
                    .with_app_event(CollectionsAppEvents::NetworkSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: 0,
                    })
                    .with_app_event(CollectionsAppEvents::ProcessingSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: 0,
                    })
                    .setup();

                let success_counter = Rc::new(RefCell::new(0));
                network_client.collection_initial(
                    {
                        let counter = Rc::clone(&success_counter);
                        move |_, _| {
                            let mut c = counter.borrow_mut();
                            *c += 1;
                        }
                    },
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "movies", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".to_string(),
                        pageId: "movies".to_string(),
                        pageSection: None,
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                let test_page =
                    include_str!("../test_assets/collections_page_full_response.json").to_string();
                props.invoke_callback(Some(test_page), 200);

                assert_eq!(*success_counter.borrow(), 1);
            })
        }

        #[test]
        fn should_invoke_failure_cb_on_failure_without_beekeeper() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx)
                    .with_app_event(CollectionsAppEvents::NetworkFailure {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                    })
                    .setup();

                let failure_counter = Rc::new(RefCell::new(0));
                network_client.collection_initial(
                    |_, _| {},
                    {
                        let counter = Rc::clone(&failure_counter);
                        move |_| {
                            let mut c = counter.borrow_mut();
                            *c += 1;
                        }
                    },
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "movies", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".to_string(),
                        pageId: "movies".to_string(),
                        pageSection: None,
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                let test_page =
                    include_str!("../test_assets/collections_page_full_response.json").to_string();
                props.invoke_callback(Some(test_page), 404);

                assert_eq!(*failure_counter.borrow(), 1);
            })
        }

        #[test]
        #[ignore = "https://sim.amazon.com/issues/LR-Rust-1341"]
        fn should_invoke_success_cb_and_metrics_cbs_on_success_with_beekeeper() {
            todo!()
        }

        #[test]
        #[ignore = "https://sim.amazon.com/issues/LR-Rust-1341"]
        fn should_invoke_failure_cb_on_failure_and_beekeeper_failure_with_beekeeper() {
            todo!()
        }

        #[test]
        #[ignore = "https://sim.amazon.com/issues/LR-Rust-1341"]
        fn should_invoke_success_cb_and_metrics_cbs_on_failure_and_beekeeper_success_with_beekeeper(
        ) {
            todo!()
        }

        #[test]
        #[serial]
        fn should_cache_response() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                // the network client's success callbacks are invoked in this test, including the
                // with_network_latency and with_processing_latency metrics callbacks. Since we
                // set these up to report app events, we need to set up corresponding expectations.
                let page_controller = MockControllerBuilder::new(ctx)
                    .with_app_event(CollectionsAppEvents::NetworkSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: 0,
                    })
                    .with_app_event(CollectionsAppEvents::ProcessingSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: 0,
                    })
                    .setup();
                network_client.collection_initial(
                    |_, _| {},
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "movies", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".to_string(),
                        pageId: "movies".to_string(),
                        pageSection: None,
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                let test_page =
                    include_str!("../test_assets/collections_page_full_response.json").to_string();
                props.invoke_callback(Some(test_page), 200);

                let mut cache = cache.borrow_mut();
                let cached_entry =
                    cache.get(&"movies-home-Some(\"serviceToken\")-\"en_GB\"-Some(\"mock_active_profile_id\")".to_string());
                assert!(cached_entry.is_some());
            })
        }

        #[test]
        #[serial]
        fn should_not_cache_my_stuff() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, false);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                // app events included as we trigger success callbacks including network + processing latency
                let page_controller = MockControllerBuilder::new(ctx)
                    .with_app_event(CollectionsAppEvents::NetworkSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: 0,
                    })
                    .with_app_event(CollectionsAppEvents::ProcessingSuccess {
                        transform: CollectionsTransforms::CollectionsPageInitial,
                        time: 0,
                    })
                    .setup();
                network_client.collection_initial(
                    |_, _| {},
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "MyStuff", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::LrcEdge(LrcEdgeBeekeeperPageParams {
                        pageType: "home".to_string(),
                        pageId: "movies".to_string(),
                        pageSection: None,
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                let test_page =
                    include_str!("../test_assets/collections_page_full_response.json").to_string();
                props.invoke_callback(Some(test_page), 200);

                let mut cache = cache.borrow_mut();
                let cached_entry =
                    cache.get(&"movies-home-Some(\"serviceToken\")-\"en_GB\"-Some(\"mock_active_profile_id\")".to_string());
                assert!(cached_entry.is_none());
            })
        }
    }

    mod prefetch_collection_initial {
        use crate::network::collection_initial::tests::{
            collection_initial_request, expected_url, mock_context,
        };
        use crate::network::collection_initial::CollectionsRequests;
        use cache::expirable_lru_cache::ExpirableLruCache;
        use ignx_compositron::app::launch_only_app_context;
        use ignx_compositron::network::http::{HttpMethod, MockHttpRequestContext};
        use location::RustPage;
        use network::common::lrc_edge_constants::CollectionInitialTypes;
        use network::NetworkClient;
        use serial_test::serial;
        use std::rc::Rc;
        use std::time::Duration;

        #[test]
        #[serial]
        fn should_prefetch_with_right_params() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                network_client.prefetch_collection_initial(
                    RustPage::RUST_COLLECTIONS,
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "movies", Some("serviceToken")),
                    Rc::clone(&cache),
                );

                let props = MockHttpRequestContext::get();
                assert_eq!(props.method, HttpMethod::Get);
                assert_eq!(
                    props.url,
                    expected_url("home", "movies", Some("serviceToken"))
                );
                let mut headers = props.headers.clone();
                headers.sort();
                let mut expected_headers = vec![
                    "x-atv-page-id: movies",
                    "x-client-app: avlrc",
                    "accept: application/json",
                    "x-client-version: unknown-version",
                    "content-type: application/json",
                    "x-atv-page-type: ATVHome",
                    "x-request-priority: BACKGROUND",
                ];
                expected_headers.sort();
                assert_eq!(headers, expected_headers);
                assert_eq!(props.timeout, None);

                let test_page =
                    include_str!("../test_assets/collections_page_full_response.json").to_string();
                props.invoke_callback(Some(test_page), 200);

                let mut cache = cache.borrow_mut();
                let cached_entry =
                    cache.get(&"movies-home-Some(\"serviceToken\")-\"en_GB\"-Some(\"mock_active_profile_id\")".to_string());
                assert!(cached_entry.is_some());
            })
        }

        #[test]
        fn should_not_prefetch_my_stuff() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, false);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                network_client.prefetch_collection_initial(
                    RustPage::RUST_COLLECTIONS,
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("home", "MyStuff", Some("serviceToken")),
                    Rc::clone(&cache),
                );

                let props = MockHttpRequestContext::try_get();
                assert!(props.is_none());
            })
        }
    }

    mod get_collection_page_sports_edge {
        use super::*;
        use beekeeper::types::SportEdgeBeekeeperPageParams;
        use cache::expirable_lru_cache::ExpirableLruCache;
        use ignx_compositron::app::launch_only_app_context;
        use ignx_compositron::network::http::MockHttpRequestContext;
        use serial_test::serial;
        use std::cell::RefCell;
        use std::time::Duration;

        #[test]
        #[serial]
        fn should_call_sports_edge_with_right_params() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, false);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx).setup();
                network_client.get_collection_page_sports_edge(
                    |_, _| {},
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("tournament", "pageId", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                        pageType: "tournament".to_string(),
                        pageId: "pageId".to_string(),
                        query_hash: "query_hash".to_string(),
                        operation_name: "operation_name".to_string(),
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                assert_eq!(props.method, HttpMethod::Get);
                assert_eq!(props.url, expected_sports_edge_url("tournament", "pageId"));
                let mut headers = props.headers.clone();
                headers.sort();
                let mut expected_headers = vec![
                    "x-client-app: avlrc",
                    "accept: application/json",
                    "x-client-version: unknown-version",
                    "content-type: application/json",
                    "x-request-priority: CRITICAL",
                ];
                expected_headers.sort();
                assert_eq!(headers, expected_headers);
                assert_eq!(props.timeout, None);
                assert_eq!(props.sent_data, Some("".into()));
            })
        }

        #[test]
        #[serial]
        fn should_call_sports_edge_with_right_params_when_beekeeper_enabled() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, false);
                let mock_beekeeper = MockBeekeeper::default();
                provide_context::<BeekeeperContext>(ctx.scope(), Rc::new(mock_beekeeper));

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx).setup();
                network_client.get_collection_page_sports_edge(
                    |_, _| {},
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request("pageType", "pageId", Some("serviceToken")),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                        pageType: "pageType".to_string(),
                        pageId: "pageId".to_string(),
                        query_hash: "some_query_hash".to_string(),
                        operation_name: "some_operation_name".to_string(),
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::try_get();
                // Request is built when we call `execute`. When Beekeeper is available, we don't construct the request right away.
                assert!(props.is_none());
            })
        }

        #[test]
        fn should_invoke_success_cb_and_metrics_cbs_on_success_without_beekeeper() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx)
                    .with_app_event(CollectionsAppEvents::NetworkSuccess {
                        transform: CollectionsTransforms::SportsEdgeInitial,
                        time: 0,
                    })
                    .with_app_event(CollectionsAppEvents::ProcessingSuccess {
                        transform: CollectionsTransforms::SportsEdgeInitial,
                        time: 0,
                    })
                    .setup();

                let success_counter = Rc::new(RefCell::new(0));
                network_client.get_collection_page_sports_edge(
                    {
                        let counter = Rc::clone(&success_counter);
                        move |_, _| {
                            let mut c = counter.borrow_mut();
                            *c += 1;
                        }
                    },
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request(
                        "tournament",
                        "amzn1.dv.icid",
                        Some("serviceToken"),
                    ),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                        pageType: "pageType".to_string(),
                        pageId: "pageId".to_string(),
                        query_hash: "some_query_hash".to_string(),
                        operation_name: "some_operation_name".to_string(),
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                let test_page =
                    include_str!("../test_assets/sports_edge_collections_page_valid_response.json")
                        .to_string();
                props.invoke_callback(Some(test_page), 200);

                assert_eq!(*success_counter.borrow(), 1);
            })
        }

        #[test]
        fn should_invoke_failure_cb_on_failure_without_beekeeper() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let page_controller = MockControllerBuilder::new(ctx)
                    .with_app_event(CollectionsAppEvents::NetworkFailure {
                        transform: CollectionsTransforms::SportsEdgeInitial,
                    })
                    .setup();

                let failure_counter = Rc::new(RefCell::new(0));
                network_client.get_collection_page_sports_edge(
                    |_, _| {},
                    {
                        let counter = Rc::clone(&failure_counter);
                        move |_| {
                            let mut c = counter.borrow_mut();
                            *c += 1;
                        }
                    },
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request(
                        "tournament",
                        "amzn1.dv.icid",
                        Some("serviceToken"),
                    ),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                        pageType: "tournament".to_string(),
                        pageId: "amzn1.dv.icid".to_string(),
                        query_hash: "some_query_hash".to_string(),
                        operation_name: "some_operation_name".to_string(),
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                let test_page =
                    include_str!("../test_assets/sports_edge_collections_page_valid_response.json")
                        .to_string();
                props.invoke_callback(Some(test_page), 404);

                assert_eq!(*failure_counter.borrow(), 1);
            })
        }
        #[test]
        #[serial]
        fn should_cache_sports_edge_response() {
            launch_only_app_context(|ctx| {
                mock_context(&ctx, true);

                let network_client = NetworkClient::new(&ctx);
                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                // app events included as we trigger success callbacks including network + processing latency
                let page_controller = MockControllerBuilder::new(ctx)
                    .with_app_event(CollectionsAppEvents::NetworkSuccess {
                        transform: CollectionsTransforms::SportsEdgeInitial,
                        time: 0,
                    })
                    .with_app_event(CollectionsAppEvents::ProcessingSuccess {
                        transform: CollectionsTransforms::SportsEdgeInitial,
                        time: 0,
                    })
                    .setup();
                network_client.get_collection_page_sports_edge(
                    |_, _| {},
                    |_| {},
                    |_| {},
                    Rc::new(page_controller),
                    CollectionInitialTypes::NavItem,
                    &collection_initial_request(
                        "tournament",
                        "amzn1.dv.icid",
                        Some("serviceToken"),
                    ),
                    Rc::clone(&cache),
                    BeekeeperFallbackPageParams::Sport(SportEdgeBeekeeperPageParams {
                        pageType: "tournament".to_string(),
                        pageId: "amzn1.dv.icid".to_string(),
                        query_hash: "some_query_hash".to_string(),
                        operation_name: "some_operation_name".to_string(),
                    }),
                    None,
                    false,
                );

                let props = MockHttpRequestContext::get();
                let test_page =
                    include_str!("../test_assets/sports_edge_collections_page_valid_response.json")
                        .to_string();
                props.invoke_callback(Some(test_page), 200);

                let mut cache = cache.borrow_mut();
                let cached_entry =
                    cache.get(&"amzn1.dv.icid-tournament-Some(\"serviceToken\")-\"en_GB\"-Some(\"mock_active_profile_id\")".to_string());
                assert!(cached_entry.is_some());
            })
        }
    }

    mod cache_key {
        use super::*;
        use container_types::network::ContainerRequestBase;
        use network::common::lrc_edge_constants::{DynamicFeature, WidgetSchemes};

        #[test]
        fn should_match_regardless_of_unrelated_params() {
            assert_eq!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en_GB".to_string(),
                    None
                ),
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en_GB".to_string(),
                    None
                )
            );

            assert_eq!(
                cache_key(
                    &CollectionInitialRequest {
                        container_request_base: ContainerRequestBase {
                            page_id: "page_id_X".to_string(),
                            page_type: "page_type_A".to_string(),
                            widget_scheme: WidgetSchemes::LRCRustCollectionsV1,
                            presentation_scheme: "living-room-react-focus".to_string(),
                            taps_roles: vec![],
                            additional_dynamic_features: vec![
                                DynamicFeature::GLOBAL_TRAINING,
                                DynamicFeature::LinearStationInAllCarousels
                            ],
                            additional_client_features: vec![],
                        },
                        service_token: Some("token".to_string()),
                        is_category_page_redesign_enabled: None,
                        include_voice_hints: None,
                    },
                    "en_GB".to_string(),
                    None
                ),
                cache_key(
                    &CollectionInitialRequest {
                        container_request_base: ContainerRequestBase {
                            page_id: "page_id_X".to_string(),
                            page_type: "page_type_A".to_string(),
                            widget_scheme: WidgetSchemes::DetailsPageATF,
                            presentation_scheme: "living-room-react-focus".to_string(),
                            taps_roles: vec![],
                            additional_dynamic_features: vec![
                                DynamicFeature::GLOBAL_TRAINING,
                                DynamicFeature::LinearStationInAllCarousels
                            ],
                            additional_client_features: vec![],
                        },
                        service_token: Some("token".to_string()),
                        is_category_page_redesign_enabled: Some(true),
                        include_voice_hints: Some(true),
                    },
                    "en_GB".to_string(),
                    None
                )
            );
        }

        #[test]
        fn not_match() {
            assert_ne!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en_GB".to_string(),
                    None
                ),
                cache_key(
                    &collection_initial_request("page_type_B", "page_id_X", Some("token")),
                    "en_GB".to_string(),
                    None
                )
            );

            assert_ne!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en_GB".to_string(),
                    None
                ),
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_Y", Some("token")),
                    "en_GB".to_string(),
                    None
                )
            );

            assert_ne!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en_GB".to_string(),
                    None
                ),
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("another_token")),
                    "en_GB".to_string(),
                    None
                )
            );

            assert_ne!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en_GB".to_string(),
                    None
                ),
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", None),
                    "en_GB".to_string(),
                    None
                )
            );

            // Additional tests with ux_locale
            assert_ne!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en-US".to_string(),
                    None
                ),
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "fr-FR".to_string(),
                    None
                )
            );

            // Additional tests with actor_id
            assert_ne!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en-US".to_string(),
                    Some("mock_actor_id".to_string())
                ),
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en-US".to_string(),
                    None
                )
            );

            assert_ne!(
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en-US".to_string(),
                    Some("mock_actor_id".to_string())
                ),
                cache_key(
                    &collection_initial_request("page_type_A", "page_id_X", Some("token")),
                    "en-US".to_string(),
                    Some("mock_actor_id_2".to_string())
                )
            );
        }
    }

    mod should_cache {
        use super::*;

        #[test]
        fn should_not_cache_my_stuff() {
            assert!(!should_cache(&collection_initial_request(
                "home", "MyStuff", None
            )));
            assert!(!should_cache(&collection_initial_request(
                "home",
                "MyStuff",
                Some("serviceToken")
            )));
        }

        #[test]
        fn should_cache_everything_else() {
            assert!(should_cache(&collection_initial_request(
                "home", "home", None
            )));
            assert!(should_cache(&collection_initial_request(
                "home",
                "home",
                Some("serviceToken")
            )));
            assert!(should_cache(&collection_initial_request(
                "home", "movies", None
            )));
            assert!(should_cache(&collection_initial_request(
                "Sports", "home", None
            )));
            assert!(should_cache(&collection_initial_request(
                "merch",
                "kids",
                Some("serviceToken")
            )));
        }
    }

    mod cache_type {
        use crate::network::collection_initial::{cache_type, tests::CollectionInitialRequest};
        use container_types::network::ContainerRequestBase;
        use network::common::lrc_edge_constants::DynamicFeature;
        use network::{common::lrc_edge_constants::WidgetSchemes, types::cache_types::CacheType};

        #[test]
        fn test_cache_type() {
            let cases = vec![
                (("home", "home"), CacheType::Home),
                (("home", "movie"), CacheType::Movie),
                (("home", "tv"), CacheType::Tv),
                (("home", "unknown"), CacheType::Others),
                (("kids", "merch"), CacheType::KidsMerch),
                (("kids", "unknown"), CacheType::Others),
                (("addons", "home"), CacheType::Addons),
                (("addons", "unknown"), CacheType::Others),
                (("MyStuff", "home"), CacheType::MyStuff),
                (("MyStuff", "unknown"), CacheType::Others),
                (("Sports", "home"), CacheType::Sports),
                (("Sports", "unknown"), CacheType::Others),
                (("unknown", "subscription"), CacheType::Subscription),
                (("unknown", "unknown"), CacheType::Others),
            ];

            for ((page_id, page_type), expected) in cases {
                let params = CollectionInitialRequest {
                    container_request_base: ContainerRequestBase {
                        page_id: page_id.to_string(),
                        page_type: page_type.to_string(),
                        widget_scheme: WidgetSchemes::DetailsPageATF,
                        presentation_scheme: "living-room-react-focus".to_string(),
                        taps_roles: vec![],
                        additional_dynamic_features: vec![
                            DynamicFeature::GLOBAL_TRAINING,
                            DynamicFeature::LinearStationInAllCarousels,
                        ],
                        additional_client_features: vec![],
                    },
                    service_token: None,
                    is_category_page_redesign_enabled: None,
                    include_voice_hints: None,
                };

                assert_eq!(cache_type(&params), expected);
            }
        }
    }

    fn collection_initial_request(
        page_type: &str,
        page_id: &str,
        service_token: Option<&str>,
    ) -> CollectionInitialRequest {
        CollectionInitialRequest {
            container_request_base: ContainerRequestBase {
                page_id: page_id.to_string(),
                page_type: page_type.to_string(),
                widget_scheme: WidgetSchemes::LRCRustCollectionsV1,
                presentation_scheme: "living-room-react-focus".to_string(),
                taps_roles: vec![],
                additional_dynamic_features: vec![
                    DynamicFeature::GLOBAL_TRAINING,
                    DynamicFeature::LinearStationInAllCarousels,
                ],
                additional_client_features: vec![],
            },
            service_token: service_token.map(|t| t.to_string()),
            is_category_page_redesign_enabled: None,
            include_voice_hints: None,
        }
    }

    fn mock_profile(is_adult: bool) -> Option<Profile> {
        Some(Profile {
            id: "mock_active_profile_id".to_owned(),
            avatar: ProfileAvatar {
                avatarId: "someAvatarId".to_owned(),
                avatarUrl: "someAvatarUrl".to_owned(),
                avatarDescription: None,
            },
            name: "someName".to_owned(),
            isActive: true,
            isAdult: is_adult,
            profileIsImplicit: false,
            translationDetails: None,
            permissions: None,
        })
    }

    fn setup_profiles(ctx: &AppContext) {
        let mut mock_profile_manager = MockProfileManager::default();
        let mut mock_profile_manager_clone = MockProfileManager::default();
        let profile = create_rw_signal(ctx.scope(), mock_profile(true));
        let profile_id = create_rw_signal(ctx.scope(), Some("mock_active_profile_id".to_string()));

        mock_profile_manager_clone
            .expect_get_active_profile_id()
            .returning(move || profile_id.into());

        mock_profile_manager_clone
            .expect_get_active_profile()
            .returning(move || profile.into());

        mock_profile_manager
            .expect_clone()
            .once()
            .return_once(|| mock_profile_manager_clone);

        provide_context(ctx.scope(), mock_profile_manager);
        provide_context::<WriteSignal<Option<Profile>>>(ctx.scope(), profile.write_only());
    }

    fn mock_context(ctx: &AppContext, should_setup_profiles: bool) {
        if should_setup_profiles {
            setup_profiles(ctx);
        }
        let mock_routing_context = MockRouting::new();
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
        let mut mock_app_config = MockAppConfig::default();
        mock_app_config
            .expect_get_ux_locale()
            .returning(|| "en_GB".into());
        mock_app_config
            .expect_get_geo_location()
            .returning(|| Some("IE".into()));
        mock_app_config
            .expect_get_supported_locales()
            .returning(|| vec!["de_DE".into(), "en_US".into(), "fr_FR".into()]);
        mock_app_config
            .expect_get_base_url()
            .return_once(|| Some("https://base_url.com".to_string()));
        provide_context::<AppConfigContext>(ctx.scope(), Rc::new(mock_app_config));

        let auth = Rc::new(MockAuth::new_without_params(ctx.scope()));
        provide_context::<AuthContext>(ctx.scope, auth);
    }

    fn mock_context_with_beekeeper(ctx: &AppContext) {
        mock_context(ctx, true);

        let mock_beekeeper = MockBeekeeper::default();

        provide_context::<BeekeeperContext>(ctx.scope(), Rc::new(mock_beekeeper));
    }

    fn expected_url(page_type: &str, page_id: &str, service_token: Option<&str>) -> String {
        let service_token = match service_token {
            Some(service_token) => format!("serviceToken={service_token}&"),
            None => "".to_string(),
        };
        format!(
            "https://base_url.com/lrcedge/getDataByJavaTransform/v1/lr/collections/collectionsPageInitial?pageId={page_id}&pageType={page_type}&widgetScheme=lrc%2Drust%2Dcollections%2Dv1&featureScheme=react%2Dv5&decorationScheme=lr%2Ddecoration%2Dgen4%2Dv4&clientPage=CollectionsPage&clientFeatures=DynamicBadging%2CRemaster%2CUseDynamicDatumParameters%2CUseV12TitleActionsView&dynamicFeatures=AppleTVODEnabled%2CCLIENT%5FDECORATION%5FENABLE%5FDAAPI%2CDigitalBundlePvcPvc%2CGLOBAL%5FTRAINING%2CLinearStationInAllCarousels%2CLinearTitles%2CLiveTab%2CRustLRSupported%2CSupportsImageTextLinkTextInStandardHero&presentationScheme=living%2Droom%2Dreact%2Dfocus&isBrandingEnabled=true&regulatoryLabel=true&tentpoleSSMEnabled=true&useEpisodicSynopsis=true&useMiniSynopsis=true&{service_token}clientId=pv%2Dlrc%2Drust&transformStore=local&transformStage=prod&javaTransformTimeout=5000&timeZoneId=MockTimeZoneId&gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB"
        )
    }

    fn expected_sports_edge_url(page_type: &str, page_id: &str) -> String {
        format!(
            "https://base_url.com/cdp/sportsgraphqlgateway/graphql?extensions=%7B%22persistedQuery%22%3A%7B%22sha256Hash%22%3A%22{SPORTS_EDGE_QUERY_HASH}%22%2C%22version%22%3A1%7D%7D&variables=%7B%22input%22%3A%7B%22adsToken%22%3Anull%2C%22deviceContext%22%3A%7B%22deviceTypeId%22%3A%22A71I8788P1ZV8%22%2C%22osLocale%22%3A%22GB%22%2C%22timeZoneId%22%3A%22MockTimeZoneId%22%2C%22variant%22%3A%22matrix%22%7D%2C%22journeyIngressContext%22%3Anull%2C%22pageContext%22%3A%7B%22pageId%22%3A%22{page_id}%22%2C%22pageType%22%3A%22{page_type}%22%7D%2C%22priorityLevel%22%3A%22critical%22%2C%22requestConfiguration%22%3A%7B%22dynamicFeatures%22%3A%5B%22GLOBAL%5FTRAINING%22%2C%22LinearStationInAllCarousels%22%5D%2C%22featureScheme%22%3A%22react%2Dv5%22%2C%22widgetScheme%22%3A%22lrc%2Drust%2Dcollections%2Dv1%22%7D%2C%22traceToken%22%3Anull%7D%2C%22message%5Fpresentation%5Fparams%5Froles%22%3A%22new%2Dtitle%2Dbadge%2Dsupported%2Ccoming%2Dsoon%2Dtitle%2Dbadge%2Dsupported%2Cav%2Dliveliness%2Dwith%2Dunavailable%2Dmessage%2Dsupported%22%2C%22presentation%5Fscheme%22%3A%22living%2Droom%2Dreact%2Dfocus%22%2C%22time%5Fzone%22%3A%22MockTimeZoneId%22%2C%22title%5Factions%5Fview%5Fparams%5Froles%22%3A%22new%2Dtitle%2Dbadge%2Dsupported%2Ccoming%2Dsoon%2Dtitle%2Dbadge%2Dsupported%22%7D&operationName=LRCGetCollectionPage&gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB"
        )
    }
}
