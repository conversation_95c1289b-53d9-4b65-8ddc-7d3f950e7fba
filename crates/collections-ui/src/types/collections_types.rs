use crate::ui::types::{ContainerType, ContainerTypeCacheable};
use cacheable_derive::Cacheable;
use cfg_test_attr_derive::derive_test_only;
use common_transform_types::actions::TransitionAction;
use common_transform_types::containers::{
    ChartsCarouselItem, PaginationLink, PaginationLinkCacheable, ShortCarouselItem,
    StandardCarouselItem, StandardHeroItem, SuperCarouselItem, TentpoleHeroItem,
};
use container_types::ui_signals::*;
use ignx_compositron::prelude::*;

#[derive(Clone)]
#[cfg_attr(
    any(
        debug_assertions,
        test,
        feature = "debug_impl",
        not(target_arch = "wasm32")
    ),
    derive(Debug)
)]
pub enum CollectionsFocusableItems {
    StandardCarousel(StandardCarouselItem),
    SuperCarousel(SuperCarouselItem),
    ShortCarousel(ShortCarouselItem),
    TentpoleHero(TentpoleHeroItem),
    StandardHero(StandardHeroItem),
    ChartsCarousel(ChartsCarouselItem),
}

#[derive(Clone, Cacheable)]
#[derive_test_only(Debug, PartialEq)]
pub struct StartColumn {
    pub title: Option<String>,
    pub subtitle: Option<String>,
    #[cacheable(contains_cacheable_type)]
    pub action: Option<TransitionAction>,
}

#[derive(Clone, Cacheable)]
pub struct CollectionsPageModel {
    pub container_list: RwSignal<Vec<ContainerType>>,
    pub pagination_link: RwSignal<Option<PaginationLink>>,
    pub page_title: RwSignal<Option<String>>,
    pub page_logo_image: RwSignal<Option<String>>,
    pub pagination_pending: RwSignal<bool>,
    pub unique_id: RwSignal<String>,
    pub start_column: RwSignal<Option<StartColumn>>,
}

#[derive(Clone, Cacheable)]
pub struct OnboardingPageModel {
    pub container_list: RwSignal<Vec<ContainerType>>,
    pub start_column: StartColumn,
}

impl From<&CollectionsPageModel> for PaginationSignals {
    fn from(container: &CollectionsPageModel) -> Self {
        let pending_signal = container.pagination_pending;
        let link_signal = container.pagination_link;
        let jic_signal = None;
        let signals: PaginationSignals = (link_signal, jic_signal, pending_signal);
        signals
    }
}
