#[cfg(not(test))]
use crate::network::collection_initial::CollectionsRequests;
use crate::page::helpers_sig::create_default_home_page_request;
#[cfg(test)]
use crate::test_assets::mocks::MockNetworkClient as NetworkClient;
#[double]
use beekeeper::BeekeeperContext;
use cache::ExpirableLruCacheRc;
use collection_types::network_types::CollectionsPage;
use ignx_compositron::{context::AppContext, metrics::metric, reactive::use_context};
use location::RustPage;
use mockall_double::double;
use network::common::lrc_edge_constants::CollectionInitialTypes;
#[cfg(not(test))]
use network::NetworkClient;
use std::rc::Rc;

// GRCOV_BEGIN_COVERAGE
pub fn prefetch_collection_initial_if_allowed(
    ctx: &AppContext,
    page_id: String,
    page_type: String,
    service_token: Option<String>,
    cache: &ExpirableLruCacheRc<String, CollectionsPage>,
) {
    let client = NetworkClient::new(ctx);
    let beekeeper = use_context::<BeekeeperContext>(ctx.scope);
    if let Some(beekeeper) = beekeeper {
        if beekeeper
            .as_ref()
            .is_circuit_breaker_enabled(&page_type, &page_id)
        {
            log::warn!("Not prefetching {page_id} {page_type} due to circuit breaker.");
            metric!("Rust.Cache.CircuitBreakerActive", 1, "pageType" => &page_type);
            return;
        }
    }

    client.prefetch_collection_initial(
        RustPage::RUST_COLLECTIONS,
        CollectionInitialTypes::NavItem,
        &create_default_home_page_request(page_id, page_type, service_token, ctx.scope()),
        Rc::clone(cache),
    );
}

#[cfg(test)]
mod tests {
    use std::time::Duration;

    use super::*;
    use crate::test_assets::mocks::MockNetworkClient;
    use crate::test_assets::mocks::__mock_MockNetworkClient::__new::Context;
    use beekeeper::MockBeekeeper;
    use cache::expirable_lru_cache::ExpirableLruCache;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::reactive::provide_context;
    use network::common::lrc_edge_constants::DynamicFeature;
    use rstest::rstest;
    use rust_features::{create_mock_rust_features, MockRustFeaturesBuilder};
    use serial_test::serial;
    use taps_parameters::{MockTapsParameterStore, TapsParameterStore};

    #[rstest]
    #[serial]
    #[case(true, 0)]
    #[serial]
    #[case(false, 1)]
    fn should_not_prefetch_if_circuit_breaker_is_enabled(
        #[case] circuit_breaker_enabled: bool,
        #[case] number_of_collections_initial_calls: usize,
    ) {
        launch_only_app_context(
            move |ctx: ignx_compositron::prelude::safe::AppContext<'_>| {
                MockRustFeaturesBuilder::new()
                    .set_is_rust_collection_enabled(true)
                    .set_is_pointer_control_enabled(false)
                    .set_is_sterling_profiles_enabled(false)
                    .build_into_context(ctx.scope());

                let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
                let _client = mock_client(number_of_collections_initial_calls);
                let mut mock_beekeeper = MockBeekeeper::default();
                mock_beekeeper
                    .expect_is_circuit_breaker_enabled()
                    .return_const(circuit_breaker_enabled);
                let mock_beekeeper_context = Rc::new(mock_beekeeper);
                provide_context::<BeekeeperContext>(ctx.scope, mock_beekeeper_context);
                prefetch_collection_initial_if_allowed(
                    &ctx,
                    "".to_string(),
                    "".to_string(),
                    None,
                    &cache,
                )
            },
        );
    }

    #[rstest]
    #[serial]
    #[case(true, vec!["role1".to_string(), "role2".to_string()], "retrieved-scheme")]
    #[serial]
    #[case(false, vec![], "living-room-react-focus")]
    fn should_pass_taps_roles(
        #[case] context_provided: bool,
        #[case] expected_roles: Vec<String>,
        #[case] expected_presentation_scheme: &'static str,
    ) {
        launch_only_app_context(move |ctx| {
            MockRustFeaturesBuilder::new()
                .set_is_rust_collection_enabled(true)
                .set_is_pointer_control_enabled(false)
                .set_is_sterling_profiles_enabled(false)
                .build_into_context(ctx.scope());

            let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
            let client_context = MockNetworkClient::new_context();
            client_context.expect().returning(move |_| {
                let expected_roles: Vec<String> = expected_roles.clone();

                let mut mock_client = MockNetworkClient::default();

                mock_client
                    .expect_prefetch_collection_initial()
                    .times(1)
                    .withf(move |_, _, params, _| {
                        params.container_request_base.taps_roles == expected_roles
                            && params.container_request_base.presentation_scheme
                                == expected_presentation_scheme
                    })
                    .return_const(());
                mock_client
            });

            if context_provided {
                let mut mock_parameters = MockTapsParameterStore::new();
                mock_parameters
                    .expect_get_collections_roles()
                    .times(1)
                    .returning(|| vec!["role1".to_string(), "role2".to_string()]);

                mock_parameters
                    .expect_get_presentation_scheme()
                    .times(1)
                    .returning(|_| "retrieved-scheme".to_string());

                provide_context::<Rc<dyn TapsParameterStore>>(
                    ctx.scope(),
                    Rc::new(mock_parameters),
                );
            }

            prefetch_collection_initial_if_allowed(
                &ctx,
                "".to_string(),
                "".to_string(),
                None,
                &cache,
            )
        });
    }

    #[rstest]
    #[serial]
    #[case(false, false, vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels])]
    #[serial]
    #[case(true, false, vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::GLOBAL_TRAINING])]
    #[serial]
    #[case(false, true, vec![DynamicFeature::HERO_IMAGE_OPTIONAL, DynamicFeature::ENABLE_CSIR, DynamicFeature::PromotionalBannerSupported, DynamicFeature::LinearStationInAllCarousels, DynamicFeature::LinearStationsInHero])]
    fn should_pass_additional_dynamic_features(
        #[case] enable_global_training_treatment: bool,
        #[case] enable_linear_stations_in_hero: bool,
        #[case] expected_additional_dynamic_features: Vec<DynamicFeature>,
    ) {
        launch_only_app_context(move |ctx| {
            provide_context(ctx.scope(), create_mock_rust_features(true, false, false));
            let cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
            let client_context = MockNetworkClient::new_context();
            client_context.expect().returning(move |_| {
                let mut mock_client = MockNetworkClient::default();

                let expected_additional_dynamic_features =
                    expected_additional_dynamic_features.clone();

                mock_client
                    .expect_prefetch_collection_initial()
                    .times(1)
                    .withf(move |_, _, params, _| {
                        params.container_request_base.additional_dynamic_features
                            == expected_additional_dynamic_features
                    })
                    .return_const(());
                mock_client
            });

            MockRustFeaturesBuilder::new()
                .set_is_global_training_enabled(enable_global_training_treatment)
                .set_is_linear_stations_in_hero_enabled(enable_linear_stations_in_hero)
                .set_is_promo_banner_channel_decor_widget_scheme_enabled(false)
                .set_is_prime_student_free_trial_enabled(false)
                .set_is_prime_student_free_trial_enabled(false)
                .set_is_multiview_discovery_cx_enabled(false)
                .build_into_context(ctx.scope());

            prefetch_collection_initial_if_allowed(
                &ctx,
                "".to_string(),
                "".to_string(),
                None,
                &cache,
            )
        });
    }

    fn mock_client(collections_calls: usize) -> Context {
        let client_context = MockNetworkClient::new_context();
        client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();
            mock_client
                .expect_prefetch_collection_initial()
                .times(collections_calls)
                .return_const(());
            mock_client
        });

        client_context
    }
}
