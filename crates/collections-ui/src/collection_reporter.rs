#[cfg(not(test))]
pub mod csm_count_metric_reporter {
    use ignx_compositron::prelude::metric;

    pub fn report_hero_csm_count_metric(
        event_type: &str,
        container_type: &str,
        container_slot_index: u32,
        item_producer_id: &str,
    ) {
        metric!(
            "CSM.Impression.Reported.Hero",
            1,
            "eventType" => event_type,
            "activeLayer" => "Wasm",
            "containerType" => container_type,
            "containerSlotIndex" => container_slot_index,
            "itemProducerID" => item_producer_id
        )
    }
}

#[cfg(test)]
use mockall::automock;

#[cfg(test)]
#[automock]
pub mod csm_count_metric_reporter {
    pub fn report_hero_csm_count_metric(
        _event_type: &str,
        _container_type: &str,
        _container_slot_index: u32,
        _item_producer_id: &str,
    ) {
    }
}
