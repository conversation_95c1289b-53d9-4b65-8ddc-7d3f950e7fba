{"resource": {"containerList": [{"id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt68ioZhZGRvbnOLhGhvbWWMjqUxOjFWS1pJRTVDWFRWMlYjI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy", "items": [{"title": "The Buccaneers - Season 2", "synopsis": "A group of American girls travel to 1870s London. Sent for husbands and status, their hearts are set on more than that.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "gti": "amzn1.dv.gti.d7035ae1-dc46-4b31-9efe-43d7a7089f94", "transformItemId": "amzn1.dv.gti.d7035ae1-dc46-4b31-9efe-43d7a7089f94", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_TheBuccaneers_appletv_CS_Pride/6cb5449a-da60-4650-8ccf-8e8c0e08dd47.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_TheBuccaneers_appletv_CS_Pride/de09bc18-48c2-4e74-a929-a9e706639deb.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/logos/channels-logo-color._CB562869875_.png", "providerLogoImageMetadata": {"height": 756, "width": 1999}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d7035ae1-dc46-4b31-9efe-43d7a7089f94", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS95ecc2_1_1", "itemUUID": "pvad-prod-na_1750260395529_1182304170_naws_0", "placementId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS95ecc2_1_1", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "target": "acquisition", "label": "Watch with Apple TV+{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "benefitId": "appletvus", "offerToken": "amzn.dv.offertoken.v2:2: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", "metadataActionType": "AcquisitionSVOD"}}, {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d7035ae1-dc46-4b31-9efe-43d7a7089f94", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS95ecc2_1_1", "itemUUID": "pvad-prod-na_1750260395529_1182304170_naws_0", "placementId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS95ecc2_1_1", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d7035ae1-dc46-4b31-9efe-43d7a7089f94", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS95ecc2_1_1", "itemUUID": "pvad-prod-na_1750260395529_1182304170_naws_0", "placementId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS95ecc2_1_1", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Apple TV+", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Free trial of Apple TV+"]}, "INFORMATIONAL_MESSAGE_SLOT": {"message": "Terms apply", "type": "MESSAGE", "messages": ["Terms apply"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW SEASON", "level": "INFO", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "TV-MA", "contentType": "SEASON", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/65e2bd866ddab06aa8c0e5d7b3f5b8eb091ff0bf41590806a895f0687196b460.jpg", "genres": ["Drama", "Romance", "Romance > Historical"], "cardType": "HERO_CARD"}, {"title": "Patience, Season 1", "synopsis": "<PERSON><PERSON>, an intuitive detective with an impressive track record recognizes <PERSON><PERSON>’ powers of deduction and wants her on her team.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "TV-14", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.c98ccc0c-c772-4573-895f-ec4b20977223", "transformItemId": "amzn1.dv.gti.c98ccc0c-c772-4573-895f-ec4b20977223", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_Patience_S1_PBSMasterpiece_Portfolio/c7e6c3a2-f247-4788-84f0-2b89574abed5.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_Patience_S1_PBSMasterpiece_Portfolio/7b606780-3535-4d8c-9307-9c13e50d8743.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/masterpiece/logos/channels-logo-color._CB582425541_.png", "providerLogoImageMetadata": {"height": 255, "width": 2000, "scalarHorizontal": "default"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c98ccc0c-c772-4573-895f-ec4b20977223", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HScff7b8_1_2", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HScff7b8_1_2", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.c98ccc0c-c772-4573-895f-ec4b20977223", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HScff7b8_1_2", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HScff7b8_1_2", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c98ccc0c-c772-4573-895f-ec4b20977223", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HScff7b8_1_2", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HScff7b8_1_2", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of PBS Masterpiece or buy", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Free trial of PBS Masterpiece or buy"]}, "INFORMATIONAL_MESSAGE_SLOT": {"message": "Terms apply", "type": "MESSAGE", "messages": ["Terms apply"]}, "HIGH_VALUE_MESSAGE_SLOT": {"message": "New episode Sunday", "type": "MESSAGE", "messages": ["New episode Sunday"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "SEASON PREMIERE", "level": "INFO", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "TV-14", "contentType": "SEASON", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c7bb133f49f53830263bdb3028665b3a7049ba2a93bc027f19b22149aa5cdf30.jpg", "genres": ["Drama", "Drama > Crime"], "overallRating": 5, "totalReviewCount": 1, "cardType": "HERO_CARD"}, {"title": "Private Eyes - Season 4", "synopsis": "<PERSON> partners with skilled <PERSON><PERSON><PERSON><PERSON>, forming an unexpected yet formidable investigative duo in their quest for justice.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "TV-PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.df53eb75-24c9-4eea-8289-d6b088407ce8", "transformItemId": "amzn1.dv.gti.df53eb75-24c9-4eea-8289-d6b088407ce8", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_PrivateEyes_S4_Hallmark_Portfolio/31d5e440-0cc0-4241-8568-14fe1883a345.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_PrivateEyes_S4_Hallmark_Portfolio/8a221843-6347-4ef6-ad89-6885cc19dbca.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hallmark/logos/channels-logo-white._CB565032671_.png", "providerLogoImageMetadata": {"height": 711, "width": 2000, "scalarHorizontal": "emphasis"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.df53eb75-24c9-4eea-8289-d6b088407ce8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSdfc21d_1_3", "itemUUID": "pvad-prod-na_1750260395529_1182304170_naws_2", "placementId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSdfc21d_1_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "target": "acquisition", "label": "Watch with Hallmark+{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "benefitId": "hallmark", "offerToken": "amzn.dv.offertoken.v2:2: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", "metadataActionType": "AcquisitionSVOD"}}, {"target": "legacyDetail", "pageId": "amzn1.dv.gti.df53eb75-24c9-4eea-8289-d6b088407ce8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSdfc21d_1_3", "itemUUID": "pvad-prod-na_1750260395529_1182304170_naws_2", "placementId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSdfc21d_1_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.df53eb75-24c9-4eea-8289-d6b088407ce8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSdfc21d_1_3", "itemUUID": "pvad-prod-na_1750260395529_1182304170_naws_2", "placementId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSdfc21d_1_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Hallmark+", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Free trial of Hallmark+"]}, "INFORMATIONAL_MESSAGE_SLOT": {"message": "Terms apply", "type": "MESSAGE", "messages": ["Terms apply"]}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "TV-PG", "contentType": "SEASON", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c4e4f72c07bab26a5731edc41d9b06511de0e72cc25ed0cea9cf6775eafc0bb9.jpg", "genres": ["Comedy", "Drama", "Drama > Romance"], "overallRating": 4.8, "totalReviewCount": 154, "cardType": "HERO_CARD"}, {"id": "starzSub", "title": "STARZ", "transformItemId": "starzSub", "widgetType": "channel", "image": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_Starz_Untargeted_06172025_Standardhero/973454eb-fd5b-44c7-8e55-f4b00b70a579.jpeg", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_Starz_Untargeted_06172025_Standardhero/973454eb-fd5b-44c7-8e55-f4b00b70a579.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_Starz_Untargeted_06172025_Standardhero/8db62df9-14e8-4d43-9d2b-3f8a51732edb.png", "actions": [{"refMarker": "hm_add_c_XYDFye_RucYJr_1_4", "target": "signUp", "benefitId": "starzSub", "nonSupportedText": "STARZ", "uri": "https://www.amazon.com/gp/video/offers/ref=atv_3p_amc_c_6rhKJb_HSf4bf8a_1_1?benefitId=starzSub", "analytics": {"refMarker": "hm_add_c_XYDFye_RucYJr_1_4", "itemUUID": "pvad-prod-na_1750260395529_1182304170_naws_1", "placementId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemProducerID": "Superhero-Sonata-Pinned-nontitle"}}], "entitlementMessaging": {"INFORMATIONAL_MESSAGE_SLOT": {"message": "Limited time offer. Terms apply.", "messages": ["Limited time offer. Terms apply."]}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe for $1.99/month for 2 month(s) and $10.99/month thereafter", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"title": "Flight Risk", "synopsis": "<PERSON>, <PERSON>, and <PERSON><PERSON> lead this high-stakes suspense thriller.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "gti": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "transformItemId": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_FlightRisk_STARZ_CS/3c580158-0ec8-4b4f-ba1c-e10f3be45603.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_FlightRisk_STARZ_CS/57398b19-4520-4cf6-86b2-a84651b9281e.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-color._CB555281392_.png", "providerLogoImageMetadata": {"height": 471, "width": 2000, "scalarHorizontal": "subtle"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS63f63f_1_5", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS63f63f_1_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS63f63f_1_5", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS63f63f_1_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS63f63f_1_5", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS63f63f_1_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe for $1.99/month for 2 month(s) and $10.99/month thereafter, or rent or buy", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Subscribe for $1.99/month for 2 month(s) and $10.99/month thereafter, or rent or buy"]}, "INFORMATIONAL_MESSAGE_SLOT": {"message": "Limited time offer. Terms apply.", "type": "MESSAGE", "messages": ["Limited time offer. Terms apply."]}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "R", "contentType": "MOVIE", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/46ab55f1b1962f0c9108d2024065a46c5db529f58565ad302d70121f09ea6264.jpg", "genres": ["Action", "Action/Adventure > Crime", "Action/Adventure > Thriller", "Drama", "Thriller"], "overallRating": 3.6, "totalReviewCount": 100, "cardType": "HERO_CARD"}, {"title": "The Alto Knights", "synopsis": "The Alto Knights stars Academy Award winner <PERSON> in a dual role, directed by Academy Award-winning filmmaker <PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "gti": "amzn1.dv.gti.b3e27c76-b67d-497a-992d-b7dd44bc156d", "transformItemId": "amzn1.dv.gti.b3e27c76-b67d-497a-992d-b7dd44bc156d", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_TheAltoKnights_Max_Portfolio/3e907e6a-9f32-4059-a5e9-532e93f76e79.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_TheAltoKnights_Max_Portfolio/e5446507-cf7f-4c3b-9e9f-48d4946bb740.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hbomaxus/logos/channels-logo-color._CB546864737_.png", "providerLogoImageMetadata": {"height": 561, "width": 1999, "scalarHorizontal": "subtle"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b3e27c76-b67d-497a-992d-b7dd44bc156d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS578c35_1_6", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS578c35_1_6", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b3e27c76-b67d-497a-992d-b7dd44bc156d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS578c35_1_6", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS578c35_1_6", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b3e27c76-b67d-497a-992d-b7dd44bc156d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS578c35_1_6", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS578c35_1_6", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe for $9.99/month, rent, or buy", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Subscribe for $9.99/month, rent, or buy"]}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "R", "contentType": "MOVIE", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b13146d0fd2840e92ca922665018d41af809aabb9a48634a4c88215edbf8b34e.jpg", "genres": ["Drama", "Drama > Biography", "Drama > Crime", "Historical"], "overallRating": 4, "totalReviewCount": 432, "cardType": "HERO_CARD"}, {"title": "The Rehearsal: Season 2", "synopsis": "In season 2, the urgency of <PERSON>'s project grows as he continues helping ordinary people \"rehearse\" for life's biggest moments.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.288af0fa-05e0-4ebe-8931-76b99b9923c8", "transformItemId": "amzn1.dv.gti.288af0fa-05e0-4ebe-8931-76b99b9923c8", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_TheRehearsal_S2_Max_Portfolio/5027ee27-c027-4e1d-b5ad-69ecf2e3c4bb.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_TheRehearsal_S2_Max_Portfolio/ac645dd1-388d-43e2-bf60-61dcd567854c.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hbomaxus/logos/channels-logo-color._CB546864737_.png", "providerLogoImageMetadata": {"height": 561, "width": 1999, "scalarHorizontal": "subtle"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.288af0fa-05e0-4ebe-8931-76b99b9923c8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSfb4684_1_7", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSfb4684_1_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.288af0fa-05e0-4ebe-8931-76b99b9923c8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSfb4684_1_7", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSfb4684_1_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.288af0fa-05e0-4ebe-8931-76b99b9923c8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSfb4684_1_7", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSfb4684_1_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe for $9.99/month or buy", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Subscribe for $9.99/month or buy"]}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "18+", "contentType": "SEASON", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/80da4ea1817e61d19bc3edb4843930c0d892588b94c400c6db4664cd3c79cbb4.jpg", "genres": ["Comedy", "Documentary"], "overallRating": 5, "totalReviewCount": 7, "cardType": "HERO_CARD"}, {"title": "Papá Soltero season-1", "synopsis": "Tras perder a su esposa, <PERSON> aprender a ser papá soltero de sus tres hijos… y de una hija que acaba de conocer. Entre enredos, duelos y nuevas formas de amar, esta familia imperfecta intentará reconstruirse desde el caos.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.fc74645d-b0ae-481c-abdc-4ca99139012c", "transformItemId": "amzn1.dv.gti.fc74645d-b0ae-481c-abdc-4ca99139012c", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_PapaSoltero_S1_VixPremium_CS/1edae0fe-c6b8-46cf-ab07-df8a2ad8161a.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_PapaSoltero_S1_VixPremium_CS/9594db5d-b5bc-4233-a259-9c3d30aaa559.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-color._CB558043175_.png", "providerLogoImageMetadata": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fc74645d-b0ae-481c-abdc-4ca99139012c", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSdd8a32_1_8", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSdd8a32_1_8", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "target": "acquisition", "label": "Watch with ViX Premium{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "benefitId": "vix<PERSON><PERSON>us", "offerToken": "amzn.dv.offertoken.v2:2:u5rwQ+fmbESKSybF++zDK79iXXFqsc1NK0RjlO+ABA1XSLU9myHl7JHLbmsahostjSCC4QQKePMNhUkkCFWBem3tR6SjfHol4Y9m5tUM9lNySK/j4v/oabecjLWel+g7PgL1NbRQuwvT+HDgaTunx/qSx6l+7PdIkxPwv7wnV4PfYSvVgaU70jMvk6JHVTEU55j4HzVi2an7p+ATcLMBC473rsrnA4O+eyf5PL+O4oh8hReKJlM+Dy7kSzyxhaLRY9xFWPTJZq2aH342ekr/d4+jcXRBGLZbTWtHEDVV+IEDhNORtSw7lv5qn4y52e67LpkNT0bXEW9/QHrS/7WqJRaHqTr1bTpzokhYq9eaCbleZ0g85Yr//tfXVLLdc3DIu4REICL2J0Tf0L8ukC4JRYl5BMMKyG0WWNPKOyC0ZTJVOAYlXnwWeEldwYasqs0/+hininKX0qYTwd60anH0qv3/T75HxSk6/OS/QTPZAi1RR0r+Onss02OLih7LyuNx6x3PRec2zO8q3PXowus5Nx0K7uKItH6QhuX0Z9icLyAy2SfcGZktSKKOXTEyR46sQPi+Xwz/duypDfxIVMwohDrMQcm+EockO05IVmVo3W9V50mqLZNzfpaKNf9OeWiTZHb3XGDLvrrR7yyve47QKSBsTxtQanTqPTy7drbop2Vx1RKzzPkWtkFAnsdtEOjE86DK70zQWNKGbqUwjS5N1T3wImw5Fun/Oc2+ssOuMRf6mgi2xlfUed0UGCBfvKTIbyRaar27tmnZBDMWYeZUsVjTuB8CYikb15sj6s33rN1eEq9ykWiutlPRFRU5S74r/SOB1cIRL0D5q0ZYWiEMgRGFRpUfG7rcKsUfJ5wli2YpOMwxZbFSyCIdwCRVSeEHVT/P2NHacGuhvydbwUt8M6x1AsGQmW00GFzdfnn1VQwvKm7RFt71Sf1CYU76Qm141objupH6Hjnehte/9z4ChXkE6OHmm5L1qzW6m4ZYO/KRft9lfT95Gm7E9HKdqJQZ3p/2J4f25JPkqLHIKZDfDuC7ODYJkRZEQaomFmR+fEStAMYI8jxWH5YnE7xmzPldk+0q461RgKYdN3cO2pmpEfmlCIaERWaMSjt0G8qymusD8DDz6AKQxbt7kZx9xKFkzeSQCFT2kvxSOzK7noFc3gZNoabMFCfL0pgiojYPigW/shWS8kOHwkZ4ovvhFo8kuZQc4I5kiZCS5+gz7Qh0wAdc+2xaf9woW0M8JspBBxKH057Nc1rzA0Ms2PsF4CqaGUitRa/oFKSTlO/Zu4bD6bGaQKFe6kKvo0SbxhbceF+q9/1ez9JV5xuW5iFGFpY4JYLARb7RI2hcf9jIp54VXMhoTpJEnw180Nkr4G4ihW4KkG1o2n+Vu2G4O1Hxc7HCUnu+nWx5X6RWXvO3cmpCTKfcYIhIjbLQgr0enPfdXQnt1QyV7eLmOfTdExOCHH3uYySzOtPKHiHa99ecwwn6jExLkCP+2Dd91e3GNfLtkrRHqT4fZvu+26ZLDIMl2LdCK78B1ZjhjIIfTWNi8WdjnPvL4BP+3V2htx2TaWkddmyDIyT5tZwlqMYrpdDTNVeDnrGhlawMtcT7EsuhOYSzHF0P1EN+p9mu1Whdl5kbMl2fm6ZEHDyU4DW3fg8nbwgKEm7zoonCcjUdJ3lpxT6t8os5Awea9j2XbWFMOMBs4ZL+lcmmTU+p4xNEeiCtfYfbiVkjclnQB96GiTXagreAnxoDAKxKPFmXNOLdZm7/I5dKF0xQYfQEeMFavMTiz1ViKke9V4zAtBXZPbDsht+2gfyfsnp6F3zZw8OykmpQFbSFBX+NLy3y7kz9ceGRX6WBUND1sNoNwOyTJQJr0vxxUETP9JqPJ72nAgv2U0pCERxii1Se3IDl4HAlXp+53FBdY4pEdGKiYKVubjJ1ZuVKnf+jaDu5CB6m7GkqKZrHIScdMwDNpY5OUUO4K0z3DscY2HLFyRFC8b9eGhSDkE08CQb7EPtIQ78Ig/jtCAohRA6vRFZKNau4X+GBkuT66qRbHle1N2NLh4iYyMMCkOxLfm1NRcwVzmQsHA2dJxnkTZu5rQogV2F+VPDG8xVq3lmddiEnK7Sw5w7L2oIv79II0TSc9S9E4TMbCytgoEEPIEAi6zSwcoaBKNpBAs2FKqvCgO5LUd5m/yVHFvHGbMQ8y4RY4SIANE4IQS7qa9f1cHE0aVJcG+/BeOIClVO00HEV", "metadataActionType": "AcquisitionSVOD"}}, {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fc74645d-b0ae-481c-abdc-4ca99139012c", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSdd8a32_1_8", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSdd8a32_1_8", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fc74645d-b0ae-481c-abdc-4ca99139012c", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSdd8a32_1_8", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSdd8a32_1_8", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Free trial of ViX Premium"]}, "INFORMATIONAL_MESSAGE_SLOT": {"message": "Terms apply", "type": "MESSAGE", "messages": ["Terms apply"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW SERIES", "level": "INFO", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "16+", "contentType": "SEASON", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/838d3c2d20f912a524897d6eca264a27910e22e96ad7310db4dd14a36ed61ff6.jpg", "genres": ["Comedy"], "cardType": "HERO_CARD"}, {"title": "On Patrol: Live season-3", "synopsis": "A Live Series following Law Enforcement Officers across the country, live and in real time.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "TV-14", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.1fb755ba-fc6a-4ca4-8eb2-c7b9a333b7fe", "transformItemId": "amzn1.dv.gti.1fb755ba-fc6a-4ca4-8eb2-c7b9a333b7fe", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_OnPatrolLive_S3_Reelz_Portfolio/e5466f54-240e-423f-810b-c84afe254522.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_Channels_OnPatrolLive_S3_Reelz_Portfolio/361ea186-0d52-47df-96c5-bf804fa8a384.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/reelz/logos/channels-logo-color._CB794109651_.png", "providerLogoImageMetadata": {"height": 340, "width": 1965, "scalarHorizontal": "subtle"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1fb755ba-fc6a-4ca4-8eb2-c7b9a333b7fe", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS4d6955_1_9", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS4d6955_1_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "target": "acquisition", "label": "Watch with REELZ+{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_hm_hom_c_tv_signup_3p_bb_t1JECAAAAA0lr0", "benefitId": "reelz", "offerToken": "amzn.dv.offertoken.v2:2: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", "metadataActionType": "AcquisitionSVOD"}}, {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1fb755ba-fc6a-4ca4-8eb2-c7b9a333b7fe", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS4d6955_1_9", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS4d6955_1_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1fb755ba-fc6a-4ca4-8eb2-c7b9a333b7fe", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HS4d6955_1_9", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HS4d6955_1_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of REELZ+", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Free trial of REELZ+"]}, "INFORMATIONAL_MESSAGE_SLOT": {"message": "Terms apply", "type": "MESSAGE", "messages": ["Terms apply"]}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "TV-14", "contentType": "SEASON", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c4d14151229036221539465bfc6f538dd4f31684a5b955060105cba764f331f9.jpg", "genres": ["Reality TV"], "cardType": "HERO_CARD"}, {"title": "Mountainhead", "synopsis": "​From <PERSON>, this darkly comedic satire follows a group of tech billionaire friends who reunite amid a rolling international crisis.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.218796d7-387d-440e-acd7-37f9a7b56c81", "transformItemId": "amzn1.dv.gti.218796d7-387d-440e-acd7-37f9a7b56c81", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_Mountainheads_Max_Secondary/b4a5502c-14a7-4890-a529-08483aede1b1.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_Mountainheads_Max_Secondary/e19b3f80-02c9-4cf6-ad6c-d49c423b3cd3.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hbomaxus/logos/channels-logo-color._CB546864737_.png", "providerLogoImageMetadata": {"height": 561, "width": 1999, "scalarHorizontal": "subtle"}, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.218796d7-387d-440e-acd7-37f9a7b56c81", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSee3974_1_10", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSee3974_1_10", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "actions": [{"refMarker": "atv_hm_hom_c_mv_signup_3p_bb_t1JAAAAAAA0lr0", "target": "acquisition", "label": "Watch with Max", "metadata": {"refMarker": "atv_hm_hom_c_mv_signup_3p_bb_t1JAAAAAAA0lr0", "benefitId": "<PERSON><PERSON><PERSON><PERSON>", "offerToken": "amzn.dv.offertoken.v2:2:L4wvjQEMOF326Smuh8BStOMugzNNW/WllAnT+S3wq00vGKIvchHpCKR1QmqaHyC8r9Qrs1vZddVUNfcHSPAwdhkRq3H0vbA/hOCGUat65fyNr8quyHOwIY1NIrT0dY3+P+WxaHTfhuU3xmxjw/FOvJ3iGOkcr0Qau9Y1Nh2B6PC74MmTZq/W9hyQ3+uximaZRM4FublM+3x71o3IVqnrrrDtfX8/Hq2mY8/LXIn4FtlkwhLT9B4cN7z1YRUUYsAzHKpf9Litc1zvtXBvPEJpIICL+Ig2WxBcKr8nrUHflELot54IiBJrg1G7bS/BhJ1QsLdXY08nbSN58uR4lBHEThwWdXpD2PzvxYUUSbumDSHinuJChosaEGTwRwVVh0856XM7iVTT9e6hywcZbwMOcNAEz+OYVuTBYKrgd4aUZ/w4gJp//pl8kN3nD3q3gqohOFsxpQFvWOy//n9HBbsQ2qghOiKO4JM4SNjfcOgqzKUGrr3xykAHcdPLPvUce1lKYcYFk+SbfEbfLnyWqQwV4h3YGaERK7aP/y5oyuTv6bVt3P0f8zXe5CtM3JGAeMpCm6ffpUFnGJhEB25Y68o3DYSOTTBIGbk2diceRQ2/Vp16mhJf8nHENM8FFPZ7DhVxeG/YD7EZdQ+WdVpsEVFZk62RqswS91tws1Mkg+g83Ar+bTV9+4WDjuMzsyTMKoDLqMhO9i7/729adsN22wZbEhGTBZtMqmNk6SeVN7SwYlObD4djg2O7pQWk9h/GKheDDWAjHOZUeMJtb7OA12IIAXo2VK0jVLt+XXUBZc7yBfE34Hcv+4uUg7Dr5J0dIofJ5YO2HJIQJeNhZqMWnexvOZpc4cj5XH0hwUl650JI5TV/XceRs3z+my3eieEMrfvJ7gQKI27pbyW1Dd0xXbWgUg2M9NnO+jC+vVNPjlpDYrW1fCT1FkkS8RWu+nuw8AOk/dQRJHqYrcdfjxQkPZGLxJKaOP/HMso7uPWmeX+i+6ivR/33CJf0yzZhUrwwf6LJCLFoZ7TU/mjQppAyXUerq+PGKpXvNBfOWcrPw212/bGvaxCOGbAFL7B/Qb8P2OeDiWs9shoBb9TtyfDnN7+XAA9mXIFLF0nIpEprE2BGcuMxxYNuoqmdmgWl39srILioFRdc6jwuq9i8kzCpV/Fl5J+cVt0JaCmmAHBrOMmQrJuTA5NnQdlP1+Dt5xENPuviJXq0nGzSSXGDxRUp9lnQfgKBY027skYi3jScHEapJwr8VApCynQp2RfOxzAfUuGnD22A/kP5JTNXMIiix7wOl1sZ+GGEM56Tq3wAhdiKUg2eN6x0irzKMsjtnhW+XBcYKhIPR4m9IaxL4ocd9jABAjVsgVsfz6gtBKgl60QfrPg=", "metadataActionType": "AcquisitionSVOD"}}, {"target": "legacyDetail", "pageId": "amzn1.dv.gti.218796d7-387d-440e-acd7-37f9a7b56c81", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSee3974_1_10", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSee3974_1_10", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "isInWatchlist": false, "moreDetailsAction": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.218796d7-387d-440e-acd7-37f9a7b56c81", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_XYDFye_HSee3974_1_10", "itemProducerID": "awareness-dome-channels"}, "refMarker": "hm_add_c_XYDFye_HSee3974_1_10", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe for $9.99/month", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Subscribe for $9.99/month"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW MOVIE", "level": "INFO", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "18+", "contentType": "MOVIE", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/69a5b863db6e157ff0a8052b9545a9ec5f3099b4ad593e0b03bb6bff17bbdc53.jpg", "genres": ["Comedy", "Drama"], "overallRating": 2.4, "totalReviewCount": 22, "cardType": "HERO_CARD"}], "analytics": {"refMarker": "hm_add_c_XYDFye_1", "ClientSideMetrics": "476|Cl8KMkhvbWVDbGVhblNsYXRlU3RhbmRhcmRIZXJvUGFyZW50TGl2ZURlZmF1bHREZWZhdWx0Eg8xOjFWS1pJRTVDWFRWMlYaEDI6RFlFRkIxOUY1QkIzQjQiBlhZREZ5ZRI+CgRob21lEgZhZGRvbnMiBmNlbnRlcioAMiQxNzlmZmZkNy0xOTI5LTQ3YjItYmZkZi0zZDc3MDBlNmQ5YmYaDHN1YnNjcmlwdGlvbiIDYWxsKgAyDGhlcm9DYXJvdXNlbDoJQXdhcmVuZXNzQglTdXBlckhlcm9KCGhlcmN1bGVzShRzaG93VW5kZXJFdmVyeUZpbHRlclILbm90RW50aXRsZWRaAGIMU3RhbmRhcmRIZXJvaAFyAHo4a003RDZYcjVJYS1BbGVWUkFlcWp5YjVJLTV0OG1lb1N3SFJYMlpqMHMzXzZBcE0zVEtfbWpnPT2CAQVmYWxzZYoBAJIBAA=="}, "tags": [], "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24=", "type": "STANDARD_HERO"}, {"title": "Subscriptions you might like", "unentitledText": "Subscriptions – Sponsored", "unentitledItems": [{"id": "amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "title": "Hallmark+ & STARZ", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-c82c6b4a-f555-b842-5335-547f6f30d804/browse/brand_tile._CB793770764_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-c82c6b4a-f555-b842-5335-547f6f30d804/browse/brand_tile._CB793770764_.jpg", "actions": [{"refMarker": "hm_add_c_dG5XXv_HS48a437_2_1", "target": "signUp", "benefitId": "amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS48a437_2_1", "placementId": "channelhubMySubscriptions", "itemUUID": "pvad-prod-na_1750260395537_206998325_naws_2"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $13.99/month (Save $4.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$18.98{end}{end} $13.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.06a41896-bcb5-42fd-bd69-************", "title": "STARZ", "synopsis": "STARZ", "transformItemId": "amzn1.dv.channel.06a41896-bcb5-42fd-bd69-************", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/heroes/featured-offer-tile_1920x1080._CB549202690_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/heroes/featured-offer-tile_1920x1080._CB549202690_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-focus._CB558065669_.png", "actions": [{"refMarker": "hm_add_c_dG5XXv_HS04d5a8_2_2", "target": "signUp", "benefitId": "starzSub", "nonSupportedText": "STARZ", "uri": "https://www.amazon.com/gp/video/offers/ref=atv_3p_amc_c_6rhKJb_HSf4bf8a_1_1?benefitId=starzSub", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS04d5a8_2_2", "placementId": "channelhubMySubscriptions", "itemUUID": "pvad-prod-na_1750260395537_206998325_naws_0"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.1a29015f-71e4-49a9-bb12-265d00ecf1ae", "title": "Paramount+", "synopsis": "Live sports. Breaking news. A mountain of entertainment.", "transformItemId": "amzn1.dv.channel.1a29015f-71e4-49a9-bb12-265d00ecf1ae", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/heroes/featured-offer-tile_1920x1080._CB794619275_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/heroes/featured-offer-tile_1920x1080._CB794619275_.jpg", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.1a29015f-71e4-49a9-bb12-265d00ecf1ae", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS224e25_2_3", "placementId": "channelhubMySubscriptions", "itemUUID": "pvad-prod-na_1750260395537_206998325_naws_1"}, "refMarker": "hm_add_c_dG5XXv_HS224e25_2_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.9656551b-be9b-4ba2-b80b-7c7266eb6616", "title": "Apple TV+", "synopsis": "Award-winning shows and movies, plus Friday Night Baseball", "transformItemId": "amzn1.dv.channel.9656551b-be9b-4ba2-b80b-7c7266eb6616", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/heroes/featured-offer-tile_1920x1080._CB546256954_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/heroes/featured-offer-tile_1920x1080._CB546256954_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/logos/channels-logo-focus._CB543859421_.png", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.9656551b-be9b-4ba2-b80b-7c7266eb6616", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HSd8d29e_2_4", "placementId": "channelhubMySubscriptions", "itemUUID": "pvad-prod-na_1750260395537_206998325_naws_3"}, "refMarker": "hm_add_c_dG5XXv_HSd8d29e_2_4", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.0350778f-c66c-40e5-8015-bec5c0139fe8", "title": "ViX Premium", "synopsis": "Original series, movie premieres, and sports in Spanish", "transformItemId": "amzn1.dv.channel.0350778f-c66c-40e5-8015-bec5c0139fe8", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/heroes/featured-offer-tile_1920x1080._CB792652201_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/heroes/featured-offer-tile_1920x1080._CB792652201_.jpg", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.0350778f-c66c-40e5-8015-bec5c0139fe8", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HSe8e36e_2_5", "placementId": "channelhubMySubscriptions", "itemUUID": "pvad-prod-na_1750260395537_206998325_naws_4"}, "refMarker": "hm_add_c_dG5XXv_HSe8e36e_2_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "title": "Max Standard & STARZ", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9be9c7ca-3bd4-4bfe-b49f-7028dc033453/browse/brand_tile._CB792406308_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9be9c7ca-3bd4-4bfe-b49f-7028dc033453/browse/brand_tile._CB792406308_.jpg", "actions": [{"refMarker": "hm_add_c_dG5XXv_HS17b1b5_2_6", "target": "signUp", "benefitId": "amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS17b1b5_2_6"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $20.99/month (Save $6.99/month)", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$27.98{end}{end} $20.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.05b8c408-6a8a-468a-a896-ef7432a1ce31", "title": "Max", "synopsis": "The one to watch for HBO, hit series, movies, reality, and more", "transformItemId": "amzn1.dv.channel.05b8c408-6a8a-468a-a896-ef7432a1ce31", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hbomaxus/heroes/featured-offer-tile_1920x1080._CB546863847_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hbomaxus/heroes/featured-offer-tile_1920x1080._CB546863847_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hbomaxus/logos/channels-logo-focus._CB546864737_.png", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.05b8c408-6a8a-468a-a896-ef7432a1ce31", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS16f4fb_2_7"}, "refMarker": "hm_add_c_dG5XXv_HS16f4fb_2_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "title": "MGM+ & AMC+ Ad-Free", "synopsis": "Captivating original series, hit movies, and more", "transformItemId": "amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-bfc5dc68-dbec-4d65-b1ca-3e432c6d0296/browse/brand_tile._CB791821275_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-bfc5dc68-dbec-4d65-b1ca-3e432c6d0296/browse/brand_tile._CB791821275_.jpg", "actions": [{"refMarker": "hm_add_c_dG5XXv_HS816020_2_8", "target": "signUp", "benefitId": "amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS816020_2_8"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $11.99/month (Save $4.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$16.98{end}{end} $11.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.ccf8ed18-a5c7-44ec-95cd-94a8e9eaf3ec", "title": "AMC+", "synopsis": "AMC+ and all of Shudder, Sundance Now, and IFC Films Unlimited", "transformItemId": "amzn1.dv.channel.ccf8ed18-a5c7-44ec-95cd-94a8e9eaf3ec", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/amcplus/heroes/featured-offer-tile_1920x1080._CB540870468_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/amcplus/heroes/featured-offer-tile_1920x1080._CB540870468_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/amcplus/logos/channels-logo-focus._CB558057714_.png", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.ccf8ed18-a5c7-44ec-95cd-94a8e9eaf3ec", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS23ae50_2_9"}, "refMarker": "hm_add_c_dG5XXv_HS23ae50_2_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "title": "BritBox & MGM+", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-b95e078c-4969-4a27-a49e-742d095a018c/browse/brand_tile._CB796587830_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-b95e078c-4969-4a27-a49e-742d095a018c/browse/brand_tile._CB796587830_.jpg", "actions": [{"refMarker": "hm_add_c_dG5XXv_HS15892d_2_10", "target": "signUp", "benefitId": "amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS15892d_2_10"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $11.99/month (Save $3.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$15.98{end}{end} $11.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.1cd832b6-c298-4106-b4ff-dc9ce63304ea", "title": "MGM+", "synopsis": "Acclaimed original series, blockbuster movies, and more", "transformItemId": "amzn1.dv.channel.1cd832b6-c298-4106-b4ff-dc9ce63304ea", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/epix/heroes/featured-offer-tile_1920x1080._CB796111591_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/epix/heroes/featured-offer-tile_1920x1080._CB796111591_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/epix/logos/channels-logo-focus._CB558067261_.png", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.1cd832b6-c298-4106-b4ff-dc9ce63304ea", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HSf02d5a_2_11"}, "refMarker": "hm_add_c_dG5XXv_HSf02d5a_2_11", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.ebbab5af-5874-4116-8d17-017a69c25d85", "title": "Crunchyroll", "synopsis": "All you can anime", "transformItemId": "amzn1.dv.channel.ebbab5af-5874-4116-8d17-017a69c25d85", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/crunchyrollus/heroes/featured-offer-tile_1920x1080._CB549134392_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/crunchyrollus/heroes/featured-offer-tile_1920x1080._CB549134392_.jpg", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.ebbab5af-5874-4116-8d17-017a69c25d85", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS7f55be_2_12"}, "refMarker": "hm_add_c_dG5XXv_HS7f55be_2_12", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "title": "STARZ & BET+ Premium", "synopsis": "captivating series, movies, reality, and more", "transformItemId": "amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9770b7a2-5bc8-401e-a101-4d7d6a93e191/browse/brand_tile._CB791815530_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9770b7a2-5bc8-401e-a101-4d7d6a93e191/browse/brand_tile._CB791815530_.jpg", "actions": [{"refMarker": "hm_add_c_dG5XXv_HSf97999_2_13", "target": "signUp", "benefitId": "amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "analytics": {"refMarker": "hm_add_c_dG5XXv_HSf97999_2_13"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $15.99/month (Save $5.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$21.98{end}{end} $15.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.00b9cf5e-4ece-4fde-9c11-cc4f6869fb1f", "title": "BET+", "synopsis": "1000+ hours of Black culture: movie and TV faves, plus exclusives", "transformItemId": "amzn1.dv.channel.00b9cf5e-4ece-4fde-9c11-cc4f6869fb1f", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/betplus/heroes/featured-offer-tile_1920x1080._CB545717206_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/betplus/heroes/featured-offer-tile_1920x1080._CB545717206_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/betplus/logos/channels-logo-focus._CB583956482_.png", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.00b9cf5e-4ece-4fde-9c11-cc4f6869fb1f", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS77f704_2_14"}, "refMarker": "hm_add_c_dG5XXv_HS77f704_2_14", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "title": "Max Standard & Cinemax", "synopsis": "Groundbreaking originals, epic series, hit movies, and more", "transformItemId": "amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-25722c04-d551-4eea-8ee3-7041d42fd6e4/browse/brand_tile._CB791821195_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-25722c04-d551-4eea-8ee3-7041d42fd6e4/browse/brand_tile._CB791821195_.jpg", "actions": [{"refMarker": "hm_add_c_dG5XXv_HS12ccce_2_15", "target": "signUp", "benefitId": "amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS12ccce_2_15"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $21.99/month (Save $4.99/month)", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$26.98{end}{end} $21.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.82d58d06-360f-4b41-a276-945c01205d62", "title": "Cinemax", "synopsis": "hit movies and action-packed series that will keep you on the edge of your seat", "transformItemId": "amzn1.dv.channel.82d58d06-360f-4b41-a276-945c01205d62", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cinemax/heroes/featured-offer-tile_1920x1080._CB540870619_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cinemax/heroes/featured-offer-tile_1920x1080._CB540870619_.jpg", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.82d58d06-360f-4b41-a276-945c01205d62", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HSa845d4_2_16"}, "refMarker": "hm_add_c_dG5XXv_HSa845d4_2_16", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "tribecashortlist", "title": "MovieSphere+", "synopsis": "Hundreds of blockbuster movies, cult classics, and hit series", "transformItemId": "tribecashortlist", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/tribecashortlist/heroes/featured-offer-tile_1920x1080._CB548629412_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/tribecashortlist/heroes/featured-offer-tile_1920x1080._CB548629412_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/tribecashortlist/logos/channels-logo-focus._CB540608047_.png", "actions": [{"target": "landing", "pageId": "tribecashortlist", "pageType": "subscription", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS4cfd59_2_17"}, "refMarker": "hm_add_c_dG5XXv_HS4cfd59_2_17", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "title": "STARZ & discovery+ Ad-Free", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-a94c7091-7741-f846-aff4-762992403be9/browse/brand_tile._CB796577014_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-a94c7091-7741-f846-aff4-762992403be9/browse/brand_tile._CB796577014_.jpg", "actions": [{"refMarker": "hm_add_c_dG5XXv_HS0cc865_2_18", "target": "signUp", "benefitId": "amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS0cc865_2_18"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $14.99/month (Save $5.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$20.98{end}{end} $14.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.68d5514b-a272-4e1c-a781-8d799bdf6b8e", "title": "discovery+", "synopsis": "Real-life stories, exclusive shows, and bold originals", "transformItemId": "amzn1.dv.channel.68d5514b-a272-4e1c-a781-8d799bdf6b8e", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/discoveryplus/heroes/featured-offer-tile_1920x1080._CB540870192_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/discoveryplus/heroes/featured-offer-tile_1920x1080._CB540870192_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/discoveryplus/logos/channels-logo-focus._CB559833836_.png", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.68d5514b-a272-4e1c-a781-8d799bdf6b8e", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS4b1976_2_19"}, "refMarker": "hm_add_c_dG5XXv_HS4b1976_2_19", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.e7447184-eac5-4a14-8d99-a3bc2d3cd1a9", "title": "HIDIVE", "synopsis": "Anime hits, essentials, and originals from Japan", "transformItemId": "amzn1.dv.channel.e7447184-eac5-4a14-8d99-a3bc2d3cd1a9", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hidiveus/heroes/featured-offer-tile_1920x1080._CB549168964_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hidiveus/heroes/featured-offer-tile_1920x1080._CB549168964_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hidiveus/logos/channels-logo-focus._CB583953377_.png", "actions": [{"target": "landing", "pageId": "amzn1.dv.channel.e7447184-eac5-4a14-8d99-a3bc2d3cd1a9", "pageType": "channel", "analytics": {"refMarker": "hm_add_c_dG5XXv_HS9c4df1_2_20"}, "refMarker": "hm_add_c_dG5XXv_HS9c4df1_2_20", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}}, "cardType": "CHANNEL_CARD"}], "facet": {}, "items": [], "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyODlPQzQ0Q0VTVUFTIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Subscription", "entitlement": "NotEntitled", "analytics": {"refMarker": "hm_add_c_dG5XXv_2", "ClientSideMetrics": "508|CnUKR0NvbWJpbmVkQ2hhbm5lbHNDYXJvdXNlbFVuZW50aXRsZWRPZmZlclN0YXRlRnJlZVRyaWFsTGl2ZURlZmF1bHREZWZhdWx0EhAxOjEyODlPQzQ0Q0VTVUFTGhAyOkRZODkyQkRDQkU0QTM5IgZkRzVYWHYSPgoEaG9tZRIGYWRkb25zIgZjZW50ZXIqADIkMTc5ZmZmZDctMTkyOS00N2IyLWJmZGYtM2Q3NzAwZTZkOWJmGgxzdWJzY3JpcHRpb24iA2FsbCoAMg9mYWNldGVkQ2Fyb3VzZWw6E0hlcm9Db250ZW50UHJvdmlkZXJCEENvbWJpbmVkQ2hhbm5lbHNKE2FsbENoYW5uZWxzQ2Fyb3VzZWxSC25vdEVudGl0bGVkWgBiBU5vZGVzaAJyAHo4a003RDZYcjVJYS1BbGVWUkFlcWp5YjVJLTV0OG1lb1N3SFJYMlpqMHMzXzZBcE0zVEtfbWpnPT2CAQVmYWxzZYoBAJIBAA=="}, "tags": [], "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24=", "seeMore": {"displayPlacement": "Start", "linkText": "Browse all", "accessibilityText": "Browse all", "action": {"target": "Landing", "refMarker": "atv_subs_hom_brws_sub", "pageId": "default", "pageType": "subscription", "analytics": {"refMarker": "atv_subs_hom_brws_sub"}}, "linkAction": {"target": "landing", "pageId": "default", "pageType": "subscription", "analytics": {"refMarker": "atv_subs_hom_brws_sub"}, "refMarker": "atv_subs_hom_brws_sub"}}, "type": "NODES"}, {"title": "$1.99/Month for 2 Months", "description": "Regularly $10.99/month. Limited-time offer. Terms apply.", "titleImageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/US_CHANNELS_Starz_Untargeted_PricePromotion_06172025_Cover_Carousel/104904c7-130a-4508-be7f-4386748ed6cb.png", "backgroundImageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/US_CHANNELS_Starz_Untargeted_PricePromotion_06172025_Cover_Carousel/b0b9dcd4-988f-43b7-8f34-fb766730b22c.jpeg", "action": {"target": "landing", "pageId": "starzSub", "pageType": "subscription", "analytics": {"refMarker": "hm_add_c_aG97kk_3", "ClientSideMetrics": "480|CnwKTlVTQ0hBTk5FTFNTdGFyelVudGFyZ2V0ZWRQcmljZVByb21vdGlvbjA2MTcyMDI1Q292ZXJDYXJvdXNlbExpdmVEZWZhdWx0RGVmYXVsdBIQMToxMjJNTU5XVlNLSTFNTBoQMjpEWTU5NDRCQUQ0N0I5QiIGYUc5N2trEj4KBGhvbWUSBmFkZG9ucyIGY2VudGVyKgAyJDE3OWZmZmQ3LTE5MjktNDdiMi1iZmRmLTNkNzcwMGU2ZDliZhoMc3Vic2NyaXB0aW9uIgNhbGwqCHN0YXJ6U3ViMg9mYWNldGVkQ2Fyb3VzZWw6BkJyb3dzZUIOQnJvd3NlU3RyYXRlZ3lSC25vdEVudGl0bGVkWgBiBUNvdmVyaANyAHo4a003RDZYcjVJYS1BbGVWUkFlcWp5YjVJLTV0OG1lb1N3SFJYMlpqMHMzXzZBcE0zVEtfbWpnPT2CAQVmYWxzZYoBAJIBAA=="}, "refMarker": "hm_add_c_aG97kk_3", "text": "Explore more"}, "facet": {}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEyMk1NTldWU0tJMU1MIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Subscription", "entitlement": "NotEntitled", "items": [{"title": "BMF - Season 4", "gti": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "transformItemId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "synopsis": "BMF S4 continues the journey of <PERSON><PERSON><PERSON> \"Big Meech\" <PERSON><PERSON><PERSON> and <PERSON> \"Southwest T\" <PERSON><PERSON><PERSON>. They're fighting to keep their empire and their bond.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Romance"], "starringCast": ["<PERSON><PERSON><PERSON> '<PERSON>' <PERSON>", "Ajiona Alexus", "La La Anthony"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3a12aad3465f5ccdce908dd34a3a8ad035e512bd3db58ed513268bcedee8a99a.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c49996203b1714e5b076ad3f68a6912eddc806cf80df13e03b3c2ddc3269994f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/065d63801cf65cd558c6de6475da0235f6dfb3c1348499794b6436279e246d65.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5481eaed297bd6080ea144a386d3f2dba2150125e1f7cf644b0f7d8a8d79a3d6.jpg", "publicReleaseDate": 1749168000000, "overallRating": 3, "totalReviewCount": 2, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_1"}, "refMarker": "hm_add_c_aG97kk_brws_3_1", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_1"}, "refMarker": "hm_add_c_aG97kk_brws_3_1", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "showName": "BMF", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "M3GAN", "gti": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "transformItemId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "synopsis": "When a cutting-edge AI doll named <PERSON><PERSON><PERSON><PERSON> joins a family as a child's companion, its protective instincts evolve into a terrifying display of violence.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Horror", "Suspense", "Science Fiction"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "PG-13", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/a7e3df09a9c3124c2372e179d4102a6206adfe99d36376e4f6b6a914e102b50c.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9e9c169e0ce6562c257c310744763573a7df8df084f25c0268de155830e488b4.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f95c7caa5a871fab53b3aebb979ba2ff23daa4ff1959fcf8f4947361388db9d6.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/37f55a4c4d8b303bec25422e683c937b5f3ba78c653a577e23078cfefc0eab3a.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/0e81a4765c2548e263ae186a8c80f9287a8cac95dee819f0140ca704fbd0f6e0.jpg", "publicReleaseDate": 1672963200000, "runtimeSeconds": 6114, "runtime": "101 min", "overallRating": 4.3, "totalReviewCount": 1569, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_2"}, "refMarker": "hm_add_c_aG97kk_brws_3_2", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_2"}, "refMarker": "hm_add_c_aG97kk_brws_3_2", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": true}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Flight Risk", "gti": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "transformItemId": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "synopsis": "<PERSON>, <PERSON>, and <PERSON><PERSON> lead this high-stakes suspense thriller.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/46ab55f1b1962f0c9108d2024065a46c5db529f58565ad302d70121f09ea6264.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e014cfd80f4426528614072ac4a2784f95fa9988643cc749fd3b61448855b553.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e16d1ca9804368b01bbfed331bc8164c83b97a84f9099b271274bab88a9bb23d.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/4c89e173ae180b08110e4d3386d8cc175a730421b44c13a71a7edccb4090a8e6.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/17d51d419957f4f83531257cddacd2ae3aa9ee036855955b7cf1075cdbf06134.jpg", "publicReleaseDate": 1737676800000, "runtimeSeconds": 5479, "runtime": "91 min", "overallRating": 3.6, "totalReviewCount": 100, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_3"}, "refMarker": "hm_add_c_aG97kk_brws_3_3", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.160104d7-3253-49c3-b9d6-ece90f96c869", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_3"}, "refMarker": "hm_add_c_aG97kk_brws_3_3", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Season 7", "gti": "amzn1.dv.gti.a10c44b0-d21e-459d-99f9-49a1249973fc", "transformItemId": "amzn1.dv.gti.a10c44b0-d21e-459d-99f9-49a1249973fc", "synopsis": "With their love binding them over oceans and centuries, can the MacKenzies and Frasers find their way back to each other?", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Adventure", "Drama", "Fantasy", "Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c90eadcba5cb16e69ff47ac3aecc9ffd4dfdc1af18318bef73d0f48e5167794c.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e5db14c33a1b0a7b63800168491ff453a9f2b73117b79695cb0c4ee4fdd10366.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/466c93f96554a3ebc2897d10ea819cfacff7f7c9a5181b0c68588d4f05cc3c36.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/03f1f64cd0cb0d1871e005ad4308d440a9c6617519437427b183e2a06e9167ff.jpg", "publicReleaseDate": 1686873600000, "overallRating": 4.6, "totalReviewCount": 39, "seasonNumber": 7, "numberOfSeasons": 7, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a10c44b0-d21e-459d-99f9-49a1249973fc", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_4"}, "refMarker": "hm_add_c_aG97kk_brws_3_4", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBES® 1X nominee in 2019"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a10c44b0-d21e-459d-99f9-49a1249973fc", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_4"}, "refMarker": "hm_add_c_aG97kk_brws_3_4", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "showName": "Outlander", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "The Couple Next Door - Season 1", "gti": "amzn1.dv.gti.64da0df2-58e6-4183-8261-8822bb152056", "transformItemId": "amzn1.dv.gti.64da0df2-58e6-4183-8261-8822bb152056", "synopsis": "A young couple move to the suburbs and instantly strike up a friendship with their new neighbors. But are they a bit too friendly?", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/1c001fc0560d814a6e2d2aa8e78d883d856efd7f072513e826f41a412380e380.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/114f45670975908cb14fa0a211a5a0c16d870b1997a1f87f3efb44e345a05728.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/35b39df6d32cac88b203d32f0aff7a1ef094c49326259a813f93ab8fffb9f2e0.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/839abca019058b857e277e8b9fee30e3cea74178b768c9879246bf0c26799c8c.jpg", "publicReleaseDate": 1701043200000, "overallRating": 4.2, "totalReviewCount": 25, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.64da0df2-58e6-4183-8261-8822bb152056", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_5"}, "refMarker": "hm_add_c_aG97kk_brws_3_5", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "First episode free"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "First episode free", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.64da0df2-58e6-4183-8261-8822bb152056", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_5"}, "refMarker": "hm_add_c_aG97kk_brws_3_5", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "showName": "The Couple Next Door", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Never Let Go", "gti": "amzn1.dv.gti.31e3f214-89d1-43ec-bd21-65df67d7ce63", "transformItemId": "amzn1.dv.gti.31e3f214-89d1-43ec-bd21-65df67d7ce63", "synopsis": "When an unspeakable evil takes over the world, the only protection for a mother and her twin sons is their house and strong bond.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Horror", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/70e60f54c1e8acb7f1caa2ab82cb3a502b72e244b679bb4dc90e17140fb5b2ee.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/0fc6f38a78a0398f19fc5a3776521a5622ccbf36a1df9576695fb89aaea5cef8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3a50944ddba6bb8ee22802dfb43cee7659f7d333a4f05eb2643dcc05aebb9bbf.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7ce5473bf4f3d6b80b20e29ed2e6f3d51f5d0f8450e788d32cf53dc144a14fb6.jpg", "publicReleaseDate": 1726790400000, "runtimeSeconds": 6092, "runtime": "101 min", "overallRating": 4, "totalReviewCount": 2060, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.31e3f214-89d1-43ec-bd21-65df67d7ce63", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_6"}, "refMarker": "hm_add_c_aG97kk_brws_3_6", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.31e3f214-89d1-43ec-bd21-65df67d7ce63", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_6"}, "refMarker": "hm_add_c_aG97kk_brws_3_6", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "1992", "gti": "amzn1.dv.gti.c28cf937-0dae-4fa1-8971-120799fd6df8", "transformItemId": "amzn1.dv.gti.c28cf937-0dae-4fa1-8971-120799fd6df8", "synopsis": "A factory worker must protect his son during the 1992 L.A. uprising after the Rodney King verdict.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4c9b203eae68ed15274008cb1f53b2568f999198a5b72eeabd6665a5a1f58f03.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/fafe80571758cd892852f291e09ea86e502ee8d8273e670766ae8d5b8459a23e.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b0fc43129d158ece5b3192f9865aa53bacd12c68adcc230b1c3ccdc77dc786af.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/a3e89a4b39748d6f41c275fc338d6970e50a55155abe03282756245c78d917b5.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d30c58f933178b4bb7b781faf1606c5bfd1534b888051d0fd85e2f7773725996.jpg", "publicReleaseDate": 1724976000000, "runtimeSeconds": 5828, "runtime": "97 min", "overallRating": 4.3, "totalReviewCount": 123, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c28cf937-0dae-4fa1-8971-120799fd6df8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_7"}, "refMarker": "hm_add_c_aG97kk_brws_3_7", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.c28cf937-0dae-4fa1-8971-120799fd6df8", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_7"}, "refMarker": "hm_add_c_aG97kk_brws_3_7", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Five Nights at Freddy's", "gti": "amzn1.dv.gti.b75d7886-268c-4233-94d1-b909d19e2508", "transformItemId": "amzn1.dv.gti.b75d7886-268c-4233-94d1-b909d19e2508", "synopsis": "An abandoned restaurant is haunted by murderous animatronics.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Horror", "Suspense"], "starringCast": ["<PERSON>", "Piper Rubio", "<PERSON>"], "maturityRatingString": "PG-13", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9de84446b97f8c91bd7c645637988c011e869520a4bf04fc84deb3c94ea05912.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f5ef4b9716b89a251ffcb2b3a4bfc4a0be3b655df75c48a04929fa75fe6a8e26.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/1cdd28c6092c14db231b2c2bde451fd7e370adcce96317c994439c26b537e2cc.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4bb4c740fe3b1de3108a01a0130275855cfccdd63f726e97954b94a4561915a9.jpg", "publicReleaseDate": 1698364800000, "runtimeSeconds": 6452, "runtime": "107 min", "overallRating": 4.4, "totalReviewCount": 4064, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b75d7886-268c-4233-94d1-b909d19e2508", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_8"}, "refMarker": "hm_add_c_aG97kk_brws_3_8", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b75d7886-268c-4233-94d1-b909d19e2508", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_8"}, "refMarker": "hm_add_c_aG97kk_brws_3_8", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Fast X", "gti": "amzn1.dv.gti.a02cf2fd-b452-460b-8de5-cc52f6474dfa", "transformItemId": "amzn1.dv.gti.a02cf2fd-b452-460b-8de5-cc52f6474dfa", "synopsis": "<PERSON> and his family confront a lethal enemy from the shadows of the past.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Suspense"], "starringCast": ["<PERSON>", "<PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" <PERSON>", "<PERSON>"], "maturityRatingString": "PG-13", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b14def9fe70d9b8d103e96550baa5da2ad346cdd338e192e1887c7f51ce41c73.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2cb257f2498be66f7d83221efcfe5ef99e7c62e17e19bddee4d36dbc84a92464.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4a7ff89aafdf1b5ac045a52a8b5fa80d4de87814d2e7f5658a41e4a285d7bbc3.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/0a83bd79404e36af4f91daac0e6ebe414fb406ed4f6537f6d905c033e60c7da7.jpg", "publicReleaseDate": 1684454400000, "runtimeSeconds": 8451, "runtime": "140 min", "overallRating": 4.5, "totalReviewCount": 29130, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a02cf2fd-b452-460b-8de5-cc52f6474dfa", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_9"}, "refMarker": "hm_add_c_aG97kk_brws_3_9", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a02cf2fd-b452-460b-8de5-cc52f6474dfa", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_9"}, "refMarker": "hm_add_c_aG97kk_brws_3_9", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "The Ministry of Ungentlemanly Warfare", "gti": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "transformItemId": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "synopsis": "During WWII, a secret team of military mavericks executes a mission against Nazi forces that transforms warfare and establishes black ops precedent.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Comedy", "Military and War", "Drama"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/fa020a81483f05193f21ab49e5ba761c9f67711cb18fe9a788c1d75462e8a09a.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bc49c7e59273ff21ee011b6a1a3a3bce636fda4e92193cfd35360815da636e09.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f665a827c18061f706a51f15b862135c83733efc66e99d01dbc86bc1f06877da.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/38b19522bacc5aca4f1dd20670b72aebf13246ed3e6d29eb3764a2c0be89436d.jpg", "publicReleaseDate": 1713484800000, "runtimeSeconds": 7220, "runtime": "120 min", "overallRating": 4.5, "totalReviewCount": 13638, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_10"}, "refMarker": "hm_add_c_aG97kk_brws_3_10", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_aG97kk_brws_3_10"}, "refMarker": "hm_add_c_aG97kk_brws_3_10", "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_add_c_aG97kk_3", "ClientSideMetrics": "480|CnwKTlVTQ0hBTk5FTFNTdGFyelVudGFyZ2V0ZWRQcmljZVByb21vdGlvbjA2MTcyMDI1Q292ZXJDYXJvdXNlbExpdmVEZWZhdWx0RGVmYXVsdBIQMToxMjJNTU5XVlNLSTFNTBoQMjpEWTU5NDRCQUQ0N0I5QiIGYUc5N2trEj4KBGhvbWUSBmFkZG9ucyIGY2VudGVyKgAyJDE3OWZmZmQ3LTE5MjktNDdiMi1iZmRmLTNkNzcwMGU2ZDliZhoMc3Vic2NyaXB0aW9uIgNhbGwqCHN0YXJ6U3ViMg9mYWNldGVkQ2Fyb3VzZWw6BkJyb3dzZUIOQnJvd3NlU3RyYXRlZ3lSC25vdEVudGl0bGVkWgBiBUNvdmVyaANyAHo4a003RDZYcjVJYS1BbGVWUkFlcWp5YjVJLTV0OG1lb1N3SFJYMlpqMHMzXzZBcE0zVEtfbWpnPT2CAQVmYWxzZYoBAJIBAA=="}, "tags": [], "journeyIngressContext": "32|CghzdGFyelN1YhIMc3Vic2NyaXB0aW9u", "type": "COVER"}, {"facet": {}, "title": "Bundle & Save", "heading": "This is a marketing message for bundles on prime video channels", "description": "This is a description for bundles on prime video channels", "ctaButton": {"title": "See all subscriptions", "displayPlacement": "Start", "linkAction": {"target": "landing", "pageId": "default", "pageType": "subscription", "analytics": {"refMarker": "testSpecialCarouselCTARefMarker"}, "refMarker": "testSpecialCarouselCTARefMarker"}}, "backgroundImageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_Mickey17_Max_CS/efc6f2c8-fa33-47fe-bb52-66c554c085c1.jpeg", "actions": [], "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24=", "entitlement": "NotEntitled", "paginationLink": {"serviceToken": "v0_CnEKOGtNN0Q2WHI1SWEtQWxlVlJBZXFqeWI1SS01dDhtZW9Td0hSWDJaajBzM182QXBNM1RLX21qZz09EPjXmJ34MhosTTFNdjBlNkRWWVpMTzVpVzlPdyt2bVpzckdkRHlHa3VrWGx6cFZvcytuND0gARIFaHBhZ2UYADIGY2VudGVyQBBKJDE3OWZmZmQ3LTE5MjktNDdiMi1iZmRmLTNkNzcwMGU2ZDliZnoAggEyEioxOjExQlZSNFVJR0dKMldGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE0wAFAKcAA=", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjExQlZSNFVJR0dKMldGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "pageId": "addons", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "addons"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjExQlZSNFVJR0dKMldGIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Subscription", "items": [{"id": "amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "title": "Hallmark+ & STARZ", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-c82c6b4a-f555-b842-5335-547f6f30d804/browse/bundle_and_save_tile._CB792449229_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-c82c6b4a-f555-b842-5335-547f6f30d804/browse/bundle_and_save_tile._CB792449229_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HS48a437_4_1", "target": "signUp", "benefitId": "amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.c82c6b4a-f555-b842-5335-547f6f30d804", "analytics": {"refMarker": "hm_add_c_ojwiZC_HS48a437_4_1"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $13.99/month (Save $4.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$18.98{end}{end} $13.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "title": "Max Standard & STARZ", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9be9c7ca-3bd4-4bfe-b49f-7028dc033453/browse/bundle_and_save_tile._CB792436309_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9be9c7ca-3bd4-4bfe-b49f-7028dc033453/browse/bundle_and_save_tile._CB792436309_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HS17b1b5_4_2", "target": "signUp", "benefitId": "amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.9be9c7ca-3bd4-4bfe-b49f-7028dc033453", "analytics": {"refMarker": "hm_add_c_ojwiZC_HS17b1b5_4_2"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $20.99/month (Save $6.99/month)", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$27.98{end}{end} $20.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "title": "MGM+ & AMC+ Ad-Free", "synopsis": "Captivating original series, hit movies, and more", "transformItemId": "amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-bfc5dc68-dbec-4d65-b1ca-3e432c6d0296/browse/bundle_and_save_tile._CB792406614_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-bfc5dc68-dbec-4d65-b1ca-3e432c6d0296/browse/bundle_and_save_tile._CB792406614_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HS816020_4_3", "target": "signUp", "benefitId": "amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.bfc5dc68-dbec-4d65-b1ca-3e432c6d0296", "analytics": {"refMarker": "hm_add_c_ojwiZC_HS816020_4_3"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $11.99/month (Save $4.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$16.98{end}{end} $11.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.1e0072dc-1d9b-4e18-a215-358ff05d27ce", "title": "Subscriptions on Prime Video", "synopsis": "Subscriptions on Prime Video", "transformItemId": "amzn1.dv.channel.1e0072dc-1d9b-4e18-a215-358ff05d27ce", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-1e0072dc-1d9b-4e18-a215-358ff05d27ce/browse/bundle_and_save_tile._CB792406673_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-1e0072dc-1d9b-4e18-a215-358ff05d27ce/browse/bundle_and_save_tile._CB792406673_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_1aua9S_4_4", "target": "signUp", "benefitId": "amzn1.dv.channel.1e0072dc-1d9b-4e18-a215-358ff05d27ce", "nonSupportedText": "Subscriptions on Prime Video", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.1e0072dc-1d9b-4e18-a215-358ff05d27ce", "analytics": {"refMarker": "hm_add_c_ojwiZC_1aua9S_4_4"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $7.99/month (Save $2.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$10.98{end}{end} $7.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "title": "BritBox & MGM+", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-b95e078c-4969-4a27-a49e-742d095a018c/browse/bundle_and_save_tile._CB792450188_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-b95e078c-4969-4a27-a49e-742d095a018c/browse/bundle_and_save_tile._CB792450188_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HS15892d_4_5", "target": "signUp", "benefitId": "amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.b95e078c-4969-4a27-a49e-742d095a018c", "analytics": {"refMarker": "hm_add_c_ojwiZC_HS15892d_4_5"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $11.99/month (Save $3.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$15.98{end}{end} $11.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "title": "STARZ & BET+ Premium", "synopsis": "captivating series, movies, reality, and more", "transformItemId": "amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9770b7a2-5bc8-401e-a101-4d7d6a93e191/browse/bundle_and_save_tile._CB792450165_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-9770b7a2-5bc8-401e-a101-4d7d6a93e191/browse/bundle_and_save_tile._CB792450165_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HSf97999_4_6", "target": "signUp", "benefitId": "amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.9770b7a2-5bc8-401e-a101-4d7d6a93e191", "analytics": {"refMarker": "hm_add_c_ojwiZC_HSf97999_4_6"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $15.99/month (Save $5.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$21.98{end}{end} $15.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "title": "Max Standard & Cinemax", "synopsis": "Groundbreaking originals, epic series, hit movies, and more", "transformItemId": "amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-25722c04-d551-4eea-8ee3-7041d42fd6e4/browse/bundle_and_save_tile._CB792406754_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-25722c04-d551-4eea-8ee3-7041d42fd6e4/browse/bundle_and_save_tile._CB792406754_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HS12ccce_4_7", "target": "signUp", "benefitId": "amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.25722c04-d551-4eea-8ee3-7041d42fd6e4", "analytics": {"refMarker": "hm_add_c_ojwiZC_HS12ccce_4_7"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $21.99/month (Save $4.99/month)", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$26.98{end}{end} $21.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "title": "STARZ & discovery+ Ad-Free", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-a94c7091-7741-f846-aff4-762992403be9/browse/bundle_and_save_tile._CB792450880_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-a94c7091-7741-f846-aff4-762992403be9/browse/bundle_and_save_tile._CB792450880_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HS0cc865_4_8", "target": "signUp", "benefitId": "amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.a94c7091-7741-f846-aff4-762992403be9", "analytics": {"refMarker": "hm_add_c_ojwiZC_HS0cc865_4_8"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $14.99/month (Save $5.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$20.98{end}{end} $14.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.00d54faf-e9f4-639b-da19-ac218b04f186", "title": "Acorn TV & MGM+", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.00d54faf-e9f4-639b-da19-ac218b04f186", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-00d54faf-e9f4-639b-da19-ac218b04f186/browse/bundle_and_save_tile._CB792450977_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-00d54faf-e9f4-639b-da19-ac218b04f186/browse/bundle_and_save_tile._CB792450977_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HScc7138_4_9", "target": "signUp", "benefitId": "amzn1.dv.channel.00d54faf-e9f4-639b-da19-ac218b04f186", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.00d54faf-e9f4-639b-da19-ac218b04f186", "analytics": {"refMarker": "hm_add_c_ojwiZC_HScc7138_4_9"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $10.99/month (Save $4.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$15.98{end}{end} $10.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}, {"id": "amzn1.dv.channel.a1746e4e-c2f9-0ca1-0ebb-810a16fd38bd", "title": "A&E Crime Central & Lifetime Movie Club", "synopsis": "Save with a bundle", "transformItemId": "amzn1.dv.channel.a1746e4e-c2f9-0ca1-0ebb-810a16fd38bd", "widgetType": "channel", "image": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-a1746e4e-c2f9-0ca1-0ebb-810a16fd38bd/browse/bundle_and_save_tile._CB792450843_.jpg", "heroImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/channel-id/amzn1-dv-channel-a1746e4e-c2f9-0ca1-0ebb-810a16fd38bd/browse/bundle_and_save_tile._CB792450843_.jpg", "actions": [{"refMarker": "hm_add_c_ojwiZC_HSd8833a_4_10", "target": "signUp", "benefitId": "amzn1.dv.channel.a1746e4e-c2f9-0ca1-0ebb-810a16fd38bd", "nonSupportedText": "", "uri": "https://www.amazon.com/gp/video/offers/?benefitId=amzn1.dv.channel.a1746e4e-c2f9-0ca1-0ebb-810a16fd38bd", "analytics": {"refMarker": "hm_add_c_ojwiZC_HSd8833a_4_10"}}], "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe for $6.99/month (Save $2.99/month) after free trial", "icon": "OFFER_ICON"}, "PRODUCT_SUMMARY_SLOT": {"message": "{strike}{muted}$9.98{end}{end} $6.99/month"}, "PRODUCT_PROMOTION_SLOT": {"message": "BUNDLE AND SAVE"}}, "cardType": "CHANNEL_CARD"}], "analytics": {"refMarker": "hm_add_c_ojwiZC_4", "ClientSideMetrics": "484|CnMKRVVTRml4ZWRDaGFubmVsQnVuZGxlQnVuZGxlQW5kU2F2ZUNhcm91c2VsQWRkb25zUGFnZUxpdmVEZWZhdWx0RGVmYXVsdBIQMToxMUJWUjRVSUdHSjJXRhoQMjpEWUYyQUJDODQ2OERDQyIGb2p3aVpDEj4KBGhvbWUSBmFkZG9ucyIGY2VudGVyKgAyJDE3OWZmZmQ3LTE5MjktNDdiMi1iZmRmLTNkNzcwMGU2ZDliZhoMc3Vic2NyaXB0aW9uIgNhbGwqADIPZmFjZXRlZENhcm91c2VsOhNIZXJvQ29udGVudFByb3ZpZGVyQg1CdW5kbGVBbmRTYXZlUgtub3RFbnRpdGxlZFoAYg9TcGVjaWFsQ2Fyb3VzZWxoBHIAejhrTTdENlhyNUlhLUFsZVZSQWVxanliNUktNXQ4bWVvU3dIUlgyWmowczNfNkFwTTNUS19tamc9PYIBBWZhbHNligEAkgEA"}, "tags": [], "type": "SPECIAL_CAROUSEL"}, {"title": "Top 10 fan favorites", "facet": {}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7BioZhZGRvbnOLhGhvbWWMjqoxOjEySkZKODA5WUQ1N0Q0IyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Subscription", "entitlement": "NotEntitled", "items": [{"title": "BMF - Season 4", "gti": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "transformItemId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "synopsis": "BMF S4 continues the journey of <PERSON><PERSON><PERSON> \"Big Meech\" <PERSON><PERSON><PERSON> and <PERSON> \"Southwest T\" <PERSON><PERSON><PERSON>. They're fighting to keep their empire and their bond.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Romance"], "starringCast": ["<PERSON><PERSON><PERSON> '<PERSON>' <PERSON>", "Ajiona Alexus", "La La Anthony"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3a12aad3465f5ccdce908dd34a3a8ad035e512bd3db58ed513268bcedee8a99a.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c49996203b1714e5b076ad3f68a6912eddc806cf80df13e03b3c2ddc3269994f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/065d63801cf65cd558c6de6475da0235f6dfb3c1348499794b6436279e246d65.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5481eaed297bd6080ea144a386d3f2dba2150125e1f7cf644b0f7d8a8d79a3d6.jpg", "publicReleaseDate": 1749168000000, "overallRating": 3, "totalReviewCount": 2, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_1"}, "refMarker": "hm_add_c_0JTqKT_brws_5_1", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_1"}, "refMarker": "hm_add_c_0JTqKT_brws_5_1", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "BMF", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "MobLand - Season 1", "gti": "amzn1.dv.gti.6340992b-6853-4570-a16e-ab30f526da50", "transformItemId": "amzn1.dv.gti.6340992b-6853-4570-a16e-ab30f526da50", "synopsis": "Two mob families clash in a war that threatens to topple empires and lives.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "Paddy Considine"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/86421189d0b9528d276238cf3205deb3cbfab6a49e5e8d9ac160346b90ca35a9.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/12de71cff2473f1eb790e839768b7cd9688ced745fb921a4e3051e4eb0c656e0.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/cc84faa477a3ef05d38c86ab69b52df32c880c3e3ba47d50d3274e5fb1886174.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/2b8e7b6147fb49dcd9224c391f7d67023bb41d5f44952e1e7f97a6229015a637.jpg", "publicReleaseDate": 1743292800000, "overallRating": 4.8, "totalReviewCount": 248, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.6340992b-6853-4570-a16e-ab30f526da50", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_2"}, "refMarker": "hm_add_c_0JTqKT_brws_5_2", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.6340992b-6853-4570-a16e-ab30f526da50", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_2"}, "refMarker": "hm_add_c_0JTqKT_brws_5_2", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "MobLand", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Stick - Season 1", "gti": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "transformItemId": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "synopsis": "<PERSON> stars as an ex-pro golfer who goes all in to mentor a teenage phenom—and maybe save himself.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Comedy", "Sports"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c8d36996b0a3752ed48ddb361bbcdd897139146c0315686e10ab73d98b9814c1.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f22c7b4dbf31fd792075049762a91f4833e32373bbb76411f8e473c4a2d7dc73.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/fef408c2c35d468c2b9a86ea9542eb295b763ad53a1c9e3a53076774ce8c4df4.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/logos/channels-logo-white._CB543859421_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/264069a50e9d681dea3fc7e1fc0529a83b233afc512231a1b0802026e50c0179.jpg", "publicReleaseDate": 1748995200000, "overallRating": 3.8, "totalReviewCount": 35, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_3"}, "refMarker": "hm_add_c_0JTqKT_brws_5_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "First episode free"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "First episode free", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_3"}, "refMarker": "hm_add_c_0JTqKT_brws_5_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Stick", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 884, "width": 1991}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>'s <PERSON><PERSON><PERSON> <PERSON><PERSON> 1", "gti": "amzn1.dv.gti.f845d927-1c64-4791-ba18-c0d317110633", "transformItemId": "amzn1.dv.gti.f845d927-1c64-4791-ba18-c0d317110633", "synopsis": "<PERSON><PERSON><PERSON> and <PERSON> lead a cast exploring the power of female friendship as five women support each other through life's challenges.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Comedy", "Drama"], "starringCast": [], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/753f7ea523625e9c43bfeb0bf298f1bafc8582790ff5af00599046b8bdd3fe21.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/0075cae737e18cdf9c304e255b054d99bb8e98f3e4a650b3a8b97c3635623757.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/ac44198dc730c85ee396561ec14096e947ae93777ffcf8e14dbf486b017b19c6.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/betplus/logos/channels-logo-white-stacked._CB564870593_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4a6bc7355852b555637b915f0dfcf7203ab76c4466f8ab38856f47b47043f788.jpg", "publicReleaseDate": 1749427200000, "overallRating": 4.9, "totalReviewCount": 19, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f845d927-1c64-4791-ba18-c0d317110633", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_4"}, "refMarker": "hm_add_c_0JTqKT_brws_5_4", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of BET+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f845d927-1c64-4791-ba18-c0d317110633", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_4"}, "refMarker": "hm_add_c_0JTqKT_brws_5_4", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "<PERSON>'s Di<PERSON><PERSON> <PERSON><PERSON><PERSON>", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 617, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "The Chi", "gti": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "transformItemId": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "synopsis": "With <PERSON> at the helm, the women of the South Side reclaim their power.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": [], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/548634d46bc144e3c43d66c92ad675d185eaf94c3a14d2a482f8bb757cbd1b44.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8be6a47c6225360d4055f66db97119c69ae21a39414eccd7e89c6b56cbf88aae.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/48bd81c16dae8b2bc4963c79f2041ed6a1df68fbfeeb5a988765e7b81cd1a6ee.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/paramountpremium/logos/channels-logo-white._CB583166680_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/8cb8a366da8e0249bddca089267676dd47dffd5e0987b51c95a0e48df5ee9330.jpg", "publicReleaseDate": 1747353600000, "overallRating": 5, "totalReviewCount": 8, "seasonNumber": 7, "numberOfSeasons": 7, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_5"}, "refMarker": "hm_add_c_0JTqKT_brws_5_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_5"}, "refMarker": "hm_add_c_0JTqKT_brws_5_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "The Chi", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Art Detectives: Series 1", "gti": "amzn1.dv.gti.20c6cc6e-b259-407b-b129-c1666acc8e0d", "transformItemId": "amzn1.dv.gti.20c6cc6e-b259-407b-b129-c1666acc8e0d", "synopsis": "True Blood's <PERSON> stars as <PERSON><PERSON>, leading the Met's Heritage Crime unit to solve murders and heists involving precious antiquities.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-14", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-14", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/77f91ab6a735f5efde7be84aa753ea47d97f7551f3752a114c0f40fb2418505b.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2c2125d8d38566954e6a377bceaad7d2e551b0967b139b75d69e84b19af412fa.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f4383bdbb733ead4437d5773afc7466322fbfe27d54462cdd7bce95bdb2cb602.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/acorn/logos/channels-logo-white._CB569177979_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d0eaefd95a1bdf45d2b00ee5778bda191765cfd0d2a95eeff1e12358a22bf1a2.jpg", "publicReleaseDate": 1749427200000, "overallRating": 4.8, "totalReviewCount": 9, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.20c6cc6e-b259-407b-b129-c1666acc8e0d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_6"}, "refMarker": "hm_add_c_0JTqKT_brws_5_6", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Acorn TV", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.20c6cc6e-b259-407b-b129-c1666acc8e0d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_6"}, "refMarker": "hm_add_c_0JTqKT_brws_5_6", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Art Detectives", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 369, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Criminal Minds - Season 18", "gti": "amzn1.dv.gti.a2bd6906-b1c4-444c-8774-c2723c124b75", "transformItemId": "amzn1.dv.gti.a2bd6906-b1c4-444c-8774-c2723c124b75", "synopsis": "The FBI's elite team of criminal profilers faces their greatest threat yet: an UnSub who built a network of serial killers during the pandemic.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Suspense"], "starringCast": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/932ec905a114465b8d93936d2cd88fa2ec03567f0a428c8d194e8c261eeaca38.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bc608e199fdb10438dbea1c6327d1440c8087e9cb144af30de41b8b214b6fc18.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f7b52d5f36d0415fc3ef9f1c48cebdbaa3b9567862ca758fbb216c57f81fbba5.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/34a3e8ef1c5757ffc21deb92a6ae3e6599bb9cc7a9a5e98370201c475da74ee2.jpg", "publicReleaseDate": 1746662400000, "overallRating": 3.5, "totalReviewCount": 11, "seasonNumber": 18, "numberOfSeasons": 18, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a2bd6906-b1c4-444c-8774-c2723c124b75", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_7"}, "refMarker": "hm_add_c_0JTqKT_brws_5_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2012"}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a2bd6906-b1c4-444c-8774-c2723c124b75", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_7"}, "refMarker": "hm_add_c_0JTqKT_brws_5_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Criminal Minds", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Godfather of Harlem Season 4", "gti": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "transformItemId": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "synopsis": "Following <PERSON>'s death, crime boss <PERSON><PERSON><PERSON> faces dual challenges: the Italian Mafia's power and his daughter's radical political activism.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9d9907b89f25de0efb7b98e644498c2b97b05ef65d2e22ceda89aa68c22830ef.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/7a344f3aae072bfa5d8c89dac00cf62a6f76478e3840e5d94d7f1d1dc55a813c.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/598b9dd3a82b9335849ff67d2adc35c350117eb43b19d31446c59dac33ee2e7a.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/epix/logos/channels-logo-white._CB583144945_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/81f95ad8de2553114e3681efb3571fe00d008e1d45efc3d75928549f6773685c.jpg", "publicReleaseDate": 1744502400000, "overallRating": 5, "totalReviewCount": 13, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_8"}, "refMarker": "hm_add_c_0JTqKT_brws_5_8", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of MGM+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_8"}, "refMarker": "hm_add_c_0JTqKT_brws_5_8", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Godfather of Harlem", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 838, "width": 1999}}}, "cardType": "TITLE_CARD"}, {"title": "Beyond The Gates: Season 1", "gti": "amzn1.dv.gti.a130d026-f991-4b7d-bb01-a8a5b87a5d81", "transformItemId": "amzn1.dv.gti.a130d026-f991-4b7d-bb01-a8a5b87a5d81", "synopsis": "A powerful and prestigious family reigns over a posh gated community with juicy secrets and scandals waiting to be uncovered.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "TV-PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "TV-PG", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/37f4f409af191cf2513c864ae65e8ba54d3d593c1bec650bffdb60de72a57b96.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/7c8b600bd0a971c3f4b5b979f6027f1d3d0d3e1ffb1803b76215ce1ac5bf9b85.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7327ef067546e276cdb59f863d35ac5d15d2765af73c2d97dcc0601e65af306e.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4aa030d33213c4904ad6b5d1510c513cd96103c399b813d64e78543e66aa1b66.jpg", "publicReleaseDate": 1740355200000, "overallRating": 4.2, "totalReviewCount": 290, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a130d026-f991-4b7d-bb01-a8a5b87a5d81", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_9"}, "refMarker": "hm_add_c_0JTqKT_brws_5_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a130d026-f991-4b7d-bb01-a8a5b87a5d81", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_9"}, "refMarker": "hm_add_c_0JTqKT_brws_5_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Beyond the Gates", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Heartland: Season 18", "gti": "amzn1.dv.gti.f7ae7552-0806-4da6-a090-74e7d27930cb", "transformItemId": "amzn1.dv.gti.f7ae7552-0806-4da6-a090-74e7d27930cb", "synopsis": "A multi-generational saga set in Alberta, Canada and centered on a family getting through life together in both happy and trying times.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "TV-PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Western"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-PG", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3bb945a46843b9effc85cc45cc82e9a8dc841da9bc5cc641cc5b0f7d5b36ff3f.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/a30afe5b1d262613202e661dd96aad42932c1cd6d1b4009db24d812829fb6ab8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/978c03defe96738efec65cf3d83491df7cdc3ae1fea55e28c7b292d378cb93c1.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/upfaithfamily/logos/channels-logo-white-stacked._CB561270323_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/ca69700d70f8d23ac30ea4dc72112e6d52706886406ed1c3a1da66c7f42e68c8.jpg", "publicReleaseDate": 1728172800000, "overallRating": 4.3, "totalReviewCount": 13, "seasonNumber": 18, "numberOfSeasons": 18, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f7ae7552-0806-4da6-a090-74e7d27930cb", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_10"}, "refMarker": "hm_add_c_0JTqKT_brws_5_10", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of UP Faith & Family", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f7ae7552-0806-4da6-a090-74e7d27930cb", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_0JTqKT_brws_5_10"}, "refMarker": "hm_add_c_0JTqKT_brws_5_10", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Heartland", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 667, "width": 1904}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_add_c_0JTqKT_5", "ClientSideMetrics": "412|ClUKJ0NoYW5uZWxzdG9wMTBDYXJvdXNlbExpdmVEZWZhdWx0RGVmYXVsdBIQMToxMkpGSjgwOVlENTdENBoQMjpEWUM2NjU1OEFBMjg0NiIGMEpUcUtUEj4KBGhvbWUSBmFkZG9ucyIGY2VudGVyKgAyJDE3OWZmZmQ3LTE5MjktNDdiMi1iZmRmLTNkNzcwMGU2ZDliZhoMc3Vic2NyaXB0aW9uIgNhbGwqADIPZmFjZXRlZENhcm91c2VsOgZCcm93c2VCCVRvcENoYXJ0c1ILbm90RW50aXRsZWRaAGIGQ2hhcnRzaAVyAHo4a003RDZYcjVJYS1BbGVWUkFlcWp5YjVJLTV0OG1lb1N3SFJYMlpqMHMzXzZBcE0zVEtfbWpnPT2CAQVmYWxzZYoBAJIBAA=="}, "tags": [], "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24=", "type": "CHARTS"}, {"facet": {}, "title": "Fresh picks", "paginationLink": {"serviceToken": "v0_CnEKOGtNN0Q2WHI1SWEtQWxlVlJBZXFqeWI1SS01dDhtZW9Td0hSWDJaajBzM182QXBNM1RLX21qZz09EPjXmJ34MhosTTFNdjBlNkRWWVpMTzVpVzlPdyt2bVpzckdkRHlHa3VrWGx6cFZvcytuND0gARIFaHBhZ2UYADIGY2VudGVyQChKJDE3OWZmZmQ3LTE5MjktNDdiMi1iZmRmLTNkNzcwMGU2ZDliZnoAggGeAhIpMToxTVFOTENJRzdDMEtFIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE0wADokeyJzYnNpbiI6MCwiY3Vyc2l6ZSI6NDAsInByZXNpemUiOjB9UApixAGLl6WL85ePuizL4enm3suzu4gBnoyt-uzG8sAV0-Hc8eKDppanAcSG8aXx_tnRvwGTzpfKgsmjxoMBop758N74lZL6AfePrsuxu5rIkgGEi_WUhMmSo-sBz9jD_IuGt8eJAYuI6cKzhv_eO-alhrHBt92AmQH-l7nrkqHOtcUBjZTnqZ6cztmkAfKfrezck976J_fr5NHi9P7pjwHUm5mfkoesi_wB7ZaPioGP4KyUAfeN2eXw_-m0pAH_uuHCgfabm44BcAA=", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7AioZhZGRvbnOLhGhvbWWMjqkxOjFNUU5MQ0lHN0MwS0UjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "pageId": "addons", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "addons"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7AioZhZGRvbnOLhGhvbWWMjqkxOjFNUU5MQ0lHN0MwS0UjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "Subscription", "entitlement": "NotEntitled", "items": [{"title": "SpongeBob SquarePants - Season 14", "gti": "amzn1.dv.gti.2c743cbf-3169-4b8b-8876-ce5decda70cb", "transformItemId": "amzn1.dv.gti.2c743cbf-3169-4b8b-8876-ce5decda70cb", "synopsis": "Meet Sponge<PERSON><PERSON>, a vibrant yellow sponge who calls a pineapple home in Bikini Bottom, sharing his Pacific Ocean life with <PERSON> the snail.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Kids"], "starringCast": [], "maturityRatingString": "TV-PG", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/a74b2b95965315e0cec51d75f8c8c52c162dcc25e1087e7a0c62e4628dd9b8bb.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9bd536d4fcdba04fad51212acd33f020e7d8cb984920674009804f54f1f50b0f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/bc3ee37dc2543a79b44995dfd5ffac836d86c5c1975ea956d18b2a66c871cb1d.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3f05b84308b7e31fede8754ebb7f23f41cd70a1ae78eff09532a7270b485e998.jpg", "seasonNumber": 14, "numberOfSeasons": 17, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2c743cbf-3169-4b8b-8876-ce5decda70cb", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_1"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_1", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "RECENTLY ADDED", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2c743cbf-3169-4b8b-8876-ce5decda70cb", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_1"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_1", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "SpongeBob SquarePants", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "BMF - Season 4", "gti": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "transformItemId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "synopsis": "BMF S4 continues the journey of <PERSON><PERSON><PERSON> \"Big Meech\" <PERSON><PERSON><PERSON> and <PERSON> \"Southwest T\" <PERSON><PERSON><PERSON>. They're fighting to keep their empire and their bond.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Romance"], "starringCast": ["<PERSON><PERSON><PERSON> '<PERSON>' <PERSON>", "Ajiona Alexus", "La La Anthony"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3a12aad3465f5ccdce908dd34a3a8ad035e512bd3db58ed513268bcedee8a99a.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c49996203b1714e5b076ad3f68a6912eddc806cf80df13e03b3c2ddc3269994f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/065d63801cf65cd558c6de6475da0235f6dfb3c1348499794b6436279e246d65.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/starzSub/logos/channels-logo-white._CB570300641_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5481eaed297bd6080ea144a386d3f2dba2150125e1f7cf644b0f7d8a8d79a3d6.jpg", "publicReleaseDate": 1749168000000, "overallRating": 3, "totalReviewCount": 2, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_2"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_2", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to STARZ", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1581ca36-cf4b-461e-a72c-981e2e3730d3", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_2"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_2", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "BMF", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 463, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Stick - Season 1", "gti": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "transformItemId": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "synopsis": "<PERSON> stars as an ex-pro golfer who goes all in to mentor a teenage phenom—and maybe save himself.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Comedy", "Sports"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c8d36996b0a3752ed48ddb361bbcdd897139146c0315686e10ab73d98b9814c1.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f22c7b4dbf31fd792075049762a91f4833e32373bbb76411f8e473c4a2d7dc73.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/fef408c2c35d468c2b9a86ea9542eb295b763ad53a1c9e3a53076774ce8c4df4.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/logos/channels-logo-white._CB543859421_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/264069a50e9d681dea3fc7e1fc0529a83b233afc512231a1b0802026e50c0179.jpg", "publicReleaseDate": 1748995200000, "overallRating": 3.8, "totalReviewCount": 35, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_3"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "First episode free"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "First episode free", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.bfa367f7-14bc-4344-838c-8e482945e713", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_3"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_3", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Stick", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 884, "width": 1991}}}, "cardType": "TITLE_CARD"}, {"title": "Your Friends & Neighbors - Season 1", "gti": "amzn1.dv.gti.fa2457c5-ee1e-4f22-9290-69db196b87f7", "transformItemId": "amzn1.dv.gti.fa2457c5-ee1e-4f22-9290-69db196b87f7", "synopsis": "<PERSON> is a financial titan who turns to theft after losing his job and marriage—soon getting tangled in a deadly web.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/795260e5335c0fe0f537080e71c12e07263db091b217c312e3c16abee9663e7b.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c4e3553766cf3c350f71b45e06850357ac3da463c947ac2faaa548990364a638.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/11892eb92df1874e1f2f9b92e60a56364f8b952a22bca00f6f1205823b2b9944.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/logos/channels-logo-white._CB543859421_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/22286e302d99f3ef62fb81914a672257e3fa8842fdbb295e3f91b54bcabf5595.jpg", "publicReleaseDate": 1744329600000, "overallRating": 4.5, "totalReviewCount": 67, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fa2457c5-ee1e-4f22-9290-69db196b87f7", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_4"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_4", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "First episode free"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "First episode free", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.fa2457c5-ee1e-4f22-9290-69db196b87f7", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_4"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_4", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Your Friends & Neighbors", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 884, "width": 1991}}}, "cardType": "TITLE_CARD"}, {"title": "The Chi", "gti": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "transformItemId": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "synopsis": "With <PERSON> at the helm, the women of the South Side reclaim their power.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": [], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/548634d46bc144e3c43d66c92ad675d185eaf94c3a14d2a482f8bb757cbd1b44.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8be6a47c6225360d4055f66db97119c69ae21a39414eccd7e89c6b56cbf88aae.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/48bd81c16dae8b2bc4963c79f2041ed6a1df68fbfeeb5a988765e7b81cd1a6ee.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/paramountpremium/logos/channels-logo-white._CB583166680_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/8cb8a366da8e0249bddca089267676dd47dffd5e0987b51c95a0e48df5ee9330.jpg", "publicReleaseDate": 1747353600000, "overallRating": 5, "totalReviewCount": 8, "seasonNumber": 7, "numberOfSeasons": 7, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_5"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.eb464a48-429d-4584-898e-dc30bf90ec4f", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_5"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_5", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "The Chi", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Sonic The Hedgehog 3", "gti": "amzn1.dv.gti.3bbdfc33-385a-440b-9901-75bc162192e6", "transformItemId": "amzn1.dv.gti.3bbdfc33-385a-440b-9901-75bc162192e6", "synopsis": "<PERSON><PERSON> <PERSON> joins the franchise as <PERSON>, a powerful new nemesis who challenges <PERSON> and his faithful companions, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Kids", "Adventure"], "starringCast": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "PG", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e8b3f0edf8079a5160b35e73bebb62d4b47a44d112d52e088f4bece79b8c08bd.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/d3d170cab1ef757a9a437cb9aa70bb33a8897f31f327f02c2de4d5a50dd32ff4.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/69eed858c83baf0921057d81891b8f5e56d7979bfd31be0bf4218e8347e1f6e0.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/345338e354420aa920419baea475974f6f85cc6fd7b2d5704c3103be34051a78.jpg", "publicReleaseDate": 1734652800000, "runtimeSeconds": 6605, "runtime": "110 min", "overallRating": 4.8, "totalReviewCount": 5415, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3bbdfc33-385a-440b-9901-75bc162192e6", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_6"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_6", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trials available, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3bbdfc33-385a-440b-9901-75bc162192e6", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_6"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_6", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "FROM - Season 1", "gti": "amzn1.dv.gti.c56b3909-2d6e-4bfe-a4b3-38e1e539ca0d", "transformItemId": "amzn1.dv.gti.c56b3909-2d6e-4bfe-a4b3-38e1e539ca0d", "synopsis": "A mysterious town becomes an inescapable prison for its residents, who must survive deadly forest creatures while searching desperately for an exit.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Science Fiction", "Horror"], "starringCast": ["<PERSON>", "Catalina <PERSON>ino Moreno", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/8f2831ac050828ce9a534280416c77d73e7d69d1b116303cba3c1b09ed1d7d80.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/dc51ab2db7bdba0de12c8f9fb7bc59308d97f49c1d946c071fb94d81cd7af7b6.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/4769439e9af72b9dafa95cd0f32b8b3244574b23e96cdcffb6e8c7784c09732f.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/epix/logos/channels-logo-white._CB583144945_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7f37f5a6af1316d4a479ee0fcb6b77a03d1a77901389f0a0e9626c3f6dd0a4b4.jpg", "publicReleaseDate": 1645315200000, "runtimeSeconds": 2906, "runtime": "48 min", "overallRating": 3.9, "totalReviewCount": 5, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c56b3909-2d6e-4bfe-a4b3-38e1e539ca0d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_7"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of MGM+ or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.c56b3909-2d6e-4bfe-a4b3-38e1e539ca0d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_7"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_7", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "FROM", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 838, "width": 1999}}}, "cardType": "TITLE_CARD"}, {"title": "Godfather of Harlem Season 4", "gti": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "transformItemId": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "synopsis": "Following <PERSON>'s death, crime boss <PERSON><PERSON><PERSON> faces dual challenges: the Italian Mafia's power and his daughter's radical political activism.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9d9907b89f25de0efb7b98e644498c2b97b05ef65d2e22ceda89aa68c22830ef.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/7a344f3aae072bfa5d8c89dac00cf62a6f76478e3840e5d94d7f1d1dc55a813c.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/598b9dd3a82b9335849ff67d2adc35c350117eb43b19d31446c59dac33ee2e7a.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/epix/logos/channels-logo-white._CB583144945_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/81f95ad8de2553114e3681efb3571fe00d008e1d45efc3d75928549f6773685c.jpg", "publicReleaseDate": 1744502400000, "overallRating": 5, "totalReviewCount": 13, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_8"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_8", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of MGM+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW EPISODE", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.27f5789d-cd8b-4ff2-8fd3-fba62a3935f7", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_8"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_8", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Godfather of Harlem", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 838, "width": 1999}}}, "cardType": "TITLE_CARD"}, {"title": "Yellowjackets, Season 3", "gti": "amzn1.dv.gti.fc16b039-23e6-4dd4-9459-80781143cb6d", "transformItemId": "amzn1.dv.gti.fc16b039-23e6-4dd4-9459-80781143cb6d", "synopsis": "As summer arrives, the Yellowjackets must confront a chilling question.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "TV-MA", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/eb516a45aebeb442256f80866f832d7a34354b7c86ecd41a0cd48e831e963f86.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6fd2e4df4a630a0762756be7362f8514ecc21830b402bedafba635cc61da6ca2.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3455d194cb43831f26ad96089b507af31da85566024b4fa660766d0b5dd9d8e1.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/paramountpremium/logos/channels-logo-white._CB583166680_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/528edbc3fa8fd99a54078bd68f74eef05b13eeb5f47a87c2bec384bea596c838.jpg", "publicReleaseDate": 1739491200000, "overallRating": 4, "totalReviewCount": 51, "seasonNumber": 3, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fc16b039-23e6-4dd4-9459-80781143cb6d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_9"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+ or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBE® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.fc16b039-23e6-4dd4-9459-80781143cb6d", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_9"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_9", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "showName": "Yellowjackets", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "The Gorge", "gti": "amzn1.dv.gti.a469a7ff-0cb6-46f7-8e36-6fb018585d7f", "transformItemId": "amzn1.dv.gti.a469a7ff-0cb6-46f7-8e36-6fb018585d7f", "synopsis": "<PERSON>-<PERSON> and <PERSON> must work together to survive the evil that lies within a mysterious gorge.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense"], "starringCast": ["<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "PG-13", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/cd3880100c674155280abca547da00a85fdcc2a68ba770dce9f207ff27727dd2.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/aab0322a11698c77fe0dc30131b2ffdba59a73d3544a0b25cf5cb7d20e029f84.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b545a6cb9028c645721bdb7d144d2ae02fadabef08fc0c42f68458d2773c3b59.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/appletvus/logos/channels-logo-white._CB543859421_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/279dddb83bfb6009240cc14f0765c08c7ae4bea2960d746485167100aa34675b.jpg", "publicReleaseDate": 1739491200000, "runtimeSeconds": 7751, "runtime": "129 min", "overallRating": 4.5, "totalReviewCount": 182, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a469a7ff-0cb6-46f7-8e36-6fb018585d7f", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_10"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_10", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Apple TV+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a469a7ff-0cb6-46f7-8e36-6fb018585d7f", "pageType": "detail", "analytics": {"refMarker": "hm_add_c_Y6P4cQ_brws_6_10"}, "refMarker": "hm_add_c_Y6P4cQ_brws_6_10", "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 884, "width": 1991}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_add_c_Y6P4cQ_6", "ClientSideMetrics": "460|Cl8KMlVTM1BNaXh0dXJlUmV0cmlldmFsRnJlc2hQaWNrc1YyTGl2ZURlZmF1bHREZWZhdWx0Eg8xOjFNUU5MQ0lHN0MwS0UaEDI6RFlCNUIwQjk5NzI0QUYiBlk2UDRjURI+CgRob21lEgZhZGRvbnMiBmNlbnRlcioAMiQxNzlmZmZkNy0xOTI5LTQ3YjItYmZkZi0zZDc3MDBlNmQ5YmYaDHN1YnNjcmlwdGlvbiIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQhlGcmVzaFBpY2tzSW5TdWJzY3JpcHRpb25zUgtub3RFbnRpdGxlZFoAYhBTdGFuZGFyZENhcm91c2VsaAZyAHo4a003RDZYcjVJYS1BbGVWUkFlcWp5YjVJLTV0OG1lb1N3SFJYMlpqMHMzXzZBcE0zVEtfbWpnPT2CAQVmYWxzZYoBAJIBAA=="}, "tags": [], "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24=", "type": "STANDARD_CAROUSEL"}], "paginationLink": {"serviceToken": "v0_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", "startIndex": 6, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6WioZhZGRvbnOLhGhvbWWMD40PjoJWMg==", "pageId": "addons", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "addons"}}, "subNav": [], "pageMetadata": {"title": "", "logoImage": {}, "locationDependentPage": false, "showVoiceFilters": false, "persistentTitleOrLogo": false}}, "metadata": {"requestId": "kM7D6Xr5Ia-AleVRAeqjyb5I-5t8meoSwHRX2Zj0s3_6ApM3TK_mjg==", "requestedTransformId": "lr/collections/collectionsPageInitial", "domain": "prod", "realm": "us-east-1", "timestamp": "2025-06-18T15:26:36.147146Z"}}