use crate::network::parser::collections_response_parser;
use crate::network::types::{CollectionInitialRequest, RestoreCollectionPageFocusRequest};
use crate::page::controller::{
    CollectionsPageControlling, CollectionsPageFeatures, PageControllerSignals,
};
use crate::reporting::app_events::CollectionsAppEvents;
use crate::types::collections_types::CollectionsPageModel;
use beekeeper::types::{
    BeekeeperFallbackPageParams, ResiliencyPage, StorefrontFallbackPageFulfilledAction,
};
use cache::ExpirableLruCacheRc;
use collection_types::network_types::CollectionsPage;
use common_transform_types::containers::{Container, PaginatableComponent};
use common_transform_types::resiliency::WithResiliency;
use container_types::network::PaginationResponse;
use ignx_compositron::prelude::create_rw_signal;
use ignx_compositron::prelude::safe::Scope;
#[cfg(test)]
use ignx_compositron::prelude::safe::{create_signal, RwSignal, Signal};
use ignx_compositron::reactive::store_value;
#[cfg(test)]
use ignx_compositron::time::{Duration, MockClock};
use ignx_compositron::{prelude::safe::AppContext, time::Instant};
use location::{Location, RustPage};
use mockall::mock;
#[cfg(test)]
use modal_manager::modal_manager::AppModal;
#[cfg(test)]
use navigation_menu::utils::CollectionsPageParams;
use navigation_menu::utils::PageParams;
use network::common::{DeviceProxyResponse, PageRequestStatus};
use network::{
    common::lrc_edge_constants::{CollectionInitialTypes, DynamicNetworkParameters},
    RequestError,
};
#[cfg(test)]
use router::rust_location;
use std::cell::RefCell;
use std::rc::Rc;

mock! {
    pub NetworkClient {
        pub fn new(
            ctx: &AppContext<'static>
        ) -> Self;

        #[allow(deprecated, reason = "mock")]
        pub fn paginate<T: FnOnce(PaginationResponse, String) + 'static, U: FnOnce(RequestError) + 'static>(
            &self,
            page: RustPage,
            pagination: &PaginatableComponent,
            dynamic_params: DynamicNetworkParameters,
            success_cb: T,
            failure_cb: U,
        );

        pub fn collection_initial<T: FnOnce(CollectionsPage, Instant) + 'static, U: FnOnce(RequestError) + 'static, R: FnOnce(StorefrontFallbackPageFulfilledAction<CollectionsPage>) + 'static>(
            &self,
            success_cb: T,
            failure_cb: U,
            resiliency_callback: R,
            page_controller: Rc<dyn CollectionsPageControlling>,
            collection_initial_type: CollectionInitialTypes,
            request_params: &CollectionInitialRequest,
            cache: ExpirableLruCacheRc<String, CollectionsPage>,
            beekeeper_page_params: BeekeeperFallbackPageParams,
            redirect: Option<ResiliencyPage>,
            is_reload: bool,
        );

        pub fn restore_collection_page_focus<T: FnOnce(CollectionsPage, Instant) + 'static, U: FnOnce(RequestError) + 'static>(
            &self,
            success_cb: T,
            failure_cb: U,
            page_controller: Rc<dyn CollectionsPageControlling>,
            collection_initial_type: CollectionInitialTypes,
            request_params: &RestoreCollectionPageFocusRequest
        );

        pub fn prefetch_collection_initial(
            &self,
            page_source: RustPage,
            collection_initial_type: CollectionInitialTypes,
            params: &CollectionInitialRequest,
            cache: ExpirableLruCacheRc<String, CollectionsPage>,
        );

        pub fn get_collection_page_sports_edge<T: FnOnce(CollectionsPage, Instant) + 'static, U: FnOnce(RequestError) + 'static, R: FnOnce(StorefrontFallbackPageFulfilledAction<CollectionsPage>) + 'static>(
            &self,
            success_cb: T,
            failure_cb: U,
            resiliency_callback: R,
            page_controller: Rc<dyn CollectionsPageControlling>,
            collection_initial_type: CollectionInitialTypes,
            request_params: &CollectionInitialRequest,
            cache: ExpirableLruCacheRc<String, CollectionsPage>,
            beekeeper_page_params: BeekeeperFallbackPageParams,
            redirect: Option<ResiliencyPage>,
            is_reload: bool,
        );
    }
}

pub mod mock_display {
    // Returns 720p resolution value
    pub fn display_scale() -> f32 {
        2.0 / 3.0
    }
}

mock! {
    pub CollectionsPageController {
        pub fn new(ctx: &AppContext<'static>, page_signals: PageControllerSignals, features: CollectionsPageFeatures) -> Self;
    }

    impl CollectionsPageControlling for CollectionsPageController {
        fn report_app_event<'a>(&self, event: CollectionsAppEvents<'a>);
        fn page_load_success<'a>(&self, page_params: Option<&'a PageParams>);
        fn page_load_failure<'a>(&self, page_params: Option<&'a PageParams>, error: &RequestError);
        fn page_source(&self) -> &RustPage;
        fn ctx(&self) -> &AppContext<'static>;
        fn signals(&self) -> &PageControllerSignals;
        fn page_features(&self) -> &CollectionsPageFeatures;
    }
}

pub(crate) trait MockData {
    fn mock_data(scope: Scope<'static>) -> Self;
}

impl MockData for PageControllerSignals {
    fn mock_data(scope: Scope<'static>) -> Self {
        PageControllerSignals {
            initial_load_status: create_rw_signal(scope, PageRequestStatus::Waiting),
            page_data: create_rw_signal(
                scope,
                CollectionsPageModel {
                    container_list: create_rw_signal(scope, vec![]),
                    pagination_link: create_rw_signal(scope, None),
                    page_title: create_rw_signal(scope, None),
                    page_logo_image: create_rw_signal(scope, None),
                    pagination_pending: create_rw_signal(scope, false),
                    unique_id: create_rw_signal(scope, "default".into()),
                    start_column: create_rw_signal(scope, None),
                },
            ),
            modal_data: create_rw_signal(scope, Vec::new()).write_only(),
            internal_location: create_rw_signal(scope, Location::default()).into(),
            last_request_counter: store_value(scope, 0),
            data_fetch_data: create_rw_signal(scope, None),
            should_reload: create_rw_signal(scope, false),
        }
    }
}

pub(crate) struct MockControllerBuilder {
    signals: PageControllerSignals,
    features: CollectionsPageFeatures,
    page_source: RustPage,
    ctx: AppContext<'static>,
    expected_app_events: Vec<CollectionsAppEvents<'static>>,
    expected_any_app_event: bool,
}

impl MockControllerBuilder {
    pub(crate) fn new(ctx: AppContext<'static>) -> Self {
        MockControllerBuilder {
            signals: PageControllerSignals::mock_data(ctx.scope),
            features: CollectionsPageFeatures::default(),
            page_source: RustPage::RUST_COLLECTIONS,
            ctx,
            expected_app_events: vec![],
            expected_any_app_event: false,
        }
    }

    pub(crate) fn with_signals(mut self, signals: PageControllerSignals) -> Self {
        self.signals = signals;
        self
    }

    pub(crate) fn with_features(mut self, features: CollectionsPageFeatures) -> Self {
        self.features = features;
        self
    }

    pub(crate) fn with_source(mut self, page_source: RustPage) -> Self {
        self.page_source = page_source;
        self
    }

    pub(crate) fn with_app_event(mut self, app_event: CollectionsAppEvents<'static>) -> Self {
        self.expected_app_events.push(app_event);
        self
    }

    pub(crate) fn with_app_events(
        mut self,
        app_events: Vec<CollectionsAppEvents<'static>>,
    ) -> Self {
        self.expected_app_events = app_events;
        self
    }

    pub(crate) fn with_any_app_events(mut self) -> Self {
        self.expected_any_app_event = true;
        self
    }

    pub(crate) fn setup(self) -> MockCollectionsPageController {
        let mut page_controller = MockCollectionsPageController::default();
        page_controller
            .expect_page_source()
            .return_const(self.page_source);
        page_controller.expect_signals().return_const(self.signals);
        page_controller.expect_ctx().return_const(self.ctx);
        page_controller
            .expect_page_features()
            .times(..)
            .return_const(self.features);
        if self.expected_any_app_event {
            page_controller.expect_report_app_event().return_const(());
        } else {
            for app_event in self.expected_app_events {
                page_controller
                    .expect_report_app_event()
                    .times(1)
                    .withf(move |event| event == &app_event)
                    .return_const(());
            }
        }
        page_controller
    }
}

pub(crate) struct SuccessCallbackTestSetup {
    pub(crate) controller_signals: PageControllerSignals,
    pub(crate) features: CollectionsPageFeatures,
    pub(crate) ctx: AppContext<'static>,
    pub(crate) manipulate_page_data_fn: bool,
    pub(crate) parsed_page_from_network: CollectionsPage,
}

pub(crate) struct SuccessCallbackArgs {
    pub(crate) parsed_page_from_network: CollectionsPage,
    pub(crate) transform_name: &'static str,
    pub(crate) manipulate_page_data: Option<Box<dyn FnOnce(&mut CollectionsPageModel)>>,
    pub(crate) timestamp: Instant,
    pub(crate) manipulate_page_data_calls: Rc<RefCell<usize>>,
}

#[cfg(test)]
impl SuccessCallbackTestSetup {
    pub(crate) fn new(ctx: AppContext<'static>) -> Self {
        SuccessCallbackTestSetup {
            controller_signals: PageControllerSignals::mock_data(ctx.scope),
            features: CollectionsPageFeatures::default(),
            ctx,
            manipulate_page_data_fn: false,
            parsed_page_from_network: get_populated_collections_page(),
        }
    }

    pub(crate) fn with_page_data_and_status(
        mut self,
        page_data: RwSignal<'static, CollectionsPageModel>,
        initial_load_status: RwSignal<'static, PageRequestStatus<String>>,
    ) -> Self {
        self.controller_signals = PageControllerSignals {
            page_data,
            initial_load_status,
            last_request_counter: store_value(self.ctx.scope, 1),
            ..self.controller_signals
        };
        self
    }

    pub(crate) fn with_manipulate_page_data_fn(mut self) -> Self {
        self.manipulate_page_data_fn = true;
        self
    }

    pub(crate) fn with_modal_data_signal(
        mut self,
        signal: RwSignal<'static, Vec<AppModal>>,
    ) -> Self {
        self.controller_signals = PageControllerSignals {
            modal_data: signal.write_only(),
            ..self.controller_signals
        };
        self
    }

    pub(crate) fn with_location_signal(mut self, signal: Signal<'static, Location>) -> Self {
        self.controller_signals = PageControllerSignals {
            internal_location: signal,
            ..self.controller_signals
        };
        self
    }

    pub(crate) fn with_location_signal_from_params(
        mut self,
        params: CollectionsPageParams,
    ) -> Self {
        let signal = create_signal(
            self.ctx.scope,
            rust_location!(RUST_COLLECTIONS, {
                "pageType" => params.page_type,
                "pageId" => params.page_id,
                "serviceToken" => params.service_token
            }),
        )
        .0;
        self = self.with_location_signal(signal.into());
        self
    }

    pub(crate) fn with_location_signal_from_default_params(mut self) -> Self {
        self = self.with_location_signal_from_params(CollectionsPageParams {
            page_type: "home".into(),
            page_id: "home".into(),
            service_token: None,
        });
        self
    }

    pub(crate) fn with_features(mut self, features: CollectionsPageFeatures) -> Self {
        self.features = features;
        self
    }

    pub(crate) fn with_parsed_page(mut self, page: CollectionsPage) -> Self {
        self.parsed_page_from_network = page;
        self
    }

    pub(crate) fn to_controller(&self) -> MockCollectionsPageController {
        MockControllerBuilder::new(self.ctx.clone())
            .with_signals(self.controller_signals.clone())
            .with_features(self.features.clone())
            .setup()
    }

    pub(crate) fn to_success_cb_args(&self) -> SuccessCallbackArgs {
        let manipulate_page_data_calls = Rc::new(RefCell::new(0));
        let manipulate_page_data: Option<Box<dyn FnOnce(&mut CollectionsPageModel)>> =
            if self.manipulate_page_data_fn {
                Some(Box::new({
                    let manipulate_page_data_calls = manipulate_page_data_calls.clone();
                    move |_: &mut CollectionsPageModel| {
                        manipulate_page_data_calls.replace_with(|val| *val + 1);
                    }
                }))
            } else {
                None
            };
        MockClock::set_time(Duration::new(12, 35));
        let timestamp = Instant::now();
        SuccessCallbackArgs {
            parsed_page_from_network: self.parsed_page_from_network.clone(),
            transform_name: "a_transform",
            manipulate_page_data,
            timestamp,
            manipulate_page_data_calls,
        }
    }
}

pub(crate) fn get_populated_collections_page() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/collections_page_full_response.json").to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_promo_banner() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/collections_page_with_promo_banner.json").to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_linear_station_hero() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/collections_page_with_linear_station_hero.json").to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_see_more_links() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/collections_page_with_see_more.json").to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_beard_support() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/collections_page_with_beard_support.json").to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_onboarding_cx_content() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/onboarding_cx_page_with_single_onboarding_container.json")
            .to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_only_unusable_content() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/onboarding_cx_page_with_single_onboarding_container.json")
            .to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(mut r) => {
            r.resource.containerList = r
                .resource
                .containerList
                .into_iter()
                .filter(|container| {
                    !matches!(container, WithResiliency::Ok(Container::ONBOARDING(_)))
                })
                .collect();
            r.resource
        }
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_mixed_usable_unusable_content() -> CollectionsPage
{
    collections_response_parser(
        include_str!("../test_assets/collections_page_bad_response.json").to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_edcx_modal_single_item() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/collections_page_sports_with_edcx_single_item.json")
            .to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}

pub(crate) fn get_populated_collections_page_with_edcx_modal_multiple_items() -> CollectionsPage {
    collections_response_parser(
        include_str!("../test_assets/collections_page_sports_with_edcx_multiple_items.json")
            .to_string(),
    )
    .map(|result| match result {
        DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
        DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
    })
    .unwrap()
}
