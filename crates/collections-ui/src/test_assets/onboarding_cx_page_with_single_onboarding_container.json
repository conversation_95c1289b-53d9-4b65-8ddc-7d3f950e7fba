{"resource": {"containerList": [{"title": "What do you like to watch?", "subtitle": "Select movies and TV shows and we’ll use those to find videos we think you’ll like.", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt60ioRob21li4Rob21ljI6fMToxMVdCR1I3RDk4RUFWQyMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "items": [], "tags": [], "analytics": {"refMarker": "hm_hom_p_KeLL5X_1", "ClientSideMetrics": "484|CnMKRU9uYm9hcmRpbmdUZXh0V2lkZ2V0U3RhdGljTm9JdGVtc1NwZWVkQnVtcEFsdGVybmF0ZUxpdmVEZWZhdWx0RGVmYXVsdBIQMToxMVdCR1I3RDk4RUFWQxoQMjpEWTVCMjgyRjQ3REQ3MCIGS2VMTDVYEkQKBGhvbWUSBGhvbWUiDnByaW9yaXR5Q2VudGVyKgAyJGZlNTI3ODFmLTA4YWYtNGRhNC04NmFiLTNjNTE2ZmRhNDA2ZRoDYWxsIgNhbGwqADIIY2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oaT25ib2FyZGluZ1ZhcmlhbnRTcGVlZEJ1bXBSC25vdEVudGl0bGVkWgBiClRleHRXaWRnZXRoAXIAejhzc2FyODVzNVJhZUVSdEhsbjR5bFlWS0M4TE50U1dwTnF3TDJfVGpKcmFJX24xUkpRVGtZVXc9PYIBA2FsbIoBAJIBAA=="}, "journeyIngressContext": "8|EgNhbGw=", "type": "TEXT_WIDGET"}, {"title": " ", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMTBNMlNCOUgwODBUNyMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "items": [{"title": "BMF - Season 1", "gti": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "transformItemId": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "synopsis": "The story of how <PERSON><PERSON><PERSON> \"<PERSON> Meech\" <PERSON><PERSON><PERSON> formed a criminal empire in Detroit known as the Black Mafia Family.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_romance"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c28334d512e6e209c5b293a984c824511d4fca9715b0fe85f4e39295083c8921.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9b1b690399279aa4a2a43fe6154f0ac09790e11ec3b18dd219356922438eea93._UR1920,1080_RI_.png", "publicReleaseDate": 1637452800000, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_1"}, "refMarker": "hm_hom_p_THPXkc_brws_2_1", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "First episode free"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "First episode free", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_1"}, "refMarker": "hm_hom_p_THPXkc_brws_2_1", "journeyIngressContext": "8|EgNhbGw="}], "showName": "BMF", "cardType": "TITLE_CARD"}, {"title": "Avatar", "gti": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "transformItemId": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "synopsis": "In 2154, the U.S. military mines a valuable mineral from a distant world called Pandora. A paraplegic ex-Marine becomes an Avatar for Pandora, but when he sparks a romance with a female, he finds himself at the center of a war for control of Pandora.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_science_fiction", "av_genre_fantasy", "av_genre_action", "av_genre_adventure"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cb0a52213794c85f3bab31fd9f830c1608e760803170367ef81ffa625fb5535f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7062a2ea3e7a8d83b6e2761101247b9fb9b3af330fe16f1fcfd574a8797711db._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/34e03a8849dda34699b6aa3d05ee7f0e6ebf6451c6eb8f3b53721fa99498b056.jpg", "publicReleaseDate": 1261094400000, "runtimeSeconds": 9700, "overallRating": 4.7, "totalReviewCount": 29018, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_2"}, "refMarker": "hm_hom_p_THPXkc_brws_2_2", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 3X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_2"}, "refMarker": "hm_hom_p_THPXkc_brws_2_2", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "M3GAN", "gti": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "transformItemId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "synopsis": "A roboticist working on a life-like toy android named M<PERSON>GA<PERSON> takes in her orphaned niece. She pairs the two up, trying to solve for both issues...and it does not go as planned.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_horror", "av_genre_suspense", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ce339dce3ec9c8836c40a695cf5d8e8e9ae1ccb9c569a789e4e5b718f3f6c98c.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f95c7caa5a871fab53b3aebb979ba2ff23daa4ff1959fcf8f4947361388db9d6._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5eb3c81466ddb8f0cfbce4bd2b4d1c795db817cf7c01bfa9416bac01656a6f24.jpg", "publicReleaseDate": 1672963200000, "runtimeSeconds": 6114, "overallRating": 4.3, "totalReviewCount": 1493, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_3"}, "refMarker": "hm_hom_p_THPXkc_brws_2_3", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_3"}, "refMarker": "hm_hom_p_THPXkc_brws_2_3", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "The Boys - Season 1", "gti": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "transformItemId": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "synopsis": "THE BOYS is an irreverent take on what happens when superheroes, who are as popular as celebrities, as influential as politicians and as revered as Gods, abuse their superpowers rather than use them for good. It's the powerless against the super powerful as The Boys embark on a heroic quest to expose the truth about “The Seven,” and their formidable Vought backing.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_comedy", "av_genre_drama", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a008b073c68fc474ed6397e84cb4436c5e26d3bfdbcc6fcc16e122c37351c944.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/atv-aps-images/encoded/THBY/en_US/COVER_ART/CLEAN/<PERSON>ie_Starlight._UR1920,1080_RI_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/a40deb56c1f0b2e8d354f078c4c00f4ebcaa7f0eb4f488af01d02d25af034916.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/8759018fc457e08b5d188c696cdfbdcb1311231b4572ab078c3ef09231c0211a.jpg", "publicReleaseDate": 1564099200000, "overallRating": 3.8, "totalReviewCount": 10758, "seasonNumber": 1, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_4"}, "refMarker": "hm_hom_p_THPXkc_brws_2_4", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2023"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_4"}, "refMarker": "hm_hom_p_THPXkc_brws_2_4", "journeyIngressContext": "8|EgNhbGw="}], "showName": "The Boys", "cardType": "TITLE_CARD"}, {"title": "Yellowstone Season 1", "gti": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "transformItemId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "synopsis": "The <PERSON><PERSON>s fight to defend their ranch and way of life from an Indian reservation and land developers, as medical issues, political aspirations and deep secrets put strain on the family.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_western"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/1593fce889efbfa54b0d7f713be5ab4c7b946cd1e73e2e10f7edd7db6e107ed8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/567eca6ca1d1310cfbe06f213169dc086a95b8eb3ee28beb82f9fd0d82ed14c3._UR1920,1080_RI_.png", "publicReleaseDate": 1534896000000, "overallRating": 3.9, "totalReviewCount": 775, "seasonNumber": 1, "numberOfSeasons": 5, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_5"}, "refMarker": "hm_hom_p_THPXkc_brws_2_5", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBE® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_5"}, "refMarker": "hm_hom_p_THPXkc_brws_2_5", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Yellowstone", "cardType": "TITLE_CARD"}, {"title": "Reacher - Season 1", "gti": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "transformItemId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "synopsis": "When retired Military Police Officer <PERSON> is arrested for a murder he did not commit, he finds himself in the middle of a deadly conspiracy full of dirty cops, shady businessmen and scheming politicians. With nothing but his wits, he must figure out what is happening in Margrave, Georgia. The first season of <PERSON> is based on the international bestseller, The Killing Floor by <PERSON>.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_drama", "av_genre_suspense"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/56d391fc82789f8a0f04649842743bc853bebf2619d45d85e3486fb25a8f2d66.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/394691d70d92cb4aaf3828c985fb050fff23fc4e0f483403c5b12450f6645a56._UR1920,1080_RI_.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/34dcf97468563cb2d03824a83f526036a3f595d293843fde2345a87cb91310b4.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/atv-aps-images/encoded/RCHR_S1/US/en_US/POSTER_ART/CLEAN/KRISTEN.jpg", "publicReleaseDate": 1643932800000, "overallRating": 4.7, "totalReviewCount": 10591, "seasonNumber": 1, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_6"}, "refMarker": "hm_hom_p_THPXkc_brws_2_6", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_6"}, "refMarker": "hm_hom_p_THPXkc_brws_2_6", "journeyIngressContext": "8|EgNhbGw="}], "showName": "<PERSON>", "cardType": "TITLE_CARD"}, {"title": "Tulsa King Season 1", "gti": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "transformItemId": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "synopsis": "After serving 25 years in prison, <PERSON> (<PERSON>) is unceremoniously exiled by his mob boss to set up shop in Oklahoma.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f17883ece345f950af01279d7d15b0366789bb3522098db3d8f4d1604e428f38.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/51621d164f0d000fd3426b90111b5a04c173df788aa978422256c127150c2acc._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/be6de532e72d6dfb95def8be8cda2824138e316c8c6a9788603e1842e4d5b0a4.jpg", "publicReleaseDate": 1673136000000, "overallRating": 4.7, "totalReviewCount": 279, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_7"}, "refMarker": "hm_hom_p_THPXkc_brws_2_7", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Paramount+ or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_7"}, "refMarker": "hm_hom_p_THPXkc_brws_2_7", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Tulsa King", "cardType": "TITLE_CARD"}, {"title": "Top Gun: <PERSON><PERSON><PERSON>", "gti": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "transformItemId": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "synopsis": "After thirty years, <PERSON><PERSON><PERSON> is still pushing the envelope as a top naval aviator, but must confront ghosts of his past when he leads TOP GUN's elite graduates on a mission that demands the ultimate sacrifice from those chosen to fly it.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a99fdd0071f7598f0e438129970c8647dfb1528bd03e8b783211268214a7e1ae.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5099b0bb5c1a20bc2f43fe3a4934c94412c842a595deb5220d70a7ee959aae29._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/1e4598108574946b2227be0abe74b84ac6ae419022e04b53f4f4e96d593f3b40.jpg", "publicReleaseDate": 1653609600000, "runtimeSeconds": 7535, "overallRating": 4.7, "totalReviewCount": 115455, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_8"}, "refMarker": "hm_hom_p_THPXkc_brws_2_8", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCAR® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_8"}, "refMarker": "hm_hom_p_THPXkc_brws_2_8", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "Black Adam", "gti": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "transformItemId": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "synopsis": "The story of the DC superhero comes to the big screen in this thrilling action-adventure starring <PERSON><PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_fantasy", "av_genre_action", "av_genre_adventure", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_amazon_maturity_rating_13_plus", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8512ded1639be52f10cd3a86dfa1c15431f0e0fdcc8199a6fcd9085e0dbc993f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/bfe723f6aaa6e49a8801f9e88cfc7abb1c7e48f257f34bfb50003452f829bbc2._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/48c347301363a0af6d9744497acd376336128e278c20fcc103bc7408540748c2.jpg", "publicReleaseDate": 1666310400000, "runtimeSeconds": 7197, "overallRating": 4.2, "totalReviewCount": 25895, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_9"}, "refMarker": "hm_hom_p_THPXkc_brws_2_9", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_9"}, "refMarker": "hm_hom_p_THPXkc_brws_2_9", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "<PERSON>'s <PERSON> - Season 1", "gti": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "transformItemId": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "synopsis": "When CIA analyst <PERSON> stumbles upon a suspicious series of bank transfers his search for answers pulls him from the safety of his desk job and catapults him into a deadly game of cat and mouse throughout Europe and the Middle East, with a rising terrorist figurehead preparing for a massive attack against the US and her allies.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "TV-14", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_drama", "av_genre_suspense"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_14", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d42dd756738a5fe64ad1affe067d682c7f484f6b49dbd26955b5161c2578f08b.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/6438b5bd7aeeabf01229914f3e745321c9aa7c04daefbbef3d5f5c599c3d084d._UR1920,1080_RI_.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/58684ed93429974928a5299eb46f248730223d2a7d8b6e155d18b40072d6b569.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/156eb6ba535126a24c4630c5d08793005c8acbd7d51ca9eef55e2fc0250dcf71.png", "publicReleaseDate": -2034806400000, "overallRating": 4.6, "totalReviewCount": 20532, "seasonNumber": 1, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_10"}, "refMarker": "hm_hom_p_THPXkc_brws_2_10", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2020"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_10"}, "refMarker": "hm_hom_p_THPXkc_brws_2_10", "journeyIngressContext": "8|EgNhbGw="}], "showName": "<PERSON>'s <PERSON>", "cardType": "TITLE_CARD"}, {"title": "The Summer I Turned Pretty - Season 1", "gti": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "transformItemId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "synopsis": "<PERSON><PERSON> is about to turn 16, and she’s headed to her favorite place in the world, Cousins Beach, to spend the summer with her family and the Fishers. <PERSON><PERSON>’s grown up a lot over the past year, and she has a feeling that this summer is going to be different than all the summers before. The Summer I Turned Pretty is based on the book by <PERSON>, who is creator and executive producer.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_romance", "av_genre_young_adult_audience"], "maturityRatingString": "vcc_maturity_rating_amazon_maturity_rating_16_plus", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b7a291cc0a1b1860d0f20e355964aa221d91bdd0593fec62f45beb5ad60529ca.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/atv-aps-images/encoded/SITP_S1/US/en_US/COVER_ART/CLEAN/Image04._UR1920,1080_RI_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/b76400003c5bc461334996bbda76e01176c952d40642158731ead5fbf342ea9a.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/30fbbc1634e6f70c488b55ea96de20e9a88224544ac4503df204518173538ae5.jpg", "publicReleaseDate": 1655424000000, "overallRating": 4.6, "totalReviewCount": 1974, "seasonNumber": 1, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_11"}, "refMarker": "hm_hom_p_THPXkc_brws_2_11", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_11"}, "refMarker": "hm_hom_p_THPXkc_brws_2_11", "journeyIngressContext": "8|EgNhbGw="}], "showName": "The Summer I Turned Pretty", "cardType": "TITLE_CARD"}, {"title": "<PERSON> and the Sorcerer's Stone", "gti": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "transformItemId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "synopsis": "Based on the wildly popular <PERSON><PERSON><PERSON><PERSON>'s book about a young boy who on his eleventh birthday discovers, he is the orphaned boy of two powerful wizards and has unique magical powers.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_kids", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d6077c75475b8086e6eb7f3994ff395af0a34a5543e30e373bd7beb4b6c02dce.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/477b552bc91175c11daa52d1160e6e8736828285e35f01f6c86d24a5bf8adabb._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/187772b81de30443601c19fca2c64a0bcb8154f1116b1f17e24b8e3ab0f76682.jpg", "publicReleaseDate": 1005868800000, "runtimeSeconds": 8778, "overallRating": 4.8, "totalReviewCount": 89295, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_12"}, "refMarker": "hm_hom_p_THPXkc_brws_2_12", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 3X nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_12"}, "refMarker": "hm_hom_p_THPXkc_brws_2_12", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "Barbie", "gti": "amzn1.dv.gti.17f2bd91-afd8-4ae8-8dbc-9c49066e771c", "transformItemId": "amzn1.dv.gti.17f2bd91-afd8-4ae8-8dbc-9c49066e771c", "synopsis": "To live in Barbie Land is to be a perfect being in a perfect place. Unless you have a full‐on existential crisis. Or you're a Ken.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_comedy", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f7ab7cb25422aa76e7a87af71fa4996d6ce0d61df4dc1d34fa258cdb609392e8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/059ee8a729a193202fc78a6993a521e951d73fb385caa8d285c59b4307e2c2fc._UR1920,1080_RI_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/055a5ce7ce32b836e78840b672e7f2962d6fcc36e7f17ebff09f0de7d780bc1d.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/a02c16367449bd3cc66a864141044bf6b34016c80f0b69a7afdf0cf3abb35e03.jpg", "publicReleaseDate": 1689897600000, "runtimeSeconds": 6841, "overallRating": 4.5, "totalReviewCount": 14480, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.17f2bd91-afd8-4ae8-8dbc-9c49066e771c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_13"}, "refMarker": "hm_hom_p_THPXkc_brws_2_13", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCAR® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.17f2bd91-afd8-4ae8-8dbc-9c49066e771c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_13"}, "refMarker": "hm_hom_p_THPXkc_brws_2_13", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "Game of Thrones - Season 1", "gti": "amzn1.dv.gti.1ad8b490-650c-4a68-ad26-c8041d756712", "transformItemId": "amzn1.dv.gti.1ad8b490-650c-4a68-ad26-c8041d756712", "synopsis": "The inhabitants of a mythical world vie for power while a long-forgotten evil awakens in Season 1 of this epic HBO series.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_drama", "av_genre_fantasy", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/6fb04fc002b005a28a0d2b2bc1a1e9ca06c9dd05a7e5d006033776c05a44d706.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/ebccfc1566ad4904cca3af25d38be6a02bef8069c1a75687ac6d03045fee5de3._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/0b6788201610885abc7fa9a141770971ecb547be69bb9536324b5736019d63c5.jpg", "publicReleaseDate": 1302998400000, "overallRating": 4.7, "totalReviewCount": 9517, "seasonNumber": 1, "numberOfSeasons": 8, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1ad8b490-650c-4a68-ad26-c8041d756712", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_14"}, "refMarker": "hm_hom_p_THPXkc_brws_2_14", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X winner in 2014"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1ad8b490-650c-4a68-ad26-c8041d756712", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_14"}, "refMarker": "hm_hom_p_THPXkc_brws_2_14", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Game of Thrones", "cardType": "TITLE_CARD"}, {"title": "Spider-Man: No Way Home", "gti": "amzn1.dv.gti.53065791-4335-46ce-bc1b-5c5ebbb21073", "transformItemId": "amzn1.dv.gti.53065791-4335-46ce-bc1b-5c5ebbb21073", "synopsis": "From Marvel Studios: With his identity revealed, <PERSON> asks <PERSON> for help. But when a spell goes wrong, <PERSON> is forced to discover what it truly means to be Spider-<PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_fantasy", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f330b20efbbdd1d3dbb2ce2cdbfca09706e229998381d1968aacc8777d701b07.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3e9fb1a067246340e6aa1f1c6b281a2e9d8bc31acfb7f464cc9a1884939b1373._UR1920,1080_RI_.jpg", "publicReleaseDate": 1639699200000, "runtimeSeconds": 8890, "overallRating": 4.7, "totalReviewCount": 73886, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.53065791-4335-46ce-bc1b-5c5ebbb21073", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_15"}, "refMarker": "hm_hom_p_THPXkc_brws_2_15", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCAR® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.53065791-4335-46ce-bc1b-5c5ebbb21073", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_15"}, "refMarker": "hm_hom_p_THPXkc_brws_2_15", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "SpongeBob SquarePants Season 1", "gti": "amzn1.dv.gti.46a9f736-6880-98bb-e01b-7396e214ab5b", "transformItemId": "amzn1.dv.gti.46a9f736-6880-98bb-e01b-7396e214ab5b", "synopsis": "Ahoy, mateys! Dive into the first season of SpongeBob SquarePants, filled with waterlogged hilarity and nautical nonsense! \"R<PERSON>ped Pants,\" \"Nature pants, \"<PERSON><PERSON><PERSON> Pants,\" and much more - it's an unsinkable good time with the silliest sea sponge around!", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "TV-Y7", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_kids", "av_genre_animation_adult_interest", "av_genre_comedy", "av_genre_fantasy"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_y7", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/130834d467e6a7dc1c99a70f657706e0d0d14ef32c983d8f9f7a0a86522e4d96.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d5432e5df4dd7816fe02a11fda462a8ea5511b6ef8760c0e244eb95e7dbb922a._UR1920,1080_RI_.jpg", "publicReleaseDate": 925516800000, "overallRating": 4.7, "totalReviewCount": 8365, "seasonNumber": 1, "numberOfSeasons": 16, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.46a9f736-6880-98bb-e01b-7396e214ab5b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_16"}, "refMarker": "hm_hom_p_THPXkc_brws_2_16", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2019"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.46a9f736-6880-98bb-e01b-7396e214ab5b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_16"}, "refMarker": "hm_hom_p_THPXkc_brws_2_16", "journeyIngressContext": "8|EgNhbGw="}], "showName": "SpongeBob SquarePants", "cardType": "TITLE_CARD"}, {"title": "Friends: The Complete First Season", "gti": "amzn1.dv.gti.2a5a852a-5178-4d65-bfd2-0b0b471961b5", "transformItemId": "amzn1.dv.gti.2a5a852a-5178-4d65-bfd2-0b0b471961b5", "synopsis": "Six young people, on their own and struggling to survive in the real world, find the companionship, comfort and support they get from each other to be the perfect antidote to the pressures of life.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_comedy", "av_genre_romance"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_pg", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/6451855fcae787c6b98621cb5e3ac41b295e2d7dd619f5a4ee2ca5033266d2b1.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/6d70eec29d63add670921f50a769804b93247625d1cb74959bb25cb3b18c6e96._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/6daa0afd6ccc5fa203cc07046a9e5952d1f0ccd8b5bcf11bbd103a590c65737d.jpg", "publicReleaseDate": 800755200000, "overallRating": 4.8, "totalReviewCount": 8808, "seasonNumber": 1, "numberOfSeasons": 10, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2a5a852a-5178-4d65-bfd2-0b0b471961b5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_17"}, "refMarker": "hm_hom_p_THPXkc_brws_2_17", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 7X nominee in 2004"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2a5a852a-5178-4d65-bfd2-0b0b471961b5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_17"}, "refMarker": "hm_hom_p_THPXkc_brws_2_17", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Friends", "cardType": "TITLE_CARD"}, {"title": "Euphoria - Season 1", "gti": "amzn1.dv.gti.f44a55f5-6c67-467c-b7a2-637c3f65dab4", "transformItemId": "amzn1.dv.gti.f44a55f5-6c67-467c-b7a2-637c3f65dab4", "synopsis": "<PERSON><PERSON><PERSON> stars in this series that follows a group of high-school students as they navigate an unstable world.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a2e3558882ee7ed5e61b1e8b18b512e0442ec15f1111396d0e9956ddfd68af55.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b971bd58fbc6bece29003f69ac57b24d193fc71428dbdb2fee65ea75dc9c7dbf._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/ec632e8a5b7a62583151a9571f8bb1a311572ccf1576637f214c1db4460b07a5.jpg", "publicReleaseDate": 1564876800000, "overallRating": 4.6, "totalReviewCount": 5454, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f44a55f5-6c67-467c-b7a2-637c3f65dab4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_18"}, "refMarker": "hm_hom_p_THPXkc_brws_2_18", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBE® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f44a55f5-6c67-467c-b7a2-637c3f65dab4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_18"}, "refMarker": "hm_hom_p_THPXkc_brws_2_18", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Euphoria", "cardType": "TITLE_CARD"}, {"title": "Home Alone", "gti": "amzn1.dv.gti.00a9f78f-9bb3-697b-88f6-5dfdd2dd5228", "transformItemId": "amzn1.dv.gti.00a9f78f-9bb3-697b-88f6-5dfdd2dd5228", "synopsis": "The original comedy classic! <PERSON> gets left behind when the family leaves for an overseas Christmas vacation, and finds himself outwitting a pair of bumbling burglars in this slapstick riot!", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_comedy", "av_genre_kids"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8d08747a29bc674278647c8f3bc1d91275a5140dcca6c7908655e7b4e539289f.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5e722c37f2b5810acaeb2c0b5f15928900dc8c2cadd0b805ce30598504a87df7._UR1920,1080_RI_.jpg", "publicReleaseDate": 658713600000, "runtimeSeconds": 6168, "overallRating": 4.8, "totalReviewCount": 77715, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.00a9f78f-9bb3-697b-88f6-5dfdd2dd5228", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_19"}, "refMarker": "hm_hom_p_THPXkc_brws_2_19", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 2X nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.00a9f78f-9bb3-697b-88f6-5dfdd2dd5228", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_19"}, "refMarker": "hm_hom_p_THPXkc_brws_2_19", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "The Walking Dead, Season 1", "gti": "amzn1.dv.gti.d0cee30c-926f-4dd7-b59c-4d6f2fbf8c69", "transformItemId": "amzn1.dv.gti.d0cee30c-926f-4dd7-b59c-4d6f2fbf8c69", "synopsis": "When the world is ravaged by a zombie apocalypse, police officer <PERSON> and a small group of others must face a terrifying new reality. Can they survive 'the dead' and each other? The Walking Dead is a survival adventure series from the director of The Shawshank Redemption and the producer of The Terminator.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_suspense", "av_genre_horror", "av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_amazon_maturity_rating_18_plus", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/5aceaa41d992d16d51bac9dbb6b5ed96ffcc736fd7f1ea6c77c1ef451b37a07d.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e258b005a8c8ec7c2000783af54f9961dd89aa54faacce6db82cbe9f491d620e._UR1920,1080_RI_.jpg", "publicReleaseDate": 1288483200000, "overallRating": 4.7, "totalReviewCount": 8936, "seasonNumber": 1, "numberOfSeasons": 11, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d0cee30c-926f-4dd7-b59c-4d6f2fbf8c69", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_20"}, "refMarker": "hm_hom_p_THPXkc_brws_2_20", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2017"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d0cee30c-926f-4dd7-b59c-4d6f2fbf8c69", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_20"}, "refMarker": "hm_hom_p_THPXkc_brws_2_20", "journeyIngressContext": "8|EgNhbGw="}], "showName": "The Walking Dead", "cardType": "TITLE_CARD"}, {"title": "Superbad Unrated", "gti": "amzn1.dv.gti.34a9f7a5-1b91-c80b-7e81-bc3745a2f989", "transformItemId": "amzn1.dv.gti.34a9f7a5-1b91-c80b-7e81-bc3745a2f989", "synopsis": "Determined to have sex before leaving for college, two teenagers agree to buy the booze for the coolest party in town, leading to a night they'll never forget.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_comedy"], "maturityRatingString": "vcc_maturity_rating_amazon_maturity_rating_18_plus", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/53d55f1ba88c23f9bdd9272a64dbaeb7ebf10c734b5737dda529c15fd41a1a0a.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/284e0de74a8443ec75e1528ac107d9cc6fc4d3c9fa877bcd7c8e194036e7d0e0._UR1920,1080_RI_.jpg", "publicReleaseDate": 1187308800000, "runtimeSeconds": 7126, "overallRating": 4.7, "totalReviewCount": 12844, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.34a9f7a5-1b91-c80b-7e81-bc3745a2f989", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_21"}, "refMarker": "hm_hom_p_THPXkc_brws_2_21", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.34a9f7a5-1b91-c80b-7e81-bc3745a2f989", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_21"}, "refMarker": "hm_hom_p_THPXkc_brws_2_21", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "The Lord of the Rings: The Fellowship of the Ring", "gti": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "transformItemId": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "synopsis": "In the first part, The Lord of the Rings, a shy young hobbit named <PERSON><PERSON><PERSON> inherits a simple gold ring that holds the secret to the survival--or enslavement--of the entire world.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_fantasy", "av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/06b3a88bd674980e8dfab3347c9d991379ba476b024a3a4a64556ebff88c8191.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/2f0e365f033851be687ca2179166fb614c2e2175d4a812b671abb533650b8639._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/fa4de82caca6c3f523ccca6da0e05d4cff6e1eaf9f21535b514015b94185c895.jpg", "publicReleaseDate": 1008720000000, "runtimeSeconds": 10282, "overallRating": 4.8, "totalReviewCount": 46728, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_22"}, "refMarker": "hm_hom_p_THPXkc_brws_2_22", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 4X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f143d0a2-ea83-4f24-93cd-e5ed0d7bc3eb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_22"}, "refMarker": "hm_hom_p_THPXkc_brws_2_22", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "<PERSON> Chosen - Season 1", "gti": "amzn1.dv.gti.2811f9dc-2de4-4a24-b7e6-b82bb22bc8af", "transformItemId": "amzn1.dv.gti.2811f9dc-2de4-4a24-b7e6-b82bb22bc8af", "synopsis": "The first multi-season series about the life of <PERSON> and the highest crowd-funded TV series or film project of all time. \"The Chosen\" portrays <PERSON> through the eyes of those who met him.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_pg", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e20ae546b714b94f55b95035d2d58ef154b583cf62218ad9bd19d8eb9ce655d3.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/25377dd08c9089b12ee44d9940c84de14e8b91ae427623950f2fd94f1768d8b3._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/ca3a8c1ffb1576f0e32fbf8989ceb56820e4045d08f3c229fa428430f1dc4b62.jpg", "publicReleaseDate": 1555804800000, "overallRating": 4.8, "totalReviewCount": 973, "seasonNumber": 1, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2811f9dc-2de4-4a24-b7e6-b82bb22bc8af", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_23"}, "refMarker": "hm_hom_p_THPXkc_brws_2_23", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2811f9dc-2de4-4a24-b7e6-b82bb22bc8af", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_23"}, "refMarker": "hm_hom_p_THPXkc_brws_2_23", "journeyIngressContext": "8|EgNhbGw="}], "showName": "The Chosen", "cardType": "TITLE_CARD"}, {"title": "Mr. Robot, Season 1", "gti": "amzn1.dv.gti.dea9f717-b7fa-5e5f-2c12-0407b281879a", "transformItemId": "amzn1.dv.gti.dea9f717-b7fa-5e5f-2c12-0407b281879a", "synopsis": "\"Mr. Robot\" follows <PERSON> (<PERSON><PERSON>), a young programmer who works as a cyber-security engineer by day and a vigilante hacker by night. <PERSON> finds himself at a crossroads when the mysterious leader (<PERSON>) of an underground hacker group recruits him to destroy the corporation he is paid to protect.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_suspense"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c4520432bda337fd59654a2cc2b1c6607ef7302bd064a530ab311c065a3b488a.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/134f649dcc4873394463c6374bb990ada39ee3e62fec51f33391b529c9665d6b._UR1920,1080_RI_.jpg", "publicReleaseDate": 1441152000000, "overallRating": 4.5, "totalReviewCount": 30046, "seasonNumber": 1, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.dea9f717-b7fa-5e5f-2c12-0407b281879a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_24"}, "refMarker": "hm_hom_p_THPXkc_brws_2_24", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X winner in 2020"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.dea9f717-b7fa-5e5f-2c12-0407b281879a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_24"}, "refMarker": "hm_hom_p_THPXkc_brws_2_24", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Mr. <PERSON>", "cardType": "TITLE_CARD"}, {"title": "Person of Interest: The Complete First Season", "gti": "amzn1.dv.gti.91a99ca2-7fea-4346-af09-118ea0d01f84", "transformItemId": "amzn1.dv.gti.91a99ca2-7fea-4346-af09-118ea0d01f84", "synopsis": "Person of Interest is a crime thriller about a presumed-dead former CIA agent, <PERSON>, who teams up with mysterious billionaire <PERSON> to prevent violent crimes.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-14", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_action", "av_genre_suspense", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_14", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/145c89ccd6e1f99b79643d53b58927d48a710c438968bf9417c8314585f96cd3.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/014ff0dcabe3bcebf50162bfe0633a2d257adbae05b45758576dcb740013b54c._UR1920,1080_RI_.jpg", "publicReleaseDate": 1337212800000, "overallRating": 4.8, "totalReviewCount": 3708, "seasonNumber": 1, "numberOfSeasons": 5, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.91a99ca2-7fea-4346-af09-118ea0d01f84", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_25"}, "refMarker": "hm_hom_p_THPXkc_brws_2_25", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.91a99ca2-7fea-4346-af09-118ea0d01f84", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_25"}, "refMarker": "hm_hom_p_THPXkc_brws_2_25", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Person of Interest", "cardType": "TITLE_CARD"}, {"title": "<PERSON>", "gti": "amzn1.dv.gti.f4a9f7ae-8751-637f-45fe-baf203e8df44", "transformItemId": "amzn1.dv.gti.f4a9f7ae-8751-637f-45fe-baf203e8df44", "synopsis": "<PERSON> gives an astonishing performance as <PERSON>, an everyman whose simple innocence comes to embody a generation.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_romance", "av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/fd12f576006faf5c150f8f71653dc6cf7fbd1c6bda7f5918065e74511a7ee068.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e538f2536d77a91ab3de55cbe28d0173820fb7badc75bf2d5c5b43dfe4283516._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7f7e6c3f894c6c3fa44229d621391551af8c762c40f663a4857516c76f5f40d0.jpg", "publicReleaseDate": 773452800000, "runtimeSeconds": 8525, "overallRating": 4.9, "totalReviewCount": 40534, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f4a9f7ae-8751-637f-45fe-baf203e8df44", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_26"}, "refMarker": "hm_hom_p_THPXkc_brws_2_26", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Paramount+, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 6X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f4a9f7ae-8751-637f-45fe-baf203e8df44", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_26"}, "refMarker": "hm_hom_p_THPXkc_brws_2_26", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "Interstellar", "gti": "amzn1.dv.gti.b4a9f7c6-5def-7e63-9aa7-df38a479333e", "transformItemId": "amzn1.dv.gti.b4a9f7c6-5def-7e63-9aa7-df38a479333e", "synopsis": "Filmmaker <PERSON> takes us on another epic science fiction journey. <PERSON> and <PERSON> join an acclaimed crew as members of an interspace exploratory team that overcome the impossible.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_science_fiction", "av_genre_adventure", "av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/991edf2ea502762dc5cd74e22bff1a4e06b9b35dc3bbf3d0c56381ece888ecb8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/14db27b1570a8703e3975fddaa6b624bb2ca77ac7dbff925802102995b46420d._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/aa19dde8902f28a74a61f6e00a6272ad45161cb32b26a89b648cf7deeb0d3ad2.jpg", "publicReleaseDate": 1415318400000, "runtimeSeconds": 9731, "overallRating": 4.7, "totalReviewCount": 101333, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b4a9f7c6-5def-7e63-9aa7-df38a479333e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_27"}, "refMarker": "hm_hom_p_THPXkc_brws_2_27", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCAR® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b4a9f7c6-5def-7e63-9aa7-df38a479333e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_27"}, "refMarker": "hm_hom_p_THPXkc_brws_2_27", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "The Revenant (4K UHD)", "gti": "amzn1.dv.gti.44a9f736-a18f-83c8-38ed-16842539359b", "transformItemId": "amzn1.dv.gti.44a9f736-a18f-83c8-38ed-16842539359b", "synopsis": "<PERSON> and <PERSON> star in <PERSON>'s visceral, epic tale of survival and betrayal inspired by true events that's set in America's uncharted wilderness.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_adventure", "av_genre_action", "av_genre_drama", "av_genre_western"], "maturityRatingString": "vcc_maturity_rating_mpaa_r", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3c5ea197f5c4f5d470b15e183d5b90192611e46c3ab7f20ff29d0c815a9688e5.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/19033a9fd55c98e9d164dff719e9852e85e5c789ea1afe3ad68953dceb4fabb6._UR1920,1080_RI_.jpg", "publicReleaseDate": 1452211200000, "runtimeSeconds": 9380, "overallRating": 4.7, "totalReviewCount": 41853, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.44a9f736-a18f-83c8-38ed-16842539359b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_28"}, "refMarker": "hm_hom_p_THPXkc_brws_2_28", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 3X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.44a9f736-a18f-83c8-38ed-16842539359b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_28"}, "refMarker": "hm_hom_p_THPXkc_brws_2_28", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "Back to the Future", "gti": "amzn1.dv.gti.30a9f73b-becf-e11e-b945-01fc05bd6430", "transformItemId": "amzn1.dv.gti.30a9f73b-becf-e11e-b945-01fc05bd6430", "synopsis": "AMC Presents: <PERSON> (<PERSON>) is blasted to 1955 in the time machine created by <PERSON> (<PERSON>) and finds himself in a time-shattering situation that jeopardises his future.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_science_fiction", "av_genre_comedy", "av_genre_adventure"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cd77889f6d1ba537c5f10383b51f35a655feb02b6b78920fddad220655aa1035.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f3ca80eae991ab5706d9e314eba0edf651d8e75b4dfcf5be8fb74c78886ebe59._UR1920,1080_RI_.jpg", "publicReleaseDate": 489196800000, "runtimeSeconds": 6961, "overallRating": 4.8, "totalReviewCount": 19284, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.30a9f73b-becf-e11e-b945-01fc05bd6430", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_29"}, "refMarker": "hm_hom_p_THPXkc_brws_2_29", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCAR® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.30a9f73b-becf-e11e-b945-01fc05bd6430", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_29"}, "refMarker": "hm_hom_p_THPXkc_brws_2_29", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "The Tomorrow War", "gti": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "transformItemId": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "synopsis": "Time travelers arrive from 2051 to deliver an urgent message: 30 years in the future mankind is losing a war against a deadly alien species. The only hope for survival is for soldiers and civilians to be transported to the future and join the fight. Determined to save the world for his daughter, <PERSON> teams up with a brilliant scientist and his estranged father to rewrite the planet’s fate.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_adventure", "av_genre_suspense", "av_genre_drama", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/06e43e32087be701c6200cfc7ef1d49958b285a0deba4ed53ab9d2e16acd88c7.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c8b0605fdf14a7586bfeedc7894becf0eef1a907d093b6f88217a6c77d09d5ad._UR1920,1080_RI_.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/fb462eee520d05caef9572ed6d492d3a8e521e49255647a0aa85803650df4757.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e082ed51603463d5b5ed3875243a5fac266e2d9995bf982fff57165ec77c1af1.png", "publicReleaseDate": 1625184000000, "runtimeSeconds": 8286, "overallRating": 4.1, "totalReviewCount": 33188, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_30"}, "refMarker": "hm_hom_p_THPXkc_brws_2_30", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.66e03765-50f3-4b7e-89ed-c31e9aea7ee0", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_30"}, "refMarker": "hm_hom_p_THPXkc_brws_2_30", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}], "tags": [], "analytics": {"refMarker": "hm_hom_p_THPXkc_2", "ClientSideMetrics": "472|CmIKNE9uYm9hcmRpbmdUaXRsZXNTcGVlZEJ1bXBBbHRlcm5hdGVMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTEwTTJTQjlIMDgwVDcaEDI6RFk5QjZBRTkwMUM1RDkiBlRIUFhrYxJECgRob21lEgRob21lIg5wcmlvcml0eUNlbnRlcioAMiRmZTUyNzgxZi0wOGFmLTRkYTQtODZhYi0zYzUxNmZkYTQwNmUaA2FsbCIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQg5Ccm93c2VTdHJhdGVneUoaT25ib2FyZGluZ1ZhcmlhbnRTcGVlZEJ1bXBSC25vdEVudGl0bGVkWgBiCk9uYm9hcmRpbmdoAnIAejhzc2FyODVzNVJhZUVSdEhsbjR5bFlWS0M4TE50U1dwTnF3TDJfVGpKcmFJX24xUkpRVGtZVXc9PYIBA2FsbIoBAJIBAA=="}, "offerType": "Mixed", "entitlement": "Mixed", "journeyIngressContext": "8|EgNhbGw=", "type": "ONBOARDING"}, {"button": {"target": "Landing", "pageId": "home", "pageType": "home", "analytics": {}, "refMarker": "null", "text": "Done"}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6zioRob21li4Rob21ljI6eMToxOVI1RFRMU1lHVEZOIyNNTlFYRTMzVk9OU1dZjQ-OglYy", "items": [], "tags": [], "analytics": {"refMarker": "hm_hom_p_K7wFJE_3", "ClientSideMetrics": "512|Cn4KUU9uYm9hcmRpbmdBY3Rpb25CdXR0b25Db2xsZWN0aW9uU3RhdGljTm9JdGVtc1NwZWVkQnVtcEFsdGVybmF0ZUxpdmVEZWZhdWx0RGVmYXVsdBIPMToxOVI1RFRMU1lHVEZOGhAyOkRZODlGMDFCRUE1MkE4IgZLN3dGSkUSRAoEaG9tZRIEaG9tZSIOcHJpb3JpdHlDZW50ZXIqADIkZmU1Mjc4MWYtMDhhZi00ZGE0LTg2YWItM2M1MTZmZGE0MDZlGgNhbGwiA2FsbCoAMghjYXJvdXNlbDoGU3RhdGljQg1TdGF0aWNOb0l0ZW1zShpPbmJvYXJkaW5nVmFyaWFudFNwZWVkQnVtcFILbm90RW50aXRsZWRaAGIWQWN0aW9uQnV0dG9uQ29sbGVjdGlvbmgDcgB6OHNzYXI4NXM1UmFlRVJ0SGxuNHlsWVZLQzhMTnRTV3BOcXdMMl9UakpyYUlfbjFSSlFUa1lVdz09ggEDYWxsigEAkgEA"}, "journeyIngressContext": "8|EgNhbGw=", "type": "ACTION_BUTTON_COLLECTION"}], "subNav": [], "pageMetadata": {"title": "", "logoImage": {}, "locationDependentPage": false, "showVoiceFilters": false, "persistentTitleOrLogo": false}, "isPriorityCenter": true}, "metadata": {"requestId": "ssar85s5RaeERtHln4ylYVKC8LNtSWpNqwL2_TjJraI_n1RJQTkYUw==", "requestedTransformId": "lr/collections/collectionsPageInitial", "domain": "prod", "realm": "us-east-1", "timestamp": "2024-07-13T13:07:42.262545Z"}}