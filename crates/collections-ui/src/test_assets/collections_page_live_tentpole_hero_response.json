{"resource": {"containerList": [{"id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Lio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOpjE6MTNCWlpUSkxZSEtWTkMjI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy", "offerType": "Mixed", "entitlement": "Mixed", "item": {"title": "TNF E2E Test Page #2", "synopsis": "TNF E2E Test Page #2", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.709c6d10-9c7b-48ea-afe9-e85a634ea9d5", "transformItemId": "amzn1.dv.gti.709c6d10-9c7b-48ea-afe9-e85a634ea9d5", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/TentpoleHero_Image_Test/43172f9a-43f0-4945-85fe-70105d1210da.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_SVOD_NWSL_Game4_HoustonDashVsWashingtonSpirit/e64e0d23-f2e1-4441-b2c1-19143be2ee2c.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-color._CB554929912_.png", "providerLogoImageMetadata": {"height": 1183, "width": 2000, "scalarHorizontal": "emphasis"}, "action": {"target": "detail", "pageId": "amzn1.dv.gti.709c6d10-9c7b-48ea-afe9-e85a634ea9d5", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_YBcsjB_3ajAKr_1_1", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_YBcsjB_3ajAKr_1_1", "journeyIngressContext": "8|EgNhbGw="}, "actions": [{"refMarker": "atv_dp_hom_c_prime_hd_live_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_hom_c_prime_hd_live_play_t1ACAAAAAA0lr0", "playbackExperienceMetadata": {"playbackEnvelope": "eyJ0eXAiOiJwbGVuditqd2UiLCJjdHkiOiJwbGVuditqd3MiLCJhbGciOiJBMjU2S1ciLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiYTJ6K3BwX2VuYytzX3BuVmxvZHcifQ.-thAUW5ZG1yT2pww-14VeZ_dzTOqQwUlLG0-3DqbEgtiOrBJG6HvZUpNym_HbmqKB-PFHVjCuRNpsqL1sb2dGuuxaEQBD_m6.Xoz2VtPs5Gbn7kWfMs4eEQ.6pe0DDuZdq6hO_WohPdbHQb7M-WQkm0WbQaid8LVrYw3GgaER7GUPE6yhXUVpSHBYqB8pcuxyFze9gvOnBNfFSKwPIcuBNTzYH-kMqLUQvEKk6XfBwz5Wza_df87-c6hRbXvmpln1oDEQfBM5C4YCS5i9QG_cfcpYtx-OtDC8nLSkIfVjKSPaYyHHo6Ljikh5NojYNAMxWPGcxWr4uq106Wxi9IFDWyiIZpsIrozt_ba9j8f-gpH046M8JqA45FU3EPKvEMqi9SPMqM6tZ2EzKHMFQPvInuRy4zqjMa6BD5c2XIDMgB353CDJuEn_wKsQ6SPzuBaU0fMccGICvS5YJdI-WluvBHv3JHkjB798faezcdpEJyEfobxMWYKLyYUXNVHfk3pwHPX41JUTDQDTibgYc-S1p9UQqQ7EhemF2DljpWlBpAp9_KoTDr3K-xmRsL68rn5wy7fnbRFdbBBuDyUb0DnH6WVjFR5tdetRw07QHywkuQpmGdiVCkGKGD_U8IRlLzMTBFwoeVcfavkbvrtmaBN8IXPLtMfziNHiiGOExmaZ65u-BGkjh43trXxIlFAyMEypPDijJGxsT8ZlGL5p9R4k1gHmebHuY7o14nPfe66RpIuIdRaD-7OwYDlk0UlxqE0qcd29OYtc16WRNmUjVXCZ1q6iO8PIdgeKR40SwEDYcjFVYGCXpSe6y3gmlHTilnlu3902yZFNAMS5KVZfkcI8T0B0QlSLvZW59mO8kbnH0BD5rrGjb6cP3VXse1jD70doUIGPTYP_QOWAwTxazGkQW5WrCtcdbHaiVKs806lrJzLYHpRdnSr_RfnBW2trNhWHbaaRucT-uYnWIza4nca7EB5f-w3Gj9nlVlC8gmOjfsRH48yfIGXNlz6ZfT5Ne2GSrLY2dVAf9MkvMlUZbY1Owv_0Hx4XcO06rOv95nDz8RHAr0vfCqtbQH3T96IT5iVpBG9nxkhwWtMIK6K1T-jnIXwjQ7MWZDSyqdUn-oxdslr1HUCEKWhZ5g6Xybp_cBQk9SOpc86LqHqHWS2ZmfwLJIx3ROOAFGOaWfnaFTATdTRdXy9fLaNlMO45Y0qvGYdTdPhiPN_o_S6hkAcOKncUKQtiKFui8TyeEMnaRZNh9_hdMe8NPZRMiu3vbG6ab6ZqmRVN3G7WiAI4F2SScTd0q7dP4NEDEDDaaoNcQxDomNO0-dkOGnehEhxa7pq3EiC2AyVO5Ni1caVagS0xmV9RSkrGGG3i6x9VIgmpqIt1exC8pmMjz2Ikm3FqjZA1BNrKulepeGMRRehXR66Ogssu7Vwigre0DtnpFtqZbTDRTvlSp68fikVjvHY3IOOFlunFbaUBocg8gY58Gk1cTtOERgDF-pbUE3Q9MZX-OeFBn6jb9gGEMeOT2KrXczfN_i05687bbbednkbDEbOQ3zmIj3EKJlD38yh8hhS-x0xgs_HPaLh1fdBfACY986c5GFn0KFXJA9O1AVjbj5qA4-5OOeKMi_QDwcq6Nt1WhpA0avhe0inP3YTzznuhKZvXXPTDJYL236dds8wgumm7dNRiHYxIR-kKXiqdLbbLasRxVwBR2nlY4xkIi1rkfpkSI1f2iCDMc2GL5U_6uFymdDr73a6A2YM-wGOlXltAhXKnVcQABmZvY_FNjKFOb4PWHHT1H7iNmeHQimjv3W8_N8DMryH6ZUkICYObW3nwmGmbKHxg9bODohlvpXINvZ_Ftg7sdV0Ha4SO1e8cMvq-1sSwDhkGwSbk3lI_1cOY2jQiDeCO_Qh4LeSqne7blKrJi_mz1x8PqOyVWWNPn-gC6jpa7j8rUVzd2HhBr-PzsUsbX8XOQg2NXriGYX4NffJSWAAK_AZpEffhGA0GJnPN4PezFc0FiRRPOwt8kO8kglvoXh1I18lf-wx-OMurjzGZW-bJgpKql5WEnydxAEmL-NuWUDMLblu0k8eMMArMm_BhKw4PIRHtzDcU7mHFPTgy887PQVjzEGCGIqXtwXlYatoTJeHhVS37_6R4bVNKP-WkAlaWcXIzMd2IpT6FQyr3fwSeIWoNBiXxzx4ItiNzMdAkD5Q1vy3kKHS_5dmFAPzRW7h7o0c9mVyQ8Yt7osoNXuN6CmpC1Zx1yvfqJhRZ3jFTC5d4YhaMDJT7S2E6Io4WdJzsUudbnXbJHkeVNpVyR2xTeR-_IU1ng4Dcg7brXtvADd00_tdrdDG9g91JpXv5b5_OZgp7_89rmTOZCxYNbu2R3Bmrj1UC5ndpqw9cjnAEirrN6OuSOvcJkh7VQsy9tJ8_sWjtWBhzhXENDfCVpmPMJD3Mq_p2Z0oCHuefODbEzEvzI97kPn3Wvo.qYpMPGoJUMiJiUwyTteql7EVD9nJ-dubVXXTKQu_9-0", "expiryTime": 1724067683614, "correlationId": "YW16bjEuZHYuZ3RpLmEyOTEwMzcyLWYyMTgtNGY5NS1iNDYxLTIxNTcxMDA1YjdlMDpTb3VyY2UodHlwZT1CZW5lZml0LCBpZD1QcmltZSwgb3JkZXI9T3JkZXIoaWQ9MiksIHNoYXJlZEJ5PW51bGwpOmFtem4xLmR2LnB2aWQuZWI2NTFmZWUtYzIwYy00ZmE2LTg1N2UtYzMzYzBhY2UxOTNjOkhE"}, "position": "LIVE_STREAM_WATCH_NOW", "startPositionEpochUtc": 0, "userPlaybackMetadata": {"runtimeSeconds": 226576, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false, "linearStartTime": 0, "linearEndTime": 0}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "LiveStreaming", "playbackTitle": "amzn1.dv.gti.a2910372-f218-4f95-b461-21571005b7e0", "metadataActionType": "Playback"}, "label": "Watch Live{lineBreak}Main Broadcast (Feed 1)"}, {"target": "detail", "pageId": "amzn1.dv.gti.709c6d10-9c7b-48ea-afe9-e85a634ea9d5", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_YBcsjB_3ajAKr_1_1", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_YBcsjB_3ajAKr_1_1", "journeyIngressContext": "8|EgNhbGw="}], "isInWatchlist": false, "moreDetailsAction": {"target": "detail", "pageId": "amzn1.dv.gti.709c6d10-9c7b-48ea-afe9-e85a634ea9d5", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_YBcsjB_3ajAKr_1_1", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_YBcsjB_3ajAKr_1_1", "journeyIngressContext": "8|EgNhbGw="}, "liveEventDateBadge": {"text": "Sat, Aug 17 11:45 AM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 17, 2024", "time": "11:45 AM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON", "type": "MESSAGE", "messages": ["Included with Prime"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "7+", "tournamentIcid": "amzn1.dv.icid.c3f5032e-1463-421e-8cb5-de1846ffeb97", "tnfProperty": {"customerIntent": "UNSET"}, "contentType": "EVENT", "backgroundImageUrl": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.709c6d10-9c7b-48ea-afe9-e85a634ea9d5/570/HERO-16X9/en-US.png", "cardType": "HERO_CARD"}, "analytics": {"refMarker": "me_pv-_c_YBcsjB_1", "ClientSideMetrics": "428|ClwKLlRlc3RDaGFubmVsRXZlbnRUZW50cG9sZUhlcm9MaXZlRGVmYXVsdERlZmF1bHQSEDE6MTNCWlpUSkxZSEtWTkMaEDI6RFk0NTUyNTE4N0QxQzciBllCY3NqQhJLCgVtZXJjaBIScHYtd2ViLWxpdmUtZXZlbnRzIgZjZW50ZXIqADIkNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkGgNhbGwiA2FsbCoAMgxoZXJvQ2Fyb3VzZWw6BkZhcm1lckIJU3VwZXJIZXJvUgtub3RFbnRpdGxlZFoAYgxUZW50cG9sZUhlcm9oAXIAejgtMVVsbFhnTXF6dW44ejdUcFl1UXRfNzExamFGVGJpcmJ4ZjJUbTJXa2Z1a19jaTNhQXk2b2c9PYIBA2FsbIoBAJIBAA=="}, "tags": [], "journeyIngressContext": "8|EgNhbGw=", "type": "TENTPOLE_HERO"}, {"facet": {}, "title": "Paramount+: Live and upcoming events", "titleImageUrl": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/blast_carousel-logo_selected_rar._CB582721982_.png", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6Ii0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09OjE3MjQwNjY3ODIwMDAiLCJhcE1heCI6MzMsInN0cmlkIjoiMToxMkI0TU1MUlMwM1ZRUSMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNIiwib3JlcWsiOiJkMTlGWnNMT3hZNDh5cHlZREJhOExCQ1RyZlZESXR4WWVFOElSeDc0YmVRPSIsIm9yZXFrdiI6MSwiZXhjbFQiOltdfQ==", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Pio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqjE6MTJCNE1NTFJTMDNWUVEjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "pageId": "pv-web-live-events", "pageType": "merch", "pageContext": {"pageType": "merch", "pageId": "pv-web-live-events"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Pio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqjE6MTJCNE1NTFJTMDNWUVEjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "Subscription", "entitlement": "NotEntitled", "items": [{"title": "Super Bowl LVIII: Buffalo Bills vs. Kansas City Chiefs [BETA]", "gti": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "transformItemId": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "synopsis": "Watch Super Bowl Sunday live as the Bills face the Chiefs at Allegiant Stadium in Las Vegas. [BETA]", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "7+", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7/42/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7/42/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7/42/BOXART-16X9/en-US.png", "venue": "Test", "liveEventDateBadge": {"text": "Sun, Jan 21", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Jan 21, 2024", "time": "6:30 PM EST", "type": "LOCALIZED_HEADER"}, "liveliness": "ENDED", "startTime": 1705879800000, "endTime": 1705921200000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_1"}, "refMarker": "me_pv-_c_XgLdKK_2_1", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe on Prime Video Channels", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "ENDED", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.4d338999-b7a3-4d27-8611-4b37151b4acc", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_1"}, "refMarker": "me_pv-_c_XgLdKK_2_1", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Tentpole/Autoplay test - SBLVIII", "gti": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "transformItemId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "synopsis": "Super Bowl LVIII", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f/4/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f/4/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f/4/BOXART-16X9/en-US.png", "venue": "Test", "liveEventDateBadge": {"text": "<PERSON><PERSON>, Dec 5 2:25 PM EST", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Dec 5, 2023", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "startTime": 1701804300000, "endTime": 1705438800000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_3"}, "refMarker": "me_pv-_c_XgLdKK_2_3", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe on Prime Video Channels", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.ca51a1f1-faf8-4feb-bb41-a736eaa2bf03", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_3"}, "refMarker": "me_pv-_c_XgLdKK_2_3", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Lecce vs. Atalanta", "gti": "amzn1.dv.gti.c6e87b50-bb78-4f66-a6b1-54dedd171d4c", "transformItemId": "amzn1.dv.gti.c6e87b50-bb78-4f66-a6b1-54dedd171d4c", "synopsis": "Lecce shored up their squad with a few cunning additions this summer. Instead, Atalanta are behind the eight-ball due to a pair of sagas and injuries.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.c6e87b50-bb78-4f66-a6b1-54dedd171d4c/3/HERO-16X9/en-US.jpeg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.c6e87b50-bb78-4f66-a6b1-54dedd171d4c/3/BOXART-4X3/en-US.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.c6e87b50-bb78-4f66-a6b1-54dedd171d4c/3/BOXART-16X9/en-US.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "venue": "Stadio Ettore Giardiniero - Via del Mare", "liveEventDateBadge": {"text": "Live at 12:15 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 19, 2024", "time": "12:15 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724084100000, "endTime": 1724099700000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.c6e87b50-bb78-4f66-a6b1-54dedd171d4c", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_4"}, "refMarker": "me_pv-_c_XgLdKK_2_4", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.a0a60781-71ab-4319-b5b7-d09bca600a24", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.c6e87b50-bb78-4f66-a6b1-54dedd171d4c", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_4"}, "refMarker": "me_pv-_c_XgLdKK_2_4", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Juventus vs. Como", "gti": "amzn1.dv.gti.6401d616-d87c-47e2-a17e-5076b920aecb", "transformItemId": "amzn1.dv.gti.6401d616-d87c-47e2-a17e-5076b920aecb", "synopsis": "Juventus didn’t perform well in the pre-season, and their XI is still missing a few pieces. Como successfully went big-name hunting after the promotion.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.6401d616-d87c-47e2-a17e-5076b920aecb/3/HERO-16X9/en-US.jpeg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.6401d616-d87c-47e2-a17e-5076b920aecb/3/BOXART-4X3/en-US.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.6401d616-d87c-47e2-a17e-5076b920aecb/3/BOXART-16X9/en-US.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "venue": "Allianz Stadium", "liveEventDateBadge": {"text": "Live at 2:30 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 19, 2024", "time": "2:30 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724092200000, "endTime": 1724107800000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.6401d616-d87c-47e2-a17e-5076b920aecb", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_5"}, "refMarker": "me_pv-_c_XgLdKK_2_5", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.a0a60781-71ab-4319-b5b7-d09bca600a24", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.6401d616-d87c-47e2-a17e-5076b920aecb", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_5"}, "refMarker": "me_pv-_c_XgLdKK_2_5", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Defensa y Justicia vs. Banfield", "gti": "amzn1.dv.gti.1ff05a3c-51d8-4d97-a2af-d80a143577e8", "transformItemId": "amzn1.dv.gti.1ff05a3c-51d8-4d97-a2af-d80a143577e8", "synopsis": "Defensa y Justicia remain near the bottom of the table with zero wins and on a three-game losing skid. Banfield will aim to take advantage of that.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1ff05a3c-51d8-4d97-a2af-d80a143577e8/2/HERO-16X9/en-US.jpeg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1ff05a3c-51d8-4d97-a2af-d80a143577e8/2/BOXART-4X3/en-US.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1ff05a3c-51d8-4d97-a2af-d80a143577e8/2/BOXART-16X9/en-US.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "venue": "Estadio <PERSON><PERSON>", "liveEventDateBadge": {"text": "Live at 7:45 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 19, 2024", "time": "7:45 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724111100000, "endTime": 1724126700000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.1ff05a3c-51d8-4d97-a2af-d80a143577e8", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_6"}, "refMarker": "me_pv-_c_XgLdKK_2_6", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.e391d1a3-448d-4f75-94d5-3fa98a14ff19", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.1ff05a3c-51d8-4d97-a2af-d80a143577e8", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_6"}, "refMarker": "me_pv-_c_XgLdKK_2_6", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Lille vs. Slavia Praha", "gti": "amzn1.dv.gti.0ecf969b-0337-4afd-8a45-07dcdd8b712d", "transformItemId": "amzn1.dv.gti.0ecf969b-0337-4afd-8a45-07dcdd8b712d", "synopsis": "Lille and Slavia Praha both made it to the play-off round in the League Path and now have two legs to earn a spot in the group stage.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.0ecf969b-0337-4afd-8a45-07dcdd8b712d/1/HERO-16X9/en-US.jpeg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.0ecf969b-0337-4afd-8a45-07dcdd8b712d/1/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.0ecf969b-0337-4afd-8a45-07dcdd8b712d/1/BOXART-16X9/en-US.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "venue": "Stade du Hainaut", "liveEventDateBadge": {"text": "Tomorrow 2:50 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "2:50 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724179800000, "endTime": 1724193000000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.0ecf969b-0337-4afd-8a45-07dcdd8b712d", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_7"}, "refMarker": "me_pv-_c_XgLdKK_2_7", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.545232ed-0823-49fd-b889-e42b835b78e9", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.0ecf969b-0337-4afd-8a45-07dcdd8b712d", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_7"}, "refMarker": "me_pv-_c_XgLdKK_2_7", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Dinamo Zagreb vs. Qarabağ", "gti": "amzn1.dv.gti.1c878bd2-dbd5-498b-92e0-8e9ba6c16c3f", "transformItemId": "amzn1.dv.gti.1c878bd2-dbd5-498b-92e0-8e9ba6c16c3f", "synopsis": "Qarabağ take on Dinamo Zagreb in the first leg of the Champions League play-off round, with both clubs eyeing a spot in the group stage.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1c878bd2-dbd5-498b-92e0-8e9ba6c16c3f/1/HERO-16X9/en-US.jpeg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1c878bd2-dbd5-498b-92e0-8e9ba6c16c3f/1/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1c878bd2-dbd5-498b-92e0-8e9ba6c16c3f/1/BOXART-16X9/en-US.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "venue": "Stadion Maksimir", "liveEventDateBadge": {"text": "Tomorrow 2:50 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "2:50 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724179800000, "endTime": 1724193000000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.1c878bd2-dbd5-498b-92e0-8e9ba6c16c3f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_8"}, "refMarker": "me_pv-_c_XgLdKK_2_8", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.545232ed-0823-49fd-b889-e42b835b78e9", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.1c878bd2-dbd5-498b-92e0-8e9ba6c16c3f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_8"}, "refMarker": "me_pv-_c_XgLdKK_2_8", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Bodø/<PERSON>lim<PERSON> vs. Crvena zvezda", "gti": "amzn1.dv.gti.e4f346e1-6f86-42ab-b8a9-bf93ae138af0", "transformItemId": "amzn1.dv.gti.e4f346e1-6f86-42ab-b8a9-bf93ae138af0", "synopsis": "Bodø/Glimt face off against Crvena zvezda as both clubs look for a group stage spot after making it through the Champions Path.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.e4f346e1-6f86-42ab-b8a9-bf93ae138af0/1/HERO-16X9/en-US.jpeg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.e4f346e1-6f86-42ab-b8a9-bf93ae138af0/1/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.e4f346e1-6f86-42ab-b8a9-bf93ae138af0/1/BOXART-16X9/en-US.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "venue": "Aspmyra Stadion", "liveEventDateBadge": {"text": "Tomorrow 2:50 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "2:50 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724179800000, "endTime": 1724193000000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.e4f346e1-6f86-42ab-b8a9-bf93ae138af0", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_9"}, "refMarker": "me_pv-_c_XgLdKK_2_9", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.545232ed-0823-49fd-b889-e42b835b78e9", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.e4f346e1-6f86-42ab-b8a9-bf93ae138af0", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_9"}, "refMarker": "me_pv-_c_XgLdKK_2_9", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Young Boys vs. Galatasaray", "gti": "amzn1.dv.gti.481db1c2-836d-4bc4-8791-fc0de296f133", "transformItemId": "amzn1.dv.gti.481db1c2-836d-4bc4-8791-fc0de296f133", "synopsis": "Galatasaray travel to Bern to take on Young Boys, who are looking to book their spot in the Champions League group stage once more.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.481db1c2-836d-4bc4-8791-fc0de296f133/1/HERO-16X9/en-US.jpeg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.481db1c2-836d-4bc4-8791-fc0de296f133/1/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.481db1c2-836d-4bc4-8791-fc0de296f133/1/BOXART-16X9/en-US.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "liveEventDateBadge": {"text": "Wed, Aug 21 2:50 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 21, 2024", "time": "2:50 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724266200000, "endTime": 1724279400000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.481db1c2-836d-4bc4-8791-fc0de296f133", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_10"}, "refMarker": "me_pv-_c_XgLdKK_2_10", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.481db1c2-836d-4bc4-8791-fc0de296f133", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XgLdKK_2_10"}, "refMarker": "me_pv-_c_XgLdKK_2_10", "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "me_pv-_c_XgLdKK_2", "ClientSideMetrics": "600|ClYKKFBWV2ViTGl2ZVBhcmFtb3VudHBsdXNMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTJCNE1NTFJTMDNWUVEaEDE6MTJCNE1NTFJTMDNWUVEiBlhnTGRLSxJLCgVtZXJjaBIScHYtd2ViLWxpdmUtZXZlbnRzIgZjZW50ZXIqADIkNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkGgxzdWJzY3JpcHRpb24iACoHY2JzYWFjZjIPZmFjZXRlZENhcm91c2VsOhlMaXZlRXZlbnRzQ29udGVudFByb3ZpZGVyQhpMaXZlRXZlbnRzQnJvd3NlU3RyYXRlZ3lWMkoWY29sbGVjdGlvblBhZ2VDYXJvdXNlbEoUc2hvd1VuZGVyRXZlcnlGaWx0ZXJKEGhvbWVQYWdlQ2Fyb3VzZWxKD2xpdmVBbmRVcGNvbWluZ1IIZW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgCcgB6OC0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09ggEFZmFsc2WKAQCSAQA="}, "tags": ["collectionPageCarousel", "homePageCarousel"], "journeyIngressContext": "32|CgdjYnNhYWNmEgxzdWJzY3JpcHRpb24=", "type": "STANDARD_CAROUSEL"}, {"title": "Upcoming sports", "actions": [], "facet": {}, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6Ii0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09OjE3MjQwNjY3ODIwMDAiLCJhcE1heCI6NDMyLCJzdHJpZCI6IjE6MTJHVzlOVkM3WTdDTVgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsInN0S2V5Ijoie1wic2JzaW5cIjowLFwiY3Vyc2l6ZVwiOjQzMixcInByZXNpemVcIjowfSIsIm9yZXFrIjoiZDE5RlpzTE94WTQ4eXB5WURCYThMQkNUcmZWREl0eFllRThJUng3NGJlUT0iLCJvcmVxa3YiOjEsImV4Y2xUIjpbXX0=", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Pio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqjE6MTJHVzlOVkM3WTdDTVgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "pageId": "pv-web-live-events", "pageType": "merch", "pageContext": {"pageType": "merch", "pageId": "pv-web-live-events"}}, "items": [{"title": "PPV Test Title - Live ", "gti": "amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9", "transformItemId": "amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9", "synopsis": "Tampa Bay Rays LIVE with MLB.TV.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9/14/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9/14/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9/14/BOXART-16X9/en-US.png", "poster2x3Image": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9/14/POSTER-2X3/en-US.png", "venue": "St. Petersburg, USA", "liveEventDateBadge": {"text": "Thu, Feb 29 2:00 PM EST", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Feb 29, 2024", "time": "2:00 PM EST", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "startTime": 1709233200000, "endTime": 1709266200000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_1"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_1", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Pay-Per-View", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available for Pay-Per-View", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.d4e491e9-f8b4-483e-a89d-8bdb2267d014", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.9e1b26d6-3be9-461a-bd8a-6fafc50892b9", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_1"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_1", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Civil War", "gti": "amzn1.dv.gti.bfb2c3e6-ae5e-4a7c-97c4-fd5e65de5d90", "transformItemId": "amzn1.dv.gti.bfb2c3e6-ae5e-4a7c-97c4-fd5e65de5d90", "synopsis": "<PERSON><PERSON> and <PERSON><PERSON><PERSON> lead an ensemble cast in a high-stakes thriller set in a near-future fractured America on the brink of collapse.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Suspense"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c851051c15d203552c4fe2c3f4406445fc9bcbe36506ef820640875189c0ec2b.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4c323f4d28b96b83c1b72e1202d0bfe5ed44018f325a17417ecc198474724443.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/dab0a048513b4345648640d5d824ce8f66026d8cb604b16c2f634be1802a1bb6.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/b4c289b8c761192d5fbe913c06995bf121ba428d939cc5d885be7eed99bd0b9f.jpg", "publicReleaseDate": 1712880000000, "runtimeSeconds": 6525, "runtime": "108 min", "overallRating": 3.3, "totalReviewCount": 6703, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.bfb2c3e6-ae5e-4a7c-97c4-fd5e65de5d90", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_2"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_2", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.bfb2c3e6-ae5e-4a7c-97c4-fd5e65de5d90", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_2"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_2", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "House of the Dragon - Season 2", "gti": "amzn1.dv.gti.62abb030-dd30-43ef-8547-23bb3514f3b5", "transformItemId": "amzn1.dv.gti.62abb030-dd30-43ef-8547-23bb3514f3b5", "synopsis": "Delve into the fiery history of House Targaryen, 200 years before Game of Thrones, in this series adapted from <PERSON><PERSON>'s Fire & Blood.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Science Fiction", "Fantasy", "Drama"], "maturityRatingString": "18+", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c721fcded1dad89537ddc5ab7f7e62fbba0e84ff299600bc219beaa62c7fc75f.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/25b287a47988ebd918d8d41eba52e056bfa4f9198b921b70ed477bf44871f361.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5b2d28b09c4b4cc8ef0270c2b63fe58f00eecd4aec166715582fe955f1beb2f6.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/g-l/hbomaxus/logos/channels-logo-white._CB583144831_.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/1f42ff38f7483d1c32bb8c6169e2f02905f35d1d221d31b11311d35dc0cf3556.jpg", "publicReleaseDate": 1722729600000, "overallRating": 4, "totalReviewCount": 101, "seasonNumber": 2, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.62abb030-dd30-43ef-8547-23bb3514f3b5", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_3"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_3", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.62abb030-dd30-43ef-8547-23bb3514f3b5", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_3"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_3", "journeyIngressContext": "8|EgR0dm9k"}], "showName": "House of the Dragon", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 547, "width": 1999, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "A Quiet Place: Day One - Bonus X-Ray Edition", "gti": "amzn1.dv.gti.b979b010-8275-44d8-988e-2826d1046b12", "transformItemId": "amzn1.dv.gti.b979b010-8275-44d8-988e-2826d1046b12", "synopsis": "When creatures that hunt by sound attack NYC, a woman must embark on a perilous journey with her cat and an ally to stay quiet and survive.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Horror", "Science Fiction", "Suspense"], "maturityRatingString": "PG-13", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f688f17e4b7a52e13cf2464e1e432cc51d03a503668850b29451bc4a848c7f11.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/d6e03d274da2f19f01c57589fa7384619824e4d056431e1589f35f46c2ddb7ba.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/c0be12da507684c1d34e9e04fb6d0ee090b268152f4243a8d5aec0ef4cf49bef.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/85e3ef9e80d05f920587b18d697c2e63d4618c8387f44d8d6802c15d92266c04.jpg", "publicReleaseDate": 1719532800000, "runtimeSeconds": 6109, "runtime": "101 min", "overallRating": 4.6, "totalReviewCount": 61708, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b979b010-8275-44d8-988e-2826d1046b12", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_4"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_4", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b979b010-8275-44d8-988e-2826d1046b12", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_4"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_4", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "The Fall Guy (2024)", "gti": "amzn1.dv.gti.a66c1ff4-34df-4952-87c9-386cd76ec104", "transformItemId": "amzn1.dv.gti.a66c1ff4-34df-4952-87c9-386cd76ec104", "synopsis": "In a high-stakes adventure, a stuntman embarks on a quest to unravel a conspiracy, rescue a missing celebrity, and rekindle a lost romance.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Comedy", "Drama", "Suspense"], "maturityRatingString": "PG-13", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/0716b53dc0c303cf48ccc2064edc1b5f7bad46f46c0940bc7fea97486526db52.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bca3bfd37ab0d745a9ecf80a66e37ec14f410556f8dff3d6cef8801f7353941b.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/2011690030e99113fa68aac13761e123c969718438e61f1db89bc4f92cdb1711.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/e2ffdb2ae31a9ee5553d93c78d1a74f1112fa996b1d6bdd1631e7bf406f5c027.jpg", "publicReleaseDate": 1714694400000, "runtimeSeconds": 7712, "runtime": "128 min", "overallRating": 4.3, "totalReviewCount": 4430, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a66c1ff4-34df-4952-87c9-386cd76ec104", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_5"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_5", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a66c1ff4-34df-4952-87c9-386cd76ec104", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_5"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_5", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Bad Boys: Ride or Die - Bonus X-Ray Edition", "gti": "amzn1.dv.gti.699ad055-17a6-4491-9ac0-e2502e8bab82", "transformItemId": "amzn1.dv.gti.699ad055-17a6-4491-9ac0-e2502e8bab82", "synopsis": "The Bad Boys detectives are on the run in Miami while trying to solve a case with their usual mix of action and comedy.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Action"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f669ed06157da0daeaf7e543fe55b8d9ec95b701ed64abb47d41503f4bc12502.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/81ae23195cec037fb59e457d04c488e47bbcd8e73fac837d578ed1d3286b2756.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/a487e4787d31a4532fd04f89a76d07971a07ff6e2b93e265a3d7295626d3cfb9.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d8b8d3b198f223e6e46d9b05b924e2dda7ddf4f6245655cde97859b052d4bbfc.jpg", "publicReleaseDate": 1717718400000, "runtimeSeconds": 6958, "runtime": "115 min", "overallRating": 4.7, "totalReviewCount": 1271, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.699ad055-17a6-4491-9ac0-e2502e8bab82", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_6"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_6", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.699ad055-17a6-4491-9ac0-e2502e8bab82", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_6"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_6", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>'s <PERSON><PERSON><PERSON> 7", "gti": "amzn1.dv.gti.969d4a4e-491b-46b1-895e-414ec51a7fc7", "transformItemId": "amzn1.dv.gti.969d4a4e-491b-46b1-895e-414ec51a7fc7", "synopsis": "Four beautiful African American girlfriends in their 30's navigate the zany world of dating in a present-day society.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "TV-14", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "maturityRatingString": "TV-14", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e47ba4973c70151a924a5f453cfd043b3075cc9fb63de6bc9923671623db1c35.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/94ebccb18e70f396e0e6d2f56714627b422095e7a7a3f27711079960c06e75f2.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/90d1637c694f66175045faf44270f70519801155a2a5dda28199ddf13a39693a.jpg", "publicReleaseDate": 1722988800000, "overallRating": 4.8, "totalReviewCount": 176, "seasonNumber": 7, "numberOfSeasons": 7, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.969d4a4e-491b-46b1-895e-414ec51a7fc7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_7"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_7", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.969d4a4e-491b-46b1-895e-414ec51a7fc7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_7"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_7", "journeyIngressContext": "8|EgR0dm9k"}], "showName": "<PERSON>'s <PERSON><PERSON><PERSON>", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": true}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Furiosa: A Mad Max Saga", "gti": "amzn1.dv.gti.3f53de6b-4d3f-42c2-8f21-dbf813fffa66", "transformItemId": "amzn1.dv.gti.3f53de6b-4d3f-42c2-8f21-dbf813fffa66", "synopsis": "Abducted from her home, <PERSON><PERSON><PERSON> embarks on a relentless journey across the Wasteland, confronting warring warlords <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Science Fiction", "Suspense"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/64db339397e936ba23af1436b25406dea0b80afa8a91e977a8af62b9fcda3778.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/0a71b39e811472959a8be939971a26e053c2075aa7648a379e51e8ff5d8f9bef.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/6897302e0d63d281485342a4e8af9c7757e7c406352d8ba6777447d7a07328b0.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/cd25508e5a6b65b9e33aab6bd71d180f9287506e0b16ca7b16cf2d751782d215.jpg", "publicReleaseDate": 1716508800000, "runtimeSeconds": 8890, "runtime": "148 min", "overallRating": 4.4, "totalReviewCount": 1646, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3f53de6b-4d3f-42c2-8f21-dbf813fffa66", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_8"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_8", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3f53de6b-4d3f-42c2-8f21-dbf813fffa66", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_8"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_8", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "The Ministry of Ungentlemanly Warfare", "gti": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "transformItemId": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "synopsis": "A U.S. military combat unit goes on a secret mission against the Nazis in World War II, altering history and pioneering black ops tactics.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Comedy", "Military and War", "Drama"], "maturityRatingString": "R", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/a25a11f1fca7091779413afc901df0b53e65dfe77fa28d17d48525aa66981db0.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/b32d4904eb31998855eb13696e4d08eb1d799b56204fa33abb4e0c85ecf9c2aa.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/3ec07e03776b51fc1eafc83b3691a661a13983a02aa023dd0a872e635c87a0ac.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9215674349522cb1728ce1f4cadd0ca999aee873c25300f762c36e1ca5bf7501.jpg", "publicReleaseDate": 1713484800000, "runtimeSeconds": 7220, "runtime": "120 min", "overallRating": 4.6, "totalReviewCount": 5320, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_9"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_9", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.62ec52b8-70e1-47bf-92e9-07b28a679b92", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_9"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_9", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "IF", "gti": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "transformItemId": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "synopsis": "In a whimsical tale, a girl's ability to perceive imaginary friends leads her on an enchanting quest to reunite them with their young owners.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Comedy"], "maturityRatingString": "PG", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3201e0cbc9d4ba3db94187a93327607126767684f647fefaba12bd63629f1c2e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/76df0dac7be58ecfc65d95d64d5a5196f8ce741500b616c9fe7d667ca40244fe.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/9b98d52bc1dc340a3ce5c48665e11c875a55823fdf5a8bdf8e9c700ccb5b3fca.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/cbsaacf/logos/channels-logo-white._CB583955135_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/03d24de2daa6d7f1a24e64203a366e5531dec89100f5b351ef582ed64e7fded7.jpg", "publicReleaseDate": 1715904000000, "runtimeSeconds": 6248, "runtime": "104 min", "overallRating": 4.6, "totalReviewCount": 2829, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_10"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_10", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trials available, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "NEW", "level": "INFO", "type": "BADGE"}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_brws_3_10"}, "refMarker": "me_pv-_c_Y4Z0qE_brws_3_10", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}], "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Pio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqjE6MTJHVzlOVkM3WTdDTVgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "TVOD", "entitlement": "NotEntitled", "analytics": {"refMarker": "me_pv-_c_Y4Z0qE_3", "ClientSideMetrics": "452|ClwKLnB2d2ViTGl2ZUV2ZW50c3N1cGVyY2Fyb3VzZWxMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTJHVzlOVkM3WTdDTVgaEDI6RFlBNjI5NjhGNEM0MzkiBlk0WjBxRRJLCgVtZXJjaBIScHYtd2ViLWxpdmUtZXZlbnRzIgZjZW50ZXIqADIkNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkGgR0dm9kIgNhbGwqADIPZmFjZXRlZENhcm91c2VsOgZCcm93c2VCFk9yaWdpbmFsc0FuZEV4Y2x1c2l2ZXNSC25vdEVudGl0bGVkWgBiDVN1cGVyQ2Fyb3VzZWxoA3IAejgtMVVsbFhnTXF6dW44ejdUcFl1UXRfNzExamFGVGJpcmJ4ZjJUbTJXa2Z1a19jaTNhQXk2b2c9PYIBBWZhbHNligEAkgEA"}, "tags": [], "journeyIngressContext": "8|EgR0dm9k", "notExpandable": false, "type": "SUPER_CAROUSEL"}, {"id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Lio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOpjE6MTFSUDZRMVlMWEJIUUsjI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy", "items": [{"title": "Carousel Title", "imageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/US_CHANNELS_NBA_LeaguePass_Evergreen/ab532dfd-f7a6-4245-8daa-1e4cde00e1b4.jpeg", "logoImageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/US_CHANNELS_NBA_LeaguePass_Evergreen/3bed55d9-b53f-4a73-b905-f070d60e4354.png", "gradientRequired": false, "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": ""}}, "action": {"target": "landing", "pageId": "nbalp", "pageType": "subscription", "analytics": {"refMarker": "me_pv-_c_fw2Eb8_5Fuc4s_4_1", "itemProducerID": "Superhero-Sonata-Pinned-nontitle"}, "refMarker": "me_pv-_c_fw2Eb8_5Fuc4s_4_1"}, "widgetType": "imageTextLink", "transformItemId": "landing:subscription:nbalp", "cardType": "LINK_CARD"}, {"title": "Tentpole/Autoplay test - SBLVIII", "synopsis": "Super Bowl LVIII", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "transformItemId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_CHANNELS_MAX_BRSportsMatchSpecificSoccer2024_CS_0608/69943e0b-7aa8-4543-b8df-3f103f66f09b.jpeg", "action": {"target": "detail", "pageId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_fw2Eb8_WQ5EyV_4_2", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_fw2Eb8_WQ5EyV_4_2", "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "actions": [{"refMarker": "atv_hm_hom_c_live_signup_3p_subwatch_t1JEBAAAAA0lr2", "target": "openModal", "label": "Subscribe and Watch", "modalHeader": "Subscribe and Watch", "actionSegments": [{"childActions": [{"refMarker": "atv_hm_hom_c_live_signup_3p_bb_t1JEDAAAAA0lr0", "target": "acquisition", "label": "Watch with Paramount+{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_hm_hom_c_live_signup_3p_bb_t1JEDAAAAA0lr0", "benefitId": "cbsaacf", "offerToken": "amzn.dv.offertoken.v2:2: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", "metadataActionType": "AcquisitionSVOD"}}], "entitlementMessaging": {"BUYBOX_MESSAGE_SLOT": {"message": "Included with Paramount+ from $5.99/month after trial", "icon": "OFFER_ICON", "type": "MESSAGE"}}}, {"childActions": [{"refMarker": "atv_hm_hom_c_live_signup_3p_bb_t1JEDAAAAA0lr1", "target": "acquisition", "label": "Watch with ViX Premium{lineBreak}Start your 7-day free trial", "metadata": {"refMarker": "atv_hm_hom_c_live_signup_3p_bb_t1JEDAAAAA0lr1", "benefitId": "vix<PERSON><PERSON>us", "offerToken": "amzn.dv.offertoken.v2:2: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", "metadataActionType": "AcquisitionSVOD"}}], "entitlementMessaging": {"BUYBOX_MESSAGE_SLOT": {"message": "Included with ViX Premium from $4.99/month after trial", "icon": "OFFER_ICON", "type": "MESSAGE"}}}]}, {"target": "detail", "pageId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_fw2Eb8_WQ5EyV_4_2", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_fw2Eb8_WQ5EyV_4_2", "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "isInWatchlist": false, "moreDetailsAction": {"target": "detail", "pageId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_fw2Eb8_WQ5EyV_4_2", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_fw2Eb8_WQ5EyV_4_2", "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "liveEventDateBadge": {"text": "<PERSON><PERSON>, Dec 5 2:25 PM EST", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Dec 5, 2023", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Paramount+ or ViX Premium", "icon": "OFFER_ICON", "type": "MESSAGE", "messages": ["Subscribe to Paramount+ or ViX Premium"]}, "INFORMATIONAL_MESSAGE_SLOT": {"message": "Terms apply", "type": "MESSAGE", "messages": ["Terms apply"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "all", "tournamentIcid": "amzn1.dv.icid.ca51a1f1-faf8-4feb-bb41-a736eaa2bf03", "contentType": "EVENT", "backgroundImageUrl": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f/4/HERO-16X9/en-US.png", "cardType": "HERO_CARD"}, {"title": "Cowboys vs. Giants", "synopsis": "Watch the New York Giants take on the Dallas Cowboys as part of exclusive live coverage of Thursday Night Football on Prime Video.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.ddbf1bae-088a-4e49-bd85-f303ad4b0191", "transformItemId": "amzn1.dv.gti.ddbf1bae-088a-4e49-bd85-f303ad4b0191", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_MLBTV_2024_CSFR/5d95593d-b1f7-4348-bf3a-8514542a776f.jpeg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-color._CB554929912_.png", "providerLogoImageMetadata": {"height": 1183, "width": 2000, "scalarHorizontal": "emphasis"}, "action": {"target": "detail", "pageId": "amzn1.dv.gti.ddbf1bae-088a-4e49-bd85-f303ad4b0191", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_fw2Eb8_E7FnPK_4_3", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_fw2Eb8_E7FnPK_4_3", "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.ddbf1bae-088a-4e49-bd85-f303ad4b0191", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_fw2Eb8_E7FnPK_4_3", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_fw2Eb8_E7FnPK_4_3", "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}], "isInWatchlist": false, "moreDetailsAction": {"target": "detail", "pageId": "amzn1.dv.gti.ddbf1bae-088a-4e49-bd85-f303ad4b0191", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_fw2Eb8_E7FnPK_4_3", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_fw2Eb8_E7FnPK_4_3", "journeyIngressContext": "16|CgNhbGwSA2FsbA=="}, "liveEventDateBadge": {"text": "Thu, Sep 26 7:00 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Sep 26, 2024", "time": "7:00 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON", "type": "MESSAGE", "messages": ["Included with Prime"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "7+", "tournamentIcid": "amzn1.dv.icid.ae758359-5951-4043-9160-a5099d73d3ff", "tnfProperty": {"customerIntent": "UNSET"}, "contentType": "EVENT", "backgroundImageUrl": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.ddbf1bae-088a-4e49-bd85-f303ad4b0191/407/HERO-16X9/en-US.jpg", "cardType": "HERO_CARD"}], "analytics": {"refMarker": "me_pv-_c_fw2Eb8_4", "ClientSideMetrics": "452|ClkKK3B2d2ViTGl2ZUV2ZW50c0hlcm9QYXJlbnRMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTFSUDZRMVlMWEJIUUsaEDI6RFkxQTU0ODg1QjY1NDIiBmZ3MkViOBJLCgVtZXJjaBIScHYtd2ViLWxpdmUtZXZlbnRzIgZjZW50ZXIqADIkNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkGgNhbGwiA2FsbCoDYWxsMgxoZXJvQ2Fyb3VzZWw6BkZhcm1lckIJU3VwZXJIZXJvShRzaG93VW5kZXJFdmVyeUZpbHRlclIIZW50aXRsZWRaAGIMU3RhbmRhcmRIZXJvaARyAHo4LTFVbGxYZ01xenVuOHo3VHBZdVF0XzcxMWphRlRiaXJieGYyVG0yV2tmdWtfY2kzYUF5Nm9nPT2CAQNhbGyKAQCSAQA="}, "tags": [], "journeyIngressContext": "16|CgNhbGwSA2FsbA==", "type": "STANDARD_HERO"}, {"facet": {}, "title": "ViX+: Live and upcoming events", "titleImageUrl": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/blast_carousel-logo_selected_rar._CB561014429_.png", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6Ii0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09OjE3MjQwNjY3ODIwMDAiLCJhcE1heCI6MzYsInN0cmlkIjoiMToxWTlQSUIyOU9FMlRUIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE0iLCJvcmVxayI6ImQxOUZac0xPeFk0OHlweVlEQmE4TEJDVHJmVkRJdHhZZUU4SVJ4NzRiZVE9Iiwib3JlcWt2IjoxLCJleGNsVCI6W119", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Oio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqTE6MVk5UElCMjlPRTJUVCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "pageId": "pv-web-live-events", "pageType": "merch", "pageContext": {"pageType": "merch", "pageId": "pv-web-live-events"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Oio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqTE6MVk5UElCMjlPRTJUVCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "offerType": "Subscription", "entitlement": "NotEntitled", "items": [{"title": "Super Bowl LVIII: Buffalo Bills vs. Kansas City Chiefs [BETA]", "gti": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "transformItemId": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "synopsis": "Watch Super Bowl Sunday live as the Bills face the Chiefs at Allegiant Stadium in Las Vegas. [BETA]", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "7+", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7/42/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7/42/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7/42/BOXART-16X9/en-US.png", "venue": "Test", "liveEventDateBadge": {"text": "Sun, Jan 21", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Jan 21, 2024", "time": "6:30 PM EST", "type": "LOCALIZED_HEADER"}, "liveliness": "ENDED", "startTime": 1705879800000, "endTime": 1705921200000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_1"}, "refMarker": "me_pv-_c_XMF4x3_5_1", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe on Prime Video Channels", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "ENDED", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.4d338999-b7a3-4d27-8611-4b37151b4acc", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.12871c28-3a85-4bae-83b6-57ba7e20f6f7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_1"}, "refMarker": "me_pv-_c_XMF4x3_5_1", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Tentpole/Autoplay test - SBLVIII", "gti": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "transformItemId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "synopsis": "Super Bowl LVIII", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f/4/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f/4/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f/4/BOXART-16X9/en-US.png", "venue": "Test", "liveEventDateBadge": {"text": "<PERSON><PERSON>, Dec 5 2:25 PM EST", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Dec 5, 2023", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "startTime": 1701804300000, "endTime": 1705438800000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_2"}, "refMarker": "me_pv-_c_XMF4x3_5_2", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe on Prime Video Channels", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.ca51a1f1-faf8-4feb-bb41-a736eaa2bf03", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.8f3179fe-1e8b-43cb-9133-3a21e541430f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_2"}, "refMarker": "me_pv-_c_XMF4x3_5_2", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Patriotas FC vs. Once Caldas SA", "gti": "amzn1.dv.gti.afba21c9-36b3-4b30-8d10-2544f5ea9838", "transformItemId": "amzn1.dv.gti.afba21c9-36b3-4b30-8d10-2544f5ea9838", "synopsis": "Watch Patriotas FC take on Once Caldas SA", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.afba21c9-36b3-4b30-8d10-2544f5ea9838/4/HERO-16X9/en-US.jpg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.afba21c9-36b3-4b30-8d10-2544f5ea9838/4/BOXART-4X3/en-US.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.afba21c9-36b3-4b30-8d10-2544f5ea9838/4/BOXART-16X9/en-US.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-white._CB558043175_.png", "venue": "Estadio de La Independencia", "liveEventDateBadge": {"text": "Live at 4:40 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 19, 2024", "time": "4:40 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724100000000, "endTime": 1724111400000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.afba21c9-36b3-4b30-8d10-2544f5ea9838", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_4"}, "refMarker": "me_pv-_c_XMF4x3_5_4", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.6accb402-7a41-4ce4-911d-d35c1c541b67", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.afba21c9-36b3-4b30-8d10-2544f5ea9838", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_4"}, "refMarker": "me_pv-_c_XMF4x3_5_4", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Jaguares de Córdoba FC vs. Club Deportes Tolima SA", "gti": "amzn1.dv.gti.b49c858d-1a5a-4e6d-8cf1-32dcdca50654", "transformItemId": "amzn1.dv.gti.b49c858d-1a5a-4e6d-8cf1-32dcdca50654", "synopsis": "Watch Jaguares de Córdoba FC take on Club Deportes Tolima SA", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.b49c858d-1a5a-4e6d-8cf1-32dcdca50654/7/HERO-16X9/en-US.jpg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.b49c858d-1a5a-4e6d-8cf1-32dcdca50654/7/BOXART-4X3/en-US.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.b49c858d-1a5a-4e6d-8cf1-32dcdca50654/7/BOXART-16X9/en-US.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-white._CB558043175_.png", "venue": "Estadio de Fútbol Jaraguay de Montería", "liveEventDateBadge": {"text": "Live at 6:55 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 19, 2024", "time": "6:55 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724108100000, "endTime": 1724119500000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.b49c858d-1a5a-4e6d-8cf1-32dcdca50654", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_5"}, "refMarker": "me_pv-_c_XMF4x3_5_5", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.6accb402-7a41-4ce4-911d-d35c1c541b67", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.b49c858d-1a5a-4e6d-8cf1-32dcdca50654", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_5"}, "refMarker": "me_pv-_c_XMF4x3_5_5", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "LOSC Lille vs. SK Slavia Praha", "gti": "amzn1.dv.gti.26382d22-2373-4abb-9655-67cd8bf52100", "transformItemId": "amzn1.dv.gti.26382d22-2373-4abb-9655-67cd8bf52100", "synopsis": "Watch LOSC Lille take on SK Slavia Praha", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.26382d22-2373-4abb-9655-67cd8bf52100/7/HERO-16X9/en-US.jpg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.26382d22-2373-4abb-9655-67cd8bf52100/7/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.26382d22-2373-4abb-9655-67cd8bf52100/7/BOXART-16X9/en-US.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-white._CB558043175_.png", "venue": "Stade du Hainaut", "liveEventDateBadge": {"text": "Tomorrow 1:55 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "1:55 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724176500000, "endTime": 1724182500000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.26382d22-2373-4abb-9655-67cd8bf52100", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_6"}, "refMarker": "me_pv-_c_XMF4x3_5_6", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.545232ed-0823-49fd-b889-e42b835b78e9", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.26382d22-2373-4abb-9655-67cd8bf52100", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_6"}, "refMarker": "me_pv-_c_XMF4x3_5_6", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "FK Bodø / Glimt vs. FK Crvena zvezda Beograd", "gti": "amzn1.dv.gti.a7388b92-fae1-4857-93b1-5f96e7f2ddd7", "transformItemId": "amzn1.dv.gti.a7388b92-fae1-4857-93b1-5f96e7f2ddd7", "synopsis": "Watch FK Bodø / Glimt take on FK Crvena zvezda Beograd", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.a7388b92-fae1-4857-93b1-5f96e7f2ddd7/9/HERO-16X9/en-US.jpg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.a7388b92-fae1-4857-93b1-5f96e7f2ddd7/9/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.a7388b92-fae1-4857-93b1-5f96e7f2ddd7/9/BOXART-16X9/en-US.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-white._CB558043175_.png", "venue": "Aspmyra Stadion", "liveEventDateBadge": {"text": "Tomorrow 2:40 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "2:40 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724179200000, "endTime": 1724187600000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.a7388b92-fae1-4857-93b1-5f96e7f2ddd7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_7"}, "refMarker": "me_pv-_c_XMF4x3_5_7", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.545232ed-0823-49fd-b889-e42b835b78e9", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.a7388b92-fae1-4857-93b1-5f96e7f2ddd7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_7"}, "refMarker": "me_pv-_c_XMF4x3_5_7", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "GNK Dinamo Zagreb vs. Qarabağ Ağdam FK", "gti": "amzn1.dv.gti.ad006362-c8a6-499b-ad26-63174c6e2666", "transformItemId": "amzn1.dv.gti.ad006362-c8a6-499b-ad26-63174c6e2666", "synopsis": "Watch GNK Dinamo Zagreb take on Qarabağ Ağdam FK", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.ad006362-c8a6-499b-ad26-63174c6e2666/8/HERO-16X9/en-US.jpg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.ad006362-c8a6-499b-ad26-63174c6e2666/8/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.ad006362-c8a6-499b-ad26-63174c6e2666/8/BOXART-16X9/en-US.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-white._CB558043175_.png", "venue": "Stadion Maksimir", "liveEventDateBadge": {"text": "Tomorrow 2:40 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "2:40 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724179200000, "endTime": 1724190600000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.ad006362-c8a6-499b-ad26-63174c6e2666", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_8"}, "refMarker": "me_pv-_c_XMF4x3_5_8", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.545232ed-0823-49fd-b889-e42b835b78e9", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.ad006362-c8a6-499b-ad26-63174c6e2666", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_8"}, "refMarker": "me_pv-_c_XMF4x3_5_8", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "Asociación Deportivo Cali vs. Independiente Santa Fe SA", "gti": "amzn1.dv.gti.469afc75-be3a-4d23-98ca-cbf0a40d1e83", "transformItemId": "amzn1.dv.gti.469afc75-be3a-4d23-98ca-cbf0a40d1e83", "synopsis": "Watch Asociación Deportivo Cali take on Independiente Santa Fe SA", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.469afc75-be3a-4d23-98ca-cbf0a40d1e83/8/HERO-16X9/en-US.jpg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.469afc75-be3a-4d23-98ca-cbf0a40d1e83/8/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.469afc75-be3a-4d23-98ca-cbf0a40d1e83/8/BOXART-16X9/en-US.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-white._CB558043175_.png", "venue": "Estadio Deportivo Cali", "liveEventDateBadge": {"text": "Tomorrow 7:40 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "7:40 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724197200000, "endTime": 1724208600000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.469afc75-be3a-4d23-98ca-cbf0a40d1e83", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_9"}, "refMarker": "me_pv-_c_XMF4x3_5_9", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.6accb402-7a41-4ce4-911d-d35c1c541b67", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.469afc75-be3a-4d23-98ca-cbf0a40d1e83", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_9"}, "refMarker": "me_pv-_c_XMF4x3_5_9", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}, {"title": "MLB Baseball", "gti": "amzn1.dv.gti.0d57a45d-f347-4307-afd7-808ec716db50", "transformItemId": "amzn1.dv.gti.0d57a45d-f347-4307-afd7-808ec716db50", "synopsis": "All the latest action from Major League Baseball.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.0d57a45d-f347-4307-afd7-808ec716db50/1/HERO-16X9/en-US.jpg", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.0d57a45d-f347-4307-afd7-808ec716db50/1/BOXART-4X3/en-US.jpg", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.0d57a45d-f347-4307-afd7-808ec716db50/1/BOXART-16X9/en-US.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/s-z/vixplusus/logos/channels-logo-white._CB558043175_.png", "liveEventDateBadge": {"text": "Tomorrow 8:55 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 20, 2024", "time": "8:55 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "UPCOMING", "startTime": 1724201700000, "endTime": 1724211000000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.0d57a45d-f347-4307-afd7-808ec716db50", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_10"}, "refMarker": "me_pv-_c_XMF4x3_5_10", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of ViX Premium", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UPCOMING", "level": "INFO_INACTIVE", "type": "BADGE"}}, "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.0d57a45d-f347-4307-afd7-808ec716db50", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_XMF4x3_5_10"}, "refMarker": "me_pv-_c_XMF4x3_5_10", "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 247, "width": 2000, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "me_pv-_c_XMF4x3_5", "ClientSideMetrics": "592|Ck4KIlBWV2ViTGl2ZVZpeHBsdXNMaXZlRGVmYXVsdERlZmF1bHQSDzE6MVk5UElCMjlPRTJUVBoPMToxWTlQSUIyOU9FMlRUIgZYTUY0eDMSSwoFbWVyY2gSEnB2LXdlYi1saXZlLWV2ZW50cyIGY2VudGVyKgAyJDRiMDUxZWE0LWE1MjMtNDc5NS05YmQyLWQ5M2EzNGIzOTMzZBoMc3Vic2NyaXB0aW9uIgAqCXZpeHBsdXN1czIPZmFjZXRlZENhcm91c2VsOhlMaXZlRXZlbnRzQ29udGVudFByb3ZpZGVyQhpMaXZlRXZlbnRzQnJvd3NlU3RyYXRlZ3lWMkoWY29sbGVjdGlvblBhZ2VDYXJvdXNlbEoUc2hvd1VuZGVyRXZlcnlGaWx0ZXJKEGhvbWVQYWdlQ2Fyb3VzZWxKD2xpdmVBbmRVcGNvbWluZ1IIZW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgFcgB6OC0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09ggEFZmFsc2WKAQCSAQA="}, "tags": ["collectionPageCarousel", "homePageCarousel"], "journeyIngressContext": "36|Cgl2aXhwbHVzdXMSDHN1YnNjcmlwdGlvbg==", "type": "STANDARD_CAROUSEL"}, {"facet": {}, "title": "Live sports", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Pio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqjE6MTJCUVpHODRWVzFHR0YjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "Mixed", "entitlement": "NotEntitled", "items": [{"title": "New York Yankees at Baltimore Orioles", "gti": "amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7", "transformItemId": "amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7", "synopsis": "Watch New York Yankees at Baltimore Orioles LIVE with Prime (NYC region only).", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7/2/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7/2/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7/2/BOXART-16X9/en-US.png", "poster2x3Image": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7/2/POSTER-2X3/en-US/2000x3000.png", "venue": "Baltimore, USA", "liveEventDateBadge": {"text": "Wed, May 11 6:30 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "May 11, 2022", "time": "6:30 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "startTime": 1652308200000, "endTime": 1652332500000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_U07vwD_6_1"}, "refMarker": "me_pv-_c_U07vwD_6_1", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Unavailable on Prime Video in your region", "level": "INFO", "type": "MESSAGE"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "UNAVAILABLE", "level": "INFO_INACTIVE", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.86ebcbb5-1f61-44ff-aebd-6508d083e404", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.1db9a482-3724-4c02-8980-9ae521a344f7", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_U07vwD_6_1"}, "refMarker": "me_pv-_c_U07vwD_6_1", "journeyIngressContext": "8|EgNhbGw="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "LECX Rapid Recap Test Event", "gti": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "transformItemId": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "synopsis": "This title is used for LECX automation tests , Please DO NOT modify this title.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "7+", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/BOXART-16X9/en-US.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/POSTER-2X3/en-US.png", "venue": "U.S. Bank Stadium, Minneapolis, MN", "liveEventDateBadge": {"text": "<PERSON><PERSON>, Dec 31 11:45 AM EST", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Dec 31, 2024", "time": "11:45 AM EST", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "startTime": *************, "endTime": *************, "action": {"target": "detail", "pageId": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_U07vwD_6_2"}, "refMarker": "me_pv-_c_U07vwD_6_2", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.cbe8c969-b66d-44b7-ba28-f588f3c56b16", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_U07vwD_6_2"}, "refMarker": "me_pv-_c_U07vwD_6_2", "journeyIngressContext": "8|EgNhbGw="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Condensed Replay", "gti": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "transformItemId": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "synopsis": "Watch the condensed replay of Cavaliers at Wizards ", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "VOD_EVENT_ITEM", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e/1/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e/1/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e/1/BOXART-16X9/en-US.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/nbalp/logos/channels-logo-white._CB557657793_.png", "action": {"target": "detail", "pageId": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_U07vwD_6_3"}, "refMarker": "me_pv-_c_U07vwD_6_3", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of NBA League Pass", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_U07vwD_6_3"}, "refMarker": "me_pv-_c_U07vwD_6_3", "journeyIngressContext": "8|EgNhbGw="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 425, "width": 2000}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "me_pv-_c_U07vwD_6", "ClientSideMetrics": "504|ClMKJXB2d2VidXBjb21pbmdzcG9ydHNMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTJCUVpHODRWVzFHR0YaEDE6MTJCUVpHODRWVzFHR0YiBlUwN3Z3RBJLCgVtZXJjaBIScHYtd2ViLWxpdmUtZXZlbnRzIgZjZW50ZXIqADIkNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkGgNhbGwiACoAMg9mYWNldGVkQ2Fyb3VzZWw6GUxpdmVFdmVudHNDb250ZW50UHJvdmlkZXJCGkxpdmVFdmVudHNCcm93c2VTdHJhdGVneVYySg9saXZlQW5kVXBjb21pbmdKCXdhdGNobGlzdFIIZW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgGcgB6OC0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09ggEFZmFsc2WKAQCSAQA="}, "tags": ["watchlist"], "journeyIngressContext": "8|EgNhbGw=", "type": "STANDARD_CAROUSEL"}, {"id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Lio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOpjE6MTEwWERHUTk0UkJDM1EjI05CU1hFMzJETUZaRzY1TFRNVldBjQ-OglYy", "offerType": "Mixed", "entitlement": "Mixed", "item": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> Drop Test - 8/16", "synopsis": "Watch the Denver Broncos take on the Green Bay Packers as part of exclusive live coverage of Thursday Night Football on Prime Video.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": false}, "gti": "amzn1.dv.gti.b807ed75-b176-4679-b1ad-79aa69088910", "transformItemId": "amzn1.dv.gti.b807ed75-b176-4679-b1ad-79aa69088910", "heroImage": "https://m.media-amazon.com/images/S/sonata-images-prod/TentpoleHero_Image_Test/43172f9a-43f0-4945-85fe-70105d1210da.jpeg", "titleLogoImage": "https://m.media-amazon.com/images/S/sonata-images-prod/US_SVOD_NWSL_Game4_HoustonDashVsWashingtonSpirit/e64e0d23-f2e1-4441-b2c1-19143be2ee2c.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-color._CB554929912_.png", "providerLogoImageMetadata": {"height": 1183, "width": 2000, "scalarHorizontal": "emphasis"}, "action": {"target": "detail", "pageId": "amzn1.dv.gti.b807ed75-b176-4679-b1ad-79aa69088910", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_uTSSUF_xpok9L_7_1", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_uTSSUF_xpok9L_7_1", "journeyIngressContext": "8|EgNhbGw="}, "actions": [{"refMarker": "atv_dp_hom_c_prime_hd_live_play_t1ACAAAAAA0lr0", "target": "playback", "playbackMetadata": {"refMarker": "atv_dp_hom_c_prime_hd_live_play_t1ACAAAAAA0lr0", "playbackExperienceMetadata": {"playbackEnvelope": "eyJ0eXAiOiJwbGVuditqd2UiLCJjdHkiOiJwbGVuditqd3MiLCJhbGciOiJBMjU2S1ciLCJlbmMiOiJBMjU2Q0JDLUhTNTEyIiwia2lkIjoiYTJ6K3BwX2VuYytzX3BuVmxvZHcifQ.r85Fq6h-d6ifC7B8DoMvZVk_uKIJ9unrhLl1JWOMu8BZTTM5BVas4b_Be0I-bIERzgh9MWZt-GRxM29i41UX-lZL4wJbjGqc.kf5CwZxc_ebqnrf_hybEEQ.85yLNaB5VsDmHFvIUytFRPfP0FMuJF9XnnYT783AC-HhEFAvTGKmw5myU4zw0s6tOiU6x1wYpoXVFP7Jajr6YooIxspXvJT2Vxs5ScTGBKPamQWDc4IDPz7sCUp5-2TPs0L0JIgfXceaF8PoidPTUuTQ0376bPluniFLtHJSrN4gQ2ohEEEGjmy7jZAW5rYKNIxsyNMnIGqCijRp6jWxpb8l-vKbISXyIzCJHezL_j6YH7HFuQWy046EuGcHhKpC8MTkRejwltWadu4-AiBKA6inknXI39cuZ0jLIBh9bIfJAvWgQvg1abV29c4QhaCP28xRHrjcZQexGO3i3eWC6JKP40zF4dViT60Gi3ShecVL4NaPWbfrDG8Rf_XLEi3ZyIlMlR9D3mC8UPgjdC9YZc85DBuRGJdqvDuG3J8reDCSOE_mydZHUvfK6n5HUvsxdUVwlmpLEpXicidcKG-bsoed--WOFFOd7GebKlXAaZbS2mqPNgriuJR9W6Xl3IQxqBgPQN6jxAOM9fUaEOA8sWMJV6IfmHDPFK9B7Mv5E4l-xUYtT_OgORkArYRiHPyKLtTgrCaF85L_iUUegL0_8QDsiCROwCttbxTqLcq15JN_qABd2X1do-_8aitKEMMCGFfnM41hB6cMDFa0sm2cmTBVcLxORwBtDiSIMrfFDax1KCAvXF2bFtF48BKLuDDGfwZT1PI8WE3kmCQnkzQhCz9-_Gf3gqGEVG2Kcn6SHZKQ2Ug_VkaQ3c1k-ntJntYN5mWw1Qtc5SKcMOxf8B7E2Kp6oxaWtV9_XiM-LJjmbwIqr3X_mD1us0u6ameo1FgB7aRIl0ma5_b44zS9b3nKxjtd7doc31g6h4KQ50Baslq7nE6Bde_Pc-FZUSZ4HA8GELBFKW8SQ_i-vkg3AEW7RgZ2JgzL8Ef43uMrTNe8yu4z-5UE3g-qV4obJ5_wUOsyAvTBOc1eF2FXoLWLiFblxQ5kOtsGlh3LwPN8igX-w87BKzK-D84b3JPuzgyJfgxfjdzrI2W_ACKmlvU4XZdybEf694LeZpSlplWogoQ-cORhZoI-A0bEekAcMPpvpk2Og1eAks7VCU-7s3PWPBt17HMWR6iEfNe2yTW1G4LHxtXI_0twL_RfAyywnTRG6_KoQs8-Y2Da91bXGRlOqkmDYvOAev_DG5JbImmbVhmlgAYDrY4tPb5uLEeARmTQnez8apmiLFuRgRNSeuEXFAqx3PbPsJi6sdCvscm_zuATtprjaUqH4ILXsd9YiHcx9V1_zt36GAGhd_bm6-qlfY8qlAjlEJAooKXDL4hBAnOBgvPM2wtY7yMlv5ugyp_y8w4wMjYiG75-OSyGnSCOHd9nLUmrbBl5W_B_2CmEnTIYrxFYAkVdMtLsb8vUX2UIFtbypn5k3gTbUeoFJQDylzK1QZY3bo_RKEPd6Lk3oaAmSEzMacq0yTuA1-EFSbB7JPERPDPSAb1ssFdEdec4rv-akvMDSLifvfmqDtH-_B2_6tLo-znOxPpE6p1qQJKpOmlhkRcqEqIHpLX-6b6Po2QEaS8xenosLxn7zKF1yl4bmbd_FRIq3M5_S2ukw1MC1MQ5XcMtGxuU13P-vNP--H-EJ4pgXPT_kp-FRF6YM8DM8L8YtXKTJ9JVtRiMm7FsmiubX8NfSAj032vVLKdHMCniGsMdF8lRpyThq0u_KAi24VK1b8BV7GY-g6n69gnY-OHx4SLJYbk6zQJqJ4o8EuHEuoEWzUj8lQrtpMoTTYC0PfEVPZQkPNqmB9_4Q05JoY6AelIkBRyFBiiTXp_n33tHtzcyrEPKnmXrBeoQ4qIVzoNb7fmmKX_sCj4VhLUQVDGE4HvHDA6eI6WQm1btNnay-rOkaarn5JhyDFAYBnqog-bpFh0rV-onIZTIMy9jhgrT5EFkBHiI_rSLSyW9IA5v4AIPIfaTsIikz6eR-romFYbs7R_e8f8PxhuNYIMvkXe62GWRlBbv91UQejcXhT-SLzCnjXqOYIXC5vxb6kIvJqKno5-b2s_AJV_PRM38rx0vU8OyArYRJE2-MxQ8YBT2a3aZFciDqxsrC_tyDyq5zWelHVb4yVlS9CoOh6ArEQpZjdkc6bE1_FeR2O18SjnIChk1ZjPzWXlJZXqDlTey2I5N6nJz1C7U8Uf3MYYxhxfLb_VedUNR7BAg0ZerC2iyXnt_VSMlR8P8EaEgg9a8b4j_mOGppM6DpE3zpq_ZjQPMpQilzgoDcjyIKbzzeZd8CfmPoeS-yKuzZ0MkVRYkBs1WHaGh2yyqIWXFvoyyx7H5CmVqZRAZBRoUm0bWXH1BNiNbmSvuCTrw7oBp4yHYQhE.8da3Cr-uTmBpDLXGJVPBWJ8G7AG2O7-WqPLFiBoBU0I", "expiryTime": 1724067683624, "correlationId": "YW16bjEuZHYuZ3RpLjhiZDZmOGRkLTI1ZWUtNDJhYi04MjQ4LWJlMzBmMDkxM2RjMjpTb3VyY2UodHlwZT1CZW5lZml0LCBpZD1QcmltZSwgb3JkZXI9T3JkZXIoaWQ9MiksIHNoYXJlZEJ5PW51bGwpOmFtem4xLmR2LnB2aWQuNjcxNjQ2OTctZDUwNC00ZjZlLTg2YTEtMGJhMzU2NzMzYWQ4OkhE"}, "position": "LIVE_STREAM_WATCH_NOW", "startPositionEpochUtc": 0, "userPlaybackMetadata": {"runtimeSeconds": 226783, "timecodeSeconds": 0, "hasStreamed": false, "isLinear": false, "linearStartTime": 0, "linearEndTime": 0}, "userEntitlementMetadata": {"entitlementType": "PRIME_SUBSCRIPTION", "benefitType": []}, "videoMaterialType": "LiveStreaming", "playbackTitle": "amzn1.dv.gti.8bd6f8dd-25ee-42ab-8248-be30f0913dc2", "metadataActionType": "Playback"}, "label": "Watch Live{lineBreak}Main Broadcast"}, {"target": "detail", "pageId": "amzn1.dv.gti.b807ed75-b176-4679-b1ad-79aa69088910", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_uTSSUF_xpok9L_7_1", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_uTSSUF_xpok9L_7_1", "journeyIngressContext": "8|EgNhbGw="}], "isInWatchlist": false, "moreDetailsAction": {"target": "detail", "pageId": "amzn1.dv.gti.b807ed75-b176-4679-b1ad-79aa69088910", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_uTSSUF_xpok9L_7_1", "itemProducerID": "Superhero-Sonata-Pinned-svod"}, "refMarker": "me_pv-_c_uTSSUF_xpok9L_7_1", "journeyIngressContext": "8|EgNhbGw="}, "liveEventDateBadge": {"text": "Sun, Aug 18 6:45 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Aug 18, 2024", "time": "6:45 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON", "type": "MESSAGE", "messages": ["Included with Prime"]}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE", "messages": []}}, "widgetType": "<PERSON><PERSON><PERSON><PERSON>", "maturityRatingString": "7+", "tournamentIcid": "amzn1.dv.icid.c3f5032e-1463-421e-8cb5-de1846ffeb97", "tnfProperty": {"customerIntent": "UNSET"}, "contentType": "EVENT", "backgroundImageUrl": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.b807ed75-b176-4679-b1ad-79aa69088910/309/HERO-16X9/en-US.png", "cardType": "HERO_CARD"}, "analytics": {"refMarker": "me_pv-_c_uTSSUF_7", "ClientSideMetrics": "416|ClUKJ1BWV2ViTGl2ZVRlbnRwb2xlSGVyb0xpdmVEZWZhdWx0RGVmYXVsdBIQMToxMTBYREdROTRSQkMzURoQMjpEWTYxMDkyM0IzOUMzMSIGdVRTU1VGEksKBW1lcmNoEhJwdi13ZWItbGl2ZS1ldmVudHMiBmNlbnRlcioAMiQ0YjA1MWVhNC1hNTIzLTQ3OTUtOWJkMi1kOTNhMzRiMzkzM2QaA2FsbCIDYWxsKgAyDGhlcm9DYXJvdXNlbDoGRmFybWVyQglTdXBlckhlcm9SC25vdEVudGl0bGVkWgBiDFRlbnRwb2xlSGVyb2gHcgB6OC0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09ggEDYWxsigEAkgEA"}, "tags": [], "journeyIngressContext": "8|EgNhbGw=", "type": "TENTPOLE_HERO"}, {"facet": {}, "title": "Live sports", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt7Pio6ScHYtd2ViLWxpdmUtZXZlbnRzi4VtZXJjaIyOqjE6MTNGRjVPQ1RLWU5STjgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTY0PjoJWMg==", "offerType": "Mixed", "entitlement": "NotEntitled", "items": [{"title": "LECX Rapid Recap Test Event", "gti": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "transformItemId": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "synopsis": "This title is used for LECX automation tests , Please DO NOT modify this title.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "7+", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/BOXART-16X9/en-US.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e/1339/POSTER-2X3/en-US.png", "venue": "U.S. Bank Stadium, Minneapolis, MN", "liveEventDateBadge": {"text": "<PERSON><PERSON>, Dec 31 11:45 AM EST", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "Dec 31, 2024", "time": "11:45 AM EST", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "startTime": *************, "endTime": *************, "action": {"target": "detail", "pageId": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_AlkQah_8_1"}, "refMarker": "me_pv-_c_AlkQah_8_1", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.cbe8c969-b66d-44b7-ba28-f588f3c56b16", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.cdca387d-2b6e-4b11-86e8-2a1aca33bb5e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_AlkQah_8_1"}, "refMarker": "me_pv-_c_AlkQah_8_1", "journeyIngressContext": "8|EgNhbGw="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Condensed Replay", "gti": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "transformItemId": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "synopsis": "Watch the condensed replay of Cavaliers at Wizards ", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "ALL", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "VOD_EVENT_ITEM", "isInWatchlist": false, "genres": [], "maturityRatingString": "all", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e/1/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e/1/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e/1/BOXART-16X9/en-US.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/nbalp/logos/channels-logo-white._CB557657793_.png", "action": {"target": "detail", "pageId": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Alk<PERSON>ah_8_2"}, "refMarker": "me_pv-_c_Alk<PERSON>ah_8_2", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of NBA League Pass", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.73af7d7c-7896-45ce-92f3-3249a242d49e", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Alk<PERSON>ah_8_2"}, "refMarker": "me_pv-_c_Alk<PERSON>ah_8_2", "journeyIngressContext": "8|EgNhbGw="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 425, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "[LECX_QA]Chiefs vs. Chargers", "gti": "amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f", "transformItemId": "amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f", "synopsis": "Watch the Los Angeles Chargers take on the Kansas CityChiefs as part of exclusive live coverage of Thursday Night Football on Prime Video.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "7+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "EVENT", "isInWatchlist": false, "genres": [], "maturityRatingString": "7+", "heroImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f/8/HERO-16X9/en-US.png", "boxartImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f/8/BOXART-4X3/en-US.png", "coverImage": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f/8/BOXART-16X9/en-US.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/le-target-images-prod/amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f/8/POSTER-2X3/en-US.png", "venue": "SoFi Stadium, Inglewood, CA", "liveEventDateBadge": {"text": "<PERSON><PERSON>, May 30 9:05 PM EDT", "type": "LOCALIZED_BADGE"}, "liveEventDateHeader": {"date": "May 30, 2023", "time": "9:05 PM EDT", "type": "LOCALIZED_HEADER"}, "liveliness": "LIVE", "startTime": 1685495100000, "endTime": 1685514300000, "action": {"target": "detail", "pageId": "amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Alk<PERSON>ah_8_3"}, "refMarker": "me_pv-_c_Alk<PERSON>ah_8_3", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": "LIVE", "level": "INFO_HIGHLIGHT", "type": "BADGE"}}, "tournamentIcid": "amzn1.dv.icid.cbe8c969-b66d-44b7-ba28-f588f3c56b16", "actions": [{"target": "detail", "pageId": "amzn1.dv.gti.5ce8a2ae-cb0a-4f56-b03e-8526d4dc8a8f", "pageType": "detail", "analytics": {"refMarker": "me_pv-_c_Alk<PERSON>ah_8_3"}, "refMarker": "me_pv-_c_Alk<PERSON>ah_8_3", "journeyIngressContext": "8|EgNhbGw="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "me_pv-_c_AlkQah_8", "ClientSideMetrics": "504|ClMKJXB2d2ViM3BscmF1dG9tYXRpb25MaXZlRGVmYXVsdERlZmF1bHQSEDE6MTNGRjVPQ1RLWU5STjgaEDE6MTNGRjVPQ1RLWU5STjgiBkFsa1FhaBJLCgVtZXJjaBIScHYtd2ViLWxpdmUtZXZlbnRzIgZjZW50ZXIqADIkNGIwNTFlYTQtYTUyMy00Nzk1LTliZDItZDkzYTM0YjM5MzNkGgNhbGwiACoAMg9mYWNldGVkQ2Fyb3VzZWw6GUxpdmVFdmVudHNDb250ZW50UHJvdmlkZXJCGkxpdmVFdmVudHNCcm93c2VTdHJhdGVneVYySg9saXZlQW5kVXBjb21pbmdKCXdhdGNobGlzdFIIZW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgIcgB6OC0xVWxsWGdNcXp1bjh6N1RwWXVRdF83MTFqYUZUYmlyYnhmMlRtMldrZnVrX2NpM2FBeTZvZz09ggEFZmFsc2WKAQCSAQA="}, "tags": ["watchlist"], "journeyIngressContext": "8|EgNhbGw=", "type": "STANDARD_CAROUSEL"}], "subNav": [], "pageMetadata": {"title": "", "logoImage": {}, "locationDependentPage": false, "showVoiceFilters": false, "persistentTitleOrLogo": false}}, "metadata": {"requestId": "-1UllXgMqzun8z7TpYuQt_711jaFTbirbxf2Tm2Wkfuk_ci3aAy6og==", "requestedTransformId": "lr/collections/collectionsPageInitial", "domain": "prod", "realm": "us-east-1", "timestamp": "2024-08-19T11:26:23.80615Z"}}