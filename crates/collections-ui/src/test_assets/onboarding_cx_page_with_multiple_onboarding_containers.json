{"resource": {"containerList": [{"title": "What do you like to watch?", "subtitle": "Select movies and TV shows and we’ll use those to find videos we think you’ll like.", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt60ioRob21li4Rob21ljI6fMToxMVdCR1I3RDk4RUFWQyMjTU5RWEUzM1ZPTlNXWY0PjoJWMg==", "items": [], "tags": [], "analytics": {"refMarker": "hm_hom_p_KeLL5X_1", "ClientSideMetrics": "484|CnMKRU9uYm9hcmRpbmdUZXh0V2lkZ2V0U3RhdGljTm9JdGVtc1NwZWVkQnVtcEFsdGVybmF0ZUxpdmVEZWZhdWx0RGVmYXVsdBIQMToxMVdCR1I3RDk4RUFWQxoQMjpEWTVCMjgyRjQ3REQ3MCIGS2VMTDVYEkQKBGhvbWUSBGhvbWUiDnByaW9yaXR5Q2VudGVyKgAyJGZlNTI3ODFmLTA4YWYtNGRhNC04NmFiLTNjNTE2ZmRhNDA2ZRoDYWxsIgNhbGwqADIIY2Fyb3VzZWw6BlN0YXRpY0INU3RhdGljTm9JdGVtc0oaT25ib2FyZGluZ1ZhcmlhbnRTcGVlZEJ1bXBSC25vdEVudGl0bGVkWgBiClRleHRXaWRnZXRoAXIAejhzc2FyODVzNVJhZUVSdEhsbjR5bFlWS0M4TE50U1dwTnF3TDJfVGpKcmFJX24xUkpRVGtZVXc9PYIBA2FsbIoBAJIBAA=="}, "journeyIngressContext": "8|EgNhbGw=", "type": "TEXT_WIDGET"}, {"title": " ", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMTBNMlNCOUgwODBUNyMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "items": [{"title": "BMF - Season 1", "gti": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "transformItemId": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "synopsis": "The story of how <PERSON><PERSON><PERSON> \"<PERSON> Meech\" <PERSON><PERSON><PERSON> formed a criminal empire in Detroit known as the Black Mafia Family.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_romance"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c28334d512e6e209c5b293a984c824511d4fca9715b0fe85f4e39295083c8921.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/9b1b690399279aa4a2a43fe6154f0ac09790e11ec3b18dd219356922438eea93._UR1920,1080_RI_.png", "publicReleaseDate": 1637452800000, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_1"}, "refMarker": "hm_hom_p_THPXkc_brws_2_1", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "First episode free"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "First episode free", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.31bdc438-e953-4314-9d43-097d9fe746c6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_1"}, "refMarker": "hm_hom_p_THPXkc_brws_2_1", "journeyIngressContext": "8|EgNhbGw="}], "showName": "BMF", "cardType": "TITLE_CARD"}, {"title": "Avatar", "gti": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "transformItemId": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "synopsis": "In 2154, the U.S. military mines a valuable mineral from a distant world called Pandora. A paraplegic ex-Marine becomes an Avatar for Pandora, but when he sparks a romance with a female, he finds himself at the center of a war for control of Pandora.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_science_fiction", "av_genre_fantasy", "av_genre_action", "av_genre_adventure"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cb0a52213794c85f3bab31fd9f830c1608e760803170367ef81ffa625fb5535f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/7062a2ea3e7a8d83b6e2761101247b9fb9b3af330fe16f1fcfd574a8797711db._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/34e03a8849dda34699b6aa3d05ee7f0e6ebf6451c6eb8f3b53721fa99498b056.jpg", "publicReleaseDate": 1261094400000, "runtimeSeconds": 9700, "overallRating": 4.7, "totalReviewCount": 29018, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_2"}, "refMarker": "hm_hom_p_THPXkc_brws_2_2", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 3X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a2ba0520-e31d-8e9f-dab4-eca7fdb78aa8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_2"}, "refMarker": "hm_hom_p_THPXkc_brws_2_2", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "M3GAN", "gti": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "transformItemId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "synopsis": "A roboticist working on a life-like toy android named M<PERSON>GA<PERSON> takes in her orphaned niece. She pairs the two up, trying to solve for both issues...and it does not go as planned.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_horror", "av_genre_suspense", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ce339dce3ec9c8836c40a695cf5d8e8e9ae1ccb9c569a789e4e5b718f3f6c98c.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/f95c7caa5a871fab53b3aebb979ba2ff23daa4ff1959fcf8f4947361388db9d6._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5eb3c81466ddb8f0cfbce4bd2b4d1c795db817cf7c01bfa9416bac01656a6f24.jpg", "publicReleaseDate": 1672963200000, "runtimeSeconds": 6114, "overallRating": 4.3, "totalReviewCount": 1493, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_3"}, "refMarker": "hm_hom_p_THPXkc_brws_2_3", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b64cd816-81bd-49f0-a0a8-d2b8b95852d8", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_3"}, "refMarker": "hm_hom_p_THPXkc_brws_2_3", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "The Boys - Season 1", "gti": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "transformItemId": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "synopsis": "THE BOYS is an irreverent take on what happens when superheroes, who are as popular as celebrities, as influential as politicians and as revered as Gods, abuse their superpowers rather than use them for good. It's the powerless against the super powerful as The Boys embark on a heroic quest to expose the truth about “The Seven,” and their formidable Vought backing.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_comedy", "av_genre_drama", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a008b073c68fc474ed6397e84cb4436c5e26d3bfdbcc6fcc16e122c37351c944.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/atv-aps-images/encoded/THBY/en_US/COVER_ART/CLEAN/<PERSON>ie_Starlight._UR1920,1080_RI_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/a40deb56c1f0b2e8d354f078c4c00f4ebcaa7f0eb4f488af01d02d25af034916.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/8759018fc457e08b5d188c696cdfbdcb1311231b4572ab078c3ef09231c0211a.jpg", "publicReleaseDate": 1564099200000, "overallRating": 3.8, "totalReviewCount": 10758, "seasonNumber": 1, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_4"}, "refMarker": "hm_hom_p_THPXkc_brws_2_4", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2023"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5eb510bc-7578-d2dd-49d3-484070a96b52", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_4"}, "refMarker": "hm_hom_p_THPXkc_brws_2_4", "journeyIngressContext": "8|EgNhbGw="}], "showName": "The Boys", "cardType": "TITLE_CARD"}, {"title": "Yellowstone Season 1", "gti": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "transformItemId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "synopsis": "The <PERSON><PERSON>s fight to defend their ranch and way of life from an Indian reservation and land developers, as medical issues, political aspirations and deep secrets put strain on the family.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_western"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/1593fce889efbfa54b0d7f713be5ab4c7b946cd1e73e2e10f7edd7db6e107ed8.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/567eca6ca1d1310cfbe06f213169dc086a95b8eb3ee28beb82f9fd0d82ed14c3._UR1920,1080_RI_.png", "publicReleaseDate": 1534896000000, "overallRating": 3.9, "totalReviewCount": 775, "seasonNumber": 1, "numberOfSeasons": 5, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_5"}, "refMarker": "hm_hom_p_THPXkc_brws_2_5", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBE® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_5"}, "refMarker": "hm_hom_p_THPXkc_brws_2_5", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Yellowstone", "cardType": "TITLE_CARD"}, {"title": "Reacher - Season 1", "gti": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "transformItemId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "synopsis": "When retired Military Police Officer <PERSON> is arrested for a murder he did not commit, he finds himself in the middle of a deadly conspiracy full of dirty cops, shady businessmen and scheming politicians. With nothing but his wits, he must figure out what is happening in Margrave, Georgia. The first season of <PERSON> is based on the international bestseller, The Killing Floor by <PERSON>.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "TV-MA", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_drama", "av_genre_suspense"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/56d391fc82789f8a0f04649842743bc853bebf2619d45d85e3486fb25a8f2d66.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/394691d70d92cb4aaf3828c985fb050fff23fc4e0f483403c5b12450f6645a56._UR1920,1080_RI_.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/34dcf97468563cb2d03824a83f526036a3f595d293843fde2345a87cb91310b4.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/atv-aps-images/encoded/RCHR_S1/US/en_US/POSTER_ART/CLEAN/KRISTEN.jpg", "publicReleaseDate": 1643932800000, "overallRating": 4.7, "totalReviewCount": 10591, "seasonNumber": 1, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_6"}, "refMarker": "hm_hom_p_THPXkc_brws_2_6", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d62095f9-f33c-429b-a8a6-fd74c0461704", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_6"}, "refMarker": "hm_hom_p_THPXkc_brws_2_6", "journeyIngressContext": "8|EgNhbGw="}], "showName": "<PERSON>", "cardType": "TITLE_CARD"}], "tags": [], "analytics": {"refMarker": "hm_hom_p_THPXkc_2", "ClientSideMetrics": "472|CmIKNE9uYm9hcmRpbmdUaXRsZXNTcGVlZEJ1bXBBbHRlcm5hdGVMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTEwTTJTQjlIMDgwVDcaEDI6RFk5QjZBRTkwMUM1RDkiBlRIUFhrYxJECgRob21lEgRob21lIg5wcmlvcml0eUNlbnRlcioAMiRmZTUyNzgxZi0wOGFmLTRkYTQtODZhYi0zYzUxNmZkYTQwNmUaA2FsbCIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQg5Ccm93c2VTdHJhdGVneUoaT25ib2FyZGluZ1ZhcmlhbnRTcGVlZEJ1bXBSC25vdEVudGl0bGVkWgBiCk9uYm9hcmRpbmdoAnIAejhzc2FyODVzNVJhZUVSdEhsbjR5bFlWS0M4TE50U1dwTnF3TDJfVGpKcmFJX24xUkpRVGtZVXc9PYIBA2FsbIoBAJIBAA=="}, "offerType": "Mixed", "entitlement": "Mixed", "journeyIngressContext": "8|EgNhbGw=", "type": "ONBOARDING"}, {"title": " ", "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMTBNMlNCOUgwODBUNyMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "items": [{"title": "Tulsa King Season 1", "gti": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "transformItemId": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "synopsis": "After serving 25 years in prison, <PERSON> (<PERSON>) is unceremoniously exiled by his mob boss to set up shop in Oklahoma.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "TV-MA", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_ma", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f17883ece345f950af01279d7d15b0366789bb3522098db3d8f4d1604e428f38.png", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/51621d164f0d000fd3426b90111b5a04c173df788aa978422256c127150c2acc._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/be6de532e72d6dfb95def8be8cda2824138e316c8c6a9788603e1842e4d5b0a4.jpg", "publicReleaseDate": 1673136000000, "overallRating": 4.7, "totalReviewCount": 279, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_7"}, "refMarker": "hm_hom_p_THPXkc_brws_2_7", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Paramount+ or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMY® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d282c0bc-9ee3-4988-914b-5d831f856a8e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_7"}, "refMarker": "hm_hom_p_THPXkc_brws_2_7", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Tulsa King", "cardType": "TITLE_CARD"}, {"title": "Top Gun: <PERSON><PERSON><PERSON>", "gti": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "transformItemId": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "synopsis": "After thirty years, <PERSON><PERSON><PERSON> is still pushing the envelope as a top naval aviator, but must confront ghosts of his past when he leads TOP GUN's elite graduates on a mission that demands the ultimate sacrifice from those chosen to fly it.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_drama"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg_13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a99fdd0071f7598f0e438129970c8647dfb1528bd03e8b783211268214a7e1ae.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/5099b0bb5c1a20bc2f43fe3a4934c94412c842a595deb5220d70a7ee959aae29._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/1e4598108574946b2227be0abe74b84ac6ae419022e04b53f4f4e96d593f3b40.jpg", "publicReleaseDate": 1653609600000, "runtimeSeconds": 7535, "overallRating": 4.7, "totalReviewCount": 115455, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_8"}, "refMarker": "hm_hom_p_THPXkc_brws_2_8", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCAR® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e6867686-3989-4e51-ad7b-19ff5edea2f4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_8"}, "refMarker": "hm_hom_p_THPXkc_brws_2_8", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "Black Adam", "gti": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "transformItemId": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "synopsis": "The story of the DC superhero comes to the big screen in this thrilling action-adventure starring <PERSON><PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "13+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_fantasy", "av_genre_action", "av_genre_adventure", "av_genre_science_fiction"], "maturityRatingString": "vcc_maturity_rating_amazon_maturity_rating_13_plus", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8512ded1639be52f10cd3a86dfa1c15431f0e0fdcc8199a6fcd9085e0dbc993f.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/bfe723f6aaa6e49a8801f9e88cfc7abb1c7e48f257f34bfb50003452f829bbc2._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/48c347301363a0af6d9744497acd376336128e278c20fcc103bc7408540748c2.jpg", "publicReleaseDate": 1666310400000, "runtimeSeconds": 7197, "overallRating": 4.2, "totalReviewCount": 25895, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_9"}, "refMarker": "hm_hom_p_THPXkc_brws_2_9", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5a7724bb-c4e5-44d6-8dde-17abce9681c1", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_9"}, "refMarker": "hm_hom_p_THPXkc_brws_2_9", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}, {"title": "<PERSON>'s <PERSON> - Season 1", "gti": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "transformItemId": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "synopsis": "When CIA analyst <PERSON> stumbles upon a suspicious series of bank transfers his search for answers pulls him from the safety of his desk job and catapults him into a deadly game of cat and mouse throughout Europe and the Middle East, with a rising terrorist figurehead preparing for a massive attack against the US and her allies.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "TV-14", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_action", "av_genre_drama", "av_genre_suspense"], "maturityRatingString": "vcc_maturity_rating_tvpg_tv_14", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d42dd756738a5fe64ad1affe067d682c7f484f6b49dbd26955b5161c2578f08b.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/6438b5bd7aeeabf01229914f3e745321c9aa7c04daefbbef3d5f5c599c3d084d._UR1920,1080_RI_.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/58684ed93429974928a5299eb46f248730223d2a7d8b6e155d18b40072d6b569.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/156eb6ba535126a24c4630c5d08793005c8acbd7d51ca9eef55e2fc0250dcf71.png", "publicReleaseDate": -2034806400000, "overallRating": 4.6, "totalReviewCount": 20532, "seasonNumber": 1, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_10"}, "refMarker": "hm_hom_p_THPXkc_brws_2_10", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYS® 1X nominee in 2020"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3aae96d6-2f06-c508-8773-90cdfdce7e44", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_10"}, "refMarker": "hm_hom_p_THPXkc_brws_2_10", "journeyIngressContext": "8|EgNhbGw="}], "showName": "<PERSON>'s <PERSON>", "cardType": "TITLE_CARD"}, {"title": "The Summer I Turned Pretty - Season 1", "gti": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "transformItemId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "synopsis": "<PERSON><PERSON> is about to turn 16, and she’s headed to her favorite place in the world, Cousins Beach, to spend the summer with her family and the Fishers. <PERSON><PERSON>’s grown up a lot over the past year, and she has a feeling that this summer is going to be different than all the summers before. The Summer I Turned Pretty is based on the book by <PERSON>, who is creator and executive producer.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16+", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["av_genre_drama", "av_genre_romance", "av_genre_young_adult_audience"], "maturityRatingString": "vcc_maturity_rating_amazon_maturity_rating_16_plus", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b7a291cc0a1b1860d0f20e355964aa221d91bdd0593fec62f45beb5ad60529ca.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/atv-aps-images/encoded/SITP_S1/US/en_US/COVER_ART/CLEAN/Image04._UR1920,1080_RI_.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/b76400003c5bc461334996bbda76e01176c952d40642158731ead5fbf342ea9a.png", "providerLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/7e5ebe135ef7e9f912cf8c29b6c9f4e22cab8ff78c0584a45c129cc1df33336e.png", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/30fbbc1634e6f70c488b55ea96de20e9a88224544ac4503df204518173538ae5.jpg", "publicReleaseDate": 1655424000000, "overallRating": 4.6, "totalReviewCount": 1974, "seasonNumber": 1, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_11"}, "refMarker": "hm_hom_p_THPXkc_brws_2_11", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_11"}, "refMarker": "hm_hom_p_THPXkc_brws_2_11", "journeyIngressContext": "8|EgNhbGw="}], "showName": "The Summer I Turned Pretty", "cardType": "TITLE_CARD"}, {"title": "<PERSON> and the Sorcerer's Stone", "gti": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "transformItemId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "synopsis": "Based on the wildly popular <PERSON><PERSON><PERSON><PERSON>'s book about a young boy who on his eleventh birthday discovers, he is the orphaned boy of two powerful wizards and has unique magical powers.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["av_genre_kids", "av_genre_adventure", "av_genre_fantasy"], "maturityRatingString": "vcc_maturity_rating_mpaa_pg", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d6077c75475b8086e6eb7f3994ff395af0a34a5543e30e373bd7beb4b6c02dce.jpg", "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/477b552bc91175c11daa52d1160e6e8736828285e35f01f6c86d24a5bf8adabb._UR1920,1080_RI_.jpg", "poster2x3Image": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/187772b81de30443601c19fca2c64a0bcb8154f1116b1f17e24b8e3ab0f76682.jpg", "publicReleaseDate": 1005868800000, "runtimeSeconds": 8778, "overallRating": 4.8, "totalReviewCount": 89295, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_12"}, "refMarker": "hm_hom_p_THPXkc_brws_2_12", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARS® 3X nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_p_THPXkc_brws_2_12"}, "refMarker": "hm_hom_p_THPXkc_brws_2_12", "journeyIngressContext": "8|EgNhbGw="}], "cardType": "TITLE_CARD"}], "tags": [], "analytics": {"refMarker": "hm_hom_p_THPXkc_2", "ClientSideMetrics": "472|CmIKNE9uYm9hcmRpbmdUaXRsZXNTcGVlZEJ1bXBBbHRlcm5hdGVMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTEwTTJTQjlIMDgwVDcaEDI6RFk5QjZBRTkwMUM1RDkiBlRIUFhrYxJECgRob21lEgRob21lIg5wcmlvcml0eUNlbnRlcioAMiRmZTUyNzgxZi0wOGFmLTRkYTQtODZhYi0zYzUxNmZkYTQwNmUaA2FsbCIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQg5Ccm93c2VTdHJhdGVneUoaT25ib2FyZGluZ1ZhcmlhbnRTcGVlZEJ1bXBSC25vdEVudGl0bGVkWgBiCk9uYm9hcmRpbmdoAnIAejhzc2FyODVzNVJhZUVSdEhsbjR5bFlWS0M4TE50U1dwTnF3TDJfVGpKcmFJX24xUkpRVGtZVXc9PYIBA2FsbIoBAJIBAA=="}, "offerType": "Mixed", "entitlement": "Mixed", "journeyIngressContext": "8|EgNhbGw=", "type": "ONBOARDING"}, {"button": {"target": "Landing", "pageId": "home", "pageType": "home", "analytics": {}, "refMarker": "null", "text": "Done"}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6zioRob21li4Rob21ljI6eMToxOVI1RFRMU1lHVEZOIyNNTlFYRTMzVk9OU1dZjQ-OglYy", "items": [], "tags": [], "analytics": {"refMarker": "hm_hom_p_K7wFJE_3", "ClientSideMetrics": "512|Cn4KUU9uYm9hcmRpbmdBY3Rpb25CdXR0b25Db2xsZWN0aW9uU3RhdGljTm9JdGVtc1NwZWVkQnVtcEFsdGVybmF0ZUxpdmVEZWZhdWx0RGVmYXVsdBIPMToxOVI1RFRMU1lHVEZOGhAyOkRZODlGMDFCRUE1MkE4IgZLN3dGSkUSRAoEaG9tZRIEaG9tZSIOcHJpb3JpdHlDZW50ZXIqADIkZmU1Mjc4MWYtMDhhZi00ZGE0LTg2YWItM2M1MTZmZGE0MDZlGgNhbGwiA2FsbCoAMghjYXJvdXNlbDoGU3RhdGljQg1TdGF0aWNOb0l0ZW1zShpPbmJvYXJkaW5nVmFyaWFudFNwZWVkQnVtcFILbm90RW50aXRsZWRaAGIWQWN0aW9uQnV0dG9uQ29sbGVjdGlvbmgDcgB6OHNzYXI4NXM1UmFlRVJ0SGxuNHlsWVZLQzhMTnRTV3BOcXdMMl9UakpyYUlfbjFSSlFUa1lVdz09ggEDYWxsigEAkgEA"}, "journeyIngressContext": "8|EgNhbGw=", "type": "ACTION_BUTTON_COLLECTION"}], "subNav": [], "pageMetadata": {"title": "", "logoImage": {}, "locationDependentPage": false, "showVoiceFilters": false, "persistentTitleOrLogo": false}, "isPriorityCenter": true}, "metadata": {"requestId": "ssar85s5RaeERtHln4ylYVKC8LNtSWpNqwL2_TjJraI_n1RJQTkYUw==", "requestedTransformId": "lr/collections/collectionsPageInitial", "domain": "prod", "realm": "us-east-1", "timestamp": "2024-07-13T13:07:42.262545Z"}}