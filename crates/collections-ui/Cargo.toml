[package]
name = "collections-ui"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
network-parser.workspace = true
network-parser-derive.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
serde_json.workspace = true
serde.workspace = true
common-transform-types.workspace = true
location.workspace = true
router.workspace = true
containers.workspace = true
fableous.workspace = true
mockall.workspace = true
media-background.workspace = true
navigation-menu.workspace = true
title-details.workspace = true
auth.workspace = true
profile-manager.workspace = true
lrc-image.workspace = true
serde_path_to_error.workspace = true
transition-executor.workspace = true
chrono.workspace = true
log.workspace = true
hero.workspace = true
container-list.workspace = true
mockall_double.workspace = true
container-types.workspace = true
app-config.workspace = true
acm-config.workspace = true
network.workspace = true
cache.workspace = true
beekeeper.workspace = true
cross-app-events.workspace = true
resiliency-store.workspace = true
cfg-test-attr-derive.workspace = true
app-events.workspace = true
cacheable-derive.workspace = true
contextual-menu.workspace = true
contextual-menu-types.workspace = true
modal-manager.workspace = true
educational-cx.workspace = true
cross-benefit-discovery.workspace = true
rust-features.workspace = true
app-reporting.workspace = true
taps-parameters.workspace = true
watch-modal.workspace = true
strum.workspace = true
popups.workspace = true
in-app-survey.workspace = true
collections-ui-constants.workspace = true
collections-ui-signals.workspace = true
details-integration.workspace = true
reduce-steps-to-mlp.workspace = true
storage-events.workspace = true
discovery-assistant.workspace = true
payment-risk-message.workspace = true
settings-manager.workspace = true
toast-manager.workspace = true
two-column-page.workspace = true
title-reaction-buttons.workspace = true
clickstream.workspace = true
personalization-feedback-service.workspace = true
onboarding-lifecycle-service.workspace = true
current-time.workspace = true
playback-navigation.workspace = true
consumption-only.workspace = true
collection-types.workspace = true
sports-edge-parser.workspace = true
sports-edge-types.workspace = true
container-item-types.workspace = true
linear-common.workspace = true
uuid.workspace = true
metrics-client.workspace = true
liveliness-types.workspace = true
sports-favorites-utils.workspace = true
explicit-signals-service.workspace = true
firetv.workspace = true

[dev-dependencies]
rstest.workspace = true
insta.workspace = true
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
    "mock_timer",
    "lifetime_apis",
] }
consent.workspace = true
common-transform-types = { workspace = true, features = ["test_utils"] }
container-types = { workspace = true, features = ["test_utils", "example_data"] }
testing_logger.workspace = true
network = { workspace = true, features = ["test_utils", "mock_network"] }
navigation-menu = { workspace = true, features = ["test_utils"] }
serial_test.workspace = true
beekeeper = { workspace = true, features = ["mocks"] }
synchronized-state-store = { workspace = true, features = ["test-utils"] }
rust-features.workspace = true
in-app-survey = { workspace = true, features = ["test_utils"] }
settings-manager.workspace = true
hero = { workspace = true, features = ["test_utils"] }
watch-modal = { workspace = true, features = ["test_utils"] }
app-config = { workspace = true, features = ["test_utils"] }
cache = { workspace = true, features = ["test-utils"] }
contextual-menu = { workspace = true, features = ["test_utils"] }
transition-executor = { workspace = true, features = ["test_utils"] }
clickstream = { workspace = true, features = ["test_utils"] }
onboarding-lifecycle-service = { workspace = true, features = ["test_utils"] }
mock_instant.workspace = true
container-item-types = { workspace = true, features = ["mock_data"] }
educational-cx = { workspace = true, features = ["test_utils"] }
metrics-client = { workspace = true, features = ["test_utils"] }
sports-favorites-utils = { workspace = true, features = ["test_utils"] }
firetv = { workspace = true, features = ["test_utils"] }
explicit-signals-service = { workspace = true, features = ["test_utils"] }

[features]
test_utils = ["sports-favorites-utils/test_utils"]
example_data = ["container-types/example_data", "sports-favorites-utils/test_utils"]
rust_details = ["transition-executor/rust_details"]
debug_impl = ["common-transform-types/debug_impl", "beekeeper/debug_impl"]

[lints]
workspace = true

[[example]]
name = "collections"
crate-type = ["cdylib"]
path = "examples/collections.rs"

[[example]]
name = "collections_all_containers"
crate-type = ["cdylib"]
path = "examples/collections_all_containers.rs"
required-features = ["example_data"]

[[example]]
name = "collections_stubbed"
crate-type = ["cdylib"]
path = "examples/collections_stubbed.rs"
required-features = ["example_data"]

[[example]]
name = "collections_page_skeleton"
crate-type = ["cdylib"]
path = "examples/collections_page_skeleton.rs"

[[example]]
name = "grid"
crate-type = ["cdylib"]
path = "examples/grid.rs"