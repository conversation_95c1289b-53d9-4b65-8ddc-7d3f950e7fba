use crate::location_util::update_linear_page_stack;
use chrono::Utc;
use fableous::progress_bar::ProgressBarVariant;
use fableous::tiles::common_string_ids::{AIV_BLAST_LIVE_BADGE_TEXT, AV_LRC_LIVE_TV_ON_NOW};
use fableous::tiles::tile::TileProgressBarProps;
use ignx_compositron::app::wasm_app::TaskGuard;
use ignx_compositron::compose;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::*;
use ignx_compositron::text::{LocalizedText, TextContent};
use linear_common::util::datetime_util::get_time_remaining;
use linear_common::util::localization_util::localize_hierarchy_context;
use linear_common::util::schedule_util::get_progress_from_ms;
use playback_carousel::title::{
    PlaybackTitleCard, PlaybackTitleCardBadge, PlaybackTitleCarousel, PlaybackTitleCarouselProps,
};
use playback_carousel::PlaybackTitleCarouselCaller;
use playback_core::PlaybackObserver;
use playback_feature_manager::Feature;
use playback_feature_manager::PanelController;
use playback_feature_manager::{FeatureControllers, FeatureName, Panel, PanelGroup, PanelName};
use playback_feature_manager::{Tab, TabGroup};
use playback_metadata_provider::network::types::generic_carousel::GenericCarouselItem;
use playback_metadata_provider::network::types::on_now::Consent;
use playback_metadata_provider::network::types::on_now::LinearAiring;
use playback_metadata_provider::network::types::on_now::{OnNowCarousel, OnNowCarouselItem};
use playback_metadata_provider::types::ResourceName;
use playback_metadata_provider::types::ResourceResponse;
use playback_metadata_provider::RequiresMetadataResources;
use playback_src::IntrinsicContentConfig;
use playback_src::StreamingType;
use playback_src::{LinearSrc, LinearStartMode, PlaybackContent, PlaybackPageOrigin, PlaybackSrc};
use playback_ui_kit::playback_controller::PlaybackProxy;
use router::hooks::use_router;
use std::collections::HashSet;
use std::rc::Rc;
use std::time::Duration;

const LINEAR_PLAYBACK_PAGE: &str = "LinearPlaybackPage";
const ITEM_NAVIGATION_HINT_TTS: &str = "AV_LRC_PLAYBACK_ON_NOW_CARD_NAVIGATION_TTS";

pub struct OnNowFeature {
    ctx: AppContext,
    controllers: FeatureControllers,
    panel_controller: Option<PanelController>,
    carousel_items: StoredValue<Vec<ReactiveCarouselItem>>,
    cards: RwSignal<Vec<PlaybackTitleCard>>,
    current_time_signal: RwSignal<i64>,
    current_time_task_guard: Option<TaskGuard>,
    playing_title_id: Signal<Option<String>>,
    carousel_show_debounce: StoredValue<i64>,
}

impl OnNowFeature {
    pub fn new(ctx: AppContext, controllers: FeatureControllers) -> Self {
        let content = controllers.content_controller;
        let playing_title_id = Signal::derive(ctx.scope(), move || {
            content.with(|content| match content.as_ref().map(|c| &c.src) {
                Some(PlaybackSrc::Linear(src)) => Some(src.title_id.clone()),
                _ => None,
            })
        });
        Self {
            ctx: ctx.clone(),
            controllers,
            panel_controller: None,
            carousel_items: store_value(ctx.scope(), vec![]),
            cards: create_rw_signal(ctx.scope(), vec![]),
            current_time_signal: create_rw_signal(ctx.scope(), Utc::now().timestamp_millis()),
            current_time_task_guard: None,
            playing_title_id,
            carousel_show_debounce: store_value(ctx.scope(), 0),
        }
    }

    fn set_carousel_data(&mut self, carousel: Option<OnNowCarousel>) {
        let carousel_title = carousel
            .as_ref()
            .and_then(|c| c.base.carousel_title.clone());
        let carousel_items: Vec<_> = carousel
            .map(|c| c.carousel_items)
            .unwrap_or_default()
            .into_iter()
            // TODO: LEX3PLR-1477 Handle stations requiring VPPA. Filter out for now.
            .filter(|item| item.consent.is_none())
            .collect();

        // When receive empty carousel data, do nothing. Keep existing carousel if exist.
        if carousel_items.is_empty() {
            metric!("Linear.UnexpectedEvent", 1, "pageType" => LINEAR_PLAYBACK_PAGE, "eventName" => "PlaybackOnNowCarouselMissingData");
            return;
        }

        let is_same_content = self.carousel_items.with_value(|old_items| {
            if old_items.len() != carousel_items.len() {
                return false;
            }
            old_items
                .iter()
                .zip(carousel_items.iter())
                .all(|(old, new)| old.base.title_id == new.base.title_id)
        });
        // When receive the same carousel items, update items schedule signal with fresh data
        if is_same_content {
            self.carousel_items.with_value(|old_items| {
                old_items
                    .iter()
                    .zip(carousel_items)
                    .for_each(|(old, new)| old.schedule.set(new.schedule));
            });
            return;
        }

        // Otherwise, build carousel cards from new carousel items.
        let carousel_items: Vec<_> = carousel_items
            .into_iter()
            .map(|item| ReactiveCarouselItem::new(self.ctx.scope(), item))
            .collect();
        let cards = carousel_items
            .iter()
            .map(|item| {
                build_playback_card(
                    self.ctx.scope(),
                    item,
                    self.current_time_signal.read_only(),
                    self.playing_title_id,
                )
            })
            .collect();

        // Hide previous carousel if exist to rerender whole carousel and reset focus.
        if let Some(panel_controller) = self.panel_controller.as_ref() {
            panel_controller.hide();
        }
        self.carousel_items.set_value(carousel_items);
        self.cards.set(cards);
        self.register_panel(carousel_title);
        if let Some(panel_controller) = self.panel_controller.as_ref() {
            panel_controller.show();
            self.schedule_current_time_update();
        }
    }

    fn register_panel(&mut self, carousel_title: Option<String>) {
        if self.panel_controller.is_some() {
            return;
        }

        let card_event_handler = CardEventsHandler {
            scope: self.ctx.scope(),
            controllers: self.controllers.clone(),
            carousel_items: self.carousel_items,
            playing_title_id: self.playing_title_id,
        };
        let title = carousel_title.map_or(
            TextContent::LocalizedText("AV_LRC_ON_NOW_TTS".into()),
            TextContent::String,
        );
        let cards = self.cards;
        let carousel_show_debounce = self.carousel_show_debounce;
        let panel = Panel {
            panel_name: PanelName::OnNow,
            group: PanelGroup::Btf,
            feature_name: self.feature_name(),
            auto_hide_timeout: Some(Duration::from_secs(10)),
            tab: Some(Tab {
                feature_name: self.feature_name(),
                panel_name: PanelName::OnNow,
                group: TabGroup::Btf,
                title: title.clone().into(),
                icon_name: None,
                order: None,
                accessibility: None,
            }),
            component: Box::new(move |ctx| {
                let card_event_handler = card_event_handler.clone();
                let on_card_select = Rc::new(move |index| {
                    card_event_handler.on_card_select(index);
                });
                let title = title.clone();

                // Debounce render call to count rerender as one render
                carousel_show_debounce.update_value(|prev| {
                    let now = Utc::now().timestamp_millis();
                    if now - *prev > 200 {
                        metric!("ComponentAction.Count", 1, "pageType" => LINEAR_PLAYBACK_PAGE, "componentName" => "PlaybackOnNowCarousel", "actionName" => "Show");
                    }
                    *prev = now;
                });
                compose! {
                    Row() {
                        PlaybackTitleCarousel(
                            cards,
                            on_card_select
                        )
                    }
                    .accessibility_context_message(title)
                    .into_any()
                }
            }),
            on_hidden: None,
            on_shown: None,
        };

        match self.controllers.panels_controller.register(panel) {
            Ok(panel_controller) => {
                self.panel_controller = Some(panel_controller);
            }
            Err(error) => {
                log::error!(
                    "{}.onPanelRegistrationFailed: {:?}",
                    LINEAR_PLAYBACK_PAGE,
                    error
                );
            }
        }
    }

    fn schedule_current_time_update(&mut self) {
        if self.current_time_task_guard.is_some() {
            return;
        }
        let current_time_signal = self.current_time_signal;
        let task_guard = self
            .ctx
            .schedule_repeating_task(Duration::from_secs(10), move || {
                current_time_signal.try_set(Utc::now().timestamp_millis());
            });
        self.current_time_task_guard = Some(task_guard);
    }
}

/**
 * Carousel item with schedule signal to allow schedule update
 */
struct ReactiveCarouselItem {
    base: GenericCarouselItem,
    schedule: RwSignal<Vec<LinearAiring>>,
    consent: Option<Consent>,
}

impl ReactiveCarouselItem {
    fn new(scope: Scope, item: OnNowCarouselItem) -> Self {
        Self {
            base: item.base,
            schedule: create_rw_signal(scope, item.schedule),
            consent: item.consent,
        }
    }
}

#[derive(Clone)]
struct CardEventsHandler {
    scope: Scope,
    controllers: FeatureControllers,
    carousel_items: StoredValue<Vec<ReactiveCarouselItem>>,
    playing_title_id: Signal<Option<String>>,
}

impl CardEventsHandler {
    pub fn on_card_select(&self, index: usize) {
        let selected_title_id = self
            .carousel_items
            .with_value(|items| items.get(index).map(|item| item.base.title_id.clone()));
        if self.playing_title_id.get_untracked() == selected_title_id {
            return;
        }
        let Some(title_id) = selected_title_id else {
            return;
        };
        metric!("ComponentAction.Count", 1, "pageType" => "LinearPlaybackPage", "componentName" => "PlaybackOnNowCarousel", "actionName" => "Select");
        let src = PlaybackSrc::Linear(LinearSrc {
            title_id: title_id.clone(),
            start_mode: LinearStartMode::AtLive,
            client_id: None,
        });
        let content = PlaybackContent::new(src).origin(PlaybackPageOrigin::OnNow.into());
        if self
            .controllers
            .content_controller
            .try_set(Some(content))
            .is_none()
        {
            let router = use_router(self.scope);
            router.mutate_history(Box::new(move |history| {
                update_linear_page_stack(history, title_id);
            }));
        }
    }
}

fn build_playback_card(
    scope: Scope,
    item: &ReactiveCarouselItem,
    current_time_signal: ReadSignal<i64>,
    playing_title_id: Signal<Option<String>>,
) -> PlaybackTitleCard {
    let schedule = item.schedule;

    // Create signal for current airing as current time changes
    let current_airing = create_rw_signal(scope, None);
    create_effect(scope, move |prev_start_time| {
        let current_time = current_time_signal.get();
        // Get current airing from current time and schedule signals
        let (new_airing, new_start_time) = schedule.with(|schedule| {
            let airing = schedule
                .iter()
                .find(|a| a.start_time <= current_time && current_time < a.end_time)
                .or_else(|| schedule.last());

            // Return airing if airing start time is different from previous
            let new_start_time = airing.map(|a| a.start_time);
            if prev_start_time.is_none_or(|prev| prev != new_start_time) {
                (Some(airing.cloned()), new_start_time)
            } else {
                (None, new_start_time)
            }
        });

        if let Some(new_airing) = new_airing {
            current_airing.set(new_airing);
        }
        new_start_time
    });

    let image_url = Signal::derive(scope, move || {
        current_airing.with(|airing| {
            airing
                .as_ref()
                .and_then(|airing| airing.images.as_ref())
                .and_then(|images| images.cover_image.clone())
        })
    });

    let hierarchy_text = Signal::derive(scope, move || {
        current_airing.with(|airing| {
            airing
                .as_ref()
                .and_then(|a| a.hierarchy_context.as_ref())
                .and_then(|h| localize_hierarchy_context(h.season, h.episode))
        })
    });

    let rating = Signal::derive(scope, move || {
        current_airing.with(|airing| {
            airing
                .as_ref()
                .and_then(|a| a.rating_info.as_ref())
                .map(|r| TextContent::String(r.rating_display_text.clone()))
        })
    });

    let badge = Signal::derive(scope, move || {
        current_airing.with(|airing| {
            // Render LIVE badge if airing attributes contain "LIVE"
            let should_render_live_badge = airing
                .as_ref()
                .map(|a| {
                    a.airing_attributes
                        .as_ref()
                        .is_some_and(|attrs| attrs.iter().any(|att| att == "LIVE"))
                })
                .unwrap_or_default();
            Some(if should_render_live_badge {
                PlaybackTitleCardBadge::Live
            } else {
                PlaybackTitleCardBadge::OnNow
            })
        })
    });

    let progress_signal = create_rw_signal(scope, 0.0);
    create_effect(scope, move |_| {
        current_airing.with(|airing| {
            let (start_time, end_time) = airing
                .as_ref()
                .map(|airing| (airing.start_time, airing.end_time))
                .unwrap_or_default();
            let progress = get_progress_from_ms(current_time_signal.get(), start_time, end_time)
                .unwrap_or_default();
            progress_signal.set(progress);
        });
    });

    // Render progress bar if there is airing in the schedule
    let progress_bar = if !schedule.with_untracked(|s| s.is_empty()) {
        Some(TileProgressBarProps {
            variant: ProgressBarVariant::OnAir.into(),
            progress: progress_signal,
            tts_total_minutes: create_rw_signal(scope, None),
        })
    } else {
        None
    };

    let title_id = item.base.title_id.clone();
    let is_playing = Signal::derive(scope, move || {
        playing_title_id.with(|playing| playing.as_ref().is_some_and(|p| p == &title_id))
    });

    let station_name = item.base.card_title.clone();
    let accessibility_descriptions = Signal::derive(scope, move || {
        let mut messages = vec![TextContent::String(station_name.clone())];
        current_airing.with(|airing| {
            // Airing title if not same as station name
            if let Some(airing) = airing {
                if let Some(title) = &airing.title {
                    if title != &station_name {
                        messages.push(TextContent::String(title.clone()));
                    }
                }
            }

            // Badge text
            if let Some(badge_text) = match badge.get() {
                Some(PlaybackTitleCardBadge::Live) => Some(AIV_BLAST_LIVE_BADGE_TEXT),
                Some(PlaybackTitleCardBadge::OnNow) => Some(AV_LRC_LIVE_TV_ON_NOW),
                _ => None,
            } {
                messages.push(TextContent::LocalizedText(badge_text.into()))
            }
            // Time remaining
            if let Some(airing) = airing {
                messages.push(get_time_remaining(
                    current_time_signal.get(),
                    airing.end_time,
                    true,
                ))
            }

            // Episode hierarchy text
            if let Some(hierarchy_text) = hierarchy_text.get() {
                messages.push(hierarchy_text);
            }
            // Rating text
            if let Some(rating) = rating.get() {
                messages.push(rating);
            }
        });
        messages
    });

    PlaybackTitleCard {
        id: item.base.title_id.clone(),
        title: TextContent::String(item.base.card_title.clone()).into(),
        subtitle: hierarchy_text.into(),
        subtitle_suffix: rating.into(),
        image_url: image_url.into(),
        badge: badge.into(),
        progress_bar,
        is_playing: is_playing.into(),
        provider_logo: None,
        accessibility_descriptions: Some(accessibility_descriptions.into()),
        accessibility_hint: Some(TextContent::LocalizedText(LocalizedText::new(
            ITEM_NAVIGATION_HINT_TTS,
        ))),
    }
}

impl Feature for OnNowFeature {
    fn feature_name(&self) -> FeatureName {
        FeatureName::OnNow
    }

    fn initialize(&mut self) {}

    fn is_feature_enabled(&self) -> bool {
        true
    }

    fn reset(&mut self) {
        if let Some(panel_controller) = self.panel_controller.as_ref() {
            panel_controller.hide();
        }
    }
}

impl RequiresMetadataResources for OnNowFeature {
    fn on_resource(&mut self, resource_response: &ResourceResponse) {
        let data = match resource_response {
            ResourceResponse::OnNow(Ok(on_now_data)) => {
                if on_now_data.is_none() {
                    log::error!(
                        "{}.onMissingCarouselData: empty response",
                        LINEAR_PLAYBACK_PAGE
                    );
                }
                on_now_data.as_ref().and_then(|data| data.carousel.clone())
            }
            ResourceResponse::OnNow(Err(error)) => {
                log::error!(
                    "{}.onMissingCarouselData: {:?}",
                    LINEAR_PLAYBACK_PAGE,
                    error.message
                );
                None
            }
            _ => {
                log::error!("{}.onInvalidCarouselData", LINEAR_PLAYBACK_PAGE);
                None
            }
        };
        self.set_carousel_data(data);
    }

    fn get_required_resources_on_demand(
        &self,
        _content_config: IntrinsicContentConfig,
    ) -> HashSet<ResourceName> {
        HashSet::new()
    }

    fn get_required_resources_on_playback_start(
        &self,
        content_config: IntrinsicContentConfig,
    ) -> HashSet<ResourceName> {
        if content_config.streaming_type() == StreamingType::Linear {
            return HashSet::from([ResourceName::OnNow]);
        }
        HashSet::new()
    }
}

impl PlaybackObserver for OnNowFeature {}

impl PlaybackProxy for OnNowFeature {}

pub fn create_on_now_feature(ctx: AppContext, controllers: FeatureControllers) -> OnNowFeature {
    OnNowFeature::new(ctx, controllers)
}

#[cfg(test)]
mod tests {
    use super::*;
    use app_config::test_utils::MockAppConfigBuilder;
    use auth::MockAuth;
    use common_transform_types::container_items::ShowContext;
    use cross_app_events::create_serde_map;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::reactive::store_value;
    use ignx_compositron::test_utils::assert_node_exists;
    use ignx_compositron::text::{LocalizedText, SubstitutionParameters};
    use linear_common::constants::LIVE_TV_LAST_PLAYBACK_TITLE_PARAM;
    use location::Location;
    use location::{PageType, RustPage};
    use playback_core::provide_playback_hooks;
    use playback_feature_manager::use_panels_controller;
    use playback_machine::PlaybackMachineContext;
    use playback_metadata_provider::network::types::generic_carousel::{
        GenericCarousel, GenericCarouselItem,
    };
    use playback_metadata_provider::network::types::images::Images;
    use playback_metadata_provider::network::types::on_now::{LinearAiring, OnNowData};
    use playback_metadata_provider::network::types::regulatory_rating::RatingInfo;
    use playback_metadata_provider::types::ResourceError;
    use playback_metadata_provider::{provide_metadata_controller, MetadataProvider};
    use playback_src::MinimalSrc;
    use playback_src::{LinearStartMode, NetworkClient, StreamingConfig};
    use playback_ui_kit::background_image::provide_background_image;
    use playback_ui_kit::exit::provide_exit_playback;
    use playback_ui_kit::playback_mode::{provide_playback_mode, PlaybackMode};
    use playback_ui_kit::playback_src::provide_content;
    use router::hooks::setup_mock_routing_ctx;
    use router::{MockRouting, RoutingContext};
    use rstest::*;
    use serde_json::{Map, Value};
    use std::collections::HashMap;
    use std::rc::Rc;

    fn setup_controllers(ctx: &AppContext) -> FeatureControllers {
        let network_context = NetworkClient::new_context();
        network_context
            .expect()
            .returning(|_| NetworkClient::default());
        store_value(ctx.scope(), network_context);
        PlaybackMachineContext::test_builder(ctx.scope())
            .build()
            .provide_on(ctx.scope());
        MockAuth::new_without_params(ctx.scope()).provide(ctx.scope());
        MockAppConfigBuilder::new().build_into_context(ctx.scope());
        setup_mock_routing_ctx(ctx.scope, |_| {});
        provide_playback_mode(ctx.scope(), PlaybackMode::Chrome(None));
        provide_playback_hooks(ctx.scope());
        provide_metadata_controller(
            ctx.scope(),
            store_value(ctx.scope(), Box::new(MetadataProvider::new(ctx))),
        );
        provide_exit_playback(ctx.scope(), move |_| {});
        provide_background_image(ctx.scope());
        provide_exit_playback(ctx.scope(), |_| {});

        FeatureControllers::new(ctx, "test".to_string())
    }

    fn setup(ctx: &AppContext) -> OnNowFeature {
        OnNowFeature::new(ctx.clone(), setup_controllers(ctx))
    }

    #[test]
    fn it_implements_playback_feature() {
        launch_only_app_context(|ctx| {
            let feature = setup(&ctx);

            assert_eq!(feature.feature_name(), FeatureName::OnNow);
            assert_eq!(feature.is_feature_enabled(), true);
        })
    }

    #[test]
    fn it_renders_playback_carousel() {
        launch_test(
            |ctx| {
                let mut feature = setup(&ctx);
                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(build_on_now_data()))));

                assert_eq!(feature.carousel_show_debounce.get_value(), 0);
                provide_context(ctx.scope(), Rc::new(feature));

                let panel_signal =
                    use_panels_controller(&ctx).get_active_panel_in_group(&PanelGroup::Btf);
                let panel = panel_signal.get_untracked().unwrap();

                compose! {
                    Memo(item_builder: Box::new(move |ctx| {
                        let panel = &panel.upgrade();
                        if let Some(panel) = panel {
                            let component_fn = &panel.component;
                            Some(component_fn(ctx))
                        } else {
                            None
                        }
                    }))
                }
            },
            |scope, mut test_loop| {
                let tree = test_loop.tick_until_done();
                assert_node_exists!(tree.find_by_test_id("btf-card-station1-test-id"));
                assert_node_exists!(tree.find_by_test_id("btf-card-station2-test-id"));

                let feature = expect_context::<Rc<OnNowFeature>>(scope);
                assert!(feature.carousel_show_debounce.get_value() > 0);
            },
        )
    }

    #[test]
    fn it_builds_playing_title_id_signal() {
        launch_only_app_context(|ctx| {
            let content_controller = create_rw_signal(ctx.scope(), None);
            provide_content(ctx.scope(), content_controller);
            let feature = setup(&ctx);

            let playing_title_id = feature.playing_title_id;
            assert_eq!(playing_title_id.get_untracked(), None);

            // Set to linear content
            content_controller.set(Some(PlaybackContent::new(PlaybackSrc::Linear(LinearSrc {
                title_id: "station_id".to_string(),
                start_mode: LinearStartMode::AtLive,
                client_id: None,
            }))));
            assert_eq!(
                playing_title_id.get_untracked(),
                Some("station_id".to_string())
            );

            // Set to non-linear content
            content_controller.set(Some(PlaybackContent::new(PlaybackSrc::Minimal(
                MinimalSrc {
                    title_id: "titile_id".to_string(),
                },
            ))));
            assert_eq!(playing_title_id.get_untracked(), None);
        })
    }

    #[test]
    fn it_hides_panel_on_reset() {
        launch_only_app_context(|ctx| {
            let mut feature = setup(&ctx);
            feature.on_resource(&ResourceResponse::OnNow(Ok(Some(build_on_now_data()))));

            let panel_signal =
                use_panels_controller(&ctx).get_active_panel_in_group(&PanelGroup::Btf);
            assert!(panel_signal.get_untracked().is_some());

            feature.reset();
            assert!(panel_signal.get_untracked().is_none());
        })
    }

    mod set_carousel_data {
        use super::*;

        #[test]
        fn it_register_panel_on_valid_data() {
            launch_only_app_context(|ctx| {
                let mut feature = setup(&ctx);
                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(build_on_now_data()))));

                assert!(feature.panel_controller.is_some());

                let panels_controller = use_panels_controller(&ctx);
                let panel = panels_controller
                    .get_active_panel_in_group(&PanelGroup::Btf)
                    .get_untracked()
                    .unwrap();
                let carousel_title = panel
                    .upgrade()
                    .unwrap()
                    .tab
                    .as_ref()
                    .map(|t| t.title.clone().get_untracked());

                assert_eq!(
                    carousel_title,
                    Some(TextContent::String("carousel_title".to_string()).into())
                );
                assert!(panels_controller.deregister(&PanelName::OnNow).is_some());
            })
        }

        #[test]
        fn it_only_replace_carousel_when_new_data_has_different_items() {
            launch_only_app_context(|ctx| {
                let mut feature = setup(&ctx);

                let cards_signal = feature.cards;
                let cards_set_count = store_value(ctx.scope(), 0);
                create_effect(ctx.scope(), move |_| {
                    cards_signal.track();
                    cards_set_count.update_value(|c| *c += 1);
                });
                assert_eq!(cards_set_count.get_value(), 1);

                // Initial resource data
                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(build_on_now_data()))));
                assert_eq!(cards_set_count.get_value(), 2);

                let first_item_schedule =
                    feature.carousel_items.with_value(|items| items[0].schedule);
                let schedule_set_count = store_value(ctx.scope(), 0);
                create_effect(ctx.scope(), move |_| {
                    first_item_schedule.track();
                    schedule_set_count.update_value(|c| *c += 1);
                });
                assert_eq!(first_item_schedule.get_untracked().len(), 3);
                assert_eq!(schedule_set_count.get_value(), 1);

                // Update with empty date
                feature.on_resource(&ResourceResponse::OnNow(Ok(None)));
                assert_eq!(cards_set_count.get_value(), 2);
                assert_eq!(schedule_set_count.get_value(), 1);

                // Assert panel is still registered
                let panel_signal =
                    use_panels_controller(&ctx).get_active_panel_in_group(&PanelGroup::Btf);
                assert!(panel_signal.get_untracked().is_some());

                // Update with carousel of same items with updated schedule
                let mut item = build_carousel_item("station1", None);
                item.schedule.push(LinearAiring {
                    airing_id: Some("airing99".to_string()),
                    start_time: 1234,
                    end_time: 5678,
                    ..Default::default()
                });
                let carousel = OnNowCarousel {
                    base: GenericCarousel {
                        carousel_title: Some("carousel_title".to_string()),
                        ..Default::default()
                    },
                    carousel_items: vec![item, build_carousel_item("station2", None)],
                };
                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(OnNowData {
                    carousel: Some(carousel),
                }))));
                assert_eq!(cards_set_count.get_value(), 2);
                assert_eq!(schedule_set_count.get_value(), 2);
                assert_eq!(first_item_schedule.get_untracked().len(), 4);

                // Update with different data
                let mut new_data = build_on_now_data();
                new_data.carousel = Some(OnNowCarousel {
                    base: GenericCarousel::default(),
                    carousel_items: vec![
                        build_carousel_item("station2", None),
                        build_carousel_item("station1", None),
                    ],
                });
                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(new_data))));
                assert_eq!(cards_set_count.get_value(), 3);
                assert!(panel_signal.get_untracked().is_some());
            })
        }

        #[test]
        fn it_uses_carousel_title_fallback() {
            launch_only_app_context(|ctx| {
                let mut feature = setup(&ctx);
                let mut data = build_on_now_data();
                data.carousel.as_mut().unwrap().base.carousel_title = None;
                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(data))));

                assert!(feature.panel_controller.is_some());

                let panels_controller = use_panels_controller(&ctx);
                let panel = panels_controller
                    .get_active_panel_in_group(&PanelGroup::Btf)
                    .get_untracked()
                    .unwrap();
                let carousel_title = panel
                    .upgrade()
                    .unwrap()
                    .tab
                    .as_ref()
                    .map(|t| t.title.clone().get_untracked());

                assert_eq!(
                    carousel_title,
                    Some(LocalizedText::new("AV_LRC_ON_NOW_TTS".to_string()).into())
                );
                assert!(panels_controller.deregister(&PanelName::OnNow).is_some());
            })
        }

        #[test]
        fn it_logs_panel_registration_error() {
            testing_logger::setup();
            launch_only_app_context(|ctx| {
                let mut feature = setup(&ctx);
                let panels_controller = use_panels_controller(&ctx);
                // Register another feature of same name to cause error
                let _ = panels_controller.register(Panel {
                    panel_name: PanelName::OnNow,
                    group: PanelGroup::Btf,
                    feature_name: FeatureName::OnNow,
                    auto_hide_timeout: None,
                    tab: None,
                    component: Box::new(move |ctx| {
                        compose! {
                            Row() {}.into_any()
                        }
                    }),
                    on_hidden: None,
                    on_shown: None,
                });

                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(build_on_now_data()))));

                assert!(feature.panel_controller.is_none());
                assert_error_log(
                    "LinearPlaybackPage.onPanelRegistrationFailed: DuplicatePanelName",
                );
            })
        }
    }

    mod build_playback_card {
        use super::*;

        #[test]
        fn it_builds_card_for_current_airing() {
            launch_only_scope(|scope| {
                let now = Utc::now().timestamp_millis();
                let current_time = create_rw_signal(scope, now);
                let carousel_item =
                    ReactiveCarouselItem::new(scope, build_carousel_item("station", Some(now)));

                let playing_title_id = Signal::derive(scope, || None);
                let card = build_playback_card(
                    scope,
                    &carousel_item,
                    current_time.read_only(),
                    playing_title_id,
                );
                let progress_signal = card.progress_bar.unwrap().progress;

                assert_eq!(card.id, "station".to_string());
                assert_eq!(
                    card.title,
                    TextContent::String("station title".to_string()).into()
                );
                assert_eq!(
                    card.subtitle.get_untracked(),
                    Some(TextContent::LocalizedText(LocalizedText {
                        string_id: "AV_LRC_EPISODE_COMPACT".to_string().into(),
                        substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                            "episodeNumber".into(),
                            "1".to_string().into()
                        )]))),
                    }))
                );
                assert_eq!(
                    card.subtitle_suffix.get_untracked(),
                    Some(TextContent::String("16+".to_string()))
                );
                assert_eq!(
                    card.image_url.get_untracked(),
                    Some("airing1_image".to_string())
                );
                assert_eq!(
                    card.badge.get_untracked(),
                    Some(PlaybackTitleCardBadge::OnNow).into()
                );
                assert_eq!(card.is_playing.get_untracked(), false);
                assert_eq!(card.provider_logo, None);
                assert_eq!(progress_signal.get_untracked(), 0.5);

                // Update time to before first airing end
                current_time.update(|t| *t = now + 500);

                // Assert card image does not change and progress increase
                assert_eq!(
                    card.image_url.get_untracked(),
                    Some("airing1_image".to_string())
                );
                assert_eq!(
                    card.badge.get_untracked(),
                    Some(PlaybackTitleCardBadge::OnNow).into()
                );
                assert_eq!(progress_signal.get_untracked(), 0.75);

                // Update time to second airing start
                current_time.update(|t| *t = now + 1100);

                // Assert card attributes update
                assert_eq!(
                    card.subtitle.get_untracked(),
                    Some(TextContent::LocalizedText(LocalizedText {
                        string_id: "AV_LRC_EPISODE_COMPACT".to_string().into(),
                        substitution_parameters: Some(SubstitutionParameters(HashMap::from([(
                            "episodeNumber".into(),
                            "2".to_string().into()
                        ),]))),
                    }))
                );
                assert_eq!(
                    card.subtitle_suffix.get_untracked(),
                    Some(TextContent::String("All".to_string()))
                );
                assert_eq!(
                    card.image_url.get_untracked(),
                    Some("airing2_image".to_string())
                );
                assert_eq!(
                    card.badge.get_untracked(),
                    Some(PlaybackTitleCardBadge::Live).into()
                );
                assert_eq!(progress_signal.get_untracked(), 0.1);

                // Update time to after second airing end
                current_time.update(|t| *t = now + 5000);

                // Assert card renders last airing and progress at 1
                assert_eq!(
                    card.image_url.get_untracked(),
                    Some("airing2_image".to_string())
                );
                assert_eq!(progress_signal.get_untracked(), 1.0);
            });
        }

        #[test]
        fn it_builds_card_when_schedule_empty() {
            launch_only_scope(|scope| {
                let current_time = create_rw_signal(scope, Utc::now().timestamp_millis());
                let mut carousel_item =
                    build_carousel_item("station", Some(current_time.get_untracked()));
                carousel_item.schedule = vec![];
                let carousel_item = ReactiveCarouselItem::new(scope, carousel_item);

                let playing_title_id = Signal::derive(scope, || None);
                let card = build_playback_card(
                    scope,
                    &carousel_item,
                    current_time.read_only(),
                    playing_title_id,
                );

                assert_eq!(card.id, "station".to_string());
                assert_eq!(
                    card.title,
                    TextContent::String("station title".to_string()).into()
                );
                assert_eq!(card.subtitle.get_untracked(), None);
                assert_eq!(card.subtitle_suffix.get_untracked(), None);
                assert_eq!(card.image_url.get_untracked(), None);
                assert_eq!(
                    card.badge.get_untracked(),
                    Some(PlaybackTitleCardBadge::OnNow).into()
                );
                assert!(card.progress_bar.is_none());
                assert_eq!(card.is_playing.get_untracked(), false);
                assert_eq!(card.provider_logo, None);
            });
        }

        #[test]
        fn it_builds_card_with_is_playing_signal() {
            launch_only_scope(|scope| {
                let now = Utc::now().timestamp_millis();
                let current_time = create_rw_signal(scope, now);
                let carousel_item =
                    ReactiveCarouselItem::new(scope, build_carousel_item("station_id", Some(now)));

                let playing_title_id = create_rw_signal(scope, None);
                let card = build_playback_card(
                    scope,
                    &carousel_item,
                    current_time.read_only(),
                    playing_title_id.read_only().into(),
                );

                assert_eq!(card.is_playing.get_untracked(), false);
                playing_title_id.set(Some("station_id".to_string()));
                assert_eq!(card.is_playing.get_untracked(), true);
            });
        }

        #[test]
        fn it_only_update_card_signals_when_current_airing_change() {
            launch_only_scope(|scope| {
                let now = Utc::now().timestamp_millis();
                let current_time = create_rw_signal(scope, now);
                let carousel_item =
                    ReactiveCarouselItem::new(scope, build_carousel_item("station", Some(now)));

                let playing_title_id = Signal::derive(scope, || None);
                let card = build_playback_card(
                    scope,
                    &carousel_item,
                    current_time.read_only(),
                    playing_title_id,
                );
                let progress_signal = card.progress_bar.unwrap().progress;
                let image_url = card.image_url;

                let progress_update_count = store_value(scope, 0);
                let image_url_update_count = store_value(scope, 0);
                create_effect(scope, move |_| {
                    progress_signal.track();
                    progress_update_count.update_value(|c| *c += 1);
                });
                create_effect(scope, move |_| {
                    image_url.track();
                    image_url_update_count.update_value(|c| *c += 1);
                });
                // Verify initial update
                assert_eq!(progress_update_count.get_value(), 1);
                assert_eq!(image_url_update_count.get_value(), 1);

                // Build new schedule with additional airing
                let mut schedule = build_carousel_item("station", Some(now)).schedule;
                schedule.push(LinearAiring {
                    airing_id: Some("airing99".to_string()),
                    start_time: 1234,
                    end_time: 5678,
                    ..Default::default()
                });
                carousel_item.schedule.set(schedule);
                // Verify no new update
                assert_eq!(progress_update_count.get_value(), 1);
                assert_eq!(image_url_update_count.get_value(), 1);

                // Update time
                current_time.update(|t| *t = now + 1);
                // Verify only progress bar update
                assert_eq!(progress_update_count.get_value(), 2);
                assert_eq!(image_url_update_count.get_value(), 1);

                // Update time to second airing start
                current_time.update(|t| *t = now + 1100);
                // Verify new update
                assert_eq!(progress_update_count.get_value(), 4);
                assert_eq!(image_url_update_count.get_value(), 2);
            });
        }

        #[test]
        fn it_builds_card_accessbility_description_for_current_airing() {
            launch_only_scope(|scope| {
                let now = Utc::now().timestamp_millis();
                let current_time = create_rw_signal(scope, now);
                let playing_title_id = Signal::derive(scope, || None);
                let mut carousel_item = build_carousel_item("station", Some(now));
                // Change third airing title to same as station name
                carousel_item.schedule.get_mut(2).unwrap().title =
                    Some("station title".to_string());

                // Assert all airing accessibility description present
                let card = build_playback_card(
                    scope,
                    &ReactiveCarouselItem::new(scope, carousel_item),
                    current_time.read_only(),
                    playing_title_id,
                );
                let accessibility_descriptions = card.accessibility_descriptions.unwrap();
                assert_eq!(
                    accessibility_descriptions.get_untracked(),
                    vec![
                        TextContent::String("station title".to_string()),
                        TextContent::String("station airing1 title".to_string()),
                        TextContent::LocalizedText(LocalizedText::new(
                            "AV_LRC_LIVE_TV_ON_NOW".to_string()
                        )),
                        TextContent::LocalizedText(LocalizedText {
                            string_id: "AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES".to_string().into(),
                            substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                                ("minutesRemaining".into(), "1".to_string().into()),
                            ]))),
                        }),
                        TextContent::LocalizedText(LocalizedText {
                            string_id: "AV_LRC_EPISODE_COMPACT".to_string().into(),
                            substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                                ("episodeNumber".into(), "1".to_string().into()),
                            ]))),
                        }),
                        TextContent::String("16+".to_string()),
                    ],
                );

                current_time.set(now + 2000);
                // Assert airing name omitted and LIVE badge spoken
                assert_eq!(
                    accessibility_descriptions.get_untracked(),
                    vec![
                        TextContent::String("station title".to_string()),
                        TextContent::LocalizedText(LocalizedText::new(
                            "AIV_BLAST_LIVE_BADGE_TEXT".to_string()
                        )),
                        TextContent::LocalizedText(LocalizedText {
                            string_id: "AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES".to_string().into(),
                            substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                                ("minutesRemaining".into(), "1".to_string().into()),
                            ]))),
                        }),
                        TextContent::LocalizedText(LocalizedText {
                            string_id: "AV_LRC_EPISODE_COMPACT".to_string().into(),
                            substitution_parameters: Some(SubstitutionParameters(HashMap::from([
                                ("episodeNumber".into(), "2".to_string().into())
                            ]))),
                        }),
                        TextContent::String("All".to_string()),
                    ],
                );
            });
        }

        #[test]
        fn it_builds_card_accessibiility_hint() {
            launch_only_scope(|scope| {
                let now = Utc::now().timestamp_millis();
                let current_time = create_rw_signal(scope, now);
                let carousel_item =
                    &ReactiveCarouselItem::new(scope, build_carousel_item("station", Some(now)));
                let playing_title_id = Signal::derive(scope, || None);

                let card = build_playback_card(
                    scope,
                    &carousel_item,
                    current_time.read_only(),
                    playing_title_id,
                );
                assert_eq!(
                    card.accessibility_hint,
                    Some(TextContent::LocalizedText(LocalizedText::new(
                        ITEM_NAVIGATION_HINT_TTS
                    )))
                );
            })
        }
    }

    mod card_events_handler {
        use super::*;

        fn setup(
            ctx: &AppContext,
            items: Vec<OnNowCarouselItem>,
        ) -> (CardEventsHandler, RwSignal<Option<PlaybackContent>>) {
            let content_controller = create_rw_signal(ctx.scope(), None);
            provide_content(ctx.scope(), content_controller);

            let items = items
                .into_iter()
                .map(|item| ReactiveCarouselItem::new(ctx.scope(), item))
                .collect();
            let event_handler = CardEventsHandler {
                scope: ctx.scope(),
                carousel_items: store_value(ctx.scope(), items),
                controllers: setup_controllers(&ctx),
                playing_title_id: Signal::derive(ctx.scope(), || Some("playing_title".to_string())),
            };
            (event_handler, content_controller)
        }

        #[test]
        fn it_sets_content_on_card_selection() {
            launch_only_app_context(|ctx| {
                let (event_handler, content_controller) = setup(
                    &ctx,
                    vec![
                        build_carousel_item("station1", Some(Utc::now().timestamp_millis())),
                        build_carousel_item("station2", Some(Utc::now().timestamp_millis())),
                        build_carousel_item("playing_title", Some(Utc::now().timestamp_millis())),
                    ],
                );

                // Ignores currently playing title
                event_handler.on_card_select(2);
                assert_eq!(content_controller.get_untracked(), None);

                // Ignores invalid index
                event_handler.on_card_select(10);
                assert_eq!(content_controller.get_untracked(), None);

                // Setup mutate history
                let mut mock_routing_context = MockRouting::new();
                mock_routing_context
                    .expect_mutate_history()
                    .once()
                    .return_const({});
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing_context));

                // Select valid card
                event_handler.on_card_select(1);
                assert_eq!(
                    content_controller.get_untracked().unwrap(),
                    PlaybackContent::new(PlaybackSrc::Linear(LinearSrc {
                        title_id: "station2".to_string(),
                        start_mode: LinearStartMode::AtLive,
                        client_id: None,
                    }))
                    .origin(PlaybackPageOrigin::OnNow.into())
                );
            });
        }

        #[test]
        fn it_updates_page_stack() {
            launch_only_app_context(|ctx| {
                let (event_handler, _) = setup(
                    &ctx,
                    vec![build_carousel_item(
                        "station1",
                        Some(Utc::now().timestamp_millis()),
                    )],
                );

                // Setup mutate history
                let mut mock_routing_context = MockRouting::new();
                mock_routing_context
                    .expect_mutate_history()
                    .once()
                    .returning(move |callback| {
                        let mut locations = vec![
                            Location {
                                pageType: PageType::Rust(RustPage::RUST_LIVE_TV),
                                pageParams: Map::new(),
                            },
                            Location {
                                pageType: PageType::Rust(RustPage::RUST_PLAYBACK),
                                pageParams: Map::new(),
                            },
                        ];

                        callback(&mut locations);
                        assert_eq!(
                            locations.get(0).unwrap(),
                            &Location {
                                pageType: PageType::Rust(RustPage::RUST_LIVE_TV),
                                pageParams: create_serde_map([(
                                    LIVE_TV_LAST_PLAYBACK_TITLE_PARAM,
                                    Value::String("station1".to_string()),
                                )]),
                            }
                        );
                    });
                provide_context::<RoutingContext>(ctx.scope(), Rc::new(mock_routing_context));

                // Select valid card
                event_handler.on_card_select(0);
            });
        }
    }

    mod metadata_resource {
        use super::*;

        #[test]
        fn it_sets_data_when_receive_valid_resource() {
            launch_only_app_context(|ctx| {
                let mut feature = setup(&ctx);

                feature.on_resource(&ResourceResponse::OnNow(Ok(Some(build_on_now_data()))));

                assert_eq!(feature.cards.get_untracked().len(), 2);
            });
        }

        #[rstest]
        #[case::resource_error(
            ResourceResponse::OnNow(Err(ResourceError { error_cause: "cause".to_string(), message: "message".to_string() })),
            "LinearPlaybackPage.onMissingCarouselData: \"message\""
        )]
        #[case::missing_resource(
            ResourceResponse::OnNow(Ok(None)),
            "LinearPlaybackPage.onMissingCarouselData: empty response"
        )]
        #[case::invalid_resource(
            ResourceResponse::NextUp(Ok(None)),
            "LinearPlaybackPage.onInvalidCarouselData"
        )]
        fn it_logs_resource_error(
            #[case] resource_response: ResourceResponse,
            #[case] expected_message: &str,
        ) {
            testing_logger::setup();
            launch_only_app_context(move |ctx| {
                setup(&ctx).on_resource(&resource_response);
            });
            assert_error_log(expected_message);
        }

        #[rstest]
        #[case(
            StreamingConfig::Linear { start_mode: LinearStartMode::AtLive },
            vec![ResourceName::OnNow]
        )]
        #[case(
            StreamingConfig::Vod { position: 0 },
            vec![]
        )]
        fn it_requests_resource_on_linear_playback_start(
            #[case] config: StreamingConfig,
            #[case] expected_resource: Vec<ResourceName>,
        ) {
            launch_only_app_context(|ctx| {
                let feature = setup(&ctx);

                let config = IntrinsicContentConfig::builder()
                    .title_id("title_id")
                    .streaming_config(config)
                    .build();

                let resource = feature.get_required_resources_on_playback_start(config);

                assert_eq!(resource, HashSet::from_iter(expected_resource));
            });
        }

        #[test]
        fn it_requests_no_resource_on_demand() {
            launch_only_app_context(|ctx| {
                let feature = setup(&ctx);

                let config = IntrinsicContentConfig::builder()
                    .title_id("title_id")
                    .streaming_config(StreamingConfig::Linear {
                        start_mode: LinearStartMode::AtLive,
                    })
                    .build();

                let resource = feature.get_required_resources_on_demand(config);

                assert_eq!(resource, HashSet::new());
            });
        }
    }

    fn build_on_now_data() -> OnNowData {
        OnNowData {
            carousel: Some(OnNowCarousel {
                base: GenericCarousel {
                    carousel_title: Some("carousel_title".to_string()),
                    ..Default::default()
                },
                carousel_items: vec![
                    build_carousel_item("station1", None),
                    build_carousel_item("station2", None),
                ],
            }),
        }
    }

    fn build_carousel_item(id: &str, now: Option<i64>) -> OnNowCarouselItem {
        let now = now.unwrap_or(Utc::now().timestamp_millis());
        OnNowCarouselItem {
            base: GenericCarouselItem {
                title_id: id.to_string(),
                card_title: format!("{} title", id),
                ..Default::default()
            },
            consent: None,
            schedule: vec![
                LinearAiring {
                    airing_id: Some(format!("{} airing0", id)),
                    start_time: now - 2000,
                    end_time: now - 1000,
                    ..Default::default()
                },
                LinearAiring {
                    airing_id: Some(format!("{} airing1", id)),
                    title: Some(format!("{} airing1 title", id)),
                    synopsis: Some("synopsis1".to_string()),
                    images: Some(Images {
                        cover_image: Some("airing1_image".to_string()),
                        ..Default::default()
                    }),
                    start_time: now - 1000,
                    end_time: now + 1000,
                    rating_info: Some(RatingInfo {
                        rating_display_text: "16+".to_string(),
                        ..Default::default()
                    }),
                    content_descriptors: Some(vec![
                        "Violence".to_string(),
                        "Strong Language".to_string(),
                    ]),
                    hierarchy_context: Some(ShowContext {
                        season: None,
                        episode: Some(1),
                        episodeTitle: None,
                    }),
                    airing_attributes: None,
                },
                LinearAiring {
                    airing_id: Some(format!("{} airing2", id)),
                    title: Some(format!("{} airing2 title", id)),
                    synopsis: Some("synopsis2".to_string()),
                    images: Some(Images {
                        cover_image: Some("airing2_image".to_string()),
                        ..Default::default()
                    }),
                    start_time: now + 1000,
                    end_time: now + 2000,
                    rating_info: Some(RatingInfo {
                        rating_display_text: "All".to_string(),
                        ..Default::default()
                    }),
                    content_descriptors: None,
                    hierarchy_context: Some(ShowContext {
                        season: None,
                        episode: Some(2),
                        episodeTitle: None,
                    }),
                    airing_attributes: Some(vec!["LIVE".to_string()]),
                },
            ],
        }
    }

    fn assert_error_log(message: &str) {
        testing_logger::validate(|logs| assert!(logs.iter().find(|l| l.body == message).is_some()));
    }
}
