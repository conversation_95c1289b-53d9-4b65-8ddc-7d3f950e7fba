use crate::on_now::create_on_now_feature;
use ignx_compositron::reactive::StoredValue;
#[cfg(test)]
use mockall_double::double;
#[cfg_attr(test, double)]
use playback_feature_manager::FeatureManager;
use playback_feature_manager::FeatureName;
#[cfg_attr(test, double)]
use playback_metadata_provider::MetadataProvider;

pub fn register_features(
    feature_manager: &mut FeatureManager,
    metadata_provider: StoredValue<Box<MetadataProvider>>,
) {
    if let Err(err) =
        feature_manager.register(metadata_provider, FeatureName::OnNow, create_on_now_feature)
    {
        log::error!(
            "LinearFeatureCreator.onFeatureRegistrationFailed {:?}: {:?}",
            FeatureName::OnNow,
            err
        );
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::on_now::OnNowFeature;
    use ignx_compositron::{app::launch_only_app_context, reactive::store_value};
    use playback_feature_manager::FeatureManagerError;

    #[test]
    fn it_registers_features() {
        launch_only_app_context(|ctx| {
            let metadata_provider = store_value(ctx.scope(), Box::new(MetadataProvider::default()));
            let mut feature_manager = FeatureManager::default();
            feature_manager
                .expect_register::<OnNowFeature>()
                .times(1)
                .return_const(Ok(()));

            register_features(&mut feature_manager, metadata_provider);
        });
    }

    #[test]
    fn it_logs_registration_error() {
        testing_logger::setup();
        launch_only_app_context(|ctx| {
            let metadata_provider = store_value(ctx.scope(), Box::new(MetadataProvider::default()));
            let mut feature_manager = FeatureManager::default();
            feature_manager
                .expect_register::<OnNowFeature>()
                .times(1)
                .return_const(Err(FeatureManagerError::DuplicateFeatureName));

            register_features(&mut feature_manager, metadata_provider);
        });

        testing_logger::validate(|logs| {
            assert!(logs.iter().find(|l| l.body == "LinearFeatureCreator.onFeatureRegistrationFailed onNow: DuplicateFeatureName").is_some())
        });
    }
}
