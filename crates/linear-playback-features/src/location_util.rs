use linear_common::constants::*;
use location::Location;
use location::{PageType, RustPage};
use serde_json::{Map, Value};

/**
 * Updates linear pages in the page stack below playback page
 * Live TV: set `LIVE_TV_LAST_PLAYBACK_TITLE_PARAM` param
 * Station Details: add/remove station details to keep two most recently played stations
 */
pub fn update_linear_page_stack(history: &mut Vec<Location>, title_id: String) {
    let last_page = history.last();
    if last_page.map(|p| p.pageType) != Some(PageType::Rust(RustPage::RUST_PLAYBACK)) {
        return;
    }
    // Pop playback page from history
    let playback_page = history.pop();
    // Modify pages below playback page
    let last_page = history.pop();
    if let Some(mut last_page) = last_page {
        if last_page.pageType == PageType::Rust(RustPage::RUST_LIVE_TV) {
            // Update Rust Live TV page param
            last_page.pageParams.insert(
                LIVE_TV_LAST_PLAYBACK_TITLE_PARAM.to_string(),
                Value::String(title_id),
            );
            history.push(last_page);
        } else if last_page.pageType == PageType::Rust(RustPage::RUST_STATION_DETAILS) {
            // Pop remaining station detail pages
            while history
                .last()
                .is_some_and(|l| l.pageType == PageType::Rust(RustPage::RUST_STATION_DETAILS))
            {
                history.pop();
            }
            // Modify and push most recent the station detail page before playback.
            // Clear startTime to focus on live airing and clear journey ingress context param.
            last_page
                .pageParams
                .remove(STATION_DETAILS_START_TIME_PARAM);
            last_page.pageParams.remove(STATION_DETAILS_JIC_PARAM);
            history.push(last_page);
            // Add new station detail page to the top
            history.push(Location {
                pageType: PageType::Rust(RustPage::RUST_STATION_DETAILS),
                pageParams: Map::from_iter(vec![("titleId".to_string(), Value::String(title_id))]),
            });
        } else {
            history.push(last_page);
        }
    }
    // Push the playback page to the stack
    if let Some(playback_page) = playback_page {
        history.push(playback_page);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use cross_app_events::create_serde_map;

    mod update_linear_page_stack {
        use super::*;

        #[test]
        fn it_does_not_update_when_page_stack_empty() {
            let mut history = vec![];
            update_linear_page_stack(&mut history, "station_id".to_string());
            assert_eq!(history, vec![]);
        }

        #[test]
        fn it_does_not_update_when_top_page_is_not_playback() {
            let mut history = vec![
                Location {
                    pageType: PageType::Rust(RustPage::RUST_LIVE_TV),
                    pageParams: Map::new(),
                },
                Location {
                    pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                    pageParams: Map::new(),
                },
            ];
            let initial_history = history.clone();
            update_linear_page_stack(&mut history, "station_id".to_string());
            assert_eq!(history, initial_history);
        }

        #[test]
        fn it_does_not_update_when_page_before_playback_is_not_linear() {
            let mut history = vec![
                Location {
                    pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                    pageParams: create_serde_map([("param", Value::String("val".to_string()))]),
                },
                Location {
                    pageType: PageType::Rust(RustPage::RUST_PLAYBACK),
                    pageParams: Map::new(),
                },
            ];
            let initial_history = history.clone();
            update_linear_page_stack(&mut history, "station_id".to_string());
            assert_eq!(history, initial_history);
        }

        #[test]
        fn it_updates_rust_live_tv_page_param() {
            let mut history = vec![
                Location {
                    pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                    pageParams: Map::new(),
                },
                Location {
                    pageType: PageType::Rust(RustPage::RUST_LIVE_TV),
                    pageParams: create_serde_map([
                        ("param", Value::String("val".to_string())),
                        (
                            LIVE_TV_LAST_PLAYBACK_TITLE_PARAM,
                            Value::String("old_station_id".to_string()),
                        ),
                    ]),
                },
                Location {
                    pageType: PageType::Rust(RustPage::RUST_PLAYBACK),
                    pageParams: Map::new(),
                },
            ];
            update_linear_page_stack(&mut history, "station_id".to_string());
            assert_eq!(
                history,
                vec![
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                        pageParams: Map::new(),
                    },
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_LIVE_TV),
                        pageParams: create_serde_map([
                            ("param", Value::String("val".to_string()),),
                            (
                                LIVE_TV_LAST_PLAYBACK_TITLE_PARAM,
                                Value::String("station_id".to_string()),
                            )
                        ]),
                    },
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_PLAYBACK),
                        pageParams: Map::new(),
                    }
                ]
            );
        }

        #[test]
        fn it_maintains_two_most_recent_station_detail_page() {
            let mut history = vec![
                Location {
                    pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                    pageParams: Map::new(),
                },
                Location {
                    pageType: PageType::Rust(RustPage::RUST_STATION_DETAILS),
                    pageParams: create_serde_map([
                        (STATION_DETAILS_START_TIME_PARAM, Value::Number(123.into())),
                        (STATION_DETAILS_JIC_PARAM, Value::String("jic".into())),
                        (
                            STATION_DETAILS_TITLE_ID_PARAM,
                            Value::String("station1".to_string()),
                        ),
                    ]),
                },
                Location {
                    pageType: PageType::Rust(RustPage::RUST_STATION_DETAILS),
                    pageParams: create_serde_map([
                        (STATION_DETAILS_START_TIME_PARAM, Value::Number(456.into())),
                        (STATION_DETAILS_JIC_PARAM, Value::String("jic".into())),
                        (
                            STATION_DETAILS_TITLE_ID_PARAM,
                            Value::String("station2".to_string()),
                        ),
                    ]),
                },
                Location {
                    pageType: PageType::Rust(RustPage::RUST_PLAYBACK),
                    pageParams: Map::new(),
                },
            ];
            update_linear_page_stack(&mut history, "station_id".to_string());
            assert_eq!(
                history,
                vec![
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                        pageParams: Map::new(),
                    },
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_STATION_DETAILS),
                        pageParams: create_serde_map([(
                            STATION_DETAILS_TITLE_ID_PARAM,
                            Value::String("station2".to_string()),
                        )]),
                    },
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_STATION_DETAILS),
                        pageParams: create_serde_map([(
                            STATION_DETAILS_TITLE_ID_PARAM,
                            Value::String("station_id".to_string()),
                        )]),
                    },
                    Location {
                        pageType: PageType::Rust(RustPage::RUST_PLAYBACK),
                        pageParams: Map::new(),
                    }
                ]
            );
        }
    }
}
