[package]
name = "linear-playback-features"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
chrono.workspace = true
fableous.workspace = true
linear-common.workspace = true
location.workspace = true
log.workspace = true
playback-feature-manager.workspace = true
playback-core.workspace = true
playback-carousel.workspace = true
playback-metadata-provider.workspace = true
playback-ui-kit.workspace = true
playback-src.workspace = true
router.workspace = true
serde_json.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }
common-transform-types.workspace = true
cross-app-events.workspace = true
testing_logger.workspace = true
rstest.workspace = true
mockall.workspace = true
mockall_double.workspace = true
playback-src = { workspace = true, features = ["test_utils"] }
playback-machine = { workspace = true, features = ["test_utils"] }
playback-core = { workspace = true, features = ["test_utils"] }
playback-ui-kit.workspace = true
playback-metadata-provider = { workspace = true, features = ["test_utils"] }
serial_test.workspace = true
auth.workspace = true
app-config = { workspace = true, features = ["test_utils"] }
router = { workspace = true, features = ["test_utils"] }

[features]
debug_impl = []
test_utils = []

[lints]
workspace = true

