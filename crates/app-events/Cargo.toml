[package]
name = "app-events"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
log.workspace = true
serde.workspace = true
serde_json.workspace = true
cross-app-events.workspace = true
network-parser.workspace = true
mockall.workspace = true
mockall_double.workspace = true
common-transform-types.workspace = true
router.workspace = true
location.workspace = true
cfg-test-attr-derive.workspace = true

[dev-dependencies]
rstest.workspace = true
insta.workspace = true
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
    "mock_timer",
] }
testing_logger.workspace = true
serial_test.workspace = true

[features]
test_utils = []
debug_impl = []
