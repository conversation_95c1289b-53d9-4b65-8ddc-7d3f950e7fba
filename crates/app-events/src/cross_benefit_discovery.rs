use cfg_test_attr_derive::derive_test_only;
use cross_app_events::app_event::AppEventReporter;
use ignx_compositron::reactive::{Scope, SignalWithUntracked};
use location::{JSPage, PageType, RustPage};
use router::hooks::try_use_location;
use serde::Serialize;
use serde_json::{json, Map, Value};

const SOURCE: &str = "XBD_MARKETING_MODAL";
const MORE_DETAILS_CLICK: &str = "MORE_DETAILS_CLICK";
const COLLECTIONS_PAGE_CLICK: &str = "COLLECTIONS_PAGE_CLICK";
const MODAL_DISPLAY: &str = "MODAL_DISPLAY";
const DISMISS_MODAL: &str = "DISMISS_MODAL";
const OPT_OUT: &str = "OPT_OUT";
const SIGNUP_PAGE_CLICK: &str = "SIGNUP_PAGE_CLICK";
const FUSE_OFFER_ACTIVATION_PAGE_CLICK: &str = "FUSE_OFFER_ACTIVATION_PAGE_CLICK";
const PRIME_RETENTION_MODAL_CLICK: &str = "PRIME_RETENTION_MODAL_CLICK";
const API_LINK_MODAL_CLICK: &str = "API_LINK_MODAL_CLICK";

#[derive(Serialize)]
#[derive_test_only(PartialEq, Debug)]
enum SubPageType {
    Home,
    Detail,
    Unknown,
}

#[derive(Serialize)]
#[allow(non_camel_case_types)]
pub enum DismissMethod {
    btn,  // Button click
    opt,  // Opt out
    back, // Back button press
}

fn get_sub_page_type(scope: Scope) -> Option<SubPageType> {
    if let Some(location) = try_use_location(scope) {
        let page_type = location.with_untracked(|location| location.pageType);

        match page_type {
            PageType::Rust(RustPage::RUST_COLLECTIONS) | PageType::Js(JSPage::COLLECTION_PAGE) => {
                Some(SubPageType::Home)
            }
            PageType::Rust(RustPage::RUST_DETAILS | RustPage::RUST_LIVE_TV)
            | PageType::Js(
                JSPage::OPEN_DETAIL_PAGE | JSPage::NEW_DETAILS_PAGE | JSPage::LIVE_PAGE,
            ) => Some(SubPageType::Detail),
            _ => Some(SubPageType::Unknown),
        }
    } else {
        log::warn!(
            "[cross_benefit_discovery] Unable to get sub page type whilst reporting app event."
        );
        None
    }
}

pub fn report_opt_out(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
) {
    if let Some(sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        params.insert("subPageType".into(), json!(sub_page_type));

        app_event_reporter.send_app_event(OPT_OUT, SOURCE, Some(params));
    }
}

pub fn report_more_details_click(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    card_index: usize,
) {
    if let Some(sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        params.insert("subPageType".into(), json!(sub_page_type));
        params.insert("interactionOrder".into(), card_index.into());

        app_event_reporter.send_app_event(MORE_DETAILS_CLICK, SOURCE, Some(params));
    }
}

pub fn report_collections_page_click(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    card_index: usize,
) {
    if let Some(sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        params.insert("subPageType".into(), json!(sub_page_type));
        params.insert("interactionOrder".into(), card_index.into());

        app_event_reporter.send_app_event(COLLECTIONS_PAGE_CLICK, SOURCE, Some(params));
    }
}

pub fn report_display(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    card_index: usize,
) {
    if let Some(sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        params.insert("subPageType".into(), json!(sub_page_type));
        params.insert("interactionOrder".into(), card_index.into());
        app_event_reporter.send_app_event(MODAL_DISPLAY, SOURCE, Some(params));
    }
}

pub fn report_dismiss(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    dismiss_method: DismissMethod,
) {
    if let Some(sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        params.insert("subPageType".into(), json!(sub_page_type));
        params.insert("interactionMethod".into(), json!(dismiss_method));

        app_event_reporter.send_app_event(DISMISS_MODAL, SOURCE, Some(params));
    }
}

pub fn sign_up_page_click(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    card_index: usize,
) {
    if let Some(sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        params.insert("subPageType".into(), json!(sub_page_type));
        params.insert("interactionOrder".into(), card_index.into());
        params.insert("interactionMethod".into(), json!(DismissMethod::btn));

        app_event_reporter.send_app_event(SIGNUP_PAGE_CLICK, SOURCE, Some(params));
    }
}

pub fn report_fuse_offer_activation_page_click(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    card_index: usize,
) {
    if let Some(_sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("interactionMethod".into(), json!(DismissMethod::btn));
        params.insert("interactionOrder".into(), card_index.into());
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        app_event_reporter.send_app_event(FUSE_OFFER_ACTIVATION_PAGE_CLICK, SOURCE, Some(params));
    }
}

pub fn report_prime_retention_click(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    card_index: usize,
) {
    if let Some(_sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("interactionMethod".into(), json!(DismissMethod::btn));
        params.insert("subPageType".into(), json!(_sub_page_type));
        params.insert("interactionOrder".into(), card_index.into());
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        app_event_reporter.send_app_event(PRIME_RETENTION_MODAL_CLICK, SOURCE, Some(params));
    }
}

pub fn report_api_link_click(
    scope: Scope,
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    card_index: usize,
) {
    if let Some(_sub_page_type) = get_sub_page_type(scope) {
        let mut params: Map<String, Value> = Map::new();
        params.insert("interactionMethod".into(), json!(DismissMethod::btn));
        params.insert("subPageType".into(), json!(_sub_page_type));
        params.insert("interactionOrder".into(), card_index.into());
        params.insert("refMarker".into(), ref_marker.unwrap_or_default().into());
        app_event_reporter.send_app_event(API_LINK_MODAL_CLICK, SOURCE, Some(params));
    }
}

#[cfg(test)]
mod tests {
    use std::rc::Rc;

    use ignx_compositron::{
        app::launch_only_scope,
        prelude::{create_signal, provide_context, Signal, SignalGet},
    };
    use location::Location;
    use router::{rust_location, MockRouting, RoutingContext};
    use rstest::rstest;

    use crate::cross_benefit_discovery::get_sub_page_type;

    use super::SubPageType;

    #[rstest]
    #[case(rust_location!(RUST_COLLECTIONS), Some(SubPageType::Home), true)]
    #[case(rust_location!(RUST_DETAILS), Some(SubPageType::Detail), true)]
    #[case(rust_location!(RUST_EMPTY), Some(SubPageType::Unknown), true)]
    #[case(rust_location!(RUST_COLLECTIONS), None, false)]
    fn get_sub_page_type_should_return_correct_value(
        #[case] location: Location,
        #[case] expected_sub_page_type: Option<SubPageType>,
        #[case] router_in_context: bool,
    ) {
        let mut mock_routing = MockRouting::new();

        launch_only_scope(move |scope| {
            let (location_signal, _) = create_signal(scope, location);

            if router_in_context {
                mock_routing
                    .expect_location()
                    .return_once_st(move || Signal::derive(scope, move || location_signal.get()));

                provide_context::<RoutingContext>(scope, Rc::new(mock_routing));
            }
            let sub_page_type = get_sub_page_type(scope);
            assert_eq!(sub_page_type, expected_sub_page_type);
        });
    }
}
