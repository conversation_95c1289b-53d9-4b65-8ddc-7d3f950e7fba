use cfg_test_attr_derive::derive_test_only;

pub const SOURCE: &str = "STREAM_SELECT_MODAL";
pub const OPEN_MODAL: &str = "OPEN_MODAL";
pub const OPEN_WATCH_MODAL: &str = "OPEN_WATCH_MODAL";
pub const LOAD_ACQUISITION: &str = "LOAD_ACQUISITION";
pub const LOAD_PLAYBACK: &str = "LOAD_PLAYBACK";
pub const LEAVE_MODAL: &str = "LEAVE_MODAL";
pub const LEAVE_WATCH_MODAL: &str = "LEAVE_WATCH_MODAL";
pub const CLICK_PLAYBACK: &str = "CLICK_PLAYBACK";
pub const CLICK_ACQUISITION: &str = "CLICK_ACQUISITION";
pub const CLICK_MORE_DETAILS: &str = "CLICK_MORE_DETAILS";
pub const CLICK_WATCHLIST: &str = "CLICK_WATCHLIST";
pub const SVOD_ACQUISITION_PAGE_TYPE: &str = "ATV3PSignup";
pub const PRIME_ACQUISITION_PAGE_TYPE: &str = "ATVAIVSignup";
pub const PRIME_ACQUISITION_SUBPAGE_TYPE: &str = "Prime";
pub const WATCH_MODAL_LOAD_FAILED_FATAL: &str = "LIVE_WATCH_MODAL_LOAD_FAILED_FATAL";
pub const SAVE_INTENT_FAILED_FATAL: &str = "SAVE_INTENT_FAILED_FATAL";
pub const MISSING_TITLE_ACTIONS_FATAL: &str = "MISSING_TITLE_ACTIONS_FATAL";

#[derive_test_only(Debug, PartialEq)]
#[derive(Clone)]
pub enum WatchModalPlaybackButtonType {
    WatchLive,
    WatchFromEarliestPoint,
    WatchFromBeginning,
    RapidRecap,
    Resume,
    FeatureNotStarted,
    FeatureInProgress,
    FeatureFinished,
    MultiView,
    None,
}

impl WatchModalPlaybackButtonType {
    pub fn to_csm_name(&self) -> String {
        match self {
            WatchModalPlaybackButtonType::WatchLive => "LIVE_STREAM_WATCH_NOW".to_string(),
            WatchModalPlaybackButtonType::WatchFromEarliestPoint => {
                "LIVE_STREAM_FROM_EARLIEST".to_string()
            }
            WatchModalPlaybackButtonType::WatchFromBeginning => "WATCH_FROM_BEGINNING".to_string(),
            WatchModalPlaybackButtonType::RapidRecap => "RAPID_RECAP".to_string(),
            WatchModalPlaybackButtonType::Resume => "LIVE_STREAM_FROM_BOOKMARK".to_string(),
            WatchModalPlaybackButtonType::FeatureNotStarted => "FEATURE_NOT_STARTED".to_string(),
            WatchModalPlaybackButtonType::FeatureInProgress => "FEATURE_IN_PROGRESS".to_string(),
            WatchModalPlaybackButtonType::FeatureFinished => "FEATURE_FINISHED".to_string(),
            WatchModalPlaybackButtonType::MultiView => "MULTI_VIEW".to_string(),
            WatchModalPlaybackButtonType::None => String::default(),
        }
    }
}
