use crate::watch_modal_events::types::*;
use cross_app_events::app_event::AppEventReporter;
use ignx_compositron::prelude::Scope;
use serde_json::{Map, Value};

pub fn csm_report_playback_button_click(
    app_event_reporter: AppEventReporter,
    button_type: WatchModalPlaybackButtonType,
    gti: String,
    playback_ref_marker: String,
    container_ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();
    params.insert(
        "playbackRefMarker".to_string(),
        Value::String(playback_ref_marker),
    );
    params.insert(
        "containerRefMarker".to_string(),
        Value::String(container_ref_marker.unwrap_or_default()),
    );
    params.insert("gti".to_string(), Value::String(gti));
    params.insert(
        "buttonType".to_string(),
        Value::String(button_type.to_csm_name()),
    );

    app_event_reporter.send_app_event(CLICK_PLAYBACK, SOURCE, Some(params));
}

pub fn csm_report_svod_acquisition_button_click(
    app_event_reporter: AppEventReporter,
    playback_ref_marker: String,
    container_ref_marker: Option<String>,
    benefit_id: String,
    gti: String,
) {
    let mut params: Map<String, Value> = Map::new();
    params.insert(
        "buttonRefMarker".to_string(),
        Value::String(playback_ref_marker),
    );
    params.insert(
        "containerRefMarker".to_string(),
        Value::String(container_ref_marker.unwrap_or_default()),
    );
    params.insert(
        "subscriptionType".to_string(),
        Value::String(SVOD_ACQUISITION_PAGE_TYPE.to_string()),
    );
    params.insert("subscriptionSubType".to_string(), Value::String(benefit_id));
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(CLICK_ACQUISITION, SOURCE, Some(params));
}

pub fn csm_report_prime_acquisition_button_click(
    app_event_reporter: AppEventReporter,
    button_ref_marker: Option<String>,
    container_ref_marker: Option<String>,
    gti: String,
) {
    let mut params: Map<String, Value> = Map::new();
    params.insert(
        "buttonRefMarker".to_string(),
        Value::String(button_ref_marker.unwrap_or_default()),
    );
    params.insert(
        "containerRefMarker".to_string(),
        Value::String(container_ref_marker.unwrap_or_default()),
    );
    params.insert(
        "subscriptionType".to_string(),
        Value::String(PRIME_ACQUISITION_PAGE_TYPE.to_string()),
    );
    params.insert(
        "subscriptionSubType".to_string(),
        Value::String(PRIME_ACQUISITION_SUBPAGE_TYPE.to_string()),
    );
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(CLICK_ACQUISITION, SOURCE, Some(params));
}

pub fn csm_report_playback_button_shown(
    app_event_reporter: AppEventReporter,
    button_type: WatchModalPlaybackButtonType,
    gti: String,
    playback_ref_marker: String,
    container_ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();
    params.insert(
        "playbackRefMarker".to_string(),
        Value::String(playback_ref_marker),
    );
    params.insert(
        "containerRefMarker".to_string(),
        Value::String(container_ref_marker.unwrap_or_default()),
    );
    params.insert("gti".to_string(), Value::String(gti));
    params.insert(
        "buttonType".to_string(),
        Value::String(button_type.to_csm_name()),
    );

    app_event_reporter.send_app_event(LOAD_PLAYBACK, SOURCE, Some(params));
}

pub fn csm_report_svod_acquisition_button_shown(
    app_event_reporter: AppEventReporter,
    playback_ref_marker: String,
    container_ref_marker: Option<String>,
    benefit_id: String,
    gti: String,
) {
    let mut params: Map<String, Value> = Map::new();
    params.insert(
        "buttonRefMarker".to_string(),
        Value::String(playback_ref_marker),
    );
    params.insert(
        "containerRefMarker".to_string(),
        Value::String(container_ref_marker.unwrap_or_default()),
    );
    params.insert(
        "subscriptionType".to_string(),
        Value::String(SVOD_ACQUISITION_PAGE_TYPE.to_string()),
    );
    params.insert("subscriptionSubType".to_string(), Value::String(benefit_id));
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(LOAD_ACQUISITION, SOURCE, Some(params));
}

pub fn csm_report_prime_acquisition_button_shown(
    app_event_reporter: AppEventReporter,
    button_ref_marker: Option<String>,
    container_ref_marker: Option<String>,
    gti: String,
) {
    let mut params: Map<String, Value> = Map::new();
    params.insert(
        "buttonRefMarker".to_string(),
        Value::String(button_ref_marker.unwrap_or_default()),
    );
    params.insert(
        "containerRefMarker".to_string(),
        Value::String(container_ref_marker.unwrap_or_default()),
    );
    params.insert(
        "subscriptionType".to_string(),
        Value::String(PRIME_ACQUISITION_PAGE_TYPE.to_string()),
    );
    params.insert(
        "subscriptionSubType".to_string(),
        Value::String(PRIME_ACQUISITION_SUBPAGE_TYPE.to_string()),
    );
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(LOAD_ACQUISITION, SOURCE, Some(params));
}

pub fn report_missing_title_actions_app_fatal(app_event_reporter: AppEventReporter) {
    log::error!("AppFatal#SSMDataProvider found no title actions in ContextualMenuProperties");
    app_event_reporter.send_app_event(MISSING_TITLE_ACTIONS_FATAL, SOURCE, None);
}

pub fn report_save_intent_failure_app_fatal(scope: Scope) {
    log::error!("AppFatal#Failed to save Record Season CustomerIntent selection");
    AppEventReporter::new(scope).send_app_event(SAVE_INTENT_FAILED_FATAL, SOURCE, None)
}

pub fn report_watch_modal_failed_app_fatal(app_event_reporter: AppEventReporter) {
    log::error!("AppFatal#WatchModal transform call failed");
    app_event_reporter.send_app_event(WATCH_MODAL_LOAD_FAILED_FATAL, SOURCE, None)
}

pub fn report_watch_modal_launch(
    app_event_reporter: AppEventReporter,
    ref_marker_prefix: Option<String>,
    gti: String,
) {
    let ref_marker = if let Some(mut ref_marker_prefix) = ref_marker_prefix {
        ref_marker_prefix.push_str("_live_mod");
        ref_marker_prefix
    } else {
        String::default()
    };

    let mut params: Map<String, Value> = Map::new();

    params.insert("refMarker".to_string(), Value::String(ref_marker));
    // TODO: Update this pageType and subpageType based on the page from which Watch Modal is opened
    params.insert("pageType".to_string(), Value::String("ATVHome".to_string()));
    params.insert("subPageType".to_string(), Value::String("home".to_string()));
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(OPEN_WATCH_MODAL, SOURCE, Some(params));
}

pub fn report_watch_modal_close(scope: Scope, ref_marker_prefix: Option<String>, gti: String) {
    let ref_marker = if let Some(mut ref_marker_prefix) = ref_marker_prefix {
        ref_marker_prefix.push_str("_live_mod_back");
        ref_marker_prefix
    } else {
        String::default()
    };

    let mut params: Map<String, Value> = Map::new();
    params.insert("refMarker".to_string(), Value::String(ref_marker));
    params.insert("gti".to_string(), Value::String(gti));

    AppEventReporter::new(scope).send_app_event(LEAVE_WATCH_MODAL, SOURCE, Some(params));
}

pub fn csm_report_watchlist_button_clicked(
    scope: Scope,
    ref_marker_prefix: Option<String>,
    gti: String,
    is_in_watchlist: bool,
) {
    let ref_marker_suffix = if is_in_watchlist {
        "_live_wtl_remove"
    } else {
        "_live_wtl_add"
    };
    let ref_marker = if let Some(mut ref_marker_prefix) = ref_marker_prefix {
        ref_marker_prefix.push_str(ref_marker_suffix);
        ref_marker_prefix
    } else {
        String::default()
    };

    let mut params: Map<String, Value> = Map::new();
    params.insert("refMarker".to_string(), Value::String(ref_marker));
    params.insert("isAdd".into(), Value::Bool(!is_in_watchlist));
    params.insert("gti".to_string(), Value::String(gti));

    AppEventReporter::new(scope).send_app_event(CLICK_WATCHLIST, SOURCE, Some(params));
}

pub fn csm_report_more_details_button_clicked(
    scope: Scope,
    ref_marker_prefix: Option<String>,
    gti: String,
) {
    let ref_marker = if let Some(mut ref_marker_prefix) = ref_marker_prefix {
        ref_marker_prefix.push_str("_live_dp");
        ref_marker_prefix
    } else {
        String::default()
    };

    let mut params: Map<String, Value> = Map::new();
    params.insert("refMarker".to_string(), Value::String(ref_marker));
    params.insert("gti".to_string(), Value::String(gti));

    AppEventReporter::new(scope).send_app_event(CLICK_MORE_DETAILS, SOURCE, Some(params));
}
