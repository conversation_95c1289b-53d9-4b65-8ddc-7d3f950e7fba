use crate::watch_modal_events::types::*;
#[double]
use cross_app_events::app_event::AppEventReporter;
use log::error;
use mockall_double::double;
use serde_json::{Map, Value};

const SSM_DETAILS_LAUNCH_REFMARKER: &str = "atv_dp_atf_live_mod";

pub fn report_hero_ssm_launch(
    app_event_reporter: AppEventReporter,
    ref_marker_prefix: Option<String>,
    gti: String,
) {
    let ref_marker = if let Some(mut ref_marker_prefix) = ref_marker_prefix {
        ref_marker_prefix.push_str("_live_mod");
        ref_marker_prefix
    } else {
        error!(
            "[report_hero_ssm_launch] No refmarker found for gti {}",
            gti
        );
        String::default()
    };

    let mut params: Map<String, Value> = Map::new();

    params.insert("refMarker".to_string(), Value::String(ref_marker));
    params.insert("pageType".to_string(), Value::String("ATVHome".to_string()));
    params.insert("subPageType".to_string(), Value::String("home".to_string()));
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(OPEN_WATCH_MODAL, SOURCE, Some(params));
}

pub fn report_hero_ssm_close(
    app_event_reporter: AppEventReporter,
    ref_marker_prefix: Option<String>,
    gti: String,
) {
    let ref_marker = if let Some(mut ref_marker_prefix) = ref_marker_prefix {
        ref_marker_prefix.push_str("_live_mod_back");
        ref_marker_prefix
    } else {
        error!("[report_hero_ssm_close] No refmarker found for gti {}", gti);
        String::default()
    };

    let mut params: Map<String, Value> = Map::new();
    params.insert("refMarker".to_string(), Value::String(ref_marker));
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(LEAVE_WATCH_MODAL, SOURCE, Some(params));
}

pub fn report_details_ssm_launch(app_event_reporter: AppEventReporter, gti: String) {
    let mut params: Map<String, Value> = Map::new();
    params.insert(
        "refMarker".to_string(),
        Value::String(SSM_DETAILS_LAUNCH_REFMARKER.to_string()),
    );
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(OPEN_MODAL, SOURCE, Some(params));
}

pub fn report_details_ssm_close(app_event_reporter: AppEventReporter, gti: String) {
    let mut params: Map<String, Value> = Map::new();
    let mut ref_marker = SSM_DETAILS_LAUNCH_REFMARKER.to_string();
    ref_marker.push_str("_back");

    params.insert("refMarker".to_string(), Value::String(ref_marker));
    params.insert("gti".to_string(), Value::String(gti));

    app_event_reporter.send_app_event(LEAVE_MODAL, SOURCE, Some(params));
}

#[cfg(test)]
mod tests {
    use super::*;
    use cross_app_events::app_event::MockAppEventReporter;
    use ignx_compositron::app::launch_only_scope;
    use log::Level;

    #[test]
    fn should_report_details_ssm_launch() {
        launch_only_scope(|_| {
            let mut mock_reporter = MockAppEventReporter::default();
            let mut expected_params = Map::new();
            expected_params.insert(
                "refMarker".to_string(),
                Value::String("atv_dp_atf_live_mod".to_string()),
            );
            expected_params.insert("gti".to_string(), Value::String("test_gti".to_string()));

            mock_reporter
                .expect_send_app_event()
                .withf(move |name, source, params| {
                    assert_eq!(name, OPEN_MODAL);
                    assert_eq!(source, SOURCE);
                    let params_inner = params.clone().expect("params should not be None");
                    assert_eq!(params_inner, expected_params);
                    true
                })
                .once()
                .return_const({});

            report_details_ssm_launch(mock_reporter, "test_gti".to_string());
        })
    }
    #[test]
    fn should_report_details_ssm_close() {
        launch_only_scope(|_| {
            let mut mock_reporter = MockAppEventReporter::default();
            let mut expected_params = Map::new();
            expected_params.insert(
                "refMarker".to_string(),
                Value::String("atv_dp_atf_live_mod_back".to_string()),
            );
            expected_params.insert("gti".to_string(), Value::String("test_gti".to_string()));

            mock_reporter
                .expect_send_app_event()
                .withf(move |name, source, params| {
                    assert_eq!(name, LEAVE_MODAL);
                    assert_eq!(source, SOURCE);
                    let params_inner = params.clone().expect("params should not be None");
                    assert_eq!(params_inner, expected_params);
                    true
                })
                .once()
                .return_const({});

            report_details_ssm_close(mock_reporter, "test_gti".to_string());
        })
    }

    #[test]
    fn should_report_hero_ssm_close() {
        launch_only_scope(|_| {
            let mut mock_reporter = MockAppEventReporter::default();
            let mut expected_params = Map::new();
            expected_params.insert(
                "refMarker".to_string(),
                Value::String("container_refmarker_live_mod_back".to_string()),
            );
            expected_params.insert("gti".to_string(), Value::String("test_gti".to_string()));

            mock_reporter
                .expect_send_app_event()
                .withf(move |name, source, params| {
                    assert_eq!(name, LEAVE_WATCH_MODAL);
                    assert_eq!(source, SOURCE);
                    let params_inner = params.clone().expect("params should not be None");
                    assert_eq!(params_inner, expected_params);
                    true
                })
                .once()
                .return_const({});

            report_hero_ssm_close(
                mock_reporter,
                Some("container_refmarker".to_string()),
                "test_gti".to_string(),
            );
        })
    }

    #[test]
    fn should_report_hero_ssm_launch() {
        launch_only_scope(|_| {
            let mut mock_reporter = MockAppEventReporter::default();
            let mut expected_params = Map::new();
            expected_params.insert(
                "refMarker".to_string(),
                Value::String("container_refmarker_live_mod".to_string()),
            );
            expected_params.insert("gti".to_string(), Value::String("test_gti".to_string()));
            expected_params.insert("pageType".to_string(), Value::String("ATVHome".to_string()));
            expected_params.insert("subPageType".to_string(), Value::String("home".to_string()));

            mock_reporter
                .expect_send_app_event()
                .withf(move |name, source, params| {
                    assert_eq!(name, OPEN_WATCH_MODAL);
                    assert_eq!(source, SOURCE);
                    let params_inner = params.clone().expect("params should not be None");
                    assert_eq!(params_inner, expected_params);
                    true
                })
                .once()
                .return_const({});

            report_hero_ssm_launch(
                mock_reporter,
                Some("container_refmarker".to_string()),
                "test_gti".to_string(),
            );
        })
    }

    #[test]
    fn should_log_error_if_hero_launch_refmarker_is_none() {
        launch_only_scope(|_| {
            testing_logger::setup();
            let mut mock_reporter = MockAppEventReporter::default();
            let mut expected_params = Map::new();
            expected_params.insert("refMarker".to_string(), Value::String(String::default()));
            expected_params.insert("gti".to_string(), Value::String("test_gti".to_string()));
            expected_params.insert("pageType".to_string(), Value::String("ATVHome".to_string()));
            expected_params.insert("subPageType".to_string(), Value::String("home".to_string()));

            mock_reporter
                .expect_send_app_event()
                .withf(move |name, source, params| {
                    assert_eq!(name, OPEN_WATCH_MODAL);
                    assert_eq!(source, SOURCE);
                    let params_inner = params.clone().expect("params should not be None");
                    assert_eq!(params_inner, expected_params);
                    true
                })
                .once()
                .return_const({});

            report_hero_ssm_launch(mock_reporter, None, "test_gti".to_string());
            testing_logger::validate(|logs| {
                let mut filtered_logs = logs.iter().filter(|log| log.level == Level::Error);
                assert!(filtered_logs.any(|log| log.body
                    == "[report_hero_ssm_launch] No refmarker found for gti test_gti"));
            });
        })
    }

    #[test]
    fn should_log_error_if_hero_close_refmarker_is_none() {
        launch_only_scope(|_| {
            testing_logger::setup();
            let mut mock_reporter = MockAppEventReporter::default();
            let mut expected_params = Map::new();
            expected_params.insert("refMarker".to_string(), Value::String(String::default()));
            expected_params.insert("gti".to_string(), Value::String("test_gti".to_string()));

            mock_reporter
                .expect_send_app_event()
                .withf(move |name, source, params| {
                    assert_eq!(name, LEAVE_WATCH_MODAL);
                    assert_eq!(source, SOURCE);
                    let params_inner = params.clone().expect("params should not be None");
                    assert_eq!(params_inner, expected_params);
                    true
                })
                .once()
                .return_const({});

            report_hero_ssm_close(mock_reporter, None, "test_gti".to_string());
            testing_logger::validate(|logs| {
                let mut filtered_logs = logs.iter().filter(|log| log.level == Level::Error);
                assert!(filtered_logs
                    .any(|log| log.body
                        == "[report_hero_ssm_close] No refmarker found for gti test_gti"));
            });
        })
    }
}
