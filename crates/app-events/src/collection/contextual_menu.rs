use cross_app_events::app_event::AppEventReporter;
use serde_json::{Map, Value};

const SOURCE: &str = "CONTEXTUAL_MENU";
const ADD_TO_WATCHLIST_EVENT_NAME: &str = "ADD_TO_WATCHLIST_PRESSED";
const REMOVE_FROM_WATCHLIST_EVENT_NAME: &str = "REMOVE_FROM_WATCHLIST_PRESSED";
const HIDE_THIS_PRESSED_EVENT_NAME: &str = "HIDE_THIS_PRESSED";
const UNHIDE_THIS_PRESSED_EVENT_NAME: &str = "UNHIDE_THIS_PRESSED";
const MORE_DETAILS_PRESSED_EVENT_NAME: &str = "MORE_DETAILS_PRESSED";
const WATCH_NOW_BUTTON_PRESSED_EVENT_NAME: &str = "WATCH_NOW_BUTTON_PRESSED";
const SUBSCRIBE_BUTTON_PRESSED_EVENT_NAME: &str = "SUBSCRIPTION_BUTTON_PRESSED";
const CONTEXTUAL_MENU_OPEN_EVENT_NAME: &str = "CONTEXTUAL_MENU_OPEN";
const CONTEXTUAL_MENU_CLOSE_EVENT_NAME: &str = "CONTEXTUAL_MENU_CLOSE";
const CONTEXTUAL_MENU_PAGETYPE_AND_SUBPAGETYPE_CLEAR: &str =
    "CONTEXTUAL_MENU_PAGETYPE_AND_SUBPAGETYPE_CLEAR";

pub fn report_watchlist_pressed(
    app_event_reporter: AppEventReporter,
    is_in_watchlist: bool,
    gti: String,
    ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    params.insert("isPill".into(), Value::Bool(false));
    params.insert("titleId".to_string(), Value::String(gti));
    if let Some(ref_marker) = ref_marker {
        params.insert("refMarkerPrefix".to_string(), Value::String(ref_marker));
    }

    let event_name = if is_in_watchlist {
        REMOVE_FROM_WATCHLIST_EVENT_NAME
    } else {
        ADD_TO_WATCHLIST_EVENT_NAME
    };

    app_event_reporter.send_app_event(event_name, SOURCE, Some(params));
}

pub fn report_hide_pressed(
    app_event_reporter: AppEventReporter,
    is_content_hidden: bool,
    ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    if let Some(ref_marker) = ref_marker {
        params.insert("refMarkerPrefix".to_string(), Value::String(ref_marker));
    }

    let event_name = if is_content_hidden {
        UNHIDE_THIS_PRESSED_EVENT_NAME
    } else {
        HIDE_THIS_PRESSED_EVENT_NAME
    };

    app_event_reporter.send_app_event(event_name, SOURCE, Some(params));
}

pub fn report_more_details_pressed(
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    if let Some(ref_marker) = ref_marker {
        params.insert("refMarkerPrefix".to_string(), Value::String(ref_marker));
    }

    app_event_reporter.send_app_event(MORE_DETAILS_PRESSED_EVENT_NAME, SOURCE, Some(params));
}

pub fn report_watch_now_button_pressed(
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    if let Some(ref_marker) = ref_marker {
        params.insert("refMarkerPrefix".to_string(), Value::String(ref_marker));
    }

    app_event_reporter.send_app_event(WATCH_NOW_BUTTON_PRESSED_EVENT_NAME, SOURCE, Some(params));
}

pub fn report_subscribe_button_pressed(
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    if let Some(ref_marker) = ref_marker {
        params.insert("refMarkerPrefix".to_string(), Value::String(ref_marker));
    }

    app_event_reporter.send_app_event(SUBSCRIBE_BUTTON_PRESSED_EVENT_NAME, SOURCE, Some(params));
}

pub fn report_open_close_contextual_menu(
    app_event_reporter: AppEventReporter,
    will_open: bool,
    ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    if let Some(ref_marker) = ref_marker {
        params.insert("refMarkerPrefix".to_string(), Value::String(ref_marker));
    }

    let event_name = if will_open {
        CONTEXTUAL_MENU_OPEN_EVENT_NAME
    } else {
        CONTEXTUAL_MENU_CLOSE_EVENT_NAME
    };

    app_event_reporter.send_app_event(event_name, SOURCE, Some(params));

    if !will_open {
        report_clear_page_metadata(app_event_reporter);
    }
}

pub fn report_clear_page_metadata(app_event_reporter: AppEventReporter) {
    app_event_reporter.send_app_event(CONTEXTUAL_MENU_PAGETYPE_AND_SUBPAGETYPE_CLEAR, SOURCE, None);
}

pub fn report_page_metadata_change(
    app_event_reporter: AppEventReporter,
    page_type: String,
    sub_page_type: String,
) {
    let mut params: Map<String, Value> = Map::new();

    params.insert("pageType".to_string(), Value::String(page_type));
    params.insert("subPageType".to_string(), Value::String(sub_page_type));

    app_event_reporter.send_app_event(
        CONTEXTUAL_MENU_PAGETYPE_AND_SUBPAGETYPE_CLEAR,
        SOURCE,
        Some(params),
    );
}
