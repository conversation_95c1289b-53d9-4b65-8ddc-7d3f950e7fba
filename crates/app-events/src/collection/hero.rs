use std::collections::HashMap;

use crate::common::card_event::{log_card_select_event, CardType};
use common_transform_types::actions::{TitleActionMetadata, TransitionAction};
use cross_app_events::app_event::AppEventReporter;
use ignx_compositron::{input::KeyCode, reactive::Scope};
use location::{PageType, RustPage};
use serde_json::{Map, Value};

// Cross reference: https://code.amazon.com/packages/AVLivingRoomClient/blobs/4cdf7e6e7a30583b0335ed7b4b2ee1eb8dfeb827/--/packages/avlrc-container-events/types/ContainerEvents.ts#L8
const SOURCE: &str = "CONTAINERS";
const EVENT_NAME: &str = "HERO_PAGINATION";
const CONTAINER_PLAY_BUTTON_PRESSED: &str = "CONTAINER_PLAY_BUTTON_PRESSED";
const CONTAINER_TVOD_PURCHASE_BUTTON_PRESSED: &str = "CONTAINER_TVOD_PURCHASE_BUTTON_PRESSED";
const CONTAINER_PRIME_SIGN_UP_BUTTON_PRESSED: &str = "CONTAINER_PRIME_SIGN_UP_BUTTON_PRESSED";
const CONTAINER_SVOD_SIGN_UP_BUTTON_PRESSED: &str = "CONTAINER_SVOD_SIGN_UP_BUTTON_PRESSED";

pub fn report_hero_scroll_clickstream(scope: Scope, index: usize, direction_code: KeyCode) {
    let mut params: Map<String, Value> = Map::new();

    let direction = match direction_code {
        KeyCode::Left => "left",
        KeyCode::Right => "right",
        _ => "",
    };

    params.insert("index".into(), Value::Number(index.into()));
    params.insert("direction".into(), Value::String(direction.to_string()));

    AppEventReporter::new(scope).send_app_event(EVENT_NAME, SOURCE, Some(params));
}

pub fn report_hero_action_button_pressed(
    scope: Scope,
    action: Option<TransitionAction>,
    item_index: usize,
    carousel_analytics: HashMap<String, String>,
    container_index: usize,
) {
    let mut params: Map<String, Value> = Map::new();

    params.insert("titleIndex".into(), Value::Number(item_index.into()));
    params.insert(
        "containerIndex".into(),
        Value::Number(container_index.into()),
    );

    let ref_marker = carousel_analytics
        .get("refMarker")
        .map_or(Value::Null, |marker| Value::String(marker.to_owned()));

    params.insert("containerRefMarker".into(), ref_marker);

    let app_event_reporter = AppEventReporter::new(scope);

    if let Some(action) = action {
        match action {
            TransitionAction::acquisition(action) => {
                if let Some(metadata) = action.clone().metadata {
                    match metadata {
                        TitleActionMetadata::AcquisitionTVOD(metadata) => {
                            params.insert("offerType".into(), Value::String(metadata.offerType));
                            params.insert(
                                "videoQuality".into(),
                                Value::String(
                                    metadata
                                        .videoQuality
                                        .map_or_else(String::new, |quality| quality),
                                ),
                            );
                            params
                                .insert("actionRefMarker".into(), Value::String(action.refMarker));

                            app_event_reporter.send_app_event(
                                CONTAINER_TVOD_PURCHASE_BUTTON_PRESSED,
                                SOURCE,
                                Some(params),
                            );
                        }
                        TitleActionMetadata::AcquisitionPrime(metadata) => {
                            params.insert(
                                "actionRefMarker".into(),
                                Value::String(
                                    metadata.refMarker.map_or_else(String::new, |marker| marker),
                                ),
                            );

                            app_event_reporter.send_app_event(
                                CONTAINER_PRIME_SIGN_UP_BUTTON_PRESSED,
                                SOURCE,
                                Some(params),
                            );
                        }
                        TitleActionMetadata::AcquisitionSVOD(metadata) => {
                            params.insert(
                                "actionRefMarker".into(),
                                Value::String(metadata.refMarker),
                            );
                            params.insert("benefitId".into(), Value::String(metadata.benefitId));

                            app_event_reporter.send_app_event(
                                CONTAINER_SVOD_SIGN_UP_BUTTON_PRESSED,
                                SOURCE,
                                Some(params),
                            );
                        }
                        TitleActionMetadata::Playback(metadata) => {
                            params.insert(
                                "actionRefMarker".into(),
                                Value::String(metadata.refMarker),
                            );

                            app_event_reporter.send_app_event(
                                CONTAINER_PLAY_BUTTON_PRESSED,
                                SOURCE,
                                Some(params),
                            );
                        }
                        _ => log_card_select_event(
                            scope,
                            Some(TransitionAction::acquisition(action)),
                            &CardType::OtherCard(PageType::Rust(RustPage::RUST_COLLECTIONS)),
                        ),
                    }
                }
            }
            TransitionAction::playback(playback_action) => {
                params.insert(
                    "actionRefMarker".into(),
                    Value::String(playback_action.refMarker),
                );

                app_event_reporter.send_app_event(
                    CONTAINER_PLAY_BUTTON_PRESSED,
                    SOURCE,
                    Some(params),
                );
            }
            _ => log_card_select_event(
                scope,
                Some(action),
                &CardType::OtherCard(PageType::Rust(RustPage::RUST_COLLECTIONS)),
            ),
        }
    }
}
