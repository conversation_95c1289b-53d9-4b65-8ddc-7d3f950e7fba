use cross_app_events::app_event::AppEventReporter;
use serde_json::{Map, Value};

const SOURCE: &str = "EDUCATIONAL_CX";
const EDUCATIONAL_CX_PAGE_DISPLAYED_EVENT_NAME: &str = "EDUCATIONAL_CX_PAGE_DISPLAYED";
const EDUCATIONAL_CX_BUTTON_CLICKED_EVENT_NAME: &str = "EDUCATIONAL_CX_BUTTON_CLICKED";

pub fn report_button_clicked(
    app_event_reporter: AppEventReporter,
    button_ref_marker: Option<String>,
    page_ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    let concatenated_string: Option<String> = Some(
        [page_ref_marker, button_ref_marker]
            .map(Option::unwrap_or_default)
            .concat(),
    )
    .filter(|s| !s.is_empty());

    if let Some(concatenated_string) = concatenated_string {
        params.insert("refMarker".into(), Value::String(concatenated_string));
    }

    params.insert("action".into(), "Click".into());
    params.insert("pageType".into(), "ATVModal".into());
    params.insert("subPageType".into(), "CriticalNotification".into());
    params.insert("hitType".into(), "pageHit".into());

    app_event_reporter.send_app_event(
        EDUCATIONAL_CX_BUTTON_CLICKED_EVENT_NAME,
        SOURCE,
        Some(params),
    );
}

pub fn report_page_display(
    app_event_reporter: AppEventReporter,
    ref_marker: Option<String>,
    container_metadata_ref_marker: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    let concatenated_string: Option<String> = match (ref_marker, container_metadata_ref_marker) {
        (Some(ref_marker_str), Some(container_metadata_ref_marker_str)) => Some(format!(
            "{}{}",
            ref_marker_str, container_metadata_ref_marker_str
        )),
        (Some(ref_marker_str), None) => Some(ref_marker_str),
        (None, Some(container_metadata_ref_marker_str)) => Some(container_metadata_ref_marker_str),
        (None, None) => None,
    };

    if let Some(concatenated_string) = concatenated_string {
        params.insert("refMarker".into(), Value::String(concatenated_string));
    }

    params.insert("action".into(), "ModalRendered".into());
    params.insert("pageType".into(), "ATVModal".into());
    params.insert("subPageType".into(), "CriticalNotification".into());
    params.insert("hitType".into(), "popUp".into());

    app_event_reporter.send_app_event(
        EDUCATIONAL_CX_PAGE_DISPLAYED_EVENT_NAME,
        SOURCE,
        Some(params),
    );
}
