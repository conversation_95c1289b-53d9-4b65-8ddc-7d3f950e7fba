use common_transform_types::actions::TransitionAction;
use cross_app_events::app_event::AppEventReporter;
use ignx_compositron::reactive::Scope;
use location::PageType;
use mockall::automock;
use serde_json::{Map, Value};
use std::collections::HashMap;

const CARD: &str = "CARD";
const CARD_CLICKED: &str = "CARD_CLICKED";

// ShortCarouselCard and LeadingButtonCard currently only exist on collections pages so we don't
// need to add logic to account for other pages.
#[derive(Clone)]
pub enum CardType {
    ShortCarouselCard,
    LeadingButtonCard,
    OtherCard(PageType),
}

pub const DETAILS_CLICKSTREAM_PAGE_TYPE: &str = "ATVDetail";
pub const BROWSE_CLICKSTREAM_PAGE_TYPE: &str = "ATVBrowse";
pub const MANAGE_CLICKSTREAM_PAGE_TYPE: &str = "ATVManage";
pub const SUBSCRIPTION_CLICKSTREAM_PAGE_TYPE: &str = "Subscription";

pub fn log_card_select_event(scope: Scope, action: Option<TransitionAction>, card_type: &CardType) {
    let mut params: Map<String, Value> = Map::new();

    if let Some(action) = action {
        let clickstream_page_params = generate_clickstream_page_params(card_type, &action);

        if let Some(clickstream_page_params) = clickstream_page_params {
            params.insert(
                "clickstreamPageParams".into(),
                Value::Object(clickstream_page_params),
            );
        }

        match action {
            TransitionAction::landing(action)
            | TransitionAction::registration(action)
            | TransitionAction::settings(action)
            | TransitionAction::search(action)
            | TransitionAction::clientSearch(action)
            | TransitionAction::watchList(action)
            | TransitionAction::yvl(action)
            | TransitionAction::legacyDetail(action)
            | TransitionAction::primeSignUp(action)
            | TransitionAction::detail(action)
            | TransitionAction::tournament(action)
            | TransitionAction::category(action)
            | TransitionAction::linearStationDetail(action)
            | TransitionAction::store(action)
            | TransitionAction::live(action)
            | TransitionAction::freeWithAds(action)
            | TransitionAction::myStuff(action)
            | TransitionAction::collection(action)
            | TransitionAction::testPlayground(action) => {
                params.insert(
                    "analytics".to_string(),
                    Value::Object(convert_analytics(action.analytics)),
                );

                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::signUp(action) => {
                params.insert(
                    "analytics".to_string(),
                    Value::Object(convert_analytics(action.analytics)),
                );
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::player(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::playback(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::playbackGroup(action) => {
                // only a card action for BonusScheduleCards on the Details Page
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::acquisition(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::manageSubscription(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::profileSettings(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::consent(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::browse(_action) => {}
            TransitionAction::openModal(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::fuseOfferActivation(action)
            | TransitionAction::sportsFavorites(action)
            | TransitionAction::removeFromFavorites(action)
            | TransitionAction::changeHomeRegion(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::primeRetention(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::sportsSchedule(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
            TransitionAction::liveSportsNoOp => {}
            TransitionAction::apiLink(action) => {
                params.insert("refMarker".to_string(), Value::String(action.refMarker));
            }
        };

        AppEventReporter::new(scope).send_app_event(CARD_CLICKED, CARD, Some(params));
    }
}

fn generate_clickstream_page_params(
    card_type: &CardType,
    action: &TransitionAction,
) -> Option<Map<String, Value>> {
    match card_type {
        CardType::ShortCarouselCard => {
            let mut clickstream_page_params: Map<String, Value> = Map::new();
            let sub_page_type = match action.clone() {
                TransitionAction::landing(action)
                | TransitionAction::registration(action)
                | TransitionAction::settings(action)
                | TransitionAction::search(action)
                | TransitionAction::clientSearch(action)
                | TransitionAction::watchList(action)
                | TransitionAction::yvl(action)
                | TransitionAction::legacyDetail(action)
                | TransitionAction::primeSignUp(action)
                | TransitionAction::detail(action)
                | TransitionAction::tournament(action)
                | TransitionAction::category(action)
                | TransitionAction::store(action)
                | TransitionAction::live(action)
                | TransitionAction::freeWithAds(action)
                | TransitionAction::myStuff(action)
                | TransitionAction::collection(action)
                | TransitionAction::testPlayground(action) => action.pageId,
                _ => "unknownPageType".into(),
            };

            clickstream_page_params.insert(
                "pageType".into(),
                Value::String(BROWSE_CLICKSTREAM_PAGE_TYPE.into()),
            );
            clickstream_page_params.insert("subPageType".into(), Value::String(sub_page_type));

            Some(clickstream_page_params)
        }
        CardType::LeadingButtonCard => {
            let mut clickstream_page_params: Map<String, Value> = Map::new();

            let page_type = match action.clone() {
                TransitionAction::manageSubscription(_action) => MANAGE_CLICKSTREAM_PAGE_TYPE,
                _ => BROWSE_CLICKSTREAM_PAGE_TYPE,
            };

            clickstream_page_params.insert("pageType".into(), Value::String(page_type.into()));
            clickstream_page_params.insert(
                "subPageType".into(),
                Value::String(SUBSCRIPTION_CLICKSTREAM_PAGE_TYPE.into()),
            );

            Some(clickstream_page_params)
        }

        CardType::OtherCard(_) => None,
    }
}
fn convert_analytics(analytics: HashMap<String, String>) -> Map<String, Value> {
    analytics
        .iter()
        .map(|(key, value)| (key.clone(), Value::String(value.clone())))
        .collect()
}

#[automock]
pub trait CardEventMock {
    fn log_card_select_event(&self, scope: Scope, action: Option<TransitionAction>);
}

//TODO uncomment the below test when dependencies fixed, since we are not able to import test_helpers from collection-ui
// so it should moved first to a separate crate so we can use it below

// #[cfg(test)]
// mod tests {

//     use std::vec;

//     use crate::parser::page_data::common::test_helpers::{
//         create_playback_action, create_signup_action, create_swift_action,
//         create_title_acquisition_action, create_title_playback_action,
//     };

//     use super::*;

//     use ignx_compositron::app::launch_only_scope;

//     #[test]
//     fn test_log_card_select_event() {
//         launch_only_scope(|scope| {
//             log_card_select_event(scope, None);

//             //TODO assert send_app_event not being called

//             let actions_with_analytics = vec![
//                 TransitionAction::landing(create_swift_action()),
//                 TransitionAction::registration(create_swift_action()),
//                 TransitionAction::settings(create_swift_action()),
//                 TransitionAction::search(create_swift_action()),
//                 TransitionAction::clientSearch(create_swift_action()),
//                 TransitionAction::watchList(create_swift_action()),
//                 TransitionAction::yvl(create_swift_action()),
//                 TransitionAction::legacyDetail(create_swift_action()),
//                 TransitionAction::primeSignUp(create_swift_action()),
//                 TransitionAction::detail(create_swift_action()),
//                 TransitionAction::tournament(create_swift_action()),
//                 TransitionAction::category(create_swift_action()),
//                 TransitionAction::store(create_swift_action()),
//                 TransitionAction::live(create_swift_action()),
//                 TransitionAction::freeWithAds(create_swift_action()),
//                 TransitionAction::myStuff(create_swift_action()),
//                 TransitionAction::collection(create_swift_action()),
//                 TransitionAction::testPlayground(create_swift_action()),
//                 TransitionAction::signUp(create_signup_action()),
//             ];

//             for action in actions_with_analytics {
//                 log_card_select_event(scope, Some(action));

//                 //TODO assert send_app_event being with refMarker and analytics
//             }

//             let actions_with_only_ref = vec![
//                 TransitionAction::playback(create_title_playback_action(true, false, None)),
//                 TransitionAction::player(create_playback_action()),
//                 TransitionAction::acquisition(create_title_acquisition_action()),
//             ];

//             for action in actions_with_only_ref {
//                 log_card_select_event(scope, Some(action));

//                 //TODO assert send_app_event being with only refMarker
//             }
//         })
//     }
// }
