#[double]
use cross_app_events::app_event::AppEventReporter;
use mockall_double::double;
use serde_json::{Map, Value};

const SERVICE_ANNOUNCEMENT_RENDERED: &str = "LIVE_DETAILS_PAGE_SERVICE_ANNOUNCEMENT_RENDERED";
const SERVICE_ANNOUNCEMENT_SOURCE: &str = "DETAILS_PAGE";
pub fn report_service_announcement_rendered(
    app_event_reporter: AppEventReporter,
    title_id: Option<String>,
) {
    let mut params: Map<String, Value> = Map::new();

    let gti = title_id.map_or("unknown".to_string(), |id| id);

    params.insert("titleId".to_string(), Value::String(gti));
    app_event_reporter.send_app_event(
        SERVICE_ANNOUNCEMENT_RENDERED,
        SERVICE_ANNOUNCEMENT_SOURCE,
        Some(params),
    );
}

#[cfg(test)]
mod tests {
    use super::*;
    use cross_app_events::app_event::MockAppEventReporter;
    use mockall::predicate::eq;

    #[test]
    fn test_report_service_announcement_rendered() {
        let mut app_event_reporter = MockAppEventReporter::default();
        let mut expected_params: Map<String, Value> = Map::new();
        expected_params.insert("titleId".to_string(), Value::String("testId".to_string()));

        app_event_reporter
            .expect_send_app_event()
            .with(
                eq(SERVICE_ANNOUNCEMENT_RENDERED),
                eq(SERVICE_ANNOUNCEMENT_SOURCE),
                eq(Some(expected_params)),
            )
            .times(1)
            .returning(|_, _, _| ());

        report_service_announcement_rendered(app_event_reporter, Some("testId".to_string()));
    }
}
