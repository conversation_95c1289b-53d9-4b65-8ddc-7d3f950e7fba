use super::common::{
    get_card_image_with_attributes, title_card_metadata_to_provider_logo_props,
    CardImageAndAttributes,
};
use crate::container_item_parsing::common::title_card_to_common_metadata;
use crate::ui_signals::{
    ChartCardUIData, ChartsCarouselCardData, ChartsCarouselItemModel, ChartsCarouselItemType,
    ChartsCarouselItemTypeWrapper,
};
use common_transform_types::container_items::EventCard;
use common_transform_types::container_items::{GenericTitleCard, MovieCard, SeriesCard, ShowCard};
use contextual_menu_types::prelude::*;
use fableous::tiles::tile::{TileBadgeProps, TileFontIconProps};
use ignx_compositron::prelude::{create_rw_signal, Scope};
use media_background::types::MediaBackgroundType;
use title_details::core::standard_titles_details_to_tts_details_data;
use title_details::types::common::StandardTitleDetailsData;
use uuid::Uuid;

pub fn charts_carousel_series_card(
    item: &SeriesCard,
    scope: Scope,
    td_data: StandardTitleDetailsData,
    mb_data: MediaBackgroundType,
    cm_data: ContextualMenuMetadata,
    carousel_analytics: Option<String>,
    rank: u32,
) -> Option<ChartsCarouselItemTypeWrapper> {
    let (item_metadata, card_id) = title_card_to_common_metadata(item, carousel_analytics, None)?;
    let box_art = item.titleCardBaseMetadata.boxartImage.as_ref();
    let cover_image = item.titleCardBaseMetadata.coverImage.as_ref();
    let is_adult = item
        .titleCardBaseMetadata
        .imageAttributes
        .as_ref()
        .and_then(|a| a.isAdult)
        .unwrap_or_default();
    let CardImageAndAttributes {
        image,
        base_attributes,
        secondary_base_image,
    } = get_card_image_with_attributes(box_art, cover_image, is_adult)?;
    let icon_props = item
        .titleCardBaseMetadata
        .entitlementMessaging
        .as_ref()
        .and_then(TileFontIconProps::from_entitlement_messaging_glance);
    let badge_props = TileBadgeProps::from_title_card_metadata(&item.titleCardBaseMetadata);
    let provider_logo_props =
        title_card_metadata_to_provider_logo_props(&item.titleCardBaseMetadata);
    let tts_details_data = standard_titles_details_to_tts_details_data(&td_data);

    // title cards must have image, no fallback text, no text overlay
    Some(ChartsCarouselItemTypeWrapper {
        uuid: Uuid::new_v4(),
        id: card_id,
        model: create_rw_signal(
            scope,
            ChartsCarouselItemType::Series(create_rw_signal(
                scope,
                ChartsCarouselItemModel {
                    title_details_data: create_rw_signal(scope, td_data),
                    media_background_data: create_rw_signal(scope, mb_data),
                    carousel_card_data: create_rw_signal(
                        scope,
                        ChartsCarouselCardData {
                            metadata: create_rw_signal(scope, item_metadata),
                            card_ui_data: create_rw_signal(
                                scope,
                                ChartCardUIData {
                                    title: create_rw_signal(scope, None),
                                    rank: create_rw_signal(scope, rank),
                                    progress_bar_props: create_rw_signal(scope, None),
                                    badge_props: create_rw_signal(scope, badge_props),
                                    icon_props: create_rw_signal(scope, icon_props),
                                    card_image: create_rw_signal(scope, Some(image)),
                                    card_secondary_base_image: create_rw_signal(
                                        scope,
                                        secondary_base_image,
                                    ),
                                    card_image_base_attributes: create_rw_signal(
                                        scope,
                                        base_attributes,
                                    ),
                                    fallback_text: create_rw_signal(scope, None),
                                    provider_logo_props: create_rw_signal(
                                        scope,
                                        provider_logo_props,
                                    ),
                                    tts_details_data: create_rw_signal(scope, tts_details_data),
                                },
                            ),
                        },
                    ),
                    contextual_menu_metadata: create_rw_signal(scope, cm_data),
                },
            )),
        ),
    })
}

pub fn charts_carousel_show_card(
    item: &ShowCard,
    scope: Scope,
    td_data: StandardTitleDetailsData,
    mb_data: MediaBackgroundType,
    cm_data: ContextualMenuMetadata,
    carousel_analytics: Option<String>,
    rank: u32,
) -> Option<ChartsCarouselItemTypeWrapper> {
    let (item_metadata, card_id) = title_card_to_common_metadata(item, carousel_analytics, None)?;
    let box_art = item.titleCardBaseMetadata.boxartImage.as_ref();
    let cover_image = item.titleCardBaseMetadata.coverImage.as_ref();
    let is_adult = item
        .titleCardBaseMetadata
        .imageAttributes
        .as_ref()
        .and_then(|a| a.isAdult)
        .unwrap_or_default();
    let CardImageAndAttributes {
        image,
        base_attributes,
        secondary_base_image,
    } = get_card_image_with_attributes(box_art, cover_image, is_adult)?;
    let icon_props = item
        .titleCardBaseMetadata
        .entitlementMessaging
        .as_ref()
        .and_then(TileFontIconProps::from_entitlement_messaging_glance);
    let badge_props = TileBadgeProps::from_title_card_metadata(&item.titleCardBaseMetadata);
    let provider_logo_props =
        title_card_metadata_to_provider_logo_props(&item.titleCardBaseMetadata);
    let tts_details_data = standard_titles_details_to_tts_details_data(&td_data);

    // title cards must have image, no fallback text, no text overlay
    Some(ChartsCarouselItemTypeWrapper {
        uuid: Uuid::new_v4(),
        id: card_id,
        model: create_rw_signal(
            scope,
            ChartsCarouselItemType::Show(create_rw_signal(
                scope,
                ChartsCarouselItemModel {
                    title_details_data: create_rw_signal(scope, td_data),
                    media_background_data: create_rw_signal(scope, mb_data),
                    carousel_card_data: create_rw_signal(
                        scope,
                        ChartsCarouselCardData {
                            metadata: create_rw_signal(scope, item_metadata),
                            card_ui_data: create_rw_signal(
                                scope,
                                ChartCardUIData {
                                    title: create_rw_signal(scope, None),
                                    rank: create_rw_signal(scope, rank),
                                    progress_bar_props: create_rw_signal(scope, None),
                                    badge_props: create_rw_signal(scope, badge_props),
                                    icon_props: create_rw_signal(scope, icon_props),
                                    card_image: create_rw_signal(scope, Some(image)),
                                    card_secondary_base_image: create_rw_signal(
                                        scope,
                                        secondary_base_image,
                                    ),
                                    card_image_base_attributes: create_rw_signal(
                                        scope,
                                        base_attributes,
                                    ),
                                    fallback_text: create_rw_signal(scope, None),
                                    provider_logo_props: create_rw_signal(
                                        scope,
                                        provider_logo_props,
                                    ),
                                    tts_details_data: create_rw_signal(scope, tts_details_data),
                                },
                            ),
                        },
                    ),
                    contextual_menu_metadata: create_rw_signal(scope, cm_data),
                },
            )),
        ),
    })
}

pub fn charts_carousel_movie_card(
    item: &MovieCard,
    scope: Scope,
    td_data: StandardTitleDetailsData,
    mb_data: MediaBackgroundType,
    cm_data: ContextualMenuMetadata,
    carousel_analytics: Option<String>,
    rank: u32,
) -> Option<ChartsCarouselItemTypeWrapper> {
    let (item_metadata, card_id) = title_card_to_common_metadata(item, carousel_analytics, None)?;
    let box_art = item.titleCardBaseMetadata.boxartImage.as_ref();
    let cover_image = item.titleCardBaseMetadata.coverImage.as_ref();
    let is_adult = item
        .titleCardBaseMetadata
        .imageAttributes
        .as_ref()
        .and_then(|a| a.isAdult)
        .unwrap_or_default();
    let CardImageAndAttributes {
        image,
        base_attributes,
        secondary_base_image,
    } = get_card_image_with_attributes(box_art, cover_image, is_adult)?;
    let icon_props = item
        .titleCardBaseMetadata
        .entitlementMessaging
        .as_ref()
        .and_then(TileFontIconProps::from_entitlement_messaging_glance);
    let badge_props = TileBadgeProps::from_title_card_metadata(&item.titleCardBaseMetadata);
    let provider_logo_props =
        title_card_metadata_to_provider_logo_props(&item.titleCardBaseMetadata);
    let tts_details_data = standard_titles_details_to_tts_details_data(&td_data);
    // title cards must have image, no fallback text, no text overlay
    Some(ChartsCarouselItemTypeWrapper {
        uuid: Uuid::new_v4(),
        id: card_id,
        model: create_rw_signal(
            scope,
            ChartsCarouselItemType::Movie(create_rw_signal(
                scope,
                ChartsCarouselItemModel {
                    title_details_data: create_rw_signal(scope, td_data),
                    media_background_data: create_rw_signal(scope, mb_data),
                    carousel_card_data: create_rw_signal(
                        scope,
                        ChartsCarouselCardData {
                            metadata: create_rw_signal(scope, item_metadata),
                            card_ui_data: create_rw_signal(
                                scope,
                                ChartCardUIData {
                                    title: create_rw_signal(scope, None),
                                    rank: create_rw_signal(scope, rank),
                                    progress_bar_props: create_rw_signal(scope, None),
                                    badge_props: create_rw_signal(scope, badge_props),
                                    icon_props: create_rw_signal(scope, icon_props),
                                    card_image: create_rw_signal(scope, Some(image)),
                                    card_secondary_base_image: create_rw_signal(
                                        scope,
                                        secondary_base_image,
                                    ),
                                    card_image_base_attributes: create_rw_signal(
                                        scope,
                                        base_attributes,
                                    ),
                                    fallback_text: create_rw_signal(scope, None),
                                    provider_logo_props: create_rw_signal(
                                        scope,
                                        provider_logo_props,
                                    ),
                                    tts_details_data: create_rw_signal(scope, tts_details_data),
                                },
                            ),
                        },
                    ),
                    contextual_menu_metadata: create_rw_signal(scope, cm_data),
                },
            )),
        ),
    })
}

pub fn charts_carousel_generic_card(
    item: &GenericTitleCard,
    scope: Scope,
    td_data: StandardTitleDetailsData,
    mb_data: MediaBackgroundType,
    cm_data: ContextualMenuMetadata,
    carousel_analytics: Option<String>,
    rank: u32,
) -> Option<ChartsCarouselItemTypeWrapper> {
    let (item_metadata, card_id) = title_card_to_common_metadata(item, carousel_analytics, None)?;
    let box_art = item.titleCardBaseMetadata.boxartImage.as_ref();
    let cover_image = item.titleCardBaseMetadata.coverImage.as_ref();
    let is_adult = item
        .titleCardBaseMetadata
        .imageAttributes
        .as_ref()
        .and_then(|a| a.isAdult)
        .unwrap_or_default();
    let CardImageAndAttributes {
        image,
        base_attributes,
        secondary_base_image,
    } = get_card_image_with_attributes(box_art, cover_image, is_adult)?;
    let icon_props = item
        .titleCardBaseMetadata
        .entitlementMessaging
        .as_ref()
        .and_then(TileFontIconProps::from_entitlement_messaging_glance);
    let badge_props = TileBadgeProps::from_title_card_metadata(&item.titleCardBaseMetadata);
    let provider_logo_props =
        title_card_metadata_to_provider_logo_props(&item.titleCardBaseMetadata);
    let tts_details_data = standard_titles_details_to_tts_details_data(&td_data);

    // title cards must have image, no fallback text, no text overlay
    Some(ChartsCarouselItemTypeWrapper {
        uuid: Uuid::new_v4(),
        id: card_id,
        model: create_rw_signal(
            scope,
            ChartsCarouselItemType::GenericTitleCard(create_rw_signal(
                scope,
                ChartsCarouselItemModel {
                    title_details_data: create_rw_signal(scope, td_data),
                    media_background_data: create_rw_signal(scope, mb_data),
                    carousel_card_data: create_rw_signal(
                        scope,
                        ChartsCarouselCardData {
                            metadata: create_rw_signal(scope, item_metadata),
                            card_ui_data: create_rw_signal(
                                scope,
                                ChartCardUIData {
                                    title: create_rw_signal(scope, None),
                                    rank: create_rw_signal(scope, rank),
                                    progress_bar_props: create_rw_signal(scope, None),
                                    badge_props: create_rw_signal(scope, badge_props),
                                    icon_props: create_rw_signal(scope, icon_props),
                                    card_image: create_rw_signal(scope, Some(image)),
                                    card_secondary_base_image: create_rw_signal(
                                        scope,
                                        secondary_base_image,
                                    ),
                                    card_image_base_attributes: create_rw_signal(
                                        scope,
                                        base_attributes,
                                    ),
                                    fallback_text: create_rw_signal(scope, None),
                                    provider_logo_props: create_rw_signal(
                                        scope,
                                        provider_logo_props,
                                    ),
                                    tts_details_data: create_rw_signal(scope, tts_details_data),
                                },
                            ),
                        },
                    ),
                    contextual_menu_metadata: create_rw_signal(scope, cm_data),
                },
            )),
        ),
    })
}

pub fn charts_carousel_event_card(
    item: &EventCard,
    scope: Scope,
    td_data: StandardTitleDetailsData,
    mb_data: MediaBackgroundType,
    cm_data: ContextualMenuMetadata,
    carousel_analytics: Option<String>,
    rank: u32,
) -> Option<ChartsCarouselItemTypeWrapper> {
    let (item_metadata, card_id) = title_card_to_common_metadata(item, carousel_analytics, None)?;
    let card_image = item.titleCardBaseMetadata.coverImage.to_owned()?;
    let icon_props = item
        .titleCardBaseMetadata
        .entitlementMessaging
        .as_ref()
        .and_then(TileFontIconProps::from_entitlement_messaging_glance);
    let badge_props = TileBadgeProps::from_event_card(item); // leave badges on event card
    let provider_logo_props =
        title_card_metadata_to_provider_logo_props(&item.titleCardBaseMetadata);
    let tts_details_data = standard_titles_details_to_tts_details_data(&td_data);

    // title cards must have image, no fallback text, no text overlay
    Some(ChartsCarouselItemTypeWrapper {
        uuid: Uuid::new_v4(),
        id: card_id,
        model: create_rw_signal(
            scope,
            ChartsCarouselItemType::Event(create_rw_signal(
                scope,
                ChartsCarouselItemModel {
                    title_details_data: create_rw_signal(scope, td_data),
                    media_background_data: create_rw_signal(scope, mb_data),
                    carousel_card_data: create_rw_signal(
                        scope,
                        ChartsCarouselCardData {
                            metadata: create_rw_signal(scope, item_metadata),
                            card_ui_data: create_rw_signal(
                                scope,
                                ChartCardUIData {
                                    title: create_rw_signal(scope, None),
                                    rank: create_rw_signal(scope, rank),
                                    progress_bar_props: create_rw_signal(scope, None),
                                    badge_props: create_rw_signal(scope, badge_props),
                                    icon_props: create_rw_signal(scope, icon_props),
                                    card_image: create_rw_signal(scope, Some(card_image)),
                                    card_secondary_base_image: create_rw_signal(scope, None),
                                    card_image_base_attributes: create_rw_signal(
                                        scope,
                                        Default::default(),
                                    ),
                                    fallback_text: create_rw_signal(scope, None),
                                    provider_logo_props: create_rw_signal(
                                        scope,
                                        provider_logo_props,
                                    ),
                                    tts_details_data: create_rw_signal(scope, tts_details_data),
                                },
                            ),
                        },
                    ),
                    contextual_menu_metadata: create_rw_signal(scope, cm_data),
                },
            )),
        ),
    })
}

#[cfg(test)]
mod tests {
    use super::super::common::test_helpers::*;
    use crate::ui_signals::CommonCarouselCardMetadata;
    use amzn_fable_tokens::{FableColor, FableIcon};
    use common_transform_types::actions::Action;
    use fableous::badges::label_badge::LabelBadgeColorScheme;
    use fableous::tiles::tile::{TileBadgeProps, TileFontIconProps};
    use fableous::utils::get_ignx_color;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::prelude::{RwSignal, SignalGetUntracked};
    use ignx_compositron::text::TextContent;
    use rstest::*;
    use title_details::types::common::TitleDetailsMetadata;

    use super::*;

    enum LivelinessOptions {
        None,
        Live,
        Upcoming,
    }

    fn store_icon_props(scope: Scope) -> TileFontIconProps {
        TileFontIconProps {
            icon: create_rw_signal(scope, FableIcon::STORE_FILLED.to_string()).into(),
            color: create_rw_signal(scope, get_ignx_color(FableColor::STORE)).into(),
        }
    }

    fn error_icon_props(scope: Scope) -> TileFontIconProps {
        TileFontIconProps {
            icon: create_rw_signal(scope, FableIcon::ERROR.to_string()).into(),
            color: create_rw_signal(scope, get_ignx_color(FableColor::ERROR)).into(),
        }
    }

    fn entitlement_options_to_icon_props(
        entitlement_options: &EntitlementOptions,
        scope: Scope,
    ) -> Option<TileFontIconProps> {
        match entitlement_options {
            EntitlementOptions::OfferIcon => Some(store_icon_props(scope)),
            EntitlementOptions::EntitledIcon => None,
            EntitlementOptions::ErrorIcon => Some(error_icon_props(scope)),
            EntitlementOptions::AdsIcon => None,
            EntitlementOptions::None => None,
            EntitlementOptions::EntitlementMessageWithIcon => None,
            EntitlementOptions::EntitlementMessageWithoutIcon => None,
            EntitlementOptions::HVM => None,
            EntitlementOptions::LegalMessage => None,
            EntitlementOptions::TitleMetadataBadgeInfo => None,
            EntitlementOptions::TitleMetadataBadgeInfoActive => None,
            EntitlementOptions::TitleMetadataBadgeInfoInactive => None,
            EntitlementOptions::TitleMetadataBadgeInfoHighlight => None,
        }
    }

    fn entitlement_options_to_badge_props(
        entitlement_options: &EntitlementOptions,
        scope: Scope,
    ) -> Option<TileBadgeProps> {
        match entitlement_options {
            EntitlementOptions::OfferIcon => None,
            EntitlementOptions::EntitledIcon => None,
            EntitlementOptions::ErrorIcon => None,
            EntitlementOptions::AdsIcon => None,
            EntitlementOptions::None => None,
            EntitlementOptions::EntitlementMessageWithIcon => None,
            EntitlementOptions::EntitlementMessageWithoutIcon => None,
            EntitlementOptions::HVM => None,
            EntitlementOptions::LegalMessage => None,
            EntitlementOptions::TitleMetadataBadgeInfo => Some(TileBadgeProps {
                text: create_rw_signal(scope, TextContent::from("badge message")).into(),
                color_scheme: create_rw_signal(scope, LabelBadgeColorScheme::PRIMARY).into(),
            }),
            EntitlementOptions::TitleMetadataBadgeInfoActive => Some(TileBadgeProps {
                text: create_rw_signal(scope, TextContent::from("badge message")).into(),
                color_scheme: create_rw_signal(scope, LabelBadgeColorScheme::PRIMARY).into(),
            }),
            EntitlementOptions::TitleMetadataBadgeInfoInactive => Some(TileBadgeProps {
                text: create_rw_signal(scope, TextContent::from("badge message")).into(),
                color_scheme: create_rw_signal(scope, LabelBadgeColorScheme::SECONDARY).into(),
            }),
            EntitlementOptions::TitleMetadataBadgeInfoHighlight => Some(TileBadgeProps {
                text: create_rw_signal(scope, TextContent::from("badge message")).into(),
                color_scheme: create_rw_signal(scope, LabelBadgeColorScheme::LIVE).into(),
            }),
        }
    }

    fn create_title_details() -> StandardTitleDetailsData {
        StandardTitleDetailsData {
            title_data: Default::default(),
            metadata: TitleDetailsMetadata::None,
            synopsis: None,
            entitlement_data: Default::default(),
        }
    }

    fn create_liveliness(liveliness_options: &LivelinessOptions) -> Option<String> {
        match liveliness_options {
            LivelinessOptions::None => None,
            LivelinessOptions::Live => Some("LIVE".to_string()),
            LivelinessOptions::Upcoming => Some("UPCOMING".to_string()),
        }
    }

    fn create_series_card(
        options: CardOptions,
        entitlement_options: &EntitlementOptions,
    ) -> SeriesCard {
        let (transform_item_id, action, gti, images) = properties_from_options(&options);
        SeriesCard {
            carouselCardMetadata: create_carousel_card_metadata(
                transform_item_id,
                action,
                Some("card title".to_string()),
                None,
            ),
            titleCardBaseMetadata: create_title_card_metadata(
                images,
                gti,
                entitlement_options,
                None,
            ),
            seasonNumber: None,
            numberOfSeasons: None,
            showName: None,
            episodeNumber: None,
            episodicSynopsis: None,
            linearAttributes: None,
        }
    }

    fn create_movie_card(
        options: CardOptions,
        entitlement_options: &EntitlementOptions,
    ) -> MovieCard {
        let (transform_item_id, action, gti, images) = properties_from_options(&options);
        MovieCard {
            carouselCardMetadata: create_carousel_card_metadata(
                transform_item_id,
                action,
                Some("card title".to_string()),
                None,
            ),
            titleCardBaseMetadata: create_title_card_metadata(
                images,
                gti,
                entitlement_options,
                None,
            ),
            linearAttributes: None,
        }
    }

    fn create_show_card(
        options: CardOptions,
        entitlement_options: &EntitlementOptions,
    ) -> ShowCard {
        let (transform_item_id, action, gti, images) = properties_from_options(&options);
        ShowCard {
            carouselCardMetadata: create_carousel_card_metadata(
                transform_item_id,
                action,
                Some("card title".to_string()),
                None,
            ),
            titleCardBaseMetadata: create_title_card_metadata(
                images,
                gti,
                entitlement_options,
                None,
            ),
            numberOfSeasons: None,
            showName: None,
        }
    }

    fn create_event_card(
        options: CardOptions,
        entitlement_options: &EntitlementOptions,
        liveliness: &LivelinessOptions,
    ) -> EventCard {
        let (transform_item_id, action, gti, images) = properties_from_options(&options);
        let liveliness = create_liveliness(liveliness);
        EventCard {
            carouselCardMetadata: create_carousel_card_metadata(
                transform_item_id,
                action,
                Some("card title".to_string()),
                None,
            ),
            titleCardBaseMetadata: create_title_card_metadata(
                images,
                gti,
                entitlement_options,
                None,
            ),
            eventMetadata: create_event_metadata(liveliness),
        }
    }

    fn create_generic_card(
        options: CardOptions,
        entitlement_options: &EntitlementOptions,
    ) -> GenericTitleCard {
        let (transform_item_id, action, gti, images) = properties_from_options(&options);
        GenericTitleCard {
            carouselCardMetadata: create_carousel_card_metadata(
                transform_item_id,
                action,
                Some("card title".to_string()),
                None,
            ),
            titleCardBaseMetadata: create_title_card_metadata(
                images,
                gti,
                entitlement_options,
                None,
            ),
        }
    }

    #[test]
    fn title_cards_dropped_if_no_cover_or_boxart_image() {
        let series_card = create_series_card(
            CardOptions::ImageOption(ImageOptions::AllImageMissing),
            &EntitlementOptions::None,
        );
        let movie_card = create_movie_card(
            CardOptions::ImageOption(ImageOptions::AllImageMissing),
            &EntitlementOptions::None,
        );
        let show_card = create_show_card(
            CardOptions::ImageOption(ImageOptions::AllImageMissing),
            &EntitlementOptions::None,
        );
        let event_card = create_event_card(
            CardOptions::ImageOption(ImageOptions::AllImageMissing),
            &EntitlementOptions::None,
            &LivelinessOptions::None,
        );
        let generic_card = create_generic_card(
            CardOptions::ImageOption(ImageOptions::AllImageMissing),
            &EntitlementOptions::None,
        );
        launch_only_scope(move |scope| {
            let converted_series_card = charts_carousel_series_card(
                &series_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                1,
            );
            let converted_movie_card = charts_carousel_movie_card(
                &movie_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                1,
            );
            let converted_show_card = charts_carousel_show_card(
                &show_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                1,
            );
            let converted_event_card = charts_carousel_event_card(
                &event_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                2,
            );
            let converted_generic_card = charts_carousel_generic_card(
                &generic_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                2,
            );
            assert!(converted_series_card.is_none());
            assert!(converted_movie_card.is_none());
            assert!(converted_show_card.is_none());
            assert!(converted_event_card.is_none());
            assert!(converted_generic_card.is_none());
        });
    }

    #[test]
    fn event_cards_dropped_if_no_cover_image() {
        let event_card = create_event_card(
            CardOptions::ImageOption(ImageOptions::CoverImageMissing),
            &EntitlementOptions::None,
            &LivelinessOptions::None,
        );
        launch_only_scope(move |scope| {
            let converted_event_card = charts_carousel_event_card(
                &event_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                2,
            );
            assert!(converted_event_card.is_none());
        });
    }

    #[test]
    fn cards_dropped_if_no_action() {
        let series_card = create_series_card(CardOptions::ActionMissing, &EntitlementOptions::None);
        let movie_card = create_movie_card(CardOptions::ActionMissing, &EntitlementOptions::None);
        let show_card = create_show_card(CardOptions::ActionMissing, &EntitlementOptions::None);
        let event_card = create_event_card(
            CardOptions::ActionMissing,
            &EntitlementOptions::None,
            &LivelinessOptions::None,
        );
        let generic_card =
            create_generic_card(CardOptions::ActionMissing, &EntitlementOptions::None);
        launch_only_scope(move |scope| {
            let converted_series_card = charts_carousel_series_card(
                &series_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                1,
            );
            let converted_movie_card = charts_carousel_movie_card(
                &movie_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                2,
            );
            let converted_show_card = charts_carousel_show_card(
                &show_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                3,
            );
            let converted_event_card = charts_carousel_event_card(
                &event_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                4,
            );
            let converted_generic_card = charts_carousel_generic_card(
                &generic_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                5,
            );
            assert!(converted_series_card.is_none());
            assert!(converted_movie_card.is_none());
            assert!(converted_show_card.is_none());
            assert!(converted_event_card.is_none());
            assert!(converted_generic_card.is_none());
        });
    }

    #[test]
    fn cards_dropped_if_action_not_transition() {
        let series_card =
            create_series_card(CardOptions::ActionIncorrect, &EntitlementOptions::None);
        let movie_card = create_movie_card(CardOptions::ActionIncorrect, &EntitlementOptions::None);
        let show_card = create_show_card(CardOptions::ActionIncorrect, &EntitlementOptions::None);
        let event_card = create_event_card(
            CardOptions::ActionIncorrect,
            &EntitlementOptions::None,
            &LivelinessOptions::None,
        );
        let generic_card =
            create_generic_card(CardOptions::ActionIncorrect, &EntitlementOptions::None);
        launch_only_scope(move |scope| {
            let converted_series_card = charts_carousel_series_card(
                &series_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                1,
            );
            let converted_movie_card = charts_carousel_movie_card(
                &movie_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                2,
            );
            let converted_show_card = charts_carousel_show_card(
                &show_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                3,
            );
            let converted_event_card = charts_carousel_event_card(
                &event_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                4,
            );
            let converted_generic_card = charts_carousel_generic_card(
                &generic_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                5,
            );
            assert!(converted_series_card.is_none());
            assert!(converted_movie_card.is_none());
            assert!(converted_show_card.is_none());
            assert!(converted_event_card.is_none());
            assert!(converted_generic_card.is_none());
        });
    }

    #[test]
    fn cards_dropped_if_no_id() {
        let series_card = create_series_card(CardOptions::IdMissing, &EntitlementOptions::None);
        let movie_card = create_movie_card(CardOptions::IdMissing, &EntitlementOptions::None);
        let show_card = create_show_card(CardOptions::IdMissing, &EntitlementOptions::None);
        let event_card = create_event_card(
            CardOptions::IdMissing,
            &EntitlementOptions::None,
            &LivelinessOptions::None,
        );
        let generic_card = create_generic_card(CardOptions::IdMissing, &EntitlementOptions::None);
        launch_only_scope(move |scope| {
            let converted_series_card = charts_carousel_series_card(
                &series_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                1,
            );
            let converted_movie_card = charts_carousel_movie_card(
                &movie_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                2,
            );
            let converted_show_card = charts_carousel_show_card(
                &show_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                3,
            );
            let converted_event_card = charts_carousel_event_card(
                &event_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                4,
            );
            let converted_generic_card = charts_carousel_generic_card(
                &generic_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                5,
            );
            assert!(converted_series_card.is_none());
            assert!(converted_movie_card.is_none());
            assert!(converted_show_card.is_none());
            assert!(converted_event_card.is_none());
            assert!(converted_generic_card.is_none());
        });
    }

    #[test]
    fn non_linear_cards_fallback_gti() {
        let series_card = create_series_card(
            CardOptions::TransformItemIdMissing,
            &EntitlementOptions::None,
        );
        let movie_card = create_movie_card(
            CardOptions::TransformItemIdMissing,
            &EntitlementOptions::None,
        );
        let show_card = create_show_card(
            CardOptions::TransformItemIdMissing,
            &EntitlementOptions::None,
        );
        let event_card = create_event_card(
            CardOptions::TransformItemIdMissing,
            &EntitlementOptions::None,
            &LivelinessOptions::None,
        );
        let generic_card = create_generic_card(
            CardOptions::TransformItemIdMissing,
            &EntitlementOptions::None,
        );
        launch_only_scope(move |scope| {
            let converted_series_card = charts_carousel_series_card(
                &series_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                1,
            )
            .expect("expected defined series card");
            let converted_movie_card = charts_carousel_movie_card(
                &movie_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                2,
            )
            .expect("expected defined movie card");
            let converted_show_card = charts_carousel_show_card(
                &show_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                3,
            )
            .expect("expected defined show card");
            let converted_event_card = charts_carousel_event_card(
                &event_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                4,
            )
            .expect("expected defined event card");
            let converted_generic_card = charts_carousel_generic_card(
                &generic_card,
                scope,
                create_title_details(),
                create_media_background(),
                create_contextual_menu_metadata(),
                None,
                5,
            )
            .expect("expected defined generic card");
            assert_eq!(converted_series_card.id, "card gti");
            assert_eq!(converted_movie_card.id, "card gti");
            assert_eq!(converted_show_card.id, "card gti");
            assert_eq!(converted_event_card.id, "card gti");
            assert_eq!(converted_generic_card.id, "card gti");
        });
    }

    fn common_card_props(
        item: RwSignal<ChartsCarouselItemModel>,
        td: StandardTitleDetailsData,
        mb: MediaBackgroundType,
        cm: ContextualMenuMetadata,
        card_action: Option<Action>,
        entitlement: EntitlementOptions,
        scope: Scope,
        card_ui_assertions: impl Fn(ChartCardUIData),
    ) {
        let item_model = item.get_untracked();
        let ChartsCarouselItemModel {
            title_details_data,
            media_background_data,
            carousel_card_data,
            contextual_menu_metadata,
        } = item_model;
        assert_eq!(title_details_data.get_untracked(), td);
        assert_eq!(media_background_data.get_untracked(), mb);
        assert_eq!(contextual_menu_metadata.get_untracked(), cm);
        let card_data = carousel_card_data.get_untracked();
        let ChartsCarouselCardData {
            metadata,
            card_ui_data,
        } = card_data;
        let metadata = metadata.get_untracked();
        let CommonCarouselCardMetadata { id, action, .. } = metadata;
        assert_eq!(id, "card id");
        let Action::TransitionAction(transition_action) =
            card_action.expect("expected defined action")
        else {
            panic!("Expected transition action");
        };
        assert_eq!(action, transition_action);
        let card_ui_data = card_ui_data.get_untracked();

        let icon_props = card_ui_data.icon_props.get_untracked();
        if let Some(expected_props) = entitlement_options_to_icon_props(&entitlement, scope) {
            assert!(icon_props.is_some());
            let TileFontIconProps { icon, color } = icon_props.unwrap();
            let TileFontIconProps {
                icon: expected_icon,
                color: expected_color,
            } = expected_props;
            assert_eq!(icon.get_untracked(), expected_icon.get_untracked());
            assert_eq!(color.get_untracked(), expected_color.get_untracked());
        } else {
            assert!(icon_props.is_none());
        }
        card_ui_assertions(card_ui_data);
    }

    fn title_card_props(
        item: RwSignal<ChartsCarouselItemModel>,
        td: StandardTitleDetailsData,
        mb: MediaBackgroundType,
        cm: ContextualMenuMetadata,
        card_action: Option<Action>,
        entitlement: EntitlementOptions,
        scope: Scope,
        extra_card_ui_assertions: impl Fn(ChartCardUIData),
    ) {
        let card_ui_assertions = |ui_data: ChartCardUIData| {
            let ChartCardUIData {
                title,
                progress_bar_props,
                badge_props: _,
                icon_props: _,
                card_image: _,
                card_secondary_base_image: _,
                card_image_base_attributes: _,
                fallback_text,
                provider_logo_props,
                rank,
                tts_details_data: _,
            } = ui_data;
            assert_eq!(title.get_untracked(), None);
            assert_eq!(progress_bar_props.get_untracked(), None);
            assert_eq!(fallback_text.get_untracked(), None);
            assert_eq!(provider_logo_props.get_untracked(), None);
            assert_eq!(rank.get_untracked(), 1);
            extra_card_ui_assertions(ui_data);
        };
        common_card_props(
            item,
            td,
            mb,
            cm,
            card_action,
            entitlement,
            scope,
            card_ui_assertions,
        );
    }

    fn vod_title_card_props(
        item: RwSignal<ChartsCarouselItemModel>,
        td: StandardTitleDetailsData,
        mb: MediaBackgroundType,
        cm: ContextualMenuMetadata,
        card_action: Option<Action>,
        entitlement: EntitlementOptions,
        card_options: CardOptions,
        scope: Scope,
    ) {
        let entitlement_clone = entitlement.clone();
        let vod_assertions = |ui_data: ChartCardUIData| {
            let badge_props = ui_data.badge_props.get_untracked();
            if let Some(expected_badge_props) =
                entitlement_options_to_badge_props(&entitlement_clone, scope)
            {
                assert!(badge_props.is_some());
                let TileBadgeProps { text, color_scheme } = badge_props.unwrap();
                let TileBadgeProps {
                    text: expected_text,
                    color_scheme: expected_color_scheme,
                } = expected_badge_props;
                assert_eq!(text.get_untracked(), expected_text.get_untracked());
                assert_eq!(
                    color_scheme.get_untracked(),
                    expected_color_scheme.get_untracked()
                );
            } else {
                assert!(badge_props.is_none());
            }
            match &card_options {
                CardOptions::ImageOption(ImageOptions::CoverImageMissing) => {
                    assert_eq!(
                        ui_data.card_image.get_untracked(),
                        Some("box art image".to_string())
                    );
                    assert!(ui_data
                        .card_image_base_attributes
                        .get_untracked()
                        .stretch_background
                        .has_stretch());
                    assert!(ui_data.card_image_base_attributes.get_untracked().blur);
                    assert_eq!(
                        ui_data.card_secondary_base_image.get_untracked(),
                        Some("box art image".to_string())
                    );
                }
                CardOptions::ImageOption(ImageOptions::CoverImageAdult) => {
                    assert_eq!(
                        ui_data.card_image.get_untracked(),
                        Some("card image".to_string())
                    );
                    assert!(!ui_data
                        .card_image_base_attributes
                        .get_untracked()
                        .stretch_background
                        .has_stretch());
                    assert!(ui_data.card_image_base_attributes.get_untracked().blur);
                    assert_eq!(ui_data.card_secondary_base_image.get_untracked(), None);
                }
                CardOptions::ImageOption(ImageOptions::CoverImageMissingAdult) => {
                    assert_eq!(
                        ui_data.card_image.get_untracked(),
                        Some("box art image".to_string())
                    );
                    assert!(ui_data
                        .card_image_base_attributes
                        .get_untracked()
                        .stretch_background
                        .has_stretch());
                    assert!(ui_data.card_image_base_attributes.get_untracked().blur);
                    assert_eq!(ui_data.card_secondary_base_image.get_untracked(), None);
                }
                _ => {
                    assert_eq!(
                        ui_data.card_image.get_untracked(),
                        Some("card image".to_string())
                    );
                    assert!(!ui_data
                        .card_image_base_attributes
                        .get_untracked()
                        .stretch_background
                        .has_stretch());
                    assert!(!ui_data.card_image_base_attributes.get_untracked().blur);
                    assert_eq!(ui_data.card_secondary_base_image.get_untracked(), None);
                }
            }
        };
        title_card_props(
            item,
            td,
            mb,
            cm,
            card_action,
            entitlement,
            scope,
            vod_assertions,
        );
    }

    #[rstest]
    #[case(EntitlementOptions::None, CardOptions::Default)]
    #[case(EntitlementOptions::OfferIcon, CardOptions::Default)]
    #[case(EntitlementOptions::EntitledIcon, CardOptions::Default)]
    #[case(EntitlementOptions::AdsIcon, CardOptions::Default)]
    #[case(EntitlementOptions::ErrorIcon, CardOptions::Default)]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissing)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageAdult)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissingAdult)
    )]
    fn series_card_props(
        #[case] entitlement: EntitlementOptions,
        #[case] card_options: CardOptions,
    ) {
        let series_card = create_series_card(card_options.clone(), &entitlement);
        launch_only_scope(move |scope| {
            let td = create_title_details();
            let mb = create_media_background();
            let cm = create_contextual_menu_metadata();
            let converted_series_card = charts_carousel_series_card(
                &series_card,
                scope,
                td.clone(),
                mb.clone(),
                cm.clone(),
                None,
                1,
            );
            assert!(converted_series_card.is_some());
            let converted_series_card = converted_series_card.unwrap();
            let ChartsCarouselItemTypeWrapper { id, model, .. } = converted_series_card;
            assert_eq!(id, "card id");
            let item_type = model.get_untracked();
            let ChartsCarouselItemType::Series(item) = item_type else {
                panic!("Expected series item");
            };
            let action = series_card.carouselCardMetadata.action;
            vod_title_card_props(item, td, mb, cm, action, entitlement, card_options, scope);
        });
    }

    #[rstest]
    #[case(EntitlementOptions::None, CardOptions::Default)]
    #[case(EntitlementOptions::OfferIcon, CardOptions::Default)]
    #[case(EntitlementOptions::EntitledIcon, CardOptions::Default)]
    #[case(EntitlementOptions::AdsIcon, CardOptions::Default)]
    #[case(EntitlementOptions::ErrorIcon, CardOptions::Default)]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissing)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageAdult)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissingAdult)
    )]
    fn show_card_props(#[case] entitlement: EntitlementOptions, #[case] card_options: CardOptions) {
        let show_card = create_show_card(card_options.clone(), &entitlement);
        launch_only_scope(move |scope| {
            let td = create_title_details();
            let mb = create_media_background();
            let cm = create_contextual_menu_metadata();
            let converted_show_card = charts_carousel_show_card(
                &show_card,
                scope,
                td.clone(),
                mb.clone(),
                cm.clone(),
                None,
                1,
            );
            assert!(converted_show_card.is_some());
            let converted_show_card = converted_show_card.unwrap();
            let ChartsCarouselItemTypeWrapper { id, model, .. } = converted_show_card;
            assert_eq!(id, "card id");
            let item_type = model.get_untracked();
            let ChartsCarouselItemType::Show(item) = item_type else {
                panic!("Expected show item");
            };
            let action = show_card.carouselCardMetadata.action;
            vod_title_card_props(item, td, mb, cm, action, entitlement, card_options, scope);
        });
    }

    #[rstest]
    #[case(EntitlementOptions::None, CardOptions::Default)]
    #[case(EntitlementOptions::OfferIcon, CardOptions::Default)]
    #[case(EntitlementOptions::EntitledIcon, CardOptions::Default)]
    #[case(EntitlementOptions::AdsIcon, CardOptions::Default)]
    #[case(EntitlementOptions::ErrorIcon, CardOptions::Default)]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissing)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageAdult)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissingAdult)
    )]
    fn movie_card_props(
        #[case] entitlement: EntitlementOptions,
        #[case] card_options: CardOptions,
    ) {
        let movie_card = create_movie_card(card_options.clone(), &entitlement);
        launch_only_scope(move |scope| {
            let td = create_title_details();
            let mb = create_media_background();
            let cm = create_contextual_menu_metadata();
            let converted_movie_card = charts_carousel_movie_card(
                &movie_card,
                scope,
                td.clone(),
                mb.clone(),
                cm.clone(),
                None,
                1,
            );
            assert!(converted_movie_card.is_some());
            let converted_movie_card = converted_movie_card.unwrap();
            let ChartsCarouselItemTypeWrapper { id, model, .. } = converted_movie_card;
            assert_eq!(id, "card id");
            let item_type = model.get_untracked();
            let ChartsCarouselItemType::Movie(item) = item_type else {
                panic!("Expected movie item");
            };
            let action = movie_card.carouselCardMetadata.action;
            vod_title_card_props(item, td, mb, cm, action, entitlement, card_options, scope);
        });
    }

    #[rstest]
    #[case(EntitlementOptions::None, CardOptions::Default)]
    #[case(EntitlementOptions::OfferIcon, CardOptions::Default)]
    #[case(EntitlementOptions::EntitledIcon, CardOptions::Default)]
    #[case(EntitlementOptions::AdsIcon, CardOptions::Default)]
    #[case(EntitlementOptions::ErrorIcon, CardOptions::Default)]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissing)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageAdult)
    )]
    #[case(
        EntitlementOptions::None,
        CardOptions::ImageOption(ImageOptions::CoverImageMissingAdult)
    )]
    fn generic_title_card_props(
        #[case] entitlement: EntitlementOptions,
        #[case] card_options: CardOptions,
    ) {
        let generic_card = create_generic_card(card_options.clone(), &entitlement);
        launch_only_scope(move |scope| {
            let td = create_title_details();
            let mb = create_media_background();
            let cm = create_contextual_menu_metadata();
            let converted_generic_card = charts_carousel_generic_card(
                &generic_card,
                scope,
                td.clone(),
                mb.clone(),
                cm.clone(),
                None,
                1,
            );
            assert!(converted_generic_card.is_some());
            let converted_generic_card = converted_generic_card.unwrap();
            let ChartsCarouselItemTypeWrapper { id, model, .. } = converted_generic_card;
            assert_eq!(id, "card id");
            let item_type = model.get_untracked();
            let ChartsCarouselItemType::GenericTitleCard(item) = item_type else {
                panic!("Expected generic title card item");
            };
            let action = generic_card.carouselCardMetadata.action;
            vod_title_card_props(item, td, mb, cm, action, entitlement, card_options, scope);
        });
    }

    #[rstest]
    #[case(EntitlementOptions::None, LivelinessOptions::None)]
    #[case(EntitlementOptions::None, LivelinessOptions::Live)]
    #[case(EntitlementOptions::None, LivelinessOptions::Upcoming)]
    #[case(EntitlementOptions::OfferIcon, LivelinessOptions::None)]
    #[case(EntitlementOptions::EntitledIcon, LivelinessOptions::None)]
    #[case(EntitlementOptions::AdsIcon, LivelinessOptions::None)]
    #[case(EntitlementOptions::ErrorIcon, LivelinessOptions::None)]
    #[case(EntitlementOptions::TitleMetadataBadgeInfo, LivelinessOptions::None)]
    #[case(
        EntitlementOptions::TitleMetadataBadgeInfoActive,
        LivelinessOptions::None
    )]
    #[case(
        EntitlementOptions::TitleMetadataBadgeInfoHighlight,
        LivelinessOptions::Live
    )]
    #[case(
        EntitlementOptions::TitleMetadataBadgeInfoInactive,
        LivelinessOptions::Upcoming
    )]
    fn event_card_props(
        #[case] entitlement: EntitlementOptions,
        #[case] liveliness: LivelinessOptions,
    ) {
        let event_card = create_event_card(CardOptions::Default, &entitlement, &liveliness);
        launch_only_scope(move |scope| {
            let td = create_title_details();
            let mb = create_media_background();
            let cm = create_contextual_menu_metadata();
            let converted_event_card =
                charts_carousel_event_card(&event_card, scope, td, mb, cm, None, 1);
            assert!(converted_event_card.is_some());
            let converted_event_card = converted_event_card.unwrap();
            let ChartsCarouselItemTypeWrapper { id, model, .. } = converted_event_card;
            assert_eq!(id, "card id");
            let item_type = model.get_untracked();
            let ChartsCarouselItemType::Event(_) = item_type else {
                panic!("Expected event item");
            };
        });
    }
}
