use crate::container_item_parsing::common::sports_card_to_common_metadata;
use crate::ui_signals::{SportsCardData, SportsCardItemModel, SportsCardUIData};
use common_transform_types::containers::EntityCarouselItem;
use ignx_compositron::prelude::{create_rw_signal, <PERSON><PERSON>};
use uuid::Uuid;

pub fn entity_carousel_item_to_model(
    item: &EntityCarouselItem,
    scope: Scope,
    carousel_analytics: Option<String>,
) -> Option<SportsCardItemModel> {
    let EntityCarouselItem::SPORTS_CARD(card) = item;

    // Convert card data to common metadata
    let (item_metadata, card_id) = sports_card_to_common_metadata(card, carousel_analytics)?;

    Some(SportsCardItemModel {
        uuid: Uuid::new_v4(),
        id: card_id,
        carousel_card_data: create_rw_signal(
            scope,
            SportsCardData {
                metadata: create_rw_signal(scope, item_metadata),
                card_ui_data: create_rw_signal(
                    scope,
                    SportsCardUIData {
                        title: create_rw_signal(scope, card.carouselCardMetadata.title.clone()?),
                        background_image: create_rw_signal(scope, card.backgroundImage.clone()),
                        background_color: create_rw_signal(scope, card.backgroundColor.clone()),
                        is_favorited: Some(create_rw_signal(
                            scope,
                            card.isFavorited.unwrap_or(false),
                        )),
                        entity_type: create_rw_signal(scope, card.entityType.clone()),
                        is_see_more_item: Some(false),
                    },
                ),
            },
        ),
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use common_transform_types::actions::Action;
    use common_transform_types::container_items::{CarouselItemData, RGBColor, SportsCard};
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::reactive::SignalGetUntracked;

    fn create_mock_sports_card() -> SportsCard {
        SportsCard {
            carouselCardMetadata: CarouselItemData {
                transformItemId: Some("test_id".to_string()),
                title: Some("Test Title".to_string()),
                synopsis: None,
                action: Some(Action::create_transition_landing(None)),
                deferredAction: None,
                actions: vec![],
                widgetType: None,
            },
            backgroundImage: Some("test_image.jpg".to_string()),
            backgroundColor: Some(RGBColor {
                red: 255,
                green: 0,
                blue: 0,
            }),
            isFavorited: Some(true),
            entityType: Some("TEAM".to_string()),
        }
    }

    #[test]
    fn test_entity_carousel_item_to_model() {
        launch_only_scope(|scope| {
            let sports_card = create_mock_sports_card();
            let entity_item = EntityCarouselItem::SPORTS_CARD(sports_card);

            let model = entity_carousel_item_to_model(&entity_item, scope, None).unwrap();

            assert_eq!(model.id, "test_id");

            let card_data = model.carousel_card_data.get_untracked();
            let ui_data = card_data.card_ui_data.get_untracked();

            assert_eq!(ui_data.title.get_untracked(), "Test Title");
            assert_eq!(
                ui_data.background_image.get_untracked(),
                Some("test_image.jpg".to_string())
            );
            assert_eq!(
                ui_data.background_color.get_untracked(),
                Some(RGBColor {
                    red: 255,
                    green: 0,
                    blue: 0,
                })
            );
            assert_eq!(ui_data.is_favorited.unwrap().get_untracked(), true);
            assert_eq!(
                ui_data.entity_type.get_untracked(),
                Some("TEAM".to_string())
            );
        });
    }

    #[test]
    fn test_sports_card_to_common_metadata() {
        let sports_card = create_mock_sports_card();
        let (metadata, id) = sports_card_to_common_metadata(&sports_card, None).unwrap();

        assert_eq!(id, "test_id");
        assert_eq!(metadata.id, "test_id");
        assert!(matches!(
            metadata.action,
            common_transform_types::actions::TransitionAction::landing(_)
        ));
    }
}
