use super::impressions::get_card_impression_data;
use crate::ui_signals::CommonCarouselCardMetadata;
use amzn_fable_tokens::FableIcon;
#[cfg(not(test))]
use chrono::Utc;
use clickstream::events::cards::{ActionToClickstreamPageParamsFn, CardClickedEvent};
use common_transform_types::actions::{Action, TitleActionMetadata, TransitionAction};
use common_transform_types::container_items::{
    CarouselItemData, ChannelCard, EntitlementMessaging, EventMetadata, ImageAttributes, LinkCard,
    LiveEventDateTime, SportsCard, TitleCardBaseMetadata,
};
use common_transform_types::containers::{
    ChartsCarouselItem, CommonCarouselTitleCardItem, GetTransitionAction, ScheduleCarouselItem,
    SeeMoreLink, ShortCarouselItem, SpecialCarouselItem, StandardCarouselItem, SuperCarouselItem,
    TitleCard,
};
use common_transform_types::impressions::{
    GetContentId, GetCreativeId, GetImpressionsContentType, GetWidgetType,
};
use contextual_menu_types::prelude::ContextualMenuMetadata;
use fableous::badges::label_badge::LabelBadgeColorScheme;
use fableous::buttons::primary_button::PrimaryButtonVariant;
use fableous::buttons::secondary_button::SecondaryButtonVariant;
use fableous::collaged_image::{ImageBackgroundStretch, ImageCollageBaseAttributes};
use fableous::common_string_ids::{AV_LRC_MORE_DETAILS, AV_LRC_WATCH_LIVE, AV_LRC_WATCH_NOW};
use fableous::progress_bar::ProgressBarVariant;
use fableous::tiles::tile::{
    TileBadgeProps, TileFontIconProps, TileProgressBarProps, TileProviderLogoProps,
    TileTextOverlayPosition,
};
use ignx_compositron::metrics::metric;
use ignx_compositron::prelude::{create_rw_signal, MaybeSignal, Scope, SignalGetUntracked};
use ignx_compositron::text::{LocalizedText, TextContent};
use linear_common::util::schedule_util::find_current_item_from_schedule;
use liveliness_types::Liveliness;
use location::RustPage;
use router::hooks::try_use_location;
use serde_json::{Map, Value};
use std::str::FromStr;
use title_details::types::common::LivelinessData;
use watch_modal::metrics::ssm_reporters::report_on_ssm_watch_button_rendered_metric;

pub fn get_button_label(action: Option<&TransitionAction>) -> Option<TextContent> {
    if let Some(action) = action {
        match &action {
            TransitionAction::playback(action) => {
                if action.is_valid_title_playback_action() {
                    action.label.clone().map(TextContent::String)
                } else {
                    None
                }
            }
            TransitionAction::consent(action) => Some(TextContent::String(action.label.clone())),
            TransitionAction::player(action) => {
                // Only linear player actions use the playback action label
                Some(if action.is_linear() {
                    action.label.clone().map_or_else(
                        || TextContent::LocalizedText(LocalizedText::new(AV_LRC_WATCH_LIVE)),
                        TextContent::String,
                    )
                } else {
                    TextContent::LocalizedText(LocalizedText::new(AV_LRC_WATCH_NOW))
                })
            }
            TransitionAction::acquisition(action) => Some(TextContent::from(action.label.clone())),
            TransitionAction::openModal(action) => Some(TextContent::from(action.label.clone())),
            _ => Some(TextContent::LocalizedText(LocalizedText::new(
                AV_LRC_MORE_DETAILS,
            ))),
        }
    } else {
        None
    }
}

pub fn get_button_icon(action: Option<&TransitionAction>) -> Option<String> {
    if let Some(action) = action {
        match &action {
            TransitionAction::playback(_)
            | TransitionAction::player(_)
            | TransitionAction::consent(_)
            | TransitionAction::openModal(_) => Some(FableIcon::PLAY.to_string()),
            TransitionAction::acquisition(_) => None,
            _ => Some(FableIcon::INFO.to_string()),
        }
    } else {
        None
    }
}

pub struct TransitionActionPrimaryButton<'a>(pub Option<&'a TransitionAction>);

impl TransitionActionPrimaryButton<'_> {
    pub fn to_button_variant(&self) -> PrimaryButtonVariant {
        let text = get_button_label(self.0).unwrap_or_else(|| {
            TextContent::LocalizedText(LocalizedText::new(START_BROWSING_STRING_ID))
        });
        if let Some(icon) = get_button_icon(self.0) {
            return PrimaryButtonVariant::IconAndTextSize400(icon, text);
        }
        PrimaryButtonVariant::TextSize400(text)
    }
}

pub struct TransitionActionSecondaryButton<'a>(pub Option<&'a TransitionAction>);

impl TransitionActionSecondaryButton<'_> {
    pub fn to_button_variant(&self) -> Option<SecondaryButtonVariant> {
        let text = get_button_label(self.0)?;
        if let Some(icon) = get_button_icon(self.0) {
            return Some(SecondaryButtonVariant::IconAndTextSize400(icon, text));
        }
        Some(SecondaryButtonVariant::TextSize400(text))
    }
}

pub const START_BROWSING_STRING_ID: &str = "AV_LRC_START_BROWSING";

pub fn emit_transition_action_metric(
    transition_action: &TransitionAction,
    component_name: &String,
) {
    match transition_action {
        TransitionAction::landing(_)
        | TransitionAction::registration(_)
        | TransitionAction::settings(_)
        | TransitionAction::search(_)
        | TransitionAction::clientSearch(_)
        | TransitionAction::watchList(_)
        | TransitionAction::yvl(_)
        | TransitionAction::legacyDetail(_)
        | TransitionAction::primeSignUp(_)
        | TransitionAction::detail(_)
        | TransitionAction::tournament(_)
        | TransitionAction::category(_)
        | TransitionAction::store(_)
        | TransitionAction::live(_)
        | TransitionAction::freeWithAds(_)
        | TransitionAction::myStuff(_)
        | TransitionAction::collection(_)
        | TransitionAction::testPlayground(_) => {
            metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "SwiftActionReceived");
        }
        TransitionAction::playback(_) => {
            metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "PlaybackActionReceived");
        }
        TransitionAction::acquisition(title_acquisition_action) => {
            if let Some(TitleActionMetadata::AcquisitionTVOD(_)) =
                &title_acquisition_action.metadata
            {
                metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "TVODActionReceived");
            } else if let Some(
                TitleActionMetadata::AcquisitionSVOD(_) | TitleActionMetadata::AcquisitionPrime(_),
            ) = &title_acquisition_action.metadata
            {
                metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "SVODActionReceived");
            }
        }
        TransitionAction::openModal(_) => {
            metric!("ComponentAction.Count", 1, "pageType" => "Collection", "componentName" => component_name, "actionName" => "OpenModalActionReceived");
            report_on_ssm_watch_button_rendered_metric(RustPage::RUST_COLLECTIONS);
        }
        _ => {}
    };
}

pub fn get_provider_logo_params(
    url: &Option<String>,
    attributes: &Option<ImageAttributes>,
) -> Option<TileProviderLogoProps> {
    let url = url.as_deref()?;

    let metadata = attributes
        .as_ref()
        .and_then(|attrs| attrs.individualImageMetadata.to_owned())?;

    Some(TileProviderLogoProps {
        url: url.to_owned(),
        metadata: metadata.providerLogoImage,
        display_as_overlay: false,
    })
}

pub fn title_card_metadata_to_provider_logo_props(
    metadata: &TitleCardBaseMetadata,
) -> Option<TileProviderLogoProps> {
    let TitleCardBaseMetadata {
        providerLogoImage,
        imageAttributes,
        ..
    } = metadata;
    get_provider_logo_params(providerLogoImage, imageAttributes)
}

pub fn link_card_action_fallback_id(action: &TransitionAction) -> Option<String> {
    match action {
        TransitionAction::landing(action)
        | TransitionAction::registration(action)
        | TransitionAction::settings(action)
        | TransitionAction::search(action)
        | TransitionAction::clientSearch(action)
        | TransitionAction::watchList(action)
        | TransitionAction::yvl(action)
        | TransitionAction::linearStationDetail(action)
        | TransitionAction::legacyDetail(action)
        | TransitionAction::primeSignUp(action)
        | TransitionAction::detail(action)
        | TransitionAction::tournament(action)
        | TransitionAction::category(action)
        | TransitionAction::store(action)
        | TransitionAction::live(action)
        | TransitionAction::freeWithAds(action)
        | TransitionAction::myStuff(action)
        | TransitionAction::collection(action)
        | TransitionAction::testPlayground(action) => {
            let page_id = action.pageId.to_owned();
            let page_type = action.pageType.to_owned();
            Some(format!("{page_id}-{page_type}"))
        }
        TransitionAction::signUp(action) => Some(action.benefitId.to_owned()),
        TransitionAction::player(action) => Some(action.uri.to_owned()),
        TransitionAction::playback(action) => {
            let label = action.label.to_owned();
            let channel_id = action.playbackMetadata.channelId.to_owned();
            label.or(channel_id)
        }
        TransitionAction::acquisition(action) => Some(action.label.to_owned()),
        TransitionAction::browse(action) => {
            let page_id = action.swiftAction.pageId.to_owned();
            let page_type = action.swiftAction.pageType.to_owned();
            Some(format!("{page_id}-{page_type}"))
        }
        TransitionAction::consent(_)
        | TransitionAction::manageSubscription(_)
        | TransitionAction::profileSettings(_)
        | TransitionAction::openModal(_)
        | TransitionAction::fuseOfferActivation(_)
        | TransitionAction::changeHomeRegion(_)
        | TransitionAction::primeRetention(_)
        | TransitionAction::playbackGroup(_)
        | TransitionAction::sportsFavorites(_)
        | TransitionAction::removeFromFavorites(_)
        | TransitionAction::sportsSchedule(_) => None,
        TransitionAction::liveSportsNoOp => None,
        TransitionAction::apiLink(_) => None,
    }
}

pub fn event_metadata_to_subtitle(event_metadata: &EventMetadata) -> Option<String> {
    if let Some(liveliness) = &event_metadata.liveliness {
        if liveliness == "LIVE" {
            return None;
        }
        if let Some(LiveEventDateTime::LOCALIZED_BADGE(badge)) = &event_metadata.liveEventDateBadge
        {
            return Some(badge.text.to_owned());
        }
    }
    None
}

pub struct CardImageAndAttributes {
    pub image: String,
    pub base_attributes: ImageCollageBaseAttributes,
    pub secondary_base_image: Option<String>,
}

/// There are 5 cases to handle with the card image
/// Case 1: We have a cover image; it is not adult content (most typical case)
///     - the cover image is the card image, no blurring or stretching is applied, other icons,
///         text, gradients are overlaid as normal (done in [`fableous::collaged_image::collaged_image::CollagedImage`])
/// Case 2: We have a cover image; it is adult content
///     - same as case 1, but we blur the cover image on top of which everything else is collaged.
/// Case 3: We do not have a cover image, we do have a box art image; it is not adult content
///     - the box art is the card image, but we need to fill the card. To achieve this we create a
///         'base' image from a stretched and blurred version of the box art. We then overlay a
///         'clean' (i.e. not stretched or blurred) version of the box art to ensure it's visible
///         as the first image in the collage overlay, and allow the remaining icons, text,
///         gradients to be overlaid onto this.
/// Case 4: We do not have a cover image, we do have a box art image; it is adult content
///     - same as case 3, but we don't bother to overlay the 'clean' box art as it should not be
///         visible anyway.
/// Case 5: We do not have a cover image or a box art image
///     - we have no image to display for the card and so we drop the card
///
/// This should not be used for Event cards, as these are expected to always have a correct cover
/// image.
pub fn get_card_image_with_attributes(
    box_art: Option<&String>,
    cover_image: Option<&String>,
    is_adult: bool,
) -> Option<CardImageAndAttributes> {
    let Some(cover) = cover_image else {
        let Some(box_art) = box_art else {
            // case 5
            return None;
        };
        return if is_adult {
            // case 4
            Some(CardImageAndAttributes {
                image: box_art.clone(),
                base_attributes: ImageCollageBaseAttributes {
                    stretch_background: ImageBackgroundStretch::StretchOriginal,
                    blur: true,
                    apply_bg_color: None,
                    quality: None,
                },
                secondary_base_image: None,
            })
        } else {
            // case 3
            Some(CardImageAndAttributes {
                image: box_art.clone(),
                base_attributes: ImageCollageBaseAttributes {
                    stretch_background: ImageBackgroundStretch::StretchOriginal,
                    blur: true,
                    apply_bg_color: None,
                    quality: None,
                },
                secondary_base_image: Some(box_art.clone()),
            })
        };
    };
    // cases 1, 2
    Some(CardImageAndAttributes {
        image: cover.clone(),
        base_attributes: ImageCollageBaseAttributes {
            stretch_background: ImageBackgroundStretch::None,
            blur: is_adult,
            apply_bg_color: None,
            quality: None,
        },
        secondary_base_image: None,
    })
}

pub fn progress_variant_to_progress_bar_props(
    watch_progress: Option<f32>,
    variant: ProgressBarVariant,
    total_length_minutes: Option<u16>,
    scope: Scope,
) -> Option<TileProgressBarProps> {
    watch_progress.map(|wp| TileProgressBarProps {
        progress: create_rw_signal(scope, wp),
        variant: variant.into(),
        tts_total_minutes: create_rw_signal(scope, total_length_minutes),
    })
}

pub fn title_card_card_id<'a>(
    carousel_item_data: &'a CarouselItemData,
    title_card_base_metadata: &'a TitleCardBaseMetadata,
) -> Option<&'a String> {
    carousel_item_data
        .transformItemId
        .as_ref()
        .or_else(|| title_card_base_metadata.gti.as_ref())
}

pub fn link_card_to_common_metadata(
    card: &LinkCard,
    carousel_analytics: Option<String>,
    with_service_token_in_id: bool,
    page_params_constructor: Option<ActionToClickstreamPageParamsFn>,
) -> Option<(CommonCarouselCardMetadata, String)> {
    let action = card.get_transition_action()?;
    let card_id = card
        .carouselCardMetadata
        .transformItemId
        .to_owned()
        .or_else(|| link_card_action_fallback_id(action))?;

    let card_id = if with_service_token_in_id {
        let service_token = action
            .is_swift_action_and(|swift_action| swift_action.serviceToken.clone())
            .flatten();
        format!(
            "{}-{}",
            card_id,
            service_token.unwrap_or_else(|| "no-service-token".to_string())
        )
    } else {
        card_id
    };
    let clickstream_data = CardClickedEvent::new_from_action(action);
    let clickstream_data = if let Some(page_params_constructor) = page_params_constructor {
        clickstream_data.with_page_params(page_params_constructor(action))
    } else {
        clickstream_data
    };

    let impression_data = get_card_impression_data(card, action, carousel_analytics);
    Some((
        CommonCarouselCardMetadata {
            id: card_id.clone(),
            action: action.clone(),
            impression_data,
            swift_content_type: None,
            gti: None,
            clickstream_data,
        },
        card_id,
    ))
}

pub(crate) fn title_card_to_common_metadata<
    C: TitleCard + GetContentId + GetImpressionsContentType + GetCreativeId + GetWidgetType,
>(
    item: &C,
    carousel_analytics: Option<String>,
    fallback_action: Option<TransitionAction>,
) -> Option<(CommonCarouselCardMetadata, String)> {
    let (title_card_metadata, carousel_card_metadata) = item.get_metadata();
    let action = carousel_card_metadata
        .action
        .as_ref()
        .map_or_else(
            || {
                if title_card_metadata.contentType == Some("LIVE_EVENT_ITEM".to_string())
                    && carousel_card_metadata.widgetType == Some("scheduleCard".to_string())
                {
                    Some(&TransitionAction::liveSportsNoOp)
                } else {
                    None
                }
            },
            |action| action.get_transition_action(),
        )
        .or(fallback_action.as_ref())?;

    let card_id = title_card_card_id(carousel_card_metadata, title_card_metadata)?;
    let impression_data = get_card_impression_data(item, action, carousel_analytics);
    let swift_content_type = title_card_metadata.contentType.to_owned();
    let gti = title_card_metadata.gti.to_owned();
    let clickstream_data = CardClickedEvent::new_from_action(action);
    Some((
        CommonCarouselCardMetadata {
            id: card_id.clone(),
            action: action.clone(),
            impression_data,
            swift_content_type,
            gti,
            clickstream_data,
        },
        card_id.to_owned(),
    ))
}

pub fn sports_card_to_common_metadata(
    card: &SportsCard,
    carousel_analytics: Option<String>,
) -> Option<(CommonCarouselCardMetadata, String)> {
    let action = card
        .carouselCardMetadata
        .action
        .as_ref()?
        .get_transition_action()?;
    let card_id = card.carouselCardMetadata.transformItemId.to_owned()?;

    let impression_data = get_card_impression_data(card, action, carousel_analytics);
    let clickstream_data = CardClickedEvent::new_from_action(action);

    Some((
        CommonCarouselCardMetadata {
            id: card_id.clone(),
            action: action.clone(),
            impression_data,
            swift_content_type: None,
            gti: None,
            clickstream_data,
        },
        card_id,
    ))
}

pub fn channel_card_to_common_metadata(
    card: &ChannelCard,
    carousel_analytics: Option<String>,
) -> Option<(CommonCarouselCardMetadata, String)> {
    let action = card
        .carouselCardMetadata
        .actions
        .get(0)
        .as_ref()?
        .get_transition_action()?;
    let card_id = card.carouselCardMetadata.transformItemId.to_owned()?;

    let impression_data = get_card_impression_data(card, action, carousel_analytics);
    let clickstream_data = CardClickedEvent::new_from_action(action);

    Some((
        CommonCarouselCardMetadata {
            id: card_id.clone(),
            action: action.clone(),
            impression_data,
            swift_content_type: None,
            gti: None,
            clickstream_data,
        },
        card_id,
    ))
}

pub trait CarouselItem {
    fn get_tile_image(&self) -> &Option<String>;

    fn get_provider_logo(&self) -> Option<TileProviderLogoProps>;

    fn get_missing_image_fallback_text(&self) -> &Option<String>;

    fn get_overlay_text(&self) -> &Option<String>;

    fn get_overlay_text_position(&self) -> Option<TileTextOverlayPosition>;

    fn get_title_metadata_badge_props(&self) -> Option<TileBadgeProps>;

    fn get_entitlement_message_icon_props(&self) -> Option<TileFontIconProps>;

    fn get_contextual_menu_metadata(&self) -> ContextualMenuMetadata;
}

impl CarouselItem for StandardCarouselItem {
    fn get_tile_image(&self) -> &Option<String> {
        match self {
            StandardCarouselItem::TITLE_CARD(card) => {
                &card.get_base_title_card_metadata().coverImage
            }
            StandardCarouselItem::LIVE_LINEAR_CARD(card) => {
                find_current_item_from_schedule(&card.schedule, Utc::now().timestamp_millis())
                    .map_or(&card.image, |show| &show.image)
            }
            StandardCarouselItem::LINEAR_STATION(card) => {
                find_current_item_from_schedule(&card.schedule, Utc::now().timestamp_millis())
                    .map_or(&card.image, |show| &show.image)
            }
            StandardCarouselItem::LINK_CARD(card) => &card.imageUrl,
            StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => {
                &card.titleCardBaseMetadata.coverImage
            }
            StandardCarouselItem::VOD_EXTRA_CONTENT(card) => {
                &card.get_base_title_card_metadata().coverImage
            }
            StandardCarouselItem::HERO_CARD(_)
            | StandardCarouselItem::LINEAR_AIRING(_)
            | StandardCarouselItem::CHANNEL_CARD(_) => &None,
        }
    }

    fn get_provider_logo(&self) -> Option<TileProviderLogoProps> {
        match self {
            StandardCarouselItem::TITLE_CARD(card) => {
                let TitleCardBaseMetadata {
                    providerLogoImage: provider_logo_image,
                    imageAttributes: image_attributes,
                    ..
                } = card.get_base_title_card_metadata();
                get_provider_logo_params(provider_logo_image, image_attributes)
            }
            StandardCarouselItem::LIVE_LINEAR_CARD(_) => None,
            StandardCarouselItem::LINEAR_STATION(_) => None,
            StandardCarouselItem::LINK_CARD(_) => None,
            StandardCarouselItem::BONUS_SCHEDULE_CARD(_) => None,
            StandardCarouselItem::VOD_EXTRA_CONTENT(card) => {
                let TitleCardBaseMetadata {
                    providerLogoImage: provider_logo_image,
                    imageAttributes: image_attributes,
                    ..
                } = card.get_base_title_card_metadata();
                get_provider_logo_params(provider_logo_image, image_attributes)
            }
            StandardCarouselItem::HERO_CARD(_)
            | StandardCarouselItem::LINEAR_AIRING(_)
            | StandardCarouselItem::CHANNEL_CARD(_) => None,
        }
    }

    fn get_missing_image_fallback_text(&self) -> &Option<String> {
        match self {
            StandardCarouselItem::TITLE_CARD(card) => &card.get_carousel_card_metadata().title,
            StandardCarouselItem::VOD_EXTRA_CONTENT(card) => {
                &card.get_carousel_card_metadata().title
            }
            StandardCarouselItem::LIVE_LINEAR_CARD(card) => &card.carouselCardMetadata.title,
            StandardCarouselItem::LINEAR_STATION(card) => &card.name,
            StandardCarouselItem::LINK_CARD(card) => &card.carouselCardMetadata.title,
            StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => &card.carouselCardMetadata.title,
            StandardCarouselItem::HERO_CARD(_)
            | StandardCarouselItem::LINEAR_AIRING(_)
            | StandardCarouselItem::CHANNEL_CARD(_) => &None,
        }
    }

    fn get_overlay_text(&self) -> &Option<String> {
        match self {
            StandardCarouselItem::LINK_CARD(card) => &card.carouselCardMetadata.title,
            StandardCarouselItem::TITLE_CARD(_)
            | StandardCarouselItem::LIVE_LINEAR_CARD(_)
            | StandardCarouselItem::LINEAR_STATION(_)
            | StandardCarouselItem::VOD_EXTRA_CONTENT(_)
            | StandardCarouselItem::BONUS_SCHEDULE_CARD(_)
            | StandardCarouselItem::HERO_CARD(_)
            | StandardCarouselItem::LINEAR_AIRING(_)
            | StandardCarouselItem::CHANNEL_CARD(_) => &None,
        }
    }

    fn get_overlay_text_position(&self) -> Option<TileTextOverlayPosition> {
        match self {
            StandardCarouselItem::LINK_CARD(card) => TileTextOverlayPosition::from_transform_type(
                card.overlayTextPosition.as_ref(),
                false,
            ),
            StandardCarouselItem::TITLE_CARD(_)
            | StandardCarouselItem::LIVE_LINEAR_CARD(_)
            | StandardCarouselItem::LINEAR_STATION(_)
            | StandardCarouselItem::VOD_EXTRA_CONTENT(_)
            | StandardCarouselItem::BONUS_SCHEDULE_CARD(_)
            | StandardCarouselItem::HERO_CARD(_)
            | StandardCarouselItem::LINEAR_AIRING(_)
            | StandardCarouselItem::CHANNEL_CARD(_) => None,
        }
    }

    fn get_title_metadata_badge_props(&self) -> Option<TileBadgeProps> {
        match self {
            StandardCarouselItem::VOD_EXTRA_CONTENT(card)
            | StandardCarouselItem::TITLE_CARD(card) => match card {
                CommonCarouselTitleCardItem::EVENT(card) => TileBadgeProps::from_event_card(card),
                _ => TileBadgeProps::from_title_card_metadata(card.get_base_title_card_metadata()),
            },
            StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => {
                TileBadgeProps::from_event_card(card)
            }
            StandardCarouselItem::LINK_CARD(card) => TileBadgeProps::from_link_card(card),
            StandardCarouselItem::LIVE_LINEAR_CARD(_) | StandardCarouselItem::LINEAR_STATION(_) => {
                Some(TileBadgeProps {
                    // TODO: Localize text
                    text: TextContent::from("ON NOW").into(),
                    color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::PRIMARY),
                })
            }
            StandardCarouselItem::HERO_CARD(_)
            | StandardCarouselItem::LINEAR_AIRING(_)
            | StandardCarouselItem::CHANNEL_CARD(_) => None,
        }
    }

    fn get_entitlement_message_icon_props(&self) -> Option<TileFontIconProps> {
        let entitlement_messaging: Option<&EntitlementMessaging> = self.into();
        TileFontIconProps::from_entitlement_messaging_message(entitlement_messaging?)
    }

    fn get_contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        match self {
            StandardCarouselItem::TITLE_CARD(card)
            | StandardCarouselItem::VOD_EXTRA_CONTENT(card) => {
                get_title_card_contextual_menu_metadata(card)
            }
            StandardCarouselItem::LIVE_LINEAR_CARD(card) => {
                let carousel_card_metadata = &card.carouselCardMetadata;
                let title = get_title(carousel_card_metadata);
                let gti = card.gti.clone();
                let action = get_transition_action(&carousel_card_metadata.action);
                let is_in_watchlist = None;
                let maturity_rating_string = card.rating.clone();
                let maturity_rating_image = None;
                let contextual_actions = card.contextualActions.clone();

                ContextualMenuMetadata {
                    title,
                    gti,
                    is_in_watchlist,
                    is_linear_title: false,
                    primary_action: action,
                    secondary_action: None,
                    maturity_rating_string,
                    maturity_rating_image,
                    event_metadata: None,
                    tnf_properties: None,
                    liveliness_data: None,
                    journey_ingress_context: None,
                    content_type: None,
                    contextual_actions,
                    is_favorited: None,
                }
            }
            StandardCarouselItem::LINK_CARD(card) => get_link_card_contextual_menu_metadata(card),
            StandardCarouselItem::BONUS_SCHEDULE_CARD(card) => {
                let carousel_card_metadata = &card.carouselCardMetadata;
                let gti = card.titleCardBaseMetadata.gti.clone();
                let title = get_title(carousel_card_metadata);
                let action = get_transition_action(&carousel_card_metadata.action);
                let is_in_watchlist = card.titleCardBaseMetadata.isInWatchlist;
                let maturity_rating_string =
                    card.titleCardBaseMetadata.maturityRatingString.clone();
                let maturity_rating_image = card.titleCardBaseMetadata.maturityRatingImage.clone();
                let (message, title_metadata_badge_level) = card
                    .titleCardBaseMetadata
                    .entitlementMessaging
                    .as_ref()
                    .map_or((None, None), |x| {
                        (
                            x.to_title_metadata_badge_message().cloned(),
                            x.to_title_metadata_badge_level().cloned(),
                        )
                    });

                let liveliness = card
                    .eventMetadata
                    .liveliness
                    .as_deref()
                    .and_then(|l| Liveliness::from_str(l).ok());
                let liveliness_data = Some(LivelinessData {
                    message,
                    level: title_metadata_badge_level.map(|level| level.into()),
                    liveliness,
                });
                let content_type = card.titleCardBaseMetadata.contentType.clone();
                let event_metadata = card.eventMetadata.clone();

                ContextualMenuMetadata {
                    title,
                    gti,
                    primary_action: action,
                    secondary_action: None,
                    is_in_watchlist,
                    is_linear_title: false,
                    maturity_rating_string,
                    maturity_rating_image,
                    event_metadata: Some(event_metadata),
                    tnf_properties: None,
                    liveliness_data,
                    journey_ingress_context: None,
                    content_type,
                    contextual_actions: None,
                    is_favorited: None,
                }
            }
            StandardCarouselItem::LINEAR_STATION(card) => {
                let title = card.name.clone();
                let gti = card.gti.clone();
                let action = get_transition_action(&card.action);
                let is_in_watchlist = None;
                let maturity_rating_string = card.rating.clone();
                let maturity_rating_image = None;
                let contextual_actions = card.contextualActions.clone();
                let is_favorited = card.reactions.as_ref().map(|r| r.isFavorite);

                let secondary_action = card.actions.iter().find_map(|action| {
                    action
                        .get_transition_action()
                        .filter(|action| matches!(action, TransitionAction::linearStationDetail(_)))
                        .cloned()
                });

                ContextualMenuMetadata {
                    title,
                    gti,
                    is_in_watchlist,
                    is_favorited,
                    is_linear_title: false,
                    maturity_rating_string,
                    maturity_rating_image,
                    primary_action: action,
                    secondary_action,
                    event_metadata: None,
                    tnf_properties: None,
                    liveliness_data: None,
                    journey_ingress_context: None,
                    content_type: None,
                    contextual_actions,
                }
            }
            StandardCarouselItem::HERO_CARD(_) => Default::default(),
            StandardCarouselItem::LINEAR_AIRING(_) => Default::default(),
            StandardCarouselItem::CHANNEL_CARD(_) => Default::default(),
        }
    }
}

impl CarouselItem for ChartsCarouselItem {
    fn get_tile_image(&self) -> &Option<String> {
        match self {
            ChartsCarouselItem::TITLE_CARD(card) => &card.get_base_title_card_metadata().coverImage,
        }
    }

    fn get_provider_logo(&self) -> Option<TileProviderLogoProps> {
        let metadata: Option<&TitleCardBaseMetadata> = self.into();
        let metadata = metadata?;
        let TitleCardBaseMetadata {
            providerLogoImage: provider_logo_image,
            imageAttributes: image_attributes,
            ..
        } = metadata;
        get_provider_logo_params(provider_logo_image, image_attributes)
    }

    fn get_missing_image_fallback_text(&self) -> &Option<String> {
        match self {
            ChartsCarouselItem::TITLE_CARD(card) => &card.get_carousel_card_metadata().title,
        }
    }

    fn get_overlay_text(&self) -> &Option<String> {
        &None
    }

    fn get_overlay_text_position(&self) -> Option<TileTextOverlayPosition> {
        None
    }

    fn get_title_metadata_badge_props(&self) -> Option<TileBadgeProps> {
        let metadata: Option<&TitleCardBaseMetadata> = self.into();
        let metadata = metadata?;
        TileBadgeProps::from_title_card_metadata(metadata)
    }

    fn get_entitlement_message_icon_props(&self) -> Option<TileFontIconProps> {
        let entitlement_messaging: Option<&EntitlementMessaging> = self.into();
        TileFontIconProps::from_entitlement_messaging_message(entitlement_messaging?)
    }

    fn get_contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        match self {
            ChartsCarouselItem::TITLE_CARD(card) => get_title_card_contextual_menu_metadata(card),
        }
    }
}

impl CarouselItem for SuperCarouselItem {
    fn get_tile_image(&self) -> &Option<String> {
        match self {
            SuperCarouselItem::TITLE_CARD(card) => {
                &card.get_base_title_card_metadata().poster2x3Image
            }
            SuperCarouselItem::LINK_CARD(card) => &card.imageUrl,
        }
    }

    fn get_provider_logo(&self) -> Option<TileProviderLogoProps> {
        match self {
            SuperCarouselItem::TITLE_CARD(card) => {
                let TitleCardBaseMetadata {
                    providerLogoImage,
                    imageAttributes,
                    ..
                } = card.get_base_title_card_metadata();
                get_provider_logo_params(providerLogoImage, imageAttributes)
            }
            SuperCarouselItem::LINK_CARD(_) => None,
        }
    }

    fn get_missing_image_fallback_text(&self) -> &Option<String> {
        match self {
            SuperCarouselItem::TITLE_CARD(card) => &card.get_carousel_card_metadata().title,
            SuperCarouselItem::LINK_CARD(card) => &card.carouselCardMetadata.title,
        }
    }

    fn get_overlay_text(&self) -> &Option<String> {
        &None
    }

    fn get_overlay_text_position(&self) -> Option<TileTextOverlayPosition> {
        None
    }

    fn get_title_metadata_badge_props(&self) -> Option<TileBadgeProps> {
        match self {
            SuperCarouselItem::TITLE_CARD(card) => {
                let metadata = card.get_base_title_card_metadata();
                TileBadgeProps::from_title_card_metadata(metadata)
            }
            SuperCarouselItem::LINK_CARD(card) => TileBadgeProps::from_link_card(card),
        }
    }

    fn get_entitlement_message_icon_props(&self) -> Option<TileFontIconProps> {
        let entitlement_messaging: Option<&EntitlementMessaging> = self.into();
        TileFontIconProps::from_entitlement_messaging_message(entitlement_messaging?)
    }

    fn get_contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        match self {
            SuperCarouselItem::TITLE_CARD(card) => get_title_card_contextual_menu_metadata(card),
            SuperCarouselItem::LINK_CARD(card) => get_link_card_contextual_menu_metadata(card),
        }
    }
}

impl CarouselItem for ShortCarouselItem {
    fn get_tile_image(&self) -> &Option<String> {
        match self {
            ShortCarouselItem::LINK_CARD(card) => &card.imageUrl,
        }
    }

    fn get_provider_logo(&self) -> Option<TileProviderLogoProps> {
        None
    }

    fn get_missing_image_fallback_text(&self) -> &Option<String> {
        &None
    }

    fn get_overlay_text(&self) -> &Option<String> {
        match self {
            ShortCarouselItem::LINK_CARD(card) => &card.carouselCardMetadata.title,
        }
    }

    fn get_overlay_text_position(&self) -> Option<TileTextOverlayPosition> {
        match self {
            ShortCarouselItem::LINK_CARD(card) => TileTextOverlayPosition::from_transform_type(
                card.overlayTextPosition.as_ref(),
                true,
            ),
        }
    }

    fn get_title_metadata_badge_props(&self) -> Option<TileBadgeProps> {
        None
    }

    fn get_entitlement_message_icon_props(&self) -> Option<TileFontIconProps> {
        None
    }

    fn get_contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        match self {
            ShortCarouselItem::LINK_CARD(card) => get_link_card_contextual_menu_metadata(card),
        }
    }
}

impl CarouselItem for ScheduleCarouselItem {
    fn get_tile_image(&self) -> &Option<String> {
        &self.eventCard.titleCardBaseMetadata.coverImage
    }
    fn get_provider_logo(&self) -> Option<TileProviderLogoProps> {
        None
    }

    fn get_missing_image_fallback_text(&self) -> &Option<String> {
        &None
    }

    fn get_overlay_text(&self) -> &Option<String> {
        &None
    }

    fn get_overlay_text_position(&self) -> Option<TileTextOverlayPosition> {
        None
    }

    fn get_title_metadata_badge_props(&self) -> Option<TileBadgeProps> {
        None
    }

    fn get_entitlement_message_icon_props(&self) -> Option<TileFontIconProps> {
        None
    }

    fn get_contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        get_title_card_contextual_menu_metadata(&CommonCarouselTitleCardItem::EVENT(
            self.eventCard.clone(),
        ))
    }
}

impl CarouselItem for SpecialCarouselItem {
    fn get_tile_image(&self) -> &Option<String> {
        match self {
            SpecialCarouselItem::CHANNEL_CARD(card) => &card.image,
            SpecialCarouselItem::TITLE_CARD(card) => {
                &card.get_base_title_card_metadata().poster2x3Image
            }
        }
    }

    fn get_provider_logo(&self) -> Option<TileProviderLogoProps> {
        None
    }

    fn get_missing_image_fallback_text(&self) -> &Option<String> {
        match self {
            SpecialCarouselItem::CHANNEL_CARD(card) => &card.carouselCardMetadata.title,
            SpecialCarouselItem::TITLE_CARD(card) => &card.get_carousel_card_metadata().title,
        }
    }

    fn get_overlay_text(&self) -> &Option<String> {
        &None
    }

    fn get_overlay_text_position(&self) -> Option<TileTextOverlayPosition> {
        None
    }

    fn get_title_metadata_badge_props(&self) -> Option<TileBadgeProps> {
        match self {
            SpecialCarouselItem::CHANNEL_CARD(card) => {
                card.entitlementMessaging.as_ref().and_then(|em| {
                    em.TITLE_METADATA_BADGE_SLOT.as_ref().and_then(|badge| {
                        badge.message.as_ref().map(|message| TileBadgeProps {
                            text: MaybeSignal::Static(TextContent::from(message.clone())),
                            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::PRIMARY),
                        })
                    })
                })
            }
            SpecialCarouselItem::TITLE_CARD(card) => card
                .get_base_title_card_metadata()
                .entitlementMessaging
                .as_ref()
                .and_then(|em| {
                    em.TITLE_METADATA_BADGE_SLOT.as_ref().and_then(|badge| {
                        badge.message.as_ref().map(|message| TileBadgeProps {
                            text: MaybeSignal::Static(TextContent::from(message.clone())),
                            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::PRIMARY),
                        })
                    })
                }),
        }
    }

    fn get_entitlement_message_icon_props(&self) -> Option<TileFontIconProps> {
        match self {
            SpecialCarouselItem::CHANNEL_CARD(card) => {
                let entitlement_messaging = card.entitlementMessaging.as_ref()?;
                TileFontIconProps::from_entitlement_messaging_message(entitlement_messaging)
            }
            SpecialCarouselItem::TITLE_CARD(card) => {
                let entitlement_messaging = card
                    .get_base_title_card_metadata()
                    .entitlementMessaging
                    .as_ref()?;
                TileFontIconProps::from_entitlement_messaging_message(entitlement_messaging)
            }
        }
    }

    fn get_contextual_menu_metadata(&self) -> ContextualMenuMetadata {
        match self {
            SpecialCarouselItem::CHANNEL_CARD(card) => {
                get_channel_card_contextual_menu_metadata(card)
            }
            SpecialCarouselItem::TITLE_CARD(card) => get_title_card_contextual_menu_metadata(card),
        }
    }
}

pub fn get_title_card_contextual_menu_metadata(
    card: &CommonCarouselTitleCardItem,
) -> ContextualMenuMetadata {
    let carousel_card_metadata = card.get_carousel_card_metadata();
    let title_card_metadata = card.get_base_title_card_metadata();
    let event_metadata = card.get_event_metadata();
    let action = get_transition_action(&carousel_card_metadata.action);
    let title = get_title(carousel_card_metadata);
    let gti = title_card_metadata.gti.clone();
    let is_in_watchlist = title_card_metadata.isInWatchlist;
    let maturity_rating_string = title_card_metadata.maturityRatingString.clone();
    let maturity_rating_image = title_card_metadata.maturityRatingImage.clone();
    let content_type = title_card_metadata.contentType.clone();

    let is_linear_title = match card {
        CommonCarouselTitleCardItem::SEASON(season) => season.linearAttributes.is_some(),
        CommonCarouselTitleCardItem::MOVIE(movie) => movie.linearAttributes.is_some(),
        _ => false,
    };
    let (message, title_metadata_badge_level) = card
        .get_base_title_card_metadata()
        .entitlementMessaging
        .as_ref()
        .map_or((None, None), |x| {
            (
                x.to_title_metadata_badge_message().cloned(),
                x.to_title_metadata_badge_level().cloned(),
            )
        });
    let liveliness_data = Some(LivelinessData {
        message,
        level: title_metadata_badge_level.map(|level| level.into()),
        liveliness: event_metadata
            .as_ref()
            .and_then(|data| {
                data.liveliness
                    .as_deref()
                    .map(|l| Liveliness::from_str(l).ok())
            })
            .flatten(),
    });

    let journey_ingress_context = action.clone().and_then(|a| match a {
        TransitionAction::detail(action) => action.journeyIngressContext,
        _ => None,
    });

    let contextual_actions = title_card_metadata.contextualActions.clone();

    ContextualMenuMetadata {
        title,
        gti,
        is_in_watchlist,
        is_linear_title,
        primary_action: action,
        secondary_action: None,
        maturity_rating_string,
        maturity_rating_image,
        event_metadata,
        tnf_properties: None,
        liveliness_data,
        journey_ingress_context,
        content_type,
        contextual_actions,
        is_favorited: None,
    }
}

pub fn get_see_more_link_contextual_menu_metadata(link: &SeeMoreLink) -> ContextualMenuMetadata {
    let action = get_transition_action(&link.linkAction);
    let title = link.title.clone();
    let gti = None;
    let is_in_watchlist = None;
    let maturity_rating_string = None;
    let maturity_rating_image = None;

    ContextualMenuMetadata {
        title,
        gti,
        is_in_watchlist,
        is_linear_title: false,
        primary_action: action,
        secondary_action: None,
        maturity_rating_string,
        maturity_rating_image,
        event_metadata: None,
        tnf_properties: None,
        liveliness_data: None,
        journey_ingress_context: None,
        content_type: None,
        contextual_actions: None,
        is_favorited: None,
    }
}

pub fn get_link_card_contextual_menu_metadata(card: &LinkCard) -> ContextualMenuMetadata {
    let carousel_card_metadata = &card.carouselCardMetadata;
    let action = get_transition_action(&carousel_card_metadata.action);
    let title = get_title(carousel_card_metadata);
    let gti = None;
    let is_in_watchlist = None;
    let maturity_rating_string = None;
    let maturity_rating_image = None;

    ContextualMenuMetadata {
        title,
        gti,
        is_in_watchlist,
        is_linear_title: false,
        contextual_actions: None,
        primary_action: action,
        secondary_action: None,
        maturity_rating_string,
        maturity_rating_image,
        event_metadata: None,
        tnf_properties: None,
        liveliness_data: None,
        journey_ingress_context: None,
        content_type: None,
        is_favorited: None,
    }
}

pub fn get_channel_card_contextual_menu_metadata(card: &ChannelCard) -> ContextualMenuMetadata {
    let action = card.get_transition_action().cloned();
    let title = card.carouselCardMetadata.title.clone();

    ContextualMenuMetadata {
        title,
        is_linear_title: false,
        primary_action: action,
        ..Default::default()
    }
}

fn get_transition_action(action: &Option<Action>) -> Option<TransitionAction> {
    action
        .as_ref()
        .and_then(|action| action.get_transition_action())
        .cloned()
}

fn get_title(carousel_item_data: &CarouselItemData) -> Option<String> {
    carousel_item_data.title.clone()
}

fn get_my_stuff_page_params() -> Map<String, Value> {
    Map::from_iter(vec![
        ("pageType".to_string(), Value::String("home".to_string())),
        ("pageId".to_string(), Value::String("MyStuff".to_string())),
    ])
}

pub fn is_my_stuff_page(scope: Scope) -> bool {
    let is_my_stuff_page = try_use_location(scope).is_some_and(|loc| {
        loc.get_untracked()
            .contains_page_params(get_my_stuff_page_params())
    });

    is_my_stuff_page
}

#[cfg(any(feature = "example_data", test))]
pub mod test_helpers {
    use common_transform_types::actions::{
        Action, CategoryPageSwiftAction, ConsentAction, PlaybackAction, ProfileSettingsAction,
        SignUpAction, SwiftAction, TitleAcquisitionAction, TitlePlaybackAction, TransitionAction,
    };
    use common_transform_types::container_items::{
        CarouselItemData, ChannelCard, CurrentPeriod, Dimension, EntitlementGlance,
        EntitlementGlanceIcons, EntitlementMessage, EntitlementMessageIcons, EntitlementMessaging,
        EventMetadata, HighValueMessage, ImageAttributes, InformationalMessage, LinkCard,
        LiveEventDateTime, LocalizedLiveEventBadge, LocalizedLiveEventHeader, MaturityRatingImage,
        OrderedParticipant, OverlayTextPosition, ScoreBug, ScoreSummary, TitleCardBaseMetadata,
        TitleMetadataBadge, TitleMetadataBadgeLevel,
    };
    use common_transform_types::playback_metadata::{
        PlaybackMetadata, PlaybackPosition, TitleActionMetadataType, UserEntitlementMetadata,
        UserPlaybackMetadata,
    };
    use contextual_menu_types::prelude::ContextualMenuMetadata;
    use media_background::types::MediaBackgroundType;
    use std::collections::HashMap;

    #[derive(Clone)]
    pub enum CardOptions {
        Default,
        ImageOption(ImageOptions),
        ActionMissing,
        ActionIncorrect,
        IdMissing,
        TransformItemIdMissing,
        DefaultWithServiceToken,
    }

    #[derive(Clone)]
    pub enum ImageOptions {
        AllImageMissing,
        CoverImageMissing,
        CoverImageAdult,
        CoverImageMissingAdult,
    }

    #[derive(Clone)]
    pub enum EntitlementOptions {
        None,
        OfferIcon,
        EntitledIcon,
        ErrorIcon,
        AdsIcon,
        EntitlementMessageWithIcon,
        EntitlementMessageWithoutIcon,
        HVM,
        LegalMessage,
        TitleMetadataBadgeInfo,
        TitleMetadataBadgeInfoHighlight,
        TitleMetadataBadgeInfoActive,
        TitleMetadataBadgeInfoInactive,
    }
    pub fn create_entitlement_messaging(
        entitlement_options: Vec<&EntitlementOptions>,
    ) -> Option<EntitlementMessaging> {
        if entitlement_options.is_empty() {
            return None;
        }

        // Ensure each of these fields are only modified by at most one of the given entitlement options
        let mut icon: Option<EntitlementGlanceIcons> = None;
        let mut metadata_badge_message: Option<String> = None;
        let mut metadata_badge_level: Option<TitleMetadataBadgeLevel> = None;
        let mut hvm_message: Option<String> = None;
        let mut entitlement_message_icon: Option<EntitlementMessageIcons> = None;
        let mut entitlement_message_message: Option<String> = None;
        let mut legal_messages: Option<Vec<String>> = None;
        let mut all_none: bool = true;

        for entitlement_option in entitlement_options {
            if !matches!(entitlement_option, &EntitlementOptions::None) {
                all_none = false;
            }

            icon = match entitlement_option {
                EntitlementOptions::OfferIcon => Some(EntitlementGlanceIcons::OFFER_ICON),
                EntitlementOptions::EntitledIcon => Some(EntitlementGlanceIcons::ENTITLED_ICON),
                EntitlementOptions::ErrorIcon => Some(EntitlementGlanceIcons::ERROR_ICON),
                EntitlementOptions::AdsIcon => Some(EntitlementGlanceIcons::ADS_ICON),
                _ => icon,
            };

            metadata_badge_message = match entitlement_option {
                EntitlementOptions::TitleMetadataBadgeInfo => Some("badge message".to_string()),
                EntitlementOptions::TitleMetadataBadgeInfoActive => {
                    Some("badge message".to_string())
                }
                EntitlementOptions::TitleMetadataBadgeInfoInactive => {
                    Some("badge message".to_string())
                }
                EntitlementOptions::TitleMetadataBadgeInfoHighlight => {
                    Some("badge message".to_string())
                }
                _ => metadata_badge_message,
            };

            metadata_badge_level = match entitlement_option {
                EntitlementOptions::TitleMetadataBadgeInfo => Some(TitleMetadataBadgeLevel::INFO),
                EntitlementOptions::TitleMetadataBadgeInfoActive => {
                    Some(TitleMetadataBadgeLevel::INFO_ACTIVE)
                }
                EntitlementOptions::TitleMetadataBadgeInfoInactive => {
                    Some(TitleMetadataBadgeLevel::INFO_INACTIVE)
                }
                EntitlementOptions::TitleMetadataBadgeInfoHighlight => {
                    Some(TitleMetadataBadgeLevel::INFO_HIGHLIGHT)
                }
                _ => metadata_badge_level,
            };

            hvm_message = match entitlement_option {
                EntitlementOptions::HVM => Some("hvm message".to_string()),
                _ => hvm_message,
            };

            entitlement_message_icon = match entitlement_option {
                EntitlementOptions::EntitlementMessageWithIcon => {
                    Some(EntitlementMessageIcons::ENTITLED_ICON)
                }
                _ => entitlement_message_icon,
            };

            entitlement_message_message = match entitlement_option {
                EntitlementOptions::EntitlementMessageWithIcon => {
                    Some("entitlement message".to_string())
                }
                EntitlementOptions::EntitlementMessageWithoutIcon => {
                    Some("entitlement message".to_string())
                }
                _ => entitlement_message_message,
            };

            legal_messages = match entitlement_option {
                EntitlementOptions::LegalMessage => Some(vec!["legal message".to_string()]),
                _ => legal_messages,
            };
        }

        if all_none {
            return None;
        }

        Some(EntitlementMessaging {
            GLANCE_MESSAGE_SLOT: Some(EntitlementGlance {
                message: None,
                icon,
                level: None,
                glance_type: None,
            }),
            ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                message: entitlement_message_message,
                icon: entitlement_message_icon,
            }),
            HIGH_VALUE_MESSAGE_SLOT: Some(HighValueMessage {
                message: hvm_message,
                icon: None,
                level: None,
                hvm_type: None,
            }),
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                message: metadata_badge_message,
                level: metadata_badge_level,
            }),
            INFORMATIONAL_MESSAGE_SLOT: Some(InformationalMessage {
                messages: legal_messages,
            }),
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        })
    }

    pub fn create_carousel_card_metadata(
        id: Option<String>,
        action: Option<Action>,
        title: Option<String>,
        secondary_action: Option<Action>,
    ) -> CarouselItemData {
        let actions = match (&action, &secondary_action) {
            (Some(action), Some(secondary_action)) => {
                vec![action.clone(), secondary_action.clone()]
            }
            (Some(action), None) => vec![action.clone()],
            (None, Some(secondary_action)) => vec![secondary_action.clone()],
            (None, None) => vec![],
        };
        CarouselItemData {
            transformItemId: id,
            title,
            synopsis: Some("card synopsis".to_string()),
            action,
            deferredAction: None,
            actions,
            widgetType: None,
        }
    }

    pub fn create_event_metadata(liveliness: Option<String>) -> EventMetadata {
        EventMetadata {
            liveliness,
            liveEventDateBadge: Some(LiveEventDateTime::LOCALIZED_BADGE(
                LocalizedLiveEventBadge {
                    text: "Some Date and Time".to_string(),
                },
            )),
            liveEventDateHeader: Some(LiveEventDateTime::LOCALIZED_HEADER(
                LocalizedLiveEventHeader {
                    date: Some("Some Date".to_string()),
                    time: Some("Some Time".to_string()),
                },
            )),
            venue: Some("Some venue".to_string()),
            scoreBug: Some(ScoreBug {
                currentPeriod: Some(CurrentPeriod {
                    periodClockText: Some("Bot 5th".to_string()),
                    periodClockAccessibilityText: Some("Bottom of inning 5".to_string()),
                }),
                scoreSummary: Some(ScoreSummary {
                    orderedParticipants: Some(vec![
                        OrderedParticipant {
                            name: Some("Diamondbacks".to_string()),
                            formattedScore: Some("5".to_string()),
                            competitionFinalResult: Some("WIN".to_string()),
                        },
                        OrderedParticipant {
                            name: Some("Rockies".to_string()),
                            formattedScore: Some("3".to_string()),
                            competitionFinalResult: Some("LOSS".to_string()),
                        },
                    ]),
                    scoreAccessibilityText: Some("Diamondbacks 5 - Rockies 3".to_string()),
                }),
                scoreBugLastUpdateTimestamp: Some(1751047447508),
            }),
        }
    }

    pub fn create_title_card_metadata(
        images: (Option<String>, Option<String>, bool),
        gti: Option<String>,
        entitlement_options: &EntitlementOptions,
        title_logo_image: Option<String>,
    ) -> TitleCardBaseMetadata {
        let (cover_image, box_art_image, adult) = images;
        TitleCardBaseMetadata {
            coverImage: cover_image,
            boxartImage: box_art_image,
            titleLogoImage: title_logo_image,
            providerLogoImage: None,
            poster2x3Image: None,
            heroImage: None,
            totalReviewCount: None,
            overallRating: None,
            gti,
            badges: None,
            publicReleaseDate: None,
            runtimeSeconds: None,
            entitlementStatus: None,
            entitlementMessaging: create_entitlement_messaging(vec![entitlement_options]),
            genres: vec![],
            watchProgress: None,
            imageAlternateText: None,
            isEntitled: None,
            offerText: None,
            isInWatchlist: None,
            maturityRatingString: Some("maturity rating".to_string()),
            maturityRatingImage: Some(MaturityRatingImage {
                url: "card maturity rating".to_string(),
                dimension: Dimension {
                    width: 10,
                    height: 10,
                },
            }),
            regulatoryLabel: None,
            imageAttributes: Some(ImageAttributes {
                isAdult: Some(adult),
                isRestricted: None,
                individualImageMetadata: None,
            }),
            contentType: Some("MOVIE".to_string()),
            contextualActions: None,
        }
    }

    pub fn create_media_background() -> MediaBackgroundType {
        MediaBackgroundType::None
    }

    pub fn create_contextual_menu_metadata() -> ContextualMenuMetadata {
        ContextualMenuMetadata {
            title: None,
            gti: None,
            is_in_watchlist: None,
            is_linear_title: false,
            primary_action: None,
            secondary_action: None,
            maturity_rating_string: None,
            maturity_rating_image: None,
            tnf_properties: None,
            event_metadata: None,
            liveliness_data: None,
            journey_ingress_context: None,
            content_type: None,
            contextual_actions: None,
            is_favorited: None,
        }
    }

    pub fn create_swift_action() -> SwiftAction {
        SwiftAction {
            text: None,
            analytics: HashMap::new(),
            refMarker: "a ref marker".to_string(),
            pageId: "link_id".to_string(),
            pageType: "link_type".to_string(),
            serviceToken: None,
            journeyIngressContext: None,
        }
    }

    pub fn create_signup_action() -> SignUpAction {
        SignUpAction {
            text: None,
            analytics: HashMap::new(),
            benefitId: "link_benefit_id".to_string(),
            bundleId: None,
            nonSupportedText: "not supported".to_string(),
            uri: "link_uri".to_string(),
            benefitName: None,
            offerDetails: None,
            refMarker: "a ref marker".to_string(),
        }
    }

    pub fn create_playback_action() -> PlaybackAction {
        PlaybackAction::create_populated_action("link_uri")
    }

    pub fn create_player_action() -> TransitionAction {
        TransitionAction::player(PlaybackAction::create_populated_action("uri"))
    }

    pub fn create_station_detail_action() -> TransitionAction {
        TransitionAction::linearStationDetail(create_swift_action())
    }

    pub fn create_title_playback_action(
        label: bool,
        channel_id: bool,
        metadata_action_type: TitleActionMetadataType,
    ) -> TitlePlaybackAction {
        let label = if label {
            Some("some label".to_string())
        } else {
            None
        };
        let channel_id = if channel_id {
            Some("some channel id".to_string())
        } else {
            None
        };
        TitlePlaybackAction {
            playbackMetadata: PlaybackMetadata {
                refMarker: "a ref marker".to_string(),
                contentDescriptors: None,
                playbackExperienceMetadata: None,
                position: PlaybackPosition::FeatureFinished,
                startPositionEpochUtc: None,
                userPlaybackMetadata: UserPlaybackMetadata {
                    runtimeSeconds: Some(0),
                    timecodeSeconds: Some(0),
                    hasStreamed: Some(false),
                    isLinear: None,
                    linearStartTime: None,
                    linearEndTime: None,
                },
                userEntitlementMetadata: UserEntitlementMetadata {
                    entitlementType: "some type".to_string(),
                    benefitType: vec![],
                },
                videoMaterialType: "link_video_material_type".to_string(),
                channelId: channel_id,
                playbackTitle: None,
                metadataActionType: metadata_action_type,
                catalogMetadata: None,
                isTrailer: None,
                isUnopenedRental: false,
            },
            label,
            refMarker: "a ref marker".to_string(),
        }
    }

    pub fn create_title_acquisition_action() -> TitleAcquisitionAction {
        TitleAcquisitionAction {
            label: "acquisition label".to_string(),
            metadata: None,
            refMarker: "a ref marker".to_string(),
        }
    }

    pub fn create_category_page_swift_action() -> CategoryPageSwiftAction {
        CategoryPageSwiftAction {
            swiftAction: create_swift_action(),
            journeyIngressContext: None,
            serviceToken: "a service token".to_string(),
        }
    }

    pub fn create_consent_action() -> ConsentAction {
        ConsentAction {
            refMarker: "a ref marker".to_string(),
            label: "consent label".to_string(),
            channelTierId: "channel tier id".to_string(),
            consentType: "consent type".to_string(),
        }
    }

    pub fn properties_from_options(
        options: &CardOptions,
    ) -> (
        Option<String>,
        Option<Action>,
        Option<String>,
        (Option<String>, Option<String>, bool),
    ) {
        let transform_item_id = match options {
            CardOptions::TransformItemIdMissing | CardOptions::IdMissing => None,
            _ => Some("card id".to_string()),
        };
        let action = match options {
            CardOptions::ActionMissing => None,
            CardOptions::ActionIncorrect => Some(Action::create_link_default()),
            CardOptions::DefaultWithServiceToken => Some(Action::create_transition_landing(Some(
                "service token".to_string(),
            ))),
            _ => Some(Action::create_transition_landing(None)),
        };
        let gti = match options {
            CardOptions::IdMissing => None,
            _ => Some("card gti".to_string()),
        };
        let images = match options {
            CardOptions::ImageOption(ImageOptions::AllImageMissing) => (None, None, false),
            CardOptions::ImageOption(ImageOptions::CoverImageMissing) => {
                (None, Some("box art image".to_string()), false)
            }
            CardOptions::ImageOption(ImageOptions::CoverImageAdult) => (
                Some("card image".to_string()),
                Some("box art image".to_string()),
                true,
            ),
            CardOptions::ImageOption(ImageOptions::CoverImageMissingAdult) => {
                (None, Some("box art image".to_string()), true)
            }
            _ => (
                Some("card image".to_string()),
                Some("box art image".to_string()),
                false,
            ),
        };
        (transform_item_id, action, gti, images)
    }

    pub enum LinkCardOptions {
        ImageAndTextMissing,
        NoTextOverlayPosition,
        Other(CardOptions),
    }

    pub enum ChannelCardOptions {
        SignUpAction,
        LandingAction,
    }

    pub fn create_link_card(options: LinkCardOptions) -> LinkCard {
        let card_options = match &options {
            LinkCardOptions::ImageAndTextMissing => {
                CardOptions::ImageOption(ImageOptions::AllImageMissing)
            }
            LinkCardOptions::Other(card_options) => card_options.clone(),
            LinkCardOptions::NoTextOverlayPosition => CardOptions::DefaultWithServiceToken,
        };
        let (transform_item_id, action, _, images) = properties_from_options(&card_options);
        let title = if matches!(options, LinkCardOptions::ImageAndTextMissing) {
            None
        } else {
            Some("title".to_string())
        };
        let action = if matches!(
            options,
            LinkCardOptions::Other(CardOptions::IdMissing | CardOptions::TransformItemIdMissing)
        ) {
            // except for some very specific action types, we can always construct a fallback id
            // from the action params, so we need to use one of these types to test
            Some(Action::TransitionAction(TransitionAction::profileSettings(
                ProfileSettingsAction {
                    refMarker: "a marker".to_string(),
                    text: None,
                },
            )))
        } else {
            action
        };
        let image_url = images.0;
        let text_overlay_position = if matches!(options, LinkCardOptions::NoTextOverlayPosition) {
            None
        } else {
            Some(OverlayTextPosition::Center_Center)
        };
        let card_metadata = CarouselItemData::populated_default()
            .with_action(action)
            .with_id(transform_item_id)
            .with_title(title)
            .with_widget_type(None);

        LinkCard::populated_default()
            .with_carousel_card_metadata(card_metadata)
            .with_image_url(image_url)
            .with_text_overlay_position(text_overlay_position)
    }

    pub fn create_channel_card(options: ChannelCardOptions) -> ChannelCard {
        let action = Action::TransitionAction(match options {
            ChannelCardOptions::LandingAction => {
                TransitionAction::create_landing_with_service_token(Some("token".to_string()))
            }
            ChannelCardOptions::SignUpAction => TransitionAction::create_sign_up(),
        });

        let card_metadata = CarouselItemData::populated_default()
            .with_action(None)
            .with_actions(vec![action])
            .with_id(Some("card id".to_string()))
            .with_title(Some("title".to_string()))
            .with_widget_type(None);

        ChannelCard::populated_default().with_carousel_card_metadata(card_metadata)
    }
}

#[cfg(test)]
pub struct Utc;

#[cfg(test)]
impl Utc {
    pub fn now() -> chrono::DateTime<chrono::Utc> {
        use chrono::TimeZone;
        chrono::Utc.ymd(2024, 4, 29).and_hms(13, 0, 0)
    }
}
#[cfg(test)]
mod test {
    use super::*;
    use crate::container_item_parsing::common::CarouselItem;
    use amzn_fable_tokens::{FableColor, FableIcon};
    use common_transform_types::actions::{
        Action, ClientAction, PlaybackAction, SwiftAction, TitleAcquisitionAction,
        TitleActionSegment, TitleOpenModalAction, TitlePlaybackAction, TransitionAction,
    };
    use common_transform_types::container_items::{
        Badges, BonusScheduleCard, CarouselItemData, CurrentPeriod, EntitlementMessage,
        EntitlementMessageIcons, EntitlementMessaging, EventCard, EventMetadata, GenericTitleCard,
        ImageAttributes, IndividualImageMetadata, IndividualImageMetadataMapping, LinearAiringItem,
        LinearAttributes, LinearBadges, LinearSchedule, LinearSeriesAttributes, LinearStationCard,
        LinkCard, LiveEventDateTime, LiveLinearCard, LocalizedLiveEventBadge,
        LocalizedLiveEventHeader, MovieCard, OrderedParticipant, ScheduledShow, ScoreBug,
        ScoreSummary, SeriesCard, ShowCard, TitleCardBaseMetadata, TitleMetadataBadge,
        TitleMetadataBadgeLevel,
    };
    use common_transform_types::containers::{
        ChartsCarouselItem, CommonCarouselTitleCardItem, DisplayPlacement, SeeMoreLink,
        ShortCarouselItem, StandardCarouselItem, SuperCarouselItem,
    };
    use common_transform_types::playback_metadata::{
        PlaybackMetadata, TitleActionMetadataType, UserEntitlementMetadata, UserPlaybackMetadata,
    };
    use fableous::badges::label_badge::LabelBadgeColorScheme;
    use fableous::tiles::{
        common_string_ids::{AIV_BLAST_LIVE_BADGE_TEXT, AIV_BLAST_UPCOMING_BADGE_TEXT},
        tile::{TileBadgeProps, TileFontIconProps, TileProgressBarProps},
    };
    use fableous::utils::get_ignx_color;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::prelude::{create_signal, provide_context, Signal, SignalGet};
    use ignx_compositron::reactive::MaybeSignal;
    use ignx_compositron::text::{LocalizedText, TextContent};
    use router::MockRouting;
    use router::{rust_location, RoutingContext};
    use rstest::*;
    use std::collections::HashMap;
    use std::rc::Rc;
    use title_details::types::common::LivelinessData;

    #[fixture]
    fn carousel_card_action() -> SwiftAction {
        SwiftAction {
            text: None,
            analytics: HashMap::default(),
            refMarker: "a mock ref marker ".to_string(),
            pageId: "a mock page id".to_string(),
            pageType: "a mock page type".to_string(),
            serviceToken: None,
            journeyIngressContext: None,
        }
    }

    #[fixture]
    fn carousel_card_metadata() -> CarouselItemData {
        CarouselItemData {
            transformItemId: Some("a mock id".to_string()),
            title: Some("a mock title".to_string()),
            synopsis: Some("a mock synopsis".to_string()),
            action: Some(Action::TransitionAction(TransitionAction::detail(
                carousel_card_action(),
            ))),
            deferredAction: None,
            actions: vec![],
            widgetType: Some("a mock widget type".to_string()),
        }
    }

    #[fixture]
    fn event_metadata() -> EventMetadata {
        EventMetadata {
            liveliness: Some("LIVE".to_string()),
            liveEventDateBadge: Some(LiveEventDateTime::LOCALIZED_BADGE(
                LocalizedLiveEventBadge {
                    text: "Some Date and Time".to_string(),
                },
            )),
            liveEventDateHeader: Some(LiveEventDateTime::LOCALIZED_HEADER(
                LocalizedLiveEventHeader {
                    date: Some("Some Date".to_string()),
                    time: Some("Some Time".to_string()),
                },
            )),
            venue: Some("Some venue".to_string()),
            scoreBug: Some(ScoreBug {
                currentPeriod: Some(CurrentPeriod {
                    periodClockText: Some("Bot 5th".to_string()),
                    periodClockAccessibilityText: Some("Bottom of inning 5".to_string()),
                }),
                scoreSummary: Some(ScoreSummary {
                    orderedParticipants: Some(vec![
                        OrderedParticipant {
                            name: Some("Diamondbacks".to_string()),
                            formattedScore: Some("5".to_string()),
                            competitionFinalResult: Some("WIN".to_string()),
                        },
                        OrderedParticipant {
                            name: Some("Rockies".to_string()),
                            formattedScore: Some("3".to_string()),
                            competitionFinalResult: Some("LOSS".to_string()),
                        },
                    ]),
                    scoreAccessibilityText: Some("Diamondbacks 5 - Rockies 3".to_string()),
                }),
                scoreBugLastUpdateTimestamp: Some(1751047447508),
            }),
        }
    }

    #[fixture]
    fn title_card_metadata() -> TitleCardBaseMetadata {
        TitleCardBaseMetadata {
            coverImage: Some("cover image".to_string()),
            boxartImage: Some("box art image".to_string()),
            titleLogoImage: Some("title logo image".to_string()),
            providerLogoImage: Some("provider logo image".to_string()),
            poster2x3Image: Some("poster image".to_string()),
            heroImage: Some("hero image".to_string()),
            totalReviewCount: Some(0),
            overallRating: Some(0.0),
            gti: Some("gti".to_string()),
            badges: Some(Badges {
                applyAudioDescription: false,
                applyCC: false,
                applyDolbyAtmos: false,
                applyDolby: false,
                applyDolbyVision: false,
                applyHdr10: false,
                applyPrime: false,
                applyUhd: false,
                regulatoryRating: Some("18".to_string()),
                showPSE: false,
            }),
            publicReleaseDate: None,
            runtimeSeconds: None,
            entitlementStatus: None,
            entitlementMessaging: Some(EntitlementMessaging {
                TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                    message: Some("Example Badge".to_string()),
                    level: None,
                }),
                ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                    message: None,
                    icon: Some(EntitlementMessageIcons::OFFER_ICON),
                }),
                GLANCE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                INFORMATIONAL_MESSAGE_SLOT: None,
                BUYBOX_MESSAGE_SLOT: None,
                PRODUCT_SUMMARY_SLOT: None,
                PRODUCT_PROMOTION_SLOT: None,
            }),
            genres: vec![],
            watchProgress: Some(0.2),
            imageAlternateText: None,
            isEntitled: None,
            offerText: None,
            isInWatchlist: Some(false),
            maturityRatingString: None,
            maturityRatingImage: None,
            regulatoryLabel: None,
            imageAttributes: Some(ImageAttributes {
                isAdult: Some(true),
                isRestricted: None,
                individualImageMetadata: Some(IndividualImageMetadataMapping {
                    providerLogoImage: Some(IndividualImageMetadata {
                        height: Some(200),
                        width: Some(300),
                        scalarHorizontal: None,
                        scalarStacked: None,
                        safeToOverlay: None,
                    }),
                }),
            }),
            contentType: Some("MOVIE".to_string()),
            contextualActions: Some(vec![ClientAction {
                text: None,
                refMarker: "some ref marker".to_string(),
                target: "removeFromContinueWatching".to_string(),
            }]),
        }
    }

    fn linear_attributes() -> Option<LinearAttributes> {
        Some(LinearAttributes {
            isOnNow: Some(true),
            schedule: Some(LinearSchedule {
                startTime: 947638923004_u64,
                endTime: 947639523004_u64, // startTime + 10 minutes
            }),
            linearVodAction: None,
            stationName: None,
            upNextShowTitle: None,
        })
    }

    #[fixture]
    fn title_card() -> CommonCarouselTitleCardItem {
        CommonCarouselTitleCardItem::MOVIE(MovieCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(),
            linearAttributes: None,
        })
    }

    #[fixture]
    fn show_title_card() -> CommonCarouselTitleCardItem {
        CommonCarouselTitleCardItem::SHOW(ShowCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(),
            numberOfSeasons: None,
            showName: None,
        })
    }

    fn season_title_card() -> CommonCarouselTitleCardItem {
        CommonCarouselTitleCardItem::SEASON(SeriesCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(),
            linearAttributes: Some(LinearSeriesAttributes {
                linearAttributes: linear_attributes().unwrap(),
                episodeNumber: None,
                episodeTitle: None,
                episodeSynopsis: None,
                episodeGti: None,
            }),
            seasonNumber: None,
            numberOfSeasons: None,
            showName: None,
            episodeNumber: None,
            episodicSynopsis: None,
        })
    }

    fn generic_title_card() -> CommonCarouselTitleCardItem {
        CommonCarouselTitleCardItem::GENERIC_TITLE_CARD(GenericTitleCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(),
        })
    }

    #[fixture]
    fn movie_title_card_with_linear_attributes() -> CommonCarouselTitleCardItem {
        CommonCarouselTitleCardItem::MOVIE(MovieCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(),
            linearAttributes: linear_attributes(),
        })
    }

    #[fixture]
    fn live_event_title_card_with_title_metadata() -> EventCard {
        EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                entitlementMessaging: live_event_card_metadata(),
                ..title_card_metadata()
            },
            eventMetadata: event_metadata(),
        }
    }

    #[fixture]
    fn live_event_title_card_without_title_metadata() -> EventCard {
        EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                entitlementMessaging: card_metadata_with_empty_badge_message(),
                ..title_card_metadata()
            },
            eventMetadata: event_metadata(),
        }
    }

    #[fixture]
    fn upcoming_event_title_card_with_title_metadata() -> EventCard {
        EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                entitlementMessaging: upcoming_event_card_metadata(),
                ..title_card_metadata()
            },
            eventMetadata: EventMetadata {
                liveliness: Some("UPCOMING".to_string()),
                liveEventDateHeader: None,
                liveEventDateBadge: None,
                venue: None,
                scoreBug: None,
            },
        }
    }

    #[fixture]
    fn upcoming_event_title_card_without_title_metadata() -> EventCard {
        EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: TitleCardBaseMetadata {
                entitlementMessaging: card_metadata_with_empty_badge_message(),
                ..title_card_metadata()
            },
            eventMetadata: EventMetadata {
                liveliness: Some("UPCOMING".to_string()),
                liveEventDateHeader: None,
                liveEventDateBadge: None,
                venue: None,
                scoreBug: None,
            },
        }
    }

    #[fixture]
    fn event_title_card_with_no_liveliness() -> EventCard {
        EventCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(),
            eventMetadata: EventMetadata {
                liveliness: None,
                liveEventDateHeader: None,
                liveEventDateBadge: None,
                venue: None,
                scoreBug: None,
            },
        }
    }

    #[fixture]
    fn live_linear_card() -> LiveLinearCard {
        LiveLinearCard {
            carouselCardMetadata: carousel_card_metadata(),
            contextualActions: Some(vec![ClientAction {
                text: None,
                refMarker: "some ref marker".to_string(),
                target: "removeFromContinueWatching".to_string(),
            }]),
            gti: Some("gti".to_string()),
            rating: None,
            image: Some("image".to_string()),
            swiftId: None,
            schedule: vec![ScheduledShow {
                startTimeDate: Some("2024-04-29T12:00Z".to_string()),
                endTimeDate: Some("2024-04-29T14:00Z".to_string()),
                title: None,
                synopsis: None,
                image: Some("image".to_string()),
                heroImage: None,
                rating: None,
                badges: None,
                showContext: None,
                accessControls: None,
            }],
            entitlementMessaging: Some(EntitlementMessaging {
                TITLE_METADATA_BADGE_SLOT: None,
                ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                    message: None,
                    icon: Some(EntitlementMessageIcons::OFFER_ICON),
                }),
                GLANCE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                INFORMATIONAL_MESSAGE_SLOT: None,
                BUYBOX_MESSAGE_SLOT: None,
                PRODUCT_SUMMARY_SLOT: None,
                PRODUCT_PROMOTION_SLOT: None,
            }),
        }
    }

    #[fixture]
    fn linear_station_card() -> LinearStationCard {
        let action = Action::TransitionAction(TransitionAction::detail(carousel_card_action()));
        LinearStationCard {
            gti: Some("gti".to_string()),
            rating: None,
            image: Some("image".to_string()),
            name: Some("LinearStationCard name".to_string()),
            schedule: vec![LinearAiringItem {
                airingId: Some("airingId".to_string()),
                startTime: "2024-04-29T12:00Z".to_string(),
                endTime: "2024-04-29T14:00Z".to_string(),
                title: None,
                synopsis: None,
                image: None,
                heroImage: None,
                rating: None,
                context: None,
                contentDescriptors: vec![],
                badges: Some(LinearBadges::default()),
            }],
            stationLogo: None,
            entitlementMessaging: Some(EntitlementMessaging {
                TITLE_METADATA_BADGE_SLOT: None,
                ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                    message: None,
                    icon: Some(EntitlementMessageIcons::OFFER_ICON),
                }),
                GLANCE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                INFORMATIONAL_MESSAGE_SLOT: None,
                BUYBOX_MESSAGE_SLOT: None,
                PRODUCT_SUMMARY_SLOT: None,
                PRODUCT_PROMOTION_SLOT: None,
            }),
            widgetType: "linearStationCard".to_string(),
            actions: vec![action.clone()],
            action: Some(action),
            contextualActions: Some(vec![ClientAction {
                text: None,
                refMarker: "some ref marker".to_string(),
                target: "removeFromContinueWatching".to_string(),
            }]),
            deferredAction: None,
            reactions: None,
        }
    }

    #[fixture]
    fn linear_station_card_with_station_detail_action() -> LinearStationCard {
        let mut card = linear_station_card();

        card.actions.push(Action::TransitionAction(
            TransitionAction::linearStationDetail(carousel_card_action()),
        ));

        card
    }

    #[fixture]
    fn link_card() -> LinkCard {
        LinkCard {
            carouselCardMetadata: carousel_card_metadata(),
            imageAlternateText: None,
            headerText: None,
            imageUrl: Some("image".to_string()),
            backgroundImageUrl: Some("bgimage".to_string()),
            isEntitled: None,
            offerText: None,
            overlayTextPosition: None,
            logoImageUrl: None,
            entitlementMessaging: Some(EntitlementMessaging {
                TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                    message: Some("Example Badge".to_string()),
                    level: None,
                }),
                ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                    message: None,
                    icon: Some(EntitlementMessageIcons::OFFER_ICON),
                }),
                GLANCE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT: None,
                HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                INFORMATIONAL_MESSAGE_SLOT: None,
                BUYBOX_MESSAGE_SLOT: None,
                PRODUCT_SUMMARY_SLOT: None,
                PRODUCT_PROMOTION_SLOT: None,
            }),
            regulatoryLabel: None,
            description: None,
        }
    }

    #[fixture]
    fn bonus_schedule_card() -> BonusScheduleCard {
        BonusScheduleCard {
            carouselCardMetadata: carousel_card_metadata(),
            titleCardBaseMetadata: title_card_metadata(),
            eventMetadata: event_metadata(),
        }
    }

    #[fixture]
    fn empty_badge() -> Option<TileBadgeProps> {
        None
    }

    #[fixture]
    fn example_badge_props() -> Option<TileBadgeProps> {
        Some(TileBadgeProps {
            text: MaybeSignal::Static(TextContent::from("Example Badge")),
            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::PRIMARY),
        })
    }

    #[fixture]
    fn empty_icon_props() -> Option<TileFontIconProps> {
        None
    }

    #[fixture]
    fn example_icon_props() -> Option<TileFontIconProps> {
        Some(TileFontIconProps {
            icon: MaybeSignal::Static(FableIcon::STORE_FILLED.to_string()),
            color: MaybeSignal::Static(get_ignx_color(FableColor::STORE)),
        })
    }

    #[fixture]
    fn empty_progress_bar_props() -> Option<TileProgressBarProps> {
        None
    }

    #[fixture]
    fn card_metadata_with_empty_badge() -> Option<EntitlementMessaging> {
        Some(EntitlementMessaging {
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                message: Some("".to_string()),
                level: None,
            }),
            ENTITLEMENT_MESSAGE_SLOT: None,
            GLANCE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        })
    }

    #[fixture]
    fn live_event_card_metadata() -> Option<EntitlementMessaging> {
        Some(EntitlementMessaging {
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                message: Some("LIVE".to_string()),
                level: Some(TitleMetadataBadgeLevel::INFO_HIGHLIGHT),
            }),
            ENTITLEMENT_MESSAGE_SLOT: None,
            GLANCE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        })
    }

    #[fixture]
    fn upcoming_event_card_metadata() -> Option<EntitlementMessaging> {
        Some(EntitlementMessaging {
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                message: Some("UPCOMING".to_string()),
                level: Some(TitleMetadataBadgeLevel::INFO_INACTIVE),
            }),
            ENTITLEMENT_MESSAGE_SLOT: None,
            GLANCE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        })
    }

    #[fixture]
    fn card_metadata_with_no_badge() -> Option<EntitlementMessaging> {
        Some(EntitlementMessaging {
            TITLE_METADATA_BADGE_SLOT: None,
            ENTITLEMENT_MESSAGE_SLOT: None,
            GLANCE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        })
    }

    #[fixture]
    fn card_metadata_with_empty_badge_message() -> Option<EntitlementMessaging> {
        Some(EntitlementMessaging {
            TITLE_METADATA_BADGE_SLOT: Some(TitleMetadataBadge {
                message: None,
                level: None,
            }),
            ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                message: None,
                icon: None,
            }),
            GLANCE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT: None,
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        })
    }

    #[rstest]
    #[case(StandardCarouselItem::TITLE_CARD(title_card()), "cover image")]
    #[case(StandardCarouselItem::LIVE_LINEAR_CARD(live_linear_card()), "image")]
    #[case(StandardCarouselItem::LINK_CARD(link_card()), "image")]
    #[case(
        StandardCarouselItem::BONUS_SCHEDULE_CARD(bonus_schedule_card()),
        "cover image"
    )]
    fn test_get_tile_image_standard_carousel(
        #[case] item: StandardCarouselItem,
        #[case] expected_image_url: &str,
    ) {
        assert_eq!(expected_image_url, item.get_tile_image().clone().unwrap());
    }

    #[rstest]
    #[case(ChartsCarouselItem::TITLE_CARD(title_card()), "cover image")]
    fn test_get_tile_image_chart_carousel(
        #[case] item: ChartsCarouselItem,
        #[case] expected_image_url: &str,
    ) {
        assert_eq!(expected_image_url, item.get_tile_image().clone().unwrap());
    }

    #[rstest]
    #[case(ScheduleCarouselItem {
    eventCard: live_event_title_card_with_title_metadata(),
    isOffPlatformTitle: Some(false),
    isCustomerGeoRestrictedToLiveEvent: None
        }, "cover image")]
    fn test_get_tile_image_schedule_carousel(
        #[case] item: ScheduleCarouselItem,
        #[case] expected_image_url: &str,
    ) {
        assert_eq!(expected_image_url, item.get_tile_image().clone().unwrap());
    }

    #[rstest]
    #[case(SuperCarouselItem::TITLE_CARD(title_card()), "poster image")]
    #[case(SuperCarouselItem::LINK_CARD(link_card()), "image")]
    fn test_get_tile_image_super_carousel(
        #[case] item: SuperCarouselItem,
        #[case] expected_image_url: &str,
    ) {
        assert_eq!(expected_image_url, item.get_tile_image().clone().unwrap());
    }

    #[rstest]
    #[case(StandardCarouselItem::TITLE_CARD(title_card()), "a mock title")]
    #[case(
        StandardCarouselItem::LIVE_LINEAR_CARD(live_linear_card()),
        "a mock title"
    )]
    #[case(StandardCarouselItem::LINK_CARD(link_card()), "a mock title")]
    #[case(
        StandardCarouselItem::BONUS_SCHEDULE_CARD(bonus_schedule_card()),
        "a mock title"
    )]
    fn test_get_fallback_text_standard_carousel(
        #[case] item: StandardCarouselItem,
        #[case] expected_text: &str,
    ) {
        assert_eq!(
            expected_text,
            item.get_missing_image_fallback_text().clone().unwrap()
        );
    }

    #[rstest]
    #[case(ChartsCarouselItem::TITLE_CARD(title_card()), "a mock title")]
    fn test_get_fallback_text_chart_carousel(
        #[case] item: ChartsCarouselItem,
        #[case] expected_text: &str,
    ) {
        assert_eq!(
            expected_text,
            item.get_missing_image_fallback_text().clone().unwrap()
        );
    }

    #[rstest]
    #[case(SuperCarouselItem::TITLE_CARD(title_card()), "a mock title")]
    #[case(SuperCarouselItem::LINK_CARD(link_card()), "a mock title")]
    fn test_get_fallback_text_super_carousel(
        #[case] item: SuperCarouselItem,
        #[case] expected_text: &str,
    ) {
        assert_eq!(
            expected_text,
            item.get_missing_image_fallback_text().clone().unwrap()
        );
    }

    #[rstest]
    #[case(
        StandardCarouselItem::TITLE_CARD(
            CommonCarouselTitleCardItem::MOVIE(MovieCard{
                carouselCardMetadata: carousel_card_metadata(),
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    entitlementMessaging: card_metadata_with_empty_badge(),
                    ..title_card_metadata()
                },
                linearAttributes: None,
                })),
        empty_badge()
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(
            CommonCarouselTitleCardItem::MOVIE(MovieCard{
                carouselCardMetadata: carousel_card_metadata(),
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    entitlementMessaging: card_metadata_with_no_badge(),
                    ..title_card_metadata()
                },
                linearAttributes: None,
                })),
        empty_badge()
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(
            CommonCarouselTitleCardItem::MOVIE(MovieCard{
                carouselCardMetadata: carousel_card_metadata(),
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    entitlementMessaging: card_metadata_with_empty_badge_message(),
                    ..title_card_metadata()
                },
                linearAttributes: None,
                })),
        empty_badge()
    )]
    #[case(StandardCarouselItem::TITLE_CARD(title_card()), example_badge_props())]
    #[case(StandardCarouselItem::TITLE_CARD(CommonCarouselTitleCardItem::EVENT(live_event_title_card_with_title_metadata())),
        Some(TileBadgeProps{
            text: MaybeSignal::Static(TextContent::from("LIVE")),
            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::LIVE),
            }))]
    #[case(StandardCarouselItem::TITLE_CARD(CommonCarouselTitleCardItem::EVENT(live_event_title_card_without_title_metadata())),
        Some(TileBadgeProps{
            text: MaybeSignal::Static(TextContent::LocalizedText(LocalizedText::new(
                AIV_BLAST_LIVE_BADGE_TEXT,
            ))),
            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::LIVE),
            }))]
    #[case(StandardCarouselItem::TITLE_CARD(CommonCarouselTitleCardItem::EVENT(upcoming_event_title_card_with_title_metadata())),
        Some(TileBadgeProps{
            text: MaybeSignal::Static(TextContent::from("UPCOMING")),
            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::SECONDARY),
            }))]
    #[case(StandardCarouselItem::TITLE_CARD(CommonCarouselTitleCardItem::EVENT(upcoming_event_title_card_without_title_metadata())),
        Some(TileBadgeProps{
            text: MaybeSignal::Static(TextContent::LocalizedText(LocalizedText::new(
                AIV_BLAST_UPCOMING_BADGE_TEXT,
            ))),
            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::SECONDARY),
            }))]
    #[case(
        StandardCarouselItem::TITLE_CARD(CommonCarouselTitleCardItem::EVENT(
            event_title_card_with_no_liveliness()
        )),
        example_badge_props()
    )]
    #[case(
        StandardCarouselItem::LIVE_LINEAR_CARD(live_linear_card()),
        Some(TileBadgeProps{
            text: MaybeSignal::Static(TextContent::from("ON NOW")),
            color_scheme: MaybeSignal::Static(LabelBadgeColorScheme::PRIMARY),
            })
    )]
    #[case(StandardCarouselItem::LINK_CARD(link_card()), example_badge_props())]
    #[case(
        StandardCarouselItem::BONUS_SCHEDULE_CARD(bonus_schedule_card()),
        example_badge_props()
    )]
    fn test_get_title_metadata_badge_props_standard_carousel(
        #[case] item: StandardCarouselItem,
        #[case] expected_badge_props: Option<TileBadgeProps>,
    ) {
        assert_eq!(expected_badge_props, item.get_title_metadata_badge_props());
    }

    #[rstest]
    #[case(ChartsCarouselItem::TITLE_CARD(title_card()), example_badge_props())]
    fn test_get_title_metadata_badge_props_chart_carousel(
        #[case] item: ChartsCarouselItem,
        #[case] expected_badge_props: Option<TileBadgeProps>,
    ) {
        assert_eq!(expected_badge_props, item.get_title_metadata_badge_props());
    }

    #[rstest]
    #[case(SuperCarouselItem::TITLE_CARD(title_card()), example_badge_props())]
    #[case(SuperCarouselItem::LINK_CARD(link_card()), example_badge_props())]
    fn test_get_title_metadata_badge_props_super_carousel(
        #[case] item: SuperCarouselItem,
        #[case] expected_badge_props: Option<TileBadgeProps>,
    ) {
        assert_eq!(expected_badge_props, item.get_title_metadata_badge_props());
    }

    #[rstest]
    #[case(
        StandardCarouselItem::TITLE_CARD(
            CommonCarouselTitleCardItem::MOVIE(MovieCard{
                carouselCardMetadata: carousel_card_metadata(),
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    entitlementMessaging: card_metadata_with_no_badge(),
                    ..title_card_metadata()
                },
                linearAttributes: None,
                })),
        empty_icon_props()
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(
            CommonCarouselTitleCardItem::MOVIE(MovieCard{
                carouselCardMetadata: carousel_card_metadata(),
                titleCardBaseMetadata: TitleCardBaseMetadata {
                    entitlementMessaging: card_metadata_with_empty_badge_message(),
                    ..title_card_metadata()
                },
                linearAttributes: None,
                })),
        empty_icon_props()
    )]
    #[case(StandardCarouselItem::TITLE_CARD(title_card()), example_icon_props())]
    #[case(
        StandardCarouselItem::LIVE_LINEAR_CARD(live_linear_card()),
        example_icon_props()
    )]
    #[case(
        StandardCarouselItem::LINEAR_STATION(linear_station_card()),
        example_icon_props()
    )]
    #[case(StandardCarouselItem::LINK_CARD(link_card()), example_icon_props())]
    #[case(
        StandardCarouselItem::BONUS_SCHEDULE_CARD(bonus_schedule_card()),
        example_icon_props()
    )]
    fn test_get_entitlement_message_icon_props_standard_carousel(
        #[case] item: StandardCarouselItem,
        #[case] expected_icon_props: Option<TileFontIconProps>,
    ) {
        assert_eq!(
            expected_icon_props,
            item.get_entitlement_message_icon_props()
        );
    }

    #[rstest]
    #[case(ChartsCarouselItem::TITLE_CARD(title_card()), example_icon_props())]
    fn test_get_entitlement_message_icon_props_chart_carousel(
        #[case] item: ChartsCarouselItem,
        #[case] expected_icon_props: Option<TileFontIconProps>,
    ) {
        assert_eq!(
            expected_icon_props,
            item.get_entitlement_message_icon_props()
        );
    }

    #[rstest]
    #[case(SuperCarouselItem::TITLE_CARD(title_card()), example_icon_props())]
    #[case(SuperCarouselItem::LINK_CARD(link_card()), example_icon_props())]
    fn test_get_entitlement_message_icon_props_super_carousel(
        #[case] item: SuperCarouselItem,
        #[case] expected_icon_props: Option<TileFontIconProps>,
    ) {
        assert_eq!(
            expected_icon_props,
            item.get_entitlement_message_icon_props()
        );
    }

    #[fixture]
    fn title_vod_extra_contextual_menu_metadata() -> ContextualMenuMetadata {
        let carousel_card_metadata = carousel_card_metadata();
        let title_card_metadata = title_card_metadata();
        ContextualMenuMetadata {
            title: carousel_card_metadata.title,
            gti: title_card_metadata.gti,
            is_in_watchlist: title_card_metadata.isInWatchlist,
            is_linear_title: false,
            maturity_rating_string: title_card_metadata.maturityRatingString,
            maturity_rating_image: title_card_metadata.maturityRatingImage,
            primary_action: Some(TransitionAction::detail(carousel_card_action())),
            secondary_action: None,
            liveliness_data: Some(LivelinessData {
                message: Some("Example Badge".to_string()),
                level: None,
                liveliness: None,
            }),
            event_metadata: None,
            tnf_properties: None,
            journey_ingress_context: None,
            content_type: title_card_metadata.contentType,
            contextual_actions: Some(vec![ClientAction {
                text: None,
                refMarker: "some ref marker".to_string(),
                target: "removeFromContinueWatching".to_string(),
            }]),
            is_favorited: None,
        }
    }

    #[fixture]
    fn title_vod_extra_contextual_menu_metadata_with_linear_title() -> ContextualMenuMetadata {
        let mut metadata = title_vod_extra_contextual_menu_metadata();
        metadata.is_linear_title = true;
        metadata
    }

    #[fixture]
    fn event_card_contextual_menu_metadata() -> ContextualMenuMetadata {
        let card = live_event_title_card_with_title_metadata();

        ContextualMenuMetadata {
            title: card.carouselCardMetadata.title,
            gti: card.titleCardBaseMetadata.gti,
            is_in_watchlist: card.titleCardBaseMetadata.isInWatchlist,
            is_linear_title: false,
            maturity_rating_string: card.titleCardBaseMetadata.maturityRatingString,
            maturity_rating_image: card.titleCardBaseMetadata.maturityRatingImage,
            primary_action: Some(TransitionAction::detail(carousel_card_action())),
            secondary_action: None,
            event_metadata: Some(card.eventMetadata.clone()),
            tnf_properties: None,
            liveliness_data: Some(LivelinessData {
                level: card
                    .titleCardBaseMetadata
                    .entitlementMessaging
                    .as_ref()
                    .and_then(|message| message.TITLE_METADATA_BADGE_SLOT.clone())
                    .and_then(|data| data.level)
                    .map(|level| level.into()),
                message: card
                    .titleCardBaseMetadata
                    .entitlementMessaging
                    .as_ref()
                    .and_then(|message| message.TITLE_METADATA_BADGE_SLOT.clone())
                    .and_then(|data| data.message.clone()),
                liveliness: card
                    .eventMetadata
                    .liveliness
                    .as_deref()
                    .map(|l| Liveliness::from_str(l).ok())
                    .flatten(),
            }),
            journey_ingress_context: None,
            content_type: card.titleCardBaseMetadata.contentType.clone(),
            contextual_actions: Some(vec![ClientAction {
                text: None,
                refMarker: "some ref marker".to_string(),
                target: "removeFromContinueWatching".to_string(),
            }]),
            is_favorited: None,
        }
    }

    #[fixture]
    fn live_linear_contextual_menu_metadata() -> ContextualMenuMetadata {
        let card = live_linear_card();
        ContextualMenuMetadata {
            title: card.carouselCardMetadata.title,
            gti: card.gti,
            is_in_watchlist: None,
            is_linear_title: false,
            maturity_rating_string: card.rating,
            maturity_rating_image: None,
            primary_action: Some(TransitionAction::detail(carousel_card_action())),
            secondary_action: None,
            event_metadata: None,
            tnf_properties: None,
            liveliness_data: None,
            journey_ingress_context: None,
            content_type: None,
            contextual_actions: Some(vec![ClientAction {
                text: None,
                refMarker: "some ref marker".to_string(),
                target: "removeFromContinueWatching".to_string(),
            }]),
            is_favorited: None,
        }
    }

    #[fixture]
    fn link_card_contextual_menu_metadata() -> ContextualMenuMetadata {
        let carousel_card_metadata = carousel_card_metadata();
        ContextualMenuMetadata {
            title: carousel_card_metadata.title,
            gti: None,
            is_in_watchlist: None,
            is_linear_title: false,
            primary_action: Some(TransitionAction::detail(carousel_card_action())),
            secondary_action: None,
            maturity_rating_string: None,
            maturity_rating_image: None,
            event_metadata: None,
            tnf_properties: None,
            liveliness_data: None,
            journey_ingress_context: None,
            content_type: None,
            contextual_actions: None,
            is_favorited: None,
        }
    }

    #[fixture]
    fn bonus_schedule_card_contextual_menu_metadata() -> ContextualMenuMetadata {
        let carousel_card_metadata = carousel_card_metadata();
        let title_metadata = title_card_metadata();
        ContextualMenuMetadata {
            title: carousel_card_metadata.title,
            gti: title_metadata.gti,
            is_in_watchlist: title_metadata.isInWatchlist,
            is_linear_title: false,
            primary_action: Some(TransitionAction::detail(carousel_card_action())),
            secondary_action: None,
            maturity_rating_string: title_metadata.maturityRatingString,
            maturity_rating_image: title_metadata.maturityRatingImage,
            event_metadata: Some(event_metadata()),
            tnf_properties: None,
            liveliness_data: Some(LivelinessData {
                message: Some("Example Badge".to_string()),
                level: None,
                liveliness: Some(Liveliness::Live),
            }),
            journey_ingress_context: None,
            content_type: title_metadata.contentType,
            contextual_actions: None,
            is_favorited: None,
        }
    }

    #[fixture]
    fn see_more_link_contextual_menu_metadata() -> ContextualMenuMetadata {
        ContextualMenuMetadata {
            title: see_more_link().title,
            gti: None,
            primary_action: Some(TransitionAction::detail(carousel_card_action())),
            secondary_action: None,
            is_in_watchlist: None,
            is_linear_title: false,
            maturity_rating_string: None,
            maturity_rating_image: None,
            event_metadata: None,
            tnf_properties: None,
            liveliness_data: None,
            journey_ingress_context: None,
            content_type: None,
            contextual_actions: None,
            is_favorited: None,
        }
    }

    #[fixture]
    fn see_more_link() -> SeeMoreLink {
        SeeMoreLink {
            displayPlacement: Some(DisplayPlacement::Start),
            title: Some("Mock title".to_string()),
            linkText: None,
            accessibilityText: None,
            action: None,
            linkAction: Some(Action::TransitionAction(TransitionAction::detail(
                carousel_card_action(),
            ))),
            backgroundImage: None,
            linkImage: None,
            logoImage: None,
            description: None,
        }
    }

    #[fixture]
    fn linear_station_card_contextual_menu_metadata() -> ContextualMenuMetadata {
        let linear_station = linear_station_card();
        ContextualMenuMetadata {
            title: linear_station.name,
            gti: linear_station.gti,
            is_in_watchlist: None,
            is_linear_title: false,
            primary_action: Some(TransitionAction::detail(carousel_card_action())),
            secondary_action: None,
            maturity_rating_string: None,
            maturity_rating_image: None,
            event_metadata: None,
            tnf_properties: None,
            liveliness_data: None,
            journey_ingress_context: None,
            content_type: None,
            contextual_actions: Some(vec![ClientAction {
                text: None,
                refMarker: "some ref marker".to_string(),
                target: "removeFromContinueWatching".to_string(),
            }]),
            is_favorited: None,
        }
    }

    #[fixture]
    fn linear_station_card_contextual_menu_metadata_with_secondary_action() -> ContextualMenuMetadata
    {
        let mut menu = linear_station_card_contextual_menu_metadata();

        menu.secondary_action = Some(TransitionAction::linearStationDetail(carousel_card_action()));

        menu
    }

    #[rstest]
    #[case(
        StandardCarouselItem::TITLE_CARD(title_card()),
        title_vod_extra_contextual_menu_metadata()
    )]
    #[case(
        StandardCarouselItem::VOD_EXTRA_CONTENT(title_card()),
        title_vod_extra_contextual_menu_metadata()
    )]
    #[case(
        StandardCarouselItem::LIVE_LINEAR_CARD(live_linear_card()),
        live_linear_contextual_menu_metadata()
    )]
    #[case(
        StandardCarouselItem::LINK_CARD(link_card()),
        link_card_contextual_menu_metadata()
    )]
    #[case(
        StandardCarouselItem::BONUS_SCHEDULE_CARD(bonus_schedule_card()),
        bonus_schedule_card_contextual_menu_metadata()
    )]
    #[case(
        StandardCarouselItem::LINEAR_STATION(linear_station_card()),
        linear_station_card_contextual_menu_metadata()
    )]
    #[case(
        StandardCarouselItem::LINEAR_STATION(linear_station_card_with_station_detail_action()),
        linear_station_card_contextual_menu_metadata_with_secondary_action()
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(movie_title_card_with_linear_attributes()),
        title_vod_extra_contextual_menu_metadata_with_linear_title()
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(season_title_card()),
        title_vod_extra_contextual_menu_metadata_with_linear_title()
    )]
    #[case(
        StandardCarouselItem::TITLE_CARD(CommonCarouselTitleCardItem::EVENT(
            live_event_title_card_with_title_metadata()
        )),
        event_card_contextual_menu_metadata()
    )]
    fn test_get_contextual_menu_metadata_standard_carousel(
        #[case] item: StandardCarouselItem,
        #[case] expected_contextual_menu_metadata: ContextualMenuMetadata,
    ) {
        let data = item.get_contextual_menu_metadata();
        assert_eq!(data, expected_contextual_menu_metadata);
    }

    #[rstest]
    #[case(
        ChartsCarouselItem::TITLE_CARD(title_card()),
        title_vod_extra_contextual_menu_metadata()
    )]
    fn test_get_contextual_menu_metadata_charts_carousel(
        #[case] item: ChartsCarouselItem,
        #[case] expected_contextual_menu_metadata: ContextualMenuMetadata,
    ) {
        let data = item.get_contextual_menu_metadata();
        assert_eq!(data, expected_contextual_menu_metadata);
    }

    #[rstest]
    #[case(
        SuperCarouselItem::TITLE_CARD(title_card()),
        title_vod_extra_contextual_menu_metadata()
    )]
    #[case(
        SuperCarouselItem::LINK_CARD(link_card()),
        link_card_contextual_menu_metadata()
    )]
    fn test_get_contextual_menu_metadata_super_carousel(
        #[case] item: SuperCarouselItem,
        #[case] expected_contextual_menu_metadata: ContextualMenuMetadata,
    ) {
        let data = item.get_contextual_menu_metadata();
        assert_eq!(data, expected_contextual_menu_metadata);
    }

    #[rstest]
    #[case(
        ShortCarouselItem::LINK_CARD(link_card()),
        link_card_contextual_menu_metadata()
    )]
    fn test_get_contextual_menu_metadata_short_carousel(
        #[case] item: ShortCarouselItem,
        #[case] expected_contextual_menu_metadata: ContextualMenuMetadata,
    ) {
        let data = item.get_contextual_menu_metadata();
        assert_eq!(data, expected_contextual_menu_metadata);
    }

    #[rstest]
    #[case(see_more_link(), see_more_link_contextual_menu_metadata())]
    fn test_get_contextual_menu_metadata_see_more_link(
        #[case] see_more_link: SeeMoreLink,
        #[case] expected_contextual_menu_metadata: ContextualMenuMetadata,
    ) {
        let data = get_see_more_link_contextual_menu_metadata(&see_more_link);
        assert_eq!(data, expected_contextual_menu_metadata);
    }

    #[rstest]
    #[case(
        ScheduleCarouselItem {
        eventCard: live_event_title_card_with_title_metadata(),
        isOffPlatformTitle: None,
        isCustomerGeoRestrictedToLiveEvent: None
            },
        event_card_contextual_menu_metadata()
    )]
    fn test_get_contextual_menu_metadata_schedule_carousel(
        #[case] item: ScheduleCarouselItem,
        #[case] expected_contextual_menu_metadata: ContextualMenuMetadata,
    ) {
        let data = item.get_contextual_menu_metadata();
        assert_eq!(data, expected_contextual_menu_metadata);
    }

    #[rstest]
    #[case("home", "home", false)]
    #[case("home", "detail", false)]
    #[case("unknown", "MyStuff", false)]
    #[case("home", "MyStuff", true)]
    fn test_is_my_stuff_page(
        #[case] page_type: String,
        #[case] page_id: String,
        #[case] expected: bool,
    ) {
        let mut mock_routing = MockRouting::new();
        launch_only_scope(move |scope| {
            let (location_signal, _) = create_signal(
                scope,
                rust_location!(RUST_COLLECTIONS, {"pageType" => page_type.clone(), "pageId" => page_id.clone()}),
            );

            mock_routing
                .expect_location()
                .return_once_st(move || Signal::derive(scope, move || location_signal.get()));

            provide_context::<RoutingContext>(scope, Rc::new(mock_routing));

            assert_eq!(is_my_stuff_page(scope), expected);
        });
    }

    mod button {
        use super::*;
        use common_transform_types::actions::{ConsentAction, LIVE_EVENT_STREAMING_TYPE};

        pub fn carousel_card_metadata(
            actions: Vec<Action>,
            action: Option<Action>,
        ) -> CarouselItemData {
            CarouselItemData {
                transformItemId: Some("a mock id".to_string()),
                title: Some("a mock title".to_string()),
                synopsis: Some("a mock synopsis".to_string()),
                action,
                deferredAction: None,
                actions,
                widgetType: Some("a mock widget type".to_string()),
            }
        }

        fn open_modal_action() -> TransitionAction {
            TransitionAction::openModal(TitleOpenModalAction {
                refMarker: "Test RefMarker".to_string(),
                label: "Open Modal Label".to_string(),
                modalHeader: "Test header".to_string(),
                actionSegments: vec![TitleActionSegment {
                    childActions: vec![playback_action(TitleActionMetadataType::Playback, None)],
                    entitlementMessaging: None,
                }],
            })
        }

        fn playback_action(
            metadata_action_type: TitleActionMetadataType,
            label: Option<String>,
        ) -> TransitionAction {
            TransitionAction::playback(TitlePlaybackAction {
                playbackMetadata: PlaybackMetadata {
                    refMarker: "ref_marker".to_string(),
                    contentDescriptors: None,
                    playbackExperienceMetadata: None,
                    position:
                        common_transform_types::playback_metadata::PlaybackPosition::FeatureFinished,
                    startPositionEpochUtc: None,
                    userPlaybackMetadata: UserPlaybackMetadata {
                        runtimeSeconds: Some(0),
                        timecodeSeconds: Some(0),
                        hasStreamed: Some(false),
                        isLinear: None,
                        linearStartTime: None,
                        linearEndTime: None,
                    },
                    userEntitlementMetadata: UserEntitlementMetadata {
                        entitlementType: "type".to_string(),
                        benefitType: vec![],
                    },
                    videoMaterialType: "type".to_string(),
                    channelId: None,
                    playbackTitle: None,
                    metadataActionType: metadata_action_type,
                    catalogMetadata: None,
                    isTrailer: None,
                    isUnopenedRental: false,
                },
                label,
                refMarker: "ref_marker".to_string(),
            })
        }

        fn player_action(
            label: Option<String>,
            streaming_type: Option<String>,
        ) -> TransitionAction {
            TransitionAction::player(
                PlaybackAction::create_populated_action("a_uri")
                    .with_label(label)
                    .with_streaming_type(streaming_type),
            )
        }

        fn acquisition_action(label: String) -> TransitionAction {
            TransitionAction::acquisition(TitleAcquisitionAction {
                label,
                metadata: None,
                refMarker: "ref_marker".to_string(),
            })
        }

        fn more_details_action() -> TransitionAction {
            TransitionAction::legacyDetail(SwiftAction {
                text: None,
                analytics: HashMap::new(),
                refMarker: "ref_marker".to_string(),
                pageId: "pageId".to_string(),
                pageType: "pageType".to_string(),
                serviceToken: Some("service token".to_string()),
                journeyIngressContext: Some("token".to_string()),
            })
        }

        fn consent_action() -> TransitionAction {
            TransitionAction::consent(ConsentAction {
                refMarker: "ref_marker".to_string(),
                label: "Watch Episode 1".to_string(),
                channelTierId: "channelTier".to_string(),
                consentType: "consentType".to_string(),
            })
        }

        #[rstest]
        #[case(None, None)]
        #[case(
            Some(playback_action(TitleActionMetadataType::Playback, Some("text".to_string()))),
            Some(TextContent::from("text"))
        )]
        #[case(Some(consent_action()), Some(TextContent::from("Watch Episode 1")))]
        #[case(
            Some(playback_action(TitleActionMetadataType::AcquisitionPrime, Some("text".to_string()))),
            None
        )]
        #[case(Some(playback_action(TitleActionMetadataType::Playback, None)), None)]
        #[case(
            Some(acquisition_action("text".to_string())),
            Some(TextContent::from("text"))
        )]
        #[case(
            Some(more_details_action()),
            Some(TextContent::LocalizedText(LocalizedText::new(AV_LRC_MORE_DETAILS)))
        )]
        #[case(Some(open_modal_action()), Some(TextContent::from("Open Modal Label")))]
        fn test_get_button_label(
            #[case] action: Option<TransitionAction>,
            #[case] expected: Option<TextContent>,
        ) {
            assert_eq!(get_button_label(action.as_ref()), expected);
        }

        #[rstest]
        #[case::linear_player_action_no_label(
            Some(player_action(None, Some("LiveChannel".to_string()))),
            Some(TextContent::LocalizedText(LocalizedText::new(AV_LRC_WATCH_LIVE)))
        )]
        #[case::linear_player_action_with_label(
            Some(player_action(Some("Watch Label".to_string()), None)),
            Some(TextContent::String("Watch Label".to_string()))
        )]
        #[case::live_event_player_action(
            Some(player_action(Some("Label".to_string()), Some(LIVE_EVENT_STREAMING_TYPE.to_string()))),
            Some(TextContent::LocalizedText(LocalizedText::new(AV_LRC_WATCH_NOW)))
        )]
        fn get_player_action_button_label(
            #[case] action: Option<TransitionAction>,
            #[case] expected: Option<TextContent>,
        ) {
            assert_eq!(get_button_label(action.as_ref()), expected);
        }

        #[rstest]
        #[case(None, None)]
        #[case(
            Some(playback_action(TitleActionMetadataType::Playback, None)),
            Some(FableIcon::PLAY.to_string())
        )]
        #[case(
            Some(player_action(None, None)),
            Some(FableIcon::PLAY.to_string())
        )]
        #[case(
            Some(consent_action()),
            Some(FableIcon::PLAY.to_string())
        )]
        #[case(
            Some(more_details_action()),
            Some(FableIcon::INFO.to_string())
        )]
        #[case(
            Some(acquisition_action("text".to_string())),
            None
        )]
        #[case(
            Some(open_modal_action()),
            Some(FableIcon::PLAY.to_string())
        )]
        fn test_get_button_icon(
            #[case] action: Option<TransitionAction>,
            #[case] expected: Option<String>,
        ) {
            assert_eq!(get_button_icon(action.as_ref()), expected);
        }
    }
}
