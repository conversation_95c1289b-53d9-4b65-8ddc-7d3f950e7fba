use super::common::link_card_to_common_metadata;
use crate::ui_signals::{DiscoveryAssistantGridItemModel, DiscoveryAssistantGridItemUIModel};
use common_transform_types::container_items::LinkCard;
use fableous::tiles::tile::TileTextOverlayPosition;
use ignx_compositron::prelude::{create_rw_signal, Scope};

pub fn discovery_assistant_grid_item(
    item: &LinkCard,
    scope: Scope,
    carousel_analytics: Option<String>,
    filtered_item_index: usize,
) -> Option<(DiscoveryAssistantGridItemModel, String)> {
    let (item_metadata, card_id) =
        link_card_to_common_metadata(item, carousel_analytics, true, None)?;

    let text = item.carouselCardMetadata.title.to_owned();
    let text_overlay_position =
        TileTextOverlayPosition::from_transform_type(item.overlayTextPosition.as_ref(), true);
    let image = item.imageUrl.to_owned();

    if image.is_none() && text.is_none() {
        return None;
    }

    Some((
        DiscoveryAssistantGridItemModel {
            id: card_id.clone(),
            metadata: create_rw_signal(scope, item_metadata),
            card_ui_data: create_rw_signal(
                scope,
                DiscoveryAssistantGridItemUIModel {
                    card_image: create_rw_signal(scope, image),
                    card_image_opacity: create_rw_signal(scope, 0.0),
                    card_text: create_rw_signal(scope, text),
                    card_text_opacity: create_rw_signal(scope, 0.7),
                    text_overlay_position: create_rw_signal(scope, text_overlay_position),
                },
            ),
            impression_item_index: filtered_item_index,
        },
        card_id,
    ))
}

#[cfg(test)]
mod tests {
    use super::super::common::test_helpers::{create_link_card, CardOptions, LinkCardOptions};
    use super::*;
    use crate::ui_signals::CommonCarouselCardMetadata;
    use clickstream::events::cards::CardClickedEvent;
    use common_transform_types::actions::TransitionAction;
    use cross_app_events::ImpressionData;
    use fableous::tiles::tile::TileTextOverlayPosition;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::prelude::SignalGetUntracked;
    use rstest::*;
    use std::collections::HashMap;

    #[rstest]
    #[case::action_not_transition_incorrect(LinkCardOptions::Other(CardOptions::ActionIncorrect))]
    #[case::action_not_transition_missing(LinkCardOptions::Other(CardOptions::ActionMissing))]
    #[case::no_id_constructable_missing(LinkCardOptions::Other(CardOptions::IdMissing))]
    #[case::no_id_constructable_transform_missing(LinkCardOptions::Other(
        CardOptions::TransformItemIdMissing
    ))]
    #[case::no_image_and_text(LinkCardOptions::ImageAndTextMissing)]
    fn returns_none_if(#[case] link_card_options: LinkCardOptions) {
        launch_only_scope(|scope| {
            let card = create_link_card(link_card_options);
            assert!(discovery_assistant_grid_item(&card, scope, None, 0).is_none())
        })
    }

    #[test]
    fn falls_back_text_overlay_position() {
        launch_only_scope(|scope| {
            let card = create_link_card(LinkCardOptions::NoTextOverlayPosition);
            let (item, _) = discovery_assistant_grid_item(&card, scope, None, 0).unwrap();
            assert_eq!(
                item.card_ui_data
                    .get_untracked()
                    .text_overlay_position
                    .get_untracked()
                    .unwrap(),
                TileTextOverlayPosition::BottomStart
            );
        })
    }

    #[test]
    fn falls_back_id_without_service_token() {
        launch_only_scope(|scope| {
            let card = create_link_card(LinkCardOptions::Other(CardOptions::Default));
            let (item, id) = discovery_assistant_grid_item(&card, scope, None, 0).unwrap();
            let metadata = item.metadata.get_untracked();
            let expected_impression_data = ImpressionData {
                benefit_id: Some("home".to_string()),
                content_id: Some("home:home".to_string()),
                content_type: Some("LINK".to_string()),
                creative_id: Some("card image".to_string()),
                widget_type: Some("imageTextLink".to_string()),
                ref_marker: Some("ref_marker".to_string()),
                analytics: Some(HashMap::new()),
                slot_id: None,
                size: None,
                carousel_analytics: None,
            };
            let expected_clickstream_data = CardClickedEvent {
                analytics: Some(HashMap::new()),
                ref_marker: Some("ref_marker".to_string()),
                clickstream_page_params: None,
            };
            let expected_metadata = CommonCarouselCardMetadata::from_id_and_action(
                "card id-no-service-token".to_string(),
                TransitionAction::create_landing_with_service_token(None),
            )
            .with_impression_data(expected_impression_data)
            .with_clickstream_data(expected_clickstream_data);
            assert_eq!(metadata, expected_metadata);
            assert_eq!(id, "card id-no-service-token");
        })
    }

    #[test]
    fn parses() {
        launch_only_scope(|scope| {
            let card =
                create_link_card(LinkCardOptions::Other(CardOptions::DefaultWithServiceToken));
            let (item, id) = discovery_assistant_grid_item(&card, scope, None, 0).unwrap();
            let ui_data = item.card_ui_data.get_untracked();
            assert_eq!(ui_data.card_text.get_untracked().unwrap(), "title");
            assert_eq!(ui_data.card_image.get_untracked().unwrap(), "card image");
            assert_eq!(ui_data.card_image_opacity.get_untracked(), 0.0);
            assert_eq!(
                ui_data.text_overlay_position.get_untracked().unwrap(),
                TileTextOverlayPosition::CenterCenter
            );
            let metadata = item.metadata.get_untracked();

            let expected_impression_data = ImpressionData {
                benefit_id: Some("home".to_string()),
                content_id: Some("home:home".to_string()),
                content_type: Some("LINK".to_string()),
                creative_id: Some("card image".to_string()),
                widget_type: Some("imageTextLink".to_string()),
                ref_marker: Some("ref_marker".to_string()),
                analytics: Some(HashMap::new()),
                slot_id: None,
                size: None,
                carousel_analytics: None,
            };

            let expected_clickstream_data = CardClickedEvent {
                analytics: Some(HashMap::new()),
                ref_marker: Some("ref_marker".to_string()),
                clickstream_page_params: None,
            };
            let transition_action = TransitionAction::create_landing_with_service_token(Some(
                "service token".to_string(),
            ));
            let expected_metadata = CommonCarouselCardMetadata::from_id_and_action(
                "card id-service token".to_string(),
                transition_action.clone(),
            )
            .with_impression_data(expected_impression_data)
            .with_clickstream_data(expected_clickstream_data);
            assert_eq!(metadata, expected_metadata);
            assert_eq!(id, "card id-service token");
            assert_eq!(item.impression_item_index, 0);
        })
    }
}
