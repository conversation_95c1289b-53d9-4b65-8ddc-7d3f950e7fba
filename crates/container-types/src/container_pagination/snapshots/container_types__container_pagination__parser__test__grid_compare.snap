---
source: crates/container-types/src/container_pagination/parser.rs
expression: response
---
{
  "metadata": {
    "request_id": "sJONh88QF88SM8x6NGcwFWTvicjActwDjG4ol-yjWbetKjoqQjLMjg=="
  },
  "resource": {
    "paginationLink": {
      "serviceToken": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
      "startIndex": 16,
      "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6UioRob21li4Rob21ljA-ND46CVjI=",
      "pageId": "home",
      "pageType": "home",
      "decorationScheme": null,
      "pageSize": null
    },
    "items": {
      "Grid": [
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1",
                "title": "The Wall",
                "synopsis": "A deadly psychological thriller that follows two soldiers pinned down by an Iraqi sniper, with nothing but a crumbling wall between them.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_1"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_1",
                      "pageId": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_1"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_1",
                        "pageId": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/7a516d8598487997dcfd25a4bb27b199c5e23fc7702421b3d01fbed28d5c488e.jpg",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/3475a9ef55360b9b66d17913182a1d0e7b4158166be577d31e5e86419720df39.jpg",
                "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/0d7d881898f6cd6816ab81834082c06bd281ae2538c5fca7809b271a037cda6b.png",
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png",
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/271ae0d0612c98a670a5f02f6d838ead65342746bcb017a68891802b11cd2c35.png",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ab33f2339de91b2377610ef21bebc3a6d284151baa05af804be0ba623a7f7f72.jpg",
                "totalReviewCount": 2110,
                "overallRating": 2.8,
                "gti": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1",
                "badges": {
                  "applyAudioDescription": true,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": true,
                  "applyPrime": true,
                  "applyUhd": true,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1494547200000,
                "runtimeSeconds": 5371,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Subscribe",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Watch with a Prime membership",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Drama",
                  "Action",
                  "Military and War"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 1182,
                      "width": 2000,
                      "scalarHorizontal": "Emphasis",
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7",
                "title": "AK-47 Kalashnikov",
                "synopsis": "The story of Mikhail Kalashnikov, inventor of the most infamous weapon on earth: the AK-47 rifle.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_2"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_2",
                      "pageId": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_2"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_2",
                        "pageId": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8578bdfe07eeacb5a2f4a9ffc67299dd97d4d5984b2608fafb620ef0c1fe6227.jpg",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/dadd1dc1cec71f3199e2edcca4b18e1ea26d868b651d15cfb06b9443da9e8875.jpg",
                "titleLogoImage": null,
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png",
                "poster2x3Image": null,
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ef089eb53235f636f50fd725af133f99f0b07fba2504be006cd3960ac493e2a9.jpg",
                "totalReviewCount": 3260,
                "overallRating": 4.4,
                "gti": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": true,
                  "applyUhd": false,
                  "regulatoryRating": "18+",
                  "showPSE": false
                },
                "publicReleaseDate": 1582156800000,
                "runtimeSeconds": 6281,
                "entitlementStatus": "ENTITLED_FREE_WITH_ADS",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Freevee (with ads)",
                    "icon": "ENTITLED_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Military and War",
                  "Action",
                  "Drama",
                  "Historical"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "18+",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 624,
                      "width": 2000,
                      "scalarHorizontal": null,
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb",
                "title": "Midway",
                "synopsis": "Experience the epic true story of the Battle of Midway, a pivotal clash where American forces' bravery and fortitude changed WWII's course.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_3"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_3",
                      "pageId": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_3"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_3",
                        "pageId": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/4bba32fb80f87e55b26c3e84dc4803dad7eca66451082ad39f930ca3e69b6ed5.jpg",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/348dc911031d54b16c39abe3c2bd7ff323defd9dd72f3bfcdcc2ba00b1ee3e82.jpg",
                "titleLogoImage": null,
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png",
                "poster2x3Image": null,
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7164ff056c593fd03a67f5a5b5ac180bf369c8faf6d59054c20f6336bb9812b6.jpg",
                "totalReviewCount": 12223,
                "overallRating": 4.7,
                "gti": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": true,
                  "applyPrime": true,
                  "applyUhd": true,
                  "regulatoryRating": "PG-13",
                  "showPSE": false
                },
                "publicReleaseDate": 1573171200000,
                "runtimeSeconds": 8303,
                "entitlementStatus": "ENTITLED_FREE_WITH_ADS",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Freevee (with ads)",
                    "icon": "ENTITLED_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Drama",
                  "Military and War",
                  "Historical"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "PG-13",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 624,
                      "width": 2000,
                      "scalarHorizontal": null,
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0",
                "title": "Valkyrie",
                "synopsis": "Based on the true story of Colonel Claus von Stauffenberg (Tom Cruise) and his assassination plot targeting Adolph Hitler, this engrossing thriller reenacts the daring operation to elimin...",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_4"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_4",
                      "pageId": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_4"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_4",
                        "pageId": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/6354f96de74348ce48b44505b46184e716cd9d082e7a54327d3142fad6c78164.jpg",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2d3fec41ccc02e5026ddd6835de913a92677e1dc20746902c377080b2b923216.jpg",
                "titleLogoImage": null,
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png",
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/5ee312827aca2eb2391cc592f0e695c04dd214bd78d28d3ade8df67cb88a6b3f.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9badf1c122096d5eea6cac3d41ddedea4599ba711469bdd334b221bd42c3785e.jpg",
                "totalReviewCount": 4496,
                "overallRating": 4.6,
                "gti": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": true,
                  "applyUhd": false,
                  "regulatoryRating": "PG-13",
                  "showPSE": false
                },
                "publicReleaseDate": 1230163200000,
                "runtimeSeconds": 6986,
                "entitlementStatus": "ENTITLED_FREE_WITH_ADS",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Freevee (with ads)",
                    "icon": "ENTITLED_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "PG-13",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 624,
                      "width": 2000,
                      "scalarHorizontal": null,
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf",
                "title": "USS Indianapolis: Men of Courage",
                "synopsis": "Starring Nicolas Cage, Tom Sizemore, and Thomas Jane, this WWII drama follows the true story of American heroes shipwrecked in shark-filled waters.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_5"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_5",
                      "pageId": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_5"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_5",
                        "pageId": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/a35df06c147b11df6988fdf4e86cf989f9c4fb61a26fd7f4c7c6dff824138614.jpg",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ada5ea011f556765d40a92c913e3ce5031c47e7b1e55356d703ce190cfc3db52.jpg",
                "titleLogoImage": null,
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png",
                "poster2x3Image": null,
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e4e714ba96b30c79562de06702b37e1d6a3a2e5d41a4a20512fdf145f498cf87.jpg",
                "totalReviewCount": 1711,
                "overallRating": 4.5,
                "gti": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": true,
                  "applyUhd": false,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1476403200000,
                "runtimeSeconds": 7828,
                "entitlementStatus": "ENTITLED_FREE_WITH_ADS",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Freevee (with ads)",
                    "icon": "ENTITLED_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Military and War",
                  "Drama",
                  "Action",
                  "Historical",
                  "Suspense"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 624,
                      "width": 2000,
                      "scalarHorizontal": null,
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7",
                "title": "1944",
                "synopsis": "In 1944, Estonia grapples with invading Russian and Nazi forces, families torn apart, soldiers making difficult choices.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_6"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_6",
                      "pageId": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_6"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_6",
                        "pageId": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/155d3d49575b5d9898ca73526e3e2bc4790c7ebadca2cb4a803e6b1f0e6e3700.png",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/618c3972e0d1a82fc8eba6fff9b34d90f40d6ff9fdbadb83af590d5b0cba0249.jpg",
                "titleLogoImage": null,
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png",
                "poster2x3Image": null,
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/71da908c05935cfbefc51bbe5962ed319d13bf261e470a5f01064e755b6cc059.png",
                "totalReviewCount": 1348,
                "overallRating": 4.3,
                "gti": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": false,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": true,
                  "applyUhd": false,
                  "regulatoryRating": "13+",
                  "showPSE": false
                },
                "publicReleaseDate": 1446595200000,
                "runtimeSeconds": 6007,
                "entitlementStatus": "ENTITLED_FREE_WITH_ADS",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Freevee (with ads)",
                    "icon": "ENTITLED_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Drama",
                  "Action",
                  "Military and War",
                  "Historical"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "13+",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 624,
                      "width": 2000,
                      "scalarHorizontal": null,
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad",
                "title": "Sniper: The White Raven",
                "synopsis": "After suffering a senseless tragedy, a Ukrainian man seeks revenge.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_7"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_7",
                      "pageId": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_7"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_7",
                        "pageId": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/aa86047692759221915c3b9a03c7eb4280a2e047b365a9f0253d9b2f09a97c04.jpg",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2d695d81ae663afa2ef93832c5605edbf26c1060781365731ce250f59616d2d4.jpg",
                "titleLogoImage": null,
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/paramountpremium/logos/channels-logo-white._CB583166680_.png",
                "poster2x3Image": null,
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/88ddd79277e9f1d12981f3114096da703d1e3e0e91b1981941d90958841f682e.jpg",
                "totalReviewCount": 1971,
                "overallRating": 4.3,
                "gti": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": false,
                  "applyUhd": false,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1641013200000,
                "runtimeSeconds": 6694,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Free trial or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Free trial of Paramount+, rent, or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Drama",
                  "Military and War"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 461,
                      "width": 1999,
                      "scalarHorizontal": "Default",
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "SHOW": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.a8a9f765-858c-b499-2ec4-25f68b0bd8c7",
                "title": "Vietnam in HD",
                "synopsis": "Two years after the release of its landmark Emmy-winning series WWII in HD, HISTORY shifts its focus to a new generation and one of the most controversial chapters in American history, the Vietnam War.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "pillId": "2044",
                        "pillVersion": "demo-20241101_v2",
                        "refMarker": "hm_ass_c_sr63757f_dast_1_8"
                      },
                      "refMarker": "hm_ass_c_sr63757f_dast_1_8",
                      "pageId": "amzn1.dv.gti.e4a9f737-161f-0574-6234-e2459ecd9ed7",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": null
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "pillId": "2044",
                          "pillVersion": "demo-20241101_v2",
                          "refMarker": "hm_ass_c_sr63757f_dast_1_8"
                        },
                        "refMarker": "hm_ass_c_sr63757f_dast_1_8",
                        "pageId": "amzn1.dv.gti.e4a9f737-161f-0574-6234-e2459ecd9ed7",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": null
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/2e81efe132d945e887c88195d2dc61d0605f4e53bf9c66c289b80d358bbce5d6.jpg",
                "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/a29d35ed4a333dfd6324a100e038545cbbb562c9ca0fe914257cba2d7257fc64.jpg",
                "titleLogoImage": null,
                "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png",
                "poster2x3Image": null,
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/11d164e4c26b1d776aa83806ef5532297c0bf7dd91840458391fca5d8758ed52.jpg",
                "totalReviewCount": 657,
                "overallRating": 4.7,
                "gti": "amzn1.dv.gti.a8a9f765-858c-b499-2ec4-25f68b0bd8c7",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": false,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": false,
                  "applyUhd": false,
                  "regulatoryRating": "NR",
                  "showPSE": false
                },
                "publicReleaseDate": 1320537600000,
                "runtimeSeconds": null,
                "entitlementStatus": "ENTITLED_FREE_WITH_ADS",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Freevee (with ads)",
                    "icon": "ENTITLED_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "SHOW",
                "genres": [
                  "Documentary",
                  "Historical",
                  "Military and War"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "NR",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": {
                  "isAdult": false,
                  "isRestricted": false,
                  "individualImageMetadata": {
                    "providerLogoImage": {
                      "height": 624,
                      "width": 2000,
                      "scalarHorizontal": null,
                      "scalarStacked": null,
                      "safeToOverlay": null
                    }
                  }
                },
                "contextualActions": []
              },
              "numberOfSeasons": 1,
              "showName": null
            }
          }
        }
      ]
    }
  }
}
