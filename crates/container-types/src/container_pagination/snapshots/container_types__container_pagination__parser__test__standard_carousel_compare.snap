---
source: crates/container-types/src/container_pagination/parser.rs
expression: response
---
{
  "metadata": {
    "request_id": "afS0_4fay7Jiz6q6dNJl134C-jG4uFImfW4cPK5RYUkOZBIfE5rfgA=="
  },
  "resource": {
    "paginationLink": {
      "serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiZWE3MDBhZmUtOTExNS00YWUzLTk1MjctOTliOWU3NDYzYjA4IiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoyMCwib3JlcSI6ImFmUzBfNGZheTdKaXo2cTZkTkpsMTM0Qy1qRzR1RkltZlc0Y1BLNVJZVWtPWkJJZkU1cmZnQT09OjE3MTQ0MTg3MDEwMDAiLCJhcE1heCI6NDk1LCJzdHJpZCI6IjI6T0JFNzEzQ0M1RjgxRjEjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUGE1ZDBkNjY4OGE2ZjQ2MzEzMTQ2Y2M0ODhhYTY3YmNjNWEwYzRhYWY0NGYwOTE4OGJkNDBkZWYzZWNkMzAwZjNcIn0iLCJzdEtleSI6IntcInNic2luXCI6MCxcImN1cnNpemVcIjo0OTUsXCJwcmVzaXplXCI6MH0iLCJvcmVxayI6ImxjUHFPdmdOMHhzMU04aUZRVVhYclFINWRtYjZyQzNGRStqSUN1TWs5T009Iiwib3JlcWt2IjoxLCJleGNsVCI6W119",
      "startIndex": 20,
      "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6lioRob21li4Rob21ljI6QMjpPQkU3MTNDQzVGODFGMY0PjoJWMg==",
      "pageId": "home",
      "pageType": "home",
      "decorationScheme": null,
      "pageSize": null
    },
    "items": {
      "StandardCarousel": [
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747",
                "title": "The Book Of Clarence",
                "synopsis": "<PERSON> wants to show he's not a nobody, gets into trouble, but at last finds redemption and faith.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_11"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_11",
                      "pageId": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_11"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_11",
                        "pageId": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c81c0b20b87918f3f074efce745a64976cdf0104cf8fe2e5cbdd1dac35e52b8c._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/0c6a521d661868f963ec83216a8aab1c7bba582aac00efee5277a344ee57e5c2.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c5aea2ec4d42cf2ef865cbfa08f77660ba6aa81b9335826c8e5c1cf8ce3e1f68.jpg",
                "totalReviewCount": 743,
                "overallRating": 4.3,
                "gti": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": true,
                  "applyPrime": false,
                  "applyUhd": true,
                  "regulatoryRating": "PG-13",
                  "showPSE": false
                },
                "publicReleaseDate": 1705017600000,
                "runtimeSeconds": 7820,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Rent or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Available to rent or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Adventure",
                  "Comedy",
                  "Drama",
                  "Historical",
                  "Action"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "PG-13",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b",
                "title": "Sisu",
                "synopsis": "During the last days of World War II, a solitary prospector crosses paths with Nazis on a scorched-Earth retreat in northern Finland. When the soldiers decide to steal his gold, they quickly discover they just tangled with no ordinary miner.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_12"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_12",
                      "pageId": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_12"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_12",
                        "pageId": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/b3d7e03093c0714c52147bb0892ced90eec50165f7ae84cbdf59320853af99ad._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/4422a624d6ab07e49aff433b753f6a1f20db5bb113d709f2dee074b4994b90a3.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c7558cdf25bdbd2b274669051379f55f220f39df2ba18dd5cf6c252a972a317e.jpg",
                "totalReviewCount": 13699,
                "overallRating": 4.5,
                "gti": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": true,
                  "applyPrime": false,
                  "applyUhd": true,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1682640000000,
                "runtimeSeconds": 5248,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Free trial, rent, or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Free trial of STARZ, rent, or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Military and War"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe",
                "title": "Cash Out",
                "synopsis": "John Travolta is criminal mastermind Mason in this tense heist movie. Thrust into his brother's botched bank robbery, Mason stumbles into his biggest score…but can he escape alive?",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_13"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_13",
                      "pageId": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_13"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_13",
                        "pageId": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c53b7c0b4ba10099c8f9a63a1832d2cd09859c4806aa590764d8402620c5e021._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/e0e40e21bb9e936cde8c4e50dc47c298fdcc63802783acf43ef288418475f7b9.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/368c070aca27395416293b2f05725656c6d66b66b2271a16800bbd968bf8421a.jpg",
                "totalReviewCount": null,
                "overallRating": null,
                "gti": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": false,
                  "applyUhd": true,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1714089600000,
                "runtimeSeconds": 5422,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Rent or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Available to rent or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e",
                "title": "Muzzle",
                "synopsis": "LAPD officer Jake Rosser (Aaron Eckhart) and his partner Socks, a violent K-9 with titanium incisors and a mysterious past, aim to uncover a vast conspiracy that has a chokehold on the city.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_14"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_14",
                      "pageId": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_14"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_14",
                        "pageId": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8190d658f63289a4bc23cc054cff29cfc1b637c9eb10dc3225635158e7ea67f1._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/bf3052915401fcf8b0533bb6335894ba3b56bc1573adb68088634470d118723c.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/1637a3d27d06e8c3d5b7f6996ced2a18e2f4c415a19cbaad48e2f6bf00b1c06b.jpg",
                "totalReviewCount": 2623,
                "overallRating": 4.3,
                "gti": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": false,
                  "applyUhd": true,
                  "regulatoryRating": "16+",
                  "showPSE": false
                },
                "publicReleaseDate": 1695945600000,
                "runtimeSeconds": 5994,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Free trial, rent, or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Free trial of AMC+, rent, or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Suspense",
                  "Drama"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "16+",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125",
                "title": "Soul Mates",
                "synopsis": "The two must find their way out of this sick maze but with every twist, turn and setback curated by the Matchmaker, they may never escape.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_15"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_15",
                      "pageId": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_15"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_15",
                        "pageId": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/7c73223b6e54d6ac0d2f60364ba75fd5045f3e68b796925749335ac8fec09d14._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": null,
                "heroImage": null,
                "totalReviewCount": 21,
                "overallRating": 3.8,
                "gti": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": false,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": false,
                  "applyUhd": false,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1710979200000,
                "runtimeSeconds": 5246,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Free trial, rent, or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Free trial of BET+, rent, or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Horror",
                  "Suspense"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f",
                "title": "Retribution",
                "synopsis": "In this high-octane ride of redemption and revenge, a father must play a deadly game when a mysterious caller threatens his family with a bomb beneath their car seats.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_16"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_16",
                      "pageId": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_16"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_16",
                        "pageId": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/642deea587e0b2012aa638243011bca8f25ad9225f5a07c73e6ae3ea77b3b39e._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/a86428dd0caf94e78d12b76be8c00da636873cb4dd7b542d2b1247b14f06b69b.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cf791b0d830732571b9597ecfc30db4ee1991ab88d4b181bdaa32dc30f552e80.jpg",
                "totalReviewCount": 4541,
                "overallRating": 4.4,
                "gti": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": true,
                  "applyPrime": false,
                  "applyUhd": true,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1692921600000,
                "runtimeSeconds": 5249,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Free trial, rent, or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Free trial of STARZ, rent, or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Suspense"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b",
                "title": "The Equalizer 3 - Bonus X-Ray Edition",
                "synopsis": "Ex-assassin Robert McCall confronts his past as he battles the Italian mafia.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_17"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_17",
                      "pageId": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_17"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_17",
                        "pageId": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/bb41592bbb836f9157a8355de11b3da54a05758ffc32943e5f6454ca75efca5b._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/3a1e47133e96b8c24074e92ac01ed7f7965cb229f39c1fb40b50c932caa42cad.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8d00fc467ad27b13122df0dba2679e3f50717eac0b7386a1c25afe515246b606.jpg",
                "totalReviewCount": 88767,
                "overallRating": 4.7,
                "gti": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": true,
                  "applyPrime": false,
                  "applyUhd": true,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1693526400000,
                "runtimeSeconds": 6533,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Rent or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Available to rent or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Suspense"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6",
                "title": "Dune",
                "synopsis": "The son of a noble family travels to a dangerous planet to ensure the future of his people in this visually stunning sci-fi epic.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_18"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_18",
                      "pageId": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_18"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_18",
                        "pageId": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/2703313c37a993af7a5971f68f79cd665e04643b3560c92736eeaab2bb235eb2._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/fc5bf647573bf2c34813496a7cb9cf2db978e0b7d4d3dd3ae5befe3e94672c0c.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3e6707e7bdc406ff0e1b61ec3d1cb307a1bdc257472a5032f8e6d2a77ff97383.jpg",
                "totalReviewCount": 45202,
                "overallRating": 4.5,
                "gti": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6",
                "badges": {
                  "applyAudioDescription": true,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": true,
                  "applyPrime": false,
                  "applyUhd": true,
                  "regulatoryRating": "PG-13",
                  "showPSE": false
                },
                "publicReleaseDate": 1634860800000,
                "runtimeSeconds": 9323,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Subscribe, rent, or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Subscribe to Max, rent, or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "GOLDEN GLOBE® winner",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Adventure",
                  "Drama",
                  "Science Fiction"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "PG-13",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc",
                "title": "Cloud Atlas",
                "synopsis": "Actors (Tom Hanks, Halle Berry, Jim Broadbent) take on multiple roles in an epic that spans five centuries. An attorney harbors a fleeing slave on a voyage from the Pacific Islands in 1849; a poor composer in pre-World War II Britain struggles to finish his magnum opus before a past act catches up with him; a genetically engineered worker in 2144 feels the forbidden stirring of human consciousn...",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_19"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_19",
                      "pageId": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_19"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_19",
                        "pageId": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d2f85abd1c5056885db929c3836676f3c0df77fb2d7798effcc5aadc7d7a7bff._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/29fb7d4abdfb3a54ab838e0a64888818f0b64918bb02c2d5b0055d3e58f9e9bd.jpg",
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/59b2847735d8f3b6aea8c51b5f666e234daac66fa03c86b25f0c0ada93378933.jpg",
                "totalReviewCount": 7824,
                "overallRating": 4.3,
                "gti": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc",
                "badges": {
                  "applyAudioDescription": true,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": false,
                  "applyUhd": false,
                  "regulatoryRating": "R",
                  "showPSE": false
                },
                "publicReleaseDate": 1351209600000,
                "runtimeSeconds": 10313,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Free trial, rent, or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Free trial of Paramount+, rent, or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "GOLDEN GLOBE® nominee",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Adventure",
                  "Science Fiction",
                  "Drama",
                  "Action",
                  "Suspense"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "R",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        },
        {
          "TITLE_CARD": {
            "MOVIE": {
              "carouselCardMetadata": {
                "transformItemId": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546",
                "title": "The Shift",
                "synopsis": "Kevin (Kristoffer Polaha) travels across worlds to reunite with the love of his life, Molly (Elizabeth Tabish). When The Benefactor (Neal McDonough) threatens Kevin's survival, he fights to return to the world he knows and the woman he loves.",
                "action": {
                  "TransitionAction": {
                    "legacyDetail": {
                      "text": null,
                      "analytics": {
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_20"
                      },
                      "refMarker": "hm_hom_c_OB8d21f2_brws_1_20",
                      "pageId": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546",
                      "pageType": "detail",
                      "serviceToken": null,
                      "journeyIngressContext": "8|EgR0dm9k"
                    }
                  }
                },
                "deferredAction": null,
                "actions": [
                  {
                    "TransitionAction": {
                      "legacyDetail": {
                        "text": null,
                        "analytics": {
                          "refMarker": "hm_hom_c_OB8d21f2_brws_1_20"
                        },
                        "refMarker": "hm_hom_c_OB8d21f2_brws_1_20",
                        "pageId": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546",
                        "pageType": "detail",
                        "serviceToken": null,
                        "journeyIngressContext": "8|EgR0dm9k"
                      }
                    }
                  }
                ],
                "widgetType": "titleCard"
              },
              "titleCardBaseMetadata": {
                "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/85cb4c6d41f7153516414b2a672c59b088b49b8cdf69acaaf591d51128689e77._UR1920,1080_RI_.jpg",
                "boxartImage": null,
                "titleLogoImage": null,
                "providerLogoImage": null,
                "poster2x3Image": null,
                "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/0b3f51fa4094e2096dbbb4843609f18cb100a38d7fcc90863873310741e38692.jpg",
                "totalReviewCount": 324,
                "overallRating": 4.3,
                "gti": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546",
                "badges": {
                  "applyAudioDescription": false,
                  "applyCC": true,
                  "applyDolby": true,
                  "applyDolbyVision": false,
                  "applyDolbyAtmos": false,
                  "applyHdr10": false,
                  "applyPrime": false,
                  "applyUhd": false,
                  "regulatoryRating": "PG-13",
                  "showPSE": false
                },
                "publicReleaseDate": 1701388800000,
                "runtimeSeconds": 6781,
                "entitlementStatus": "UNENTITLED",
                "entitlementMessaging": {
                  "GLANCE_MESSAGE_SLOT": {
                    "message": "Rent or buy",
                    "icon": "OFFER_ICON",
                    "level": null,
                    "glance_type": null
                  },
                  "ENTITLEMENT_MESSAGE_SLOT": {
                    "message": "Available to rent or buy",
                    "icon": "OFFER_ICON"
                  },
                  "HIGH_VALUE_MESSAGE_SLOT": null,
                  "HIGH_VALUE_MESSAGE_SLOT_LITE": {
                    "message": "",
                    "icon": null,
                    "level": null,
                    "hvm_type": null
                  },
                  "TITLE_METADATA_BADGE_SLOT": {
                    "message": "",
                    "level": null
                  },
                  "INFORMATIONAL_MESSAGE_SLOT": null,
                  "BUYBOX_MESSAGE_SLOT": null,
                  "PRODUCT_SUMMARY_SLOT": null,
                  "PRODUCT_PROMOTION_SLOT": null
                },
                "contentType": "MOVIE",
                "genres": [
                  "Action",
                  "Science Fiction"
                ],
                "watchProgress": null,
                "imageAlternateText": null,
                "isEntitled": null,
                "offerText": null,
                "isInWatchlist": false,
                "maturityRatingString": "PG-13",
                "maturityRatingImage": null,
                "regulatoryLabel": null,
                "imageAttributes": null,
                "contextualActions": []
              },
              "linearAttributes": null
            }
          }
        }
      ]
    }
  }
}
