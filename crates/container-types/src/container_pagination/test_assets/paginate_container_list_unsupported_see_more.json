{"resource": {"containerList": [{"title": "Amazon Originals and Exclusives", "actions": [{"serviceToken": "eyJ0eXBlIjoicXVlcnkiLCJuYXYiOnRydWUsInBpIjoiZGVmYXVsdCIsInNlYyI6ImNlbnRlciIsInN0eXBlIjoic2VhcmNoIiwicXJ5IjoiZmllbGQtd2F5c190b193YXRjaD03NDQ4Njk1MDMxJmFkdWx0LXByb2R1Y3Q9MCZicT0oYW5kIChhbmQgKG5vdCBnZW5yZTonYXZfZ2VucmVfa2lkcycpIChub3QgZ2VucmU6J2F2X2dlbnJlX2FuaW1lJykpIChub3QgZW50aXR5X3R5cGU6J1Byb21vdGlvbnxUcmFpbGVyfEJvbnVzIENvbnRlbnQnKSkmZmllbGQtYXZfdGVycml0b3J5X2V4Y2x1c2l2ZT1ERTpmaXJzdHJ1bnxERTpvcmlnaW5hbCZmaWVsZC12aWRlb19xdWFsaXR5PVNEJmZpZWxkLWxhbmd1YWdlPURldXRzY2gmc2VhcmNoLWFsaWFzPWluc3RhbnQtdmlkZW8mcXMtYXZfcmVxdWVzdF90eXBlPTQmcXMtaXMtcHJpbWUtY3VzdG9tZXI9MiZwdl9icm93c2VfaW50ZXJuYWxfb2ZmZXI9c3ZvZCZwdl9icm93c2VfaW50ZXJuYWxfbGFuZ3VhZ2U9YWxsIiwicnQiOiJpeE81SHJzbXIiLCJ0eHQiOiJBbWF6b24gT3JpZ2luYWxzIGFuZCBFeGNsdXNpdmVzIiwib2Zmc2V0IjowLCJucHNpIjowLCJvcmVxIjoic0pPTmg4OFFGODhTTTh4Nk5HY3dGV1R2aWNqQWN0d0RqRzRvbC15aldiZXRLam9xUWpMTWpnPT06MTczNDcyMTI4NjAwMCIsInN0cmlkIjoiMToxNzRERzlVTkUyQjZXIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE0iLCJvcmVxayI6InJEUlUwcWhla0lqQWNiUk1pZFd6amc4T2kyNnNoWGtHQ01rY2xjY25EZlE9Iiwib3JlcWt2IjoxfQ==", "analytics": {"refMarker": "hm_hom_c_ixo5hr_7_smr", "ClientSideMetrics": "472|CmYKOURFUHJpbWVPcmlnaW5hbHNhbmRFeGNsdXNpdmVzQ29udmVyc2lvbkxpdmVEZWZhdWx0RGVmYXVsdBIPMToxNzRERzlVTkUyQjZXGhAyOkRZMjhDNkYyRDEyODA4IgZpeE81SHISPAoEaG9tZRIEaG9tZSIGY2VudGVyKgAyJDQ0ODY1NmYxLTAyZDktNDRhYS1hMGQxLTMyZGVjNjQ5MjEzMBoEc3ZvZCIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQhZPcmlnaW5hbHNBbmRFeGNsdXNpdmVzSghoZXJjdWxlc0oLaXNPcmlnaW5hbHNSCGVudGl0bGVkWgBiDVN1cGVyQ2Fyb3VzZWxoB3IAejhzSk9OaDg4UUY4OFNNOHg2Tkdjd0ZXVHZpY2pBY3R3RGpHNG9sLXlqV2JldEtqb3FRakxNamc9PYIBBHRydWWKAQCSAQA="}, "pageType": "browse", "pageId": "default", "refMarker": "hm_hom_c_ixo5hr_7_smr", "target": "browse", "text": "See more"}], "facet": {}, "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjotMTksIm5wc2kiOjEwLCJvcmVxIjoic0pPTmg4OFFGODhTTTh4Nk5HY3dGV1R2aWNqQWN0d0RqRzRvbC15aldiZXRLam9xUWpMTWpnPT06MTczNDcyMTI4NjAwMCIsImFwTWF4IjoxMDAsInByZXZJdGVtcyI6WyI5NjcyIiwiNzIzOSIsImI0MmUiLCJiYzliIiwiM2NjMyIsImEwMTMiLCIzMzQ3IiwiYmNiNSIsIjZlZTciLCIwYjE2Il0sInN0cmlkIjoiMToxNzRERzlVTkUyQjZXIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE0iLCJhdXRvYm90Ijoie1wicnNpcFN0YXRlSWRcIjpcIlJTSVAyODM3NzM1MjNkMWQwMDVlODU2NzQyMTYzNzI1MmJiMThlNzE0NTE1OTc0ODM1NGMwMzJjNDkwNjc5OWM0NWQ0XCJ9Iiwic3RLZXkiOiJ7XCJzYnNpblwiOjAsXCJjdXJzaXplXCI6Njk4LFwicHJlc2l6ZVwiOjB9Iiwib3JlcWsiOiJyRFJVMHFoZWtJakFjYlJNaWRXempnOE9pMjZzaFhrR0NNa2NsY2NuRGZRPSIsIm9yZXFrdiI6MSwiZXhjbFQiOlsiYW16bjEuZHYuZ3RpLmVmODdiNTNlLTY1OTUtNGZjNC1iOTQ5LTM3ODlhOGEzOTY3MiIsImFtem4xLmR2Lmd0aS5kMmZjYWZmYi0wNzQwLTQzMGYtOGFiYi1jYmRiMDAzNDcyMzkiLCJhbXpuMS5kdi5ndGkuM2RiOTFmNmItMzNjYy00NGViLWE2NWQtYjUzYjU1YTViNDJlIiwiYW16bjEuZHYuZ3RpLmExYzZmOTc2LWNkMDEtNGE1Zi1hMzFlLTVlNGE2OTQ4YmM5YiIsImFtem4xLmR2Lmd0aS41ZjViMTVjZS1mODU1LTQ0YzItOWUwNC1jMDcxNWM2YzNjYzMiLCJhbXpuMS5kdi5ndGkuZmZiMzQ0ZGItZTdlOC00NjIwLWJmNmUtYzA2NGVkMGNhMDEzIiwiYW16bjEuZHYuZ3RpLmU2MjFhZTFjLWQwNWMtNDY0NS05OTUyLTNmNzkyMjRmMzM0NyIsImFtem4xLmR2Lmd0aS43YjljZTAwYy1mMmFkLTRkOTAtYjkzNi1hYWZkZjlmOWJjYjUiLCJhbXpuMS5kdi5ndGkuZjdkNzNhMjktYWY3Mi00MjAyLTk5ZjItYWNhNDhhNWE2ZWU3IiwiYW16bjEuZHYuZ3RpLjZkNmQxYzI3LWIxNDQtNDg4ZC04NTdlLWQ3ZTVlOTJlMGIxNiIsImFtem4xLmR2Lmd0aS44ZTBlZGQ5MS02MWYwLTQ4YjAtYmVlZC1iNGYyMmEzNDBiNWQiLCJhbXpuMS5kdi5ndGkuYzQ1MDZjYzAtNGYxMS00M2JiLTkyYTUtMDlhNGNjZmE5MDUzIiwiYW16bjEuZHYuZ3RpLjEwMjA0ZThjLWU4ZGUtNDdmNS05ZmEyLWMwNjhiOGJlNTk3YSIsImFtem4xLmR2Lmd0aS5jYjVkZjBhMy01YTk4LTQ3M2QtYjIwYS04M2ZjMmQ4OGRhZGQiLCJhbXpuMS5kdi5ndGkuOGViMDM5YWUtOWYyNi00YzFlLTgzNzItODMwYTNjYjdjMTlkIl19", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxNzRERzlVTkUyQjZXIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "items": [{"title": "Beast Games - Season 1", "gti": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "transformItemId": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "synopsis": "In this high-stakes competition series, 1,000 participants battle for the largest TV prize purse ever: $5 million, plus extraordinary bonus prizes.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ce689202927ce880eec6ca93e6462c69c5568d7aff03ec27d4402de05aa3e619.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/229f1846461068621910c94d319a85bfdb1e409a05cf1dae3f67fa8b6588e281.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/dba04e61f844b918594d6884cb4575bb9bb11426908d391c446aa3d21a6983fa.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/d2f0589bec1d7e54f6bbde9987a38e740bebe2fd07ead21a7757788aac3395e2.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/95845da262e4d43f977c705ad5ef9b7f6c65ae43ade9fd953b80210cbc21a094.png", "publicReleaseDate": 1734566400000, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_1", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_1", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "New episode Thursday"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_1", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_1", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Beast Games", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Red One", "gti": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "transformItemId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "synopsis": "After <PERSON> aka RED ONE â is kidnapped, the North Pole's Head of Security teams up with an infamous bounty hunter to save <PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Action", "Adventure"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4cf72f249c3520bf05438824a039bfceaab19d21b533d20c2165829384561f1d.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/115800d972e291ce343fea1f88af9bc27fdb2c437d82608ce183b86c66b70de2.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/cd805cfff16808f6deef99c583bd46da4b035a9e5ff3907bd651ac255235d4f1.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/a2a9006c929d5f3c1d29104fb61dfa767a975f4a779a39a38bd1780f1749f920.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f96f371b2d8b70a1e1c1f44f0ddc0aa07f83fa7bf7e61e5f671d3fb74be00e35.jpg", "publicReleaseDate": 1733961600000, "runtimeSeconds": 7475, "runtime": "124 min", "overallRating": 4.0, "totalReviewCount": 218, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_2", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_2", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#1 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_2", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_2", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Secret Level - Season 1", "gti": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "transformItemId": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "synopsis": "SECRET LEVEL is a new animated anthology series featuring original stories set within beloved video game worlds.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Adventure", "Animation"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f39a5161a21e12347afb4ddfbbc0f109486c79b9b659ffea83921e130828c042.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ee9810d6ea0357473605272ace805415a3fa8d935f4132e52b4d0f9ad0a5604c.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/025cdb93817b5e3cfee3106dec7db006a4733a4729618fd8c40c40d36ce1d106.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/93a1599ba87c6345fa7bdbcabe4686a305738c8a7fa7c5036d7a9b9eb3c577b2.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6fe4c1c1aab1bf6d6f47b697a6aa3e9dfecb593deb9ff6a0477f0f8dd3aa168b.png", "publicReleaseDate": 1734393600000, "overallRating": 4.5, "totalReviewCount": 228, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_3", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_3", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#2 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_3", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_3", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Secret Level", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Die Discounter - Staffel 4", "gti": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "transformItemId": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "synopsis": "DIE 4 FINALEN FOLGEN AB 23.12.24!\n\t\nEs herrscht ein neuer Wind im Kolinski Billstedt: Zum ersten Mal macht Thorsten zum Stolz von Pina einen richtig guten Job als Filialleiter. Auch bei der Belegschaft lÃ¤uft's: <PERSON> en<PERSON>t den KlimaschÃ¼tzer in sich und <PERSON> wird zum <PERSON>. In all dem Chaos struggelt Titus damit, Lia aus dem fernen Kolinski Eppendorf weiterhin fÃ¼r sich zu gewinnen.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Comedy"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cd4968d32a69f1668ea969d3e1c432246d629d99ca51884202e3eadb4b9d06ce.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bd164e57c186b3f661de319777cf77f0adbc864d1cae2dee6ffa2a4b626db971.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/3845a58e253b346774c662dddf9a335c5a77296029141eb46bdb1a54b8abbff3.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7807a621d03743920f5e79637246211af9e3ed3e6f0dddcd886a94072033d507.jpg", "publicReleaseDate": 1734912000000, "overallRating": 3.4, "totalReviewCount": 197, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_4", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_4", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#4 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_4", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_4", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Die Discounter", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Renfield", "gti": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "transformItemId": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "synopsis": "Das BÃ¶se Ã¼berdauert die Ewigkeit nicht ohne ein bisschen Hilfe: In dieser modernen Monstergeschichte Ã¼ber Draculas treuen Diener schlÃ¼pft <PERSON> in die Rolle von <PERSON>, dem gepeinigten Handlanger des grÃ¶Ãten Narzissten unter den Vampiren -- Dracula.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Comedy", "Fantasy", "Horror"], "starringCast": ["<PERSON>", "<PERSON>", "Awkwa<PERSON><PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f362998a4c5b0b423a4dfcf7def673c405d7db72c5fda90fc7ac1f167cee8f60.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/cd84bb530b8ee860a2f3129dc8595c4190db85f631c0887714a0bfa36219ed63.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ca619e6440094badeaef558ef7b27cc376752ee388d40e180f6c59aee3732be0.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c92b4129d177a59ecd0772f89a89ab280fab69c76a03ecc643fae00ac949cdc0.jpg", "publicReleaseDate": 1684972800000, "runtimeSeconds": 5630, "runtime": "93 min", "overallRating": 4.0, "totalReviewCount": 7691, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_5", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_5", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#8 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_5", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_5", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "The Whale", "gti": "amzn1.dv.gti.ffb344db-e7e8-4620-bf6e-c064ed0ca013", "transformItemId": "amzn1.dv.gti.ffb344db-e7e8-4620-bf6e-c064ed0ca013", "synopsis": "The Whale \t\n\nFrom <PERSON> comes The Whale, the story of a reclusive English teacher who attempts to reconnect with his estranged teenage daughter. Starring <PERSON> and based on the acclaimed play by <PERSON>.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/335c668f7e0899f1d8568950e9c44a9a9c43f9329cfde0fa776009a467aa7c8b.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ec205e796b8ba606763f0ef2b6a0998751b9551904c76b24cee0d1173a75381f.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/5ac45c7343b28c12f06a41f75113876fa840dba80aed43a66c523739366ee62e.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/fca3638d5a9fc75c8d2a01742c52359e984c23f57bd36717adbcc5627fcaa936.jpg", "publicReleaseDate": 1682553600000, "runtimeSeconds": 7014, "runtime": "116 min", "overallRating": 4.1, "totalReviewCount": 5621, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.ffb344db-e7e8-4620-bf6e-c064ed0ca013", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_6", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_6", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARSÂ® 2X winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.ffb344db-e7e8-4620-bf6e-c064ed0ca013", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_6", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_6", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Back to Black", "gti": "amzn1.dv.gti.e621ae1c-d05c-4645-9952-3f79224f3347", "transformItemId": "amzn1.dv.gti.e621ae1c-d05c-4645-9952-3f79224f3347", "synopsis": "Die talentierte SÃ¤ngerin Amy Winehouse (<PERSON><PERSON>) begeistert mit ihrer auÃergewÃ¶hnlichen Stimme und ihrem Charisma das Publikum. Schnell werden Musikfans und Talent Scouts auf sie aufmerksam und ihr kometenhafter Aufstieg in den Pophimmel beginnt, doch der Ruhm hat seinen Preisâ¦ Amy Winehouse konnte mit ihrem zweiten Album âBack To Blackâ Weltruhm erlangen und fÃ¼nf Grammys gewinnen.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Music Videos and Concerts"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/725d3e4238c0ef4ce1d37d47436eb7bfea5e344d7157699e48d455b1c1fa7575.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/cd86b3bd79167a47b4d6bd922fe4b7d57b4c9ffcf4db0419e46a4c8090b7b6a9.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/a160dd458721d02c91593ba34401714f52e5c47016074ff41a4e241f417addfd.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/5e0c9199f2e1619b01022d79158660d9e386c73e555d50a85fca34e98fc60f8e.jpg", "publicReleaseDate": 1712793600000, "runtimeSeconds": 7359, "runtime": "122 min", "overallRating": 4.2, "totalReviewCount": 1019, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e621ae1c-d05c-4645-9952-3f79224f3347", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_7", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_7", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e621ae1c-d05c-4645-9952-3f79224f3347", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_7", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_7", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "The Sticky - Season 1", "gti": "amzn1.dv.gti.7b9ce00c-f2ad-4d90-b936-aafdf9f9bcb5", "transformItemId": "amzn1.dv.gti.7b9ce00c-f2ad-4d90-b936-aafdf9f9bcb5", "synopsis": "A tough, middle-aged farmer, a Bostonian mobster and a mild-mannered French-Canadian security guard carry out a multi-million-dollar heist on Quebecâs maple syrup surplus.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Comedy", "Drama"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e7601eec15b23a334a5c5922b97c001188e84ac3b5ed62cf666038f467091f20.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bdaec6c09c8b080537b29f796ce96c209ffa3d57b6f2ba568bde7dc2e1d561e4.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/4312b6bc453931e6bf97d55f2d823f4d03d6dd2e576233090992fdc86910009f.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/bd57e5221bda75b8287551e53a27fe47958a31fd503d6f4465fe1362064d72b5.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/99927275b66d353de4f6e2c4289097a8bf49085b771430812d64577e40161126.jpg", "publicReleaseDate": 1733443200000, "overallRating": 4.5, "totalReviewCount": 24, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.7b9ce00c-f2ad-4d90-b936-aafdf9f9bcb5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_8", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_8", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.7b9ce00c-f2ad-4d90-b936-aafdf9f9bcb5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_8", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_8", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "The Sticky", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Cruel Intentions - Season 1", "gti": "amzn1.dv.gti.f7d73a29-af72-4202-99f2-aca48a5a6ee7", "transformItemId": "amzn1.dv.gti.f7d73a29-af72-4202-99f2-aca48a5a6ee7", "synopsis": "Two step-siblings will do anything, including blackmail, seduction, and backstabbing, to stay on top of their universityâs cutthroat social hierarchy.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Romance", "Suspense", "Young Adult Audience"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7e96c7965fe161260caff88c205e6afe3c1c958019bcf3c5858ae9749a70fa04.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/60a513d5db27763499fcbcde7018c38b4d0afd968736ce0ffa494995c324a978.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/4abdbd651ce96b8bebb76365ecc9fb5784beac82ae5a4dfda1e6400ea034b331.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/0f15e6f8d035ee96e6e4721aab604112f681826910abc8aa33b4b34a543bd0ab.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/92f309e1e98c76b22cef3e33f4205892e1abb42698a7db99b52d2cc2d84c6790.jpg", "publicReleaseDate": 1732147200000, "overallRating": 3.9, "totalReviewCount": 98, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f7d73a29-af72-4202-99f2-aca48a5a6ee7", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_9", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_9", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f7d73a29-af72-4202-99f2-aca48a5a6ee7", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_9", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_9", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Cruel Intentions", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Hunting With Tigers", "gti": "amzn1.dv.gti.6d6d1c27-b144-488d-857e-d7e5e92e0b16", "transformItemId": "amzn1.dv.gti.6d6d1c27-b144-488d-857e-d7e5e92e0b16", "synopsis": "<PERSON>, a young Parisian hustler, discovers that his stepfather <PERSON>, a famous bank robber, has been arrested along with his accomplices. During the trial, <PERSON>, one of the accused's lawyers, ChÃ©rif, requests <PERSON> to accept a dangerous heist in exchange for <PERSON> and her client's liberty. <PERSON> must convince and reunite ChÃ©rif's former partners to accomplish this high-risk stickup.", "badges": {"applyAudioDescription": true, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "starringCast": ["<PERSON><PERSON>", "WaÃ«l <PERSON><PERSON><PERSON>", "GÃ©raldine Nakache"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/6dcf674652d995ba05951a76935fd9a0936d7810e92ae3c746f8322c02a397da.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8be1acdcd9a9cc883ae1d242fcf427760ae5a30d23ad80657b7e187527761cc6.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/78b50ba9fd6913806b3cd94258f79bb6788489ac0bad10b00c1cb0f296a5f34e.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/da3d6cfe5d7d7be1edeb1bff5af7f3e5f3ef46f3cf9fb3211ce474799efcb29a.png", "publicReleaseDate": 1732233600000, "runtimeSeconds": 6571, "runtime": "109 min", "overallRating": 3.9, "totalReviewCount": 27, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.6d6d1c27-b144-488d-857e-d7e5e92e0b16", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_10", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_10", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.6d6d1c27-b144-488d-857e-d7e5e92e0b16", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_awns_7_10", "itemProducerID": "awareness-dome"}, "refMarker": "hm_hom_c_ixO5Hr_awns_7_10", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}], "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxNzRERzlVTkUyQjZXIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "SVOD", "entitlement": "Entitled", "analytics": {"refMarker": "hm_hom_c_ixO5Hr_7", "ClientSideMetrics": "472|CmYKOURFUHJpbWVPcmlnaW5hbHNhbmRFeGNsdXNpdmVzQ29udmVyc2lvbkxpdmVEZWZhdWx0RGVmYXVsdBIPMToxNzRERzlVTkUyQjZXGhAyOkRZMjhDNkYyRDEyODA4IgZpeE81SHISPAoEaG9tZRIEaG9tZSIGY2VudGVyKgAyJDQ0ODY1NmYxLTAyZDktNDRhYS1hMGQxLTMyZGVjNjQ5MjEzMBoEc3ZvZCIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQhZPcmlnaW5hbHNBbmRFeGNsdXNpdmVzSghoZXJjdWxlc0oLaXNPcmlnaW5hbHNSCGVudGl0bGVkWgBiDVN1cGVyQ2Fyb3VzZWxoB3IAejhzSk9OaDg4UUY4OFNNOHg2Tkdjd0ZXVHZpY2pBY3R3RGpHNG9sLXlqV2JldEtqb3FRakxNamc9PYIBBHRydWWKAQCSAQA="}, "tags": ["isOriginals"], "journeyIngressContext": "8|EgRzdm9k", "notExpandable": false, "type": "SUPER_CAROUSEL"}, {"title": "My Subscriptions", "unentitledText": "Subscriptions you might like", "unentitledItems": [{"title": "DAZN", "isEntitled": false, "offerText": "Subscribe", "description": "Watch DAZN on Prime Video", "imageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/DAZN_DE_Featured_Channel/de6fbf16-4808-4d8c-b804-bda443e7228e.jpeg", "imageAlternateText": "Watch DAZN on Prime Video", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_HSe36d54_8_1", "target": "signUp", "benefitId": "daz<PERSON>", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=daznde", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HSe36d54_8_1"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "AXN Black", "isEntitled": false, "offerText": "Free trial", "description": "We exclusively show the latest episodes of high-calibre series such as YELLOWSTONE, CHICAGO P.D. and ALMOST PARADISE. Action, crime and extraordinary characters.", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/axnblack/heroes/featured-offer-tile_1920x1080._CB555891927_.jpg", "imageAlternateText": "We exclusively show the latest episodes of high-calibre series such as YELLOWSTONE, CHICAGO P.D. and ALMOST PARADISE. Action, crime and extraordinary characters.", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "axnblack", "pageType": "subscription", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HS8cc0d0_8_2"}, "refMarker": "hm_hom_c_aP3Uk6_HS8cc0d0_8_2"}, "widgetType": "imageTextLink", "transformItemId": "landing:subscription:axnblack", "cardType": "LINK_CARD"}, {"title": "ALLSTARS", "isEntitled": false, "offerText": "Free trial", "description": "Top-movies, series and more: The biggest stars for you at home!", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/allstarsde/heroes/featured-offer-tile_1920x1080._CB606406819_.jpg", "imageAlternateText": "Top-movies, series and more: The biggest stars for you at home!", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_HSc66b76_8_3", "target": "signUp", "benefitId": "allstarsde", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=allstarsde", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HSc66b76_8_3"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "MGM+", "isEntitled": false, "offerText": "Free trial", "description": "From the iconic studio, MGM+ curates acclaimed original TV series & blockbuster films from leading Hollywood libraries, uncut and commercial-free. This is Entertainment", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/mgm/heroes/featured-offer-tile_1920x1080._CB590326766_.jpg", "imageAlternateText": "From the iconic studio, MGM+ curates acclaimed original TV series & blockbuster films from leading Hollywood libraries, uncut and commercial-free. This is Entertainment", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_HS4fea43_8_4", "target": "signUp", "benefitId": "mgm", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=mgm", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HS4fea43_8_4"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "Crunchyroll", "isEntitled": false, "offerText": "Free trial", "description": "All you can anime", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/crunchyrollde/heroes/featured-offer-tile_1920x1080._CB562160138_.jpg", "imageAlternateText": "All you can anime", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_EFAVbS_8_5", "target": "signUp", "benefitId": "crunchyrollde", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=crunchyrollde", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_EFAVbS_8_5"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "aniverse", "isEntitled": false, "offerText": "Free trial", "description": "Anime  highlights such as <PERSON>, <PERSON><PERSON>, \nFullmetal  Alchemist: Brotherhood and many more\nalso  in German dubbing on aniverse!", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/aniversede/heroes/featured-offer-tile_1920x1080._CB410813529_.jpg", "imageAlternateText": "Anime  highlights such as <PERSON>, <PERSON><PERSON>, \nFullmetal  Alchemist: Brotherhood and many more\nalso  in German dubbing on aniverse!", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_HS6f053c_8_6", "target": "signUp", "benefitId": "aniversede", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=aniversede", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HS6f053c_8_6"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "CINEMA OF HEARTS", "isEntitled": false, "offerText": "Free trial", "description": "Movies that make your heart beat faster", "imageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/DE_Cinehearts_Winback_2024/3abedc28-44c3-4734-a395-78ea3b5085f5.jpeg", "imageAlternateText": "Movies that make your heart beat faster", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_y8HIzD_8_7", "target": "signUp", "benefitId": "cineheartsde", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=cineheartsde", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_y8HIzD_8_7"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "ARD Plus", "isEntitled": false, "offerText": "Free trial", "description": "Discover highlights which are no longer available on ARD Mediathek. \nEnjoy Classics such as LindenstraÃe, thrilling Crime with <PERSON><PERSON><PERSON>, \nAnd beloved Kids-Content.", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/ardplusde/heroes/featured-offer-tile_1920x1080._CB568855522_.jpg", "imageAlternateText": "Discover highlights which are no longer available on ARD Mediathek. \nEnjoy Classics such as LindenstraÃe, thrilling Crime with <PERSON><PERSON><PERSON>, \nAnd beloved Kids-Content.", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_HSe7a4c5_8_8", "target": "signUp", "benefitId": "ardplusde", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=ardplusde", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HSe7a4c5_8_8"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "ARTHAUS+", "isEntitled": false, "offerText": "Free trial", "description": "Big cinema moments, classics, indie hits and more:\nThe home for film lovers!", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/arthaus/heroes/featured-offer-tile_1920x1080._CB610189879_.jpg", "imageAlternateText": "Big cinema moments, classics, indie hits and more:\nThe home for film lovers!", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_yGMCSV_8_9", "target": "signUp", "benefitId": "arthaus", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=arthaus", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_yGMCSV_8_9"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "Kixi Select", "isEntitled": false, "offerText": "Free trial", "description": "Kixi Select brings the best entertainment for children age 0 to 13 with more than 1,000 titles available. From new movies to beloved television series.", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/g-l/kixi/heroes/featured-offer-tile_1920x1080._CB419979595_.jpg", "imageAlternateText": "Kixi Select brings the best entertainment for children age 0 to 13 with more than 1,000 titles available. From new movies to beloved television series.", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_MfLxiH_8_10", "target": "signUp", "benefitId": "kixi", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=kixi", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_MfLxiH_8_10"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "AXN White", "isEntitled": false, "offerText": "Free trial", "description": "The home of award winning, outstanding series", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/axnwhite/heroes/featured-offer-tile_1920x1080._CB596248191_.jpg", "imageAlternateText": "The home of award winning, outstanding series", "gradientRequired": false, "entitlementMessaging": {}, "action": {"target": "landing", "pageId": "axnwhite", "pageType": "subscription", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HS9a2828_8_11"}, "refMarker": "hm_hom_c_aP3Uk6_HS9a2828_8_11"}, "widgetType": "imageTextLink", "transformItemId": "landing:subscription:axnwhite", "cardType": "LINK_CARD"}, {"title": "Home of Horror", "isEntitled": false, "offerText": "Subscribe", "description": "Gory, captivating, terrifying. The biggest horror collection with new highlights every week", "imageUrl": "https://m.media-amazon.com/images/S/sonata-images-prod/DE_Winback_24_homeofhorro/2f615d80-2f7d-4633-8a18-e3304ea4f34b.jpeg", "imageAlternateText": "Gory, captivating, terrifying. The biggest horror collection with new highlights every week", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_eGiYPb_8_12", "target": "signUp", "benefitId": "homeofhorror", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=homeofhorror", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_eGiYPb_8_12"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "discovery+", "isEntitled": false, "offerText": "Subscribe", "description": "Discovery+ is the streaming platform for food, home, travel, true crime, paranormal and much more. Watch exciting \"must-see\" originals and all your favorite shows from the best TV brands. There's something for everyone here.", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/discoveryplusde/heroes/featured-offer-tile_1920x1080._CB608192634_.jpg", "imageAlternateText": "Discovery+ is the streaming platform for food, home, travel, true crime, paranormal and much more. Watch exciting \"must-see\" originals and all your favorite shows from the best TV brands. There's something for everyone here.", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_HS67f05c_8_13", "target": "signUp", "benefitId": "discoveryplusde", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=discoveryplusde", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HS67f05c_8_13"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "ZDFtivi", "isEntitled": false, "offerText": "Free trial", "description": "Huge selection of movies and series for families from ZDF and KiKa", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/s-z/zdftivi/heroes/featured-offer-tile_1920x1080._CB411393133_.jpg", "imageAlternateText": "Huge selection of movies and series for families from ZDF and KiKa", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_HHq6lz_8_14", "target": "signUp", "benefitId": "zdftivi", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=zdftivi", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_HHq6lz_8_14"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}, {"title": "BBC Player", "isEntitled": false, "offerText": "Free trial", "description": "Unique stories, first-class production: Series and documentaries from the BBC", "imageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/bbcplayer/heroes/featured-offer-tile_1920x1080._CB589905517_.jpg", "imageAlternateText": "Unique stories, first-class production: Series and documentaries from the BBC", "gradientRequired": false, "entitlementMessaging": {}, "action": {"refMarker": "hm_hom_c_aP3Uk6_1qY8jU_8_15", "target": "signUp", "benefitId": "bbcplayer", "nonSupportedText": "Customers who subscribe to this channel on Amazon can watch videos here.", "uri": "https://www.amazon.de/gp/video/offers?benefitID=bbcplayer", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_1qY8jU_8_15"}}, "widgetType": "imageTextLink", "transformItemId": "signUp", "cardType": "LINK_CARD"}], "facet": {}, "items": [], "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxM040MTZDUllVTjhWNyMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "offerType": "Subscription", "entitlement": "NotEntitled", "analytics": {"refMarker": "hm_hom_c_aP3Uk6_8", "ClientSideMetrics": "488|CmkKO0RFQ29tYmluZWRDaGFubmVsc0Nhcm91c2VsVW5lbnRpdGxlZE1peGVkTGl2ZURlZmF1bHREZWZhdWx0EhAxOjEzTjQxNkNSWVVOOFY3GhAyOkRZRTQ2QkUzRUVGMUJCIgZhUDNVazYSPAoEaG9tZRIEaG9tZSIGY2VudGVyKgAyJDQ0ODY1NmYxLTAyZDktNDRhYS1hMGQxLTMyZGVjNjQ5MjEzMBoMc3Vic2NyaXB0aW9uIgNhbGwqADIPZmFjZXRlZENhcm91c2VsOhNIZXJvQ29udGVudFByb3ZpZGVyQhBDb21iaW5lZENoYW5uZWxzShNhbGxDaGFubmVsc0Nhcm91c2VsUgtub3RFbnRpdGxlZFoAYgVOb2Rlc2gIcgB6OHNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09ggEFZmFsc2WKAQCSAQA="}, "tags": [], "journeyIngressContext": "20|EgxzdWJzY3JpcHRpb24=", "type": "NODES"}, {"facet": {}, "title": "Titles expiring in the next 30 days", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6InNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09OjE3MzQ3MjEyODYwMDAiLCJhcE1heCI6MjI1LCJwcmV2SXRlbXMiOlsiZWI2ZiIsImZmNWQiLCI0NGY1IiwiMzYxZiIsIjA2NjMiLCIwMTFhIiwiMWNjMSIsImFhODUiLCIwM2Q2IiwiMjU3NyJdLCJzdHJpZCI6IjE6MTFNV0FXMjJRSDhYVlQjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUDI4Mzc3MzUyM2QxZDAwNWU4NTY3NDIxNjM3MjUyYmIxOGU3MTQ1MTU5NzQ4MzU0YzAzMmM0OTA2Nzk5YzQ1ZDRcIn0iLCJzdEtleSI6IntcInNic2luXCI6MCxcImN1cnNpemVcIjoyMjUsXCJwcmVzaXplXCI6MH0iLCJvcmVxayI6InJEUlUwcWhla0lqQWNiUk1pZFd6amc4T2kyNnNoWGtHQ01rY2xjY25EZlE9Iiwib3JlcWt2IjoxLCJleGNsVCI6W119", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMU1XQVcyMlFIOFhWVCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMU1XQVcyMlFIOFhWVCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "offerType": "SVOD", "entitlement": "Entitled", "items": [{"title": "Pompeii [dt./OV]", "gti": "amzn1.dv.gti.d911742a-4133-4025-8544-f32f4609eb6f", "transformItemId": "amzn1.dv.gti.d911742a-4133-4025-8544-f32f4609eb6f", "synopsis": "Das monumentale und actiongeladene Historienepos um die Macht der Liebe und die zerstÃ¶rerische Kraft der Natur.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Historical"], "starringCast": ["<PERSON>", "<PERSON>", "Carrie-<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a1d573393a75050b07afe2326675c183847624c7020d0ddcbcfda68a04c0ced3.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/69fdf302cef5cde4d09f64fee958012a31d44f41eb15a41efa7ddd46f2c4906b.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/24601ad95e60a7b7575d870a43d5b259317ce03340e5cd86b26ca0494a93bfb2.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1392940800000, "runtimeSeconds": 6019, "runtime": "100 min", "overallRating": 4.1, "totalReviewCount": 3251, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d911742a-4133-4025-8544-f32f4609eb6f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_1"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_1", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d911742a-4133-4025-8544-f32f4609eb6f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_1"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_1", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Passengers", "gti": "amzn1.dv.gti.feacb58c-f3ef-5797-15ac-e4fe87b4ff5d", "transformItemId": "amzn1.dv.gti.feacb58c-f3ef-5797-15ac-e4fe87b4ff5d", "synopsis": "<PERSON> and <PERSON> awaken from hibernation pods on a spacecraft 90 years too soon, falling for each other while trying to survive.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Science Fiction", "Romance", "Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/6c3530b7e5fb348129eaa29ad4ba886fecf0e3e3bba1006812ded2a75cdd967f.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/75081af119dcf1c7a9b79b69c56f1d137954a8cdc7e01b4a5942057cf17921e8.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c757c7b01b4d60d34547b5c7a49e6666c1707c3e964feaf1fc35f2a99f3900cb.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/603212220327e01b1c2f133a733232a9ab0ca35daa0ca7d8551e5dd5e522334c.jpg", "publicReleaseDate": 1482278400000, "runtimeSeconds": 5325, "runtime": "88 min", "overallRating": 4.6, "totalReviewCount": 14567, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.feacb58c-f3ef-5797-15ac-e4fe87b4ff5d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_2"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_2", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 12 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.feacb58c-f3ef-5797-15ac-e4fe87b4ff5d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_2"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_2", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Me Before You", "gti": "amzn1.dv.gti.e2aa8b67-c873-cf0e-8f48-04360b2a44f5", "transformItemId": "amzn1.dv.gti.e2aa8b67-c873-cf0e-8f48-04360b2a44f5", "synopsis": "Based on the international best-selling novel by <PERSON><PERSON><PERSON>, Me Before You is a romance about a young woman content with her small town life who is hired as a caretaker for an affluent Lo...", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/5b9ee518c0ffc2533c4a2226fb3c51578d9bb44e5d20ddb0f4c6c51778762f36.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/47bc74c5c2adaec78340b73f2a0df288481751d7e0b572fc10b43f29d9b4544e.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e5eb6f9efede6a6d972a76a091184d39b4e21a45d63525bf3b8cedcff3beb2c5.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1466640000000, "runtimeSeconds": 6337, "runtime": "105 min", "overallRating": 4.6, "totalReviewCount": 15, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e2aa8b67-c873-cf0e-8f48-04360b2a44f5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_3"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_3", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 12 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e2aa8b67-c873-cf0e-8f48-04360b2a44f5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_3"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_3", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Aquaman", "gti": "amzn1.dv.gti.6abbf101-cb31-7daa-d84b-e1867e20361f", "transformItemId": "amzn1.dv.gti.6abbf101-cb31-7daa-d84b-e1867e20361f", "synopsis": "\"Aquaman\" reveals the origin story of half-human, half-Atlantean <PERSON> and takes him on the journey of his lifetime-to discover if he is worthy of who he was born to beâ¦a king.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Action", "Fantasy", "Science Fiction"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/28565f2e2859badbd8a6124743d7d5e0537ea677f12b8a6b6958aebc3b31640e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bdb9ac49f510586ea75979cabefc76465899ad7dba1d988e058f489667555628.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/1571ca99ac00bf53ba460e3682eceaab7d87bbcaa4fcec4d7a24e44f60c9dd59.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/af5d09881a000af603051cd9a27e814e55c3c21bfdcc2c4ffd8a6bdf764b3289.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/8b4a7e5a501b001e8414bbb5ce1a9127aeffb580f82b63466fd5762fa961e007.jpg", "publicReleaseDate": 1545350400000, "runtimeSeconds": 8245, "runtime": "137 min", "overallRating": 4.5, "totalReviewCount": 43483, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.6abbf101-cb31-7daa-d84b-e1867e20361f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_4"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_4", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.6abbf101-cb31-7daa-d84b-e1867e20361f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_4"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_4", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Extraction - Operation Condor", "gti": "amzn1.dv.gti.70ab80ac-9513-7c8f-8e1e-58de20700663", "transformItemId": "amzn1.dv.gti.70ab80ac-9513-7c8f-8e1e-58de20700663", "synopsis": "<PERSON> folgt den Spuren seines Vaters, der als Agent der CIA im AuÃendienst sein groÃes Vorbild ist. Leider ist Harry nur im BÃ¼roeinsatz, denn obwohl er gut ausgebildet ist, werden seine AntrÃ¤ge auf Versetzung abgelehnt. Als Terroristen seinen Vater entfÃ¼hren, startet er eigenmÃ¤chtig eine nicht autorisierte Rettungsaktion. <PERSON><PERSON> kann Harry seine Kampfausbildung unter Beweis stellen.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense", "Action"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/43cc5a3fe0f54978efda647eabb8e86011f69fad448e3af605d5dd46215350f9.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f269a4208b88b41e0abb4eac27ac5df57977e5c25f52ee3fd67026a90256ca87.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/31de6c717c579675dfa926aad44d91916a1347a1e08541fbb0a34b2ffc109a88.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1450396800000, "runtimeSeconds": 5318, "runtime": "88 min", "overallRating": 4.0, "totalReviewCount": 1688, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.70ab80ac-9513-7c8f-8e1e-58de20700663", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_5"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_5", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 11 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.70ab80ac-9513-7c8f-8e1e-58de20700663", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_5"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_5", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Picture This", "gti": "amzn1.dv.gti.0cb1d97d-6b43-f9a8-c249-b77c32c2011a", "transformItemId": "amzn1.dv.gti.0cb1d97d-6b43-f9a8-c249-b77c32c2011a", "synopsis": "Unpopular teen <PERSON> lands a dream date with the most popular guy, but faces obstacles from her dad and his ex-girlfriend.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "0", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cde038f74126c69ca3faac48ee6aafeaa5917be886a8e574a6640ab1227ef57f.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/26d1f8791619b12ee40679d7d983133f7f6f2f46e68a1af490dd10480c55edcd.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/fc534756ac6dc43d7f5e03113f1a0b8adf6c497c383eea84dae336029f1bc92a.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1215907200000, "runtimeSeconds": 5550, "runtime": "92 min", "overallRating": 4.4, "totalReviewCount": 195, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.0cb1d97d-6b43-f9a8-c249-b77c32c2011a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_6"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_6", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 12 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.0cb1d97d-6b43-f9a8-c249-b77c32c2011a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_6"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_6", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Dangerous [dt./OV]", "gti": "amzn1.dv.gti.be58aa6e-16fd-4a70-80a2-ae12fb711cc1", "transformItemId": "amzn1.dv.gti.be58aa6e-16fd-4a70-80a2-ae12fb711cc1", "synopsis": "<PERSON> legt sich in diesem Action-Thriller als wortkarge Ein-Mann-Armee mit den MÃ¶rdern seines Bruders an. In weiteren Rollen glÃ¤nzen <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> und <PERSON><PERSON><PERSON>. Ex-HÃ¤ftling <PERSON> ist auf BewÃ¤hrung frei und will keinerlei Aufsehen erregen. Doch als sein Bruder unter mysteriÃ¶sen UmstÃ¤nden stirbt, reist er zu dessen Beerdigung auf eine abgelegene Insel ...", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/5c97d4e6b6353ff6eaf16e6b534156031db181aef6abf210528e43dce2a626c5.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4405aadbdc84896c3c79c096f33908582c9c27142b20dede203a48b28ba53120.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/b42ef48f1054ee5a43d8dfd8d0c10be340fc84fb7efe975bacd6c550dbeb83f2.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1645056000000, "runtimeSeconds": 5961, "runtime": "99 min", "overallRating": 3.8, "totalReviewCount": 4268, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.be58aa6e-16fd-4a70-80a2-ae12fb711cc1", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_7"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_7", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 14 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.be58aa6e-16fd-4a70-80a2-ae12fb711cc1", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_7"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_7", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Zookeeper", "gti": "amzn1.dv.gti.28ae07d5-9bcb-9820-3226-f3f318a6aa85", "transformItemId": "amzn1.dv.gti.28ae07d5-9bcb-9820-3226-f3f318a6aa85", "synopsis": "Zoo animals help their caretaker woo a woman.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "0", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Fantasy", "Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/541e033eea972704a83deecbb898c2020d602e33c5be1f4d023ad2a8de5987eb.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/980275752e4047039a89acbcc0d0cb7621b3648da55098faeef914cda9a313c6.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e9e5ccfe2810d03672a9522fbbde3abb90ed0741645e0a77a1e142aea78d012c.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/727a3ed12c7a8b8418ae06550479e2bb7f02c7af9753ed106c80ff16e20b51a1.jpg", "publicReleaseDate": 1310083200000, "runtimeSeconds": 5857, "runtime": "97 min", "overallRating": 4.5, "totalReviewCount": 3288, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.28ae07d5-9bcb-9820-3226-f3f318a6aa85", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_8"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_8", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 12 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.28ae07d5-9bcb-9820-3226-f3f318a6aa85", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_8"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_8", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "TatsÃ¤chlich...<PERSON><PERSON>", "gti": "amzn1.dv.gti.b2a9f6c8-b34a-f17b-991f-84ab619903d6", "transformItemId": "amzn1.dv.gti.b2a9f6c8-b34a-f17b-991f-84ab619903d6", "synopsis": "In der Vorweihnachtszeit schlagen die GefÃ¼hlswellen hoch. So verspÃ¼rt etwa Englands Premier eine unziemliche Zuneigung zu einer Mitarbeiterin, zwei Pornodarsteller mÃ¼ssen ihre SchÃ¼chternheit Ã¼berwinden und ein verstÃ¶rter Schriftsteller findet erst in der Ferne sein GlÃ¼ck.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Romance", "Comedy", "Drama"], "starringCast": ["<PERSON>", "Chiwetel Ejiofor", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/14a98cb4eb5dbc0d33ddf49e56dfec3f9f785624388444a04bcba80bb70ee344.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/3ae025d98740e7b6795551721a5244ba699b953a136878975c59dc8470200a1e.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/bdb444133c025ed4eecb5c1d2aab0b041f17860607463bbaaede71a2acdc6913.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1068768000000, "runtimeSeconds": 8085, "runtime": "134 min", "overallRating": 4.2, "totalReviewCount": 6351, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b2a9f6c8-b34a-f17b-991f-84ab619903d6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_9"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_9", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "BAFTA FILM AWARDÂ® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b2a9f6c8-b34a-f17b-991f-84ab619903d6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_9"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_9", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Blade Runner 2049", "gti": "amzn1.dv.gti.54af725d-4434-ea14-b98b-af121a712577", "transformItemId": "amzn1.dv.gti.54af725d-4434-ea14-b98b-af121a712577", "synopsis": "<PERSON> stars as a blade runner who stumbles upon a buried truth that jeopardizes the fragile social order in this long-awaited sequel.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "18", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Science Fiction", "Action", "Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "18", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4729a1867076f8d6348bbbb9d3a69a750d1969d2850216ca89590a57062e4e11.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/d00a2a36b5411a5267dd38bc019936cf9b388073f676f77e59cc16ffa4cea6b8.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/5abf375855cb09ff9fe3b4bd518c02d2bc5470c626e528eab95567a322d47a65.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/158f9a2dd1b5be86f98334d346ae177c87261f43e66ec828b1daed71a7cf4e7c.jpg", "publicReleaseDate": 1507161600000, "runtimeSeconds": 9403, "runtime": "156 min", "overallRating": 4.5, "totalReviewCount": 1428, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.54af725d-4434-ea14-b98b-af121a712577", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_10"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_10", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "Leaves Prime in 12 days"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.54af725d-4434-ea14-b98b-af121a712577", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_W6Yvio_brws_9_10"}, "refMarker": "hm_hom_c_W6Yvio_brws_9_10", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_W6Yvio_9", "ClientSideMetrics": "412|ClQKJklUdGVzdEV4cGlyaW5nVGl0bGVzTGl2ZURlZmF1bHREZWZhdWx0EhAxOjExTVdBVzIyUUg4WFZUGhAyOkRZOTlFQ0FDNEUxMTYyIgZXNll2aW8SPAoEaG9tZRIEaG9tZSIGY2VudGVyKgAyJDQ0ODY1NmYxLTAyZDktNDRhYS1hMGQxLTMyZGVjNjQ5MjEzMBoEc3ZvZCIDYWxsKgAyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQg5Ccm93c2VTdHJhdGVneVIIZW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgJcgB6OHNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09ggEEdHJ1ZYoBAJIBAA=="}, "tags": [], "journeyIngressContext": "8|EgRzdm9k", "type": "STANDARD_CAROUSEL"}, {"facet": {}, "title": "Paramount+: Most popular", "titleImageUrl": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/blast_carousel-logo_selected_rar._CB605605981_.png", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0Ijo0LCJucHNpIjoxMCwib3JlcSI6InNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09OjE3MzQ3MjEyODYwMDAiLCJhcE1heCI6NDAwLCJwcmV2SXRlbXMiOlsiYjNlYSIsIjBiN2QiLCI1YjJlIiwiMmRjOSIsImEzNGIiLCI3MzU5IiwiNDY0ZCIsIjdjZTciLCI3NDg3IiwiYTU3NCJdLCJzdHJpZCI6IjE6MTdJVkdUVDNPWkZGVCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNIiwiYXV0b2JvdCI6IntcInJzaXBTdGF0ZUlkXCI6XCJSU0lQMjgzNzczNTIzZDFkMDA1ZTg1Njc0MjE2MzcyNTJiYjE4ZTcxNDUxNTk3NDgzNTRjMDMyYzQ5MDY3OTljNDVkNFwifSIsInN0S2V5Ijoie1wic2JzaW5cIjowLFwiY3Vyc2l6ZVwiOjYyMSxcInByZXNpemVcIjowfSIsIm9yZXFrIjoickRSVTBxaGVrSWpBY2JSTWlkV3pqZzhPaTI2c2hYa0dDTWtjbGNjbkRmUT0iLCJvcmVxa3YiOjEsImV4Y2xUIjpbXX0=", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxN0lWR1RUM09aRkZUIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxN0lWR1RUM09aRkZUIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Subscription", "entitlement": "Entitled", "items": [{"title": "Germany Shore Staffel 4", "gti": "amzn1.dv.gti.3a7a64ae-fb50-4cb4-9833-baeadbc6b3ea", "transformItemId": "amzn1.dv.gti.3a7a64ae-fb50-4cb4-9833-baeadbc6b3ea", "synopsis": "Germany Shore schickt partywÃ¼tige Girls und Boys in den SÃ¼den, um die Party ihres Lebens zu feiern. Sie alle sind jung, sexy und bereit fÃ¼r SpaÃ! Zwei Wochen leben die Bewohner in einer Traumvilla und machen diese zu ihrer Partybase.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "starringCast": [], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7d6b9730a85cd32eac4bd1860acc7560f27e368270d487568024ee2c8fa2d7f7.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/b2382af634e3f91c96c8204327f8c6f927b8cb52045924d4ab08d0f269df3126.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d589dd6646c7c923ae7f5d39970395c0f881d62ac461db2d0502a113cd50026d.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "publicReleaseDate": 1736812800000, "overallRating": 3.8, "totalReviewCount": 4, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3a7a64ae-fb50-4cb4-9833-baeadbc6b3ea", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_1"}, "refMarker": "hm_hom_c_hWu21e_brws_10_1", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3a7a64ae-fb50-4cb4-9833-baeadbc6b3ea", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_1"}, "refMarker": "hm_hom_c_hWu21e_brws_10_1", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "showName": "Germany Shore", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "IF", "gti": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "transformItemId": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "synopsis": "Unraveling a fantastical world, a child possesses a remarkable talent to witness imaginary friends and endeavors to reunite them with children.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "0", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Comedy", "Kids"], "starringCast": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9ab835d40864521155b9caeb3bb9cc52ecb50e9933612d93486c113504fa6451.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/76df0dac7be58ecfc65d95d64d5a5196f8ce741500b616c9fe7d667ca40244fe.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7295c3c34836d7cbdc6def66790e4b856690bd8dc0a15c35360ed6ee34ae84d5.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/355b300892cd74d9f5567c8b6906fe94f7c7aa2eba95ce17c81f7f2dfed9bd20.jpg", "publicReleaseDate": 1715904000000, "runtimeSeconds": 6248, "runtime": "104 min", "overallRating": 4.4, "totalReviewCount": 534, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_2"}, "refMarker": "hm_hom_c_hWu21e_brws_10_2", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.7648df40-32d3-4027-8943-1d23ac770b7d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_2"}, "refMarker": "hm_hom_c_hWu21e_brws_10_2", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "SEAL Team - Staffel 6", "gti": "amzn1.dv.gti.e8779236-9a44-4884-ba77-e21ee7925b2e", "transformItemId": "amzn1.dv.gti.e8779236-9a44-4884-ba77-e21ee7925b2e", "synopsis": "Das MilitÃ¤rdrama SEAL TEAM folgt dem beruflichen und privaten Leben der Eliteeinheit der Navy SEALs, wÃ¤hrend sie gefÃ¤hrlichste Missionen ausfÃ¼hren.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Drama", "Military and War"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON> Jr."], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8047766cb3ed543559e1f1ea21778ab72cd7d0a4be8eeba4b854560d587f25d2.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/d8f620527a74d4abb562e3e40d47709d0cc6d807e64c92f3883fc00b45822169.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/836e573c0e340fdc377e16917e1829cdfc5d56a0c75226bd2658c2ed4793e778.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "publicReleaseDate": 1668902400000, "overallRating": 4.8, "totalReviewCount": 1499, "seasonNumber": 6, "numberOfSeasons": 7, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e8779236-9a44-4884-ba77-e21ee7925b2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_3"}, "refMarker": "hm_hom_c_hWu21e_brws_10_3", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e8779236-9a44-4884-ba77-e21ee7925b2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_3"}, "refMarker": "hm_hom_c_hWu21e_brws_10_3", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "showName": "SEAL Team [dt./OV]", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "Dear Santa", "gti": "amzn1.dv.gti.2d818f86-ee39-4f63-b792-f20632262dc9", "transformItemId": "amzn1.dv.gti.2d818f86-ee39-4f63-b792-f20632262dc9", "synopsis": "<PERSON> stars as a devilish entity who appears after a boy's innocent spelling mistake on his letter to <PERSON> leads to holiday chaos.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>-<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/51d78e6356f1937d367f7ad6f62bd078c36bacdc7288f1e7a6bb049b55cc1f0c.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/b405255b01b865e9b5527cf9d4ca0732da8accc15943e8497805263788fd750a.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/1b27753322603eb6a7962e17ea09528569d58ea081da70aff7da8478454dda1e.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/b497f7d0d54e3a4d751ad13269c494bbf3fd238b71d6e6a9c66357dfa83675ba.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/577b3b9146c9ddc9c8ff0809aa922f054b370ab13c6c9b3a2ef9a09e4d8a3d52.png", "publicReleaseDate": 1734480000000, "runtimeSeconds": 6458, "runtime": "107 min", "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2d818f86-ee39-4f63-b792-f20632262dc9", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_4"}, "refMarker": "hm_hom_c_hWu21e_brws_10_4", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2d818f86-ee39-4f63-b792-f20632262dc9", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_4"}, "refMarker": "hm_hom_c_hWu21e_brws_10_4", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "Landman Season 1", "gti": "amzn1.dv.gti.61dd1e12-2d7d-4ed4-8c23-7715937ea34b", "transformItemId": "amzn1.dv.gti.61dd1e12-2d7d-4ed4-8c23-7715937ea34b", "synopsis": "Set in Texas' booming oil towns, <PERSON><PERSON> explores the intertwined lives of roughnecks and billionaires navigating a modern-day oil rush.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Western"], "starringCast": [], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e067c2a37378f8739f980c260517bcc7739225415d4a0b256091baad90aac80f.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/3a47be836bd8a77cb5774df70f10f4637f0f06da44dc6365bbd957e2eea0cf71.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d930bfb30205cc358ecd32a4780b17f3d133a1acd09a172e236bda232b210cba.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "publicReleaseDate": 1735430400000, "overallRating": 4.8, "totalReviewCount": 38, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.61dd1e12-2d7d-4ed4-8c23-7715937ea34b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_5"}, "refMarker": "hm_hom_c_hWu21e_brws_10_5", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.61dd1e12-2d7d-4ed4-8c23-7715937ea34b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_5"}, "refMarker": "hm_hom_c_hWu21e_brws_10_5", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "showName": "Landman", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "Dexter: New Blood - Staffel 1", "gti": "amzn1.dv.gti.00f7ca9b-a6c7-41d3-be39-cad4ca7d7359", "transformItemId": "amzn1.dv.gti.00f7ca9b-a6c7-41d3-be39-cad4ca7d7359", "synopsis": "Amerikas beliebtester SerienmÃ¶rder ist zurÃ¼ck und sein dunkler Begleiter auch.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "18", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": [], "maturityRatingString": "18", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9afb51c23214ef8778960ff617a4cca2b4258783204faaf05c72f59a4bc2997f.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9661e54440591711538ba37bcaf6c0b03c26ce61df93bc583c6e0b93f49057ca.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ca1549ec55943ec9ec3abc1287e5bb068078a522634c58589e1b61d9a9ac25fc.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "publicReleaseDate": 1641686400000, "overallRating": 4.4, "totalReviewCount": 1350, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.00f7ca9b-a6c7-41d3-be39-cad4ca7d7359", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_6"}, "refMarker": "hm_hom_c_hWu21e_brws_10_6", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.00f7ca9b-a6c7-41d3-be39-cad4ca7d7359", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_6"}, "refMarker": "hm_hom_c_hWu21e_brws_10_6", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "showName": "Dexter: New Blood", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "Dexter: Original Sin Season 1", "gti": "amzn1.dv.gti.1c2627ee-473b-46b9-84a5-d8a03d9f464d", "transformItemId": "amzn1.dv.gti.1c2627ee-473b-46b9-84a5-d8a03d9f464d", "synopsis": "Young <PERSON> transitions from student to avenging serial killer.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": [], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/1d843da1feaf51d70d8e853626fc6948032b8193c181adccbf159130f3c1dabd.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9f3117e9a2a3efadff10bae6c96ae341c030504aa6c5fe9dcb76dd4fbe4e0bf5.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/982a6b24029fda21b56523317049a8dbf95b71bfaaebd470e3fb13cebb9feef2.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "publicReleaseDate": 1735171200000, "overallRating": 4.9, "totalReviewCount": 17, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1c2627ee-473b-46b9-84a5-d8a03d9f464d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_7"}, "refMarker": "hm_hom_c_hWu21e_brws_10_7", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1c2627ee-473b-46b9-84a5-d8a03d9f464d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_7"}, "refMarker": "hm_hom_c_hWu21e_brws_10_7", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "showName": "Dexter: Original Sin", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "gti": "amzn1.dv.gti.56b23e06-855c-4fe9-91be-967766ad7ce7", "transformItemId": "amzn1.dv.gti.56b23e06-855c-4fe9-91be-967766ad7ce7", "synopsis": "The new live-action event series follows <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) on a hilarious and action-packed journey of self-discovery as he agrees to train <PERSON> (<PERSON>) as his protÃ©gÃ© and teach him the ways of the Echidna warrior. The series takes place between the films SONIC THE HEDGEHOG 2 and SONIC THE HEDGEHOG 3.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Adventure", "Comedy", "Fantasy", "Science Fiction", "Kids"], "starringCast": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f3feb5d0176a0e6e8da4ad4a14683a5cca1067be459b232c4ca1aacf83a937d7.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/b2906c611a3bd102cfecb9cf76c355850c91a64be0470b8c9c83ac5ea1f2b229.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ef19f96771fb2c538b347d619915b46f53c214be4959e8646e02bf60f8747c73.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/3153af41b9c975d624e9bd1cafeac1440c8fde39429814b5008474be2b5f7aa0.jpg", "publicReleaseDate": 1714176000000, "overallRating": 4.1, "totalReviewCount": 73, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.56b23e06-855c-4fe9-91be-967766ad7ce7", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_8"}, "refMarker": "hm_hom_c_hWu21e_brws_10_8", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.56b23e06-855c-4fe9-91be-967766ad7ce7", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_8"}, "refMarker": "hm_hom_c_hWu21e_brws_10_8", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "showName": "<PERSON><PERSON><PERSON><PERSON>", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>", "gti": "amzn1.dv.gti.98a9f6b7-55f1-1d97-9cbe-20096bc37487", "transformItemId": "amzn1.dv.gti.98a9f6b7-55f1-1d97-9cbe-20096bc37487", "synopsis": "<PERSON> gerÃ¤t in einen Sumpf aus tÃ¶dlichen Intrigen.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e7f83309c41a85964947dfba6f63cde2e25f3b5fdf6e7e4b6f275769914df572.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/72d0d1d2b4d65135647cd35a9e9943375bc71ef3840edb446ed4e058feacced3.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/029e7ed585ee5ab2f2cb2a06e8a8f05c5f02616717367f8fc4f5992189580329.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "publicReleaseDate": 1325379600000, "runtimeSeconds": 7511, "runtime": "125 min", "overallRating": 4.6, "totalReviewCount": 7062, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.98a9f6b7-55f1-1d97-9cbe-20096bc37487", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_9"}, "refMarker": "hm_hom_c_hWu21e_brws_10_9", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.98a9f6b7-55f1-1d97-9cbe-20096bc37487", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_9"}, "refMarker": "hm_hom_c_hWu21e_brws_10_9", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "Tulsa King - Season 2", "gti": "amzn1.dv.gti.c92cdd66-9869-4191-9e6d-fb7d1f30a574", "transformItemId": "amzn1.dv.gti.c92cdd66-9869-4191-9e6d-fb7d1f30a574", "synopsis": "<PERSON> continues to grow his empire in Tulsa, but just as the crew gets their bearings, a looming turf war threatens to take them down.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Comedy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3a056ec78171893d320c81440e63ac136ce4831a8b0f5ff4d8bec24304d0b600.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/3eed85691c1b5f1062a3eeed2268c215ee6786f6ae01c163f8334368ce6f8924.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/b6dfbec32cff8e6206eab676f7ead1460f53a611373c702e6e2407b40b7c2521.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d409833b73d9aa6b4ed30ae361fa4d2b7e34713854441c41d7eb80031b5c7f36.jpg", "publicReleaseDate": 1731801600000, "overallRating": 3.6, "totalReviewCount": 6, "seasonNumber": 2, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c92cdd66-9869-4191-9e6d-fb7d1f30a574", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_10"}, "refMarker": "hm_hom_c_hWu21e_brws_10_10", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYÂ® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.c92cdd66-9869-4191-9e6d-fb7d1f30a574", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hWu21e_brws_10_10"}, "refMarker": "hm_hom_c_hWu21e_brws_10_10", "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg=="}], "showName": "Tulsa King", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_hWu21e_10", "ClientSideMetrics": "472|ClEKJFBQTFVTREVNb3N0UG9wdWxhckxpdmVEZWZhdWx0RGVmYXVsdBIPMToxN0lWR1RUM09aRkZUGhAyOkRZQjMwQkU0QTg3RDhBIgZoV3UyMWUSPAoEaG9tZRIEaG9tZSIGY2VudGVyKgAyJDQ0ODY1NmYxLTAyZDktNDRhYS1hMGQxLTMyZGVjNjQ5MjEzMBoMc3Vic2NyaXB0aW9uIgNhbGwqD3BhcmFtb3VudHBsdXNkZTIPZmFjZXRlZENhcm91c2VsOgZCcm93c2VCDkJyb3dzZVN0cmF0ZWd5ShhlbnRpdGxlZENoYW5uZWxzUGFnZVJ1bGVSCGVudGl0bGVkWgBiEFN0YW5kYXJkQ2Fyb3VzZWxoCnIAejhzSk9OaDg4UUY4OFNNOHg2Tkdjd0ZXVHZpY2pBY3R3RGpHNG9sLXlqV2JldEtqb3FRakxNamc9PYIBBHRydWWKAQCSAQA="}, "tags": [], "journeyIngressContext": "44|Cg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbg==", "type": "STANDARD_CAROUSEL"}, {"facet": {}, "title": "Christmas movies", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6InNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09OjE3MzQ3MjEyODYwMDAiLCJhcE1heCI6MTAwLCJwcmV2SXRlbXMiOlsiNzIzOSIsIjBhZGIiLCJmZjFmIiwiMDNjZCIsIjExMmIiLCI2ZGRiIiwiZGE4YSIsIjVjODciLCIxZjViIiwiYTAyNiJdLCJzdHJpZCI6IjE6MTExVzNQQkUyQUVMT1QjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUDI4Mzc3MzUyM2QxZDAwNWU4NTY3NDIxNjM3MjUyYmIxOGU3MTQ1MTU5NzQ4MzU0YzAzMmM0OTA2Nzk5YzQ1ZDRcIn0iLCJzdEtleSI6IntcInNic2luXCI6MCxcImN1cnNpemVcIjoxNTMsXCJwcmVzaXplXCI6MH0iLCJvcmVxayI6InJEUlUwcWhla0lqQWNiUk1pZFd6amc4T2kyNnNoWGtHQ01rY2xjY25EZlE9Iiwib3JlcWt2IjoxLCJleGNsVCI6W119", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMTFXM1BCRTJBRUxPVCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMTFXM1BCRTJBRUxPVCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "offerType": "Mixed", "entitlement": "Entitled", "items": [{"title": "Red One", "gti": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "transformItemId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "synopsis": "After <PERSON> aka RED ONE â is kidnapped, the North Pole's Head of Security teams up with an infamous bounty hunter to save <PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Action", "Adventure"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4cf72f249c3520bf05438824a039bfceaab19d21b533d20c2165829384561f1d.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/115800d972e291ce343fea1f88af9bc27fdb2c437d82608ce183b86c66b70de2.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/cd805cfff16808f6deef99c583bd46da4b035a9e5ff3907bd651ac255235d4f1.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/a2a9006c929d5f3c1d29104fb61dfa767a975f4a779a39a38bd1780f1749f920.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f96f371b2d8b70a1e1c1f44f0ddc0aa07f83fa7bf7e61e5f671d3fb74be00e35.jpg", "publicReleaseDate": 1733961600000, "runtimeSeconds": 7475, "runtime": "124 min", "overallRating": 4.0, "totalReviewCount": 218, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_1"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_1", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#1 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_1"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_1", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON> and the Philosopher's Stone", "gti": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "transformItemId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "synopsis": "Upon his 11th birthday, <PERSON> learns that he is the orphaned son of two powerful wizards and possesses unique magical powers of his own.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "6", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Kids", "Adventure", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/988931ca68d7b60c094396909c4e2427554cf0cee0a14a16a614abac6876b4ac.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/58cb7ed4768aeb90e31eef999e33f6a5171d4f04a73f22d50cd7106b9e64abc8.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/17ce609928dab45d65dd34b3d4c8cb9cba09eb3cc8c478f5ff112c5329e1a5a6.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/a55d1860afebb4b7a2e777de2e8e2e17de3527f414396e22c54284da1ad5d799.jpg", "publicReleaseDate": 1006387200000, "runtimeSeconds": 8778, "runtime": "146 min", "overallRating": 4.7, "totalReviewCount": 22351, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_2"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_2", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#7 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.50b4e520-051d-31a7-c3a2-d79e0b270adb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_2"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_2", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Christmas on Ice", "gti": "amzn1.dv.gti.7e822b9e-bae4-4d69-ad29-0fb385dbff1f", "transformItemId": "amzn1.dv.gti.7e822b9e-bae4-4d69-ad29-0fb385dbff1f", "synopsis": "Die zauberhafte LiebeskomÃ¶die verspricht weihnachtliche WohlfÃ¼hlgarantie. FÃ¼r ein Weihnachtswunder muss das ungleiche Paar aus der charmanten Eiskunstprinzes<PERSON> und dem kantigen Hockey-Star Noah zusammenfinden. Denn die traditionsreiche Eislaufbahn, die <PERSON> betriebt, ist von der SchlieÃung bedroht. Gemeinsam machen sie sich auf die Suche nach Rettung und finden dabei die groÃe Liebe.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "0", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e95803082da5808b3da2974b2417918c231c6a24d6de5c6a3681604b920d1866.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4be70585aa42fca7c0de5ca2aa1c2979415e23c6f17b5c6ab59ee10bfe3006fb.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/4e0f63d111a34bfd72fc584c1a112a6628a0404997a40f1a25880811606ee6e9.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1603411200000, "runtimeSeconds": 5235, "runtime": "87 min", "overallRating": 4.5, "totalReviewCount": 400, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.7e822b9e-bae4-4d69-ad29-0fb385dbff1f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_3"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_3", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.7e822b9e-bae4-4d69-ad29-0fb385dbff1f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_3"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_3", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "The Grinch", "gti": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "transformItemId": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "synopsis": "Illumination's <PERSON> Grinch chronicles a cynical creature's mission to steal Christmas, until a young girl's generosity melts his heart.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "0", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Animation", "Comedy", "Kids", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3bbfb93823a638d452accf9a4f074c6141736a3a7cb3765b805e3fcd9e374d58.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/996e9861988631a0e2dd9ed5c71b24cf5a63c97b8fdbacf8adeac15eaf11ac53.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/fde4186ead1cc89035c6dc6c326e40e3871432ced4cb196c744dc011dad62fd2.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7bd7c2cbac1e069513cbae6ac55ca06599fa59180199d434abcad8dc2e38cdff.jpg", "publicReleaseDate": 1543449600000, "runtimeSeconds": 4885, "runtime": "81 min", "overallRating": 4.7, "totalReviewCount": 7095, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_4"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_4", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#2 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_4"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_4", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Drei HaselnÃ¼sse fÃ¼r AschenbrÃ¶del (2021)", "gti": "amzn1.dv.gti.e1a0c951-52db-443c-bdc8-4c0d593d112b", "transformItemId": "amzn1.dv.gti.e1a0c951-52db-443c-bdc8-4c0d593d112b", "synopsis": "AschenbrÃ¶del lebt mit ihrer grausamen Stiefmutter und verwÃ¶hnten Stiefschwester zusammen, die sie wie eine Dienerin behandeln. Bei einem ihrer AusflÃ¼ge in den Wald trifft sie zufÃ¤llig den schÃ¶nen P<PERSON>zen. Sie fÃ¼hlen sich sofort zueinander hingezogen, aber er soll auf dem kÃ¶niglichen Ball eine passende Braut finden. Werden sie ihr Schicksal selbst bestimmen und fÃ¼r die wahre Liebe einstehen kÃ¶nnen?", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Fantasy", "Kids"], "starringCast": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ThorbjÃ¸rn Harr"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/71d0a719c653b043f146c36455abc4f8dde16d22e48b5caf4bea645aee50897b.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ef2b73eb69509b984600bf55605370e58b9919369a9a83a66fb0a27c01996444.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/18122d35f647f277b8c9cb974ffbad8021130e0b5e163d0dff6a366c8c9683f8.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/dfc380fa7bd6426a8490d30c248287cdf0515e3a2ed399a372cd53efee250cb0.png", "publicReleaseDate": 1639958400000, "runtimeSeconds": 5227, "runtime": "87 min", "overallRating": 3.2, "totalReviewCount": 2113, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e1a0c951-52db-443c-bdc8-4c0d593d112b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_5"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_5", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e1a0c951-52db-443c-bdc8-4c0d593d112b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_5"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_5", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON> Prinzessin zu Weihnachten", "gti": "amzn1.dv.gti.06b66a21-b75c-ec06-a6b6-6c552a226ddb", "transformItemId": "amzn1.dv.gti.06b66a21-b75c-ec06-a6b6-6c552a226ddb", "synopsis": "<PERSON> darf den Traum aller MÃ¤dchen Wirklichkeit werden lassen, als sie zusammen mit ihrer Nichte Maddie und ihrem Neffen Milo Ã¼ber Weihnachten der Einladung eines entfernten Verwandten folgt, das Fest auf einem europÃ¤ischen Schloss zu verbringen. Denn vÃ¶llig unerwartet verliebt sie sich in Ashton, den Prinz von Castlebury und sieht sich dem schÃ¶nsten Weihnachtsgeschenk gegenÃ¼ber.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Romance", "Comedy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/1d29379d749b9397d85f5a374674fcbfc852af2a6f6aa4db422ec42e76c321ba.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e66bc43233aa95f3d6c2dad52c5a0a15b06a46d1ba6c8a92fc83e0bc4cabd012.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/002e753184d1b55304a7e1c8537bb91973183262d61bb6c57f4b93bf688ef661.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1322870400000, "runtimeSeconds": 5204, "runtime": "86 min", "overallRating": 4.6, "totalReviewCount": 3144, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.06b66a21-b75c-ec06-a6b6-6c552a226ddb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_6"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_6", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.06b66a21-b75c-ec06-a6b6-6c552a226ddb", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_6"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_6", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Der Polarexpress", "gti": "amzn1.dv.gti.cca9f6d9-11e9-6332-aa00-3f5eaac2da8a", "transformItemId": "amzn1.dv.gti.cca9f6d9-11e9-6332-aa00-3f5eaac2da8a", "synopsis": "Gibt es Ã¼berhaupt einen Weihnachtsmann? \"Der Polarexpress\" nimmt uns mit auf eine Reise zum Nordpol - und hÃ¤lt fÃ¼r alle Zweifler ein auÃergewÃ¶hnliches Weihnachtsgeschenk bereit.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "6", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Animation", "Adventure", "Comedy", "Fantasy", "Arts, Entertainment, and Culture"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/145dbdcec14e218aea782072c1216baace6d13b1a92669dfd5e39c555faea79a.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/feccd8026cf2d237fe045147ae593b8bd7f8f24ad739e59d2ad12435d4db1797.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/3b5a438a3fab165d4a7a399117a8d0316c34d24d80188e4ec34a01f926d062b4.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1100044800000, "runtimeSeconds": 5992, "runtime": "99 min", "overallRating": 4.8, "totalReviewCount": 29957, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.cca9f6d9-11e9-6332-aa00-3f5eaac2da8a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_7"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_7", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARSÂ® 3X nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.cca9f6d9-11e9-6332-aa00-3f5eaac2da8a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_7"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_7", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON> [dt./OV]", "gti": "amzn1.dv.gti.3ebaf189-2041-95ef-84c3-77a35c8d5c87", "transformItemId": "amzn1.dv.gti.3ebaf189-2041-95ef-84c3-77a35c8d5c87", "synopsis": "<PERSON> hat schon bessere Zeiten erlebt: Seiner Werkstatt fehlen die AuftrÃ¤ge und die Schulden hÃ¤ufen sich. Als ob das nicht reichen wÃ¼rde, wird ein gnadenloser Killer namens Skinny Man auf Chris gehetzt. Als der Hitman samt betrÃ¤chtlichem Waffenarsenal nach langer Suche die Cringle-Farm ausfindig macht, kommt es im tiefverschneiten Kanada zu einem wahren Blutbad â¦", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Fantasy", "Action"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7022dc04b6f48931b91cf21cf01f037c4e72eede9a9bf5159f6ad9d0b9fdb301.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e764dbbd292298df3aed971f99a7d595722e3a408efb3453745c34cc95b5c719.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/eccf157e602c486b306fca94ac575dbcc8b333a5943afd4efda6487abad0adac.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1606176000000, "runtimeSeconds": 6014, "runtime": "100 min", "overallRating": 3.7, "totalReviewCount": 7247, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3ebaf189-2041-95ef-84c3-77a35c8d5c87", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_8"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_8", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3ebaf189-2041-95ef-84c3-77a35c8d5c87", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_8"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_8", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Die Legende vom Weihnachtsstern", "gti": "amzn1.dv.gti.34a9f6e0-7788-bddf-9209-8717d8761f5b", "transformItemId": "amzn1.dv.gti.34a9f6e0-7788-bddf-9209-8717d8761f5b", "synopsis": "Auf der Flucht vor einer Bande von Dieben versteckt sich die Sonja in der Vorratskammer des KÃ¶nigsschlosses und wird Zeugin einer unglaublichen Geschichte: Nachdem die Tochter des KÃ¶nigs, auf der Suche nach dem Weihnachtsstern im dunklen Wald von einer bÃ¶sen Hexe verhext wurde und daraufhin verschwand, verfluchte der traurige KÃ¶nig den leuchtenden Stern, der dann am Himmel verloren ging.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "0", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["International", "Kids", "Adventure", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c0120bd4cab42605491f47460d594a921fd799cb65e6c6ee47ecacf25b0b2a68.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/1fb5f30b005dab5033a301dfd0e0df3092b741a029799f931c1c31ae11ea56f0.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/23ad1b8162b615eda5a2a0d2043cd7ac4c9b0c3505695744787117d1a88c7de4.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1384387200000, "runtimeSeconds": 4633, "runtime": "77 min", "overallRating": 4.5, "totalReviewCount": 1604, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.34a9f6e0-7788-bddf-9209-8717d8761f5b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_9"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_9", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.34a9f6e0-7788-bddf-9209-8717d8761f5b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_9"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_9", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON> G<PERSON>", "gti": "amzn1.dv.gti.a4a9f6d4-5cd0-800b-2816-6826bbd6a026", "transformItemId": "amzn1.dv.gti.a4a9f6d4-5cd0-800b-2816-6826bbd6a026", "synopsis": "FÃ¼r die Einwohner des mÃ¤rchenhaften Dorfes Whoville dreht sich alles nur um ein einziges Thema: Weihnachten! Ãberall im Ort laufen die Festvorbereitungen auf Hochtouren. Nur der bitterbÃ¶se, giftgrÃ¼ne Grinch hockt allein in seiner HÃ¶hle auf einer Bergspitze und beobachtet das frÃ¶hliche Geschenkeverpacken mit grenzenloser Abscheu.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "6", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Kids", "Comedy", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/57c91e46df9439f6c1521f35bc9ee1b4fda35c2ef98d427d92b49c12a4be71bc.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/37176bb2681d00c36d8630c38f2d5b59c196f42bf280084438cd84644c8d5ccd.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/dc773b3746c9b672fd85287f362755cc8a39476239176377d99785ce8d131905.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 974419200000, "runtimeSeconds": 6296, "runtime": "104 min", "overallRating": 4.6, "totalReviewCount": 22368, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a4a9f6d4-5cd0-800b-2816-6826bbd6a026", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_10"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_10", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARÂ® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a4a9f6d4-5cd0-800b-2816-6826bbd6a026", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_g8ftJ7_brws_11_10"}, "refMarker": "hm_hom_c_g8ftJ7_brws_11_10", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_g8ftJ7_11", "ClientSideMetrics": "408|Ck0KH0VVNnhtYXNtb3ZpZXNMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTExVzNQQkUyQUVMT1QaEDI6RFlFQjAwMkU1QkRBMDYiBmc4ZnRKNxI8CgRob21lEgRob21lIgZjZW50ZXIqADIkNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwGgNhbGwiBW1vdmllKgNhbGwyD2ZhY2V0ZWRDYXJvdXNlbDoGQnJvd3NlQg5Ccm93c2VTdHJhdGVneVIIZW50aXRsZWRaAGIQU3RhbmRhcmRDYXJvdXNlbGgLcgB6OHNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09ggEEdHJ1ZYoBAJIBAA=="}, "tags": [], "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA==", "type": "STANDARD_CAROUSEL"}, {"facet": {}, "title": "Explore: Movies to rent for â¬0.99 each. Only for Prime members.", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6InNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09OjE3MzQ3MjEyODYwMDAiLCJhcE1heCI6MTAwLCJwcmV2SXRlbXMiOlsiOGQyNCIsIjFmYzIiLCJhYWVjIiwiNzk0NSIsIjIyYzQiLCI0Yjc0IiwiMmY1OSIsIjZlNmMiLCIzMmRhIiwiZjA4MiJdLCJzdHJpZCI6IjE6MUVYUEtWTEdVSTdPRyMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNIiwiYXV0b2JvdCI6IntcInJzaXBTdGF0ZUlkXCI6XCJSU0lQMjgzNzczNTIzZDFkMDA1ZTg1Njc0MjE2MzcyNTJiYjE4ZTcxNDUxNTk3NDgzNTRjMDMyYzQ5MDY3OTljNDVkNFwifSIsInN0S2V5Ijoie1wic2JzaW5cIjowLFwiY3Vyc2l6ZVwiOjQ2NSxcInByZXNpemVcIjowfSIsIm9yZXFrIjoickRSVTBxaGVrSWpBY2JSTWlkV3pqZzhPaTI2c2hYa0dDTWtjbGNjbkRmUT0iLCJvcmVxa3YiOjEsImV4Y2xUIjpbXX0=", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxRVhQS1ZMR1VJN09HIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxRVhQS1ZMR1VJN09HIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "TVOD", "entitlement": "NotEntitled", "items": [{"title": "Despicable Me 4", "gti": "amzn1.dv.gti.af991753-e4cf-4d28-880d-dfca3d1e8d24", "transformItemId": "amzn1.dv.gti.af991753-e4cf-4d28-880d-dfca3d1e8d24", "synopsis": "The beloved family of <PERSON><PERSON>, <PERSON>, their daughters, and the mischievous <PERSON><PERSON> returns in a hilarious escapade with the addition of baby <PERSON><PERSON> <PERSON>.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "6", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Animation", "Kids"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a97f119914ce87ed2d5fb3fb3fe712d690c9ba5311c748842b244e3f047ffa54.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e61572c36459dbddfd0bd9d638693fa8257c45067ba3150b7ce788c255b8c0ee.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/3d382b5108e3e6c17952115b812fc5e00d0cf1893efe3b0f7dc2fc0eaae9dbc3.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/497967723b5a9ee7d45e7479da870f29871042f91b4b747df459cbc6cec947fc.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/18dbaee4edf52898539d3723237681a372b22bfc557d9118535bcfc349cfd8f9.jpg", "publicReleaseDate": 1720656000000, "runtimeSeconds": 6196, "runtime": "103 min", "overallRating": 4.5, "totalReviewCount": 2261, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.af991753-e4cf-4d28-880d-dfca3d1e8d24", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_1"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_1", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.af991753-e4cf-4d28-880d-dfca3d1e8d24", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_1"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_1", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Bad Boys: Ride or Die", "gti": "amzn1.dv.gti.235a932d-8422-409a-8384-65a6fb2b1fc2", "transformItemId": "amzn1.dv.gti.235a932d-8422-409a-8384-65a6fb2b1fc2", "synopsis": "The Bad Boys are back but this time with a twist: Miami's finest are now on the run.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Comedy", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8b1deeffc97c5693353db733fbcbf7765e4876b293a5bc07332e5fa8788ed96e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4c4f9f2aef79c9b1f01841215bae43b096779a2007dee74b0e159b8c09febf0f.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/588c2bcf1014830fb866a3f328f3dd1320ece86938ab4eeb6107dc62393b4c70.jpg", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/15364eca1baf02d41da0404aa1ee5e7c9c2367f9f1f3bacc8ed1eeb2ee9de93f.jpg", "publicReleaseDate": 1717545600000, "runtimeSeconds": 6960, "runtime": "116 min", "overallRating": 4.4, "totalReviewCount": 3487, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.235a932d-8422-409a-8384-65a6fb2b1fc2", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_2"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_2", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.235a932d-8422-409a-8384-65a6fb2b1fc2", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_2"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_2", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>", "gti": "amzn1.dv.gti.e36bbcd5-bb9f-4350-b370-deeca115aaec", "transformItemId": "amzn1.dv.gti.e36bbcd5-bb9f-4350-b370-deeca115aaec", "synopsis": "10 <PERSON><PERSON>, <PERSON> <PERSON><PERSON>, ein unvergessliches Abenteuer: FÃ¼r den Profi-Athleten Michael <PERSON> sind die Adventure Racing World Championships in der Dominikanischen Republik die letzte Chance, einen groÃen Wettkampf zu gewinnen. Das fordernde Rennen bringt ihn und seine Teamkollegen Leo, <PERSON> und <PERSON> an ihre Grenzen. Doch die unerwartete Begegnung mit einem StraÃenhund Ã¤ndert alles...", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "6", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Drama", "Sports"], "starringCast": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/0c78acfefccf30a61f70dbd48cd26d747082da32c8a601469ccba58b2a771cd4.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/a79816d3f163d96eb2bacf88ed61690bfec0453c4fb16f46c723f7239502cc58.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/b2cc275007c4e128267d86dd3f31ea2c743ded1606d06da7f80e4bc3dd218fe5.jpg", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/4beddea4e3534173ed1ce7915ab64158d91e1b0d8509d3151455df4a1e112fb9.jpg", "publicReleaseDate": 1714003200000, "runtimeSeconds": 6473, "runtime": "107 min", "overallRating": 4.5, "totalReviewCount": 919, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e36bbcd5-bb9f-4350-b370-deeca115aaec", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_3"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_3", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e36bbcd5-bb9f-4350-b370-deeca115aaec", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_3"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_3", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Scrooged", "gti": "amzn1.dv.gti.54b71560-f0db-5cd5-6046-b59904007945", "transformItemId": "amzn1.dv.gti.54b71560-f0db-5cd5-6046-b59904007945", "synopsis": "A mean-spirited TV executive is visited by three ghosts from different time periods, mirroring <PERSON><PERSON><PERSON>'s journey in 19th-century London.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Fantasy", "Comedy", "Drama", "Romance"], "starringCast": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/483887eed46d83ae99ee98d2ea1f9ecde7fc5c630bca39334485927aebf8f0b8.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/1585973cbc9c1c0a74f0cb30a96e315e81e40245679552f6f2cd91c6ec32c25a.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/85e07d89719487ee7f5bb93d68d52389b060df68582764c144c87b4a00dcc22f.jpg", "publicReleaseDate": 596246400000, "runtimeSeconds": 5832, "runtime": "97 min", "overallRating": 4.7, "totalReviewCount": 2897, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.54b71560-f0db-5cd5-6046-b59904007945", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_4"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_4", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "OSCARÂ® nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.54b71560-f0db-5cd5-6046-b59904007945", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_4"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_4", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "The Bridge", "gti": "amzn1.dv.gti.85e0c8a3-de87-4168-872f-c836828422c4", "transformItemId": "amzn1.dv.gti.85e0c8a3-de87-4168-872f-c836828422c4", "synopsis": "Eine Gruppe bewaffneter SÃ¶ldner verÃ¼bt einen Anschlag auf die Tobin Bridge und nimmt Hunderte von Menschen als Geiseln, unter anderem Army Ranger Eric und seine Schwester. Als ihr AnfÃ¼hrer beginnt, <PERSON><PERSON><PERSON><PERSON> zu tÃ¶ten, muss <PERSON> sich auf seine militÃ¤rische Ausbildung und auf seine Instinkte verlassen, um seine Schwester â und alle anderen â zu retten!", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/98864ba5df7669d6911908855116b9cd0159d4ceb482296d357dd13a361189c9.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f1e75d46611f4e185cf20bda5fd0ca6b7c602ed75e3d2eacd358f10108e81748.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/878a757b30c0df782de3b3bc2ff7dc9dd56a0619cc1d403993368bf87a9d49ac.jpg", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/a98beadedbbc45ab36a026b60c0c421f90c1c48cbd026e77ed246163a41d8fe7.jpg", "publicReleaseDate": 1732233600000, "runtimeSeconds": 5767, "runtime": "96 min", "overallRating": 4.2, "totalReviewCount": 54, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.85e0c8a3-de87-4168-872f-c836828422c4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_5"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_5", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.85e0c8a3-de87-4168-872f-c836828422c4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_5"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_5", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "It's A Wonderful LIfe", "gti": "amzn1.dv.gti.88aecabd-558f-4b71-0c50-ae68563d4b74", "transformItemId": "amzn1.dv.gti.88aecabd-558f-4b71-0c50-ae68563d4b74", "synopsis": "An angel grants a suicidal man's wish to never have been born, leading him to discover the immense impact his life has had on those around him.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "0", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Faith and Spirituality", "Fantasy", "Drama", "Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/974f2fd5f781b5ececc863196f9cfdc48631667d2ed1768ab09bc3e0ad4f32cb.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/f558b76804fdff892607e39ff1f62853bacb2016b8ad7d24053e81678219732b.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/fa943c9403af0a7129ed139b2218207aef5a533c5ccec492a540e697425ff7ad.jpg", "publicReleaseDate": -725328000000, "runtimeSeconds": 7509, "runtime": "125 min", "overallRating": 4.8, "totalReviewCount": 17402, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.88aecabd-558f-4b71-0c50-ae68563d4b74", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_6"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_6", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBEÂ® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.88aecabd-558f-4b71-0c50-ae68563d4b74", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_6"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_6", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "PlÃ¶tzlich Familie [dt./OV]", "gti": "amzn1.dv.gti.5cb4714d-e956-75f3-1731-659e29ab2f59", "transformItemId": "amzn1.dv.gti.5cb4714d-e956-75f3-1731-659e29ab2f59", "synopsis": "<PERSON> und <PERSON> wollen Kinder und stÃ¼rzen sich in eine Adoption. Mit drei Geschwistern - darunter eine rebellische 15-JÃ¤hrige - starten sie den urkomischen Versuch, sich die Grundlagen plÃ¶tzlicher Elternschaft anzueignen und eine Familie zu werden.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9d64edf4b69242d6f83e8939da02dd92031c7529f935c40c0ead3afd868b0f14.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8982243410b8a0722cf30e6afa6af462476208b1663358a1406bce2279d2d7e3.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d4256cb4eadf984a3b291510e85b5c806fd13cad4e0a579d2b14104905ff06b3.jpg", "publicReleaseDate": 1548892800000, "runtimeSeconds": 7086, "runtime": "118 min", "overallRating": 4.5, "totalReviewCount": 5634, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5cb4714d-e956-75f3-1731-659e29ab2f59", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_7"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_7", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5cb4714d-e956-75f3-1731-659e29ab2f59", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_7"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_7", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Die Liebe ist stark", "gti": "amzn1.dv.gti.9e4e660e-0543-40ec-8bc2-0303d1c96e6c", "transformItemId": "amzn1.dv.gti.9e4e660e-0543-40ec-8bc2-0303d1c96e6c", "synopsis": "âDie <PERSON> ist stark\" basiert auf dem Bestseller-<PERSON> von Francine <PERSON>. Der Film erzÃ¤hlt die kraftvolle Liebesgeschichte eines jungen Paares, die mit ihrer StÃ¤rke und Ausdauer die harte RealitÃ¤t des kalifornischen Goldrausches von 1850 bewÃ¤ltigt. Es ist eine bewegende Geschichte Ã¼ber bedingungslose und alles verzehrende Leidenschaft.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Faith and Spirituality", "Western", "Romance", "Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/549fdbc9f2c58ad5da296b301b65115bd020c177dd58e9c2deed81b5341da294.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/7ddf8345fc49718dafaa9bfe36ab2c95b6df869ef02d8d65c0b88b83988b6f58.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6a83531ef78d083f9b0c704b1dc78b0065b2abd4b57af0ce220352598288df77.png", "publicReleaseDate": 1697673600000, "runtimeSeconds": 8057, "runtime": "134 min", "overallRating": 4.4, "totalReviewCount": 1044, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.9e4e660e-0543-40ec-8bc2-0303d1c96e6c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_8"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_8", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.9e4e660e-0543-40ec-8bc2-0303d1c96e6c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_8"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_8", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": true}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Der blutige Pfad Gottes", "gti": "amzn1.dv.gti.42a9f6c3-eb71-ba49-eb39-6c48246432da", "transformItemId": "amzn1.dv.gti.42a9f6c3-eb71-ba49-eb39-6c48246432da", "synopsis": "Boston, St. Patrick's Day: Die irischen ZwillingsbrÃ¼der <PERSON> und <PERSON> sitzen in ihrer Stammkneipe, als plÃ¶tzlich SchlÃ¤ger der Russen-Mafia auftauchen. Doch sie haben die Rechnung ohne die BrÃ¼der gemacht - die Beiden tÃ¶ten die Russen und gelten in der Ãffentlichkeit plÃ¶tzlich als Heilige. Ihnen ist klar: sie wurden von <PERSON> auserwÃ¤hlt, die Welt vom BÃ¶sen zu befreien!", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "18", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "18", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/5c103eaa958014ddfb74bd114bb3570a3cb6b01d2a33660c7be2c590bb9b297d.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ece63f4ce067e494da4541d9c5b920eda23938d322786c99b3b4e8a5c247b7d5.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d4cfc3044504eca9b3eddd78dbdf6b44d6ffd0f2ed34b8840cff69a459a1575a.jpg", "publicReleaseDate": 968889600000, "runtimeSeconds": 6240, "runtime": "104 min", "overallRating": 4.7, "totalReviewCount": 6882, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.42a9f6c3-eb71-ba49-eb39-6c48246432da", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_9"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_9", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.42a9f6c3-eb71-ba49-eb39-6c48246432da", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_9"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_9", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Das schÃ¶nste MÃ¤dchen der Welt", "gti": "amzn1.dv.gti.06b2db2b-d535-48e1-96bb-788fb582f082", "transformItemId": "amzn1.dv.gti.06b2db2b-d535-48e1-96bb-788fb582f082", "synopsis": "<PERSON> ist <PERSON><PERSON><PERSON> und trotz seiner gewaltigen Nase in die coole Roxy verliebt. Auf der Klassenfahrt nach Berlin versucht er, versteckt hinter dem hÃ¼bschen, aber dumme<PERSON>, mit Reimen, <PERSON><PERSON><PERSON> und der Hilfe von WhatsApp Roxys Herz zu erobern.", "badges": {"applyAudioDescription": true, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Romance", "Music Videos and Concerts"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9ea0b07aff01883a5d243d5d0fc9a8ed21f9f408f08c1df9bf1d5d3f2c040d9f.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6db4003eac63dedfed872d19fb2e9b97d1234c1003da57cc78d952e1d4a8d0fe.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c89f6b29268601d3452a2fa722aebb5fa3bca3b6d0a0e0246583c3463e7ba915.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f1d3d006aeecacaa3e14d44b52e9f431196d4f55cb6d207df249226242dd282b.png", "publicReleaseDate": 1536192000000, "runtimeSeconds": 6166, "runtime": "102 min", "overallRating": 4.6, "totalReviewCount": 11486, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.06b2db2b-d535-48e1-96bb-788fb582f082", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_10"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_10", "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.06b2db2b-d535-48e1-96bb-788fb582f082", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_EIgnpV_brws_12_10"}, "refMarker": "hm_hom_c_EIgnpV_brws_12_10", "journeyIngressContext": "8|EgR0dm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_EIgnpV_12", "ClientSideMetrics": "432|Cl8KMkRFVFZPRE1hcnF1ZWVQTURTaGVsZk1vdmllVk9EMDk5TGl2ZURlZmF1bHREZWZhdWx0Eg8xOjFFWFBLVkxHVUk3T0caEDI6RFkyOTE3NjI5RjkwODQiBkVJZ25wVhI8CgRob21lEgRob21lIgZjZW50ZXIqADIkNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwGgR0dm9kIgVtb3ZpZSoAMg9mYWNldGVkQ2Fyb3VzZWw6BkJyb3dzZUIOQnJvd3NlU3RyYXRlZ3lSC25vdEVudGl0bGVkWgBiEFN0YW5kYXJkQ2Fyb3VzZWxoDHIAejhzSk9OaDg4UUY4OFNNOHg2Tkdjd0ZXVHZpY2pBY3R3RGpHNG9sLXlqV2JldEtqb3FRakxNamc9PYIBBWZhbHNligEAkgEA"}, "tags": [], "journeyIngressContext": "8|EgR0dm9k", "seeMore": {"action": {"target": "BrowseToStrategyInstance", "pageId": "default", "pageType": "browse", "analytics": {}}}, "type": "STANDARD_CAROUSEL"}, {"facet": {}, "title": "Recommended TV", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0Ijo3LCJucHNpIjoxMCwib3JlcSI6InNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09OjE3MzQ3MjEyODYwMDAiLCJhcE1heCI6MTAwLCJwcmV2SXRlbXMiOlsiNWExNSIsIjYyMGEiLCI5ZjJlIiwiOTU4YyIsIjYyMDIiLCJjMmUzIiwiNTc2ZCIsIjNmOTgiLCJiM2RiIiwiM2JhYyJdLCJzdHJpZCI6IjE6MTFYVzlOMDdPRkFMUzgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUDI4Mzc3MzUyM2QxZDAwNWU4NTY3NDIxNjM3MjUyYmIxOGU3MTQ1MTU5NzQ4MzU0YzAzMmM0OTA2Nzk5YzQ1ZDRcIn0iLCJvcmVxayI6InJEUlUwcWhla0lqQWNiUk1pZFd6amc4T2kyNnNoWGtHQ01rY2xjY25EZlE9Iiwib3JlcWt2IjoxLCJleGNsVCI6W119", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMVhXOU4wN09GQUxTOCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMVhXOU4wN09GQUxTOCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "offerType": "Mixed", "entitlement": "Entitled", "items": [{"title": "Mayor of Kingstown Season 1", "gti": "amzn1.dv.gti.5cb91ecd-0def-45c3-a6cb-0fd0ad145a15", "transformItemId": "amzn1.dv.gti.5cb91ecd-0def-45c3-a6cb-0fd0ad145a15", "synopsis": "Brothers act as go-betweens for the town of Kingstown and its prisons, maintaining order amidst criminals, drug traffickers, and Russian mobsters.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8f7d6b13c80ac608f7936295e181ed8297c82b7e2a38a29eea2a45096ecb0bac.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8863d30c3aa6bb80797c479f3eccf8032e6aec91a4341dac61ccd20706c0fb1d.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/2c07cdd8f8515cbe6d3bd93cc9c83b92516245e6e00b1632e24686d723d0c940.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/b9310c41ed11ce7f27818c96b8953d36c33226c139e195da4280432a800c578d.jpg", "publicReleaseDate": 1641686400000, "overallRating": 4.0, "totalReviewCount": 2, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5cb91ecd-0def-45c3-a6cb-0fd0ad145a15", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_1"}, "refMarker": "hm_hom_c_e7ulE1_13_1", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5cb91ecd-0def-45c3-a6cb-0fd0ad145a15", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_1"}, "refMarker": "hm_hom_c_e7ulE1_13_1", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "Mayor Of Kingstown", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "The Summer I Turned Pretty - Season 1", "gti": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "transformItemId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "synopsis": "<PERSON>'s book-turned-series chronicles <PERSON><PERSON>'s eventful summer at Cousins Beach, marking her transition from childhood to womanhood.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Young Adult Audience", "Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b7a291cc0a1b1860d0f20e355964aa221d91bdd0593fec62f45beb5ad60529ca.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/8a2b3eb05a09d5af9513ba9cf50993e0bb200158c75651e04774b84fdca4d8d3.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/82fc93ec0c06dfc6b57fc10fce82c6f335cefb95742bf965c49133847220e3d5.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/b76400003c5bc461334996bbda76e01176c952d40642158731ead5fbf342ea9a.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/30fbbc1634e6f70c488b55ea96de20e9a88224544ac4503df204518173538ae5.jpg", "publicReleaseDate": 1652140800000, "overallRating": 4.3, "totalReviewCount": 465, "seasonNumber": 1, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_2"}, "refMarker": "hm_hom_c_e7ulE1_13_2", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.56ef5162-901b-4277-9bfa-c5166a89620a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_2"}, "refMarker": "hm_hom_c_e7ulE1_13_2", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "The Summer I Turned Pretty", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Yellowstone - Season 1", "gti": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "transformItemId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "synopsis": "Experience the riveting saga of America's largest ranching dynasty, the <PERSON><PERSON>s, as they confront deadly threats to their land and legacy.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Western", "Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/dbf03a1479ef5d14543281aa3c788ab4f3dbe5fcbecb10d55ee3f8c90af6de36.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c5276b7318402975e8512392fb231e927df8b6ea6a6060c8bf49740e09d57ad2.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7b5eb6cb1bbc8c7616e10ca0fa4deff24867a95e029ad12010e5038fa057b041.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/051be8166db035dc484bc736b3d50b4175da004dc4b7ff23a6a61455902b2e77.jpg", "publicReleaseDate": 1606176000000, "overallRating": 4.3, "totalReviewCount": 9, "seasonNumber": 1, "numberOfSeasons": 5, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_3"}, "refMarker": "hm_hom_c_e7ulE1_13_3", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime and Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBEÂ® winner"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.f2b1c3db-1a04-3ba1-4fbd-bc5ece959f2e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_3"}, "refMarker": "hm_hom_c_e7ulE1_13_3", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "Yellowstone", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "2 Broke Girls - Season 1", "gti": "amzn1.dv.gti.aeb778d5-a2f3-cbba-0923-45af813e958c", "transformItemId": "amzn1.dv.gti.aeb778d5-a2f3-cbba-0923-45af813e958c", "synopsis": "Experience the hilarious misadventures of <PERSON> and <PERSON>, two broke but spirited girls in Brooklyn's vibrant backdrop.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Comedy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/10dc814cf47ff6fbab249c7d97c81e0a3ebdaa2a6de46895e969834a8824f1cf.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6dc5af0a376fee0ded2bcb751750cabddbc32ca740540b32050d527799b9910c.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7bba6f0bb3fa619bd16e65f4a10a5203360132cf12f6e1c89337b9241a81a496.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/2419a94c96330cade076bc809e945bdb2f435b408c73b041e950a01095196aeb.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/1a9b622c189bdb12c6dd44b547a431a5f06d6715209ef224c0a22a72868ef864.jpg", "publicReleaseDate": 1316433600000, "seasonNumber": 1, "numberOfSeasons": 6, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.aeb778d5-a2f3-cbba-0923-45af813e958c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_4"}, "refMarker": "hm_hom_c_e7ulE1_13_4", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYSÂ® 2X nominee in 2017"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.aeb778d5-a2f3-cbba-0923-45af813e958c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_4"}, "refMarker": "hm_hom_c_e7ulE1_13_4", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "2 Broke Girls", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "The Royals", "gti": "amzn1.dv.gti.6f9dc5aa-d0f7-4c90-a642-e657a35d6202", "transformItemId": "amzn1.dv.gti.6f9dc5aa-d0f7-4c90-a642-e657a35d6202", "synopsis": "Staring <PERSON> as The Queen, The Royals follows the lives of a fictional British Royal family that inhabits a world of opulence and regal tradition - a world that also comes with a price tag of duty, destiny and intense public scrutiny.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d4ec1dc603c25369b11e90525206d129492926aea218a13a7338a1fe182e2085.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/424a73b28a13c85698c484e4ae14b6b5d822847418cd95cb1823fabcf1238552.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e00254cf4c889730dc167693d2113d07d916b0c3db8fd7f36464dd520b9cc449.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1431846000000, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.6f9dc5aa-d0f7-4c90-a642-e657a35d6202", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_5"}, "refMarker": "hm_hom_c_e7ulE1_13_5", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.6f9dc5aa-d0f7-4c90-a642-e657a35d6202", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_5"}, "refMarker": "hm_hom_c_e7ulE1_13_5", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "The Royals", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Germany Shore OG Staffel 1", "gti": "amzn1.dv.gti.8de9d724-737e-449d-bbf7-6dab5f85c2e3", "transformItemId": "amzn1.dv.gti.8de9d724-737e-449d-bbf7-6dab5f85c2e3", "synopsis": "In dieser Staffel werden Tommy, <PERSON><PERSON>, <PERSON><PERSON> und <PERSON><PERSON>. <PERSON><PERSON> will <PERSON> werden In Ni<PERSON> gehen <PERSON> und <PERSON><PERSON> mit ihren Freunden auf ein Festival. Hati nimmt einen Rap-Track auf und hat einen groÃen Auftritt.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "starringCast": [], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a998ea8d4b08fcdb4411edd01c882f293ba4998b992f4fdc65b8acd5af79c1f9.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/e5e30d107d3d937e7beea9b430702845677110ec1050ddc1666f0c174e181339.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/4cafcb2f2f430c8a826dd8c9f35368f28544d99a5954154b08034245ed62aa37.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/paramountplusde/logos/channels-logo-white._CB579401473_.png", "publicReleaseDate": 1731974400000, "overallRating": 3.3, "totalReviewCount": 3, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.8de9d724-737e-449d-bbf7-6dab5f85c2e3", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_6"}, "refMarker": "hm_hom_c_e7ulE1_13_6", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.8de9d724-737e-449d-bbf7-6dab5f85c2e3", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_6"}, "refMarker": "hm_hom_c_e7ulE1_13_6", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "Germany Shore OG", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1995}}}, "cardType": "TITLE_CARD"}, {"title": "Arrow  - Staffel 1", "gti": "amzn1.dv.gti.d8a9f6d5-b425-6591-5ed5-9ec141c6576d", "transformItemId": "amzn1.dv.gti.d8a9f6d5-b425-6591-5ed5-9ec141c6576d", "synopsis": "Nach einem katastrophalen SchiffsunglÃ¼ck wird der MilliardÃ¤r und Playboy Oliver Queen vermisst. FÃ¼nf Jahre lang hÃ¤lt man ihn fÃ¼r tot, bis er im Pazific auf einer entlegenen Insel lebend aufgefunden wird.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action"], "starringCast": [], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/52f65f1963b9db9c805f401694bcbf18a254cc103e7b36b689eff1e8c4cd39bd.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/468f79439d021fd2062356a1dc74afc95f331c46b8f230a73cc2f4a1973d4984.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c03e671111f38021485a8950dfd6e0e52dcdd105cd21817e03f2fb052f45bcc9.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1357516800000, "overallRating": 4.6, "totalReviewCount": 1015, "seasonNumber": 1, "numberOfSeasons": 6, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d8a9f6d5-b425-6591-5ed5-9ec141c6576d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_7"}, "refMarker": "hm_hom_c_e7ulE1_13_7", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d8a9f6d5-b425-6591-5ed5-9ec141c6576d", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_7"}, "refMarker": "hm_hom_c_e7ulE1_13_7", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "Arrow", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": true}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Bitten [OV]", "gti": "amzn1.dv.gti.efbd033c-cef6-4223-80d1-1be84a073f98", "transformItemId": "amzn1.dv.gti.efbd033c-cef6-4223-80d1-1be84a073f98", "synopsis": "<PERSON> and the Pack team up with a Coven of witches to fight against a mysterious foe determined to claim dominion over the Otherworld.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Science Fiction", "Horror"], "starringCast": ["<PERSON>", "Greyston Holt", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d886382a361db8fa3cd951a64d8709d01a1fc2e43cf5c3c754456bf00dc7ded2.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/51e1fe7488b10cab5f47cd635e9c850ce91a3f1afe484fca4fded3d2226e9df2.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/716290ed09336943be07b28412c02f5c3abcdcdfbd9042f2c3ee77eeb59d924f.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "publicReleaseDate": 1428724800000, "seasonNumber": 2, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.efbd033c-cef6-4223-80d1-1be84a073f98", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_8"}, "refMarker": "hm_hom_c_e7ulE1_13_8", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.efbd033c-cef6-4223-80d1-1be84a073f98", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_8"}, "refMarker": "hm_hom_c_e7ulE1_13_8", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "Bitten", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "My Lady <PERSON> - Season 1", "gti": "amzn1.dv.gti.e3d03023-4e3b-45f1-9988-0c404a6eb3db", "transformItemId": "amzn1.dv.gti.e3d03023-4e3b-45f1-9988-0c404a6eb3db", "synopsis": "Immerse yourself in a delightful historical world filled with romance, swashbuckling action, and undeniable chemistry.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Comedy", "Fantasy", "Romance", "Historical"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b20c6a98d7a7ea3bbbb23b25f27cde4c62cf00d9cf82a96e017aa7d5f94373cc.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6775ef7556a168cb76ab5dcb97c9ec67dd5c6ca2942eaf658074b743f3442e0a.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/fddebbe61bb6494bb3d2563c94d34290f2765e411f7cdea9b4757b0de800998d.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/9b6f6e2fb674ab78b927b66edcc9119d9fd77a589aa74f070ccbba23ebeea6c9.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e65a2722336c48126d88d968ffc0c6cad1adae9df2eded46bdeb59e7bd5c2edd.png", "publicReleaseDate": 1719446400000, "overallRating": 4.5, "totalReviewCount": 251, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e3d03023-4e3b-45f1-9988-0c404a6eb3db", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_9"}, "refMarker": "hm_hom_c_e7ulE1_13_9", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e3d03023-4e3b-45f1-9988-0c404a6eb3db", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_9"}, "refMarker": "hm_hom_c_e7ulE1_13_9", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "My Lady Jane", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Shooter - Staffel 1", "gti": "amzn1.dv.gti.eeb67cf1-0ea7-4aa1-9309-9cea87f93bac", "transformItemId": "amzn1.dv.gti.eeb67cf1-0ea7-4aa1-9309-9cea87f93bac", "synopsis": "Der frÃ¼here Marine Bob <PERSON> Swagger wird von seinem ehemaligen kommandierenden Offizier und jetzigen Secret-Service-Agent<PERSON> aus dem selbst gewÃ¤hlten Ruhestand geholt, um ein Attentat auf den US-PrÃ¤sidenten zu verhindern. Von einer geheimen Gruppierung wird er jedoch als SÃ¼ndenbock fÃ¼r den versuchten Mord gebrandmarkt.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Drama"], "starringCast": ["<PERSON>", "Shantel VanSanten", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/d62209566dec4d862b0046d4146fb236a2adaa9cb944967b8786bd94894ba113.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9d1586c2c464d641e52a55d36ffe36193375ac34b5092dfdebb657d3e3b538a1.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/bf7df0d9a0c4ea342c0375c478beef23fe0b1768458a94522a978b03f488b0f6.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/s-z/telemuenchen/logos/channels-logo-white._CB579599823_.png", "publicReleaseDate": 1485388800000, "overallRating": 4.4, "totalReviewCount": 3, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.eeb67cf1-0ea7-4aa1-9309-9cea87f93bac", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_10"}, "refMarker": "hm_hom_c_e7ulE1_13_10", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}, "contextualActions": [{"refMarker": "hm_hom_c_e7ulE1_13", "target": "notInterestedInRecommendation", "text": "Not Interested"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with your Filmtastic subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.eeb67cf1-0ea7-4aa1-9309-9cea87f93bac", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_e7ulE1_13_10"}, "refMarker": "hm_hom_c_e7ulE1_13_10", "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA=="}], "showName": "Shooter", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 324, "width": 1975, "scalarHorizontal": "subtle"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_e7ulE1_13", "ClientSideMetrics": "460|ClUKJ1NWT0RUVkFVQlJFQ1NZU0JhbmtzeUxpdmVEZWZhdWx0RGVmYXVsdBIQMToxMVhXOU4wN09GQUxTOBoQMjpEWUIxMDgwQTgzMzRFRSIGZTd1bEUxEjwKBGhvbWUSBGhvbWUiBmNlbnRlcioAMiQ0NDg2NTZmMS0wMmQ5LTQ0YWEtYTBkMS0zMmRlYzY0OTIxMzAaA2FsbCICdHYqA2FsbDIPZmFjZXRlZENhcm91c2VsOhFSZWNvbW1lbmRlZEZvcllvdUIRUmVjb21tZW5kZWRGb3JZb3VKE1ZpZGVvV2l6YXJkRWxpZ2libGVSCGVudGl0bGVkWgBiEFN0YW5kYXJkQ2Fyb3VzZWxoDXIAejhzSk9OaDg4UUY4OFNNOHg2Tkdjd0ZXVHZpY2pBY3R3RGpHNG9sLXlqV2JldEtqb3FRakxNamc9PYIBBHRydWWKAQCSAQA="}, "tags": [], "journeyIngressContext": "88|CgtmcmVld2l0aGFkcwoMdGVsZW11ZW5jaGVuCg9wYXJhbW91bnRwbHVzZGUSDHN1YnNjcmlwdGlvbhIEc3ZvZA==", "type": "STANDARD_CAROUSEL"}, {"facet": {}, "title": "Popular movies and TV â Free with ads", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjoxLCJucHNpIjoxMCwib3JlcSI6InNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09OjE3MzQ3MjEyODYwMDAiLCJhcE1heCI6MTAwLCJwcmV2SXRlbXMiOlsiNGJlNCIsIjY0YjUiLCIyNDllIiwiYjFhZCIsImE5N2MiLCI3MDYzIiwiZGU5ZiIsIjhlM2MiLCI4YzlmIiwiMjBmYSJdLCJzdHJpZCI6IjE6MTJWNzQ3TDdTT09PNEgjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUDI4Mzc3MzUyM2QxZDAwNWU4NTY3NDIxNjM3MjUyYmIxOGU3MTQ1MTU5NzQ4MzU0YzAzMmM0OTA2Nzk5YzQ1ZDRcIn0iLCJzdEtleSI6IntcInNic2luXCI6MCxcImN1cnNpemVcIjo3NjAsXCJwcmVzaXplXCI6MH0iLCJvcmVxayI6InJEUlUwcWhla0lqQWNiUk1pZFd6amc4T2kyNnNoWGtHQ01rY2xjY25EZlE9Iiwib3JlcWt2IjoxLCJleGNsVCI6W119", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMlY3NDdMN1NPT080SCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMlY3NDdMN1NPT080SCMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "offerType": "Subscription", "entitlement": "Entitled", "items": [{"title": "KÃ¼ss mich, <PERSON><PERSON><PERSON><PERSON>! [dt./OV]", "gti": "amzn1.dv.gti.ae85f81f-93db-4fed-b214-4447da8c4be4", "transformItemId": "amzn1.dv.gti.ae85f81f-93db-4fed-b214-4447da8c4be4", "synopsis": "<PERSON> Einzige, der <PERSON> zur BefÃ¶rderung auf den Chefposten in ihrer Literatur-Verlagsfirma im Weg steht, ist ihr smarter, attraktiver Kollege und Erzfeind Josh. Die beiden schlieÃen den Deal, dass der, der die BefÃ¶rderung nicht bekommt kÃ¼ndigt. Schnell mÃ¼ssen sich die beiden aber fragen, ob die BÃ¼rorivalitÃ¤t zwischen ihnen nicht nur ein Spiel ist, als beide plÃ¶tzlich doch mehr fÃ¼reinander empfinden.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e846bed9fb9a0f96bf5c719d1c1f6bae72d26eecda39b47c57cd435e5988db7e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9c36cc981905d2acf9fa03e313811f8912ede7cf1790f423a447c5075e17784a.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d91a27a4766be2cce508ca5408876d5eb1732783daa9f356709018e0f70fec88.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/d1eaaa883f2a61a3dd3acb6c7d976f6f0f682c1c30910f848699a6ce9bc4f17c.jpg", "publicReleaseDate": 1639094400000, "runtimeSeconds": 6194, "runtime": "103 min", "overallRating": 4.3, "totalReviewCount": 2055, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.ae85f81f-93db-4fed-b214-4447da8c4be4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_1"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_1", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.ae85f81f-93db-4fed-b214-4447da8c4be4", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_1"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_1", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON>, <PERSON> - FÃ¼r immer vielleicht [dt./OV]", "gti": "amzn1.dv.gti.6ca9f6cb-a3f1-d139-0d84-c0f5282d64b5", "transformItemId": "amzn1.dv.gti.6ca9f6cb-a3f1-d139-0d84-c0f5282d64b5", "synopsis": "Love, Rosie - FÃ¼r immer vielle<PERSON>t, ist eine bezaubernde Story Ã¼ber Freundschaft, Schicksal und die groÃe Liebe.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Romance", "Comedy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9449d8ae3f5cc1735d0980b2e0b3fc220bf02e18596a08ec8aadaaf79d7d9308.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/790d7a7000723a6629be59a4f0fd44edcf75591e6916fd78b5f5989995d07aff.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f1d48ce88a23fa92ce3891e32fef77819e7b8e6213c58b5f9d59afbb491794e9.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "publicReleaseDate": 1413504000000, "runtimeSeconds": 5915, "runtime": "98 min", "overallRating": 4.6, "totalReviewCount": 3714, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.6ca9f6cb-a3f1-d139-0d84-c0f5282d64b5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_2"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_2", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.6ca9f6cb-a3f1-d139-0d84-c0f5282d64b5", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_2"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_2", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON><PERSON> Christmas â Eine Hochzeit kommt selten allein", "gti": "amzn1.dv.gti.900daee9-b199-418c-b293-f21c6d9f249e", "transformItemId": "amzn1.dv.gti.900daee9-b199-418c-b293-f21c6d9f249e", "synopsis": "Als Hochzeitsplanerin Rachel von einem Influencer-PÃ¤rchen fÃ¼r die Organisation ihrer opulenten Hochzeit engagiert wird, trifft sie plÃ¶tzlich ihre erste groÃe <PERSON><PERSON> wieder, der sie einst sitzengelassen hat. Das Wiedersehen erinnert <PERSON>, was es bedeutet jemanden zu lieben und der Zauber der Weihnachtszeit Ã¶ffnet ihr verschlossenes Herz.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "0", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b97beba45941c25bb10bfc49530a3f3a987223896794432ed9649786949dd610.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/80b465aba713cb63e9dad5b2147a3fe2e1850e7126500fceb56e48884fa6fa8a.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/072876614b6df5984d6e4ddedea7fd52491a40baa926d967a7f29d65bc4acd61.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "publicReleaseDate": 1700697600000, "runtimeSeconds": 5521, "runtime": "92 min", "overallRating": 4.3, "totalReviewCount": 49, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.900daee9-b199-418c-b293-f21c6d9f249e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_3"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_3", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.900daee9-b199-418c-b293-f21c6d9f249e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_3"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_3", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON> und der traurige <PERSON>", "gti": "amzn1.dv.gti.a93cb482-1140-47b4-af11-5c2f1915b1ad", "transformItemId": "amzn1.dv.gti.a93cb482-1140-47b4-af11-5c2f1915b1ad", "synopsis": "Die 5-jÃ¤hrige Lucy liegt im Krankenhaus, da sie an einer unheilbaren Krankheit leidet. Si<PERSON> beschlieÃt, ein Bilderb<PERSON> zu malen, des<PERSON>gur (der âtraurige Mannâ) sie noch gar nicht kennt. <PERSON><PERSON><PERSON><PERSON> ihrer offenen, freundlichen Art macht Lucy sich daran, die positive Einstellung an alle im Krankenhaus weiterzugeben.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c8efde6583e9e9da0723bbe8e814ec4b83740c70dd6a3fe165f1652eac9cc38f.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/79f461717a2aa6d1dd7f2ccb1362bdaecac93f41268623cd2e860d9fa8283efb.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7bef422d6122963e2198f16a064421ee282088d1b5405587f97f1c11edc953d1.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/411cc788488d63c3e2a205b10c57135c2632edc96a0f9a3be58a86204e2a9374.jpg", "publicReleaseDate": 1630022400000, "runtimeSeconds": 5035, "runtime": "83 min", "overallRating": 4.6, "totalReviewCount": 144, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a93cb482-1140-47b4-af11-5c2f1915b1ad", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_4"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_4", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a93cb482-1140-47b4-af11-5c2f1915b1ad", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_4"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_4", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "Magic Beyond Words: Die zauberhafte Geschichte der J.K. Rowling", "gti": "amzn1.dv.gti.3abaf14e-3a76-e2fb-8ff3-9183f1bfa97c", "transformItemId": "amzn1.dv.gti.3abaf14e-3a76-e2fb-8ff3-9183f1bfa97c", "synopsis": "âMagic Beyond Words- Die zauberhafte Geschichte der J.K. Rowling\" zeigt das Ringen der britischen Autorin, zwischen der Situation als alleinerziehende Mutter und ihrem Traum, die geliebte âZaubererwelt\" zum Leben zu erwecken.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Documentary"], "starringCast": ["<PERSON><PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b5e289b30374ca2161bf311c65ef98a3682a19876afcc6a3644efc257d5ee20e.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/364bb100fb76494a16732ead97b2e4a2abe9cf4fd0882507eb93ec093c6252ad.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/1863ad87738070cb47b452f08eebd816023b4e284d1db16a8f69bdffaec07613.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/32211c5bc17fb9b0c9fac2020588c422d6a30a2293666dd7f79ac5f1a9befd25.jpg", "publicReleaseDate": 1310947200000, "runtimeSeconds": 4949, "runtime": "82 min", "overallRating": 4.1, "totalReviewCount": 117, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3abaf14e-3a76-e2fb-8ff3-9183f1bfa97c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_5"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_5", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3abaf14e-3a76-e2fb-8ff3-9183f1bfa97c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_5"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_5", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "EXmas", "gti": "amzn1.dv.gti.1c2a2055-9ffa-4c20-8c95-16f036157063", "transformItemId": "amzn1.dv.gti.1c2a2055-9ffa-4c20-8c95-16f036157063", "synopsis": "When <PERSON> decides to surprise his family by traveling home for Christmas, he is shocked to discover them already celebrating with an unexpected guest of honor, his ex-fiancÃ©e, <PERSON>. The two exes battle it out to see who the family will pick to stay through Christmas Day and who must go. Let the holiday chaos begin!", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Romance"], "starringCast": ["Leighton Meester", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7d73c0dd269af2773460a1da3a28a0176233a676d1323dd927f37e2f124827b4.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ede0da64d979865f1e51741f4784f0b342526794d6323024c01027394698a786.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e3654ee9d5d6eb9d34edda5bccef50625d2f62db0809c040c0b962a8fa3b5370.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/690736f308d4c9daac20ac8b8550e596d0dc389228b12c87f0492ab62b9bf3a9.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/164a5b4f81273276a49e7e716599856beabb003c85b8e2ac62754672ac54e0f9.jpg", "publicReleaseDate": 1697500800000, "runtimeSeconds": 5614, "runtime": "93 min", "overallRating": 3.2, "totalReviewCount": 46, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.1c2a2055-9ffa-4c20-8c95-16f036157063", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_6"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_6", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.1c2a2055-9ffa-4c20-8c95-16f036157063", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_6"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_6", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "Die Jagd nach dem Bernsteinzimmer", "gti": "amzn1.dv.gti.056dd049-e487-4364-92da-89289c69de9f", "transformItemId": "amzn1.dv.gti.056dd049-e487-4364-92da-89289c69de9f", "synopsis": "Als Schatzsucher Eik im Krankenhaus aufwacht, kann er sich an nichts mehr erinn<PERSON>, bis sich herausste<PERSON>t, dass er kurz vor dem Fund des legendÃ¤ren Bernsteinzimmers angeschossen wurde. <PERSON><PERSON> se<PERSON>, se<PERSON><PERSON> und der Enkelin Albert <PERSON> begibt er sich auf eine neue, noch aufregendere Suche. Niemand geringeres als das Forschergenie soll einst den Schatz vor den Nazis versteckt haben.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": false, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Drama"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "12", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4fb42b7470656a378d0d24fb0b36a27474cf31375c60a88236f13995a307b79d.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/e7b807477fbe6ab61a73fbf62619d73e1d2217276880936e1a1b30141c8dc48f.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "publicReleaseDate": 1347753600000, "runtimeSeconds": 6518, "runtime": "108 min", "overallRating": 4.2, "totalReviewCount": 207, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.056dd049-e487-4364-92da-89289c69de9f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_7"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_7", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.056dd049-e487-4364-92da-89289c69de9f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_7"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_7", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "Fire Country: Season 1", "gti": "amzn1.dv.gti.cfc611fd-2777-4a70-aeb6-03e36f308e3c", "transformItemId": "amzn1.dv.gti.cfc611fd-2777-4a70-aeb6-03e36f308e3c", "synopsis": "After making bad choices that landed him in prison, <PERSON><PERSON> joins a unique program fighting California wildfires for reduced sentences.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16+", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4989e4d219a37703c7ba8f46990aaeff8094385638cc256d5b94e73be89da612.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/1ba0ffa845dbc893b7aa38fd19c838702cff4692c8abe294830732cef58b0bec.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f03cc7d304f1b7bbe9ed14f9db954452e15e97f2376e86d18cfbe871b7a964c4.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "publicReleaseDate": 1684454400000, "overallRating": 3.0, "totalReviewCount": 2, "seasonNumber": 1, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.cfc611fd-2777-4a70-aeb6-03e36f308e3c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_8"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_8", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.cfc611fd-2777-4a70-aeb6-03e36f308e3c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_8"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_8", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "showName": "Fire Country", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "Die Magie der TrÃ¤ume", "gti": "amzn1.dv.gti.febb9fe7-403a-d663-88d7-008abc9e8c9f", "transformItemId": "amzn1.dv.gti.febb9fe7-403a-d663-88d7-008abc9e8c9f", "synopsis": "Die Geschwister Alice und Peter erleben im Wald die unglaublichsten Abenteuer â dank ihrer Fantasie. Doch die Idylle wÃ¤hrt nicht ewig: Nach einem Schicksalsschlag sind Mutter Rose (<PERSON>) und Vater Jack verzweifelt. Die Kinder wollen ihren Eltern helfen und machen sich auf eine Reise in die Unterwelt Londons. Dort begegnen sie dem Hutmacher, der HerzkÃ¶nigin und einigen realen Schurkenâ¦", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "6", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Adventure", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "6", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/0e5f8dc119db9445c23e4e6f24d386c860384245c44d7a88eee9d9eda03f2b8e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4600c9c62b32b447a3ea8ecb4f950453e7d623d1dedfbe385a5f2ab106bea0eb.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c2b3ad6014729a3dddd90413c38d8736b6b94ae64acd6cd36011488007389d29.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "publicReleaseDate": 1616112000000, "runtimeSeconds": 5671, "runtime": "94 min", "overallRating": 3.7, "totalReviewCount": 637, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.febb9fe7-403a-d663-88d7-008abc9e8c9f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_9"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_9", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.febb9fe7-403a-d663-88d7-008abc9e8c9f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_9"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_9", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": true}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}, {"title": "Die Jagd nach dem Schatz der Nibelungen", "gti": "amzn1.dv.gti.883616ab-13a1-4096-9541-7740778420fa", "transformItemId": "amzn1.dv.gti.883616ab-13a1-4096-9541-7740778420fa", "synopsis": "Bei der besessenen Suche nach dem Schatz der Nibelungen hat Eik vor Jahren seine Frau und seinen besten Freund verloren. Seitdem hat er den Traum aufgegeben und lebt zurÃ¼ckgezogen mit seiner Tochter - bis sich plÃ¶tzlich eine Spur auftut, die ihn in ein neues Abenteuer rund um den sagenumwobenen Schatz aus mythischen Zeiten hineinzieht. Mit zwei Freunden beginnt eine die Suche quer durch Europa.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Drama"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "12", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/0f4cc5ecb3c488e7153fce1b1001954fc36d333b26bc9e0fa72faca1824e7815.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/f954eb72ff7dee82fa2bbae80fdd40647706865275eef335a12352227378fcfb.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB538065731_.png", "publicReleaseDate": 1220140800000, "runtimeSeconds": 7192, "runtime": "119 min", "overallRating": 4.3, "totalReviewCount": 190, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.883616ab-13a1-4096-9541-7740778420fa", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_10"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_10", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch for free", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.883616ab-13a1-4096-9541-7740778420fa", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_7kKBIy_brws_14_10"}, "refMarker": "hm_hom_c_7kKBIy_brws_14_10", "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_7kKBIy_14", "ClientSideMetrics": "456|CloKLERFQVZPRElUVlBWTEVBRElOR0NBUk9VU0VMTGl2ZURlZmF1bHREZWZhdWx0EhAxOjEyVjc0N0w3U09PTzRIGhAyOkRZMzY0M0UzMUNFRDQxIgY3a0tCSXkSPAoEaG9tZRIEaG9tZSIGY2VudGVyKgAyJDQ0ODY1NmYxLTAyZDktNDRhYS1hMGQxLTMyZGVjNjQ5MjEzMBoMc3Vic2NyaXB0aW9uIgNhbGwqC2ZyZWV3aXRoYWRzMg9mYWNldGVkQ2Fyb3VzZWw6BkJyb3dzZUIOQnJvd3NlU3RyYXRlZ3lKCGhlcmN1bGVzUghlbnRpdGxlZFoAYhBTdGFuZGFyZENhcm91c2VsaA5yAHo4c0pPTmg4OFFGODhTTTh4Nk5HY3dGV1R2aWNqQWN0d0RqRzRvbC15aldiZXRLam9xUWpMTWpnPT2CAQR0cnVligEAkgEA"}, "tags": [], "journeyIngressContext": "36|CgtmcmVld2l0aGFkcxIMc3Vic2NyaXB0aW9u", "type": "STANDARD_CAROUSEL"}, {"title": "Top 10 in Germany", "facet": {}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6_ioRob21li4Rob21ljI6qMToxMVpCMzNTTFMyVE5BSSMjTVpRV0daTFVNVlNFR1lMU041MlhHWkxNjQ-OglYy", "offerType": "SVOD", "entitlement": "Entitled", "items": [{"title": "Red One", "gti": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "transformItemId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "synopsis": "After <PERSON> aka RED ONE â is kidnapped, the North Pole's Head of Security teams up with an infamous bounty hunter to save <PERSON>.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy", "Action", "Adventure"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/4cf72f249c3520bf05438824a039bfceaab19d21b533d20c2165829384561f1d.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/115800d972e291ce343fea1f88af9bc27fdb2c437d82608ce183b86c66b70de2.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/cd805cfff16808f6deef99c583bd46da4b035a9e5ff3907bd651ac255235d4f1.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/a2a9006c929d5f3c1d29104fb61dfa767a975f4a779a39a38bd1780f1749f920.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f96f371b2d8b70a1e1c1f44f0ddc0aa07f83fa7bf7e61e5f671d3fb74be00e35.jpg", "publicReleaseDate": 1733961600000, "runtimeSeconds": 7475, "runtime": "124 min", "overallRating": 4.0, "totalReviewCount": 218, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_1"}, "refMarker": "hm_hom_c_hbjydd_brws_15_1", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#1 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d2fcaffb-0740-430f-8abb-cbdb00347239", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_1"}, "refMarker": "hm_hom_c_hbjydd_brws_15_1", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Secret Level - Season 1", "gti": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "transformItemId": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "synopsis": "SECRET LEVEL is a new animated anthology series featuring original stories set within beloved video game worlds.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Adventure", "Animation"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f39a5161a21e12347afb4ddfbbc0f109486c79b9b659ffea83921e130828c042.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ee9810d6ea0357473605272ace805415a3fa8d935f4132e52b4d0f9ad0a5604c.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/025cdb93817b5e3cfee3106dec7db006a4733a4729618fd8c40c40d36ce1d106.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/93a1599ba87c6345fa7bdbcabe4686a305738c8a7fa7c5036d7a9b9eb3c577b2.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/6fe4c1c1aab1bf6d6f47b697a6aa3e9dfecb593deb9ff6a0477f0f8dd3aa168b.png", "publicReleaseDate": 1734393600000, "overallRating": 4.5, "totalReviewCount": 228, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_2"}, "refMarker": "hm_hom_c_hbjydd_brws_15_2", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#2 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.3db91f6b-33cc-44eb-a65d-b53b55a5b42e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_2"}, "refMarker": "hm_hom_c_hbjydd_brws_15_2", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Secret Level", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Beast Games - Season 1", "gti": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "transformItemId": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "synopsis": "In this high-stakes competition series, 1,000 participants battle for the largest TV prize purse ever: $5 million, plus extraordinary bonus prizes.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Unscripted"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ce689202927ce880eec6ca93e6462c69c5568d7aff03ec27d4402de05aa3e619.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/229f1846461068621910c94d319a85bfdb1e409a05cf1dae3f67fa8b6588e281.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/dba04e61f844b918594d6884cb4575bb9bb11426908d391c446aa3d21a6983fa.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/d2f0589bec1d7e54f6bbde9987a38e740bebe2fd07ead21a7757788aac3395e2.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/95845da262e4d43f977c705ad5ef9b7f6c65ae43ade9fd953b80210cbc21a094.png", "publicReleaseDate": 1734566400000, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_3"}, "refMarker": "hm_hom_c_hbjydd_brws_15_3", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "New episode Thursday"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.ef87b53e-6595-4fc4-b949-3789a8a39672", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_3"}, "refMarker": "hm_hom_c_hbjydd_brws_15_3", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Beast Games", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Die Discounter - Staffel 4", "gti": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "transformItemId": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "synopsis": "DIE 4 FINALEN FOLGEN AB 23.12.24!\n\t\nEs herrscht ein neuer Wind im Kolinski Billstedt: Zum ersten Mal macht Thorsten zum Stolz von Pina einen richtig guten Job als Filialleiter. Auch bei der Belegschaft lÃ¤uft's: <PERSON> en<PERSON>t den KlimaschÃ¼tzer in sich und <PERSON> wird zum <PERSON>. In all dem Chaos struggelt Titus damit, Lia aus dem fernen Kolinski Eppendorf weiterhin fÃ¼r sich zu gewinnen.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Comedy"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cd4968d32a69f1668ea969d3e1c432246d629d99ca51884202e3eadb4b9d06ce.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/bd164e57c186b3f661de319777cf77f0adbc864d1cae2dee6ffa2a4b626db971.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/3845a58e253b346774c662dddf9a335c5a77296029141eb46bdb1a54b8abbff3.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7807a621d03743920f5e79637246211af9e3ed3e6f0dddcd886a94072033d507.jpg", "publicReleaseDate": 1734912000000, "overallRating": 3.4, "totalReviewCount": 197, "seasonNumber": 4, "numberOfSeasons": 4, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_4"}, "refMarker": "hm_hom_c_hbjydd_brws_15_4", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#4 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a1c6f976-cd01-4a5f-a31e-5e4a6948bc9b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_4"}, "refMarker": "hm_hom_c_hbjydd_brws_15_4", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Die Discounter", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "The Grinch", "gti": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "transformItemId": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "synopsis": "Illumination's <PERSON> Grinch chronicles a cynical creature's mission to steal Christmas, until a young girl's generosity melts his heart.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "0", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Animation", "Comedy", "Kids", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3bbfb93823a638d452accf9a4f074c6141736a3a7cb3765b805e3fcd9e374d58.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/996e9861988631a0e2dd9ed5c71b24cf5a63c97b8fdbacf8adeac15eaf11ac53.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/fde4186ead1cc89035c6dc6c326e40e3871432ced4cb196c744dc011dad62fd2.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/7bd7c2cbac1e069513cbae6ac55ca06599fa59180199d434abcad8dc2e38cdff.jpg", "publicReleaseDate": 1543449600000, "runtimeSeconds": 4885, "runtime": "81 min", "overallRating": 4.7, "totalReviewCount": 7095, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_5"}, "refMarker": "hm_hom_c_hbjydd_brws_15_5", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#5 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.bcb3c57a-7581-7d30-af92-0db26d4103cd", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_5"}, "refMarker": "hm_hom_c_hbjydd_brws_15_5", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Cross - Season 1", "gti": "amzn1.dv.gti.2d4ade33-2c00-49c6-aba8-ab96fc6bff2f", "transformItemId": "amzn1.dv.gti.2d4ade33-2c00-49c6-aba8-ab96fc6bff2f", "synopsis": "Brilliant psychologist <PERSON> investigates America's most twisted minds while safeguarding his loved ones from the criminal underworld.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Suspense", "Action", "Drama"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/1d8b7b8caa828463e62aa0efd802372d5713212320bd0a1cfb8de3030ba4fb14.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/7672da4cc90b458ecc95aed14697e3d84b8b238156248193266417bd4ce7be30.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/74ccc280eb32954bac47c8e98508d7e612b66e92b3f25885f9c040171c34c12d.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/eafbdc73d5108ad3f592b516e38ae7ee5445a1b61a5625054074804adb0eb269.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/74bb25a842ae4a39745c112f8dca0bfd18ea12e46463702ac3dbe74cda90f50f.jpg", "publicReleaseDate": 1731542400000, "overallRating": 3.7, "totalReviewCount": 272, "seasonNumber": 1, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2d4ade33-2c00-49c6-aba8-ab96fc6bff2f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_6"}, "refMarker": "hm_hom_c_hbjydd_brws_15_6", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#6 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2d4ade33-2c00-49c6-aba8-ab96fc6bff2f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_6"}, "refMarker": "hm_hom_c_hbjydd_brws_15_6", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "Cross", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Fast X", "gti": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "transformItemId": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "synopsis": "A lethal adversary from <PERSON> and his family's past reappears, determined to obliterate their family and everything <PERSON> cherishes.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Suspense"], "starringCast": ["Vin Diesel", "<PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3463f5c24f64b5fc877fb051c39fcae1ed5f96be284aea94ee829abc5bd57483.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/3eca0741c4b92a137ef23fd966b314a9570e08889d25503f8e40751f3d96264b.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ab6ea0638f3318ab529eb73231e84d1ecd4804405d05ff53275a83ff3eac2bc8.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c4c743c1aa9e18d57fd64ba385cc9794288714f14aab537cbc515ac5e43f4bea.jpg", "publicReleaseDate": 1684281600000, "runtimeSeconds": 8451, "runtime": "140 min", "overallRating": 4.6, "totalReviewCount": 2988, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_7"}, "refMarker": "hm_hom_c_hbjydd_brws_15_7", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#7 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.10204e8c-e8de-47f5-9fa2-c068b8be597a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_7"}, "refMarker": "hm_hom_c_hbjydd_brws_15_7", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Renfield", "gti": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "transformItemId": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "synopsis": "Das BÃ¶se Ã¼berdauert die Ewigkeit nicht ohne ein bisschen Hilfe: In dieser modernen Monstergeschichte Ã¼ber Draculas treuen Diener schlÃ¼pft <PERSON> in die Rolle von <PERSON>, dem gepeinigten Handlanger des grÃ¶Ãten Narzissten unter den Vampiren -- Dracula.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Comedy", "Fantasy", "Horror"], "starringCast": ["<PERSON>", "<PERSON>", "Awkwa<PERSON><PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/f362998a4c5b0b423a4dfcf7def673c405d7db72c5fda90fc7ac1f167cee8f60.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/cd84bb530b8ee860a2f3129dc8595c4190db85f631c0887714a0bfa36219ed63.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ca619e6440094badeaef558ef7b27cc376752ee388d40e180f6c59aee3732be0.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c92b4129d177a59ecd0772f89a89ab280fab69c76a03ecc643fae00ac949cdc0.jpg", "publicReleaseDate": 1684972800000, "runtimeSeconds": 5630, "runtime": "93 min", "overallRating": 4.0, "totalReviewCount": 7691, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_8"}, "refMarker": "hm_hom_c_hbjydd_brws_15_8", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#8 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5f5b15ce-f855-44c2-9e04-c0715c6c3cc3", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_8"}, "refMarker": "hm_hom_c_hbjydd_brws_15_8", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Redemption - Stunde der Vergeltung", "gti": "amzn1.dv.gti.a4bacf79-5d4a-5754-3aba-d8e69df4075c", "transformItemId": "amzn1.dv.gti.a4bacf79-5d4a-5754-3aba-d8e69df4075c", "synopsis": "Nachdem er Zeuge wurde, wie seine Einheit in Afghanistan ausgelÃ¶scht wurde, lebt <PERSON> in den StraÃen Londons. Seine schwangere Freundin wird tot aus der Themse geborgen. Durch seine Verbindung zur chinesischen Mafia muss Joey die Verantwortlichen finden und Rache Ã¼ben, auch wenn er dabei seine eigenen Prinzipien Ã¼ber Bord werfen muss.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/540ddcbd0192bf7981e859bce0b835957c32359a15cfb87904908a6bde264be4.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/6b5a7f740253f4b9f2e208110878c569f75fe332520a3c4b1d637523b9488020.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/2036d0e7d1fe34e4208866dfe392993fbe2d17dc309d1406bc09b78f681efbbe.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1365984000000, "runtimeSeconds": 5993, "runtime": "99 min", "overallRating": 4.4, "totalReviewCount": 2818, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a4bacf79-5d4a-5754-3aba-d8e69df4075c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_9"}, "refMarker": "hm_hom_c_hbjydd_brws_15_9", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#9 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a4bacf79-5d4a-5754-3aba-d8e69df4075c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_9"}, "refMarker": "hm_hom_c_hbjydd_brws_15_9", "journeyIngressContext": "8|EgRzdm9k"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "From Season 1", "gti": "amzn1.dv.gti.2fa088f3-f916-4550-ac6f-6c11f6af0a1c", "transformItemId": "amzn1.dv.gti.2fa088f3-f916-4550-ac6f-6c11f6af0a1c", "synopsis": "A chilling tale of a town that entraps visitors, forcing them to navigate the perils of the enigmatic forest while maintaining semblance of normalcy.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Horror", "Science Fiction", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "Catalina <PERSON>ino Moreno"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/917d0a81682084345badff5a05eed04f07f7d8894112852bf248510288c9eac5.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/209bf81e7b8cef8ad9af24f4122776e8600120b72703fd192f87f87e8410f4c9.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/07c377138a10598514954af76e3994ce59719e1210669af5f545becd5b13f796.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1670457600000, "overallRating": 3.9, "totalReviewCount": 27, "seasonNumber": 1, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2fa088f3-f916-4550-ac6f-6c11f6af0a1c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_10"}, "refMarker": "hm_hom_c_hbjydd_brws_15_10", "journeyIngressContext": "8|EgRzdm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime and Paramount+ subscription", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "#10 in Germany", "icon": "TRENDING_ICON"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2fa088f3-f916-4550-ac6f-6c11f6af0a1c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_hbjydd_brws_15_10"}, "refMarker": "hm_hom_c_hbjydd_brws_15_10", "journeyIngressContext": "8|EgRzdm9k"}], "showName": "From", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_hbjydd_15", "ClientSideMetrics": "404|Cl8KMUVVNlNWT0RUb3AxMENoYXJ0c1dlYkNsZWFuU2xhdGVMaXZlRGVmYXVsdERlZmF1bHQSEDE6MTFaQjMzU0xTMlROQUkaEDI6RFk4RTY4NTMzRkZBOUUiBmhianlkZBI8CgRob21lEgRob21lIgZjZW50ZXIqADIkNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwGgRzdm9kIgNhbGwqADIPZmFjZXRlZENhcm91c2VsOgZCcm93c2VCCVRvcENoYXJ0c1IIZW50aXRsZWRaAGIGQ2hhcnRzaA9yAHo4c0pPTmg4OFFGODhTTTh4Nk5HY3dGV1R2aWNqQWN0d0RqRzRvbC15aldiZXRLam9xUWpMTWpnPT2CAQR0cnVligEAkgEA"}, "tags": [], "journeyIngressContext": "8|EgRzdm9k", "type": "CHARTS"}, {"facet": {}, "title": "Watch it again", "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiNDQ4NjU2ZjEtMDJkOS00NGFhLWEwZDEtMzJkZWM2NDkyMTMwIiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoxMCwib3JlcSI6InNKT05oODhRRjg4U004eDZOR2N3RldUdmljakFjdHdEakc0b2wteWpXYmV0S2pvcVFqTE1qZz09OjE3MzQ3MjEyODYwMDAiLCJhcE1heCI6ODMsInByZXZJdGVtcyI6WyJkNDkwIiwiZGU5YyIsIjllNWEiLCI3Yjk0IiwiMjk0YiIsImE1MzUiLCI5MWY3IiwiYTQ0MiIsIjc3YTIiLCI1MTU2Il0sInN0cmlkIjoiMToxRFFMNEVBM1VWUDAxIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE0iLCJhdXRvYm90Ijoie1wicnNpcFN0YXRlSWRcIjpcIlJTSVAyODM3NzM1MjNkMWQwMDVlODU2NzQyMTYzNzI1MmJiMThlNzE0NTE1OTc0ODM1NGMwMzJjNDkwNjc5OWM0NWQ0XCJ9Iiwib3JlcWsiOiJyRFJVMHFoZWtJakFjYlJNaWRXempnOE9pMjZzaFhrR0NNa2NsY2NuRGZRPSIsIm9yZXFrdiI6MSwiZXhjbFQiOltdfQ==", "startIndex": 10, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxRFFMNEVBM1VWUDAxIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}, "id": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4Rob21ljI6pMToxRFFMNEVBM1VWUDAxIyNNWlFXR1pMVU1WU0VHWUxTTjUyWEdaTE2ND46CVjI=", "offerType": "Mixed", "entitlement": "Mixed", "items": [{"title": "Grimm - Staffel 6 [dt./OV]", "gti": "amzn1.dv.gti.b4b1feb7-247b-8c4b-94ac-36526a68d490", "transformItemId": "amzn1.dv.gti.b4b1feb7-247b-8c4b-94ac-36526a68d490", "synopsis": "<PERSON><PERSON> sind die ruchlosen MÃ¤chte von Schwarzkralle zum Schweigen gebracht, aber nun steht <PERSON> in Captain <PERSON><PERSON> erneut ein allzu vertrauter Feind gegenÃ¼ber. Der ist inzwischen als BÃ¼rgermeister zu neuer Macht gelangt und legt nun die G<PERSON>tze so aus, dass sie ihm von <PERSON> sind.", "episodicSynopsis": "Serienfinale: <PERSON> muss bei seinen Vorfahren die Kraft suchen, um die Welt vor einem grausamen Ende zu bewahren.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama", "Science Fiction", "Horror", "Fantasy"], "starringCast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/0c4a686b6e211ee19a601958b53bbdf237aa111120f988fd6fedf91dd0a38f73.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/4bb1ce28d4f136d7a98f0f47613b616fff0d9de5211f48c7a9c80eb3c0553732.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/25f843dbbedfb2fe0e7da4e3181b587fb387180d276e89714181ca071b7765d4.jpg", "publicReleaseDate": 1490918400000, "overallRating": 4.8, "totalReviewCount": 3657, "seasonNumber": 6, "numberOfSeasons": 6, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.b4b1feb7-247b-8c4b-94ac-36526a68d490", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_1"}, "refMarker": "hm_hom_c_ExgnYj_16_1", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYSÂ® 1X nominee in 2014"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.b4b1feb7-247b-8c4b-94ac-36526a68d490", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_1"}, "refMarker": "hm_hom_c_ExgnYj_16_1", "journeyIngressContext": "8|EgNhbGw="}], "showName": "<PERSON> [dt./OV]", "episodeNumber": 13, "watchNextType": "CONTINUE", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": true}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "In The Land of Saints and Sinners", "gti": "amzn1.dv.gti.8085225f-0a4f-44f0-8048-0f7f6973de9c", "transformItemId": "amzn1.dv.gti.8085225f-0a4f-44f0-8048-0f7f6973de9c", "synopsis": "In a remote Irish village, a damaged <PERSON><PERSON> is forced to fight for redemption after a lifetime of sins, but what price is he willing to pay? In the land of saints and sinners, some sins can't be buried", "episodicSynopsis": "In a remote Irish village, a damaged <PERSON><PERSON> is forced to fight for redemption after a lifetime of sins, but what price is he willing to pay? In the land of saints and sinners, some sins can't be buried", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/b5163d5250a6b49ae4639e2cf4ddc6958d6ef9e2fd1d584fdcee23827f1ac13a.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/081356a9dc784955cb01c6288a78343521b33e1951fb1b2566ace3f8ffb51577.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/53529625ad9790463ed918fe210e27611ef61a61fe31f31682ceff117e645538.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c1dd8da43e2692ac5191cfb4a3545f0d582d3044628cf4c2af401a955b8b1609.jpg", "publicReleaseDate": 1730332800000, "runtimeSeconds": 6413, "runtime": "106 min", "overallRating": 4.0, "totalReviewCount": 411, "watchProgress": 0.97, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.8085225f-0a4f-44f0-8048-0f7f6973de9c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_2"}, "refMarker": "hm_hom_c_ExgnYj_16_2", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.8085225f-0a4f-44f0-8048-0f7f6973de9c", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_2"}, "refMarker": "hm_hom_c_ExgnYj_16_2", "journeyIngressContext": "8|EgNhbGw="}], "watchNextType": "UNKNOWN", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "One Fast Move", "gti": "amzn1.dv.gti.cc1d1dd2-d3d5-4b8d-856e-49a2877c9e5a", "transformItemId": "amzn1.dv.gti.cc1d1dd2-d3d5-4b8d-856e-49a2877c9e5a", "synopsis": "One Fast Move is an action-adventure thrill ride about a young man down on his luck who seeks out his estranged father to help him pursue his dream of becoming a professional motorcycle racer. With the help of his small town love interest and a motorcycle shop owner who moonlights as his mentor, he begins to break down the walls that his fatherâs absence had built up.", "episodicSynopsis": "One Fast Move is an action-adventure thrill ride about a young man down on his luck who seeks out his estranged father to help him pursue his dream of becoming a professional motorcycle racer. With the help of his small town love interest and a motorcycle shop owner who moonlights as his mentor, he begins to break down the walls that his fatherâs absence had built up.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Romance", "Drama"], "starringCast": ["K.J. <PERSON>", "<PERSON>", "Maia <PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/12ee9936ddb16a4adf22baad38cfd265843368e1902fe0d592c3b919bd0ebd93.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/66c8671f18f3cbdc78749869ec93dd52715ad52bf510ccbe9a913482ff86bcfd.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/e0e4eeb894edf810e798b11889dadca0394e747b5aa5bf26deb8e7965854d714.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/ca99b49f4529e29a42b8c78dd0a30b4994211e39a9fb74556671dea9309e495b.png", "publicReleaseDate": 1723075200000, "runtimeSeconds": 6468, "runtime": "107 min", "overallRating": 3.0, "totalReviewCount": 45, "watchProgress": 0.94, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.cc1d1dd2-d3d5-4b8d-856e-49a2877c9e5a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_3"}, "refMarker": "hm_hom_c_ExgnYj_16_3", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.cc1d1dd2-d3d5-4b8d-856e-49a2877c9e5a", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_3"}, "refMarker": "hm_hom_c_ExgnYj_16_3", "journeyIngressContext": "8|EgNhbGw="}], "watchNextType": "UNKNOWN", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "After Everything", "gti": "amzn1.dv.gti.aed03e6e-602a-4caa-bae5-786ceefd7b94", "transformItemId": "amzn1.dv.gti.aed03e6e-602a-4caa-bae5-786ceefd7b94", "synopsis": "The fifth and final installment of the AFTER franchise sees the emotional culmination of <PERSON><PERSON> and <PERSON>'s timeless romance. For a couple that's been through it all, only one question remains: what happens After Everything?", "episodicSynopsis": "The fifth and final installment of the AFTER franchise sees the emotional culmination of <PERSON><PERSON> and <PERSON>'s timeless romance. For a couple that's been through it all, only one question remains: what happens After Everything?", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "12", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Romance"], "starringCast": ["<PERSON>", "<PERSON>"], "maturityRatingString": "12", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8ea056894055a6ea3bd9dd7bc5b189e216bdfbb9e63089aab47cbd796b094ec8.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ef836a94ece0cb981fe2867a6195fa89ea9791434a12113050007c28ab90a9c6.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/9fd60ae212e02e3f03177e54bb01e3ef20ce10d894adf8649d9cb11cee90496a.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/47f1407fa1bd173c930ef9e67131a2159acea6caf91249180e701277e386551b.jpg", "publicReleaseDate": 1696291200000, "runtimeSeconds": 5604, "runtime": "93 min", "overallRating": 2.8, "totalReviewCount": 325, "watchProgress": 0.94, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.aed03e6e-602a-4caa-bae5-786ceefd7b94", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_4"}, "refMarker": "hm_hom_c_ExgnYj_16_4", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.aed03e6e-602a-4caa-bae5-786ceefd7b94", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_4"}, "refMarker": "hm_hom_c_ExgnYj_16_4", "journeyIngressContext": "8|EgNhbGw="}], "watchNextType": "UNKNOWN", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": true}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": true}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "<PERSON> - MÃ¤nner sind faul, sagen die Frauen", "gti": "amzn1.dv.gti.c2b777fe-234b-ea79-9925-94115e49294b", "transformItemId": "amzn1.dv.gti.c2b777fe-234b-ea79-9925-94115e49294b", "synopsis": "Zwei Stunden mit Vollgas auf einer Reise durch die AbsurditÃ¤ten des Alltags, ausgedehnte Lachsalven und zum Finale ein Feuerwerk: <PERSON>, der King of Comedy lÃ¤sst es in seinem neuen BÃ¼hnenprogramm âMÃ¤nner sind faul, sagen die Frauenâ wieder so richtig krachen.", "episodicSynopsis": "Zwei Stunden mit Vollgas auf einer Reise durch die AbsurditÃ¤ten des Alltags, ausgedehnte Lachsalven und zum Finale ein Feuerwerk: <PERSON>, der King of Comedy lÃ¤sst es in seinem neuen BÃ¼hnenprogramm âMÃ¤nner sind faul, sagen die Frauenâ wieder so richtig krachen.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "0", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Comedy"], "starringCast": ["<PERSON>"], "maturityRatingString": "0", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/5aebaa75c0087d2ee4d95dfbacadd568e0a5c044e0f5a5a4c539ad4f6c77af86.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/b47dcc6871f79eecf41192cb483b6d20cdc49958b19adc081f25e7673a132cee.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/5a32c81688988c652db161372ee3f4b836a8525728ade8dca91180d170b6fb8d.jpg", "publicReleaseDate": 1575590400000, "runtimeSeconds": 9089, "runtime": "151 min", "overallRating": 4.6, "totalReviewCount": 3681, "watchProgress": 0.97, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.c2b777fe-234b-ea79-9925-94115e49294b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_5"}, "refMarker": "hm_hom_c_ExgnYj_16_5", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "You purchased this", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.c2b777fe-234b-ea79-9925-94115e49294b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_5"}, "refMarker": "hm_hom_c_ExgnYj_16_5", "journeyIngressContext": "8|EgNhbGw="}], "watchNextType": "UNKNOWN", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Reacher - Season 2", "gti": "amzn1.dv.gti.5c2c1110-ccb6-4922-9690-1193f46fa535", "transformItemId": "amzn1.dv.gti.5c2c1110-ccb6-4922-9690-1193f46fa535", "synopsis": "When members of <PERSON><PERSON><PERSON> old military unit start turning up dead, <PERSON> has just one thing on his mindârevenge.", "episodicSynopsis": "<PERSON> and <PERSON><PERSON><PERSON> make a final desperate attempt to save <PERSON><PERSON><PERSON><PERSON> and <PERSON>, stop A.M. and avenge their friendsâ murder.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/bce3012744e145ee428361c5fd3cf0fa81f8760b7696d99505a24eab855c3018.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/9bb7352274f306a5a2de7ad7e173e0e66c90f94a0b65fc1e6c5984f388f9b050.png", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/002742c147a08d0ef24ee7b61c438964f612eb986f3d0dffc91e7c6edfd26af7.png", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/707d5487243d7ae503c0fce964b909cf06884b193f5673c2781f12d9a63b400c.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/9fbf4ff8741c6d895a3560108603b01ac24bcfeaaaff69c74234a1612954822b.png", "publicReleaseDate": 1643932800000, "overallRating": 4.3, "totalReviewCount": 913, "seasonNumber": 2, "numberOfSeasons": 3, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.5c2c1110-ccb6-4922-9690-1193f46fa535", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_6"}, "refMarker": "hm_hom_c_ExgnYj_16_6", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.5c2c1110-ccb6-4922-9690-1193f46fa535", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_6"}, "refMarker": "hm_hom_c_ExgnYj_16_6", "journeyIngressContext": "8|EgNhbGw="}], "showName": "REACHER (TV)", "episodeNumber": 8, "watchNextType": "CONTINUE", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Person of Interest: <PERSON> komple<PERSON> - Staffel 5", "gti": "amzn1.dv.gti.8cacec19-128c-4df7-63e1-5e5d1e9c91f7", "transformItemId": "amzn1.dv.gti.8cacec19-128c-4df7-63e1-5e5d1e9c91f7", "synopsis": "Obwohl das POI-Team dank der Allwissenheit der Maschine viele Verbrechen verhindert hat, konnten sie nicht verhindern, dass die rivalisierende K.I. Samaritan online geht und den Rest der Maschine zerstÃ¶ren will. Das Team muss auf der Flucht und ohne die Daten der Maschine einen Weg finden, <PERSON><PERSON> zu retten, als sie versuchen, die Maschine neu zu starten, und sich gegen UnÃ¼berwindbares wehren.", "episodicSynopsis": "Im Serienfinale begibt sich das POI-Team auf eine letzte Mission, um Samaritan daran zu hindern, die <PERSON>schine zu zerstÃ¶ren und seine Macht Ã¼ber die Menschheit zu verstÃ¤rken.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Action", "Suspense", "Drama", "Science Fiction"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/a262fec9678ada730be1f86e9faed075aba1463b1e14b4e12c5adafb08c58ad4.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/64fb41429da80a3dd4c8e65780e3d3bc309c1a7e423e47e34e89b24a45ca63d6.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/9641fec92c04703b122016aa32782b01f6481272f43f46cd72755ffba83f9aa7.jpg", "publicReleaseDate": 1466467200000, "overallRating": 4.8, "totalReviewCount": 843, "seasonNumber": 5, "watchProgress": 0.99, "numberOfSeasons": 5, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.8cacec19-128c-4df7-63e1-5e5d1e9c91f7", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_7"}, "refMarker": "hm_hom_c_ExgnYj_16_7", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.8cacec19-128c-4df7-63e1-5e5d1e9c91f7", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_7"}, "refMarker": "hm_hom_c_ExgnYj_16_7", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Person of Interest", "episodeNumber": 13, "watchNextType": "CONTINUE", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "Fringe: GrenzfÃ¤lle des FBI - Staffel 5 [dt./OV]", "gti": "amzn1.dv.gti.aca9f6c7-4268-a87e-b610-8e1e3247a442", "transformItemId": "amzn1.dv.gti.aca9f6c7-4268-a87e-b610-8e1e3247a442", "synopsis": "Im Jahr 2015 hatten die scheinbar friedlichen \"Beobachter\" die Kontrolle des Universums Ã¼bernommen. Jetzt, im Jahr 2036, haben sie sich zu skrupellosen Herrschern entwickelt.", "episodicSynopsis": "Die packenden \"Fringe\"-Welten finden mit dem zweistÃ¼ndigen Serienfinale in der 100. Episode ihr Ã¼berwÃ¤ltigendes Ende: Das Team stellt sich den Beobachtern in einem letzten Kampf.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Science Fiction", "Drama", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/588c960c884b7388d603397098b4537670a8f89d82a7dccfb45629fc431a1705.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/c010e650633de120589fe471ee9d52401e50dba1000b1bc8775f4fbd315d7de1.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/881ec8f8ef5d8d53c91909d0b40a2d4c01bf979940ab7f5421bca0b4b37f5a19.jpg", "publicReleaseDate": 1363305600000, "overallRating": 4.8, "totalReviewCount": 3291, "seasonNumber": 5, "watchProgress": 0.99, "numberOfSeasons": 5, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.aca9f6c7-4268-a87e-b610-8e1e3247a442", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_8"}, "refMarker": "hm_hom_c_ExgnYj_16_8", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYSÂ® 1X nominee in 2011"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.aca9f6c7-4268-a87e-b610-8e1e3247a442", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_8"}, "refMarker": "hm_hom_c_ExgnYj_16_8", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Fringe: GrenzfÃ¤lle des FBI [dt./OV]", "episodeNumber": 13, "watchNextType": "CONTINUE", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}}}, "cardType": "TITLE_CARD"}, {"title": "The Vampire Diaries - Staffel 8", "gti": "amzn1.dv.gti.d0ad891d-613a-c0d5-316e-40b66f3f77a2", "transformItemId": "amzn1.dv.gti.d0ad891d-613a-c0d5-316e-40b66f3f77a2", "synopsis": "Bei THE VAMPIRE DIARIES beginnt die letzte Staffel mit mehr Romantik und Abenteuer als je zuvor: Die Suche nach Damon und Enzo beginnt, obwohl es zu spÃ¤t sein kÃ¶nnte.", "episodicSynopsis": "Das Schicksal von Mystic Falls steht auf dem Spiel und Stefan und Damon mÃ¼ssen ihren grÃ¶Ãten Feind bei einer letzten Schlacht bekÃ¤mpfen.", "badges": {"applyAudioDescription": false, "applyCC": false, "applyDolby": true, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Fantasy", "Drama", "Horror", "Young Adult Audience", "Romance"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/2086f341de3ed984ae6e8ff8e7b21f2ac3a89383de49b2e6f254e55dce78b7cd.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ca585bb27acc33434722154085bd365b1d20b795a1d19fa051dcc119b1d86f0c.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/649d9bdf8ea502a83a57eecd80d8bb9b2a58a7f103c8d2ec19693ebdb4dc1fc5.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "publicReleaseDate": 1489104000000, "overallRating": 4.7, "totalReviewCount": 476, "seasonNumber": 8, "watchProgress": 0.99, "numberOfSeasons": 8, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.d0ad891d-613a-c0d5-316e-40b66f3f77a2", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_9"}, "refMarker": "hm_hom_c_ExgnYj_16_9", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.d0ad891d-613a-c0d5-316e-40b66f3f77a2", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_9"}, "refMarker": "hm_hom_c_ExgnYj_16_9", "journeyIngressContext": "8|EgNhbGw="}], "showName": "The Vampire Diaries", "episodeNumber": 16, "watchNextType": "CONTINUE", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "Carnival Row - Season 2", "gti": "amzn1.dv.gti.ec3e3ae0-be35-4e8c-8490-316ea8b65156", "transformItemId": "amzn1.dv.gti.ec3e3ae0-be35-4e8c-8490-316ea8b65156", "synopsis": "A murder investigation unravels a dark conspiracy amidst tensions between the oppressed Faefolk and their Human overlords in \"Carnival Row.\"", "episodicSynopsis": "The epic finale of âCarnival Row.â", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "16", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "SEASON", "isInWatchlist": false, "genres": ["Drama"], "starringCast": ["Orlando Bloom", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "16", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/bc47874589f7cf45460563a07f16c6d3eee3a94d82938215a529854670f6ba0d.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/08d22e5e8ae0091ad2b3a9ecfea47ed9642a0df8fe14d059adf02b90c8ae5c85.jpg", "coverImage": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/c8a11af2ccee1a6cac39b6c6882f4bedc2f53700fb8e18e99503a79c1ad72ebc.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/6a7f6a89cf9dac5bf0dffb8a0f1dd19eb5acc01ee3c56e00e07201b778311f25.png", "providerLogoImage": "https://m.media-amazon.com/images/G/03/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB559052816_.png", "poster2x3Image": "https://images-eu.ssl-images-amazon.com/images/S/pv-target-images/f83bbcb7661ff59f8845e1cc8ee03a337bc8ddb8c8b04530363a72725b3dce4b.jpg", "publicReleaseDate": 1679011200000, "overallRating": 4.4, "totalReviewCount": 595, "seasonNumber": 2, "numberOfSeasons": 2, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.ec3e3ae0-be35-4e8c-8490-316ea8b65156", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_10"}, "refMarker": "hm_hom_c_ExgnYj_16_10", "journeyIngressContext": "8|EgNhbGw="}, "contextualActions": [{"refMarker": "hm_hom_c_ExgnYj_16", "target": "removeFromNextUp", "text": "Remove From List"}], "widgetType": "titleCard", "entitlementStatus": "ENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Included with Prime", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "PRIMETIME EMMYSÂ® 3X nominee"}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.ec3e3ae0-be35-4e8c-8490-316ea8b65156", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_ExgnYj_16_10"}, "refMarker": "hm_hom_c_ExgnYj_16_10", "journeyIngressContext": "8|EgNhbGw="}], "showName": "Carnival Row", "episodeNumber": 10, "watchNextType": "CONTINUE", "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 2724, "width": 4607, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}], "analytics": {"refMarker": "hm_hom_c_ExgnYj_16", "ClientSideMetrics": "548|CmYKOURFQ29udGludWVXYXRjaGluZ1dhdGNoQWdhaW5XYXRjaGxpc3RUM0xpdmVEZWZhdWx0RGVmYXVsdBIPMToxRFFMNEVBM1VWUDAxGhAyOkRZNDZDQjc1NUNCMjlEIgZFeGduWWoSPAoEaG9tZRIEaG9tZSIGY2VudGVyKgAyJDQ0ODY1NmYxLTAyZDktNDRhYS1hMGQxLTMyZGVjNjQ5MjEzMBoDYWxsIgNhbGwqADIPZmFjZXRlZENhcm91c2VsOhtBVFZXYXRjaE5leHRTdHJhdGVneVNlcnZpY2VCCnlvdXJWaWRlb3NKEXdhdGNoTmV4dENhcm91c2VsSgl3YXRjaGxpc3RKEWxhdW5jaGVyV2F0Y2hOZXh0UghlbnRpdGxlZFoAYhBTdGFuZGFyZENhcm91c2VsaBByFHlvdXJWaWRlb3NXYXRjaEFnYWluejhzSk9OaDg4UUY4OFNNOHg2Tkdjd0ZXVHZpY2pBY3R3RGpHNG9sLXlqV2JldEtqb3FRakxNamc9PYIBA2FsbIoBAJIBAA=="}, "tags": ["watchNextCarousel", "watchlist", "launcherWatchNext"], "journeyIngressContext": "8|EgNhbGw=", "type": "STANDARD_CAROUSEL"}], "paginationLink": {"serviceToken": "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", "startIndex": 16, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6UioRob21li4Rob21ljA-ND46CVjI=", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}}, "metadata": {"requestId": "sJONh88QF88SM8x6NGcwFWTvicjActwDjG4ol-yjWbetKjoqQjLMjg==", "requestedTransformId": "lr/collections/paginateContainerList", "domain": "prod", "realm": "eu-west-1", "timestamp": "2024-12-20T19:01:26.645608Z"}}