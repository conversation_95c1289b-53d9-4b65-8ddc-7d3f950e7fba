{"resource": {"items": [{"title": "The Wall", "gti": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1", "transformItemId": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1", "synopsis": "A deadly psychological thriller that follows two soldiers pinned down by an Iraqi sniper, with nothing but a crumbling wall between them.", "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Action", "Military and War"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ab33f2339de91b2377610ef21bebc3a6d284151baa05af804be0ba623a7f7f72.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/3475a9ef55360b9b66d17913182a1d0e7b4158166be577d31e5e86419720df39.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/7a516d8598487997dcfd25a4bb27b199c5e23fc7702421b3d01fbed28d5c488e.jpg", "titleLogoImage": "https://m.media-amazon.com/images/S/pv-target-images/0d7d881898f6cd6816ab81834082c06bd281ae2538c5fca7809b271a037cda6b.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/Prime/logos/channels-logo-white._CB554929912_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/271ae0d0612c98a670a5f02f6d838ead65342746bcb017a68891802b11cd2c35.png", "publicReleaseDate": 1494547200000, "runtimeSeconds": 5371, "runtime": "89 min", "overallRating": 2.8, "totalReviewCount": 2110, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_1", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_1"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Watch with a Prime membership", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.14b952f0-50d8-fb79-847a-4301d4aa55d1", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_1", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_1"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "titleLogoImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 1182, "width": 2000, "scalarHorizontal": "emphasis"}}}, "cardType": "TITLE_CARD"}, {"title": "AK-47 Kalashnikov", "gti": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7", "transformItemId": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7", "synopsis": "The story of <PERSON>, inventor of the most infamous weapon on earth: the AK-47 rifle.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "18+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Military and War", "Action", "Drama", "Historical"], "starringCast": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "maturityRatingString": "18+", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/ef089eb53235f636f50fd725af133f99f0b07fba2504be006cd3960ac493e2a9.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/dadd1dc1cec71f3199e2edcca4b18e1ea26d868b651d15cfb06b9443da9e8875.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8578bdfe07eeacb5a2f4a9ffc67299dd97d4d5984b2608fafb620ef0c1fe6227.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "publicReleaseDate": 1582156800000, "runtimeSeconds": 6281, "runtime": "104 min", "overallRating": 4.4, "totalReviewCount": 3260, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_2", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_2"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e4bb683f-430c-30bf-dcc4-71446e80ecf7", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_2", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_2"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "Midway", "gti": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb", "transformItemId": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb", "synopsis": "Experience the epic true story of the Battle of Midway, a pivotal clash where American forces' bravery and fortitude changed WWII's course.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Military and War", "Historical"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "PG-13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/7164ff056c593fd03a67f5a5b5ac180bf369c8faf6d59054c20f6336bb9812b6.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/348dc911031d54b16c39abe3c2bd7ff323defd9dd72f3bfcdcc2ba00b1ee3e82.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/4bba32fb80f87e55b26c3e84dc4803dad7eca66451082ad39f930ca3e69b6ed5.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "publicReleaseDate": 1573171200000, "runtimeSeconds": 8303, "runtime": "138 min", "overallRating": 4.7, "totalReviewCount": 12223, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_3", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_3"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.fb464e5d-c1d1-451a-adc2-679553aef3bb", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_3", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_3"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "Valkyrie", "gti": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0", "transformItemId": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0", "synopsis": "Based on the true story of Colonel <PERSON> (<PERSON>) and his assassination plot targeting <PERSON><PERSON><PERSON>, this engrossing thriller reenacts the daring operation to elimin...", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "PG-13", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "PG-13", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/9badf1c122096d5eea6cac3d41ddedea4599ba711469bdd334b221bd42c3785e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2d3fec41ccc02e5026ddd6835de913a92677e1dc20746902c377080b2b923216.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/6354f96de74348ce48b44505b46184e716cd9d082e7a54327d3142fad6c78164.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/5ee312827aca2eb2391cc592f0e695c04dd214bd78d28d3ade8df67cb88a6b3f.jpg", "publicReleaseDate": 1230163200000, "runtimeSeconds": 6986, "runtime": "116 min", "overallRating": 4.6, "totalReviewCount": 4496, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_4", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_4"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2fe49bdd-6dda-446b-9809-5f7952b918b0", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_4", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_4"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "poster2x3Image": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "USS Indianapolis: Men of Courage", "gti": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf", "transformItemId": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf", "synopsis": "Starring <PERSON>, <PERSON>, and <PERSON>, this WWII drama follows the true story of American heroes shipwrecked in shark-filled waters.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": true, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Military and War", "Drama", "Action", "Historical", "Suspense"], "starringCast": ["<PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/e4e714ba96b30c79562de06702b37e1d6a3a2e5d41a4a20512fdf145f498cf87.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/ada5ea011f556765d40a92c913e3ce5031c47e7b1e55356d703ce190cfc3db52.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/a35df06c147b11df6988fdf4e86cf989f9c4fb61a26fd7f4c7c6dff824138614.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "publicReleaseDate": 1476403200000, "runtimeSeconds": 7828, "runtime": "130 min", "overallRating": 4.5, "totalReviewCount": 1711, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_5", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_5"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.26989980-ad5b-4eca-814d-ac556b3b24bf", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_5", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_5"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "1944", "gti": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7", "transformItemId": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7", "synopsis": "In 1944, Estonia grapples with invading Russian and Nazi forces, families torn apart, soldiers making difficult choices.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": true, "regulatoryRating": "13+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Drama", "Action", "Military and War", "Historical"], "starringCast": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "maturityRatingString": "13+", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/71da908c05935cfbefc51bbe5962ed319d13bf261e470a5f01064e755b6cc059.png", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/618c3972e0d1a82fc8eba6fff9b34d90f40d6ff9fdbadb83af590d5b0cba0249.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/155d3d49575b5d9898ca73526e3e2bc4790c7ebadca2cb4a803e6b1f0e6e3700.png", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "publicReleaseDate": 1446595200000, "runtimeSeconds": 6007, "runtime": "100 min", "overallRating": 4.3, "totalReviewCount": 1348, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_6", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_6"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a6aed27e-e39b-0ae7-e4d3-28cae5307ea7", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_6", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_6"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}, {"title": "Sniper: The White Raven", "gti": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad", "transformItemId": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad", "synopsis": "After suffering a senseless tragedy, a Ukrainian man seeks revenge.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Drama", "Military and War"], "starringCast": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "maturityRatingString": "R", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/88ddd79277e9f1d12981f3114096da703d1e3e0e91b1981941d90958841f682e.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/2d695d81ae663afa2ef93832c5605edbf26c1060781365731ce250f59616d2d4.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/aa86047692759221915c3b9a03c7eb4280a2e047b365a9f0253d9b2f09a97c04.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/m-r/paramountpremium/logos/channels-logo-white._CB583166680_.png", "publicReleaseDate": 1641013200000, "runtimeSeconds": 6694, "runtime": "111 min", "overallRating": 4.3, "totalReviewCount": 1971, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_7", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_7"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial or buy", "icon": "OFFER_ICON"}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+, rent, or buy", "icon": "OFFER_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.46984f93-afb3-497d-9f5d-cda8a3c68aad", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_7", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_7"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 461, "width": 1999, "scalarHorizontal": "default"}}}, "cardType": "TITLE_CARD"}, {"title": "Vietnam in HD", "gti": "amzn1.dv.gti.a8a9f765-858c-b499-2ec4-25f68b0bd8c7", "transformItemId": "amzn1.dv.gti.a8a9f765-858c-b499-2ec4-25f68b0bd8c7", "synopsis": "Two years after the release of its landmark Emmy-winning series WWII in HD, HISTORY shifts its focus to a new generation and one of the most controversial chapters in American history, the Vietnam War.", "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "NR", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "SHOW", "isInWatchlist": false, "genres": ["Documentary", "Historical", "Military and War"], "starringCast": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "maturityRatingString": "NR", "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/11d164e4c26b1d776aa83806ef5532297c0bf7dd91840458391fca5d8758ed52.jpg", "boxartImage": "https://m.media-amazon.com/images/S/pv-target-images/a29d35ed4a333dfd6324a100e038545cbbb562c9ca0fe914257cba2d7257fc64.jpg", "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/2e81efe132d945e887c88195d2dc61d0605f4e53bf9c66c289b80d358bbce5d6.jpg", "providerLogoImage": "https://m.media-amazon.com/images/G/01/digital/video/merch/subs/benefit-id/a-f/freewithads/logos/channels-logo-white._CB558492252_.png", "publicReleaseDate": 1320537600000, "overallRating": 4.7, "totalReviewCount": 657, "numberOfSeasons": 1, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e4a9f737-161f-0574-6234-e2459ecd9ed7", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_8", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_8"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "ENTITLED_FREE_WITH_ADS", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": ""}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Freevee (with ads)", "icon": "ENTITLED_ICON"}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": ""}, "TITLE_METADATA_BADGE_SLOT": {"message": ""}}, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e4a9f737-161f-0574-6234-e2459ecd9ed7", "pageType": "detail", "analytics": {"refMarker": "hm_ass_c_sr63757f_dast_1_8", "pillId": "2044", "pillVersion": "demo-20241101_v2"}, "refMarker": "hm_ass_c_sr63757f_dast_1_8"}], "imageAttributes": {"primeExclusive": false, "isAdult": false, "isRestricted": false, "individualImageMetadata": {"coverImage": {"safeToOverlay": false}, "heroImage": {"safeToOverlay": false}, "boxartImage": {"safeToOverlay": false}, "providerLogoImage": {"height": 624, "width": 2000}}}, "cardType": "TITLE_CARD"}], "paginationLink": {"serviceToken": "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", "startIndex": 16, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6UioRob21li4Rob21ljA-ND46CVjI=", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}}, "metadata": {"requestId": "sJONh88QF88SM8x6NGcwFWTvicjActwDjG4ol-yjWbetKjoqQjLMjg==", "requestedTransformId": "lr/collections/paginateContainerList", "domain": "prod", "realm": "eu-west-1", "timestamp": "2024-12-20T19:01:26.645608Z"}}