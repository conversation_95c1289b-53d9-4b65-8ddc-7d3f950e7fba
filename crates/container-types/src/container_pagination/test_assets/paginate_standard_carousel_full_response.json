{"resource": {"items": [{"title": "The Book Of Clarence", "gti": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747", "transformItemId": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747", "synopsis": "<PERSON> wants to show he's not a nobody, gets into trouble, but at last finds redemption and faith.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Comedy", "Drama", "Historical", "Action"], "maturityRatingString": "PG-13", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c5aea2ec4d42cf2ef865cbfa08f77660ba6aa81b9335826c8e5c1cf8ce3e1f68.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c81c0b20b87918f3f074efce745a64976cdf0104cf8fe2e5cbdd1dac35e52b8c._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/0c6a521d661868f963ec83216a8aab1c7bba582aac00efee5277a344ee57e5c2.jpg", "publicReleaseDate": 1705017600000, "runtimeSeconds": 7820, "runtime": "130 min", "overallRating": 4.3, "totalReviewCount": 743, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_11"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_11", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.2c0a075b-cc39-4bf5-9b84-ab2986f8c747", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_11"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_11", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Si<PERSON>", "gti": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b", "transformItemId": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b", "synopsis": "During the last days of World War II, a solitary prospector crosses paths with Nazis on a scorched-Earth retreat in northern Finland. When the soldiers decide to steal his gold, they quickly discover they just tangled with no ordinary miner.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Military and War"], "maturityRatingString": "R", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/c7558cdf25bdbd2b274669051379f55f220f39df2ba18dd5cf6c252a972a317e.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/b3d7e03093c0714c52147bb0892ced90eec50165f7ae84cbdf59320853af99ad._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/4422a624d6ab07e49aff433b753f6a1f20db5bb113d709f2dee074b4994b90a3.jpg", "publicReleaseDate": 1682640000000, "runtimeSeconds": 5248, "runtime": "87 min", "overallRating": 4.5, "totalReviewCount": 13699, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_12"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_12", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of STARZ, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.6a652d10-13ed-457c-8792-ed0c3846149b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_12"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_12", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Cash Out", "gti": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe", "transformItemId": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe", "synopsis": "<PERSON> is criminal mastermind <PERSON> in this tense heist movie. Thrust into his brother's botched bank robbery, <PERSON> stumbles into his biggest score…but can he escape alive?", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action"], "maturityRatingString": "R", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/368c070aca27395416293b2f05725656c6d66b66b2271a16800bbd968bf8421a.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/c53b7c0b4ba10099c8f9a63a1832d2cd09859c4806aa590764d8402620c5e021._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/e0e40e21bb9e936cde8c4e50dc47c298fdcc63802783acf43ef288418475f7b9.jpg", "publicReleaseDate": **********000, "runtimeSeconds": 5422, "runtime": "90 min", "overallRating": null, "totalReviewCount": null, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_13"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_13", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.57c433b7-e1e1-423a-b6a7-2e80259bfdbe", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_13"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_13", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Muzzle", "gti": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e", "transformItemId": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e", "synopsis": "LAPD officer <PERSON> (<PERSON>) and his partner <PERSON><PERSON>, a violent K-9 with titanium incisors and a mysterious past, aim to uncover a vast conspiracy that has a chokehold on the city.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "16+", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense", "Drama"], "maturityRatingString": "16+", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/1637a3d27d06e8c3d5b7f6996ced2a18e2f4c415a19cbaad48e2f6bf00b1c06b.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/8190d658f63289a4bc23cc054cff29cfc1b637c9eb10dc3225635158e7ea67f1._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/bf3052915401fcf8b0533bb6335894ba3b56bc1573adb68088634470d118723c.jpg", "publicReleaseDate": 1695945600000, "runtimeSeconds": 5994, "runtime": "99 min", "overallRating": 4.3, "totalReviewCount": 2623, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_14"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_14", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of AMC+, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.67efa002-0651-433a-bcbe-a8c7a736527e", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_14"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_14", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Soul Mates", "gti": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125", "transformItemId": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125", "synopsis": "The two must find their way out of this sick maze but with every twist, turn and setback curated by the Matchmaker, they may never escape.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": false, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Horror", "Suspense"], "maturityRatingString": "R", "maturityRatingImage": null, "heroImage": null, "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/7c73223b6e54d6ac0d2f60364ba75fd5045f3e68b796925749335ac8fec09d14._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1710979200000, "runtimeSeconds": 5246, "runtime": "87 min", "overallRating": 3.8, "totalReviewCount": 21, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_15"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_15", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of BET+, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.e8e58d41-2093-405c-aaec-2fd5c061f125", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_15"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_15", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Retribution", "gti": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f", "transformItemId": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f", "synopsis": "In this high-octane ride of redemption and revenge, a father must play a deadly game when a mysterious caller threatens his family with a bomb beneath their car seats.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "maturityRatingString": "R", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/cf791b0d830732571b9597ecfc30db4ee1991ab88d4b181bdaa32dc30f552e80.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/642deea587e0b2012aa638243011bca8f25ad9225f5a07c73e6ae3ea77b3b39e._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/a86428dd0caf94e78d12b76be8c00da636873cb4dd7b542d2b1247b14f06b69b.jpg", "publicReleaseDate": 1692921600000, "runtimeSeconds": 5249, "runtime": "87 min", "overallRating": 4.4, "totalReviewCount": 4541, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_16"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_16", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of STARZ, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.de8e549e-ea93-4811-9b2c-01130ad9874f", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_16"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_16", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Equalizer 3 - Bonus X-Ray Edition", "gti": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b", "transformItemId": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b", "synopsis": "Ex-assassin <PERSON> confronts his past as he battles the Italian mafia.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Suspense"], "maturityRatingString": "R", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/8d00fc467ad27b13122df0dba2679e3f50717eac0b7386a1c25afe515246b606.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/bb41592bbb836f9157a8355de11b3da54a05758ffc32943e5f6454ca75efca5b._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/3a1e47133e96b8c24074e92ac01ed7f7965cb229f39c1fb40b50c932caa42cad.jpg", "publicReleaseDate": 1693526400000, "runtimeSeconds": 6533, "runtime": "108 min", "overallRating": 4.7, "totalReviewCount": 88767, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_17"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_17", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.9f54e311-81c5-486a-abea-d3f645710d4b", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_17"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_17", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Dune", "gti": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6", "transformItemId": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6", "synopsis": "The son of a noble family travels to a dangerous planet to ensure the future of his people in this visually stunning sci-fi epic.", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": true, "applyDolbyVision": false, "applyUhd": true}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Adventure", "Drama", "Science Fiction"], "maturityRatingString": "PG-13", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/3e6707e7bdc406ff0e1b61ec3d1cb307a1bdc257472a5032f8e6d2a77ff97383.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/2703313c37a993af7a5971f68f79cd665e04643b3560c92736eeaab2bb235eb2._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/fc5bf647573bf2c34813496a7cb9cf2db978e0b7d4d3dd3ae5befe3e94672c0c.jpg", "publicReleaseDate": 1634860800000, "runtimeSeconds": 9323, "runtime": "155 min", "overallRating": 4.5, "totalReviewCount": 45202, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_18"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_18", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Subscribe, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Subscribe to Max, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBE® winner", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a8025548-3908-4562-8896-8fffd945cfb6", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_18"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_18", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "Cloud Atlas", "gti": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc", "transformItemId": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc", "synopsis": "Actors (<PERSON>, <PERSON>, <PERSON>) take on multiple roles in an epic that spans five centuries. An attorney harbors a fleeing slave on a voyage from the Pacific Islands in 1849; a poor composer in pre-World War II Britain struggles to finish his magnum opus before a past act catches up with him; a genetically engineered worker in 2144 feels the forbidden stirring of human consciousn...", "episodicSynopsis": null, "badges": {"applyAudioDescription": true, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "R", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Adventure", "Science Fiction", "Drama", "Action", "Suspense"], "maturityRatingString": "R", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/59b2847735d8f3b6aea8c51b5f666e234daac66fa03c86b25f0c0ada93378933.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://images-na.ssl-images-amazon.com/images/S/pv-target-images/d2f85abd1c5056885db929c3836676f3c0df77fb2d7798effcc5aadc7d7a7bff._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": "https://m.media-amazon.com/images/S/pv-target-images/29fb7d4abdfb3a54ab838e0a64888818f0b64918bb02c2d5b0055d3e58f9e9bd.jpg", "publicReleaseDate": 1351209600000, "runtimeSeconds": 10313, "runtime": "171 min", "overallRating": 4.3, "totalReviewCount": 7824, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_19"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_19", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Free trial, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Free trial of Paramount+, rent, or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "GOLDEN GLOBE® nominee", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.a0a9f7b8-af0d-4f95-7cec-2bd6d61ea9dc", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_19"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_19", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}, {"title": "The Shift", "gti": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546", "transformItemId": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546", "synopsis": "<PERSON> (<PERSON><PERSON><PERSON>) travels across worlds to reunite with the love of his life, <PERSON> (<PERSON>). When The Benefactor (<PERSON>) threatens <PERSON>'s survival, he fights to return to the world he knows and the woman he loves.", "episodicSynopsis": null, "badges": {"applyAudioDescription": false, "applyCC": true, "applyDolby": true, "applyPrime": false, "regulatoryRating": "PG-13", "applyHdr10": false, "applyDolbyVision": false, "applyUhd": false}, "contentType": "MOVIE", "isInWatchlist": false, "genres": ["Action", "Science Fiction"], "maturityRatingString": "PG-13", "maturityRatingImage": null, "heroImage": "https://m.media-amazon.com/images/S/pv-target-images/0b3f51fa4094e2096dbbb4843609f18cb100a38d7fcc90863873310741e38692.jpg", "unbrandedCoverImage": null, "boxartImage": null, "coverImage": "https://m.media-amazon.com/images/S/pv-target-images/85cb4c6d41f7153516414b2a672c59b088b49b8cdf69acaaf591d51128689e77._UR1920,1080_RI_.jpg", "titleLogoImage": null, "providerLogoImage": null, "poster2x3Image": null, "publicReleaseDate": 1701388800000, "runtimeSeconds": 6781, "runtime": "113 min", "overallRating": 4.3, "totalReviewCount": 324, "seasonNumber": null, "watchProgress": null, "numberOfSeasons": null, "venue": null, "venueCity": null, "venueCountry": null, "liveEventDateBadge": null, "liveEventDateHeader": null, "liveliness": null, "startTime": null, "endTime": null, "action": {"target": "legacyDetail", "pageId": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_20"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_20", "text": null, "journeyIngressContext": "8|EgR0dm9k"}, "contextualActions": [], "widgetType": "titleCard", "entitlementStatus": "UNENTITLED", "entitlementMessaging": {"GLANCE_MESSAGE_SLOT": {"message": "Rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "ENTITLEMENT_MESSAGE_SLOT": {"message": "Available to rent or buy", "icon": "OFFER_ICON", "level": null, "type": null}, "HIGH_VALUE_MESSAGE_SLOT_LITE": {"message": "", "icon": null, "level": null, "type": null}, "TITLE_METADATA_BADGE_SLOT": {"message": "", "icon": null, "level": null, "type": null}}, "linearAttributes": null, "tournamentIcid": null, "actions": [{"target": "legacyDetail", "pageId": "amzn1.dv.gti.4c0edf3b-2227-41a5-992e-8719365ad546", "pageType": "detail", "analytics": {"refMarker": "hm_hom_c_OB8d21f2_brws_1_20"}, "refMarker": "hm_hom_c_OB8d21f2_brws_1_20", "text": null, "journeyIngressContext": "8|EgR0dm9k"}], "playableGti": null, "showName": null, "episodeNumber": null, "watchNextType": null, "imageAttributes": null, "cardType": "TITLE_CARD"}], "paginationLink": {"serviceToken": "eyJ0eXBlIjoiaHBhZ2UiLCJuYXYiOmZhbHNlLCJzZWMiOiJjZW50ZXIiLCJwcmlkIjoiZWE3MDBhZmUtOTExNS00YWUzLTk1MjctOTliOWU3NDYzYjA4IiwiZmlsdGVyIjp7fSwib2Zmc2V0IjowLCJucHNpIjoyMCwib3JlcSI6ImFmUzBfNGZheTdKaXo2cTZkTkpsMTM0Qy1qRzR1RkltZlc0Y1BLNVJZVWtPWkJJZkU1cmZnQT09OjE3MTQ0MTg3MDEwMDAiLCJhcE1heCI6NDk1LCJzdHJpZCI6IjI6T0JFNzEzQ0M1RjgxRjEjI01aUVdHWkxVTVZTRUdZTFNONTJYR1pMTSIsImF1dG9ib3QiOiJ7XCJyc2lwU3RhdGVJZFwiOlwiUlNJUGE1ZDBkNjY4OGE2ZjQ2MzEzMTQ2Y2M0ODhhYTY3YmNjNWEwYzRhYWY0NGYwOTE4OGJkNDBkZWYzZWNkMzAwZjNcIn0iLCJzdEtleSI6IntcInNic2luXCI6MCxcImN1cnNpemVcIjo0OTUsXCJwcmVzaXplXCI6MH0iLCJvcmVxayI6ImxjUHFPdmdOMHhzMU04aUZRVVhYclFINWRtYjZyQzNGRStqSUN1TWs5T009Iiwib3JlcWt2IjoxLCJleGNsVCI6W119", "startIndex": 20, "swiftId": "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6lioRob21li4Rob21ljI6QMjpPQkU3MTNDQzVGODFGMY0PjoJWMg==", "pageId": "home", "pageType": "home", "pageContext": {"pageType": "home", "pageId": "home"}}}, "metadata": {"requestId": "afS0_4fay7Jiz6q6dNJl134C-jG4uFImfW4cPK5RYUkOZBIfE5rfgA==", "requestedTransformId": "lr/collections/paginateContainer", "domain": "prod", "realm": "us-east-1", "timestamp": "2024-04-29T19:25:01.419213Z"}}