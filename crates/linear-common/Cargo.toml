[package]
name = "linear-common"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
chrono.workspace = true
cfg-test-attr-derive.workspace = true
liveliness-types.workspace = true
serde_json.workspace = true

[dev-dependencies]
rstest.workspace = true

[lints]
workspace = true
