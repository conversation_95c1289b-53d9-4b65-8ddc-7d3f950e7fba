use serde_json::{Map, Value};

pub mod util {
    pub mod datetime_util;
    pub mod localization_util;
    pub mod schedule_util;
}
pub mod constants;
pub mod types;

pub fn get_news_page_params() -> Map<String, Value> {
    serde_json::Map::from_iter(vec![
        ("pageType".to_string(), Value::String("home".to_string())),
        ("pageId".to_string(), Value::String("news".to_string())),
    ])
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_news_page_params() {
        let params = get_news_page_params();

        assert_eq!(
            params.get("pageType"),
            Some(&Value::String("home".to_string()))
        );
        assert_eq!(
            params.get("pageId"),
            Some(&Value::String("news".to_string()))
        );
        assert_eq!(params.len(), 2);
    }
}
