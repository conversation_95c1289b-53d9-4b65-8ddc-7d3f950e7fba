use cfg_test_attr_derive::derive_test_only;
use chrono::{DateTime, FixedOffset, ParseError, Utc};

// TODO: used unsigned integer for time types and functions: https://issues.amazon.com/issues/LEX3PLR-1395
pub type TimeRange = (i64, i64);

/// Trait to be implemented by model for an EPG row item component.
pub trait Schedule {
    /// Provides a start and end time of the item.
    fn get_time_range(&self) -> TimeRange;
}

#[derive(Clone, Copy)]
pub struct TimeProvider {}
impl TimeProvider {
    #[cfg(test)]
    pub fn get_epoch_milli_as_local_time(epoch: i64) -> DateTime<FixedOffset> {
        use chrono::{Offset, TimeZone, Utc};
        Utc.fix().timestamp_millis_opt(epoch).unwrap()
    }

    #[cfg(not(test))]
    pub fn get_epoch_milli_as_local_time(epoch: i64) -> DateTime<FixedOffset> {
        use chrono::{Offset, TimeZone, Utc};
        use ignx_compositron::timezone::get_timezone_offset_at_timestamp;

        if let Some(tz_offset) =
            DateTime::from_timestamp_millis(epoch).and_then(get_timezone_offset_at_timestamp)
        {
            tz_offset.timestamp_millis_opt(epoch).unwrap()
        } else {
            Utc.fix().timestamp_millis_opt(epoch).unwrap()
        }
    }
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct DatePair {
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
}

#[derive(Debug)]
pub(crate) struct DateStringParseError {
    pub(crate) naive_dt_parse_err: ParseError,
    pub(crate) rfc_3339_parse_err: ParseError,
}
