use crate::types::{Schedule, TimeRange};
use chrono::{self, DateTime, Utc};
use liveliness_types::Liveliness;

// TODO: used unsigned integer for time types and functions: https://issues.amazon.com/issues/LEX3PLR-1395
pub fn get_liveliness(is_on_air: bool, apply_live: bool) -> Liveliness {
    match (is_on_air, apply_live) {
        (true, true) => Liveliness::Live,
        (true, false) => Liveliness::OnNow,
        (false, _) => Liveliness::Upcoming,
    }
}

pub fn is_on_air_at_time(time_range: TimeRange, timestamp: i64) -> bool {
    let (start_time, end_time) = time_range;
    start_time <= timestamp && timestamp < end_time
}

pub fn find_current_item_from_schedule<T>(schedule: &[T], now: i64) -> Option<&T>
where
    T: Schedule,
{
    schedule
        .iter()
        .find(|item| is_on_air_at_time(item.get_time_range(), now))
}

pub fn get_duration_minutes(start: i64, end: i64) -> Option<u16> {
    if start > end {
        return None;
    }
    let duration_in_ms = end - start;
    let duration = chrono::Duration::milliseconds(duration_in_ms);
    Some(duration.num_minutes().max(1) as u16)
}

pub fn get_current_item_progress_props_from_schedule<T>(
    schedule: &[T],
    now: DateTime<Utc>,
) -> (Option<f32>, Option<u16>)
where
    T: Schedule,
{
    let current_item = find_current_item_from_schedule(schedule, now.timestamp_millis());
    current_item
        .map(|item| {
            let (start, end) = item.get_time_range();
            (
                get_progress_from_ms(now.timestamp_millis(), start, end),
                get_duration_minutes(start, end),
            )
        })
        .unwrap_or_default()
}

pub fn get_progress_from_ms(current_time: i64, start_time: i64, end_time: i64) -> Option<f32> {
    let duration = end_time - start_time;

    if duration <= 0 {
        None
    } else {
        let elapsed = current_time - start_time;
        let progress = elapsed as f32 / duration as f32;
        Some(progress.clamp(0.0, 1.0))
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use chrono::TimeZone;
    use ignx_compositron::id::Id;
    use rstest::*;

    // Mock schedule item for generic testing
    #[derive(Clone, Debug)]
    struct Airing(i64, i64);
    impl Schedule for Airing {
        fn get_time_range(&self) -> TimeRange {
            (self.0, self.1)
        }
    }
    impl Id for Airing {
        type Id = i64;

        fn id(&self) -> &Self::Id {
            &self.0
        }
    }

    impl PartialEq for Airing {
        fn eq(&self, other: &Self) -> bool {
            self.0 == other.0 && self.1 == other.1
        }
    }

    // Airing of length 100ms
    fn scheduled_airing(offset: i64) -> Airing {
        let start = (100 * offset) + 100;
        Airing(start, start + 100)
    }

    #[fixture]
    fn scheduled_airings() -> Vec<Airing> {
        vec![
            Airing(
                Utc.with_ymd_and_hms(2024, 4, 29, 12, 0, 0)
                    .unwrap()
                    .timestamp_millis(),
                Utc.with_ymd_and_hms(2024, 4, 29, 14, 0, 0)
                    .unwrap()
                    .timestamp_millis(),
            ),
            Airing(
                Utc.with_ymd_and_hms(2024, 4, 29, 14, 0, 0)
                    .unwrap()
                    .timestamp_millis(),
                Utc.with_ymd_and_hms(2024, 4, 29, 16, 0, 0)
                    .unwrap()
                    .timestamp_millis(),
            ),
        ]
    }

    mod find_current_item_from_schedule {
        use super::*;

        #[rstest]
        #[case::before_first_airing(0, None)]
        #[case::during_upcoming_airing(250, Some(scheduled_airing(1)))]
        #[case::after_all_airings(500, None)]
        fn test_no_current_item_before_schedule_starts(
            #[case] now: i64,
            #[case] expected: Option<Airing>,
        ) {
            let airings = vec![
                scheduled_airing(0),
                scheduled_airing(1),
                scheduled_airing(2),
            ];

            let result = find_current_item_from_schedule(&airings, now);
            assert_eq!(result, expected.as_ref());
        }

        #[test]
        fn test_no_current_item_with_empty_schedule() {
            let empty_schedule: Vec<Airing> = Vec::new();
            let result = find_current_item_from_schedule(&empty_schedule, 100);
            assert_eq!(result, None);
        }
    }

    #[rstest]
    #[case::with_an_airing_on_now(
        scheduled_airings(),
        Utc.with_ymd_and_hms(2024, 4, 29, 13, 0, 0).unwrap(),
        Some(120)
    )]
    #[case::with_no_airing_on_now(
        scheduled_airings(),
        Utc.with_ymd_and_hms(2024, 4, 29, 19, 0, 0).unwrap(),
        None
    )]
    #[case::with_empty_schedule(
        vec![],
        Utc.with_ymd_and_hms(2024, 4, 29, 13, 0, 0).unwrap(),
        None
    )]
    fn test_get_current_item_duration_from_schedule(
        #[case] schedule: Vec<Airing>,
        #[case] now: DateTime<Utc>,
        #[case] expected: Option<u16>,
    ) {
        let (_, duration) = get_current_item_progress_props_from_schedule(&schedule, now);
        assert_eq!(duration, expected);
    }

    #[rstest]
    #[case::before(Utc.with_ymd_and_hms(2024, 4, 29, 11, 0, 0).unwrap(), None)]
    #[case::during(Utc.with_ymd_and_hms(2024, 4, 29, 13, 0, 0).unwrap(), Some(0.5))]
    #[case::after(Utc.with_ymd_and_hms(2024, 4, 29, 15, 0, 0).unwrap(), None)]
    fn test_get_progress_from_scheduled_shows(
        #[case] now: DateTime<Utc>,
        #[case] expected: Option<f32>,
    ) {
        let scheduled_airings = vec![Airing(
            Utc.with_ymd_and_hms(2024, 4, 29, 12, 0, 0)
                .unwrap()
                .timestamp_millis(),
            Utc.with_ymd_and_hms(2024, 4, 29, 14, 0, 0)
                .unwrap()
                .timestamp_millis(),
        )];

        let (progress, _) = get_current_item_progress_props_from_schedule(&scheduled_airings, now);
        assert_eq!(progress, expected);
    }

    #[rstest]
    #[case::live(true, true, Liveliness::Live)]
    #[case::on_now(true, false, Liveliness::OnNow)]
    #[case::upcoming_with_badge(false, true, Liveliness::Upcoming)]
    #[case::upcoming_without_badge(false, false, Liveliness::Upcoming)]
    fn test_get_liveliness(
        #[case] on_air: bool,
        #[case] apply_live: bool,
        #[case] expected: Liveliness,
    ) {
        assert_eq!(get_liveliness(on_air, apply_live), expected)
    }

    #[rstest]
    #[case::valid_times(0, 150_000, Some(2))]
    #[case::invalid_times(1, 0, None)]
    fn test_get_duration_minutes(
        #[case] start: i64,
        #[case] end: i64,
        #[case] expected: Option<u16>,
    ) {
        assert_eq!(get_duration_minutes(start, end), expected);
    }

    #[rstest]
    #[case::middle_range(1000, 0, 2000, Some(0.5))]
    #[case::start_range(0, 0, 2000, Some(0.0))]
    #[case::end_range(2000, 0, 2000, Some(1.0))]
    #[case::after_end(3000, 0, 2000, Some(1.0))]
    #[case::before_start(0, 1000, 2000, Some(0.0))]
    #[case::zero_duration(1000, 0, 0, None)]
    #[case::negative_duration(1000, 0, -1000, None)]
    fn test_get_progress_from_ms(
        #[case] current_time: i64,
        #[case] start_time: i64,
        #[case] end_time: i64,
        #[case] expected: Option<f32>,
    ) {
        assert_eq!(
            get_progress_from_ms(current_time, start_time, end_time),
            expected
        );
    }
}
