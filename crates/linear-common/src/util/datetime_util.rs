use chrono::{DateTime, FixedOffset, NaiveDateTime, TimeZone, Timelike, Utc};
use ignx_compositron::text::{LocalizedText, SubstitutionParameters, TextContent};

use crate::types::{DatePair, DateStringParseError, TimeProvider};

const CHRONO_FORMAT_STRING_HOUR_MINUTE: &str = "%-I:%M";
const CHRONO_FORMAT_STRING_HOUR_MINUTE_PERIOD: &str = "%-I:%M %p";
const AV_LRC_LIVE_TV_TIME_REMAINING_ONLY_HOURS: &str = "AV_LRC_LIVE_TV_TIME_REMAINING_ONLY_HOURS";
const AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR: &str = "AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR";
const AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_WITH_HOURS: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_WITH_HOURS";
const AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_MINUTES_ONLY: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_MINUTES_ONLY";
const AV_LRC_LIVE_TV_TIME_REMAINING_HOURS_MINUTES: &str =
    "AV_LRC_LIVE_TV_TIME_REMAINING_HOURS_MINUTES";
const AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES: &str = "AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES";

fn parse_date_string_flexible(date_string: &str) -> Result<DateTime<Utc>, DateStringParseError> {
    NaiveDateTime::parse_from_str(date_string, "%Y-%m-%dT%H:%MZ")
        .map(|naive_dt| Utc.from_utc_datetime(&naive_dt))
        .or_else(|naive_dt_parse_err| {
            DateTime::parse_from_rfc3339(date_string)
                .map(|fixed_dt| fixed_dt.with_timezone(&Utc))
                .map_err(|rfc_3339_parse_err| DateStringParseError {
                    naive_dt_parse_err,
                    rfc_3339_parse_err,
                })
        })
}

pub fn parse_date_pair(start_date: Option<&str>, end_date: Option<&str>) -> Option<DatePair> {
    let parse = |date_str: Option<&str>| {
        date_str.and_then(|date_str| parse_date_string_flexible(date_str).ok())
    };
    let start_date = parse(start_date)?;
    let end_date = parse(end_date)?;
    Some(DatePair {
        start_date,
        end_date,
    })
}

pub fn parse_date_string_to_timestamp_millis(date_string: &str) -> Option<i64> {
    parse_date_string_flexible(date_string)
        .ok()
        .map(|date_time| date_time.timestamp_millis())
}

pub fn periods_differ(t1: DateTime<FixedOffset>, t2: DateTime<FixedOffset>) -> bool {
    (t1.hour() >= 12) != (t2.hour() >= 12)
}

pub fn get_formatted_times(start_time: i64, end_time: i64) -> (String, String) {
    let start_time = TimeProvider::get_epoch_milli_as_local_time(start_time);
    let end_time = TimeProvider::get_epoch_milli_as_local_time(end_time);

    let formatted_start_time = if periods_differ(start_time, end_time) {
        start_time.format(CHRONO_FORMAT_STRING_HOUR_MINUTE_PERIOD)
    } else {
        start_time.format(CHRONO_FORMAT_STRING_HOUR_MINUTE)
    };

    let formatted_end_time = end_time.format(CHRONO_FORMAT_STRING_HOUR_MINUTE_PERIOD);

    (
        formatted_start_time.to_string(),
        formatted_end_time.to_string(),
    )
}

pub fn get_time_remaining(current_time: i64, end_time: i64, tts: bool) -> TextContent {
    // Round up remaining time to minutes and 1 minute minimum.
    let total_minutes = (((end_time - current_time) as f32 / 60_000.0).ceil() as i64).max(1);
    let hours_remaining = total_minutes / 60;
    let minutes_remaining = total_minutes % 60;

    let (localization_string, param) = if hours_remaining > 0 {
        if minutes_remaining > 0 {
            let string_id = if tts {
                AV_LRC_LIVE_TV_TIME_REMAINING_HOURS_MINUTES
            } else {
                AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_WITH_HOURS
            };
            (
                string_id,
                vec![
                    (
                        "hoursRemaining".to_owned(),
                        TextContent::String(hours_remaining.to_string()),
                    ),
                    (
                        "minutesRemaining".to_owned(),
                        TextContent::String(minutes_remaining.to_string()),
                    ),
                ],
            )
        } else if hours_remaining == 1 {
            (AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR, vec![])
        } else {
            (
                AV_LRC_LIVE_TV_TIME_REMAINING_ONLY_HOURS,
                vec![(
                    "hoursRemaining".to_owned(),
                    TextContent::String(hours_remaining.to_string()),
                )],
            )
        }
    } else {
        let string_id = if tts {
            AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES
        } else {
            AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_MINUTES_ONLY
        };
        (
            string_id,
            vec![(
                "minutesRemaining".to_owned(),
                TextContent::String(minutes_remaining.to_string()),
            )],
        )
    };

    TextContent::LocalizedText(LocalizedText {
        string_id: localization_string.into(),
        substitution_parameters: Some(SubstitutionParameters(param.into_iter().collect())),
    })
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::{types::TimeRange, util::schedule_util::is_on_air_at_time};
    use rstest::*;

    mod parse_date_pair {
        use super::*;

        #[rstest]
        #[case(None, None, None)]
        #[case(Some("2024-04-30T12:00Z".to_string()), None, None)]
        #[case(None, Some("2024-04-30T13:00Z".to_string()), None)]
        fn test_parse_date_pair_missing_dates(
            #[case] start_date: Option<String>,
            #[case] end_date: Option<String>,
            #[case] expected: Option<DatePair>,
        ) {
            assert_eq!(
                parse_date_pair(start_date.as_deref(), end_date.as_deref()),
                expected
            );
        }

        #[rstest]
        #[case("2024-04-30T12:00", "2024-04-30T13:00Z")]
        #[case("2024-04-30T12:00Z", "2024-04-30T13:00")]
        fn test_parse_date_pair_invalid_format(
            #[case] start_date_str: &str,
            #[case] end_date_str: &str,
        ) {
            assert_eq!(
                parse_date_pair(Some(start_date_str), Some(end_date_str)),
                None
            );
        }

        #[test]
        fn test_parse_date_pair_valid() {
            let start_date = Some("2024-04-30T12:00Z".to_string());
            let end_date = Some("2024-04-30T13:00Z".to_string());

            let expected_start = Utc.with_ymd_and_hms(2024, 4, 30, 12, 0, 0).unwrap();
            let expected_end = Utc.with_ymd_and_hms(2024, 4, 30, 13, 0, 0).unwrap();
            let expected_result = Some(DatePair {
                start_date: expected_start,
                end_date: expected_end,
            });

            assert_eq!(
                parse_date_pair(start_date.as_deref(), end_date.as_deref()),
                expected_result
            );
        }
    }

    #[test]
    fn test_get_formatted_time_range_same_period() {
        let start_time = 1618988400000; // April 21, 2021 7:00:00 AM UTC
        let end_time = 1618992000000; // April 21, 2021 8:00:00 AM UTC
        let (formatted_start_time, formatted_end_time) = get_formatted_times(start_time, end_time);

        assert_eq!(formatted_start_time, "7:00");
        assert_eq!(formatted_end_time, "8:00 AM");
    }

    #[test]
    fn test_get_formatted_time_range_different_periods() {
        let start_time = 1618988400000; // April 21, 2021 7:00:00 AM UTC
        let end_time = 1619010000000; // April 21, 2021 1:00:00 PM UTC
        let (formatted_start_time, formatted_end_time) = get_formatted_times(start_time, end_time);

        assert_eq!(formatted_start_time, "7:00 AM");
        assert_eq!(formatted_end_time, "1:00 PM");
    }

    const MOCK_RANGE: TimeRange = (10, 20);

    #[rstest]
    #[case::before_range(9, false)]
    #[case::at_start(10, true)]
    #[case::in_range(15, true)]
    #[case::at_end(20, false)]
    #[case::after_range(21, false)]
    fn test_is_on_air_at_time(#[case] now: i64, #[case] expected: bool) {
        assert_eq!(is_on_air_at_time(MOCK_RANGE, now), expected)
    }

    mod get_time_remaining {
        use super::*;
        const HOURS_WITH_MINUTE_STRING: &str = AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_WITH_HOURS;
        const HOURS_STRING: &str = AV_LRC_LIVE_TV_TIME_REMAINING_ONLY_HOURS;
        const ONE_HOUR_STRING: &str = AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR;
        const MINUTES_STRING: &str = AV_LRC_LIVE_TV_TIME_REMAINING_ON_NOW_MINUTES_ONLY;

        #[rstest]
        // Case 1: With hours and mins, end time: April 21, 2021 8:16:40 AM UTC
        #[case(1618993000000, HOURS_WITH_MINUTE_STRING, "1", "17")]
        // Case 2: Only hours, end time: April 21, 2021 8:00:00 AM UTC
        #[case(1618992000000, ONE_HOUR_STRING, "1", "")]
        // Case 3: Only hours, end time: April 22, 2021 7:00:00 AM UTC
        #[case(1619074800000, HOURS_STRING, "24", "")]
        // Case 4: Only mins, end time: April 21, 2021 7:07:40 AM UTC
        #[case(1618988860000, MINUTES_STRING, "", "8")]
        // Case 5: Less than 1 min, end time: April 21, 2021 7:00:15 AM UTC
        #[case(1618988415000, MINUTES_STRING, "", "1")]
        // Case 6: 0 min remaining, end time: April 21, 2021 7:00:00 AM UTC
        #[case(1618988400000, MINUTES_STRING, "", "1")]
        fn test_get_time_remaining_for_display(
            #[case] end_time: i64,
            #[case] expected_string_id: &str,
            #[case] expected_hours: &str,
            #[case] expected_minutes: &str,
        ) {
            let current_time = 1618988400000; // April 21, 2021 7:00:00 AM UTC
            let result = get_time_remaining(current_time, end_time, false);

            match result {
                TextContent::LocalizedText(localized_text) => {
                    assert_eq!(localized_text.string_id.as_string(), expected_string_id);
                    if !expected_hours.is_empty() {
                        // Here we want to assert that if the localized string_id is `AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR` then the substitution_parameters is empty
                        if localized_text.string_id.as_string()
                            == AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR
                        {
                            assert_eq!(
                                localized_text
                                    .substitution_parameters
                                    .clone()
                                    .unwrap()
                                    .0
                                    .len(),
                                0
                            );
                        } else {
                            assert_eq!(
                                localized_text.substitution_parameters.clone().unwrap().0
                                    ["hoursRemaining"],
                                TextContent::String(expected_hours.to_string())
                            );
                        }
                    }
                    if !expected_minutes.is_empty() {
                        assert_eq!(
                            localized_text.substitution_parameters.unwrap().0["minutesRemaining"],
                            TextContent::String(expected_minutes.to_string())
                        );
                    }
                }
                _ => panic!("Unexpected TextContent variant"),
            }
        }

        #[rstest]
        // Case 1: With hours and mins, end time: April 21, 2021 8:16:40 AM UTC
        #[case(1618993000000, AV_LRC_LIVE_TV_TIME_REMAINING_HOURS_MINUTES, "1", "17")]
        // Case 2: Only hours, end time: April 21, 2021 8:00:00 AM UTC
        #[case(1618992000000, ONE_HOUR_STRING, "1", "")]
        // Case 3: Only hours, end time: April 22, 2021 7:00:00 AM UTC
        #[case(1619074800000, HOURS_STRING, "24", "")]
        // Case 4: Only mins, end time: April 21, 2021 7:07:40 AM UTC
        #[case(1618988860000, AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES, "", "8")]
        // Case 5: Less than 1 min, end time: April 21, 2021 7:00:15 AM UTC
        #[case(1618988415000, AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES, "", "1")]
        // Case 6: 0 min remaining, end time: April 21, 2021 7:00:00 AM UTC
        #[case(1618988400000, AV_LRC_LIVE_TV_TIME_REMAINING_MINUTES, "", "1")]
        fn test_get_time_remaining_for_tts(
            #[case] end_time: i64,
            #[case] expected_string_id: &str,
            #[case] expected_hours: &str,
            #[case] expected_minutes: &str,
        ) {
            let current_time = 1618988400000; // April 21, 2021 7:00:00 AM UTC
            let result = get_time_remaining(current_time, end_time, true);

            match result {
                TextContent::LocalizedText(localized_text) => {
                    assert_eq!(localized_text.string_id.as_string(), expected_string_id);
                    if !expected_hours.is_empty() {
                        // Here we want to assert that if the localized string_id is `AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR` then the substitution_parameters is empty
                        if localized_text.string_id.as_string()
                            == AV_LRC_LIVE_TV_TIME_REMAINING_ONE_HOUR
                        {
                            assert_eq!(
                                localized_text
                                    .substitution_parameters
                                    .clone()
                                    .unwrap()
                                    .0
                                    .len(),
                                0
                            );
                        } else {
                            assert_eq!(
                                localized_text.substitution_parameters.clone().unwrap().0
                                    ["hoursRemaining"],
                                TextContent::String(expected_hours.to_string())
                            );
                        }
                    }
                    if !expected_minutes.is_empty() {
                        assert_eq!(
                            localized_text.substitution_parameters.unwrap().0["minutesRemaining"],
                            TextContent::String(expected_minutes.to_string())
                        );
                    }
                }
                _ => panic!("Unexpected TextContent variant"),
            }
        }
    }
}
