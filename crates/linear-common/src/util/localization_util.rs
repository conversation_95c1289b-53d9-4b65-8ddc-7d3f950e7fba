use ignx_compositron::text::{LocalizedText, SubstitutionParameters, TextContent};

pub fn localize_hierarchy_context(
    season: Option<u32>,
    episode: Option<u32>,
) -> Option<TextContent> {
    // Get string id based on presence of season and episode number
    match (season, episode) {
        (Some(_), Some(_)) => Some("AV_LRC_SEASON_EPISODE_COMPACT".to_string()),
        (Some(_), None) => Some("AV_LRC_SEASON_COMPACT".to_string()),
        (None, Some(_)) => Some("AV_LRC_EPISODE_COMPACT".to_string()),
        (None, None) => None,
    }
    .map(|string_id| {
        // Build text content with substitution parameters
        TextContent::LocalizedText(LocalizedText {
            string_id: string_id.into(),
            substitution_parameters: Some(SubstitutionParameters(
                // Filter out None value and build substitution params
                [("seasonNumber", season), ("episodeNumber", episode)]
                    .iter()
                    .filter_map(|(key, val)| {
                        val.map(|val| ((*key).to_string(), val.to_string().into()))
                    })
                    .collect(),
            )),
        })
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::*;

    #[rstest]
    #[case::season_and_episode(
        (Some(1), Some(2)),
        Some(("AV_LRC_SEASON_EPISODE_COMPACT".to_string(), vec![
            ("seasonNumber".to_string(), "1".to_string()),
            ("episodeNumber".to_string(), "2".to_string()),
        ]))
    )]
    #[case::season_only(
        (Some(1), None),
        Some(("AV_LRC_SEASON_COMPACT".to_string(), vec![
            ("seasonNumber".to_string(), "1".to_string()),
        ]))
    )]
    #[case::episode_only(
        (None, Some(2)),
        Some(("AV_LRC_EPISODE_COMPACT".to_string(), vec![
            ("episodeNumber".to_string(), "2".to_string()),
        ]))
    )]
    #[case::no_data(
        (None, None),
        None
    )]
    fn it_builds_hierarchy_text_correctly(
        #[case] (season, episode): (Option<u32>, Option<u32>),
        #[case] expected_subtitle_text: Option<(String, Vec<(String, String)>)>,
    ) {
        assert_eq!(
            localize_hierarchy_context(season, episode),
            expected_subtitle_text.map(|(string_id, params)| {
                TextContent::LocalizedText(LocalizedText {
                    string_id: string_id.to_string().into(),
                    substitution_parameters: Some(SubstitutionParameters(
                        params
                            .iter()
                            .map(|(key, val)| (key.to_string(), val.to_string().into()))
                            .collect(),
                    )),
                })
            })
        );
    }
}
