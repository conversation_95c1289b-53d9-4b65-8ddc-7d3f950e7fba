use crate::components::contextual_menu_drawer::*;
use crate::components::contextual_menu_hint::*;
use crate::components::overlay::*;
use crate::controllers::transition_controllers::*;
use crate::data_provider::data_provider;
use crate::events::event_manager::ContextualMenuEventManager;
use crate::events::event_signals::*;
use crate::events::on_navigation::on_navigation_manager;
use crate::events::types::ContextualMenuEvent;
use crate::metrics::helpers::report_contextual_menu_overlay_rendered;
use crate::prelude::*;
use crate::types::ui_constants::*;
use contextual_menu_types::prelude::*;
use contextual_menu_types::visibility_context::use_contextual_menu_visibility_signal;
use fableous::pointer_control::use_focus_pointer_control;
use firetv::use_firetv_context;
use ignx_compositron::input::KeyCode;
use ignx_compositron::show::*;
use location::Location;
use std::rc::Rc;

const WATCH_MODAL_TEST_ID: &str = "WatchModalTestId";
const CONTEXTUAL_MENU_TEST_ID: &str = "ContextualMenuTestId";

#[Composer]
pub fn ContextualMenu(
    ctx: &AppContext,
    contextual_menu_data: RwSignal<Option<ContextualMenuData>>,
    // R/W because both the CM and the parent must manage visibility state
    show_contextual_menu_hint: RwSignal<bool>,
    contextual_menu_events: WriteSignal<Vec<ContextualMenuEvent>>,
    notify_navigation: Option<WriteSignal<Option<Location>>>,
) -> StackComposable {
    let (contextual_menu_state, set_contextual_menu_state) =
        create_signal(ctx.scope(), ContextualMenuState::Closed);
    let (opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0f32);
    let (drawer_x_position, set_drawer_x_position) =
        create_signal(ctx.scope(), CONTEXTUAL_MENU_WIDTH);

    let is_contextual_menu_visible = use_contextual_menu_visibility_signal(ctx.scope());

    create_effect(ctx.scope(), move |_| {
        is_contextual_menu_visible.set(contextual_menu_state.get() != ContextualMenuState::Closed)
    });

    let (was_drawer_dismissed, set_was_drawer_dismissed) = create_signal(ctx.scope(), false);

    // Contextual Menu Data Provider
    let contextual_menu_props =
        create_rw_signal::<Option<ContextualMenuProperties>>(ctx.scope(), None);
    data_provider(ctx, contextual_menu_data, contextual_menu_props);

    // Main state transition controller
    transition_controller(
        ctx,
        contextual_menu_state,
        set_contextual_menu_state,
        contextual_menu_props,
        set_overlay_opacity,
        set_drawer_x_position,
        show_contextual_menu_hint,
    );

    // Instantiate Event Manager and internal event signal
    let contextual_menu_event_manager = Rc::new(ContextualMenuEventManager::new());
    let (internal_contextual_menu_event, write_internal_contextual_menu_event) =
        create_signal(ctx.scope(), ContextualMenuEvent::None);

    // Subscribe to internal event signal to record event in the event manager
    event_manager_emitter(
        ctx,
        Rc::clone(&contextual_menu_event_manager),
        internal_contextual_menu_event,
    );

    // Flush events to the external signal when state transitions from Opened to Closing
    event_manager_flusher(
        ctx,
        contextual_menu_state,
        Rc::clone(&contextual_menu_event_manager),
        contextual_menu_events,
    );

    // Instantiate internal on_navigation signal
    let (internal_notify_navigation, write_internal_notify_navigation) =
        create_signal(ctx.scope(), None);

    // When the internal on_navigation signal is set, we need to flush the event buffer and set the external signal to trigger page-level effects
    on_navigation_manager(
        ctx,
        Rc::clone(&contextual_menu_event_manager),
        contextual_menu_events,
        notify_navigation,
        internal_notify_navigation,
    );

    let exit_cb = {
        let ctx = ctx.clone();
        move || {
            // Emit Close Event
            write_internal_contextual_menu_event.set(ContextualMenuEvent::Closed);
            // Call on_dismiss_cb if defined
            if let Some(props) = contextual_menu_props.get_untracked() {
                if let Some(on_dismiss_cb) = &props.on_dismiss_cb {
                    on_dismiss_cb(&ctx, &props);
                }
            }
            // Close the contextual menu (will flush event manager)
            contextual_menu_props.set(None);
            set_was_drawer_dismissed.set(true);
        }
    };
    let on_backspace_key_down = exit_cb.clone();
    let on_escape_key_down = exit_cb.clone();
    let firetv_context = use_firetv_context(ctx);
    let on_menu_key_down = {
        let exit_cb = exit_cb.clone();
        move || {
            if firetv_context.is_firetv() {
                exit_cb();
            }
        }
    };

    let if_builder: Box<ConditionalComposableBuilder> = Box::new({
        move |ctx| {
            let composable = compose! {
                 ContextualMenuDrawer(contextual_menu_data: contextual_menu_props, drawer_x_position, write_contextual_menu_event: write_internal_contextual_menu_event, read_contextual_menu_event: internal_contextual_menu_event, was_drawer_dismissed, notify_navigation: write_internal_notify_navigation, exit_cb: Rc::new(exit_cb.clone()))
                    // Close handlers
                    .on_key_down(KeyCode::Backspace, on_backspace_key_down.clone())
                    .on_key_down(KeyCode::Escape, on_escape_key_down.clone())
                    .on_key_down(KeyCode::Menu, on_menu_key_down.clone())
                    .focus_pointer_control(use_focus_pointer_control(ctx.scope()))
                    .focus_window()
                    .test_id(contextual_menu_data.with_untracked(|data| {
                         match data {
                             Some(ContextualMenuData::WatchModal(_)) => WATCH_MODAL_TEST_ID,
                                 _ => CONTEXTUAL_MENU_TEST_ID,
                             }
                     }))
            };
            composable.into_widget()
        }
    });

    create_effect(ctx.scope(), move |_| {
        if contextual_menu_state.get() == ContextualMenuState::Opened {
            contextual_menu_props.with_untracked(|props| {
                if let Some(props) = props {
                    report_contextual_menu_overlay_rendered(&props.metrics_context);
                }
            });
        }
    });

    compose! {
        Stack() {
            Overlay(opacity)

            ContextualMenuHint(show_contextual_menu_hint)

            Show(condition: is_contextual_menu_visible.into(), if_builder, else_builder: None)
        }
        .alignment(Alignment::EndCenter)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::components::body::buttons::hide_this_button::HIDE_THIS_BUTTON_TEST_ID;
    use crate::components::body::buttons::more_details_button::MORE_DETAILS_BUTTON_TEST_ID;
    use crate::components::body::buttons::record_season_checkbox::RECORD_SEASON_CHECKBOX_TEST_ID;
    use crate::components::body::buttons::watchlist_button::WATCHLIST_BUTTON_TEST_ID;
    use crate::events::types::ToggleEvent;
    use crate::test_utils::{
        get_mock_props, get_mock_props_with_buttons, open_cm,
        setup_mock_personalisation_service_success, setup_mock_watchlist_service_success,
    };
    use contextual_menu_types::prelude::ContextualMenuData;
    use firetv::MockFireTV;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::input::KeyEventType;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use ignx_compositron::time::{Duration, MockClock};
    use router::{MockRouting, RoutingContext};
    use rstest::{fixture, rstest};
    use rust_features::provide_context_test_rust_features;

    #[fixture]
    fn simple_mock_cm_data() -> ContextualMenuData {
        ContextualMenuData::ContextualMenu(get_mock_props())
    }

    #[fixture]
    fn simple_mock_watch_modal_cm_data() -> ContextualMenuData {
        ContextualMenuData::WatchModal(get_mock_props())
    }

    fn provide_firetv_context(ctx: &AppContext, is_firetv: bool) {
        let mut firetv = MockFireTV::default();
        firetv.expect_is_firetv().return_const(is_firetv);
        firetv.provide_mock(ctx.scope());
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn should_render_hint_accordingly_to_its_signal(#[case] hint_signal_value: bool) {
        launch_test(
            move |ctx| {
                let show_contextual_menu_hint = create_rw_signal(ctx.scope(), hint_signal_value);
                let contextual_menu_data = create_rw_signal(ctx.scope(), None);
                let (_contextual_menu_events, set_contextual_menu_events) =
                    create_signal(ctx.scope(), Vec::new());
                provide_firetv_context(&ctx, false);
                compose! {
                    ContextualMenu(contextual_menu_data, show_contextual_menu_hint, contextual_menu_events: set_contextual_menu_events, notify_navigation: None)
                }
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let contextual_menu_overlay = tree.find_by_test_id(OVERLAY_TEST_ID);
                let contextual_menu_hint = tree.find_by_test_id(HINT_TEST_ID);
                let contextual_menu_drawer = tree.find_by_test_id(DRAWER_TEST_ID);

                if hint_signal_value {
                    assert_node_exists!(contextual_menu_hint);
                } else {
                    assert_node_does_not_exist!(contextual_menu_hint);
                }

                assert_eq!(
                    contextual_menu_overlay
                        .borrow_props()
                        .base_styles
                        .opacity
                        .unwrap(),
                    0.0
                );
                assert_node_does_not_exist!(contextual_menu_drawer);
            },
        )
    }

    #[rstest]
    #[case(None)]
    #[case(Some(simple_mock_cm_data()))]
    #[case(Some(simple_mock_watch_modal_cm_data()))]
    fn should_render_drawer_and_overlay_and_hint_accordingly_to_cm_data(
        #[case] cm_data_signal_value: Option<ContextualMenuData>,
    ) {
        let is_visible = cm_data_signal_value.is_some();
        launch_test(
            move |ctx| {
                let show_contextual_menu_hint = create_rw_signal(ctx.scope(), true);
                let contextual_menu_data = create_rw_signal(ctx.scope(), cm_data_signal_value);
                let (_contextual_menu_events, set_contextual_menu_events) =
                    create_signal(ctx.scope(), Vec::new());
                provide_firetv_context(&ctx, false);
                provide_context_test_rust_features(ctx.scope());

                compose! {
                    ContextualMenu(contextual_menu_data, show_contextual_menu_hint, contextual_menu_events: set_contextual_menu_events, notify_navigation: None)
                }
            },
            move |_scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let tree = test_game_loop.tick_until_done();
                let contextual_menu_overlay = tree.find_by_test_id(OVERLAY_TEST_ID);
                let contextual_menu_hint = tree.find_by_test_id(HINT_TEST_ID);
                let contextual_menu_drawer = tree.find_by_test_id(DRAWER_TEST_ID);

                if is_visible {
                    assert_node_exists!(contextual_menu_drawer);
                    assert_node_exists!(contextual_menu_overlay);
                    assert_node_does_not_exist!(contextual_menu_hint);
                } else {
                    assert_node_does_not_exist!(contextual_menu_drawer);
                    assert_eq!(
                        contextual_menu_overlay
                            .borrow_props()
                            .base_styles
                            .opacity
                            .unwrap(),
                        0.0
                    );
                    assert_node_exists!(contextual_menu_hint);
                }
            },
        )
    }

    #[fixture]
    fn mock_cm_data_with_buttons() -> ContextualMenuData {
        ContextualMenuData::ContextualMenu(get_mock_props_with_buttons())
    }

    #[rstest]
    #[case(Some(mock_cm_data_with_buttons()))]
    fn can_navigate_drawer_button_list_in_added_order(
        #[case] cm_data_signal_value: Option<ContextualMenuData>,
    ) {
        launch_test(
            move |ctx| {
                let show_contextual_menu_hint = create_rw_signal(ctx.scope(), true);
                let contextual_menu_data = create_rw_signal(ctx.scope(), cm_data_signal_value);
                let (_contextual_menu_events, set_contextual_menu_events) =
                    create_signal(ctx.scope(), Vec::new());
                provide_firetv_context(&ctx, false);
                provide_context_test_rust_features(ctx.scope());

                compose! {
                    ContextualMenu(contextual_menu_data, show_contextual_menu_hint, contextual_menu_events: set_contextual_menu_events, notify_navigation: None)
                }
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();

                let watchlist_button = tree.find_by_test_id(WATCHLIST_BUTTON_TEST_ID);
                let mode_details_button = tree.find_by_test_id(MORE_DETAILS_BUTTON_TEST_ID);
                let hide_this_button = tree.find_by_test_id(HIDE_THIS_BUTTON_TEST_ID);
                let record_season_checkbox_button =
                    tree.find_by_test_id(RECORD_SEASON_CHECKBOX_TEST_ID);

                // Assert first button is focused by default
                assert!(watchlist_button.borrow_props().is_focused);

                // Assert all expected buttons exist
                assert_node_exists!(watchlist_button);
                assert_node_exists!(mode_details_button);
                assert_node_exists!(hide_this_button);
                assert_node_exists!(record_season_checkbox_button);

                // Assert button navigation works as expected
                test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Down);
                let tree = test_game_loop.tick_until_done();

                let mode_details_button = tree.find_by_test_id(MORE_DETAILS_BUTTON_TEST_ID);
                assert!(mode_details_button.borrow_props().is_focused);

                test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Down);
                let tree = test_game_loop.tick_until_done();

                let hide_this_button = tree.find_by_test_id(HIDE_THIS_BUTTON_TEST_ID);
                assert!(hide_this_button.borrow_props().is_focused);

                test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Down);
                let tree = test_game_loop.tick_until_done();

                let record_season_checkbox_button =
                    tree.find_by_test_id(RECORD_SEASON_CHECKBOX_TEST_ID);
                assert!(record_season_checkbox_button.borrow_props().is_focused);

                // cannot go further that last button
                test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Down);
                let tree = test_game_loop.tick_until_done();
                let record_season_checkbox_button =
                    tree.find_by_test_id(RECORD_SEASON_CHECKBOX_TEST_ID);
                assert!(record_season_checkbox_button.borrow_props().is_focused);

                // Go up 10 times, the first button should be focused (doesn't jump to top nav)
                for _ in 0..10 {
                    test_game_loop.send_key_press_event(KeyEventType::ButtonDown, KeyCode::Up);
                }
                let tree = test_game_loop.tick_until_done();
                let watchlist_button = tree.find_by_test_id(WATCHLIST_BUTTON_TEST_ID);
                assert!(watchlist_button.borrow_props().is_focused);
            },
        )
    }

    #[rstest]
    #[case(Some(mock_cm_data_with_buttons()))]
    fn emits_events_for_interactions(#[case] cm_data_signal_value: Option<ContextualMenuData>) {
        let cm_data_signal_value_cloned = cm_data_signal_value.clone();
        launch_test(
            move |ctx| {
                let show_contextual_menu_hint = create_rw_signal(ctx.scope(), true);
                let contextual_menu_data = create_rw_signal(ctx.scope(), cm_data_signal_value);
                let (contextual_menu_events, set_contextual_menu_events) =
                    create_signal(ctx.scope(), Vec::new());

                provide_context(ctx.scope(), contextual_menu_data);
                provide_context(ctx.scope(), contextual_menu_events);
                provide_firetv_context(&ctx, false);
                provide_context_test_rust_features(ctx.scope());

                compose! {
                    ContextualMenu(contextual_menu_data, show_contextual_menu_hint, contextual_menu_events: set_contextual_menu_events, notify_navigation: None)
                }
            },
            move |scope, mut test_game_loop| {
                open_cm(&mut test_game_loop);

                let events_signal = use_context::<ReadSignal<Vec<ContextualMenuEvent>>>(scope);
                let cm_data_signal = use_context::<RwSignal<Option<ContextualMenuData>>>(scope);

                let tree = test_game_loop.tick_until_done();

                // Hide this button
                let _personalisation_service_ctx = setup_mock_personalisation_service_success();
                let hide_this_button = tree.find_by_test_id(HIDE_THIS_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(hide_this_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id(DRAWER_TEST_ID);
                test_game_loop.send_key_down_up_event_to_node(
                    drawer.borrow_props().node_id,
                    KeyCode::Backspace,
                );

                test_game_loop.tick_until_done();

                let events = events_signal.unwrap().get_untracked();
                assert_eq!(events.len(), 3);
                assert_eq!(events[0], ContextualMenuEvent::None);
                assert_eq!(events[2], ContextualMenuEvent::Closed);
                match &events[1] {
                    ContextualMenuEvent::HideThis(event) => {
                        assert_eq!(event.gti, "Test gti".to_string());
                        assert!(event.is_hidden);
                    }
                    _ => panic!("unexpected event"),
                }

                // Re-open the CM
                cm_data_signal.unwrap().set(cm_data_signal_value_cloned);

                open_cm(&mut test_game_loop);

                // Watchlist toggle
                let _watchlist_service_ctx = setup_mock_watchlist_service_success();
                let watchlist_button = tree.find_by_test_id(WATCHLIST_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(watchlist_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id(DRAWER_TEST_ID);
                test_game_loop.send_key_down_up_event_to_node(
                    drawer.borrow_props().node_id,
                    KeyCode::Backspace,
                );

                test_game_loop.tick_until_done();

                let events = events_signal.unwrap().get_untracked();
                assert_eq!(events.len(), 2);
                assert_eq!(events[1], ContextualMenuEvent::Closed);
                match &events[0] {
                    ContextualMenuEvent::ToggleWatchlist(event) => {
                        assert_eq!(
                            *event,
                            ToggleEvent {
                                gti: "Test gti".to_string(),
                                new_value: true,
                            }
                        );
                    }
                    _ => panic!("unexpected event"),
                }
            },
        )
    }

    #[test]
    fn emits_events_and_sets_signal_on_navigation() {
        launch_test(
            move |ctx| {
                let cm_data = mock_cm_data_with_buttons();
                let show_contextual_menu_hint = create_rw_signal(ctx.scope(), true);
                let contextual_menu_data = create_rw_signal(ctx.scope(), Some(cm_data));
                let (contextual_menu_events, set_contextual_menu_events) =
                    create_signal(ctx.scope(), Vec::new());
                let (notify_navigation, set_notify_navigation) =
                    create_signal::<Option<Location>>(ctx.scope(), None);

                provide_firetv_context(&ctx, false);
                provide_context_test_rust_features(ctx.scope());
                let mut mock_routing_context = MockRouting::new();
                mock_routing_context
                    .expect_navigate()
                    .times(1)
                    .returning(|_, _| ());

                provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
                provide_context(ctx.scope(), contextual_menu_events);
                provide_context(ctx.scope(), notify_navigation);

                compose! {
                    ContextualMenu(contextual_menu_data, show_contextual_menu_hint, contextual_menu_events: set_contextual_menu_events, notify_navigation: Some(set_notify_navigation))
                }
            },
            move |scope, mut test_game_loop| {
                open_cm(&mut test_game_loop);

                let cm_events_signal =
                    expect_context::<ReadSignal<Vec<ContextualMenuEvent>>>(scope);
                let on_navigation_signal = expect_context::<ReadSignal<Option<Location>>>(scope);

                let tree = test_game_loop.tick_until_done();

                assert!(on_navigation_signal.get_untracked().is_none());

                // Add to Watchlist to test event emission
                let _watchlist_service_ctx = setup_mock_watchlist_service_success();
                let watchlist_button = tree.find_by_test_id(WATCHLIST_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(watchlist_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                // Click More Details
                let more_details = tree.find_by_test_id(MORE_DETAILS_BUTTON_TEST_ID);
                test_game_loop.send_on_select_event(more_details.borrow_props().node_id);
                test_game_loop.tick_until_done();

                // transition is delayed in More Details button, so we must delay here
                MockClock::advance(Duration::from_millis(350));
                test_game_loop.tick_until_done();

                let events = cm_events_signal.get_untracked();

                // Ensure events were emitted
                assert_eq!(events.len(), 2);
                assert_eq!(events[0], ContextualMenuEvent::None);
                assert_eq!(
                    events[1],
                    ContextualMenuEvent::ToggleWatchlist(ToggleEvent {
                        gti: "Test gti".to_string(),
                        new_value: true
                    })
                );

                // Ensure on_navigation was set
                assert!(on_navigation_signal.get_untracked().is_some());
            },
        )
    }

    #[rstest]
    #[case(Some(mock_cm_data_with_buttons()))]
    fn closes_contextual_menu_when_presisng_menu_key_on_firetv(
        #[case] cm_data_signal_value: Option<ContextualMenuData>,
    ) {
        let cm_data_signal_value_cloned = cm_data_signal_value.clone();
        launch_test(
            move |ctx| {
                let show_contextual_menu_hint = create_rw_signal(ctx.scope(), true);
                let contextual_menu_data = create_rw_signal(ctx.scope(), cm_data_signal_value);
                let (contextual_menu_events, set_contextual_menu_events) =
                    create_signal(ctx.scope(), Vec::new());

                provide_context(ctx.scope(), contextual_menu_data);
                provide_context(ctx.scope(), contextual_menu_events);
                provide_firetv_context(&ctx, true);
                provide_context_test_rust_features(ctx.scope());

                compose! {
                    ContextualMenu(contextual_menu_data, show_contextual_menu_hint, contextual_menu_events: set_contextual_menu_events, notify_navigation: None)
                }
            },
            move |scope, mut test_game_loop| {
                let events_signal = use_context::<ReadSignal<Vec<ContextualMenuEvent>>>(scope);
                let cm_data_signal = use_context::<RwSignal<Option<ContextualMenuData>>>(scope);

                cm_data_signal.unwrap().set(cm_data_signal_value_cloned);
                open_cm(&mut test_game_loop);

                let tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id(DRAWER_TEST_ID);
                test_game_loop
                    .send_key_down_up_event_to_node(drawer.borrow_props().node_id, KeyCode::Menu);

                test_game_loop.tick_until_done();

                let events = events_signal.unwrap().get_untracked();
                assert_eq!(events.len(), 2);
                assert_eq!(events[0], ContextualMenuEvent::None);
                assert_eq!(events[1], ContextualMenuEvent::Closed);
            },
        )
    }

    #[rstest]
    #[case(Some(mock_cm_data_with_buttons()))]
    fn does_not_close_contextual_menu_if_not_firetv(
        #[case] cm_data_signal_value: Option<ContextualMenuData>,
    ) {
        let cm_data_signal_value_cloned = cm_data_signal_value.clone();
        launch_test(
            move |ctx| {
                let show_contextual_menu_hint = create_rw_signal(ctx.scope(), true);
                let contextual_menu_data = create_rw_signal(ctx.scope(), cm_data_signal_value);
                let (contextual_menu_events, set_contextual_menu_events) =
                    create_signal(ctx.scope(), Vec::new());

                provide_context(ctx.scope(), contextual_menu_data);
                provide_context(ctx.scope(), contextual_menu_events);
                provide_firetv_context(&ctx, false);
                provide_context_test_rust_features(ctx.scope());

                compose! {
                    ContextualMenu(contextual_menu_data, show_contextual_menu_hint, contextual_menu_events: set_contextual_menu_events, notify_navigation: None)
                }
            },
            move |scope, mut test_game_loop| {
                let events_signal = use_context::<ReadSignal<Vec<ContextualMenuEvent>>>(scope);
                let cm_data_signal = use_context::<RwSignal<Option<ContextualMenuData>>>(scope);

                cm_data_signal.unwrap().set(cm_data_signal_value_cloned);
                open_cm(&mut test_game_loop);

                let tree = test_game_loop.tick_until_done();

                let drawer = tree.find_by_test_id(DRAWER_TEST_ID);
                test_game_loop
                    .send_key_down_up_event_to_node(drawer.borrow_props().node_id, KeyCode::Menu);

                test_game_loop.tick_until_done();

                let events = events_signal.unwrap().get_untracked();
                assert_eq!(events.len(), 0);
            },
        )
    }
}
