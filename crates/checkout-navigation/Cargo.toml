[package]
name = "checkout-navigation"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
location.workspace = true
rust-features.workspace = true
serde.workspace = true
serde_json.workspace = true
cross-app-events.workspace = true
log.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["test_utils"] }
rstest.workspace = true
rust-features.workspace = true

[features]
test_utils = []
