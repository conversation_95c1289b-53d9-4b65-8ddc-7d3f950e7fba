use cross_app_events::create_serde_map;
#[cfg(any(test, feature = "test_utils"))]
use ignx_compositron::app::rpc::MockRPCManager as RPCManager;
#[cfg(not(any(test, feature = "test_utils")))]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::{
    device_information::DeviceInformation,
    prelude::{metric, use_context, Scope},
};
use location::{JSPage, Location, PageType, RustPage};
#[cfg(any(test, feature = "test_utils"))]
use rust_features::try_use_mock_rust_features as try_use_rust_features;
#[cfg(not(any(test, feature = "test_utils")))]
use rust_features::try_use_rust_features;
#[cfg(any(test, feature = "test_utils"))]
use rust_features::MockRustFeatures as RustFeatures;
#[cfg(not(any(test, feature = "test_utils")))]
use rust_features::RustFeatures;
use serde::{Deserialize, Serialize};
use serde_json::{Map, Value};

use crate::{
    utils::{check_rust_checkout_weblab_enabled, request_is_channel_bundle},
    workflow_type_identifier::{get_checkout_workflow_type, AcquisitionWorkflowType},
};

#[derive(Serialize, Deserialize, Clone, Debug, PartialEq)]
#[serde(rename_all_fields = "camelCase")]
#[serde(tag = "checkout_request_type")]
pub enum StartCheckoutRequest {
    Channels {
        benefit_id: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        ref_marker: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        bundle_id: Option<String>,
        ingress_point: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        rust_source_name: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        title_id: Option<String>,
        #[serde(flatten)]
        #[serde(skip_serializing_if = "Option::is_none")]
        #[serde(with = "location_serde")]
        deferred_navigation_location: Option<Location>,
    },
    PrimeAddOn {
        #[serde(skip_serializing_if = "Option::is_none")]
        ref_marker: Option<String>,
        ingress_point: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        rust_source_name: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        title_id: Option<String>,
    },
    Boomerang {
        #[serde(skip_serializing_if = "Option::is_none")]
        ref_marker: Option<String>,
        ingress_point: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        rust_source_name: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        title_id: Option<String>,
    },
}

mod location_serde {
    /* Serialise/deserialise a Location in the format React Checkout expects for deferred navigation.
    A Location will serialise with fields pageType and pageParams, but React Checkout expects the fields
    destinationPageType and destinationPageParams. All this code does is translate the Location to DestinationPage
    in order to rename the fields */
    use location::{Location, PageType};
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use serde_json::Map;

    #[derive(Serialize, Deserialize)]
    #[serde(rename_all = "camelCase")]
    struct DestinationPage {
        destination_page_type: PageType,
        destination_page_params: Map<String, serde_json::Value>,
    }

    pub fn serialize<S>(location: &Option<Location>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        location
            .as_ref()
            .map(
                |Location {
                     pageType,
                     pageParams,
                 }| {
                    DestinationPage {
                        destination_page_type: *pageType,
                        destination_page_params: pageParams.clone(),
                    }
                },
            )
            .serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<Location>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let maybe_location_fields: Option<DestinationPage> =
            Deserialize::deserialize(deserializer)?;
        let maybe_location = maybe_location_fields.map(
            |DestinationPage {
                 destination_page_type,
                 destination_page_params,
             }| {
                Location {
                    pageType: destination_page_type,
                    pageParams: destination_page_params,
                }
            },
        );
        Ok(maybe_location)
    }
}

const ANDROID_OS_NAME: &str = "Android";

fn should_use_rust_checkout_for_channels(
    rust_features_option: &Option<RustFeatures>,
    rpc_manager_option: &Option<RPCManager>,
    benefit_id: &str,
) -> bool {
    let device_information = DeviceInformation::new();
    let os_name = device_information.os_name;

    os_name != ANDROID_OS_NAME
        && get_checkout_workflow_type(benefit_id) == AcquisitionWorkflowType::Channels
        && !request_is_channel_bundle(benefit_id)
        && check_rust_checkout_weblab_enabled(rust_features_option, rpc_manager_option)
}

pub fn prepare_navigation_to_checkout(scope: Scope, request: StartCheckoutRequest) -> Location {
    prepare_navigation_to_checkout_with_features(
        &try_use_rust_features(scope),
        &use_context::<RPCManager>(scope),
        request,
    )
}

pub fn prepare_navigation_to_checkout_with_features(
    rust_features_option: &Option<RustFeatures>,
    rpc_manager_option: &Option<RPCManager>,
    request: StartCheckoutRequest,
) -> Location {
    match &request {
        StartCheckoutRequest::Channels { benefit_id, .. } => {
            if should_use_rust_checkout_for_channels(
                rust_features_option,
                rpc_manager_option,
                benefit_id,
            ) {
                prepare_navigation_to_rust_checkout(request)
            } else {
                prepare_navigation_to_react_checkout(request)
            }
        }
        StartCheckoutRequest::PrimeAddOn { .. } => prepare_navigation_to_react_checkout(request),
        StartCheckoutRequest::Boomerang { .. } => prepare_navigation_to_react_checkout(request),
    }
}

pub fn start_checkout(
    scope: Scope,
    request: StartCheckoutRequest,
    navigate: impl Fn(Location, &str),
    source: &str,
) {
    navigate(prepare_navigation_to_checkout(scope, request), source);
}

fn prepare_navigation_to_rust_checkout(request: StartCheckoutRequest) -> Location {
    prepare_navigation_to_checkout_helper(request, PageType::Rust(RustPage::RUST_CHECKOUT))
}

fn prepare_navigation_to_react_checkout(request: StartCheckoutRequest) -> Location {
    prepare_navigation_to_checkout_helper(request, PageType::Js(JSPage::NEW_CONFIRMATION_PAGE))
}

fn prepare_navigation_to_checkout_helper(
    request: StartCheckoutRequest,
    checkout_page_type: PageType,
) -> Location {
    let nav_params = build_nav_params(request);
    match nav_params {
        Ok(nav_params) => Location {
            pageType: checkout_page_type,
            pageParams: nav_params,
        },
        Err(error) => {
            log::error!("failed to navigate to checkout - {}", error);
            metric!("Checkout.Error", 1.0, "errorType" => "NavigationError");
            Location {
                pageType: PageType::Js(location::JSPage::ERROR_PAGE),
                pageParams: create_serde_map([(
                    "redirectButtonsEnabled",
                    serde_json::Value::Bool(true),
                )]),
            }
        }
    }
}

fn build_nav_params(request: StartCheckoutRequest) -> Result<Map<String, Value>, String> {
    let value =
        serde_json::to_value(&request).map_err(|err| format!("serde_json error {}", err))?;

    match value {
        Value::Object(mut map) => {
            // JS checkout expects boomerang use cases to have benefitId == Boomerang but we don't
            // include this field in StartCheckoutRequest, so json_serde doesn't set it. Instead we set it
            // manually here.
            if let StartCheckoutRequest::Boomerang { .. } = request {
                map.insert("benefitId".to_owned(), "Boomerang".to_owned().into());
            } else if let StartCheckoutRequest::PrimeAddOn { .. } = request {
                map.insert("benefitId".to_owned(), "primeaddon".to_owned().into());
            }
            Ok(map)
        }
        _ => Err(
            "nav params were serialized successfully but not as a map - this should never happen"
                .to_string(),
        ),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use cross_app_events::create_serde_map;
    use ignx_compositron::{app::launch_only_scope, prelude::provide_context};
    use rstest::rstest;
    use rust_features::MockRustFeaturesBuilder;
    use std::cell::Cell;

    struct StartCheckoutTestCase {
        request: StartCheckoutRequest,
        is_rust_checkout_enabled: bool,
        expected_location: Location,
    }

    #[rstest]
    // channels minimal case with rust enabled
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Channels {
            benefit_id: "test_benefit".to_string(),
            ref_marker: None,
            ingress_point: "ingress_point".to_string(),
            rust_source_name: None,
            title_id: None,
            bundle_id: None,
            deferred_navigation_location: None
        },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Rust(RustPage::RUST_CHECKOUT),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Channels".to_string()),
            ), (
                "benefitId", Value::String("test_benefit".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            )])
         }
    })]
    // channels with all optional parameters and rust enabled
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Channels {
            benefit_id: "test_benefit".to_string(),
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
            bundle_id: Some("bundle_id".to_string()),
            deferred_navigation_location: Some(Location {
                pageType: PageType::Js(JSPage::COLLECTION_PAGE),
                pageParams: create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])
            }) },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Rust(RustPage::RUST_CHECKOUT),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Channels".to_string()),
            ), (
                "benefitId", Value::String("test_benefit".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            ), (
                "destinationPageType", Value::String("COLLECTION_PAGE".to_string()),
            ), (
                "destinationPageParams", Value::Object(create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])),
            ), (
                "bundleId", Value::String("bundle_id".to_string()),
            )])
         }
    })]
    // channels with all optional parameters and rust not enabled
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Channels {
            benefit_id: "test_benefit".to_string(),
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
            bundle_id: Some("bundle_id".to_string()),
            deferred_navigation_location: Some(Location {
                pageType: PageType::Js(JSPage::COLLECTION_PAGE),
                pageParams: create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])
            }) },
        is_rust_checkout_enabled: false,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Channels".to_string()),
            ), (
                "benefitId", Value::String("test_benefit".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            ), (
                "destinationPageType", Value::String("COLLECTION_PAGE".to_string()),
            ), (
                "destinationPageParams", Value::Object(create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])),
            ),
            (
                "bundleId", Value::String("bundle_id".to_string()),
            )])
         }
    })]
    // invalid channels request with prime benefit id
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Channels {
            benefit_id: "Prime".to_string(),
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
            bundle_id: None,
            deferred_navigation_location: Some(Location {
                pageType: PageType::Js(JSPage::COLLECTION_PAGE),
                pageParams: create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])
            }) },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Channels".to_string()),
            ), (
                "benefitId", Value::String("Prime".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            ), (
                "destinationPageType", Value::String("COLLECTION_PAGE".to_string()),
            ), (
                "destinationPageParams", Value::Object(create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])),
            )])
         }
    })]
    // invalid channels request with primeaddon benefit id
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Channels {
            benefit_id: "primeaddon".to_string(),
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
            bundle_id: None,
            deferred_navigation_location: Some(Location {
                pageType: PageType::Js(JSPage::COLLECTION_PAGE),
                pageParams: create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])
            }) },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Channels".to_string()),
            ), (
                "benefitId", Value::String("primeaddon".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            ), (
                "destinationPageType", Value::String("COLLECTION_PAGE".to_string()),
            ), (
                "destinationPageParams", Value::Object(create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])),
            )])
         }
    })]
    // primeaddon with rust enabled directs to JS
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::PrimeAddOn {
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
        },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("PrimeAddOn".to_string()),
            ), (
                "benefitId", Value::String("primeaddon".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            )])
         }
    })]
    // primeaddon with rust not enabled directs to JS
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::PrimeAddOn {
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
        },
        is_rust_checkout_enabled: false,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("PrimeAddOn".to_string()),
            ), (
                "benefitId", Value::String("primeaddon".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            )])
         }
    })]
    // boomerang with rust enabled directs to JS
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Boomerang {
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
        },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Boomerang".to_string()),
            ), (
                "benefitId", Value::String("Boomerang".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            )])
         }
    })]
    // boomerang with rust not enabled directs to JS
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Boomerang {
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
        },
        is_rust_checkout_enabled: false,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Boomerang".to_string()),
            ), (
                "benefitId", Value::String("Boomerang".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            )])
         }
    })]
    // channel bundle (benefit_id is a spid) should go to React checkout even if rust is enabled
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Channels {
            benefit_id: "amzn1.bundle.test".to_string(),
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
            bundle_id: None,
            deferred_navigation_location: None
        },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Js(JSPage::NEW_CONFIRMATION_PAGE),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Channels".to_string()),
            ), (
                "benefitId", Value::String("amzn1.bundle.test".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            )])
        }
    })]
    // channel requiring legal disclaimer regarding sharing personal data should go to rust checkout
    #[case(StartCheckoutTestCase {
        request: StartCheckoutRequest::Channels {
            benefit_id: "daznfutboles".to_string(),
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
            bundle_id: None,
            deferred_navigation_location: None
        },
        is_rust_checkout_enabled: true,
        expected_location: Location {
            pageType: PageType::Rust(RustPage::RUST_CHECKOUT),
            pageParams: create_serde_map([(
                "checkout_request_type", Value::String("Channels".to_string()),
            ), (
                "benefitId", Value::String("daznfutboles".to_string()),
            ), (
                "ingressPoint", Value::String("ingress_point".to_string()),
            ), (
                "rustSourceName", Value::String("rust_source_name".to_string()),
            ), (
                "refMarker", Value::String("ref_marker".to_string()),
            ), (
                "titleId", Value::String("title_id".to_string()),
            )])
        }
    })]
    fn test_start_checkout(#[case] test_params: StartCheckoutTestCase) {
        let StartCheckoutTestCase {
            request,
            is_rust_checkout_enabled,
            expected_location,
        } = test_params;

        launch_only_scope(move |scope| {
            let features = MockRustFeaturesBuilder::new()
                .set_is_rust_checkout_enabled(is_rust_checkout_enabled)
                .build();
            provide_context(scope, features);

            let navigate_called = Cell::new(false);
            let navigate = |location: Location, source: &str| {
                navigate_called.set(true);
                assert_eq!(source, "test_source");
                assert_eq!(location, expected_location)
            };

            start_checkout(scope, request, navigate, "test_source");

            assert!(navigate_called.get());
        });
    }

    #[rstest]
    #[case(StartCheckoutRequest::Channels {
            benefit_id: "test_benefit".to_string(),
            ref_marker: None,
            ingress_point: "ingress_point".to_string(),
            rust_source_name: None,
            title_id: None,
            bundle_id: None,
            deferred_navigation_location: None
    })]
    #[case(StartCheckoutRequest::Channels {
            benefit_id: "test_benefit".to_string(),
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
            bundle_id: None,
            deferred_navigation_location: Some(Location {
                pageType: PageType::Js(JSPage::COLLECTION_PAGE),
                pageParams: create_serde_map([(
                    "test", Value::String("foo".to_string())
                )])
            })
    })]
    #[case(StartCheckoutRequest::PrimeAddOn {
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
    })]
    #[case(StartCheckoutRequest::Boomerang {
            ref_marker: Some("ref_marker".to_string()),
            ingress_point: "ingress_point".to_string(),
            rust_source_name: Some("rust_source_name".to_string()),
            title_id: Some("title_id".to_string()),
    })]
    fn test_serialize_then_deserialize(#[case] request: StartCheckoutRequest) {
        let serialized = serde_json::to_string(&request).unwrap();
        let deserialised: StartCheckoutRequest = serde_json::from_str(&serialized).unwrap();

        assert_eq!(request, deserialised);
    }
}
