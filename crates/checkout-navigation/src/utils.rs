#[cfg(any(test, feature = "test_utils"))]
use ignx_compositron::app::rpc::MockRPCManager as RPCManager;
#[cfg(not(any(test, feature = "test_utils")))]
use ignx_compositron::app::rpc::RPCManager;
#[cfg(any(test, feature = "test_utils"))]
use rust_features::MockRustFeatures as RustFeatures;
#[cfg(not(any(test, feature = "test_utils")))]
use rust_features::RustFeatures;

// Source of truth
// https://translator.amazon.com/search/strings?tag=aiv_acquisition_blast_*_body&exact_match=on&text=Share&stage%5Bprod%5D=prod&locale_invert=false&locale=1005&marketplace_invert=false&marketplace=-1&is_untranslated_locale_by_string=false&untranslated_locale=-1&stringset_invert=false&set=-1&team_invert=false&group=-1&query_id=&last_requested_date_type=within&last_requested_date=&last_updated_date_type=within&last_updated_date=&show_advanced=1
const CHANNEL_BENEFIT_IDS_WITH_LEGAL_SHARE_DATA_DISCLAIMERS: [&str; 30] = [
    "discoveryplusde",
    "discoverytntuk",
    "daznfutboles",
    "hbomaxus",
    "hbomaxco",
    "discoveryplus",
    "warnerpassfr",
    "diiscoveryplus",
    "discoveryplusonlynl",
    "discoveryplussportuk",
    "daznvictoriaes",
    "daznde",
    "daznoriginalses",
    "discoveryplusuk",
    "discoveryplusbr",
    "daznes",
    "daznmotores",
    "daznjp",
    "discoveryplusca",
    "hbomaxbr",
    "daznesenciales",
    "discoverypluspremiumuk",
    "starzSub",
    "amcplus",
    "cinemaxmax",
    "liveeventsus",
    "discoverysportuk",
    "daznlaligaes",
    "saltofr",
    "discoveryplusnl",
];

pub fn request_is_channel_bundle(benefit_id: &str) -> bool {
    // We pass bundles as spids whilst we pass regular channels as channel names
    // Therefore by detecting if this is a spid we can deduce if it's a bundle
    benefit_id.starts_with("amzn1")
}

pub fn check_rust_checkout_weblab_enabled(
    rust_features_option: &Option<RustFeatures>,
    rpc_manager_option: &Option<RPCManager>,
) -> bool {
    if let Some(rust_features) = rust_features_option {
        if let Some(rpc_manager) = rpc_manager_option {
            record_weblab_trigger(rpc_manager, TriggerFn::RustCheckout)
        } else {
            log::error!(
                "[Start Checkout] Failed to locate RPCManager - will not record triggers for rust checkout"
            )
        }

        rust_features.is_rust_checkout_enabled()
    } else {
        log::error!("[Start Checkout] Failed to locate RustFeatures");
        false
    }
}

enum TriggerFn {
    RustCheckout,
}

impl std::fmt::Display for TriggerFn {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let fn_name = match self {
            TriggerFn::RustCheckout => "recordRustCheckoutRustTrigger",
        };

        write!(f, "{}", fn_name)
    }
}

fn record_weblab_trigger(rpc_manager: &RPCManager, trigger_function: TriggerFn) {
    rpc_manager
        .call::<()>(&trigger_function.to_string())
        .error_callback(Box::new(move |error| {
            log::error!("[Start Checkout] {} failed {}", trigger_function, error)
        }))
        .send();
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::request_is_channel_bundle;
    use ignx_compositron::app::rpc::{MockRPCCall, MockRPCManager};
    use rust_features::MockRustFeaturesBuilder;

    use super::record_weblab_trigger;

    #[test]
    fn request_is_channel_bundle_returns_true_for_channel_bundle() {
        assert!(request_is_channel_bundle("amzn1...."));
    }

    #[test]
    fn request_is_channel_bundle_returns_false_for_non_channel_bundle() {
        assert!(!request_is_channel_bundle("bundleid"));
    }

    #[test]
    fn expect_rpc_trigger_when_calling_record_weblab_trigger() {
        let rpc_manager = setup_mock_rpc_manager();

        record_weblab_trigger(&rpc_manager, TriggerFn::RustCheckout);
    }

    #[test]
    fn expect_true_when_checking_rust_checkout_weblab() {
        let mut rust_features = MockRustFeaturesBuilder::new().build();
        let rpc_manager = setup_mock_rpc_manager();

        rust_features
            .expect_is_rust_checkout_enabled()
            .return_once(|| true);

        assert!(check_rust_checkout_weblab_enabled(
            &Some(rust_features),
            &Some(rpc_manager)
        ));
    }

    #[test]
    fn expect_false_when_checking_rust_checkout_weblab() {
        let mut rust_features = MockRustFeaturesBuilder::new().build();
        let rpc_manager = setup_mock_rpc_manager();

        rust_features
            .expect_is_rust_checkout_enabled()
            .return_once(|| false);

        assert_eq!(
            false,
            check_rust_checkout_weblab_enabled(&Some(rust_features), &Some(rpc_manager))
        );
    }

    #[test]
    fn expect_false_when_rust_features_not_provided() {
        let rpc_manager = MockRPCManager::new();

        assert_eq!(
            false,
            check_rust_checkout_weblab_enabled(&None, &Some(rpc_manager))
        );
    }

    fn setup_mock_rpc_manager() -> MockRPCManager {
        let mut rpc_manager = MockRPCManager::new();

        let mut rpc_call: MockRPCCall<()> = MockRPCCall::new();
        let mut rpc_call_2: MockRPCCall<()> = MockRPCCall::new();

        rpc_call_2.expect_send().return_once(move || ());

        rpc_call
            .expect_error_callback()
            .once()
            .return_once(move |_| rpc_call_2);

        rpc_manager
            .expect_call()
            .withf(|f| f.eq("recordRustCheckoutRustTrigger"))
            .times(1)
            .return_once(move |_| rpc_call);

        rpc_manager
    }
}
