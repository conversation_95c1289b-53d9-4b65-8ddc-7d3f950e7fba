const BOOMERANG: &str = "Boomerang";
const PRIME_ADDON: &str = "primeaddon";
const PRIME: &str = "Prime";

#[derive(PartialEq, Debug)]
pub enum AcquisitionWorkflowType {
    Channels,
    Other,
    // Other is used to represent any flow that is NOT supported on RUST
    // any data that resolves to other will be taken to REACT as part of
    // the start_checkout API
}

pub fn get_checkout_workflow_type(benefit_id: &str) -> AcquisitionWorkflowType {
    // For now no Fuse check is needed as Fuse flows are all Prime
    // By sending all Prime flows to react we still handle fuse as we have done
    // previously on react. In the future when we move Prime to rust we will
    // need to decide which client handles Fuse and also detect fuse flows here

    if benefit_id == BOOMERANG || benefit_id == PRIME_ADDON || benefit_id == PRIME {
        return AcquisitionWorkflowType::Other;
    }

    AcquisitionWorkflowType::Channels
}

#[cfg(test)]
mod tests {
    use core::assert_eq;

    use super::*;

    #[test]
    fn test_get_checkout_workflow_type() {
        assert_eq!(
            get_checkout_workflow_type("Boomerang"),
            AcquisitionWorkflowType::Other
        );
        assert_eq!(
            get_checkout_workflow_type("primeaddon"),
            AcquisitionWorkflowType::Other
        );
        assert_eq!(
            get_checkout_workflow_type("Prime"),
            AcquisitionWorkflowType::Other
        );
        assert_eq!(
            get_checkout_workflow_type(""),
            AcquisitionWorkflowType::Channels
        );
    }
}
