use ignx_compositron::app::launch_composable;
use ignx_compositron::composable::SingleAxisLayoutComposable;
use ignx_compositron::composable::VisualComposable;
use ignx_compositron::compose;
use ignx_compositron::focusable::FocusableComposable;
use ignx_compositron::layout::MainAxisAlignment;
use ignx_compositron::reactive::*;
use ignx_compositron::rectangle::*;
use ignx_compositron::row::*;
use ignx_compositron::stack::*;
use media_background::media_background::*;
use media_background::provide_media_background_context;
use media_background::types::provide_trailer_playback;
use media_background::types::MediaStrategy;
use media_background::types::RotationDirection;
use media_background::types::StandardHeroBackgroundData;
use media_background::types::TentpoleBackgroundData;
use media_background::types::{MediaBackgroundType, StandardBackgroundData};
use playback::context::provide_playback;
use playback::provide_hybrid_playback;

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let (media_background_data, set_media_background_data): (
            ReadSignal<MediaBackgroundType>,
            WriteSignal<MediaBackgroundType>,
        ) = create_signal(ctx.scope(), MediaBackgroundType::None);

        let scope = ctx.scope();
        let autoplay_enabled = Signal::derive(ctx.scope(), move || false);

        provide_playback(&ctx);
        provide_media_background_context(ctx.scope());
        provide_trailer_playback(ctx.scope());
        provide_hybrid_playback(ctx.scope());

        compose! {
            Stack() {
                MediaBackground(incoming_data: media_background_data, rtl_enabled: false, autoplay_enabled),

                Row() {
                    Rectangle()
                        .height(10.0)
                        .width(10.0)
                        .focusable()
                        .on_focus(move || {
                            set_media_background_data.set(MediaBackgroundType::StandardHero(StandardHeroBackgroundData { id: "example".to_string(), image_url: Some("https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_True_Detective_Season4_Max_CS/1c748265-efee-40de-99a9-025e9febb2bf.jpeg".to_string()),  rotation_direction: RotationDirection::NONE, video_id: None, enter_immediately: false, is_expanded: create_rw_signal(scope, false), placement: "PVBrowse".to_string(), media_strategy: MediaStrategy::Promo, csm_data: None }));
                        })

                    Rectangle()
                        .height(10.0)
                        .width(10.0)
                        .focusable()
                        .on_focus(move || {
                            set_media_background_data.set(MediaBackgroundType::StandardHero(StandardHeroBackgroundData { id: "example".to_string(), image_url: Some("https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_True_Detective_Season4_Max_CS/1c748265-efee-40de-99a9-025e9febb2bf.jpeg".to_string()), rotation_direction: RotationDirection::LEFT, video_id: Some("some_video".to_string()), enter_immediately: false, is_expanded: create_rw_signal(scope, false), placement: "PVBrowse".to_string(), media_strategy: MediaStrategy::Promo, csm_data: None  }));
                        })

                    Rectangle()
                        .height(22.0)
                        .width(10.0)
                        .focusable()
                        .on_focus(move || {
                            set_media_background_data.set(MediaBackgroundType::StandardHero(StandardHeroBackgroundData { id: "example2".to_string(), image_url: Some("https://m.media-amazon.com/images/S/sonata-images-prod/US_TVOD_AquamanandTheLostKingdom_PVOD_SH/5e1f70d0-7164-4be4-b0e2-a2cb704cbf64.jpeg".to_string()), rotation_direction: RotationDirection::RIGHT, video_id: None, enter_immediately: false, is_expanded: create_rw_signal(scope, false), placement: "PVBrowse".to_string(), media_strategy: MediaStrategy::Promo, csm_data: None  }));
                        })

                    Rectangle()
                        .height(22.0)
                        .width(10.0)
                        .focusable()
                        .on_focus(move || {
                            set_media_background_data.set(MediaBackgroundType::Tentpole(TentpoleBackgroundData { id: "example".to_string(), image_url: "https://m.media-amazon.com/images/S/sonata-images-prod/US_SVOD_LTUM_S1_101022_SH_WSFN1/a9e075b3-4904-419e-949e-c1a088581902.jpeg".to_string(), video_id: None, enter_immediately: false, placement: "PVBrowse".to_string(), media_strategy: MediaStrategy::Promo, csm_data: None  }));
                        })

                    Rectangle()
                        .width(10.0)
                        .height(10.0)
                        .focusable()
                        .on_focus(move || {
                            set_media_background_data.set(MediaBackgroundType::Standard(StandardBackgroundData { id: "example2".to_string(), image_url:Some("https://m.media-amazon.com/images/S/pv-target-images/605f3723af37a5cb6615e3eb09797557b50f621f91856a487e7dc793a8f8a9b5.jpg".to_string()), video_id: None, enter_immediately: false, placement: "PVBrowse".to_string(), media_strategy: MediaStrategy::Promo, csm_data: None,
                            interaction_source_override: None  }));
                        })

                    Rectangle()
                        .width(10.0)
                        .height(10.0)
                        .focusable()
                        .on_focus(move || {
                            set_media_background_data.set(MediaBackgroundType::Standard(StandardBackgroundData { id: "example".to_string(), image_url: Some("https://m.media-amazon.com/images/S/pv-target-images/dd042466518894cdb3848a017422df924f4fd6d92dbe6dc253423ff690e1da15.jpg".to_string()), video_id: None, enter_immediately: false, placement: "PVBrowse".to_string(), media_strategy: MediaStrategy::Promo, csm_data: None,
                            interaction_source_override: None   }));
                        })
                }
                .main_axis_alignment(MainAxisAlignment::SpacedBy(10.0))
            }
            .width(1920.0)
            .height(1080.0)
        }
    });
}
