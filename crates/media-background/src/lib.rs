use ignx_compositron::prelude::*;
use media_background::MediaBackgroundContext;
use playback::context::use_playback;
use types::{PlayerStatus, Video};

pub mod media_background;

mod player_overlay;

pub mod backgrounds;

pub mod components;

pub mod controllers;

pub mod types;

pub fn provide_media_background_context(scope: Scope) -> MediaBackgroundContext {
    let playback = use_playback(scope);

    let playing_video_rw = create_rw_signal(scope, None);
    let ready_rw = create_rw_signal(scope, false);

    let player_status = Signal::derive(scope, move || {
        let status = playback.status.get();

        if status.is_playing() {
            PlayerStatus::PLAYING
        } else {
            PlayerStatus::STOPPED
        }
    });

    let media_background_ctx = MediaBackgroundContext {
        playing_video_rw,
        ready_rw,
        playing_video: playing_video_rw.read_only(),
        ready: ready_rw.read_only(),
        player_status,
    };

    provide_context(scope, media_background_ctx);
    media_background_ctx
}

pub fn use_media_background_context(scope: Scope) -> MediaBackgroundContext {
    expect_context::<MediaBackgroundContext>(scope)
}

pub fn create_media_background_context(
    player_status: RwSignal<PlayerStatus>,
    playing_video_rw: RwSignal<Option<Video>>,
    ready_rw: RwSignal<bool>,
) -> MediaBackgroundContext {
    MediaBackgroundContext {
        playing_video_rw,
        ready_rw,
        playing_video: playing_video_rw.read_only(),
        ready: ready_rw.read_only(),
        player_status: player_status.into(),
    }
}
