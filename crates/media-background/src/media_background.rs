pub use crate::types::common::MediaBackgroundContext;
use crate::types::use_hybrid_playback;
use crate::{
    backgrounds::{
        fullscreen::fullscreen_background::*, linear_background::*,
        micro_background::micro_background_composer::*,
        resizable_standard_background::resizable_standard_background_composer::*,
        resizable_super_carousel_background::resizable_super_carousel_background_composer::*,
        special_collections_background::*, standard_background::standard_background_composer::*,
        standard_hero::standard_hero_background::*, super_carousel::super_carousel_background::*,
        tentpole::tentpole_background::*,
    },
    components::overlay::*,
    controllers::transition_controller::*,
    player_overlay::*,
    types::{
        common::PAGE_DIMENSIONS, context::use_media_background_status_internal,
        use_trailer_playback, MediaBackgroundType, PlayerStatus, ReleaseState, Window,
    },
    use_media_background_context,
};
use ignx_compositron::{
    composable::*, compose, player_data::PlayerCapabilities, prelude::*, reactive::*, Composer,
};
use mockall_double::double;
use playback::context::{use_playback, PlaybackOptions, PlaybackSurface, PlaybackView};
#[double]
use profile_manager::use_profile_manager;
#[double]
use resiliency_store::use_resiliency_store;
use router::hooks::use_location_with_effect;
use std::{mem, time::Duration};

pub fn create_non_optional_signal<T: Clone + PartialEq>(
    ctx: &AppContext,
    incoming_data: Signal<Option<T>>,
) -> ReadSignal<T> {
    // Using unwrap here is safe but not ideal. We should validate this using `if let` once https://issues.amazon.com/issues/LRCP-4130 is done.
    #[allow(
        clippy::unwrap_used,
        reason = "https://issues.amazon.com/issues/LRCP-4130"
    )]
    let (new_signal, set_new_signal) =
        create_signal(ctx.scope(), incoming_data.get_untracked().unwrap());

    create_effect(ctx.scope(), move |_| {
        if let Some(d) = incoming_data.get() {
            if new_signal.get_untracked() != d {
                set_new_signal.set(d);
            }
        }
    });

    new_signal
}

fn has_instant_transition(data: &MediaBackgroundType) -> bool {
    matches!(data, MediaBackgroundType::None)
}

pub(crate) fn debounce_comparator(prev: &MediaBackgroundType, next: &MediaBackgroundType) -> u64 {
    if has_instant_transition(prev) || has_instant_transition(next) {
        0
    } else {
        300
    }
}

#[Composer]
pub fn MediaBackground(
    ctx: &AppContext,
    incoming_data: ReadSignal<MediaBackgroundType>,
    rtl_enabled: bool,
    #[into] autoplay_enabled: MaybeSignal<bool>,
) -> StackComposable {
    log::info!("[media-background]: v2 mounted");

    let playback = use_playback(ctx.scope());
    let trailer_playback = use_trailer_playback(ctx.scope());
    let hybrid_playback = use_hybrid_playback(ctx.scope());
    let location_with_effect = use_location_with_effect(ctx.scope());
    let resiliency_store = use_resiliency_store(ctx.scope());
    let profile_manager = use_profile_manager(ctx.scope());

    let initial_data = incoming_data.get_untracked();

    let media_background_context = use_media_background_context(ctx.scope());
    let rendered_data = create_rw_signal(ctx.scope(), initial_data);
    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 1.0f32);
    let (ready, set_ready) = media_background_context.ready_rw.split();
    let (window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);

    let media_background_status_ctx = use_media_background_status_internal(ctx.scope());
    let player_capabilities_ctx = use_context::<RwSignal<Option<PlayerCapabilities>>>(ctx.scope());

    // Update media background context with player capabilities
    create_effect(ctx.scope(), move |_| {
        let capabilities = playback.capabilities.get();
        let resize_support = capabilities.as_ref().and_then(|v| v.resize_support.clone());

        log::info!("Player capabilities set to: {:?}", capabilities);

        if let Some(media_background_status_ctx) = media_background_status_ctx.as_ref() {
            media_background_status_ctx
                .player_resize_support
                .set(resize_support)
        }

        if let Some(player_capabilities_ctx) = player_capabilities_ctx.as_ref() {
            player_capabilities_ctx.set(capabilities)
        }
    });

    let player_status = Signal::derive(ctx.scope(), move || {
        let status = playback.status.get();

        if status.is_playing() {
            PlayerStatus::PLAYING
        } else {
            PlayerStatus::STOPPED
        }
    });

    transition_controller(
        ctx,
        incoming_data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready.into(),
        Options {
            debounce: Box::new(debounce_comparator),
        },
    );

    let playing_video = media_background_context.playing_video_rw;
    // Keep playback in sync with playing_video
    create_effect(ctx.scope(), move |_| {
        let capabilities = playback.capabilities.get_untracked();
        let resize_support = capabilities.as_ref().and_then(|v| v.resize_support.clone());
        playing_video.with(|v| {
            if !autoplay_enabled.get_untracked() {
                return;
            }
            let should_autoplay = v
                .as_ref()
                .map(|v| v.should_autoplay(&resiliency_store, &profile_manager, resize_support))
                .is_some_and(|v| v);

            if !should_autoplay {
                playback.stop();
                return;
            }

            let viewport = v.as_ref().map(|v| v.window.to_viewport());
            let src = v.as_ref().and_then(|v| v.to_playback_src());

            playback.view.set(PlaybackView::NoControls {
                viewport: viewport.unwrap_or_default(),
                surface: PlaybackSurface::PV,
            });

            if let Some(src) = src {
                let mut options = PlaybackOptions::default()
                    .dwell(Duration::from_millis(1_500))
                    .engaged(false);

                let origin = rendered_data
                    .with_untracked(|rendered_data| rendered_data.get_playback_origin());

                if let Some(origin) = origin {
                    options = options.origin(origin)
                }

                playback.start_with_options(src, options);
            } else {
                playback.stop();
            }
        })
    });

    // Keep TrailerPlaybackContext in sync
    create_effect(ctx.scope(), move |_| {
        let status = player_status.get();
        if !autoplay_enabled.get_untracked() {
            return;
        }
        playing_video.with(|video| {
            trailer_playback
                .on_trailer_playback_start
                .set(video.is_some() && status == PlayerStatus::PLAYING);
        });
    });

    // Whenever the media background changes, reset `ready` to false
    create_effect(ctx.scope(), move |prev| {
        rendered_data.with(|data| {
            let discrim = mem::discriminant(data);

            if prev == Some(discrim) {
                return discrim;
            }

            if matches!(data, MediaBackgroundType::Playback) {
                return discrim;
            }

            set_ready.set(false);

            discrim
        })
    });

    on_cleanup(ctx.scope(), move || {
        trailer_playback.reset();
    });

    // If playback is suppressed, clear the playing video.
    create_effect(ctx.scope(), move |_| {
        let suppress_trailer_playback = trailer_playback.suppress_trailer_playback.get();

        if !suppress_trailer_playback {
            return;
        }

        playing_video.set(None);
    });

    // Handle active layer switch
    create_effect(ctx.scope(), move |_| {
        let should_release = hybrid_playback.should_release.get();
        let seamless_egress = hybrid_playback.seamless_egress.get_value();

        let Some(release_state) = should_release else {
            return;
        };

        if matches!(release_state, ReleaseState::NotSeamless) && !seamless_egress {
            rendered_data.set_untracked(MediaBackgroundType::None);
            playing_video.set_untracked(None);
        }
    });

    // Animations between backgrounds
    let (translate_x, opacity) = {
        let translate_x = create_rw_signal(ctx.scope(), 0.0);
        let opacity = create_rw_signal(ctx.scope(), 1.0);

        create_effect(ctx.scope(), {
            let ctx = ctx.clone();
            move |_| {
                location_with_effect.with(|e| {
                    let Some(exit_effect) = &e.exit_effect else {
                        return;
                    };

                    if !exit_effect.fade_media_background() {
                        return;
                    }

                    ctx.with_animation_completion(
                        exit_effect.to_animation(),
                        move || {
                            translate_x.set(exit_effect.to_translate_x());
                            opacity.set(0.0);
                        },
                        move || {
                            translate_x.set(0.0);
                            opacity.set(1.0);
                        },
                    );
                })
            }
        });

        (translate_x.read_only(), opacity.read_only())
    };

    let window = Signal::derive(ctx.scope(), move || {
        playing_video.with(|v| {
            v.as_ref()
                .map_or_else(Window::fullscreen, |v| v.window.clone())
        })
    });

    // Opacity of rectangle rendered over player
    let player_cover_opacity = Signal::derive(ctx.scope(), move || 1. - window_opacity.get());

    // None background, we don't actually render a background for `None` type,
    // so we place logic in an effect.
    create_effect(ctx.scope(), move |_| {
        if rendered_data.with(|v| !matches!(v, MediaBackgroundType::None)) {
            return;
        }

        playing_video.set(None);
        set_window_opacity.set(0.);
        hybrid_playback.seamless_egress.set_value(false);
    });

    create_effect(ctx.scope(), move |_| {
        if rendered_data.with(|v| !matches!(v, MediaBackgroundType::Playback)) {
            return;
        }

        set_ready.set(true);
        set_window_opacity.set(1.);
    });

    let standard_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::Standard(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let tentpole_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::Tentpole(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let standard_hero_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::StandardHero(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let fullscreen_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::FullscreenBackground(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let linear_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::Linear(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let super_carousel_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::SuperCarousel(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let resizable_super_carousel_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::ResizableSuperCarousel(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let resizable_standard_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::ResizableStandard(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let micro_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::Micro(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    let special_collections_background = Signal::derive(ctx.scope(), move || {
        if let MediaBackgroundType::SpecialCollections(data) = rendered_data.get() {
            Some(data)
        } else {
            None
        }
    });

    compose! {
        Stack() {
            Stack() {
                PlayerOverlay(window, player_cover_opacity)

                if standard_background.get().is_some() {
                    StandardBackground(
                        data: create_non_optional_signal(ctx, standard_background),
                        rtl_enabled,
                        playing_video,
                        player_status,
                        set_initial_ready: set_ready,
                        set_window_opacity
                    )
                    .test_id("mb_standard_background")
                }

                if resizable_standard_background.get().is_some() {
                    ResizableStandardBackground(data: create_non_optional_signal(ctx, resizable_standard_background), rtl_enabled, playing_video, player_status, set_initial_ready: set_ready, set_window_opacity).test_id("mb_resizable_standard_background")

                }

                if tentpole_background.get().is_some() {
                    TentpoleBackground(
                        data: create_non_optional_signal(ctx, tentpole_background),
                        rtl_enabled,
                        playing_video,
                        player_status,
                        set_initial_ready: set_ready,
                        set_window_opacity
                    )
                    .test_id("mb_tentpole_background")
                }

                if standard_hero_background.get().is_some() {
                    StandardHeroBackground(
                        data: create_non_optional_signal(ctx, standard_hero_background),
                        rtl_enabled,
                        playing_video,
                        player_status,
                        set_initial_ready: set_ready,
                        set_window_opacity
                    )
                    .test_id("mb_standard_hero_background")
                }

                if fullscreen_background.get().is_some() {
                    FullScreenBackground(
                        data: create_non_optional_signal(ctx, fullscreen_background),
                        rtl_enabled,
                        playing_video,
                        set_initial_ready: set_ready
                    )
                    .test_id("mb_fullscreen_background")
                }

                if linear_background.get().is_some() {
                    LinearBackground(
                        data: create_non_optional_signal(ctx, linear_background),
                        rtl_enabled,
                        playing_video,
                        player_status,
                        set_initial_ready: set_ready,
                        set_window_opacity
                    )
                    .test_id("mb_linear_background")
                }

                if super_carousel_background.get().is_some() {
                    SuperCarouselBackground(
                        data: create_non_optional_signal(ctx, super_carousel_background),
                        rtl_enabled,
                        playing_video,
                        player_status,
                        set_initial_ready: set_ready,
                        set_window_opacity
                    )
                    .test_id("mb_super_carousel_background")
                }

                if resizable_super_carousel_background.get().is_some() {
                    ResizableSuperCarouselBackground(data: create_non_optional_signal(ctx, resizable_super_carousel_background), rtl_enabled, playing_video, player_status, set_initial_ready: set_ready, set_window_opacity).test_id("mb_resizable_super_carousel_background")
                }

                if micro_background.get().is_some() {
                    MicroBackground(
                        data: create_non_optional_signal(ctx, micro_background),
                        rtl_enabled,
                        playing_video,
                        player_status,
                        set_initial_ready: set_ready,
                        set_window_opacity
                    )
                    .test_id("mb_micro_background")
                }

                if special_collections_background.get().is_some() {
                    SpecialCollectionsBackground(
                        data: create_non_optional_signal(ctx, special_collections_background),
                        playing_video,
                        player_status,
                        set_initial_ready: set_ready,
                        set_window_opacity
                    )
                    .test_id("mb_special_collections_background")
                }
            }
            .opacity(opacity).translate_x(translate_x)
            .test_id("media_background")

            Overlay(
                size: PAGE_DIMENSIONS,
                opacity: overlay_opacity
            )
        }
    }
}

#[cfg(test)]
mod tests {
    use crate::provide_media_background_context;
    use crate::types::{
        AutoplayInteractionSource, FullscreenBackgroundData, LinearBackgroundData,
        LinearVignetteStyle, MediaStrategy, MicroBackgroundData, ResizableStandardBackgroundData,
        ResizableSuperCarouselBackgroundData, RotationDirection, StandardBackgroundData,
        StandardHeroBackgroundData, SuperCarouselBackgroundData, TentpoleBackgroundData,
        TrailerPlaybackContext,
    };

    use super::*;
    use common_transform_types::profile::{Profile, ProfileAvatar};
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};
    use ignx_compositron::time::MockClock;
    use location::{EnterEffect, ExitEffect, Location, LocationWithEffect};
    use playback::{context::provide_playback, provide_hybrid_playback, src::PlaybackOrigin};
    use profile_manager::MockProfileManager;
    use resiliency_store::MockResiliencyStore;
    use router::{rust_location, MockRouting, RoutingContext};
    use rstest::rstest;
    use serial_test::serial;
    use std::rc::Rc;
    use std::time::Duration;

    struct TestSetup {
        is_adult_profile: bool,
        disable_live_event_autoplay: bool,
    }

    impl TestSetup {
        fn new() -> Self {
            TestSetup {
                is_adult_profile: true,
                disable_live_event_autoplay: false,
            }
        }

        fn is_adult_profile(mut self, is_adult: bool) -> Self {
            self.is_adult_profile = is_adult;
            self
        }

        fn set_live_event_autoplay_disabled_from_resiliency(mut self) -> Self {
            self.disable_live_event_autoplay = true;
            self
        }

        fn setup(self, ctx: &AppContext) {
            let scope = ctx.scope();
            mock_resiliency_store(scope, self.disable_live_event_autoplay);
            mock_active_profile(scope, self.is_adult_profile);
            provide_playback(&ctx);
            provide_hybrid_playback(scope);
            provide_context(
                scope,
                TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(scope, false),
                    on_trailer_playback_start: create_rw_signal(scope, false),
                    should_play_hero: create_rw_signal(scope, false),
                    suppress_trailer_playback: create_rw_signal(scope, false),
                },
            );

            provide_media_background_context(scope);
        }
    }

    #[rstest]
    #[case("mb_standard_background")]
    #[case("mb_resizable_standard_background")]
    #[case("mb_tentpole_background")]
    #[case("mb_standard_hero_background")]
    #[case("mb_fullscreen_background")]
    #[case("mb_linear_background")]
    #[case("mb_super_carousel_background")]
    #[case("mb_resizable_super_carousel_background")]
    #[case("mb_micro_background")]
    #[serial]
    fn should_render_given_background_type(#[case] test_id: &'static str) {
        launch_test(
            |ctx| {
                TestSetup::new().setup(&ctx);
                mock_router(ctx.scope());
                let media_background = media_background_type(ctx.scope(), test_id);
                let (media_background, _) = create_signal(ctx.scope(), media_background);
                compose! {
                    MediaBackground(incoming_data: media_background, rtl_enabled: false, autoplay_enabled: true)
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();
                let background = node_tree.find_by_test_id(test_id);
                assert_node_exists!(background);
            },
        )
    }

    #[test]
    fn should_pass_origin_and_engaged_flag_to_playback() {
        launch_test(
            |ctx| {
                mock_router(ctx.scope());
                TestSetup::new().setup(&ctx);
                let media_background_sig = create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                provide_context(ctx.scope(), media_background_sig);
                compose! {
                    MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                }
            },
            |scope, mut test_game_loop| {
                let media_background_sig = expect_context::<RwSignal<MediaBackgroundType>>(scope);
                let _ = test_game_loop.tick_until_done();
                let playback = use_playback(scope);
                assert_eq!(playback.src.get_untracked(), None);

                // change to standard background with video
                media_background_sig.set(MediaBackgroundType::Linear(linear_data_with_video(
                    "linear_video_id".to_string(),
                )));
                let _ = test_game_loop.tick_until_done();
                wait_for_autoplay_to_start();
                let _ = test_game_loop.tick_until_done();

                assert_eq!(
                    playback.content.get_untracked().map(|v| v.origin).flatten(),
                    Some(PlaybackOrigin::AlwaysOn)
                );

                assert_eq!(
                    playback.content.get_untracked().map(|v| v.engaged),
                    Some(false)
                );
            },
        )
    }

    #[test]
    #[serial]
    fn should_render_no_background() {
        launch_test(
            |ctx| {
                TestSetup::new().setup(&ctx);
                mock_router(ctx.scope());
                let (media_background, _) = create_signal(ctx.scope(), MediaBackgroundType::None);
                compose! {
                    MediaBackground(incoming_data: media_background, rtl_enabled: false, autoplay_enabled: true)
                }
            },
            move |_scope, mut test_game_loop| {
                let node_tree = test_game_loop.tick_until_done();

                for id in [
                    "mb_standard_background",
                    "mb_tentpole_background",
                    "mb_standard_hero_background",
                    "mb_fullscreen_background",
                    "mb_linear_background",
                    "mb_super_carousel_background",
                    "mb_micro_background",
                ] {
                    let background = node_tree.find_by_test_id(id);
                    assert_node_does_not_exist!(background);
                }
            },
        )
    }

    #[test]
    #[serial]
    fn should_reset_trailer_playback_start_on_media_background_type_change() {
        launch_test(
            |ctx| {
                mock_router_with_effect(ctx.scope(), Some(exit_effect()), Some(enter_effect()));
                TestSetup::new().setup(&ctx);
                let media_background = media_background_type(ctx.scope(), "mb_standard_background");
                let media_background_type_sig = create_rw_signal(ctx.scope(), media_background);
                provide_context(ctx.scope(), media_background_type_sig);

                compose! {
                    MediaBackground(incoming_data: media_background_type_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                }
            },
            move |scope, _test_game_loop| {
                let media_background_sig = use_context::<RwSignal<MediaBackgroundType>>(scope)
                    .expect("Expect signal to exist");
                let trailer_playback_context = use_trailer_playback(scope);

                media_background_sig
                    .set(media_background_type(scope, "mb_standard_hero_background"));
                trailer_playback_context.on_trailer_playback_start.set(true);
                assert!(trailer_playback_context
                    .on_trailer_playback_start
                    .get_untracked());

                media_background_sig.set(media_background_type(scope, "mb_standard_background"));
                assert!(!trailer_playback_context
                    .on_trailer_playback_start
                    .get_untracked());
            },
        )
    }

    #[test]
    #[serial]
    fn should_reset_playing_video_on_none_seamless_release() {
        launch_test(
            |ctx| {
                mock_router(ctx.scope());
                TestSetup::new().setup(&ctx);
                let media_background_sig = create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                provide_context(ctx.scope(), media_background_sig);
                compose! {
                    MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                }
            },
            move |scope, mut test_game_loop| {
                let media_background_sig = expect_context::<RwSignal<MediaBackgroundType>>(scope);
                let _ = test_game_loop.tick_until_done();

                media_background_sig.set(MediaBackgroundType::Standard(
                    standard_background_data_with_promo_video("standard_video_id".to_string()),
                ));
                let _ = test_game_loop.tick_until_done();
                wait_for_autoplay_to_start();
                let _ = test_game_loop.tick_until_done();

                let media_background_ctx = use_media_background_context(scope);
                assert!(media_background_ctx
                    .playing_video_rw
                    .get_untracked()
                    .is_some());

                let hybrid_playback = use_hybrid_playback(scope);
                hybrid_playback
                    .should_release
                    .set(Some(ReleaseState::NotSeamless));
                hybrid_playback.seamless_egress.set_value(false);
                let _ = test_game_loop.tick_until_done();
                assert!(media_background_ctx
                    .playing_video_rw
                    .get_untracked()
                    .is_none());
            },
        )
    }

    mod autoplay_enabled {
        use ignx_compositron::player_data::ResizeSupport;
        use playback::{
            context::PlaybackStatus,
            src::{LinearSrc, LinearStartMode, PlaybackSrc, PromoPlacement, PromoSrc},
        };

        use super::*;

        #[test]
        #[serial]
        fn should_set_playback_src() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                |scope, mut test_game_loop| {
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let _ = test_game_loop.tick_until_done();
                    let playback = use_playback(scope);
                    assert_eq!(playback.src.get_untracked(), None);

                    // change to standard background with video
                    media_background_sig.set(MediaBackgroundType::Standard(
                        standard_background_data_with_promo_video("standard_video_id".to_string()),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(
                        playback.src.get_untracked(),
                        Some(PlaybackSrc::Promo(PromoSrc {
                            title_id: "standard_video_id".to_string(),
                            placement: PromoPlacement::PVBrowse,
                            client_side_metrics: Some("CSMData".to_string()),
                            client_id: Some("3ab558cf-2c28-4dd1-b3c3-78dfec62b0e0".to_string()),
                        }))
                    )
                },
            )
        }

        #[test]
        #[serial]
        fn should_play_new_content_after_media_background_change() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                |scope, mut test_game_loop| {
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let _ = test_game_loop.tick_until_done();
                    let playback = use_playback(scope);

                    // change to standard background with video
                    media_background_sig.set(MediaBackgroundType::Standard(
                        standard_background_data_with_promo_video("standard_video_id".to_string()),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();
                    assert_eq!(
                        playback.src.get_untracked(),
                        Some(PlaybackSrc::Promo(PromoSrc {
                            title_id: "standard_video_id".to_string(),
                            placement: PromoPlacement::PVBrowse,
                            client_side_metrics: Some("CSMData".to_string()),
                            client_id: Some("3ab558cf-2c28-4dd1-b3c3-78dfec62b0e0".to_string()),
                        }))
                    );

                    // switch to linear background should unload player and then load the new content
                    media_background_sig.set(MediaBackgroundType::Linear(
                        standard_background_data_with_linear_video("linear_video_id".to_string()),
                    ));
                    wait_for_fadeout();
                    let _ = test_game_loop.tick_until_done();
                    assert_eq!(playback.src.get_untracked(), None);

                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();
                    assert_eq!(
                        playback.src.get_untracked(),
                        Some(PlaybackSrc::Linear(LinearSrc {
                            title_id: "linear_video_id".to_string(),
                            start_mode: LinearStartMode::AtLive,
                            client_id: Some("cfd2813d-bdc9-485b-82ac-aa75d3e7000c".to_string()),
                        }))
                    );
                },
            )
        }

        #[test]
        #[serial]
        fn should_unload_when_media_background_is_none() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                move |scope, mut test_game_loop| {
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let playback = use_playback(scope);
                    let _ = test_game_loop.tick_until_done();

                    // change to standard background
                    media_background_sig.set(MediaBackgroundType::Standard(
                        standard_background_data_with_promo_video("standard_video_id".to_string()),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();

                    assert!(playback.src.get_untracked().is_some());

                    // change media background to None
                    media_background_sig.set(MediaBackgroundType::None);
                    MockClock::advance(Duration::from_millis(350));
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(playback.src.get_untracked(), None);
                },
            )
        }

        #[test]
        #[serial]
        fn should_reset_seamless_egress_flag_when_media_background_is_none() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                move |scope, mut _test_game_loop| {
                    let hybrid_playback = use_hybrid_playback(scope);
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);

                    hybrid_playback.seamless_egress.set_value(true);
                    // change media background to None
                    media_background_sig.set(MediaBackgroundType::None);
                    assert_eq!(hybrid_playback.seamless_egress.get_value(), false);
                },
            )
        }

        #[test]
        #[serial]
        fn should_stop_playing_when_suppress_trailer_playback_set() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                |scope, mut test_game_loop| {
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let playback = use_playback(scope);
                    let _ = test_game_loop.tick_until_done();

                    // change to standard background
                    media_background_sig.set(MediaBackgroundType::Standard(
                        standard_background_data_with_promo_video("standard_video_id".to_string()),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();
                    assert!(playback.src.get_untracked().is_some());

                    let trailer_playback_ctx = expect_context::<TrailerPlaybackContext>(scope);

                    trailer_playback_ctx.suppress_trailer_playback.set(true);
                    let _ = test_game_loop.tick_until_done();
                    assert_eq!(playback.src.get_untracked(), None);
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_play_non_fullscreen_video_with_no_resize_support() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());

                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);

                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                |scope, mut test_game_loop| {
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let playback = use_playback(scope);
                    let _ = test_game_loop.tick_until_done();

                    // change to SuperCarousel background
                    media_background_sig.set(MediaBackgroundType::SuperCarousel(
                        super_carousel_background_data_with_video(
                            "non_fullscreen_video_id".to_string(),
                        ),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(playback.src.get_untracked(), None);
                },
            )
        }

        #[test]
        #[serial]
        fn should_play_non_fullscreen_video_with_resize_support() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    let playback = use_playback(ctx.scope());
                    playback.set_capabilities.set(Some(PlayerCapabilities {
                        resize_support: Some(ResizeSupport::Static),
                        hdr_support: None,
                        uhd_support: false,
                    }));

                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                |scope, mut test_game_loop| {
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let playback = use_playback(scope);
                    let _ = test_game_loop.tick_until_done();

                    // change to SuperCarousel background
                    media_background_sig.set(MediaBackgroundType::SuperCarousel(
                        super_carousel_background_data_with_video(
                            "non_fullscreen_video_id".to_string(),
                        ),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(
                        playback.src.get_untracked(),
                        Some(PlaybackSrc::Promo(PromoSrc {
                            title_id: "non_fullscreen_video_id".to_string(),
                            placement: PromoPlacement::PVBrowse,
                            client_side_metrics: None,
                            client_id: Some("3ab558cf-2c28-4dd1-b3c3-78dfec62b0e0".to_string()),
                        }))
                    );
                },
            )
        }

        #[test]
        #[serial]
        fn should_update_trailer_playback_ctx_when_player_status_updates() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                    }
                },
                |scope, mut test_game_loop| {
                    let _ = test_game_loop.tick_until_done();
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let playback = use_playback(scope);
                    let trailer_playback = use_trailer_playback(scope);
                    assert!(!trailer_playback.on_trailer_playback_start.get_untracked());

                    // change to standard background
                    media_background_sig.set(MediaBackgroundType::Standard(
                        standard_background_data_with_promo_video("standard_video_id".to_string()),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();
                    assert!(playback.src.get_untracked().is_some());
                    assert!(!trailer_playback.on_trailer_playback_start.get_untracked());

                    // Update player status
                    playback.set_status.set(PlaybackStatus::Playing);
                    assert!(trailer_playback.on_trailer_playback_start.get_untracked());
                    playback.set_status.set(PlaybackStatus::Loading);
                    assert!(!trailer_playback.on_trailer_playback_start.get_untracked());
                },
            )
        }

        mod live_event_autoplay {
            use super::*;

            #[test]
            #[serial]
            fn should_not_set_playback_src_when_live_autoplay_disabled_by_resiliency_switch() {
                launch_test(
                    |ctx| {
                        mock_router(ctx.scope());
                        TestSetup::new()
                            .set_live_event_autoplay_disabled_from_resiliency()
                            .setup(&ctx);
                        let media_background_sig =
                            create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                        provide_context(ctx.scope(), media_background_sig);
                        compose! {
                            MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                        }
                    },
                    |scope, mut test_game_loop| {
                        let media_background_sig =
                            expect_context::<RwSignal<MediaBackgroundType>>(scope);
                        let _ = test_game_loop.tick_until_done();
                        let playback = use_playback(scope);
                        assert_eq!(playback.src.get_untracked(), None);

                        // change to standard background with video
                        media_background_sig.set(MediaBackgroundType::Standard(
                            standard_background_data_with_live_event_video(
                                "live_event_video_id".to_string(),
                            ),
                        ));
                        let _ = test_game_loop.tick_until_done();
                        wait_for_autoplay_to_start();
                        let _ = test_game_loop.tick_until_done();

                        assert_eq!(playback.src.get_untracked(), None)
                    },
                )
            }

            #[test]
            #[serial]
            fn should_not_set_playback_src_on_kids_profile() {
                launch_test(
                    |ctx| {
                        mock_router(ctx.scope());
                        TestSetup::new().is_adult_profile(false).setup(&ctx);
                        let media_background_sig =
                            create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                        provide_context(ctx.scope(), media_background_sig);
                        compose! {
                            MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: true)
                        }
                    },
                    |scope, mut test_game_loop| {
                        let media_background_sig =
                            expect_context::<RwSignal<MediaBackgroundType>>(scope);
                        let _ = test_game_loop.tick_until_done();
                        let playback = use_playback(scope);
                        assert_eq!(playback.src.get_untracked(), None);

                        // change to standard background with video
                        media_background_sig.set(MediaBackgroundType::Standard(
                            standard_background_data_with_live_event_video(
                                "live_event_video_id".to_string(),
                            ),
                        ));
                        let _ = test_game_loop.tick_until_done();
                        wait_for_autoplay_to_start();
                        let _ = test_game_loop.tick_until_done();

                        assert_eq!(playback.src.get_untracked(), None)
                    },
                )
            }
        }
    }

    mod autoplay_disabled {
        use playback::context::PlaybackStatus;

        use super::*;

        #[test]
        #[serial]
        fn should_not_set_playback_src() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: false)
                    }
                },
                |scope, mut test_game_loop| {
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let _ = test_game_loop.tick_until_done();
                    let playback = use_playback(scope);
                    assert_eq!(playback.src.get_untracked(), None);

                    // change to standard background with video
                    media_background_sig.set(MediaBackgroundType::Standard(
                        standard_background_data_with_promo_video("standard_video_id".to_string()),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();

                    assert_eq!(playback.src.get_untracked(), None)
                },
            )
        }

        #[test]
        #[serial]
        fn should_not_sync_player_status() {
            launch_test(
                |ctx| {
                    mock_router(ctx.scope());
                    TestSetup::new().setup(&ctx);
                    let media_background_sig =
                        create_rw_signal(ctx.scope(), MediaBackgroundType::None);
                    provide_context(ctx.scope(), media_background_sig);
                    compose! {
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled: false)
                    }
                },
                |scope, mut test_game_loop| {
                    let _ = test_game_loop.tick_until_done();
                    let media_background_sig =
                        expect_context::<RwSignal<MediaBackgroundType>>(scope);
                    let playback = use_playback(scope);
                    let trailer_playback = use_trailer_playback(scope);
                    assert!(!trailer_playback.on_trailer_playback_start.get_untracked());

                    // change to standard background
                    media_background_sig.set(MediaBackgroundType::Standard(
                        standard_background_data_with_promo_video("standard_video_id".to_string()),
                    ));
                    let _ = test_game_loop.tick_until_done();
                    wait_for_autoplay_to_start();
                    let _ = test_game_loop.tick_until_done();
                    assert!(!trailer_playback.on_trailer_playback_start.get_untracked());

                    // Update player status
                    playback.set_status.set(PlaybackStatus::Playing);
                    assert!(!trailer_playback.on_trailer_playback_start.get_untracked());
                    playback.set_status.set(PlaybackStatus::Loading);
                    assert!(!trailer_playback.on_trailer_playback_start.get_untracked());
                },
            )
        }
    }

    fn mock_resiliency_store(scope: Scope, should_disable_live_event_autoplay: bool) {
        let mut mock_resiliency_store = MockResiliencyStore::default();

        mock_resiliency_store.expect_clone().returning(move || {
            let mut clone = MockResiliencyStore::default();
            clone
                .expect_get_disable_live_event_autoplay()
                .return_const(should_disable_live_event_autoplay);
            clone
        });

        provide_context::<MockResiliencyStore>(scope, mock_resiliency_store);
    }

    fn mock_active_profile(scope: Scope, is_adult_profile: bool) {
        let mut mock_profile_manager = MockProfileManager::default();

        let profile = create_rw_signal(
            scope,
            Some(Profile {
                id: "id".to_string(),
                avatar: ProfileAvatar {
                    avatarId: "".to_string(),
                    avatarUrl: "".to_string(),
                    avatarDescription: None,
                },
                name: "".to_string(),
                isActive: true,
                isAdult: is_adult_profile,
                profileIsImplicit: true,
                translationDetails: None,
                permissions: None,
            }),
        );

        mock_profile_manager.expect_clone().returning(move || {
            let mut mock_profile_manager_clone = MockProfileManager::default();
            mock_profile_manager_clone
                .expect_get_active_profile()
                .returning(move || profile.into());
            mock_profile_manager_clone
        });

        provide_context(scope, mock_profile_manager);
    }

    fn mock_router(scope: Scope) {
        mock_router_with_effect(scope, None, None);
    }

    fn mock_router_with_effect(
        scope: Scope,
        exit_effect: Option<ExitEffect>,
        enter_effect: Option<EnterEffect>,
    ) {
        let location_with_effect = Signal::derive(scope, move || LocationWithEffect {
            from_location: Some(Location::default()),
            to_location: rust_location!(RUST_COLLECTIONS),
            exit_effect: exit_effect.clone(),
            enter_effect: enter_effect.clone(),
        });
        let mut mock_routing_context = MockRouting::new();

        mock_routing_context
            .expect_location_with_effect()
            .return_const_st(location_with_effect);

        let mock_routing_context = Rc::new(mock_routing_context);
        provide_context::<RoutingContext>(scope, mock_routing_context);
    }

    fn media_background_type(scope: Scope, mb_type: &str) -> MediaBackgroundType {
        match mb_type {
            "mb_standard_background" => MediaBackgroundType::Standard(standard_background_data()),
            "mb_resizable_standard_background" => {
                MediaBackgroundType::ResizableStandard(resizable_standard_background_data())
            }
            "mb_tentpole_background" => MediaBackgroundType::Tentpole(tentpole_background_data()),
            "mb_standard_hero_background" => {
                MediaBackgroundType::StandardHero(standard_hero_background_data(scope))
            }
            "mb_fullscreen_background" => {
                MediaBackgroundType::FullscreenBackground(fullscreen_background_data())
            }
            "mb_linear_background" => MediaBackgroundType::Linear(linear_data()),
            "mb_super_carousel_background" => {
                MediaBackgroundType::SuperCarousel(super_carousel_background_data())
            }
            "mb_resizable_super_carousel_background" => {
                MediaBackgroundType::ResizableSuperCarousel(
                    resizable_super_carousel_background_data(),
                )
            }
            "mb_micro_background" => MediaBackgroundType::Micro(micro_background_data()),
            _ => panic!("Unknown media background type"),
        }
    }

    fn standard_background_data() -> StandardBackgroundData {
        StandardBackgroundData {
            id: "standard".to_string(),
            image_url: Some("https://test.amazon.com".to_string()),
            video_id: None,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            interaction_source_override: None,
        }
    }

    fn resizable_standard_background_data() -> ResizableStandardBackgroundData {
        ResizableStandardBackgroundData {
            id: "standard".to_string(),
            image_url: Some("https://test.amazon.com".to_string()),
            video_id: None,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            interaction_source_override: None,
            resized: false,
        }
    }

    fn standard_background_data_with_promo_video(video_id: String) -> StandardBackgroundData {
        StandardBackgroundData {
            id: "standard".to_string(),
            image_url: Some("https://test.amazon.com".to_string()),
            video_id: Some(video_id),
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None,
        }
    }

    fn standard_background_data_with_live_event_video(video_id: String) -> StandardBackgroundData {
        StandardBackgroundData {
            id: "standard".to_string(),
            image_url: Some("https://test.amazon.com".to_string()),
            video_id: Some(video_id),
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: None,
            interaction_source_override: None,
        }
    }

    fn standard_background_data_with_linear_video(video_id: String) -> LinearBackgroundData {
        LinearBackgroundData {
            id: "standard".to_string(),
            image_url: "https://test.amazon.com".to_string(),
            video_id: Some(video_id),
            vignette: LinearVignetteStyle::LiveTv,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Linear,
            csm_data: None,
            interaction_source: Some(AutoplayInteractionSource::AlwaysOn),
        }
    }

    fn tentpole_background_data() -> TentpoleBackgroundData {
        TentpoleBackgroundData {
            id: "standard".to_string(),
            image_url: "https://test.amazon.com".to_string(),
            video_id: None,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
        }
    }

    fn standard_hero_background_data(scope: Scope) -> StandardHeroBackgroundData {
        StandardHeroBackgroundData {
            id: "standard".to_string(),
            image_url: Some("https://test.amazon.com".to_string()),
            video_id: None,
            enter_immediately: true,
            rotation_direction: RotationDirection::RIGHT,
            is_expanded: create_rw_signal(scope, false),
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
        }
    }

    fn super_carousel_background_data() -> SuperCarouselBackgroundData {
        SuperCarouselBackgroundData {
            id: "standard".to_string(),
            video_id: None,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            has_title: true,
        }
    }

    fn resizable_super_carousel_background_data() -> ResizableSuperCarouselBackgroundData {
        ResizableSuperCarouselBackgroundData {
            id: "standard".to_string(),
            video_id: None,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            has_title: true,
            image_url: None,
            resized: false,
        }
    }

    fn super_carousel_background_data_with_video(video_id: String) -> SuperCarouselBackgroundData {
        SuperCarouselBackgroundData {
            id: "standard".to_string(),
            video_id: Some(video_id),
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            has_title: true,
        }
    }

    fn fullscreen_background_data() -> FullscreenBackgroundData {
        FullscreenBackgroundData {
            id: "standard".to_string(),
            image_url: "https://test.amazon.com".to_string(),
            enter_immediately: true,
            image_opacity_percentage: 100,
        }
    }

    fn linear_data() -> LinearBackgroundData {
        LinearBackgroundData {
            id: "standard".to_string(),
            image_url: "https://test.amazon.com".to_string(),
            video_id: None,
            vignette: LinearVignetteStyle::LiveTv,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Linear,
            csm_data: None,
            interaction_source: Some(AutoplayInteractionSource::AlwaysOn),
        }
    }

    fn linear_data_with_video(video_id: String) -> LinearBackgroundData {
        LinearBackgroundData {
            id: "standard".to_string(),
            image_url: "https://test.amazon.com".to_string(),
            video_id: Some(video_id),
            vignette: LinearVignetteStyle::LiveTv,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Linear,
            csm_data: None,
            interaction_source: Some(AutoplayInteractionSource::AlwaysOn),
        }
    }

    fn micro_background_data() -> MicroBackgroundData {
        MicroBackgroundData {
            id: "standard".to_string(),
            image_url: Some("https://test.amazon.com".to_string()),
            video_id: Some("VIDEO_ID".into()),
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None,
        }
    }

    fn exit_effect() -> ExitEffect {
        ExitEffect::FadeToStart {
            duration: Duration::from_millis(500),
            fade_media_background: true,
        }
    }

    fn enter_effect() -> EnterEffect {
        EnterEffect::FadeFromEnd {
            duration: Duration::from_millis(200),
        }
    }

    fn wait_for_autoplay_to_start() {
        MockClock::advance(Duration::from_millis(1500));
    }

    fn wait_for_fadeout() {
        MockClock::advance(Duration::from_millis(1500));
    }
}
