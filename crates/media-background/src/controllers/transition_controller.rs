use crate::types::common::ANIMATION_DURATION;
use crate::types::Transitionable;
use ignx_compositron::animation::Animation;
use ignx_compositron::context::AppContext;
use ignx_compositron::reactive::{
    create_effect, create_signal, on_cleanup, ReadSignal, RwSignal, Signal, SignalGet,
    SignalGetUntracked, SignalSet, WriteSignal,
};
use ignx_compositron::time::{Duration, Instant};
use std::ops::Deref;
use std::rc::Rc;

pub struct Options<T> {
    pub debounce: Box<dyn Fn(&T, &T) -> u64>,
}

#[derive(Clone, PartialEq)]
enum State {
    Visible,
    Pending,
    FadingOut,
    FadingIn,
}

const LINGER_OPACITY_FOR_OVERLAY: f32 = 0.6;

pub fn transition_controller<T: Transitionable<T> + Clone + PartialEq>(
    ctx: &AppContext,
    incoming_data: ReadSignal<T>,
    rendered_data: RwSignal<T>,
    next_data: Option<WriteSignal<T>>,
    set_overlay_opacity: WriteSignal<f32>,
    background_ready: Signal<bool>,
    options: Options<T>,
) {
    let (state, set_state) = create_signal(ctx.scope(), State::Pending);

    let change_data = move |data: T| {
        rendered_data.set(data.clone());

        if let Some(next_data) = next_data {
            next_data.set(data);
        }
    };

    let (transition_task_id, set_transition_task_id) = create_signal(ctx.scope(), None);
    let (dimming_task_id, set_dimming_task_id) = create_signal(ctx.scope(), None);

    let cancel_transition_timer = Rc::new({
        let ctx = ctx.clone();

        move || {
            if let Some(transition_task_id) = transition_task_id.try_get_untracked().flatten() {
                ctx.cancel_task(transition_task_id);
                set_transition_task_id.set(None);
            }
        }
    });

    let cancel_dimming_timer = Rc::new({
        let ctx = ctx.clone();

        move || {
            if let Some(dimming_task_id) = dimming_task_id.try_get_untracked().flatten() {
                ctx.cancel_task(dimming_task_id);
                set_dimming_task_id.set(None);
            }
        }
    });

    on_cleanup(ctx.scope(), {
        let cancel_transition_timer = cancel_transition_timer.clone();
        let cancel_dimming_timer = cancel_dimming_timer.clone();

        move || {
            cancel_transition_timer();
            cancel_dimming_timer();
        }
    });

    if let Some(incoming_data) = incoming_data.try_get_untracked() {
        change_data(incoming_data);
    }

    // Handles when the incoming_data changes.
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let cancel_dimming_timer = cancel_dimming_timer.clone();

        move |prev_data: Option<T>| {
            let new_data = incoming_data.get();
            let existing_data = rendered_data.get_untracked();
            let current_state = state.get_untracked();

            if !existing_data.should_transition(&new_data) || current_state == State::Pending {
                change_data(new_data.clone());
                cancel_transition_timer();
                cancel_dimming_timer();
            } else if let Some(prev_data) = prev_data {
                // If a new background comes in and then an update for that same background, we don't want to cancel the timer
                // unless should_transition_immediately() is now true.
                if (!prev_data.should_transition(&new_data) && !new_data.should_enter_immediately())
                    || current_state == State::FadingOut
                {
                    return new_data;
                }

                let delay = if new_data.should_enter_immediately() {
                    0
                } else {
                    options.debounce.deref()(&existing_data, &new_data)
                };

                cancel_transition_timer();

                let on_complete = move || set_state.set(State::FadingOut);

                if delay == 0 {
                    on_complete();
                } else {
                    set_transition_task_id.set(Some(ctx.schedule_task(
                        Instant::now() + Duration::from_millis(delay),
                        on_complete,
                    )));

                    if let Some(None) = dimming_task_id.try_get_untracked() {
                        set_dimming_task_id.set(Some(ctx.schedule_task(
                            Instant::now() + Duration::from_millis(delay + 1),
                            {
                                let ctx = ctx.clone();
                                move || {
                                    ctx.with_animation(
                                        Animation::default()
                                            .with_duration(Duration::from_millis(delay)),
                                        move || set_overlay_opacity.set(LINGER_OPACITY_FOR_OVERLAY),
                                    );

                                    set_dimming_task_id.set(None);
                                }
                            },
                        )));
                    }
                }
            }

            new_data
        }
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();

        let animate_opacity = move |target: f32| {
            ctx.with_animation(
                Animation::default().with_duration(Duration::from_millis(ANIMATION_DURATION)),
                move || set_overlay_opacity.set(target),
            );

            // temporary until animation::on_complete is implemented.
            ctx.schedule_task(Instant::now() + Duration::from_millis(150), move || {
                if target == 1.0 {
                    if let Some(incoming_data) = incoming_data.try_get_untracked() {
                        change_data(incoming_data);
                    }

                    if background_ready.try_get_untracked().unwrap_or_default() {
                        set_state.set(State::FadingIn);
                    } else {
                        set_state.set(State::Pending);
                    }
                } else {
                    set_state.set(State::Visible);
                }
            });
        };

        move |prev_state| {
            let new_state: State = state.get();

            if let Some(prev_state) = prev_state {
                if prev_state == new_state {
                    return new_state;
                }
            }

            if new_state == State::FadingOut {
                cancel_dimming_timer();

                if let Some(next_data) = next_data {
                    if let Some(incoming_data) = incoming_data.try_get_untracked() {
                        next_data.set(incoming_data);
                    }
                }
                animate_opacity(1.0);
            }

            if new_state == State::FadingIn {
                animate_opacity(0.0);
            }

            new_state
        }
    });

    create_effect(ctx.scope(), move |_| {
        if background_ready.get() && state.try_get_untracked() == Some(State::Pending) {
            set_state.set(State::FadingIn);
        }
    });
}
