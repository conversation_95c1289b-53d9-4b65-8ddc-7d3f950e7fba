use crate::types::common::{ANIMATION_DURATION, PAGE_DIMENSIONS};
use crate::types::{
    Dimensions, MediaStrategy, Playable, PlayerStatus, Position, Resizable, Transitionable, Video,
    Window,
};
use ignx_compositron::animation::Animation;
use ignx_compositron::context::AppContext;
use ignx_compositron::reactive::{
    create_effect, create_signal, on_cleanup, ReadSignal, RwSignal, Signal, SignalGet,
    SignalGetUntracked, SignalSet, WriteSignal,
};
use ignx_compositron::time::{Duration, Instant};
use std::cell::RefCell;
use std::fmt;
use std::rc::Rc;

#[derive(Clone)]
pub enum PlayerClientID {
    // Determines a Playback session of Autoplay promotion in BrowsePage (Mini-Details).
    SuperDraperOnBrowsePage,
    // Determines a Playback session of Autoplay promotion in BrowsePage (Standard and Tentpole Hero).
    SuperDraperHeroPlacements,
    // Used to classify the Watch Party playback sessions
    WatchParty,
}

impl fmt::Display for PlayerClientID {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match *self {
            PlayerClientID::SuperDraperOnBrowsePage => {
                write!(f, "3ab558cf-2c28-4dd1-b3c3-78dfec62b0e0")
            }
            PlayerClientID::SuperDraperHeroPlacements => {
                write!(f, "cfd2813d-bdc9-485b-82ac-aa75d3e7000c")
            }
            PlayerClientID::WatchParty => {
                write!(f, "28b5741e-56b3-4334-ae61-397780729a5a")
            }
        }
    }
}

pub struct VideoControllerOptions {
    pub window: Window,
    pub window_rtl: Option<Window>,
    pub client_id: PlayerClientID,
    pub delay: Option<Duration>,
    pub delay_only_after_image_loads: bool,
}

pub const RESIZED_WINDOW: Window = Window {
    dimensions: Dimensions {
        height: PAGE_DIMENSIONS.height,
        width: PAGE_DIMENSIONS.width,
    },
    position: Position { x: 0.0, y: 0.0 },
};

pub fn build_video<T: Playable + Resizable>(
    data: &T,
    window: &Window,
    client_id: &str,
) -> Option<Video> {
    let new_window = match (data.is_resizable(), data.is_resized()) {
        (true, true) => RESIZED_WINDOW,
        _ => window.clone(),
    };
    data.get_video_id().as_ref().map(|video_id| Video {
        id: video_id.clone(),
        client_id: client_id.to_owned(),
        window: new_window,
        media_strategy: data.get_media_strategy().clone(),
        csm_data: data.get_csm_data().clone(),
        placement: data.get_placement().clone(),
    })
}

// Linear content can update media background without changing video_id, and we should not interrupt Linear stream in this situation
fn linear_background_update_with_same_video<T: Transitionable<T> + Playable + Clone + PartialEq>(
    prev_data: &T,
    new_data: &T,
    player_status: PlayerStatus,
) -> bool {
    matches!(player_status, PlayerStatus::PLAYING)
        && new_data.get_video_id() == prev_data.get_video_id()
        && matches!(new_data.get_media_strategy(), MediaStrategy::Linear)
        && matches!(prev_data.get_media_strategy(), MediaStrategy::Linear)
}

pub fn video_controller<T: Transitionable<T> + Playable + Clone + PartialEq + Resizable>(
    ctx: &AppContext,
    rendered_data: ReadSignal<T>,
    incoming_data: ReadSignal<T>,
    background_ready: Signal<bool>,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_image_opacity: WriteSignal<f32>,
    set_window_opacity: WriteSignal<f32>,
    set_vignette_opacity: WriteSignal<f32>,
    options: VideoControllerOptions,
) {
    let (delay_completed, set_delay_completed) = create_signal::<bool>(ctx.scope(), false);
    let (current_trailer_has_played, set_current_trailer_has_played) =
        create_signal::<bool>(ctx.scope(), false);
    let delay_task_id = Rc::new(RefCell::new(None));

    let cancel_delay_timer_builder = {
        let ctx = ctx.clone();
        let delay_task_id = delay_task_id.clone();

        move || {
            let ctx = ctx.clone();
            let delay_task_id = delay_task_id.clone();

            move || {
                if let Some(task_id) = delay_task_id.take() {
                    ctx.cancel_task(task_id);
                }
            }
        }
    };

    on_cleanup(ctx.scope(), cancel_delay_timer_builder());

    let cancel_timer = cancel_delay_timer_builder();

    let schedule_delay_builder = {
        let delay_task_id = delay_task_id.clone();
        let ctx = ctx.clone();
        move || {
            let ctx = ctx.clone();
            let cancel_timer = cancel_delay_timer_builder();
            let delay_task_id = delay_task_id.clone();

            move || {
                cancel_timer();

                if let Some(delay) = options.delay {
                    let delay_task_id_cloned = delay_task_id.clone();

                    let task_id = ctx.schedule_task(Instant::now() + delay, move || {
                        set_delay_completed.set(true);
                        delay_task_id_cloned.replace(None);
                    });

                    delay_task_id.replace(Some(task_id));
                } else {
                    set_delay_completed.set(true);
                }
            }
        }
    };

    let schedule_delay = schedule_delay_builder();

    //Due to a bug described below we need to track whether a trailer has been played
    create_effect(
        ctx.scope(),
        move |prev: Option<(Option<String>, PlayerStatus)>| {
            let current_player_status = player_status.get();
            let current_data = rendered_data.get();
            let current_video_id = current_data.get_video_id();

            if let Some(prev) = prev {
                if &prev.0 == current_video_id {
                    if prev.1 == PlayerStatus::PLAYING && prev.1 != current_player_status {
                        set_current_trailer_has_played.set(true);
                    }
                } else {
                    set_current_trailer_has_played.set(false)
                }
            }

            (current_video_id.clone(), current_player_status)
        },
    );

    // handles when the rendered data changes.
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let window = options.window.clone();
        let client_id = options.client_id.to_string();
        move |prev_data: Option<T>| {
            let new_data = rendered_data.get();

            if let Some(prev_data) = prev_data {
                let should_not_transition = !new_data.should_transition(&prev_data);
                // check if the data is for the currently rendered background.
                if should_not_transition
                    || linear_background_update_with_same_video(
                        &prev_data,
                        &new_data,
                        player_status.get_untracked(),
                    )
                {
                    // If the update is for the same background, we may need to start or stop the video if 1) the video_id has changed or 2) the video_id is the same but the video was stopped/finished
                    if background_ready.get_untracked() {
                        if prev_data.get_video_id() != new_data.get_video_id()
                            || playing_video.get_untracked().is_none()
                        {
                            let new_video = build_video(&new_data, &window, &client_id);
                            let start_next_video = move || playing_video.set(new_video);
                            if playing_video.get_untracked().is_some() {
                                ctx.with_animation(
                                    Animation::default()
                                        .with_duration(Duration::from_micros(ANIMATION_DURATION)),
                                    move || {
                                        set_image_opacity.set(1.0);
                                        set_window_opacity.set(0.0);
                                    },
                                );
                                ctx.schedule_task(
                                    Instant::now() + Duration::from_millis(ANIMATION_DURATION),
                                    start_next_video,
                                );
                            } else {
                                start_next_video();
                            }
                        } else if let Some(current_video) = playing_video.get_untracked() {
                            let can_be_resized = new_data.is_resizable() && should_not_transition;
                            let is_resized = new_data.is_resized();
                            let resized_changed = prev_data.is_resized() != is_resized;
                            if can_be_resized && resized_changed {
                                let new_video = Video {
                                    window: if is_resized {
                                        RESIZED_WINDOW
                                    } else {
                                        window.clone()
                                    },
                                    ..current_video
                                };

                                if player_status.get_untracked() == PlayerStatus::PLAYING {
                                    set_vignette_opacity.set(if !is_resized { 1.0 } else { 0.0 });
                                } else if !current_trailer_has_played.get() {
                                    //Hack - we should not have to destroy and recreate the playing_video signal, but without this line the subsequent playing_video
                                    //window update is not consistently applied, resulting in a misalignment between window size and vignettes rendered.
                                    //Namely on transition back from details to collections before the trailer has started playing.
                                    //We need to ensure the trailer has not already played, otherwise destroying the signal will trigger the trailer playing for a
                                    //second time.
                                    //There is a side effect to this hack in that the timer will restart each time the `resized` flag changes before the trailer has
                                    //started to play.
                                    //Platform TT: https://t.corp.amazon.com/D262196767
                                    playing_video.set(None);
                                }
                                playing_video.set(Some(new_video));
                            }
                        }
                    }
                    return new_data;
                }
            }
            cancel_timer();
            playing_video.set(None);
            set_delay_completed.set(false);
            if !options.delay_only_after_image_loads {
                schedule_delay();
            }

            new_data
        }
    });

    let schedule_delay = schedule_delay_builder();

    // handles when the background becomes ready.
    create_effect(ctx.scope(), move |_| {
        if background_ready.get() && playing_video.get_untracked().is_none() {
            if delay_completed.get() {
                playing_video.set(build_video(
                    &rendered_data.get_untracked(),
                    &options.window.clone(),
                    &options.client_id.to_string(),
                ));
            } else if options.delay_only_after_image_loads && delay_task_id.borrow().is_none() {
                schedule_delay();
            }
        }
    });

    // handles when the background becomes ready.
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            if background_ready.get() {
                let target = match player_status.get() {
                    PlayerStatus::STOPPED => 1.0,
                    PlayerStatus::PLAYING => 0.0,
                    PlayerStatus::PAUSED => 0.0,
                };
                if target == 1.0 {
                    set_vignette_opacity.set(1.0);
                    set_window_opacity.set(0.0);
                }

                ctx.with_animation(
                    Animation::default()
                        .with_duration(Duration::from_millis(ANIMATION_DURATION))
                        .with_on_finish(move || {
                            if target == 0.0 {
                                set_window_opacity.set(1.0);
                                let use_vignette_while_trailer_playing =
                                    rendered_data.get_untracked().is_resizable()
                                        && !rendered_data.get_untracked().is_resized();
                                set_vignette_opacity.set(if use_vignette_while_trailer_playing {
                                    1.0
                                } else {
                                    0.0
                                });
                            }
                        }),
                    move || {
                        set_image_opacity.set(target);
                    },
                );
            }
        }
    });

    // We must stop the video immediately when moving to another title, irregardless of normal debounce.
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();

        move |_| {
            let next = incoming_data.get();
            let current = rendered_data.get_untracked();
            if next.should_transition(&current)
                && player_status.try_get_untracked() == Some(PlayerStatus::PLAYING)
                && !linear_background_update_with_same_video(
                    &current,
                    &next,
                    player_status
                        .try_get_untracked()
                        .unwrap_or(PlayerStatus::STOPPED),
                )
            {
                set_window_opacity.set(0.0);
                set_vignette_opacity.set(1.0);
                ctx.with_animation(
                    Animation::default().with_duration(Duration::from_millis(ANIMATION_DURATION)),
                    move || {
                        set_image_opacity.set(1.0);
                    },
                );
                ctx.schedule_task(
                    Instant::now() + Duration::from_millis(ANIMATION_DURATION),
                    move || {
                        playing_video.set(None);
                    },
                );
            }
        }
    });
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::types::{
        Dimensions, Position, ResizableStandardBackgroundData, StandardBackgroundData,
    };
    use crate::Label;
    use crate::LabelCaller;
    use crate::LabelProps;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::label::LabelComposable;
    use ignx_compositron::prelude::{provide_context, use_context};
    use ignx_compositron::{
        app::launch_only_app_context,
        compose,
        prelude::{create_rw_signal, SignalUpdate},
    };
    use rstest::rstest;
    use serial_test::serial;

    #[rstest]
    #[serial]
    #[case::linear_playback_no_interruption("linearVideoId-1".to_string(), MediaStrategy::Linear, PlayerStatus::PLAYING, false)]
    #[case::interrupt_new_video("linearVideoId-2".to_string(), MediaStrategy::Linear, PlayerStatus::PLAYING, true)]
    #[case::interrupt_different_strategy("linearVideoId-1".to_string(), MediaStrategy::Promo, PlayerStatus::PLAYING, true)]
    #[case::interrupt_not_playing("linearVideoId-1".to_string(), MediaStrategy::Linear, PlayerStatus::STOPPED, true)]
    fn it_handles_data_change_with_linear_content(
        #[case] incoming_id: String,
        #[case] incoming_strategy: MediaStrategy,
        #[case] player_status: PlayerStatus,
        #[case] stop_video: bool,
    ) {
        launch_only_app_context(move |ctx| {
            let scope = ctx.scope();
            let rendered_data = create_rw_signal(
                scope,
                StandardBackgroundData {
                    id: "heroImage".to_string(),
                    image_url: Some("heroImage".to_string()),
                    video_id: Some("linearVideoId-1".to_string()),
                    enter_immediately: false,
                    placement: "PVBrowse".to_string(),
                    media_strategy: MediaStrategy::Linear,
                    csm_data: None,
                    interaction_source_override: None,
                },
            );

            let incoming_data = create_rw_signal(scope, rendered_data.get_untracked());
            let background_ready = Signal::derive(scope, move || true);
            let playing_video: RwSignal<Option<Video>> = create_rw_signal(scope, None);
            let player_status = create_rw_signal(scope, player_status);
            let image_opacity = create_rw_signal(scope, 1.0f32);
            let vignette_opacity = create_rw_signal(scope, 0.0f32);
            let window_opacity = create_rw_signal(scope, 1.0);

            video_controller(
                &ctx,
                rendered_data.read_only(),
                incoming_data.read_only(),
                background_ready,
                playing_video,
                player_status.into(),
                image_opacity.write_only(),
                window_opacity.write_only(),
                vignette_opacity.write_only(),
                VideoControllerOptions {
                    window: Window {
                        dimensions: Dimensions {
                            height: 0.0,
                            width: 0.0,
                        },
                        position: Position { x: 0.0, y: 0.0 },
                    },
                    window_rtl: None,
                    client_id: PlayerClientID::SuperDraperOnBrowsePage,
                    delay: Some(Duration::from_millis(0)),
                    delay_only_after_image_loads: false,
                },
            );

            // update incoming data with new data to test stop video effect
            incoming_data.update(|data| {
                data.video_id = Some(incoming_id.clone());
                data.media_strategy = incoming_strategy.clone();
                data.id = "newID".to_string();
            });

            // update rendered data with new data to test playing_video effect
            rendered_data.set(incoming_data.get_untracked());

            // Window opacity should be zeroed when video changes
            if stop_video {
                assert_eq!(window_opacity.get_untracked(), 0.0);
                assert!(playing_video.get_untracked().is_none());
            } else {
                assert_eq!(window_opacity.get_untracked(), 1.0);
                assert!(playing_video.get_untracked().is_some());
            }
        });
    }

    fn get_mock_resizable_data() -> ResizableStandardBackgroundData {
        ResizableStandardBackgroundData {
            id: "test".to_string(),
            video_id: Some("video1".to_string()),
            image_url: None,
            enter_immediately: false,
            placement: "test".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            interaction_source_override: None,
            resized: false,
        }
    }

    fn get_default_window() -> Window {
        Window {
            dimensions: Dimensions {
                width: 100.0,
                height: 100.0,
            },
            position: Position { x: 0.0, y: 0.0 },
        }
    }

    #[test]
    #[serial]
    fn test_build_video_with_default_window() {
        let default_window = get_default_window();

        let resizable_data = get_mock_resizable_data();

        let actual_window = build_video(&resizable_data, &default_window, "test-client")
            .unwrap()
            .window;
        let expected_window = default_window;

        assert!(actual_window == expected_window);
    }

    fn setup_resize_test(ctx: AppContext) -> LabelComposable {
        let default_window = get_default_window();
        let scope = ctx.scope();
        let resizable_data = get_mock_resizable_data();

        let rendered_data = create_rw_signal(scope, resizable_data.clone());
        provide_context::<RwSignal<ResizableStandardBackgroundData>>(scope, rendered_data);
        let incoming_data = create_rw_signal(scope, resizable_data.clone());
        let background_ready = Signal::derive(scope, move || true);
        let playing_video = create_rw_signal(scope, None);
        provide_context::<RwSignal<Option<Video>>>(scope, playing_video);
        let player_status = create_rw_signal(scope, PlayerStatus::PLAYING);
        let image_opacity = create_rw_signal(scope, 1.0f32);
        let window_opacity = create_rw_signal(scope, 1.0f32);
        let vignette_opacity = create_rw_signal(scope, 0.0f32);

        video_controller(
            &ctx,
            rendered_data.read_only(),
            incoming_data.read_only(),
            background_ready,
            playing_video,
            player_status.into(),
            image_opacity.write_only(),
            window_opacity.write_only(),
            vignette_opacity.write_only(),
            VideoControllerOptions {
                window: default_window,
                window_rtl: None,
                client_id: PlayerClientID::SuperDraperOnBrowsePage,
                delay: Some(Duration::from_millis(10)),
                delay_only_after_image_loads: true,
            },
        );
        compose! {
            Label(text: "empty")
        }
    }

    #[test]
    #[serial]
    fn test_window_resizes_if_should_transition_false() {
        launch_test(setup_resize_test, move |scope, mut game_loop| {
            let rendered_data = use_context::<RwSignal<ResizableStandardBackgroundData>>(scope)
                .expect("ResizableStandardBackgroundData to exist");
            let playing_video =
                use_context::<RwSignal<Option<Video>>>(scope).expect("Video to exist");
            game_loop.advance_time(Duration::from_millis(10));
            game_loop.tick_until_done();

            let actual_window = playing_video.get_untracked().unwrap().window;

            assert!(actual_window == get_default_window());

            rendered_data.update(|data| {
                data.resized = true;
            });

            let actual_window = playing_video.get_untracked().unwrap().window;
            assert!(actual_window == RESIZED_WINDOW);
        });
    }

    #[test]
    fn test_window_does_not_resize_if_should_transition_true() {
        launch_test(setup_resize_test, move |scope, mut game_loop| {
            let rendered_data = use_context::<RwSignal<ResizableStandardBackgroundData>>(scope)
                .expect("ResizableStandardBackgroundData to exist");
            let playing_video =
                use_context::<RwSignal<Option<Video>>>(scope).expect("Video to exist");
            game_loop.advance_time(Duration::from_millis(10));
            game_loop.tick_until_done();

            let actual_window = playing_video.get_untracked().unwrap().window;

            assert!(actual_window == get_default_window());

            rendered_data.update(|data| {
                data.id = "test2".to_string();
                data.resized = true;
            });

            assert!(playing_video.get_untracked().is_none());
        });
    }
}
