use ignx_compositron::prelude::*;

use super::{Dimensions, PlayerStatus, Video};

pub const PAGE_DIMENSIONS: Dimensions = Dimensions {
    height: 1080.0,
    width: 1920.0,
};

pub const ANIMATION_DURATION: u64 = 150;

#[derive(<PERSON><PERSON>, Co<PERSON>)]
pub struct MediaBackgroundContext {
    pub(crate) playing_video_rw: RwSignal<Option<Video>>,
    pub(crate) ready_rw: RwSignal<bool>,
    pub playing_video: ReadSignal<Option<Video>>,
    pub ready: ReadSignal<bool>,
    pub player_status: Signal<PlayerStatus>,
}
