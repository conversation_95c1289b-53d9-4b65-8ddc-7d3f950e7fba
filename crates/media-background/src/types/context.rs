use ignx_compositron::{player_data::ResizeSupport, prelude::*};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
pub enum BackgroundState {
    Pending,
    Downloaded,
    Error,
}

#[derive(Clone)]
pub struct MediaBackgroundStatus {
    pub id: String,
    pub background_state: BackgroundState,
}

// Only to be used within the crate because it has write-access.
#[derive(Clone)]
pub(crate) struct MediaBackgroundStatusContextInternal {
    pub background_state: RwSignal<Option<MediaBackgroundStatus>>,
    pub player_resize_support: RwSignal<Option<ResizeSupport>>,
}

#[derive(Clone)]
pub struct MediaBackgroundStatusContext {
    pub background_state: Signal<Option<MediaBackgroundStatus>>,
    pub player_resize_support: Signal<Option<ResizeSupport>>,
}

pub fn provide_media_background_status_context(scope: Scope) {
    let full_access_context = MediaBackgroundStatusContextInternal {
        background_state: create_rw_signal(scope, None),
        player_resize_support: create_rw_signal(scope, None),
    };
    let read_only_context = MediaBackgroundStatusContext {
        background_state: full_access_context.background_state.into(),
        player_resize_support: full_access_context.player_resize_support.into(),
    };

    // Full access context
    provide_context::<MediaBackgroundStatusContextInternal>(scope, full_access_context);

    // To be consumed by other crates
    provide_context::<MediaBackgroundStatusContext>(scope, read_only_context);
}

pub(crate) fn use_media_background_status_internal(
    scope: Scope,
) -> Option<MediaBackgroundStatusContextInternal> {
    use_context(scope)
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::app::launch_only_scope;

    #[test]
    fn provide_read_only_context_from_internal_context() {
        launch_only_scope(|scope| {
            provide_media_background_status_context(scope);
            let MediaBackgroundStatusContextInternal {
                background_state: write_background_state,
                player_resize_support: write_player_resize_support,
            } = expect_context(scope);
            let MediaBackgroundStatusContext {
                background_state: read_background_state,
                player_resize_support: read_player_resize_support,
            } = expect_context(scope);

            // Verify initialization
            assert!(read_background_state.get_untracked().is_none());
            assert!(read_player_resize_support.get_untracked().is_none());

            // Verify setting value expose to read-only context
            write_background_state.set(Some(MediaBackgroundStatus {
                id: "some id".to_string(),
                background_state: BackgroundState::Downloaded,
            }));
            assert_eq!(read_background_state.get_untracked().unwrap().id, "some id");

            write_player_resize_support.set(Some(ResizeSupport::Dynamic));
            assert_eq!(
                read_player_resize_support.get_untracked(),
                Some(ResizeSupport::Dynamic)
            );
        })
    }
}
