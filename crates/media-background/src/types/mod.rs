pub mod common;
pub mod context;
use cacheable_derive::*;
use cfg_test_attr_derive::derive_test_only;
use common::PAGE_DIMENSIONS;
use ignx_compositron::player_data::{MediaReproductionType, ResizeSupport};
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::{RwSignal, SignalSetUntracked};
use media_background_derive::*;
use mockall_double::double;
use playback::context::Viewport;
use playback::src::{
    LinearSrc, LinearStartMode, LiveEventSrc, LiveEventStartMode, PlaybackOrigin, PlaybackSrc,
    PromoPlacement, PromoSrc,
};
pub use playback::{
    provide_hybrid_playback, use_hybrid_playback, HybridPlaybackContext, ReleaseState,
};
#[double]
use profile_manager::ProfileManager;
#[double]
use resiliency_store::ResiliencyStore;
use std::mem;
use strum::Display;

#[derive(Clone, PartialEq, Cacheable, Display)]
#[derive_test_only(Debug)]
pub enum AutoplayInteractionSource {
    AlwaysOn,
    StationDetailPage,
    HomePageLinearCarousel,
}

impl From<AutoplayInteractionSource> for PlaybackOrigin {
    fn from(value: AutoplayInteractionSource) -> Self {
        match value {
            AutoplayInteractionSource::AlwaysOn => PlaybackOrigin::AlwaysOn,
            AutoplayInteractionSource::StationDetailPage => PlaybackOrigin::StationDetailPage,
            AutoplayInteractionSource::HomePageLinearCarousel => {
                PlaybackOrigin::HomePageLinearCarousel
            }
        }
    }
}

#[derive(Clone, PartialEq, Cacheable)]
#[derive_test_only(Debug)]
pub enum MediaStrategy {
    Live,
    Linear,
    Promo,
}

impl From<MediaStrategy> for MediaReproductionType {
    fn from(val: MediaStrategy) -> Self {
        match val {
            MediaStrategy::Live => MediaReproductionType::Live,
            MediaStrategy::Linear => MediaReproductionType::Linear,
            MediaStrategy::Promo => MediaReproductionType::Promo,
        }
    }
}

#[derive(Clone, PartialEq, Cacheable)]
#[derive_test_only(Debug)]
pub enum MediaBackgroundType {
    None,
    #[cacheable(contains_cacheable_type)]
    Standard(StandardBackgroundData),
    #[cacheable(contains_cacheable_type)]
    ResizableStandard(ResizableStandardBackgroundData),
    #[cacheable(contains_cacheable_type)]
    Tentpole(TentpoleBackgroundData),
    StandardHero(StandardHeroBackgroundData),
    #[cacheable(contains_cacheable_type)]
    FullscreenBackground(FullscreenBackgroundData),
    #[cacheable(contains_cacheable_type)]
    Linear(LinearBackgroundData),
    #[cacheable(contains_cacheable_type)]
    SuperCarousel(SuperCarouselBackgroundData),
    #[cacheable(contains_cacheable_type)]
    ResizableSuperCarousel(ResizableSuperCarouselBackgroundData),
    #[cacheable(contains_cacheable_type)]
    Micro(MicroBackgroundData),
    Playback,
    #[cacheable(contains_cacheable_type)]
    SpecialCollections(SpecialCollectionsBackgroundData),
}

impl MediaBackgroundType {
    pub fn get_interaction_source_override(&self) -> Option<String> {
        match self {
            MediaBackgroundType::Linear(data) => &data.interaction_source,
            MediaBackgroundType::Standard(data) => &data.interaction_source_override,
            MediaBackgroundType::ResizableStandard(data) => &data.interaction_source_override,
            MediaBackgroundType::None
            | MediaBackgroundType::Tentpole(_)
            | MediaBackgroundType::StandardHero(_)
            | MediaBackgroundType::Micro(_)
            | MediaBackgroundType::FullscreenBackground(_)
            | MediaBackgroundType::SuperCarousel(_)
            | MediaBackgroundType::ResizableSuperCarousel(_)
            | MediaBackgroundType::Playback => &None,
            MediaBackgroundType::SpecialCollections(_) => &None,
        }
        .as_ref()
        .map(|src| src.to_string())
    }

    pub fn get_playback_origin(&self) -> Option<PlaybackOrigin> {
        match self {
            MediaBackgroundType::Linear(data) => data.interaction_source.clone(),
            MediaBackgroundType::Standard(data) => data.interaction_source_override.clone(),
            MediaBackgroundType::ResizableStandard(data) => {
                data.interaction_source_override.clone()
            }
            MediaBackgroundType::None
            | MediaBackgroundType::Tentpole(_)
            | MediaBackgroundType::StandardHero(_)
            | MediaBackgroundType::Micro(_)
            | MediaBackgroundType::FullscreenBackground(_)
            | MediaBackgroundType::SuperCarousel(_)
            | MediaBackgroundType::ResizableSuperCarousel(_)
            | MediaBackgroundType::SpecialCollections(_)
            | MediaBackgroundType::Playback => None,
        }
        .map(Into::into)
    }

    pub fn get_id(&self) -> String {
        match self {
            MediaBackgroundType::Standard(data) => data.id.clone(),
            MediaBackgroundType::ResizableStandard(data) => data.id.clone(),
            MediaBackgroundType::Tentpole(data) => data.id.clone(),
            MediaBackgroundType::StandardHero(data) => data.id.clone(),
            MediaBackgroundType::FullscreenBackground(data) => data.id.clone(),
            MediaBackgroundType::Linear(data) => data.id.clone(),
            MediaBackgroundType::SuperCarousel(data) => data.id.clone(),
            MediaBackgroundType::ResizableSuperCarousel(data) => data.id.clone(),
            MediaBackgroundType::Micro(data) => data.id.clone(),
            MediaBackgroundType::None => "None".to_string(),
            MediaBackgroundType::Playback => "Playback".to_string(),
            MediaBackgroundType::SpecialCollections(data) => data.id.clone(),
        }
    }
}

impl Transitionable<MediaBackgroundType> for MediaBackgroundType {
    fn should_transition(&self, other: &MediaBackgroundType) -> bool {
        let are_different =
            mem::discriminant::<MediaBackgroundType>(self) != mem::discriminant(other);
        let is_super = move |mbt: &MediaBackgroundType| {
            matches!(mbt, MediaBackgroundType::SuperCarousel(_))
                || matches!(mbt, MediaBackgroundType::ResizableSuperCarousel(_))
        };

        are_different && (!is_super(self) || is_super(other))
    }

    fn should_enter_immediately(&self) -> bool {
        match &self {
            MediaBackgroundType::Standard(data) => data.should_enter_immediately(),
            MediaBackgroundType::ResizableStandard(data) => data.should_enter_immediately(),
            MediaBackgroundType::StandardHero(data) => data.should_enter_immediately(),
            MediaBackgroundType::Tentpole(data) => data.should_enter_immediately(),
            MediaBackgroundType::FullscreenBackground(data) => data.should_enter_immediately(),
            MediaBackgroundType::Linear(data) => data.should_enter_immediately(),
            MediaBackgroundType::SuperCarousel(data) => data.should_enter_immediately(),
            MediaBackgroundType::ResizableSuperCarousel(data) => data.should_enter_immediately(),
            MediaBackgroundType::Micro(data) => data.should_enter_immediately(),
            MediaBackgroundType::SpecialCollections(data) => data.should_enter_immediately(),
            MediaBackgroundType::None => false,
            MediaBackgroundType::Playback => false,
        }
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl MediaBackgroundType {
    pub fn create_mock() -> Self {
        Self::Standard(StandardBackgroundData {
            id: "gti".to_string(),
            image_url: Some("hero image".to_string()),
            video_id: Some("gti".to_string()),
            enter_immediately: false,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: Some("CSMData".to_string()),
            interaction_source_override: None,
        })
    }
}

pub trait Transitionable<T> {
    fn should_transition(&self, other: &T) -> bool;
    fn should_enter_immediately(&self) -> bool;
}

pub trait Playable {
    fn get_video_id(&self) -> &Option<String>;
    fn get_media_strategy(&self) -> &MediaStrategy;
    fn get_csm_data(&self) -> &Option<String>;
    fn get_placement(&self) -> &String;
}

pub trait Resizable {
    fn is_resizable(&self) -> bool;
    fn is_resized(&self) -> bool;
}

impl Resizable for ResizableStandardBackgroundData {
    fn is_resizable(&self) -> bool {
        true
    }

    fn is_resized(&self) -> bool {
        self.resized
    }
}

impl Resizable for ResizableSuperCarouselBackgroundData {
    fn is_resizable(&self) -> bool {
        true
    }

    fn is_resized(&self) -> bool {
        self.resized
    }
}

#[derive(Clone, PartialEq, Playable, Transitionable, Resizable)]
#[derive_test_only(Debug)]
pub struct StandardBackgroundData {
    pub id: String,
    pub image_url: Option<String>,
    pub csm_data: Option<String>,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub placement: String,
    pub media_strategy: MediaStrategy,
    pub interaction_source_override: Option<AutoplayInteractionSource>,
}

#[derive(Clone, PartialEq, Playable, Transitionable)]
#[derive_test_only(Debug)]
pub struct ResizableStandardBackgroundData {
    pub id: String,
    pub image_url: Option<String>,
    pub csm_data: Option<String>,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub placement: String,
    pub media_strategy: MediaStrategy,
    pub interaction_source_override: Option<AutoplayInteractionSource>,
    pub resized: bool,
}

#[derive(Clone, Eq, PartialEq, Transitionable)]
#[derive_test_only(Debug)]
pub struct FullscreenBackgroundData {
    pub id: String,
    pub image_url: String,
    pub enter_immediately: bool,
    // Out of 100
    pub image_opacity_percentage: u8,
}

#[derive(Clone, Eq, PartialEq, Transitionable)]
#[derive_test_only(Debug)]
pub struct RecapBackgroundData {
    pub id: String,
    pub image_url: String,
    pub enter_immediately: bool,
    pub width: Option<i32>,
    pub height: Option<i32>,
}

#[derive(Clone, PartialEq, Playable, Transitionable, Resizable)]
#[derive_test_only(Debug)]
pub struct TentpoleBackgroundData {
    pub id: String,
    pub image_url: String,
    pub csm_data: Option<String>,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub placement: String,
    pub media_strategy: MediaStrategy,
}

#[derive(Clone, PartialEq)]
#[derive_test_only(Debug)]
pub enum LinearVignetteStyle {
    LiveTv,
    StationDetails,
}

#[derive(Clone, PartialEq, Playable, Transitionable, Resizable)]
#[derive_test_only(Debug)]
pub struct LinearBackgroundData {
    pub id: String,
    pub image_url: String,
    pub csm_data: Option<String>,
    pub video_id: Option<String>,
    pub vignette: LinearVignetteStyle,
    pub enter_immediately: bool,
    pub placement: String,
    pub media_strategy: MediaStrategy,
    pub interaction_source: Option<AutoplayInteractionSource>,
}

#[derive(Clone, PartialEq, Playable, Transitionable, Cacheable, Resizable)]
#[derive_test_only(Debug)]
pub struct StandardHeroBackgroundData {
    pub id: String,
    pub image_url: Option<String>,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub media_strategy: MediaStrategy,
    pub csm_data: Option<String>,
    // TODO Don't convert
    pub rotation_direction: RotationDirection,
    pub is_expanded: RwSignal<bool>,
    pub placement: String,
}

#[derive(Clone, PartialEq, Playable, Transitionable, Cacheable, Resizable)]
#[derive_test_only(Debug)]
pub struct SuperCarouselBackgroundData {
    pub id: String,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub media_strategy: MediaStrategy,
    pub has_title: bool,
    pub csm_data: Option<String>,
    pub placement: String,
}

#[derive(Clone, PartialEq, Playable, Transitionable, Cacheable)]
#[derive_test_only(Debug)]
pub struct ResizableSuperCarouselBackgroundData {
    pub id: String,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub media_strategy: MediaStrategy,
    pub has_title: bool,
    pub csm_data: Option<String>,
    pub placement: String,
    pub image_url: Option<String>,
    pub resized: bool,
}

#[derive(Clone, PartialEq, Playable, Transitionable, Cacheable, Resizable)]
#[derive_test_only(Debug)]
pub struct MicroBackgroundData {
    pub id: String,
    pub image_url: Option<String>,
    pub csm_data: Option<String>,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub placement: String,
    pub media_strategy: MediaStrategy,
    pub interaction_source_override: Option<AutoplayInteractionSource>,
}

#[derive(Clone, PartialEq, Playable, Transitionable, Cacheable, Resizable)]
#[derive_test_only(Debug)]
pub struct SpecialCollectionsBackgroundData {
    pub id: String,
    pub image_url: Option<String>,
    pub csm_data: Option<String>,
    pub video_id: Option<String>,
    pub enter_immediately: bool,
    pub placement: String,
    pub media_strategy: MediaStrategy,
    pub interaction_source_override: Option<AutoplayInteractionSource>,
}

#[derive(Copy, Clone, Eq, PartialEq, Cacheable)]
#[derive_test_only(Debug)]
pub enum RotationDirection {
    NONE,
    LEFT,
    RIGHT,
}

#[derive(Copy, Clone, PartialEq)]
pub struct Dimensions {
    pub width: f32,
    pub height: f32,
}

#[derive(Copy, Clone, PartialEq)]
pub struct Position {
    pub x: f32,
    pub y: f32,
}

#[derive(Clone, PartialEq)]
pub struct Window {
    pub position: Position,
    pub dimensions: Dimensions,
}

impl Window {
    const DELTA: f32 = 0.01;

    pub fn fullscreen() -> Self {
        Self {
            position: Position { x: 0., y: 0. },
            dimensions: PAGE_DIMENSIONS,
        }
    }

    pub fn is_fullscreen(&self) -> bool {
        let Position { x, y } = self.position;
        let Dimensions { width, height } = self.dimensions;

        x.abs() < Self::DELTA
            && y.abs() < Self::DELTA
            && (width - PAGE_DIMENSIONS.width).abs() < Self::DELTA
            && (height - PAGE_DIMENSIONS.height).abs() < Self::DELTA
    }

    pub fn to_viewport(&self) -> Viewport {
        let Position { x, y } = self.position;
        let Dimensions { width, height } = self.dimensions;

        Viewport {
            left: x,
            top: y,
            width,
            height,
        }
    }
}

#[derive(Clone, PartialEq)]
pub struct Video {
    pub window: Window,
    pub client_id: String,
    pub id: String,
    pub media_strategy: MediaStrategy,
    pub csm_data: Option<String>,
    pub placement: String,
}

impl Video {
    pub fn to_playback_src(&self) -> Option<PlaybackSrc> {
        let client_id = Some(self.client_id.clone());
        match self.media_strategy {
            MediaStrategy::Promo => Some(PlaybackSrc::Promo(PromoSrc {
                placement: PromoPlacement::PVBrowse,
                client_side_metrics: self.csm_data.clone(),
                title_id: self.id.clone(),
                client_id,
            })),
            MediaStrategy::Linear => Some(PlaybackSrc::Linear(LinearSrc {
                title_id: self.id.clone(),
                start_mode: LinearStartMode::AtLive,
                client_id,
            })),
            MediaStrategy::Live => Some(PlaybackSrc::LiveEvent(LiveEventSrc {
                title_id: self.id.clone(),
                playback_envelope: None,
                start_mode: LiveEventStartMode::AtLive,
                client_id,
            })),
        }
    }

    pub fn should_autoplay(
        &self,
        resiliency_store: &ResiliencyStore,
        profile_manager: &ProfileManager,
        resize_support: Option<ResizeSupport>,
    ) -> bool {
        if resize_support.is_none() && !self.window.is_fullscreen() {
            return false;
        }

        match self.media_strategy {
            MediaStrategy::Live => {
                let resiliency_switch_on = resiliency_store.get_disable_live_event_autoplay();
                let active_profile = profile_manager.get_active_profile().get();
                let is_kids_profile = active_profile.is_some_and(|profile| !profile.isAdult);
                !resiliency_switch_on && !is_kids_profile
            }
            _ => true,
        }
    }
}

#[derive(Clone, PartialEq)]
pub enum PlayerStatus {
    PLAYING,
    PAUSED,
    STOPPED,
}

#[derive(Copy, Clone)]
pub struct TrailerPlaybackContext {
    pub on_playback_finished: RwSignal<bool>,
    pub on_trailer_playback_start: RwSignal<bool>,
    pub should_play_hero: RwSignal<bool>,
    pub suppress_trailer_playback: RwSignal<bool>,
}

impl TrailerPlaybackContext {
    fn new(scope: Scope) -> Self {
        Self {
            on_playback_finished: create_rw_signal(scope, false),
            on_trailer_playback_start: create_rw_signal(scope, false),
            should_play_hero: create_rw_signal(scope, false),
            suppress_trailer_playback: create_rw_signal(scope, false),
        }
    }

    pub fn reset(&self) {
        self.on_playback_finished.set_untracked(false);
        self.on_trailer_playback_start.set_untracked(false);
    }
}

pub fn provide_trailer_playback(scope: Scope) {
    provide_context(scope, TrailerPlaybackContext::new(scope));
}

pub fn use_trailer_playback(scope: Scope) -> TrailerPlaybackContext {
    expect_context(scope)
}

pub fn derive_is_hero_trailer_playing_signal(scope: Scope) -> Signal<bool> {
    let Some(trailer_context) = use_context::<TrailerPlaybackContext>(scope) else {
        return Signal::derive(scope, || false);
    };
    let Some(media_background_type) = use_context::<RwSignal<MediaBackgroundType>>(scope) else {
        return Signal::derive(scope, || false);
    };

    Signal::derive(scope, move || {
        let is_hero_trailer = matches!(
            media_background_type.try_get_untracked(),
            Some(MediaBackgroundType::StandardHero(_) | MediaBackgroundType::Tentpole(_))
        );
        let is_playing = trailer_context.on_trailer_playback_start.get();

        is_playing && is_hero_trailer
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use common_transform_types::profile::Profile;
    use common_transform_types::profile::ProfileAvatar;
    use ignx_compositron::app::launch_only_scope;
    use profile_manager::mock_use_profile_manager;
    use profile_manager::MockProfileManager;
    use resiliency_store::{mock_use_resiliency_store, MockResiliencyStore};
    use rstest::rstest;

    #[rstest]
    #[case(MediaStrategy::Live, MediaReproductionType::Live)]
    #[case(MediaStrategy::Linear, MediaReproductionType::Linear)]
    #[case(MediaStrategy::Promo, MediaReproductionType::Promo)]
    fn test_from_media_strategy(
        #[case] input: MediaStrategy,
        #[case] expected: MediaReproductionType,
    ) {
        let reproduction_type: MediaReproductionType = input.into();

        assert_eq!(reproduction_type, expected);
    }

    #[rstest]
    #[case(0.0, 0.0, 1920.0, 1080.0, true)]
    #[case(0.005, -0.008, 1919.991, 1080.0, true)]
    #[case(100.0, 100.0, 100.0, 100.0, false)]
    #[case(1.0, 0.0, 1920.0, 1080.0, false)]
    fn window_is_fullscreen(
        #[case] x: f32,
        #[case] y: f32,
        #[case] width: f32,
        #[case] height: f32,
        #[case] expected: bool,
    ) {
        let window = Window {
            position: Position { x, y },
            dimensions: Dimensions { width, height },
        };

        assert_eq!(window.is_fullscreen(), expected)
    }

    #[test]
    fn test_promo_video_to_playback_src() {
        let video = Video {
            window: get_fullscreen_window(),
            client_id: "foo".to_string(),
            id: "bar".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            placement: "qux".to_string(),
        };

        let playback_src = video.to_playback_src();

        assert_eq!(
            playback_src,
            Some(PlaybackSrc::Promo(PromoSrc {
                placement: PromoPlacement::PVBrowse,
                client_side_metrics: None,
                title_id: "bar".to_string(),
                client_id: Some("foo".to_string())
            }))
        )
    }

    #[test]
    fn test_linear_video_to_playback_src() {
        let video = Video {
            window: get_fullscreen_window(),
            client_id: "foo".to_string(),
            id: "bar".to_string(),
            media_strategy: MediaStrategy::Linear,
            csm_data: None,
            placement: "qux".to_string(),
        };

        let playback_src = video.to_playback_src();

        assert_eq!(
            playback_src,
            Some(PlaybackSrc::Linear(LinearSrc {
                title_id: "bar".to_string(),
                start_mode: LinearStartMode::AtLive,
                client_id: Some("foo".to_string())
            }))
        )
    }

    #[test]
    fn test_live_event_video_to_playback_src() {
        let video = Video {
            window: get_fullscreen_window(),
            client_id: "foo".to_string(),
            id: "bar".to_string(),
            media_strategy: MediaStrategy::Live,
            csm_data: None,
            placement: "qux".to_string(),
        };

        let playback_src = video.to_playback_src();

        assert_eq!(
            playback_src,
            Some(PlaybackSrc::LiveEvent(LiveEventSrc {
                title_id: "bar".to_string(),
                playback_envelope: None,
                start_mode: LiveEventStartMode::AtLive,
                client_id: Some("foo".to_string())
            }))
        )
    }

    #[rstest]
    #[case(MediaStrategy::Live, true, false)]
    #[case(MediaStrategy::Live, false, true)]
    #[case(MediaStrategy::Promo, true, true)]
    #[case(MediaStrategy::Linear, true, true)]
    fn test_live_event_autoplay_resiliency_switch(
        #[case] media_strategy: MediaStrategy,
        #[case] disable_live_event_autoplay: bool,
        #[case] expected_result: bool,
    ) {
        launch_only_scope(move |scope| {
            setup_resiliency_store(scope, disable_live_event_autoplay);
            setup_active_profile(scope, true);
            let video = Video {
                window: get_fullscreen_window(),
                client_id: "foo".to_string(),
                id: "bar".to_string(),
                media_strategy,
                csm_data: None,
                placement: "qux".to_string(),
            };
            let resiliency_store = mock_use_resiliency_store(scope);
            let profile_manager = mock_use_profile_manager(scope);
            let resizable_support = Some(ResizeSupport::Static);
            assert_eq!(
                video.should_autoplay(&resiliency_store, &profile_manager, resizable_support),
                expected_result
            )
        });
    }

    #[rstest]
    #[case(MediaStrategy::Live, true, true)]
    #[case(MediaStrategy::Live, false, false)]
    #[case(MediaStrategy::Promo, false, true)]
    #[case(MediaStrategy::Linear, false, true)]
    fn test_live_event_autoplay_with_profile(
        #[case] media_strategy: MediaStrategy,
        #[case] is_adult_profile: bool,
        #[case] expected_result: bool,
    ) {
        launch_only_scope(move |scope| {
            setup_resiliency_store(scope, false);
            setup_active_profile(scope, is_adult_profile);
            let video = Video {
                window: get_fullscreen_window(),
                client_id: "foo".to_string(),
                id: "bar".to_string(),
                media_strategy,
                csm_data: None,
                placement: "qux".to_string(),
            };
            let resiliency_store = mock_use_resiliency_store(scope);
            let profile_manager = mock_use_profile_manager(scope);
            let resizable_support = Some(ResizeSupport::Static);
            assert_eq!(
                video.should_autoplay(&resiliency_store, &profile_manager, resizable_support),
                expected_result
            )
        });
    }

    #[rstest]
    #[case(Some(ResizeSupport::Static), get_fullscreen_window(), true)]
    #[case(Some(ResizeSupport::Static), get_non_reference_viewport(), true)]
    #[case(Some(ResizeSupport::Dynamic), get_fullscreen_window(), true)]
    #[case(Some(ResizeSupport::Dynamic), get_non_reference_viewport(), true)]
    #[case(Some(ResizeSupport::Animated), get_fullscreen_window(), true)]
    #[case(Some(ResizeSupport::Animated), get_non_reference_viewport(), true)]
    #[case(None, get_fullscreen_window(), true)]
    #[case(None, get_non_reference_viewport(), false)]
    fn test_autoplay_with_different_resizable_support(
        #[case] mock_resize_support: Option<ResizeSupport>,
        #[case] viewport: Window,
        #[case] expected_result: bool,
    ) {
        launch_only_scope(move |scope| {
            setup_resiliency_store(scope, false);
            setup_active_profile(scope, true);
            let video = Video {
                window: viewport,
                client_id: "foo".to_string(),
                id: "bar".to_string(),
                media_strategy: MediaStrategy::Promo,
                csm_data: None,
                placement: "qux".to_string(),
            };
            let resiliency_store = mock_use_resiliency_store(scope);
            let profile_manager = mock_use_profile_manager(scope);
            assert_eq!(
                video.should_autoplay(&resiliency_store, &profile_manager, mock_resize_support),
                expected_result
            )
        });
    }

    fn get_fullscreen_window() -> Window {
        Window {
            position: Position { x: 0.0, y: 0.0 },
            dimensions: Dimensions {
                width: 1920.0,
                height: 1080.0,
            },
        }
    }

    fn get_non_reference_viewport() -> Window {
        Window {
            position: Position { x: 10.0, y: 10.0 },
            dimensions: Dimensions {
                width: 1000.0,
                height: 800.0,
            },
        }
    }

    fn setup_resiliency_store(scope: Scope, should_disable_live_event_autoplay: bool) {
        let mut mock_resiliency_store = MockResiliencyStore::default();

        mock_resiliency_store.expect_clone().returning(move || {
            let mut clone = MockResiliencyStore::default();
            clone
                .expect_get_disable_live_event_autoplay()
                .return_const(should_disable_live_event_autoplay);
            clone
        });

        provide_context::<MockResiliencyStore>(scope, mock_resiliency_store);
    }

    fn setup_active_profile(scope: Scope, is_adult: bool) {
        let mut mock_profile_manager = MockProfileManager::default();

        let profile = create_rw_signal(
            scope,
            Some(Profile {
                id: "id".to_string(),
                avatar: ProfileAvatar {
                    avatarId: "".to_string(),
                    avatarUrl: "".to_string(),
                    avatarDescription: None,
                },
                name: "".to_string(),
                isActive: true,
                isAdult: is_adult,
                profileIsImplicit: true,
                translationDetails: None,
                permissions: None,
            }),
        );

        mock_profile_manager.expect_clone().returning(move || {
            let mut mock_profile_manager_clone = MockProfileManager::default();
            mock_profile_manager_clone
                .expect_get_active_profile()
                .returning(move || profile.into());
            mock_profile_manager_clone
        });

        provide_context(scope, mock_profile_manager);
    }
}
