use crate::components::overlay::*;
use crate::controllers::transition_controller::{transition_controller, Options};
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::{FullscreenBackgroundData, Video};
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::context::AppContext;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::{ImageData, ImageFormat, ImageTag, ScalingStrategy};
fn debounce_comparator(_prev: &FullscreenBackgroundData, _next: &FullscreenBackgroundData) -> u64 {
    500
}

const STANDARD_HERO_VIGNETTE_URL_LTR: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_standardhero_v1.png";
const STANDARD_HERO_VIGNETTE_URL_RTL: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_standardhero_v1_rtl.png";

#[Composer]
pub fn FullScreenBackground(
    ctx: &AppContext,
    data: ReadSignal<FullscreenBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    set_initial_ready: WriteSignal<bool>,
) -> StackComposable {
    playing_video.set(None);

    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());
    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (vignettes_ready, set_vignettes_ready) = create_signal(ctx.scope(), false);

    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get() && vignettes_ready.get()
    });

    create_effect(ctx.scope(), move |prev: Option<String>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0f32);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    let image_data = Signal::derive(ctx.scope(), move || ImageData {
        url: rendered_data.get().image_url,
        height: PAGE_DIMENSIONS.height,
        width: PAGE_DIMENSIONS.width,
        tags: vec![ImageTag::Format(ImageFormat::JPG), ImageTag::DB],
    });

    let image_opacity = Signal::derive(ctx.scope(), move || {
        data.get().image_opacity_percentage as f32 / 100.0
    });

    compose! {
        Stack() {
            Stack() {
                LRCImage(data: image_data)
                    .on_image_result(move |result| set_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
                    .opacity(image_opacity)
                    .test_id("fullscreen_background_image")

                LRCImage(data: ImageData {
                    url: if rtl_enabled { STANDARD_HERO_VIGNETTE_URL_RTL.to_string() } else { STANDARD_HERO_VIGNETTE_URL_LTR.to_string() },
                    height: PAGE_DIMENSIONS.height,
                    width: PAGE_DIMENSIONS.width,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(PAGE_DIMENSIONS.width * 0.25, PAGE_DIMENSIONS.height * 0.25)),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .on_image_result(move |result| set_vignettes_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
            }
            .height(PAGE_DIMENSIONS.height)
            .width(PAGE_DIMENSIONS.width)

            Overlay(size: PAGE_DIMENSIONS, opacity: overlay_opacity)
        }
        .height(SCREEN_HEIGHT)
        .width(SCREEN_WIDTH)
    }
}

#[cfg(test)]
mod tests {
    use crate::types::{MediaStrategy, Window};

    use super::*;
    use ignx_compositron::{app::launch_test, test_utils::assert_node_exists};

    #[test]
    fn it_renders() {
        launch_test(
            |ctx| {
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    FullscreenBackgroundData {
                        image_url: "image_url".to_string(),
                        id: "foo".to_string(),
                        enter_immediately: true,
                        image_opacity_percentage: 60,
                    },
                );
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);

                compose! {
                    FullScreenBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        set_initial_ready,
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let image = tree.find_by_test_id("fullscreen_background_image");

                assert_node_exists!(image);
            },
        )
    }

    #[test]
    fn unloads_video() {
        launch_test(
            |ctx| {
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    FullscreenBackgroundData {
                        image_url: "image_url".to_string(),
                        id: "foo".to_string(),
                        enter_immediately: true,
                        image_opacity_percentage: 60,
                    },
                );
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(
                    ctx.scope(),
                    Some(Video {
                        id: "foo".to_string(),
                        window: Window::fullscreen(),
                        csm_data: None,
                        client_id: "foo".to_string(),
                        placement: "foo".to_string(),
                        media_strategy: MediaStrategy::Promo,
                    }),
                );
                provide_context(ctx.scope(), playing_video);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);

                compose! {
                    FullScreenBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        set_initial_ready,
                    )
                }
            },
            move |scope, _| {
                let video = expect_context::<RwSignal<Option<Video>>>(scope);
                assert!(video.get_untracked().is_none());
            },
        )
    }
}
