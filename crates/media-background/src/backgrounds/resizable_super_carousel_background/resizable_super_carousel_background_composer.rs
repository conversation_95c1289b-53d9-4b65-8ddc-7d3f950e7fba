use crate::backgrounds::resizable_background_shared_components::resizable_background_composer::*;
use crate::backgrounds::super_carousel::super_carousel_background::{
    VIDEO_WINDOW_WITHOUT_TITLE, VIDEO_WINDOW_WITH_TITLE,
};
use crate::components::overlay::*;
use crate::controllers::transition_controller::*;
use crate::controllers::video_controller::*;
use crate::types::{
    PlayerStatus, ResizableSuperCarouselBackgroundData, TrailerPlaybackContext, Video, Window,
};
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::{ImageComposable, ImageLoadResultEvent};
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::LRCImage;
use lrc_image::lrc_image::LRCImageProps;
use lrc_image::types::*;
use lrc_image::LRCImageCaller;

const SUPER_CAROUSEL_CORNER_IMAGE_URL_PREFIX: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/supercarousel/";
const SUPER_CAROUSEL_CORNER_DIMENSION: f32 = 15.0;

fn get_super_collapsed_window(data: &ResizableSuperCarouselBackgroundData) -> Window {
    match data.has_title {
        true => VIDEO_WINDOW_WITH_TITLE,
        false => VIDEO_WINDOW_WITHOUT_TITLE,
    }
}

#[Composer]
fn SuperCarouselCorner(
    ctx: &AppContext,
    top: bool,
    left: bool,
    on_ready: RwSignal<bool>,
) -> ImageComposable {
    let url = format!(
        "{}{}.{}.png",
        SUPER_CAROUSEL_CORNER_IMAGE_URL_PREFIX,
        if top { "top" } else { "bottom" },
        if left { "left" } else { "right" }
    );

    compose! {
        LRCImage(data: ImageData {
            url,
            height: SUPER_CAROUSEL_CORNER_DIMENSION,
            width: SUPER_CAROUSEL_CORNER_DIMENSION,
            tags: vec![ImageTag::Format(ImageFormat::PNG)]
        })
        .on_image_result(move |result| on_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
    }
}

fn debounce_comparator(
    _: &ResizableSuperCarouselBackgroundData,
    _: &ResizableSuperCarouselBackgroundData,
) -> u64 {
    300
}
#[Composer]
pub fn ResizableSuperCarouselBackground(
    ctx: &AppContext,
    data: ReadSignal<ResizableSuperCarouselBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };

    let window = data.with_untracked(|data| get_super_collapsed_window(data));

    let video_options: VideoControllerOptions = VideoControllerOptions {
        window: window.clone(),
        window_rtl: None,
        client_id: PlayerClientID::SuperDraperOnBrowsePage,
        delay: Some(Duration::from_millis(0)),
        delay_only_after_image_loads: false,
    };

    let Window {
        dimensions: collapsed_window_dimensions,
        position: collapsed_window_position,
    } = window;

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());

    let top_left_corner_ready = create_rw_signal(ctx.scope(), false);
    let top_right_corner_ready = create_rw_signal(ctx.scope(), false);
    let bottom_left_corner_ready = create_rw_signal(ctx.scope(), false);
    let bottom_right_corner_ready = create_rw_signal(ctx.scope(), false);

    let resized_image_url = Signal::derive(ctx.scope(), move || {
        rendered_data.with(|rendered_data| rendered_data.image_url.as_ref().cloned())
    });
    let (resized_image_ready, set_resized_image_ready) = create_signal(ctx.scope(), false);
    let (resized_image_opacity, set_image_opacity) = create_signal(ctx.scope(), 1.0f32);

    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0f32);

    let (vignette_ready, set_vignette_ready) = create_signal(ctx.scope(), false);
    let (vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 1.0f32);

    let ready = Signal::derive(ctx.scope(), move || {
        [
            top_left_corner_ready,
            top_right_corner_ready,
            bottom_left_corner_ready,
            bottom_right_corner_ready,
        ]
        .iter()
        .all(|c| c.get())
            && vignette_ready.get()
            && resized_image_ready.get()
    });

    create_effect(ctx.scope(), move |prev: Option<Option<String>>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_resized_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready,
        playing_video,
        player_status,
        set_image_opacity,
        set_window_opacity,
        set_vignette_opacity,
        video_options,
    );

    // set trailer playback started/ended signals
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            if player_status.get() == PlayerStatus::PLAYING {
                if let Some(playback_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                    playback_context.on_trailer_playback_start.set(true);
                    playback_context.on_playback_finished.set(false);
                    log::debug!("[ResizableSuperCarousel] Super Draper Playback began");
                }
            } else if player_status.get() == PlayerStatus::STOPPED {
                if let Some(playback_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                    if playback_context.on_trailer_playback_start.get_untracked() {
                        log::debug!("[ResizableSuperCarousel] Super Draper Playback ended");
                        playback_context.on_playback_finished.set(true);
                    }
                    playback_context.on_trailer_playback_start.set(false);
                }
            }
        }
    });

    let resized_background_opacity = Signal::derive(ctx.scope(), move || {
        if rendered_data.get().resized
            && playing_video
                .get()
                .is_none_or(|v| v.window == RESIZED_WINDOW)
        {
            1.0
        } else {
            0.0
        }
    });

    let collapsed_background_opacity = Signal::derive(ctx.scope(), move || {
        if !rendered_data.get().resized && playing_video.get().is_none_or(|v| v.window == window) {
            1.0
        } else {
            0.0
        }
    });

    compose! {
        Stack() {
            ResizableBackground(
                image_url: resized_image_url,
                image_opacity: resized_image_opacity,
                on_image_ready: set_resized_image_ready,
                on_vignette_ready: set_vignette_ready,
                rtl_enabled,
                vignette_opacity,
                overlay_opacity,
                resized_vignette_opacity: Signal::derive(ctx.scope(), || 1.0)
            )
            .test_id("resized_super_carousel_background")
            .opacity(resized_background_opacity)

            Stack() {
                SuperCarouselCorner(top: true, left: true, on_ready: top_left_corner_ready)

                SuperCarouselCorner(top: false, left: true, on_ready: bottom_left_corner_ready)
                    .translate_y(collapsed_window_dimensions.height - SUPER_CAROUSEL_CORNER_DIMENSION)

                SuperCarouselCorner(top: true, left: false, on_ready: top_right_corner_ready)
                    .translate_x(collapsed_window_dimensions.width - SUPER_CAROUSEL_CORNER_DIMENSION)

                SuperCarouselCorner(top: false, left: false, on_ready: bottom_right_corner_ready)
                    .translate_y(collapsed_window_dimensions.height - SUPER_CAROUSEL_CORNER_DIMENSION)
                    .translate_x(collapsed_window_dimensions.width - SUPER_CAROUSEL_CORNER_DIMENSION)

                LRCImage(data: ImageData {
                    url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/screensaver/Vignette.png".to_string(),
                    height: collapsed_window_dimensions.height,
                    width: collapsed_window_dimensions.width,
                    tags: vec![
                        ImageTag::Format(ImageFormat::PNG),
                        ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(collapsed_window_dimensions.width * 0.25, collapsed_window_dimensions.height * 0.25)),
                    ]
                })
                .on_image_result(move |result| set_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

                Overlay(size: collapsed_window_dimensions, opacity: overlay_opacity)
            }
            .translate_x(collapsed_window_position.x)
            .translate_y(collapsed_window_position.y)
            .test_id("collapsed_super_carousel_background")
            .opacity(collapsed_background_opacity)
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::types::MediaStrategy;
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::{app::launch_test, test_utils::*};

    fn get_mock_resizable_super_data(
        resized: bool,
        image_present: bool,
    ) -> ResizableSuperCarouselBackgroundData {
        ResizableSuperCarouselBackgroundData {
            id: "test".to_string(),
            video_id: Some("video1".to_string()),
            image_url: if image_present {
                Some("background_image_url".to_string())
            } else {
                None
            },
            enter_immediately: false,
            placement: "test".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            has_title: true,
            resized,
        }
    }

    fn setup_test(ctx: AppContext, resized: bool, image_present: bool) -> StackComposable {
        let data = create_signal(
            ctx.scope(),
            get_mock_resizable_super_data(resized, image_present),
        )
        .0;
        let rtl_enabled = false;
        let playing_video = create_rw_signal(ctx.scope(), None);
        let player_status = Signal::derive(ctx.scope(), || PlayerStatus::STOPPED);
        let set_initial_ready = create_signal(ctx.scope(), false).1;
        let set_window_opacity = create_signal(ctx.scope(), 1.0).1;

        compose! {
            Stack() {
                ResizableSuperCarouselBackground(
                    data,
                    rtl_enabled,
                    playing_video,
                    player_status,
                    set_initial_ready,
                    set_window_opacity,
                )
            }
        }
    }

    fn assert_background(tree: &SceneNodeTree, collapsed: bool, image_present: bool) {
        let collapsed_background = tree.find_by_test_id("collapsed_super_carousel_background");
        assert_node_exists!(&collapsed_background);
        let collapsed_opacity = collapsed_background.get_props().base_styles.opacity;
        assert_eq!(
            collapsed_opacity.unwrap(),
            if collapsed { 1.0 } else { 0.0 }
        );
        let resized_background = tree.find_by_test_id("resized_super_carousel_background");
        assert_node_exists!(&resized_background);
        let resized_opacity = resized_background.borrow_props().base_styles.opacity;
        assert_eq!(resized_opacity.unwrap(), if collapsed { 0.0 } else { 1.0 });
        if !collapsed && image_present {
            let image = resized_background
                .find_any_child_with()
                .test_id("resizable_background_image")
                .find_first();
            if image_present {
                assert_node_exists!(image);
            } else {
                assert_node_does_not_exist!(&image);
            }
        }
    }

    #[test]
    fn renders_collapsed_background() {
        launch_test(
            move |ctx| setup_test(ctx, false, true),
            move |_scope, mut test_loop| {
                assert_background(&test_loop.tick_until_done(), true, false)
            },
        )
    }

    #[test]
    fn renders_resized_background_image_present() {
        launch_test(
            move |ctx| setup_test(ctx, true, true),
            move |_scope, mut test_loop| {
                assert_background(&test_loop.tick_until_done(), false, true)
            },
        )
    }

    #[test]
    fn renders_resized_background_image_absent() {
        launch_test(
            move |ctx| setup_test(ctx, true, false),
            move |_scope, mut test_loop| {
                assert_background(&test_loop.tick_until_done(), false, false)
            },
        )
    }
}
