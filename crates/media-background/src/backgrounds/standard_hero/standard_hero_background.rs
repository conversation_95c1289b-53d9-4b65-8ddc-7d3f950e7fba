use std::time::Duration;

use crate::components::overlay::*;
use crate::controllers::transition_controller::*;
use crate::controllers::video_controller::*;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::context::{
    BackgroundState, MediaBackgroundStatus, MediaBackgroundStatusContextInternal,
};
use crate::types::TrailerPlaybackContext;
use crate::types::{
    Dimensions, PlayerStatus, Position, RotationDirection, StandardHeroBackgroundData,
    Transitionable, Video, Window,
};
use amzn_fable_tokens::*;
use fableous::animations::{FableMotionDuration, FableMotionEasing, MotionDuration, MotionEasing};
use fableous::utils::get_ignx_color;
use ignx_compositron::animation::Animation;
use ignx_compositron::composable::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::*;
use ignx_compositron::{compose, compose_option, Composer};
use log::error;
use lrc_image::lrc_image::*;
use lrc_image::types::{
    CropDimensions, CroppingStrategy, ImageData, ImageFormat, ImageTag, ScalingStrategy,
};

const IMAGE_DIMENSIONS: Dimensions = Dimensions {
    height: 720.0,
    width: PAGE_DIMENSIONS.width,
};

const IMAGE_DIMENSIONS_EXPANDED: Dimensions = Dimensions {
    height: IMAGE_DIMENSIONS.height * 1.15,
    width: IMAGE_DIMENSIONS.width * 1.15,
};

const VIDEO_DIMENSIONS: Dimensions = Dimensions {
    height: PAGE_DIMENSIONS.height,
    width: PAGE_DIMENSIONS.width,
};

const VIDEO_WINDOW: Window = Window {
    dimensions: VIDEO_DIMENSIONS,
    position: Position { x: 0.0, y: 0.0 },
};

const BOTTOM_RECTANGLE_HEIGHT: f32 = 160.0;
const RTL_IMAGE_OFFSET: f32 = 768.0;

const ROTATION_OFFSET: f32 = 15.0;

fn debounce_comparator(
    _prev: &StandardHeroBackgroundData,
    _next: &StandardHeroBackgroundData,
) -> u64 {
    0
}

fn get_rotation_offset(direction: RotationDirection) -> f32 {
    if direction == RotationDirection::LEFT {
        -ROTATION_OFFSET
    } else {
        ROTATION_OFFSET
    }
}

const VIGNETTE_LTR: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/hero/hero_scrim_collapsed_ltr.png";
const VIGNETTE_RTL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/hero/hero_scrim_collapsed_rtl.png";

const EXTRA_RIGHT_VIGNETTE_FOR_RTL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/hero/hero_scrim_right_rtl.png";

#[Composer]
pub fn StandardHeroBackground(
    ctx: &AppContext,
    data: ReadSignal<StandardHeroBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };

    let video_options: VideoControllerOptions = VideoControllerOptions {
        window: VIDEO_WINDOW,
        window_rtl: None,
        client_id: PlayerClientID::SuperDraperHeroPlacements,
        delay: Some(Duration::from_millis(0)),
        delay_only_after_image_loads: true,
    };

    let playing_sh_video = create_rw_signal(ctx.scope(), None::<Video>);
    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());

    let image_width_initial_value = match rendered_data.try_get_untracked() {
        None => {
            error!("[standard_hero_background] failed obtaining rendered data for image width, scope disposed.");
            0.0
        }
        Some(val) => {
            let expand_data = val.is_expanded.try_get_untracked();
            match expand_data {
                None => 0.0,
                Some(is_expanded) => {
                    if is_expanded && !rtl_enabled {
                        IMAGE_DIMENSIONS_EXPANDED.width
                    } else {
                        IMAGE_DIMENSIONS.width
                    }
                }
            }
        }
    };
    let image_width = create_rw_signal(ctx.scope(), image_width_initial_value);

    let image_height = create_rw_signal(ctx.scope(), {
        if rendered_data.get_untracked().is_expanded.get_untracked() && !rtl_enabled {
            IMAGE_DIMENSIONS_EXPANDED.height
        } else {
            IMAGE_DIMENSIONS.height
        }
    });

    let title_image_translate_x = create_rw_signal(
        ctx.scope(),
        if rendered_data.get_untracked().is_expanded.get_untracked() && !rtl_enabled {
            PAGE_DIMENSIONS.width - IMAGE_DIMENSIONS_EXPANDED.width
        } else {
            0.0
        },
    );

    let calc_title_image_expansion = move |is_expanded: bool| {
        if is_expanded && !rtl_enabled {
            image_width.set(IMAGE_DIMENSIONS_EXPANDED.width);
            image_height.set(IMAGE_DIMENSIONS_EXPANDED.height);
            title_image_translate_x.set(PAGE_DIMENSIONS.width - IMAGE_DIMENSIONS_EXPANDED.width);
        } else {
            image_width.set(IMAGE_DIMENSIONS.width);
            image_height.set(IMAGE_DIMENSIONS.height);
            title_image_translate_x.set(0.0);
        }
    };

    on_cleanup(ctx.scope(), {
        let ctx = ctx.clone();
        move || {
            if let Some(trailer_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                trailer_context.reset();
            }
        }
    });

    // set trailer playback started/ended signals for autorotation
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            if player_status.get() == PlayerStatus::PLAYING {
                if let Some(hero_trailer_playback_events) =
                    use_context::<TrailerPlaybackContext>(ctx.scope())
                {
                    if hero_trailer_playback_events
                        .should_play_hero
                        .get_untracked()
                    {
                        hero_trailer_playback_events
                            .on_trailer_playback_start
                            .set(true);
                        log::debug!("[AutoRotation] Super Draper Playback began");
                    }
                    hero_trailer_playback_events.on_playback_finished.set(false);
                }
            } else if player_status.get() == PlayerStatus::STOPPED {
                if let Some(hero_trailer_playback_events) =
                    use_context::<TrailerPlaybackContext>(ctx.scope())
                {
                    if hero_trailer_playback_events
                        .on_trailer_playback_start
                        .get_untracked()
                        && hero_trailer_playback_events
                            .should_play_hero
                            .get_untracked()
                    {
                        log::debug!("[AutoRotation] Super Draper playback ended");

                        hero_trailer_playback_events.on_playback_finished.set(true);
                    }
                    hero_trailer_playback_events
                        .on_trailer_playback_start
                        .set(false);
                }
            }
        }
    });

    // set video when hero is expanded
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            if let Some(hero_trailer_playback_events) =
                use_context::<TrailerPlaybackContext>(ctx.scope())
            {
                let should_play_hero = hero_trailer_playback_events.should_play_hero.get();
                if should_play_hero {
                    playing_video.set(playing_sh_video.get());
                } else {
                    playing_video.set(None);
                }
            } else {
                playing_video.set(None);
            }
        }
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();

        move |prev| {
            let Some(is_expanded) = rendered_data.with(|data| data.is_expanded.try_get()) else {
                return false;
            };

            if let Some(prev) = prev {
                if is_expanded != prev {
                    ctx.with_animation(
                        Animation::default()
                            .with_interpolation(FableMotionEasing::Persistent.to_interpolation())
                            .with_duration(FableMotionDuration::Standard.to_duration()),
                        move || calc_title_image_expansion(is_expanded),
                    );
                } else {
                    calc_title_image_expansion(is_expanded)
                }
            }
            is_expanded
        }
    });

    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (video_vignette_ready, set_video_vignette_ready) = create_signal(ctx.scope(), false);
    let (image_vignette_ready, set_image_vignette_ready) = create_signal(ctx.scope(), false);
    let (rtl_vignette_ready, set_rtl_vignette_ready) = create_signal(ctx.scope(), !rtl_enabled);
    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get()
            && video_vignette_ready.get()
            && image_vignette_ready.get()
            && rtl_vignette_ready.get()
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let Some(ref mb_ready_context) =
                use_context::<MediaBackgroundStatusContextInternal>(ctx.scope())
            else {
                return;
            };
            let background_state = if ready.get() {
                BackgroundState::Downloaded
            } else {
                BackgroundState::Pending
            };
            let id = rendered_data.with_untracked(|data| data.id.clone());
            mb_ready_context
                .background_state
                .set(Some(MediaBackgroundStatus {
                    background_state,
                    id,
                }));
        }
    });

    create_effect(ctx.scope(), move |prev: Option<Option<String>>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    let DEFAULT_IMAGE_OFFSET = if rtl_enabled { -RTL_IMAGE_OFFSET } else { 0.0 };

    let (image_opacity, set_image_opacity) = create_signal(ctx.scope(), 1.0);
    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0);
    let (image_translate_x, set_image_translate_x) =
        create_signal(ctx.scope(), DEFAULT_IMAGE_OFFSET);
    let (_vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 0.0);

    // Handles moving the background horizontally when rotated.
    // TODO: Pass in the rotation data and fix this section to animate correctly: https://i.amazon.com/LR-11086
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();

        move |prev_data: Option<StandardHeroBackgroundData>| {
            let new_data = rendered_data.get();

            if let Some(prev_data) = prev_data {
                if !prev_data.should_transition(&new_data) {
                    return new_data;
                }

                if new_data.rotation_direction != RotationDirection::NONE {
                    set_image_translate_x.set(
                        get_rotation_offset(new_data.rotation_direction) + DEFAULT_IMAGE_OFFSET,
                    );

                    ctx.with_animation(
                        Animation::default()
                            .with_interpolation(FableMotionEasing::Enter.to_interpolation())
                            .with_duration(FableMotionDuration::Fast.to_duration()),
                        move || set_image_translate_x.set(DEFAULT_IMAGE_OFFSET),
                    );
                }
            }

            new_data
        }
    });

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    let image_data = Signal::derive(ctx.scope(), move || {
        rendered_data.with(|rendered_data| {
            rendered_data.image_url.as_ref().map(|image_url| ImageData {
                url: image_url.clone(),
                height: IMAGE_DIMENSIONS.height,
                width: IMAGE_DIMENSIONS.width,
                // These tags fit the image to the ideal 8:3 image spec for hero
                // 8:3 images are unaffected, and other sized images (16:9 liner hero image) are made to fit
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::ScaleToWidth),
                    ImageTag::Cropping(CroppingStrategy::Crop(CropDimensions {
                        x: 0,
                        y: 0,
                        height: IMAGE_DIMENSIONS.height as i32,
                        width: IMAGE_DIMENSIONS.width as i32,
                    })),
                ],
            })
        })
    });

    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready,
        playing_sh_video,
        player_status,
        set_image_opacity,
        set_window_opacity,
        set_vignette_opacity,
        video_options,
    );

    compose! {
        Stack() {
            // Vignette for the video.
            LRCImage(data: ImageData {
                url: if rtl_enabled { VIGNETTE_RTL.to_string() } else { VIGNETTE_LTR.to_string() },
                height: (VIDEO_DIMENSIONS.height - BOTTOM_RECTANGLE_HEIGHT),
                width: IMAGE_DIMENSIONS.width,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(IMAGE_DIMENSIONS.width * 0.25, IMAGE_DIMENSIONS.height * 0.25)),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .on_image_result(move |result| set_video_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

            // Covers the the bottom 160px of the video.
            Rectangle()
                .height(BOTTOM_RECTANGLE_HEIGHT)
                .width(VIDEO_DIMENSIONS.width)
                .translate_y(VIDEO_DIMENSIONS.height - BOTTOM_RECTANGLE_HEIGHT)
                .background_color(get_ignx_color(FableColor::COOL900))

            // Image layer. Sits on top of the video and all fades out to reveal the video.
            Stack() {
                Stack() {
                    Memo(item_builder: Box::new(move |ctx| {
                        image_data.with(|image_data| {
                            if let Some(image_data) = image_data {
                                compose_option! {
                                    // The title image.
                                    LRCImage(data: image_data.clone(), dimensions_type: DimensionsType::CUSTOM)
                                        .on_image_result(move |result| set_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
                                        .height(image_height)
                                        .width(image_width)
                                        .translate_x(title_image_translate_x).test_id("standard_hero_background_image")
                                }
                            } else {
                                set_image_ready.set(true);
                                None
                            }
                        })
                    }))

                    if rtl_enabled {
                        // If rtl is enabled, an extra vignette is shown on the right hand side of the image.
                        LRCImage(data: ImageData {
                            url: EXTRA_RIGHT_VIGNETTE_FOR_RTL.to_string(),
                            height: 722.0,
                            width: 326.0,
                            tags: vec![ImageTag::Format(ImageFormat::PNG)]
                        })
                        .translate_x(IMAGE_DIMENSIONS.width - 326.0)
                        .on_image_result(move |result| set_rtl_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
                    }
                }
                .translate_x(image_translate_x)
                .width(image_width)
                .height(image_height)

                // Vignette for the image. Does not shift left with it.
                LRCImage(data: ImageData {
                    url: if rtl_enabled { VIGNETTE_RTL.to_string() } else { VIGNETTE_LTR.to_string() },
                    height: IMAGE_DIMENSIONS.height,
                    width: IMAGE_DIMENSIONS.width,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(IMAGE_DIMENSIONS.width * 0.25, IMAGE_DIMENSIONS.height * 0.25)),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .on_image_result(move |result| set_image_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
                .height(image_height)

                // Covers the space where the video shows.
                Rectangle()
                    .width(IMAGE_DIMENSIONS.width)
                    .height(VIDEO_DIMENSIONS.height - BOTTOM_RECTANGLE_HEIGHT)
                    .translate_y(image_height)
                    .background_color(get_ignx_color(FableColor::COOL900))
            }
            .height(VIDEO_DIMENSIONS.height - BOTTOM_RECTANGLE_HEIGHT)
            .width(VIDEO_DIMENSIONS.height)
            .opacity(image_opacity)

            // Covers everything, used when transitioning between items.
            Overlay(size: VIDEO_DIMENSIONS, opacity: overlay_opacity)
        }
        .height(VIDEO_DIMENSIONS.height)
        .width(VIDEO_DIMENSIONS.width)
    }
}

#[cfg(test)]
mod test {
    use crate::types::{MediaStrategy, TrailerPlaybackContext};

    use super::*;
    use ignx_compositron::{app::launch_test, test_utils::*};
    use rstest::*;

    fn create_mock_standard_hero_background(
        is_expanded: RwSignal<bool>,
        has_image_url: bool,
    ) -> StandardHeroBackgroundData {
        StandardHeroBackgroundData {
            id: "background_id".to_string(),
            image_url: if has_image_url {
                Some("background_image_url".to_string())
            } else {
                None
            },
            video_id: None,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            rotation_direction: RotationDirection::NONE,
            is_expanded,
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
        }
    }

    #[rstest]
    #[case(true)]
    #[case(false)]
    fn it_renders(#[case] has_image_url: bool) {
        launch_test(
            move |ctx| {
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, _set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::STOPPED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                let is_expanded = create_rw_signal(ctx.scope(), false);
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    create_mock_standard_hero_background(is_expanded, has_image_url),
                );

                compose! {
                    StandardHeroBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let image = tree.find_by_test_id("standard_hero_background_image");

                if has_image_url {
                    assert_node_exists!(&image);
                } else {
                    assert_node_does_not_exist!(&image);
                }
            },
        )
    }

    #[test]
    fn should_send_event_when_trailer_started() {
        launch_test(
            |ctx| {
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::PAUSED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                let is_expanded = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), set_player_status);
                let hero_trailer_playback_events = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_playback_events);
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    create_mock_standard_hero_background(is_expanded, true),
                );

                compose! {
                    StandardHeroBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |scope, mut test_loop| {
                let set_player_status = use_context::<WriteSignal<PlayerStatus>>(scope).unwrap();
                let hero_trailer_playback_events =
                    use_context::<TrailerPlaybackContext>(scope).unwrap();
                hero_trailer_playback_events.reset();
                hero_trailer_playback_events.should_play_hero.set(true);
                set_player_status.set(PlayerStatus::PLAYING);
                test_loop.tick_until_done();

                assert!(!hero_trailer_playback_events
                    .on_playback_finished
                    .get_untracked());

                assert!(hero_trailer_playback_events
                    .on_trailer_playback_start
                    .get_untracked());
            },
        );
    }

    #[test]
    fn should_send_event_when_trailer_ended() {
        launch_test(
            |ctx| {
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::PLAYING);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                let is_expanded = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), set_player_status);
                let hero_trailer_playback_events = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_playback_events);
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    create_mock_standard_hero_background(is_expanded, true),
                );

                compose! {
                    StandardHeroBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |scope, mut test_loop| {
                let set_player_status = use_context::<WriteSignal<PlayerStatus>>(scope).unwrap();
                let hero_trailer_playback_events =
                    use_context::<TrailerPlaybackContext>(scope).unwrap();
                hero_trailer_playback_events.reset();
                hero_trailer_playback_events
                    .on_trailer_playback_start
                    .set(true);
                hero_trailer_playback_events.should_play_hero.set(true);
                set_player_status.set(PlayerStatus::STOPPED);
                test_loop.tick_until_done();
                assert!(!hero_trailer_playback_events
                    .on_trailer_playback_start
                    .get_untracked());

                assert!(hero_trailer_playback_events
                    .on_playback_finished
                    .get_untracked());
            },
        );
    }

    #[test]
    fn should_not_send_event_when_trailer_ended_if_hero_should_not_play() {
        launch_test(
            |ctx| {
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::PLAYING);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                let is_expanded = create_rw_signal(ctx.scope(), false);
                provide_context(ctx.scope(), set_player_status);
                let hero_trailer_playback_events = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hero_trailer_playback_events);
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    create_mock_standard_hero_background(is_expanded, true),
                );

                compose! {
                    StandardHeroBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |scope, mut test_loop| {
                let set_player_status = use_context::<WriteSignal<PlayerStatus>>(scope).unwrap();
                let hero_trailer_playback_events =
                    use_context::<TrailerPlaybackContext>(scope).unwrap();
                hero_trailer_playback_events.reset();
                hero_trailer_playback_events
                    .on_trailer_playback_start
                    .set(true);
                hero_trailer_playback_events.should_play_hero.set(false);
                set_player_status.set(PlayerStatus::STOPPED);
                test_loop.tick_until_done();
                assert!(!hero_trailer_playback_events
                    .on_trailer_playback_start
                    .get_untracked());

                assert!(!hero_trailer_playback_events
                    .on_playback_finished
                    .get_untracked());
            },
        );
    }

    #[test]
    #[ignore]
    fn should_set_media_background_status_on_image_result() {
        todo!(
            "Blocked until we can mock on_image_result https://issues.amazon.com/issues/LRCP-4531"
        )
        // When unblocked, we should test that the media background status is set correctly with the correct id
    }
}
