#![allow(unused)] // TODO when video integration is complete

use std::time::Duration;

use crate::controllers::transition_controller::*;
use crate::controllers::video_controller::*;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::context::{
    BackgroundState, MediaBackgroundStatus, MediaBackgroundStatusContextInternal,
};
use crate::types::SpecialCollectionsBackgroundData;
use crate::types::TrailerPlaybackContext;
use crate::types::{PlayerStatus, Position, Video, Window};
use ignx_compositron::composable::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::*;
use ignx_compositron::{compose, compose_option, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;
use lrc_image::types::ScalingStrategy;

const VIDEO_WINDOW: Window = Window {
    dimensions: PAGE_DIMENSIONS,
    position: Position { x: 0.0, y: 0.0 },
};

fn debounce_comparator(
    _prev: &SpecialCollectionsBackgroundData,
    _next: &SpecialCollectionsBackgroundData,
) -> u64 {
    0
}

const VIGNETTE: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/special-collection/special-collection-scrim.png";

#[Composer]
pub fn SpecialCollectionsBackground(
    ctx: &AppContext,
    data: ReadSignal<SpecialCollectionsBackgroundData>,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };

    let video_options: VideoControllerOptions = VideoControllerOptions {
        window: VIDEO_WINDOW,
        window_rtl: None,
        client_id: PlayerClientID::SuperDraperHeroPlacements,
        delay: Some(Duration::from_millis(0)),
        delay_only_after_image_loads: true,
    };

    let playing_sh_video = create_rw_signal(ctx.scope(), None::<Video>);
    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());

    on_cleanup(ctx.scope(), {
        let ctx = ctx.clone();
        move || {
            if let Some(trailer_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                trailer_context.reset();
            }
        }
    });

    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (vignette_ready, set_vignette_ready) = create_signal(ctx.scope(), false);
    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get() && vignette_ready.get()
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let Some(ref mb_ready_context) =
                use_context::<MediaBackgroundStatusContextInternal>(ctx.scope())
            else {
                return;
            };
            let background_state = if ready.try_get().is_some_and(|ready| ready) {
                BackgroundState::Downloaded
            } else {
                BackgroundState::Pending
            };
            let id = rendered_data.with_untracked(|data| data.id.clone());
            mb_ready_context
                .background_state
                .try_set(Some(MediaBackgroundStatus {
                    background_state,
                    id,
                }));
        }
    });

    create_effect(ctx.scope(), move |prev: Option<Option<String>>| {
        rendered_data
            .try_with(|data| {
                if let Some(prev) = prev {
                    if data.image_url != prev {
                        set_image_ready.try_set(false);
                    }
                }

                data.image_url.clone()
            })
            .flatten()
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.try_get().is_some_and(|ready| ready) {
            set_initial_ready.try_set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    let (image_opacity, set_image_opacity) = create_signal(ctx.scope(), 0.0);
    let (_overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0);
    let (_vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 0.0);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    let image_data = Signal::derive(ctx.scope(), move || {
        rendered_data.with(|rendered_data| {
            rendered_data.image_url.as_ref().map(|image_url| ImageData {
                url: image_url.clone(),
                height: PAGE_DIMENSIONS.height,
                width: PAGE_DIMENSIONS.width,
                tags: vec![],
            })
        })
    });

    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready,
        playing_sh_video,
        player_status,
        set_image_opacity,
        set_window_opacity,
        set_vignette_opacity,
        video_options,
    );

    compose! {
        Stack() {
            // Image layer. Sits on top of the video and all fades out to reveal the video.
            Stack() {
                Stack() {
                    Memo(item_builder: Box::new(move |ctx| {
                        image_data.with(|image_data| {
                            if let Some(image_data) = image_data {
                                compose_option! {
                                    // The title image.
                                    LRCImage(data: image_data.clone(), dimensions_type: DimensionsType::EXACT)
                                        .on_image_result(move |result| {
                                            set_image_ready.try_set(matches!(&result, ImageLoadResultEvent::Success));
                                        })
                                        .test_id("standard_hero_background_image")
                                }
                            } else {
                                set_image_ready.set(true);
                                None
                            }
                        })
                    }))
                }
                .width(PAGE_DIMENSIONS.width)
                .height(PAGE_DIMENSIONS.height)

                // Vignette
                LRCImage(data: ImageData {
                    url: VIGNETTE.to_string(),
                    height: PAGE_DIMENSIONS.height,
                    width: PAGE_DIMENSIONS.width,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(PAGE_DIMENSIONS.width * 0.25, PAGE_DIMENSIONS.height * 0.25)),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .on_image_result(move |result| {
                    set_vignette_ready.try_set(matches!(&result, ImageLoadResultEvent::Success));
                })

            }
            .height(PAGE_DIMENSIONS.height)
            .width(PAGE_DIMENSIONS.width)
            .opacity(image_opacity)
        }
        .height(PAGE_DIMENSIONS.height)
        .width(PAGE_DIMENSIONS.width)
    }
}
