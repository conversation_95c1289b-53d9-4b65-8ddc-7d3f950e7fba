use crate::types::Dimensions;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;
use lrc_image::types::ScalingStrategy;

const VIGNETTE_URL_LTR: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v1.png";
const VIGNETTE_URL_RTL: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v1_rtl.png";

#[Composer]
pub fn MicroVignette(
    ctx: &AppContext,
    dimensions: Dimensions,
    set_ready: WriteSignal<bool>,
    rtl_enabled: bool,
    vignette_opacity: ReadSignal<f32>,
) -> StackComposable {
    compose! {
        Stack() {
            LRCImage(data: ImageData {
                url: (if rtl_enabled { VIGNETTE_URL_RTL.to_string() } else { VIGNETTE_URL_LTR.to_string() }),
                height: dimensions.height,
                width: dimensions.width,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::ScaleToWidth),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .test_id("vignette_image")
            .opacity(vignette_opacity)
            .on_image_result(move |result| set_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
        }
        .width(dimensions.width)
        .height(dimensions.height)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::test_utils::node_properties::NodeTypeProperties;
    use ignx_compositron::test_utils::*;
    use rstest::rstest;

    #[rstest]
    #[case(false, "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v1._SX200_FMpng_DB,0_.png".into())]
    #[case(true, "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v1_rtl._SX200_FMpng_DB,0_.png".into())]
    fn it_renders(#[case] rtl_enabled: bool, #[case] expected_image: String) {
        launch_test(
            move |ctx| {
                compose! {
                    MicroVignette(
                        dimensions: Dimensions {
                            height: 100.0,
                            width: 200.0,
                        },
                        set_ready: create_rw_signal(ctx.scope(), false).write_only(),
                        rtl_enabled,
                        vignette_opacity: create_rw_signal(ctx.scope(), 0.0).read_only()
                    )
                }
            },
            |_scope, mut test_renderer_game_loop| {
                let tree = test_renderer_game_loop.tick_until_done();
                let image = tree.find_by_test_id("vignette_image");
                assert_node_exists!(&image);

                assert_eq!(&image.borrow_props().layout.size.height, &100.0);
                assert_eq!(&image.borrow_props().layout.size.width, &200.0);

                let NodeTypeProperties::Image(ref i) = &image.borrow_props().node_type_props else {
                    panic!("Expected an image node");
                };

                assert_eq!(i.uri, Some(expected_image))
            },
        );
    }
}
