use crate::backgrounds::resizable_background_shared_components::resizable_background_image::*;
use crate::components::overlay::*;
use crate::safe::ImageLoadResultEvent;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::Dimensions;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;
use lrc_image::types::ScalingStrategy;

const FULL_SCREEN_VIDEO_VIGNETTE_URL_LTR: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_standardhero_v1.png";
const FULL_SCREEN_VIDEO_VIGNETTE_URL_RTL: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_standardhero_v1_rtl.png";
pub const STANDARD_IMAGE_DIMENSIONS: Dimensions = Dimensions {
    height: IMAGE_HEIGHT,
    width: IMAGE_WIDTH,
};

#[Composer]
pub fn ResizableBackground(
    ctx: &AppContext,
    image_url: Signal<Option<String>>,
    image_opacity: ReadSignal<f32>,
    on_image_ready: WriteSignal<bool>,
    on_vignette_ready: WriteSignal<bool>,
    rtl_enabled: bool,
    vignette_opacity: ReadSignal<f32>,
    overlay_opacity: ReadSignal<f32>,
    resized_vignette_opacity: Signal<f32>,
) -> StackComposable {
    let (resized_vignette_ready, set_resized_vignette_ready) = create_signal(ctx.scope(), false);
    let (image_vignette_ready, set_image_vignette_ready) = create_signal(ctx.scope(), false);

    create_effect(ctx.scope(), move |_| {
        if resized_vignette_ready.get() && image_vignette_ready.get() {
            on_vignette_ready.set(true)
        }
    });

    compose! {
        Stack() {
            LRCImage(data: ImageData {
                url: (if rtl_enabled { FULL_SCREEN_VIDEO_VIGNETTE_URL_RTL.to_string() } else { FULL_SCREEN_VIDEO_VIGNETTE_URL_LTR.to_string() }),
                height: PAGE_DIMENSIONS.height,
                width: PAGE_DIMENSIONS.width,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(PAGE_DIMENSIONS.width * 0.25, PAGE_DIMENSIONS.height * 0.25)),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .on_image_result(move |result| set_resized_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
            .opacity(resized_vignette_opacity)
            .test_id("resizable_background_resized_vignette")

            ResizableBackgroundImage(
                image_url,
                image_opacity,
                on_image_ready,
                on_vignette_ready: set_image_vignette_ready,
                rtl_enabled,
                vignette_opacity
            )

            Overlay(size: PAGE_DIMENSIONS, opacity: overlay_opacity)
        }
        .height(PAGE_DIMENSIONS.height)
        .width(PAGE_DIMENSIONS.width)
        .test_id("resizable_background")
    }
}
