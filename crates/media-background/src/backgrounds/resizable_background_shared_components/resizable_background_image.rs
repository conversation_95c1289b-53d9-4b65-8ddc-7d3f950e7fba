use crate::types::common::PAGE_DIMENSIONS;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::memo::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::{compose, compose_option, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;
use lrc_image::types::ScalingStrategy;

const IMAGE_HORIZONTAL_VIGNETTE_HEIGHT: f32 = 530.0;
const IMAGE_VERTICAL_VIGNETTE_WIDTH: f32 = 480.0;
const VERTICAL_VIGNETTE_URL_LTR: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v2_1.png";
const VERTICAL_VIGNETTE_URL_RTL: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground_rtl/vignette_v2_2.png";
const HORIZONTAL_VIGNETTE_URL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v2_2.png";

pub const IMAGE_HEIGHT: f32 = 756.0;
pub const IMAGE_WIDTH: f32 = 1344.0;
pub const RESIZABLE_BACKGROUND_IMAGE_TRANSLATE_X: f32 = PAGE_DIMENSIONS.width - IMAGE_WIDTH;

#[Composer]
fn Vignette(ctx: &AppContext, set_ready: WriteSignal<bool>, rtl_enabled: bool) -> StackComposable {
    let (vertical_image_ready, set_vertical_image_ready) = create_signal(ctx.scope(), false);
    let (horizontal_image_ready, set_horizontal_image_ready) = create_signal(ctx.scope(), false);

    create_effect(ctx.scope(), move |_| {
        if vertical_image_ready.get() && horizontal_image_ready.get() {
            set_ready.set(true);
        }
    });

    compose! {
        Stack() {
            LRCImage(data: ImageData {
                url: (if rtl_enabled { VERTICAL_VIGNETTE_URL_RTL.to_string() } else { VERTICAL_VIGNETTE_URL_LTR.to_string() }),
                height: IMAGE_HEIGHT,
                width: IMAGE_VERTICAL_VIGNETTE_WIDTH,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::ScaleToWidth),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .on_image_result(move |result| set_vertical_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

            LRCImage(data: ImageData {
                url: HORIZONTAL_VIGNETTE_URL.to_string(),
                height: IMAGE_HORIZONTAL_VIGNETTE_HEIGHT,
                width: IMAGE_WIDTH,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::ScaleToHeight),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .translate_y(IMAGE_HEIGHT - IMAGE_HORIZONTAL_VIGNETTE_HEIGHT)
            .on_image_result(move |result| set_horizontal_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
        }
        .width(IMAGE_WIDTH)
        .height(IMAGE_HEIGHT)
    }
}

#[Composer]
pub fn ResizableBackgroundImage(
    ctx: &AppContext,
    image_url: Signal<Option<String>>,
    image_opacity: ReadSignal<f32>,
    on_image_ready: WriteSignal<bool>,
    on_vignette_ready: WriteSignal<bool>,
    rtl_enabled: bool,
    vignette_opacity: ReadSignal<f32>,
) -> StackComposable {
    compose! {
        Stack() {
            Memo(item_builder: Box::new(move |ctx| {
                image_url.with(|image_url| {
                    if let Some(image_url) = image_url {
                        let image_data = ImageData {
                            url: image_url.clone(),
                            height: IMAGE_HEIGHT,
                            width: IMAGE_WIDTH,
                            tags: vec![],
                        };
                        compose_option! {
                            LRCImage(data: image_data)
                                .opacity(image_opacity)
                                .on_image_result(move |result| on_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
                                .test_id("resizable_background_image")
                        }
                    } else {
                        on_image_ready.set(true);
                        None
                    }
                })
            }))

            Vignette(
                set_ready: on_vignette_ready,
                rtl_enabled,
            )
            .test_id("resizable_background_image_vignette")
            .opacity(vignette_opacity)
        }
        .height(IMAGE_HEIGHT)
        .width(IMAGE_WIDTH)
        .translate_x(RESIZABLE_BACKGROUND_IMAGE_TRANSLATE_X)
    }
}
