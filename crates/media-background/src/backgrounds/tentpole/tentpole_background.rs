use crate::components::overlay::*;
use crate::controllers::transition_controller::*;
use crate::controllers::video_controller::*;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::context::{
    BackgroundState, MediaBackgroundStatus, MediaBackgroundStatusContextInternal,
};
use crate::types::{Dimensions, PlayerStatus, Position, TentpoleBackgroundData, Video, Window};
use amzn_fable_tokens::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::prelude::*;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;
use lrc_image::types::ScalingStrategy;

const IMAGE_DIMENSIONS: Dimensions = Dimensions {
    height: PAGE_DIMENSIONS.height,
    width: PAGE_DIMENSIONS.width,
};

const VIDEO_WINDOW: Window = Window {
    dimensions: IMAGE_DIMENSIONS,
    position: Position { x: 0.0, y: 0.0 },
};

const VIGNETTE_LTR: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/hero/hero_scrim_collapsed_ltr.png";
const VIGNETTE_RTL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/hero/hero_scrim_collapsed_ltr.png";

const EXTRA_RIGHT_VIGNETTE_FOR_RTL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/hero/hero_scrim_right_rtl.png";

const BOTTOM_RECTANGLE_HEIGHT: f32 = 160.0;

fn debounce_comparator(_prev: &TentpoleBackgroundData, _next: &TentpoleBackgroundData) -> u64 {
    0
}

#[Composer]
pub fn TentpoleBackground(
    ctx: &AppContext,
    data: ReadSignal<TentpoleBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());

    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (image_vignette_ready, set_image_vignette_ready) = create_signal(ctx.scope(), false);
    let (rtl_vignette_ready, set_rtl_vignette_ready) = create_signal(ctx.scope(), !rtl_enabled);
    let video_options: VideoControllerOptions = VideoControllerOptions {
        window: VIDEO_WINDOW,
        window_rtl: None,
        client_id: PlayerClientID::SuperDraperHeroPlacements,
        delay: Some(Duration::from_millis(3000)),
        delay_only_after_image_loads: true,
    };
    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get() && image_vignette_ready.get() && rtl_vignette_ready.get()
    });
    let (_vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 0.0);

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let Some(ref mb_ready_context) =
                use_context::<MediaBackgroundStatusContextInternal>(ctx.scope())
            else {
                return;
            };
            let background_state = if ready.get() {
                BackgroundState::Downloaded
            } else {
                BackgroundState::Pending
            };
            let id = rendered_data.with_untracked(|data| data.id.clone());

            mb_ready_context
                .background_state
                .set(Some(MediaBackgroundStatus {
                    background_state,
                    id,
                }));
        }
    });

    create_effect(ctx.scope(), move |prev: Option<String>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    let (image_opacity, set_image_opacity) = create_signal(ctx.scope(), 1.0);
    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready,
        playing_video,
        player_status,
        set_image_opacity,
        set_window_opacity,
        set_vignette_opacity,
        video_options,
    );

    let image_data = Signal::derive(ctx.scope(), move || ImageData {
        url: rendered_data.get().image_url,
        height: IMAGE_DIMENSIONS.height,
        width: IMAGE_DIMENSIONS.width,
        tags: vec![],
    });

    compose! {
        Stack() {
            // The main image.
            Stack() {
                LRCImage(data: image_data)
                .on_image_result(move |result| set_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

                if rtl_enabled {
                    // If rtl is enabled, an extra vignette is shown on the right hand side of the image.
                    LRCImage(data: ImageData {
                        url: EXTRA_RIGHT_VIGNETTE_FOR_RTL.to_string(),
                        height: 920.0,
                        width: 326.0,
                        tags: vec![ImageTag::Format(ImageFormat::PNG)]
                    })
                    .translate_x(IMAGE_DIMENSIONS.width - 326.0)
                    .on_image_result(move |result| set_rtl_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
                }
            }
            .width(IMAGE_DIMENSIONS.width)
            .height(IMAGE_DIMENSIONS.height)
            .opacity(image_opacity)
            .translate_x(if rtl_enabled { -768.0 } else { 0.0 })
            .test_id("tentpole_background")

            // Vignette.
            LRCImage(data: ImageData {
                url: if rtl_enabled { VIGNETTE_RTL.to_string() } else { VIGNETTE_LTR.to_string() },
                height: (IMAGE_DIMENSIONS.height - BOTTOM_RECTANGLE_HEIGHT),
                width: IMAGE_DIMENSIONS.width,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(IMAGE_DIMENSIONS.width * 0.25, IMAGE_DIMENSIONS.height * 0.25)),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .on_image_result(move |result| set_image_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

            // Hides the bottom of the video.
            Rectangle()
                .height(BOTTOM_RECTANGLE_HEIGHT)
                .width(IMAGE_DIMENSIONS.width)
                .translate_y(IMAGE_DIMENSIONS.height - BOTTOM_RECTANGLE_HEIGHT)
                .background_color(get_ignx_color(FableColor::COOL900))

            Overlay(size: IMAGE_DIMENSIONS, opacity: overlay_opacity)
        }
        .height(IMAGE_DIMENSIONS.height)
        .width(IMAGE_DIMENSIONS.width)
    }
}

#[cfg(test)]
mod test {
    use crate::types::MediaStrategy;

    use super::*;
    use ignx_compositron::{app::launch_test, test_utils::*};

    #[test]
    fn it_renders() {
        launch_test(
            |ctx| {
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, _set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::STOPPED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    TentpoleBackgroundData {
                        id: "background_id".to_string(),
                        image_url: "background_image_url".to_string(),
                        video_id: None,
                        enter_immediately: true,
                        placement: "PVBrowse".to_string(),
                        media_strategy: MediaStrategy::Promo,
                        csm_data: None,
                    },
                );

                compose! {
                    TentpoleBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let image = tree.find_by_test_id("tentpole_background");
                assert_node_exists!(&image);
            },
        )
    }

    #[test]
    #[ignore]
    fn should_set_media_background_status_on_image_result() {
        todo!(
            "Blocked until we can mock on_image_result https://issues.amazon.com/issues/LRCP-4531"
        )
        // When unblocked, we should test that the media background status is set correctly with the correct id
    }
}
