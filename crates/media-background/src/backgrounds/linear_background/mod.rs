use crate::components::overlay::*;
use crate::controllers::transition_controller::*;
use crate::controllers::video_controller::*;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::context::{
    BackgroundState, MediaBackgroundStatus, MediaBackgroundStatusContextInternal,
};
use crate::types::HybridPlaybackContext;
use crate::types::{
    Dimensions, LinearBackgroundData, LinearVignetteStyle, PlayerStatus, Position, Video, Window,
};
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::{ImageData, ImageFormat, ImageTag, ScalingStrategy};

const IMAGE_DIMENSIONS: Dimensions = Dimensions {
    height: PAGE_DIMENSIONS.height,
    width: PAGE_DIMENSIONS.width,
};

const VIDEO_WINDOW: Window = Window {
    dimensions: IMAGE_DIMENSIONS,
    position: Position { x: 0.0, y: 0.0 },
};

const LIVE_TV_VIGNETTE: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/live_tv_fable_scrim_background.png";
const STATION_DETAILS_VIGNETTE: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/linear-cx/station-details/station_details_scrim_background.png";

fn debounce_comparator(_prev: &LinearBackgroundData, _next: &LinearBackgroundData) -> u64 {
    300
}

#[Composer]
pub fn LinearBackground(
    ctx: &AppContext,
    data: ReadSignal<LinearBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    // TODO: figure out right-to-left design for live tv
    let _ = rtl_enabled;

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());

    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (image_vignette_ready, set_image_vignette_ready) = create_signal(ctx.scope(), false);
    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get() && image_vignette_ready.get()
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let Some(ref mb_ready_context) =
                use_context::<MediaBackgroundStatusContextInternal>(ctx.scope())
            else {
                return;
            };

            let background_state = if ready.get() {
                BackgroundState::Downloaded
            } else {
                BackgroundState::Pending
            };

            let id = rendered_data.with_untracked(|data| data.id.clone());
            mb_ready_context
                .background_state
                .set(Some(MediaBackgroundStatus {
                    background_state,
                    id,
                }));
        }
    });

    create_effect(ctx.scope(), move |prev: Option<String>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), {
        move |prev| {
            if prev == Some(true) {
                return true;
            }
            if ready.get() {
                set_initial_ready.set(true); // only do this the first time.
                true
            } else {
                false
            }
        }
    });

    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        Options {
            debounce: Box::new(debounce_comparator),
        },
    );

    let image_opacity = create_rw_signal(ctx.scope(), 1.0);
    let window_opacity = create_rw_signal(ctx.scope(), 0.0);
    let (_vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 0.0);
    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready,
        playing_video,
        player_status,
        image_opacity.write_only(),
        window_opacity.write_only(),
        set_vignette_opacity,
        VideoControllerOptions {
            window: VIDEO_WINDOW,
            window_rtl: None,
            client_id: PlayerClientID::SuperDraperHeroPlacements,
            delay: Some(Duration::from_millis(0)),
            delay_only_after_image_loads: true,
        },
    );

    // Video controller works correctly for autoplay and changing background.
    // However, after seamless/non-seamless ingress to playback, player status signal from
    // player can be incorrect which causes video controller to incorrectly set image and video opacity.
    // This implementation forces video to be visible when playback ingress is triggered with seamless egress.
    // This ensures Linear pages AlwaysOn works correctly regardless of when player issue is fixed.
    // The only consequence is that to change background after seamless egress from playback,
    // a full unmount and remount of this component is required
    let hybrid_playback_context = use_context::<HybridPlaybackContext>(ctx.scope());
    let force_video_visible = create_rw_signal(ctx.scope(), false);
    create_effect(ctx.scope(), move |_| {
        if let Some(hybrid_playback_context) = hybrid_playback_context {
            hybrid_playback_context.should_release.track();
            if hybrid_playback_context.seamless_egress.get_value() {
                force_video_visible.set(true);
            }
        }
    });

    create_effect(ctx.scope(), move |_| {
        if force_video_visible.get() {
            set_window_opacity.set(1.0);
            return;
        }
        set_window_opacity.set(window_opacity.get());
    });

    let background_image_opacity = Signal::derive(ctx.scope(), move || {
        if force_video_visible.get() {
            0.0
        } else {
            image_opacity.get()
        }
    });

    let background_overlay_opacity = Signal::derive(ctx.scope(), move || {
        if force_video_visible.get() {
            0.0
        } else {
            overlay_opacity.get()
        }
    });

    let image_data = Signal::derive(ctx.scope(), move || ImageData {
        url: rendered_data.get().image_url,
        height: IMAGE_DIMENSIONS.height,
        width: IMAGE_DIMENSIONS.width,
        tags: vec![ImageTag::Format(ImageFormat::JPG), ImageTag::DB],
    });

    let vignette_image_data = Signal::derive(ctx.scope(), move || {
        let image_url = match rendered_data.get().vignette {
            LinearVignetteStyle::LiveTv => LIVE_TV_VIGNETTE,
            LinearVignetteStyle::StationDetails => STATION_DETAILS_VIGNETTE,
        };
        ImageData {
            url: image_url.to_string(),
            height: IMAGE_DIMENSIONS.height,
            width: IMAGE_DIMENSIONS.width,
            tags: vec![
                ImageTag::Format(ImageFormat::PNG),
                ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(
                    IMAGE_DIMENSIONS.width * 0.25,
                    IMAGE_DIMENSIONS.height * 0.25,
                )),
            ],
        }
    });

    compose! {
        Stack() {
            // The main image.
            LRCImage(data: image_data)
            .on_image_result(move |result| set_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
            .width(IMAGE_DIMENSIONS.width)
            .height(IMAGE_DIMENSIONS.height)
            .opacity(background_image_opacity)
            .test_id("linear_background")

            // Vignette.
            LRCImage(data: vignette_image_data)
            .on_image_result(move |result| set_image_vignette_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

            Overlay(size: IMAGE_DIMENSIONS, opacity: background_overlay_opacity).test_id("linear_overlay")
        }
        .height(IMAGE_DIMENSIONS.height)
        .width(IMAGE_DIMENSIONS.width)
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::types::{MediaStrategy, ReleaseState};
    use ignx_compositron::{app::launch_test, test_utils::*};

    #[test]
    fn it_renders() {
        launch_test(
            |ctx| {
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    LinearBackgroundData {
                        id: "background_id".to_string(),
                        image_url: "background_image_url".to_string(),
                        video_id: None,
                        vignette: LinearVignetteStyle::LiveTv,
                        enter_immediately: true,
                        placement: "PVBrowse".to_string(),
                        media_strategy: MediaStrategy::Linear,
                        csm_data: None,
                        interaction_source: None,
                    },
                );
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (player_status, _set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::STOPPED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 1.0);
                compose! {
                    LinearBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let image = tree.find_by_test_id("linear_background");
                assert_node_exists!(&image);
            },
        )
    }

    #[test]
    fn it_should_render_video_after_seamless_egress_transition() {
        launch_test(
            |ctx| {
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    LinearBackgroundData {
                        id: "background_id".to_string(),
                        image_url: "background_image_url".to_string(),
                        video_id: None,
                        vignette: LinearVignetteStyle::StationDetails,
                        enter_immediately: true,
                        placement: "PVBrowse".to_string(),
                        media_strategy: MediaStrategy::Promo,
                        csm_data: None,
                        interaction_source: None,
                    },
                );
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (player_status, _set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::STOPPED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                let (window_opacity, set_window_opacity) = create_signal(ctx.scope(), 1.0);
                provide_context(ctx.scope(), window_opacity);

                let hybrid_playback_context = HybridPlaybackContext {
                    should_release: create_rw_signal(ctx.scope(), None),
                    seamless_egress: store_value(ctx.scope(), false),
                };
                provide_context(ctx.scope(), hybrid_playback_context);
                compose! {
                    LinearBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |scope, mut test_loop| {
                let hybrid_playback_context = use_context::<HybridPlaybackContext>(scope).unwrap();
                let window_opacity = expect_context::<ReadSignal<f32>>(scope);

                // Trigger playback ingress without seamless egress
                hybrid_playback_context.seamless_egress.set_value(false);
                hybrid_playback_context
                    .should_release
                    .set(Some(ReleaseState::NotSeamless));

                let tree = test_loop.tick_until_done();
                let image_opacity = tree
                    .find_by_test_id("linear_background")
                    .borrow_props()
                    .base_styles
                    .opacity;
                let overlay_opacity = tree
                    .find_by_test_id("linear_overlay")
                    .borrow_props()
                    .base_styles
                    .opacity;
                assert_eq!(image_opacity, Some(1.0));
                assert_eq!(overlay_opacity, Some(0.0));
                assert_eq!(window_opacity.get_untracked(), 0.0);

                // Trigger playback ingress with seamless egress
                hybrid_playback_context.seamless_egress.set_value(true);
                hybrid_playback_context
                    .should_release
                    .set(Some(ReleaseState::NotSeamless));

                let tree = test_loop.tick_until_done();
                let image_opacity = tree
                    .find_by_test_id("linear_background")
                    .borrow_props()
                    .base_styles
                    .opacity;
                let overlay_opacity = tree
                    .find_by_test_id("linear_overlay")
                    .borrow_props()
                    .base_styles
                    .opacity;
                assert_eq!(image_opacity, Some(0.0));
                assert_eq!(overlay_opacity, Some(0.0));
                assert_eq!(window_opacity.get_untracked(), 1.0);
            },
        )
    }

    #[test]
    #[ignore]
    fn should_set_media_background_status_on_image_result() {
        todo!(
            "Blocked until we can mock on_image_result https://issues.amazon.com/issues/LRCP-4531"
        )
        // When unblocked, we should test that the media background status is set correctly with the correct id
    }
}
