use amzn_fable_tokens::{FableSpacing, FableText};
use collections_ui_constants::SUPER_CAROUSEL_TOP_OFFSET;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;

use crate::components::overlay::*;
use crate::controllers::transition_controller::transition_controller;
use crate::controllers::transition_controller::Options;
use crate::controllers::video_controller::*;
use crate::types::TrailerPlaybackContext;
use crate::types::{
    Dimensions, PlayerStatus, Position, SuperCarouselBackgroundData, Video, Window,
};

const DEFAULT_BORDER_WIDTH: f32 = 3.0;
const DEFAULT_BORDER_PADDING: f32 = 3.0;
const LEFT_OFFSET: f32 = 138.0;

// https://code.amazon.com/packages/AVLivingRoomClient/blobs/0d749ba87bef785f9d8644dcd9effb2e71a9b3bc/--/packages/avlrc-containers/common/constants/index.ts#L7
const REMASTER_TITLE_TYPE_RAMP_LINE_HEIGHT: f32 =
    FableText::TYPE_LABEL500_EMPHASIS.line_height as f32;
const REMASTER_TITLE_MARGIN_BOTTOM: f32 = FableSpacing::SPACING050;
const REMASTER_TITLE_HEIGHT: f32 =
    REMASTER_TITLE_TYPE_RAMP_LINE_HEIGHT + REMASTER_TITLE_MARGIN_BOTTOM;

const VIDEO_DIMENSIONS: Dimensions = Dimensions {
    width: 1024.0,
    height: 576.0,
};

pub const VIDEO_WINDOW_WITH_TITLE: Window = Window {
    dimensions: VIDEO_DIMENSIONS,
    position: Position {
        x: LEFT_OFFSET + DEFAULT_BORDER_WIDTH + DEFAULT_BORDER_PADDING,
        y: SUPER_CAROUSEL_TOP_OFFSET + REMASTER_TITLE_HEIGHT,
    },
};

pub const VIDEO_WINDOW_WITHOUT_TITLE: Window = Window {
    dimensions: VIDEO_DIMENSIONS,
    position: Position {
        x: LEFT_OFFSET + DEFAULT_BORDER_WIDTH + DEFAULT_BORDER_PADDING,
        y: SUPER_CAROUSEL_TOP_OFFSET,
    },
};

fn debounce_comparator(
    _prev: &SuperCarouselBackgroundData,
    _next: &SuperCarouselBackgroundData,
) -> u64 {
    300
}

#[Composer]
pub fn SuperCarouselBackground(
    ctx: &AppContext,
    data: ReadSignal<SuperCarouselBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };
    let VIDEO_WINDOW = match data.try_with_untracked(|data| data.has_title) {
        Some(true) => VIDEO_WINDOW_WITH_TITLE,
        Some(false) => VIDEO_WINDOW_WITHOUT_TITLE,
        None => VIDEO_WINDOW_WITHOUT_TITLE,
    };
    let video_options: VideoControllerOptions = VideoControllerOptions {
        window: VIDEO_WINDOW.clone(),
        window_rtl: None,
        client_id: PlayerClientID::SuperDraperOnBrowsePage,
        delay: Some(Duration::from_millis(0)),
        delay_only_after_image_loads: false,
    };
    let _rtl_enabled = rtl_enabled;

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());

    let top_left_corner_ready = create_rw_signal(ctx.scope(), false);
    let top_right_corner_ready = create_rw_signal(ctx.scope(), false);
    let bottom_left_corner_ready = create_rw_signal(ctx.scope(), false);
    let bottom_right_corner_ready = create_rw_signal(ctx.scope(), false);

    let ready = create_rw_signal(ctx.scope(), false);
    let gradients_ready = Signal::derive(ctx.scope(), move || {
        top_left_corner_ready.try_get().unwrap_or_default()
            && top_right_corner_ready.try_get().unwrap_or_default()
            && bottom_left_corner_ready.try_get().unwrap_or_default()
            && bottom_right_corner_ready.try_get().unwrap_or_default()
    });

    create_effect(ctx.scope(), move |_| {
        rendered_data.with(|_| {
            // Simulate background becoming unready and then ready again when data changes.
            // Ticket: https://t.corp.amazon.com/V1771733165
            if gradients_ready.get() {
                ready.set(false);
                ready.set(true);
            }
        });
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    let (_image_opacity, set_image_opacity) = create_signal(ctx.scope(), 1.0f32);
    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0f32);
    let (_vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 0.0f32);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready.read_only().into(),
        transition_options,
    );

    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready.read_only().into(),
        playing_video,
        player_status,
        set_image_opacity,
        set_window_opacity,
        set_vignette_opacity,
        video_options,
    );

    // set trailer playback started/ended signals
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            if player_status.get() == PlayerStatus::PLAYING {
                if let Some(playback_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                    playback_context.on_trailer_playback_start.set(true);
                    playback_context.on_playback_finished.set(false);
                    log::debug!("[SuperCarousel] Super Draper Playback began");
                }
            } else if player_status.get() == PlayerStatus::STOPPED {
                if let Some(playback_context) = use_context::<TrailerPlaybackContext>(ctx.scope()) {
                    if playback_context.on_trailer_playback_start.get_untracked() {
                        log::debug!("[SuperCarousel] Super Draper playback ended");
                        playback_context.on_playback_finished.set(true);
                    }
                    playback_context.on_trailer_playback_start.set(false);
                }
            }
        }
    });

    compose! {
        Stack() {
            //Cover corners of the video so that it fits within a card with rounded corners
            LRCImage(data: ImageData {
                url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/supercarousel/top.left.png".to_string(),
                height: 15.0,
                width: 15.0,
                tags: vec![ImageTag::Format(ImageFormat::PNG)]
            })
            .on_image_result(move |result| top_left_corner_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
            LRCImage(data: ImageData {
                url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/supercarousel/bottom.left.png".to_string(),
                height: 15.0,
                width: 15.0,
                tags: vec![ImageTag::Format(ImageFormat::PNG)]
            })
            .translate_y(VIDEO_WINDOW.dimensions.height - 15.0)
            .on_image_result(move |result| bottom_left_corner_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
            LRCImage(data: ImageData {
                url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/supercarousel/top.right.png".to_string(),
                height: 15.0,
                width: 15.0,
                tags: vec![ImageTag::Format(ImageFormat::PNG)]
            })
            .translate_x(VIDEO_WINDOW.dimensions.width - 15.0)
            .on_image_result(move |result| top_right_corner_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
            LRCImage(data: ImageData {
                url: "https://m.media-amazon.com/images/G/01/AVLRC/images/default/supercarousel/bottom.right.png".to_string(),
                height: 15.0,
                width: 15.0,
                tags: vec![ImageTag::Format(ImageFormat::PNG)]
            })
            .translate_x(VIDEO_WINDOW.dimensions.width - 15.0)
            .translate_y(VIDEO_WINDOW.dimensions.height - 15.0)
            .on_image_result(move |result| bottom_right_corner_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

            // Covers everything, used when transitioning between items.
            Overlay(size: VIDEO_DIMENSIONS, opacity: overlay_opacity)
        }
        .translate_x(VIDEO_WINDOW.position.x)
        .translate_y(VIDEO_WINDOW.position.y)
    }
}

#[cfg(test)]
mod test {
    use crate::types::MediaStrategy;

    use super::*;
    use ignx_compositron::app::launch_test;
    use rstest::*;

    fn create_mock_super_carousel_background(has_title: bool) -> SuperCarouselBackgroundData {
        SuperCarouselBackgroundData {
            id: "background_id".to_string(),
            video_id: None,
            enter_immediately: true,
            placement: "PVBrowse".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            has_title,
        }
    }

    #[rstest]
    #[case(true, VIDEO_WINDOW_WITH_TITLE.position)]
    #[case(false, VIDEO_WINDOW_WITHOUT_TITLE.position)]
    fn it_renders_in_correct_position(
        #[case] has_title: bool,
        #[case] expected_position: Position,
    ) {
        launch_test(
            move |ctx| {
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    create_mock_super_carousel_background(has_title),
                );
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, _set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::STOPPED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);

                compose! {
                    SuperCarouselBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let overlay = tree.find_by_test_id("media-background-overlay");
                let overlay_props = overlay.borrow_props();
                assert_eq!(overlay_props.layout.position.x, expected_position.x);
                assert_eq!(overlay_props.layout.position.y, expected_position.y);
            },
        )
    }

    #[test]
    fn should_send_event_when_trailer_started() {
        launch_test(
            |ctx| {
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::PAUSED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                provide_context(ctx.scope(), set_player_status);
                let playback_context = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), playback_context);
                let (data, _set_data) =
                    create_signal(ctx.scope(), create_mock_super_carousel_background(true));

                compose! {
                    SuperCarouselBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |scope, mut test_loop| {
                let set_player_status = use_context::<WriteSignal<PlayerStatus>>(scope).unwrap();
                let playback_context = use_context::<TrailerPlaybackContext>(scope).unwrap();
                playback_context.reset();
                set_player_status.set(PlayerStatus::PLAYING);
                test_loop.tick_until_done();
                assert!(!playback_context.on_playback_finished.get_untracked());

                assert!(playback_context.on_trailer_playback_start.get_untracked());
            },
        );
    }

    #[test]
    fn should_send_event_when_trailer_ended() {
        launch_test(
            |ctx| {
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::PLAYING);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);
                provide_context(ctx.scope(), set_player_status);
                let playback_context = TrailerPlaybackContext {
                    on_playback_finished: create_rw_signal(ctx.scope(), false),
                    on_trailer_playback_start: create_rw_signal(ctx.scope(), false),
                    should_play_hero: create_rw_signal(ctx.scope(), false),
                    suppress_trailer_playback: create_rw_signal(ctx.scope(), false),
                };
                provide_context(ctx.scope(), playback_context);
                let (data, _set_data) =
                    create_signal(ctx.scope(), create_mock_super_carousel_background(true));

                compose! {
                    SuperCarouselBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            |scope, mut test_loop| {
                let set_player_status = use_context::<WriteSignal<PlayerStatus>>(scope).unwrap();
                let playback_context = use_context::<TrailerPlaybackContext>(scope).unwrap();
                playback_context.reset();
                playback_context.on_trailer_playback_start.set(true);
                set_player_status.set(PlayerStatus::STOPPED);
                test_loop.tick_until_done();
                assert!(!playback_context.on_trailer_playback_start.get_untracked());
                assert!(playback_context.on_playback_finished.get_untracked());
            },
        );
    }
}
