use crate::components::overlay::*;
use crate::controllers::transition_controller::{transition_controller, Options};
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::RecapBackgroundData;
use amzn_fable_tokens::FableColor;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::context::AppContext;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageAlignment::Centered;
use lrc_image::types::{
    ImageData, ImageFormat, ImageTag, ScaleToRectangleOptions, ScalingStrategy,
};

fn debounce_comparator(_prev: &RecapBackgroundData, _next: &RecapBackgroundData) -> u64 {
    300
}

const VIGNETTE_URL: &str = "https://m.media-amazon.com/images/S/sash/WYvPP3kOzT-IYqm.png";

#[Composer]
pub fn RecapBackground(
    ctx: &AppContext,
    #[into] data: ReadSignal<RecapBackgroundData>,
    #[optional]
    #[into]
    set_initial_ready: Option<WriteSignal<bool>>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());
    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (vignettes_ready, set_vignettes_ready) = create_signal(ctx.scope(), false);

    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get() && vignettes_ready.get()
    });

    create_effect(ctx.scope(), move |prev: Option<String>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.inspect(|setter| setter.set(true)); // only do this the first time.
            true
        } else {
            false
        }
    });

    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0f32);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    // For images that are not given as 1920x1080, we currently add "black bars" which is currently acceptable.
    // However, we can consider blurring the edges by adding the blurred stretched image to the background, which we
    // currently also do for boxarts using collaged images. https://issues.amazon.com/issues/PVRE-7098

    let image_data = Signal::derive(ctx.scope(), move || ImageData {
        url: rendered_data.get().image_url,
        height: PAGE_DIMENSIONS.height,
        width: PAGE_DIMENSIONS.width,
        tags: vec![
            ImageTag::Format(ImageFormat::PNG),
            ImageTag::Scaling(ScalingStrategy::ScaleToRectangle(ScaleToRectangleOptions {
                alignment: Centered,
                hide_canvas: true,
            })),
            ImageTag::DB,
        ],
    });

    compose! {
        Stack() {
            Stack() {
                LRCImage(data: image_data)
                    .on_image_result(move |result| set_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

                LRCImage(data: ImageData {
                    url: VIGNETTE_URL.to_string(),
                    height: PAGE_DIMENSIONS.height,
                    width: PAGE_DIMENSIONS.width,
                    tags: vec![
                        ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(PAGE_DIMENSIONS.width * 0.25, PAGE_DIMENSIONS.height * 0.25)),
                        ImageTag::Format(ImageFormat::PNG)
                    ]
                })
                .on_image_result(move |result| set_vignettes_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
            }
            .height(PAGE_DIMENSIONS.height)
            .width(PAGE_DIMENSIONS.width)

            Overlay(size: PAGE_DIMENSIONS, opacity: overlay_opacity)
        }
        .background_color(get_ignx_color(FableColor::BACKGROUND))
        .height(SCREEN_HEIGHT)
        .width(SCREEN_WIDTH)
    }
}
