use crate::types::Dimensions;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::{compose, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;
use lrc_image::types::ScalingStrategy;

const IMAGE_HORIZONTAL_VIGNETTE_HEIGHT: f32 = 530.0;
const IMAGE_VERTICAL_VIGNETTE_WIDTH: f32 = 600.0;

const VERTICAL_VIGNETTE_URL_LTR: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v2_1.png";
const VERTICAL_VIGNETTE_URL_RTL: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground_rtl/vignette_v2_2.png";
const HORIZONTAL_VIGNETTE_URL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_v2_2.png";

#[Composer]
pub fn StandardVignette(
    ctx: &AppContext,
    dimensions: Dimensions,
    set_ready: WriteSignal<bool>,
    rtl_enabled: bool,
    vignette_opacity: ReadSignal<f32>,
) -> StackComposable {
    let (vertical_image_ready, set_vertical_image_ready) = create_signal(ctx.scope(), false);
    let (horizontal_image_ready, set_horizontal_image_ready) = create_signal(ctx.scope(), false);

    create_effect(ctx.scope(), move |_| {
        if vertical_image_ready.get() && horizontal_image_ready.get() {
            set_ready.set(true);
        }
    });

    compose! {
        Stack() {
            LRCImage(data: ImageData {
                url: (if rtl_enabled { VERTICAL_VIGNETTE_URL_RTL.to_string() } else { VERTICAL_VIGNETTE_URL_LTR.to_string() }),
                height: dimensions.height,
                width: IMAGE_VERTICAL_VIGNETTE_WIDTH,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::ScaleToWidth),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .opacity(vignette_opacity)
            .on_image_result(move |result| set_vertical_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))

            LRCImage(data: ImageData {
                url: HORIZONTAL_VIGNETTE_URL.to_string(),
                height: IMAGE_HORIZONTAL_VIGNETTE_HEIGHT,
                width: dimensions.width,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::ScaleToHeight),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })
            .opacity(vignette_opacity)
            .translate_y(dimensions.height - IMAGE_HORIZONTAL_VIGNETTE_HEIGHT)
            .on_image_result(move |result| set_horizontal_image_ready.set(matches!(&result, ImageLoadResultEvent::Success)))
        }
        .width(dimensions.width)
        .height(dimensions.height)
    }
}
