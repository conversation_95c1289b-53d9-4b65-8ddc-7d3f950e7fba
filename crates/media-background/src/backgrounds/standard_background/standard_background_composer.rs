use crate::backgrounds::standard_background::vignette::*;
use crate::components::overlay::*;
use crate::controllers::transition_controller::*;
use crate::controllers::video_controller::*;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::{Dimensions, PlayerStatus, Position, StandardBackgroundData, Video, Window};
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::image::ImageLoadResultEvent;
use ignx_compositron::memo::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, compose_option, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use lrc_image::types::ImageFormat;
use lrc_image::types::ImageTag;
use lrc_image::types::ScalingStrategy;

const IMAGE_HEIGHT: f32 = 756.0;
const IMAGE_WIDTH: f32 = 1344.0;

const VIDEO_DIMENSIONS: Dimensions = Dimensions {
    height: PAGE_DIMENSIONS.height,
    width: PAGE_DIMENSIONS.width,
};

pub const IMAGE_DIMENSIONS: Dimensions = Dimensions {
    height: IMAGE_HEIGHT,
    width: IMAGE_WIDTH,
};

const VIDEO_WINDOW: Window = Window {
    dimensions: VIDEO_DIMENSIONS,
    position: Position {
        x: PAGE_DIMENSIONS.width - VIDEO_DIMENSIONS.width,
        y: 0.0,
    },
};

const VIDEO_VIGNETTE_URL_LTR: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_standardhero_v1.png";
const VIDEO_VIGNETTE_URL_RTL: &str = "https://m.media-amazon.com/images/G/01/AVLRC/images/default/mediabackground/vignette_standardhero_v1_rtl.png";

fn debounce_comparator(_prev: &StandardBackgroundData, _next: &StandardBackgroundData) -> u64 {
    300
}

#[Composer]
pub fn StandardBackground(
    ctx: &AppContext,
    data: ReadSignal<StandardBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };
    let video_options: VideoControllerOptions = VideoControllerOptions {
        window: VIDEO_WINDOW,
        window_rtl: None,
        client_id: PlayerClientID::SuperDraperOnBrowsePage,
        delay: Some(Duration::from_millis(0)),
        delay_only_after_image_loads: false,
    };

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());
    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (vignettes_ready, set_vignettes_ready) = create_signal(ctx.scope(), false);

    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get() && vignettes_ready.get()
    });

    let (vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 1.0f32);

    create_effect(ctx.scope(), move |prev: Option<Option<String>>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    let (image_opacity, set_image_opacity) = create_signal(ctx.scope(), 1.0f32);
    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0f32);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready,
        playing_video,
        player_status,
        set_image_opacity,
        set_window_opacity,
        set_vignette_opacity,
        video_options,
    );

    let image_data = Signal::derive(ctx.scope(), move || {
        rendered_data.with(|rendered_data| {
            rendered_data.image_url.as_ref().map(|image_url| ImageData {
                url: image_url.clone(),
                height: IMAGE_HEIGHT,
                width: IMAGE_WIDTH,
                tags: vec![],
            })
        })
    });

    compose! {
        Stack() {
            LRCImage(data: ImageData {
                url: (if rtl_enabled { VIDEO_VIGNETTE_URL_RTL.to_string() } else { VIDEO_VIGNETTE_URL_LTR.to_string() }),
                height: PAGE_DIMENSIONS.height,
                width: PAGE_DIMENSIONS.width,
                tags: vec![
                    ImageTag::Scaling(ScalingStrategy::UpscaleToRectangleCustom(PAGE_DIMENSIONS.width * 0.25, PAGE_DIMENSIONS.height * 0.25)),
                    ImageTag::Format(ImageFormat::PNG)
                ]
            })

            Stack() {
                Memo(item_builder: Box::new(move |ctx| {
                    image_data.with(|image_data| {
                        if let Some(image_data) = image_data {
                            compose_option! {
                                LRCImage(data: image_data.clone()).opacity(image_opacity).on_image_result(move |result| set_image_ready.set(matches!(&result, ImageLoadResultEvent::Success))).test_id("standard_background_image")
                            }
                        } else {
                            set_image_ready.set(true);
                            None
                        }
                    })
                }))

                StandardVignette(dimensions: IMAGE_DIMENSIONS, set_ready: set_vignettes_ready, rtl_enabled, vignette_opacity)
            }
            .height(IMAGE_HEIGHT)
            .width(IMAGE_WIDTH)
            // position to the end of the screen.
            // TODO: use start once RTL support is added
            .translate_x(PAGE_DIMENSIONS.width - IMAGE_WIDTH)

            Overlay(size: VIDEO_DIMENSIONS, opacity: overlay_opacity)
        }
        .height(PAGE_DIMENSIONS.height)
        .width(PAGE_DIMENSIONS.width)
    }
}

#[cfg(test)]
mod test {
    use crate::types::MediaStrategy;

    use super::*;
    use ignx_compositron::{app::launch_test, test_utils::*};
    use rstest::*;

    #[rstest]
    #[case(Some("background_image_url".to_string()))]
    #[case(None)]
    fn it_renders(#[case] image_url: Option<String>) {
        let has_image = image_url.is_some();

        launch_test(
            |ctx| {
                let (data, _set_data) = create_signal(
                    ctx.scope(),
                    StandardBackgroundData {
                        id: "background_id".to_string(),
                        image_url,
                        video_id: None,
                        enter_immediately: true,
                        placement: "PVBrowse".to_string(),
                        media_strategy: MediaStrategy::Promo,
                        csm_data: None,
                        interaction_source_override: None,
                    },
                );
                let rtl_enabled = false;
                let playing_video: RwSignal<Option<Video>> = create_rw_signal(ctx.scope(), None);
                let (_window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                let (player_status, _set_player_status) =
                    create_signal(ctx.scope(), PlayerStatus::STOPPED);
                let (_ready, set_initial_ready) = create_signal(ctx.scope(), false);

                compose! {
                    StandardBackground(
                        data,
                        rtl_enabled,
                        playing_video,
                        player_status: player_status.into(),
                        set_initial_ready,
                        set_window_opacity,
                    )
                }
            },
            move |_scope, mut test_loop| {
                let tree = test_loop.tick_until_done();

                let image = tree.find_by_test_id("standard_background_image");

                if has_image {
                    assert_node_exists!(&image);
                } else {
                    assert_node_does_not_exist!(&image);
                }
            },
        )
    }
}
