use crate::backgrounds::resizable_background_shared_components::resizable_background_composer::*;
use crate::controllers::transition_controller::*;
use crate::controllers::video_controller::*;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::{PlayerStatus, Position, ResizableStandardBackgroundData, Video, Window};
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::time::Duration;
use ignx_compositron::{compose, Composer};

pub const STANDARD_COLLAPSED_VIDEO_WINDOW: Window = Window {
    dimensions: STANDARD_IMAGE_DIMENSIONS,
    position: Position {
        x: PAGE_DIMENSIONS.width - STANDARD_IMAGE_DIMENSIONS.width,
        y: 0.0,
    },
};

fn debounce_comparator(
    _: &ResizableStandardBackgroundData,
    _: &ResizableStandardBackgroundData,
) -> u64 {
    300
}

#[Composer]
pub fn ResizableStandardBackground(
    ctx: &AppContext,
    data: ReadSignal<ResizableStandardBackgroundData>,
    rtl_enabled: bool,
    playing_video: RwSignal<Option<Video>>,
    player_status: Signal<PlayerStatus>,
    set_initial_ready: WriteSignal<bool>,
    set_window_opacity: WriteSignal<f32>,
) -> StackComposable {
    let transition_options = Options {
        debounce: Box::new(debounce_comparator),
    };
    let video_options: VideoControllerOptions = VideoControllerOptions {
        window: STANDARD_COLLAPSED_VIDEO_WINDOW,
        window_rtl: None,
        client_id: PlayerClientID::SuperDraperOnBrowsePage,
        delay: Some(Duration::from_millis(0)),
        delay_only_after_image_loads: false,
    };

    let rendered_data = create_rw_signal(ctx.scope(), data.get_untracked());
    let (image_ready, set_image_ready) = create_signal(ctx.scope(), false);
    let (vignettes_ready, set_vignettes_ready) = create_signal(ctx.scope(), false);

    let ready = Signal::derive(ctx.scope(), move || {
        image_ready.get() && vignettes_ready.get()
    });

    let (vignette_opacity, set_vignette_opacity) = create_signal(ctx.scope(), 1.0f32);

    create_effect(ctx.scope(), move |prev: Option<Option<String>>| {
        rendered_data.with(|data| {
            if let Some(prev) = prev {
                if data.image_url != prev {
                    set_image_ready.set(false);
                }
            }

            data.image_url.clone()
        })
    });

    // inform media background when background becomes ready for the first time.
    create_effect(ctx.scope(), move |prev| {
        if prev == Some(true) {
            return true;
        }
        if ready.get() {
            set_initial_ready.set(true); // only do this the first time.
            true
        } else {
            false
        }
    });

    let (image_opacity, set_image_opacity) = create_signal(ctx.scope(), 1.0f32);
    let (overlay_opacity, set_overlay_opacity) = create_signal(ctx.scope(), 0.0f32);

    transition_controller(
        ctx,
        data,
        rendered_data,
        None,
        set_overlay_opacity,
        ready,
        transition_options,
    );

    video_controller(
        ctx,
        rendered_data.read_only(),
        data,
        ready,
        playing_video,
        player_status,
        set_image_opacity,
        set_window_opacity,
        set_vignette_opacity,
        video_options,
    );

    let image_url = Signal::derive(ctx.scope(), move || {
        rendered_data.with(|rendered_data| rendered_data.image_url.as_ref().cloned())
    });

    let resized_vignette_opacity = Signal::derive(ctx.scope(), move || {
        if rendered_data.get().resized
            && playing_video
                .get()
                .is_none_or(|v| v.window == RESIZED_WINDOW)
        {
            1.0
        } else {
            0.0
        }
    });

    compose! {
        ResizableBackground(
            image_url,
            image_opacity,
            on_image_ready: set_image_ready,
            on_vignette_ready: set_vignettes_ready,
            rtl_enabled,
            vignette_opacity,
            overlay_opacity,
            resized_vignette_opacity
        )
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::types::MediaStrategy;
    use ignx_compositron::test_utils::node_properties::SceneNodeTree;
    use ignx_compositron::{app::launch_test, test_utils::*};

    fn get_mock_resizable_data(
        resized: bool,
        image_present: bool,
    ) -> ResizableStandardBackgroundData {
        ResizableStandardBackgroundData {
            id: "test".to_string(),
            video_id: Some("video1".to_string()),
            image_url: if image_present {
                Some("background_image_url".to_string())
            } else {
                None
            },
            enter_immediately: false,
            placement: "test".to_string(),
            media_strategy: MediaStrategy::Promo,
            csm_data: None,
            interaction_source_override: None,
            resized,
        }
    }

    fn setup_test(ctx: AppContext, resized: bool, image_present: bool) -> StackComposable {
        let data = create_signal(ctx.scope(), get_mock_resizable_data(resized, image_present)).0;
        let rtl_enabled = false;
        let playing_video = create_rw_signal(ctx.scope(), None);
        let player_status = Signal::derive(ctx.scope(), || PlayerStatus::STOPPED);
        let set_initial_ready = create_signal(ctx.scope(), false).1;
        let set_window_opacity = create_signal(ctx.scope(), 1.0).1;

        compose! {
            Stack() {
                ResizableStandardBackground(
                    data,
                    rtl_enabled,
                    playing_video,
                    player_status,
                    set_initial_ready,
                    set_window_opacity,
                )
            }
        }
    }

    fn assert_resizable_background(tree: &SceneNodeTree, image_present: bool) {
        let resizable_background = tree.find_by_test_id("resizable_background");
        assert_node_exists!(&resizable_background);
        let image = resizable_background
            .find_any_child_with()
            .test_id("resizable_background_image")
            .find_first();
        if image_present {
            assert_node_exists!(image);
        } else {
            assert_node_does_not_exist!(&image);
        }
    }

    fn assert_resized_vignette_visibility(tree: &SceneNodeTree, visible: bool) {
        let vignette = tree.find_by_test_id("resizable_background_resized_vignette");
        assert_node_exists!(&vignette);
        let opacity = vignette.get_props().base_styles.opacity.unwrap();
        assert_eq!(opacity, if visible { 1.0 } else { 0.0 })
    }

    fn assert_collapsed_background(tree: &SceneNodeTree, image_present: bool) {
        assert_resizable_background(tree, image_present);
        assert_resized_vignette_visibility(tree, false);
    }

    fn assert_resized_background(tree: &SceneNodeTree, image_present: bool) {
        assert_resizable_background(tree, image_present);
        assert_resized_vignette_visibility(tree, true);
    }

    #[test]
    fn renders_collapsed_background_image_present() {
        launch_test(
            move |ctx| setup_test(ctx, false, true),
            move |_scope, mut test_loop| {
                assert_collapsed_background(&test_loop.tick_until_done(), true)
            },
        )
    }

    #[test]
    fn renders_collapsed_background_image_absent() {
        launch_test(
            move |ctx| setup_test(ctx, false, false),
            move |_scope, mut test_loop| {
                assert_collapsed_background(&test_loop.tick_until_done(), false)
            },
        )
    }

    #[test]
    fn renders_resized_background_image_present() {
        launch_test(
            move |ctx| setup_test(ctx, true, true),
            move |_scope, mut test_loop| {
                assert_resized_background(&test_loop.tick_until_done(), true)
            },
        )
    }

    #[test]
    fn renders_resized_background_image_absent() {
        launch_test(
            move |ctx| setup_test(ctx, true, false),
            move |_scope, mut test_loop| {
                assert_resized_background(&test_loop.tick_until_done(), false)
            },
        )
    }
}
