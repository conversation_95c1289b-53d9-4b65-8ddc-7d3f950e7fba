use amzn_fable_tokens::FableColor;
use fableous::utils::get_ignx_color;
use ignx_compositron::{compose, prelude::*, Composer};

use crate::types::{common::PAGE_DIMENSIONS, Window};

#[Composer]
pub fn PlayerOverlay(
    ctx: &AppContext,
    window: Signal<Window>,
    player_cover_opacity: Signal<f32>,
) -> impl Composable<'static> {
    let x = Signal::derive(ctx.scope(), move || window.get().position.x);
    let y = Signal::derive(ctx.scope(), move || window.get().position.y);
    let width = Signal::derive(ctx.scope(), move || window.get().dimensions.width);
    let height = Signal::derive(ctx.scope(), move || window.get().dimensions.height);

    let y_plus_height = Signal::derive(ctx.scope(), move || y.get() + height.get());
    let x_plus_width = Signal::derive(ctx.scope(), move || x.get() + width.get());

    compose! {
        Stack() {
            // Top rectangle
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(PAGE_DIMENSIONS.width)
                .height(y)

            // Right rectangle
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(Signal::derive(ctx.scope(), move || PAGE_DIMENSIONS.width - x_plus_width.get()))
                .height(Signal::derive(ctx.scope(), move || PAGE_DIMENSIONS.height - y.get()))
                .translate_x(x_plus_width)
                .translate_y(y)

            // Bottom rectangle
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(x_plus_width)
                .height(Signal::derive(ctx.scope(), move || PAGE_DIMENSIONS.height - y_plus_height.get()))
                .translate_y(y_plus_height)

            // Left rectangle
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(x)
                .height(height)
                .translate_y(y)

            // Player rectangle
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(width)
                .height(height)
                .translate_x(x)
                .translate_y(y)
                .opacity(player_cover_opacity)
        }
        .width(PAGE_DIMENSIONS.width)
        .height(PAGE_DIMENSIONS.height)
    }
}
