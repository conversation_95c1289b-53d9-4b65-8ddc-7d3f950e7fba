use crate::components::windowed_background::*;
use crate::types::common::PAGE_DIMENSIONS;
use crate::types::{Dimensions, Position, Video, Window};
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::player::PlayerCommand;
use ignx_compositron::player_data::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use ignx_compositron::{compose, Composer};

const DEFAULT_WINDOW: Window = Window {
    position: Position { y: 0.0, x: 0.0 },
    dimensions: Dimensions {
        height: PAGE_DIMENSIONS.height,
        width: PAGE_DIMENSIONS.width,
    },
};

#[Composer]
pub fn Player(
    ctx: &AppContext,
    video: ReadSignal<Option<Video>>,
    content_state: RwSignal<ContentState>,
    playback_state: RwSignal<PlaybackState>,
    player_command: RwSignal<Option<PlayerCommand>>,
    set_player_initialised: WriteSignal<bool>,
    window_opacity: ReadSignal<f32>,
) -> StackComposable {
    let (window, set_window) = create_signal(ctx.scope(), DEFAULT_WINDOW);

    create_effect(ctx.scope(), move |_| {
        // We only want to change the background for a new video. There isn't any point tearing down the old one in between videos as the next
        // one may be the same dimensions.
        if let Some(video) = video.get() {
            set_window.set(video.window);
        }
    });
    compose! {
        Stack() {
            WindowedBackground(window, content_state, playback_state, player_command, set_player_initialised, window_opacity)
        }
        .height(PAGE_DIMENSIONS.height)
        .width(PAGE_DIMENSIONS.width)
    }
}
