use crate::types::Dimensions;
use amzn_fable_tokens::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::composable::*;
use ignx_compositron::context::*;
use ignx_compositron::reactive::*;
use ignx_compositron::rectangle::*;
use ignx_compositron::{compose, Composer};

#[Composer]
pub fn Overlay(
    ctx: &AppContext,
    size: Dimensions,
    #[into] opacity: MaybeSignal<f32>,
) -> RectangleComposable {
    compose! {
        Rectangle()
            .height(size.height)
            .width(size.width)
            .background_color(get_ignx_color(FableColor::COOL900))
            .opacity(opacity)
            .test_id("media-background-overlay")
    }
}
