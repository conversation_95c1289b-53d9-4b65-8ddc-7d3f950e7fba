use crate::types::common::PAGE_DIMENSIONS;
use crate::types::context::MediaBackgroundStatusContextInternal;
use crate::types::Window;
use amzn_fable_tokens::*;
use fableous::utils::get_ignx_color;
use ignx_compositron::context::*;
use ignx_compositron::player::*;
use ignx_compositron::player_data::*;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use log::info;

#[Composer]
pub fn WindowedBackground(
    ctx: &AppContext,
    window: ReadSignal<Window>,
    content_state: RwSignal<ContentState>,
    playback_state: RwSignal<PlaybackState>,
    player_command: RwSignal<Option<PlayerCommand>>,
    set_player_initialised: WriteSignal<bool>,
    window_opacity: ReadSignal<f32>,
) -> StackComposable {
    let media_background_context = use_context::<MediaBackgroundStatusContextInternal>(ctx.scope());
    let player_resize_support = media_background_context.map(|c| c.player_resize_support);

    let resize_support = Signal::derive(ctx.scope(), move || {
        player_resize_support.and_then(|signal| signal.get())
    });

    let width = create_memo(ctx.scope(), move |_| match resize_support.get() {
        None => PAGE_DIMENSIONS.width,
        Some(ResizeSupport::Static) => window.get().dimensions.width,
        Some(ResizeSupport::Dynamic) => window.get().dimensions.width,
        Some(ResizeSupport::Animated) => window.get().dimensions.width,
    });

    let height = create_memo(ctx.scope(), move |_| match resize_support.get() {
        None => PAGE_DIMENSIONS.height,
        Some(ResizeSupport::Static) => window.get().dimensions.height,
        Some(ResizeSupport::Dynamic) => window.get().dimensions.height,
        Some(ResizeSupport::Animated) => window.get().dimensions.height,
    });

    let x = create_memo(ctx.scope(), move |_| match resize_support.get() {
        None => 0.0,
        Some(ResizeSupport::Static) => window.get().position.x,
        Some(ResizeSupport::Dynamic) => window.get().position.x,
        Some(ResizeSupport::Animated) => window.get().position.x,
    });

    let y = create_memo(ctx.scope(), move |_| match resize_support.get() {
        None => 0.0,
        Some(ResizeSupport::Static) => window.get().position.y,
        Some(ResizeSupport::Dynamic) => window.get().position.y,
        Some(ResizeSupport::Animated) => window.get().position.y,
    });

    let yAndHeight = create_memo(ctx.scope(), move |_| y.get() + height.get());

    let xAndWidth = create_memo(ctx.scope(), move |_| x.get() + width.get());

    let pageWidthMinusXAndWidth = create_memo(ctx.scope(), move |_| {
        PAGE_DIMENSIONS.width - xAndWidth.get()
    });

    let pageHeightMinusY = create_memo(ctx.scope(), move |_| PAGE_DIMENSIONS.height - y.get());

    let pageHeightMinusYAndHeight =
        create_memo(ctx.scope(), move |_| pageHeightMinusY.get() - height.get());

    // When the player is shown, the underlaying window must be transparent to avoid showing artefacts on Xbox
    let background_opacity = Signal::derive(ctx.scope(), move || {
        let window_opacity = window_opacity.get();
        if window_opacity == 0.0 {
            1.0
        } else {
            0.0
        }
    });

    let scope = ctx.scope();
    compose! {
        Stack() {
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(PAGE_DIMENSIONS.width)
                .height(y)

            // right.
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(pageWidthMinusXAndWidth)
                .height(pageHeightMinusY)
                .translate_x(xAndWidth)
                .translate_y(y)

            // bottom.
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .height(pageHeightMinusYAndHeight)
                .width(xAndWidth)
                .translate_y(yAndHeight)

            // left.
            Rectangle()
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(x)
                .height(height)
                .translate_y(y)

            // window.
            Rectangle().opacity(background_opacity)
                .background_color(get_ignx_color(FableColor::COOL900))
                .width(width)
                .height(height)
                .translate_x(x)
                .translate_y(y).test_id("window")

           Player(player_command)
                .translate_x(x)
                .translate_y(y)
                .height(height)
                .width(width).opacity(window_opacity).test_id("player").on_player_event(move |event| {
                match event {
                    PlayerEvent::Initialisation(Ok(capabilities)) => {
                        set_player_initialised.set(true);

                        if let Some(signal) = player_resize_support {
                            signal.set(capabilities.resize_support.clone());
                        }
                        if let Some(capabilities_signal) = use_context::<RwSignal<Option<PlayerCapabilities>>>(scope) {
                            capabilities_signal.try_set(Some(capabilities.clone()));
                            info!("Player capabilities set to: {:?}", capabilities)
                        };
                    }
                    PlayerEvent::ContentStateChange(Ok(ContentState::Unloaded)) => {
                        content_state.set(ContentState::Unloaded);
                    }
                    PlayerEvent::ContentStateChange(Ok(ContentState::Ready)) => {
                        content_state.set(ContentState::Ready);
                    }
                    PlayerEvent::PlaybackStateChange(PlaybackState::Playing) => {
                        playback_state.set(PlaybackState::Playing);
                    }
                    PlayerEvent::PlaybackStateChange(PlaybackState::Paused) => {
                        playback_state.set(PlaybackState::Paused);
                    }
                    PlayerEvent::TimelineEnded => {
                        content_state.set(ContentState::Waiting);
                    }
                    _ => {}
                }
           })
        }
        .height(PAGE_DIMENSIONS.height)
        .width(PAGE_DIMENSIONS.width)
    }
}

#[cfg(test)]
mod test {

    use super::*;
    use crate::types::{
        context::provide_media_background_status_context, Dimensions, Position, Window,
    };
    use ignx_compositron::compose;
    use ignx_compositron::{
        app::launch_test,
        player::PlayerCommand,
        player_data::{ContentState, PlaybackState},
        reactive::{
            create_rw_signal, create_signal, provide_context, use_context, SignalSet, WriteSignal,
        },
    };

    const PLAYER_WINDOW: Window = Window {
        position: Position { x: 0.0, y: 0.0 },
        dimensions: Dimensions {
            width: 1920.0,
            height: 1080.0,
        },
    };

    #[test]
    fn background_is_transparent_when_player_is_shown() {
        launch_test(
            |ctx| {
                let content_state = create_rw_signal(ctx.scope(), ContentState::Ready);
                let (window, _) = create_signal(ctx.scope(), PLAYER_WINDOW);
                let playback_state = create_rw_signal(ctx.scope(), PlaybackState::Playing);
                let (_, set_player_initialised) = create_signal(ctx.scope(), false);
                let player_command = create_rw_signal(ctx.scope(), None::<PlayerCommand>);
                let (window_opacity, set_window_opacity) = create_signal(ctx.scope(), 0.0);
                provide_context(ctx.scope(), set_window_opacity);
                provide_media_background_status_context(ctx.scope());

                compose! {
                    WindowedBackground(
                        window,
                        content_state,
                        playback_state,player_command,
                        set_player_initialised,
                        window_opacity
                    );
                }
            },
            |scope, mut test_loop| {
                let set_window_opacity = use_context::<WriteSignal<f32>>(scope).unwrap();
                set_window_opacity.set(1.0);
                let tree = test_loop.tick_until_done();
                let player_opacity = tree
                    .find_by_test_id("player")
                    .borrow_props()
                    .base_styles
                    .opacity;
                let background_opacity = tree
                    .find_by_test_id("window")
                    .borrow_props()
                    .base_styles
                    .opacity;

                assert_eq!(player_opacity, Some(1.0));
                assert_eq!(background_opacity, Some(0.0));
                set_window_opacity.set(0.0);
                let tree = test_loop.tick_until_done();
                let player_opacity = tree
                    .find_by_test_id("player")
                    .borrow_props()
                    .base_styles
                    .opacity;
                let background_opacity = tree
                    .find_by_test_id("window")
                    .borrow_props()
                    .base_styles
                    .opacity;

                assert_eq!(player_opacity, Some(0.0));
                assert_eq!(background_opacity, Some(1.0));
            },
        )
    }
}
