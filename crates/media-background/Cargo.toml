[package]
name = "media-background"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
log.workspace = true
serde.workspace = true
serde_json.workspace = true
fableous.workspace = true
auth.workspace = true
media-background-derive.workspace = true
lrc-image.workspace = true
cacheable-derive.workspace = true
location.workspace = true
router.workspace = true
collections-ui-constants.workspace = true
cfg-test-attr-derive.workspace = true
rust-features.workspace = true
playback.workspace = true
strum.workspace = true
serial_test.workspace = true
resiliency-store.workspace = true
mockall_double.workspace = true
profile-manager.workspace = true
common-transform-types.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
playback = { workspace = true, features = ["test_utils"] }
rstest.workspace = true

[[example]]
name = "media_background"
crate-type = ["cdylib"]
path = "examples/media_background.rs"

[features]
debug_impl = []
test_utils = []
