use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;
use serde_json::Value;

#[derive(Serialize, NetworkParsed, Default, Clone, Debug)]
#[derive_test_only(PartialEq)]
#[serde(rename_all = "camelCase")]
#[network(camelCaseAll)]
pub struct DetailsTapParameters {
    pub atf: Vec<String>,
    pub btf: Vec<String>,
    pub btf_event: Vec<String>,
}

#[derive(Serialize, NetworkParsed, Default, Clone, Debug)]
#[derive_test_only(PartialEq)]
#[serde(rename_all = "camelCase")]
#[network(camelCaseAll)]
pub struct LiveDetailsTapParameters {
    pub atf: Vec<String>,
    pub btf: Vec<String>,
}

#[derive(Serialize, NetworkParsed, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Debug)]
#[derive_test_only(PartialEq)]
#[serde(rename_all = "camelCase")]
#[network(camelCaseAll)]
pub struct TapsParameterPages {
    pub collections: Vec<String>,
    pub details: DetailsTapParameters,
    pub live_details: LiveDetailsTapParameters,
}
