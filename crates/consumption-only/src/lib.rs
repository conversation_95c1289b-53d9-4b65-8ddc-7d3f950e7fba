mod types;

use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::reactive::*;
use mockall::{automock, mock};
use mockall_double::double;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;
use serde_json::Value;
use synchronized_state_store::MockStateDispatcher;
#[double]
use synchronized_state_store::StateDispatcher;
#[double]
use synchronized_state_store::SynchronizedStateStore;

const CONSUMPTION_ONLY_STORE_ID: &str = "consumption-only";

#[derive(Serialize, NetworkParsed, Clone, Debug)]
#[derive_test_only(PartialEq)]
#[serde(rename_all = "camelCase")]
#[network(camelCaseAll)]
pub struct ConsumptionOnlyStoreState {
    pub prime_add_on: bool,
    pub loaded: bool,
}

impl Default for ConsumptionOnlyStoreState {
    fn default() -> Self {
        ConsumptionOnlyStoreState {
            prime_add_on: false,
            loaded: false,
        }
    }
}

#[automock]
pub trait ConsumptionOnlyStore {
    fn get_prime_add_on(&self) -> bool;
}

pub struct ConsumptionOnly {
    sync_state_store: SynchronizedStateStore<ConsumptionOnlyStoreState>,
}

impl ConsumptionOnly {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher) -> Self {
        Self {
            sync_state_store: SynchronizedStateStore::new(
                scope,
                state_dispatcher,
                CONSUMPTION_ONLY_STORE_ID.into(),
                ConsumptionOnlyStoreState::default(),
            ),
        }
    }
}

impl ConsumptionOnlyStore for ConsumptionOnly {
    fn get_prime_add_on(&self) -> bool {
        self.sync_state_store.derive(|state| state.prime_add_on)
    }
}

mock! {
    pub ConsumptionOnly {
        pub fn new(scope: Scope, state_dispatcher: &MockStateDispatcher) -> Self;
    }
    impl ConsumptionOnlyStore for ConsumptionOnly {
        fn get_prime_add_on(&self) -> bool;
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use ignx_compositron::app::launch_only_scope;
    use synchronized_state_store::test_utils::mock_synchronized_state_store_new;
    use synchronized_state_store::{MockStateDispatcher, MockSynchronizedStateStore};

    #[test]
    fn should_construct_using_new() {
        launch_only_scope(|scope| {
            let mock_state_dispatcher = MockStateDispatcher::default();

            let (_guard, _context, store_info) =
                mock_synchronized_state_store_new::<ConsumptionOnlyStoreState>();

            ConsumptionOnly::new(scope, &mock_state_dispatcher);

            let store_data = store_info.read().expect("Unable to read store_info");
            assert_eq!(store_data.id, CONSUMPTION_ONLY_STORE_ID);
            assert_eq!(store_data.state, Some(ConsumptionOnlyStoreState::default()));
        });
    }

    #[test]
    fn should_get_taps_roles_from_store() {
        let mut mock_sync_store = MockSynchronizedStateStore::default();

        let mut mock_state = ConsumptionOnlyStoreState::default();
        mock_state.prime_add_on = true;

        mock_sync_store
            .expect_derive::<bool>()
            .once()
            .return_once(move |inner_fn| inner_fn(&mock_state));

        let consumption_only = ConsumptionOnly {
            sync_state_store: mock_sync_store,
        };

        assert_eq!(consumption_only.get_prime_add_on(), true);
    }
}
