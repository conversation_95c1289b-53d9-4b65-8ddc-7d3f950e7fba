[package]
name = "consumption-only"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
synchronized-state-store.workspace = true
serde.workspace = true
serde_json.workspace = true
log.workspace = true
mockall.workspace = true
mockall_double.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
cfg-test-attr-derive.workspace = true
acm-config.workspace = true

[dev-dependencies]
synchronized-state-store = { workspace = true, features = ["test-utils"] }
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
rstest.workspace = true

[lints]
workspace = true

[features]
debug_impl = []
