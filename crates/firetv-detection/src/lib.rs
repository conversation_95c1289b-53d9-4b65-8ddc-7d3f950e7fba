#![allow(deprecated, reason = "intentionally marked as deprecated")]
mod is_firetv;

#[cfg(not(feature = "test_utils"))]
pub use is_firetv::implementation::{is_firetv, is_firetv_from};

#[cfg(feature = "test_utils")]
pub use is_firetv::mock_implementation::{
    __is_firetv::Context as IsFireTVMockContext,
    __is_firetv_from::Context as IsFireTVFromMockContext, is_firetv, is_firetv_context,
    is_firetv_from, is_firetv_from_context,
};
