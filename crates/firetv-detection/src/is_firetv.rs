use mockall::automock;

#[automock]
pub mod implementation {
    use ignx_compositron::device_information::DeviceInformation;
    /// List of operating system names that identify `FireTV` devices.
    const SUPPORTED_OS_NAMES: [&str; 1] = ["FireOS"];

    /// Determines if the current device is a `FireTV` device.
    ///
    /// # Returns
    ///
    /// `true` if the device is a `FireTV` device, `false` otherwise.
    #[deprecated = "Use firetv create unless it creates a cyclic dependency"]
    pub fn is_firetv() -> bool {
        SUPPORTED_OS_NAMES.contains(&DeviceInformation::new().os_name.as_str())
    }

    /// Determines if the current device is a `FireTV` device. Uses the [`DeviceInformation`] provided
    /// rather than creating a new one.
    ///
    /// # Returns
    ///
    /// `true` if the device is a `FireTV` device, `false` otherwise.
    #[deprecated = "Use firetv create unless it creates a cyclic dependency"]
    pub fn is_firetv_from(device_information: &DeviceInformation) -> bool {
        SUPPORTED_OS_NAMES.contains(&device_information.os_name.as_str())
    }
}

#[cfg(test)]
mod tests {
    use super::implementation::*;
    use ignx_compositron::device_information::DeviceInformation;
    use rstest::*;

    #[test]
    fn test_is_firetv() {
        // NOTE: DeviceInformation returns 'osx' in ignx_api mocks
        assert!(!is_firetv());
    }

    #[rstest]
    #[case("FireOS", true)]
    #[case("Android", false)]
    #[case("osx", false)]
    fn detects_firetv_from_device_information(#[case] os_name: String, #[case] is_firetv: bool) {
        let mut device_information = DeviceInformation::new();
        device_information.os_name = os_name;
        assert_eq!(is_firetv_from(&device_information), is_firetv);
    }
}
