# firetv-detection

## Overview

This crate provides functionality to detect if the application is running on a FireTV device. It's intentionally separate from the main `firetv` crate to avoid cyclic dependencies in the codebase. The `firetv-detection` crate offers a lightweight alternative to the full `firetv` crate when you only need to determine if the current environment is a FireTV device.

## Usage

Don't use this crate. This crate is not meant to be directly consumed unless `firetv` crate creates a cyclic dependency in your crate.

**IF** you have to use this due to a cyclic dependency, allow the deprecated warning with `#[allow(deprecated, reason = "firetv creates cyclic dependency")]`

## API

The crate provides two main functions:

- `is_firetv()` - Determines if the current device is a FireTV device
- `is_firetv_from(device_information)` - Determines if a device is a FireTV device using provided DeviceInformation

## Example

```rust
use firetv_detection::{is_firetv, is_firetv_from};
use ignx_compositron::device_information::DeviceInformation;

// Simple check for current device
if is_firetv() {
    // FireTV-specific code
} else {
    // Code for other platforms
}

// Using with existing DeviceInformation
let device_info = DeviceInformation::new();
if is_firetv_from(&device_info) {
    // FireTV-specific code
}
```

## Mock Support

When the `test_utils` feature is enabled, the crate provides mock implementations for testing using `mockall`.

### Enabling Mocks

To use the mock implementations, enable the `test_utils` feature in your `Cargo.toml`:

```toml
[dependencies]
firetv-detection = { workspace = true, features = ["test_utils"] }
```

### Using Mocks in Tests

The crate provides two context functions for mocking:

- `is_firetv_context()` - For mocking the basic `is_firetv()` function
- `is_firetv_from_context()` - For mocking the `is_firetv_from()` function

Here's a complete example of how to use mocks in your tests:

```rust
use firetv_detection::is_firetv_from_context;
use ignx_compositron::device_information::DeviceInformation;
use serial_test::serial;

#[test]
#[serial]
fn test_firetv_detection() {
    // Get the mock context
    let mock = is_firetv_from_context();
    
    // Set the expected return value
    mock.expect().return_const(true);
    
    // Now any call to is_firetv_from() will return true
    let device_info = DeviceInformation::new();
    assert!(firetv_detection::is_firetv_from(&device_info));
    
    // You can also set different return values for different test cases
    let mock = is_firetv_from_context();
    mock.expect().return_const(false);
    assert!(!firetv_detection::is_firetv_from(&device_info));
}

#[test]
#[serial]
fn test_basic_firetv_detection() {
    // Get the mock context for the basic is_firetv() function
    let mock = firetv_detection::is_firetv_context();
    
    // Set the expected return value
    mock.expect().return_const(true);
    
    // Now any call to is_firetv() will return true
    assert!(firetv_detection::is_firetv());
}
```

### Important Notes

1. Use the `#[serial]` attribute from the `serial_test` crate when testing with mocks to prevent test interference.
1. Always get a fresh mock context at the beginning of each test.
1. Set expectations before executing the code that uses the mocked functions.
