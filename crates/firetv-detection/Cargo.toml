[package]
name = "firetv-detection"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true
description = "Crate to detect if client runs on a FireTV or not"

[dependencies]
amzn-ignx-compositron.workspace = true
mockall.workspace = true

[lints]
workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["test_utils"] }
rstest.workspace = true

[features]
test_utils = []
