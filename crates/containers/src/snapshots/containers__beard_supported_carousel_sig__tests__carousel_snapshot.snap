---
source: crates/containers/src/beard_supported_carousel_sig.rs
expression: tree
---
props:
  node_id: 0
  test_id: ~
  composable_type: Column
  is_focused: false
  is_focusable: false
  is_on_screen: true
  on_screen_amount: 1
  is_visible: true
  base_styles:
    background_color: ~
    opacity: ~
    border_color: ~
    border_width: ~
    border_radius: ~
  text: ~
  text_layout: ~
  layout:
    size:
      height: 258
      width: 1920
    position:
      x: 0
      y: 0
    bounding_box:
      left: 0
      top: 0
      width: 1920
      height: 258
    border_box:
      left: 0
      top: 0
      width: 1920
      height: 258
    rotation: 0
child_nodes:
  - props:
      node_id: 2
      test_id: beard-supported-carousel
      composable_type: Column
      is_focused: false
      is_focusable: false
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color: ~
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: ~
      text: ~
      text_layout: ~
      layout:
        size:
          height: 258
          width: 1920
        position:
          x: 0
          y: 0
        bounding_box:
          left: 0
          top: 0
          width: 1920
          height: 258
        border_box:
          left: 0
          top: 0
          width: 1920
          height: 258
        rotation: 0
    child_nodes:
      - props:
          node_id: 3
          test_id: basic-carousel
          composable_type: Column
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: 1
            border_color: ~
            border_width: ~
            border_radius: ~
          text: ~
          text_layout: ~
          layout:
            size:
              height: 258
              width: 1920
            position:
              x: 0
              y: 0
            bounding_box:
              left: 0
              top: 0
              width: 1920
              height: 258
            border_box:
              left: 0
              top: 0
              width: 1920
              height: 258
            rotation: 0
        child_nodes:
          - props:
              node_id: 5
              test_id: basic-carousel-header
              composable_type: Label
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: "Facet: Carousel Title"
              text_layout:
                is_text_truncated: false
                num_lines: 1
                text_bounding_box:
                  left: 0
                  top: 0
                  width: 306.73123
                  height: 33
                is_rich_text_parent: ~
              layout:
                size:
                  height: 42
                  width: 306.73123
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 306.73123
                  height: 42
                border_box:
                  left: 0
                  top: 0
                  width: 306.73123
                  height: 42
                rotation: 0
            child_nodes: []
          - props:
              node_id: 6
              test_id: ~
              composable_type: Stack
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 216
                  width: 1920
                position:
                  x: 0
                  y: 42
                bounding_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 216
                border_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 216
                rotation: 0
            child_nodes:
              - props:
                  node_id: 7
                  test_id: item-list
                  composable_type: RowList
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 216
                      width: 1920
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 216
                    border_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 216
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 8
                      test_id: ~
                      composable_type: Nothing
                      is_focused: true
                      is_focusable: true
                      is_on_screen: true
                      on_screen_amount: 0.6666667
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 216
                          width: 2880
                        position:
                          x: 0
                          y: 42
                        bounding_box:
                          left: 0
                          top: 42
                          width: 2880
                          height: 216
                        border_box:
                          left: 0
                          top: 42
                          width: 2880
                          height: 216
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 134217729
                          test_id: beard-supported-card-test-id
                          composable_type: Stack
                          is_focused: true
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 0
                              y: 42
                            bounding_box:
                              left: 0
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 0
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217744
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 0
                                  y: 42
                                bounding_box:
                                  left: 0
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 0
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217741
                                  test_id: ""
                                  composable_type: Stack
                                  is_focused: true
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 0
                                      y: 42
                                    bounding_box:
                                      left: 0
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 0
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217737
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 0
                                          y: 42
                                        bounding_box:
                                          left: 0
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 0
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 18
                                          test_id: tile-fallback-label-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 0
                                              y: 42
                                            bounding_box:
                                              left: 0
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 0
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 19
                                              test_id: typography-test-id
                                              composable_type: Label
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ""
                                              text_layout:
                                                is_text_truncated: false
                                                num_lines: 1
                                                text_bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 0
                                                  height: 39
                                                is_rich_text_parent: ~
                                              layout:
                                                size:
                                                  height: 39
                                                  width: 0
                                                position:
                                                  x: 192
                                                  y: 130.5
                                                bounding_box:
                                                  left: 192
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                border_box:
                                                  left: 192
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 20
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 0
                                          y: 42
                                        bounding_box:
                                          left: 0
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 0
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 22
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 0
                                              y: 42
                                            bounding_box:
                                              left: 0
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 0
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 24
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 0
                                              y: 42
                                            bounding_box:
                                              left: 0
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 0
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 26
                                  test_id: ~
                                  composable_type: Nothing
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 0
                                      width: 0
                                    position:
                                      x: 0
                                      y: 42
                                    bounding_box:
                                      left: 0
                                      top: 42
                                      width: 0
                                      height: 0
                                    border_box:
                                      left: 0
                                      top: 42
                                      width: 0
                                      height: 0
                                    rotation: 0
                                child_nodes: []
                      - props:
                          node_id: 27
                          test_id: beard-supported-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 416
                              y: 42
                            bounding_box:
                              left: 416
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 416
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 28
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 416
                                  y: 42
                                bounding_box:
                                  left: 416
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 416
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 29
                                  test_id: ""
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 416
                                      y: 42
                                    bounding_box:
                                      left: 416
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 416
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 30
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 416
                                          y: 42
                                        bounding_box:
                                          left: 416
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 416
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 32
                                          test_id: tile-fallback-label-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 416
                                              y: 42
                                            bounding_box:
                                              left: 416
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 416
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 33
                                              test_id: typography-test-id
                                              composable_type: Label
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ""
                                              text_layout:
                                                is_text_truncated: false
                                                num_lines: 1
                                                text_bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 0
                                                  height: 39
                                                is_rich_text_parent: ~
                                              layout:
                                                size:
                                                  height: 39
                                                  width: 0
                                                position:
                                                  x: 608
                                                  y: 130.5
                                                bounding_box:
                                                  left: 608
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                border_box:
                                                  left: 608
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 34
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 416
                                          y: 42
                                        bounding_box:
                                          left: 416
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 416
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 36
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 416
                                              y: 42
                                            bounding_box:
                                              left: 416
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 416
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 38
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 416
                                              y: 42
                                            bounding_box:
                                              left: 416
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 416
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 40
                                  test_id: ~
                                  composable_type: Nothing
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 0
                                      width: 0
                                    position:
                                      x: 416
                                      y: 42
                                    bounding_box:
                                      left: 416
                                      top: 42
                                      width: 0
                                      height: 0
                                    border_box:
                                      left: 416
                                      top: 42
                                      width: 0
                                      height: 0
                                    rotation: 0
                                child_nodes: []
                      - props:
                          node_id: 134217767
                          test_id: beard-supported-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 832
                              y: 42
                            bounding_box:
                              left: 832
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 832
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217765
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 832
                                  y: 42
                                bounding_box:
                                  left: 832
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 832
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217763
                                  test_id: ""
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 832
                                      y: 42
                                    bounding_box:
                                      left: 832
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 832
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217759
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 832
                                          y: 42
                                        bounding_box:
                                          left: 832
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 832
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217751
                                          test_id: tile-fallback-label-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 832
                                              y: 42
                                            bounding_box:
                                              left: 832
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 832
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 134217749
                                              test_id: typography-test-id
                                              composable_type: Label
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ""
                                              text_layout:
                                                is_text_truncated: false
                                                num_lines: 1
                                                text_bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 0
                                                  height: 39
                                                is_rich_text_parent: ~
                                              layout:
                                                size:
                                                  height: 39
                                                  width: 0
                                                position:
                                                  x: 1024
                                                  y: 130.5
                                                bounding_box:
                                                  left: 1024
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                border_box:
                                                  left: 1024
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 268435460
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 832
                                          y: 42
                                        bounding_box:
                                          left: 832
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 832
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 42
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 832
                                              y: 42
                                            bounding_box:
                                              left: 832
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 832
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 44
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 832
                                              y: 42
                                            bounding_box:
                                              left: 832
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 832
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 46
                                  test_id: ~
                                  composable_type: Nothing
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 0
                                      width: 0
                                    position:
                                      x: 832
                                      y: 42
                                    bounding_box:
                                      left: 832
                                      top: 42
                                      width: 0
                                      height: 0
                                    border_box:
                                      left: 832
                                      top: 42
                                      width: 0
                                      height: 0
                                    rotation: 0
                                child_nodes: []
                      - props:
                          node_id: 47
                          test_id: beard-supported-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 1248
                              y: 42
                            bounding_box:
                              left: 1248
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 1248
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 48
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 1248
                                  y: 42
                                bounding_box:
                                  left: 1248
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 1248
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 49
                                  test_id: ""
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 1248
                                      y: 42
                                    bounding_box:
                                      left: 1248
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 1248
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 50
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1248
                                          y: 42
                                        bounding_box:
                                          left: 1248
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1248
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 52
                                          test_id: tile-fallback-label-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 1248
                                              y: 42
                                            bounding_box:
                                              left: 1248
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 1248
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 53
                                              test_id: typography-test-id
                                              composable_type: Label
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ""
                                              text_layout:
                                                is_text_truncated: false
                                                num_lines: 1
                                                text_bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 0
                                                  height: 39
                                                is_rich_text_parent: ~
                                              layout:
                                                size:
                                                  height: 39
                                                  width: 0
                                                position:
                                                  x: 1440
                                                  y: 130.5
                                                bounding_box:
                                                  left: 1440
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                border_box:
                                                  left: 1440
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 54
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1248
                                          y: 42
                                        bounding_box:
                                          left: 1248
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1248
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 56
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1248
                                              y: 42
                                            bounding_box:
                                              left: 1248
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1248
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 58
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1248
                                              y: 42
                                            bounding_box:
                                              left: 1248
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1248
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 60
                                  test_id: ~
                                  composable_type: Nothing
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 0
                                      width: 0
                                    position:
                                      x: 1248
                                      y: 42
                                    bounding_box:
                                      left: 1248
                                      top: 42
                                      width: 0
                                      height: 0
                                    border_box:
                                      left: 1248
                                      top: 42
                                      width: 0
                                      height: 0
                                    rotation: 0
                                child_nodes: []
                      - props:
                          node_id: 134217787
                          test_id: beard-supported-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 0.6666667
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 1664
                              y: 42
                            bounding_box:
                              left: 1664
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 1664
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217785
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 0.6666667
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 1664
                                  y: 42
                                bounding_box:
                                  left: 1664
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 1664
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217783
                                  test_id: ""
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 0.6666667
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 1664
                                      y: 42
                                    bounding_box:
                                      left: 1664
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 1664
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217779
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 0.6666667
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1664
                                          y: 42
                                        bounding_box:
                                          left: 1664
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1664
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217771
                                          test_id: tile-fallback-label-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 0.6666667
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 1664
                                              y: 42
                                            bounding_box:
                                              left: 1664
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 1664
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 134217769
                                              test_id: typography-test-id
                                              composable_type: Label
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ""
                                              text_layout:
                                                is_text_truncated: false
                                                num_lines: 1
                                                text_bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 0
                                                  height: 39
                                                is_rich_text_parent: ~
                                              layout:
                                                size:
                                                  height: 39
                                                  width: 0
                                                position:
                                                  x: 1856
                                                  y: 130.5
                                                bounding_box:
                                                  left: 1856
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                border_box:
                                                  left: 1856
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 268435481
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 0.6666667
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1664
                                          y: 42
                                        bounding_box:
                                          left: 1664
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1664
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 62
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1664
                                              y: 42
                                            bounding_box:
                                              left: 1664
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1664
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 64
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1664
                                              y: 42
                                            bounding_box:
                                              left: 1664
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1664
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 66
                                  test_id: ~
                                  composable_type: Nothing
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 0
                                      width: 0
                                    position:
                                      x: 1664
                                      y: 42
                                    bounding_box:
                                      left: 1664
                                      top: 42
                                      width: 0
                                      height: 0
                                    border_box:
                                      left: 1664
                                      top: 42
                                      width: 0
                                      height: 0
                                    rotation: 0
                                child_nodes: []
                      - props:
                          node_id: 67
                          test_id: beard-supported-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 2080
                              y: 42
                            bounding_box:
                              left: 2080
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 2080
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 68
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 2080
                                  y: 42
                                bounding_box:
                                  left: 2080
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 2080
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 69
                                  test_id: ""
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 2080
                                      y: 42
                                    bounding_box:
                                      left: 2080
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 2080
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 70
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2080
                                          y: 42
                                        bounding_box:
                                          left: 2080
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2080
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 72
                                          test_id: tile-fallback-label-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 2080
                                              y: 42
                                            bounding_box:
                                              left: 2080
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 2080
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 73
                                              test_id: typography-test-id
                                              composable_type: Label
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ""
                                              text_layout:
                                                is_text_truncated: false
                                                num_lines: 1
                                                text_bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 0
                                                  height: 39
                                                is_rich_text_parent: ~
                                              layout:
                                                size:
                                                  height: 39
                                                  width: 0
                                                position:
                                                  x: 2272
                                                  y: 130.5
                                                bounding_box:
                                                  left: 2272
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                border_box:
                                                  left: 2272
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 74
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2080
                                          y: 42
                                        bounding_box:
                                          left: 2080
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2080
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 76
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2080
                                              y: 42
                                            bounding_box:
                                              left: 2080
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2080
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 78
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2080
                                              y: 42
                                            bounding_box:
                                              left: 2080
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2080
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 80
                                  test_id: ~
                                  composable_type: Nothing
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 0
                                      width: 0
                                    position:
                                      x: 2080
                                      y: 42
                                    bounding_box:
                                      left: 2080
                                      top: 42
                                      width: 0
                                      height: 0
                                    border_box:
                                      left: 2080
                                      top: 42
                                      width: 0
                                      height: 0
                                    rotation: 0
                                child_nodes: []
                      - props:
                          node_id: 134217807
                          test_id: beard-supported-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 2496
                              y: 42
                            bounding_box:
                              left: 2496
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 2496
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217805
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 2496
                                  y: 42
                                bounding_box:
                                  left: 2496
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 2496
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217803
                                  test_id: ""
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 2496
                                      y: 42
                                    bounding_box:
                                      left: 2496
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 2496
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217799
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2496
                                          y: 42
                                        bounding_box:
                                          left: 2496
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2496
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217791
                                          test_id: tile-fallback-label-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 2496
                                              y: 42
                                            bounding_box:
                                              left: 2496
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 2496
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 134217789
                                              test_id: typography-test-id
                                              composable_type: Label
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ""
                                              text_layout:
                                                is_text_truncated: false
                                                num_lines: 1
                                                text_bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 0
                                                  height: 39
                                                is_rich_text_parent: ~
                                              layout:
                                                size:
                                                  height: 39
                                                  width: 0
                                                position:
                                                  x: 2688
                                                  y: 130.5
                                                bounding_box:
                                                  left: 2688
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                border_box:
                                                  left: 2688
                                                  top: 130.5
                                                  width: 0
                                                  height: 39
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 268435501
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2496
                                          y: 42
                                        bounding_box:
                                          left: 2496
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2496
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 82
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2496
                                              y: 42
                                            bounding_box:
                                              left: 2496
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2496
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 84
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2496
                                              y: 42
                                            bounding_box:
                                              left: 2496
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2496
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 86
                                  test_id: ~
                                  composable_type: Nothing
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 0
                                      width: 0
                                    position:
                                      x: 2496
                                      y: 42
                                    bounding_box:
                                      left: 2496
                                      top: 42
                                      width: 0
                                      height: 0
                                    border_box:
                                      left: 2496
                                      top: 42
                                      width: 0
                                      height: 0
                                    rotation: 0
                                child_nodes: []
              - props:
                  node_id: 10
                  test_id: ~
                  composable_type: Nothing
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 0
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    border_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 11
                  test_id: ~
                  composable_type: Row
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 1920
                    position:
                      x: 0
                      y: 150
                    bounding_box:
                      left: 0
                      top: 150
                      width: 1920
                      height: 0
                    border_box:
                      left: 0
                      top: 150
                      width: 1920
                      height: 0
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 12
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 0
                          y: 150
                        bounding_box:
                          left: 0
                          top: 150
                          width: 0
                          height: 0
                        border_box:
                          left: 0
                          top: 150
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 14
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 0
                              y: 150
                            bounding_box:
                              left: 0
                              top: 150
                              width: 0
                              height: 0
                            border_box:
                              left: 0
                              top: 150
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
                  - props:
                      node_id: 15
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 1920
                          y: 150
                        bounding_box:
                          left: 1920
                          top: 150
                          width: 0
                          height: 0
                        border_box:
                          left: 1920
                          top: 150
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 17
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 1920
                              y: 150
                            bounding_box:
                              left: 1920
                              top: 150
                              width: 0
                              height: 0
                            border_box:
                              left: 1920
                              top: 150
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
