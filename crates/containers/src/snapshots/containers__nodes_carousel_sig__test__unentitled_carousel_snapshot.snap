---
source: crates/containers/src/nodes_carousel_sig.rs
expression: tree
---
props:
  node_id: 0
  test_id: ~
  composable_type: Column
  is_focused: false
  is_focusable: false
  is_on_screen: true
  on_screen_amount: 1
  is_visible: true
  base_styles:
    background_color: ~
    opacity: ~
    border_color: ~
    border_width: ~
    border_radius: ~
  text: ~
  text_layout: ~
  layout:
    size:
      height: 258
      width: 1920
    position:
      x: 0
      y: 0
    bounding_box:
      left: 0
      top: 0
      width: 1920
      height: 258
    border_box:
      left: 0
      top: 0
      width: 1920
      height: 258
    rotation: 0
child_nodes:
  - props:
      node_id: 2
      test_id: ~
      composable_type: Column
      is_focused: false
      is_focusable: false
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color: ~
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: ~
      text: ~
      text_layout: ~
      layout:
        size:
          height: 258
          width: 1920
        position:
          x: 0
          y: 0
        bounding_box:
          left: 0
          top: 0
          width: 1920
          height: 258
        border_box:
          left: 0
          top: 0
          width: 1920
          height: 258
        rotation: 0
    child_nodes:
      - props:
          node_id: 3
          test_id: nodes-carousel
          composable_type: Column
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: 1
            border_color: ~
            border_width: ~
            border_radius: ~
          text: ~
          text_layout: ~
          layout:
            size:
              height: 258
              width: 1920
            position:
              x: 0
              y: 0
            bounding_box:
              left: 0
              top: 0
              width: 1920
              height: 258
            border_box:
              left: 0
              top: 0
              width: 1920
              height: 258
            rotation: 0
        child_nodes:
          - props:
              node_id: 5
              test_id: basic-carousel-header
              composable_type: Label
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: nodes-carousel-title
              text_layout:
                is_text_truncated: false
                num_lines: 1
                text_bounding_box:
                  left: 0
                  top: 0
                  width: 292.125
                  height: 33
                is_rich_text_parent: ~
              layout:
                size:
                  height: 42
                  width: 292.125
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 292.125
                  height: 42
                border_box:
                  left: 0
                  top: 0
                  width: 292.125
                  height: 42
                rotation: 0
            child_nodes: []
          - props:
              node_id: 6
              test_id: ~
              composable_type: Stack
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 216
                  width: 1920
                position:
                  x: 0
                  y: 42
                bounding_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 216
                border_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 216
                rotation: 0
            child_nodes:
              - props:
                  node_id: 7
                  test_id: item-list
                  composable_type: RowList
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 216
                      width: 1920
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 216
                    border_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 216
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 8
                      test_id: ~
                      composable_type: Nothing
                      is_focused: true
                      is_focusable: true
                      is_on_screen: true
                      on_screen_amount: 0.661157
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 216
                          width: 2904
                        position:
                          x: 0
                          y: 42
                        bounding_box:
                          left: 0
                          top: 42
                          width: 2904
                          height: 216
                        border_box:
                          left: 0
                          top: 42
                          width: 2904
                          height: 216
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 134217729
                          test_id: ~
                          composable_type: Column
                          is_focused: true
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 57
                              width: 384
                            position:
                              x: 0
                              y: 121.5
                            bounding_box:
                              left: 0
                              top: 121.5
                              width: 384
                              height: 57
                            border_box:
                              left: 0
                              top: 121.5
                              width: 384
                              height: 57
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217741
                              test_id: primary-button-test-id
                              composable_type: Row
                              is_focused: true
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color:
                                  r: 241
                                  g: 241
                                  b: 241
                                  a: 255
                                opacity: ~
                                border_color:
                                  r: 0
                                  g: 0
                                  b: 0
                                  a: 0
                                border_width: 0
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 57
                                  width: 384
                                position:
                                  x: 0
                                  y: 121.5
                                bounding_box:
                                  left: 0
                                  top: 121.5
                                  width: 384
                                  height: 57
                                border_box:
                                  left: 0
                                  top: 121.5
                                  width: 384
                                  height: 57
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217737
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 57
                                      width: 206.3175
                                    position:
                                      x: 88.84125
                                      y: 121.5
                                    bounding_box:
                                      left: 88.84125
                                      top: 121.5
                                      width: 206.3175
                                      height: 57
                                    border_box:
                                      left: 88.84125
                                      top: 121.5
                                      width: 206.3175
                                      height: 57
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217732
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 88.84125
                                          y: 121.5
                                        bounding_box:
                                          left: 88.84125
                                          top: 121.5
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 88.84125
                                          top: 121.5
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 18
                                      test_id: ~
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 31.25
                                          width: 158.3175
                                        position:
                                          x: 112.84125
                                          y: 134.375
                                        bounding_box:
                                          left: 112.84125
                                          top: 134.375
                                          width: 158.3175
                                          height: 31.25
                                        border_box:
                                          left: 112.84125
                                          top: 134.375
                                          width: 158.3175
                                          height: 31.25
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 20
                                          test_id: ~
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 31.25
                                              width: 158.3175
                                            position:
                                              x: 112.84125
                                              y: 134.375
                                            bounding_box:
                                              left: 112.84125
                                              top: 134.375
                                              width: 158.3175
                                              height: 31.25
                                            border_box:
                                              left: 112.84125
                                              top: 134.375
                                              width: 158.3175
                                              height: 31.25
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 21
                                              test_id: ~
                                              composable_type: ColumnForEach
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 31.25
                                                  width: 158.3175
                                                position:
                                                  x: 112.84125
                                                  y: 134.375
                                                bounding_box:
                                                  left: 112.84125
                                                  top: 134.375
                                                  width: 158.3175
                                                  height: 31.25
                                                border_box:
                                                  left: 112.84125
                                                  top: 134.375
                                                  width: 158.3175
                                                  height: 31.25
                                                rotation: 0
                                            child_nodes:
                                              - props:
                                                  node_id: 22
                                                  test_id: ~
                                                  composable_type: RowForEach
                                                  is_focused: false
                                                  is_focusable: false
                                                  is_on_screen: true
                                                  on_screen_amount: 1
                                                  is_visible: true
                                                  base_styles:
                                                    background_color: ~
                                                    opacity: ~
                                                    border_color: ~
                                                    border_width: ~
                                                    border_radius: ~
                                                  text: ~
                                                  text_layout: ~
                                                  layout:
                                                    size:
                                                      height: 31.25
                                                      width: 158.3175
                                                    position:
                                                      x: 112.84125
                                                      y: 134.375
                                                    bounding_box:
                                                      left: 112.84125
                                                      top: 134.375
                                                      width: 158.3175
                                                      height: 31.25
                                                    border_box:
                                                      left: 112.84125
                                                      top: 134.375
                                                      width: 158.3175
                                                      height: 31.25
                                                    rotation: 0
                                                child_nodes:
                                                  - props:
                                                      node_id: 23
                                                      test_id: typography-test-id
                                                      composable_type: RichTextLabel
                                                      is_focused: false
                                                      is_focusable: false
                                                      is_on_screen: true
                                                      on_screen_amount: 1
                                                      is_visible: true
                                                      base_styles:
                                                        background_color: ~
                                                        opacity: ~
                                                        border_color: ~
                                                        border_width: ~
                                                        border_radius: ~
                                                      text: Button text
                                                      text_layout:
                                                        is_text_truncated: false
                                                        num_lines: 1
                                                        text_bounding_box:
                                                          left: 0
                                                          top: 0
                                                          width: 158.3175
                                                          height: 31.25
                                                        is_rich_text_parent: true
                                                      layout:
                                                        size:
                                                          height: 31.25
                                                          width: 158.3175
                                                        position:
                                                          x: 112.84125
                                                          y: 134.375
                                                        bounding_box:
                                                          left: 112.84125
                                                          top: 134.375
                                                          width: 158.3175
                                                          height: 31.25
                                                        border_box:
                                                          left: 112.84125
                                                          top: 134.375
                                                          width: 158.3175
                                                          height: 31.25
                                                        rotation: 0
                                                    child_nodes: []
                      - props:
                          node_id: 24
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 420
                              y: 42
                            bounding_box:
                              left: 420
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 420
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 26
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 420
                                  y: 42
                                bounding_box:
                                  left: 420
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 420
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 27
                                  test_id: card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 420
                                      y: 42
                                    bounding_box:
                                      left: 420
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 420
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 28
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 420
                                          y: 42
                                        bounding_box:
                                          left: 420
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 420
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 30
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 420
                                              y: 42
                                            bounding_box:
                                              left: 420
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 420
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 31
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 12
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 216
                                                  width: 384
                                                position:
                                                  x: 420
                                                  y: 42
                                                bounding_box:
                                                  left: 420
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                border_box:
                                                  left: 420
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 32
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 420
                                          y: 42
                                        bounding_box:
                                          left: 420
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 420
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 34
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 420
                                              y: 42
                                            bounding_box:
                                              left: 420
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 420
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 36
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 420
                                              y: 42
                                            bounding_box:
                                              left: 420
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 420
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 37
                                  test_id: ~
                                  composable_type: Column
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 39
                                      width: 49.199997
                                    position:
                                      x: 420
                                      y: 219
                                    bounding_box:
                                      left: 420
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    border_box:
                                      left: 420
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 38
                                      test_id: entitlement-label-icon-test-id
                                      composable_type: Label
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: 
                                      text_layout:
                                        is_text_truncated: false
                                        num_lines: 1
                                        text_bounding_box:
                                          left: 0
                                          top: 0
                                          width: 34.199997
                                          height: 24
                                        is_rich_text_parent: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 34.199997
                                        position:
                                          x: 435
                                          y: 219
                                        bounding_box:
                                          left: 435
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        border_box:
                                          left: 435
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217753
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 840
                              y: 42
                            bounding_box:
                              left: 840
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 840
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217761
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 840
                                  y: 42
                                bounding_box:
                                  left: 840
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 840
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217757
                                  test_id: card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 840
                                      y: 42
                                    bounding_box:
                                      left: 840
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 840
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 268435472
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 840
                                          y: 42
                                        bounding_box:
                                          left: 840
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 840
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 39
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 840
                                              y: 42
                                            bounding_box:
                                              left: 840
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 840
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 40
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 12
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 216
                                                  width: 384
                                                position:
                                                  x: 840
                                                  y: 42
                                                bounding_box:
                                                  left: 840
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                border_box:
                                                  left: 840
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 41
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 840
                                          y: 42
                                        bounding_box:
                                          left: 840
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 840
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 43
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 840
                                              y: 42
                                            bounding_box:
                                              left: 840
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 840
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 45
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 840
                                              y: 42
                                            bounding_box:
                                              left: 840
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 840
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 46
                                  test_id: ~
                                  composable_type: Column
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 39
                                      width: 49.199997
                                    position:
                                      x: 840
                                      y: 219
                                    bounding_box:
                                      left: 840
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    border_box:
                                      left: 840
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 47
                                      test_id: entitlement-label-icon-test-id
                                      composable_type: Label
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: 
                                      text_layout:
                                        is_text_truncated: false
                                        num_lines: 1
                                        text_bounding_box:
                                          left: 0
                                          top: 0
                                          width: 34.199997
                                          height: 24
                                        is_rich_text_parent: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 34.199997
                                        position:
                                          x: 855
                                          y: 219
                                        bounding_box:
                                          left: 855
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        border_box:
                                          left: 855
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 48
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 1260
                              y: 42
                            bounding_box:
                              left: 1260
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 1260
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 50
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 1260
                                  y: 42
                                bounding_box:
                                  left: 1260
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 1260
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 51
                                  test_id: card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 1260
                                      y: 42
                                    bounding_box:
                                      left: 1260
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 1260
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 52
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1260
                                          y: 42
                                        bounding_box:
                                          left: 1260
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1260
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 54
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 1260
                                              y: 42
                                            bounding_box:
                                              left: 1260
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 1260
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 55
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 12
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 216
                                                  width: 384
                                                position:
                                                  x: 1260
                                                  y: 42
                                                bounding_box:
                                                  left: 1260
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                border_box:
                                                  left: 1260
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 56
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1260
                                          y: 42
                                        bounding_box:
                                          left: 1260
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1260
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 58
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1260
                                              y: 42
                                            bounding_box:
                                              left: 1260
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1260
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 60
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1260
                                              y: 42
                                            bounding_box:
                                              left: 1260
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1260
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 61
                                  test_id: ~
                                  composable_type: Column
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 39
                                      width: 49.199997
                                    position:
                                      x: 1260
                                      y: 219
                                    bounding_box:
                                      left: 1260
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    border_box:
                                      left: 1260
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 62
                                      test_id: entitlement-label-icon-test-id
                                      composable_type: Label
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: 
                                      text_layout:
                                        is_text_truncated: false
                                        num_lines: 1
                                        text_bounding_box:
                                          left: 0
                                          top: 0
                                          width: 34.199997
                                          height: 24
                                        is_rich_text_parent: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 34.199997
                                        position:
                                          x: 1275
                                          y: 219
                                        bounding_box:
                                          left: 1275
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        border_box:
                                          left: 1275
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217777
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 0.625
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 1680
                              y: 42
                            bounding_box:
                              left: 1680
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 1680
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217785
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 0.625
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 1680
                                  y: 42
                                bounding_box:
                                  left: 1680
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 1680
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217781
                                  test_id: card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 0.625
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 1680
                                      y: 42
                                    bounding_box:
                                      left: 1680
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 1680
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 268435491
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 0.625
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1680
                                          y: 42
                                        bounding_box:
                                          left: 1680
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1680
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217770
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 0.625
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 1680
                                              y: 42
                                            bounding_box:
                                              left: 1680
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 1680
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 268435475
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 0.625
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 12
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 216
                                                  width: 384
                                                position:
                                                  x: 1680
                                                  y: 42
                                                bounding_box:
                                                  left: 1680
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                border_box:
                                                  left: 1680
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 63
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 0.625
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 1680
                                          y: 42
                                        bounding_box:
                                          left: 1680
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 1680
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 65
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1680
                                              y: 42
                                            bounding_box:
                                              left: 1680
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1680
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 67
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 1680
                                              y: 42
                                            bounding_box:
                                              left: 1680
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 1680
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 68
                                  test_id: ~
                                  composable_type: Column
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 39
                                      width: 49.199997
                                    position:
                                      x: 1680
                                      y: 219
                                    bounding_box:
                                      left: 1680
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    border_box:
                                      left: 1680
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 69
                                      test_id: entitlement-label-icon-test-id
                                      composable_type: Label
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: 
                                      text_layout:
                                        is_text_truncated: false
                                        num_lines: 1
                                        text_bounding_box:
                                          left: 0
                                          top: 0
                                          width: 34.199997
                                          height: 24
                                        is_rich_text_parent: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 34.199997
                                        position:
                                          x: 1695
                                          y: 219
                                        bounding_box:
                                          left: 1695
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        border_box:
                                          left: 1695
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 70
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 2100
                              y: 42
                            bounding_box:
                              left: 2100
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 2100
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 72
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 2100
                                  y: 42
                                bounding_box:
                                  left: 2100
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 2100
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 73
                                  test_id: card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 2100
                                      y: 42
                                    bounding_box:
                                      left: 2100
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 2100
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 74
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2100
                                          y: 42
                                        bounding_box:
                                          left: 2100
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2100
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 76
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 2100
                                              y: 42
                                            bounding_box:
                                              left: 2100
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 2100
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 77
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 12
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 216
                                                  width: 384
                                                position:
                                                  x: 2100
                                                  y: 42
                                                bounding_box:
                                                  left: 2100
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                border_box:
                                                  left: 2100
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 78
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2100
                                          y: 42
                                        bounding_box:
                                          left: 2100
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2100
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 80
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2100
                                              y: 42
                                            bounding_box:
                                              left: 2100
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2100
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 82
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2100
                                              y: 42
                                            bounding_box:
                                              left: 2100
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2100
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 83
                                  test_id: ~
                                  composable_type: Column
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 39
                                      width: 49.199997
                                    position:
                                      x: 2100
                                      y: 219
                                    bounding_box:
                                      left: 2100
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    border_box:
                                      left: 2100
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 84
                                      test_id: entitlement-label-icon-test-id
                                      composable_type: Label
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: 
                                      text_layout:
                                        is_text_truncated: false
                                        num_lines: 1
                                        text_bounding_box:
                                          left: 0
                                          top: 0
                                          width: 34.199997
                                          height: 24
                                        is_rich_text_parent: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 34.199997
                                        position:
                                          x: 2115
                                          y: 219
                                        bounding_box:
                                          left: 2115
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        border_box:
                                          left: 2115
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217799
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 216
                              width: 384
                            position:
                              x: 2520
                              y: 42
                            bounding_box:
                              left: 2520
                              top: 42
                              width: 384
                              height: 216
                            border_box:
                              left: 2520
                              top: 42
                              width: 384
                              height: 216
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217807
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 216
                                  width: 384
                                position:
                                  x: 2520
                                  y: 42
                                bounding_box:
                                  left: 2520
                                  top: 42
                                  width: 384
                                  height: 216
                                border_box:
                                  left: 2520
                                  top: 42
                                  width: 384
                                  height: 216
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217803
                                  test_id: card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 216
                                      width: 384
                                    position:
                                      x: 2520
                                      y: 42
                                    bounding_box:
                                      left: 2520
                                      top: 42
                                      width: 384
                                      height: 216
                                    border_box:
                                      left: 2520
                                      top: 42
                                      width: 384
                                      height: 216
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 268435515
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2520
                                          y: 42
                                        bounding_box:
                                          left: 2520
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2520
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217792
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 216
                                              width: 384
                                            position:
                                              x: 2520
                                              y: 42
                                            bounding_box:
                                              left: 2520
                                              top: 42
                                              width: 384
                                              height: 216
                                            border_box:
                                              left: 2520
                                              top: 42
                                              width: 384
                                              height: 216
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 268435500
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: false
                                              on_screen_amount: 0
                                              is_visible: false
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 12
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 216
                                                  width: 384
                                                position:
                                                  x: 2520
                                                  y: 42
                                                bounding_box:
                                                  left: 2520
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                border_box:
                                                  left: 2520
                                                  top: 42
                                                  width: 384
                                                  height: 216
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 85
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 216
                                          width: 384
                                        position:
                                          x: 2520
                                          y: 42
                                        bounding_box:
                                          left: 2520
                                          top: 42
                                          width: 384
                                          height: 216
                                        border_box:
                                          left: 2520
                                          top: 42
                                          width: 384
                                          height: 216
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 87
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2520
                                              y: 42
                                            bounding_box:
                                              left: 2520
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2520
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 89
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 2520
                                              y: 42
                                            bounding_box:
                                              left: 2520
                                              top: 42
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 2520
                                              top: 42
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 90
                                  test_id: ~
                                  composable_type: Column
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 39
                                      width: 49.199997
                                    position:
                                      x: 2520
                                      y: 219
                                    bounding_box:
                                      left: 2520
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    border_box:
                                      left: 2520
                                      top: 219
                                      width: 49.199997
                                      height: 39
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 91
                                      test_id: entitlement-label-icon-test-id
                                      composable_type: Label
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: 
                                      text_layout:
                                        is_text_truncated: false
                                        num_lines: 1
                                        text_bounding_box:
                                          left: 0
                                          top: 0
                                          width: 34.199997
                                          height: 24
                                        is_rich_text_parent: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 34.199997
                                        position:
                                          x: 2535
                                          y: 219
                                        bounding_box:
                                          left: 2535
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        border_box:
                                          left: 2535
                                          top: 219
                                          width: 34.199997
                                          height: 24
                                        rotation: 0
                                    child_nodes: []
              - props:
                  node_id: 10
                  test_id: ~
                  composable_type: Nothing
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 0
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    border_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 11
                  test_id: ~
                  composable_type: Row
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 1920
                    position:
                      x: 0
                      y: 150
                    bounding_box:
                      left: 0
                      top: 150
                      width: 1920
                      height: 0
                    border_box:
                      left: 0
                      top: 150
                      width: 1920
                      height: 0
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 12
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 0
                          y: 150
                        bounding_box:
                          left: 0
                          top: 150
                          width: 0
                          height: 0
                        border_box:
                          left: 0
                          top: 150
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 14
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 0
                              y: 150
                            bounding_box:
                              left: 0
                              top: 150
                              width: 0
                              height: 0
                            border_box:
                              left: 0
                              top: 150
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
                  - props:
                      node_id: 15
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 1920
                          y: 150
                        bounding_box:
                          left: 1920
                          top: 150
                          width: 0
                          height: 0
                        border_box:
                          left: 1920
                          top: 150
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 17
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 1920
                              y: 150
                            bounding_box:
                              left: 1920
                              top: 150
                              width: 0
                              height: 0
                            border_box:
                              left: 1920
                              top: 150
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
