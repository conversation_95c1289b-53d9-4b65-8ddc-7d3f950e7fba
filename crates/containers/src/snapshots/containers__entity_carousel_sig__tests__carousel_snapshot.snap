---
source: crates/containers/src/entity_carousel_sig.rs
assertion_line: 626
expression: tree
---
props:
  node_id: 0
  test_id: ~
  composable_type: Column
  is_focused: false
  is_focusable: false
  is_on_screen: true
  on_screen_amount: 1
  is_visible: true
  base_styles:
    background_color: ~
    opacity: ~
    border_color: ~
    border_width: ~
    border_radius: ~
  text: ~
  text_layout: ~
  layout:
    size:
      height: 254
      width: 1920
    position:
      x: 0
      y: 0
    bounding_box:
      left: 0
      top: 0
      width: 1920
      height: 254
    border_box:
      left: 0
      top: 0
      width: 1920
      height: 254
    rotation: 0
child_nodes:
  - props:
      node_id: 2
      test_id: entity-carousel
      composable_type: Column
      is_focused: false
      is_focusable: false
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color: ~
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: ~
      text: ~
      text_layout: ~
      layout:
        size:
          height: 254
          width: 1920
        position:
          x: 0
          y: 0
        bounding_box:
          left: 0
          top: 0
          width: 1920
          height: 254
        border_box:
          left: 0
          top: 0
          width: 1920
          height: 254
        rotation: 0
    child_nodes:
      - props:
          node_id: 3
          test_id: basic-carousel
          composable_type: Column
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: 1
            border_color: ~
            border_width: ~
            border_radius: ~
          text: ~
          text_layout: ~
          layout:
            size:
              height: 254
              width: 1920
            position:
              x: 0
              y: 0
            bounding_box:
              left: 0
              top: 0
              width: 1920
              height: 254
            border_box:
              left: 0
              top: 0
              width: 1920
              height: 254
            rotation: 0
        child_nodes:
          - props:
              node_id: 5
              test_id: ~
              composable_type: Nothing
              is_focused: false
              is_focusable: false
              is_on_screen: false
              on_screen_amount: 0
              is_visible: false
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 0
                  width: 0
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 0
                  height: 0
                border_box:
                  left: 0
                  top: 0
                  width: 0
                  height: 0
                rotation: 0
            child_nodes: []
          - props:
              node_id: 6
              test_id: ~
              composable_type: Stack
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 254
                  width: 1920
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 1920
                  height: 254
                border_box:
                  left: 0
                  top: 0
                  width: 1920
                  height: 254
                rotation: 0
            child_nodes:
              - props:
                  node_id: 7
                  test_id: item-list
                  composable_type: RowList
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 254
                      width: 1152
                    position:
                      x: 0
                      y: 0
                    bounding_box:
                      left: 0
                      top: 0
                      width: 1152
                      height: 254
                    border_box:
                      left: 0
                      top: 0
                      width: 1152
                      height: 254
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 8
                      test_id: ~
                      composable_type: Nothing
                      is_focused: true
                      is_focusable: true
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 254
                          width: 1152
                        position:
                          x: 0
                          y: 0
                        bounding_box:
                          left: 0
                          top: 0
                          width: 1152
                          height: 254
                        border_box:
                          left: 0
                          top: 0
                          width: 1152
                          height: 254
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 134217729
                          test_id: ~
                          composable_type: Stack
                          is_focused: true
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 254
                              width: 192
                            position:
                              x: 0
                              y: 0
                            bounding_box:
                              left: 0
                              top: 0
                              width: 192
                              height: 254
                            border_box:
                              left: 0
                              top: 0
                              width: 192
                              height: 254
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217744
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 230
                                  width: 192
                                position:
                                  x: 0
                                  y: 0
                                bounding_box:
                                  left: 0
                                  top: 0
                                  width: 192
                                  height: 230
                                border_box:
                                  left: 0
                                  top: 0
                                  width: 192
                                  height: 230
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217741
                                  test_id: circular-card-test-id
                                  composable_type: Stack
                                  is_focused: true
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 0
                                      y: 0
                                    bounding_box:
                                      left: 0
                                      top: 0
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 0
                                      top: 0
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217737
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 0
                                          y: 0
                                        bounding_box:
                                          left: 0
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 0
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 18
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 0
                                              y: 0
                                            bounding_box:
                                              left: 0
                                              top: 0
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 0
                                              top: 0
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 19
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 96
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 192
                                                  width: 192
                                                position:
                                                  x: 0
                                                  y: 0
                                                bounding_box:
                                                  left: 0
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                border_box:
                                                  left: 0
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 20
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 0
                                          y: 0
                                        bounding_box:
                                          left: 0
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 0
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 22
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 0
                                              y: 0
                                            bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 24
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 0
                                              y: 0
                                            bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 25
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 38
                                      width: 192
                                    position:
                                      x: 0
                                      y: 192
                                    bounding_box:
                                      left: 0
                                      top: 192
                                      width: 192
                                      height: 38
                                    border_box:
                                      left: 0
                                      top: 192
                                      width: 192
                                      height: 38
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 27
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 0
                                          y: 192
                                        bounding_box:
                                          left: 0
                                          top: 192
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 0
                                          top: 192
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 29
                                      test_id: circular-card-title-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 192
                                        position:
                                          x: 0
                                          y: 206
                                        bounding_box:
                                          left: 0
                                          top: 206
                                          width: 192
                                          height: 24
                                        border_box:
                                          left: 0
                                          top: 206
                                          width: 192
                                          height: 24
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 30
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: Card 0 Title
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 140.22
                                              height: 24
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 24
                                              width: 140.22
                                            position:
                                              x: 25.89
                                              y: 206
                                            bounding_box:
                                              left: 25.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            border_box:
                                              left: 25.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            rotation: 0
                                        child_nodes: []
                      - props:
                          node_id: 31
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 254
                              width: 192
                            position:
                              x: 240
                              y: 0
                            bounding_box:
                              left: 240
                              top: 0
                              width: 192
                              height: 254
                            border_box:
                              left: 240
                              top: 0
                              width: 192
                              height: 254
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 32
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 230
                                  width: 192
                                position:
                                  x: 240
                                  y: 0
                                bounding_box:
                                  left: 240
                                  top: 0
                                  width: 192
                                  height: 230
                                border_box:
                                  left: 240
                                  top: 0
                                  width: 192
                                  height: 230
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 33
                                  test_id: circular-card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 240
                                      y: 0
                                    bounding_box:
                                      left: 240
                                      top: 0
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 240
                                      top: 0
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 34
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 240
                                          y: 0
                                        bounding_box:
                                          left: 240
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 240
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 36
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 240
                                              y: 0
                                            bounding_box:
                                              left: 240
                                              top: 0
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 240
                                              top: 0
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 37
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 96
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 192
                                                  width: 192
                                                position:
                                                  x: 240
                                                  y: 0
                                                bounding_box:
                                                  left: 240
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                border_box:
                                                  left: 240
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 38
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 240
                                          y: 0
                                        bounding_box:
                                          left: 240
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 240
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 40
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 240
                                              y: 0
                                            bounding_box:
                                              left: 240
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 240
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 42
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 240
                                              y: 0
                                            bounding_box:
                                              left: 240
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 240
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 43
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 38
                                      width: 192
                                    position:
                                      x: 240
                                      y: 192
                                    bounding_box:
                                      left: 240
                                      top: 192
                                      width: 192
                                      height: 38
                                    border_box:
                                      left: 240
                                      top: 192
                                      width: 192
                                      height: 38
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 45
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 240
                                          y: 192
                                        bounding_box:
                                          left: 240
                                          top: 192
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 240
                                          top: 192
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 47
                                      test_id: circular-card-title-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 192
                                        position:
                                          x: 240
                                          y: 206
                                        bounding_box:
                                          left: 240
                                          top: 206
                                          width: 192
                                          height: 24
                                        border_box:
                                          left: 240
                                          top: 206
                                          width: 192
                                          height: 24
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 48
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: Card 1 Title
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 140.22
                                              height: 24
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 24
                                              width: 140.22
                                            position:
                                              x: 265.89
                                              y: 206
                                            bounding_box:
                                              left: 265.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            border_box:
                                              left: 265.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            rotation: 0
                                        child_nodes: []
                      - props:
                          node_id: 134217774
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 254
                              width: 192
                            position:
                              x: 480
                              y: 0
                            bounding_box:
                              left: 480
                              top: 0
                              width: 192
                              height: 254
                            border_box:
                              left: 480
                              top: 0
                              width: 192
                              height: 254
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217772
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 230
                                  width: 192
                                position:
                                  x: 480
                                  y: 0
                                bounding_box:
                                  left: 480
                                  top: 0
                                  width: 192
                                  height: 230
                                border_box:
                                  left: 480
                                  top: 0
                                  width: 192
                                  height: 230
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217769
                                  test_id: circular-card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 480
                                      y: 0
                                    bounding_box:
                                      left: 480
                                      top: 0
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 480
                                      top: 0
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217767
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 480
                                          y: 0
                                        bounding_box:
                                          left: 480
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 480
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217756
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 480
                                              y: 0
                                            bounding_box:
                                              left: 480
                                              top: 0
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 480
                                              top: 0
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 134217754
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 96
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 192
                                                  width: 192
                                                position:
                                                  x: 480
                                                  y: 0
                                                bounding_box:
                                                  left: 480
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                border_box:
                                                  left: 480
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 134217751
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 480
                                          y: 0
                                        bounding_box:
                                          left: 480
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 480
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 268435460
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 480
                                              y: 0
                                            bounding_box:
                                              left: 480
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 480
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 50
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 480
                                              y: 0
                                            bounding_box:
                                              left: 480
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 480
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 51
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 38
                                      width: 192
                                    position:
                                      x: 480
                                      y: 192
                                    bounding_box:
                                      left: 480
                                      top: 192
                                      width: 192
                                      height: 38
                                    border_box:
                                      left: 480
                                      top: 192
                                      width: 192
                                      height: 38
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 53
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 480
                                          y: 192
                                        bounding_box:
                                          left: 480
                                          top: 192
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 480
                                          top: 192
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 55
                                      test_id: circular-card-title-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 192
                                        position:
                                          x: 480
                                          y: 206
                                        bounding_box:
                                          left: 480
                                          top: 206
                                          width: 192
                                          height: 24
                                        border_box:
                                          left: 480
                                          top: 206
                                          width: 192
                                          height: 24
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 56
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: Card 2 Title
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 140.22
                                              height: 24
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 24
                                              width: 140.22
                                            position:
                                              x: 505.89
                                              y: 206
                                            bounding_box:
                                              left: 505.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            border_box:
                                              left: 505.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            rotation: 0
                                        child_nodes: []
                      - props:
                          node_id: 57
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 254
                              width: 192
                            position:
                              x: 720
                              y: 0
                            bounding_box:
                              left: 720
                              top: 0
                              width: 192
                              height: 254
                            border_box:
                              left: 720
                              top: 0
                              width: 192
                              height: 254
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 58
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 230
                                  width: 192
                                position:
                                  x: 720
                                  y: 0
                                bounding_box:
                                  left: 720
                                  top: 0
                                  width: 192
                                  height: 230
                                border_box:
                                  left: 720
                                  top: 0
                                  width: 192
                                  height: 230
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 59
                                  test_id: circular-card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 720
                                      y: 0
                                    bounding_box:
                                      left: 720
                                      top: 0
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 720
                                      top: 0
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 60
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 720
                                          y: 0
                                        bounding_box:
                                          left: 720
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 720
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 62
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 720
                                              y: 0
                                            bounding_box:
                                              left: 720
                                              top: 0
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 720
                                              top: 0
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 63
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 96
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 192
                                                  width: 192
                                                position:
                                                  x: 720
                                                  y: 0
                                                bounding_box:
                                                  left: 720
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                border_box:
                                                  left: 720
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 64
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 720
                                          y: 0
                                        bounding_box:
                                          left: 720
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 720
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 66
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 720
                                              y: 0
                                            bounding_box:
                                              left: 720
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 720
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 68
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 720
                                              y: 0
                                            bounding_box:
                                              left: 720
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 720
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 69
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 38
                                      width: 192
                                    position:
                                      x: 720
                                      y: 192
                                    bounding_box:
                                      left: 720
                                      top: 192
                                      width: 192
                                      height: 38
                                    border_box:
                                      left: 720
                                      top: 192
                                      width: 192
                                      height: 38
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 71
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 720
                                          y: 192
                                        bounding_box:
                                          left: 720
                                          top: 192
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 720
                                          top: 192
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 73
                                      test_id: circular-card-title-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 192
                                        position:
                                          x: 720
                                          y: 206
                                        bounding_box:
                                          left: 720
                                          top: 206
                                          width: 192
                                          height: 24
                                        border_box:
                                          left: 720
                                          top: 206
                                          width: 192
                                          height: 24
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 74
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: Card 3 Title
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 140.22
                                              height: 24
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 24
                                              width: 140.22
                                            position:
                                              x: 745.89
                                              y: 206
                                            bounding_box:
                                              left: 745.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            border_box:
                                              left: 745.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            rotation: 0
                                        child_nodes: []
                      - props:
                          node_id: 134217800
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 254
                              width: 192
                            position:
                              x: 960
                              y: 0
                            bounding_box:
                              left: 960
                              top: 0
                              width: 192
                              height: 254
                            border_box:
                              left: 960
                              top: 0
                              width: 192
                              height: 254
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217798
                              test_id: ~
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 230
                                  width: 192
                                position:
                                  x: 960
                                  y: 0
                                bounding_box:
                                  left: 960
                                  top: 0
                                  width: 192
                                  height: 230
                                border_box:
                                  left: 960
                                  top: 0
                                  width: 192
                                  height: 230
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217795
                                  test_id: circular-card-test-id
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 960
                                      y: 0
                                    bounding_box:
                                      left: 960
                                      top: 0
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 960
                                      top: 0
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217793
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 25
                                          g: 30
                                          b: 37
                                          a: 255
                                        opacity: ~
                                        border_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        border_width: 0
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 960
                                          y: 0
                                        bounding_box:
                                          left: 960
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 960
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217782
                                          test_id: collaged-image-wrapper-test-id
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: 1
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 960
                                              y: 0
                                            bounding_box:
                                              left: 960
                                              top: 0
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 960
                                              top: 0
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 134217780
                                              test_id: collaged-image-test-id
                                              composable_type: Image
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: 96
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 192
                                                  width: 192
                                                position:
                                                  x: 960
                                                  y: 0
                                                bounding_box:
                                                  left: 960
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                border_box:
                                                  left: 960
                                                  top: 0
                                                  width: 192
                                                  height: 192
                                                rotation: 0
                                            child_nodes: []
                                  - props:
                                      node_id: 134217777
                                      test_id: ~
                                      composable_type: Stack
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 960
                                          y: 0
                                        bounding_box:
                                          left: 960
                                          top: 0
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 960
                                          top: 0
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 268435491
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 960
                                              y: 0
                                            bounding_box:
                                              left: 960
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 960
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                                      - props:
                                          node_id: 76
                                          test_id: ~
                                          composable_type: Nothing
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 0
                                              width: 0
                                            position:
                                              x: 960
                                              y: 0
                                            bounding_box:
                                              left: 960
                                              top: 0
                                              width: 0
                                              height: 0
                                            border_box:
                                              left: 960
                                              top: 0
                                              width: 0
                                              height: 0
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 77
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 38
                                      width: 192
                                    position:
                                      x: 960
                                      y: 192
                                    bounding_box:
                                      left: 960
                                      top: 192
                                      width: 192
                                      height: 38
                                    border_box:
                                      left: 960
                                      top: 192
                                      width: 192
                                      height: 38
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 79
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 960
                                          y: 192
                                        bounding_box:
                                          left: 960
                                          top: 192
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 960
                                          top: 192
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 81
                                      test_id: circular-card-title-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 192
                                        position:
                                          x: 960
                                          y: 206
                                        bounding_box:
                                          left: 960
                                          top: 206
                                          width: 192
                                          height: 24
                                        border_box:
                                          left: 960
                                          top: 206
                                          width: 192
                                          height: 24
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 82
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: Card 4 Title
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 140.22
                                              height: 24
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 24
                                              width: 140.22
                                            position:
                                              x: 985.89
                                              y: 206
                                            bounding_box:
                                              left: 985.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            border_box:
                                              left: 985.89
                                              top: 206
                                              width: 140.22
                                              height: 24
                                            rotation: 0
                                        child_nodes: []
              - props:
                  node_id: 10
                  test_id: ~
                  composable_type: Nothing
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 0
                    position:
                      x: 0
                      y: 0
                    bounding_box:
                      left: 0
                      top: 0
                      width: 0
                      height: 0
                    border_box:
                      left: 0
                      top: 0
                      width: 0
                      height: 0
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 11
                  test_id: ~
                  composable_type: Row
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 1920
                    position:
                      x: 0
                      y: 127
                    bounding_box:
                      left: 0
                      top: 127
                      width: 1920
                      height: 0
                    border_box:
                      left: 0
                      top: 127
                      width: 1920
                      height: 0
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 12
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 0
                          y: 127
                        bounding_box:
                          left: 0
                          top: 127
                          width: 0
                          height: 0
                        border_box:
                          left: 0
                          top: 127
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 14
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 0
                              y: 127
                            bounding_box:
                              left: 0
                              top: 127
                              width: 0
                              height: 0
                            border_box:
                              left: 0
                              top: 127
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
                  - props:
                      node_id: 15
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 1920
                          y: 127
                        bounding_box:
                          left: 1920
                          top: 127
                          width: 0
                          height: 0
                        border_box:
                          left: 1920
                          top: 127
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 17
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 1920
                              y: 127
                            bounding_box:
                              left: 1920
                              top: 127
                              width: 0
                              height: 0
                            border_box:
                              left: 1920
                              top: 127
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
