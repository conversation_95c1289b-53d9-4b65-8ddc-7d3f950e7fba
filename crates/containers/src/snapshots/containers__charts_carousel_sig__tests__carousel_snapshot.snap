---
source: crates/containers/src/charts_carousel_sig.rs
expression: tree
---
props:
  node_id: 0
  test_id: ~
  composable_type: Column
  is_focused: false
  is_focusable: false
  is_on_screen: true
  on_screen_amount: 1
  is_visible: true
  base_styles:
    background_color: ~
    opacity: ~
    border_color: ~
    border_width: ~
    border_radius: ~
  text: ~
  text_layout: ~
  layout:
    size:
      height: 231
      width: 1920
    position:
      x: 0
      y: 0
    bounding_box:
      left: 0
      top: 0
      width: 1920
      height: 231
    border_box:
      left: 0
      top: 0
      width: 1920
      height: 231
    rotation: 0
child_nodes:
  - props:
      node_id: 2
      test_id: charts-carousel
      composable_type: Column
      is_focused: false
      is_focusable: false
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color: ~
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: ~
      text: ~
      text_layout: ~
      layout:
        size:
          height: 231
          width: 1920
        position:
          x: 0
          y: 0
        bounding_box:
          left: 0
          top: 0
          width: 1920
          height: 231
        border_box:
          left: 0
          top: 0
          width: 1920
          height: 231
        rotation: 0
    child_nodes:
      - props:
          node_id: 3
          test_id: basic-carousel
          composable_type: Column
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: 1
            border_color: ~
            border_width: ~
            border_radius: ~
          text: ~
          text_layout: ~
          layout:
            size:
              height: 231
              width: 1920
            position:
              x: 0
              y: 0
            bounding_box:
              left: 0
              top: 0
              width: 1920
              height: 231
            border_box:
              left: 0
              top: 0
              width: 1920
              height: 231
            rotation: 0
        child_nodes:
          - props:
              node_id: 5
              test_id: basic-carousel-header
              composable_type: Label
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: Carousel Title
              text_layout:
                is_text_truncated: false
                num_lines: 1
                text_bounding_box:
                  left: 0
                  top: 0
                  width: 204.4875
                  height: 33
                is_rich_text_parent: ~
              layout:
                size:
                  height: 42
                  width: 204.4875
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 204.4875
                  height: 42
                border_box:
                  left: 0
                  top: 0
                  width: 204.4875
                  height: 42
                rotation: 0
            child_nodes: []
          - props:
              node_id: 6
              test_id: ~
              composable_type: Stack
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 189
                  width: 1920
                position:
                  x: 0
                  y: 42
                bounding_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 189
                border_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 189
                rotation: 0
            child_nodes:
              - props:
                  node_id: 7
                  test_id: item-list
                  composable_type: RowList
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 189
                      width: 1920
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 189
                    border_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 189
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 8
                      test_id: ~
                      composable_type: Nothing
                      is_focused: true
                      is_focusable: true
                      is_on_screen: true
                      on_screen_amount: 0.627451
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 189
                          width: 3060
                        position:
                          x: 0
                          y: 42
                        bounding_box:
                          left: 0
                          top: 42
                          width: 3060
                          height: 189
                        border_box:
                          left: 0
                          top: 42
                          width: 3060
                          height: 189
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 134217729
                          test_id: chart-card-test-id
                          composable_type: Stack
                          is_focused: true
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 189
                              width: 480
                            position:
                              x: 0
                              y: 42
                            bounding_box:
                              left: 0
                              top: 42
                              width: 480
                              height: 189
                            border_box:
                              left: 0
                              top: 42
                              width: 480
                              height: 189
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217744
                              test_id: chart-rank-test-id
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: 1
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 144
                                position:
                                  x: 0
                                  y: 42
                                bounding_box:
                                  left: 0
                                  top: 42
                                  width: 144
                                  height: 189
                                border_box:
                                  left: 0
                                  top: 42
                                  width: 144
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217741
                                  test_id: lrc-image-test-id
                                  composable_type: Image
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 128
                                      width: 144
                                    position:
                                      x: 0
                                      y: 72.5
                                    bounding_box:
                                      left: 0
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    border_box:
                                      left: 0
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    rotation: 0
                                child_nodes: []
                          - props:
                              node_id: 134217737
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: true
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 336
                                position:
                                  x: 144
                                  y: 42
                                bounding_box:
                                  left: 144
                                  top: 42
                                  width: 336
                                  height: 189
                                border_box:
                                  left: 144
                                  top: 42
                                  width: 336
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217732
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 144
                                      y: 42
                                    bounding_box:
                                      left: 144
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 144
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 19
                                      test_id: tile-fallback-label-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 189
                                          width: 336
                                        position:
                                          x: 144
                                          y: 42
                                        bounding_box:
                                          left: 144
                                          top: 42
                                          width: 336
                                          height: 189
                                        border_box:
                                          left: 144
                                          top: 42
                                          width: 336
                                          height: 189
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 20
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ""
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 33
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 33
                                              width: 0
                                            position:
                                              x: 312
                                              y: 120
                                            bounding_box:
                                              left: 312
                                              top: 120
                                              width: 0
                                              height: 33
                                            border_box:
                                              left: 312
                                              top: 120
                                              width: 0
                                              height: 33
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 21
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 144
                                      y: 42
                                    bounding_box:
                                      left: 144
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 144
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 23
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 144
                                          y: 42
                                        bounding_box:
                                          left: 144
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 144
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 25
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 144
                                          y: 42
                                        bounding_box:
                                          left: 144
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 144
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 26
                          test_id: chart-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 189
                              width: 480
                            position:
                              x: 516
                              y: 42
                            bounding_box:
                              left: 516
                              top: 42
                              width: 480
                              height: 189
                            border_box:
                              left: 516
                              top: 42
                              width: 480
                              height: 189
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 27
                              test_id: chart-rank-test-id
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: 0.5
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 144
                                position:
                                  x: 534
                                  y: 42
                                bounding_box:
                                  left: 534
                                  top: 42
                                  width: 144
                                  height: 189
                                border_box:
                                  left: 534
                                  top: 42
                                  width: 144
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 28
                                  test_id: lrc-image-test-id
                                  composable_type: Image
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 128
                                      width: 144
                                    position:
                                      x: 534
                                      y: 72.5
                                    bounding_box:
                                      left: 534
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    border_box:
                                      left: 534
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    rotation: 0
                                child_nodes: []
                          - props:
                              node_id: 29
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 336
                                position:
                                  x: 660
                                  y: 42
                                bounding_box:
                                  left: 660
                                  top: 42
                                  width: 336
                                  height: 189
                                border_box:
                                  left: 660
                                  top: 42
                                  width: 336
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 30
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 660
                                      y: 42
                                    bounding_box:
                                      left: 660
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 660
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 32
                                      test_id: tile-fallback-label-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 189
                                          width: 336
                                        position:
                                          x: 660
                                          y: 42
                                        bounding_box:
                                          left: 660
                                          top: 42
                                          width: 336
                                          height: 189
                                        border_box:
                                          left: 660
                                          top: 42
                                          width: 336
                                          height: 189
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 33
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ""
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 33
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 33
                                              width: 0
                                            position:
                                              x: 828
                                              y: 120
                                            bounding_box:
                                              left: 828
                                              top: 120
                                              width: 0
                                              height: 33
                                            border_box:
                                              left: 828
                                              top: 120
                                              width: 0
                                              height: 33
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 34
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 660
                                      y: 42
                                    bounding_box:
                                      left: 660
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 660
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 36
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 660
                                          y: 42
                                        bounding_box:
                                          left: 660
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 660
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 38
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 660
                                          y: 42
                                        bounding_box:
                                          left: 660
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 660
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217765
                          test_id: chart-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 189
                              width: 480
                            position:
                              x: 1032
                              y: 42
                            bounding_box:
                              left: 1032
                              top: 42
                              width: 480
                              height: 189
                            border_box:
                              left: 1032
                              top: 42
                              width: 480
                              height: 189
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217763
                              test_id: chart-rank-test-id
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: 0.5
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 144
                                position:
                                  x: 1050
                                  y: 42
                                bounding_box:
                                  left: 1050
                                  top: 42
                                  width: 144
                                  height: 189
                                border_box:
                                  left: 1050
                                  top: 42
                                  width: 144
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217759
                                  test_id: lrc-image-test-id
                                  composable_type: Image
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 128
                                      width: 144
                                    position:
                                      x: 1050
                                      y: 72.5
                                    bounding_box:
                                      left: 1050
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    border_box:
                                      left: 1050
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    rotation: 0
                                child_nodes: []
                          - props:
                              node_id: 134217752
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 336
                                position:
                                  x: 1176
                                  y: 42
                                bounding_box:
                                  left: 1176
                                  top: 42
                                  width: 336
                                  height: 189
                                border_box:
                                  left: 1176
                                  top: 42
                                  width: 336
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217750
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 1176
                                      y: 42
                                    bounding_box:
                                      left: 1176
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 1176
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 39
                                      test_id: tile-fallback-label-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 189
                                          width: 336
                                        position:
                                          x: 1176
                                          y: 42
                                        bounding_box:
                                          left: 1176
                                          top: 42
                                          width: 336
                                          height: 189
                                        border_box:
                                          left: 1176
                                          top: 42
                                          width: 336
                                          height: 189
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 40
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ""
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 33
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 33
                                              width: 0
                                            position:
                                              x: 1344
                                              y: 120
                                            bounding_box:
                                              left: 1344
                                              top: 120
                                              width: 0
                                              height: 33
                                            border_box:
                                              left: 1344
                                              top: 120
                                              width: 0
                                              height: 33
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 41
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 1176
                                      y: 42
                                    bounding_box:
                                      left: 1176
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 1176
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 43
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1176
                                          y: 42
                                        bounding_box:
                                          left: 1176
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1176
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 45
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1176
                                          y: 42
                                        bounding_box:
                                          left: 1176
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1176
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 46
                          test_id: chart-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 0.775
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 189
                              width: 480
                            position:
                              x: 1548
                              y: 42
                            bounding_box:
                              left: 1548
                              top: 42
                              width: 480
                              height: 189
                            border_box:
                              left: 1548
                              top: 42
                              width: 480
                              height: 189
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 47
                              test_id: chart-rank-test-id
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: 0.5
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 144
                                position:
                                  x: 1566
                                  y: 42
                                bounding_box:
                                  left: 1566
                                  top: 42
                                  width: 144
                                  height: 189
                                border_box:
                                  left: 1566
                                  top: 42
                                  width: 144
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 48
                                  test_id: lrc-image-test-id
                                  composable_type: Image
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 128
                                      width: 144
                                    position:
                                      x: 1566
                                      y: 72.5
                                    bounding_box:
                                      left: 1566
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    border_box:
                                      left: 1566
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    rotation: 0
                                child_nodes: []
                          - props:
                              node_id: 49
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 0.6785714
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 336
                                position:
                                  x: 1692
                                  y: 42
                                bounding_box:
                                  left: 1692
                                  top: 42
                                  width: 336
                                  height: 189
                                border_box:
                                  left: 1692
                                  top: 42
                                  width: 336
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 50
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 0.6785714
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 1692
                                      y: 42
                                    bounding_box:
                                      left: 1692
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 1692
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 52
                                      test_id: tile-fallback-label-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 0.6785714
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 189
                                          width: 336
                                        position:
                                          x: 1692
                                          y: 42
                                        bounding_box:
                                          left: 1692
                                          top: 42
                                          width: 336
                                          height: 189
                                        border_box:
                                          left: 1692
                                          top: 42
                                          width: 336
                                          height: 189
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 53
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ""
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 33
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 33
                                              width: 0
                                            position:
                                              x: 1860
                                              y: 120
                                            bounding_box:
                                              left: 1860
                                              top: 120
                                              width: 0
                                              height: 33
                                            border_box:
                                              left: 1860
                                              top: 120
                                              width: 0
                                              height: 33
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 54
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 0.6785714
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 1692
                                      y: 42
                                    bounding_box:
                                      left: 1692
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 1692
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 56
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1692
                                          y: 42
                                        bounding_box:
                                          left: 1692
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1692
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 58
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1692
                                          y: 42
                                        bounding_box:
                                          left: 1692
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1692
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217785
                          test_id: chart-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 189
                              width: 480
                            position:
                              x: 2064
                              y: 42
                            bounding_box:
                              left: 2064
                              top: 42
                              width: 480
                              height: 189
                            border_box:
                              left: 2064
                              top: 42
                              width: 480
                              height: 189
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217783
                              test_id: chart-rank-test-id
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: 0.5
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 144
                                position:
                                  x: 2082
                                  y: 42
                                bounding_box:
                                  left: 2082
                                  top: 42
                                  width: 144
                                  height: 189
                                border_box:
                                  left: 2082
                                  top: 42
                                  width: 144
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217779
                                  test_id: lrc-image-test-id
                                  composable_type: Image
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 128
                                      width: 144
                                    position:
                                      x: 2082
                                      y: 72.5
                                    bounding_box:
                                      left: 2082
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    border_box:
                                      left: 2082
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    rotation: 0
                                child_nodes: []
                          - props:
                              node_id: 134217772
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 336
                                position:
                                  x: 2208
                                  y: 42
                                bounding_box:
                                  left: 2208
                                  top: 42
                                  width: 336
                                  height: 189
                                border_box:
                                  left: 2208
                                  top: 42
                                  width: 336
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217770
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 2208
                                      y: 42
                                    bounding_box:
                                      left: 2208
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 2208
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 59
                                      test_id: tile-fallback-label-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 189
                                          width: 336
                                        position:
                                          x: 2208
                                          y: 42
                                        bounding_box:
                                          left: 2208
                                          top: 42
                                          width: 336
                                          height: 189
                                        border_box:
                                          left: 2208
                                          top: 42
                                          width: 336
                                          height: 189
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 60
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ""
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 33
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 33
                                              width: 0
                                            position:
                                              x: 2376
                                              y: 120
                                            bounding_box:
                                              left: 2376
                                              top: 120
                                              width: 0
                                              height: 33
                                            border_box:
                                              left: 2376
                                              top: 120
                                              width: 0
                                              height: 33
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 61
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 2208
                                      y: 42
                                    bounding_box:
                                      left: 2208
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 2208
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 63
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2208
                                          y: 42
                                        bounding_box:
                                          left: 2208
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2208
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 65
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2208
                                          y: 42
                                        bounding_box:
                                          left: 2208
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2208
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 66
                          test_id: chart-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 189
                              width: 480
                            position:
                              x: 2580
                              y: 42
                            bounding_box:
                              left: 2580
                              top: 42
                              width: 480
                              height: 189
                            border_box:
                              left: 2580
                              top: 42
                              width: 480
                              height: 189
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 67
                              test_id: chart-rank-test-id
                              composable_type: Column
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: 0.5
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 144
                                position:
                                  x: 2598
                                  y: 42
                                bounding_box:
                                  left: 2598
                                  top: 42
                                  width: 144
                                  height: 189
                                border_box:
                                  left: 2598
                                  top: 42
                                  width: 144
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 68
                                  test_id: lrc-image-test-id
                                  composable_type: Image
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 128
                                      width: 144
                                    position:
                                      x: 2598
                                      y: 72.5
                                    bounding_box:
                                      left: 2598
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    border_box:
                                      left: 2598
                                      top: 72.5
                                      width: 144
                                      height: 128
                                    rotation: 0
                                child_nodes: []
                          - props:
                              node_id: 69
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 189
                                  width: 336
                                position:
                                  x: 2724
                                  y: 42
                                bounding_box:
                                  left: 2724
                                  top: 42
                                  width: 336
                                  height: 189
                                border_box:
                                  left: 2724
                                  top: 42
                                  width: 336
                                  height: 189
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 70
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 2724
                                      y: 42
                                    bounding_box:
                                      left: 2724
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 2724
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 72
                                      test_id: tile-fallback-label-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 189
                                          width: 336
                                        position:
                                          x: 2724
                                          y: 42
                                        bounding_box:
                                          left: 2724
                                          top: 42
                                          width: 336
                                          height: 189
                                        border_box:
                                          left: 2724
                                          top: 42
                                          width: 336
                                          height: 189
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 73
                                          test_id: typography-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ""
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 33
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 33
                                              width: 0
                                            position:
                                              x: 2892
                                              y: 120
                                            bounding_box:
                                              left: 2892
                                              top: 120
                                              width: 0
                                              height: 33
                                            border_box:
                                              left: 2892
                                              top: 120
                                              width: 0
                                              height: 33
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 74
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 189
                                      width: 336
                                    position:
                                      x: 2724
                                      y: 42
                                    bounding_box:
                                      left: 2724
                                      top: 42
                                      width: 336
                                      height: 189
                                    border_box:
                                      left: 2724
                                      top: 42
                                      width: 336
                                      height: 189
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 76
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2724
                                          y: 42
                                        bounding_box:
                                          left: 2724
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2724
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 78
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2724
                                          y: 42
                                        bounding_box:
                                          left: 2724
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2724
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
              - props:
                  node_id: 10
                  test_id: ~
                  composable_type: Nothing
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 0
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    border_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 11
                  test_id: ~
                  composable_type: Row
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 1920
                    position:
                      x: 0
                      y: 136.5
                    bounding_box:
                      left: 0
                      top: 136.5
                      width: 1920
                      height: 0
                    border_box:
                      left: 0
                      top: 136.5
                      width: 1920
                      height: 0
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 12
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 0
                          y: 136.5
                        bounding_box:
                          left: 0
                          top: 136.5
                          width: 0
                          height: 0
                        border_box:
                          left: 0
                          top: 136.5
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 14
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 0
                              y: 136.5
                            bounding_box:
                              left: 0
                              top: 136.5
                              width: 0
                              height: 0
                            border_box:
                              left: 0
                              top: 136.5
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
                  - props:
                      node_id: 15
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 1920
                          y: 136.5
                        bounding_box:
                          left: 1920
                          top: 136.5
                          width: 0
                          height: 0
                        border_box:
                          left: 1920
                          top: 136.5
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 17
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 1920
                              y: 136.5
                            bounding_box:
                              left: 1920
                              top: 136.5
                              width: 0
                              height: 0
                            border_box:
                              left: 1920
                              top: 136.5
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
