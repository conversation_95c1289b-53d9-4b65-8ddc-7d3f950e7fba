---
source: crates/containers/src/promotional_banner.rs
expression: tree
---
props:
  node_id: 0
  test_id: ~
  composable_type: Column
  is_focused: false
  is_focusable: false
  is_on_screen: true
  on_screen_amount: 1
  is_visible: true
  base_styles:
    background_color: ~
    opacity: ~
    border_color: ~
    border_width: ~
    border_radius: ~
  text: ~
  text_layout: ~
  layout:
    size:
      height: 680
      width: 1920
    position:
      x: 0
      y: 0
    bounding_box:
      left: 0
      top: 0
      width: 1920
      height: 680
    border_box:
      left: 0
      top: 0
      width: 1920
      height: 680
    rotation: 0
child_nodes:
  - props:
      node_id: 1
      test_id: promo-banner
      composable_type: Column
      is_focused: true
      is_focusable: true
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color: ~
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: ~
      text: ~
      text_layout: ~
      layout:
        size:
          height: 600
          width: 1920
        position:
          x: 0
          y: 0
        bounding_box:
          left: 0
          top: 0
          width: 1920
          height: 600
        border_box:
          left: 0
          top: 0
          width: 1920
          height: 600
        rotation: 0
    child_nodes:
      - props:
          node_id: 2
          test_id: ~
          composable_type: Stack
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: ~
            border_color: ~
            border_width: ~
            border_radius: ~
          text: ~
          text_layout: ~
          layout:
            size:
              height: 600
              width: 1920
            position:
              x: 0
              y: 0
            bounding_box:
              left: 0
              top: 0
              width: 1920
              height: 600
            border_box:
              left: 0
              top: 0
              width: 1920
              height: 600
            rotation: 0
        child_nodes:
          - props:
              node_id: 3
              test_id: promo-banner-background
              composable_type: Stack
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 0.9739583
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 600
                  width: 1920
                position:
                  x: -50
                  y: 0
                bounding_box:
                  left: -50
                  top: 0
                  width: 1920
                  height: 600
                border_box:
                  left: -50
                  top: 0
                  width: 1920
                  height: 600
                rotation: 0
            child_nodes:
              - props:
                  node_id: 4
                  test_id: lrc-image-test-id
                  composable_type: Image
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 0.9739583
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 600
                      width: 1920
                    position:
                      x: -50
                      y: 0
                    bounding_box:
                      left: -50
                      top: 0
                      width: 1920
                      height: 600
                    border_box:
                      left: -50
                      top: 0
                      width: 1920
                      height: 600
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 5
                  test_id: lrc-image-test-id
                  composable_type: Image
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 0.9739583
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 600
                      width: 1920
                    position:
                      x: -50
                      y: 0
                    bounding_box:
                      left: -50
                      top: 0
                      width: 1920
                      height: 600
                    border_box:
                      left: -50
                      top: 0
                      width: 1920
                      height: 600
                    rotation: 0
                child_nodes: []
          - props:
              node_id: 6
              test_id: ~
              composable_type: Row
              is_focused: true
              is_focusable: true
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 600
                  width: 1920
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 1920
                  height: 600
                border_box:
                  left: 0
                  top: 0
                  width: 1920
                  height: 600
                rotation: 0
            child_nodes:
              - props:
                  node_id: 7
                  test_id: promo-banner-left-column
                  composable_type: Column
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 460
                      width: 650
                    position:
                      x: 0
                      y: 70
                    bounding_box:
                      left: 0
                      top: 70
                      width: 650
                      height: 460
                    border_box:
                      left: 0
                      top: 70
                      width: 650
                      height: 460
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 8
                      test_id: title-row
                      composable_type: Column
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: 1
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 87
                          width: 591.79675
                        position:
                          x: 0
                          y: 70
                        bounding_box:
                          left: 0
                          top: 70
                          width: 591.79675
                          height: 87
                        border_box:
                          left: 0
                          top: 70
                          width: 591.79675
                          height: 87
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 10
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 0
                              y: 70
                            bounding_box:
                              left: 0
                              top: 70
                              width: 0
                              height: 0
                            border_box:
                              left: 0
                              top: 70
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
                      - props:
                          node_id: 12
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 66
                              width: 591.79675
                            position:
                              x: 0
                              y: 70
                            bounding_box:
                              left: 0
                              top: 70
                              width: 591.79675
                              height: 66
                            border_box:
                              left: 0
                              top: 70
                              width: 591.79675
                              height: 66
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 13
                              test_id: ~
                              composable_type: Row
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 66
                                  width: 591.79675
                                position:
                                  x: 0
                                  y: 70
                                bounding_box:
                                  left: 0
                                  top: 70
                                  width: 591.79675
                                  height: 66
                                border_box:
                                  left: 0
                                  top: 70
                                  width: 591.79675
                                  height: 66
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 14
                                  test_id: title-text-test-id
                                  composable_type: Label
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: promo banner title
                                  text_layout:
                                    is_text_truncated: false
                                    num_lines: 1
                                    text_bounding_box:
                                      left: 0
                                      top: 0
                                      width: 591.79675
                                      height: 66
                                    is_rich_text_parent: ~
                                  layout:
                                    size:
                                      height: 66
                                      width: 591.79675
                                    position:
                                      x: 0
                                      y: 70
                                    bounding_box:
                                      left: 0
                                      top: 70
                                      width: 591.79675
                                      height: 66
                                    border_box:
                                      left: 0
                                      top: 70
                                      width: 591.79675
                                      height: 66
                                    rotation: 0
                                child_nodes: []
                  - props:
                      node_id: 15
                      test_id: hvm-row
                      composable_type: RowForEach
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 49
                          width: 233.7
                        position:
                          x: 0
                          y: 157
                        bounding_box:
                          left: 0
                          top: 157
                          width: 233.7
                          height: 49
                        border_box:
                          left: 0
                          top: 157
                          width: 233.7
                          height: 49
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 16
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 33
                              width: 233.7
                            position:
                              x: 0
                              y: 157
                            bounding_box:
                              left: 0
                              top: 157
                              width: 233.7
                              height: 33
                            border_box:
                              left: 0
                              top: 157
                              width: 233.7
                              height: 33
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 18
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 33
                                  width: 233.7
                                position:
                                  x: 0
                                  y: 157
                                bounding_box:
                                  left: 0
                                  top: 157
                                  width: 233.7
                                  height: 33
                                border_box:
                                  left: 0
                                  top: 157
                                  width: 233.7
                                  height: 33
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 19
                                  test_id: hvm-label-test-id
                                  composable_type: Label
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: promo banner HVM
                                  text_layout:
                                    is_text_truncated: false
                                    num_lines: 1
                                    text_bounding_box:
                                      left: 0
                                      top: 0
                                      width: 233.7
                                      height: 33
                                    is_rich_text_parent: ~
                                  layout:
                                    size:
                                      height: 33
                                      width: 233.7
                                    position:
                                      x: 0
                                      y: 157
                                    bounding_box:
                                      left: 0
                                      top: 157
                                      width: 233.7
                                      height: 33
                                    border_box:
                                      left: 0
                                      top: 157
                                      width: 233.7
                                      height: 33
                                    rotation: 0
                                child_nodes: []
                  - props:
                      node_id: 20
                      test_id: event-row
                      composable_type: RowForEach
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 52
                          width: 64.74
                        position:
                          x: 0
                          y: 206
                        bounding_box:
                          left: 0
                          top: 206
                          width: 64.74
                          height: 52
                        border_box:
                          left: 0
                          top: 206
                          width: 64.74
                          height: 52
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 21
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 36
                              width: 64.74
                            position:
                              x: 0
                              y: 206
                            bounding_box:
                              left: 0
                              top: 206
                              width: 64.74
                              height: 36
                            border_box:
                              left: 0
                              top: 206
                              width: 64.74
                              height: 36
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 23
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 36
                                  width: 64.74
                                position:
                                  x: 0
                                  y: 206
                                bounding_box:
                                  left: 0
                                  top: 206
                                  width: 64.74
                                  height: 36
                                border_box:
                                  left: 0
                                  top: 206
                                  width: 64.74
                                  height: 36
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 24
                                  test_id: liveliness-badge-test-id
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 235
                                      g: 0
                                      b: 23
                                      a: 255
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 8
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 36
                                      width: 64.74
                                    position:
                                      x: 0
                                      y: 206
                                    bounding_box:
                                      left: 0
                                      top: 206
                                      width: 64.74
                                      height: 36
                                    border_box:
                                      left: 0
                                      top: 206
                                      width: 64.74
                                      height: 36
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 25
                                      test_id: typography-test-id
                                      composable_type: Label
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: LIVE
                                      text_layout:
                                        is_text_truncated: false
                                        num_lines: 1
                                        text_bounding_box:
                                          left: 0
                                          top: 0
                                          width: 46.739998
                                          height: 24
                                        is_rich_text_parent: ~
                                      layout:
                                        size:
                                          height: 24
                                          width: 46.739998
                                        position:
                                          x: 9
                                          y: 212
                                        bounding_box:
                                          left: 9
                                          top: 212
                                          width: 46.739998
                                          height: 24
                                        border_box:
                                          left: 9
                                          top: 212
                                          width: 46.739998
                                          height: 24
                                        rotation: 0
                                    child_nodes: []
                  - props:
                      node_id: 26
                      test_id: genre-row
                      composable_type: RowForEach
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 49
                          width: 284.715
                        position:
                          x: 0
                          y: 258
                        bounding_box:
                          left: 0
                          top: 258
                          width: 284.715
                          height: 49
                        border_box:
                          left: 0
                          top: 258
                          width: 284.715
                          height: 49
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 27
                          test_id: ~
                          composable_type: Stack
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 33
                              width: 284.715
                            position:
                              x: 0
                              y: 258
                            bounding_box:
                              left: 0
                              top: 258
                              width: 284.715
                              height: 33
                            border_box:
                              left: 0
                              top: 258
                              width: 284.715
                              height: 33
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 29
                              test_id: ~
                              composable_type: Stack
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 33
                                  width: 284.715
                                position:
                                  x: 0
                                  y: 258
                                bounding_box:
                                  left: 0
                                  top: 258
                                  width: 284.715
                                  height: 33
                                border_box:
                                  left: 0
                                  top: 258
                                  width: 284.715
                                  height: 33
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 30
                                  test_id: genres-list-label-test-id
                                  composable_type: Label
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: Adventure • Horror
                                  text_layout:
                                    is_text_truncated: false
                                    num_lines: 1
                                    text_bounding_box:
                                      left: 0
                                      top: 0
                                      width: 284.715
                                      height: 33
                                    is_rich_text_parent: ~
                                  layout:
                                    size:
                                      height: 33
                                      width: 284.715
                                    position:
                                      x: 0
                                      y: 258
                                    bounding_box:
                                      left: 0
                                      top: 258
                                      width: 284.715
                                      height: 33
                                    border_box:
                                      left: 0
                                      top: 258
                                      width: 284.715
                                      height: 33
                                    rotation: 0
                                child_nodes: []
                  - props:
                      node_id: 32
                      test_id: synopsis-row
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 52
                          width: 298.95074
                        position:
                          x: 0
                          y: 307
                        bounding_box:
                          left: 0
                          top: 307
                          width: 298.95074
                          height: 52
                        border_box:
                          left: 0
                          top: 307
                          width: 298.95074
                          height: 52
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 33
                          test_id: typography-test-id
                          composable_type: Label
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: promo banner synopsis
                          text_layout:
                            is_text_truncated: false
                            num_lines: 1
                            text_bounding_box:
                              left: 0
                              top: 0
                              width: 298.95074
                              height: 36
                            is_rich_text_parent: ~
                          layout:
                            size:
                              height: 36
                              width: 298.95074
                            position:
                              x: 0
                              y: 307
                            bounding_box:
                              left: 0
                              top: 307
                              width: 298.95074
                              height: 36
                            border_box:
                              left: 0
                              top: 307
                              width: 298.95074
                              height: 36
                            rotation: 0
                        child_nodes: []
                  - props:
                      node_id: 34
                      test_id: buttons-row
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 110
                          width: 333
                        position:
                          x: 0
                          y: 359
                        bounding_box:
                          left: 0
                          top: 359
                          width: 333
                          height: 110
                        border_box:
                          left: 0
                          top: 359
                          width: 333
                          height: 110
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 36
                          test_id: ~
                          composable_type: Row
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 57
                              width: 240
                            position:
                              x: 0
                              y: 404.5
                            bounding_box:
                              left: 0
                              top: 404.5
                              width: 240
                              height: 57
                            border_box:
                              left: 0
                              top: 404.5
                              width: 240
                              height: 57
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 37
                              test_id: primary-button
                              composable_type: Row
                              is_focused: true
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color:
                                  r: 241
                                  g: 241
                                  b: 241
                                  a: 255
                                opacity: ~
                                border_color:
                                  r: 0
                                  g: 0
                                  b: 0
                                  a: 0
                                border_width: 0
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 57
                                  width: 240
                                position:
                                  x: 0
                                  y: 404.5
                                bounding_box:
                                  left: 0
                                  top: 404.5
                                  width: 240
                                  height: 57
                                border_box:
                                  left: 0
                                  top: 404.5
                                  width: 240
                                  height: 57
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 38
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 57
                                      width: 177.5325
                                    position:
                                      x: 0
                                      y: 404.5
                                    bounding_box:
                                      left: 0
                                      top: 404.5
                                      width: 177.5325
                                      height: 57
                                    border_box:
                                      left: 0
                                      top: 404.5
                                      width: 177.5325
                                      height: 57
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 39
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 0
                                          y: 404.5
                                        bounding_box:
                                          left: 0
                                          top: 404.5
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 0
                                          top: 404.5
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 40
                                      test_id: ~
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 31.25
                                          width: 129.5325
                                        position:
                                          x: 24
                                          y: 417.375
                                        bounding_box:
                                          left: 24
                                          top: 417.375
                                          width: 129.5325
                                          height: 31.25
                                        border_box:
                                          left: 24
                                          top: 417.375
                                          width: 129.5325
                                          height: 31.25
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 42
                                          test_id: ~
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 31.25
                                              width: 129.5325
                                            position:
                                              x: 24
                                              y: 417.375
                                            bounding_box:
                                              left: 24
                                              top: 417.375
                                              width: 129.5325
                                              height: 31.25
                                            border_box:
                                              left: 24
                                              top: 417.375
                                              width: 129.5325
                                              height: 31.25
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 43
                                              test_id: ~
                                              composable_type: ColumnForEach
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 31.25
                                                  width: 129.5325
                                                position:
                                                  x: 24
                                                  y: 417.375
                                                bounding_box:
                                                  left: 24
                                                  top: 417.375
                                                  width: 129.5325
                                                  height: 31.25
                                                border_box:
                                                  left: 24
                                                  top: 417.375
                                                  width: 129.5325
                                                  height: 31.25
                                                rotation: 0
                                            child_nodes:
                                              - props:
                                                  node_id: 44
                                                  test_id: ~
                                                  composable_type: RowForEach
                                                  is_focused: false
                                                  is_focusable: false
                                                  is_on_screen: true
                                                  on_screen_amount: 1
                                                  is_visible: true
                                                  base_styles:
                                                    background_color: ~
                                                    opacity: ~
                                                    border_color: ~
                                                    border_width: ~
                                                    border_radius: ~
                                                  text: ~
                                                  text_layout: ~
                                                  layout:
                                                    size:
                                                      height: 31.25
                                                      width: 129.5325
                                                    position:
                                                      x: 24
                                                      y: 417.375
                                                    bounding_box:
                                                      left: 24
                                                      top: 417.375
                                                      width: 129.5325
                                                      height: 31.25
                                                    border_box:
                                                      left: 24
                                                      top: 417.375
                                                      width: 129.5325
                                                      height: 31.25
                                                    rotation: 0
                                                child_nodes:
                                                  - props:
                                                      node_id: 45
                                                      test_id: typography-test-id
                                                      composable_type: RichTextLabel
                                                      is_focused: false
                                                      is_focusable: false
                                                      is_on_screen: true
                                                      on_screen_amount: 1
                                                      is_visible: true
                                                      base_styles:
                                                        background_color: ~
                                                        opacity: ~
                                                        border_color: ~
                                                        border_width: ~
                                                        border_radius: ~
                                                      text: Watch now
                                                      text_layout:
                                                        is_text_truncated: false
                                                        num_lines: 1
                                                        text_bounding_box:
                                                          left: 0
                                                          top: 0
                                                          width: 129.5325
                                                          height: 31.25
                                                        is_rich_text_parent: true
                                                      layout:
                                                        size:
                                                          height: 31.25
                                                          width: 129.5325
                                                        position:
                                                          x: 24
                                                          y: 417.375
                                                        bounding_box:
                                                          left: 24
                                                          top: 417.375
                                                          width: 129.5325
                                                          height: 31.25
                                                        border_box:
                                                          left: 24
                                                          top: 417.375
                                                          width: 129.5325
                                                          height: 31.25
                                                        rotation: 0
                                                    child_nodes: []
                      - props:
                          node_id: 47
                          test_id: ~
                          composable_type: RowList
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 72
                              width: 72
                            position:
                              x: 261
                              y: 397
                            bounding_box:
                              left: 261
                              top: 397
                              width: 72
                              height: 72
                            border_box:
                              left: 261
                              top: 397
                              width: 72
                              height: 72
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 48
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 72
                                  width: 72
                                position:
                                  x: 261
                                  y: 397
                                bounding_box:
                                  left: 261
                                  top: 397
                                  width: 72
                                  height: 72
                                border_box:
                                  left: 261
                                  top: 397
                                  width: 72
                                  height: 72
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217792
                                  test_id: watchlist-button
                                  composable_type: Column
                                  is_focused: false
                                  is_focusable: true
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 72
                                      width: 72
                                    position:
                                      x: 261
                                      y: 397
                                    bounding_box:
                                      left: 261
                                      top: 397
                                      width: 72
                                      height: 72
                                    border_box:
                                      left: 261
                                      top: 397
                                      width: 72
                                      height: 72
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217789
                                      test_id: label-placeholder-test-id
                                      composable_type: Column
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 297
                                          y: 358
                                        bounding_box:
                                          left: 297
                                          top: 358
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 297
                                          top: 358
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217777
                                          test_id: icon-button-label-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ""
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 0
                                              height: 30
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 30
                                              width: 0
                                            position:
                                              x: 297
                                              y: 358
                                            bounding_box:
                                              left: 297
                                              top: 358
                                              width: 0
                                              height: 30
                                            border_box:
                                              left: 297
                                              top: 358
                                              width: 0
                                              height: 30
                                            rotation: 0
                                        child_nodes: []
                                  - props:
                                      node_id: 134217784
                                      test_id: icon-button-icon-section-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color:
                                          r: 0
                                          g: 0
                                          b: 0
                                          a: 0
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 10000
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 72
                                          width: 72
                                        position:
                                          x: 261
                                          y: 397
                                        bounding_box:
                                          left: 261
                                          top: 397
                                          width: 72
                                          height: 72
                                        border_box:
                                          left: 261
                                          top: 397
                                          width: 72
                                          height: 72
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217782
                                          test_id: font-icon-test-id
                                          composable_type: Label
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: 
                                          text_layout:
                                            is_text_truncated: false
                                            num_lines: 1
                                            text_bounding_box:
                                              left: 0
                                              top: 0
                                              width: 54.72
                                              height: 40
                                            is_rich_text_parent: ~
                                          layout:
                                            size:
                                              height: 40
                                              width: 54.72
                                            position:
                                              x: 269.64
                                              y: 413
                                            bounding_box:
                                              left: 269.64
                                              top: 413
                                              width: 54.72
                                              height: 40
                                            border_box:
                                              left: 269.64
                                              top: 413
                                              width: 54.72
                                              height: 40
                                            rotation: 0
                                        child_nodes: []
                  - props:
                      node_id: 50
                      test_id: entitlement-label-row
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 61
                          width: 341.658
                        position:
                          x: 0
                          y: 469
                        bounding_box:
                          left: 0
                          top: 469
                          width: 341.658
                          height: 61
                        border_box:
                          left: 0
                          top: 469
                          width: 341.658
                          height: 61
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 51
                          test_id: ~
                          composable_type: Row
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 24
                              width: 341.658
                            position:
                              x: 0
                              y: 490
                            bounding_box:
                              left: 0
                              top: 490
                              width: 341.658
                              height: 24
                            border_box:
                              left: 0
                              top: 490
                              width: 341.658
                              height: 24
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 53
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 0
                                  y: 490
                                bounding_box:
                                  left: 0
                                  top: 490
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 0
                                  top: 490
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                          - props:
                              node_id: 55
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 0
                                  y: 490
                                bounding_box:
                                  left: 0
                                  top: 490
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 0
                                  top: 490
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                          - props:
                              node_id: 57
                              test_id: entitlement-label-text-test-id
                              composable_type: Label
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: promo banner entitlement label
                              text_layout:
                                is_text_truncated: false
                                num_lines: 1
                                text_bounding_box:
                                  left: 0
                                  top: 0
                                  width: 341.658
                                  height: 24
                                is_rich_text_parent: ~
                              layout:
                                size:
                                  height: 24
                                  width: 341.658
                                position:
                                  x: 0
                                  y: 490
                                bounding_box:
                                  left: 0
                                  top: 490
                                  width: 341.658
                                  height: 24
                                border_box:
                                  left: 0
                                  top: 490
                                  width: 341.658
                                  height: 24
                                rotation: 0
                            child_nodes: []
              - props:
                  node_id: 58
                  test_id: ~
                  composable_type: Column
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 430
                    position:
                      x: 650
                      y: 300
                    bounding_box:
                      left: 650
                      top: 300
                      width: 430
                      height: 0
                    border_box:
                      left: 650
                      top: 300
                      width: 430
                      height: 0
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 59
                  test_id: promo-banner-right-column
                  composable_type: Column
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 510
                      width: 600
                    position:
                      x: 1080
                      y: 45
                    bounding_box:
                      left: 1080
                      top: 45
                      width: 600
                      height: 510
                    border_box:
                      left: 1080
                      top: 45
                      width: 600
                      height: 510
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 60
                      test_id: regulatory-row
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 36
                          width: 169.165
                        position:
                          x: 1510.835
                          y: 486
                        bounding_box:
                          left: 1510.835
                          top: 486
                          width: 169.165
                          height: 36
                        border_box:
                          left: 1510.835
                          top: 486
                          width: 169.165
                          height: 36
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 62
                          test_id: maturity-rating
                          composable_type: Stack
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 36
                              width: 36
                            position:
                              x: 1510.835
                              y: 486
                            bounding_box:
                              left: 1510.835
                              top: 486
                              width: 36
                              height: 36
                            border_box:
                              left: 1510.835
                              top: 486
                              width: 36
                              height: 36
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 63
                              test_id: maturity-rating-image
                              composable_type: Image
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 36
                                  width: 36
                                position:
                                  x: 1510.835
                                  y: 486
                                bounding_box:
                                  left: 1510.835
                                  top: 486
                                  width: 36
                                  height: 36
                                border_box:
                                  left: 1510.835
                                  top: 486
                                  width: 36
                                  height: 36
                                rotation: 0
                            child_nodes: []
                      - props:
                          node_id: 65
                          test_id: regulatory-label
                          composable_type: Stack
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 36
                              width: 133.165
                            position:
                              x: 1546.835
                              y: 486
                            bounding_box:
                              left: 1546.835
                              top: 486
                              width: 133.165
                              height: 36
                            border_box:
                              left: 1546.835
                              top: 486
                              width: 133.165
                              height: 36
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 66
                              test_id: regulatory-label-text
                              composable_type: Row
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color:
                                  r: 51
                                  g: 55
                                  b: 61
                                  a: 255
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 8
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 36
                                  width: 123.16499
                                position:
                                  x: 1556.835
                                  y: 486
                                bounding_box:
                                  left: 1556.835
                                  top: 486
                                  width: 123.16499
                                  height: 36
                                border_box:
                                  left: 1556.835
                                  top: 486
                                  width: 123.16499
                                  height: 36
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 67
                                  test_id: typography-test-id
                                  composable_type: Label
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: SPONSORED
                                  text_layout:
                                    is_text_truncated: false
                                    num_lines: 1
                                    text_bounding_box:
                                      left: 0
                                      top: 0
                                      width: 105.16499
                                      height: 24
                                    is_rich_text_parent: ~
                                  layout:
                                    size:
                                      height: 24
                                      width: 105.16499
                                    position:
                                      x: 1565.835
                                      y: 492
                                    bounding_box:
                                      left: 1565.835
                                      top: 492
                                      width: 105.16499
                                      height: 24
                                    border_box:
                                      left: 1565.835
                                      top: 492
                                      width: 105.16499
                                      height: 24
                                    rotation: 0
                                child_nodes: []
                  - props:
                      node_id: 68
                      test_id: promo-banner-legal-text
                      composable_type: ColumnList
                      is_focused: false
                      is_focusable: false
                      is_on_screen: true
                      on_screen_amount: 1
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 33
                          width: 148.05179
                        position:
                          x: 1531.9482
                          y: 522
                        bounding_box:
                          left: 1531.9482
                          top: 522
                          width: 148.05179
                          height: 33
                        border_box:
                          left: 1531.9482
                          top: 522
                          width: 148.05179
                          height: 33
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 69
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 24
                              width: 148.05179
                            position:
                              x: 1531.9482
                              y: 531
                            bounding_box:
                              left: 1531.9482
                              top: 531
                              width: 148.05179
                              height: 24
                            border_box:
                              left: 1531.9482
                              top: 531
                              width: 148.05179
                              height: 24
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217780
                              test_id: legal-message-0
                              composable_type: Label
                              is_focused: false
                              is_focusable: false
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: legal message
                              text_layout:
                                is_text_truncated: false
                                num_lines: 1
                                text_bounding_box:
                                  left: 0
                                  top: 0
                                  width: 148.05179
                                  height: 24
                                is_rich_text_parent: ~
                              layout:
                                size:
                                  height: 24
                                  width: 148.05179
                                position:
                                  x: 1531.9482
                                  y: 531
                                bounding_box:
                                  left: 1531.9482
                                  top: 531
                                  width: 148.05179
                                  height: 24
                                border_box:
                                  left: 1531.9482
                                  top: 531
                                  width: 148.05179
                                  height: 24
                                rotation: 0
                            child_nodes: []
  - props:
      node_id: 70
      test_id: other_button
      composable_type: Button
      is_focused: false
      is_focusable: true
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color:
          r: 128
          g: 128
          b: 128
          a: 255
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: 12
      text: ~
      text_layout: ~
      layout:
        size:
          height: 80
          width: 567.2
        position:
          x: 0
          y: 600
        bounding_box:
          left: 0
          top: 600
          width: 567.2
          height: 80
        border_box:
          left: 0
          top: 600
          width: 567.2
          height: 80
        rotation: 0
    child_nodes:
      - props:
          node_id: 71
          test_id: ~
          composable_type: Nothing
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: ~
            border_color: ~
            border_width: ~
            border_radius: ~
          text: Other content button
          text_layout:
            is_text_truncated: false
            num_lines: 1
            text_bounding_box:
              left: 0
              top: 0
              width: 547.2
              height: 60
            is_rich_text_parent: ~
          layout:
            size:
              height: 60
              width: 547.2
            position:
              x: 10
              y: 610
            bounding_box:
              left: 10
              top: 610
              width: 547.2
              height: 60
            border_box:
              left: 10
              top: 610
              width: 547.2
              height: 60
            rotation: 0
        child_nodes: []
