---
source: crates/containers/src/nodes_carousel_sig.rs
expression: tree
---
props:
  node_id: 0
  test_id: ~
  composable_type: Column
  is_focused: false
  is_focusable: false
  is_on_screen: true
  on_screen_amount: 1
  is_visible: true
  base_styles:
    background_color: ~
    opacity: ~
    border_color: ~
    border_width: ~
    border_radius: ~
  text: ~
  text_layout: ~
  layout:
    size:
      height: 234
      width: 1920
    position:
      x: 0
      y: 0
    bounding_box:
      left: 0
      top: 0
      width: 1920
      height: 234
    border_box:
      left: 0
      top: 0
      width: 1920
      height: 234
    rotation: 0
child_nodes:
  - props:
      node_id: 2
      test_id: ~
      composable_type: Column
      is_focused: false
      is_focusable: false
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color: ~
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: ~
      text: ~
      text_layout: ~
      layout:
        size:
          height: 234
          width: 1920
        position:
          x: 0
          y: 0
        bounding_box:
          left: 0
          top: 0
          width: 1920
          height: 234
        border_box:
          left: 0
          top: 0
          width: 1920
          height: 234
        rotation: 0
    child_nodes:
      - props:
          node_id: 3
          test_id: nodes-carousel
          composable_type: Column
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: 1
            border_color: ~
            border_width: ~
            border_radius: ~
          text: ~
          text_layout: ~
          layout:
            size:
              height: 234
              width: 1920
            position:
              x: 0
              y: 0
            bounding_box:
              left: 0
              top: 0
              width: 1920
              height: 234
            border_box:
              left: 0
              top: 0
              width: 1920
              height: 234
            rotation: 0
        child_nodes:
          - props:
              node_id: 5
              test_id: basic-carousel-header
              composable_type: Label
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: nodes-carousel-title
              text_layout:
                is_text_truncated: false
                num_lines: 1
                text_bounding_box:
                  left: 0
                  top: 0
                  width: 292.125
                  height: 33
                is_rich_text_parent: ~
              layout:
                size:
                  height: 42
                  width: 292.125
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 292.125
                  height: 42
                border_box:
                  left: 0
                  top: 0
                  width: 292.125
                  height: 42
                rotation: 0
            child_nodes: []
          - props:
              node_id: 6
              test_id: ~
              composable_type: Stack
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 192
                  width: 1920
                position:
                  x: 0
                  y: 42
                bounding_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 192
                border_box:
                  left: 0
                  top: 42
                  width: 1920
                  height: 192
                rotation: 0
            child_nodes:
              - props:
                  node_id: 7
                  test_id: item-list
                  composable_type: RowList
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 192
                      width: 1920
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 192
                    border_box:
                      left: 0
                      top: 42
                      width: 1920
                      height: 192
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 8
                      test_id: ~
                      composable_type: Nothing
                      is_focused: true
                      is_focusable: true
                      is_on_screen: true
                      on_screen_amount: 0.754717
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 192
                          width: 2544
                        position:
                          x: 0
                          y: 42
                        bounding_box:
                          left: 0
                          top: 42
                          width: 2544
                          height: 192
                        border_box:
                          left: 0
                          top: 42
                          width: 2544
                          height: 192
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 134217729
                          test_id: ~
                          composable_type: Column
                          is_focused: true
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 57
                              width: 384
                            position:
                              x: 0
                              y: 109.5
                            bounding_box:
                              left: 0
                              top: 109.5
                              width: 384
                              height: 57
                            border_box:
                              left: 0
                              top: 109.5
                              width: 384
                              height: 57
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217741
                              test_id: primary-button-test-id
                              composable_type: Row
                              is_focused: true
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color:
                                  r: 241
                                  g: 241
                                  b: 241
                                  a: 255
                                opacity: ~
                                border_color:
                                  r: 0
                                  g: 0
                                  b: 0
                                  a: 0
                                border_width: 0
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 57
                                  width: 384
                                position:
                                  x: 0
                                  y: 109.5
                                bounding_box:
                                  left: 0
                                  top: 109.5
                                  width: 384
                                  height: 57
                                border_box:
                                  left: 0
                                  top: 109.5
                                  width: 384
                                  height: 57
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217737
                                  test_id: ~
                                  composable_type: Row
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: ~
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 57
                                      width: 206.3175
                                    position:
                                      x: 88.84125
                                      y: 109.5
                                    bounding_box:
                                      left: 88.84125
                                      top: 109.5
                                      width: 206.3175
                                      height: 57
                                    border_box:
                                      left: 88.84125
                                      top: 109.5
                                      width: 206.3175
                                      height: 57
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217732
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 88.84125
                                          y: 109.5
                                        bounding_box:
                                          left: 88.84125
                                          top: 109.5
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 88.84125
                                          top: 109.5
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 18
                                      test_id: ~
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 31.25
                                          width: 158.3175
                                        position:
                                          x: 112.84125
                                          y: 122.375
                                        bounding_box:
                                          left: 112.84125
                                          top: 122.375
                                          width: 158.3175
                                          height: 31.25
                                        border_box:
                                          left: 112.84125
                                          top: 122.375
                                          width: 158.3175
                                          height: 31.25
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 20
                                          test_id: ~
                                          composable_type: Row
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: ~
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 31.25
                                              width: 158.3175
                                            position:
                                              x: 112.84125
                                              y: 122.375
                                            bounding_box:
                                              left: 112.84125
                                              top: 122.375
                                              width: 158.3175
                                              height: 31.25
                                            border_box:
                                              left: 112.84125
                                              top: 122.375
                                              width: 158.3175
                                              height: 31.25
                                            rotation: 0
                                        child_nodes:
                                          - props:
                                              node_id: 21
                                              test_id: ~
                                              composable_type: ColumnForEach
                                              is_focused: false
                                              is_focusable: false
                                              is_on_screen: true
                                              on_screen_amount: 1
                                              is_visible: true
                                              base_styles:
                                                background_color: ~
                                                opacity: ~
                                                border_color: ~
                                                border_width: ~
                                                border_radius: ~
                                              text: ~
                                              text_layout: ~
                                              layout:
                                                size:
                                                  height: 31.25
                                                  width: 158.3175
                                                position:
                                                  x: 112.84125
                                                  y: 122.375
                                                bounding_box:
                                                  left: 112.84125
                                                  top: 122.375
                                                  width: 158.3175
                                                  height: 31.25
                                                border_box:
                                                  left: 112.84125
                                                  top: 122.375
                                                  width: 158.3175
                                                  height: 31.25
                                                rotation: 0
                                            child_nodes:
                                              - props:
                                                  node_id: 22
                                                  test_id: ~
                                                  composable_type: RowForEach
                                                  is_focused: false
                                                  is_focusable: false
                                                  is_on_screen: true
                                                  on_screen_amount: 1
                                                  is_visible: true
                                                  base_styles:
                                                    background_color: ~
                                                    opacity: ~
                                                    border_color: ~
                                                    border_width: ~
                                                    border_radius: ~
                                                  text: ~
                                                  text_layout: ~
                                                  layout:
                                                    size:
                                                      height: 31.25
                                                      width: 158.3175
                                                    position:
                                                      x: 112.84125
                                                      y: 122.375
                                                    bounding_box:
                                                      left: 112.84125
                                                      top: 122.375
                                                      width: 158.3175
                                                      height: 31.25
                                                    border_box:
                                                      left: 112.84125
                                                      top: 122.375
                                                      width: 158.3175
                                                      height: 31.25
                                                    rotation: 0
                                                child_nodes:
                                                  - props:
                                                      node_id: 23
                                                      test_id: typography-test-id
                                                      composable_type: RichTextLabel
                                                      is_focused: false
                                                      is_focusable: false
                                                      is_on_screen: true
                                                      on_screen_amount: 1
                                                      is_visible: true
                                                      base_styles:
                                                        background_color: ~
                                                        opacity: ~
                                                        border_color: ~
                                                        border_width: ~
                                                        border_radius: ~
                                                      text: Button text
                                                      text_layout:
                                                        is_text_truncated: false
                                                        num_lines: 1
                                                        text_bounding_box:
                                                          left: 0
                                                          top: 0
                                                          width: 158.3175
                                                          height: 31.25
                                                        is_rich_text_parent: true
                                                      layout:
                                                        size:
                                                          height: 31.25
                                                          width: 158.3175
                                                        position:
                                                          x: 112.84125
                                                          y: 122.375
                                                        bounding_box:
                                                          left: 112.84125
                                                          top: 122.375
                                                          width: 158.3175
                                                          height: 31.25
                                                        border_box:
                                                          left: 112.84125
                                                          top: 122.375
                                                          width: 158.3175
                                                          height: 31.25
                                                        rotation: 0
                                                    child_nodes: []
                      - props:
                          node_id: 24
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 432
                              y: 42
                            bounding_box:
                              left: 432
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 432
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 26
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 432
                                  y: 42
                                bounding_box:
                                  left: 432
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 432
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 27
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 432
                                      y: 42
                                    bounding_box:
                                      left: 432
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 432
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 29
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 432
                                          y: 42
                                        bounding_box:
                                          left: 432
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 432
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 30
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 432
                                              y: 42
                                            bounding_box:
                                              left: 432
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 432
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 31
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 432
                                      y: 42
                                    bounding_box:
                                      left: 432
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 432
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 33
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 432
                                          y: 42
                                        bounding_box:
                                          left: 432
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 432
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 35
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 432
                                          y: 42
                                        bounding_box:
                                          left: 432
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 432
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217753
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 672
                              y: 42
                            bounding_box:
                              left: 672
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 672
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217760
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 672
                                  y: 42
                                bounding_box:
                                  left: 672
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 672
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217756
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 672
                                      y: 42
                                    bounding_box:
                                      left: 672
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 672
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217747
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 672
                                          y: 42
                                        bounding_box:
                                          left: 672
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 672
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 36
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 672
                                              y: 42
                                            bounding_box:
                                              left: 672
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 672
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 37
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 672
                                      y: 42
                                    bounding_box:
                                      left: 672
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 672
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 39
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 672
                                          y: 42
                                        bounding_box:
                                          left: 672
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 672
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 41
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 672
                                          y: 42
                                        bounding_box:
                                          left: 672
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 672
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 42
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 912
                              y: 42
                            bounding_box:
                              left: 912
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 912
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 44
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 912
                                  y: 42
                                bounding_box:
                                  left: 912
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 912
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 45
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 912
                                      y: 42
                                    bounding_box:
                                      left: 912
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 912
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 47
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 912
                                          y: 42
                                        bounding_box:
                                          left: 912
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 912
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 48
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 912
                                              y: 42
                                            bounding_box:
                                              left: 912
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 912
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 49
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 912
                                      y: 42
                                    bounding_box:
                                      left: 912
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 912
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 51
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 912
                                          y: 42
                                        bounding_box:
                                          left: 912
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 912
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 53
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 912
                                          y: 42
                                        bounding_box:
                                          left: 912
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 912
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217771
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 1152
                              y: 42
                            bounding_box:
                              left: 1152
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 1152
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217778
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 1152
                                  y: 42
                                bounding_box:
                                  left: 1152
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 1152
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217774
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1152
                                      y: 42
                                    bounding_box:
                                      left: 1152
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1152
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217768
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 1152
                                          y: 42
                                        bounding_box:
                                          left: 1152
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 1152
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217766
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 1152
                                              y: 42
                                            bounding_box:
                                              left: 1152
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 1152
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 402653200
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1152
                                      y: 42
                                    bounding_box:
                                      left: 1152
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1152
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 55
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1152
                                          y: 42
                                        bounding_box:
                                          left: 1152
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1152
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 57
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1152
                                          y: 42
                                        bounding_box:
                                          left: 1152
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1152
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 58
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 1392
                              y: 42
                            bounding_box:
                              left: 1392
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 1392
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 60
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 1392
                                  y: 42
                                bounding_box:
                                  left: 1392
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 1392
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 61
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1392
                                      y: 42
                                    bounding_box:
                                      left: 1392
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1392
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 63
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 1392
                                          y: 42
                                        bounding_box:
                                          left: 1392
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 1392
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 64
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 1392
                                              y: 42
                                            bounding_box:
                                              left: 1392
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 1392
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 65
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1392
                                      y: 42
                                    bounding_box:
                                      left: 1392
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1392
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 67
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1392
                                          y: 42
                                        bounding_box:
                                          left: 1392
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1392
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 69
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1392
                                          y: 42
                                        bounding_box:
                                          left: 1392
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1392
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217787
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 1632
                              y: 42
                            bounding_box:
                              left: 1632
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 1632
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217794
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 1632
                                  y: 42
                                bounding_box:
                                  left: 1632
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 1632
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217790
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1632
                                      y: 42
                                    bounding_box:
                                      left: 1632
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1632
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217784
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 1632
                                          y: 42
                                        bounding_box:
                                          left: 1632
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 1632
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217782
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 1632
                                              y: 42
                                            bounding_box:
                                              left: 1632
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 1632
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 402653218
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1632
                                      y: 42
                                    bounding_box:
                                      left: 1632
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1632
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 71
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1632
                                          y: 42
                                        bounding_box:
                                          left: 1632
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1632
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 73
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1632
                                          y: 42
                                        bounding_box:
                                          left: 1632
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1632
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 74
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 0.25
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 1872
                              y: 42
                            bounding_box:
                              left: 1872
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 1872
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 76
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 0.25
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 1872
                                  y: 42
                                bounding_box:
                                  left: 1872
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 1872
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 77
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 0.25
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1872
                                      y: 42
                                    bounding_box:
                                      left: 1872
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1872
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 79
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 0.25
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 1872
                                          y: 42
                                        bounding_box:
                                          left: 1872
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 1872
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 80
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 0.25
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 1872
                                              y: 42
                                            bounding_box:
                                              left: 1872
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 1872
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 81
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 0.25
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 1872
                                      y: 42
                                    bounding_box:
                                      left: 1872
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 1872
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 83
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1872
                                          y: 42
                                        bounding_box:
                                          left: 1872
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1872
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 85
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1872
                                          y: 42
                                        bounding_box:
                                          left: 1872
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1872
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 134217803
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 2112
                              y: 42
                            bounding_box:
                              left: 2112
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 2112
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217810
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 2112
                                  y: 42
                                bounding_box:
                                  left: 2112
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 2112
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217806
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 2112
                                      y: 42
                                    bounding_box:
                                      left: 2112
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 2112
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217800
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 2112
                                          y: 42
                                        bounding_box:
                                          left: 2112
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 2112
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217798
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 2112
                                              y: 42
                                            bounding_box:
                                              left: 2112
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 2112
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 402653236
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 2112
                                      y: 42
                                    bounding_box:
                                      left: 2112
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 2112
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 87
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2112
                                          y: 42
                                        bounding_box:
                                          left: 2112
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2112
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 89
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2112
                                          y: 42
                                        bounding_box:
                                          left: 2112
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2112
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                      - props:
                          node_id: 90
                          test_id: ~
                          composable_type: Column
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 192
                              width: 192
                            position:
                              x: 2352
                              y: 42
                            bounding_box:
                              left: 2352
                              top: 42
                              width: 192
                              height: 192
                            border_box:
                              left: 2352
                              top: 42
                              width: 192
                              height: 192
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 92
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 96
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 192
                                  width: 192
                                position:
                                  x: 2352
                                  y: 42
                                bounding_box:
                                  left: 2352
                                  top: 42
                                  width: 192
                                  height: 192
                                border_box:
                                  left: 2352
                                  top: 42
                                  width: 192
                                  height: 192
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 93
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 2352
                                      y: 42
                                    bounding_box:
                                      left: 2352
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 2352
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 95
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 96
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 192
                                          width: 192
                                        position:
                                          x: 2352
                                          y: 42
                                        bounding_box:
                                          left: 2352
                                          top: 42
                                          width: 192
                                          height: 192
                                        border_box:
                                          left: 2352
                                          top: 42
                                          width: 192
                                          height: 192
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 96
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 96
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 192
                                              width: 192
                                            position:
                                              x: 2352
                                              y: 42
                                            bounding_box:
                                              left: 2352
                                              top: 42
                                              width: 192
                                              height: 192
                                            border_box:
                                              left: 2352
                                              top: 42
                                              width: 192
                                              height: 192
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 97
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 96
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 192
                                      width: 192
                                    position:
                                      x: 2352
                                      y: 42
                                    bounding_box:
                                      left: 2352
                                      top: 42
                                      width: 192
                                      height: 192
                                    border_box:
                                      left: 2352
                                      top: 42
                                      width: 192
                                      height: 192
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 99
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2352
                                          y: 42
                                        bounding_box:
                                          left: 2352
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2352
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 101
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2352
                                          y: 42
                                        bounding_box:
                                          left: 2352
                                          top: 42
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2352
                                          top: 42
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
              - props:
                  node_id: 10
                  test_id: ~
                  composable_type: Nothing
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 0
                    position:
                      x: 0
                      y: 42
                    bounding_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    border_box:
                      left: 0
                      top: 42
                      width: 0
                      height: 0
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 11
                  test_id: ~
                  composable_type: Row
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 1920
                    position:
                      x: 0
                      y: 138
                    bounding_box:
                      left: 0
                      top: 138
                      width: 1920
                      height: 0
                    border_box:
                      left: 0
                      top: 138
                      width: 1920
                      height: 0
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 12
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 0
                          y: 138
                        bounding_box:
                          left: 0
                          top: 138
                          width: 0
                          height: 0
                        border_box:
                          left: 0
                          top: 138
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 14
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 0
                              y: 138
                            bounding_box:
                              left: 0
                              top: 138
                              width: 0
                              height: 0
                            border_box:
                              left: 0
                              top: 138
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
                  - props:
                      node_id: 15
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 1920
                          y: 138
                        bounding_box:
                          left: 1920
                          top: 138
                          width: 0
                          height: 0
                        border_box:
                          left: 1920
                          top: 138
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 17
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 1920
                              y: 138
                            bounding_box:
                              left: 1920
                              top: 138
                              width: 0
                              height: 0
                            border_box:
                              left: 1920
                              top: 138
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
