---
source: crates/containers/src/short_carousel_sig.rs
expression: tree
---
props:
  node_id: 0
  test_id: ~
  composable_type: Column
  is_focused: false
  is_focusable: false
  is_on_screen: true
  on_screen_amount: 1
  is_visible: true
  base_styles:
    background_color: ~
    opacity: ~
    border_color: ~
    border_width: ~
    border_radius: ~
  text: ~
  text_layout: ~
  layout:
    size:
      height: 144
      width: 1920
    position:
      x: 0
      y: 0
    bounding_box:
      left: 0
      top: 0
      width: 1920
      height: 144
    border_box:
      left: 0
      top: 0
      width: 1920
      height: 144
    rotation: 0
child_nodes:
  - props:
      node_id: 2
      test_id: short-carousel
      composable_type: Column
      is_focused: false
      is_focusable: false
      is_on_screen: true
      on_screen_amount: 1
      is_visible: true
      base_styles:
        background_color: ~
        opacity: ~
        border_color: ~
        border_width: ~
        border_radius: ~
      text: ~
      text_layout: ~
      layout:
        size:
          height: 144
          width: 1920
        position:
          x: 0
          y: 0
        bounding_box:
          left: 0
          top: 0
          width: 1920
          height: 144
        border_box:
          left: 0
          top: 0
          width: 1920
          height: 144
        rotation: 0
    child_nodes:
      - props:
          node_id: 3
          test_id: basic-carousel
          composable_type: Column
          is_focused: false
          is_focusable: false
          is_on_screen: true
          on_screen_amount: 1
          is_visible: true
          base_styles:
            background_color: ~
            opacity: 1
            border_color: ~
            border_width: ~
            border_radius: ~
          text: ~
          text_layout: ~
          layout:
            size:
              height: 144
              width: 1920
            position:
              x: 0
              y: 0
            bounding_box:
              left: 0
              top: 0
              width: 1920
              height: 144
            border_box:
              left: 0
              top: 0
              width: 1920
              height: 144
            rotation: 0
        child_nodes:
          - props:
              node_id: 5
              test_id: ~
              composable_type: Nothing
              is_focused: false
              is_focusable: false
              is_on_screen: false
              on_screen_amount: 0
              is_visible: false
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 0
                  width: 0
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 0
                  height: 0
                border_box:
                  left: 0
                  top: 0
                  width: 0
                  height: 0
                rotation: 0
            child_nodes: []
          - props:
              node_id: 6
              test_id: ~
              composable_type: Stack
              is_focused: false
              is_focusable: false
              is_on_screen: true
              on_screen_amount: 1
              is_visible: true
              base_styles:
                background_color: ~
                opacity: ~
                border_color: ~
                border_width: ~
                border_radius: ~
              text: ~
              text_layout: ~
              layout:
                size:
                  height: 144
                  width: 1920
                position:
                  x: 0
                  y: 0
                bounding_box:
                  left: 0
                  top: 0
                  width: 1920
                  height: 144
                border_box:
                  left: 0
                  top: 0
                  width: 1920
                  height: 144
                rotation: 0
            child_nodes:
              - props:
                  node_id: 7
                  test_id: item-list
                  composable_type: RowList
                  is_focused: false
                  is_focusable: false
                  is_on_screen: true
                  on_screen_amount: 1
                  is_visible: true
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 144
                      width: 1920
                    position:
                      x: 0
                      y: 0
                    bounding_box:
                      left: 0
                      top: 0
                      width: 1920
                      height: 144
                    border_box:
                      left: 0
                      top: 0
                      width: 1920
                      height: 144
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 8
                      test_id: ~
                      composable_type: Nothing
                      is_focused: true
                      is_focusable: true
                      is_on_screen: true
                      on_screen_amount: 0.661157
                      is_visible: true
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 144
                          width: 2904
                        position:
                          x: 0
                          y: 0
                        bounding_box:
                          left: 0
                          top: 0
                          width: 2904
                          height: 144
                        border_box:
                          left: 0
                          top: 0
                          width: 2904
                          height: 144
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 134217729
                          test_id: short-card-test-id
                          composable_type: Stack
                          is_focused: true
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 144
                              width: 384
                            position:
                              x: 0
                              y: 0
                            bounding_box:
                              left: 0
                              top: 0
                              width: 384
                              height: 144
                            border_box:
                              left: 0
                              top: 0
                              width: 384
                              height: 144
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217744
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: true
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 144
                                  width: 384
                                position:
                                  x: 0
                                  y: 0
                                bounding_box:
                                  left: 0
                                  top: 0
                                  width: 384
                                  height: 144
                                border_box:
                                  left: 0
                                  top: 0
                                  width: 384
                                  height: 144
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217741
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 0
                                      y: 0
                                    bounding_box:
                                      left: 0
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 0
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217732
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 144
                                          width: 384
                                        position:
                                          x: 0
                                          y: 0
                                        bounding_box:
                                          left: 0
                                          top: 0
                                          width: 384
                                          height: 144
                                        border_box:
                                          left: 0
                                          top: 0
                                          width: 384
                                          height: 144
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 18
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 144
                                              width: 384
                                            position:
                                              x: 0
                                              y: 0
                                            bounding_box:
                                              left: 0
                                              top: 0
                                              width: 384
                                              height: 144
                                            border_box:
                                              left: 0
                                              top: 0
                                              width: 384
                                              height: 144
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 19
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 0
                                      y: 0
                                    bounding_box:
                                      left: 0
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 0
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 21
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 0
                                          y: 0
                                        bounding_box:
                                          left: 0
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 0
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 23
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 0
                                          y: 0
                                        bounding_box:
                                          left: 0
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 0
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                          - props:
                              node_id: 25
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 0
                                  y: 0
                                bounding_box:
                                  left: 0
                                  top: 0
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 0
                                  top: 0
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                      - props:
                          node_id: 26
                          test_id: short-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 144
                              width: 384
                            position:
                              x: 420
                              y: 0
                            bounding_box:
                              left: 420
                              top: 0
                              width: 384
                              height: 144
                            border_box:
                              left: 420
                              top: 0
                              width: 384
                              height: 144
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 27
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 144
                                  width: 384
                                position:
                                  x: 420
                                  y: 0
                                bounding_box:
                                  left: 420
                                  top: 0
                                  width: 384
                                  height: 144
                                border_box:
                                  left: 420
                                  top: 0
                                  width: 384
                                  height: 144
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 28
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 420
                                      y: 0
                                    bounding_box:
                                      left: 420
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 420
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 30
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 144
                                          width: 384
                                        position:
                                          x: 420
                                          y: 0
                                        bounding_box:
                                          left: 420
                                          top: 0
                                          width: 384
                                          height: 144
                                        border_box:
                                          left: 420
                                          top: 0
                                          width: 384
                                          height: 144
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 31
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 144
                                              width: 384
                                            position:
                                              x: 420
                                              y: 0
                                            bounding_box:
                                              left: 420
                                              top: 0
                                              width: 384
                                              height: 144
                                            border_box:
                                              left: 420
                                              top: 0
                                              width: 384
                                              height: 144
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 32
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 420
                                      y: 0
                                    bounding_box:
                                      left: 420
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 420
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 34
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 420
                                          y: 0
                                        bounding_box:
                                          left: 420
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 420
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 36
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 420
                                          y: 0
                                        bounding_box:
                                          left: 420
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 420
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                          - props:
                              node_id: 38
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 420
                                  y: 0
                                bounding_box:
                                  left: 420
                                  top: 0
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 420
                                  top: 0
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                      - props:
                          node_id: 134217765
                          test_id: short-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 144
                              width: 384
                            position:
                              x: 840
                              y: 0
                            bounding_box:
                              left: 840
                              top: 0
                              width: 384
                              height: 144
                            border_box:
                              left: 840
                              top: 0
                              width: 384
                              height: 144
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217763
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 144
                                  width: 384
                                position:
                                  x: 840
                                  y: 0
                                bounding_box:
                                  left: 840
                                  top: 0
                                  width: 384
                                  height: 144
                                border_box:
                                  left: 840
                                  top: 0
                                  width: 384
                                  height: 144
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217761
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 840
                                      y: 0
                                    bounding_box:
                                      left: 840
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 840
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217752
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 144
                                          width: 384
                                        position:
                                          x: 840
                                          y: 0
                                        bounding_box:
                                          left: 840
                                          top: 0
                                          width: 384
                                          height: 144
                                        border_box:
                                          left: 840
                                          top: 0
                                          width: 384
                                          height: 144
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217750
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 144
                                              width: 384
                                            position:
                                              x: 840
                                              y: 0
                                            bounding_box:
                                              left: 840
                                              top: 0
                                              width: 384
                                              height: 144
                                            border_box:
                                              left: 840
                                              top: 0
                                              width: 384
                                              height: 144
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 134217748
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 840
                                      y: 0
                                    bounding_box:
                                      left: 840
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 840
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 39
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 840
                                          y: 0
                                        bounding_box:
                                          left: 840
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 840
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 41
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 840
                                          y: 0
                                        bounding_box:
                                          left: 840
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 840
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                          - props:
                              node_id: 43
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 840
                                  y: 0
                                bounding_box:
                                  left: 840
                                  top: 0
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 840
                                  top: 0
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                      - props:
                          node_id: 44
                          test_id: short-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 1
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 144
                              width: 384
                            position:
                              x: 1260
                              y: 0
                            bounding_box:
                              left: 1260
                              top: 0
                              width: 384
                              height: 144
                            border_box:
                              left: 1260
                              top: 0
                              width: 384
                              height: 144
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 45
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 1
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 144
                                  width: 384
                                position:
                                  x: 1260
                                  y: 0
                                bounding_box:
                                  left: 1260
                                  top: 0
                                  width: 384
                                  height: 144
                                border_box:
                                  left: 1260
                                  top: 0
                                  width: 384
                                  height: 144
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 46
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 1260
                                      y: 0
                                    bounding_box:
                                      left: 1260
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 1260
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 48
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 1
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 144
                                          width: 384
                                        position:
                                          x: 1260
                                          y: 0
                                        bounding_box:
                                          left: 1260
                                          top: 0
                                          width: 384
                                          height: 144
                                        border_box:
                                          left: 1260
                                          top: 0
                                          width: 384
                                          height: 144
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 49
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 1
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 144
                                              width: 384
                                            position:
                                              x: 1260
                                              y: 0
                                            bounding_box:
                                              left: 1260
                                              top: 0
                                              width: 384
                                              height: 144
                                            border_box:
                                              left: 1260
                                              top: 0
                                              width: 384
                                              height: 144
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 50
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 1
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 1260
                                      y: 0
                                    bounding_box:
                                      left: 1260
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 1260
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 52
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1260
                                          y: 0
                                        bounding_box:
                                          left: 1260
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1260
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 54
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1260
                                          y: 0
                                        bounding_box:
                                          left: 1260
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1260
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                          - props:
                              node_id: 56
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 1260
                                  y: 0
                                bounding_box:
                                  left: 1260
                                  top: 0
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 1260
                                  top: 0
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                      - props:
                          node_id: 134217783
                          test_id: short-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: true
                          on_screen_amount: 0.625
                          is_visible: true
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 144
                              width: 384
                            position:
                              x: 1680
                              y: 0
                            bounding_box:
                              left: 1680
                              top: 0
                              width: 384
                              height: 144
                            border_box:
                              left: 1680
                              top: 0
                              width: 384
                              height: 144
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217781
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: true
                              on_screen_amount: 0.625
                              is_visible: true
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 144
                                  width: 384
                                position:
                                  x: 1680
                                  y: 0
                                bounding_box:
                                  left: 1680
                                  top: 0
                                  width: 384
                                  height: 144
                                border_box:
                                  left: 1680
                                  top: 0
                                  width: 384
                                  height: 144
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217779
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 0.625
                                  is_visible: true
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 1680
                                      y: 0
                                    bounding_box:
                                      left: 1680
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 1680
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217770
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: true
                                      on_screen_amount: 0.625
                                      is_visible: true
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 144
                                          width: 384
                                        position:
                                          x: 1680
                                          y: 0
                                        bounding_box:
                                          left: 1680
                                          top: 0
                                          width: 384
                                          height: 144
                                        border_box:
                                          left: 1680
                                          top: 0
                                          width: 384
                                          height: 144
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217768
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: true
                                          on_screen_amount: 0.625
                                          is_visible: true
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 144
                                              width: 384
                                            position:
                                              x: 1680
                                              y: 0
                                            bounding_box:
                                              left: 1680
                                              top: 0
                                              width: 384
                                              height: 144
                                            border_box:
                                              left: 1680
                                              top: 0
                                              width: 384
                                              height: 144
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 402653193
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: true
                                  on_screen_amount: 0.625
                                  is_visible: true
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 1680
                                      y: 0
                                    bounding_box:
                                      left: 1680
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 1680
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 57
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1680
                                          y: 0
                                        bounding_box:
                                          left: 1680
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1680
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 59
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 1680
                                          y: 0
                                        bounding_box:
                                          left: 1680
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 1680
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                          - props:
                              node_id: 61
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 1680
                                  y: 0
                                bounding_box:
                                  left: 1680
                                  top: 0
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 1680
                                  top: 0
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                      - props:
                          node_id: 62
                          test_id: short-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 144
                              width: 384
                            position:
                              x: 2100
                              y: 0
                            bounding_box:
                              left: 2100
                              top: 0
                              width: 384
                              height: 144
                            border_box:
                              left: 2100
                              top: 0
                              width: 384
                              height: 144
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 63
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 144
                                  width: 384
                                position:
                                  x: 2100
                                  y: 0
                                bounding_box:
                                  left: 2100
                                  top: 0
                                  width: 384
                                  height: 144
                                border_box:
                                  left: 2100
                                  top: 0
                                  width: 384
                                  height: 144
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 64
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 2100
                                      y: 0
                                    bounding_box:
                                      left: 2100
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 2100
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 66
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 144
                                          width: 384
                                        position:
                                          x: 2100
                                          y: 0
                                        bounding_box:
                                          left: 2100
                                          top: 0
                                          width: 384
                                          height: 144
                                        border_box:
                                          left: 2100
                                          top: 0
                                          width: 384
                                          height: 144
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 67
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 144
                                              width: 384
                                            position:
                                              x: 2100
                                              y: 0
                                            bounding_box:
                                              left: 2100
                                              top: 0
                                              width: 384
                                              height: 144
                                            border_box:
                                              left: 2100
                                              top: 0
                                              width: 384
                                              height: 144
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 68
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 2100
                                      y: 0
                                    bounding_box:
                                      left: 2100
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 2100
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 70
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2100
                                          y: 0
                                        bounding_box:
                                          left: 2100
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2100
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 72
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2100
                                          y: 0
                                        bounding_box:
                                          left: 2100
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2100
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                          - props:
                              node_id: 74
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 2100
                                  y: 0
                                bounding_box:
                                  left: 2100
                                  top: 0
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 2100
                                  top: 0
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
                      - props:
                          node_id: 134217801
                          test_id: short-card-test-id
                          composable_type: Stack
                          is_focused: false
                          is_focusable: true
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: 12
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 144
                              width: 384
                            position:
                              x: 2520
                              y: 0
                            bounding_box:
                              left: 2520
                              top: 0
                              width: 384
                              height: 144
                            border_box:
                              left: 2520
                              top: 0
                              width: 384
                              height: 144
                            rotation: 0
                        child_nodes:
                          - props:
                              node_id: 134217799
                              test_id: card-test-id
                              composable_type: Stack
                              is_focused: false
                              is_focusable: true
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: 12
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 144
                                  width: 384
                                position:
                                  x: 2520
                                  y: 0
                                bounding_box:
                                  left: 2520
                                  top: 0
                                  width: 384
                                  height: 144
                                border_box:
                                  left: 2520
                                  top: 0
                                  width: 384
                                  height: 144
                                rotation: 0
                            child_nodes:
                              - props:
                                  node_id: 134217797
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color:
                                      r: 25
                                      g: 30
                                      b: 37
                                      a: 255
                                    opacity: ~
                                    border_color:
                                      r: 0
                                      g: 0
                                      b: 0
                                      a: 0
                                    border_width: 0
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 2520
                                      y: 0
                                    bounding_box:
                                      left: 2520
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 2520
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 134217788
                                      test_id: collaged-image-wrapper-test-id
                                      composable_type: Row
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: 1
                                        border_color: ~
                                        border_width: ~
                                        border_radius: 12
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 144
                                          width: 384
                                        position:
                                          x: 2520
                                          y: 0
                                        bounding_box:
                                          left: 2520
                                          top: 0
                                          width: 384
                                          height: 144
                                        border_box:
                                          left: 2520
                                          top: 0
                                          width: 384
                                          height: 144
                                        rotation: 0
                                    child_nodes:
                                      - props:
                                          node_id: 134217786
                                          test_id: collaged-image-test-id
                                          composable_type: Image
                                          is_focused: false
                                          is_focusable: false
                                          is_on_screen: false
                                          on_screen_amount: 0
                                          is_visible: false
                                          base_styles:
                                            background_color: ~
                                            opacity: ~
                                            border_color: ~
                                            border_width: ~
                                            border_radius: 12
                                          text: ~
                                          text_layout: ~
                                          layout:
                                            size:
                                              height: 144
                                              width: 384
                                            position:
                                              x: 2520
                                              y: 0
                                            bounding_box:
                                              left: 2520
                                              top: 0
                                              width: 384
                                              height: 144
                                            border_box:
                                              left: 2520
                                              top: 0
                                              width: 384
                                              height: 144
                                            rotation: 0
                                        child_nodes: []
                              - props:
                                  node_id: 402653213
                                  test_id: ~
                                  composable_type: Stack
                                  is_focused: false
                                  is_focusable: false
                                  is_on_screen: false
                                  on_screen_amount: 0
                                  is_visible: false
                                  base_styles:
                                    background_color: ~
                                    opacity: ~
                                    border_color: ~
                                    border_width: ~
                                    border_radius: 12
                                  text: ~
                                  text_layout: ~
                                  layout:
                                    size:
                                      height: 144
                                      width: 384
                                    position:
                                      x: 2520
                                      y: 0
                                    bounding_box:
                                      left: 2520
                                      top: 0
                                      width: 384
                                      height: 144
                                    border_box:
                                      left: 2520
                                      top: 0
                                      width: 384
                                      height: 144
                                    rotation: 0
                                child_nodes:
                                  - props:
                                      node_id: 75
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2520
                                          y: 0
                                        bounding_box:
                                          left: 2520
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2520
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                                  - props:
                                      node_id: 77
                                      test_id: ~
                                      composable_type: Nothing
                                      is_focused: false
                                      is_focusable: false
                                      is_on_screen: false
                                      on_screen_amount: 0
                                      is_visible: false
                                      base_styles:
                                        background_color: ~
                                        opacity: ~
                                        border_color: ~
                                        border_width: ~
                                        border_radius: ~
                                      text: ~
                                      text_layout: ~
                                      layout:
                                        size:
                                          height: 0
                                          width: 0
                                        position:
                                          x: 2520
                                          y: 0
                                        bounding_box:
                                          left: 2520
                                          top: 0
                                          width: 0
                                          height: 0
                                        border_box:
                                          left: 2520
                                          top: 0
                                          width: 0
                                          height: 0
                                        rotation: 0
                                    child_nodes: []
                          - props:
                              node_id: 79
                              test_id: ~
                              composable_type: Nothing
                              is_focused: false
                              is_focusable: false
                              is_on_screen: false
                              on_screen_amount: 0
                              is_visible: false
                              base_styles:
                                background_color: ~
                                opacity: ~
                                border_color: ~
                                border_width: ~
                                border_radius: ~
                              text: ~
                              text_layout: ~
                              layout:
                                size:
                                  height: 0
                                  width: 0
                                position:
                                  x: 2520
                                  y: 0
                                bounding_box:
                                  left: 2520
                                  top: 0
                                  width: 0
                                  height: 0
                                border_box:
                                  left: 2520
                                  top: 0
                                  width: 0
                                  height: 0
                                rotation: 0
                            child_nodes: []
              - props:
                  node_id: 10
                  test_id: ~
                  composable_type: Nothing
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 0
                    position:
                      x: 0
                      y: 0
                    bounding_box:
                      left: 0
                      top: 0
                      width: 0
                      height: 0
                    border_box:
                      left: 0
                      top: 0
                      width: 0
                      height: 0
                    rotation: 0
                child_nodes: []
              - props:
                  node_id: 11
                  test_id: ~
                  composable_type: Row
                  is_focused: false
                  is_focusable: false
                  is_on_screen: false
                  on_screen_amount: 0
                  is_visible: false
                  base_styles:
                    background_color: ~
                    opacity: ~
                    border_color: ~
                    border_width: ~
                    border_radius: ~
                  text: ~
                  text_layout: ~
                  layout:
                    size:
                      height: 0
                      width: 1920
                    position:
                      x: 0
                      y: 72
                    bounding_box:
                      left: 0
                      top: 72
                      width: 1920
                      height: 0
                    border_box:
                      left: 0
                      top: 72
                      width: 1920
                      height: 0
                    rotation: 0
                child_nodes:
                  - props:
                      node_id: 12
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 0
                          y: 72
                        bounding_box:
                          left: 0
                          top: 72
                          width: 0
                          height: 0
                        border_box:
                          left: 0
                          top: 72
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 14
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 0
                              y: 72
                            bounding_box:
                              left: 0
                              top: 72
                              width: 0
                              height: 0
                            border_box:
                              left: 0
                              top: 72
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
                  - props:
                      node_id: 15
                      test_id: pointer-control-caret-test-id
                      composable_type: Row
                      is_focused: false
                      is_focusable: false
                      is_on_screen: false
                      on_screen_amount: 0
                      is_visible: false
                      base_styles:
                        background_color: ~
                        opacity: ~
                        border_color: ~
                        border_width: ~
                        border_radius: ~
                      text: ~
                      text_layout: ~
                      layout:
                        size:
                          height: 0
                          width: 0
                        position:
                          x: 1920
                          y: 72
                        bounding_box:
                          left: 1920
                          top: 72
                          width: 0
                          height: 0
                        border_box:
                          left: 1920
                          top: 72
                          width: 0
                          height: 0
                        rotation: 0
                    child_nodes:
                      - props:
                          node_id: 17
                          test_id: ~
                          composable_type: Nothing
                          is_focused: false
                          is_focusable: false
                          is_on_screen: false
                          on_screen_amount: 0
                          is_visible: false
                          base_styles:
                            background_color: ~
                            opacity: ~
                            border_color: ~
                            border_width: ~
                            border_radius: ~
                          text: ~
                          text_layout: ~
                          layout:
                            size:
                              height: 0
                              width: 0
                            position:
                              x: 1920
                              y: 72
                            bounding_box:
                              left: 1920
                              top: 72
                              width: 0
                              height: 0
                            border_box:
                              left: 1920
                              top: 72
                              width: 0
                              height: 0
                            rotation: 0
                        child_nodes: []
