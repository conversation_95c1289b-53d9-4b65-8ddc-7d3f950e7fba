[package]
name = "buybox"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
log.workspace = true
cfg-test-attr-derive.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
mockall.workspace = true
mockall_double.workspace = true
fableous.workspace = true
title-reaction-buttons.workspace = true
amzn-fable-tokens.workspace = true

[dev-dependencies]
rstest.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }

[features]
test-utils = []
debug_impl = []

[[example]]
name = "buybox"
crate-type = ["cdylib"]
path = "examples/buybox.rs"