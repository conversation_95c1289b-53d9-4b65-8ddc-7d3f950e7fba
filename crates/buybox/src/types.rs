use cfg_test_attr_derive::derive_test_only;
use fableous::buttons::{expandable_button::*, primary_button::PrimaryButtonVariant};
use ignx_compositron::{id::Id, prelude::MaybeSignal, text::TextContent};
use title_reaction_buttons::{state::title_reaction_state::ReactionState, ReactionButtonVariant};

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub enum IconButtonVariant {
    Icon {
        id: String,
        label: TextContent,
        icon: MaybeSignal<String>,
    },
    Reaction {
        id: String,
        state: MaybeSignal<ReactionState>,
        variant: ReactionButtonVariant,
    },
}

impl Id for IconButtonVariant {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            IconButtonVariant::Icon { id, .. } => id,
            IconButtonVariant::Reaction { id, .. } => id,
        }
    }
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub enum BuyBoxRowVariant {
    Icons {
        id: String,
        icons: Vec<IconButtonVariant>,
    },
    SingleButton(SingleButtonVariant),
}

#[derive(Clone)]
#[derive_test_only(PartialEq, Debug)]
pub enum SingleButtonVariant {
    PrimaryButton {
        id: String,
        variant: PrimaryButtonVariant,
        disabled: bool,
        playback_progress: Option<f32>,
    },
    ExpandableButton {
        id: String,
        primary_asset: ExpandableButtonPrimaryAssetVariant,
        secondary_text: ExpandableButtonSecondaryTextVariant,
        supporting_messages: Vec<SupportingMessage>,
    },
}

impl Id for SingleButtonVariant {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            SingleButtonVariant::PrimaryButton { id, .. } => id,
            SingleButtonVariant::ExpandableButton { id, .. } => id,
        }
    }
}

impl Id for BuyBoxRowVariant {
    type Id = String;

    fn id(&self) -> &Self::Id {
        match self {
            BuyBoxRowVariant::Icons { id, .. } => id,
            BuyBoxRowVariant::SingleButton(single_button) => single_button.id(),
        }
    }
}
