use amzn_fable_tokens::{FableIcon, FableSpacing};
use fableous::buttons::expandable_button::*;
use ignx_compositron::{compose, main, prelude::*};

#[main]
fn main() {
    launch_composable(|ctx| {
        compose! {
            Column(){
                ExpandableButton(
                    primary_asset: ExpandableButtonPrimaryAssetVariant::Logo(
                        PrimaryLogo {
                            image_url: "https://m.media-amazon.com/images/G/01/primeLogo/primelogo-330x90-v2.png".to_string(),
                            height: 33.0,
                            width: 121.0,
                            accessibility_text: "Prime".to_string().into()
                        },
                    ),
                    secondary_text: ExpandableButtonSecondaryTextVariant::Size100("$9.99".to_string().into()),
                    supporting_messages: vec![]
                )
                ExpandableButton(
                    primary_asset: ExpandableButtonPrimaryAssetVariant::Text(
                        PrimaryText {
                            text: "Watch Episode 2 Longer Text".to_string().into(),
                            icon: Some(FableIcon::PLAY.to_string()),
                            size: PrimaryTextSize::Size400
                        }
                    ),
                    secondary_text: ExpandableButtonSecondaryTextVariant::Size100("$5".to_string().into()),
                    supporting_messages: vec![
                        SupportingMessage { id: 0, text: "Message 1".to_string().into(), icon: Some(FableIcon::CHECK.to_string()) },
                        SupportingMessage { id: 1, text: "Message 2 - super long and should extend on to 2 lines because of it's length blah blah blah".to_string().into(), icon: Some(FableIcon::CHECK.to_string()) }
                    ]
                )
                ExpandableButton(
                    primary_asset: ExpandableButtonPrimaryAssetVariant::Text(
                        PrimaryText {
                            text: "Watch Episode 3".to_string().into(),
                            icon: None,
                            size: PrimaryTextSize::Size400
                        }
                    ),
                    secondary_text: ExpandableButtonSecondaryTextVariant::Size100("$5".to_string().into()),
                    supporting_messages: vec![
                        SupportingMessage { id: 0, text: "Message 1".to_string().into(), icon: Some(FableIcon::CHECK.to_string()) },
                        SupportingMessage { id: 1, text: "Message 2".to_string().into(), icon: Some(FableIcon::CHECK.to_string()) }
                    ]
                )
                ExpandableButton(
                    primary_asset: ExpandableButtonPrimaryAssetVariant::Text(
                        PrimaryText {
                            text: "No Secondary Text".to_string().into(),
                            icon: None,
                            size: PrimaryTextSize::Size400
                        }
                    ),
                    secondary_text: ExpandableButtonSecondaryTextVariant::None,
                    supporting_messages: vec![]
                )
            }
            .main_axis_alignment(MainAxisAlignment::SpacedBy(FableSpacing::SPACING100))
            .max_width(1500.0)
            .min_width(430.0)
            .cross_axis_stretch(true)
            .translate_x(50.0)
            .translate_y(50.0)
        }
    });
}
