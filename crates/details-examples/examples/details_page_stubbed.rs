use details::context::hooks::empty_stack_fallback;
use details::context::hooks::use_details_page_context;
use details::ui::details_page::*;
use details_examples::details_stubbed_data::{get_all_with_tags, ATF_EXAMPLE_TAG};
use details_examples::example_helpers::details_wrapped_example::*;
use details_examples::example_helpers::types::NonOptionalComposableBuilder;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};
use log::error;
use std::rc::Rc;
use title_details::core::signals::TitleDetailsSignals;

#[Composer]
fn DetailsPageExample(ctx: &AppContext) -> StackComposable {
    let details_ctx = match use_details_page_context(ctx.scope()) {
        None => {
            error!("[DetailsPageExample] Failed to get details context!");
            return empty_stack_fallback(ctx);
        }
        Some(ctx) => ctx,
    };

    let signals = use_context::<TitleDetailsSignals>(ctx.scope())
        .expect("Expected title details signals to exist!");

    let update_media_background_rw = details_ctx.borrow().state.media_background;

    let app_modal_data = create_rw_signal(ctx.scope(), vec![]).write_only();

    compose! {
        Stack() {
            DetailsPage(title_details_signals: &signals, update_media_background_rw, app_modal_data)
        }
    }
}

#[ignx_compositron::main]
fn main() {
    launch_composable(|ctx| {
        let responses = get_all_with_tags([ATF_EXAMPLE_TAG]);

        let example_builder: Box<NonOptionalComposableBuilder<StackComposable>> =
            Box::new(move |ctx| {
                compose! {
                    DetailsPageExample()
                }
            });
        compose! {
            DetailsWrappedExample(builder: Rc::new(example_builder), responses, with_nav_menu: false)
        }
    });
}
