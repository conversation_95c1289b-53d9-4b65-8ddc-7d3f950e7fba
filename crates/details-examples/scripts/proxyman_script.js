function getJsonFromUrl(url) {
    var query = url.substr(1);
    var result = {};
    query.split("&").forEach(function(part) {
        var item = part.split("=");
        result[item[0]] = decodeURIComponent(item[1]);
    });
    return result;
}

async function onRequest(context, url, request) {
    return request;
}

async function onResponse(context, url, request, response) {
    if((url.includes("api.amazon.co.uk/auth/register") || url.includes("api.amazon.co.uk/auth/token")) && response.statusCode == 200) {
        console.log(response.body);
        let token = response.body["access_token"];
        writeToFile(token, "~/.rust_details_data/access_token");
    }

    if(url.includes("/lrcedge/getDataByJavaTransform/v1/lr/collections/collectionsPageInitial") && response.statusCode == 200) {
        const queries = getJsonFromUrl(url);
        let firmware = queries["firmware"];
        let manufacturer = queries["manufacturer"];
        let chipset = queries["chipset"];
        let model = queries["model"];
        let operatingSystem = queries["operatingSystem"];
        let deviceTypeID = queries["deviceTypeID"];
        let deviceID = queries["deviceID"];
        let osLocale = queries["osLocale"];
        let dataPayload = {
            chipset,
            device_type_id: deviceTypeID,
            device_id: deviceID,
            firmware_version: firmware,
            manufacturer,
            model_name: model,
            os_name: operatingSystem,
            os_locale: osLocale,
            player_name: "XP"
        }
        writeToFile(dataPayload, "~/.rust_details_data/device_details.json");
    }

    // Done
    return response;
}