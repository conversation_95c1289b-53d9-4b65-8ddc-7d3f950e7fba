use crate::example_specific_mocks::EXAMPLE_VOD_ATF_RESUME_AND_MORE_PURCHASE_OPTIONS;
use details::network::mock_responses::{
    MOCK_RESPONSE_EMPTY_TAPS_ATF, MOCK_RESPONSE_LIVE_ATF, MOCK_RESPONSE_LIVE_PLAYBACK_GROUP_WATCH,
    MOCK_RESPONSE_PREORDER_SUPPRESSION_ATF, MOCK_RESPONSE_VOD_ATF,
    MOCK_RESPONSE_VOD_BOUNDS_ONE_LINE_ATF, MOCK_RESPONSE_VOD_BOUNDS_THREE_LINE_ATF,
    MOCK_RESPONSE_VOD_BOUNDS_TWO_LINE_ATF, MOCK_RESPONSE_VOD_ENTITY_TYPE_TRAILER_ATF,
    MOCK_RESPONSE_VOD_HOW_DO_I_WATCH_THIS_ATF, MOCK_RESPONSE_VOD_IN_WATCHLIST_ATF,
    MOCK_RESPONSE_VOD_LONG_ATF, MOCK_RESPONSE_VOD_LONG_MPO_ATF, MOCK_RESPONSE_VOD_MPO_99_ATF,
    MOCK_RESPONSE_VOD_MPO_ATF, MOCK_RESPONSE_VOD_NO_EXPLORE_ATF, MOCK_RESPONSE_VOD_NO_HERO_ATF,
    MOCK_RESPONSE_VOD_NO_HERO_OR_TITLE_ATF, MOCK_RESPONSE_VOD_RESUME_ATF,
    MOCK_RESPONSE_VOD_SUBSCRIPTION_TITLE_ATF, MOCK_RESPONSE_VOD_SUPPRESSION_ATF,
    MOCK_RESPONSE_VOD_TROP_BTF, MOCK_RESPONSE_VOD_WATCH_NOW_ATF,
};
use std::collections::HashSet;
use std::rc::Rc;

pub const ATF_EXAMPLE_TAG: &str = "ATF";
pub const MPO_EXAMPLE_TAG: &str = "MPO";

#[derive(Clone)]
pub struct AdditionalData {
    pub is_ssm_enabled: bool,
    pub taps_fallback_enabled: bool,
}

#[derive(Clone)]
pub enum ExampleResponseSetting {
    NetworkGti(String),
    StubbedResponse(String),
}

#[derive(Clone)]
pub struct DetailsPageExampleData {
    pub response: ExampleResponseSetting,
    pub btf_response: ExampleResponseSetting,
    pub example_name: String,
    pub additional_data: Option<AdditionalData>,
    pub tags: HashSet<String>,
}
impl DetailsPageExampleData {
    fn new<'a>(
        example_name: &'a str,
        response: &'a str,
        tags: HashSet<String>,
    ) -> DetailsPageExampleData {
        DetailsPageExampleData {
            response: ExampleResponseSetting::StubbedResponse(response.to_string()),
            btf_response: ExampleResponseSetting::StubbedResponse(
                MOCK_RESPONSE_VOD_TROP_BTF.to_string(),
            ),
            example_name: example_name.to_string(),
            additional_data: None,
            tags,
        }
    }
    fn new_with_data<'a>(
        example_name: &'a str,
        response: &'a str,
        additional_data: AdditionalData,
        tags: HashSet<String>,
    ) -> DetailsPageExampleData {
        DetailsPageExampleData {
            response: ExampleResponseSetting::StubbedResponse(response.to_string()),
            btf_response: ExampleResponseSetting::StubbedResponse(
                MOCK_RESPONSE_VOD_TROP_BTF.to_string(),
            ),
            example_name: example_name.to_string(),
            additional_data: Some(additional_data),
            tags,
        }
    }

    fn new_with_gti<'a>(
        example_name: &'a str,
        gti: &'a str,
        additional_data: Option<AdditionalData>,
        tags: HashSet<String>,
    ) -> DetailsPageExampleData {
        DetailsPageExampleData {
            response: ExampleResponseSetting::NetworkGti(gti.to_string()),
            btf_response: ExampleResponseSetting::StubbedResponse(
                MOCK_RESPONSE_VOD_TROP_BTF.to_string(),
            ),
            example_name: example_name.to_string(),
            additional_data,
            tags,
        }
    }
}

fn tags<const N: usize>(tags_str: [&'_ str; N]) -> HashSet<String> {
    let mut tags = HashSet::new();
    for tag in tags_str {
        tags.insert(tag.to_string());
    }
    tags
}

fn all_examples() -> Vec<DetailsPageExampleData> {
    vec![
        DetailsPageExampleData::new("VOD ATF", MOCK_RESPONSE_VOD_ATF, tags([ATF_EXAMPLE_TAG])),
        DetailsPageExampleData::new(
            "VOD ATF In Watchlist",
            MOCK_RESPONSE_VOD_IN_WATCHLIST_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF No Explore",
            MOCK_RESPONSE_VOD_NO_EXPLORE_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF /w Entity Type Trailer",
            MOCK_RESPONSE_VOD_ENTITY_TYPE_TRAILER_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF Suppression",
            MOCK_RESPONSE_VOD_SUPPRESSION_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF MPO",
            MOCK_RESPONSE_VOD_MPO_ATF,
            tags([ATF_EXAMPLE_TAG, MPO_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF Subscription",
            MOCK_RESPONSE_VOD_SUBSCRIPTION_TITLE_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF Watch Now",
            MOCK_RESPONSE_VOD_WATCH_NOW_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF Resume",
            MOCK_RESPONSE_VOD_RESUME_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF Resume (Double height)",
            EXAMPLE_VOD_ATF_RESUME_AND_MORE_PURCHASE_OPTIONS,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF How do I watch this? (Suppression)",
            MOCK_RESPONSE_VOD_HOW_DO_I_WATCH_THIS_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF PreOrder Suppression",
            MOCK_RESPONSE_PREORDER_SUPPRESSION_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF Empty TAPS Response (Fallback enabled)",
            MOCK_RESPONSE_EMPTY_TAPS_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: true,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF Empty TAPS Response (Fallback disabled)",
            MOCK_RESPONSE_EMPTY_TAPS_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new(
            "VOD ATF Long",
            MOCK_RESPONSE_VOD_LONG_ATF,
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new("Live ATF", MOCK_RESPONSE_LIVE_ATF, tags([ATF_EXAMPLE_TAG])),
        DetailsPageExampleData::new_with_data(
            "Live ATF Playback Group (SSM Enabled)",
            MOCK_RESPONSE_LIVE_PLAYBACK_GROUP_WATCH,
            AdditionalData {
                is_ssm_enabled: true,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "Live ATF Playback Group (SSM Disabled)",
            MOCK_RESPONSE_LIVE_PLAYBACK_GROUP_WATCH,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF No Hero Image",
            MOCK_RESPONSE_VOD_NO_HERO_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF No Hero+Title Image",
            MOCK_RESPONSE_VOD_NO_HERO_OR_TITLE_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF MPO (99)",
            MOCK_RESPONSE_VOD_MPO_99_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG, MPO_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF Long MPO",
            MOCK_RESPONSE_VOD_LONG_MPO_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG, MPO_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF Bounds (1 Line)",
            MOCK_RESPONSE_VOD_BOUNDS_ONE_LINE_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF Bounds (2 Line)",
            MOCK_RESPONSE_VOD_BOUNDS_TWO_LINE_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_data(
            "VOD ATF Bounds (3 Line)",
            MOCK_RESPONSE_VOD_BOUNDS_THREE_LINE_ATF,
            AdditionalData {
                is_ssm_enabled: false,
                taps_fallback_enabled: false,
            },
            tags([ATF_EXAMPLE_TAG]),
        ),
        DetailsPageExampleData::new_with_gti(
            "VOD ATF Networked - Fallout",
            "amzn1.dv.gti.242f5d02-0b3e-4f4d-a89b-22da3f65f0ec",
            None,
            tags([ATF_EXAMPLE_TAG]),
        ),
    ]
}

pub fn get_all_with_tags<const N: usize>(
    tags_str: [&'_ str; N],
) -> Rc<Vec<DetailsPageExampleData>> {
    let examples = all_examples();
    let mut filtered_examples = vec![];
    for example in &examples {
        let mut should_add = true;
        for tag in tags_str {
            if !example.tags.contains(tag) {
                should_add = false;
                break;
            }
        }

        if should_add {
            filtered_examples.push(example.clone());
        }
    }
    Rc::new(filtered_examples)
}

pub fn get_all_examples() -> Rc<Vec<DetailsPageExampleData>> {
    Rc::new(all_examples())
}
