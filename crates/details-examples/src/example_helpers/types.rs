use crate::details_stubbed_data::DetailsPageExampleData;
use ignx_compositron::context::AppContext;
use ignx_compositron::reactive::RwSignal;
use ignx_compositron::task::TaskID;

#[derive(Clone)]
pub struct DetailsPageExampleInfo {
    pub index: u32,
    pub example_info: Option<DetailsPageExampleData>,
    pub should_be_displayed: bool,
    pub fade_task_id: Option<TaskID>,
}

pub type NonOptionalComposableBuilder<C> = dyn Fn(&AppContext) -> C;

pub struct StartRenderingSignal {
    pub start_render_signal: RwSignal<bool>,
}
