use crate::example_helpers::prelude::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::animation::Animation;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, Composer};

#[Composer]
pub fn ExampleNameDisplay(ctx: &AppContext) -> StackComposable {
    #[allow(clippy::expect_used)]
    let example_info = use_context::<RwSignal<DetailsPageExampleInfo>>(ctx.scope())
        .expect("Expected example info to exist");

    let formatted_text = Signal::derive(ctx.scope, move || {
        let info = example_info.get();
        if let Some(example_data) = info.example_info {
            return format!("{} {}", example_data.example_name, info.index);
        }

        "Empty".to_string()
    });
    let text_opacity = Signal::derive(ctx.scope(), move || {
        if example_info.get().should_be_displayed {
            return 1.0;
        }
        0.0
    });

    let current_opacity = create_rw_signal(ctx.scope(), 0.0);

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let opacity_now = current_opacity.get();
            let next_opacity = text_opacity.get();

            if opacity_now != next_opacity {
                ctx.with_animation(Animation::default(), move || {
                    current_opacity.set(next_opacity);
                });
            }
        }
    });

    compose! {
        Stack(){
            Row() {
                Row() {
                    Label(text: formatted_text)
                        .font_weight(FontWeight::Bold)
                        .font_size(FontSize(20))
                        .color(Color::black())
                }
                .opacity(current_opacity)
                .padding(Padding::all(10.0))
                .background_color(Color::white())
                .border_radius(5.0)
            }
            .width(SCREEN_WIDTH)
            .main_axis_alignment(MainAxisAlignment::End)
        }
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
    }
}
