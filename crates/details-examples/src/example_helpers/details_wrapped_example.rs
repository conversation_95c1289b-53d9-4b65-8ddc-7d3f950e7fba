use crate::details_stubbed_data::DetailsPageExampleData;
use crate::example_helpers::example_name_display::*;
use crate::example_helpers::init_helpers::{
    create_example_data_signal, create_required_data_and_signals_for_example, wait_for_font_load,
};
use crate::example_helpers::types::NonOptionalComposableBuilder;
use amzn_fable_tokens::FableColor;
use common_transform_types::profile::Profile;
use fableous::utils::get_ignx_color;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::input::KeyCode;
use ignx_compositron::memo::ComposableBuilder;
use ignx_compositron::prelude::*;
use ignx_compositron::show::*;
use ignx_compositron::{compose, compose_option, Composer};
use media_background::media_background::*;
use media_background::provide_media_background_context;
use media_background::types::MediaBackgroundType;
use navigation_menu::model::nav_model::TopNavData;
use navigation_menu::ui::navigation_menu::*;
use std::rc::Rc;
use title_details::core::signals::TitleDetailsSignals;
use title_details::core::*;

#[Composer]
fn DetailsExample(
    ctx: &AppContext,
    example_builder: Rc<Box<NonOptionalComposableBuilder<StackComposable>>>,
) -> StackComposable {
    #[allow(clippy::expect_used, reason = "examples")]
    let title_details_signals = use_context::<TitleDetailsSignals>(ctx.scope())
        .expect("Expected title details data to exist");
    #[allow(clippy::expect_used, reason = "examples")]
    let media_background_data = use_context::<RwSignal<MediaBackgroundType>>(ctx.scope())
        .expect("Expected media background data to exist");

    let example_builder_cloned = example_builder.clone();
    let wrapped_optional_builder: Box<ComposableBuilder<StackComposable>> = Box::new(move |ctx| {
        let result = example_builder_cloned(ctx);
        Some(result)
    });
    let autoplay_enabled = Signal::derive(ctx.scope(), move || false);
    provide_media_background_context(ctx.scope());
    compose! {
         Stack() {
            MediaBackground(incoming_data: media_background_data.read_only(), rtl_enabled: false, autoplay_enabled)
            ExampleNameDisplay(),
            TitleDetails(incoming_data: title_details_signals.data, opacity: title_details_signals.opacity, x_offset: title_details_signals.x_offset, y_offset: title_details_signals.y_offset)
            Memo(item_builder: wrapped_optional_builder)
        }
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
        .background_color(get_ignx_color(FableColor::BACKGROUND))
    }
}

#[Composer]
fn WithNavigationMenu(
    ctx: &AppContext,
    example_builder: Box<dyn Fn(&AppContext, FocusSignal) -> StackComposable>,
) -> StackComposable {
    let top_nav_data = create_rw_signal(
        ctx.scope(),
        TopNavData::new(
            vec![],
            vec![],
            None,
            Default::default(),
            Rc::new(|_| {}),
            true,
        ),
    );
    let active_profile = create_rw_signal::<Option<Profile>>(ctx.scope(), None);

    compose! {
        Stack() {
            NavigationMenu(content: example_builder, top_nav_data: Signal::from(top_nav_data), active_profile: Signal::from(active_profile))
        }
    }
}

#[Composer]
pub fn DetailsMemoWrappedExample(
    ctx: &AppContext,
    builder: Rc<Box<NonOptionalComposableBuilder<StackComposable>>>,
    #[into] with_nav_menu: MaybeSignal<bool>,
    responses: Rc<Vec<DetailsPageExampleData>>,
    starting_example_index: i32,
) -> StackComposable {
    let response_length = responses.len();
    let index_signal = create_example_data_signal(ctx, responses, starting_example_index);

    let memo_builder: Box<ComposableBuilder<StackComposable>> =
        Box::new(move |ctx| match with_nav_menu.get() {
            true => {
                let inner_example_builder_clone = builder.clone();
                let inner_example_builder: Box<
                    dyn Fn(&AppContext, FocusSignal) -> StackComposable,
                > = Box::new(move |ctx, _| {
                    compose! {
                        DetailsExample(example_builder: inner_example_builder_clone.clone())
                    }
                });
                compose_option! {
                    WithNavigationMenu(example_builder: inner_example_builder)
                }
            }
            false => {
                compose_option! {
                    DetailsExample(example_builder: builder.clone())
                }
            }
        });

    compose! {
        Stack() {
            Memo(item_builder: memo_builder)
        }
            // Code for switching examples
            .on_key_down(KeyCode::EqualSign, move || {
                index_signal.update(|prev_val| {
                    if *prev_val == response_length-1 {
                        *prev_val = 0;
                        return;
                    }

                    *prev_val += 1;
                });
            })
            .on_key_down(KeyCode::Hyphen, move || {
                index_signal.update(|prev_val| {
                    if *prev_val == 0 {
                        *prev_val = response_length-1;
                        return;
                    }

                    *prev_val -= 1;
                });
            })
    }
}

#[Composer]
pub fn DetailsWrappedExample(
    ctx: &AppContext,
    builder: Rc<Box<NonOptionalComposableBuilder<StackComposable>>>,
    responses: Rc<Vec<DetailsPageExampleData>>,
    #[into] with_nav_menu: MaybeSignal<bool>,
    #[optional = 0] starting_example_index: i32,
) -> StackComposable {
    let should_render = wait_for_font_load(ctx);
    create_required_data_and_signals_for_example(ctx);
    let if_builder: Box<ConditionalComposableBuilder> = Box::new({
        move |build_context| {
            let ctx = &build_context.clone();
            let message = compose! {
                DetailsMemoWrappedExample(builder: builder.clone(), with_nav_menu, responses: responses.clone(), starting_example_index)
            };
            message.into_widget()
        }
    });

    compose! {
        Stack() {
            Show(condition: should_render.into(), if_builder: if_builder)
        }
    }
}
