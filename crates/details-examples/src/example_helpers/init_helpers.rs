use crate::config_helpers::auth::get_access_token;
use crate::config_helpers::device_info::get_device_info;
use crate::details_stubbed_data::{DetailsPageExampleData, ExampleResponseSetting};
use crate::example_helpers::types::DetailsPageExampleInfo;
use app_config::{AppConfigContext, MockAppConfig};
use auth::{AuthContext, MockAuth};
use details::context::fixture::DEFAULT_MOCKED_TITLE_ID;
use details::context::hooks::use_details_page_context;
use details::context::{
    create_details_context_from_app_context, create_wrapped_rc_context_and_provide,
    DetailsPageContext, DetailsPageContextStore, ResourceState,
};
use details::modules::navigation_transitions::utils::transition_to_page_from_details;
use details::network::mock_responses::{
    get_mock_supported_locales, MOCK_GEO_LOCATION, MOCK_UX_LOCALE,
};
use details::network::parser::{details_page_atf_parser, details_page_btf_parser};
use details::ui::error_overlay::ErrorState;
use ignx_compositron::player_data::HdrSupport::Hdr10;
use ignx_compositron::player_data::PlayerCapabilities;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::store_value;
use ignx_compositron::time::Instant;
use location::RustPage::RUST_DETAILS;
use location::{
    Location, LocationWithEffect, NavigationAction, NavigationDirection, PageType, RustPage,
};
use media_background::provide_media_background_context;
use media_background::types::{HybridPlaybackContext, MediaBackgroundType};
#[cfg(feature = "details_example")]
use mockall::predicate::always;
use navigation_menu::context::nav_context::{NavControl, TopNavMode};
use network::NetworkClient;
use router::hooks::use_router;
use router::{MockRouting, RoutingContext};
use rust_features::provide_context_test_rust_features;
use serde_json::{Map, Value};
use std::cell::RefCell;
use std::ops::Add;
use std::rc::Rc;
use std::time::Duration;
use taps_parameters::{MockTapsParameterStore, TapsParameterStore};
use title_details::core::create_title_details_signals;
use title_details::core::signals::TitleDetailsSignals;

pub fn setup_mock_routing(ctx: AppContext) {
    let mut serde_location_map = Map::new();
    serde_location_map.insert("titleId".to_string(), Value::String("fakeGti".to_string()));
    serde_location_map.insert("isLiveDetailsPage".to_string(), Value::Bool(false));
    serde_location_map.insert("journeyIngressContext".to_string(), Value::Null);
    let details_location = Location {
        pageType: PageType::Rust(RUST_DETAILS),
        pageParams: serde_location_map.clone(),
    };
    let location = create_rw_signal(ctx.scope(), details_location);
    let location_with_effect_data = LocationWithEffect {
        from_location: Some(Location {
            pageType: PageType::Rust(RUST_DETAILS),
            pageParams: Default::default(),
        }),
        to_location: location.get_untracked(),
        exit_effect: None,
        enter_effect: None,
    };

    let location_with_effect = create_rw_signal(ctx.scope(), location_with_effect_data);

    let mut mock_routing_context = MockRouting::new();
    mock_routing_context
        .expect_location()
        .returning(move || location.into());
    mock_routing_context
        .expect_location_with_effect()
        .returning(move || location_with_effect.into());
    mock_routing_context
        .expect_get_last_navigation_action()
        .returning(move || NavigationAction {
            direction: NavigationDirection::Forward,
            from: PageType::Rust(RustPage::RUST_DETAILS),
            to: PageType::Rust(RustPage::RUST_DETAILS),
        });
    mock_routing_context
        .expect_get_previous_page()
        .return_const(None);
    mock_routing_context
        .expect_navigate()
        .with(always(), always())
        .returning(move |loc, _| {
            location.set(loc);
        });
    provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
}

pub fn set_details_page_location(scope: Scope, gti: String) {
    let mut serde_location_map = Map::new();
    serde_location_map.insert("titleId".to_string(), Value::String(gti));
    serde_location_map.insert("isLiveDetailsPage".to_string(), Value::Bool(false));
    serde_location_map.insert("journeyIngressContext".to_string(), Value::Null);
    let details_location = Location {
        pageType: PageType::Rust(RUST_DETAILS),
        pageParams: serde_location_map.clone(),
    };
    let router = use_router(scope);
    router.navigate(details_location, "SOURCE");
}

pub fn wait_for_font_load(ctx: &AppContext) -> RwSignal<bool> {
    // FIXME: Hack to fix badge rendering issues caused by FontManager not knowing the size of text
    let start_rendering = create_rw_signal(ctx.scope(), false);
    ctx.schedule_task(Instant::now() + Duration::from_millis(1000), move || {
        start_rendering.set(true);
    });

    start_rendering
}

pub fn setup_refreshing_access_token_job() {}

pub fn create_required_data_and_signals_for_example(ctx: &AppContext) {
    let mut mock_app_config = MockAppConfig::default();

    #[allow(clippy::redundant_closure, reason = "examples")]
    mock_app_config
        .expect_get_supported_locales()
        .returning(|| get_mock_supported_locales());
    mock_app_config
        .expect_get_geo_location()
        .returning(|| Some(MOCK_GEO_LOCATION.to_string()));
    mock_app_config
        .expect_get_ux_locale()
        .returning(|| MOCK_UX_LOCALE.to_string());
    mock_app_config
        .expect_get_base_url()
        .returning(|| Some("https://gtc-lrcdev-0.eu.api.amazonvideo.com".to_string()));

    // Taps roles
    let mut mock_taps_params = MockTapsParameterStore::default();
    mock_taps_params.expect_get_taps_roles().returning(move || {
        vec![
            "live-supported".to_string(),
            "startover-supported".to_string(),
            "supports-dai-timeshifting".to_string(),
        ]
    });
    mock_taps_params
        .expect_get_details_atf_roles()
        .returning(move || {
            vec![
                "live-supported".to_string(),
                "startover-supported".to_string(),
                "supports-dai-timeshifting".to_string(),
            ]
        });
    mock_taps_params
        .expect_get_details_btf_roles()
        .returning(move |_| {
            vec![
                "live-supported".to_string(),
                "startover-supported".to_string(),
                "supports-dai-timeshifting".to_string(),
            ]
        });

    mock_taps_params
        .expect_get_presentation_scheme()
        .returning(move |_| "living-room-react".to_string());

    // Player capabilities
    provide_context::<RwSignal<Option<PlayerCapabilities>>>(
        ctx.scope(),
        create_rw_signal(
            ctx.scope(),
            Some(PlayerCapabilities {
                resize_support: None,
                hdr_support: Some(Hdr10),
                uhd_support: true,
            }),
        ),
    );

    // Rust features
    provide_context_test_rust_features(ctx.scope());

    // Routing
    setup_mock_routing(ctx.clone());

    // Other data
    provide_context::<Rc<dyn TapsParameterStore>>(ctx.scope(), Rc::new(mock_taps_params));
    provide_context::<AppConfigContext>(ctx.scope(), Rc::new(mock_app_config));

    // Auth
    let mut mock_auth = MockAuth::new_without_params(ctx.scope());
    let access_token_signal = create_rw_signal(ctx.scope(), get_access_token());
    mock_auth.set_access_token_signal(access_token_signal);

    // Constantly refresh the access token from the file
    let task_guard = ctx.schedule_repeating_task(Duration::from_secs(30), move || {
        access_token_signal.set(get_access_token());
    });
    provide_context(ctx.scope(), Rc::new(task_guard));

    let access_token = get_access_token();
    mock_auth.set_access_token(access_token);

    provide_context::<AuthContext>(ctx.scope(), Rc::new(mock_auth));

    let title_details_signal = create_title_details_signals(ctx.scope());
    let media_background_signal = create_rw_signal(ctx.scope(), MediaBackgroundType::None);

    provide_context::<RwSignal<MediaBackgroundType>>(ctx.scope(), media_background_signal);
    provide_context::<TitleDetailsSignals>(ctx.scope(), title_details_signal);

    provide_context::<HybridPlaybackContext>(
        ctx.scope(),
        HybridPlaybackContext {
            should_release: create_rw_signal(ctx.scope(), None),
            seamless_egress: store_value(ctx.scope(), false),
        },
    );

    provide_context::<RwSignal<ErrorState>>(
        ctx.scope(),
        create_rw_signal(ctx.scope(), ErrorState::no_error()),
    );

    provide_context::<NavControl>(
        ctx.scope(),
        NavControl {
            show_top_nav_intent: create_rw_signal(ctx.scope(), false),
            set_report_top_nav_visibility: create_rw_signal(ctx.scope(), None).write_only(),
            top_nav_mode: create_rw_signal(ctx.scope(), TopNavMode::TopNav).read_only(),
            focused_nav: create_focus_value_signal(ctx.scope()),
            show_utility_nav: create_rw_signal(ctx.scope(), false),
            disable_top_nav_focus_trap: create_rw_signal(ctx.scope(), false),
            disable_utility_nav_focus_trap: create_rw_signal(ctx.scope(), false),
            enable_focus: create_rw_signal(ctx.scope(), true),
        },
    );

    provide_media_background_context(ctx.scope());

    #[allow(clippy::needless_borrow, reason = "examples")]
    let details_context: DetailsPageContext = match create_details_context_from_app_context(&ctx) {
        None => panic!("DetailsPageContext could not be constructed!"),
        Some(context) => {
            let mut details_ctx: DetailsPageContextStore = context;
            details_ctx.state.title_details.global = title_details_signal.data;
            details_ctx.state.title_details.global_opacity = title_details_signal.opacity;
            details_ctx.state.title_details.global_y_offset = title_details_signal.y_offset;
            details_ctx.state.media_background = media_background_signal;

            #[allow(clippy::expect_used, reason = "examples")]
            details_ctx
                .state
                .loaded_title_id
                .set(Some(DEFAULT_MOCKED_TITLE_ID.to_string()));

            #[allow(clippy::expect_used, reason = "examples")]
            create_wrapped_rc_context_and_provide(&ctx, details_ctx)
                .expect("should succeed creating context")
        }
    };

    // Device info
    let device_info = get_device_info();

    // Setup network
    details_context.borrow_mut().network_client = Rc::new(RefCell::new(
        NetworkClient::new_with_device_information(ctx, device_info),
    ));

    let details_example_info = create_rw_signal(
        ctx.scope(),
        DetailsPageExampleInfo {
            index: 0,
            example_info: None,
            fade_task_id: None,
            should_be_displayed: false,
        },
    );
    provide_context::<RwSignal<DetailsPageExampleInfo>>(ctx.scope(), details_example_info);
}

fn update_response(scope: Scope, data: DetailsPageExampleData) {
    let context = match use_details_page_context(scope) {
        None => panic!("Failed to create details context [init_helpers]"),
        Some(ctx) => ctx,
    };
    scope.batch(move || {
        context
            .borrow()
            .state
            .season_episodes_state
            .current_episode_idx
            .set(0);
        context
            .borrow()
            .state
            .season_episodes_state
            .last_focused_episode_idx
            .set(None);

        if let ExampleResponseSetting::StubbedResponse(response) = data.response.clone() {
            #[allow(
                clippy::redundant_clone,
                clippy::expect_used,
                clippy::string_to_string,
                reason = "examples"
            )]
            let details_response = details_page_atf_parser(response.to_string())
                .map(|result| match result {
                    network::common::DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                    network::common::DeviceProxyResponse::ErrorResponse(_) => {
                        panic!("unexpected error response")
                    }
                })
                .expect("VOD response to be parsed successfully");

            let atf_gti = details_response.title_decoration.gti.clone();
            context
                .borrow()
                .state
                .page_resources
                .atf_response
                .set(Some(details_response));

            set_details_page_location(scope, atf_gti.clone());
            context.borrow().state.loaded_title_id.set(Some(atf_gti));

            context
                .borrow()
                .state
                .page_resources
                .atf_state
                .set(ResourceState::Loaded);
        }

        if let ExampleResponseSetting::StubbedResponse(btf_response) = data.btf_response.clone() {
            #[allow(
                clippy::redundant_clone,
                clippy::expect_used,
                clippy::string_to_string,
                reason = "examples"
            )]
            let details_btf_response = details_page_btf_parser(btf_response.to_string())
                .map(|result| match result {
                    network::common::DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                    network::common::DeviceProxyResponse::ErrorResponse(_) => {
                        panic!("unexpected error response")
                    }
                })
                .expect("BTF response to be parsed successfully");
            context
                .borrow()
                .state
                .page_resources
                .btf_response
                .set(Some(details_btf_response));

            context
                .borrow()
                .state
                .page_resources
                .btf_state
                .set(ResourceState::Loaded);
        }

        if let ExampleResponseSetting::NetworkGti(gti) = data.response {
            #[allow(clippy::redundant_clone, clippy::string_to_string, reason = "examples")]
            set_details_page_location(scope, gti.to_string());
        };

        if let Some(additional) = data.additional_data {
            context
                .borrow()
                .state
                .title_actions_state
                .is_ssm_enabled
                .set(additional.is_ssm_enabled);
            context
                .borrow()
                .state
                .title_actions_state
                .taps_fallback_enabled
                .set(additional.taps_fallback_enabled);
        }
    });
}

pub fn create_example_data_signal(
    ctx: &AppContext,
    responses: Rc<Vec<DetailsPageExampleData>>,
    starting_example_idx: i32,
) -> RwSignal<usize> {
    let has_index_changed = create_rw_signal(ctx.scope(), false);
    let index_signal = create_rw_signal(ctx.scope(), starting_example_idx as usize);
    #[allow(clippy::expect_used, reason = "examples")]
    let details_page_example_info = use_context::<RwSignal<DetailsPageExampleInfo>>(ctx.scope())
        .expect("DetailsPageExampleInfo to exist");
    #[allow(clippy::expect_used, reason = "examples")]
    let details_ctx = use_details_page_context(ctx.scope()).expect("details context to exist");

    #[allow(clippy::redundant_clone, reason = "examples")]
    let responses_effect = responses.clone();
    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        move |_| {
            let example_index = index_signal.get();
            #[allow(clippy::expect_used, reason = "examples")]
            let example_data = responses_effect
                .get(example_index)
                .expect("Example index to be valid data")
                .clone();

            transition_to_page_from_details(
                &ctx,
                &details_ctx,
                PageType::Rust(RUST_DETAILS),
                false,
                Rc::new({
                    let ctx = ctx.clone();
                    move || {
                        // Small hack to make it so the response signals update after the initial details render not before
                        if !has_index_changed.get_untracked() {
                            ctx.schedule_task(Instant::now().add(Duration::from_millis(200)), {
                                let example_data = example_data.clone();
                                let scope = ctx.scope();
                                move || {
                                    #[allow(clippy::redundant_clone, reason = "examples")]
                                    update_response(scope, example_data.clone());
                                }
                            });
                            has_index_changed.set_untracked(false);
                        } else {
                            update_response(ctx.scope(), example_data.clone());
                        }

                        if let Some(fade_id) =
                            details_page_example_info.get_untracked().fade_task_id
                        {
                            ctx.cancel_task(fade_id)
                        }

                        let mut example_info = DetailsPageExampleInfo {
                            index: index_signal.get() as u32,
                            example_info: Some(example_data.clone()),
                            should_be_displayed: true,
                            fade_task_id: None,
                        };

                        let update_example_info = example_info.clone();
                        let fade_id = ctx.schedule_task(
                            Instant::now().add(Duration::from_millis(1500)),
                            move || {
                                details_page_example_info.set(DetailsPageExampleInfo {
                                    //fade_task_id: None,
                                    should_be_displayed: false,
                                    ..update_example_info
                                });
                            },
                        );

                        example_info.fade_task_id = Some(fade_id);
                        details_page_example_info.set(example_info);
                    }
                }),
            );
        }
    });

    index_signal
}
