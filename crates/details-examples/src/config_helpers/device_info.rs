use homedir::my_home;
use ignx_compositron::device_information::DeviceInformation;
use log::error;
use serde::Deserialize;
use std::fs::File;
use std::io::Read;

#[derive(Deserialize)]
pub struct DeviceInformationConfig {
    pub chipset: String,
    pub device_type_id: String,
    pub device_id: String,
    pub firmware_version: String,
    pub manufacturer: String,
    pub model_name: String,
    pub os_name: String,
    pub os_locale: Option<String>,
    pub player_name: Option<String>,
}

fn get_default_device_info() -> DeviceInformation {
    DeviceInformation {
        chipset: "Unknown".to_string(),

        // These are the default details of the emulator
        device_type_id: "AFOQV1TK6EU6O".to_string(),
        device_id: "uuid0d02b8fb0e724c609d75b9ee0cbd29e4".to_string(),

        firmware_version: "".to_string(),
        is_app_exit_supported: false,
        supports_avif_decoding: false,
        manufacturer: "Apple".to_string(),
        model_name: "Macintosh".to_string(),
        os_name: "".to_string(),
        os_locale: None,
        player_name: None,
        player_version: None,
        avpk_version: None,
        native_version: None,
        platform_execution_mode: None,
        terminator_id: "".to_string(),
    }
}

fn get_device_info_from_config(data: String) -> DeviceInformation {
    match serde_json::from_str::<DeviceInformationConfig>(&data) {
        Ok(data) => DeviceInformation {
            chipset: data.chipset,
            device_type_id: data.device_type_id,
            device_id: data.device_id,
            firmware_version: data.firmware_version,
            is_app_exit_supported: false,
            supports_avif_decoding: false,
            manufacturer: data.manufacturer,
            model_name: data.model_name,
            os_name: data.os_name,
            os_locale: data.os_locale,
            player_name: data.player_name,
            player_version: None,
            avpk_version: None,
            native_version: None,
            platform_execution_mode: None,
            terminator_id: "".to_string(),
        },
        Err(err) => {
            error!("Failed to parse device_info file {:?}", err);
            get_default_device_info()
        }
    }
}

pub fn get_device_info() -> DeviceInformation {
    let home_dir = match my_home() {
        Ok(dir) => {
            let Some(dir) = dir else {
                error!("Failed to get homedir not attempt to get auth info");
                return get_default_device_info();
            };
            dir
        }
        Err(err) => {
            error!(
                "Failed to get homedir not attempt to get auth info {:?}",
                err
            );
            return get_default_device_info();
        }
    };

    let auth_token_file_path = home_dir
        .join(".rust_details_data")
        .join("device_details.json");
    if !auth_token_file_path.exists() {
        return get_default_device_info();
    }

    let Ok(mut auth_file) = File::open(auth_token_file_path) else {
        error!("Failed to open auth file");
        return get_default_device_info();
    };
    let mut data = "".to_string();
    let _ = File::read_to_string(&mut auth_file, &mut data);
    get_device_info_from_config(data)
}
