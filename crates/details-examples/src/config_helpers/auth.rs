use homedir::my_home;
use log::error;
use std::fs::File;
use std::io::Read;

pub fn get_access_token() -> Option<String> {
    let home_dir = match my_home() {
        Ok(dir) => {
            let Some(dir) = dir else {
                error!("Failed to get homedir not attempt to get auth info");
                return None;
            };
            dir
        }
        Err(err) => {
            error!(
                "Failed to get homedir not attempt to get auth info {:?}",
                err
            );
            return None;
        }
    };

    let auth_token_file_path = home_dir.join(".rust_details_data").join("access_token");
    if !auth_token_file_path.exists() {
        return None;
    }

    let Ok(mut auth_file) = File::open(auth_token_file_path) else {
        error!("Failed to open auth file");
        return None;
    };
    let mut data = "".to_string();
    let _ = File::read_to_string(&mut auth_file, &mut data);
    Some(data)
}
