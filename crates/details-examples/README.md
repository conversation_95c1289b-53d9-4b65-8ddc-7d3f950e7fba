# Details Examples
This package contains all the details page examples

## Why a separate crate?
- Ignx compositron uses a global mocked timer for testing in the `details` crate that prevents scheduled tasks from running how you'd expect
  - By creating a seperate crate we can depend on ignx-compistron seperately without the `mock_timer` feature

## Features 
The main example in the crate is `details_page_stubbed`
The idea is it replicates the application while allowing mocking and rendering specific components of the details page
- Creates a full app like context:
  - Top nav
  - All required details dependencies
    - Rust features
    - Player capabilities
    - Taps roles 
- Configurable
  - Sign in / Out (Signed in is a bit more complex see setup instructions)
  - Create semi stubbed details pages, fully or completely loaded from network
  - Saved as examples via tags which can then be seperated via discrete examples if needed

Note: You do not need to utilize the functionality of details_page_stubbed and the example system if not required feel free to create a simple example if that is all you need.

## Why not just use Proxyman?
For most things especially if you **just** need to modify a network response use proxyman its much more simple and you'll save yourself some time setting up examples.
**However** if you require any of the following the example(s) can be helpful:
  - Quicker iteration by directly mounting the component you need to test
  - Allows working on a component before its linked with the details page directly
  - Serves as a repository of edge cases and/or different layouts of details pages that can be tested by going through each
    - For example you can load through the following within 10-20 seconds
      - All variations of title actions
      - Signed out / Signed in variations
      - Modal examples
      - Titles referenced by Gtis
      - Mocked Live titles (which aren't even supported directly by the details page **yet**)

## What can't be done via the examples
- Interactions with other pages if you need to test Collections->DP or DP->DP or any other combination use the hybrid application
- Deeplinking (it just won't work on the examples)
- *Some network requests* (* this only applies if you haven't done the signed in setup otherwise they all should work)
- Localization 
  - Currently, this is all handled by JS and examples do not have access to it

## Using the native rebuilder script
- The native rebuilder script is an alternative to using vscode configurations
  - You can use those however the configurations may not always be up to date
- By running `build-tools/native-rebuilder.sh --crate details-examples --features details_example --example details_page_stubbed --kill_existing --proxyman`
  - This will allow you to run the details main example and it will kill any previous spawned process if already existing!
  - If you are using RustRover or other JetBrains IDE I recommend putting the above into a shell script configuration then you can quickly run it via a shortcut

## Signed in setup (recommended) 
- **Proxyman** is required for the initial setup if you want to use signed in pages.
  - Additionally, you will need a **PRO license of proxyman**
- Take the script from `details-examples/scripts/proxyman_script.js`
- Go into proxyman `Scripting > Script List`
- Click the + button in the bottom left of the window
- Set the url to `(.*?/collections/collectionsPageInitial.*|https://api.amazon.co.uk/auth/token|https://api.amazon.co.uk/auth/register)`
- Set the method to `ANY` and the type to `Use Regex` (the default is `Use wildcard` at the time of writing)
- Paste in the script contents into the editor window and replace all previous contents
- Press `Save & Activate`
- Run the hybrid application and navigate to collections and wait a couple seconds
  - If it worked successfully you should see device_info and access_token within `~/rust_details_data` in your home directory.
  - These are used by the example code to authenticate you with endpoints
- **(Important) Close the hybrid application**
  - If you don't close the application it will de-register the access token after a short period and cause the example app to stop working

## How to add a new example
- Go to `src/details_stubbed_data.rs`
- In the `all_examples()` function add your example with references to the mock responses (if required)
- If you need to add a new mock response this is added within the details crate at: `crates/details/network/mock_responses`
  - This is done because a lot of the mock responses that are helpful for this example are likely helpful for tests too.

## Will any of this effect bundle size?
Nope! All code, examples and mocks are all excluded from the prod bundle and are behind a feature flag

## How will examples and this stay working long term?
In general the build will compile the examples during CI so it will always build there may occasionally be breakages if the details page is significantly updated.

## Known Issues
### Logged out after 5-10 Minutes
- Currently the auth breaks after 5/10 Minutes I'm currently looking for a fix but currently haven't found a reliable way to get it working

## Troubleshooting
### Icons aren't loading?
- Seems to be an intermittent issue as of right now try relaunching the example and it should self-resolve.

### Season&Episodes carousel isn't loading
- You need to follow the `Signed in examples` setup for most S&E carousels to load

### Requests all of a sudden stopped working
- This is likely because your access token got revoked, re-enable the script to collect a new `access_token` and `device_info` and then run the example again

## References
[Proxyman](https://proxyman.io/)