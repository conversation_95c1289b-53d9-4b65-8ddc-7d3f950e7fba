[package]
name = "details-examples"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
details.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
serde.workspace = true
serde_json.workspace = true
collections-ui.workspace = true
common-transform-types.workspace = true
container-list.workspace = true
container-types.workspace = true
containers.workspace = true
fableous.workspace = true
amzn-fable-tokens.workspace = true
auth.workspace = true
location.workspace = true
network.workspace = true
app-config.workspace = true
router.workspace = true
navigation-menu.workspace = true
title-details.workspace = true
media-background.workspace = true
transition-executor.workspace = true
cfg-test-attr-derive.workspace = true
log.workspace = true
watchlist-service.workspace = true
personalization-feedback-service.workspace = true
details-derive.workspace = true
profile-manager.workspace = true
mockall_double.workspace = true
strum.workspace = true
strum_macros.workspace = true
cross-app-events.workspace = true
playback-navigation.workspace = true
taps-parameters.workspace = true
rust-features.workspace = true
mockall.workspace = true
beekeeper.workspace = true
homedir = { version = "=0.3.3" , optional = true}

[dev-dependencies]
# Just used to help construct types
contextual-menu.workspace = true
contextual-menu-types.workspace = true
details = { workspace = true, features = ["details_example"] }
mockall.workspace = true

[lints]
workspace = true

[features]
details_example = ["details/details_example", "dep:homedir"]
default = []

[[example]]
name = "details_page_stubbed"
crate-type = ["cdylib"]
path = "examples/details_page_stubbed.rs"
required-features = ["details_example"]
