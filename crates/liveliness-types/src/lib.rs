use ignx_compositron::prelude::*;
use std::str::FromStr;

#[derive(Clone, PartialEq, Debug)]
pub enum Liveliness {
    Live,
    Delayed,
    Postponed,
    Suspended,
    Ended,
    Cancelled,
    Upcoming,
    OnNow,
}

impl AssociatedSignalType for Liveliness {
    type DerivedType = Liveliness;

    type SignalType = DataSignal<Liveliness>;
}

impl SignalSetDataMut<Liveliness> for Liveliness {
    fn set_data_mut(&mut self, _: Scope, new_value: Liveliness) -> bool {
        *self = new_value;
        true
    }
}

impl SignalSetRefMut<Liveliness> for Liveliness {
    fn set_mut(&mut self, scope: Scope, new_value: Liveliness) -> bool {
        self.set_data_mut(scope, new_value)
    }
}

impl SignalFromData<Liveliness> for Liveliness {
    fn from_data(_: Scope, value: Liveliness) -> Liveliness {
        value
    }
}

impl SignalDeepClone for Liveliness {
    fn deep_clone(&self, _: Scope) -> Liveliness {
        self.clone()
    }
}

impl FromStr for Liveliness {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "LIVE" => Ok(Liveliness::Live),
            "DELAYED" => Ok(Liveliness::Delayed),
            "POSTPONED" => Ok(Liveliness::Postponed),
            "SUSPENDED" => Ok(Liveliness::Suspended),
            "ENDED" => Ok(Liveliness::Ended),
            "CANCELLED" => Ok(Liveliness::Cancelled),
            "UPCOMING" => Ok(Liveliness::Upcoming),
            "ON NOW" => Ok(Liveliness::OnNow),
            _ => Err("Not a supported liveliness state".into()),
        }
    }
}
