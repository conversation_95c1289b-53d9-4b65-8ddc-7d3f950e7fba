use cross_app_events::impression_tracker::ImpressionTracker;
use cross_app_events::{
    send_highlight_impression_event, send_select_impression_event, send_view_impression_event,
    ImpressionData,
};
use ignx_compositron::impression::ViewImpressionData;
use ignx_compositron::prelude::Scope;
use mockall::automock;
use std::cell::RefCell;
use std::rc::Rc;

#[automock]
pub trait ImpressionSender {
    fn send_view_impression_event(
        &self,
        scope: Scope,
        view_data: ViewImpressionData,
        impression: ImpressionData,
    );
    fn send_select_impression_event(&self, scope: Scope, impression: ImpressionData);
    fn send_highlight_impression_event(&self, scope: Scope, impression: ImpressionData);
}

#[derive(<PERSON><PERSON>, Co<PERSON>, Default)]
pub struct ImpressionServiceSender;

impl ImpressionSender for ImpressionServiceSender {
    fn send_view_impression_event(
        &self,
        scope: Scope,
        view_data: ViewImpressionData,
        impression: ImpressionData,
    ) {
        send_view_impression_event(scope, view_data, impression);
    }

    fn send_select_impression_event(&self, scope: Scope, impression: ImpressionData) {
        send_select_impression_event(scope, impression);
    }

    fn send_highlight_impression_event(&self, scope: Scope, impression: ImpressionData) {
        send_highlight_impression_event(scope, impression);
    }
}

pub struct ImpressionService {
    scope: Scope,
    tracker: Rc<RefCell<ImpressionTracker<String>>>,
    sender: Rc<dyn ImpressionSender>,
}

enum ImpressionEventType {
    View,
    Highlight,
    Select,
}

impl ImpressionService {
    pub fn new(scope: Scope) -> Self {
        let tracker = Rc::new(RefCell::new(ImpressionTracker::default()));
        let sender = Rc::new(ImpressionServiceSender::default());
        ImpressionService {
            scope,
            tracker,
            sender,
        }
    }

    pub fn new_with_sender(scope: Scope, sender: Rc<dyn ImpressionSender>) -> Self {
        let tracker = Rc::new(RefCell::new(ImpressionTracker::default()));
        ImpressionService {
            scope,
            tracker,
            sender,
        }
    }

    /// Returns based on if an impression has been sent already
    /// If returning true it will update the internal tracker to mark that impression to ensure it's not duplicated
    fn should_send_impression_event(
        &self,
        data: &'_ ImpressionData,
        event_type: ImpressionEventType,
    ) -> bool {
        if let Some(id) = data
            .slot_id
            .map(|(idx, container_idx)| format!("{}-{}", idx, container_idx))
        {
            let should_send = match event_type {
                ImpressionEventType::View => !self.tracker.borrow().was_viewed(&id),
                ImpressionEventType::Highlight => !self.tracker.borrow().was_highlighted(&id),
                ImpressionEventType::Select => !self.tracker.borrow().was_selected(&id),
            };

            if should_send {
                match event_type {
                    ImpressionEventType::View => self.tracker.borrow_mut().set_viewed(id),
                    ImpressionEventType::Highlight => self.tracker.borrow_mut().set_highlighted(id),
                    ImpressionEventType::Select => self.tracker.borrow_mut().set_selected(id),
                }
            }

            should_send
        } else {
            // If we are unable to determine an ID based on slot
            // default to allowing it to be sent
            true
        }
    }
}

#[automock]
pub trait ImpressionReporting {
    fn report_view(&self, item: ImpressionData, view_impression_data: ViewImpressionData);
    fn report_highlight(&self, item: ImpressionData);
    fn report_select(&self, item: ImpressionData);
    fn report_select_untracked(&self, item: ImpressionData);
    fn reset(&self);
}

impl ImpressionReporting for ImpressionService {
    fn report_view(&self, item: ImpressionData, view_impression_data: ViewImpressionData) {
        if self.should_send_impression_event(&item, ImpressionEventType::View) {
            self.sender
                .send_view_impression_event(self.scope, view_impression_data, item)
        }
    }

    fn report_highlight(&self, item: ImpressionData) {
        if self.should_send_impression_event(&item, ImpressionEventType::Highlight) {
            self.sender
                .send_highlight_impression_event(self.scope, item)
        }
    }

    fn report_select(&self, item: ImpressionData) {
        if self.should_send_impression_event(&item, ImpressionEventType::Select) {
            self.sender.send_select_impression_event(self.scope, item)
        }
    }

    fn report_select_untracked(&self, item: ImpressionData) {
        self.sender.send_select_impression_event(self.scope, item)
    }

    fn reset(&self) {
        self.tracker.borrow_mut().clear_all()
    }
}

#[cfg(test)]
mod test {
    use crate::{ImpressionReporting, ImpressionService, MockImpressionSender};
    use cross_app_events::ImpressionData;
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::impression::{ViewEventTrigger, ViewImpressionData};
    use std::rc::Rc;

    fn create_impression_data(index: u32, container_idx: u32) -> ImpressionData {
        ImpressionData {
            widget_type: None,
            ref_marker: None,
            content_type: None,
            benefit_id: None,
            content_id: None,
            creative_id: None,
            analytics: None,
            slot_id: Some((index, container_idx)),
            size: None,
            carousel_analytics: None,
        }
    }

    fn create_impression_data_no_slot_id() -> ImpressionData {
        ImpressionData {
            widget_type: None,
            ref_marker: None,
            content_type: None,
            benefit_id: None,
            content_id: None,
            creative_id: None,
            analytics: None,
            slot_id: None,
            size: None,
            carousel_analytics: None,
        }
    }

    fn create_view_data() -> ViewImpressionData {
        ViewImpressionData {
            in_view_time: Default::default(),
            in_view_stripe: 0.0,
            time_to_view: Default::default(),
            event_trigger: ViewEventTrigger::PageLoad,
        }
    }

    #[test]
    fn should_track_send_and_clear_view_events() {
        launch_only_scope(|scope| {
            let mut sender = MockImpressionSender::default();
            sender
                .expect_send_view_impression_event()
                .times(5)
                .returning(|_, _, _| {});

            let impression_service = ImpressionService::new_with_sender(scope, Rc::new(sender));
            impression_service.report_view(create_impression_data(0, 0), create_view_data());
            impression_service.report_view(create_impression_data(0, 0), create_view_data());
            impression_service.report_view(create_impression_data(1, 1), create_view_data());
            impression_service.report_view(create_impression_data_no_slot_id(), create_view_data());
            impression_service.report_view(create_impression_data_no_slot_id(), create_view_data());

            impression_service.reset();
            impression_service.report_view(create_impression_data(0, 0), create_view_data());
        });
    }

    #[test]
    fn should_track_send_and_clear_highlight_events() {
        launch_only_scope(|scope| {
            let mut sender = MockImpressionSender::default();
            sender
                .expect_send_highlight_impression_event()
                .times(5)
                .returning(|_, _| {});

            let impression_service = ImpressionService::new_with_sender(scope, Rc::new(sender));
            impression_service.report_highlight(create_impression_data(0, 0));
            impression_service.report_highlight(create_impression_data(0, 0));
            impression_service.report_highlight(create_impression_data(1, 1));
            impression_service.report_highlight(create_impression_data_no_slot_id());
            impression_service.report_highlight(create_impression_data_no_slot_id());

            impression_service.reset();
            impression_service.report_highlight(create_impression_data(0, 0));
        })
    }

    #[test]
    fn should_track_send_and_clear_select_events() {
        launch_only_scope(|scope| {
            let mut sender = MockImpressionSender::default();
            sender
                .expect_send_select_impression_event()
                .times(5)
                .returning(|_, _| {});

            let impression_service = ImpressionService::new_with_sender(scope, Rc::new(sender));
            impression_service.report_select(create_impression_data(0, 0));
            impression_service.report_select(create_impression_data(0, 0));
            impression_service.report_select(create_impression_data(1, 1));
            impression_service.report_select(create_impression_data_no_slot_id());
            impression_service.report_select(create_impression_data_no_slot_id());

            impression_service.reset();
            impression_service.report_select(create_impression_data(0, 0));
        })
    }

    #[test]
    fn should_send_untracked_even_with_duplicates() {
        launch_only_scope(|scope| {
            let mut sender = MockImpressionSender::default();
            sender
                .expect_send_select_impression_event()
                .times(5)
                .returning(|_, _| {});

            let impression_service = ImpressionService::new_with_sender(scope, Rc::new(sender));
            impression_service.report_select_untracked(create_impression_data(0, 0));
            impression_service.report_select_untracked(create_impression_data(0, 0));
            impression_service.report_select(create_impression_data(0, 0));

            impression_service.reset();
            impression_service.report_select_untracked(create_impression_data(0, 0));
            impression_service.report_select(create_impression_data(0, 0));
        })
    }
}
