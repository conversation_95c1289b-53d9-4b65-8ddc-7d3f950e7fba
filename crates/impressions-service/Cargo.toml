[package]
name = "impressions-service"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
container-types.workspace = true
cross-app-events.workspace = true
mockall.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils"] }
rstest.workspace = true

[lints]
workspace = true

[features]
debug_impl = []
