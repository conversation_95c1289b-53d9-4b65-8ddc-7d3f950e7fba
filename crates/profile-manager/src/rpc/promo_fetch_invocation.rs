#[double]
use crate::try_use_profile_manager;
#[double]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::context::AppContext;
use mockall_double::double;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
struct FetchPromoArgs {
    enabled: bool,
}

pub fn register_fetch_promotions_invocation(ctx: &AppContext, rpc_manager: RPCManager) {
    let ctx = ctx.clone();
    rpc_manager.register_cross_app_function(
        "fetch_promotions_via_rpc".into(),
        move |_: FetchPromoArgs| {
            let mut fetch_promotion_succeeded = false;

            if let Some(profile_manager) = try_use_profile_manager(ctx.scope()) {
                log::info!("[profile_selection_page] Initiating promotions fetch.");
                fetch_promotion_succeeded = profile_manager.initiate_promotion_fetch(&ctx);
            }

            if fetch_promotion_succeeded {
                Ok(true)
            } else {
                Err("Unable to initiate promotion fetch via RPC".into())
            }
        },
    );
}

#[cfg(test)]
mod tests {
    use crate::rpc::promo_fetch_invocation::register_fetch_promotions_invocation;
    use crate::MockProfileManager;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::app::rpc::MockRPCManager;
    use ignx_compositron::reactive::provide_context;
    use std::error::Error;

    use super::FetchPromoArgs;

    #[test]
    fn should_register_fetch_promotions_invocation_successfully() {
        launch_only_app_context(|ctx| {
            let mut mock_rpc_manager = MockRPCManager::new();
            let mut mock_profile_manager = MockProfileManager::default();
            let mut mock_profile_manager_2 = MockProfileManager::default();

            mock_rpc_manager
                .expect_register_cross_app_function()
                .once()
                .return_once(
                    |_, callback: Box<dyn Fn(FetchPromoArgs) -> Result<bool, Box<dyn Error>>>| {
                        callback(FetchPromoArgs { enabled: true })
                            .expect("promotion fetch invocation failed");
                    },
                );

            mock_profile_manager_2
                .expect_initiate_promotion_fetch()
                .return_once(|_| true);

            mock_profile_manager
                .expect_clone()
                .once()
                .return_once(|| mock_profile_manager_2);

            provide_context(ctx.scope(), mock_profile_manager);

            register_fetch_promotions_invocation(&(ctx.clone()), mock_rpc_manager);
        });
    }
}
