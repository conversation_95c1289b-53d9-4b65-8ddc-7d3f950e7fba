use common_transform_types::profile::PromotionsForProfileResponse;
use ignx_compositron::prelude::AppContext;
use network::{common::DeviceProxyResponse, RequestError};
use network_parser::core::network_parse_from_str;

pub fn promotions_for_profile_response_parser_callback(
    _ctx: &AppContext,
    response: String,
    _status_code: i32,
    cb: Box<dyn FnOnce(Result<DeviceProxyResponse<PromotionsForProfileResponse>, RequestError>)>,
) {
    cb(promotions_for_profile_response_parser(response))
}

pub fn promotions_for_profile_response_parser(
    response: String,
) -> Result<DeviceProxyResponse<PromotionsForProfileResponse>, RequestError> {
    network_parse_from_str(&response).map_err(RequestError::DeserializationFailed)
}

#[cfg(test)]
mod test {
    use super::*;

    mod promotions_for_profile_response_parser {
        use super::*;
        use common_transform_types::actions::Action::TransitionAction;
        use common_transform_types::actions::SwiftAction;
        use common_transform_types::actions::TransitionAction::legacyDetail;
        use common_transform_types::container_items::EntitlementMessageIcons::ENTITLED_ICON;
        use common_transform_types::container_items::{
            Badges, CarouselItemData, EntitlementGlance, EntitlementMessage, EntitlementMessaging,
            EventMetadata, HeroCard, TitleCardBaseMetadata,
        };
        use common_transform_types::containers::FullscreenHeroItem::HERO_CARD;
        use common_transform_types::containers::{
            Container, ContainerMetadata, FullscreenHeroDeserialization,
        };
        use std::collections::HashMap;

        #[test]
        fn empty_string_to_error() {
            let input = "";
            let e = promotions_for_profile_response_parser(input.into());
            assert!(e.is_err());
        }

        #[test]
        fn valid_response_should_be_correctly_parsed() {
            let valid_response_str = include_str!(
                "../../assets/mock_responses/promotions_for_profile/valid_response.json"
            );

            let expected_promotions_response = PromotionsForProfileResponse {
                containerList: vec![
                    Container::FULL_SCREEN_HERO(FullscreenHeroDeserialization {
                        containerMetadata: ContainerMetadata {
                            id: "V2=4AEA6u69gYPeuYe-toZwYWdlSWSIcGFnZVR5cGWMY29sbGVjdGlvbklkiHdpZGdldElkjo5zd2lmdElkVmVyc2lvbt6-ioRob21li4dwcm9maWxljI6mMToxMzJMRUtUWDI2Rkk4SSMjTkJTWEUzMkRNRlpHNjVMVE1WV0GND46CVjI=".to_string(),
                            offerType: None,
                            entitlement: None,
                            analytics: HashMap::from([
                                ("refMarker".to_string(), "pv_hom_pvp_DyyZTP_1".to_string()),
                                ("ClientSideMetrics".to_string(), "400|ClgKKkF3YXJlbmVzc1Byb2ZpbGVwYWdlSGVyb0xpdmVEZWZhdWx0RGVmYXVsdBIQMToxMzJMRUtUWDI2Rkk4SRoQMToxMzJMRUtUWDI2Rkk4SSIGRHl5WlRQEj8KB3Byb2ZpbGUSBGhvbWUiBmNlbnRlcioAMiQwODRlNTA4NC0xN2FiLTRjYTctYTA1Ni1iYTM5ZDQ3OTE3NjMaA2FsbCIDYWxsKgAyDGhlcm9DYXJvdXNlbDoJQXdhcmVuZXNzQhRBd2FyZW5lc3NQcm9maWxlSGVyb1ILbm90RW50aXRsZWRaAGIORnVsbHNjcmVlbkhlcm9oAXIAeiRjZGZmYjRiMS1lYWU4LTQ5NzEtODcxNC0yNmJlMTJmZDc2NzWCAQNhbGyKAQCSAQA=".to_string()),
                            ]),
                            tags: vec![],
                            badges: None
                        },
                        journeyIngressContext: Some("8|EgNhbGw=".to_string()),
                        items: vec![HERO_CARD(
                            HeroCard {
                                carouselCardMetadata: CarouselItemData {
                                    transformItemId: Some("amzn1.dv.gti.d8a9f6fd-58c9-64dd-15d0-365bdf846db4".to_string()),
                                    title: Some("The Hobbit: An Unexpected Journey".to_string()),
                                    synopsis: Some("Hobbit Bilbo Baggins joins 13 dwarves on a quest to reclaim the lost kingdom of Erebor.".to_string()),
                                    action: Some(TransitionAction(legacyDetail( SwiftAction{
                                        serviceToken: None,
                                        pageId: "amzn1.dv.gti.d8a9f6fd-58c9-64dd-15d0-365bdf846db4".to_string(),
                                        pageType: "detail".to_string(),
                                        analytics: HashMap::from([
                                            ("itemProducerID".to_string(), "awareness-dome".to_string()),
                                            ("refMarker".to_string(), "pv_hom_pvp_DyyZTP_HS2e6b23_1_1".to_string()),
                                        ]),
                                        refMarker: "pv_hom_pvp_DyyZTP_HS2e6b23_1_1".to_string(),
                                        text: None,
                                        journeyIngressContext: Some("8|EgNhbGw=".to_string()),
                                    }))),
                                    deferredAction: None,
                                    actions: vec![],
                                    widgetType: Some("heroTitle".to_string()),
                                },
                                titleCardBaseMetadata: TitleCardBaseMetadata {
                                    coverImage: None, // Not provided in the JSON
                                    boxartImage: None,
                                    titleLogoImage: Some("https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_SVOD_The_Hobbit_An_Unexpected_Journey_Remaster/341e407e-2db1-4611-af42-a2437382ec35.png".to_string()),
                                    providerLogoImage: None,
                                    poster2x3Image: None, // Not provided in the JSON
                                    heroImage: Some("https://m.media-amazon.com/images/S/sonata-images-prod/US_3P_SVOD_The_Hobbit_An_Unexpected_Journey_Remaster/ef0b3393-c881-4cc7-8c8c-9be8af9f1947.jpeg".to_string()),
                                    totalReviewCount: None, // Not provided in the JSON
                                    overallRating: None, // Not provided in the JSON
                                    gti: Some("amzn1.dv.gti.d8a9f6fd-58c9-64dd-15d0-365bdf846db4".to_string()),
                                    badges: Some(Badges {
                                        applyAudioDescription: true,
                                        applyCC: true,
                                        applyDolby: true,
                                        applyDolbyAtmos: false, // Not specified in JSON, assuming false
                                        applyDolbyVision: false,
                                        applyHdr10: false,
                                        applyPrime: true,
                                        applyUhd: true,
                                        regulatoryRating: Some("PG-13".to_string()),
                                        showPSE: false,
                                    }),
                                    contentType: Some("MOVIE".to_string()),
                                    publicReleaseDate: None, // Not provided in the JSON
                                    runtimeSeconds: None, // Not provided in the JSON
                                    entitlementStatus: Some("ENTITLED".to_string()),
                                    entitlementMessaging: Some(EntitlementMessaging {
                                        GLANCE_MESSAGE_SLOT: Some(EntitlementGlance {
                                            message: Some("".to_string()),
                                            icon: None,
                                            level: None,
                                            glance_type: None,
                                        }),
                                        ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                                            message: Some("Included with Prime".to_string()),
                                            icon: Some(ENTITLED_ICON),
                                        }),
                                        TITLE_METADATA_BADGE_SLOT: None,
                                        HIGH_VALUE_MESSAGE_SLOT: None,
                                        HIGH_VALUE_MESSAGE_SLOT_LITE: None,
                                        INFORMATIONAL_MESSAGE_SLOT: None,
                                        BUYBOX_MESSAGE_SLOT: None,
                                        PRODUCT_SUMMARY_SLOT: None,
                                        PRODUCT_PROMOTION_SLOT: None,
                                    }),
                                    genres: vec![], // Not provided in the JSON
                                    watchProgress: None, // Not provided in the JSON
                                    imageAlternateText: None, // Not provided in the JSON
                                    isEntitled: None, // Assuming true based on entitlementStatus
                                    offerText: None, // Not provided in the JSON
                                    isInWatchlist: Some(false),
                                    maturityRatingString: Some("".to_string()),
                                    regulatoryLabel: None,
                                    maturityRatingImage: None,
                                    imageAttributes: None, // Not provided in the JSON
                                    contextualActions: None,
                                },
                                providerLogoImageMetadata: None,
                                eventMetadata: EventMetadata {
                                    liveliness: None,
                                    liveEventDateBadge: None,
                                    liveEventDateHeader: None,
                                    venue: None,
                                    scoreBug: None,
                                },
                                moreDetailsAction: Some(TransitionAction(legacyDetail( SwiftAction {
                                    pageId: "amzn1.dv.gti.d8a9f6fd-58c9-64dd-15d0-365bdf846db4".to_string(),
                                    pageType: "detail".to_string(),
                                    analytics: HashMap::from([
                                        ("itemProducerID".to_string(), "awareness-dome".to_string()),
                                        ("refMarker".to_string(), "pv_hom_pvp_DyyZTP_HS2e6b23_1_1".to_string()),
                                    ]),
                                    refMarker: "pv_hom_pvp_DyyZTP_HS2e6b23_1_1".to_string(),
                                    text: None,
                                    journeyIngressContext: Some("8|EgNhbGw=".to_string()),
                                    serviceToken: None,
                                }))),
                                tnfProperty: None,
                                callToAction: None,
                                tournamentIcid: None,
                                regulatoryLabel: None,
                            }

                        )],
                    })
                ]
            };

            let parsed_promotions_response =
                match promotions_for_profile_response_parser(valid_response_str.into()).unwrap() {
                    DeviceProxyResponse::LRCEdgeResponse(r) => r.resource,
                    DeviceProxyResponse::ErrorResponse(_) => panic!("unexpected error response"),
                };

            assert_eq!(parsed_promotions_response, expected_promotions_response);
        }
    }
}
