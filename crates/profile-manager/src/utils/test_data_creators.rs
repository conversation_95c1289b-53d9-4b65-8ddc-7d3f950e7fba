#[cfg(test)]
use common_transform_types::actions::Action::TransitionAction;
#[cfg(test)]
use common_transform_types::actions::SwiftAction;
#[cfg(test)]
use common_transform_types::actions::TransitionAction::legacyDetail;
#[cfg(test)]
use common_transform_types::container_items::{
    Badges, CarouselItemData, EntitlementMessage, EntitlementMessageIcons, EntitlementMessaging,
    EventMetadata, HeroCard, HighValueMessage, IndividualImageMetadata,
    IndividualImageMetadataMapping, LiveEventDateTime, LocalizedLiveEventBadge,
    LocalizedLiveEventHeader, TitleCardBaseMetadata,
};
#[cfg(test)]
use std::collections::HashMap;

#[cfg(test)]
fn carousel_card_metadata() -> CarouselItemData {
    CarouselItemData {
        transformItemId: Some("a mock id".to_string()),
        title: Some("a mock title".to_string()),
        synopsis: Some("a mock synopsis".to_string()),
        action: None,
        deferredAction: None,
        actions: vec![],
        widgetType: Some("a mock widget type".to_string()),
    }
}
#[cfg(test)]
pub fn create_event_metadata(liveliness: Option<String>) -> EventMetadata {
    EventMetadata {
        liveliness,
        liveEventDateBadge: Some(LiveEventDateTime::LOCALIZED_BADGE(
            LocalizedLiveEventBadge {
                text: "Some Date and Time".to_string(),
            },
        )),
        liveEventDateHeader: Some(LiveEventDateTime::LOCALIZED_HEADER(
            LocalizedLiveEventHeader {
                date: Some("Some Date".to_string()),
                time: Some("Some Time".to_string()),
            },
        )),
        venue: Some("Some venue".to_string()),
        scoreBug: None,
    }
}
#[cfg(test)]
fn title_card_metadata(
    hero_image_available: bool,
    gti_available: bool,
    entitlement_status: Option<String>,
) -> TitleCardBaseMetadata {
    TitleCardBaseMetadata {
        coverImage: Some("cover image".to_string()),
        boxartImage: Some("box art image".to_string()),
        titleLogoImage: Some("title logo image".to_string()),
        providerLogoImage: Some("provider logo image".to_string()),
        poster2x3Image: Some("poster image".to_string()),
        heroImage: if hero_image_available {
            Some("hero_image_url".to_string())
        } else {
            None
        },
        totalReviewCount: Some(0),
        overallRating: Some(0.0),
        gti: if gti_available {
            Some("gti".to_string())
        } else {
            None
        },
        badges: Some(Badges {
            applyAudioDescription: false,
            applyCC: false,
            applyDolby: false,
            applyDolbyAtmos: false,
            applyDolbyVision: false,
            applyHdr10: false,
            applyPrime: false,
            applyUhd: false,
            regulatoryRating: Some("18".to_string()),
            showPSE: false,
        }),
        contentType: Some("MOVIE".to_string()),
        publicReleaseDate: None,
        runtimeSeconds: None,
        entitlementStatus: entitlement_status,
        entitlementMessaging: Some(EntitlementMessaging {
            ENTITLEMENT_MESSAGE_SLOT: Some(EntitlementMessage {
                message: Some("Entitled".to_string()),
                icon: Some(EntitlementMessageIcons::ENTITLED_ICON),
            }),
            HIGH_VALUE_MESSAGE_SLOT: Some(HighValueMessage {
                message: Some("High Value Message".to_string()),
                icon: None,
                level: None,
                hvm_type: None,
            }),
            HIGH_VALUE_MESSAGE_SLOT_LITE: None,
            GLANCE_MESSAGE_SLOT: None,
            TITLE_METADATA_BADGE_SLOT: None,
            INFORMATIONAL_MESSAGE_SLOT: None,
            BUYBOX_MESSAGE_SLOT: None,
            PRODUCT_SUMMARY_SLOT: None,
            PRODUCT_PROMOTION_SLOT: None,
        }),
        genres: vec![],
        watchProgress: None,
        imageAlternateText: None,
        isEntitled: None,
        offerText: None,
        isInWatchlist: None,
        maturityRatingString: Some("maturity_rating_string".to_string()),
        maturityRatingImage: None,
        regulatoryLabel: None,
        imageAttributes: Some(common_transform_types::container_items::ImageAttributes {
            isAdult: Some(true),
            isRestricted: None,
            individualImageMetadata: Some(IndividualImageMetadataMapping {
                providerLogoImage: Some(IndividualImageMetadata {
                    height: Some(200),
                    width: Some(300),
                    scalarHorizontal: None,
                    scalarStacked: None,
                    safeToOverlay: None,
                }),
            }),
        }),
        contextualActions: None,
    }
}
#[cfg(test)]
pub fn hero_card(
    hero_image_available: bool,
    gti_available: bool,
    entitlement_status: Option<String>,
) -> HeroCard {
    HeroCard {
        carouselCardMetadata: carousel_card_metadata(),
        titleCardBaseMetadata: title_card_metadata(
            hero_image_available,
            gti_available,
            entitlement_status,
        ),
        providerLogoImageMetadata: None,
        eventMetadata: create_event_metadata(None),
        moreDetailsAction: Some(TransitionAction(legacyDetail(SwiftAction {
            pageId: "gti".to_string(),
            pageType: "detail".to_string(),
            analytics: HashMap::from([
                ("itemProducerID".to_string(), "awareness-dome".to_string()),
                (
                    "refMarker".to_string(),
                    "pv_hom_pvp_DyyZTP_HS2e6b23_1_1".to_string(),
                ),
            ]),
            refMarker: "pv_hom_pvp_DyyZTP_HS2e6b23_1_1".to_string(),
            text: None,
            journeyIngressContext: Some("jic".to_string()),
            serviceToken: None,
        }))),
        tnfProperty: None,
        callToAction: None,
        tournamentIcid: None,
        regulatoryLabel: None,
    }
}
