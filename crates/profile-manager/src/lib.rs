#![allow(nonstandard_style, reason = "JSON compability")]

mod network;
pub mod rpc;
mod utils;

mod home_page_props;

pub use home_page_props::*;
use std::cell::RefCell;
use std::rc::Rc;

use common_transform_types::profile::{
    Profile, ProfilePermission, ProfileSelectionPage, PromotionsForProfileResponse,
};
#[double]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::prelude::{
    AppContext, DataSignal, OptionSignal, SignalFromData, SignalSetData, VecSignal,
};
use ignx_compositron::{app::rpc::RPCError, reactive::*};
use mockall::mock;
use mockall_double::double;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use profile_selection_shared::components::fullscreen_hero::{
    FullscreenHeroComponentItem, FullscreenHeroComponentItemSignal,
};
use serde::Serialize;
use serde_json::{json, Value};

#[double]
use crate::network::client::NetworkClient;
use crate::network::client::PromotionRequests;
use cfg_test_attr_derive::derive_test_only;
use synchronized_state_store::{MockStateDispatcher, StateDispatcher, SynchronizedStateStore};

#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
use cross_app_events::send_app_event;

pub fn use_profile_manager(scope: Scope) -> ProfileManager {
    #[allow(clippy::expect_used, reason = "fatal if not available")]
    use_context::<ProfileManager>(scope)
        .expect("ProfileManager was not found in the given context.")
}

pub fn try_use_profile_manager(scope: Scope) -> Option<ProfileManager> {
    use_context::<ProfileManager>(scope)
}

pub fn mock_try_use_profile_manager(scope: Scope) -> Option<MockProfileManager> {
    use_context::<MockProfileManager>(scope)
}

pub fn mock_use_profile_manager(scope: Scope) -> MockProfileManager {
    #[allow(clippy::expect_used, reason = "mock")]
    use_context::<MockProfileManager>(scope)
        .expect("MockProfileManager was not found in the given context.")
}

const PROFILES_MANAGER_STORE_ID: &str = "profile-manager";
const SET_PROFILES_CROSS_APP_FUNCTION: &str = "setProfiles";
const SET_ACTIVE_PROFILE_CROSS_APP_FUNCTION: &str = "setActiveProfile";

#[derive(Serialize, NetworkParsed, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct SynchronisedProfileState {
    activeProfileId: Option<String>,
    profiles: Vec<Profile>,
    canCreateProfileHint: Option<bool>,
    createOrAssociatePermission: Option<ProfilePermission>,
    errorLoadingProfiles: bool,
}

#[derive(Clone)]
pub enum PromotionTransformResponse {
    None,
    Loading,
    Success((PromotionsForProfileResponse, bool)),
    Error(bool),
}

#[derive(Clone)]
pub struct ProfileManager {
    profile_manager_synchronised_state_store: SynchronizedStateStore<SynchronisedProfileState>,
    rpc_manager: RPCManager,
    active_profile_signal: Signal<Option<Profile>>,
    active_profile_id_signal: Signal<Option<String>>,
    promotion_transform_response_signal: RwSignal<PromotionTransformResponse>,
    promotion_data_option_signal:
        OptionSignal<VecSignal<DataSignal<FullscreenHeroComponentItemSignal>>>,
}

impl ProfileManager {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher, rpc_manager: RPCManager) -> Self {
        Self::new_with_initial_state(
            scope,
            rpc_manager,
            state_dispatcher,
            None,
            vec![],
            None,
            None,
            false,
        )
    }

    fn new_with_initial_state(
        scope: Scope,
        rpc_manager: RPCManager,
        state_dispatcher: &StateDispatcher,
        active_profile_id: Option<String>,
        profiles: Vec<Profile>,
        canCreateProfileHint: Option<bool>,
        createOrAssociatePermission: Option<ProfilePermission>,
        errorLoadingProfiles: bool,
    ) -> Self {
        let profile_manager_synchronised_state_store = SynchronizedStateStore::new(
            scope,
            state_dispatcher,
            PROFILES_MANAGER_STORE_ID.into(),
            SynchronisedProfileState {
                activeProfileId: active_profile_id,
                profiles,
                canCreateProfileHint,
                createOrAssociatePermission,
                errorLoadingProfiles,
            },
        );

        let state_store_signal = profile_manager_synchronised_state_store.get_read_signal();

        Self {
            profile_manager_synchronised_state_store,
            rpc_manager,
            active_profile_signal: Signal::derive(scope, move || {
                state_store_signal
                    .try_with(|store| store.activeProfileId.clone())
                    .flatten()
                    .and_then(|active_profile_id| {
                        state_store_signal.try_with(|state| {
                            state
                                .profiles
                                .iter()
                                .find(|profile| profile.id == *active_profile_id)
                                .cloned()
                        })
                    })
                    .flatten()
            }),
            active_profile_id_signal: Signal::derive(scope, move || {
                state_store_signal
                    .try_with(|store| store.activeProfileId.clone())
                    .flatten()
            }),
            promotion_data_option_signal: OptionSignal::from_data(scope, None),
            promotion_transform_response_signal: create_rw_signal(
                scope,
                PromotionTransformResponse::None,
            ),
        }
    }

    pub fn get_active_profile_id(&self) -> Signal<Option<String>> {
        self.active_profile_id_signal
    }

    pub fn get_active_profile(&self) -> Signal<Option<Profile>> {
        self.active_profile_signal
    }

    pub fn get_profiles(&self) -> Vec<Profile> {
        self.profile_manager_synchronised_state_store.get().profiles
    }

    pub fn get_error_loading_profiles(&self) -> bool {
        self.profile_manager_synchronised_state_store
            .get()
            .errorLoadingProfiles
    }

    pub fn get_profile_selection_page_data(&self) -> ProfileSelectionPage {
        let profile_synchronised_state = self.profile_manager_synchronised_state_store.get();
        ProfileSelectionPage {
            showCreateButton: profile_synchronised_state
                .canCreateProfileHint
                .unwrap_or(false),
            createOrAssociatePermission: profile_synchronised_state.createOrAssociatePermission,
            profiles: profile_synchronised_state.profiles,
        }
    }

    pub fn set_active_profile(
        &self,
        active_profile_id: &str,
        is_adult: bool,
        is_user_action: bool,
        on_success: Option<Box<dyn FnOnce()>>,
        on_error: Option<Box<dyn FnOnce()>>,
    ) {
        let error_callback = Box::new(move |error: RPCError| {
            log::error!("set_active_profile error: {}", error.to_string());
            if let Some(callback) = on_error {
                callback();
            }
        });

        let success_callback = Box::new(|_| {
            if let Some(callback) = on_success {
                callback();
            }
        });

        self.rpc_manager
            .call::<bool>(SET_ACTIVE_PROFILE_CROSS_APP_FUNCTION)
            .arg("activeProfileId", active_profile_id.into())
            .arg("isAdult", is_adult.into())
            .arg("isUserAction", is_user_action.into())
            .success_callback(success_callback)
            .error_callback(error_callback)
            .send();
    }

    pub fn set_profiles(
        &self,
        profiles: Vec<Profile>,
        on_success: Option<Box<dyn FnOnce()>>,
        on_error: Option<Box<dyn FnOnce()>>,
    ) {
        let error_callback = Box::new(move |error: RPCError| {
            log::error!("set_profiles error: {}", error.to_string());
            if let Some(callback) = on_error {
                callback();
            }
        });

        let success_callback = Box::new(|_| {
            if let Some(callback) = on_success {
                callback();
            }
        });

        self.rpc_manager
            .call::<bool>(SET_PROFILES_CROSS_APP_FUNCTION)
            .arg("profiles", json!(profiles))
            .success_callback(success_callback)
            .error_callback(error_callback)
            .send();
    }

    // This function will only return true for the initial case where
    // there's neither an ongoing fetch, nor an already completed fetch
    pub fn should_initiate_promotion_fetch(&self) -> bool {
        matches!(
            self.promotion_transform_response_signal.get_untracked(),
            PromotionTransformResponse::None
        )
    }

    // Function that is actually responsible for fetching the promotion-response from downstream
    // and, storing it in the promotion_transform_response_signal
    // Profile-Manager is unable to convert this data into data signal.
    // Profile-selection-page is listening on this signal, and will process it as soon as it is stored
    pub fn initiate_promotion_fetch(&self, appContext: &AppContext) -> bool {
        if !matches!(
            self.promotion_transform_response_signal.get_untracked(),
            PromotionTransformResponse::Loading
        ) {
            self.promotion_transform_response_signal
                .set(PromotionTransformResponse::Loading);
            if let Some(promotion_data) = self.promotion_data_option_signal.get() {
                promotion_data.clear();
                self.promotion_data_option_signal.set_data(None);
            }

            let client = NetworkClient::new(appContext);
            let self_rc = Rc::new(RefCell::new(self.clone()));
            let scope = appContext.scope();

            // It could be that there is no active profile when the promo fetch request is called.
            // This can happen if the list profiles call has failed and the current auth token is
            // ACCOUNT one. But, ACCOUNT token will always correspond to an adult profile so, the following
            // will still work.
            let is_kids_profile = self
                .get_active_profile()
                .try_get_untracked()
                .flatten()
                .is_some_and(|p| !p.isAdult);

            let promotion_fetch_failure_cb = {
                let self_rc = Rc::clone(&self_rc);
                move |e| {
                    let this = self_rc.borrow();
                    app_event(scope, "GET_PROMOTIONS_FAILED");
                    this.promotion_transform_response_signal
                        .set(PromotionTransformResponse::Error(is_kids_profile));
                    log::error!(
                        "[profile_manager] Could not retrieve promotions. is_kid_profile: {}, Error: {}",
                        is_kids_profile,
                        e
                    );
                }
            };

            let promotion_fetch_success_cb = {
                let self_rc = Rc::clone(&self_rc);
                move |promo_response: PromotionsForProfileResponse| {
                    let this = self_rc.borrow();
                    app_event(scope, "GET_PROMOTIONS_COMPLETED");
                    log::info!("[profile_manager] Successfully retrieved promotions for profile. is_kid_profile: {}", is_kids_profile);
                    this.promotion_transform_response_signal.set(
                        PromotionTransformResponse::Success((promo_response, is_kids_profile)),
                    );
                }
            };

            client.promotions_for_profile(promotion_fetch_success_cb, promotion_fetch_failure_cb);
            true
        } else {
            log::error!(
                "[profile_manager] Could not initiate promotions because the promotions transform response was Loading."
            );
            false
        }
    }

    pub fn get_promotion_transform_response(&self) -> RwSignal<PromotionTransformResponse> {
        self.promotion_transform_response_signal
    }

    pub fn update_promotion_data(&self, updated_data: Vec<FullscreenHeroComponentItem>) {
        let updated_data = if updated_data.is_empty() {
            None
        } else {
            Some(updated_data)
        };
        self.promotion_data_option_signal.set_data(updated_data);
    }

    pub fn get_promotion_data(
        &self,
    ) -> OptionSignal<VecSignal<DataSignal<FullscreenHeroComponentItemSignal>>> {
        self.promotion_data_option_signal
    }

    pub fn reset_promotion_transform_response_if_loading(&self) {
        if matches!(
            self.get_promotion_transform_response().get_untracked(),
            PromotionTransformResponse::Loading
        ) {
            self.promotion_transform_response_signal
                .set_untracked(PromotionTransformResponse::None)
        }
    }
}

mock! {
    pub ProfileManager {
        pub fn new(scope: Scope, state_dispatcher: &MockStateDispatcher, rpc_manager: RPCManager) -> Self;
        pub fn get_active_profile_id(&self) -> Signal<Option<String>>;
        pub fn get_active_profile(&self) -> Signal<Option<Profile>>;
        pub fn get_profiles(&self) -> Vec<Profile>;
        pub fn get_error_loading_profiles(&self) -> bool;
        pub fn get_profile_selection_page_data(&self) -> ProfileSelectionPage;
        pub fn set_active_profile(
            &self,
            active_profile_id: &str,
            is_adult: bool,
            is_user_action: bool,
            on_success: Option<Box<dyn FnOnce()>>,
            on_error: Option<Box<dyn FnOnce()>>,
        );
        pub fn set_profiles(
            &self,
            profiles: Vec<Profile>,
            on_success: Option<Box<dyn FnOnce()>>,
            on_error: Option<Box<dyn FnOnce()>>,
        );
        pub fn should_initiate_promotion_fetch(&self) -> bool;
        pub fn initiate_promotion_fetch(&self, ctx: &AppContext) -> bool;
        pub fn get_promotion_transform_response(&self) -> RwSignal<PromotionTransformResponse>;
        pub fn update_promotion_data(&self, updated_data: Vec<FullscreenHeroComponentItem>);
        pub fn get_promotion_data(&self,) -> OptionSignal<VecSignal<DataSignal<FullscreenHeroComponentItemSignal>>>;
        pub fn reset_promotion_transform_response_if_loading(&self);
    }

    impl Clone for ProfileManager {
        fn clone(&self) -> Self;
    }
}

pub fn app_event(scope: Scope, name: &'static str) {
    #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
    send_app_event(scope, name, "PROFILES", None);
}

#[cfg(test)]
mod test {
    use super::*;
    use crate::utils::test_data_creators::hero_card;
    use ::network::RequestError;
    use common_transform_types::containers::FullscreenHeroItem::HERO_CARD;
    use common_transform_types::containers::{
        Container, ContainerMetadata, FullscreenHeroDeserialization,
    };
    use common_transform_types::profile::ProfileAvatar;
    use ignx_compositron::app::rpc::{MockRPCCall, MockRPCManager};
    use ignx_compositron::app::{launch_only_app_context, launch_only_scope};
    use rstest::rstest;
    use serial_test::serial;
    use std::sync::Mutex;
    use std::{cell::Cell, rc::Rc};

    static MTX: Mutex<()> = Mutex::new(());

    #[test]
    fn returns_none_active_profile() {
        launch_only_scope(|scope| {
            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let rpc_manager: RPCManager = MockRPCManager::new();
            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                false,
            );

            assert_eq!(profile_manager.get_active_profile_id().get(), None);
            assert_eq!(profile_manager.get_active_profile().get(), None);
            assert_eq!(profile_manager.get_profiles(), vec![]);
        });
    }

    #[test]
    fn returns_none_when_active_profile_not_present() {
        launch_only_scope(|scope| {
            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let rpc_manager: RPCManager = MockRPCManager::new();
            let mock_active_profile_id = Some("mock_active_profile_id".to_owned());

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                mock_active_profile_id.clone(),
                vec![],
                None,
                None,
                false,
            );

            assert_eq!(
                profile_manager.get_active_profile_id().get(),
                mock_active_profile_id
            );
            assert_eq!(profile_manager.get_active_profile().get(), None);
            assert_eq!(profile_manager.get_profiles(), vec![]);
        });
    }

    #[test]
    fn returns_active_profile() {
        launch_only_scope(|scope| {
            let mock_active_profile_id = "mock_active_profile_id".to_owned();
            let mock_active_profile = Profile {
                id: mock_active_profile_id.clone(),
                avatar: ProfileAvatar {
                    avatarId: "someAvatarId".to_owned(),
                    avatarUrl: "someAvatarUrl".to_owned(),
                    avatarDescription: None,
                },
                name: "someName".to_owned(),
                isActive: true,
                isAdult: true,
                profileIsImplicit: false,
                translationDetails: None,
                permissions: None,
            };
            let mock_profile: Profile = Profile {
                id: "some_other_profile".to_owned(),
                avatar: ProfileAvatar {
                    avatarId: "someAvatarId".to_owned(),
                    avatarUrl: "someAvatarUrl".to_owned(),
                    avatarDescription: None,
                },
                name: "someName".to_owned(),
                isActive: true,
                isAdult: true,
                profileIsImplicit: false,
                translationDetails: None,
                permissions: None,
            };

            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let rpc_manager: RPCManager = MockRPCManager::new();
            let profiles = vec![mock_profile, mock_active_profile.clone()];
            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                Some(mock_active_profile_id.clone()),
                profiles.clone(),
                None,
                None,
                false,
            );

            assert_eq!(
                profile_manager.get_active_profile_id().get(),
                Some(mock_active_profile_id)
            );
            assert_eq!(profile_manager.get_profiles(), profiles);
            assert_eq!(
                profile_manager.get_active_profile().get(),
                Some(mock_active_profile)
            );
        });
    }

    #[test]
    fn returns_error_loading_profiles() {
        launch_only_scope(|scope| {
            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let rpc_manager: RPCManager = MockRPCManager::new();

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                true,
            );

            assert!(profile_manager.get_error_loading_profiles());
        });
    }

    #[test]
    fn returns_profile_selection_page_data() {
        launch_only_scope(|scope| {
            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let rpc_manager: RPCManager = MockRPCManager::new();

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                true,
            );

            assert_eq!(
                profile_manager.get_profile_selection_page_data(),
                ProfileSelectionPage {
                    showCreateButton: false,
                    createOrAssociatePermission: None,
                    profiles: vec![],
                }
            );
        });
    }

    #[test]
    fn set_profiles_rpc_function_success() {
        launch_only_scope(|scope| {
            let state_dispatcher = StateDispatcher::new();
            let mut rpc_manager = MockRPCManager::new();

            let mut first_mock = MockRPCCall::<bool>::new();
            let mut second_mock = MockRPCCall::<bool>::new();
            let mut third_mock = MockRPCCall::<bool>::new();
            let mut fourth_mock = MockRPCCall::<bool>::new();

            fourth_mock.expect_send().return_once(|| ());
            third_mock
                .expect_error_callback()
                .return_once(|_| fourth_mock);
            second_mock
                .expect_success_callback()
                .return_once(|callback| {
                    callback(true);
                    third_mock
                });
            first_mock.expect_arg().return_once(|_, _| second_mock);

            rpc_manager.expect_call().return_once(|_| first_mock);

            let success_called = Rc::new(Cell::new(false));
            let success_called_clone = success_called.clone();
            let error_called = Rc::new(Cell::new(false));
            let error_called_clone = error_called.clone();

            let success_callback = Box::new(move || success_called_clone.set(true));
            let error_callback = Box::new(move || error_called_clone.set(true));

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                false,
            );

            profile_manager.set_profiles(vec![], Some(success_callback), Some(error_callback));

            assert!(success_called.get());
            assert!(!error_called.get());
        });
    }

    #[test]
    fn set_profiles_rpc_function_error() {
        launch_only_scope(|scope| {
            let state_dispatcher = StateDispatcher::new();
            let mut rpc_manager = MockRPCManager::new();

            let mut first_mock = MockRPCCall::<bool>::new();
            let mut second_mock = MockRPCCall::<bool>::new();
            let mut third_mock = MockRPCCall::<bool>::new();
            let mut fourth_mock = MockRPCCall::<bool>::new();

            fourth_mock.expect_send().return_once(|| ());
            third_mock.expect_error_callback().return_once(|callback| {
                callback(RPCError::TimeoutExceeded);
                fourth_mock
            });
            second_mock
                .expect_success_callback()
                .return_once(|_| third_mock);
            first_mock.expect_arg().return_once(|_, _| second_mock);

            rpc_manager.expect_call().return_once(|_| first_mock);

            let success_called = Rc::new(Cell::new(false));
            let success_called_clone = success_called.clone();
            let error_called = Rc::new(Cell::new(false));
            let error_called_clone = error_called.clone();

            let success_callback = Box::new(move || success_called_clone.set(true));
            let error_callback = Box::new(move || error_called_clone.set(true));

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                false,
            );

            profile_manager.set_profiles(vec![], Some(success_callback), Some(error_callback));

            assert!(!success_called.get());
            assert!(error_called.get());
        });
    }

    #[test]
    fn set_active_profile_rpc_function_success() {
        launch_only_scope(|scope| {
            let state_dispatcher = StateDispatcher::new();
            let mut rpc_manager = MockRPCManager::new();

            let mut first_mock = MockRPCCall::<bool>::new();
            let mut second_mock = MockRPCCall::<bool>::new();
            let mut third_mock = MockRPCCall::<bool>::new();
            let mut fourth_mock = MockRPCCall::<bool>::new();
            let mut fifth_mock = MockRPCCall::<bool>::new();
            let mut sixth_mock = MockRPCCall::<bool>::new();

            sixth_mock.expect_send().return_once(|| ());
            fifth_mock
                .expect_error_callback()
                .return_once(|_| sixth_mock);
            fourth_mock
                .expect_success_callback()
                .return_once(|callback| {
                    callback(true);
                    fifth_mock
                });
            third_mock.expect_arg().return_once(|_, _| fourth_mock);
            second_mock.expect_arg().return_once(|_, _| third_mock);
            first_mock.expect_arg().return_once(|_, _| second_mock);

            rpc_manager.expect_call().return_once(|_| first_mock);

            let success_called = Rc::new(Cell::new(false));
            let success_called_clone = success_called.clone();
            let error_called = Rc::new(Cell::new(false));
            let error_called_clone = error_called.clone();

            let success_callback = Box::new(move || success_called_clone.set(true));
            let error_callback = Box::new(move || error_called_clone.set(true));

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                false,
            );

            profile_manager.set_active_profile(
                "some_id",
                true,
                false,
                Some(success_callback),
                Some(error_callback),
            );

            assert!(success_called.get());
            assert!(!error_called.get());
        });
    }

    #[test]
    fn set_active_profile_rpc_function_error() {
        launch_only_scope(|scope| {
            let state_dispatcher = StateDispatcher::new();
            let mut rpc_manager = MockRPCManager::new();

            let mut first_mock = MockRPCCall::<bool>::new();
            let mut second_mock = MockRPCCall::<bool>::new();
            let mut third_mock = MockRPCCall::<bool>::new();
            let mut fourth_mock = MockRPCCall::<bool>::new();
            let mut fifth_mock = MockRPCCall::<bool>::new();
            let mut sixth_mock = MockRPCCall::<bool>::new();

            sixth_mock.expect_send().return_once(|| ());
            fifth_mock.expect_error_callback().return_once(|callback| {
                callback(RPCError::TimeoutExceeded);
                sixth_mock
            });
            fourth_mock
                .expect_success_callback()
                .return_once(|_| fifth_mock);
            third_mock.expect_arg().return_once(|_, _| fourth_mock);
            second_mock.expect_arg().return_once(|_, _| third_mock);
            first_mock.expect_arg().return_once(|_, _| second_mock);

            rpc_manager.expect_call().return_once(|_| first_mock);

            let success_called = Rc::new(Cell::new(false));
            let success_called_clone = success_called.clone();
            let error_called = Rc::new(Cell::new(false));
            let error_called_clone = error_called.clone();

            let success_callback = Box::new(move || success_called_clone.set(true));
            let error_callback = Box::new(move || error_called_clone.set(true));

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                false,
            );

            profile_manager.set_active_profile(
                "some_id",
                true,
                false,
                Some(success_callback),
                Some(error_callback),
            );

            assert!(!success_called.get());
            assert!(error_called.get());
        });
    }

    #[rstest]
    #[serial]
    #[case(None, false)]
    #[case(Some(get_mock_profile("adult_profile", true)), false)]
    #[case(Some(get_mock_profile("kids_profile", false)), true)]
    fn should_correctly_invoke_callbacks_and_signals_on_promo_fetch_success(
        #[case] active_profile: Option<Profile>,
        #[case] is_kids_profile_in_response: bool,
    ) {
        let _lock = MTX.lock();
        launch_only_app_context(move |ctx| {
            provide_context::<AppContext>(ctx.scope(), ctx.clone());
            let mut mock_network_client = NetworkClient::default();
            let network_context = NetworkClient::new_context();

            mock_network_client
                .expect_promotions_for_profile()
                .once()
                .return_once(|success, _| {
                    success(mock_promotions_for_profile());
                });

            network_context
                .expect()
                .once()
                .return_once(|_| mock_network_client);

            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let mut rpc_manager: RPCManager = MockRPCManager::new();
            let rpc_manager_clone: RPCManager = MockRPCManager::new();

            rpc_manager.expect_clone().return_once(|| rpc_manager_clone);

            let active_profile_id = active_profile.clone().and_then(|f| Some(f.id));
            let mut profiles = vec![];
            if let Some(active_profile) = active_profile {
                profiles.push(active_profile);
            };
            let profile_manager = ProfileManager::new_with_initial_state(
                ctx.scope(),
                rpc_manager,
                &state_dispatcher,
                active_profile_id,
                profiles,
                None,
                None,
                false,
            );

            //Initially there should be no promotion fetch ongoing, and no transform response in store
            //So it should be possible to initiate the promotion fetch
            let should_initiate_promotion_fetch = profile_manager.should_initiate_promotion_fetch();
            assert!(should_initiate_promotion_fetch);

            //Promotion fetch should initiate successfully
            let promotion_fetch_initiation_status =
                profile_manager.initiate_promotion_fetch(&(ctx.clone()));
            assert!(promotion_fetch_initiation_status);

            create_effect(ctx.scope(), move |_| {
                if let PromotionTransformResponse::Success(promo_response) =
                    profile_manager.get_promotion_transform_response().get()
                {
                    assert_eq!(promo_response.1, is_kids_profile_in_response);

                    // Creating this data as processed network response
                    let mock_data = vec![
                        FullscreenHeroComponentItem::default(),
                        FullscreenHeroComponentItem::default(),
                    ];

                    // Call the function with the mock data
                    profile_manager.update_promotion_data(mock_data.clone());

                    // After calling, the data should match the mock data.
                    let stored_promotion_data = profile_manager.get_promotion_data().get();
                    assert!(stored_promotion_data.is_some());

                    if let Some(promotion_data) = stored_promotion_data {
                        let promotion_data = promotion_data.get();

                        assert_eq!(promotion_data.len(), mock_data.len());

                        for i in 0..mock_data.len() {
                            assert_eq!(promotion_data[i].image_url.get(), mock_data[i].image_url);
                        }
                    }
                } else {
                    assert!(
                        false,
                        "profile_manager should have received promotions network response"
                    );
                }
            });
        })
    }

    #[rstest]
    #[serial]
    #[case(vec![], false)]
    #[serial]
    #[case(vec![FullscreenHeroComponentItem::default(),FullscreenHeroComponentItem::default(),], true)]
    fn should_only_update_promotion_data_to_some_if_the_vector_is_non_empty(
        #[case] mock_data: Vec<FullscreenHeroComponentItem>,
        #[case] should_promotion_data_be_some: bool,
    ) {
        launch_only_scope(move |scope| {
            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let rpc_manager: RPCManager = MockRPCManager::new();
            let mock_active_profile_id = Some("mock_active_profile_id".to_owned());

            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                mock_active_profile_id,
                vec![],
                None,
                None,
                false,
            );

            profile_manager.update_promotion_data(mock_data);

            assert_eq!(
                profile_manager.promotion_data_option_signal.get().is_some(),
                should_promotion_data_be_some
            );
        });
    }

    #[test]
    fn should_reset_pending_signal_along_with_data_signals_on_promo_fetch_failure() {
        let _lock = MTX.lock();
        launch_only_app_context(move |ctx| {
            provide_context::<AppContext>(ctx.scope(), ctx.clone());
            let mut mock_network_client = NetworkClient::default();
            let network_context = NetworkClient::new_context();

            mock_network_client
                .expect_promotions_for_profile()
                .once()
                .return_once(|_, failure| {
                    failure(RequestError::Builder("Error".to_string()));
                });

            network_context
                .expect()
                .once()
                .return_once(|_| mock_network_client);

            let state_dispatcher: StateDispatcher = StateDispatcher::new();
            let mut rpc_manager: RPCManager = MockRPCManager::new();
            let rpc_manager_clone: RPCManager = MockRPCManager::new();

            rpc_manager.expect_clone().return_once(|| rpc_manager_clone);

            let profile_manager = ProfileManager::new_with_initial_state(
                ctx.scope(),
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                false,
            );

            //Initially there should be no promotion fetch ongoing, and no transform response in store
            //So it should be possible to initiate the promotion fetch
            let should_initiate_promotion_fetch = profile_manager.should_initiate_promotion_fetch();
            assert!(should_initiate_promotion_fetch);

            //Promotion fetch should initiate successfully
            let promotion_fetch_initiation_status =
                profile_manager.initiate_promotion_fetch(&(ctx.clone()));
            assert!(promotion_fetch_initiation_status);
            assert!(matches!(
                profile_manager
                    .promotion_transform_response_signal
                    .get_untracked(),
                PromotionTransformResponse::Error(false)
            ));
            assert!(profile_manager.get_promotion_data().get().is_none());
        })
    }

    #[test]
    fn test_reset_promotion_transform_response_if_loading() {
        launch_only_scope(|scope| {
            let state_dispatcher = StateDispatcher::new();
            let rpc_manager = MockRPCManager::new();
            let profile_manager = ProfileManager::new_with_initial_state(
                scope,
                rpc_manager,
                &state_dispatcher,
                None,
                vec![],
                None,
                None,
                false,
            );

            // resets to None when state is Loading
            profile_manager
                .promotion_transform_response_signal
                .set(PromotionTransformResponse::Loading);
            profile_manager.reset_promotion_transform_response_if_loading();
            assert!(matches!(
                profile_manager
                    .promotion_transform_response_signal
                    .get_untracked(),
                PromotionTransformResponse::None
            ));

            // doesn't reset when state is Error
            profile_manager
                .promotion_transform_response_signal
                .set(PromotionTransformResponse::Error(false));
            profile_manager.reset_promotion_transform_response_if_loading();
            assert!(matches!(
                profile_manager
                    .promotion_transform_response_signal
                    .get_untracked(),
                PromotionTransformResponse::Error(false)
            ));

            // doesn't reset when state is Success
            let mock_response = (mock_promotions_for_profile(), false);
            profile_manager
                .promotion_transform_response_signal
                .set(PromotionTransformResponse::Success(mock_response));
            profile_manager.reset_promotion_transform_response_if_loading();
            assert!(matches!(
                profile_manager
                    .promotion_transform_response_signal
                    .get_untracked(),
                PromotionTransformResponse::Success(_)
            ));
        });
    }

    fn mock_promotions_for_profile() -> PromotionsForProfileResponse {
        PromotionsForProfileResponse {
            containerList: vec![Container::FULL_SCREEN_HERO(FullscreenHeroDeserialization {
                containerMetadata: ContainerMetadata {
                    id: "1".to_string(),
                    offerType: None,
                    entitlement: None,
                    analytics: Default::default(),
                    tags: vec![],
                    badges: None,
                },
                journeyIngressContext: None,
                items: vec![HERO_CARD(hero_card(
                    true,
                    true,
                    Some("ENTITLED".to_string()),
                ))],
            })],
        }
    }

    fn get_mock_profile(profile_id: &str, isAdult: bool) -> Profile {
        Profile {
            id: profile_id.to_owned(),
            avatar: ProfileAvatar {
                avatarId: "someAvatarId".to_owned(),
                avatarUrl: "someAvatarUrl".to_owned(),
                avatarDescription: None,
            },
            name: "someName".to_owned(),
            isActive: true,
            isAdult,
            profileIsImplicit: false,
            translationDetails: None,
            permissions: None,
        }
    }
}
