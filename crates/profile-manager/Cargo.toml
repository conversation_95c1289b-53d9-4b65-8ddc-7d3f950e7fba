[package]
name = "profile-manager"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
synchronized-state-store.workspace = true
mockall_double.workspace = true
serde.workspace = true
serde_json.workspace = true
log.workspace = true
mockall.workspace = true
common-transform-types.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
network-parser.workspace = true
network-parser-derive.workspace = true
profile-selection-shared.workspace = true
cross-app-events.workspace = true
deferred-navigation.workspace = true
network.workspace = true
app-config.workspace = true
derive_more.workspace = true
app-reporting.workspace = true
taps-parameters.workspace = true
router.workspace = true
auth.workspace = true
cfg-test-attr-derive.workspace = true

[dev-dependencies]
rstest.workspace = true
mockall.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
network = { workspace = true, features = ["test_utils", "mock_network"] }
serial_test.workspace = true

[lints]
workspace = true

[features]
debug_impl = []
