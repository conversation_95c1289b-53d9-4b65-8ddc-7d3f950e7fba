use crate::{types::NotificationPopup, util::clickstream::params_with_ref_marker};
use amzn_fable_tokens::{FableBorder, FableColor, FableOpacity, FableSpacing};
#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
use cross_app_events::send_app_event;
use fableous::{
    buttons::tertiary_button::*, typography::typography::*, utils::get_ignx_color, SCREEN_HEIGHT,
    SCREEN_WIDTH,
};
use ignx_compositron::{compose, Composer};
use ignx_compositron::{
    input::KeyCode,
    prelude::*,
    text::{LocalizedText, SubstitutionParameters, TextContent},
};
use std::{collections::HashMap, rc::Rc};

const ANNOUNCEMENT_STRING_ID: &str = "AV_LRC_CORNER_DIALOG_ANNOUNCEMENT";
const EXIT_STRING_ID: &str = "AV_LRC_CORNER_DIALOG_EXIT";
const CTA_STRING_ID: &str = "AV_LRC_CORNER_DIALOG_CTA";

const SOURCE: &str = "IAS";
const IAS_NOTIFICATION_BUTTON_CLICKED: &str = "IAS_NOTIFICATION_BUTTON_CLICKED";

#[Composer]
pub fn Notification(
    ctx: &AppContext,
    data: &NotificationPopup,
    on_start_press: Rc<dyn Fn()>,
    on_close: Rc<dyn Fn()>,
    scale_vec: Memo<Vec2>,
) -> StackComposable {
    let mut confirm_subsitution_params = HashMap::new();
    confirm_subsitution_params.insert(
        "ctaButtonText".to_string(),
        TextContent::String(data.startButton.translatedText.clone()),
    );

    let context_messages: Vec<TextContent> = vec![
        LocalizedText {
            string_id: ANNOUNCEMENT_STRING_ID.into(),
            substitution_parameters: None,
        }
        .into(),
        data.translatedText.clone().into(),
        LocalizedText {
            string_id: CTA_STRING_ID.into(),
            substitution_parameters: Some(SubstitutionParameters(confirm_subsitution_params)),
        }
        .into(),
        LocalizedText {
            string_id: EXIT_STRING_ID.into(),
            substitution_parameters: None,
        }
        .into(),
    ];

    let on_start_press = {
        let ctx = ctx.clone();
        let start_ref_marker = data.startButton.clickRefMarker.clone();

        move || {
            #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
            send_app_event(
                ctx.scope(),
                IAS_NOTIFICATION_BUTTON_CLICKED,
                SOURCE,
                params_with_ref_marker(&start_ref_marker),
            );
            on_start_press();
        }
    };

    let on_close_press = {
        let ctx = ctx.clone();
        let close_ref_marker = data.closeButton.clickRefMarker.clone();
        let on_close = on_close.clone();

        move || {
            #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
            send_app_event(
                ctx.scope(),
                IAS_NOTIFICATION_BUTTON_CLICKED,
                SOURCE,
                params_with_ref_marker(&close_ref_marker),
            );
            on_close();
        }
    };

    compose! {
        Stack() {
            Column() {
                TypographyHeading100(content: data.translatedText.clone()).test_id("survey-notification-header")
                    .flex(1.0)
                    .flex_fit(FlexFit::Loose)

                Row() {
                    TertiaryButton(variant: TertiaryButtonVariant::TextSize400(data.startButton.translatedText.clone().into()))
                        .focusable()
                        .preferred_focus(true)
                        .on_select(on_start_press)
                        .test_id("survey-notification-confirm")
                    Rectangle().width(FableSpacing::SPACING100)
                    TertiaryButton(variant: TertiaryButtonVariant::TextSize400(data.closeButton.translatedText.clone().into()))
                        .focusable()
                        .on_select(on_close_press)
                        .test_id("survey-notification-close")
                }
                .translate_y(FableSpacing::SPACING065)
                .focus_window()
                .preferred_focus(true)
                .test_id("survey-notification-buttons")
            }
            .background_color(get_ignx_color(FableColor::BACKGROUND))
            .min_height(200.0)
            .border_radius(FableBorder::RADIUS150)
            .opacity(FableOpacity::OPACITY096)
            .padding(Padding::all(FableSpacing::SPACING150))
            .translate_x(-60.0)
            .translate_y(-60.0)
            .scale(scale_vec)
            .test_id("survey-notification")
            .accessibility_context_messages(context_messages)
        }
        .alignment(Alignment::EndBottom)
        .height(SCREEN_HEIGHT)
        .width(SCREEN_WIDTH)
    }
    .on_key_down(KeyCode::Backspace, {
        let on_close = on_close.clone();
        move || on_close()
    })
    .on_key_down(KeyCode::Escape, {
        let on_close = on_close.clone();
        move || on_close()
    })
}

#[cfg(test)]
pub mod test {
    use crate::types::SurveyButton;

    use super::*;
    use ignx_compositron::{app::launch_test, test_utils::assert_node_exists};

    fn get_mock_data() -> NotificationPopup {
        NotificationPopup {
            displayRefMarker: "survey-noticication-display-refmarker".to_string(),
            clickRefMarker: "survey-noticication-click-refmarker".to_string(),
            translatedText: "Help make Prime Video better by answering two survey questions."
                .to_string(),
            startButton: SurveyButton {
                clickRefMarker: "notification_start_refmarker".to_string(),
                translatedText: "Continue to survey".to_string(),
            },
            closeButton: SurveyButton {
                clickRefMarker: "notification_close_refmarker".to_string(),
                translatedText: "I'm not interested".to_string(),
            },
        }
    }

    #[test]
    pub fn render_notification() {
        launch_test(
            move |ctx| {
                let data = get_mock_data();
                compose! { Notification(
                    data: &data,
                    on_start_press: Rc::new(|| {}),
                    on_close: Rc::new(|| {}),
                    scale_vec: create_memo(ctx.scope(), move |_| Vec2::new(0.9, 0.9))
                )}
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let notification = tree.find_by_test_id("survey-notification");

                assert_node_exists!(&notification);

                let children = notification.find_child_with().find_all();

                assert_eq!(children.len(), 2);

                let header = &children[0];

                assert_eq!(
                    header.borrow_props().test_id,
                    Some("survey-notification-header".to_string())
                );
                assert_eq!(
                    header.borrow_props().text,
                    Some(
                        "Help make Prime Video better by answering two survey questions."
                            .to_string()
                    )
                );

                let button_row = &children[1];

                assert_eq!(
                    button_row.borrow_props().test_id,
                    Some("survey-notification-buttons".to_string())
                );

                let elements = button_row.find_child_with().find_all();

                assert_eq!(elements.len(), 3);

                let confirm_button = &elements[0];

                assert_eq!(
                    confirm_button.borrow_props().test_id,
                    Some("survey-notification-confirm".to_string())
                );

                let spacing = &elements[1];

                assert_eq!(
                    spacing.borrow_props().layout.size.width,
                    FableSpacing::SPACING100
                );

                let close_button = &elements[2];

                assert_eq!(
                    close_button.borrow_props().test_id,
                    Some("survey-notification-close".to_string())
                );
            },
        );
    }

    #[test]
    pub fn handles_button_presses() {
        launch_test(
            move |ctx| {
                let data = get_mock_data();
                let button_pressed_times = create_rw_signal(ctx.scope(), (0, 0));

                provide_context(ctx.scope(), button_pressed_times);

                compose! { Notification(
                    data: &data,
                    on_start_press: Rc::new(move || {
                        button_pressed_times.set((button_pressed_times.get_untracked().0 + 1, button_pressed_times.get().1));
                    }),
                    on_close: Rc::new(move || {
                        button_pressed_times.set((button_pressed_times.get_untracked().0, button_pressed_times.get().1 + 1));
                    }),
                    scale_vec: create_memo(ctx.scope(), move |_| Vec2::new(0.9, 0.9))
                )}
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let confirm_button = tree
                    .find_by_test_id("survey-notification-confirm")
                    .borrow_props()
                    .node_id;
                let cancel_button = tree
                    .find_by_test_id("survey-notification-close")
                    .borrow_props()
                    .node_id;
                let button_pressed_times = use_context::<RwSignal<(i32, i32)>>(scope).unwrap();

                assert_eq!(button_pressed_times.get(), (0, 0));

                test_game_loop.send_on_select_event(confirm_button);
                test_game_loop.tick_until_done();

                assert_eq!(button_pressed_times.get(), (1, 0));

                test_game_loop.send_on_select_event(confirm_button);
                test_game_loop.tick_until_done();

                assert_eq!(button_pressed_times.get(), (2, 0));

                test_game_loop.send_on_select_event(cancel_button);
                test_game_loop.tick_until_done();

                assert_eq!(button_pressed_times.get(), (2, 1));

                test_game_loop.send_on_select_event(cancel_button);
                test_game_loop.tick_until_done();

                assert_eq!(button_pressed_times.get(), (2, 2));

                test_game_loop.send_key_down_up_event_to_node(confirm_button, KeyCode::Backspace);
                test_game_loop.tick_until_done();

                assert_eq!(button_pressed_times.get(), (2, 3));

                test_game_loop.send_key_down_up_event_to_node(confirm_button, KeyCode::Escape);
                test_game_loop.tick_until_done();

                assert_eq!(button_pressed_times.get(), (2, 4));
            },
        );
    }
}
