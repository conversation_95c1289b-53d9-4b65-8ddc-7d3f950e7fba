#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
use cross_app_events::send_app_event;
use fableous::{buttons::primary_button::*, typography::typography::*};
use ignx_compositron::{compose, Composer};
use ignx_compositron::{
    input::KeyCode,
    prelude::*,
    reactive::on_cleanup,
    text::{LocalizedText, SubstitutionParameters, TextContent},
    time::Instant,
};
use lrc_image::{
    lrc_image::*,
    types::{ImageData, ImageFormat, ImageTag},
};
use std::{collections::HashMap, rc::Rc, time::Duration};

use crate::{types::ThankYouPopup, util::clickstream::params_with_ref_marker};

const LOGO_URL: &str =
    "https://m.media-amazon.com/images/G/01/AVLRC/images/default/common/prime/pv_logo_white.png";
const SELECT_TO_CLOSE_OR_WAIT_STRING_ID: &str = "AV_LRC_SURVEY_SELECT_TO_CLOSE_OR_WAIT";

const SOURCE: &str = "IAS";
const IAS_DRAWER_BUTTON_CLICKED: &str = "IAS_DRAWER_BUTTON_CLICKED";

#[Composer]
pub fn Completed(
    ctx: &AppContext,
    data: &ThankYouPopup,
    on_close: Rc<dyn Fn()>,
) -> StackComposable {
    let close = Rc::new({
        let ctx = ctx.clone();
        let close_button_ref_marker = data.closeButton.clickRefMarker.clone();

        move || {
            #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
            send_app_event(
                ctx.scope(),
                IAS_DRAWER_BUTTON_CLICKED,
                SOURCE,
                params_with_ref_marker(&close_button_ref_marker),
            );
            on_close();
        }
    });

    let task_id = ctx.schedule_task(Instant::now() + Duration::from_secs(10), {
        let close = close.clone();
        move || close()
    });

    on_cleanup(ctx.scope(), {
        let ctx = ctx.clone();
        move || {
            ctx.cancel_task(task_id);
        }
    });

    let on_close_press = Rc::new({
        let ctx = ctx.clone();
        let close = close.clone();

        move || {
            ctx.cancel_task(task_id);
            close();
        }
    });

    let mut substitution_params = HashMap::new();
    substitution_params.insert(
        "numberOfSeconds".to_string(),
        TextContent::String("10".to_string()),
    );

    let context_messages: Vec<TextContent> = vec![
        data.translatedText.clone().into(),
        LocalizedText {
            string_id: SELECT_TO_CLOSE_OR_WAIT_STRING_ID.into(),
            substitution_parameters: Some(SubstitutionParameters(substitution_params)),
        }
        .into(),
    ];

    let on_close_button_select = {
        let on_close_press = on_close_press.clone();
        move || on_close_press()
    };

    compose! {
        Stack() {
            Column() {
                LRCImage(data: ImageData {
                    url: LOGO_URL.to_string(),
                    width: 159.0,
                    height: 48.0,
                    tags: vec![ImageTag::Format(ImageFormat::PNG)]
                })
                .test_id("logo")
                TypographyHeading200(content: data.translatedText.clone()).line_height(80.0).translate_y(100.0).test_id("confirmation-text")
                PrimaryButton(variant: PrimaryButtonVariant::TextSize200(TextContent::String(data.closeButton.translatedText.clone())))
                    .focusable()
                    .preferred_focus(true)
                    .on_select(on_close_button_select)
                    .test_id("close-button")
                    .translate_y(200.0)
            }
            .padding(Padding::vertical(210.0))
            .test_id("survey-completed")
            .accessibility_context_messages(context_messages)
            .focus_window()
            .on_key_down(KeyCode::Backspace, {
                let on_close_press = on_close_press.clone();
                move || on_close_press()
            })
            .on_key_down(KeyCode::Escape, {
                let on_close_press = on_close_press.clone();
                move || on_close_press()
            })
        }
    }
}

#[cfg(test)]
pub mod test {
    use crate::types::SurveyButton;

    use super::*;
    use ignx_compositron::{
        app::launch_test,
        node::NodeID,
        test_utils::{assert_node_exists, TestRendererGameLoop},
        time::MockClock,
    };
    use rstest::*;
    use std::cell::RefCell;

    fn get_mock_data() -> ThankYouPopup {
        ThankYouPopup {
            closeButton: SurveyButton {
                clickRefMarker: "thankyou-close-refmarker".to_string(),
                translatedText: "Close window".to_string(),
            },
            translatedText:
                "Thank you. We've received your answers.\n\nThis window will close automatically."
                    .to_string(),
            displayRefMarker: "thankyou-refmarker".to_string(),
        }
    }

    #[test]
    pub fn render_completed() {
        launch_test(
            move |ctx| {
                let data = get_mock_data();
                compose! { Completed(
                    data: &data,
                    on_close: Rc::new(|| {})
                )}
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let container = tree.find_by_test_id("survey-completed");

                assert_node_exists!(&container);

                let container_children = container.find_child_with().find_all();
                assert_eq!(container_children.len(), 3);

                let logo = &container_children[0];

                assert_node_exists!(&logo);
                assert_eq!(logo.borrow_props().test_id, Some("logo".to_string()));

                let text = &container_children[1];

                assert_eq!(
                    text.borrow_props().test_id,
                    Some("confirmation-text".to_string())
                );
                assert_eq!(text.borrow_props().text, Some("Thank you. We've received your answers.\n\nThis window will close automatically.".to_string()));

                let close_button = &container_children[2];

                assert_eq!(
                    close_button.borrow_props().test_id,
                    Some("close-button".to_string())
                );
            },
        );
    }

    #[rstest]
    #[case(|test_game_loop: &mut TestRendererGameLoop, node_id: NodeID| test_game_loop.send_on_select_event(node_id))]
    #[case(|test_game_loop: &mut TestRendererGameLoop, node_id: NodeID| test_game_loop.send_key_down_up_event_to_node(node_id, KeyCode::Escape))]
    #[case(|test_game_loop: &mut TestRendererGameLoop, node_id: NodeID| test_game_loop.send_key_down_up_event_to_node(node_id, KeyCode::Backspace))]
    pub fn close_manually(
        #[case] close_or_back: impl Fn(&mut TestRendererGameLoop, NodeID) + 'static,
    ) {
        launch_test(
            move |ctx| {
                let data = get_mock_data();
                let (on_close_called_times, set_on_close_called_times) =
                    create_signal(ctx.scope(), 0);

                provide_context(ctx.scope(), on_close_called_times);

                compose! { Completed(
                    data: &data,
                    on_close: Rc::new(move || set_on_close_called_times.set(on_close_called_times.get_untracked() + 1))
                )}
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let on_close_called_times = use_context::<ReadSignal<i32>>(scope).unwrap();
                let close_button = tree.find_by_test_id("close-button");

                // wait a bit.
                MockClock::advance(Duration::from_secs(6));

                assert_eq!(on_close_called_times.get_untracked(), 0);

                close_or_back(&mut test_game_loop, close_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                assert_eq!(on_close_called_times.get_untracked(), 1);

                MockClock::advance(Duration::from_secs(30));
                test_game_loop.tick_until_done();

                // still only called once.
                assert_eq!(on_close_called_times.get_untracked(), 1);
            },
        );
    }

    #[test]
    pub fn close_automatically() {
        launch_test(
            move |ctx| {
                let data = get_mock_data();
                let (on_close_called_times, set_on_close_called_times) =
                    create_signal(ctx.scope(), 0);

                provide_context(ctx.scope(), on_close_called_times);

                compose! { Completed(
                    data: &data,
                    on_close: Rc::new(move || set_on_close_called_times.set(on_close_called_times.get_untracked() + 1))
                )}
            },
            move |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();
                let button_called_times = use_context::<ReadSignal<i32>>(scope).unwrap();

                MockClock::advance(Duration::from_millis(9999));
                test_game_loop.tick_until_done();

                assert_eq!(button_called_times.get_untracked(), 0);

                MockClock::advance(Duration::from_millis(1));
                test_game_loop.tick_until_done();

                assert_eq!(button_called_times.get_untracked(), 1);

                MockClock::advance(Duration::from_secs(30));
                test_game_loop.tick_until_done();

                // still only called once.
                assert_eq!(button_called_times.get_untracked(), 1);
            },
        );
    }

    #[test]
    pub fn cancel_task_on_cleanup() {
        launch_test(
            move |ctx| {
                let data = get_mock_data();
                let on_close_called = Rc::new(RefCell::new(false));

                provide_context(ctx.scope(), on_close_called.clone());

                compose! { Completed(
                    data: &data,
                    on_close: Rc::new(move || {
                        *on_close_called.borrow_mut() = true;
                    })
                )}
            },
            move |scope, mut test_game_loop| {
                let on_close_pressed = use_context::<Rc<RefCell<bool>>>(scope).unwrap();
                test_game_loop.tick_until_done();

                scope.dispose();
                test_game_loop.tick_until_done();

                MockClock::advance(Duration::from_secs(30));
                test_game_loop.tick_until_done();

                assert!(!*on_close_pressed.borrow());
            },
        );
    }
}
