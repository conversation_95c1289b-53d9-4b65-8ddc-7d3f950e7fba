#[cfg(not(test))]
use crate::network::record_ias_feedback::RecordIASFeedback;
#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
use cross_app_events::send_app_event;
use fableous::{buttons::primary_button::*, typography::typography::*};
use ignx_compositron::{compose, compose_option, Composer};
use ignx_compositron::{input::KeyCode, prelude::*, text::*};
use location::{PageType, RustPage};
#[cfg(not(test))]
use network::NetworkClient;
use std::rc::Rc;

#[cfg(test)]
use crate::network::mocks::MockNetworkClient as NetworkClient;
use crate::{
    network::types::{QuestionResponse, RecordFeedbackRequest, RecordFeedbackResponse},
    types::{Question, QuestionOption},
    util::clickstream::params_with_ref_marker,
};

const SURVEY_QUESTION_OPTION_SELECTED: &str = "SURVEY_QUESTION_OPTION_SELECTED";
const SURVEY_RECORD_ANSWER_SUCCESS: &str = "SURVEY_RECORD_ANSWER_SUCCESS";
const SURVEY_RECORD_ANSWER_FAILURE: &str = "SURVEY_RECORD_ANSWER_FAILURE";
const SURVEY_QUESTION_SKIPPED: &str = "SURVEY_QUESTION_SKIPPED";

const BACK_TO_CLOSE_STRING_ID: &str = "AV_LRC_TTS_CLOSE_DRAWER_MODAL_INSTRUCTION";
const SELECT_TO_SUBMIT_STRING_ID: &str = "AV_LRC_SURVEY_SELECT_TO_SUBMIT";
const MOVE_DOWN_TO_MAKE_SELECTION_STRING_ID: &str = "AV_LRC_SURVEY_MOVE_DOWN_TO_MAKE_SELECTION";

const SOURCE: &str = "IAS";
const IAS_DRAWER_BUTTON_CLICKED: &str = "IAS_DRAWER_BUTTON_CLICKED";

#[Composer]
pub fn Survey(
    ctx: &AppContext,
    survey_id: String,
    questions: Vec<Question>,
    on_survey_completed: Rc<dyn Fn()>,
    on_close: Rc<dyn Fn()>,
) -> StackComposable {
    let questions = Rc::new(questions);
    let page_source = PageType::Rust(RustPage::RUST_COLLECTIONS).to_string();
    let questions_len = questions.len();
    let (current_question_index, set_current_question_index) = create_signal(ctx.scope(), 0);
    let client = Rc::new(NetworkClient::new(ctx));

    let advance_to_next_option = Rc::new(move || {
        let current_question_index = current_question_index.get_untracked();
        if current_question_index == questions_len - 1 {
            on_survey_completed();
        } else {
            set_current_question_index.set(current_question_index + 1);
        }
    });

    let on_option_selected = {
        let ctx = ctx.clone();
        let advance_to_next_option = advance_to_next_option.clone();
        let page_source = page_source.clone();

        Rc::new(
            move |option_id: String, question_id: String, ref_marker: String| {
                let request = RecordFeedbackRequest {
                    survey_responses: vec![QuestionResponse {
                        survey_id: survey_id.clone(),
                        question_id,
                        option_id,
                    }],
                    client_variant: "PV3PLR".to_string(),
                    is_test: "true".to_string(),
                };

                metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_QUESTION_OPTION_SELECTED);
                #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
                send_app_event(
                    ctx.scope(),
                    IAS_DRAWER_BUTTON_CLICKED,
                    SOURCE,
                    params_with_ref_marker(&ref_marker),
                );

                client.record_ias_feedback(
                {
                    let page_source = page_source.clone();
                    move |_response: RecordFeedbackResponse| {
                        metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_RECORD_ANSWER_SUCCESS);
                    }
                },
                {
                    let page_source = page_source.clone();
                    move |_error| {
                        metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_RECORD_ANSWER_FAILURE);
                    }
                },
                &request,
            );

                advance_to_next_option();
            },
        )
    };

    let option_builder = Rc::new(
        move |ctx: &AppContext, option: &QuestionOption, question_id: String, idx| {
            let on_option_selected = on_option_selected.clone();
            let option = option.clone();

            compose! {
                Stack() {
                    PrimaryButton(variant: PrimaryButtonVariant::TextSize400(TextContent::String(option.button.translatedText.clone())))
                        .focusable()
                        .on_select(move || on_option_selected(option.optionId.clone(), question_id.clone(), option.button.clickRefMarker.clone()))
                        .test_id(format!("option-{}", idx))
                        .accessibility_closing_context_message(LocalizedText {string_id: SELECT_TO_SUBMIT_STRING_ID.into(), substitution_parameters: None })
                }
                .padding(Padding::new(0.0, 0.0, 24.0, 0.0))
            }
        },
    );

    let on_question_skipped = Rc::new({
        let ctx = ctx.clone();
        let questions = questions.clone();
        let advance_to_next_option = advance_to_next_option.clone();

        move || {
            metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_QUESTION_SKIPPED);

            let question = questions.get(current_question_index.get());
            if let Some(question) = question {
                #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
                send_app_event(
                    ctx.scope(),
                    IAS_DRAWER_BUTTON_CLICKED,
                    SOURCE,
                    params_with_ref_marker(&question.skipButton.clickRefMarker),
                );
            }

            advance_to_next_option();
        }
    });

    let close = Rc::new({
        let ctx = ctx.clone();
        let questions = questions.clone();
        let on_close = on_close.clone();

        move || {
            let question = questions.get(current_question_index.get());
            if let Some(question) = question {
                #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
                send_app_event(
                    ctx.scope(),
                    IAS_DRAWER_BUTTON_CLICKED,
                    SOURCE,
                    params_with_ref_marker(&question.closeButton.clickRefMarker),
                );
            }

            on_close();
        }
    });

    let question_builder = Box::new({
        let close = close.clone();

        move |ctx: &AppContext| {
            let option_builder = option_builder.clone();
            let on_question_skipped = on_question_skipped.clone();
            let close = close.clone();
            let question = questions.get(current_question_index.get())?;
            let question_id = question.questionId.clone();

            let option_builder_wrapper =
                Rc::new(move |ctx: &AppContext, option: &QuestionOption, idx| {
                    option_builder(ctx, option, question_id.clone(), idx)
                });

            let context_messages: Vec<TextContent> = vec![
                question.translatedText.clone().into(),
                LocalizedText {
                    string_id: MOVE_DOWN_TO_MAKE_SELECTION_STRING_ID.into(),
                    substitution_parameters: None,
                }
                .into(),
                LocalizedText {
                    string_id: BACK_TO_CLOSE_STRING_ID.into(),
                    substitution_parameters: None,
                }
                .into(),
            ];

            compose_option! {
                Column() {
                    PrimaryButton(variant: PrimaryButtonVariant::TextSize200(TextContent::String(question.closeButton.translatedText.clone())))
                            .focusable()
                            .preferred_focus(true)
                            .on_select({
                                let close = close.clone();
                                move || close()
                            })
                            .test_id("close-button")

                    Row() {
                        Column() {
                            TypographyHeading200(content: question.translatedText.clone())
                                .test_id("question-header")
                            ColumnList(item_builder: option_builder_wrapper, items: question.options.clone())
                                .test_id("question-options")
                        }
                        .test_id("question")
                    }
                    .cross_axis_alignment(CrossAxisAlignment::Center)
                    .height(850.0)
                    .test_id("question-row")

                    PrimaryButton(variant: PrimaryButtonVariant::TextSize200(TextContent::String(question.skipButton.translatedText.clone())))
                            .on_select(move || on_question_skipped())
                            .test_id("skip-button")
                }
                .accessibility_context_messages(context_messages)
                .test_id("survey-contents")
                .focus_window()
            }
        }
    });

    compose! {
        Stack() {
            Memo(item_builder: question_builder)
        }
        .on_key_down(KeyCode::Backspace, {
            let close = close.clone();
            move || close()
        })
        .on_key_down(KeyCode::Escape, {
            let close = close.clone();
            move || close()
        })
    }
}

#[cfg(test)]
pub mod test {
    use crate::network::mocks::MockNetworkClient;
    use crate::{
        mocks::mock_survey::get_mock_survey,
        network::mocks::__mock_MockNetworkClient::__new::Context,
    };

    use super::*;
    use ignx_compositron::node::NodeID;
    use ignx_compositron::test_utils::TestRendererGameLoop;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_does_not_exist, assert_node_exists},
    };
    use mockall::Sequence;
    use rstest::*;
    use serial_test::serial;

    fn get_mock_questions() -> Vec<Question> {
        get_mock_survey().questions
    }

    #[test]
    #[serial]
    pub fn render_survey() {
        launch_test(
            move |ctx| {
                let _context = expect_no_calls();

                let questions = get_mock_questions();
                compose! { Survey(
                    survey_id: "survey-1".to_string(),
                    questions,
                    on_close: Rc::new(|| {}),
                    on_survey_completed: Rc::new(|| {}),
                )}
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let survey = tree.find_by_test_id("survey-contents");

                assert_node_exists!(&survey);

                let survey_children = survey.find_child_with().find_all();
                assert_eq!(survey_children.len(), 3);

                let close_button = &survey_children[0];

                assert_node_exists!(&close_button);
                assert_eq!(
                    close_button.borrow_props().test_id,
                    Some("close-button".to_string())
                );

                let question_row = &survey_children[1];

                assert_node_exists!(&question_row);
                assert_eq!(
                    question_row.borrow_props().test_id,
                    Some("question-row".to_string())
                );

                let question = question_row.find_child_with().find_first();

                assert_node_exists!(&question);
                assert_eq!(
                    question.borrow_props().test_id,
                    Some("question".to_string())
                );

                let header = question.find_child_with().find_first();

                assert_node_exists!(&header);
                assert_eq!(
                    header.borrow_props().test_id,
                    Some("question-header".to_string())
                );
                assert_eq!(
                    header.borrow_props().text,
                    Some(
                        "1. How satisfied are you with the content available in Prime Video?"
                            .to_string()
                    )
                );

                let options_list = header.find_first_sibling();

                assert_node_exists!(&options_list);
                assert_eq!(
                    options_list.borrow_props().test_id,
                    Some("question-options".to_string())
                );

                for i in 0..5 {
                    let option = options_list
                        .find_any_child_with()
                        .test_id(format!("option-{}", i))
                        .find_first();
                    assert_node_exists!(&option);
                }

                let non_existant_option = options_list
                    .find_any_child_with()
                    .test_id("option-5")
                    .find_first();

                assert_node_does_not_exist!(&non_existant_option);

                let skip_button = &survey_children[2];

                assert_node_exists!(&skip_button);
                assert_eq!(
                    skip_button.borrow_props().test_id,
                    Some("skip-button".to_string())
                );
            },
        );
    }

    #[rstest]
    #[serial]
    #[case(|test_game_loop: &mut TestRendererGameLoop, node_id: NodeID| test_game_loop.send_on_select_event(node_id))]
    #[serial]
    #[case(|test_game_loop: &mut TestRendererGameLoop, node_id: NodeID| test_game_loop.send_key_down_up_event_to_node(node_id, KeyCode::Escape))]
    #[serial]
    #[case(|test_game_loop: &mut TestRendererGameLoop, node_id: NodeID| test_game_loop.send_key_down_up_event_to_node(node_id, KeyCode::Backspace))]
    pub fn close_manually(
        #[case] close_or_back: impl Fn(&mut TestRendererGameLoop, NodeID) + 'static,
    ) {
        launch_test(
            move |ctx| {
                let _context = expect_no_calls();

                let questions = get_mock_questions();
                let (closed_called, set_closed_called) = create_signal(ctx.scope(), 0);

                provide_context(ctx.scope(), closed_called);

                compose! { Survey(
                    survey_id: "survey-1".to_string(),
                    questions,
                    on_close: Rc::new(move || {
                        set_closed_called.set_untracked(closed_called.get_untracked() + 1);
                    }),
                    on_survey_completed: Rc::new(|| {}),
                )}
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let close_button = tree.find_by_test_id("close-button");
                let closed_called = use_context::<ReadSignal<i32>>(scope).unwrap();

                assert_eq!(closed_called.get_untracked(), 0);

                close_or_back(&mut test_game_loop, close_button.borrow_props().node_id);
                test_game_loop.tick_until_done();

                assert_eq!(closed_called.get_untracked(), 1);
            },
        );
    }

    #[rstest]
    #[serial]
    #[case(vec![None, Some(2)])]
    #[serial]
    #[case(vec![Some(1), None])]
    #[serial]
    #[case(vec![None, None])]
    #[serial]
    #[case(vec![Some(0), Some(2)])]
    pub fn complete_survey(#[case] answers: Vec<Option<usize>>) {
        launch_test(
            {
                let answers = answers.clone();
                move |ctx| {
                    let questions: Vec<Question> = get_mock_questions();
                    let (callbacks_called, set_callbacks_called) =
                        create_signal(ctx.scope(), (0, 0));

                    provide_context(ctx.scope(), callbacks_called);

                    let mock_context = MockNetworkClient::new_context();
                    let mut seq = Sequence::new();

                    mock_context.expect().returning({
                        let questions = questions.clone();
                        let answers = answers;

                        move |_| {
                            let mut mock_client = MockNetworkClient::default();

                            for i in 0..answers.len() {
                                if let Some(answer) = answers[i] {
                                    let question = questions[i].clone();

                                    mock_client
                                        .expect_record_ias_feedback()
                                        .once()
                                        .in_sequence(&mut seq)
                                        .withf(move |_, _, request| {
                                            *request
                                                == RecordFeedbackRequest {
                                                    survey_responses: vec![QuestionResponse {
                                                        survey_id: "survey-1".to_string(),
                                                        question_id: question.questionId.clone(),
                                                        option_id: question.options[answer]
                                                            .optionId
                                                            .clone(),
                                                    }],
                                                    client_variant: "PV3PLR".to_string(),
                                                    is_test: "true".to_string(),
                                                }
                                        })
                                        .return_const(());
                                }
                            }

                            mock_client
                        }
                    });

                    compose! { Survey(
                        survey_id: "survey-1".to_string(),
                        questions,
                        on_close: Rc::new(move || {
                            set_callbacks_called.set_untracked((callbacks_called.get_untracked().0 + 1, callbacks_called.get_untracked().1));
                        }),
                        on_survey_completed: Rc::new(move || {
                            set_callbacks_called.set_untracked((callbacks_called.get_untracked().0, callbacks_called.get_untracked().1 + 1));
                        }),
                    )}
                }
            },
            move |scope, mut test_game_loop| {
                let mut tree = test_game_loop.tick_until_done();
                let callbacks_called = use_context::<ReadSignal<(i32, i32)>>(scope).unwrap();
                let questions = get_mock_questions();

                for i in 0..answers.len() {
                    assert_eq!(callbacks_called.get_untracked(), (0, 0));

                    let question = &questions[i];
                    let option = answers[i];
                    let header = tree.find_by_test_id("question-header");

                    assert_node_exists!(&header);
                    assert_eq!(
                        header.borrow_props().text,
                        Some(question.translatedText.clone())
                    );

                    let button_to_press = if let Some(option) = option {
                        tree.find_by_test_id(format!("option-{}", option))
                    } else {
                        tree.find_by_test_id("skip-button")
                    };

                    test_game_loop.send_on_select_event(button_to_press.borrow_props().node_id);
                    tree = test_game_loop.tick_until_done();
                }

                // survey is complete.
                assert_eq!(callbacks_called.get_untracked(), (0, 1));
            },
        );
    }

    fn expect_no_calls() -> Context {
        let context = MockNetworkClient::new_context();
        context.expect().returning(|_| {
            let mut client = MockNetworkClient::default();
            client.expect_record_ias_feedback().never();
            client
        });
        context
    }
}
