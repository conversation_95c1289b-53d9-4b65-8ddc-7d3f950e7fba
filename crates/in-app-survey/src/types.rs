#![allow(
    nonstandard_style,
    clippy::upper_case_acronyms,
    reason = "JSON compability"
)]

use cfg_test_attr_derive::*;
use ignx_compositron::id::Id;
use ignx_compositron::serde::de::AsyncDeserialize;
use network_parser::prelude::*;
use network_parser_derive::*;

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct SurveyButton {
    pub clickRefMarker: String,
    pub translatedText: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct QuestionOption {
    pub button: SurveyButton,
    pub optionId: String,
}

impl Id for QuestionOption {
    type Id = String;

    fn id(&self) -> &Self::Id {
        &self.optionId
    }
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct Question {
    pub questionId: String,
    pub translatedText: String,
    pub closeButton: SurveyButton,
    pub skipButton: SurveyButton,
    pub submitErrorTranslatedText: String,
    pub options: Vec<QuestionOption>,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct NotificationPopup {
    pub displayRefMarker: String,
    pub clickRefMarker: String,
    pub translatedText: String,
    pub startButton: SurveyButton,
    pub closeButton: SurveyButton,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct ThankYouPopup {
    pub closeButton: SurveyButton,
    pub displayRefMarker: String,
    pub translatedText: String,
}

#[derive(NetworkParsed, AsyncDeserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct SurveyData {
    pub surveyId: String,
    pub notificationPopup: NotificationPopup,
    pub questions: Vec<Question>,
    pub thankYouPopup: ThankYouPopup,
}
