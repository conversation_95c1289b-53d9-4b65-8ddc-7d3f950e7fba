#![cfg(any(test, feature = "example_data"))]

use crate::network::types::{RecordFeedbackRequest, RecordFeedbackResponse};
use ignx_compositron::context::AppContext;
use mockall::mock;
use network::RequestError;

mock! {
    pub NetworkClient {
        pub fn new(
            ctx: &AppContext
        ) -> Self;

        pub fn record_ias_feedback<T: FnOnce(RecordFeedbackResponse) + 'static, U: FnOnce(RequestError) + 'static>(
            &self,
            success_callback: T,
            failure_callback: U,
            params: &RecordFeedbackRequest
        );
    }
}
