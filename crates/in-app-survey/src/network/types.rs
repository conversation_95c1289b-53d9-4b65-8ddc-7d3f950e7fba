use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::*;
use serde::{Deserialize, Serialize};

#[derive(Serialize)]
#[derive_test_only(PartialEq, Debug)]
#[serde(rename_all = "camelCase")]
pub struct QuestionResponse {
    pub survey_id: String,
    pub question_id: String,
    pub option_id: String,
}

#[derive(Serialize)]
#[derive_test_only(PartialEq, Debug)]
#[serde(rename_all = "camelCase")]
pub struct RecordFeedbackRequest {
    pub survey_responses: Vec<QuestionResponse>,
    pub client_variant: String,
    pub is_test: String,
}

#[derive(Deserialize, Clone, NetworkParsed)]
#[derive_test_only(PartialEq, Debug)]
pub struct RecordFeedbackResponse {
    #[network(alias = "testResponse")]
    pub test_response: Option<String>,
}
