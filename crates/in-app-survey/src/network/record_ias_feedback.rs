use std::rc::Rc;

use crate::network::types::{RecordFeedbackRequest, RecordFeedbackResponse};
use ignx_compositron::network::http::HttpMethod;
use ignx_compositron::prelude::AppContext;
use network::common::{DeviceProxyResponse, LRCEdgeResponse};
use network::NetworkClient;
use network::{RequestError, URLBuilder};
use network_parser::core::network_parse_from_str;

const ENDPOINT: &str = "/cdp/usage/RecordIASFeedback";

pub trait RecordIASFeedback {
    fn record_ias_feedback<S, F>(
        &self,
        success_callback: S,
        failure_callback: F,
        params: &RecordFeedbackRequest,
    ) where
        S: FnOnce(RecordFeedbackResponse) + 'static,
        F: FnOnce(RequestError) + 'static;
}

impl RecordIASFeedback for NetworkClient {
    fn record_ias_feedback<S, F>(
        &self,
        success_callback: S,
        failure_callback: F,
        params: &RecordFeedbackRequest,
    ) where
        S: FnOnce(RecordFeedbackResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
        let url = match URLBuilder::for_device_proxy(
            ENDPOINT,
            vec![],
            false,
            Rc::clone(&self.device_info),
            Rc::clone(&self.app_config),
        ) {
            Ok(url) => url,
            Err(e) => {
                log::error!(
                    "RecordIASFeedback: Cannot create the request url. Error: {:?}",
                    e
                );
                return failure_callback(RequestError::Builder(e.to_string()));
            }
        };

        let body: String = match serde_json::to_string(params) {
            Ok(body) => body,
            Err(err) => {
                log::error!(
                    "RecordIASFeedback: Cannot create the request body. Error: {:?}",
                    err.to_string()
                );
                return failure_callback(RequestError::Builder(err.to_string()));
            }
        };

        let success_cb = move |v: RecordFeedbackResponse, _| {
            success_callback(v);
        };

        let failure_cb = move |e: RequestError, _| {
            failure_callback(e);
        };

        let parser = |_: &AppContext,
                      response: String,
                      _status_code: i32,
                      cb: Box<
            dyn FnOnce(Result<DeviceProxyResponse<RecordFeedbackResponse>, RequestError>),
        >| {
            cb(network_parse_from_str::<RecordFeedbackResponse>(&response)
                .map(LRCEdgeResponse::with_resource)
                .map(DeviceProxyResponse::LRCEdgeResponse)
                .map_err(RequestError::DeserializationFailed))
        };

        self.builder(url, HttpMethod::Post, "RecordIASFeedback")
            .data(body)
            .with_device_proxy_headers()
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb))
            .with_parser(parser)
            .execute();
    }
}

#[cfg(test)]
pub mod test {
    use std::cell::RefCell;

    use crate::network::types::QuestionResponse;

    use super::*;
    use app_config::{AppConfigContext, MockAppConfig};
    use auth::{AuthContext, MockAuth};
    use ignx_compositron::{
        app::launch_only_app_context, context::AppContext, network::http::MockHttpRequestContext,
        reactive::provide_context,
    };
    use router::{MockRouting, RoutingContext};
    use serial_test::*;

    #[test]
    #[serial]
    fn should_call_network_client_with_right_params() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);

            let network_client = NetworkClient::new(&ctx);

            network_client.record_ias_feedback(
                |_| {},
                |_| {},
                &RecordFeedbackRequest {
                    survey_responses: vec![
                        QuestionResponse {
                            survey_id: "survey-1".to_string(),
                            question_id: "question-1".to_string(),
                            option_id: "option-1".to_string(),
                        },
                        QuestionResponse {
                            survey_id: "survey-2".to_string(),
                            question_id: "question-2".to_string(),
                            option_id: "option-2".to_string(),
                        },
                    ],
                    client_variant: "PV3PLR".to_string(),
                    is_test: "true".to_string(),
                },
            );

            let props = MockHttpRequestContext::get();

            assert_eq!(props.method, HttpMethod::Post);
            assert_eq!(
                props.url,
                "https://base_url.com/cdp/usage/RecordIASFeedback?gascEnabled=true&uxLocale=en%5FGB&geoLocation=IE&supportedLocales=de%5FDE%2Cen%5FUS%2Cfr%5FFR&firmware=0%2E0%2E0&manufacturer=manufacturer&chipset=chipset&model=model&operatingSystem=osx&deviceTypeID=A71I8788P1ZV8&deviceID=random123456789&osLocale=GB"
            );

            let mut headers = props.headers.clone();
            headers.sort();

            let mut expected_headers = vec![
                "x-client-app: avlrc",
                "accept: application/json",
                "x-client-version: unknown-version",
                "content-type: application/json",
                "x-request-priority: CRITICAL",
            ];

            expected_headers.sort();

            assert_eq!(headers, expected_headers);
            assert_eq!(props.timeout, None);
            assert_eq!(props.sent_data, Some("{\"surveyResponses\":[{\"surveyId\":\"survey-1\",\"questionId\":\"question-1\",\"optionId\":\"option-1\"},{\"surveyId\":\"survey-2\",\"questionId\":\"question-2\",\"optionId\":\"option-2\"}],\"clientVariant\":\"PV3PLR\",\"isTest\":\"true\"}".into()));
        })
    }

    #[test]
    #[serial]
    fn should_handle_successful_request() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);

            let network_client = NetworkClient::new(&ctx);

            let success_called: Rc<RefCell<Option<RecordFeedbackResponse>>> =
                Rc::new(RefCell::new(None));
            let failed_called: Rc<RefCell<bool>> = Rc::new(RefCell::new(false));

            network_client.record_ias_feedback(
                {
                    let success_called = success_called.clone();
                    move |data| {
                        *success_called.borrow_mut() = Some(data);
                    }
                },
                {
                    let failed_called = failed_called.clone();
                    move |_| {
                        *failed_called.borrow_mut() = true;
                    }
                },
                &RecordFeedbackRequest {
                    survey_responses: vec![
                        QuestionResponse {
                            survey_id: "survey-1".to_string(),
                            question_id: "question-1".to_string(),
                            option_id: "option-1".to_string(),
                        },
                        QuestionResponse {
                            survey_id: "survey-2".to_string(),
                            question_id: "question-2".to_string(),
                            option_id: "option-2".to_string(),
                        },
                    ],
                    client_variant: "PV3PLR".to_string(),
                    is_test: "true".to_string(),
                },
            );

            let props = MockHttpRequestContext::get();

            assert_eq!(*success_called.borrow(), None);
            assert!(!(*failed_called.borrow()));

            props.invoke_callback(Some("{\"testResponse\": \"123\"}".to_string()), 200);

            assert_eq!(
                *success_called.borrow(),
                Some(RecordFeedbackResponse {
                    test_response: Some("123".to_string())
                })
            );
            assert!(!(*failed_called.borrow()));
        })
    }

    #[test]
    #[serial]
    fn should_handle_failed_request() {
        launch_only_app_context(|ctx| {
            mock_context(&ctx);

            let network_client = NetworkClient::new(&ctx);

            let success_called: Rc<RefCell<bool>> = Rc::new(RefCell::new(false));
            let failed_called: Rc<RefCell<bool>> = Rc::new(RefCell::new(false));

            network_client.record_ias_feedback(
                {
                    let success_called = success_called.clone();
                    move |_| {
                        *success_called.borrow_mut() = true;
                    }
                },
                {
                    let failed_called = failed_called.clone();
                    move |_| {
                        *failed_called.borrow_mut() = true;
                    }
                },
                &RecordFeedbackRequest {
                    survey_responses: vec![
                        QuestionResponse {
                            survey_id: "survey-1".to_string(),
                            question_id: "question-1".to_string(),
                            option_id: "option-1".to_string(),
                        },
                        QuestionResponse {
                            survey_id: "survey-2".to_string(),
                            question_id: "question-2".to_string(),
                            option_id: "option-2".to_string(),
                        },
                    ],
                    client_variant: "PV3PLR".to_string(),
                    is_test: "true".to_string(),
                },
            );

            let props = MockHttpRequestContext::get();

            assert!(!(*success_called.borrow()));
            assert!(!(*failed_called.borrow()));

            props.invoke_callback(Some("{}".to_string()), 502);

            assert!(!(*success_called.borrow()));
            assert!(*failed_called.borrow());
        })
    }

    fn mock_context(ctx: &AppContext) {
        let mut mock_app_config = MockAppConfig::default();
        mock_app_config
            .expect_get_ux_locale()
            .returning(|| "en_GB".into());
        mock_app_config
            .expect_get_geo_location()
            .returning(|| Some("IE".into()));
        mock_app_config
            .expect_get_supported_locales()
            .returning(|| vec!["de_DE".into(), "en_US".into(), "fr_FR".into()]);
        mock_app_config
            .expect_get_base_url()
            .return_once(|| Some("https://base_url.com".to_string()));

        provide_context::<AppConfigContext>(ctx.scope(), Rc::new(mock_app_config));

        let auth = Rc::new(MockAuth::new_without_params(ctx.scope()));
        provide_context::<AuthContext>(ctx.scope, auth);

        let mock_routing_context = MockRouting::new();
        provide_context::<RoutingContext>(ctx.scope, Rc::new(mock_routing_context));
    }
}
