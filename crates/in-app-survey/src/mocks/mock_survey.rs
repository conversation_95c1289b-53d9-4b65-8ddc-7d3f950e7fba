use crate::types::{
    NotificationPopup, Question, QuestionOption, SurveyButton, SurveyData, ThankYouPopup,
};

pub fn get_mock_survey() -> SurveyData {
    SurveyData {
        surveyId: "survey-id".to_string(),
        notificationPopup: NotificationPopup {
            displayRefMarker: "survey-noticication-display-refmarker".to_string(),
            clickRefMarker: "survey-noticication-click-refmarker".to_string(),
            translatedText: "Help make Prime Video better by answering two survey questions."
                .to_string(),
            startButton: SurveyButton {
                clickRefMarker: "notification_start_refmarker".to_string(),
                translatedText: "Continue to survey".to_string(),
            },
            closeButton: SurveyButton {
                clickRefMarker: "notification_close_refmarker".to_string(),
                translatedText: "I'm not interested".to_string(),
            },
        },
        questions: vec![
            Question {
                questionId: "question-id1".to_string(),
                translatedText:
                    "1. How satisfied are you with the content available in Prime Video?"
                        .to_string(),
                closeButton: SurveyButton {
                    clickRefMarker: "close-ref-marker".to_string(),
                    translatedText: "Close".to_string(),
                },
                skipButton: SurveyButton {
                    clickRefMarker: "skip-ref-marker".to_string(),
                    translatedText: "Skip 1/2".to_string(),
                },
                submitErrorTranslatedText: "error while submitting answer".to_string(),
                options: vec![
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "very-satisfied-ref-marker".to_string(),
                            translatedText: "Very satisfied".to_string(),
                        },
                        optionId: "option-id-1-1".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "somewhat-satisfied-ref-marker".to_string(),
                            translatedText: "Somewhat satisfied".to_string(),
                        },
                        optionId: "option-id-2-1".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "neither-satisfied-ref-marker".to_string(),
                            translatedText: "Neither satisfied nor dissatisfied".to_string(),
                        },
                        optionId: "option-id-3-1".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "somewhat-dissatisfied-ref-marker".to_string(),
                            translatedText: "Somewhat dissatisfied".to_string(),
                        },
                        optionId: "option-id-4-1".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "very-dissatisfied-ref-marker".to_string(),
                            translatedText: "Very dissatisfied".to_string(),
                        },
                        optionId: "option-id-5-1".to_string(),
                    },
                ],
            },
            Question {
                questionId: "question-id2".to_string(),
                translatedText:
                    "2. How satisfied are you with the user interface available in Prime Video?"
                        .to_string(),
                closeButton: SurveyButton {
                    clickRefMarker: "close-ref-marker".to_string(),
                    translatedText: "Close".to_string(),
                },
                skipButton: SurveyButton {
                    clickRefMarker: "skip-ref-marker".to_string(),
                    translatedText: "Skip 2/2".to_string(),
                },
                submitErrorTranslatedText: "error while submitting answer".to_string(),
                options: vec![
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "very-satisfied-ref-marker".to_string(),
                            translatedText: "Very satisfied".to_string(),
                        },
                        optionId: "option-id-1-2".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "somewhat-satisfied-ref-marker".to_string(),
                            translatedText: "Somewhat satisfied".to_string(),
                        },
                        optionId: "option-id-2-2".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "neither-satisfied-ref-marker".to_string(),
                            translatedText: "Neither satisfied nor dissatisfied".to_string(),
                        },
                        optionId: "option-id-3-2".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "somewhat-dissatisfied-ref-marker".to_string(),
                            translatedText: "Somewhat dissatisfied".to_string(),
                        },
                        optionId: "option-id-4-2".to_string(),
                    },
                    QuestionOption {
                        button: SurveyButton {
                            clickRefMarker: "very-dissatisfied-ref-marker".to_string(),
                            translatedText: "Very dissatisfied".to_string(),
                        },
                        optionId: "option-id-5-2".to_string(),
                    },
                ],
            },
        ],
        thankYouPopup: ThankYouPopup {
            closeButton: SurveyButton {
                clickRefMarker: "thankyou-close-refmarker".to_string(),
                translatedText: "Close window".to_string(),
            },
            translatedText:
                "Thank you. We've received your answers.\n\nThis window will close automatically."
                    .to_string(),
            displayRefMarker: "thankyou-refmarker".to_string(),
        },
    }
}
