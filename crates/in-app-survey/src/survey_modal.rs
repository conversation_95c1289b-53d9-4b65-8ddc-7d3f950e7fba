use crate::components::completed::*;
use crate::components::notification::*;
use crate::components::survey::*;
use crate::types::SurveyData;
use crate::util::clickstream::params_with_ref_marker;
#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
use cross_app_events::send_app_event;
use drawer::drawer::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::prelude::*;
use ignx_compositron::{compose, compose_option, Composer};
use location::PageType;
use location::RustPage;
use std::rc::Rc;

pub const SURVEY_TEST_ID: &str = "survey-modal";

#[derive(Clone, PartialEq)]
enum Stage {
    Notification,
    Survey,
    Completed,
}

const SURVEY_OPEN_NOTIFICATION: &str = "SURVEY_OPEN_NOTIFICATION";
const SURVEY_CLOSE_NOTIFICATION: &str = "SURVEY_CLOSE_NOTIFICATION";
const SURVEY_OPEN_DRAW: &str = "SURVEY_OPEN_DRAWER";
const SURVEY_CLOSE_DRAW: &str = "SURVEY_CLOSE_DRAWER";
const SURVEY_COMPLETED: &str = "SURVEY_COMPLETED";

const SOURCE: &str = "IAS";
const IAS_NOTIFICATION_DISPLAYED: &str = "IAS_NOTIFICATION_DISPLAYED";
const IAS_DRAWER_CONTENT_DISPLAYED: &str = "IAS_DRAWER_CONTENT_DISPLAYED";

#[Composer]
pub fn SurveyModal(
    ctx: &AppContext,
    data: &SurveyData,
    on_dismiss: Rc<dyn Fn()>,
    scale_vec: Memo<Vec2>,
) -> StackComposable {
    let page_source = PageType::Rust(RustPage::RUST_COLLECTIONS).to_string();
    let (stage, set_stage) = create_signal(ctx.scope(), Stage::Notification);
    let (drawer_open, set_drawer_open) = create_signal(ctx.scope(), true);

    let on_drawer_close_press = Rc::new(move || {
        set_drawer_open.set(false);
    });

    let on_notification_close_press = Rc::new({
        let on_dismiss = on_dismiss.clone();
        let page_source = page_source.clone();

        move || {
            metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_CLOSE_NOTIFICATION);
            on_dismiss();
        }
    });

    let on_drawer_closed = Rc::new({
        let on_dismiss = on_dismiss.clone();
        let page_source = page_source.clone();
        move || {
            metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_CLOSE_DRAW);
            on_dismiss();
        }
    });

    let on_start_survey_press = Rc::new(move || set_stage.set(Stage::Survey));

    let on_survey_completed = Rc::new(move || {
        set_stage.set(Stage::Completed);
    });

    let in_drawer = create_memo(ctx.scope(), move |_| match stage.get() {
        Stage::Notification => false,
        Stage::Survey | Stage::Completed => true,
    });

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let notification_display_ref_marker = data.notificationPopup.displayRefMarker.clone();
        let survey_completed_ref_marker = data.thankYouPopup.displayRefMarker.clone();

        move |prev| {
            let new_stage = stage.get();

            if let Some(prev) = prev {
                if prev == new_stage {
                    return new_stage;
                }
            }

            match &new_stage {
                Stage::Notification => {
                    metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_OPEN_NOTIFICATION);
                    #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
                    send_app_event(
                        ctx.scope(),
                        IAS_NOTIFICATION_DISPLAYED,
                        SOURCE,
                        params_with_ref_marker(&notification_display_ref_marker),
                    );
                }
                Stage::Survey => {
                    metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_CLOSE_NOTIFICATION);
                    metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_OPEN_DRAW);
                }
                Stage::Completed => {
                    metric!("PageAction.Count", 1, "pageType" => page_source.as_str(), "actionName" => SURVEY_COMPLETED);
                    #[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
                    send_app_event(
                        ctx.scope(),
                        IAS_DRAWER_CONTENT_DISPLAYED,
                        SOURCE,
                        params_with_ref_marker(&survey_completed_ref_marker),
                    );
                }
            };

            new_stage
        }
    });

    let contents = Box::new({
        let data = Rc::new(data.clone());
        let on_notification_close_press = on_notification_close_press.clone();

        move |ctx: &AppContext| {
            let data = data.clone();

            match in_drawer.get() {
                false => {
                    compose_option! {
                        Notification(data: &data.notificationPopup, on_start_press: on_start_survey_press.clone(), on_close: on_notification_close_press.clone(), scale_vec)
                    }
                }
                true => {
                    let on_drawer_close_press = on_drawer_close_press.clone();
                    let on_survey_completed = on_survey_completed.clone();

                    compose_option! {
                        Drawer(open: drawer_open, on_closed: Some(on_drawer_closed.clone()), item_builder: Box::new(move |ctx: &AppContext| {
                            match stage.get() {
                                Stage::Survey => {
                                    compose_option! {
                                        Survey(survey_id: data.surveyId.clone(), questions: data.questions.clone(), on_close: on_drawer_close_press.clone(), on_survey_completed: on_survey_completed.clone())
                                    }
                                },
                                Stage::Completed => {
                                    compose_option! {
                                        Completed(data: &data.thankYouPopup, on_close: on_drawer_close_press.clone())
                                    }
                                },
                                Stage::Notification => None
                            }
                        }))
                    }
                }
            }
        }
    });

    compose! {
        Stack() {
            Memo(item_builder: contents)
        }
        .alignment(Alignment::Center)
        .height(SCREEN_HEIGHT)
        .width(SCREEN_WIDTH)
        .test_id(SURVEY_TEST_ID)
    }
}

#[cfg(test)]
pub mod test {
    use crate::network::mocks::MockNetworkClient;
    use crate::{
        mocks::mock_survey::get_mock_survey,
        network::mocks::__mock_MockNetworkClient::__new::Context,
    };
    use serial_test::serial;

    use super::*;
    use ignx_compositron::input::KeyCode;
    use ignx_compositron::{
        app::launch_test,
        test_utils::{assert_node_does_not_exist, assert_node_exists},
    };

    #[test]
    #[serial]
    pub fn renders_initial_state() {
        launch_test(
            move |ctx| {
                let data = get_mock_survey();
                compose! { SurveyModal(
                    data: &data,
                    on_dismiss: Rc::new(|| {}),
                    scale_vec: create_memo(ctx.scope(), move |_| Vec2::new(1.0, 1.0))
                )}
            },
            move |_scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let modal = tree.find_by_test_id(SURVEY_TEST_ID);

                assert_node_exists!(&modal);

                let notification = modal
                    .find_any_child_with()
                    .test_id("survey-notification")
                    .find_first();

                assert_node_exists!(&notification);

                let drawer = tree.find_by_test_id("drawer-wrapper");

                assert_node_does_not_exist!(&drawer);
            },
        );
    }

    #[test]
    #[serial]
    pub fn dismisses_from_notification() {
        launch_test(
            move |ctx| {
                let data = get_mock_survey();
                let (dismiss_called_times, set_dismiss_called_times) =
                    create_signal(ctx.scope(), 0);

                provide_context(ctx.scope(), dismiss_called_times);

                compose! { SurveyModal(
                    data: &data,
                    on_dismiss: Rc::new(move || set_dismiss_called_times.set(dismiss_called_times.get_untracked() + 1)),
                    scale_vec: create_memo(ctx.scope(), move |_| Vec2::new(1.0, 1.0))
                )}
            },
            move |scope, mut test_game_loop| {
                let tree = test_game_loop.tick_until_done();
                let close_notification_button = tree.find_by_test_id("survey-notification-close");
                let dismiss_called_times = use_context::<ReadSignal<i32>>(scope).unwrap();

                assert_node_exists!(&close_notification_button);
                assert_eq!(dismiss_called_times.get(), 0);

                test_game_loop.send_key_down_up_event_to_node(
                    close_notification_button.borrow_props().node_id,
                    KeyCode::Escape,
                );
                test_game_loop.tick_until_done();

                assert_eq!(dismiss_called_times.get(), 1);
            },
        );
    }

    #[test]
    #[serial]
    pub fn dismisses_from_survey() {
        launch_test(
            move |ctx| {
                let data = get_mock_survey();
                let (dismiss_called_times, set_dismiss_called_times) =
                    create_signal(ctx.scope(), 0);

                provide_context(ctx.scope(), dismiss_called_times);

                compose! { SurveyModal(
                    data: &data,
                    on_dismiss: Rc::new(move || set_dismiss_called_times.set(dismiss_called_times.get_untracked() + 1)),
                    scale_vec: create_memo(ctx.scope(), move |_| Vec2::new(1.0, 1.0))
                )}
            },
            move |scope, mut test_game_loop| {
                let mut tree = test_game_loop.tick_until_done();
                let confirm_notification_button =
                    tree.find_by_test_id("survey-notification-confirm");
                let dismiss_called_times = use_context::<ReadSignal<i32>>(scope).unwrap();

                assert_node_exists!(&confirm_notification_button);

                let _context = expect_no_calls();

                test_game_loop
                    .send_on_select_event(confirm_notification_button.borrow_props().node_id);
                tree = test_game_loop.tick_until_done();

                assert_eq!(dismiss_called_times.get(), 0);

                let drawer = tree.find_by_test_id("drawer-wrapper");
                assert_node_exists!(&drawer);

                let survey_close_button = drawer
                    .find_any_child_with()
                    .test_id("close-button")
                    .find_first();
                assert_node_exists!(&survey_close_button);

                test_game_loop.send_key_down_up_event_to_node(
                    survey_close_button.borrow_props().node_id,
                    KeyCode::Escape,
                );
                test_game_loop.tick_until_done();

                assert_eq!(dismiss_called_times.get(), 1);
            },
        );
    }

    #[test]
    #[serial]
    pub fn dismisses_from_completed() {
        launch_test(
            move |ctx| {
                let _context = expect_no_calls();
                let data = get_mock_survey();
                let (dismiss_called_times, set_dismiss_called_times) =
                    create_signal(ctx.scope(), 0);

                provide_context(ctx.scope(), dismiss_called_times);

                compose! { SurveyModal(
                    data: &data,
                    on_dismiss: Rc::new(move || set_dismiss_called_times.set(dismiss_called_times.get_untracked() + 1)),
                    scale_vec: create_memo(ctx.scope(), move |_| Vec2::new(1.0, 1.0))
                )}
            },
            move |scope, mut test_game_loop| {
                let mut tree = test_game_loop.tick_until_done();
                let confirm_notification_button =
                    tree.find_by_test_id("survey-notification-confirm");
                let dismiss_called_times = use_context::<ReadSignal<i32>>(scope).unwrap();

                assert_node_exists!(&confirm_notification_button);

                let _context = expect_no_calls();

                test_game_loop
                    .send_on_select_event(confirm_notification_button.borrow_props().node_id);
                tree = test_game_loop.tick_until_done();

                for _ in 0..2 {
                    let drawer = tree.find_by_test_id("drawer-wrapper");
                    assert_node_exists!(&drawer);

                    let survey_skip_button = drawer
                        .find_any_child_with()
                        .test_id("skip-button")
                        .find_first();
                    assert_node_exists!(&survey_skip_button);

                    test_game_loop.send_on_select_event(survey_skip_button.borrow_props().node_id);
                    tree = test_game_loop.tick_until_done();
                }

                let drawer = tree.find_by_test_id("drawer-wrapper");
                assert_node_exists!(&drawer);

                let survey_completed_wrapper = drawer
                    .find_any_child_with()
                    .test_id("survey-completed")
                    .find_first();
                assert_node_exists!(&survey_completed_wrapper);

                let close_button = survey_completed_wrapper
                    .find_any_child_with()
                    .test_id("close-button")
                    .find_first();
                assert_node_exists!(&close_button);

                assert_eq!(dismiss_called_times.get(), 0);

                test_game_loop.send_key_down_up_event_to_node(
                    close_button.borrow_props().node_id,
                    KeyCode::Escape,
                );
                test_game_loop.tick_until_done();

                assert_eq!(dismiss_called_times.get(), 1);
            },
        );
    }

    fn expect_no_calls() -> Context {
        let context = MockNetworkClient::new_context();
        context.expect().returning(|_| {
            let mut client = MockNetworkClient::default();
            client.expect_record_ias_feedback().never();
            client
        });
        context
    }
}
