[package]
name = "in-app-survey"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
log.workspace = true
serde.workspace = true
serde_json.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
cfg-test-attr-derive.workspace = true
fableous.workspace = true
drawer.workspace = true
lrc-image.workspace = true
network.workspace = true
app-config.workspace = true
auth.workspace = true
router.workspace = true
mockall.workspace = true
location.workspace = true
cross-app-events.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
    "mock_timer",
] }
network = { workspace = true, features = ["test_utils", "mock_network"] }
rstest.workspace = true
serial_test.workspace = true

[features]
default = []
test_utils = []
example_data = []
debug_impl = []
