use ignx_compositron::prelude::*;
use player::{
    DiagnosticsSubscribeEvent, PlayerCommand, PlayerEvent, PlayerFeature, PlayerFeatureType,
    StreamingTechnology,
};

const LOG_PREFIX: &str = "[player-features::diagnostics_feature]";

#[derive(<PERSON><PERSON>, <PERSON>lone)]
pub struct DiagnosticsFeatureContext {
    pub streaming_technology: Signal<Option<StreamingTechnology>>,
    set_streaming_technology: WriteSignal<Option<StreamingTechnology>>,
    set_player_command: WriteSignal<Option<PlayerCommand>>,
}

impl DiagnosticsFeatureContext {
    fn new(scope: Scope, set_player_command: WriteSignal<Option<PlayerCommand>>) -> Self {
        let (streaming_technology, set_streaming_technology) = create_signal(scope, None);
        Self {
            set_player_command,
            streaming_technology: streaming_technology.into(),
            set_streaming_technology,
        }
    }

    pub fn on_player_event(&self, event: &PlayerEvent) {
        match event {
            PlayerEvent::Initialisation(_) => {
                log::debug!("{LOG_PREFIX} requesting diagnostics feature");
                self.set_player_command.set(Some(PlayerCommand::GetFeature(
                    PlayerFeatureType::Diagnostics,
                )));
            }
            PlayerEvent::StreamingTechnologyChange(streaming_technology) => self
                .set_streaming_technology
                .set(streaming_technology.clone()),
            _ => (),
        }
    }

    pub fn on_player_feature(&self, feature: &PlayerFeature) {
        let PlayerFeature::Diagnostics(feature) = feature else {
            return;
        };

        log::debug!("{LOG_PREFIX} diagnostics feature acquired");

        let _ = feature.subscribe(DiagnosticsSubscribeEvent::StreamingTechnologyChange);
    }
}

pub fn provide_diagnostics_feature(
    scope: Scope,
    set_player_command: WriteSignal<Option<PlayerCommand>>,
) -> DiagnosticsFeatureContext {
    let ctx = DiagnosticsFeatureContext::new(scope, set_player_command);
    provide_context(scope, ctx);
    ctx
}

pub fn use_diagnostics_feature(scope: Scope) -> DiagnosticsFeatureContext {
    use_context(scope).unwrap_or_else(|| {
        #[cfg(not(test))]
        debug_assert!(
            false,
            "{LOG_PREFIX} DiagnosticsFeatureContext not found in provided scope"
        );
        log::error!(
            "{LOG_PREFIX} DiagnosticsFeatureContext not found in provided scope, using fallback"
        );
        DiagnosticsFeatureContext::new(scope, create_rw_signal(scope, None).write_only())
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use ignx_compositron::{app::launch_only_scope, player_data::PlayerCapabilities};
    use player::{DiagnosticsFeature, DiagnosticsFeatureCommand};

    #[test]
    fn requests_feature_on_init() {
        launch_only_scope(|scope| {
            let (player_command, set_player_command) = create_signal(scope, None);
            let feature = DiagnosticsFeature::new(scope);
            provide_diagnostics_feature(scope, set_player_command);

            match player_command.get_untracked() {
                None => (),
                Some(_) => panic!("expected no command initially"),
            }

            let ctx = use_diagnostics_feature(scope);
            ctx.on_player_event(&PlayerEvent::Initialisation(Ok(PlayerCapabilities {
                hdr_support: None,
                uhd_support: false,
                resize_support: None,
            })));

            match player_command.get_untracked() {
                Some(PlayerCommand::GetFeature(PlayerFeatureType::Diagnostics)) => (),
                _ => panic!("expected GetFeature(Diagnostics) command"),
            }

            ctx.on_player_feature(&PlayerFeature::Diagnostics(feature.clone()));

            feature.validate_commands(|cmds| {
                assert_eq!(cmds.len(), 1);
                assert!(matches!(
                    cmds.first().unwrap(),
                    DiagnosticsFeatureCommand::Subscribe(
                        DiagnosticsSubscribeEvent::StreamingTechnologyChange
                    )
                ))
            });
        });
    }

    #[test]
    fn should_not_panic_when_using_fallback() {
        launch_only_scope(|scope| {
            use_diagnostics_feature(scope);
        });
    }

    #[test]
    fn should_update_signal() {
        launch_only_scope(|scope| {
            let (_, set_player_command) = create_signal(scope, None);
            let feature = DiagnosticsFeature::new(scope);
            provide_diagnostics_feature(scope, set_player_command);

            let ctx = use_diagnostics_feature(scope);

            ctx.on_player_event(&PlayerEvent::Initialisation(Ok(PlayerCapabilities {
                hdr_support: None,
                uhd_support: false,
                resize_support: None,
            })));
            ctx.on_player_feature(&PlayerFeature::Diagnostics(feature));

            assert!(ctx.streaming_technology.get_untracked().is_none());

            ctx.on_player_event(&PlayerEvent::StreamingTechnologyChange(Some(
                StreamingTechnology::Dash,
            )));

            assert!(matches!(
                ctx.streaming_technology.get_untracked(),
                Some(StreamingTechnology::Dash)
            ));
        });
    }
}
