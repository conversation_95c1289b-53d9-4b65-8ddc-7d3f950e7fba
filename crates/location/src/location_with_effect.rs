use crate::Location;
use cfg_test_attr_derive::derive_test_only;
use fableous::animations::{FableMotionEasing, MotionEasing};
use ignx_compositron::animation::Animation;
use ignx_compositron::time::Duration;

/// Indicates the location transition with optional enter and exit effects
#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub struct LocationWithEffect {
    pub from_location: Option<Location>,
    pub to_location: Location,
    pub exit_effect: Option<ExitEffect>,
    pub enter_effect: Option<EnterEffect>,
}

impl LocationWithEffect {
    pub fn without_effect(from_location: Option<Location>, to_location: Location) -> Self {
        Self {
            from_location,
            to_location,
            exit_effect: None,
            enter_effect: None,
        }
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum ExitEffect {
    FadeToStart {
        duration: Duration,
        fade_media_background: bool,
    },
    FadeToEnd {
        duration: Duration,
        fade_media_background: bool,
    },
}

impl ExitEffect {
    pub fn duration(&self) -> &Duration {
        match &self {
            ExitEffect::FadeToStart { duration, .. } => duration,
            ExitEffect::FadeToEnd { duration, .. } => duration,
        }
    }

    pub fn to_translate_x(&self) -> f32 {
        match &self {
            ExitEffect::FadeToStart { .. } => -200.0,
            ExitEffect::FadeToEnd { .. } => 200.0,
        }
    }

    pub fn fade_media_background(&self) -> bool {
        match &self {
            ExitEffect::FadeToStart {
                fade_media_background,
                ..
            } => fade_media_background.to_owned(),
            ExitEffect::FadeToEnd {
                fade_media_background,
                ..
            } => fade_media_background.to_owned(),
        }
    }

    pub fn to_animation(&self) -> Animation {
        Animation::default()
            .with_interpolation(FableMotionEasing::Exit.to_interpolation())
            .with_duration(self.duration().to_owned())
    }
}

#[derive(Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum EnterEffect {
    FadeFromStart { duration: Duration },
    FadeFromEnd { duration: Duration },
}

impl EnterEffect {
    pub fn duration(&self) -> &Duration {
        match &self {
            EnterEffect::FadeFromStart { duration } => duration,
            EnterEffect::FadeFromEnd { duration } => duration,
        }
    }

    pub fn from_translate_x(&self) -> f32 {
        match &self {
            EnterEffect::FadeFromStart { duration: _ } => -200.0,
            EnterEffect::FadeFromEnd { duration: _ } => 200.0,
        }
    }

    pub fn to_animation(&self) -> Animation {
        Animation::default()
            .with_interpolation(FableMotionEasing::Enter.to_interpolation())
            .with_duration(self.duration().to_owned())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn exit_effect_duration() {
        let duration = Duration::from_secs(10);

        assert_eq!(
            ExitEffect::FadeToStart {
                duration,
                fade_media_background: false
            }
            .duration()
            .to_owned(),
            duration
        );
        assert_eq!(
            ExitEffect::FadeToEnd {
                duration,
                fade_media_background: false
            }
            .duration()
            .to_owned(),
            duration
        );
    }

    #[test]
    fn enter_effect_duration() {
        let duration = Duration::from_secs(10);

        assert_eq!(
            EnterEffect::FadeFromStart { duration }
                .duration()
                .to_owned(),
            duration
        );
        assert_eq!(
            EnterEffect::FadeFromEnd { duration }.duration().to_owned(),
            duration
        );
    }
}
