use crate::{PageType, RustPage};
use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;

#[derive(Serialize, NetworkParsed, Debug, Clone, PartialEq)]
pub enum NavigationDirection {
    Forward,
    Backwards,
}

#[derive(Serialize, NetworkParsed, Debug, Clone)]
#[derive_test_only(PartialEq)]
pub struct NavigationAction {
    pub direction: NavigationDirection,
    pub from: PageType,
    pub to: PageType,
}

impl Default for NavigationAction {
    fn default() -> Self {
        Self {
            direction: NavigationDirection::Forward,
            from: PageType::Rust(RustPage::RUST_EMPTY),
            to: PageType::Rust(RustPage::RUST_EMPTY),
        }
    }
}
