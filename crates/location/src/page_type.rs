#![allow(nonstandard_style)]

use derive_more::Display;
use network_parser::custom_network_parser;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::de::Error;
use serde::{Deserialize, Deserializer, Serialize};
use serde_json::Value;
use std::fmt;

#[derive(Serialize, NetworkParsed, Clone, Copy, Debug, PartialEq)]
pub enum RustPage {
    RUST_WELCOME,
    RUST_PROFILE_SELECTION,
    RUST_EMPTY,
    RUST_COLLECTIONS,
    RUST_DETAILS,
    RUST_LIVE_TV,
    RUST_STATION_DETAILS,
    RUST_PLAYBACK,
    RUST_DISCOVERY_PAGE,
    RUST_CHECKOUT,
    RUST_SEARCH,
    RUST_SPORTS_FAVORITES_DISCOVERY,
    RUST_SPORTS_SCHEDULE_PAGE,
    RUST_GO_TO_BACKGROUND,
    RUST_TVIF,
    RUST_AVATAR_SELECTION,
}

impl fmt::Display for RustPage {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{:?}", self)
    }
}

#[derive(Serialize, NetworkParsed, Clone, Copy, Debug, PartialEq)]
pub enum JSPage {
    UNLOADED,
    quit, // Switching to this page triggers app exit on ignite. Not used in other platforms
    EMPTY_ROUTE,
    OPEN_HOME_PAGE,
    LOADING_PAGE,
    ERROR_PAGE,
    DEVELOPER_TOOLS_PAGE,
    TTS_ERROR_PAGE,
    OPEN_DETAIL_PAGE,
    OPEN_LIVE_EVENT_DETAILS_PAGE,
    OPEN_HELP_PAGE, // note: this is the enum value for the Settings Page in JS
    OPEN_CODE_BASED_REGISTRATION_PAGE,
    OPEN_OAUTH_REGISTRATION_PAGE,
    OPEN_REGISTRATION_PAGE,
    OPEN_SEASON_BROWSE_PAGE,
    OPEN_YVL_PAGE,
    LAUNCH_APPLICATION,
    AV_LANDING_PAGE,
    START_PLAYBACK,
    START_PLAYBACK_LIVE,
    OPEN_XRAY_PAGE,
    NEW_DETAILS_PAGE,
    NEW_CONFIRMATION_PAGE,
    MANAGEMENT_PAGE,
    ON_DEVICE_PROMOS_PAGE,
    CUSTOMER_CONSENT_PAGE,
    FUSE_OFFER_ACTIVATION_PAGE,
    PAYMENT_FIXUP,

    // DO NOT USE (DEPRECATED OLD SLATE PAGE). Not to be confused with the clean slate COLLECTION_PAGE
    //#[serde(rename = "AV_COLLECTIONS_PAGE")]
    //Collections,
    MODAL,
    NAVIGATION_GATEWAY,
    PROFILE_SELECTION_PAGE,
    EDIT_PROFILES_NAME_PAGE,
    PROFILE_CREATION_PAGE,
    PROFILE_EDIT_PAGE,
    AVATAR_SELECTION_PAGE,
    CATEGORY_PAGE,
    RESIZABLE_PLAYER_TEST_PAGE,
    SEARCH_FLEX_PAGE,
    SSO_SIGNIN_PAGE,
    CREATE_ACCOUNT_PAGE,
    SECOND_SCREEN_READY_TO_CAST_PAGE,
    LOCAL_FEATURE_OVERRIDES_PAGE,

    // Page for use in testing
    UNSUPPORTED_PAGE,
    TEST_PLAYGROUND,

    // Clean Slate Pages
    COLLECTION_PAGE,
    FIND_PAGE,
    HOME_PAGE,
    ONBOARDING_PAGE,
    STORE_PAGE,
    LIVE_PAGE,
    LINEAR_STATION_DETAIL_PAGE,
    MY_STUFF_PAGE,
    FREE_WITH_ADS,
    TVOD_MODAL_HOST, // End of Clean Slate Pages
}

impl fmt::Display for JSPage {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{:?}", self)
    }
}

#[derive(Serialize, Clone, Copy, Debug, PartialEq, Display)]
#[serde(untagged)]
pub enum PageType {
    Js(JSPage),
    Rust(RustPage),
}

impl<'de> Deserialize<'de> for PageType {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let value = Value::deserialize(deserializer)?;
        let page_type: ParserReturnType<PageType> = (&value).try_into();
        match page_type {
            Ok(page_type) => Ok(page_type),
            Err(err) => Err(Error::custom(err)),
        }
    }
}

custom_network_parser!(PageType, |value: &Value| {
    let as_js_page: ParserReturnType<JSPage> = value.try_into();
    if let Ok(js_page) = as_js_page {
        return Ok(PageType::Js(js_page));
    }

    let as_rust_page: ParserReturnType<RustPage> = value.try_into();
    if let Ok(rust_page) = as_rust_page {
        return Ok(PageType::Rust(rust_page));
    }

    Err("Unable to parse PageType".to_string())
});

impl PageType {
    pub fn get_page_name(&self) -> String {
        match self {
            PageType::Js(js_page) => js_page.to_string(),
            PageType::Rust(rust_page) => rust_page.to_string(),
        }
    }

    pub fn is_refreshable(&self) -> bool {
        match self {
            PageType::Js(_) => false,

            PageType::Rust(RustPage::RUST_COLLECTIONS | RustPage::RUST_LIVE_TV) => true,

            // Listing all pages, so that adding a new one
            // will cause a compiler error
            PageType::Rust(
                RustPage::RUST_CHECKOUT
                | RustPage::RUST_DETAILS
                | RustPage::RUST_DISCOVERY_PAGE
                | RustPage::RUST_EMPTY
                | RustPage::RUST_PLAYBACK
                | RustPage::RUST_PROFILE_SELECTION
                | RustPage::RUST_SEARCH
                | RustPage::RUST_STATION_DETAILS
                | RustPage::RUST_WELCOME
                | RustPage::RUST_SPORTS_FAVORITES_DISCOVERY
                | RustPage::RUST_SPORTS_SCHEDULE_PAGE
                | RustPage::RUST_GO_TO_BACKGROUND
                | RustPage::RUST_TVIF
                | RustPage::RUST_AVATAR_SELECTION,
            ) => false,
        }
    }

    pub fn is_playback(&self) -> bool {
        matches!(
            self,
            PageType::Js(JSPage::START_PLAYBACK) | PageType::Rust(RustPage::RUST_PLAYBACK)
        )
    }
}
