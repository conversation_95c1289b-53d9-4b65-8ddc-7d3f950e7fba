#![allow(nonstandard_style)]
#[cfg(not(feature = "standalone_app"))]
use crate::JSPage;
use crate::{PageType, RustPage};
use serde::{Deserialize, Serialize};
use serde_json::{Map, Value};

// FIXME: Used within RPC so cannot be moved over to NetworkParsed
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub struct Location {
    pub pageType: PageType,
    pub pageParams: Map<String, Value>,
}

impl Location {
    pub fn empty() -> Self {
        Self {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: Map::new(),
        }
    }

    pub fn is_back(&self, log_source: impl AsRef<str>) -> bool {
        self.pageParams
            .get("back")
            .is_some_and(|value| match value {
                Value::String(v) => v == "true",
                Value::Bool(v) => *v,
                _ => {
                    log::error!(
                        "[{}] Unexpected value for pageParam back: {}",
                        log_source.as_ref(),
                        value
                    );
                    false
                }
            })
    }

    pub fn is_same_page_as(&self, loc_to_compare: &Location, ignore_back: bool) -> bool {
        // `ignore_back` only matters if `back` is present in either location
        if ignore_back
            && (self.is_back("Location::is_same_page_as")
                || loc_to_compare.is_back("Location::is_same_page_as"))
        {
            // in this case we need to compare the two locations excluding the `back` param
            let mut self_page_params = self.pageParams.clone();
            let mut compare_page_params = loc_to_compare.pageParams.clone();
            self_page_params.remove("back");
            compare_page_params.remove("back");

            self.pageType == loc_to_compare.pageType && self_page_params == compare_page_params
        } else {
            // normal comparison
            self == loc_to_compare
        }
    }

    pub fn contains_page_params(&self, compare_page_params: Map<String, Value>) -> bool {
        compare_page_params.iter().all(|(k, compare_val)| {
            self.pageParams
                .get(k)
                .is_some_and(|self_val| self_val == compare_val)
        })
    }
}

impl Default for Location {
    /// Default `Location` is set to `JSPage::Collections` as JS app is rendered initially.
    /// We should change this to `RustPage` when Rust app takes over the initial rendering.
    fn default() -> Self {
        #[cfg(not(feature = "standalone_app"))]
        let default_page_type = PageType::Js(JSPage::COLLECTION_PAGE);
        #[cfg(feature = "standalone_app")]
        let default_page_type = PageType::Rust(RustPage::RUST_COLLECTIONS);

        Self {
            pageType: default_page_type,
            pageParams: Map::new(),
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case::string_value(Value::String("true".to_string()), Value::String("false".to_string()))]
    #[case::bool_value(Value::Bool(true), Value::Bool(false))]
    fn is_back_valid(#[case] true_value: Value, #[case] false_value: Value) {
        let location_true = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![("back".to_string(), true_value)]),
        };

        let location_false = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![("back".to_string(), false_value)]),
        };

        assert!(location_true.is_back("test"));
        assert!(!location_false.is_back("test"));
    }

    #[test]
    fn is_back_invalid() {
        let location = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![(
                "back".to_string(),
                Value::Number(1.into()),
            )]),
        };

        assert!(!location.is_back("test"));
    }

    #[test]
    fn is_same_page_as_with_same_params() {
        let location = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(false)),
                ("myParam".to_string(), Value::String("value".to_string())),
            ]),
        };

        let location_to_compare = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(false)),
                ("myParam".to_string(), Value::String("value".to_string())),
            ]),
        };

        assert!(location.is_same_page_as(&location_to_compare, false));
    }

    #[test]
    fn is_same_page_as_with_different_params() {
        let location = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(false)),
                ("myParam".to_string(), Value::String("value".to_string())),
            ]),
        };

        let location_to_compare = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(false)),
                (
                    "myParam".to_string(),
                    Value::String("different".to_string()),
                ),
            ]),
        };

        assert!(!location.is_same_page_as(&location_to_compare, false));
    }

    #[test]
    fn is_same_page_as_with_different_back_and_ignore_back() {
        let location = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(true)),
                ("myParam".to_string(), Value::String("value".to_string())),
            ]),
        };

        let location_to_compare = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(false)),
                ("myParam".to_string(), Value::String("value".to_string())),
            ]),
        };

        assert!(location.is_same_page_as(&location_to_compare, true));
    }

    #[test]
    fn is_same_page_as_with_different_back_and_do_not_ignore_back() {
        let location = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(true)),
                ("myParam".to_string(), Value::String("value".to_string())),
            ]),
        };

        let location_to_compare = Location {
            pageType: PageType::Rust(RustPage::RUST_EMPTY),
            pageParams: serde_json::Map::from_iter(vec![
                ("back".to_string(), Value::Bool(false)),
                ("myParam".to_string(), Value::String("value".to_string())),
            ]),
        };

        assert!(!location.is_same_page_as(&location_to_compare, false));
    }

    mod contains_page_params {
        use super::*;
        #[test]
        fn contains_page_params_has_all_params() {
            let location = Location {
                pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                pageParams: serde_json::Map::from_iter(vec![
                    ("pageId".to_string(), Value::String("id".to_string())),
                    ("pageType".to_string(), Value::String("type".to_string())),
                    (
                        "serviceToken".to_string(),
                        Value::String("token".to_string()),
                    ),
                ]),
            };

            let compare_params = serde_json::Map::from_iter(vec![
                ("pageId".to_string(), Value::String("id".to_string())),
                ("pageType".to_string(), Value::String("type".to_string())),
            ]);

            assert!(location.contains_page_params(compare_params));
        }

        #[test]
        fn contains_page_params_mismatching_param() {
            let location = Location {
                pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                pageParams: serde_json::Map::from_iter(vec![
                    ("pageId".to_string(), Value::String("id".to_string())),
                    ("pageType".to_string(), Value::String("type".to_string())),
                    (
                        "serviceToken".to_string(),
                        Value::String("token".to_string()),
                    ),
                ]),
            };

            let compare_params = serde_json::Map::from_iter(vec![
                // `pageId` is different than location
                (
                    "pageId".to_string(),
                    Value::String("differentId".to_string()),
                ),
                ("pageType".to_string(), Value::String("type".to_string())),
            ]);

            assert!(!location.contains_page_params(compare_params));
        }

        #[test]
        fn contains_page_params_missing_param() {
            let location = Location {
                pageType: PageType::Rust(RustPage::RUST_COLLECTIONS),
                pageParams: serde_json::Map::from_iter(vec![
                    // No `pageId` param
                    ("pageType".to_string(), Value::String("type".to_string())),
                    (
                        "serviceToken".to_string(),
                        Value::String("token".to_string()),
                    ),
                ]),
            };

            let compare_params = serde_json::Map::from_iter(vec![
                ("pageId".to_string(), Value::String("id".to_string())),
                ("pageType".to_string(), Value::String("type".to_string())),
            ]);

            assert!(!location.contains_page_params(compare_params));
        }
    }
}
