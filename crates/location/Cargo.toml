[package]
name = "location"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
serde.workspace = true
serde_json.workspace = true
derive_more.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
log.workspace = true
fableous.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
cfg-test-attr-derive.workspace = true

[dev-dependencies]
rstest.workspace = true

[features]
default = []
standalone_app = []
debug_impl = []

[lints]
workspace = true
