mod endpoint_config;
mod localization_config;

#[double]
use endpoint_config::EndpointConfig;

#[double]
use localization_config::LocalizationConfig;

use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::reactive::*;
use mockall::{automock, mock};
use mockall_double::double;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;
use serde_json::Value;
use std::rc::Rc;
use std::str::FromStr;
use strum_macros::EnumString;
use synchronized_state_store::MockStateDispatcher;
// GRCOV_BEGIN_COVERAGE

#[double]
use synchronized_state_store::SynchronizedStateStore;

#[double]
use synchronized_state_store::StateDispatcher;

const APP_CONFIG_STORE_ID: &str = "app-config";

#[derive(Serialize, NetworkParsed, Clone, Debug, Default)]
#[derive_test_only(PartialEq)]
#[allow(nonstandard_style, reason = "JSON compability")]
pub struct Locale {
    uxLocale: Option<String>,
    preferredLanguage: String,
    previousUxLocale: Option<String>,
}

#[derive(Serialize, NetworkParsed, Clone, Debug, Default)]
#[derive_test_only(PartialEq)]
#[allow(nonstandard_style, reason = "JSON compability")]
pub struct LocalizationConfiguration {
    locale: Locale,
    marketplaceId: Option<String>,
    geoLocation: Option<String>,
    territoryPivotRollup: Option<String>,
    videoCountryOfRecord: Option<String>,
}

#[derive(Serialize, NetworkParsed, Clone, Debug, Default)]
#[derive_test_only(PartialEq)]
#[allow(nonstandard_style, reason = "JSON compability")]
pub struct AppConfigState {
    localizationConfiguration: LocalizationConfiguration,
}

pub struct AppConfigStore {
    app_config_synchronised_state_store: SynchronizedStateStore<AppConfigState>,
    endpoint_config: EndpointConfig,
    localization_config: LocalizationConfig,
    video_country_of_record: Signal<Option<String>>,
}

pub type AppConfigContext = Rc<dyn AppConfig>;

#[automock]
pub trait AppConfig {
    fn get_supported_locales(&self) -> Vec<String>;
    fn get_ux_locale(&self) -> String;
    fn get_geo_location(&self) -> Option<String>;
    fn get_marketplace_id(&self) -> Option<String>;
    fn get_base_url(&self) -> Option<String>;
    fn get_video_country_of_record(&self) -> Signal<Option<String>>;
    fn get_is_traveling_customer(&self) -> bool;
    fn get_is_twelve_hour_location(&self) -> bool;
}

#[cfg(feature = "test_utils")]
pub mod test_utils {
    use crate::AppConfigContext;
    use ignx_compositron::prelude::Scope;
    use ignx_compositron::reactive::provide_context;
    use std::rc::Rc;

    pub struct MockAppConfigBuilder {
        supported_locales: Vec<String>,
        ux_locale: String,
        geo_location: Option<String>,
        base_url: Option<String>,
        video_country_of_record: Option<String>,
        is_traveling_customer: bool,
        is_twelve_hour_location: bool,
    }

    impl MockAppConfigBuilder {
        pub fn new() -> Self {
            Self {
                supported_locales: vec!["en-US".to_string()],
                ux_locale: "en-US".to_string(),
                geo_location: None,
                base_url: None,
                video_country_of_record: None,
                is_traveling_customer: false,
                is_twelve_hour_location: false,
            }
        }

        pub fn set_supported_locales(mut self, supported_locales: Vec<String>) -> Self {
            self.supported_locales = supported_locales;
            self
        }

        pub fn set_ux_locale(mut self, ux_locale: String) -> Self {
            self.ux_locale = ux_locale;
            self
        }

        pub fn set_geo_location(mut self, geo_location: Option<String>) -> Self {
            self.geo_location = geo_location;
            self
        }

        pub fn set_base_url(mut self, base_url: Option<String>) -> Self {
            self.base_url = base_url;
            self
        }

        pub fn set_video_country_of_record(
            mut self,
            video_country_of_record: Option<String>,
        ) -> Self {
            self.video_country_of_record = video_country_of_record;
            self
        }

        pub fn set_is_traveling_customer(mut self, is_traveling_customer: bool) -> Self {
            self.is_traveling_customer = is_traveling_customer;
            self
        }

        pub fn set_is_twelve_hour_location(mut self, is_twelve_hour_location: bool) -> Self {
            self.is_twelve_hour_location = is_twelve_hour_location;
            self
        }

        pub fn build(self) -> super::MockAppConfig {
            let mut mock = super::MockAppConfig::new();
            mock.expect_get_supported_locales()
                .return_const(self.supported_locales);
            mock.expect_get_ux_locale().return_const(self.ux_locale);
            mock.expect_get_geo_location()
                .return_const(self.geo_location);
            mock.expect_get_base_url().return_const(self.base_url);
            mock.expect_get_is_traveling_customer()
                .return_const(self.is_traveling_customer);
            mock.expect_get_is_twelve_hour_location()
                .return_const(self.is_twelve_hour_location);
            mock
        }

        pub fn build_into_context(self, scope: Scope) {
            let mock = self.build();
            provide_context::<AppConfigContext>(scope, Rc::new(mock));
        }
    }
}

impl AppConfigStore {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher) -> AppConfigStore {
        let store = SynchronizedStateStore::new(
            scope,
            state_dispatcher,
            APP_CONFIG_STORE_ID.into(),
            AppConfigState::default(),
        );
        let store_read_signal = store.get_read_signal();
        Self {
            app_config_synchronised_state_store: store,
            endpoint_config: EndpointConfig::new(scope, state_dispatcher),
            localization_config: LocalizationConfig::new(scope, state_dispatcher),
            video_country_of_record: create_memo(scope, move |_| {
                store_read_signal
                    .with(|store| store.localizationConfiguration.videoCountryOfRecord.clone())
            })
            .into(),
        }
    }
}

#[derive(Debug, EnumString, PartialEq)]
pub enum Territory {
    AD, // Andorra
    AE, // United Arab Emirates
    AF, // Afghanistan
    AG, // Antigua and Barbuda
    AI, // Anguilla
    AL, // Albania
    AM, // Armenia
    AO, // Angola
    AQ, // Antarctica
    AR, // Argentina
    AS, // American Samoa
    AT, // Austria
    AU, // Australia
    AW, // Aruba
    AX, // Åland Islands
    AZ, // Azerbaijan
    BA, // Bosnia and Herzegovina
    BB, // Barbados
    BD, // Bangladesh
    BE, // Belgium
    BF, // Burkina Faso
    BG, // Bulgaria
    BH, // Bahrain
    BI, // Burundi
    BJ, // Benin
    BL, // Saint Barthélemy
    BM, // Bermuda
    BN, // Brunei Darussalam
    BO, // Bolivia (Plurinational State of)
    BQ, // Bonaire, Sint Eustatius and Saba
    BR, // Brazil
    BS, // Bahamas
    BT, // Bhutan
    BV, // Bouvet Island
    BW, // Botswana
    BY, // Belarus
    BZ, // Belize
    CA, // Canada
    CC, // Cocos (Keeling) Islands
    CD, // Congo, Democratic Republic of the
    CF, // Central African Republic
    CG, // Congo
    CH, // Switzerland
    CI, // Côte d'Ivoire
    CK, // Cook Islands
    CL, // Chile
    CM, // Cameroon
    CN, // China
    CO, // Colombia
    CR, // Costa Rica
    CU, // Cuba
    CV, // Cabo Verde
    CW, // Curaçao
    CX, // Christmas Island
    CY, // Cyprus
    CZ, // Czechia
    DE, // Germany
    DJ, // Djibouti
    DK, // Denmark
    DM, // Dominica
    DO, // Dominican Republic
    DZ, // Algeria
    EC, // Ecuador
    EE, // Estonia
    EG, // Egypt
    EH, // Western Sahara
    ER, // Eritrea
    ES, // Spain
    ET, // Ethiopia
    FI, // Finland
    FJ, // Fiji
    FK, // Falkland Islands (Malvinas)
    FM, // Micronesia (Federated States of)
    FO, // Faroe Islands
    FR, // France
    GA, // Gabon
    GB, // United Kingdom of Great Britain and Northern Ireland
    GD, // Grenada
    GE, // Georgia
    GF, // French Guiana
    GG, // Guernsey
    GH, // Ghana
    GI, // Gibraltar
    GL, // Greenland
    GM, // Gambia
    GN, // Guinea
    GP, // Guadeloupe
    GQ, // Equatorial Guinea
    GR, // Greece
    GS, // South Georgia and the South Sandwich Islands
    GT, // Guatemala
    GU, // Guam
    GW, // Guinea-Bissau
    GY, // Guyana
    HK, // Hong Kong
    HM, // Heard Island and McDonald Islands
    HN, // Honduras
    HR, // Croatia
    HT, // Haiti
    HU, // Hungary
    ID, // Indonesia
    IE, // Ireland
    IL, // Israel
    IM, // Isle of Man
    IN, // India
    IO, // British Indian Ocean Territory
    IQ, // Iraq
    IR, // Iran (Islamic Republic of)
    IS, // Iceland
    IT, // Italy
    JE, // Jersey
    JM, // Jamaica
    JO, // Jordan
    JP, // Japan
    KE, // Kenya
    KG, // Kyrgyzstan
    KH, // Cambodia
    KI, // Kiribati
    KM, // Comoros
    KN, // Saint Kitts and Nevis
    KP, // Korea (Democratic People's Republic of)
    KR, // Korea, Republic of
    KW, // Kuwait
    KY, // Cayman Islands
    KZ, // Kazakhstan
    LA, // Lao People's Democratic Republic
    LB, // Lebanon
    LC, // Saint Lucia
    LI, // Liechtenstein
    LK, // Sri Lanka
    LR, // Liberia
    LS, // Lesotho
    LT, // Lithuania
    LU, // Luxembourg
    LV, // Latvia
    LY, // Libya
    MA, // Morocco
    MC, // Monaco
    MD, // Moldova, Republic of
    ME, // Montenegro
    MF, // Saint Martin (French part)
    MG, // Madagascar
    MH, // Marshall Islands
    MK, // North Macedonia
    ML, // Mali
    MM, // Myanmar
    MN, // Mongolia
    MO, // Macao
    MP, // Northern Mariana Islands
    MQ, // Martinique
    MR, // Mauritania
    MS, // Montserrat
    MT, // Malta
    MU, // Mauritius
    MV, // Maldives
    MW, // Malawi
    MX, // Mexico
    MY, // Malaysia
    MZ, // Mozambique
    NA, // Namibia
    NC, // New Caledonia
    NE, // Niger
    NF, // Norfolk Island
    NG, // Nigeria
    NI, // Nicaragua
    NL, // Netherlands
    NO, // Norway
    NP, // Nepal
    NR, // Nauru
    NU, // Niue
    NZ, // New Zealand
    OM, // Oman
    PA, // Panama
    PE, // Peru
    PF, // French Polynesia
    PG, // Papua New Guinea
    PH, // Philippines
    PK, // Pakistan
    PL, // Poland
    PM, // Saint Pierre and Miquelon
    PN, // Pitcairn
    PR, // Puerto Rico
    PS, // Palestine, State of
    PT, // Portugal
    PW, // Palau
    PY, // Paraguay
    QA, // Qatar
    RE, // Réunion
    RO, // Romania
    RS, // Serbia
    RU, // Russian Federation
    RW, // Rwanda
    SA, // Saudi Arabia
    SB, // Solomon Islands
    SC, // Seychelles
    SD, // Sudan
    SE, // Sweden
    SG, // Singapore
    SH, // Saint Helena, Ascension and Tristan da Cunha
    SI, // Slovenia
    SJ, // Svalbard and Jan Mayen
    SK, // Slovakia
    SL, // Sierra Leone
    SM, // San Marino
    SN, // Senegal
    SO, // Somalia
    SR, // Suriname
    SS, // South Sudan
    ST, // Sao Tome and Principe
    SV, // El Salvador
    SX, // Sint Maarten (Dutch part)
    SY, // Syrian Arab Republic
    SZ, // Eswatini
    TC, // Turks and Caicos Islands
    TD, // Chad
    TF, // French Southern Territories
    TG, // Togo
    TH, // Thailand
    TJ, // Tajikistan
    TK, // Tokelau
    TL, // Timor-Leste
    TM, // Turkmenistan
    TN, // Tunisia
    TO, // Tonga
    TR, // Turkey
    TT, // Trinidad and Tobago
    TV, // Tuvalu
    TW, // Taiwan, Province of China
    TZ, // Tanzania, United Republic of
    UA, // Ukraine
    UG, // Uganda
    UM, // United States Minor Outlying Islands
    US, // United States of America
    UY, // Uruguay
    UZ, // Uzbekistan
    VA, // Holy See
    VC, // Saint Vincent and the Grenadines
    VE, // Venezuela (Bolivarian Republic of)
    VG, // Virgin Islands (British)
    VI, // Virgin Islands (U.S.)
    VN, // Vietnam
    VU, // Vanuatu
    WF, // Wallis and Futuna
    WS, // Samoa
    YE, // Yemen
    YT, // Mayotte
    ZA, // South Africa
    ZM, // Zambia
    ZW, // Zimbabwe
}

impl Territory {
    pub fn is_post_brexit_eu(&self) -> bool {
        matches!(
            self,
            Territory::AX
                | Territory::AD
                | Territory::AT
                | Territory::BE
                | Territory::BG
                | Territory::HR
                | Territory::CY
                | Territory::CZ
                | Territory::DK
                | Territory::EE
                | Territory::FI
                | Territory::FR
                | Territory::GF
                | Territory::PF
                | Territory::TF
                | Territory::GP
                | Territory::MQ
                | Territory::DE
                | Territory::GI
                | Territory::GR
                | Territory::HU
                | Territory::IS
                | Territory::IE
                | Territory::IT
                | Territory::LV
                | Territory::LI
                | Territory::LT
                | Territory::LU
                | Territory::MT
                | Territory::YT
                | Territory::MC
                | Territory::ME
                | Territory::NL
                | Territory::NO
                | Territory::PL
                | Territory::PT
                | Territory::RE
                | Territory::RO
                | Territory::PM
                | Territory::SM
                | Territory::SK
                | Territory::SI
                | Territory::ES
                | Territory::MF
                | Territory::SE
        )
    }

    pub fn is_post_brexit_uk(&self) -> bool {
        matches!(
            self,
            Territory::GB | Territory::GG | Territory::IM | Territory::JE
        )
    }

    pub fn is_us(&self) -> bool {
        matches!(
            self,
            Territory::AS
                | Territory::GU
                | Territory::MP
                | Territory::PR
                | Territory::UM
                | Territory::US
                | Territory::VI
        )
    }

    pub fn is_twelve_hour(&self) -> bool {
        matches!(
            self,
            Territory::AU
                | Territory::CA
                | Territory::GB
                | Territory::IN
                | Territory::MX
                | Territory::NZ
                | Territory::US
                | Territory::ZA
        )
    }
}

impl AppConfig for AppConfigStore {
    fn get_supported_locales(&self) -> Vec<String> {
        self.localization_config.get_supported_locales()
    }

    fn get_ux_locale(&self) -> String {
        self.localization_config.get_ux_locale()
    }

    fn get_geo_location(&self) -> Option<String> {
        self.app_config_synchronised_state_store
            .derive(|config| config.localizationConfiguration.geoLocation.clone())
    }

    fn get_marketplace_id(&self) -> Option<String> {
        self.app_config_synchronised_state_store
            .derive(|config| config.localizationConfiguration.marketplaceId.clone())
    }

    fn get_base_url(&self) -> Option<String> {
        Some(self.endpoint_config.get_gasc_endpoint())
    }

    fn get_video_country_of_record(&self) -> Signal<Option<String>> {
        self.video_country_of_record
    }

    fn get_is_traveling_customer(&self) -> bool {
        let vcr = self.get_video_country_of_record().get_untracked();
        let geo = self.get_geo_location();

        match (vcr, geo) {
            (Some(v), Some(g)) => {
                let vcr_territory = Territory::from_str(v.as_str());
                let geo_territory = Territory::from_str(g.as_str());

                match (vcr_territory, geo_territory) {
                    (Ok(vcr), Ok(geo)) => {
                        vcr != geo
                            && !(vcr.is_post_brexit_eu() && geo.is_post_brexit_eu())
                            && !(vcr.is_post_brexit_uk() && geo.is_post_brexit_uk())
                            && !(vcr.is_us() && geo.is_us())
                    }
                    // fallback to comparing the strings.
                    _ => !v.eq(&g),
                }
            }
            _ => false,
        }
    }

    fn get_is_twelve_hour_location(&self) -> bool {
        let geo = self.get_geo_location();

        match geo {
            Some(g) => {
                let geo_territory = Territory::from_str(g.as_str());

                match geo_territory {
                    Ok(geo) => geo.is_twelve_hour(),
                    _ => false,
                }
            }
            _ => false,
        }
    }
}

mock! {
    pub AppConfigStore {
        pub fn new(scope: Scope, state_dispatcher: &MockStateDispatcher) -> Self;
    }
    impl AppConfig for AppConfigStore {
        fn get_supported_locales(&self) -> Vec<String>;
        fn get_ux_locale(&self) -> String;
        fn get_geo_location(&self) -> Option<String>;
        fn get_marketplace_id(&self) -> Option<String>;
        fn get_base_url(&self) -> Option<String>;
        fn get_video_country_of_record(&self) -> Signal<Option<String>>;
        fn get_is_traveling_customer(&self) -> bool;
        fn get_is_twelve_hour_location(&self) -> bool;
    }
}

#[cfg(test)]
mod test {
    use crate::endpoint_config::MockEndpointConfig;
    use crate::localization_config::MockLocalizationConfig;
    use crate::{
        AppConfig, AppConfigState, AppConfigStore, Locale, LocalizationConfiguration,
        APP_CONFIG_STORE_ID,
    };
    use ignx_compositron::app::launch_only_scope;
    use ignx_compositron::reactive::{create_rw_signal, Scope, SignalGetUntracked};
    use synchronized_state_store::test_utils::mock_synchronized_state_store_new_with_mock;
    use synchronized_state_store::{MockStateDispatcher, MockSynchronizedStateStore};

    fn get_mock_app_config_state(geo_location: Option<String>) -> AppConfigState {
        AppConfigState {
            localizationConfiguration: LocalizationConfiguration {
                locale: Locale {
                    uxLocale: Some("uxLocale".to_string()),
                    preferredLanguage: "".to_string(),
                    previousUxLocale: None,
                },
                marketplaceId: None,
                geoLocation: Some(geo_location.unwrap_or("GB".to_string())),
                territoryPivotRollup: None,
                videoCountryOfRecord: Some("IE".to_string()),
            },
        }
    }

    fn construct_app_config_store_with_mocked_derive(
        scope: Scope,
        video_country_of_record: Option<String>,
        geo_location: Option<String>,
    ) -> AppConfigStore {
        let mut mock_sync_store = MockSynchronizedStateStore::default();
        let mock_app_config_state = get_mock_app_config_state(geo_location);

        mock_sync_store
            .expect_derive::<Option<String>>()
            .once()
            .returning(move |input| input(&mock_app_config_state));

        AppConfigStore {
            app_config_synchronised_state_store: mock_sync_store,
            endpoint_config: MockEndpointConfig::default(),
            localization_config: MockLocalizationConfig::default(),
            video_country_of_record: create_rw_signal(
                scope,
                Some(video_country_of_record.unwrap_or("IE".to_string())),
            )
            .into(),
        }
    }

    #[test]
    fn should_construct_app_config_store() {
        launch_only_scope(|scope| {
            let mut mock_sync_store = MockSynchronizedStateStore::<AppConfigState>::default();
            mock_sync_store
                .expect_get_read_signal()
                .return_once_st(move || {
                    create_rw_signal(scope, get_mock_app_config_state(None)).read_only()
                });

            let (_guard, _context, store_info) =
                mock_synchronized_state_store_new_with_mock::<AppConfigState>(mock_sync_store);

            let mock_state_dispatcher = MockStateDispatcher::default();
            let expected_app_config_state = AppConfigState::default();

            let mock_endpoint_config = MockEndpointConfig::default();
            let mock_endpoint_config_ctx = MockEndpointConfig::new_context();
            mock_endpoint_config_ctx
                .expect()
                .once()
                .return_once(move |_, _| mock_endpoint_config);

            let mock_localization_config = MockLocalizationConfig::default();
            let mock_localization_config_ctx = MockLocalizationConfig::new_context();
            mock_localization_config_ctx
                .expect()
                .once()
                .return_once(move |_, _| mock_localization_config);

            let app_config = AppConfigStore::new(scope, &mock_state_dispatcher);

            let store_data = store_info.read().expect("Unable to read store_info data!");
            assert_eq!(store_data.id, APP_CONFIG_STORE_ID);
            assert_eq!(store_data.state, Some(expected_app_config_state));
            assert_eq!(
                app_config.get_video_country_of_record().get_untracked(),
                Some("IE".to_string())
            )
        });
    }

    #[test]
    fn should_get_supported_locales_from_config() {
        launch_only_scope(|scope| {
            let mut mock_localization_config = MockLocalizationConfig::default();

            mock_localization_config
                .expect_get_supported_locales()
                .once()
                .return_once(|| vec!["item1".to_string(), "item2".to_string()]);

            let app_config_store = AppConfigStore {
                app_config_synchronised_state_store: MockSynchronizedStateStore::default(),
                endpoint_config: MockEndpointConfig::default(),
                localization_config: mock_localization_config,
                video_country_of_record: create_rw_signal(scope, None).into(),
            };

            let expected = vec!["item1".to_string(), "item2".to_string()];
            let actual = app_config_store.get_supported_locales();

            assert_eq!(actual, expected);
        })
    }

    #[test]
    fn should_get_ux_locale_from_config() {
        launch_only_scope(|scope| {
            let mut mock_localization_config = MockLocalizationConfig::default();

            mock_localization_config
                .expect_get_ux_locale()
                .once()
                .return_once(|| String::from("mock-uxLocale"));

            let app_config_store = AppConfigStore {
                app_config_synchronised_state_store: MockSynchronizedStateStore::default(),
                endpoint_config: MockEndpointConfig::default(),
                localization_config: mock_localization_config,
                video_country_of_record: create_rw_signal(scope, None).into(),
            };

            let expected = String::from("mock-uxLocale");
            let actual = app_config_store.get_ux_locale();

            assert_eq!(actual, expected);
        })
    }

    #[test]
    fn should_get_geo_location_from_config() {
        launch_only_scope(|scope| {
            let app_config_store = construct_app_config_store_with_mocked_derive(scope, None, None);
            assert_eq!(app_config_store.get_geo_location(), Some("GB".to_string()));
        })
    }

    mod test_traveling_customer {
        use super::*;
        use rstest::*;

        #[test]
        fn should_get_is_traveling_customer_true() {
            launch_only_scope(|scope| {
                let app_config_store =
                    construct_app_config_store_with_mocked_derive(scope, None, None);
                assert!(app_config_store.get_is_traveling_customer());
            })
        }

        #[test]
        fn should_get_is_traveling_customer_false_eu_group() {
            launch_only_scope(|scope| {
                let app_config_store = construct_app_config_store_with_mocked_derive(
                    scope,
                    Some("BG".to_string()),
                    Some("IS".to_string()),
                );
                assert!(!app_config_store.get_is_traveling_customer());
            })
        }

        #[test]
        fn should_get_is_traveling_customer_false_uk_group() {
            launch_only_scope(|scope| {
                let app_config_store = construct_app_config_store_with_mocked_derive(
                    scope,
                    Some("GB".to_string()),
                    Some("GG".to_string()),
                );
                assert!(!app_config_store.get_is_traveling_customer());
            })
        }

        #[test]
        fn should_get_is_traveling_customer_false_us_group() {
            launch_only_scope(|scope| {
                let app_config_store = construct_app_config_store_with_mocked_derive(
                    scope,
                    Some("GU".to_string()),
                    Some("UM".to_string()),
                );
                assert!(!app_config_store.get_is_traveling_customer());
            })
        }

        #[rstest]
        #[case("Unknown".to_string(), "OtherUnknown".to_string(), true)]
        #[case("Unknown".to_string(), "Unknown".to_string(), false)]
        fn should_get_is_traveling_customer_false_vcr_unknown(
            #[case] vcr: String,
            #[case] geo: String,
            #[case] expected: bool,
        ) {
            launch_only_scope(move |scope| {
                let app_config_store =
                    construct_app_config_store_with_mocked_derive(scope, Some(vcr), Some(geo));
                assert_eq!(app_config_store.get_is_traveling_customer(), expected);
            })
        }
    }

    mod get_is_twelve_hour_location {
        use super::*;

        #[test]
        fn should_get_is_twelve_hour_location_true() {
            launch_only_scope(|scope| {
                let app_config_store = construct_app_config_store_with_mocked_derive(
                    scope,
                    None,
                    Some("US".to_string()),
                );
                assert!(app_config_store.get_is_twelve_hour_location());
            })
        }

        #[test]
        fn should_get_is_twelve_hour_location_false() {
            launch_only_scope(|scope| {
                let app_config_store = construct_app_config_store_with_mocked_derive(
                    scope,
                    None,
                    Some("RO".to_string()),
                );
                assert!(!app_config_store.get_is_twelve_hour_location());
            })
        }

        #[test]
        fn should_get_is_twelve_hour_location_unknown() {
            launch_only_scope(|scope| {
                let app_config_store = construct_app_config_store_with_mocked_derive(
                    scope,
                    None,
                    Some("Unknown".to_string()),
                );
                assert!(!app_config_store.get_is_twelve_hour_location());
            })
        }
    }

    #[test]
    fn should_get_base_url_from_endpoint_config() {
        launch_only_scope(|scope| {
            let mut mock_endpoint_config = MockEndpointConfig::default();

            mock_endpoint_config
                .expect_get_gasc_endpoint()
                .returning(|| "MockBaseUrl".to_string());

            let app_config_store = AppConfigStore {
                app_config_synchronised_state_store: MockSynchronizedStateStore::default(),
                endpoint_config: mock_endpoint_config,
                localization_config: MockLocalizationConfig::default(),
                video_country_of_record: create_rw_signal(scope, None).into(),
            };

            assert_eq!(
                app_config_store.get_base_url(),
                Some("MockBaseUrl".to_string())
            );
        })
    }
}
