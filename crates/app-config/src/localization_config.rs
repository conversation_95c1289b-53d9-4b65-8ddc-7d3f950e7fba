use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::reactive::*;
use mockall::mock;
use mockall_double::double;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;
use serde_json::Value;
#[double]
use synchronized_state_store::{StateDispatcher, SynchronizedStateStore};

const LOCALIZATION_CONFIG_STORE_ID: &str = "localization-config";

#[derive(Serialize, NetworkParsed, Clone, Debug, Default)]
#[derive_test_only(PartialEq)]
#[allow(nonstandard_style, reason = "JSON compability")]
pub struct LocalizationConfigState {
    /** List of all locales supported by the client */
    supportedLocales: Vec<String>,
    uxLocale: String,
}

#[derive(Clone)]
pub struct LocalizationConfig {
    localization_config_synchronised_state_store: SynchronizedStateStore<LocalizationConfigState>,
}

impl LocalizationConfig {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher) -> Self {
        Self {
            localization_config_synchronised_state_store: SynchronizedStateStore::new(
                scope,
                state_dispatcher,
                LOCALIZATION_CONFIG_STORE_ID.into(),
                LocalizationConfigState::default(),
            ),
        }
    }

    pub fn get_supported_locales(&self) -> Vec<String> {
        self.localization_config_synchronised_state_store
            .derive(|config| config.supportedLocales.clone())
    }

    pub fn get_ux_locale(&self) -> String {
        self.localization_config_synchronised_state_store
            .derive(|config| config.uxLocale.clone())
    }
}

mock! {
    pub LocalizationConfig {
        pub fn new(scope: Scope, state_dispatcher: &StateDispatcher) -> Self;
        pub fn get_supported_locales(&self) -> Vec<String>;
        pub fn get_ux_locale(&self) -> String;
    }
}

#[cfg(test)]
mod test {
    use crate::localization_config::{
        LocalizationConfig, LocalizationConfigState, LOCALIZATION_CONFIG_STORE_ID,
    };
    use ignx_compositron::app::launch_only_scope;
    use synchronized_state_store::test_utils::*;
    use synchronized_state_store::{MockStateDispatcher, MockSynchronizedStateStore};

    #[test]
    fn should_construct_localization_config() {
        launch_only_scope(|scope| {
            let mock_state_dispatcher = MockStateDispatcher::default();

            let (_guard, _context, store_info) =
                mock_synchronized_state_store_new::<LocalizationConfigState>();
            LocalizationConfig::new(scope, &mock_state_dispatcher);

            let store_data = store_info.read().expect("Able to read store info");
            assert_eq!(store_data.id, LOCALIZATION_CONFIG_STORE_ID);
            assert_eq!(store_data.state, Some(LocalizationConfigState::default()));
        });
    }

    #[test]
    fn should_get_supported_locales() {
        let mut mock_sync_state_store = MockSynchronizedStateStore::default();

        let mut state = LocalizationConfigState::default();
        state.supportedLocales = vec!["locale1".to_string(), "locale2".to_string()];

        mock_sync_state_store
            .expect_derive::<Vec<String>>()
            .once()
            .return_once(move |input_fn| input_fn(&state));

        let locale_config = LocalizationConfig {
            localization_config_synchronised_state_store: mock_sync_state_store,
        };

        assert_eq!(
            locale_config.get_supported_locales(),
            vec!["locale1".to_string(), "locale2".to_string()]
        );
    }

    #[test]
    fn should_get_ux_locale() {
        let mut mock_sync_state_store = MockSynchronizedStateStore::default();

        let mut state = LocalizationConfigState::default();
        state.uxLocale = "es_ES".to_string();

        mock_sync_state_store
            .expect_derive::<String>()
            .once()
            .return_once(move |input_fn| input_fn(&state));

        let locale_config = LocalizationConfig {
            localization_config_synchronised_state_store: mock_sync_state_store,
        };

        assert_eq!(locale_config.get_ux_locale(), String::from("es_ES"));
    }
}
