use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::reactive::*;
use mockall::mock;
use mockall_double::double;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;
use serde_json::Value;
use synchronized_state_store::MockStateDispatcher;
#[double]
use synchronized_state_store::StateDispatcher;
#[double]
use synchronized_state_store::SynchronizedStateStore;

const ENDPOINT_CONFIG_STORE_ID: &str = "endpoint-config";

#[derive(Serialize, NetworkParsed, Clone, Debug, Default)]
#[derive_test_only(PartialEq)]
#[allow(nonstandard_style, reason = "JSON compability")]
pub struct EndpointConfigState {
    authEndpoint: Option<String>,
    baseUrl: Option<String>,
    clientMetricsUrl: Option<String>,
    gascEndpoint: String,
    gascResiliencyEndpoints: Vec<String>,
    atvEndpoint: String,
}

#[derive(Clone)]
pub struct EndpointConfig {
    endpoint_config_synchronised_state_store: SynchronizedStateStore<EndpointConfigState>,
}

impl EndpointConfig {
    pub fn new(scope: Scope, state_dispatcher: &StateDispatcher) -> Self {
        Self {
            endpoint_config_synchronised_state_store: SynchronizedStateStore::new(
                scope,
                state_dispatcher,
                ENDPOINT_CONFIG_STORE_ID.into(),
                EndpointConfigState::default(),
            ),
        }
    }

    // marked as test until its used
    #[cfg(test)]
    pub fn get_base_url(&self) -> Option<String> {
        self.endpoint_config_synchronised_state_store
            .derive(|config| config.baseUrl.clone())
    }

    pub fn get_gasc_endpoint(&self) -> String {
        self.endpoint_config_synchronised_state_store
            .derive(|config| config.gascEndpoint.clone())
    }
}

mock! {
    pub EndpointConfig {
        pub fn new(scope: Scope, state_dispatcher: &MockStateDispatcher) -> Self;
        pub fn get_base_url(&self) -> Option<String>;
        pub fn get_gasc_endpoint(&self) -> String;
    }
}

#[cfg(test)]
mod test {
    use crate::endpoint_config::{EndpointConfig, EndpointConfigState, ENDPOINT_CONFIG_STORE_ID};
    use ignx_compositron::app::launch_only_scope;
    use synchronized_state_store::test_utils::mock_synchronized_state_store_new;
    use synchronized_state_store::{MockStateDispatcher, MockSynchronizedStateStore};

    #[test]
    fn should_construct_using_new() {
        launch_only_scope(|scope| {
            let mock_state_dispatcher = MockStateDispatcher::default();

            let (_guard, _context, store_info) =
                mock_synchronized_state_store_new::<EndpointConfigState>();
            EndpointConfig::new(scope, &mock_state_dispatcher);

            let store_data = store_info.read().expect("Unable to read store_info");
            assert_eq!(store_data.id, ENDPOINT_CONFIG_STORE_ID);
            assert_eq!(store_data.state, Some(EndpointConfigState::default()));
        });
    }

    #[test]
    fn should_get_base_url_from_endpoint_config() {
        let mut mock_sync_store = MockSynchronizedStateStore::default();

        let mut mock_state = EndpointConfigState::default();
        mock_state.baseUrl = Some("BaseUrl".to_string());

        mock_sync_store
            .expect_derive::<Option<String>>()
            .once()
            .return_once(move |inner_fn| inner_fn(&mock_state));

        let endpoint_config = EndpointConfig {
            endpoint_config_synchronised_state_store: mock_sync_store,
        };

        assert_eq!(endpoint_config.get_base_url(), Some("BaseUrl".to_string()));
    }

    #[test]
    fn should_get_gasc_endpoint_from_config() {
        let mut mock_sync_store = MockSynchronizedStateStore::default();

        let mut mock_state = EndpointConfigState::default();
        mock_state.gascEndpoint = "gasc.endpoint.amazonapi.com".to_string();

        mock_sync_store
            .expect_derive::<String>()
            .once()
            .return_once(move |inner_fn| inner_fn(&mock_state));

        let endpoint_config = EndpointConfig {
            endpoint_config_synchronised_state_store: mock_sync_store,
        };

        assert_eq!(
            endpoint_config.get_gasc_endpoint(),
            "gasc.endpoint.amazonapi.com".to_string()
        );
    }
}
