# FireTV

This crate provides functionality for detecting FireTV devices and handling FireTV-specific features for the Prime Video Living Room Rust Client.

## Overview

The `firetv` crate is responsible for:

1. Detecting if the current device is a FireTV device
1. Providing a context for FireTV-specific functionality
1. Managing FireTV playback events through integration with the `firetv-playback` crate

## Usage

```rust
use firetv::{FireTV, use_firetv_context};
use ignx_compositron::prelude::AppContext;

fn init_firetv(ctx: &AppContext) {
    // Get or create a FireTV context
    let firetv_context = use_firetv_context(ctx);
    
    // Check if the current device is a FireTV
    if firetv_context.is_firetv() {
        // Perform other FireTV-specific operations 
    }
}
```

## Testing

The crate includes tests for all components and provides mock implementations for testing through the `test_utils` feature.
