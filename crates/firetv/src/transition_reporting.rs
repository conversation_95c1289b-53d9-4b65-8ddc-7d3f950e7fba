use serde::Serialize;

pub(crate) const CHANNEL_INTERACTION_MESSAGE_TYPE: &str = "gmb.firetv.channel_interaction";

#[derive(Serialize, Debug)]
pub enum FireTVChannelIngressType {
    CHANNEL,
    SUBSCRIPTION,
}

#[derive(Serialize, Debug)]
#[allow(non_snake_case)]
pub struct FireTVChannelTransitionEvent<'a> {
    #[allow(non_snake_case)]
    benefitId: &'a str,
    #[allow(non_snake_case)]
    ingressType: FireTVChannelIngressType,
}

impl<'a> FireTVChannelTransitionEvent<'a> {
    pub fn new(
        ingress_type: FireTVChannelIngressType,
        benefit_id: &'a str,
    ) -> FireTVChannelTransitionEvent<'a> {
        FireTVChannelTransitionEvent {
            ingressType: ingress_type,
            benefitId: benefit_id,
        }
    }
}

#[cfg(any(test, feature = "test_utils"))]
impl<'a> FireTVChannelTransitionEvent<'a> {
    pub fn benefit_id(&self) -> &'a str {
        self.benefitId
    }

    pub fn ingress_type(&self) -> &FireTVChannelIngressType {
        &self.ingressType
    }
}
