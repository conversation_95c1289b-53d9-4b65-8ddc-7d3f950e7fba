/// `FireTV` module for Prime Video Living Room Rust Client.
///
/// This crate provides functionality to detect and handle `FireTV` devices.
/// It offers a simple API to check if the current device is a `FireTV` device
/// and to register handlers for FireTV-specific events.
///
/// # Examples
///
/// ```ignore
/// use firetv::{FireTV, use_firetv_context};
/// use ignx_compositron::prelude::AppContext;
///
/// // In a component or service initialization:
/// fn something(ctx: &AppContext) {
///     // Get or create a FireTV context
///     let firetv_context = use_firetv_context(ctx);
///     
///     // Check if the current device is a FireTV
///     if firetv_context.is_firetv() {
///         // Perform other FireTV-specific initialization
///     }
/// }
/// ```
mod context;
mod firetv;
mod transition_reporting;

pub use context::use_firetv_context;
pub use firetv::FireTV;
pub use transition_reporting::{FireTVChannelIngressType, FireTVChannelTransitionEvent};

#[cfg(feature = "test_utils")]
pub use firetv::MockFireTV;

#[cfg(feature = "init")]
pub use context::FireTVContext;
