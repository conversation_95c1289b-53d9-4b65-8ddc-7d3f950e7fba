/// Implementation of the `FireTVContext` for Prime Video Living Room Rust Client.
///
/// This module provides the concrete implementation of the `FireTV` trait
/// and handles the context management for `FireTV` detection and event handling.
use crate::firetv::FireTV;
use crate::transition_reporting::CHANNEL_INTERACTION_MESSAGE_TYPE;
use crate::FireTVChannelTransitionEvent;
#[allow(deprecated, reason = "intended consumer crate")]
use firetv_detection::is_firetv_from;
use firetv_playback::FireTVPlaybackContext;
#[cfg(not(test))]
use ignx_compositron::gmb::generic_message_bus_send;
#[cfg(test)]
use ignx_compositron::reactive::StoredValue;
use ignx_compositron::{
    device_information::DeviceInformation,
    prelude::{provide_context, use_context, AppContext, Scope},
};
use playback::context::{use_playback, PlaybackContext};
use playback_cache::{use_playback_cache, PlaybackCache};
use serde_json::json;
use std::rc::Rc;

#[cfg(test)]
fn mock_gmb_send(ctx: &AppContext, event_type: &str, content: &str) {
    let Some(store) = use_context::<StoredValue<(String, String, usize)>>(ctx.scope) else {
        return;
    };
    store.update_value(|(e_t, c, count)| {
        *count += 1;
        *e_t = event_type.to_string();
        *c = content.to_string();
    });
}

/// Implementation of the `FireTV` trait for the app.
///
/// This struct provides the concrete implementation of the `FireTV` trait
/// and handles the context management for `FireTV` detection and event handling.
pub struct FireTVContext {
    pub(crate) device_information: Rc<DeviceInformation>,
    pub(crate) playback: FireTVPlaybackContext,
}

impl FireTV for FireTVContext {
    fn is_firetv(&self) -> bool {
        #[allow(deprecated, reason = "intended consumer crate")]
        is_firetv_from(&self.device_information)
    }

    fn register_playback_events(&mut self, ctx: &AppContext) {
        if self.is_firetv() {
            self.playback.register_playback_load_event(ctx);
            self.playback.register_playback_cache_event(ctx);
            self.playback.register_playback_play_event(ctx);
        }
    }

    fn propagate_transition_event<'a>(
        &self,
        ctx: &AppContext,
        event: &FireTVChannelTransitionEvent<'a>,
    ) {
        let content = json!(event).to_string();
        #[cfg(not(test))]
        generic_message_bus_send(ctx, CHANNEL_INTERACTION_MESSAGE_TYPE, &content);
        #[cfg(test)]
        mock_gmb_send(ctx, CHANNEL_INTERACTION_MESSAGE_TYPE, &content);
    }
}

impl FireTVContext {
    /// Creates a new [`FireTVContext`] instance with default device information.
    ///
    /// This constructor creates a new [`FireTVContext`] with a default [`DeviceInformation`] instance
    /// and the provided playback context and cache.
    ///
    /// # Arguments
    ///
    /// * `playback_context` - The playback context to use for handling playback events
    /// * `playback_cache` - The playback cache to use for handling cache events
    ///
    /// # Returns
    ///
    /// A new [`FireTVContext`] instance with default device information.
    pub fn new(playback_context: PlaybackContext, playback_cache: PlaybackCache) -> Self {
        Self {
            device_information: Rc::new(DeviceInformation::new()),
            playback: FireTVPlaybackContext::new(playback_context, playback_cache),
        }
    }

    /// Creates a new [`FireTVContext`] instance with injected fields.
    ///
    /// This constructor allows you to provide specific device information and playback context
    /// to be used for `FireTV` detection and event handling.
    ///
    /// # Arguments
    ///
    /// * `device_information` - A reference-counted [`DeviceInformation`] instance
    /// * `playback` - [`FireTVPlaybackContext`] instance for handling playback events
    ///
    /// # Returns
    ///
    /// A new [`FireTVContext`] instance with the provided components.
    pub fn new_with(
        device_information: Rc<DeviceInformation>,
        playback: FireTVPlaybackContext,
    ) -> Self {
        Self {
            device_information,
            playback,
        }
    }

    /// Provides this [`FireTVContext`] to the given scope.
    ///
    /// # Arguments
    ///
    /// * `scope` - The scope to provide the context to
    ///
    /// # Returns
    ///
    /// A reference-counted [`FireTV`] trait object
    pub fn provide(self, scope: Scope) -> Rc<dyn FireTV> {
        let instance = Rc::new(self);
        provide_context::<Rc<dyn FireTV>>(scope, instance.clone());
        instance
    }
}

/// Gets or creates a `FireTV` context from the application context.
///
/// This function attempts to retrieve an existing `FireTV` context from the scope,
/// and if one doesn't exist, it creates a new one with the default device information
/// and the playback context and cache from the application context.
///
/// # Arguments
///
/// * `ctx` - The application context to get or create the `FireTVContext` from
///
/// # Returns
///
/// A reference-counted [`FireTV`] trait object
pub fn use_firetv_context(ctx: &AppContext) -> Rc<dyn FireTV> {
    let context = use_context::<Rc<dyn FireTV>>(ctx.scope());
    if let Some(firetv_context) = context {
        return firetv_context;
    }

    // If context is not found in the scope, attempt to create one
    let playback_context = use_playback(ctx.scope());
    let playback_cache = use_playback_cache(ctx);
    let firetv_context = FireTVContext::new(playback_context, playback_cache);
    firetv_context.provide(ctx.scope())
}

#[cfg(test)]
mod tests {
    use super::*;
    use firetv_playback::FireTVPlaybackContext;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::device_information::DeviceInformation;
    use playback::context::provide_playback;
    use playback_cache::PlaybackCache;
    use std::rc::Rc;

    // TODO: add tests when PlaybackCache mock is available

    #[test]
    fn is_firetv_calls_firetv_detection() {}

    mod register_playback_events {

        #[test]
        fn register_playback_events_registers_all_events_when_is_firetv() {}

        #[test]
        fn register_playback_events_does_not_register_when_not_firetv() {}
    }

    mod use_firetv_context {

        #[test]
        fn use_firetv_context_returns_existing_context() {}

        #[test]
        fn use_firetv_context_creates_new_context_if_none_exists() {}
    }

    mod propagate_transition_event {
        use super::*;
        use crate::{FireTVChannelIngressType, FireTVChannelTransitionEvent};
        use ignx_compositron::reactive::store_value;
        use rstest::*;

        #[rstest]
        #[case::channel(
            FireTVChannelIngressType::CHANNEL,
            "test-channel-id".to_string(),
            r##"{"benefitId":"test-channel-id","ingressType":"CHANNEL"}"##.to_string()
        )]
        #[case::subscription(
            FireTVChannelIngressType::SUBSCRIPTION,
            "test-subscription-id".to_string(),
            r##"{"benefitId":"test-subscription-id","ingressType":"SUBSCRIPTION"}"##.to_string()
        )]
        fn propagate_transition_event_serializes_event_correctly(
            #[case] ingress_type: FireTVChannelIngressType,
            #[case] benefit_id: String,
            #[case] expected_json: String,
        ) {
            launch_only_app_context(move |ctx| {
                let gmb_calls = store_value(ctx.scope(), ("".to_string(), "".to_string(), 0usize));
                provide_context(ctx.scope(), gmb_calls);

                let playback_context = provide_playback(&ctx);
                let playback_cache = PlaybackCache::new(&ctx);
                let firetv_playback_context =
                    FireTVPlaybackContext::new(playback_context, playback_cache);
                let device_info = DeviceInformation {
                    os_name: "FireOS".into(),
                    ..DeviceInformation::new()
                };
                let firetv_context =
                    FireTVContext::new_with(Rc::new(device_info), firetv_playback_context);

                // Test propagate_transition_event
                let event = FireTVChannelTransitionEvent::new(ingress_type, &benefit_id);
                firetv_context.propagate_transition_event(&ctx, &event);

                // Verify the GMB call was made with correct parameters
                let (event_type, content, count) = gmb_calls.get_value();
                assert_eq!(count, 1, "GMB should be called exactly once");
                assert_eq!(
                    event_type, CHANNEL_INTERACTION_MESSAGE_TYPE,
                    "Should use correct message type"
                );

                // Verify exact JSON serialization
                assert_eq!(
                    content, expected_json,
                    "JSON should be serialized correctly for channel event"
                );
            });
        }

        #[test]
        fn propagate_transition_event_handles_multiple_calls_independently() {
            launch_only_app_context(|ctx| {
                // Setup GMB tracking
                let gmb_calls = store_value(ctx.scope(), ("".to_string(), "".to_string(), 0usize));
                provide_context(ctx.scope(), gmb_calls);

                // Create FireTVContext
                let playback_context = provide_playback(&ctx);
                let playback_cache = PlaybackCache::new(&ctx);
                let firetv_context = FireTVContext::new(playback_context, playback_cache);

                // First call - Channel event
                let event1 = FireTVChannelTransitionEvent::new(
                    FireTVChannelIngressType::CHANNEL,
                    "first-channel-id",
                );
                firetv_context.propagate_transition_event(&ctx, &event1);

                // Verify first call
                let (event_type1, content1, count1) = gmb_calls.get_value();
                assert_eq!(count1, 1, "First call should increment count to 1");
                assert_eq!(
                    event_type1, CHANNEL_INTERACTION_MESSAGE_TYPE,
                    "Should use correct message type"
                );
                let expected_json1 =
                    r##"{"benefitId":"first-channel-id","ingressType":"CHANNEL"}"##;
                assert_eq!(
                    content1, expected_json1,
                    "First call should serialize correctly"
                );

                // Second call - Subscription event with different data
                let event2 = FireTVChannelTransitionEvent::new(
                    FireTVChannelIngressType::SUBSCRIPTION,
                    "second-subscription-id",
                );
                firetv_context.propagate_transition_event(&ctx, &event2);

                // Verify second call - count should increment and content should change
                let (event_type2, content2, count2) = gmb_calls.get_value();
                assert_eq!(count2, 2, "Second call should increment count to 2");
                assert_eq!(
                    event_type2, CHANNEL_INTERACTION_MESSAGE_TYPE,
                    "Should use correct message type"
                );
                let expected_json2 =
                    r##"{"benefitId":"second-subscription-id","ingressType":"SUBSCRIPTION"}"##;
                assert_eq!(
                    content2, expected_json2,
                    "Second call should serialize independently"
                );

                // Third call - Another channel event
                let event3 = FireTVChannelTransitionEvent::new(
                    FireTVChannelIngressType::CHANNEL,
                    "third-channel-id",
                );
                firetv_context.propagate_transition_event(&ctx, &event3);

                // Verify third call - should work independently without interference
                let (event_type3, content3, count3) = gmb_calls.get_value();
                assert_eq!(count3, 3, "Third call should increment count to 3");
                assert_eq!(
                    event_type3, CHANNEL_INTERACTION_MESSAGE_TYPE,
                    "Should use correct message type"
                );
                let expected_json3 =
                    r##"{"benefitId":"third-channel-id","ingressType":"CHANNEL"}"##;
                assert_eq!(
                    content3, expected_json3,
                    "Third call should serialize independently"
                );
            });
        }
    }
}
