use mockall::automock;

use ignx_compositron::prelude::AppContext;

use crate::transition_reporting::FireTVChannelTransitionEvent;
#[cfg(feature = "test_utils")]
use {
    ignx_compositron::prelude::{provide_context, Scope},
    std::rc::Rc,
};

/// Defines the core functionality for `FireTV` device detection and event handling.
///
/// This trait provides an interface to check if the current device is a `FireTV` device
/// and to register handlers for FireTV-specific events.
#[automock]
pub trait FireTV {
    /// Determines if the current device is a `FireTV` device.
    ///
    /// # Returns
    ///
    /// `true` if the device is a `FireTV` device, `false` otherwise.
    fn is_firetv(&self) -> bool;

    /// Registers event handlers for playback events on `FireTV` devices.
    ///
    /// This function sets up the necessary event listeners to handle playback-related
    /// events specific to `FireTV` platforms, including load, cache, and unload events.
    ///
    /// # Arguments
    ///
    /// * `ctx` - The application context to register the event handlers with
    fn register_playback_events(&mut self, ctx: &AppContext);

    fn propagate_transition_event<'a>(
        &self,
        ctx: &AppContext,
        event: &FireTVChannelTransitionEvent<'a>,
    );
}

impl MockFireTV {
    /// Provides a mock implementation of the `FireTV` trait to the given scope.
    ///
    /// This is useful for testing components that depend on `FireTV` detection.
    ///
    /// # Arguments
    ///
    /// * `scope` - The scope to provide the mock to
    #[cfg(feature = "test_utils")]
    pub fn provide_mock(self, scope: Scope) {
        provide_context::<Rc<dyn FireTV>>(scope, Rc::new(self));
    }
}
