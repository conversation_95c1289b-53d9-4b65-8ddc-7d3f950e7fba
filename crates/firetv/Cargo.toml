[package]
name = "firetv"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true
description = "FireTV module for PV LRC"

[dependencies]
amzn-ignx-compositron.workspace = true
firetv-detection.workspace = true
firetv-playback.workspace = true
mockall.workspace = true
playback.workspace = true
playback-cache.workspace = true
serde_json.workspace = true
serde.workspace = true

[lints]
workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["test_utils"] }
firetv-detection = { workspace = true, features = ["test_utils"] }
serial_test.workspace = true
playback-cache = { workspace = true, features = ["test_utils"] }
rstest.workspace = true
playback-machine = { workspace = true, features = ["test_utils" ] }

[features]
# Enable mock implementations for testing
test_utils = []
# Exports the FiretTVContext for initialisation 
init = []