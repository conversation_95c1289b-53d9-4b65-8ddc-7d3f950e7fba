use crate::activity_tracker_impl::{ActivityTrackerImpl, ActivityTrackerImplRc};
use ignx_compositron::context::AppContext;
use ignx_compositron::lifecycle::LifecycleState;
use ignx_compositron::time::Instant;
use location::PageType;
use std::{cell::RefCell, rc::Rc, time::Duration};

pub type ActivityTrackerRc = Rc<RefCell<ActivityTracker>>;

pub struct ActivityTracker {
    tracker: ActivityTrackerImplRc,
}

pub(crate) type ActivityTrackerEventCallback = Rc<dyn Fn() + 'static>;

pub struct ActivityTrackerEvent {
    /// If passive, then the event firing should not be counted as activity and the timer will continue.
    pub passive: bool,
    /// The callback to fire when the event is succeeded.
    pub callback: ActivityTrackerEventCallback,
    /// The duration that should occur since the last activity for the event to fire.
    pub delay: Duration,
    /// Should start again after executing.
    pub should_repeat: bool,
}

pub struct ActivityTrackerBackgroundEvent {
    /// The callback to fire when the event is succeeded.
    pub callback: ActivityTrackerEventCallback,
    /// The duration that have to pass after going to Background for event to fire
    pub threshold: Duration,
    /// Should start again after executing.
    pub should_repeat: bool,
}

#[derive(Clone)]
pub enum ActivityTypes<'a> {
    PageChange(&'a PageType),
    ButtonPress,
    Playback(bool),
    LifecycleStateChange(LifecycleState),
}

impl ActivityTracker {
    pub fn new(ctx: AppContext) -> ActivityTrackerRc {
        let instance = Self {
            tracker: ActivityTrackerImpl::new(ctx),
        };

        Rc::new(RefCell::new(instance))
    }

    /// Call this to register an event, with a callback to be executed after `delay` time inactive.
    ///
    /// When multiple events are registered, they are queued up by order of the shortest amount of
    /// `delay`. If multiple events with the same `delay` are registered, they are queued up by
    /// reverse insertion order; the most recent event to be queued with the same `delay` will be
    /// the first one whose callback is executed.
    ///
    /// Once one event is executed, it will repeat (if `should_repeat` is true) or the next event
    /// (if any are registered) in the queue will be scheduled.
    pub fn register_event(&self, event: ActivityTrackerEvent) {
        ActivityTrackerImpl::register_event(&self.tracker, event);
    }

    /// Call this to register a background event, with a callback to be executed if the time spent
    /// in the background is more than `threshold`
    ///
    /// Once the event is executed, it will repeat only if `should_repeat` is set to true
    pub fn register_background_event(&self, event: ActivityTrackerBackgroundEvent) {
        ActivityTrackerImpl::register_background_event(&self.tracker, event);
    }

    /// Handles activity from the application. Call this function when something that should be
    /// considered "activity" in the application occurs.
    ///
    /// If an activity occurs while the WASM VM is not active, this should do nothing.
    /// If an activity occurs while playback is ongoing, this should do nothing.
    ///
    /// On switching away from the WASM VM or on playback starting, this should cancel the active
    /// timer and wait until back in WASM/no longer playing back to restart the timer.
    ///
    /// Otherwise, this should set the last activity to the current time, and restart the timer.
    pub fn on_activity(&self, activity_type: ActivityTypes<'_>) {
        ActivityTrackerImpl::on_activity(&self.tracker, activity_type);
    }

    /// Returns the last activity instant
    pub fn get_last_activity_instant(&self) -> Instant {
        ActivityTrackerImpl::get_last_activity_instant(&self.tracker)
    }
}

#[cfg(test)]
pub mod test {
    use ignx_compositron::app::launch_only_app_context;

    use super::ActivityTracker;

    #[test]
    fn should_create_instance() {
        launch_only_app_context(|ctx| {
            ActivityTracker::new(ctx);
        });
    }
}
