use std::{cell::RefCell, rc::Rc};

use ignx_compositron::lifecycle::{LifecycleState, LifecycleStateContext};
use ignx_compositron::prelude::{create_effect, use_context};
use ignx_compositron::reactive::SignalGet;
use ignx_compositron::{context::AppContext, reactive::on_cleanup, task::TaskID, time::Instant};
use location::PageType;

use crate::activity_tracker::{
    ActivityTrackerBackgroundEvent, ActivityTrackerEvent, ActivityTypes,
};

// Holds some additional fields that should not be publically accessible.
struct ActivityTrackerEventWrapper {
    pub event: ActivityTrackerEvent,

    pub last_fire_time: Instant,
}

impl ActivityTrackerEventWrapper {
    pub fn get_next_fire_time(&self, last_activity: &Instant) -> Instant {
        let starting_time = if last_activity < &self.last_fire_time {
            &self.last_fire_time
        } else {
            last_activity
        };
        *starting_time + self.event.delay
    }
}

pub struct ActivityTrackerImpl {
    pub context: AppContext,
    events: Vec<ActivityTrackerEventWrapper>,
    background_events: Vec<ActivityTrackerBackgroundEvent>,
    pub timer: Option<TaskID>,
    pub last_activity: Instant,
    pub time_going_to_background: Option<Instant>,
    in_other_vm: bool,
    in_playback: bool,
}

pub type ActivityTrackerImplRc = Rc<RefCell<ActivityTrackerImpl>>;

impl ActivityTrackerImpl {
    pub fn new(ctx: AppContext) -> ActivityTrackerImplRc {
        let instance: ActivityTrackerImpl = Self {
            context: ctx.clone(),
            events: vec![],
            background_events: vec![],
            timer: None,
            last_activity: Instant::now(),
            time_going_to_background: None,
            in_other_vm: false,
            in_playback: false,
        };

        let instance_rc = Rc::new(RefCell::new(instance));

        on_cleanup(ctx.scope(), {
            let instance_rc = instance_rc.clone();
            move || {
                ActivityTrackerImpl::cancel_timer(&instance_rc);
            }
        });

        let lifecycle_state = use_context::<LifecycleStateContext>(ctx.scope());

        create_effect(ctx.scope(), {
            let activity_tracker = instance_rc.clone();

            move |_| {
                if let Some(state) = lifecycle_state
                    .as_ref()
                    .and_then(|lifecycle_state| lifecycle_state.0.try_get())
                {
                    match state {
                        LifecycleState::Background | LifecycleState::Foreground => {
                            ActivityTrackerImpl::on_activity(
                                &activity_tracker,
                                ActivityTypes::LifecycleStateChange(state),
                            )
                        }

                        _ => {}
                    }
                }
            }
        });

        instance_rc
    }

    pub fn register_event(me: &ActivityTrackerImplRc, event: ActivityTrackerEvent) {
        let event = ActivityTrackerEventWrapper {
            event,
            last_fire_time: me.borrow().last_activity,
        };

        ActivityTrackerImpl::insert_event(me, event);
    }

    pub fn register_background_event(
        me: &ActivityTrackerImplRc,

        event: ActivityTrackerBackgroundEvent,
    ) {
        ActivityTrackerImpl::add_background_event(me, event);
    }

    fn add_background_event(me: &ActivityTrackerImplRc, event: ActivityTrackerBackgroundEvent) {
        me.borrow_mut().background_events.push(event);
    }

    fn record_going_to_background(me: &ActivityTrackerImplRc) {
        me.borrow_mut().time_going_to_background = Some(Instant::now());
    }

    fn handle_going_to_foreground(me: &ActivityTrackerImplRc) {
        let mut callbacks = vec![];

        {
            let mut borrowed_me = me.borrow_mut();

            if let Some(time_going_to_background) = borrowed_me.time_going_to_background {
                let time_in_background = Instant::now().duration_since(time_going_to_background);

                borrowed_me.background_events.retain(|event| {
                    if time_in_background > event.threshold {
                        // In case the callback triggers another `on_activity` call, we can potentially crash the app,

                        // since at this point we still hold borrow_mut on `me`.

                        // Any borrow or borrow_mut call during that time will lead to panic,

                        // so we should avoid any additional code execution until we release the borrowed value.

                        callbacks.push(event.callback.clone());

                        event.should_repeat
                    } else {
                        true
                    }
                });
            }

            borrowed_me.time_going_to_background = None;
        }

        for callback in callbacks {
            callback();
        }
    }

    fn insert_event(me: &ActivityTrackerImplRc, event: ActivityTrackerEventWrapper) {
        let index_to_insert = {
            let mut borrowed_me = me.borrow_mut();
            let events = &borrowed_me.events;

            let last_activity = &borrowed_me.last_activity;
            let next_fire_time = event.get_next_fire_time(last_activity);

            let mut index_to_insert = 0;
            while index_to_insert < events.len()
                && events
                    .get(index_to_insert)
                    .is_some_and(|event| event.get_next_fire_time(last_activity) < next_fire_time)
            {
                index_to_insert += 1;
            }

            borrowed_me.events.insert(index_to_insert, event);
            index_to_insert
        };

        if index_to_insert == 0 {
            ActivityTrackerImpl::schedule_timer(me);
        }
    }

    /// See [`crate::activity_tracker::ActivityTracker::on_activity`]
    pub fn on_activity(me: &ActivityTrackerImplRc, activity_type: ActivityTypes<'_>) {
        let should_reset = match activity_type {
            ActivityTypes::PageChange(to_page) => match to_page {
                PageType::Js(_) => {
                    if !me.borrow_mut().in_other_vm {
                        me.borrow_mut().in_other_vm = true;
                        ActivityTrackerImpl::cancel_timer(me);
                    }
                    false
                }
                PageType::Rust(_) => {
                    if me.borrow_mut().in_other_vm {
                        me.borrow_mut().in_other_vm = false;
                    }
                    true
                }
            },
            ActivityTypes::ButtonPress => {
                !(me.borrow_mut().in_other_vm || me.borrow_mut().in_playback)
            }
            ActivityTypes::Playback(in_playback) => {
                if in_playback {
                    if !me.borrow_mut().in_playback {
                        me.borrow_mut().in_playback = true;
                        ActivityTrackerImpl::cancel_timer(me);
                    }
                    false
                } else {
                    if me.borrow_mut().in_playback {
                        me.borrow_mut().in_playback = false;
                    }
                    true
                }
            }
            ActivityTypes::LifecycleStateChange(state) => {
                if state == LifecycleState::Foreground {
                    ActivityTrackerImpl::handle_going_to_foreground(me);
                } else if state == LifecycleState::Background {
                    ActivityTrackerImpl::record_going_to_background(me);
                }

                false
            }
        };
        if should_reset {
            me.borrow_mut().last_activity = Instant::now();
            ActivityTrackerImpl::schedule_timer(me);
        }
    }

    /// See [`crate::activity_tracker::ActivityTracker::get_last_activity_instant`]
    pub fn get_last_activity_instant(me: &ActivityTrackerImplRc) -> Instant {
        me.borrow().last_activity
    }

    fn cancel_timer(me: &ActivityTrackerImplRc) {
        let mut me_borrowed = me.borrow_mut();

        if let Some(timer) = me_borrowed.timer {
            me_borrowed.context.cancel_task(timer);
            me_borrowed.timer = None;
        }
    }

    fn schedule_timer(me: &ActivityTrackerImplRc) {
        // Don't allow scheduling a timer if we are in the other VM as it means it will trigger
        // on re-entry to the Rust VM.
        if me.borrow_mut().in_other_vm {
            return;
        }

        ActivityTrackerImpl::cancel_timer(me);

        let me_cloned = me.clone();
        let mut me_borrowed = me.borrow_mut();

        if let Some(next_event) = me_borrowed.events.first() {
            let timer = me_borrowed.context.schedule_task(
                next_event.get_next_fire_time(&me_borrowed.last_activity),
                {
                    let me = me_cloned;

                    move || {
                        if !me.borrow_mut().events.is_empty() {
                            let next_event = me.borrow_mut().events.remove(0).event;
                            let callback = &next_event.callback;
                            callback();

                            if !next_event.passive {
                                me.borrow_mut().last_activity = Instant::now();
                            }

                            if next_event.should_repeat {
                                ActivityTrackerImpl::insert_event(
                                    &me,
                                    ActivityTrackerEventWrapper {
                                        event: next_event,
                                        last_fire_time: Instant::now(),
                                    },
                                );
                            }
                        }

                        ActivityTrackerImpl::schedule_timer(&me);
                    }
                },
            );

            me_borrowed.timer = Some(timer);
        }
    }
}

#[cfg(test)]
pub mod test {
    use super::*;
    use ignx_compositron::compose;
    use ignx_compositron::prelude::create_rw_signal;
    use ignx_compositron::reactive::SignalSet;
    use ignx_compositron::test_utils::TestRendererGameLoop;
    use ignx_compositron::{
        app::{launch_only_app_context, launch_test},
        reactive::{provide_context, use_context},
        rectangle::*,
        time::MockClock,
    };
    use location::{JSPage, RustPage};
    use rstest::rstest;
    use std::time::Duration;

    fn mock_activity() -> ActivityTypes<'static> {
        ActivityTypes::ButtonPress
    }

    fn mock_callback() -> (Rc<RefCell<i32>>, Rc<dyn Fn()>) {
        let times_called = Rc::new(RefCell::new(0));
        let callback = {
            let times_called = times_called.clone();
            Rc::new(move || {
                times_called.replace_with(|&mut old| old + 1);
            })
        };
        (times_called, callback)
    }

    fn setup(ctx: &AppContext) {
        let instance = ActivityTrackerImpl::new(ctx.clone());
        provide_context(ctx.scope(), instance);
    }

    fn move_forward_secs(secs: usize, game_loop: &mut TestRendererGameLoop) {
        MockClock::advance(Duration::from_secs(secs as u64));
        game_loop.tick_until_done();
    }

    fn trigger_activity(
        tracker: &ActivityTrackerImplRc,
        activity: ActivityTypes<'_>,
        game_loop: &mut TestRendererGameLoop,
    ) {
        ActivityTrackerImpl::on_activity(tracker, activity);
        game_loop.tick_until_done();
    }

    fn move_forward_secs_and_assert_called_times(
        secs: usize,
        game_loop: &mut TestRendererGameLoop,
        times_called_and_expected: Vec<(&Rc<RefCell<i32>>, isize)>,
    ) {
        move_forward_secs(secs, game_loop);
        for (times_called, expected) in times_called_and_expected {
            assert_eq!(*times_called.borrow(), expected as i32);
        }
    }

    fn move_forward_secs_in_background_and_assert_called_times(
        tracker: &ActivityTrackerImplRc,

        secs: usize,

        game_loop: &mut TestRendererGameLoop,

        times_called_and_expected: Vec<(&Rc<RefCell<i32>>, isize)>,
    ) {
        ActivityTrackerImpl::on_activity(
            &tracker,
            ActivityTypes::LifecycleStateChange(LifecycleState::Background),
        );

        move_forward_secs(secs, game_loop);

        ActivityTrackerImpl::on_activity(
            &tracker,
            ActivityTypes::LifecycleStateChange(LifecycleState::Foreground),
        );

        for (times_called, expected) in times_called_and_expected {
            assert_eq!(*times_called.borrow(), expected as i32);
        }
    }

    #[test]
    pub fn should_create_instance() {
        launch_only_app_context(|ctx| {
            ActivityTrackerImpl::new(ctx);
        });
    }

    #[test]
    #[test]
    pub fn should_subscribe_to_lifecycle_events() {
        launch_only_app_context(|ctx| {
            let app_state = create_rw_signal(ctx.scope(), LifecycleState::Undefined);

            provide_context(ctx.scope(), LifecycleStateContext(app_state.read_only()));

            let instance = ActivityTrackerImpl::new(ctx.clone());

            app_state.set(LifecycleState::Background);

            assert_ne!(instance.borrow().time_going_to_background, None);

            app_state.set(LifecycleState::Foreground);

            assert_eq!(instance.borrow().time_going_to_background, None);
        })
    }

    #[test]
    pub fn should_not_schedule_any_timers_after_switching_to_js_vm() {
        launch_only_app_context(|ctx| {
            setup(&ctx);
            let tracker = use_context::<ActivityTrackerImplRc>(ctx.scope()).unwrap();

            ActivityTrackerImpl::on_activity(&tracker, ActivityTypes::ButtonPress);

            // Change VM
            ActivityTrackerImpl::on_activity(
                &tracker,
                ActivityTypes::PageChange(&PageType::Js(JSPage::COLLECTION_PAGE)),
            );
            assert_eq!(tracker.borrow().in_other_vm, true);
            assert_eq!(tracker.borrow().timer, None);

            ActivityTrackerImpl::on_activity(&tracker, ActivityTypes::Playback(false));
            assert_eq!(tracker.borrow_mut().in_other_vm, true);
            assert_eq!(tracker.borrow_mut().timer, None);
        })
    }

    #[test]

    pub fn should_register_and_fire_background_event_after_lifecycle_state_change() {
        launch_test(
            |ctx| {
                setup(&ctx);

                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();

                let (times_called, callback) = mock_callback();

                ActivityTrackerImpl::register_background_event(
                    &tracker,
                    ActivityTrackerBackgroundEvent {
                        callback,

                        should_repeat: false,

                        threshold: Duration::from_secs(10),
                    },
                );

                // Don't call

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    9,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                // Don't call as well, since the time is counted from going to background

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    1,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    11,
                    &mut game_loop,
                    vec![(&times_called, 1)],
                );

                // didn't re-register

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    11,
                    &mut game_loop,
                    vec![(&times_called, 1)],
                );
            },
        )
    }

    #[test]

    pub fn should_handle_multiple_background_events() {
        launch_test(
            |ctx| {
                setup(&ctx);

                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();

                let (times_called_1, callback_1) = mock_callback();

                let (times_called_2, callback_2) = mock_callback();

                ActivityTrackerImpl::register_background_event(
                    &tracker,
                    ActivityTrackerBackgroundEvent {
                        callback: callback_1,

                        should_repeat: false,

                        threshold: Duration::from_secs(10),
                    },
                );

                ActivityTrackerImpl::register_background_event(
                    &tracker,
                    ActivityTrackerBackgroundEvent {
                        callback: callback_2,

                        should_repeat: false,

                        threshold: Duration::from_secs(15),
                    },
                );

                // Don't call any

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    9,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 0)],
                );

                // Call only the first one

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    11,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 0)],
                );

                // Call the second one, the first one is not re-registered

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    16,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 1)],
                );
            },
        )
    }

    #[test]

    pub fn should_handle_repeating_background_events() {
        launch_test(
            |ctx| {
                setup(&ctx);

                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();

                let (times_called_1, callback_1) = mock_callback();

                let (times_called_2, callback_2) = mock_callback();

                ActivityTrackerImpl::register_background_event(
                    &tracker,
                    ActivityTrackerBackgroundEvent {
                        callback: callback_1,

                        should_repeat: false,

                        threshold: Duration::from_secs(10),
                    },
                );

                ActivityTrackerImpl::register_background_event(
                    &tracker,
                    ActivityTrackerBackgroundEvent {
                        callback: callback_2,

                        should_repeat: true,

                        threshold: Duration::from_secs(10),
                    },
                );

                // Don't call any

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    9,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 0)],
                );

                // Call both

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    11,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 1)],
                );

                // The second one is called again

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    11,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 2)],
                );

                // The second one is once more

                move_forward_secs_in_background_and_assert_called_times(
                    &tracker,
                    11,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 3)],
                );
            },
        )
    }

    #[test]
    pub fn should_register_and_fire_event_after_delay() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called, callback) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called, 1)],
                );

                // didn't re-register.
                move_forward_secs_and_assert_called_times(
                    10,
                    &mut game_loop,
                    vec![(&times_called, 1)],
                );
            },
        );
    }

    #[test]
    pub fn should_reset_timer_after_activity() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called, callback) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                trigger_activity(&tracker, mock_activity(), &mut game_loop);

                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called, 1)],
                );
            },
        );
    }

    #[rstest]
    #[case::while_in_other_vm(ActivityTypes::PageChange(&PageType::Js(JSPage::COLLECTION_PAGE)), ActivityTypes::PageChange(&PageType::Rust(RustPage::RUST_COLLECTIONS)))]
    #[case::while_in_playback(ActivityTypes::Playback(true), ActivityTypes::Playback(false))]
    pub fn should_pause_timer_and_then_restart(
        #[case] activity_to_pause: ActivityTypes<'static>,
        #[case] activity_to_restart: ActivityTypes<'static>,
    ) {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let intermediate_activity = ActivityTypes::ButtonPress;

                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called, callback) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                // timer should clear
                trigger_activity(&tracker, activity_to_pause.clone(), &mut game_loop);
                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                // if it didn't clear, it would be called after this
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                // timer should not restart
                trigger_activity(&tracker, intermediate_activity, &mut game_loop);
                move_forward_secs_and_assert_called_times(
                    10,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                // timer should not restart
                trigger_activity(&tracker, activity_to_pause, &mut game_loop);
                move_forward_secs_and_assert_called_times(
                    10,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                // timer should restart
                trigger_activity(&tracker, activity_to_restart, &mut game_loop);

                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );

                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called, 1)],
                );
            },
        );
    }

    #[test]
    pub fn should_cancel_timer_after_scope_disposal() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called, callback) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback,
                    },
                );

                move_forward_secs(9, &mut game_loop);

                scope.dispose();
                game_loop.tick_until_done();

                // never fired.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called, 0)],
                );
            },
        );
    }

    #[test]
    pub fn should_handle_multiple_events_being_registered_at_varying_times() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called_1, callback_1) = mock_callback();
                let (times_called_2, callback_2) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback: callback_1,
                    },
                );

                move_forward_secs(2, &mut game_loop);

                // register second event after 2 seconds.
                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(5),
                        passive: false,
                        should_repeat: false,
                        callback: callback_2,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    2,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 0)],
                );

                // 2nd fired after 5 seconds of activity.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                // 1st will reset so won't fire again after 9 seconds.
                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                // 1st fired and 2nd didn't fire again.
                move_forward_secs_and_assert_called_times(
                    9,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 1)],
                );
            },
        );
    }

    #[test]
    pub fn should_handle_passive_events() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called_1, callback_1) = mock_callback();
                let (times_called_2, callback_2) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback: callback_1,
                    },
                );

                move_forward_secs(2, &mut game_loop);

                // register second event after 2 seconds.
                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(5),
                        passive: true,
                        should_repeat: false,
                        callback: callback_2,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    2,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 0)],
                );

                // 2nd fired after 5 seconds of inactivity.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                move_forward_secs_and_assert_called_times(
                    4,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                // 1st fired after 4 seconds due to 2nd event being passive.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 1)],
                );
            },
        );
    }

    #[test]
    pub fn should_handle_repeating_non_passive_events() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called_1, callback_1) = mock_callback();
                let (times_called_2, callback_2) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback: callback_1,
                    },
                );

                // 2nd event will fire every 3 seconds and is not passive, preventing the 1st from ever firing.
                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(3),
                        passive: false,
                        should_repeat: true,
                        callback: callback_2,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    2,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 0)],
                );

                // 2nd fired after 3 seconds of inactivity.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                move_forward_secs_and_assert_called_times(
                    2,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                // Fired again.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 2)],
                );

                // Fired again.
                move_forward_secs_and_assert_called_times(
                    3,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 3)],
                );

                // One more time, the first never fires.
                move_forward_secs_and_assert_called_times(
                    3,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 4)],
                );
            },
        );
    }

    #[test]
    pub fn should_handle_repeating_events() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called_1, callback_1) = mock_callback();
                let (times_called_2, callback_2) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: false,
                        should_repeat: false,
                        callback: callback_1,
                    },
                );

                // 2nd event will fire every 3 seconds but is passive so the 1st will still fire.
                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(3),
                        passive: true,
                        should_repeat: true,
                        callback: callback_2,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    2,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 0)],
                );

                // 2nd fired after 3 seconds of inactivity.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                move_forward_secs_and_assert_called_times(
                    2,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 1)],
                );

                // Fired again.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 2)],
                );

                // Fired again.
                move_forward_secs_and_assert_called_times(
                    3,
                    &mut game_loop,
                    vec![(&times_called_1, 0), (&times_called_2, 3)],
                );

                // After 1 more second, the first fired, resetting the timer on the 2nd so it needs another 3 seconds to complete.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 3)],
                );

                move_forward_secs_and_assert_called_times(
                    2,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 3)],
                );

                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![(&times_called_1, 1), (&times_called_2, 4)],
                );
            },
        );
    }

    #[test]
    pub fn should_insert_new_events_at_the_correct_position() {
        launch_test(
            |ctx| {
                setup(&ctx);
                compose! { Rectangle() }
            },
            |scope, mut game_loop| {
                let tracker = use_context::<ActivityTrackerImplRc>(scope).unwrap();
                let (times_called_1, callback_1) = mock_callback();
                let (times_called_2, callback_2) = mock_callback();
                let (times_called_3, callback_3) = mock_callback();

                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(10),
                        passive: true,
                        should_repeat: false,
                        callback: callback_1,
                    },
                );

                move_forward_secs(3, &mut game_loop);

                // register second event after 3 seconds - will be in 2nd position.
                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(12),
                        passive: true,
                        should_repeat: false,
                        callback: callback_2,
                    },
                );

                move_forward_secs(3, &mut game_loop);

                // register third event after another 3 seconds - will be in 2nd position.
                ActivityTrackerImpl::register_event(
                    &tracker,
                    ActivityTrackerEvent {
                        delay: Duration::from_secs(11),
                        passive: true,
                        should_repeat: false,
                        callback: callback_3,
                    },
                );

                move_forward_secs_and_assert_called_times(
                    3,
                    &mut game_loop,
                    vec![
                        (&times_called_1, 0),
                        (&times_called_2, 0),
                        (&times_called_3, 0),
                    ],
                );

                // 1st fired.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![
                        (&times_called_1, 1),
                        (&times_called_2, 0),
                        (&times_called_3, 0),
                    ],
                );

                // 3rd fired.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![
                        (&times_called_1, 1),
                        (&times_called_2, 0),
                        (&times_called_3, 1),
                    ],
                );

                // 2nd fired.
                move_forward_secs_and_assert_called_times(
                    1,
                    &mut game_loop,
                    vec![
                        (&times_called_1, 1),
                        (&times_called_2, 1),
                        (&times_called_3, 1),
                    ],
                );
            },
        );
    }
}
