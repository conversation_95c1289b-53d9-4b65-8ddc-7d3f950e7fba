[package]
name = "activity-tracker"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
location.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
rstest.workspace = true

[lints]
workspace = true
