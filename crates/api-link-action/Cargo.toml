[package]
name = "api-link-action"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
app-config.workspace = true
cfg-test-attr-derive.workspace = true
checkout.workspace = true
common-transform-types.workspace = true
fableous.workspace = true
log.workspace = true
network.workspace = true
network-parser.workspace = true
network-parser-derive.workspace = true
router.workspace = true
serde.workspace = true
serde_json.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
common-transform-types = { workspace = true, features = ["test_utils"] }
mockall.workspace = true
rstest.workspace = true
serial_test.workspace = true

[lints]
workspace = true
