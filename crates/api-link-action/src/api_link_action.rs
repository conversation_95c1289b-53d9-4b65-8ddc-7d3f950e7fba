use common_transform_types::api_link_action::{ApiLinkAction, ApiLinkResponseStatusMessage};
use fableous::toasts::toast_context::ToastContext;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::{metric, use_context, Scope, SignalSet};
use ignx_compositron::text::{LocalizedText, TextContent};
#[cfg(not(test))]
use network::NetworkClient;
use network::RequestError;
use std::i32;
use std::rc::Rc;

#[cfg(test)]
use crate::network::MockNetworkClient as NetworkClient;
use crate::network::{ApiLinkHttpRequest, ApiLinkHttpRequestParams, ApiLinkHttpResponse};

// TODO: Update with localized LMS strings.
const DEFAULT_FAILURE_MESSAGE_ID: &str = "pv-api-link-fallback-failure-message";
const DEFAULT_SUCCESS_MESSAGE_ID: &str = "pv-api-link-fallback-success-message";

const API_LINK_ACTION_FAILURE_METRIC: &str = "Rust.ApiLinkAction.Failure";
const API_LINK_ACTION_SUCCESS_METRIC: &str = "Rust.ApiLinkAction.Success";

/// # Arguments
///
/// * `ctx` - Application context containing network client and scope information
/// * `on_finally` - Callback function to execute after the activation attempt completes
/// * `api_link_action` - Contains API link action request parameters and metadata
///
/// # Effects
///
/// - Makes network request to the target API endpoint
/// - Shows success/failure toast messages
/// - Executes provided callback when complete
pub fn process_api_link_action(
    ctx: &AppContext,
    on_finally: Rc<dyn Fn()>,
    api_link_action: ApiLinkAction,
) {
    let client = NetworkClient::new(ctx);
    let scope = ctx.scope();

    let response_status_messages = api_link_action.metadata.responseStatusMessages.clone();
    let success_callback = {
        let on_finally: Rc<dyn Fn()> = on_finally.clone();
        move |response: ApiLinkHttpResponse| {
            on_finally();
            log::info!("[API_LINK::process_api_link_action] API link network call succeded.");
            metric!(API_LINK_ACTION_SUCCESS_METRIC, 1);

            let body = response.response_body;
            let code = response.response_status_code;
            if let Some(response_status_msg) =
                get_matching_response_message(response_status_messages, body, code)
            {
                set_toast_context_from_text_message(scope, response_status_msg.message);
            } else {
                set_toast_context_from_string_id(scope, DEFAULT_SUCCESS_MESSAGE_ID.to_string());
            }
        }
    };

    let response_status_messages = api_link_action.metadata.responseStatusMessages.clone();
    let failure_callback = {
        move |err: RequestError| {
            on_finally();
            log::error!(
                "[API_LINK::process_api_link_action] API link network call failed. {:?}",
                err
            );
            metric!(API_LINK_ACTION_FAILURE_METRIC, 1);

            match err {
                RequestError::Http {
                    code,
                    body,
                    headers: _,
                } => {
                    if let Some(response_status_msg) =
                        get_matching_response_message(response_status_messages, body, code)
                    {
                        set_toast_context_from_text_message(scope, response_status_msg.message);
                    } else {
                        set_toast_context_from_string_id(
                            scope,
                            DEFAULT_FAILURE_MESSAGE_ID.to_string(),
                        );
                    }
                }
                _ => {
                    set_toast_context_from_string_id(scope, DEFAULT_FAILURE_MESSAGE_ID.to_string());
                }
            }
        }
    };

    let request = ApiLinkHttpRequestParams::new(&api_link_action);
    client.call_http_endpoint(request, success_callback, failure_callback);
}

fn set_toast_context_from_text_content(scope: Scope, content: TextContent) {
    match use_context::<ToastContext>(scope) {
        Some(toast_context) => {
            toast_context.message.set(Some(content));
            toast_context.visible.set(true);
        }
        None => log::error!(
            "[API_LINK::process_api_link_action] ToastContext not found in scope: {:?}",
            scope
        ),
    }
}

fn set_toast_context_from_text_message(scope: Scope, message: String) {
    set_toast_context_from_text_content(scope, TextContent::String(message));
}

fn set_toast_context_from_string_id(scope: Scope, message_id: String) {
    set_toast_context_from_text_content(
        scope,
        TextContent::LocalizedText(LocalizedText::new(message_id)),
    );
}

// Return matching ApiLinkResponseStatusMessage with the highest priority.
// If multiple messages are matched with the same priority, one will be picked in a non-deterministic way.
fn get_matching_response_message(
    response_status_messages: Vec<ApiLinkResponseStatusMessage>,
    response_body: Option<String>,
    response_status_code: i32,
) -> Option<ApiLinkResponseStatusMessage> {
    response_status_messages
        .iter()
        .filter(|response_status_msg| {
            response_status_msg
                .targetCriteria
                .matches(response_body.clone(), response_status_code)
        })
        .max_by_key(|message| message.priority)
        .cloned()
}

#[cfg(test)]
pub mod test {
    use super::*;
    use crate::network::MockNetworkClient;
    use crate::network::__mock_MockNetworkClient::__new::Context;
    use ignx_compositron::prelude::{use_context, SignalGet};
    use ignx_compositron::{
        app::launch_only_app_context, context::AppContext, reactive::provide_context,
    };
    use network::RequestError;
    use serial_test::serial;
    use std::cell::RefCell;
    use std::sync::Arc;

    fn mock_context(ctx: &AppContext) {
        provide_context::<ToastContext>(ctx.scope(), ToastContext::new(ctx.scope()));
    }

    fn mock_on_finally() -> (Rc<RefCell<i32>>, Rc<dyn Fn()>) {
        let times_called = Rc::new(RefCell::new(0));
        let on_finally: Rc<dyn Fn()> = {
            let times_called = times_called.clone();
            Rc::new(move || {
                times_called.replace_with(|&mut old| old + 1);
            }) as Rc<dyn Fn()>
        };
        (times_called, on_finally)
    }

    fn mock_network_client(response: ApiLinkHttpResponse) -> Context {
        let response = Arc::new(response);
        let mock_client_context: Context = MockNetworkClient::new_context();
        mock_client_context.expect().returning(move |_| {
            let mut mock_client = MockNetworkClient::default();
            let response = Arc::clone(&response);
            mock_client
                .expect_call_http_endpoint()
                .times(1)
                .returning(move |_, success_cb, _| success_cb((*response).clone()));
            mock_client
        });
        mock_client_context
    }

    #[test]
    #[serial]
    fn should_show_success_message_when_api_link_request_succeded() {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let mock_response = ApiLinkHttpResponse {
                response_body: None,
                response_status_code: 201,
            };
            let _mock_client = mock_network_client(mock_response);

            // Act
            let api_link_action = ApiLinkAction::create_with_required_only_params();
            process_api_link_action(&ctx, on_finally, api_link_action);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();

            let expected_message = TextContent::String(String::from("successMessage"));

            assert_eq!(*times_called.borrow(), 1);
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }

    #[test]
    #[serial]
    fn should_show_success_fallback_message_when_api_link_request_succeded_with_no_matching_response_message(
    ) {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let mock_response = ApiLinkHttpResponse {
                response_body: None,
                response_status_code: 100,
            };
            let _mock_client = mock_network_client(mock_response);

            // Act
            let api_link_action = ApiLinkAction::create_with_required_only_params();
            process_api_link_action(&ctx, on_finally, api_link_action);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();

            let expected_message = TextContent::LocalizedText(LocalizedText::new(
                DEFAULT_SUCCESS_MESSAGE_ID.to_string(),
            ));

            assert_eq!(*times_called.borrow(), 1);
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }

    #[test]
    #[serial]
    fn should_show_failure_message_when_api_link_request_failed() {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let mock_client_context: Context = MockNetworkClient::new_context();
            mock_client_context.expect().returning(move |_| {
                let mut mock_client = MockNetworkClient::default();
                mock_client.expect_call_http_endpoint().times(1).returning(
                    |_, _, failure_cb: Box<dyn FnOnce(RequestError) + 'static>| {
                        failure_cb(RequestError::Http {
                            code: 400,
                            body: None,
                            headers: vec![],
                        })
                    },
                );
                mock_client
            });

            // Act
            let api_link_action = ApiLinkAction::create_with_required_only_params();
            process_api_link_action(&ctx, on_finally, api_link_action);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();

            let expected_message = TextContent::String(String::from("nonRetryableErrorMessage"));

            assert_eq!(*times_called.borrow(), 1);
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }

    #[test]
    #[serial]
    fn should_show_highest_priority_failure_message_when_api_link_request_failed() {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let mock_client_context: Context = MockNetworkClient::new_context();
            mock_client_context.expect().returning(move |_| {
                let mut mock_client = MockNetworkClient::default();
                mock_client.expect_call_http_endpoint().times(1).returning(
                    |_, _, failure_cb: Box<dyn FnOnce(RequestError) + 'static>| {
                        failure_cb(RequestError::Http {
                            code: 429,
                            body: None,
                            headers: vec![],
                        })
                    },
                );
                mock_client
            });

            // Act
            let api_link_action = ApiLinkAction::create_with_required_only_params();
            process_api_link_action(&ctx, on_finally, api_link_action);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();

            let expected_message = TextContent::String(String::from("retryableErrorMessage"));

            assert_eq!(*times_called.borrow(), 1);
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }

    #[test]
    #[serial]
    fn should_show_failure_fallback_message_when_api_link_request_failed_with_no_matching_response_message(
    ) {
        let (times_called, on_finally) = mock_on_finally();
        launch_only_app_context(move |ctx| {
            // Setup
            mock_context(&ctx);
            let mock_client_context: Context = MockNetworkClient::new_context();
            mock_client_context.expect().returning(move |_| {
                let mut mock_client = MockNetworkClient::default();
                mock_client.expect_call_http_endpoint().times(1).returning(
                    |_, _, failure_cb: Box<dyn FnOnce(RequestError) + 'static>| {
                        failure_cb(RequestError::Http {
                            code: 399,
                            body: None,
                            headers: vec![],
                        })
                    },
                );
                mock_client
            });

            // Act
            let api_link_action = ApiLinkAction::create_with_required_only_params();
            process_api_link_action(&ctx, on_finally, api_link_action);

            // Assertions
            let visible = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .visible
                .get();
            let message = use_context::<ToastContext>(ctx.scope())
                .unwrap()
                .message
                .get();

            let expected_message = TextContent::LocalizedText(LocalizedText::new(
                DEFAULT_FAILURE_MESSAGE_ID.to_string(),
            ));

            assert_eq!(*times_called.borrow(), 1);
            assert_eq!(visible, true);
            assert_eq!(message.unwrap(), expected_message);
        })
    }
}
