use app_config::AppConfigContext;
use common_transform_types::api_link_action::ApiLinkAction;
use ignx_compositron::{
    device_information::DeviceInformation, network::http::HttpMethod, prelude::AppContext,
};
#[cfg(test)]
use mockall::mock;
use network::{
    common::{DeviceProxyResponse, LRCEdgeResponse},
    NetworkClient, RequestError, URLBuilder, URLBuilderError, URLBuilderResult,
};
use std::{collections::HashMap, rc::Rc, time::Duration};

pub trait ApiLinkHttpRequest {
    fn call_http_endpoint<S, F>(
        &self,
        request_params: ApiLinkHttpRequestParams,
        success_callback: S,
        failure_callback: F,
    ) where
        S: FnOnce(ApiLinkHttpResponse) + 'static,
        F: FnOnce(RequestError) + 'static;
}

impl ApiLinkHttpRequest for NetworkClient {
    fn call_http_endpoint<S, F>(
        &self,
        request_params: ApiLinkHttpRequestParams,
        success_callback: S,
        failure_callback: F,
    ) where
        S: FnOnce(ApiLinkHttpResponse) + 'static,
        F: FnOnce(RequestError) + 'static,
    {
        let url = match generate_api_link_request_url(
            self.device_info.clone(),
            self.app_config.clone(),
            request_params.uri.clone(),
        ) {
            Ok(url) => url,
            Err(e) => return failure_callback(RequestError::Builder(e.to_string())),
        };

        let success_cb = move |v: ApiLinkHttpResponse, _| {
            success_callback(v);
        };

        let failure_cb = move |e: RequestError, _| {
            failure_callback(e);
        };

        let mut request_builder = self
            .builder(url, request_params.method, "API_LINK")
            .method(request_params.method)
            .with_parser(api_link_response_parser_callback)
            .with_timeout(request_params.timeout)
            .on_success(Box::new(success_cb))
            .on_failure(Box::new(failure_cb));

        if let Some(data) = request_params.body {
            request_builder = request_builder.data(data);
        }

        if ApiLinkAction::is_pv_edge_uri(request_params.uri.clone()) {
            request_builder = request_builder.with_device_proxy_headers()
        }

        for (request_header_key, request_header_value) in request_params.headers {
            request_builder =
                request_builder.header(request_header_key.to_lowercase(), request_header_value);
        }

        request_builder.execute();
    }
}

#[derive(Clone)]
pub struct ApiLinkHttpRequestParams {
    pub body: Option<String>,
    pub headers: HashMap<String, String>,
    pub method: HttpMethod,
    pub timeout: Duration,
    pub uri: String,
}

impl ApiLinkHttpRequestParams {
    pub fn new(api_link_action: &ApiLinkAction) -> Self {
        let body = api_link_action.requestBody.clone();
        let headers = api_link_action.requestHeaders.clone();
        let method = api_link_action.requestMethod.to_http_method();
        let timeout = Duration::from_millis(api_link_action.metadata.clientTimeoutInMs.into());
        let uri = api_link_action.uri.clone();

        Self {
            body,
            headers,
            method,
            timeout,
            uri,
        }
    }
}

#[derive(Clone)]
pub struct ApiLinkHttpResponse {
    pub response_body: Option<String>,
    pub response_status_code: i32,
}

fn api_link_response_parser_callback(
    _ctx: &AppContext,
    response_body: String,
    status_code: i32,
    cb: Box<dyn FnOnce(Result<DeviceProxyResponse<ApiLinkHttpResponse>, RequestError>)>,
) {
    cb(api_link_response_parser(response_body, status_code))
}

fn api_link_response_parser(
    response_body: String,
    status_code: i32,
) -> Result<DeviceProxyResponse<ApiLinkHttpResponse>, RequestError> {
    let parsed_response = ApiLinkHttpResponse {
        response_body: Some(response_body),
        response_status_code: status_code,
    };
    let parsed_response = LRCEdgeResponse::with_resource(parsed_response);
    let parsed_response = DeviceProxyResponse::LRCEdgeResponse(parsed_response);

    Ok(parsed_response)
}

fn generate_api_link_request_url(
    device_info: Rc<DeviceInformation>,
    app_config: AppConfigContext,
    api_link_uri: String,
) -> URLBuilderResult {
    if let Some(uri_path) = ApiLinkAction::extract_pv_edge_uri_path(api_link_uri.clone()) {
        match URLBuilder::for_device_proxy(
            uri_path.as_str(),
            vec![],
            false,
            Rc::clone(&device_info),
            Rc::clone(&app_config),
        ) {
            Ok(url) => Ok(url),
            Err(e) => {
                log::error!(
                    "Unable to create a PV edge request API link URL. Error: {:?}",
                    e
                );
                Err(e)
            }
        }
    } else if ApiLinkAction::is_http_uri(api_link_uri.clone()) {
        match URLBuilder::for_full_url(api_link_uri) {
            Ok(url) => Ok(url),
            Err(e) => {
                log::error!(
                    "Unable to create a raw HTTP request API link URL. Error: {:?}",
                    e
                );
                Err(e)
            }
        }
    } else {
        Err(URLBuilderError::UnsupportedProtocol)
    }
}

#[cfg(test)]
mock! {
    pub NetworkClient {
        pub fn new(ctx: &AppContext) -> Self;
    }

    impl ApiLinkHttpRequest for NetworkClient {
        fn call_http_endpoint<S, F>(&self, request: ApiLinkHttpRequestParams, success: S, failure: F)
        where
            S: FnOnce(ApiLinkHttpResponse) + 'static,
            F: FnOnce(RequestError) + 'static;
    }
}
