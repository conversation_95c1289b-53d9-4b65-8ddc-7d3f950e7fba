[package]
name = "amzn-av-living-room-rust-client"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[lints]
workspace = true

[dependencies]
app-drawer.workspace = true
activity-tracker.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
popups.workspace = true
log.workspace = true
serde.workspace = true
serde_json.workspace = true
discovery-assistant.workspace = true
profile-selection.workspace = true
media-background.workspace = true
title-details.workspace = true
auth.workspace = true
router.workspace = true
location.workspace = true
synchronized-state-store.workspace = true
cross-app-message.workspace = true
cross-app-events.workspace = true
collections-ui.workspace = true
collections-ui-signals.workspace = true
contextual-menu.workspace = true
fableous.workspace = true
mockall_double.workspace = true
mockall.workspace = true
navigation-menu.workspace = true
profile-manager.workspace = true
settings-manager.workspace = true
linear.workspace = true
linear-ui-signals.workspace = true
app-config.workspace = true
details.workspace = true
rust-features.workspace = true
cache.workspace = true
beekeeper.workspace = true
resiliency-store.workspace = true
common-transform-types.workspace = true
modal-manager.workspace = true
critical-notification.workspace = true
taps-parameters.workspace = true
playback-navigation.workspace = true
acm-config.workspace = true
playback-page.workspace = true
payment-risk-message.workspace = true
clickstream.workspace = true
toast-manager.workspace = true
checkout.workspace = true
playback.workspace = true
screensaver.workspace = true
search-page.workspace = true
consumption-only.workspace = true
collection-types.workspace = true
diagnostics.workspace = true
playback-cache.workspace = true
storage.workspace = true
network.workspace = true
firetv = { workspace = true, features = ["init"] }
sports-favorites-ui.workspace = true
sports-schedule.workspace = true
go-to-background.workspace = true
firetv-tvif = { workspace = true }

[dev-dependencies]
rstest.workspace = true
amzn-ignx-compositron = { workspace = true, features = [
    "lifetime_apis",
    "test_utils",
] }
beekeeper = { workspace = true, features = ["mocks"] }
collections-ui = { workspace = true, features = ["example_data"] }
linear = { workspace = true, features = ["example_data"] }
details = { workspace = true, features = ["collections_mock_network"] }
network-parser.workspace = true
cache = { workspace = true, features = ["test-utils"] }

[features]
default = []
debug_impl = ["collections-ui/debug_impl"]
standalone_app = ["location/standalone_app"]
rust_details = ["collections-ui/rust_details"]
pointer_control = ["rust-features/pointer_control"]
no_run_main = []
test_utils = []

[lib]
crate-type = ["cdylib", "rlib"]
path = "src/lib.rs"

[[bin]]
name = "amzn-av-living-room-rust-client"
path = "src/main.rs"
