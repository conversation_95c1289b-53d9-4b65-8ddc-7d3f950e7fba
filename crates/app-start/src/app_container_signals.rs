use common_transform_types::profile::Profile;
use ignx_compositron::prelude::{create_memo, Scope, Signal, SignalGet};
use location::Location;
use navigation_menu::model::nav_model::TopNavData;
#[mockall_double::double]
use navigation_menu::state::TopNavManager;
use std::rc::Rc;

/// Implementing this trait will define app container specific requirements for the signals it uses
pub(crate) trait AppContainerSignal<T, U> {
    fn create_app_container_signal(scope: Scope, dependent_data: U) -> Signal<T>;
}

pub(crate) struct AppContainerDependentTopNavData {
    pub active_profile: Signal<Option<Profile>>,
    pub location: Signal<Location>,
    pub top_nav_manager: TopNavManager,
    pub back_handler: Rc<dyn Fn(Scope)>,
}

impl AppContainerSignal<TopNavData, AppContainerDependentTopNavData> for TopNavData {
    fn create_app_container_signal(
        scope: Scope,
        dependent_data: AppContainerDependentTopNavData,
    ) -> Signal<TopNavData> {
        create_memo(scope, {
            let AppContainerDependentTopNavData {
                active_profile,
                location,
                top_nav_manager,
                back_handler,
            } = dependent_data;
            move |_| {
                let active_profile_is_adult = active_profile
                    .try_get()
                    .flatten()
                    .is_none_or(|profile| profile.isAdult);
                let new_loc = location.get();

                let top_nav = TopNavData::new(
                    top_nav_manager.get_static_items(active_profile_is_adult),
                    top_nav_manager
                        .get_dynamic_items(scope)
                        .try_get()
                        .unwrap_or_default(),
                    top_nav_manager.get_addon_button(scope).try_get().flatten(),
                    top_nav_manager
                        .get_analytics(scope)
                        .try_get()
                        .unwrap_or_default(),
                    back_handler.clone(),
                    active_profile_is_adult,
                );

                // on back navigations we avoid trying to force the focus to the top nav (we may want to
                // place focus somewhere in the page)
                if new_loc.is_back("app_container") {
                    top_nav
                } else {
                    top_nav.with_focused_item_intent(&new_loc, scope)
                }
            }
        })
        .into()
    }
}
