use std::os::raw::c_char;

#[cfg(target_arch = "wasm32")]
#[allow(unused_imports, reason = "linking")]
// If the crate is not referenced in the program,
// symbols defined in that crate are being stripped
// before the crate is linked.
use ignx_compositron::allocator::amzn_ignx_allocator;

#[allow(unsafe_code, reason = "app start")]
extern "C" {
    pub fn _start();
}

#[no_mangle]
#[allow(unsafe_code, reason = "app start")]
pub extern "C" fn run_main(_argv: *const *const c_char, _argc: i32) -> i32 {
    unsafe {
        _start();
    }
    0
}

fn main() {
    amzn_av_living_room_rust_client::run_app()
}
