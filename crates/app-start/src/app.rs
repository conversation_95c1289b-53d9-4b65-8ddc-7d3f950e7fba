use auth::{AuthContext, AuthTokens};
use cross_app_events::ApplicationEventReporter;
use cross_app_message::{CrossAppCommandSender, JSToRustCommand};
use ignx_compositron::app::{layer_switcher::LayerSwitcher, rpc::RPCManager, wasm_app::WasmApp};
use ignx_compositron::localization::localization_manager::LocalizationManager;
use ignx_compositron::reactive::Scope;
use mockall_double::double;
use navigation_menu::state::top_nav_metrics_reporter::TopNavMetricsReporter;
use router::RouterProxy;
use std::cell::RefCell;
use std::rc::Rc;
use synchronized_state_store::SynchronizedStateStoreUpdate;

use acm_config::{AcmConfigLoader, AcmConfigLoaderImpl};
#[double]
use app_config::AppConfigStore;
#[double]
use auth::Auth;
#[double]
use beekeeper::Beekeeper;
#[double]
use consumption_only::ConsumptionOnly;
use consumption_only::ConsumptionOnlyStore;
#[double]
use navigation_menu::state::TopNavManager;
#[double]
use profile_manager::ProfileManager;
#[double]
use resiliency_store::ResiliencyStore;
#[double]
use rust_features::RustFeatures;
#[double]
use settings_manager::SettingsManager;
use settings_manager::{ApplicationSettings, ApplicationSettingsContext};
#[double]
use synchronized_state_store::StateDispatcher;
use taps_parameters::TapsParameterStore;
#[double]
use taps_parameters::TapsParameters;
#[double]
use toast_manager::ToastManager;

pub struct App {
    auth: Rc<dyn AuthTokens>,
    router_proxy: Rc<RouterProxy>,
    rpc_manager: RPCManager,
    profile_manager: ProfileManager,
    settings_manager: Rc<dyn ApplicationSettings>,
    app_events_reporter: ApplicationEventReporter,
    localization_manager: LocalizationManager,
    app_config: Rc<AppConfigStore>,
    top_nav_manager: TopNavManager,
    top_nav_metrics_reporter: Rc<RefCell<TopNavMetricsReporter>>,
    toast_manager: ToastManager,
    rust_features: RustFeatures,
    resiliency_store: ResiliencyStore,
    taps: Rc<dyn TapsParameterStore>,
    beekeeper: Rc<Beekeeper>,
    acm_config_loader: Rc<dyn AcmConfigLoader>,
    consumption_only: Rc<dyn ConsumptionOnlyStore>,
}

impl App {
    pub fn new(
        scope: Scope,
        layer_switcher: LayerSwitcher,
        localization_manager: LocalizationManager,
        _cross_app_command_sender: CrossAppCommandSender,
        rpc_manager: RPCManager,
        app_events_reporter: ApplicationEventReporter,
    ) -> Self {
        let state_dispatcher = Rc::new(StateDispatcher::new());

        let state_dispatcher_clone = state_dispatcher.clone();
        rpc_manager.register_cross_app_function(
            "synchronized_state_store_update".into(),
            move |args: SynchronizedStateStoreUpdate| {
                state_dispatcher_clone.dispatch_state_update(args.storeId, args.newValue);
                Ok(true)
            },
        );

        let auth = Rc::new(Auth::new(scope, &state_dispatcher, rpc_manager.clone()));
        let router_proxy = RouterProxy::new(scope, rpc_manager.clone(), layer_switcher);
        let profile_manager = ProfileManager::new(scope, &state_dispatcher, rpc_manager.clone());
        let settings_manager = Rc::new(SettingsManager::new(scope, &state_dispatcher));
        let app_config = Rc::new(AppConfigStore::new(scope, &state_dispatcher));
        let top_nav_manager = TopNavManager::new(scope, &state_dispatcher);
        let top_nav_metrics_reporter = Rc::new(RefCell::new(TopNavMetricsReporter::new()));
        let rust_features = RustFeatures::new(scope, &state_dispatcher, rpc_manager.clone());
        let beekeeper = Rc::new(Beekeeper::new(
            scope,
            &state_dispatcher,
            rpc_manager.clone(),
        ));
        let acm_config_loader = AcmConfigLoaderImpl::new(rpc_manager.clone());
        let taps = Rc::new(TapsParameters::new(scope, &state_dispatcher));
        let consumption_only = Rc::new(ConsumptionOnly::new(scope, &state_dispatcher));
        let resiliency_store = ResiliencyStore::new(scope, &state_dispatcher);
        let toast_manager = ToastManager::new(scope);

        Self {
            auth,
            router_proxy,
            rpc_manager,
            profile_manager,
            settings_manager,
            app_events_reporter,
            localization_manager,
            app_config,
            top_nav_manager,
            top_nav_metrics_reporter,
            toast_manager,
            rust_features,
            resiliency_store,
            taps,
            beekeeper,
            acm_config_loader,
            consumption_only,
        }
    }

    fn handle_cross_app_command(&mut self, cmd: JSToRustCommand) {
        match cmd {
            JSToRustCommand::LocalizationUpdate { value } => {
                self.localization_manager.send_localization_update(value)
            }
        }
    }

    pub fn get_auth(&self) -> AuthContext {
        Rc::clone(&self.auth)
    }

    pub fn get_profile_manager(&self) -> ProfileManager {
        self.profile_manager.clone()
    }

    pub fn get_settings_manager(&self) -> ApplicationSettingsContext {
        Rc::clone(&self.settings_manager)
    }

    pub fn get_top_nav_manager(&self) -> TopNavManager {
        self.top_nav_manager.clone()
    }

    pub fn get_top_nav_metrics_reporter(&self) -> Rc<RefCell<TopNavMetricsReporter>> {
        self.top_nav_metrics_reporter.clone()
    }

    pub fn get_toast_manager(&self) -> ToastManager {
        self.toast_manager.clone()
    }

    pub fn get_router(&self) -> Rc<RouterProxy> {
        Rc::clone(&self.router_proxy)
    }

    pub fn get_rpc_manager(&self) -> RPCManager {
        self.rpc_manager.clone()
    }

    pub fn get_app_events_reporter(&self) -> ApplicationEventReporter {
        self.app_events_reporter.clone()
    }

    pub fn get_app_config(&self) -> Rc<AppConfigStore> {
        Rc::clone(&self.app_config)
    }

    pub fn get_rust_features(&self) -> RustFeatures {
        self.rust_features.clone()
    }

    pub fn get_resiliency_store(&self) -> ResiliencyStore {
        self.resiliency_store.clone()
    }

    pub fn get_taps_parameters(&self) -> Rc<dyn TapsParameterStore> {
        self.taps.clone()
    }

    pub fn get_consumption_only_store(&self) -> Rc<dyn ConsumptionOnlyStore> {
        self.consumption_only.clone()
    }

    pub fn get_acm_config_loader(&self) -> Rc<dyn AcmConfigLoader> {
        self.acm_config_loader.clone()
    }

    pub fn get_beekeeper(&self) -> Rc<Beekeeper> {
        Rc::clone(&self.beekeeper)
    }
}

impl WasmApp for App {
    fn receive_cross_app_command(&mut self, command_str: &str) {
        let command: serde_json::Result<JSToRustCommand> = serde_json::from_str(command_str);

        match command {
            Ok(command) => self.handle_cross_app_command(command),
            Err(e) => log::error!("Error parsing command: {:?}", e),
        }
    }

    fn rpc_manager(&mut self) -> &mut RPCManager {
        &mut self.rpc_manager
    }
}
