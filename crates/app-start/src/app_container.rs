#![allow(unused_imports, reason = "compositron macro needs to be fixed")]

use crate::app::App;
use crate::app_container_signals::*;
use crate::app_interactions::init_app_interaction_reporting;
use crate::cache::page_cache::PageCache;
use crate::pages::*;
use activity_tracker::activity_tracker::{
    ActivityTracker, ActivityTrackerBackgroundEvent, ActivityTrackerEvent, ActivityTrackerRc,
    ActivityTypes,
};
use app_config::AppConfigContext;
use app_drawer::{component::*, context::AppDrawerContext};
use auth::AuthContext;
use cache::ExpirableLruCacheRc;
use checkout::app_context::provide_checkout_app_context;
use collections_ui::cache::CachedCollectionsPage;
use collections_ui_signals::CollectionsPageStateOverrides;
use critical_notification::service::CriticalNotificationService;
use cross_app_events::ApplicationEventReporter;
use diagnostics::components::catflap_container::*;
use fableous::toasts::toast::*;
use fableous::toasts::toast_context::ToastContext;
use ignx_compositron::composable::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::device_information::DeviceInformation;
use ignx_compositron::focusable::FocusableComposable;
use ignx_compositron::input::KeyCode;
use ignx_compositron::layout::Alignment;
use ignx_compositron::playback::set_playback_mode;
use ignx_compositron::player::PlayerCommand;
use ignx_compositron::player_data::PlaybackMode;
use ignx_compositron::player_data::PlayerCapabilities;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::*;
use ignx_compositron::stack::*;
use linear_ui_signals::LivePageStateOverrides;
use location::{Location, PageType, RustPage};
use media_background::media_background::*;
use media_background::provide_media_background_context;
use media_background::types::provide_hybrid_playback;
use media_background::types::provide_trailer_playback;
use media_background::types::{
    HybridPlaybackContext, MediaBackgroundType, PlayerStatus, TrailerPlaybackContext, Video,
};
use mockall_double::double;
use modal_manager::modal_manager::*;
use navigation_menu::model::nav_model::TopNavData;
use navigation_menu::ui::navigation_menu::*;
use payment_risk_message::{PaymentRiskContext, PaymentRiskState};
use playback::context::provide_playback;
use playback::provider::*;
use playback_cache::provide_playback_cache;
use popups::exit_popup::*;
#[double]
use resiliency_store::ResiliencyStore;
use router::{Routing, RoutingContext};
use rust_features::{try_use_rust_features, WeblabTreatmentString};
use screensaver::screensaver::*;
use std::cell::RefCell;
use std::rc::Rc;
use std::time::Duration;
use title_details::core::create_title_details_signals;
use title_details::core::*;

use acm_config::AcmConfigLoader;
#[double]
use beekeeper::BeekeeperContext;
use cache::cache_invalidation::emitter::{
    CacheInvalidationEventEmitter, CacheInvalidationEventEmitterRc,
};
use cache::cache_invalidation::events::CacheInvalidationEvent;
use clickstream::client::{provide_clickstream_client, ClickstreamEmitter};
use common_transform_types::actions::TransitionAction;
use consumption_only::ConsumptionOnlyStore;
use cross_app_events::app_event::AppEventReporter;
use details::context::hooks::use_details_page_context;
use discovery_assistant::cache::{DiscoveryAssistantCache, DiscoveryAssistantCaching};
use firetv::{FireTV, FireTVContext};
use ignx_compositron::lifecycle::{LifecycleState, LifecycleStateContext};
use ignx_compositron::{compose, Composer};
use log::error;
use media_background::types::context::provide_media_background_status_context;
use network::types::crc_types::CrcState;
use profile_manager::ProfileManager;
#[double]
use rust_features::RustFeatures;
use screensaver::screensaver_context::{new_screensaver_context, ScreensaverContext};
use serde_json::{Map, Value};
use settings_manager::ApplicationSettings;
use storage::{Storage, StorageImpl};
use taps_parameters::TapsParameterStore;
use title_details::core::context::provide_title_details_status_context;

/// Outermost composable of the app
#[Composer]
pub fn AppContainer(ctx: &AppContext, app: Rc<RefCell<App>>) -> impl Composable<'static> {
    let modal_data = create_rw_signal(ctx.scope(), vec![]);

    let profile_manager = app.borrow().get_profile_manager();
    let settings_manager = app.borrow().get_settings_manager();
    let top_nav_manager = app.borrow().get_top_nav_manager();
    let top_nav_metrics_reporter = app.borrow().get_top_nav_metrics_reporter();
    let toast_manager = app.borrow().get_toast_manager();
    let router = app.borrow().get_router();
    let location = router.location();
    let activity_tracker: ActivityTrackerRc = ActivityTracker::new(ctx.clone());

    let live_page_player_command_context =
        create_rw_signal::<Option<PlayerCommand>>(ctx.scope(), None);
    let toast_context = ToastContext::new(ctx.scope());
    provide_context::<RwSignal<Option<PlayerCommand>>>(
        ctx.scope(),
        live_page_player_command_context,
    );
    let collection_page_state_overrides = CollectionsPageStateOverrides::new(ctx.scope());
    provide_context::<CollectionsPageStateOverrides>(ctx.scope(), collection_page_state_overrides);
    let live_page_state_overrides = LivePageStateOverrides::new(ctx.scope());
    provide_context::<LivePageStateOverrides>(ctx.scope(), live_page_state_overrides);
    provide_context::<ToastContext>(ctx.scope(), toast_context);
    provide_trailer_playback(ctx.scope());
    provide_hybrid_playback(ctx.scope());
    provide_context::<AuthContext>(ctx.scope, app.borrow().get_auth());
    provide_context(ctx.scope, profile_manager.clone());
    provide_context(ctx.scope, settings_manager.clone());
    provide_context(ctx.scope, top_nav_manager.clone());
    provide_context(ctx.scope, top_nav_metrics_reporter);
    provide_context(ctx.scope, toast_manager);
    provide_context::<RoutingContext>(ctx.scope, router.clone());
    provide_context(ctx.scope, app.borrow().get_rpc_manager());
    provide_context::<ApplicationEventReporter>(ctx.scope, app.borrow().get_app_events_reporter());
    provide_context::<AppConfigContext>(ctx.scope(), app.borrow().get_app_config());
    provide_context::<RustFeatures>(ctx.scope(), app.borrow().get_rust_features());
    provide_context::<Rc<dyn AcmConfigLoader>>(ctx.scope(), app.borrow().get_acm_config_loader());
    provide_context::<Rc<dyn TapsParameterStore>>(ctx.scope(), app.borrow().get_taps_parameters());
    provide_context::<Rc<dyn ConsumptionOnlyStore>>(
        ctx.scope(),
        app.borrow().get_consumption_only_store(),
    );
    provide_context::<ResiliencyStore>(ctx.scope(), app.borrow().get_resiliency_store());
    provide_context::<ActivityTrackerRc>(ctx.scope(), activity_tracker.clone());
    provide_media_background_status_context(ctx.scope());
    provide_title_details_status_context(ctx.scope());
    AppDrawerContext::provide_context(ctx.scope());
    provide_context::<Rc<dyn Storage>>(ctx.scope(), Rc::new(StorageImpl::new()));
    let playback_cache = provide_playback_cache(ctx);
    provide_context::<StoredValue<CrcState>>(
        ctx.scope(),
        store_value(ctx.scope(), CrcState::default()),
    );

    let app_event_reporter = Rc::new(AppEventReporter::new(ctx.scope()));
    provide_clickstream_client(ctx.scope(), app_event_reporter);

    let cache_invalidation_event_emitter =
        CacheInvalidationEventEmitter::new_rc_with_rpc(app.borrow().get_rpc_manager());

    provide_context::<CacheInvalidationEventEmitterRc>(
        ctx.scope,
        cache_invalidation_event_emitter.clone(),
    );

    // Will be updated by the player once initialised
    provide_context::<RwSignal<Option<PlayerCapabilities>>>(
        ctx.scope(),
        create_rw_signal(ctx.scope(), None),
    );

    let collections_cache: ExpirableLruCacheRc<String, CachedCollectionsPage> =
        collections_ui::cache::create_cache(
            ctx.scope,
            Some(cache_invalidation_event_emitter.clone()),
        );

    let discovery_assistant_cache =
        DiscoveryAssistantCaching::new(ctx.scope, cache_invalidation_event_emitter.clone());
    provide_context::<DiscoveryAssistantCache>(ctx.scope(), discovery_assistant_cache);

    let screensaver_context = new_screensaver_context(
        ctx,
        app.borrow()
            .get_rust_features()
            .is_pv_screensaver_enabled_signal(),
        router,
        profile_manager.get_active_profile(),
        cache_invalidation_event_emitter.clone(),
    );
    screensaver_context.register_inactivity_event(ctx, &activity_tracker);

    init_app_interaction_reporting(ctx, activity_tracker.clone());
    activity_tracker
        .borrow()
        .register_event(ActivityTrackerEvent {
            callback: Rc::new({
                let cache_invalidation_event_emitter = Rc::clone(&cache_invalidation_event_emitter);
                move || {
                    cache_invalidation_event_emitter
                        .borrow_mut()
                        .emit(CacheInvalidationEvent::Inactivity)
                }
            }),
            delay: Duration::from_secs(18 * 60),
            passive: true,
            should_repeat: true,
        });

    activity_tracker
        .borrow()
        .register_background_event(ActivityTrackerBackgroundEvent {
            callback: Rc::new({
                let collection_state_overrides = collection_page_state_overrides;
                let live_state_overrides = live_page_state_overrides;

                let router = app.borrow().get_router();
                let rust_features = app.borrow().get_rust_features();

                move || {
                    let cache_experiment_treatment =
                        rust_features.get_rust_reduce_caching_treatment_string();

                    // Redirection doesn't work from JS pages, and not needed for the Profiles Page
                    if cache_experiment_treatment == WeblabTreatmentString::C
                        || router
                            .location()
                            .try_get_untracked()
                            .is_some_and(|location| {
                                matches!(
                                    location.pageType,
                                    PageType::Js(_)
                                        | PageType::Rust(RustPage::RUST_PROFILE_SELECTION)
                                )
                            })
                    {
                        return;
                    }

                    // Helper function to determine the target page type and parameters
                    fn determine_transition_page(
                        current_location: Option<Location>,
                        treatment: WeblabTreatmentString,
                    ) -> (PageType, Map<String, Value>) {
                        // Default to Home page with empty parameters
                        let home_page = (
                            PageType::Rust(RustPage::RUST_COLLECTIONS),
                            Default::default(),
                        );

                        // If no current location, return default
                        let Some(location) = current_location else {
                            return home_page;
                        };

                        match treatment {
                            // T1: Always redirect to the Home page
                            WeblabTreatmentString::T1 => home_page,

                            // T2: Preserve current page and parameters for supported pages
                            WeblabTreatmentString::T2 => match location.pageType {
                                PageType::Rust(RustPage::RUST_COLLECTIONS | RustPage::RUST_LIVE_TV | RustPage::RUST_DETAILS) => {
                                    (location.pageType, location.pageParams)
                                }
                                // For unsupported pages, redirect to Home
                                _ => home_page,
                            },

                            // Other treatments: Default to Home page
                            _ => home_page,
                        }
                    }

                    let current_page_location: Option<Location> =
                        router.location().try_get_untracked();

                    // Determine the target page
                    let transition_location = {
                        // Determine page type and parameters based on current location and experiment treatment
                        let (page_type, page_params) = determine_transition_page(
                            current_page_location.clone(),
                            cache_experiment_treatment,
                        );

                        Location {
                            pageType: page_type,
                            pageParams: page_params,
                        }
                    };

                    // Force page reload
                    match transition_location.pageType {
                        PageType::Rust(RustPage::RUST_COLLECTIONS) => {
                            collection_state_overrides.should_reload.set(true);
                        }
                        PageType::Rust(RustPage::RUST_LIVE_TV) => {
                            live_state_overrides.should_reload.set(true);
                        }
                        _ => {
                            if transition_location.pageType.is_refreshable() {
                                log::error!("[Background Inactivity] Page is refreshable, but refresh wasn't called. The data might be stale");
                            }
                        }
                    }

                    // Navigate only if the target differs,
                    // otherwise just reload is enough
                    if current_page_location.is_some_and(|current_location| {
                        !current_location.is_same_page_as(&transition_location, true)
                    }) {
                        router.navigate(transition_location, "BACKGROUND_INACTIVITY");
                    }
                }
            }),
            threshold: Duration::from_secs(18 * 60), // 18 minutes
            should_repeat: true,
        });

    create_effect(ctx.scope(), {
        let activity_tracker = activity_tracker.clone();
        move |prev| {
            let new = location.get();
            if prev.is_none_or(|prev: Location| !prev.is_same_page_as(&new, true)) {
                activity_tracker
                    .borrow()
                    .on_activity(ActivityTypes::PageChange(&new.pageType))
            }
            new
        }
    });

    provide_context::<ExpirableLruCacheRc<String, CachedCollectionsPage>>(
        ctx.scope(),
        collections_cache,
    );
    provide_context::<BeekeeperContext>(ctx.scope(), app.borrow().get_beekeeper());

    create_effect(ctx.scope(), {
        let ctx = ctx.clone();
        let auth = app.borrow().get_auth();
        let is_rust_collections_enabled_signal = app
            .borrow()
            .get_rust_features()
            .is_rust_collections_enabled_signal();
        let rpc_manager = app.borrow().get_rpc_manager();

        move |initialized: Option<bool>| {
            let is_rust_collections_enabled = is_rust_collections_enabled_signal.get();

            if !is_rust_collections_enabled {
                return false;
            }

            if initialized.is_some_and(|i| i) {
                return true;
            }

            CriticalNotificationService::new(
                ctx.clone(),
                auth.clone(),
                modal_data.write_only(),
                rpc_manager.clone(),
            );

            true
        }
    });

    let media_background_sig = create_rw_signal(ctx.scope(), MediaBackgroundType::None);
    let title_details_signals = create_title_details_signals(ctx.scope());
    let active_profile = profile_manager.get_active_profile();
    let autoplay_enabled = settings_manager.get_autoplay_enabled();

    let top_nav_data: Signal<TopNavData> = TopNavData::create_app_container_signal(
        ctx.scope(),
        AppContainerDependentTopNavData {
            active_profile,
            location,
            top_nav_manager,
            back_handler: Rc::new(exit_modal_back_handler),
        },
    );

    let page_cache = PageCache::initialize_page_cache_context(
        ctx,
        top_nav_data,
        Some(Rc::clone(&cache_invalidation_event_emitter)),
    );

    linear::cache::create_cache(
        ctx.scope(),
        Rc::clone(&cache_invalidation_event_emitter),
        app.borrow().get_rpc_manager(),
        page_cache.live_page_cache,
    );

    profile_manager::rpc::promo_fetch_invocation::register_fetch_promotions_invocation(
        ctx,
        app.borrow().get_rpc_manager(),
    );

    provide_context::<RwSignal<MediaBackgroundType>>(ctx.scope(), media_background_sig);
    provide_context::<RwSignal<TitleDetailsChangeRequest>>(ctx.scope(), title_details_signals.data);
    provide_context::<PaymentRiskContext>(ctx.scope(), Rc::new(PaymentRiskState::default().into()));
    let playback_context = provide_playback(ctx);

    let mut firetv_context = FireTVContext::new(playback_context, playback_cache);
    firetv_context.register_playback_events(ctx);
    firetv_context.provide(ctx.scope());

    provide_checkout_app_context(ctx.scope());

    let media_background_ctx = provide_media_background_context(ctx.scope());
    create_effect(ctx.scope(), {
        let activity_tracker = activity_tracker.clone();
        move |_| {
            let is_playback_running = media_background_ctx
                .player_status
                .with(|value| matches!(value, PlayerStatus::PLAYING));
            activity_tracker
                .borrow()
                .on_activity(ActivityTypes::Playback(is_playback_running));
        }
    });

    let on_app_activity = {
        let screensaver_context = screensaver_context.clone();
        move || {
            activity_tracker
                .borrow()
                .on_activity(ActivityTypes::ButtonPress);
            screensaver_context.dismiss_screensaver();
        }
    };

    let catflap_enabled: RwSignal<bool> = create_rw_signal(ctx.scope(), false);

    compose! {
        Stack() {
            NavigationMenu(active_profile, top_nav_data, content: move |ctx, focused| {
                compose! {
                    Stack() {
                        PlaybackProvider()
                        MediaBackground(incoming_data: media_background_sig.read_only(), rtl_enabled: false, autoplay_enabled)
                        TitleDetails(
                            incoming_data: title_details_signals.data.read_only(),
                            y_offset: title_details_signals.y_offset.read_only(),
                            x_offset: title_details_signals.x_offset.read_only(),
                            opacity: title_details_signals.opacity.read_only()
                        )
                        Pages(
                            update_media_background_rw: media_background_sig,
                            title_details_signals,
                            modal_data: modal_data.write_only(),
                            top_nav_data,
                            active_profile
                        )
                        Toast(
                            message: toast_context.message,
                            visible: toast_context.visible,
                        )
                        ExitPopup()
                    }
                    .focused(focused)
                }
            })
            ModalManager(modal_data)
            AppDrawer()
            Screensaver(screensaver_context)
            CatflapContainer(catflap_enabled)
        }
        .alignment(Alignment::StartTop) // Must be StartTop to avoid nodes at (0, 0) getting slightly shifted off screen when on a non-exact 16:9 screen
        .on_input_activity(on_app_activity)
        .on_combo_press(
            vec![
                KeyCode::Left,
                KeyCode::Right,
                KeyCode::Up,
                KeyCode::Down,
                KeyCode::Up,
                KeyCode::Down,
            ],
            move || catflap_enabled.toggle(),
        )
    }
}
