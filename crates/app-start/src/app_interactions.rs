use std::{cell::RefCell, rc::Rc};

use activity_tracker::activity_tracker::ActivityTracker;
#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
use cross_app_events::send_app_event;
use ignx_compositron::{
    context::AppContext,
    reactive::{on_cleanup, provide_context, Scope},
    time::{Duration, Instant},
};

const APP_INTERACTION_STATUS_CHECK_INTERVAL_IN_SECS: u64 = 30;

/// Sets up a recurring task to report whether customer has interacted with the app.
pub fn init_app_interaction_reporting(
    ctx: &AppContext,
    activity_tracker: Rc<RefCell<ActivityTracker>>,
) {
    let scope = ctx.scope();
    let task_guard = ctx.schedule_repeating_task(
        Duration::from_secs(APP_INTERACTION_STATUS_CHECK_INTERVAL_IN_SECS),
        move || {
            let last_active_instant = activity_tracker.borrow().get_last_activity_instant();
            if Instant::now()
                < last_active_instant
                    + Duration::from_secs(APP_INTERACTION_STATUS_CHECK_INTERVAL_IN_SECS)
            {
                // There has been an interaction in the last time interval. Report it.
                report_app_interaction_app_event(scope);
            }
        },
    );
    let task_id = task_guard.task_id;
    // Not actually used, but keep task_guard alive otherwise the repeating task will stop
    provide_context(ctx.scope, Rc::new(task_guard));

    on_cleanup(ctx.scope, {
        let ctx = ctx.clone();
        move || {
            ctx.cancel_task(task_id);
        }
    });
}

#[allow(deprecated, reason = "https://issues.amazon.com/issues/LR-Rust-630")]
fn report_app_interaction_app_event(scope: Scope) {
    send_app_event(scope, "APP_INTERACTION", "GLOBAL", None);
}

// Todo: Add Tests once ActivityTracker mocks are available
