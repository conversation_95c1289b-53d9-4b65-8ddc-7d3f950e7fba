use cache::ExpirableLruCacheRc;
use collection_types::network_types::CollectionsPage;
use collections_ui::prefetch::prefetch_collection_initial_if_allowed;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::*;
use linear::network::types::LivePageResponseResourceLrc;
use linear::prefetch::prefetch_initial_live_page;
use navigation_menu::model::nav_model::{TopNavData, TransitionAction};
use profile_manager::{get_home_page_props, try_use_profile_manager, HomePageProps};

pub fn prefetch_home_page(ctx: &AppContext, cache: &ExpirableLruCacheRc<String, CollectionsPage>) {
    log::info!("Trying to prefetch home page");
    let profile_manager = try_use_profile_manager(ctx.scope());
    let is_kids = profile_manager.is_some_and(|pm| {
        pm.get_active_profile()
            .get_untracked()
            .is_some_and(|p| !p.isAdult)
    });
    let HomePageProps { page_type, page_id } = get_home_page_props(!is_kids);
    prefetch_collection_initial_if_allowed(
        ctx,
        page_id.to_string(),
        page_type.to_string(),
        None,
        cache,
    );
}

/// Prefetch first 6 static top nav pages. This is expected to be called after a static top nav
/// page is loaded, so only 5 would end up an actual downstream call.
pub fn prefetch_top_nav_pages(
    ctx: &AppContext,
    top_nav_data: Signal<TopNavData>,
    cache: &ExpirableLruCacheRc<String, CollectionsPage>,
    live_page_cache: &ExpirableLruCacheRc<String, LivePageResponseResourceLrc>,
    active_profile_is_adult: bool,
) {
    let top_nav_data = top_nav_data.get_untracked();
    if top_nav_data.active_profile_is_adult() != active_profile_is_adult {
        // We have encountered a race condition where the top nav data is not yet updated but the
        // active profile has changed. We shouldn't prefetch the old top nav items.
        return;
    }
    for nav_item in top_nav_data.static_items().iter().take(6) {
        match &nav_item.action {
            TransitionAction::GoToCollectionPage {
                page_type,
                page_id,
                service_token,
                ref_marker: _,
            } => {
                log::info!(
                    "Trying to prefetch collection initial {} {} {:?}",
                    page_type,
                    page_id,
                    service_token
                );
                prefetch_collection_initial_if_allowed(
                    ctx,
                    page_id.clone(),
                    page_type.clone(),
                    service_token.clone(),
                    cache,
                );
            }
            TransitionAction::GoToLiveTv(_) => {
                log::info!("Prefetching live page");
                prefetch_initial_live_page(ctx, None, live_page_cache)
            }
            _ => {}
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;
    use cache::expirable_lru_cache::ExpirableLruCache;
    use collections_ui::examples_utils::{
        MockNetworkClient as MockCollectionsNetworkClient,
        __mock_MockNetworkClient::__new::Context as MockCollectionsNetworkClientContext,
    };
    use ignx_compositron::app::launch_only_app_context;
    use linear::mock_utils::{
        MockNetworkClient as MockLinearNetworkClient,
        MockNetworkClientContext as MockLinearNetworkClientContext,
    };
    use rust_features::create_mock_rust_features;
    use std::rc::Rc;
    use std::time::Duration;

    // these are tested against in the `prefetch_collection_initial_if_allowed` function
    const EXPECTED_PAGE_TYPE: &str = "home";
    const EXPECTED_TAPS_ROLES: Vec<String> = vec![];
    const EXPECTED_PRESENTATION_SCHEME: &str = "living-room-react-focus";

    struct TestSetup {
        pub collections_client_context: MockCollectionsNetworkClientContext,
        pub linear_client_context: MockLinearNetworkClientContext,
        pub top_nav_data: Signal<TopNavData>,
        pub collections_cache: ExpirableLruCacheRc<String, CollectionsPage>,
        pub linear_cache: ExpirableLruCacheRc<String, LivePageResponseResourceLrc>,
    }

    fn setup(scope: Scope) -> TestSetup {
        provide_context(scope, create_mock_rust_features(true, false, false));

        let collections_client_context = MockCollectionsNetworkClient::new_context();
        let linear_client_context = MockLinearNetworkClient::new_context();
        let top_nav_data: Signal<TopNavData> = create_signal(
            scope,
            TopNavData::new(
                vec![],
                vec![],
                None,
                Default::default(),
                Rc::new(|_| {}),
                true,
            ),
        )
        .0
        .into();
        let collections_cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
        let linear_cache = ExpirableLruCache::new_rc(1, Box::new(|_| Duration::from_secs(60)));
        TestSetup {
            collections_client_context,
            linear_client_context,
            top_nav_data,
            collections_cache,
            linear_cache,
        }
    }

    #[test]
    fn prefetches_top_nav_pages() {
        launch_only_app_context(move |ctx| {
            let TestSetup {
                collections_client_context,
                linear_client_context,
                top_nav_data,
                collections_cache,
                linear_cache,
            } = setup(ctx.scope());

            collections_client_context.expect().returning(move |_| {
                let mut mock_client = MockCollectionsNetworkClient::default();

                mock_client
                    .expect_prefetch_collection_initial()
                    .times(1)
                    .withf(move |_, _, params, _| {
                        params.container_request_base.taps_roles == EXPECTED_TAPS_ROLES
                            && params.container_request_base.presentation_scheme
                                == EXPECTED_PRESENTATION_SCHEME
                    })
                    .return_const(());
                mock_client
            });

            linear_client_context.expect().returning(move |_| {
                let mut mock_client = MockLinearNetworkClient::default();

                mock_client
                    .expect_prefetch_initial_live_page()
                    .times(1)
                    .return_const(());
                mock_client
            });

            prefetch_top_nav_pages(&ctx, top_nav_data, &collections_cache, &linear_cache, true);
        });
    }

    #[test]
    fn does_not_prefetch_top_nav_pages_if_active_profile_is_adult_mismatched() {
        launch_only_app_context(move |ctx| {
            let TestSetup {
                collections_client_context,
                linear_client_context,
                top_nav_data,
                collections_cache,
                linear_cache,
            } = setup(ctx.scope());

            collections_client_context.expect().times(0);
            linear_client_context.expect().times(0);

            prefetch_top_nav_pages(&ctx, top_nav_data, &collections_cache, &linear_cache, false);
        });
    }
}
