use crate::cache::prefetch::{prefetch_home_page, prefetch_top_nav_pages};
use cache::cache_control::CacheControl;
use cache::cache_invalidation::emitter::CacheInvalidationEventEmitterRc;
use cache::cache_invalidation::events::CacheInvalidationEvent;
use cache::expirable_lru_cache::ExpirableLruCache;
use cache::ExpirableLruCacheRc;
use collection_types::network_types::CollectionsPage;
use collections_ui::types::cache_config::{
    collection_page_ttl_resolver, COLLECTION_PAGE_CACHE_SIZE,
};
use common_transform_types::collection::CollectionResponse;
use common_transform_types::search_results::SearchResultsResponse;
use common_transform_types::suggestions::SuggestionsResponse;
use ignx_compositron::context::AppContext;
use ignx_compositron::prelude::{provide_context, Signal};
use ignx_compositron::reactive::create_effect;
use ignx_compositron::reactive::SignalWith;
use linear::network::cache_config::{live_page_ttl_resolver, LIVE_PAGE_CACHE_SIZE};
use linear::network::types::LivePageResponseResourceLrc;
use mockall_double::double;
use navigation_menu::model::nav_model::TopNavData;
#[double]
use resiliency_store::use_resiliency_store;
use resiliency_store::ResiliencyStoreData;
use std::cell::RefCell;
use std::hash::Hash;
use std::rc::Rc;
use std::time::Duration;

const SEARCH_RESULTS_CACHE_SIZE: usize = 5;
const SEARCH_SUGGESTIONS_CACHE_SIZE: usize = 5;
const COLLECTION_CACHE_SIZE: usize = 1; // Currently only used for initial landing page on search
const SEARCH_TTL: Duration = Duration::from_secs(600); // 10 minutes

#[derive(Clone)]
pub struct PageCache {
    collection_page_cache: ExpirableLruCacheRc<String, CollectionsPage>,
    pub collection_page_cache_buster_counter: Rc<RefCell<Option<u32>>>,
    pub live_page_cache: ExpirableLruCacheRc<String, LivePageResponseResourceLrc>,
    pub search_results_cache: ExpirableLruCacheRc<String, SearchResultsResponse>,
    pub search_suggestions_cache: ExpirableLruCacheRc<String, SuggestionsResponse>,
    pub collection_cache: ExpirableLruCacheRc<String, CollectionResponse>,
}

impl PageCache {
    fn new() -> PageCache {
        PageCache {
            collection_page_cache: ExpirableLruCache::new_rc(
                COLLECTION_PAGE_CACHE_SIZE,
                Box::new(collection_page_ttl_resolver),
            ),
            live_page_cache: ExpirableLruCache::new_rc(
                LIVE_PAGE_CACHE_SIZE,
                Box::new(live_page_ttl_resolver),
            ),
            search_results_cache: ExpirableLruCache::new_rc(
                SEARCH_RESULTS_CACHE_SIZE,
                Box::new(|_| SEARCH_TTL),
            ),
            search_suggestions_cache: ExpirableLruCache::new_rc(
                SEARCH_SUGGESTIONS_CACHE_SIZE,
                Box::new(|_| SEARCH_TTL),
            ),
            collection_cache: ExpirableLruCache::new_rc(
                COLLECTION_CACHE_SIZE,
                Box::new(|_| SEARCH_TTL),
            ),
            collection_page_cache_buster_counter: Rc::new(RefCell::new(None)),
        }
    }

    // FIXME: the event emitter shouldn't be an Option, but if it is required then we must include
    // an RPC manager to initialise it, and introducing a double import for the RPC manager in the
    // app has a bunch of cascading effects
    pub fn initialize_page_cache_context(
        ctx: &AppContext,
        top_nav_data: Signal<TopNavData>,
        cache_invalidation_event_emitter_rc: Option<CacheInvalidationEventEmitterRc>,
    ) -> PageCache {
        let page_cache = PageCache::new();
        let scope = ctx.scope();
        provide_context(scope, Rc::clone(&page_cache.collection_page_cache));
        provide_context(scope, Rc::clone(&page_cache.live_page_cache));
        provide_context(scope, Rc::clone(&page_cache.search_results_cache));
        provide_context(scope, Rc::clone(&page_cache.search_suggestions_cache));
        provide_context(scope, Rc::clone(&page_cache.collection_cache));
        provide_context(
            scope,
            Self::create_cache_control(
                ctx,
                top_nav_data,
                page_cache.clone(),
                cache_invalidation_event_emitter_rc,
            ),
        );

        page_cache
    }

    fn check_and_clear_cache_on_resiliency_store_update(
        page_cache: PageCache,
        resiliency_store: &ResiliencyStoreData,
    ) {
        // This function can be called at any time before or after the `ClientResiliencyConfig` has
        // been initialized in AVLRC.
        // When the config is initialized in AVLRC, we just update `page_cache.collection_page_cache_buster_counter`
        // with that. Then, from next update onwards, any change in the counter value should invalidate the cache.
        let new_cache_buster_counter = resiliency_store.collectionPageCacheBusterCounter.counter;

        if let Some(new_cache_buster_counter) = new_cache_buster_counter {
            let current_cache_buster_counter =
                *page_cache.collection_page_cache_buster_counter.borrow();

            if let Some(current_cache_buster_counter) = current_cache_buster_counter {
                // There is already a value for the current cache buster counter so, we can check for invalidation.
                if current_cache_buster_counter != new_cache_buster_counter {
                    Self::clear_all_cache(&page_cache);
                }
            }

            let mut counter = page_cache.collection_page_cache_buster_counter.borrow_mut();
            *counter = Some(new_cache_buster_counter);
        }
    }

    fn initialize_cache_invalidation_resiliency_mechanism(ctx: &AppContext, page_cache: PageCache) {
        let scope = ctx.scope();
        let page_cache_clone = page_cache;
        let resiliency_store = use_resiliency_store(scope);
        // Set up a listener for updates
        create_effect(scope, move |_| {
            let page_cache = page_cache_clone.clone();
            resiliency_store.get_read_signal().try_with(move |s| {
                Self::check_and_clear_cache_on_resiliency_store_update(page_cache, s);
            });
        });
    }

    fn initialize_cache_invalidation_on_event(
        event_emitter: Option<CacheInvalidationEventEmitterRc>,
        ctx: &AppContext,
        page_cache: PageCache,
    ) {
        let Some(event_emitter) = event_emitter else {
            return;
        };

        let on_event_emitted = move |event: CacheInvalidationEvent| match event {
            CacheInvalidationEvent::DroppedCard
            | CacheInvalidationEvent::WatchlistToggled
            | CacheInvalidationEvent::Inactivity
            | CacheInvalidationEvent::TitleReaction => {
                Self::clear_cache(&page_cache.collection_page_cache, "Collection Page");
            }
            CacheInvalidationEvent::LegacyUndefinedReasonFromJS
            | CacheInvalidationEvent::TransitionedToPlayback
            | CacheInvalidationEvent::AcquisitionStart
            | CacheInvalidationEvent::AuthChange
            | CacheInvalidationEvent::LocaleChange
            | CacheInvalidationEvent::SyncContent => Self::clear_all_cache(&page_cache),
            CacheInvalidationEvent::ProfileChange => {}
        };

        event_emitter
            .borrow_mut()
            .subscribe(ctx.scope(), Box::new(on_event_emitted));
    }

    pub fn create_cache_control(
        ctx: &AppContext,
        top_nav_data: Signal<TopNavData>,
        page_cache: PageCache,
        event_emitter: Option<CacheInvalidationEventEmitterRc>,
    ) -> CacheControl {
        Self::initialize_cache_invalidation_resiliency_mechanism(ctx, page_cache.clone());
        Self::initialize_cache_invalidation_on_event(event_emitter, ctx, page_cache.clone());
        CacheControl {
            prefetch_home_page: Rc::new({
                let ctx = ctx.clone();
                {
                    let collection_page_cache = Rc::clone(&page_cache.collection_page_cache);
                    move || {
                        prefetch_home_page(&ctx, &collection_page_cache);
                    }
                }
            }),
            prefetch_top_nav_pages: Rc::new({
                let ctx = ctx.clone();
                {
                    let collection_page_cache = Rc::clone(&page_cache.collection_page_cache);
                    let live_page_cache = Rc::clone(&page_cache.live_page_cache);
                    move |active_profile_is_adult| {
                        prefetch_top_nav_pages(
                            &ctx,
                            top_nav_data,
                            &collection_page_cache,
                            &live_page_cache,
                            active_profile_is_adult,
                        );
                    }
                }
            }),
            clear: Rc::new(move || {
                Self::clear_all_cache(&page_cache);
            }),
        }
    }

    fn clear_all_cache(page_cache: &PageCache) {
        Self::clear_cache(&page_cache.collection_page_cache, "Collection Page");
        Self::clear_cache(&page_cache.live_page_cache, "Live Page");
        Self::clear_cache(&page_cache.search_results_cache, "Search Results");
        Self::clear_cache(&page_cache.search_suggestions_cache, "Search Suggestions");
        Self::clear_cache(&page_cache.collection_cache, "Collection");
    }

    fn clear_cache<K, V>(page_cache: &ExpirableLruCacheRc<K, V>, cache_name: &str)
    where
        K: Hash + Eq + Clone,
    {
        log::info!("Clearing {} cache", cache_name);
        page_cache.borrow_mut().clear();
    }
}

#[cfg(test)]
mod tests {
    use cache::cache_invalidation::emitter::CacheInvalidationEventEmitter;
    use common_transform_types::page_metadata::PageMetadata;
    use common_transform_types::resiliency::WithResiliency;
    use common_transform_types::subtitle::SubTitle;
    use common_transform_types::subtitle::SubTitleType;
    use common_transform_types::suggestions::SuggestionsResponse;
    use ignx_compositron::app::launch_only_app_context;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::reactive::{create_signal, use_context, Scope, SignalSet, WriteSignal};
    use ignx_compositron::rectangle::*;
    use ignx_compositron::text::TextContent;
    use navigation_menu::model::nav_model::{TopNavStaticItem, TransitionAction};
    use network_parser::core::NetworkOptional;
    use network_parser::core::NetworkVec;
    use resiliency_store::MockResiliencyStore;
    use rstest::rstest;

    use super::*;

    #[test]
    fn should_initialize_individual_caches_in_app_context() {
        launch_test(
            |ctx| {
                setup_resiliency_store(ctx.scope(), None);
                PageCache::initialize_page_cache_context(&ctx, top_nav_data(ctx.scope), None);
                compose! {
                    Rectangle()
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();

                let collections_page_cache =
                    use_context::<ExpirableLruCacheRc<String, CollectionsPage>>(scope);
                assert!(collections_page_cache.is_some());

                let live_page_cache =
                    use_context::<ExpirableLruCacheRc<String, LivePageResponseResourceLrc>>(scope);
                assert!(live_page_cache.is_some());

                let search_results_cache =
                    use_context::<ExpirableLruCacheRc<String, SearchResultsResponse>>(scope);
                assert!(search_results_cache.is_some());

                let search_suggestions_cache =
                    use_context::<ExpirableLruCacheRc<String, SuggestionsResponse>>(scope);
                assert!(search_suggestions_cache.is_some());

                let collection_cache =
                    use_context::<ExpirableLruCacheRc<String, CollectionResponse>>(scope);
                assert!(collection_cache.is_some());
            },
        );
    }

    #[rstest]
    #[case(Some(42), Some(42), false)]
    #[case(Some(42), Some(43), true)]
    #[case(None, Some(42), false)]
    fn should_clear_cache_if_resiliency_config_update_changes_counter(
        #[case] init_counter: Option<u32>,
        #[case] updated_counter: Option<u32>,
        #[case] should_clear_cache: bool,
    ) {
        launch_only_app_context(move |ctx| {
            let scope = ctx.scope();
            setup_resiliency_store(scope, init_counter);
            PageCache::initialize_page_cache_context(&ctx, top_nav_data(ctx.scope), None);
            let collections_page_cache =
                use_context::<ExpirableLruCacheRc<String, CollectionsPage>>(scope).unwrap();
            let live_page_cache =
                use_context::<ExpirableLruCacheRc<String, LivePageResponseResourceLrc>>(scope)
                    .unwrap();
            let search_results_cache =
                use_context::<ExpirableLruCacheRc<String, SearchResultsResponse>>(scope).unwrap();
            let search_suggestions_cache =
                use_context::<ExpirableLruCacheRc<String, SuggestionsResponse>>(scope).unwrap();
            let collection_cache =
                use_context::<ExpirableLruCacheRc<String, CollectionResponse>>(scope).unwrap();

            collections_page_cache
                .borrow_mut()
                .put(key(), some_collections_page());
            assert!(collections_page_cache.borrow_mut().get(&key()).is_some());

            live_page_cache.borrow_mut().put(key(), some_live_page());
            assert!(live_page_cache.borrow_mut().get(&key()).is_some());

            search_results_cache
                .borrow_mut()
                .put(key(), some_search_results_response());
            assert!(search_results_cache.borrow_mut().get(&key()).is_some());

            search_suggestions_cache
                .borrow_mut()
                .put(key(), some_suggestions_response());
            assert!(search_suggestions_cache.borrow_mut().get(&key()).is_some());

            collection_cache
                .borrow_mut()
                .put(key(), some_collection_response());
            assert!(collection_cache.borrow_mut().get(&key()).is_some());

            let mut mock_new_resiliency_config = ResiliencyStoreData::default();
            mock_new_resiliency_config
                .collectionPageCacheBusterCounter
                .counter = updated_counter;
            let mock_write_signal = use_context::<WriteSignal<ResiliencyStoreData>>(scope).unwrap();
            mock_write_signal.set(mock_new_resiliency_config);

            if should_clear_cache {
                assert!(collections_page_cache.borrow_mut().get(&key()).is_none());
                assert!(live_page_cache.borrow_mut().get(&key()).is_none());
                assert!(search_results_cache.borrow_mut().get(&key()).is_none());
                assert!(search_suggestions_cache.borrow_mut().get(&key()).is_none());
                assert!(collection_cache.borrow_mut().get(&key()).is_none());
            } else {
                assert!(collections_page_cache.borrow_mut().get(&key()).is_some());
                assert!(live_page_cache.borrow_mut().get(&key()).is_some());
                assert!(search_results_cache.borrow_mut().get(&key()).is_some());
                assert!(search_suggestions_cache.borrow_mut().get(&key()).is_some());
                assert!(collection_cache.borrow_mut().get(&key()).is_some());
            }
        })
    }

    // FIXME: Re-add and fix this test once we've solved the mock RPC issue. Add cases for all and collections page only
    // #[test]
    // fn should_clear_all_cache_on_cache_invalidation_event() {
    //     launch_test(
    //         |ctx| {
    //             setup_resiliency_store(ctx.scope(), None);
    //             let cache_invalidation_event_emitter = setup_cache_invalidation_emitter(ctx.scope());
    //             let page_cache = PageCache::initialize_page_cache_context(
    //                 &ctx,
    //                 top_nav_data(ctx.scope),
    //                 Some(cache_invalidation_event_emitter),
    //             );
    //             compose! {
    //                 Rectangle()
    //             }
    //         },
    //         |scope, mut test_game_loop| {
    //             test_game_loop.tick_until_done();
    //
    //             let collections_page_cache =
    //                 use_context::<ExpirableLruCacheRc<String, CollectionsPage>>(scope).unwrap();
    //             let live_page_cache =
    //                 use_context::<ExpirableLruCacheRc<String, LivePageResponseResourceLrc>>(scope)
    //                     .unwrap();
    //
    //             collections_page_cache
    //                 .borrow_mut()
    //                 .put(key(), some_collections_page());
    //             assert!(collections_page_cache.borrow_mut().get(&key()).is_some());
    //
    //             live_page_cache.borrow_mut().put(key(), some_live_page());
    //             assert!(live_page_cache.borrow_mut().get(&key()).is_some());
    //
    //             let event_emitter = use_context::<CacheInvalidationEventEmitterRc>(scope).expect("expected event emitter");
    //             event_emitter.borrow().emit(CacheInvalidationEvent::AuthChange);
    //
    //             assert!(collections_page_cache.borrow_mut().get(&key()).is_none());
    //             assert!(live_page_cache.borrow_mut().get(&key()).is_none());
    //         }
    //     );
    // }

    #[rstest]
    #[case(CacheInvalidationEvent::DroppedCard)]
    #[case(CacheInvalidationEvent::WatchlistToggled)]
    #[case(CacheInvalidationEvent::Inactivity)]
    #[case(CacheInvalidationEvent::TitleReaction)]
    fn should_clear_only_collection_page_cache_on_event(#[case] event: CacheInvalidationEvent) {
        launch_only_app_context(move |ctx| {
            // Only collections page cache is cleared
            let options: ClearCacheTestOptions = ClearCacheTestOptions {
                event,
                clear_collections_page_cache: true,
                clear_live_page_cache: false,
                clear_search_results_cache: false,
                clear_search_suggestions_cache: false,
                clear_collection_cache: false,
            };
            run_clear_cache_test(&ctx, options);
        });
    }

    #[rstest]
    #[case(CacheInvalidationEvent::LegacyUndefinedReasonFromJS)]
    #[case(CacheInvalidationEvent::TransitionedToPlayback)]
    #[case(CacheInvalidationEvent::AcquisitionStart)]
    #[case(CacheInvalidationEvent::AuthChange)]
    #[case(CacheInvalidationEvent::LocaleChange)]
    #[case(CacheInvalidationEvent::SyncContent)]
    fn should_clear_all_caches_on_event(#[case] event: CacheInvalidationEvent) {
        launch_only_app_context(move |ctx| {
            // All caches are cleared
            let options: ClearCacheTestOptions = ClearCacheTestOptions {
                event,
                clear_collections_page_cache: true,
                clear_live_page_cache: true,
                clear_search_results_cache: true,
                clear_search_suggestions_cache: true,
                clear_collection_cache: true,
            };
            run_clear_cache_test(&ctx, options);
        });
    }

    #[test]
    fn should_not_clear_any_cache_on_profile_change_event() {
        launch_only_app_context(move |ctx| {
            // No caches are cleared
            let options: ClearCacheTestOptions = ClearCacheTestOptions {
                event: CacheInvalidationEvent::ProfileChange,
                clear_collections_page_cache: false,
                clear_live_page_cache: false,
                clear_search_results_cache: false,
                clear_search_suggestions_cache: false,
                clear_collection_cache: false,
            };
            run_clear_cache_test(&ctx, options);
        });
    }

    #[test]
    fn prefetch_pages() {
        // TODO: implement test
    }

    #[test]
    fn clear_all_cache() {
        launch_test(
            |ctx| {
                setup_resiliency_store(ctx.scope(), None);
                PageCache::initialize_page_cache_context(&ctx, top_nav_data(ctx.scope), None);
                compose! {
                    Rectangle()
                }
            },
            |scope, mut test_game_loop| {
                test_game_loop.tick_until_done();

                let collections_page_cache =
                    use_context::<ExpirableLruCacheRc<String, CollectionsPage>>(scope).unwrap();
                let live_page_cache =
                    use_context::<ExpirableLruCacheRc<String, LivePageResponseResourceLrc>>(scope)
                        .unwrap();
                let search_results_cache =
                    use_context::<ExpirableLruCacheRc<String, SearchResultsResponse>>(scope)
                        .unwrap();
                let search_suggestions_cache =
                    use_context::<ExpirableLruCacheRc<String, SuggestionsResponse>>(scope).unwrap();
                let collection_cache =
                    use_context::<ExpirableLruCacheRc<String, CollectionResponse>>(scope).unwrap();

                // Collection pages
                collections_page_cache
                    .borrow_mut()
                    .put(key(), some_collections_page());
                assert!(collections_page_cache.borrow_mut().get(&key()).is_some());

                // Live page
                live_page_cache.borrow_mut().put(key(), some_live_page());
                assert!(live_page_cache.borrow_mut().get(&key()).is_some());

                // Search results
                search_results_cache
                    .borrow_mut()
                    .put(key(), some_search_results_response());
                assert!(search_results_cache.borrow_mut().get(&key()).is_some());

                // Search suggestions
                search_suggestions_cache
                    .borrow_mut()
                    .put(key(), some_suggestions_response());
                assert!(search_suggestions_cache.borrow_mut().get(&key()).is_some());

                // Collection responses
                collection_cache
                    .borrow_mut()
                    .put(key(), some_collection_response());
                assert!(collection_cache.borrow_mut().get(&key()).is_some());

                let cache_control = use_context::<CacheControl>(scope).unwrap();
                (cache_control.clear)();

                assert!(collections_page_cache.borrow_mut().get(&key()).is_none());
                assert!(live_page_cache.borrow_mut().get(&key()).is_none());
                assert!(search_results_cache.borrow_mut().get(&key()).is_none());
                assert!(search_suggestions_cache.borrow_mut().get(&key()).is_none());
                assert!(collection_cache.borrow_mut().get(&key()).is_none());
            },
        );
    }

    #[test]
    fn should_hold_values_for_search_consts() {
        assert_eq!(SEARCH_RESULTS_CACHE_SIZE, 5);
        assert_eq!(SEARCH_SUGGESTIONS_CACHE_SIZE, 5);
        assert_eq!(COLLECTION_CACHE_SIZE, 1);
        assert_eq!(SEARCH_TTL, Duration::from_secs(600));
    }

    fn top_nav_data(scope: Scope) -> Signal<TopNavData> {
        create_signal(
            scope,
            TopNavData::new(
                vec![TopNavStaticItem {
                    id: "1".to_string(),
                    label: TextContent::String("label".to_string()),
                    action: TransitionAction::GoToCollectionPage {
                        page_type: "some_type".to_string(),
                        page_id: "some_id".to_string(),
                        service_token: Some("service_token".to_string()),
                        ref_marker: "ref_marker".into(),
                    },
                    clickstream_sub_page: "mock_sub_page".to_string(),
                    widget_type: None,
                }],
                vec![],
                None,
                Default::default(),
                Rc::new(|_| {}),
                true,
            ),
        )
        .0
        .into()
    }

    fn some_collections_page() -> CollectionsPage {
        CollectionsPage {
            containerList: vec![],
            paginationLink: WithResiliency::Ok(NetworkOptional::None),
            subNav: WithResiliency::Ok(NetworkVec::from(vec![])),
            pageTitle: WithResiliency::Ok(NetworkOptional::None),
            pageMetadata: WithResiliency::Ok(NetworkOptional::None),
            marketingModalData: WithResiliency::Ok(NetworkOptional::None),
            matchFound: WithResiliency::Ok(NetworkOptional::None),
            onboardingModalData: WithResiliency::Ok(NetworkOptional::None),
            isPriorityCenter: WithResiliency::Ok(NetworkOptional::None),
            voiceHints: WithResiliency::Ok(NetworkOptional::None),
            page_id: WithResiliency::Ok(NetworkOptional::None),
            surveyData: None,
        }
    }

    fn some_live_page() -> LivePageResponseResourceLrc {
        LivePageResponseResourceLrc {
            containerList: vec![],
            hero: WithResiliency::Ok(NetworkOptional::None),
            paginationLink: WithResiliency::Ok(NetworkOptional::None),
            subNav: vec![],
        }
    }

    fn some_search_results_response() -> SearchResultsResponse {
        SearchResultsResponse {
            containers: vec![],
            filters: vec![],
            subtitle: SubTitle {
                text: "Some text".into(),
                title_type: SubTitleType::SearchMessage,
            },
        }
    }

    fn some_suggestions_response() -> SuggestionsResponse {
        SuggestionsResponse {
            suggestions: vec![],
        }
    }

    fn some_collection_response() -> CollectionResponse {
        CollectionResponse {
            container_list: vec![],
            page_metadata: PageMetadata::default(),
        }
    }

    fn key() -> String {
        "some_key".to_string()
    }

    fn setup_resiliency_store(scope: Scope, init_counter: Option<u32>) {
        let mut mock_resiliency_store = MockResiliencyStore::default();
        let mut mock_resiliency_store_clone = MockResiliencyStore::default();
        let mut mock_resiliency_config = ResiliencyStoreData::default();
        mock_resiliency_config
            .collectionPageCacheBusterCounter
            .counter = init_counter;
        let (mock_read_signal, mock_write_signal) = create_signal(scope, mock_resiliency_config);
        provide_context(scope, mock_read_signal);
        provide_context(scope, mock_write_signal);
        mock_resiliency_store_clone
            .expect_get_read_signal()
            .returning(move || mock_read_signal);
        mock_resiliency_store
            .expect_clone()
            .return_once(|| mock_resiliency_store_clone);

        provide_context::<MockResiliencyStore>(scope, mock_resiliency_store);
    }

    struct ClearCacheTestOptions {
        event: CacheInvalidationEvent,
        clear_collections_page_cache: bool,
        clear_live_page_cache: bool,
        clear_search_results_cache: bool,
        clear_search_suggestions_cache: bool,
        clear_collection_cache: bool,
    }

    fn run_clear_cache_test(ctx: &AppContext, options: ClearCacheTestOptions) {
        let scope = ctx.scope();
        setup_resiliency_store(scope, None);
        let cache_invalidation_event_emitter = CacheInvalidationEventEmitter::new_rc_without_rpc();

        let page_cache =
            PageCache::initialize_page_cache_context(ctx, top_nav_data(ctx.scope), None);
        let collections_page_cache =
            use_context::<ExpirableLruCacheRc<String, CollectionsPage>>(scope).unwrap();
        let live_page_cache =
            use_context::<ExpirableLruCacheRc<String, LivePageResponseResourceLrc>>(scope).unwrap();
        let search_results_cache =
            use_context::<ExpirableLruCacheRc<String, SearchResultsResponse>>(scope).unwrap();
        let search_suggestions_cache =
            use_context::<ExpirableLruCacheRc<String, SuggestionsResponse>>(scope).unwrap();
        let collection_cache =
            use_context::<ExpirableLruCacheRc<String, CollectionResponse>>(scope).unwrap();

        collections_page_cache
            .borrow_mut()
            .put(key(), some_collections_page());
        live_page_cache.borrow_mut().put(key(), some_live_page());
        search_results_cache
            .borrow_mut()
            .put(key(), some_search_results_response());
        search_suggestions_cache
            .borrow_mut()
            .put(key(), some_suggestions_response());
        collection_cache
            .borrow_mut()
            .put(key(), some_collection_response());

        PageCache::initialize_cache_invalidation_on_event(
            Some(cache_invalidation_event_emitter.clone()),
            ctx,
            page_cache.clone(),
        );

        cache_invalidation_event_emitter
            .borrow()
            .emit(options.event);

        if options.clear_collections_page_cache {
            assert!(collections_page_cache.borrow_mut().get(&key()).is_none());
        } else {
            assert!(collections_page_cache.borrow_mut().get(&key()).is_some());
        }

        if options.clear_live_page_cache {
            assert!(live_page_cache.borrow_mut().get(&key()).is_none());
        } else {
            assert!(live_page_cache.borrow_mut().get(&key()).is_some());
        }

        if options.clear_search_results_cache {
            assert!(search_results_cache.borrow_mut().get(&key()).is_none());
        } else {
            assert!(search_results_cache.borrow_mut().get(&key()).is_some());
        }

        if options.clear_search_suggestions_cache {
            assert!(search_suggestions_cache.borrow_mut().get(&key()).is_none());
        } else {
            assert!(search_suggestions_cache.borrow_mut().get(&key()).is_some());
        }

        if options.clear_collection_cache {
            assert!(collection_cache.borrow_mut().get(&key()).is_none());
        } else {
            assert!(collection_cache.borrow_mut().get(&key()).is_some());
        }
    }

    // FIXME: Uncomment this once we've solved the mock RPC issue
    // fn setup_cache_invalidation_emitter(scope: Scope) -> CacheInvalidationEventEmitterRc {
    //     let mut rpc_manager = MockRPCManager::new();
    //
    //     rpc_manager.expect_call().times(1).return_once(move |_| {
    //         let mut call = MockRPCCall::<CacheInvalidationRPCResponse>::new();
    //         call.expect_arg().return_once(move |_, _| {
    //             let mut call = MockRPCCall::<CacheInvalidationRPCResponse>::new();
    //             call.expect_error_callback().return_once(move |_| {
    //                 let mut call = MockRPCCall::<CacheInvalidationRPCResponse>::new();
    //                 call.expect_send().return_once(|| ());
    //                 call
    //             });
    //             call
    //         });
    //         call
    //     });
    //     let event_emitter = CacheInvalidationEventEmitter::new_rc(rpc_manager);
    //
    //     provide_context(scope, event_emitter.clone());
    //
    //     event_emitter
    // }
}
