use checkout::ui::checkout_page::*;
use collections_ui::page::collections_page::*;
use common_transform_types::profile::Profile;
use details::ui::details_page::*;
use discovery_assistant::discovery_page::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use firetv_tvif::page::*;
use go_to_background::page_ui::*;
use ignx_compositron::button::*;
use ignx_compositron::color::Color;
use ignx_compositron::column::*;
use ignx_compositron::composable::*;
use ignx_compositron::context::AppContext;
use ignx_compositron::focusable::*;
use ignx_compositron::label::*;
use ignx_compositron::player::PlayerCommand;
use ignx_compositron::prelude::use_context;
use ignx_compositron::reactive::SignalSet;
use ignx_compositron::reactive::{RwSignal, Signal, WriteSignal};
use ignx_compositron::stack::*;
use ignx_compositron::{compose, Composer};
use linear::live_page::live_page_ui::*;
use linear::station_details::station_details_page::*;
use linear::utils::common_string_ids::AV_LRC_LIVE_TV_PAGE_LOAD;
use location::{PageType, RustPage};
use media_background::types::MediaBackgroundType;
use modal_manager::modal_manager::AppModal;
use navigation_menu::model::nav_model::TopNavData;
use playback_page::ui::*;
use profile_selection::page::profile_selection_page::*;
use profile_selection::page::avatar_selection_page::*;
use router::{hooks::use_navigation, route::*, rust_location};
use search_page::components::page_wrapper::*;
use sports_favorites_ui::ui::favorites_discovery_page::*;
use sports_schedule::ui::sports_schedule_page::*;
use title_details::core::signals::TitleDetailsSignals;

// NOTE: We'll re-think the page concept all together and lean towards single-page
// fluid application where components are not strictly belong to a page but can be
// added/removed from the scene if needed.
#[Composer]
pub fn Pages(
    ctx: &AppContext,
    update_media_background_rw: RwSignal<MediaBackgroundType>,
    title_details_signals: TitleDetailsSignals,
    top_nav_data: Signal<TopNavData>,
    modal_data: WriteSignal<Vec<AppModal>>,
    active_profile: Signal<Option<Profile>>,
) -> StackComposable {
    let update_media_background = update_media_background_rw.write_only();
    let update_title_details = title_details_signals.data.write_only();

    compose! {
        Stack() {
            Route(page_type: PageType::Rust(RustPage::RUST_EMPTY), f: |ctx| {
                let scope = ctx.scope();
                compose! {
                    Column() {
                        Label(text: "This page is intentionally empty").color(Color::white())
                        Button(text:"Return to Homepage").on_select(move || {
                            let navigate = use_navigation(scope);
                            navigate(rust_location!(RUST_COLLECTIONS), "RUST_EMPTY");
                        })
                    }
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_PROFILE_SELECTION), f: move |ctx| {
                compose! {
                    ProfileSelectionPage(update_media_background, update_title_details)
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_AVATAR_SELECTION), f: move |ctx| {
                compose! {
                    AvatarSelectionPage()
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_COLLECTIONS), f: move |ctx| {
                compose! {
                    CollectionsPage(update_media_background_rw, update_title_details, top_nav_data, modal_data, active_profile)
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_DETAILS), f: move |ctx| {
                compose! {
                    DetailsPage(
                        update_media_background_rw,
                        title_details_signals: &title_details_signals,
                        app_modal_data: modal_data
                    )
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_LIVE_TV), f: move |ctx| {
                if let Some(live_page_player_command_context) = use_context::<RwSignal<Option<PlayerCommand>>>(ctx.scope()){
                    live_page_player_command_context.set(Some(PlayerCommand::SendMetric(AV_LRC_LIVE_TV_PAGE_LOAD.to_string())));
                }
                compose! {
                    LivePage(update_media_background, update_title_details)
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_STATION_DETAILS), f: move |ctx| {
                compose! {
                    StationDetailsPage(
                        update_media_background,
                        update_title_details,
                    )
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_DISCOVERY_PAGE), f: move |ctx| {
                compose! {
                    DiscoveryPage(update_media_background, update_title_details)
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_PLAYBACK), f: move |ctx| {
                compose! {
                    PlaybackPage(
                        update_media_background,
                        update_title_details,
                    )
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_CHECKOUT), f: move |ctx| {
                compose! {
                    CheckoutPage()
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_SEARCH), f: move |ctx| {
                compose! {
                    SearchPage(
                        update_media_background,
                        update_title_details,
                    )
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_SPORTS_FAVORITES_DISCOVERY), f: move |ctx| {
                compose! {
                    FavoritesDiscoveryPage(
                        update_media_background,
                        update_title_details
                    )
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_SPORTS_SCHEDULE_PAGE), f: move |ctx| {
                compose! {
                    SportsSchedulePage()
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_GO_TO_BACKGROUND), f: move |ctx| {
                compose! {
                    GoToBackground()
                }
            })
            Route(page_type: PageType::Rust(RustPage::RUST_TVIF), f: move |ctx| {
                compose! {
                    TvifPage()
                }
            })
        }
        .width(SCREEN_WIDTH)
        .height(SCREEN_HEIGHT)
    }
}
