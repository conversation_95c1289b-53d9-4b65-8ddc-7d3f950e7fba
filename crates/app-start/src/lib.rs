mod app;
mod app_container;
mod app_container_signals;
mod app_interactions;
mod cache;
mod pages;

use app::App;
use app_container::{AppContainer, AppContainerProps};
use ignx_compositron::compose;
use ignx_compositron::{app::launch_app, AppLayer};

#[cfg(all(not(feature = "no_run_main"), not(target_arch = "wasm32")))]
#[allow(unsafe_code, reason = "app start")]
#[no_mangle]
extern "C" fn _start_game_loop() {
    run_app();
}

pub fn run_app() {
    #[cfg(not(feature = "standalone_app"))]
    let initial_active_layer = AppLayer::JavaScript;
    #[cfg(feature = "standalone_app")]
    let initial_active_layer = AppLayer::Wasm;
    launch_app(
        |scope,
         layer_switcher,
         cross_app_command_sender,
         localization_manager,
         rpc_manager,
         cross_app_events_reporter| {
            App::new(
                scope,
                layer_switcher,
                localization_manager,
                cross_app_command_sender,
                rpc_manager,
                cross_app_events_reporter,
            )
        },
        |ctx, app| {
            compose! {
                AppContainer(app)
            }
        },
        initial_active_layer,
    );
}
