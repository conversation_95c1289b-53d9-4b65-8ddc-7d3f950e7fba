[package]
name = "ads-playback-types"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[features]
debug_impl = []
test_utils = []

[dependencies]
network-parser.workspace = true
network-parser-derive.workspace = true
serde.workspace = true
cfg-test-attr-derive.workspace = true
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }

[dev-dependencies]
rstest.workspace = true
player = { workspace = true, features = ["test_utils"] }

[lints]
workspace = true
