# Ads Playback Types

## Surface X Orchestration Service (SOS) Models

The `sos_request.rs` and `sos_response` files contain Rust type definitions derived from the [PVSurfaceXOrchestrationServiceModel](https://code.amazon.com/packages/PVSurfaceXOrchestrationServiceModel) Smithy models.
These types are used for interacting with the Surface X Orchestration Service, which manages ad-related UI templates for several features such as IVAs, Ad Feedback, etc.
