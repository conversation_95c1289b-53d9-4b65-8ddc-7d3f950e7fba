use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;

#[derive(NetworkParsed)]
pub enum SurfaceXTemplateType {
    BaselineIVA,
    AdFeedback,
}

#[derive(NetworkParsed)]
pub struct AnimationSettings {
    #[network(rename = "entryAnimation")]
    pub entry_animation: String,

    #[network(rename = "exitAnimation")]
    pub exit_animation: String,
}

#[derive(NetworkParsed)]
pub struct BaseSurfaceXTemplateProps {
    #[network(rename = "mandatoryFields")]
    pub mandatory_fields: Vec<String>,

    pub animation: Option<AnimationSettings>,

    #[network(rename = "accessibilityMessages")]
    pub accessibility_messages: Option<HashMap<String, String>>,
}

#[derive(NetworkParsed)]
pub struct BaseSurfaceXTemplate {
    #[network(rename = "templateType")]
    pub template_type: SurfaceXTemplateType,

    #[network(rename = "templateProps")]
    pub template_props: BaseSurfaceXTemplateProps,
}

#[cfg(test)]
mod tests {
    use super::*;
    use network_parser::core::network_parse_from_str;

    #[test]
    fn test_deserialize_base_surface_x_template() {
        let json_str = r#"{
            "templateType": "BaselineIVA",
            "templateProps": {
                "mandatoryFields": ["title", "product.price", "product.rating"],
                "animation": {
                    "entryAnimation": "FadeIn",
                    "exitAnimation": "FadeOut"
                },
                "accessibilityMessages": {
                    "onFocus": "Press OK to add to cart",
                    "onClick": "Item will be added to cart"
                }
            }
        }"#;

        let template = network_parse_from_str::<BaseSurfaceXTemplate>(json_str).unwrap();

        assert!(matches!(
            template.template_type,
            SurfaceXTemplateType::BaselineIVA
        ));
        assert_eq!(
            template.template_props.mandatory_fields,
            vec!["title", "product.price", "product.rating"]
        );

        let animation = template.template_props.animation.unwrap();
        assert_eq!(animation.entry_animation, "FadeIn");
        assert_eq!(animation.exit_animation, "FadeOut");

        let messages = template.template_props.accessibility_messages.unwrap();
        assert_eq!(
            messages.get("onFocus"),
            Some(&"Press OK to add to cart".to_string())
        );
        assert_eq!(
            messages.get("onClick"),
            Some(&"Item will be added to cart".to_string())
        );
    }
}
