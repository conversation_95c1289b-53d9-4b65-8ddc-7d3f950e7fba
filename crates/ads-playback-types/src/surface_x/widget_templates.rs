use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(NetworkParsed)]
#[network(tag = "type")]
pub enum SurfaceXWidgetTemplates {
    TextWidget(TextWidgetTemplate),
    ImageWidget(ImageWidgetTemplate),
    BadgeWidget(BadgeWidgetTemplate),
    IconWidget(IconWidgetTemplate),
    DividerWidget(DividerWidgetTemplate),
    PrimaryButtonWidget(PrimaryButtonWidgetTemplate),
    PrimeLogoWidget(PrimeLogoWidgetTemplate),
    StarRatingWidget(StarRatingWidgetTemplate),
    RowWidget(RowWidgetTemplate),
}

#[derive(NetworkParsed)]
pub enum SurfaceXWidgetTypes {
    TextWidget,
    ImageWidget,
    BadgeWidget,
    IconWidget,
    DividerWidget,
    PrimaryButtonWidget,
    PrimeLogoWidget,
    StarRatingWidget,
    RowWidget,
}

#[derive(NetworkParsed)]
pub struct RowWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: RowWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
#[network(tag = "type")]
pub enum PrimitiveWidgetTemplates {
    TextWidget(TextWidgetTemplate),
    ImageWidget(ImageWidgetTemplate),
    BadgeWidget(BadgeWidgetTemplate),
    IconWidget(IconWidgetTemplate),
    DividerWidget(DividerWidgetTemplate),
    PrimaryButtonWidget(PrimaryButtonWidgetTemplate),
    PrimeLogoWidget(PrimeLogoWidgetTemplate),
    StarRatingWidget(StarRatingWidgetTemplate),
}

#[derive(NetworkParsed)]
pub struct RowWidgetPropsTemplate {
    #[network(rename = "rowComponents")]
    pub row_components: Vec<PrimitiveWidgetTemplates>,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct ImageWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: ImageWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct ImageWidgetPropsTemplate {
    #[network(rename = "imageUrl")]
    pub image_url: String,

    pub width: f32,
    pub height: f32,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct BadgeWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: BadgeWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct BadgeWidgetPropsTemplate {
    pub text: String,

    #[network(rename = "typeRamp")]
    pub type_ramp: Option<String>,

    #[network(rename = "textColor")]
    pub text_color: Option<String>,

    #[network(rename = "backgroundColor")]
    pub background_color: String,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct IconWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: IconWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct IconWidgetPropsTemplate {
    #[network(rename = "iconName")]
    pub icon_name: String,

    pub color: Option<String>,

    #[network(rename = "fontSize")]
    pub font_size: Option<u32>,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct DividerWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: DividerWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct DividerWidgetPropsTemplate {
    pub orientation: String,
    pub length: f32,
    pub color: Option<String>,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct PrimaryButtonWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: PrimaryButtonWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct PrimaryButtonWidgetPropsTemplate {
    pub text: String,

    #[network(rename = "iconName")]
    pub icon_name: Option<String>,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct PrimeLogoWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: PrimeLogoWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct PrimeLogoWidgetPropsTemplate {
    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct StarRatingWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: StarRatingWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct StarRatingWidgetPropsTemplate {
    pub rating: String,

    #[network(rename = "starSize")]
    pub star_size: u32,

    #[network(rename = "fullStarColor")]
    pub full_star_color: String,

    #[network(rename = "emptyStarColor")]
    pub empty_star_color: String,

    #[network(rename = "starSeparatorWidth")]
    pub star_separator_width: f32,

    #[network(rename = "showText")]
    pub show_text: Option<bool>,

    #[network(rename = "ratingTextTypeRamp")]
    pub rating_text_type_ramp: Option<String>,

    #[network(rename = "ratingCount")]
    pub rating_count: String,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
pub struct TextWidgetTemplate {
    #[network(rename = "type")]
    pub type_field: SurfaceXWidgetTypes,
    pub props: TextWidgetPropsTemplate,
}

#[derive(NetworkParsed)]
pub struct TextWidgetPropsTemplate {
    #[network(rename = "typeRamp")]
    pub type_ramp: String,

    pub text: String,
    pub color: Option<String>,

    #[network(rename = "textAlign")]
    pub text_align: Option<String>,

    #[network(rename = "lineHeight")]
    pub line_height: Option<f32>,

    #[network(rename = "ellipsizeMode")]
    pub ellipsize_mode: Option<String>,

    #[network(rename = "numberOfLines")]
    pub number_of_lines: Option<u32>,

    #[network(rename = "textDecorationLine")]
    pub text_decoration_line: Option<String>,

    #[network(rename = "containerStyle")]
    pub container_style: Option<SurfaceXContainerStylesTemplate>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Default)]
pub struct SurfaceXContainerStylesTemplate {
    #[network(rename = "verticalAlignment")]
    pub vertical_alignment: Option<String>,

    #[network(rename = "horizontalAlignment")]
    pub horizontal_alignment: Option<String>,

    pub width: Option<f32>,
    pub height: Option<f32>,

    #[network(rename = "maxHeight")]
    pub max_height: Option<f32>,

    #[network(rename = "minHeight")]
    pub min_height: Option<f32>,

    #[network(rename = "maxWidth")]
    pub max_width: Option<f32>,

    #[network(rename = "minWidth")]
    pub min_width: Option<f32>,

    #[network(rename = "paddingTop")]
    pub padding_top: Option<f32>,

    #[network(rename = "paddingEnd")]
    pub padding_end: Option<f32>,

    #[network(rename = "paddingBottom")]
    pub padding_bottom: Option<f32>,

    #[network(rename = "paddingStart")]
    pub padding_start: Option<f32>,

    #[network(rename = "marginTop")]
    pub margin_top: Option<f32>,

    #[network(rename = "marginEnd")]
    pub margin_end: Option<f32>,

    #[network(rename = "marginBottom")]
    pub margin_bottom: Option<f32>,

    #[network(rename = "marginStart")]
    pub margin_start: Option<f32>,

    #[network(rename = "backgroundColor")]
    pub background_color: Option<String>,

    #[network(rename = "borderColor")]
    pub border_color: Option<String>,

    #[network(rename = "borderWidth")]
    pub border_width: Option<f32>,

    #[network(rename = "borderRadius")]
    pub border_radius: Option<f32>,

    pub opacity: Option<f32>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use network_parser::core::network_parse_from_str;

    #[test]
    fn test_container_styles_default() {
        let default_style = SurfaceXContainerStylesTemplate {
            ..Default::default()
        };

        assert_eq!(default_style.vertical_alignment, None);
        assert_eq!(default_style.horizontal_alignment, None);
        assert_eq!(default_style.width, None);
        assert_eq!(default_style.height, None);
        assert_eq!(default_style.max_height, None);
        assert_eq!(default_style.min_height, None);
        assert_eq!(default_style.max_width, None);
        assert_eq!(default_style.min_width, None);
        assert_eq!(default_style.padding_top, None);
        assert_eq!(default_style.padding_end, None);
        assert_eq!(default_style.padding_bottom, None);
        assert_eq!(default_style.padding_start, None);
        assert_eq!(default_style.margin_top, None);
        assert_eq!(default_style.margin_end, None);
        assert_eq!(default_style.margin_bottom, None);
        assert_eq!(default_style.margin_start, None);
        assert_eq!(default_style.background_color, None);
        assert_eq!(default_style.border_color, None);
        assert_eq!(default_style.border_width, None);
        assert_eq!(default_style.border_radius, None);
        assert_eq!(default_style.opacity, None);
    }

    #[test]
    fn test_parse_text_widget() {
        let json = r#"{ 
          "type": "TextWidget",
          "props": 
            { "text": "text widget", 
              "typeRamp": "label-600", 
              "numberOfLines": 2, 
              "textDecorationLine": "none", 
              "lineHeight": 1.2,
              "textAlign": "start",
              "containerStyle": {
                "width": 10
              }
            } 
          }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::TextWidget(template)) = result {
            assert_eq!(template.props.text, "text widget");
            assert_eq!(template.props.type_ramp, "label-600");
            assert_eq!(template.props.number_of_lines, Some(2));
            assert_eq!(
                template.props.text_decoration_line,
                Some("none".to_string())
            );
            assert_eq!(template.props.line_height, Some(1.2));
            assert_eq!(template.props.text_align, Some("start".to_string()));
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected TextWidget");
        }
    }

    #[test]
    fn test_parse_image_widget() {
        let json = r#"{
          "type": "ImageWidget",
          "props": {
              "imageUrl": "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png",
              "width": 40,
              "height": 40,
              "containerStyle": {
                  "width": 10
              }
          }
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::ImageWidget(template)) = result {
            assert_eq!(
                template.props.image_url,
                "https://m.media-amazon.com/images/G/01/vcc/maturity-ratings-logos/png/bbfc/12.png"
            );
            assert_eq!(template.props.width, 40.0);
            assert_eq!(template.props.height, 40.0);
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected ImageWidget");
        }
    }

    #[test]
    fn test_parse_badge_widget() {
        let json = r#"{ 
          "type": "BadgeWidget",
          "props": 
            { "text": "badge widget", 
              "typeRamp": "label-600",
              "backgroundColor": "fefefe",
              "textColor": "bebebe",
              "containerStyle": {
                "width": 10
              }
            } 
          }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::BadgeWidget(template)) = result {
            assert_eq!(template.props.text, "badge widget");
            assert_eq!(template.props.type_ramp, Some("label-600".to_string()));
            assert_eq!(template.props.background_color, "fefefe");
            assert_eq!(template.props.text_color, Some("bebebe".to_string()));
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected BadgeWidget");
        }
    }

    #[test]
    fn test_parse_icon_widget() {
        let json = r#"{ 
          "type": "IconWidget",
          "props": 
            {
                "iconName": "Delete",
                "color": "d18888",
                "fontSize": 48,
                "containerStyle": {
                  "width": 10
                }
            } 
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::IconWidget(template)) = result {
            assert_eq!(template.props.icon_name, "Delete");
            assert_eq!(template.props.color, Some("d18888".to_string()));
            assert_eq!(template.props.font_size, Some(48));
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected IconWidget");
        }
    }

    #[test]
    fn test_parse_divider_widget() {
        let json = r#"{ 
          "type": "DividerWidget",
          "props": 
            {
                "orientation": "vertical",
                "length": 28,
                "color": "bebebe",
                "containerStyle": {
                  "width": 10
                }
            } 
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::DividerWidget(template)) = result {
            assert_eq!(template.props.orientation, "vertical");
            assert_eq!(template.props.length, 28.0);
            assert_eq!(template.props.color, Some("bebebe".to_string()));
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected DividerWidget");
        }
    }

    #[test]
    fn test_parse_primary_button_widget() {
        let json = r#"{ 
          "type": "PrimaryButtonWidget",
          "props": 
            {
                "text": "Button text",
                "iconName": "Delete",
                "containerStyle": {
                  "width": 10
                }
            } 
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::PrimaryButtonWidget(template)) = result {
            assert_eq!(template.props.text, "Button text");
            assert_eq!(template.props.icon_name, Some("Delete".to_string()));
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected PrimaryButtonWidget");
        }
    }

    #[test]
    fn test_parse_prime_logo_widget() {
        let json = r#"{ 
          "type": "PrimeLogoWidget",
          "props": 
            {
                "containerStyle": {
                  "width": 10
                }
            } 
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::PrimeLogoWidget(template)) = result {
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected PrimeLogoWidget");
        }
    }

    #[test]
    fn test_parse_star_rating_widget() {
        let json = r#"{ 
          "type": "StarRatingWidget",
          "props": 
            {
                "rating": "4.5", 
                "starSize": 20,
                "fullStarColor": "FFD700",
                "emptyStarColor": "CCCCCC",
                "starSeparatorWidth": 2,
                "showText": true,
                "ratingTextTypeRamp": "label-200",
                "ratingCount": "2000",
                "containerStyle": {
                  "width": 10
                }
            } 
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::StarRatingWidget(template)) = result {
            assert_eq!(template.props.rating, "4.5");
            assert_eq!(template.props.star_size, 20);
            assert_eq!(template.props.full_star_color, "FFD700");
            assert_eq!(template.props.empty_star_color, "CCCCCC");
            assert_eq!(template.props.star_separator_width, 2.0);
            assert_eq!(template.props.show_text, Some(true));
            assert_eq!(
                template.props.rating_text_type_ramp,
                Some("label-200".to_string())
            );
            assert_eq!(template.props.rating_count, "2000");
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected StarRatingWidget");
        }
    }

    #[test]
    fn test_parse_row_widget() {
        let json = r#"{ 
          "type": "RowWidget",
          "props": {
              "rowComponents": [{
                      "type": "TextWidget",
                      "props": {
                          "text": "$100",
                          "typeRamp": "label-200",
                          "lineHeight": 1.2,
                          "textDecorationLine": "none"
                      }
                  },
                  {
                      "type": "DividerWidget",
                      "props": {
                          "orientation": "vertical",
                          "length": 10,
                          "color": "bebebe"
                      }
                  }
              ],
              "containerStyle": {
                  "width": 10
              }
          }
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::RowWidget(template)) = result {
            assert_eq!(template.props.row_components.len(), 2);
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.width, Some(10.0));
        } else {
            panic!("Expected RowWidget");
        }
    }

    #[test]
    fn test_parse_full_container_style() {
        let json = r#"{ 
          "type": "PrimeLogoWidget",
          "props": 
            {
                "containerStyle": {
                  "verticalAlignment": "center",
                  "horizontalAlignment": "center",
                  "width": 100,
                  "height": 100,
                  "maxHeight": 200,
                  "minHeight": 50,
                  "maxWidth": 200,
                  "minWidth": 50,
                  "paddingTop": 10,
                  "paddingEnd": 10,
                  "paddingBottom": 10,
                  "paddingStart": 10,
                  "marginTop": 5,
                  "marginEnd": 5,
                  "marginBottom": 5,
                  "marginStart": 5,
                  "backgroundColor": "fefefe",
                  "borderColor": "000000",
                  "borderWidth": 1,
                  "borderRadius": 5,
                  "opacity": 0.8
                }
            } 
        }"#;

        let result = network_parse_from_str::<SurfaceXWidgetTemplates>(json);
        assert!(result.is_ok());

        if let Ok(SurfaceXWidgetTemplates::PrimeLogoWidget(template)) = result {
            assert!(template.props.container_style.is_some());
            let style = template.props.container_style.unwrap();
            assert_eq!(style.vertical_alignment, Some("center".to_string()));
            assert_eq!(style.horizontal_alignment, Some("center".to_string()));
            assert_eq!(style.width, Some(100.0));
            assert_eq!(style.height, Some(100.0));
            assert_eq!(style.max_height, Some(200.0));
            assert_eq!(style.min_height, Some(50.0));
            assert_eq!(style.max_width, Some(200.0));
            assert_eq!(style.min_width, Some(50.0));
            assert_eq!(style.padding_top, Some(10.0));
            assert_eq!(style.padding_end, Some(10.0));
            assert_eq!(style.padding_bottom, Some(10.0));
            assert_eq!(style.padding_start, Some(10.0));
            assert_eq!(style.margin_top, Some(5.0));
            assert_eq!(style.margin_end, Some(5.0));
            assert_eq!(style.margin_bottom, Some(5.0));
            assert_eq!(style.margin_start, Some(5.0));
            assert_eq!(style.background_color, Some("fefefe".to_string()));
            assert_eq!(style.border_color, Some("000000".to_string()));
            assert_eq!(style.border_width, Some(1.0));
            assert_eq!(style.border_radius, Some(5.0));
            assert_eq!(style.opacity, Some(0.8));
        } else {
            panic!("Expected PrimeLogoWidget");
        }
    }
}
