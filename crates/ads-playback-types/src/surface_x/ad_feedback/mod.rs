use crate::surface_x::base_template::{BaseSurfaceXTemplateProps, SurfaceXTemplateType};
use crate::surface_x::widget_templates::SurfaceXWidgetTemplates;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(NetworkParsed)]
pub struct AdFeedbackTemplateProps {
    #[network(flatten)]
    pub base: BaseSurfaceXTemplateProps,

    #[network(rename = "thankYouScreen")]
    pub thank_you_screen: Vec<SurfaceXWidgetTemplates>,

    #[network(rename = "somethingElseScreen")]
    pub something_else_screen: Vec<SurfaceXWidgetTemplates>,

    #[network(rename = "mainContent")]
    pub main_content: Vec<SurfaceXWidgetTemplates>,

    #[network(rename = "linkSentErrorScreen")]
    pub link_sent_error_screen: Vec<SurfaceXWidgetTemplates>,

    #[network(rename = "linkSentScreen")]
    pub link_sent_screen: Vec<SurfaceXWidgetTemplates>,
}

#[derive(NetworkParsed)]
pub struct AdFeedbackTemplate {
    #[network(rename = "templateType")]
    pub template_type: SurfaceXTemplateType,

    #[network(rename = "templateProps")]
    pub template_props: AdFeedbackTemplateProps,
}

#[cfg(test)]
mod tests {
    use super::*;
    use network_parser::core::network_parse_from_str;

    #[test]
    fn test_deserialize_ad_feedback_template() {
        let json_str = r#"{
            "templateType": "AdFeedback",
            "templateProps": {
                "mandatoryFields": ["feedbackType"],
                "mainContent": [
                    {
                        "type": "TextWidget",
                        "props": {
                            "text": "What do you think of this ad?",
                            "typeRamp": "title-1"
                        }
                    }
                ],
                "thankYouScreen": [
                    {
                        "type": "TextWidget",
                        "props": {
                            "text": "Thank you for your feedback",
                            "typeRamp": "title-1"
                        }
                    }
                ],
                "somethingElseScreen": [],
                "linkSentErrorScreen": [],
                "linkSentScreen": []
            }
        }"#;

        let template = network_parse_from_str::<AdFeedbackTemplate>(json_str).unwrap();

        assert!(matches!(
            template.template_type,
            SurfaceXTemplateType::AdFeedback
        ));
        assert_eq!(template.template_props.main_content.len(), 1);
        assert_eq!(template.template_props.thank_you_screen.len(), 1);
        assert_eq!(template.template_props.something_else_screen.len(), 0);
        assert_eq!(template.template_props.link_sent_error_screen.len(), 0);
        assert_eq!(template.template_props.link_sent_screen.len(), 0);

        assert_eq!(
            template.template_props.base.mandatory_fields,
            vec!["feedbackType"]
        );
    }
}
