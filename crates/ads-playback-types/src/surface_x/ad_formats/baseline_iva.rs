use crate::surface_x::base_template::{BaseSurfaceXTemplateProps, SurfaceXTemplateType};
use crate::surface_x::widget_templates::SurfaceXWidgetTemplates;
use cfg_test_attr_derive::derive_test_only;
use ignx_compositron::Signify;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

#[derive(NetworkParsed, Signify, Clone)]
#[derive_test_only(Debug, PartialEq)]
pub enum CardPosition {
    #[network(rename = "left")]
    Left,
    #[network(rename = "right")]
    Right,
}

#[derive(NetworkParsed)]
pub struct BaselineIVATemplateProps {
    #[network(flatten)]
    pub base: BaseSurfaceXTemplateProps,

    #[network(rename = "ctaButton")]
    pub cta_button: Option<SurfaceXWidgetTemplates>,

    #[network(rename = "hintText")]
    pub hint_text: Option<SurfaceXWidgetTemplates>,

    #[network(rename = "mainIcon")]
    pub main_icon: SurfaceXWidgetTemplates,

    #[network(rename = "cardPosition")]
    pub card_position: Option<CardPosition>,

    #[network(rename = "mainContent")]
    pub main_content: Vec<SurfaceXWidgetTemplates>,

    #[network(rename = "ctaBackgroundColor")]
    pub cta_background_color: Option<String>,
}

#[derive(NetworkParsed)]
pub struct BaselineIVATemplate {
    #[network(rename = "templateType")]
    pub template_type: SurfaceXTemplateType,

    #[network(rename = "templateProps")]
    pub template_props: BaselineIVATemplateProps,
}

#[cfg(test)]
mod tests {
    use super::*;
    use network_parser::core::network_parse_from_str;

    #[test]
    fn test_deserialize_baseline_iva_template() {
        let json_str = r#"{
            "templateType": "BaselineIVA",
            "templateProps": {
                "mandatoryFields": ["title", "product.price", "product.rating"],
                "animation": {
                    "entryAnimation": "FadeIn",
                    "exitAnimation": "FadeOut"
                },
                "accessibilityMessages": {
                    "onFocus": "Press OK to add to cart"
                },
                "cardPosition": "right",
                "mainContent": [
                    {
                        "type": "TextWidget",
                        "props": {
                            "text": "Sample product",
                            "typeRamp": "title-1"
                        }
                    }
                ],
                "mainIcon": {
                    "type": "ImageWidget",
                    "props": {
                        "imageUrl": "https://example.com/image.jpg",
                        "width": 100,
                        "height": 100
                    }
                },
                "ctaBackgroundColor": "Black"
            }
        }"#;

        let template = network_parse_from_str::<BaselineIVATemplate>(json_str).unwrap();

        assert!(matches!(
            template.template_type,
            SurfaceXTemplateType::BaselineIVA
        ));
        assert_eq!(
            template.template_props.card_position,
            Some(CardPosition::Right)
        );
        assert_eq!(
            template.template_props.cta_background_color,
            Some("Black".to_string())
        );
        assert_eq!(template.template_props.main_content.len(), 1);

        assert_eq!(
            template.template_props.base.mandatory_fields,
            vec!["title", "product.price", "product.rating"]
        );

        let animation = template.template_props.base.animation.unwrap();
        assert_eq!(animation.entry_animation, "FadeIn");
        assert_eq!(animation.exit_animation, "FadeOut");

        let messages = template.template_props.base.accessibility_messages.unwrap();
        assert_eq!(
            messages.get("onFocus"),
            Some(&"Press OK to add to cart".to_string())
        );
    }
}
