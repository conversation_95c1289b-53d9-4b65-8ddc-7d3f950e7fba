use crate::surface_x::ad_feedback::AdFeedbackTemplate;
use crate::surface_x::ad_formats::baseline_iva::BaselineIVATemplate;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;

pub mod ad_feedback;
pub mod ad_formats;
pub mod base_template;
pub mod widget_templates;

#[derive(NetworkParsed)]
#[network(tag = "templateType")]
pub enum SurfaceXTemplate {
    BaselineIVA(BaselineIVATemplate),
    AdFeedback(AdFeedbackTemplate),
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::surface_x::ad_formats::baseline_iva::CardPosition;
    use crate::surface_x::base_template::SurfaceXTemplateType;
    use network_parser::core::network_parse_from_str;

    #[test]
    fn test_deserialize_baseline_iva_template() {
        let json_str = r#"{
            "templateType": "BaselineIVA",
            "templateProps": {
                "mandatoryFields": ["title", "product.price"],
                "animation": {
                    "entryAnimation": "FadeIn",
                    "exitAnimation": "FadeOut"
                },
                "accessibilityMessages": {
                    "onFocus": "Press OK to view details"
                },
                "cardPosition": "left",
                "mainContent": [
                    {
                        "type": "TextWidget",
                        "props": {
                            "text": "Featured Product",
                            "typeRamp": "title-1"
                        }
                    },
                    {
                        "type": "TextWidget",
                        "props": {
                            "text": "$19.99",
                            "typeRamp": "body-1"
                        }
                    }
                ],
                "mainIcon": {
                    "type": "IconWidget",
                    "props": {
                        "iconName": "ShoppingBag"
                    }
                },
                "ctaButton": {
                    "type": "PrimaryButtonWidget",
                    "props": {
                        "text": "Buy Now",
                        "iconName": "Cart"
                    }
                },
                "ctaBackgroundColor": "Blue"
            }
        }"#;

        let template = network_parse_from_str::<SurfaceXTemplate>(json_str).unwrap();

        match template {
            SurfaceXTemplate::BaselineIVA(baseline_template) => {
                assert!(matches!(
                    baseline_template.template_type,
                    SurfaceXTemplateType::BaselineIVA
                ));
                assert_eq!(
                    baseline_template.template_props.card_position,
                    Some(CardPosition::Left)
                );
                assert_eq!(
                    baseline_template.template_props.cta_background_color,
                    Some("Blue".to_string())
                );
                assert_eq!(baseline_template.template_props.main_content.len(), 2);

                assert_eq!(
                    baseline_template.template_props.base.mandatory_fields,
                    vec!["title", "product.price"]
                );

                let animation = baseline_template.template_props.base.animation.unwrap();
                assert_eq!(animation.entry_animation, "FadeIn");
                assert_eq!(animation.exit_animation, "FadeOut");

                let messages = baseline_template
                    .template_props
                    .base
                    .accessibility_messages
                    .unwrap();
                assert_eq!(
                    messages.get("onFocus"),
                    Some(&"Press OK to view details".to_string())
                );

                assert!(baseline_template.template_props.cta_button.is_some());
            }
            _ => panic!("Expected BaselineIVA template"),
        }
    }

    #[test]
    fn test_deserialize_ad_feedback_template() {
        let json_str = r#"{
            "templateType": "AdFeedback",
            "templateProps": {
                "mandatoryFields": ["feedbackType", "adId"],
                "animation": {
                    "entryAnimation": "SlideFromBottom",
                    "exitAnimation": "SlideToBottom"
                },
                "accessibilityMessages": {
                    "onFocus": "Select feedback option"
                },
                "mainContent": [
                    {
                        "type": "TextWidget",
                        "props": {
                            "text": "How was this ad relevant to you?",
                            "typeRamp": "title-1"
                        }
                    },
                    {
                        "type": "RowWidget",
                        "props": {
                            "rowComponents": [
                                {
                                    "type": "PrimaryButtonWidget",
                                    "props": {
                                        "text": "Very Relevant",
                                        "iconName": "ThumbsUp"
                                    }
                                },
                                {
                                    "type": "PrimaryButtonWidget",
                                    "props": {
                                        "text": "Not Relevant",
                                        "iconName": "ThumbsDown"
                                    }
                                }
                            ]
                        }
                    }
                ],
                "thankYouScreen": [
                    {
                        "type": "TextWidget",
                        "props": {
                            "text": "Thank you for your feedback",
                            "typeRamp": "title-1"
                        }
                    },
                    {
                        "type": "PrimaryButtonWidget",
                        "props": {
                            "text": "Continue Watching"
                        }
                    }
                ],
                "somethingElseScreen": [],
                "linkSentErrorScreen": [],
                "linkSentScreen": []
            }
        }"#;

        let template = network_parse_from_str::<SurfaceXTemplate>(json_str).unwrap();

        match template {
            SurfaceXTemplate::AdFeedback(feedback_template) => {
                assert!(matches!(
                    feedback_template.template_type,
                    SurfaceXTemplateType::AdFeedback
                ));

                assert_eq!(feedback_template.template_props.main_content.len(), 2);
                assert_eq!(feedback_template.template_props.thank_you_screen.len(), 2);
                assert_eq!(
                    feedback_template.template_props.something_else_screen.len(),
                    0
                );
                assert_eq!(
                    feedback_template
                        .template_props
                        .link_sent_error_screen
                        .len(),
                    0
                );
                assert_eq!(feedback_template.template_props.link_sent_screen.len(), 0);
                assert_eq!(
                    feedback_template.template_props.base.mandatory_fields,
                    vec!["feedbackType", "adId"]
                );

                let animation = feedback_template.template_props.base.animation.unwrap();
                assert_eq!(animation.entry_animation, "SlideFromBottom");
                assert_eq!(animation.exit_animation, "SlideToBottom");

                let messages = feedback_template
                    .template_props
                    .base
                    .accessibility_messages
                    .unwrap();
                assert_eq!(
                    messages.get("onFocus"),
                    Some(&"Select feedback option".to_string())
                );
            }
            _ => panic!("Expected AdFeedback template"),
        }
    }
}
