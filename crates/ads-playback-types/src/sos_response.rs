#![allow(nonstandard_style, reason = "type compatibility")]

use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub enum LayoutPosition {
    LEFT,
    RIGHT,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct TemplateIdResponse {
    pub id: String,
    pub majorVersion: u32,
    pub minorVersion: u32,
    pub separator: Option<String>,
}

impl TemplateIdResponse {
    pub fn to_identifier_string(&self) -> String {
        let separator = self.separator.as_deref().unwrap_or_default();
        format!(
            "{}{}{}{}{}",
            self.id, separator, self.majorVersion, separator, self.minorVersion
        )
    }
}

#[derive(<PERSON><PERSON>, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct BaseTemplateResponse {
    pub enabled: Option<bool>,
    pub layoutPosition: Option<LayoutPosition>,
    pub ext: Option<HashMap<String, String>>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaTemplateResponse {
    #[network(flatten)]
    pub base: BaseTemplateResponse,
    pub templateId: Option<TemplateIdResponse>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct AdFeedbackTemplateResponse {
    #[network(flatten)]
    pub base: BaseTemplateResponse,
    pub thumbsUp: Option<TemplateIdResponse>,
    pub thumbsDown: Option<TemplateIdResponse>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct AdItemResponse {
    pub iva: Option<IvaTemplateResponse>,
    pub adFeedback: Option<AdFeedbackTemplateResponse>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct AdItemResponseCommons {
    pub templates: Option<HashMap<String, String>>,
    pub localizedStrings: Option<HashMap<String, HashMap<String, String>>>,
    pub ext: Option<HashMap<String, String>>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct GetSurfaceTemplatesData {
    pub adItems: HashMap<String, AdItemResponse>,
    pub commons: Option<AdItemResponseCommons>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case::with_hyphen_separator("test", 1, 2, Some("-".to_string()), "test-1-2")]
    #[case::without_separator("test", 1, 2, None, "test12")]
    #[case::with_empty_separator("test", 1, 2, Some("".to_string()), "test12")]
    #[case::with_special_chars("test@id", 1, 2, Some("_".to_string()), "test@id_1_2")]
    #[case::with_large_numbers("test", 999999, 888888, Some(".".to_string()), "test.999999.888888")]
    #[case::with_empty_id("", 1, 2, Some("-".to_string()), "-1-2")]
    #[case::with_double_colon("test", 1, 2, Some("::".to_string()), "test::1::2")]
    fn test_template_id_response(
        #[case] id: &str,
        #[case] major_version: u32,
        #[case] minor_version: u32,
        #[case] separator: Option<String>,
        #[case] expected: &str,
    ) {
        let template = TemplateIdResponse {
            id: id.to_string(),
            majorVersion: major_version,
            minorVersion: minor_version,
            separator,
        };

        assert_eq!(template.to_identifier_string(), expected);
    }
}
