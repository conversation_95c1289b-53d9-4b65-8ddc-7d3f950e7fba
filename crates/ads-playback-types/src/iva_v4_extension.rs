use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;
use std::fmt;

// The type names and type hierarchies found here are generated manually from the following place:
// https://code.amazon.com/packages/InteractiveVideoAdsRenderModule/blobs/mainline/--/src/types/vast/extensions/IvaV4ExtensionAdParameters.ts
//
// Live Events 2024 - Schema Reference: https://quip-amazon.com/eDIwAj8EFCP6/TNF-2024-IVA-VAST-Schema-Glossary
//
// Note: SendToPhone re-uses the SendMeMore variant for payload.
// Please note that IvaV4ExtensionAdParametersActionPayload is untagged.

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub enum IvaV4ExtensionAdParametersActionCode {
    #[network(rename = "ATC")]
    AddToCart,
    #[network(rename = "STP")]
    SendToPhone,
    #[network(rename = "SMM")]
    SendMeMore,
}

impl fmt::Display for IvaV4ExtensionAdParametersActionCode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::AddToCart => {
                write!(f, "ATC")
            }
            Self::SendToPhone => {
                write!(f, "STP")
            }
            Self::SendMeMore => {
                write!(f, "SMM")
            }
        }
    }
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub enum Overlay {
    #[network(rename = "DEFAULT")]
    Default,
    #[network(rename = "MINIMAL")]
    Minimal,
}

impl fmt::Display for Overlay {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Default => {
                write!(f, "DEFAULT")
            }
            Self::Minimal => {
                write!(f, "MINIMAL")
            }
        }
    }
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub enum AdFormatCta {
    #[network(rename = "PafMafAddToCart")]
    PafMafAddToCart,
    #[network(rename = "PafMafLearnMore")]
    PafMafLearnMore,
    #[network(rename = "SqueezebackAddToCart")]
    SqueezebackAddToCart,
    #[network(rename = "SqueezebackLearnMore")]
    SqueezebackLearnMore,
    #[network(rename = "SqueezebackP13NLearnMore")]
    SqueezebackP13NLearnMore,
}

impl fmt::Display for AdFormatCta {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::PafMafAddToCart => write!(f, "PafMafAddToCart"),
            Self::PafMafLearnMore => write!(f, "PafMafLearnMore"),
            Self::SqueezebackAddToCart => write!(f, "SqueezebackAddToCart"),
            Self::SqueezebackLearnMore => write!(f, "SqueezebackLearnMore"),
            Self::SqueezebackP13NLearnMore => write!(f, "SqueezebackP13NLearnMore"),
        }
    }
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub enum InventoryType {
    #[network(rename = "LE")]
    LiveEvent,
    #[network(rename = "STV")]
    StreamingTv,
}

impl fmt::Display for InventoryType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::LiveEvent => {
                write!(f, "LE")
            }
            Self::StreamingTv => {
                write!(f, "STV")
            }
        }
    }
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub enum Placement {
    #[network(rename = "MID_LEFT")]
    MidLeft,
    #[network(rename = "MID_RIGHT")]
    MidRight,
    #[network(rename = "TOP_LEFT")]
    TopLeft,
    #[network(rename = "TOP_RIGHT")]
    TopRight,
}

impl fmt::Display for Placement {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::MidLeft => {
                write!(f, "MID_LEFT")
            }
            Self::MidRight => {
                write!(f, "MID_RIGHT")
            }
            Self::TopLeft => {
                write!(f, "TOP_LEFT")
            }
            Self::TopRight => {
                write!(f, "TOP_RIGHT")
            }
        }
    }
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct Coupon {
    #[network(rename = "type")]
    pub coupon_type: String,
    pub value: f64,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParameters {
    #[network(rename = "act")]
    pub action: Vec<IvaV4ExtensionAdParametersAction>,
    #[network(rename = "adm")]
    pub ad_display_metadata: IvaV4ExtensionAdParametersAdDisplayMetadata,
    #[network(rename = "ids")]
    pub identifiers: IvaV4ExtensionAdParametersIdentifiers,
    #[network(rename = "tt")]
    pub trackers: IvaV4ExtensionAdParametersTemplatizedTrackers,
    #[network(rename = "t")]
    pub tracker_parameters: HashMap<String, Vec<String>>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4VodLinearExtensionAdParameters {
    #[network(flatten)]
    pub base: IvaV4ExtensionAdParameters,
    #[network(rename = "v")]
    pub version: String,
    #[network(rename = "exp")]
    pub experiments: Option<Vec<IvaV4AdParametersExperiment>>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4AdParametersExperiment {
    #[network(rename = "n")]
    pub name: String,
    #[network(rename = "t")]
    pub treatment: String,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersAction {
    #[network(rename = "ac")]
    pub code: IvaV4ExtensionAdParametersActionCode,
    #[network(rename = "ap")]
    pub payload: IvaV4ExtensionAdParametersActionPayload,
    #[network(rename = "apt")]
    pub token: String,
    #[network(rename = "v")]
    pub version: String,
}

// Action Payload Variants
#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(untagged)]
pub enum IvaV4ExtensionAdParametersActionPayload {
    AddToCart(IvaV4ExtensionAdParametersActionPayloadAddToCart),
    SendMeMore(IvaV4ExtensionAdParametersActionPayloadSendMeMore),
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersActionPayloadAddToCart {
    pub asin: String,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersActionPayloadSendMeMore {
    pub esi: String,
    pub nsi: String,
}

// Display Metadata
#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
#[network(untagged)]
pub enum IvaV4ExtensionAdParametersAdDisplayMetadata {
    AddToCart(IvaV4ExtensionAdParametersAdDisplayMetadataAddToCart),
    SendMeMore(IvaV4ExtensionAdParametersAdDisplayMetadataSendMeMore),
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersAdDisplayMetadataBase {
    #[network(rename = "inv")]
    pub inventory_type: InventoryType,
    #[network(rename = "ov")]
    pub overlay: Overlay,
    #[network(rename = "pl")]
    pub placement: Placement,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersAdDisplayMetadataAddToCart {
    #[network(flatten)]
    pub base: IvaV4ExtensionAdParametersAdDisplayMetadataBase,
    #[network(rename = "vft")]
    pub voice_friendly_title: String,
    #[network(rename = "prc")]
    pub price: Option<String>,
    #[network(rename = "prm")]
    pub is_prime_eligible: Option<bool>,
    #[network(rename = "rtg")]
    pub rating: Option<String>,
    #[network(rename = "sprc")]
    pub strike_through_price: Option<String>,
    #[network(rename = "hstr")]
    pub has_half_star: Option<bool>,
    #[network(rename = "fstr")]
    pub full_stars: Option<String>,
    #[network(rename = "rctn")]
    pub review_count: Option<String>,
    #[network(rename = "cpn")]
    pub coupon: Option<Coupon>,
    #[network(rename = "dpd")]
    pub discount_percentage_display: Option<f64>,
    #[network(rename = "dbt")]
    pub deal_badge_text: Option<String>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersAdDisplayMetadataSendMeMore {
    #[network(flatten)]
    pub base: IvaV4ExtensionAdParametersAdDisplayMetadataBase,
    #[network(rename = "bn")]
    pub brand_name: String,
    #[network(rename = "hl")]
    pub headline: String,
    #[network(rename = "pimg")]
    pub product_image_url: Option<String>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct SqueezebackP13nAdParameterAdDisplayMetadata {
    #[network(flatten)]
    pub base: IvaV4ExtensionAdParametersAdDisplayMetadataBase,
    #[network(rename = "blg")]
    pub brand_logo_image_url: Option<String>,
    #[network(rename = "sh")]
    pub subheading: Option<String>,
    #[network(rename = "body")]
    pub body: Option<String>,
    #[network(rename = "hl")]
    pub heading: Option<String>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct SqueezebackP13nAdParameterAction {
    #[network(flatten)]
    pub base: IvaV4ExtensionAdParametersActionPayloadSendMeMore,
    #[network(rename = "duo")]
    pub destination_url_override: Option<String>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct SqueezebackAdParameters {
    #[network(rename = "pimg")]
    pub product_image_url: Option<String>,
    #[network(rename = "vft")]
    pub voice_friendly_text: Option<String>,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersIdentifiers {
    #[network(rename = "a")]
    pub ad: String,
    #[network(rename = "c")]
    pub creative: String,
    #[network(rename = "i")]
    pub impression: String,
    #[network(rename = "u")]
    pub bid: String,
}

// There is a field called "m" that react does not use and which we also omit here
#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersTemplatizedTrackers {
    #[network(rename = "e")]
    pub events: Vec<String>,
    #[network(rename = "tmpl")]
    pub template: String,
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct TempletizedTrackersOnly {
    #[network(rename = "tmpl")]
    pub template: String,
}

// This is needed to attempt to extract the tracker template in the event
// that the whole struct deserialization failed. The business logic will
// subsequently try to use this to report back to the advertising system that
// something went wrong during Ad Parameter deserialization.
#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct IvaV4ExtensionAdParametersTempletizedTrackersOnly {
    #[network(rename = "tt")]
    pub trackers: TempletizedTrackersOnly,
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(IvaV4ExtensionAdParametersActionCode::AddToCart, "ATC")]
    #[case(IvaV4ExtensionAdParametersActionCode::SendToPhone, "STP")]
    #[case(IvaV4ExtensionAdParametersActionCode::SendMeMore, "SMM")]
    fn test_action_type_value(
        #[case] action_type: IvaV4ExtensionAdParametersActionCode,
        #[case] expected_str: &str,
    ) {
        assert_eq!(action_type.to_string(), expected_str.to_string());
    }

    #[rstest]
    #[case(Overlay::Default, "DEFAULT")]
    #[case(Overlay::Minimal, "MINIMAL")]
    fn test_overlay_value(#[case] overlay: Overlay, #[case] expected_str: &str) {
        assert_eq!(overlay.to_string(), expected_str.to_string());
    }

    #[rstest]
    #[case(Placement::MidLeft, "MID_LEFT")]
    #[case(Placement::MidRight, "MID_RIGHT")]
    #[case(Placement::TopLeft, "TOP_LEFT")]
    #[case(Placement::TopRight, "TOP_RIGHT")]
    fn test_placement_value(#[case] placement: Placement, #[case] expected_str: &str) {
        assert_eq!(placement.to_string(), expected_str.to_string());
    }

    #[rstest]
    #[case(InventoryType::LiveEvent, "LE")]
    #[case(InventoryType::StreamingTv, "STV")]
    fn test_inventory_type_value(
        #[case] inventory_type: InventoryType,
        #[case] expected_str: &str,
    ) {
        match inventory_type {
            InventoryType::LiveEvent => assert_eq!(expected_str, "LE"),
            InventoryType::StreamingTv => assert_eq!(expected_str, "STV"),
        }
    }

    #[rstest]
    #[case(AdFormatCta::PafMafAddToCart, "PafMafAddToCart")]
    #[case(AdFormatCta::PafMafLearnMore, "PafMafLearnMore")]
    #[case(AdFormatCta::SqueezebackAddToCart, "SqueezebackAddToCart")]
    #[case(AdFormatCta::SqueezebackLearnMore, "SqueezebackLearnMore")]
    #[case(AdFormatCta::SqueezebackP13NLearnMore, "SqueezebackP13NLearnMore")]
    fn test_ad_format_cta_value(#[case] ad_format_cta: AdFormatCta, #[case] expected_str: &str) {
        assert_eq!(ad_format_cta.to_string(), expected_str);
    }
}
