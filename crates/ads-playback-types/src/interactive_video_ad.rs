use crate::iva_v4_extension::{IvaV4ExtensionAdParameters, IvaV4VodLinearExtensionAdParameters};
use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::borrow::Borrow;
use std::hash::{Hash, Hasher};

pub type AdInstanceId = String;

#[derive_test_only(Debug)]
pub struct InteractiveVideoAd {
    pub id: AdInstanceId,
    pub extension: ActionableAdExtension,
}

impl InteractiveVideoAd {
    pub fn template_id(&self) -> TemplateId {
        self.extension.template_id.to_identifier_string()
    }
}

impl Hash for InteractiveVideoAd {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.id.hash(state);
    }
}

impl PartialEq for InteractiveVideoAd {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for InteractiveVideoAd {}

impl Borrow<String> for InteractiveVideoAd {
    fn borrow(&self) -> &String {
        &self.id
    }
}

#[derive_test_only(Debug, PartialEq)]
pub struct ActionableAdExtension {
    pub id: String,
    pub version: String,
    pub template_id: SurfaceXTemplateId,
    pub data: AdExtensionData,
}

#[derive_test_only(Debug, PartialEq)]
pub enum AdExtensionData {
    VodOrLinear {
        parameters: IvaV4VodLinearExtensionAdParameters,
    },
    Live {
        parameters: IvaV4ExtensionAdParameters,
        tracking_url: Option<String>,
        start_ms: f64,
        end_ms: f64,
    },
}

#[derive(NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct SurfaceXTemplateId {
    #[network(rename = "tid")]
    pub id: String,
    #[network(rename = "m")]
    pub major_version: u32,
    #[network(rename = "mq")]
    pub minor_version: u32,
}

pub type TemplateId = String;

impl SurfaceXTemplateId {
    pub fn to_identifier_string(&self) -> TemplateId {
        format!("{}#{}.{}", self.id, self.major_version, self.minor_version)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::{
        ActionableAdExtensionBuilder, AdExtensionDataBuilder, InteractiveVideoAdBuilder,
    };
    use std::hash::DefaultHasher;

    fn create_sample_vod_ad(id: impl Into<String>) -> InteractiveVideoAd {
        InteractiveVideoAdBuilder::default()
            .with_id(id)
            .with_extension(
                ActionableAdExtensionBuilder::default()
                    .with_data(
                        AdExtensionDataBuilder::default()
                            .with_atc_payload()
                            .build_vod_linear(),
                    )
                    .build(),
            )
            .build()
    }

    fn calculate_hash<T: Hash>(t: &T) -> u64 {
        let mut hasher = DefaultHasher::new();
        t.hash(&mut hasher);
        hasher.finish()
    }

    #[test]
    fn test_hash_same_id() {
        let ad1 = create_sample_vod_ad("same_id");
        let ad2 = create_sample_vod_ad("same_id");
        assert_eq!(calculate_hash(&ad1), calculate_hash(&ad2));
    }

    #[test]
    fn test_hash_different_id() {
        let ad1 = create_sample_vod_ad("id1");
        let ad2 = create_sample_vod_ad("id2");
        assert_ne!(calculate_hash(&ad1), calculate_hash(&ad2));
    }

    #[test]
    fn test_hash_consistency() {
        let ad = create_sample_vod_ad("test_id");
        let hash1 = calculate_hash(&ad);
        let hash2 = calculate_hash(&ad);
        assert_eq!(hash1, hash2);
    }

    #[test]
    fn test_partial_eq_same_id() {
        let ad1 = create_sample_vod_ad("same_id");
        let ad2 = create_sample_vod_ad("same_id");
        assert_eq!(ad1, ad2);
    }

    #[test]
    fn test_partial_eq_different_id() {
        let ad1 = create_sample_vod_ad("id1");
        let ad2 = create_sample_vod_ad("id2");
        assert_ne!(ad1, ad2);
    }

    #[test]
    fn test_template_id_to_string() {
        let template_id = SurfaceXTemplateId {
            id: "template123".to_string(),
            major_version: 1,
            minor_version: 2,
        };
        assert_eq!(template_id.to_identifier_string(), "template123#1.2");
    }
}
