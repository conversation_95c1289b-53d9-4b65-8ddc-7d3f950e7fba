use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use std::collections::HashMap;

#[derive(PartialEq, Eq, Clone, Hash)]
#[derive_test_only(Debug)]
pub struct PauseAdsParams {
    pub request_token: Option<String>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct RegolithResponsePauseAds {
    #[network(rename = "pauseAds")]
    pub pause_ads: Vec<RegolithPauseAd>,
    #[network(rename = "requestToken")]
    pub request_token: String,
    #[network(rename = "adsDeliveryRules")]
    pub ads_delivery_rules: RegolithAdsDeliveryRules,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct RegolithPauseAd {
    #[network(rename = "mediaUrl")]
    pub media_url: String,
    #[network(rename = "impressionUrls")]
    pub impression_urls: Vec<String>,
    pub actionable: Option<InteractivePauseAdsPayload>,
    #[network(rename = "creativeId")]
    pub creative_id: Option<String>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct RegolithAdsDeliveryRules {
    #[network(rename = "adsFetchPolicy")]
    pub ads_fetch_policy: RegolithAdsFetchPolicy,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct RegolithAdsFetchPolicy {
    #[network(rename = "minDurationBetweenFetchInSec")]
    pub min_duration_between_fetch_in_sec: i32,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct InteractivePauseAdsPayload {
    pub actions: Vec<InteractivePauseAdsPayloadAction>,
    pub identifiers: Option<InteractivePauseAdsPayloadIdentifiers>,
    #[network(rename = "productMetadata")]
    pub product_metadata: HashMap<String, ProductMetadataDetails>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct InteractivePauseAdsPayloadAction {
    pub __type: String,
    pub asin: String,
    pub signed: String,
    pub trackers: InteractivePauseAdsPayloadActionTrackers,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct InteractivePauseAdsPayloadActionTrackers {
    pub displayed: String,
    pub invoked: String,
    pub error: String,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct InteractivePauseAdsPayloadIdentifiers {
    #[network(rename = "adId")]
    pub ad_id: Option<String>,
    #[network(rename = "bidId")]
    pub bid_id: Option<String>,
    #[network(rename = "impressionId")]
    pub impression_id: Option<String>,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct ProductMetadataDetails {
    pub pricing: ProductMetadataDetailsPricing,
    #[network(rename = "inStock")]
    pub in_stock: Option<bool>,
    #[network(rename = "isPrime")]
    pub is_prime: Option<bool>,
    pub title: String,
}

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct ProductMetadataDetailsPricing {
    #[network(rename = "listPrice")]
    pub list_price: String,
    #[network(rename = "buyingPrice")]
    pub buying_price: String,
    #[network(rename = "discountPercent")]
    pub discount_percent: String,
}
