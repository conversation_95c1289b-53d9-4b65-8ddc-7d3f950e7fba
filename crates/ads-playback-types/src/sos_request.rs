#![allow(nonstandard_style, reason = "type compatibility")]

use cfg_test_attr_derive::derive_test_only;
use serde::Serialize;
use std::collections::HashMap;
use std::hash::{<PERSON>h, <PERSON>her};

#[derive(PartialEq, Eq, <PERSON><PERSON>, <PERSON>h, Serialize)]
#[derive_test_only(Debug)]
pub enum EndpointSource {
    PAUSE_BEHAVIOR,
    ADS_BEHAVIOR,
    FALLBACK_BEHAVIOR,
}

#[derive(PartialEq, Eq, <PERSON>lone, Hash, Serialize)]
#[derive_test_only(Debug)]
pub struct BaseTemplateRequest {
    pub enabled: bool,
}

#[derive(PartialEq, Eq, <PERSON>lone, Hash, Serialize)]
#[derive_test_only(Debug)]
pub struct AdFeedbackTemplateRequest {
    #[serde(flatten)]
    pub base: BaseTemplateRequest,
}

#[derive(PartialEq, Eq, <PERSON>lone, <PERSON>h, Serial<PERSON>)]
#[derive_test_only(Debug)]
pub struct TemplateIdRequest {
    pub id: String,
    pub majorVersion: u32,
    pub minorVersion: u32,
}

#[derive(PartialEq, Eq, <PERSON><PERSON>, <PERSON>h, Serialize)]
#[derive_test_only(Debug)]
pub struct IvaTemplateRequest {
    #[serde(flatten)]
    pub base: BaseTemplateRequest,
    pub templateId: Option<TemplateIdRequest>,
}

#[derive(PartialEq, Eq, Clone, Hash, Serialize)]
#[derive_test_only(Debug)]
pub struct AdItemRequest {
    pub iva: Option<IvaTemplateRequest>,
    pub adFeedback: Option<AdFeedbackTemplateRequest>,
}

// we are omitting session id because the url will already contain it
#[derive(PartialEq, Eq, Clone, Serialize)]
#[derive_test_only(Debug)]
pub struct GetSurfaceTemplatesParams {
    pub endpointSource: EndpointSource,
    pub adItems: HashMap<String, AdItemRequest>,
}

impl Hash for GetSurfaceTemplatesParams {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.endpointSource.hash(state);

        let mut collected: Vec<_> = self.adItems.iter().collect();
        collected.sort_by(|(k1, _), (k2, _)| k1.cmp(k2));

        collected.iter().for_each(|(k, v)| {
            k.hash(state);
            v.hash(state);
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hash_empty_params() {
        let params1 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::PAUSE_BEHAVIOR,
            adItems: HashMap::new(),
        };

        let params2 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::PAUSE_BEHAVIOR,
            adItems: HashMap::new(),
        };

        assert_eq!(calculate_hash(&params1), calculate_hash(&params2));
    }

    #[test]
    fn test_hash_different_endpoint_sources() {
        let params1 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::PAUSE_BEHAVIOR,
            adItems: HashMap::new(),
        };

        let params2 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::ADS_BEHAVIOR,
            adItems: HashMap::new(),
        };

        assert_ne!(calculate_hash(&params1), calculate_hash(&params2));
    }

    #[test]
    fn test_hash_same_ad_items_different_order() {
        let mut ad_items1 = HashMap::new();
        let mut ad_items2 = HashMap::new();

        let ad_item = AdItemRequest {
            iva: Some(IvaTemplateRequest {
                base: BaseTemplateRequest { enabled: true },
                templateId: Some(TemplateIdRequest {
                    id: "test".to_string(),
                    majorVersion: 1,
                    minorVersion: 0,
                }),
            }),
            adFeedback: Some(AdFeedbackTemplateRequest {
                base: BaseTemplateRequest { enabled: true },
            }),
        };

        // Insert items in different order
        ad_items1.insert("key1".to_string(), ad_item.clone());
        ad_items1.insert("key2".to_string(), ad_item.clone());

        ad_items2.insert("key2".to_string(), ad_item.clone());
        ad_items2.insert("key1".to_string(), ad_item);

        let params1 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::PAUSE_BEHAVIOR,
            adItems: ad_items1,
        };

        let params2 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::PAUSE_BEHAVIOR,
            adItems: ad_items2,
        };

        assert_eq!(calculate_hash(&params1), calculate_hash(&params2));
    }

    #[test]
    fn test_hash_different_ad_items() {
        let mut ad_items1 = HashMap::new();
        let mut ad_items2 = HashMap::new();

        let ad_item1 = AdItemRequest {
            iva: Some(IvaTemplateRequest {
                base: BaseTemplateRequest { enabled: true },
                templateId: Some(TemplateIdRequest {
                    id: "test1".to_string(),
                    majorVersion: 1,
                    minorVersion: 0,
                }),
            }),
            adFeedback: None,
        };

        let ad_item2 = AdItemRequest {
            iva: Some(IvaTemplateRequest {
                base: BaseTemplateRequest { enabled: false },
                templateId: Some(TemplateIdRequest {
                    id: "test2".to_string(),
                    majorVersion: 1,
                    minorVersion: 0,
                }),
            }),
            adFeedback: None,
        };

        ad_items1.insert("key1".to_string(), ad_item1);
        ad_items2.insert("key1".to_string(), ad_item2);

        let params1 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::PAUSE_BEHAVIOR,
            adItems: ad_items1,
        };

        let params2 = GetSurfaceTemplatesParams {
            endpointSource: EndpointSource::PAUSE_BEHAVIOR,
            adItems: ad_items2,
        };

        assert_ne!(calculate_hash(&params1), calculate_hash(&params2));
    }

    fn calculate_hash<T: Hash>(t: &T) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        let mut s = DefaultHasher::new();
        t.hash(&mut s);
        s.finish()
    }
}
