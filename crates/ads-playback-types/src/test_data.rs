pub const SEND_TO_PHONE_AD_PARAMETERS: &str = r#"{
    "act": [{
        "ac": "STP",
        "ap": {
            "esi": "c6990f04-93ed-45a5-bd3f-6b82f9abff82",
            "nsi": "ea185a0f-9e82-4f87-9b02-98ba4a75ae2c"
        },
        "apt": "",
        "v": "1.0"
    }],
    "adm": {
        "inv": "STV",
        "ov": "DEFAULT",
        "pl": "MID_LEFT",
        "cv": "4",
        "bn": "",
        "hl": ""
    },
    "ids": {
        "a": "587707598828198836",
        "c": "576583021466043328",
        "i": "ClBwCjBGHmbSu5I66ejr3A",
        "u": "ClBwCjBGHmbSu5I66ejr3A"
    },
    "t": {
        "ivaFirstClick": [
            "https://example.com/tracking"
        ]
    },
    "tt": {
        "e": [
            "ivaCtaDisplayed",
            "ivaCtaInvoked",
            "ivaError"
        ],
        "m": [],
        "tmpl": "https://track.test/e=[EVENTTYPE]&t=[ATTIME]&v=[CREATIVE_RENDER_VERSION]&err=[ERRORCODE]"
    },
    "v": "1.0"
}"#;

pub const SEND_ME_MORE_AD_PARAMETERS: &str = r#"{
    "act": [{
        "ac": "SMM",
        "ap": {
            "esi": "c6990f04-93ed-45a5-bd3f-6b82f9abff82",
            "nsi": "ea185a0f-9e82-4f87-9b02-98ba4a75ae2c"
        },
        "apt": "",
        "v": "1.0"
    }],
    "adm": {
        "inv": "STV",
        "ov": "DEFAULT",
        "pl": "MID_LEFT",
        "cv": "4",
        "bn": "",
        "hl": "",
        "pimg": "https://example.com/product-image.jpg"
    },
    "ids": {
        "a": "587707598828198836",
        "c": "576583021466043328",
        "i": "ClBwCjBGHmbSu5I66ejr3A",
        "u": "ClBwCjBGHmbSu5I66ejr3A"
    },
    "t": {
        "ivaFirstClick": [
            "https://example.com/tracking"
        ]
    },
    "tt": {
        "e": [
            "ivaCtaDisplayed",
            "ivaCtaInvoked",
            "ivaError"
        ],
        "m": [],
        "tmpl": "https://track.test/e=[EVENTTYPE]&t=[ATTIME]&v=[CREATIVE_RENDER_VERSION]&err=[ERRORCODE]"
    },
    "v": "1.0"
}"#;

pub const ADD_TO_CART_AD_PARAMETERS: &str = r#"{
    "act": [{
        "ac": "ATC",
        "ap": {
            "asin": "B0DKJLH1YK"
        },
        "apt": "",
        "v": "1.0"
    }],
    "adm": {
        "inv": "STV",
        "ov": "DEFAULT",
        "pl": "MID_LEFT",
        "cv": "4",
        "vft": "Tire Inflator Portable Air Compressor, Portable Tire Inflator for Car, Tire Inflator",
        "prc": "$33.99",
        "prm": true,
        "rtg": "4.6",
        "sprc": "$49.99",
        "hstr": true,
        "fstr": "4",
        "rctn": "45",
        "cpn": {
            "type": "Percent off",
            "value": 9
        },
        "dbt": "Limited Time Deal"
    },
    "ids": {
        "a": "591002688150029381",
        "c": "590882246413987492",
        "i": "Eau1gb2VuqsqJkXfuI9oWQ",
        "u": "Eau1gb2VuqsqJkXfuI9oWQ"
    },
    "t": {
        "ivaFirstClick": [
            "https://example.com/tracking"
        ]
    },
    "tt": {
        "e": [
            "ivaCtaDisplayed",
            "ivaCtaInvoked",
            "ivaError"
        ],
        "m": [],
        "tmpl": "https://track.test/e=[EVENTTYPE]&t=[ATTIME]&v=[CREATIVE_RENDER_VERSION]&err=[ERRORCODE]"
    },
    "v": "1.0"
}"#;

pub const MINIMAL_BASELINE_IVA_TEMPLATE: &str = r#"{
    "templateType": "BaselineIVA",
    "templateProps": {
        "mandatoryFields": ["title", "product.price"],
        "mainContent": [
            {
                "type": "TextWidget",
                "props": {
                    "text": "Featured Product",
                    "typeRamp": "body"
                }
            }
        ],
        "mainIcon": {
            "type": "IconWidget",
            "props": {
                "iconName": "Cart"
            }
        }
    }
}"#;

pub const MINIMAL_AD_FEEDBACK_TEMPLATE: &str = r#"{
    "templateType": "AdFeedback",
    "templateProps": {
        "mandatoryFields": [],
        "mainContent": [],
        "thankYouScreen": [],
        "somethingElseScreen": [],
        "linkSentErrorScreen": [],
        "linkSentScreen": []
    }
}"#;

pub const DEFAULT_SURFACE_X_TEMPLATE_ID: &str = "IVA";

pub const DEFAULT_SURFACE_X_TEMPLATE_MAJOR_VERSION: u32 = 1;

pub const DEFAULT_SURFACE_X_TEMPLATE_MINOR_VERSION: u32 = 0;

pub const DEFAULT_SURFACE_X_TEMPLATE_SEPARATOR: &str = "#";

pub const DEFAULT_LIVE_AD_START_TIME_MS: f64 = 0.0;

pub const DEFAULT_LIVE_AD_END_TIME_MS: f64 = 30.0;

pub const DEFAULT_ACTIONABLE_AD_EXTENSION_ID: &str = "abc123";

pub const DEFAULT_ACTIONABLE_AD_EXTENSION_VERSION: &str = "4";

pub const DEFAULT_ATC_ASIN: &str = "B0DKJLH1YK";

pub const DEFAULT_STP_ESI: &str = "c6990f04-93ed-45a5-bd3f-6b82f9abff82";

pub const DEFAULT_STP_NSI: &str = "ea185a0f-9e82-4f87-9b02-98ba4a75ae2c";

pub const DEFAULT_SMM_ESI: &str = DEFAULT_STP_ESI;

pub const DEFAULT_SMM_NSI: &str = DEFAULT_STP_NSI;
