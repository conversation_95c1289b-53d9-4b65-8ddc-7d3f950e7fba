#![allow(dead_code, reason = "test utils for ads")]

use crate::interactive_video_ad::{
    ActionableAdExtension, AdExtensionData, InteractiveVideoAd, SurfaceXTemplateId,
};
use crate::iva_v4_extension::{IvaV4ExtensionAdParameters, IvaV4VodLinearExtensionAdParameters};
use crate::sos_response::{
    AdFeedbackTemplateResponse, AdItemResponse, AdItemResponseCommons, BaseTemplateResponse,
    GetSurfaceTemplatesData, IvaTemplateResponse, LayoutPosition, TemplateIdResponse,
};
use crate::test_data::{
    ADD_TO_CART_AD_PARAMETERS, DEFAULT_ACTIONABLE_AD_EXTENSION_ID,
    DEFAULT_ACTIONABLE_AD_EXTENSION_VERSION, DEFAULT_LIVE_AD_END_TIME_MS,
    DEFAULT_LIVE_AD_START_TIME_MS, DEFAULT_SURFACE_X_TEMPLATE_ID,
    DEFAULT_SURFACE_X_TEMPLATE_MAJOR_VERSION, DEFAULT_SURFACE_X_TEMPLATE_MINOR_VERSION,
    DEFAULT_SURFACE_X_TEMPLATE_SEPARATOR, SEND_ME_MORE_AD_PARAMETERS, SEND_TO_PHONE_AD_PARAMETERS,
};
use network_parser::core::network_parse_from_str;
use std::collections::HashMap;

#[derive(Default)]
pub struct AdExtensionDataBuilder {
    payload: Option<String>,
    duration_ms: Option<f64>,
    start_ms: Option<f64>,
    end_ms: Option<f64>,
    tracking_url: Option<String>,
}

impl AdExtensionDataBuilder {
    pub fn with_stp_payload(mut self) -> Self {
        self.payload = Some(SEND_TO_PHONE_AD_PARAMETERS.to_string());
        self
    }

    pub fn with_smm_payload(mut self) -> Self {
        self.payload = Some(SEND_ME_MORE_AD_PARAMETERS.to_string());
        self
    }

    pub fn with_atc_payload(mut self) -> Self {
        self.payload = Some(ADD_TO_CART_AD_PARAMETERS.to_string());
        self
    }

    pub fn with_duration(mut self, duration_ms: f64) -> Self {
        self.duration_ms = Some(duration_ms);
        self
    }

    pub fn with_start_time(mut self, start_ms: f64) -> Self {
        self.start_ms = Some(start_ms);
        self
    }

    pub fn with_end_time(mut self, end_ms: f64) -> Self {
        self.end_ms = Some(end_ms);
        self
    }

    pub fn with_tracking_url(mut self, tracking_url: Option<String>) -> Self {
        self.tracking_url = tracking_url;
        self
    }

    pub fn build_live(self) -> AdExtensionData {
        AdExtensionData::Live {
            parameters: network_parse_from_str::<IvaV4ExtensionAdParameters>(
                &self.payload.expect("Need to set a payload"),
            )
            .expect("Failed to deserialize payload"),
            tracking_url: self.tracking_url,
            start_ms: self.start_ms.unwrap_or(DEFAULT_LIVE_AD_START_TIME_MS),
            end_ms: self.end_ms.unwrap_or(DEFAULT_LIVE_AD_END_TIME_MS),
        }
    }

    pub fn build_vod_linear(self) -> AdExtensionData {
        AdExtensionData::VodOrLinear {
            parameters: network_parse_from_str::<IvaV4VodLinearExtensionAdParameters>(
                &self.payload.expect("Need to set a payload"),
            )
            .expect("Failed to deserialize payload"),
        }
    }
}

#[derive(Default)]
pub struct ActionableAdExtensionBuilder {
    id: Option<String>,
    version: Option<String>,
    template_id: Option<SurfaceXTemplateId>,
    data: Option<AdExtensionData>,
}

impl ActionableAdExtensionBuilder {
    pub fn with_id(mut self, id: impl Into<String>) -> Self {
        self.id = Some(id.into());
        self
    }

    pub fn with_version(mut self, version: impl Into<String>) -> Self {
        self.version = Some(version.into());
        self
    }

    pub fn with_template_id(mut self, template_id: SurfaceXTemplateId) -> Self {
        self.template_id = Some(template_id);
        self
    }

    pub fn with_data(mut self, data: AdExtensionData) -> Self {
        self.data = Some(data);
        self
    }

    pub fn build(self) -> ActionableAdExtension {
        ActionableAdExtension {
            id: self
                .id
                .unwrap_or_else(|| DEFAULT_ACTIONABLE_AD_EXTENSION_ID.to_string()),
            version: self
                .version
                .unwrap_or_else(|| DEFAULT_ACTIONABLE_AD_EXTENSION_VERSION.to_string()),
            template_id: self.template_id.unwrap_or_else(|| SurfaceXTemplateId {
                id: DEFAULT_SURFACE_X_TEMPLATE_ID.to_string(),
                major_version: DEFAULT_SURFACE_X_TEMPLATE_MAJOR_VERSION,
                minor_version: DEFAULT_SURFACE_X_TEMPLATE_MINOR_VERSION,
            }),
            data: self.data.expect("Need to set ad extension data"),
        }
    }
}

#[derive(Default)]
pub struct InteractiveVideoAdBuilder {
    id: Option<String>,
    extension: Option<ActionableAdExtension>,
}

impl InteractiveVideoAdBuilder {
    pub fn with_id(mut self, id: impl Into<String>) -> Self {
        self.id = Some(id.into());
        self
    }

    pub fn with_extension(mut self, extension: ActionableAdExtension) -> Self {
        self.extension = Some(extension);
        self
    }

    pub fn build(self) -> InteractiveVideoAd {
        InteractiveVideoAd {
            id: self.id.expect("Id is required"),
            extension: self.extension.expect("Extension is required"),
        }
    }
}

#[derive(Default)]
pub struct SosTemplatesResponseBuilder {
    ad_items: HashMap<String, AdItemResponse>,
    templates: Option<HashMap<String, String>>,
    localized_strings: Option<HashMap<String, HashMap<String, String>>>,
}

impl SosTemplatesResponseBuilder {
    pub fn add_ad_item(
        mut self,
        ad_instance_id: impl Into<String>,
        response: AdItemResponse,
    ) -> Self {
        self.ad_items.insert(ad_instance_id.into(), response);
        self
    }

    pub fn add_template(
        mut self,
        template_id: impl Into<String>,
        template: impl Into<String>,
    ) -> Self {
        if self.templates.is_none() {
            self.templates = Some(HashMap::new());
        }
        if let Some(templates) = self.templates.as_mut() {
            templates.insert(template_id.into(), template.into());
        }
        self
    }

    pub fn build(self) -> GetSurfaceTemplatesData {
        GetSurfaceTemplatesData {
            adItems: self.ad_items,
            commons: Some(AdItemResponseCommons {
                templates: self.templates,
                localizedStrings: self.localized_strings,
                ext: None,
            }),
        }
    }
}

#[derive(Default)]
pub struct TemplateIdResponseBuilder {
    id: Option<String>,
    major_version: Option<u32>,
    minor_version: Option<u32>,
    separator: Option<String>,
}

impl TemplateIdResponseBuilder {
    pub fn with_id(mut self, id: impl Into<String>) -> Self {
        self.id = Some(id.into());
        self
    }

    pub fn with_major_version(mut self, version: u32) -> Self {
        self.major_version = Some(version);
        self
    }

    pub fn with_minor_version(mut self, version: u32) -> Self {
        self.minor_version = Some(version);
        self
    }

    pub fn with_separator(mut self, separator: impl Into<String>) -> Self {
        self.separator = Some(separator.into());
        self
    }

    pub fn build(self) -> TemplateIdResponse {
        TemplateIdResponse {
            id: self
                .id
                .unwrap_or_else(|| DEFAULT_SURFACE_X_TEMPLATE_ID.to_string()),
            majorVersion: self
                .major_version
                .unwrap_or(DEFAULT_SURFACE_X_TEMPLATE_MAJOR_VERSION),
            minorVersion: self
                .minor_version
                .unwrap_or(DEFAULT_SURFACE_X_TEMPLATE_MINOR_VERSION),
            separator: self
                .separator
                .or_else(|| Some(DEFAULT_SURFACE_X_TEMPLATE_SEPARATOR.to_string())),
        }
    }
}

#[derive(Default)]
pub struct AdItemResponseBuilder {
    template_id: Option<TemplateIdResponse>,
    iva: Option<IvaTemplateResponse>,
    ad_feedback: Option<AdFeedbackTemplateResponse>,
}

impl AdItemResponseBuilder {
    pub fn with_template_id(mut self, template_id: TemplateIdResponse) -> Self {
        self.template_id = Some(template_id);
        self
    }

    pub fn with_enabled_iva(mut self, layout_position: LayoutPosition) -> Self {
        let template_id = self
            .template_id
            .clone()
            .unwrap_or_else(|| TemplateIdResponseBuilder::default().build());
        self.iva = Some(IvaTemplateResponse {
            base: BaseTemplateResponse {
                enabled: Some(true),
                layoutPosition: Some(layout_position),
                ext: None,
            },
            templateId: Some(template_id),
        });
        self
    }

    pub fn build(self) -> AdItemResponse {
        AdItemResponse {
            iva: self.iva,
            adFeedback: self.ad_feedback,
        }
    }
}
