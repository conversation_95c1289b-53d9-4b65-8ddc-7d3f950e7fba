use cfg_test_attr_derive::derive_test_only;
use network_parser::prelude::*;
use network_parser_derive::NetworkParsed;
use serde::Serialize;

#[derive(Clone, NetworkParsed)]
#[derive_test_only(Debug, PartialEq)]
pub struct CTAServiceResponse {
    pub message: String,
}

#[derive(Serialize)]
#[derive_test_only(Clone, Debug, PartialEq)]
pub struct CTAServiceRequestParams {
    #[serde(rename = "runtimeInfo")]
    pub runtime_info: RuntimeInfo,
    #[serde(rename = "ctaPayload")]
    pub cta_payload: CTAServiceRequestPayload,
}

#[derive(Serialize)]
#[derive_test_only(Clone, Debug, PartialEq)]
pub enum CTAServiceRequestPayload {
    AddToCart(AddToCartPayload),
    SendToPhone(SendToPhonePayload),
    SendMeMore(SendMeMorePayload),
}

const SERVICE_URL: &str = "/cdp/v1/invokeCTA";

impl CTAServiceRequestPayload {
    pub fn get_service_url(&self) -> String {
        let path = match self {
            CTAServiceRequestPayload::AddToCart(_) => "ATC",
            CTAServiceRequestPayload::SendToPhone(_) => "STP",
            CTAServiceRequestPayload::SendMeMore(_) => "SMM",
        };
        format!("{}/{}", SERVICE_URL, path)
    }
}

#[derive(Serialize)]
#[derive_test_only(Clone, Debug, PartialEq)]
pub struct AddToCartPayload {
    #[serde(rename = "productASIN")]
    pub product_asin: String,
    #[serde(rename = "offerListingId")]
    pub offer_listing_id: Option<String>,
}

#[derive(Serialize)]
#[derive_test_only(Clone, Debug, PartialEq)]
pub struct SendToPhonePayload {
    #[serde(rename = "emailSettingId")]
    pub email_setting_id: String,
    #[serde(rename = "notificationSettingId")]
    pub notification_setting_id: String,
}

#[derive(Serialize)]
#[derive_test_only(Clone, Debug, PartialEq)]
pub struct SendMeMorePayload {
    #[serde(rename = "emailSettingId")]
    pub email_setting_id: String,
}

#[derive(Serialize)]
#[derive_test_only(Clone, Debug, PartialEq)]
pub struct RuntimeInfo {
    #[serde(rename = "appName")]
    pub app_name: String,
    #[serde(rename = "deviceName")]
    pub device_name: String,
    #[serde(rename = "impressionId")]
    pub impression_id: String,
    #[serde(rename = "bidId")]
    pub bid_id: String,
    #[serde(rename = "creativeCfId")]
    pub creative_id: u64,
    #[serde(rename = "adCfId")]
    pub ad_id: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use rstest::rstest;

    #[rstest]
    #[case(
        CTAServiceRequestPayload::AddToCart(AddToCartPayload {
            product_asin: "B123".to_string(),
            offer_listing_id: None
        }),
        "/cdp/v1/invokeCTA/ATC"
    )]
    #[case(
        CTAServiceRequestPayload::SendToPhone(SendToPhonePayload {
            email_setting_id: "email1".to_string(),
            notification_setting_id: "notif1".to_string()
        }),
        "/cdp/v1/invokeCTA/STP"
    )]
    #[case(
        CTAServiceRequestPayload::SendMeMore(SendMeMorePayload {
            email_setting_id: "email1".to_string()
        }),
        "/cdp/v1/invokeCTA/SMM"
    )]
    fn test_get_service_url(#[case] payload: CTAServiceRequestPayload, #[case] expected: &str) {
        assert_eq!(payload.get_service_url(), expected);
    }
}
