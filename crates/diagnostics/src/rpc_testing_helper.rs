use ignx_compositron::app::rpc::RPCError;
use serde::Serialize;
use std::cell::RefCell;
use std::collections::HashMap;
use std::{rc::Rc, time::Duration};

use ignx_compositron::time::Instant;
use mockall::mock;
use serde::de::DeserializeOwned;
use serde_json::Value;

// Mocking Fluent APIs (`Fn(self) -> Self`) with mockall is the absolute worst, so I am trying something new.
// In order to do that, I need to mock the RPCManager myself so that I can have it use a custom MockRPCCall.
mock! {
    pub RPCManager {
        pub fn create() -> Self;
        pub fn call<R: DeserializeOwned + 'static>(
            &self,
            _js_func_name: &str,
        ) -> RecordingMockRPCCall<R>;

        pub fn register_cross_app_function<A, R, F>(
            &self,
            name: String,
            func: F
        )
        where
            A: DeserializeOwned + 'static,
            R: Serialize + 'static,
            F: Fn(A) -> Result<R, Box<dyn std::error::Error>> + 'static;
    }

    impl Clone for RPCManager {
        fn clone(&self) -> Self;
    }
}

/// A glorified list of MockRPCCalls.
///
/// MockRPCCalls will record themselves in an instance of this when `send` is called.
/// Those recorded calls can then be used to do validation on the arguments or simulate responses and errors.
pub struct Recorder<T> {
    calls: Rc<RefCell<Vec<T>>>,
}

impl<T> Clone for Recorder<T> {
    fn clone(&self) -> Self {
        Self {
            calls: Rc::clone(&self.calls),
        }
    }
}

impl<T> Recorder<T> {
    pub fn new() -> Self {
        Self {
            calls: Rc::new(RefCell::new(Vec::new())),
        }
    }

    pub fn record_call(&self, call: T) {
        self.calls.borrow_mut().push(call);
    }

    pub fn get_and_clear(&self) -> Vec<T> {
        let mut calls = self.calls.borrow_mut();
        let mut result = Vec::new();
        std::mem::swap(&mut result, &mut calls);
        result
    }
}

/// This is just a slightly trimmed down/modified RPCCall.
/// The fluent methods are basically the same, just mutating and returning itself,
/// but the send method is replaced with a call to record its final state.
#[allow(dead_code)]
pub struct RecordingMockRPCCall<R: DeserializeOwned + 'static> {
    // fields from RPCCall that we want to record for validation
    pub args: HashMap<String, serde_json::Value>,
    pub times_out_at: Instant,
    pub success_callback: Option<Box<dyn FnOnce(R)>>,
    pub error_callback: Option<Box<dyn FnOnce(RPCError)>>,
    // the "recorder" that will hold this MockRPCCall so we can retrieve it in our test
    recorder: Recorder<RecordingMockRPCCall<R>>,
}

#[allow(dead_code)]
impl<R: DeserializeOwned> RecordingMockRPCCall<R> {
    pub fn new(recorder: Recorder<RecordingMockRPCCall<R>>) -> Self {
        Self {
            args: HashMap::new(),
            times_out_at: Instant::now() + Duration::from_secs(60 * 60),
            success_callback: None,
            error_callback: None,
            recorder,
        }
    }

    pub fn arg(mut self, key: impl Into<String>, arg: Value) -> Self {
        self.args.insert(key.into(), arg);
        self
    }

    pub fn timeout(mut self, duration: Duration) -> Self {
        self.times_out_at = Instant::now() + duration;
        self
    }

    pub fn success_callback(mut self, success_callback: Box<dyn FnOnce(R)>) -> Self {
        self.success_callback = Some(success_callback);
        self
    }

    pub fn error_callback(mut self, error_callback: Box<dyn FnOnce(RPCError)>) -> Self {
        self.error_callback = Some(error_callback);
        self
    }

    pub fn send(self) {
        self.recorder.clone().record_call(self);
    }
}
