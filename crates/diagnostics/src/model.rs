use cfg_test_attr_derive::derive_test_only;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt::{Debug, Display};

#[derive(Serialize, Deserialize, Clone)]
#[derive_test_only(PartialEq, Debug)]
pub struct CatflapDataItem {
    pub key: String,
    pub value: CatflapDataValue,
}

#[derive(Serialize, Deserialize, Clone)]
#[derive_test_only(PartialEq)]
#[serde(untagged)]
pub enum CatflapDataValue {
    Object(HashMap<String, CatflapDataValue>),
    Array(Vec<CatflapDataValue>),
    String(String),
    Number(f64),
    Bool(bool),
    Null,
}

impl Display for CatflapDataValue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CatflapDataValue::Object(map) => write!(f, "{:#?}", map),
            CatflapDataValue::Array(vec) => write!(f, "{:#?}", vec),
            CatflapDataValue::String(s) => std::fmt::Display::fmt(s, f),
            CatflapDataValue::Number(n) => std::fmt::Display::fmt(n, f),
            CatflapDataValue::Bool(b) => std::fmt::Display::fmt(b, f),
            CatflapDataValue::Null => write!(f, "null"),
        }
    }
}

impl Debug for CatflapDataValue {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CatflapDataValue::Object(map) => write!(f, "{:#?}", map),
            CatflapDataValue::Array(vec) => write!(f, "{:#?}", vec),
            CatflapDataValue::String(s) => std::fmt::Debug::fmt(s, f),
            CatflapDataValue::Number(n) => std::fmt::Debug::fmt(n, f),
            CatflapDataValue::Bool(b) => std::fmt::Debug::fmt(b, f),
            CatflapDataValue::Null => write!(f, "null"),
        }
    }
}
