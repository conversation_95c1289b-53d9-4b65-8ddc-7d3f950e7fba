#[cfg(test)]
use crate::rpc_testing_helper::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as RPCManager;
use ignx_compositron::app::rpc::RPCError;
#[cfg(not(test))]
use ignx_compositron::app::rpc::RPCManager;
use ignx_compositron::metrics::{current_vm_allocated_memory, peak_process_memory, process_memory};

use ignx_compositron::app::wasm_app::TaskGuard;
use ignx_compositron::prelude::*;
use ignx_compositron::reactive::{on_cleanup, store_value};

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::{ops::Deref, rc::Rc, time::Duration};

use crate::model::{CatflapDataItem, CatflapDataValue};
#[derive(Serialize, Deserialize)]
pub(crate) struct CatflapDataResponse {
    data: Vec<CatflapDataItem>,
}

struct CatflapPollingTaskGuard(TaskGuard);

impl Deref for CatflapPollingTaskGuard {
    type Target = TaskGuard;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl From<TaskGuard> for CatflapPollingTaskGuard {
    fn from(value: TaskGuard) -> Self {
        Self(value)
    }
}

pub fn use_catflap_data(ctx: &AppContext) -> Signal<Vec<CatflapDataItem>> {
    let data_signal: RwSignal<Vec<CatflapDataItem>> = create_rw_signal(ctx.scope(), vec![]);
    let in_progress = store_value(ctx.scope(), false);
    let Some(rpc_manager) = use_context::<RPCManager>(ctx.scope()) else {
        log::error!("No RPCManager found in context");
        return data_signal.into();
    };

    let task_guard: CatflapPollingTaskGuard = ctx
        .schedule_repeating_task(Duration::from_secs(1), move || {
            if in_progress.get_value() {
                log::info!("Already in progress.");
                return;
            }
            let rust_catflap_data = vec![get_memory_catflap_data()];

            in_progress.set_value(true);
            let success_callback = Box::new({
                let mut rust_catflap_data = rust_catflap_data.clone();
                move |response: CatflapDataResponse| {
                    log::info!("Success in getCatflapData.");
                    in_progress.set_value(false);
                    rust_catflap_data.extend(response.data);
                    data_signal.set(rust_catflap_data);
                }
            });

            let error_callback = Box::new(move |error: RPCError| {
                log::error!("Error in getCatflapData: {:?}", error);
                in_progress.set_value(false);
                data_signal.set(rust_catflap_data);
            });
            rpc_manager
                .call("getCatflapData")
                .success_callback(success_callback)
                .error_callback(error_callback)
                .send();
        })
        .into();
    let task_id = task_guard.task_id;

    provide_context(ctx.scope(), Rc::new(task_guard));

    on_cleanup(ctx.scope(), {
        let ctx = ctx.clone();
        move || {
            ctx.cancel_task(task_id);
        }
    });

    data_signal.into()
}

struct RustCatflapPollingTaskGuard(TaskGuard);

impl Deref for RustCatflapPollingTaskGuard {
    type Target = TaskGuard;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl From<TaskGuard> for RustCatflapPollingTaskGuard {
    fn from(value: TaskGuard) -> Self {
        Self(value)
    }
}

fn get_memory_catflap_data() -> CatflapDataItem {
    let allocated_memory = current_vm_allocated_memory().as_kilobytes_f32();
    let process_memory = process_memory().as_kilobytes_f32();
    let peak_process_memory = peak_process_memory().as_kilobytes_f32();
    let formatted_allocated_memory = format!("{:#}Kb", allocated_memory);
    let formatted_process_memory = format!("{:#}Kb", process_memory);
    let formatted_peak_process_memory = format!("{:#}Kb", peak_process_memory);
    CatflapDataItem {
        key: "Rust Memory".to_string(),
        value: CatflapDataValue::Object({
            HashMap::from([
                (
                    "allocatedMemory".to_string(),
                    CatflapDataValue::String(formatted_allocated_memory),
                ),
                (
                    "processMemory".to_string(),
                    CatflapDataValue::String(formatted_process_memory),
                ),
                (
                    "peakProcessMemory".to_string(),
                    CatflapDataValue::String(formatted_peak_process_memory),
                ),
            ])
        }),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::rpc_testing_helper::MockRPCManager;
    use crate::rpc_testing_helper::Recorder;
    use crate::rpc_testing_helper::RecordingMockRPCCall;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::compose;
    use ignx_compositron::time::MockClock;
    use mockall::predicate;

    /// Create a mock RPCManager that will record any RPC calls that it sends using the given recorder.
    fn create_mock_rpc_manager(
        recorder: Recorder<RecordingMockRPCCall<CatflapDataResponse>>,
    ) -> MockRPCManager {
        let mut rpc_manager = MockRPCManager::new();

        rpc_manager
            .expect_clone()
            .once()
            .returning_st(move || -> MockRPCManager {
                let recorder = recorder.clone();
                let mut rpc_manager_clone = MockRPCManager::new();
                rpc_manager_clone
                    .expect_call()
                    .times(..)
                    .with(predicate::eq("getCatflapData"))
                    .returning_st(move |_| RecordingMockRPCCall::new(recorder.clone()));
                rpc_manager_clone
            });
        rpc_manager
    }

    /// Call the hook and let the game loop run for a bit to make sure the repeating task is running.
    #[test]
    fn use_catflap_data_emits_rpc_response_to_signal() {
        let recorder: Recorder<RecordingMockRPCCall<CatflapDataResponse>> = Recorder::new();

        launch_test(
            {
                let recorder = recorder.clone();
                |ctx| {
                    let rpc_manager = create_mock_rpc_manager(recorder);
                    provide_context(ctx.scope(), rpc_manager);

                    let data_signal: Signal<Vec<CatflapDataItem>> = use_catflap_data(&ctx);
                    provide_context(ctx.scope(), data_signal);
                    compose! {
                        // In order to get the game loop, we need to return a root component
                        Column() {}
                    }
                }
            },
            move |_scope, mut renderer| {
                // the schedule_repeating_task won't be registered until the first tick
                // after the component is created
                let _ = renderer.tick_until_done();
                for i in 1..10 {
                    // advance the clock till the task should be triggered
                    MockClock::advance(Duration::from_secs(1));
                    // tick so the repeating task is executed
                    let _ = renderer.tick_until_done();

                    // retrieve RPC Call that should have been recorded
                    let mut calls = recorder.get_and_clear();
                    assert_eq!(calls.len(), 1);
                    let call = calls.remove(0);
                    assert!(call.success_callback.is_some());

                    // construct some data to send as a response
                    let expected_rpc_items = vec![CatflapDataItem {
                        key: "Test Item".to_string(),
                        value: CatflapDataValue::Number(i.into()),
                    }];
                    let test_response = CatflapDataResponse {
                        data: expected_rpc_items.clone(),
                    };
                    if let Some(success_callback) = call.success_callback {
                        // call the RPC success callback with the test response
                        success_callback(test_response);
                    }

                    let data_signal: Signal<Vec<CatflapDataItem>> = expect_context(_scope);
                    let mut data = data_signal.get_untracked();
                    assert_eq!(
                        data.len(),
                        2,
                        "Expected two data items, received {:?}",
                        data
                    );

                    let rust_catflap_data = data.remove(0);

                    assert_eq!(rust_catflap_data.key, "Rust Memory".to_string());
                    match rust_catflap_data.value {
                        CatflapDataValue::Object(obj) => {
                            assert_eq!(obj.len(), 3);
                            assert!(obj.contains_key("allocatedMemory"));
                            assert!(obj.contains_key("processMemory"));
                            assert!(obj.contains_key("peakProcessMemory"));
                        }
                        _ => panic!("Expected object, received {:?}", rust_catflap_data.value),
                    }

                    // check to make sure that the hook passed RPC response data along to the signal
                    assert_eq!(data, expected_rpc_items);
                }
            },
        );
    }

    /// Rig up a child scope using a Show. Call the hook from inside the child scope, and then
    /// disable the Show to dispose the child scope.
    ///
    /// Ensures that the hook stops making RPC calls when the child scope is disposed.
    #[test]
    fn use_catflap_data_stops_making_calls_when_scope_disposed() {
        let recorder: Recorder<RecordingMockRPCCall<CatflapDataResponse>> = Recorder::new();

        launch_test(
            {
                let recorder = recorder.clone();
                |ctx| {
                    let rpc_manager = create_mock_rpc_manager(recorder);
                    provide_context(ctx.scope(), rpc_manager);

                    let enabled: RwSignal<bool> = create_rw_signal(ctx.scope(), true);
                    provide_context(ctx.scope(), enabled);

                    compose! {
                        Column() {
                            Show(
                                condition: enabled.into(),
                                if_builder: Box::new(|ctx| {
                                    let _: Signal<Vec<CatflapDataItem>> = use_catflap_data(&ctx);

                                    compose!{
                                        Column() {}
                                    }.into_widget()
                                })
                            )
                        }
                    }
                }
            },
            move |scope, mut renderer| {
                // the schedule_repeating_task won't be registered until after the first tick
                let _ = renderer.tick_until_done();

                // make sure we actually trigger the repeating task at least once
                MockClock::advance(Duration::from_secs(1));
                let _ = renderer.tick_until_done();
                let mut calls = recorder.get_and_clear();
                assert_eq!(calls.len(), 1);
                // task is being triggered, now we can make sure it stops after we dispose the scope.
                // but to do that, we need to send a response to the current call, because
                // the hook won't make another RPC call while it is still waiting on a response
                let call = calls.remove(0);
                assert!(call.success_callback.is_some());
                if let Some(success_callback) = call.success_callback {
                    success_callback(CatflapDataResponse { data: vec![] });
                }

                // disable the Show which will dispose of the child scope that the hook is running in
                let enabled: RwSignal<bool> = expect_context(scope);
                enabled.set(false);
                // let the loop run to actually dispose of the scope
                let _ = renderer.tick_until_done();

                // advance the clock until the next task would be scheduled run if it wasn't cancelled
                MockClock::advance(Duration::from_secs(1));
                let _ = renderer.tick_until_done();

                // ensure no more RPC calls were recorded, meaning the repeating task was cancelled
                let calls_after_dispose = recorder.get_and_clear();
                assert_eq!(calls_after_dispose.len(), 0);
            },
        );
    }
}
