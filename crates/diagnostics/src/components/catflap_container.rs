use std::rc::Rc;

use crate::hooks::use_catflap_data;
use crate::model::{CatflapDataItem, CatflapDataValue};
use amzn_fable_tokens::FableSize;
use fableous::animations::{fable_motion_move_horizontal, fable_motion_move_vertical};
use fableous::typography::typography::*;
use fableous::{SCREEN_HEIGHT, SCREEN_WIDTH};
use ignx_compositron::prelude::*;
use ignx_compositron::show::*;
use ignx_compositron::{compose, compose_option, Composer};
use lrc_image::lrc_image::*;
use lrc_image::types::ImageData;
use network::QrCodeUrlBuilder;

pub enum CatflapDataProvider {
    Default,
    MaybeSignal(MaybeSignal<Vec<CatflapDataItem>>),
    Delegate(Box<dyn Fn(&AppContext) -> MaybeSignal<Vec<CatflapDataItem>>>),
}

impl CatflapDataProvider {
    pub fn get_catflap_data(&self, ctx: &AppContext) -> MaybeSignal<Vec<CatflapDataItem>> {
        match self {
            CatflapDataProvider::Default => {
                CatflapDataProvider::get_default_catflap_data(ctx).into()
            }
            CatflapDataProvider::MaybeSignal(data) => data.clone(),
            CatflapDataProvider::Delegate(delegate) => delegate(ctx),
        }
    }

    pub fn get_default_catflap_data(ctx: &AppContext) -> Signal<Vec<CatflapDataItem>> {
        use_catflap_data(ctx)
    }
}

impl From<MaybeSignal<Vec<CatflapDataItem>>> for CatflapDataProvider {
    fn from(value: MaybeSignal<Vec<CatflapDataItem>>) -> Self {
        CatflapDataProvider::MaybeSignal(value)
    }
}

impl From<Vec<CatflapDataItem>> for CatflapDataProvider {
    fn from(value: Vec<CatflapDataItem>) -> Self {
        CatflapDataProvider::MaybeSignal(value.into())
    }
}

pub const CATFLAP_CONTAINER_TEST_ID: &str = "catflap-container-test-id";

#[Composer]
pub fn CatflapContainer(
    ctx: &AppContext,
    #[into] catflap_enabled: MaybeSignal<bool>,
    #[into]
    #[optional = CatflapDataProvider::Default]
    catflap_data_provider: CatflapDataProvider,
) -> ShowComposable {
    compose! {
        Show(
            condition: catflap_enabled,
            if_builder: Box::new(move |ctx| {
                let catflap_data = catflap_data_provider.get_catflap_data(ctx);
                compose! {
                    Stack() {
                        RowList(
                            items: catflap_data,
                            id: |item| &item.key,
                            item_builder: Rc::new(move |ctx: &AppContext, item: &CatflapDataItem, _: usize| {
                                compose! {
                                    CatflapCell(item)
                                    .focusable()
                                    .width(SCREEN_WIDTH / 2.0)
                                    .padding(Padding::all(25.0))
                                }
                            })
                        )
                        .auto_scroll(AutoScroll::Pivot(Pivot::Start, fable_motion_move_horizontal()))
                    }
                    .padding(Padding { start: 100.0, end: 50.0, top: 50.0, bottom: 50.0 })
                    .background_color(Color::black().apply_opacity(0.75))
                    .size(Vec2::new(SCREEN_WIDTH, SCREEN_HEIGHT))
                    .focus_window()
                }
                .test_id(CATFLAP_CONTAINER_TEST_ID)
                .into_widget()
            })
        )
    }
}

#[Composer]
pub fn CatflapCell(ctx: &AppContext, item: &CatflapDataItem) -> ColumnComposable {
    match item.value {
        CatflapDataValue::Object(_) | CatflapDataValue::Array(_) => compose! {
            CatflapObject(title: item.key.clone(), values: item.value.clone())
        },
        CatflapDataValue::Null => {
            let undefinedItem = CatflapDataItem {
                key: item.key.clone(),
                value: CatflapDataValue::String("Unavailable".to_string()),
            };
            compose! {
                Column() {
                    CatflapKeyValueListItem(item: &undefinedItem)
                }
            }
        }
        _ => compose! {
            Column() {
                CatflapKeyValueListItem(item)
            }
        },
    }
}

pub const CATFLAP_OBJECT_TEST_ID: &str = "catflap-object-test-id";
pub const CATFLAP_QR_CODE_TEST_ID: &str = "catflap-qr-code-test-id";

#[Composer]
pub fn CatflapObject(
    ctx: &AppContext,
    title: String,
    values: CatflapDataValue,
) -> ColumnComposable {
    let qr_url = try_get_qr_data_from_value(&values);

    let items = match values {
        CatflapDataValue::Array(a) => a
            .into_iter()
            .enumerate()
            .map(|(index, value)| CatflapDataItem {
                key: format!("{}[{}]", title, index),
                value,
            })
            .collect(),
        CatflapDataValue::Object(o) => o
            .into_iter()
            .map(|(key, value)| CatflapDataItem { key, value })
            .collect(),
        _ => {
            let unsupported_item: CatflapDataItem = {
                CatflapDataItem {
                    key: title.clone(),
                    value: CatflapDataValue::String("Unsupported value type".to_string()),
                }
            };
            vec![unsupported_item]
        }
    };
    compose! {
        Column() {
            TypographyHeading400(content: title)
            .padding(Padding::vertical(10.0))
            CatflapKeyValueList(items)
            Memo(item_builder: Box::new(move |ctx| {
                if let Some(qr_url) = qr_url.clone() {
                    compose_option! {
                        LRCImage(data: ImageData {
                            url: qr_url,
                            width: FableSize::SIZE1400,
                            height: FableSize::SIZE1400,
                            tags: vec![],
                        })
                        .test_id(CATFLAP_QR_CODE_TEST_ID)
                    }
                } else {
                    None
                }
            }))
        }
        .test_id(CATFLAP_OBJECT_TEST_ID)
    }
}

/// Attempts to extract `devideId` and `deviceTypeId` fields from an object value to build a
/// QR Code for DCF.
fn try_get_qr_data_from_value(value: &CatflapDataValue) -> Option<String> {
    let CatflapDataValue::Object(o) = value else {
        return None;
    };
    let Some(CatflapDataValue::String(did)) = o.get("deviceId") else {
        return None;
    };
    let Some(CatflapDataValue::String(dtid)) = o.get("deviceTypeId") else {
        return None;
    };
    let content = format!(r#"{{"deviceId":"{did}","deviceTypeId":"{dtid}"}}"#);
    let qr_code_url = QrCodeUrlBuilder::for_simple_content(content);
    Some(qr_code_url.to_url_string())
}

pub const CATFLAP_KEY_VALUE_LIST_TEST_ID: &str = "catflap-key-value-list-test-id";

#[Composer]
pub fn CatflapKeyValueList(ctx: &AppContext, items: Vec<CatflapDataItem>) -> ColumnListComposable {
    let key_column_width = items
        .clone()
        .into_iter()
        .map(|item| get_estimate_string_width(&item.key))
        .reduce(f32::max);

    compose! {
        ColumnList(
            items: items,
            id: |item| &item.key,
            item_builder: Rc::new(move |ctx: &AppContext, item: &CatflapDataItem, index: usize| {
                compose! {
                    CatflapKeyValueListItem(item, key_column_width, row_index: index)
                    .focusable()
                }
            })
        )
        .auto_scroll(AutoScroll::Pivot(Pivot::Start, fable_motion_move_vertical()))
        .test_id(CATFLAP_KEY_VALUE_LIST_TEST_ID)
    }
}

pub const CATFLAP_KEY_VALUE_LIST_ITEM_TEST_ID: &str = "catflap-key-value-list-item-test-id";

#[Composer]
pub fn CatflapKeyValueListItem(
    ctx: &AppContext,
    item: &CatflapDataItem,
    #[optional = None] key_column_width: Option<f32>,
    #[optional = 0] row_index: usize,
) -> RowComposable {
    let key_column_width = key_column_width.unwrap_or_else(|| get_estimate_string_width(&item.key));
    let is_even_row = row_index % 2 == 0;
    let background_color = if is_even_row {
        Color::gray().apply_opacity(0.50)
    } else {
        Color::transparent()
    };
    compose! {
        CatflapKeyValuePair(key: item.key.clone(), value: item.value.clone(), key_column_width)
        .main_axis_size(MainAxisSize::Max)
        .background_color(background_color)
        .focusable()
        .test_id(CATFLAP_KEY_VALUE_LIST_ITEM_TEST_ID)
    }
}

pub const CATFLAP_VALUE_TEST_ID: &str = "catflap-value-test-id";

#[Composer]
pub fn CatflapValue(ctx: &AppContext, value: CatflapDataValue) -> LabelComposable {
    compose! {
        TypographyBody200(content: format!("{}", value))
        .test_id(CATFLAP_VALUE_TEST_ID)
    }
}

pub const CATFLAP_KEY_VALUE_PAIR_TEST_ID: &str = "catflap-key-value-pair-test-id";
pub const CATFLAP_KEY_VALUE_PAIR_HEADING_TEST_ID: &str = "catflap-key-value-pair-heading-test-id";

#[Composer]
pub fn CatflapKeyValuePair(
    ctx: &AppContext,
    key: String,
    value: CatflapDataValue,
    key_column_width: f32,
) -> RowComposable {
    compose! {
        Row() {
            TypographyHeading200(content: key.clone())
            .width(key_column_width)
            .test_id(CATFLAP_KEY_VALUE_PAIR_HEADING_TEST_ID)
            CatflapValue(value)
        }
        .test_id(CATFLAP_KEY_VALUE_PAIR_TEST_ID)
    }
}

/// heuristic for allocating space for the key column of the Catflap data sections
fn get_estimate_string_width(str: &str) -> f32 {
    20.0 * str.len() as f32
}

#[cfg(test)]
mod tests {
    use std::cell::RefCell;
    use std::collections::HashMap;

    use super::*;
    use ignx_compositron::app::launch_test;
    use ignx_compositron::reactive::on_cleanup;
    use ignx_compositron::test_utils::{assert_node_does_not_exist, assert_node_exists};

    #[test]
    fn renders_multiple_objects_when_enabled() {
        let mock_data_provider = vec![
            CatflapDataItem {
                key: "Test Section 1".to_string(),
                value: CatflapDataValue::Object({
                    HashMap::from([
                        (
                            "Test Key 1".to_string(),
                            CatflapDataValue::String("Test value 1".to_string()),
                        ),
                        ("Test Key 2".to_string(), CatflapDataValue::Number(123.0)),
                        ("Test Key 3".to_string(), CatflapDataValue::Bool(true)),
                    ])
                }),
            },
            CatflapDataItem {
                key: "Test Section 2".to_string(),
                value: CatflapDataValue::Object({
                    HashMap::from([
                        (
                            "Test Key 1".to_string(),
                            CatflapDataValue::String("Test value 2".to_string()),
                        ),
                        ("Test Key 2".to_string(), CatflapDataValue::Number(456.0)),
                        ("Test Key 3".to_string(), CatflapDataValue::Bool(false)),
                    ])
                }),
            },
        ];
        launch_test(
            |ctx| {
                compose! {
                    CatflapContainer(catflap_enabled: true, catflap_data_provider: mock_data_provider)
                }
            },
            |_scope, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let container_node = node_tree.find_by_test_id(CATFLAP_CONTAINER_TEST_ID);

                assert_node_exists!(container_node.clone());
                assert!(container_node.borrow_props().is_visible);

                assert_node_exists!(container_node
                    .find_any_child_with()
                    .test_id(CATFLAP_OBJECT_TEST_ID)
                    .find_by_index(0));
                assert_node_exists!(container_node
                    .find_any_child_with()
                    .test_id(CATFLAP_OBJECT_TEST_ID)
                    .find_by_index(1));
            },
        );
    }

    #[test]
    fn does_not_render_when_not_enabled() {
        let mock_data_provider = vec![
            CatflapDataItem {
                key: "Test Section 1".to_string(),
                value: CatflapDataValue::Object({
                    HashMap::from([
                        (
                            "Test Key 1".to_string(),
                            CatflapDataValue::String("Test value 1".to_string()),
                        ),
                        ("Test Key 2".to_string(), CatflapDataValue::Number(123.0)),
                        ("Test Key 3".to_string(), CatflapDataValue::Bool(true)),
                    ])
                }),
            },
            CatflapDataItem {
                key: "Test Section 2".to_string(),
                value: CatflapDataValue::Object({
                    HashMap::from([
                        (
                            "Test Key 1".to_string(),
                            CatflapDataValue::String("Test value 2".to_string()),
                        ),
                        ("Test Key 2".to_string(), CatflapDataValue::Number(456.0)),
                        ("Test Key 3".to_string(), CatflapDataValue::Bool(false)),
                    ])
                }),
            },
        ];
        launch_test(
            |ctx| {
                compose! {
                    CatflapContainer(catflap_enabled: false, catflap_data_provider: mock_data_provider)
                }
            },
            |_scope, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                assert_node_does_not_exist!(node_tree.find_by_test_id(CATFLAP_CONTAINER_TEST_ID));
            },
        );
    }

    #[test]
    fn renders_objects_with_uniform_key_column_widths() {
        let mock_data_provider = vec![
            CatflapDataItem {
                key: "Test Section 1".to_string(),
                value: CatflapDataValue::Object({
                    HashMap::from([
                        (
                            "Test Key 1".to_string(),
                            CatflapDataValue::String("Test value 1".to_string()),
                        ),
                        ("Test Key 2".to_string(), CatflapDataValue::Number(123.0)),
                        ("Test Key 3".to_string(), CatflapDataValue::Bool(true)),
                    ])
                }),
            },
            CatflapDataItem {
                key: "Test Section 2".to_string(),
                value: CatflapDataValue::Object({
                    HashMap::from([
                        (
                            "Short Test Key 1".to_string(),
                            CatflapDataValue::String("Test value 2".to_string()),
                        ),
                        (
                            "Longer Test Key 2".to_string(),
                            CatflapDataValue::Number(456.0),
                        ),
                        (
                            "Much Longer Test Key 3".to_string(),
                            CatflapDataValue::Bool(false),
                        ),
                    ])
                }),
            },
        ];
        launch_test(
            |ctx| {
                compose! {
                    CatflapContainer(catflap_enabled: true, catflap_data_provider: mock_data_provider)
                }
            },
            |_scope, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let container_node = node_tree.find_by_test_id(CATFLAP_CONTAINER_TEST_ID);

                let object_node = container_node
                    .find_any_child_with()
                    .test_id(CATFLAP_OBJECT_TEST_ID)
                    .find_all();

                for object in object_node {
                    let key_column_node = object
                        .find_any_child_with()
                        .test_id(CATFLAP_KEY_VALUE_PAIR_HEADING_TEST_ID)
                        .find_all();
                    let key_column_widths = key_column_node
                        .iter()
                        .map(|node| node.borrow_props().layout.size.width)
                        .collect::<Vec<f32>>();
                    let uniform_width = key_column_widths
                        .iter()
                        .all(|&width| width == key_column_widths[0]);
                    assert!(uniform_width);
                }
            },
        );
    }

    /// Test that the catflap data provider is not called if disabled
    /// Otherwise, the Rust client will send RPC calls to the React side constantly,
    /// even when the catflap is not visible.
    #[test]
    fn does_not_call_provider_when_disabled() {
        let was_called = Rc::new(RefCell::new(false));

        launch_test(
            {
                let was_called = was_called.clone();
                move |ctx| {
                    let mock_data_provider = CatflapDataProvider::Delegate(Box::new(move |_| {
                        was_called.replace(true);
                        vec![].into()
                    }));
                    compose! {
                        CatflapContainer(catflap_enabled: false, catflap_data_provider: mock_data_provider)
                    }
                }
            },
            {
                let was_called = was_called.clone();
                move |_scope, mut test_renderer_game_loop| {
                    let _ = test_renderer_game_loop.tick_until_done();

                    assert!(!*was_called.borrow())
                }
            },
        );
    }

    #[test]
    fn delegate_scope_is_destroyed_when_disabled() {
        let was_called = Rc::new(RefCell::new(false));

        let was_destroyed = Rc::new(RefCell::new(false));

        launch_test(
            {
                let was_called = was_called.clone();
                let was_destroyed = was_destroyed.clone();
                move |ctx| {
                    let catflap_enabled: RwSignal<bool> = create_rw_signal(ctx.scope(), true);
                    let mock_data_provider = CatflapDataProvider::Delegate(Box::new(move |ctx| {
                        was_called.replace(true);
                        on_cleanup(ctx.scope(), {
                            let was_destroyed = was_destroyed.clone();
                            move || {
                                was_destroyed.replace(true);
                            }
                        });
                        vec![].into()
                    }));
                    provide_context(ctx.scope(), catflap_enabled); // for use in validation
                    compose! {
                        Stack() {
                            CatflapContainer(catflap_enabled: catflap_enabled, catflap_data_provider: mock_data_provider)
                        }
                    }
                }
            },
            {
                let was_called = was_called.clone();
                move |_scope, mut test_renderer_game_loop| {
                    let _ = test_renderer_game_loop.tick_until_done();
                    assert!(*was_called.borrow());
                    assert!(!*was_destroyed.borrow());

                    let catflap_enabled: RwSignal<bool> = expect_context(_scope);
                    catflap_enabled.set(false);
                    let _ = test_renderer_game_loop.tick_until_done();

                    assert!(*was_destroyed.borrow());
                }
            },
        );
    }

    #[test]
    fn renders_null_items() {
        let mock_data_provider = vec![CatflapDataItem {
            key: "Test Section 1".to_string(),
            value: CatflapDataValue::Null,
        }];
        launch_test(
            |ctx| {
                compose! {
                    CatflapContainer(catflap_enabled: true, catflap_data_provider: mock_data_provider)
                }
            },
            |_scope, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let container_node = node_tree.find_by_test_id(CATFLAP_CONTAINER_TEST_ID);

                assert_node_exists!(container_node.clone());
                assert!(container_node.borrow_props().is_visible);
                let object_nodes = container_node
                    .find_any_child_with()
                    .test_id(CATFLAP_KEY_VALUE_LIST_ITEM_TEST_ID)
                    .find_all();
                assert_eq!(object_nodes.len(), 1);
            },
        );
    }

    #[test]
    fn renders_primitive_items() {
        let mock_data_provider = vec![CatflapDataItem {
            key: "Test Section 1".to_string(),
            value: CatflapDataValue::String("Test Value".to_string()),
        }];
        launch_test(
            |ctx| {
                compose! {
                    CatflapContainer(catflap_enabled: true, catflap_data_provider: mock_data_provider)
                }
            },
            |_scope, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let container_node = node_tree.find_by_test_id(CATFLAP_CONTAINER_TEST_ID);

                assert_node_exists!(container_node.clone());
                assert!(container_node.borrow_props().is_visible);

                let object_node = container_node
                    .find_any_child_with()
                    .test_id(CATFLAP_KEY_VALUE_LIST_ITEM_TEST_ID)
                    .find_all();

                assert_eq!(object_node.len(), 1);
            },
        );
    }

    #[test]
    fn renders_qr_code_for_device_info() {
        let mock_data_provider = vec![CatflapDataItem {
            key: "Device Info".to_string(),
            value: CatflapDataValue::Object(HashMap::from([
                (
                    "deviceId".to_string(),
                    CatflapDataValue::String("test_device_id".to_string()),
                ),
                (
                    "deviceTypeId".to_string(),
                    CatflapDataValue::String("test_device_type_id".to_string()),
                ),
            ])),
        }];
        launch_test(
            |ctx| {
                compose! {
                    CatflapContainer(catflap_enabled: true, catflap_data_provider: mock_data_provider)
                }
            },
            |_scope, mut test_renderer_game_loop| {
                let node_tree = test_renderer_game_loop.tick_until_done();
                let container_node = node_tree.find_by_test_id(CATFLAP_CONTAINER_TEST_ID);

                assert_node_exists!(container_node.clone());
                assert!(container_node.borrow_props().is_visible);

                let object_node = container_node
                    .find_any_child_with()
                    .test_id(CATFLAP_OBJECT_TEST_ID)
                    .find_first();

                assert_node_exists!(&object_node);

                let qr_code_node = object_node
                    .find_any_child_with()
                    .test_id(CATFLAP_QR_CODE_TEST_ID)
                    .find_all();

                assert_eq!(qr_code_node.len(), 1);
            },
        );
    }
}
