use std::collections::HashMap;

use diagnostics::components::catflap_container::*;
use diagnostics::model::*;
use ignx_compositron::{compose, prelude::*};

#[ignx_compositron::main]
fn main() {
    let example_data_provider = vec![
        CatflapDataItem {
            key: "Test Section 1".to_string(),
            value: CatflapDataValue::Object({
                HashMap::from([
                    (
                        "Test Key".to_string(),
                        CatflapDataValue::String("Test value 1".to_string()),
                    ),
                    ("Test Key 2".to_string(), CatflapDataValue::Number(123.0)),
                    ("Test Key 3".to_string(), CatflapDataValue::Bool(true)),
                    (
                        "Test Key 4".to_string(),
                        CatflapDataValue::Array(vec![
                            CatflapDataValue::String("a".to_string()),
                            CatflapDataValue::String("b".to_string()),
                            CatflapDataValue::String("c".to_string()),
                        ]),
                    ),
                    (
                        "deviceId".to_string(),
                        CatflapDataValue::String("test_device_id".to_string()),
                    ),
                    (
                        "deviceTypeId".to_string(),
                        CatflapDataValue::String("test_device_type_id".to_string()),
                    ),
                ])
            }),
        },
        CatflapDataItem {
            key: "Test Section 2".to_string(),
            value: CatflapDataValue::Array(vec![
                CatflapDataValue::String("a".to_string()),
                CatflapDataValue::String("b".to_string()),
                CatflapDataValue::String("c".to_string()),
            ]),
        },
    ];
    launch_composable(|ctx| {
        compose! {
            CatflapContainer(catflap_enabled: true, catflap_data_provider: example_data_provider)
        }
    });
}
