[package]
name = "diagnostics"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true

[dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis"] }
amzn-fable-tokens.workspace = true
cfg-test-attr-derive.workspace = true
cross-app-message.workspace = true
fableous.workspace = true
log.workspace = true
serde.workspace = true
serde_json.workspace = true
network.workspace = true
lrc-image.workspace = true

[dev-dependencies]
amzn-ignx-compositron = { workspace = true, features = ["lifetime_apis", "test_utils", "mock_timer"] }
mockall.workspace = true

[lints]
workspace = true

[[example]]
name = "catflap_example"
crate-type = ["cdylib"]

[features]
test_utils = []
debug_impl = []

[lib]
crate-type = [ "cdylib", "rlib" ]
path = "src/lib.rs"
